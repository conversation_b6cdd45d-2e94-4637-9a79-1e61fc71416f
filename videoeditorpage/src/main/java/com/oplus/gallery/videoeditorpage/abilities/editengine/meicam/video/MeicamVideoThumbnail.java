/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamVideoThumbnail.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;

import com.meicam.sdk.NvsClip;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.MeicamEditorEngine;
import com.oplus.gallery.videoeditorpage.utlis.VideoTypeUtils;
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser;
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.GalleryVideoThumbnailView;
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.IGalleryThumbController;
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.MeicamThumbnailView;
import com.oplus.gallery.videoeditorpage.widget.GalleryHorizontalScrollView;

import java.util.ArrayList;

/**
 * 缩图轴控制器 目前只有壁纸业务使用
 */
public class MeicamVideoThumbnail {

    private static final String TAG = "MeicamVideoThumbnail";
    /**
     * defalut video thumb ratio width : height = 1 : 1
     */
    private static final float DEFAULT_THUMB_RATIO = 1.0f;
    private static final int MILLIS_TIME_BASE = 1000;
    /*1s*/
    private static final int ONE_MINI_SECOND = 1000 * TimeUtils.TIME_1_SEC_IN_MS;
    /*单个缩图时间占比比率*/
    private static final float THUMB_SECOND_RATIO = 1.5f;

    private final Context mContext;
    private final NvsTimeline mTimeline;
    private final NvsVideoTrack mVideoTrack;
    private int mVideoOliveThumbHeight;
    private MeicamEditorEngine mEngine;

    private MeicamThumbnailView mCustomTrimNvsThumbnailView;
    private IGalleryThumbController mCustomTrimThumbController;
    private MeicamThumbnailView mVideoOliveNvsThumbnailView;
    private IGalleryThumbController mVideoOliveThumbController;
    private boolean mOnlyDecodeKeyFrame = false;

    public MeicamVideoThumbnail(Context context, NvsTimeline timeline, MeicamEditorEngine engine) {
        this.mContext = context;
        mTimeline = timeline;
        mVideoTrack = timeline.getVideoTrackByIndex(0);
        mEngine = engine;
        mVideoOliveThumbHeight = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_export_olive_thumbnail_view_height);
    }

    private void configOnlyDecodeKeyFrame(boolean onlyDecodeKeyFrame) {
        mOnlyDecodeKeyFrame = onlyDecodeKeyFrame;
    }

    private void checkCustomTrimThumbnailView(ViewGroup parent, int padding) {
        if (mCustomTrimNvsThumbnailView == null) {
            View thumbView = getThumbnailView(parent);
            if (thumbView != null) {
                mCustomTrimNvsThumbnailView = (MeicamThumbnailView) thumbView;
                mCustomTrimNvsThumbnailView.setOnWidthChangeListener(width -> {
                    mCustomTrimNvsThumbnailView.setStartPadding(padding);
                    mCustomTrimNvsThumbnailView.setEndPadding(padding);
                });
                mCustomTrimThumbController = new MeicamThumbController(mCustomTrimNvsThumbnailView);
            } else {
                GLog.w(TAG, "checkTrimExportNvsThumbnailView() thumbView is null");
            }
        } else {
            ViewGroup oldParent = (ViewGroup) mCustomTrimNvsThumbnailView.getParent();
            if (oldParent != null) {
                oldParent.removeAllViews();
            }
        }
        parent.addView(mCustomTrimNvsThumbnailView);
        if (parent instanceof GalleryVideoThumbnailView) {
            ((GalleryVideoThumbnailView) parent).setThumbController(mCustomTrimThumbController);
        }
    }

    /**
     * 渲染缩图轴
     *
     * @param parent  父容器
     * @param time    控件宽度对应的时间
     * @param padding 控件左右边距
     */
    public void showCustomTrimThumbnail(ViewGroup parent, long time, int padding) {
        checkCustomTrimThumbnailView(parent, padding);
        int thumbnailWidth = parent.getWidth() - 2 * padding;
        if ((mCustomTrimNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            double pixelPerMicrosecond = (time > 0) ? (1.0 * thumbnailWidth / (time * TimeUtils.TIME_1_MS_IN_US)) : 0;
            setMeicamThumbnailViewParam(mCustomTrimNvsThumbnailView, thumbnailWidth, pixelPerMicrosecond, "showCustomTrimThumbnail");
        }
    }

    /**
     * 加载视频导出olive缩图轴组件
     * @param parent 父容器
     */
    public void showVideoOliveThumbnail(ViewGroup parent) {
        initVideoOliveThumbnailViewIfNeed(parent);
        int thumbnailWidth = parent.getWidth();
        if ((mVideoOliveNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            // 如果当前视频不满3s，为了让缩图轴在选帧框撑满，需要修改pixelPerMicrosecond
            double pixelPerMicrosecond = 0;
            // 视频时长
            double duration = mTimeline.getDuration();
            // 视频长度是否小于等于3秒
            boolean isShortVideo = isValidShortVideo();
            // 每个缩图占1.5s
            pixelPerMicrosecond = isShortVideo
                    ? (mVideoOliveThumbHeight * AppConstants.Number.NUMBER_2) / duration
                    : (mVideoOliveThumbHeight * DEFAULT_THUMB_RATIO) / (ONE_MINI_SECOND * THUMB_SECOND_RATIO);
            // 给 MeicamThumbnailView 设置参数
            setMeicamThumbnailViewParam(mVideoOliveNvsThumbnailView, thumbnailWidth, pixelPerMicrosecond, "showVideoOliveThumbnail");
            mVideoOliveNvsThumbnailView.setRoundedRadius(parent.getContext().getResources().getDimension(R.dimen.video_edit_olive_trim_frame_radius));
        }
    }

    /**
     * 检查视频导出olive的缩图轴组件是否已经初始化
     * @param parent 父组件
     */
    @MainThread
    private void initVideoOliveThumbnailViewIfNeed(ViewGroup parent) {
        if (mVideoOliveNvsThumbnailView == null) {
            View thumbView = getThumbnailView(parent);
            if (thumbView != null) {
                mVideoOliveNvsThumbnailView = (MeicamThumbnailView) thumbView;
                mVideoOliveNvsThumbnailView.setOnWidthChangeListener(new MeicamThumbnailView.OnWidthChangeListener() {
                    @Override
                    public void onWidthChange(int width) {
                        int padding = width / AppConstants.Number.NUMBER_2;
                        mVideoOliveNvsThumbnailView.setStartPadding(padding);
                        mVideoOliveNvsThumbnailView.setEndPadding(padding);
                        GLog.d(TAG, "checkVideoOliveThumbnailView width = " + width);
                    }
                });
                mVideoOliveThumbController = new MeicamThumbController(mVideoOliveNvsThumbnailView);
            } else {
                GLog.w(TAG, "checkVideoOliveThumbnailView thumbView is null");
            }
        } else {
            ViewGroup oldParent = (ViewGroup) mVideoOliveNvsThumbnailView.getParent();
            if (oldParent != null) {
                oldParent.removeAllViews();
            }
        }
        parent.addView(mVideoOliveNvsThumbnailView);
        if (parent instanceof GalleryVideoThumbnailView) {
            ((GalleryVideoThumbnailView) parent).setThumbController(mVideoOliveThumbController);
        }
    }

    /**
     * 视频时长是否小于等于3秒
     * @return true/false
     */
    private boolean isValidShortVideo() {
        double duration = mTimeline.getDuration();
        return (duration <= AppConstants.Number.NUMBER_3 * ONE_MINI_SECOND) && (duration > 0);
    }

    /**
     * 给 MeicamThumbnailView 设置参数
     * @param thumbnailView MeicamThumbnailView
     * @param thumbnailWidth 每张缩略图的宽度
     * @param pixelPerMicrosecond 每微秒所占的像素
     * @param methodName 方法名
     */
    private void setMeicamThumbnailViewParam(MeicamThumbnailView thumbnailView, int thumbnailWidth, double pixelPerMicrosecond, String methodName) {
        ArrayList<MeicamThumbnailView.ThumbnailSequenceDesc> infoDescArray = new ArrayList<>();
        int count = mVideoTrack.getClipCount();
        for (int i = 0; i < count; i++) {
            NvsClip curClip = mVideoTrack.getClipByIndex(i);
            MeicamThumbnailView.ThumbnailSequenceDesc infoDesc = new MeicamThumbnailView.ThumbnailSequenceDesc();
            infoDesc.onlyDecodeKeyFrame = mOnlyDecodeKeyFrame;
            infoDesc.mediaFilePath = curClip.getFilePath();
            infoDesc.trimIn = curClip.getTrimIn();
            infoDesc.trimOut = curClip.getTrimOut();
            infoDesc.inPoint = curClip.getInPoint();
            infoDesc.outPoint = curClip.getOutPoint();
            infoDesc.stillImageHint = false;
            int videoType = VideoParser.Companion.getInstance().getSupportVideoType();
            if (VideoTypeUtils.isVideoSupportHdrEdit(videoType)) {
                infoDesc.supportHdr = true;
            }
            infoDescArray.add(infoDesc);
            GLog.d(TAG, methodName
                    + ", index:" + i
                    + ", trimIn:" + infoDesc.trimIn
                    + ", trimOut :" + infoDesc.trimOut
                    + ", inPoint:" + infoDesc.inPoint
                    + ", outPoint :" + infoDesc.outPoint
                    + ", width:" + thumbnailWidth
                    + ", getDuration:" + mTimeline.getDuration()
                    + ", mediaFilePath :" + infoDesc.mediaFilePath);
        }
        thumbnailView.setThumbnailSequenceDescArray(infoDescArray);
        thumbnailView.setThumbnailImageFillMode(MeicamThumbnailView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);

        GLog.d(TAG, methodName
                + ", count:" + count
                + ", DEFAULT_THUMB_RATIO:" + DEFAULT_THUMB_RATIO
                + ", pixelPerMicrosecond:" + pixelPerMicrosecond);
        thumbnailView.setPixelPerMicrosecond(pixelPerMicrosecond);
        thumbnailView.setThumbnailAspectRatio(DEFAULT_THUMB_RATIO);
        thumbnailView.setScrollEnabled(true);
    }

    private View getThumbnailView(ViewGroup parent) {
        return LayoutInflater.from(mContext).inflate(R.layout.videoeditor_video_editor_meicam_thumbnail_view, parent, false);
    }

    private static class MeicamThumbController implements IGalleryThumbController {

        private MeicamThumbnailView mNvsMultiThumbnailSequenceView;
        private ThumbScrollerListener mThumbScrollerListener;

        private MeicamThumbnailView.OnScrollChangeListener mNvsScrollListener
                = new MeicamThumbnailView.OnScrollChangeListener() {

            @Override
            public void onScrollChanged(MeicamThumbnailView view, int x, int oldx) {
                if (mThumbScrollerListener != null) {
                    mThumbScrollerListener.onScrolled(x);
                }
            }
        };

        private MeicamThumbnailView.OnScrollStateChangeListener mScrollStateChangeListener
                = new MeicamThumbnailView.OnScrollStateChangeListener() {
            @Override
            public void onScrollStateChanged(@NonNull GalleryHorizontalScrollView styleHorizontalScrollView, int newState) {
                if (mThumbScrollerListener != null) {
                    mThumbScrollerListener.onScrollStateChanged(newState);
                }
            }
        };

        MeicamThumbController(MeicamThumbnailView view) {
            mNvsMultiThumbnailSequenceView = view;
        }

        @Override
        public long mapTimelinePosFromX(int x) {
            return mNvsMultiThumbnailSequenceView.mapTimelinePosFromX(x) / MILLIS_TIME_BASE;
        }

        @Override
        public int mapXFromTimelinePos(long time) {
            return mNvsMultiThumbnailSequenceView.mapXFromTimelinePos(time * MILLIS_TIME_BASE);
        }

        @Override
        public double getPixelPerMicrosecond() {
            return mNvsMultiThumbnailSequenceView.getPixelPerMicrosecond();
        }

        @Override
        public int getScrollState() {
            return mNvsMultiThumbnailSequenceView.getScrollState();
        }

        @Override
        public void setScrollListener(ThumbScrollerListener scrollerListener) {
            mThumbScrollerListener = scrollerListener;
            mNvsMultiThumbnailSequenceView.setOnScrollChangeListenser(mNvsScrollListener);
            mNvsMultiThumbnailSequenceView.setOnScrollStateChangeListener(mScrollStateChangeListener);
        }

        @Override
        public void setTouchListener(View.OnTouchListener touchListener) {
            mNvsMultiThumbnailSequenceView.setOnTouchListener(touchListener);
        }

        @Override
        public void smoothScrollTo(int x, int y) {
            mNvsMultiThumbnailSequenceView.smoothCOUIScrollTo(x, y);
        }

        @Override
        public void scrollTo(int x, int y) {
            mNvsMultiThumbnailSequenceView.scrollTo(x, y);
        }

        @Override
        public void fullScroll(int pos) {
            mNvsMultiThumbnailSequenceView.fullScroll(pos);
        }

        @Override
        public void stopScroll() {
            mNvsMultiThumbnailSequenceView.stopScroll();
        }
    }
}
