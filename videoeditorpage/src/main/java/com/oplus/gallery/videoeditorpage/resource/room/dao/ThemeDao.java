/*
 * ********************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File        :  - ThemeDao.java
 *  ** Description : xxxxxxxxxxxx
 *  ** Version     : 1.0
 *  ** Date        : 2019/49/19
 *  ** Author      : <EMAIL>
 *  **
 *  ** ---------------------Revision History: ----------------------------
 *  **  <author>                 <data>      <version>  <desc>
 *  **  <EMAIL>  2019/7/19  1.0        build this module
 *  **********************************************************************
 */

package com.oplus.gallery.videoeditorpage.resource.room.dao;

import androidx.room.Dao;
import androidx.room.Query;

import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;

import java.util.List;

@Dao
public interface ThemeDao extends SimpleDao<ThemeItem> {

    @Query("DELETE FROM source_theme WHERE builtin = 1")
    void clearBuiltin();

    @Query("SELECT * FROM source_theme WHERE builtin = 1")
    List<ThemeItem> getAllBuiltin();

    @Query("SELECT * FROM source_theme")
    List<ThemeItem> queryAll();

    @Query("SELECT * FROM source_theme where memory_theme = 1")
    List<ThemeItem> queryAllMemoriesTheme();

    @Query("SELECT * FROM source_theme where download_state & " + (ICON_DOWNLOADED | ICON_DEFAULT)
            + " > " + NOT_DOWNLOADED + " and memory_theme = 1")
    List<ThemeItem> queryIconExistedMemoriesTheme();

    @Query("SELECT * FROM source_theme where download_state & " + FILE_DOWNLOADED + "=" + FILE_DOWNLOADED
            + " and memory_theme = 1")
    List<ThemeItem> queryEnableMemoriesTheme();

    @Query("SELECT * FROM source_theme where download_state & " + FILE_DOWNLOADED + "=" + NOT_DOWNLOADED
            + " and memory_theme = 1")
    List<ThemeItem> queryDisableMemoriesTheme();

    @Query("SELECT * FROM source_theme where memory_theme = 0")
    List<ThemeItem> queryAllVideoTheme();

    @Query("SELECT * FROM source_theme where download_state & " + (ICON_DOWNLOADED | ICON_DEFAULT)
            + " > " + NOT_DOWNLOADED + " and memory_theme = 0")
    List<ThemeItem> queryIconExistedVideoTheme();

    @Query("SELECT * FROM source_theme where download_state & " + FILE_DOWNLOADED + "=" + FILE_DOWNLOADED
            + " and memory_theme = 0")
    List<ThemeItem> queryEnableVideoTheme();

    @Query("SELECT * FROM source_theme where en_name == :name")
    ThemeItem queryThemeByName(String name);

    @Query("SELECT * FROM source_theme WHERE position = :position")
    ThemeItem getEntityByPosition(int position);

    @Query("SELECT * FROM source_theme WHERE theme_id = :themeId")
    ThemeItem getEntityByThemeId(int themeId);

    @Query("SELECT * FROM source_theme WHERE song_id = :songId")
    ThemeItem getItemByMusicId(int songId);

    @Query("SELECT * FROM source_theme WHERE download_state & " + ICON_DOWNLOADED + " = " + NOT_DOWNLOADED)
    List<ThemeItem> getNoIconEntityList();

    @Query("SELECT * FROM source_theme WHERE position > :maxPosition")
    List<ThemeItem> getInvalidEntityList(int maxPosition);

    @Query("DELETE FROM source_theme WHERE position > :maxPosition")
    int deleteInvalidEntity(int maxPosition);

    @Query("SELECT * FROM source_theme WHERE source_path = :path")
    ThemeItem getThemeByPath(String path);
}
