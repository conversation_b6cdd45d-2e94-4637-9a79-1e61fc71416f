/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorTrimState
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.trim;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.data.BaseKeyFrameInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ScrollMode;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.BaseStoryBoardVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper;
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil;
import com.oplus.gallery.videoeditorpage.utlis.KeyFrameUtil;
import com.oplus.gallery.videoeditorpage.utlis.TextUtil;
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils;
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController;
import com.oplus.gallery.videoeditorpage.video.business.cover.EditorCoverHelper;
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser;
import com.oplus.gallery.videoeditorpage.video.business.music.volume.EditorMusicTrackVolumeState;
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType;
import com.oplus.gallery.videoeditorpage.video.business.output.SaveInfo;
import com.oplus.gallery.videoeditorpage.video.business.replace.EditorReplaceEntry;
import com.oplus.gallery.videoeditorpage.video.business.replace.EditorReplaceHelper;
import com.oplus.gallery.videoeditorpage.video.business.sort.EditorSortState;
import com.oplus.gallery.videoeditorpage.video.business.sort.EditorSortUIController;
import com.oplus.gallery.videoeditorpage.video.business.speeder.EditorSpeederState;
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;
import com.oplus.gallery.videoeditorpage.video.business.transform.EditorTransformState;
import com.oplus.gallery.videoeditorpage.video.business.transition.EditorTransitionState;
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import static androidx.annotation.VisibleForTesting.PRIVATE;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.VIDEO_DELETE;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.VIDEO_REPLACE;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.VIDEO_ROTATION;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.VIDEO_SORT;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.VIDEO_SPEED;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.VIDEO_SPLIT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.MIN_CLIP_DURATION;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.Cartoon.TYPE_CARTOON_EFFECT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel.CLEAR_SELECTED_STATE_INDEX;
import static com.oplus.gallery.videoeditorpage.common.statistics.StatisticsConstant.INFO_LINK;
import static com.oplus.gallery.videoeditorpage.common.statistics.StatisticsConstant.MIN_TIME_OF_ITEM;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper.buildTrackVideoData;
import static com.oplus.gallery.videoeditorpage.widget.EditTimelineView.STATE_EDITOR;
import static com.oplus.gallery.videoeditorpage.widget.EditTimelineView.STATE_PREVIEW;
import kotlin.Unit;

/**
 * 编辑剪辑业务
 */
public class EditorTrimState extends EditorTrackBaseState<EditorTrimUIController> implements EditorBaseUIController.OnIconClickListener<Object>,
    EditorTrimUIController.OnThumbnailListener, EditorTrimUIController.OnButtonClickListener,
    EditorTrimUIController.OnPanelClickListener,
    EditorTrimUIController.OnEffectTrackSelectListener {
    private static final String TAG = "EditorTrimState";

    public static final long MIN_SPLIT_CLIP_DURATION = MIN_CLIP_DURATION * 2;
    public static final long MIN_SEEK_TOLERANCE = 10 * VideoUtils.MILLISECOND_TO_MICROSECOND;
    private static final int INT_1 = 1;
    private static final int MAX_DIRECTION_COUNT = 4;

    /**
     * 特效片段响应器，用于对视频分割、变速、替换、删除、排序、新增后，更新特效片段的开始和结束时间
     */
    private final List<IEffectClipResponder> mEffectClipResponderList = Collections.singletonList(
            new BaseEffectClipResponder()
    );
    @VisibleForTesting(otherwise = PRIVATE)
    public boolean mIsStartSubState = false;
    private boolean mIsDoingOperation = false;
    private boolean mOperationHasDone = false;
    private boolean mIsPaused = false;
    private boolean mClickBlocked;

    public EditorTrimState(Context context, EditorControlView editorControlView, WeakReference<TimelineViewModel> weakTimelineViewModel) {
        super(TAG, context, editorControlView, weakTimelineViewModel);
    }

    @Override
    public boolean isPreview() {
        return true;
    }

    @NonNull
    @Override
    protected EditorTrimUIController createUIController() {
        GLog.d(TAG, "createUIController()");
        EditorTrimUIController controller = new EditorTrimUIController(
            mContext,
            mEditorControlView,
            this,
            (getWeakTimelineViewModel() != null) ? getWeakTimelineViewModel().get() : null
        );
        controller.setOnIconClickListener(this);
        controller.setOnThumbnailListener(this);
        controller.setOnButtonClickListener(this);
        controller.setOnEffectTrackSelectListener(this);
        controller.setMaxCountText(mEditorEngine.getMaxCountText());
        controller.setOnScrollListener(new EditorTrackBaseUIController.OnScrollListener() {
            @Override
            public void onScrolling(long timeScrolled, boolean userDo) {
                if (userDo) {
                    seekToPosition(timeScrolled);
                } else {
                    boolean isPlay = mEditorEngine.isPlaying();
                    if (!isPlay) {
                        if (timeScrolled >= 0) {
                            seekToPosition(timeScrolled);
                        } else {
                            GLog.e(TAG, "onScrolling toPosition is :" + timeScrolled);
                        }
                    }
                }
            }
        });
        return controller;
    }

    public void seekToPosition(long position) {
        mEditorControlView.seekTimeline(position, 0, false);
    }

    @Override
    public boolean showOperaIcon() {
        return true;
    }

    @Override
    public void clickCancel() {
        super.clickCancel();
        GLog.d(TAG, "clickCancel()");
    }

    @Override
    public void clickDone() {
        super.clickDone();
    }

    @Override
    public void create() {
        super.create();
        GLog.d(TAG, "create()");
        //        getUIController().initMultiSequence(getEditorEngine().getCurrentTimeline(), getEditorEngine().getTimelineCurrentPosition(),
        //                true, STATE_PREVIEW);
        updateUIControlButtonStatus();
        refreshWhenSelectClipChanged(true);
    }

    @Override
    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig appUiConfig) {
        super.onAppUiStateChanged(appUiConfig);
        updateZoomInOutFrameLayout();
    }

    @Override
    public void pause(boolean isActivityPause) {
        GLog.d(TAG, "pause()");
        super.pause(isActivityPause);
        mIsPaused = true;
    }

    @Override
    public void resume(boolean isActivityPause) {
        GLog.d(TAG, " resume");
        super.resume(isActivityPause);
        mIsPaused = false;
        updateZoomInOutFrameLayout();
        if (mIsStartSubState) {
            mIsStartSubState = false;
            if ((getWeakTimelineViewModel() != null) && (getWeakTimelineViewModel().get() != null)) {
                getWeakTimelineViewModel().get().updateTransitionBorder(AppConstants.Number.NUMBER_MINUS_1, false);
            }
        }
        // 更新封面
        updateCover(EditorCoverHelper.CoverMode.DEFAULT_COVER, EditorCoverHelper.COVER_TIMESTAMP, null);
    }

    @Override
    public void onCurrentTimelineChanged(ITimeline timeline, boolean updateTimelineView) {
        // when timeline changed(by draft init or undo/redo),
        // state usually need reset view for current timeline.
        super.onCurrentTimelineChanged(timeline, updateTimelineView);
        if (timeline == null) {
            return;
        }
        updateUIControlButtonStatus();
        updateTrack(updateTimelineView ? ScrollMode.INVOCATION : ScrollMode.NONE);
        // 更新封面
        updateCover(EditorCoverHelper.CoverMode.DEFAULT_COVER, EditorCoverHelper.COVER_TIMESTAMP, null);
    }

    @Override
    public void onIconClick(View view, int position, Object item) {
    }

    @Override
    public void onClipThumbnailClick(int clipIndex) {
        stopEngine();
        GLog.d(TAG, LogFlag.DL, "onThumbnailClick index = " + clipIndex);
        if (ClickUtil.isDoubleClick()) {
            GLog.w(TAG, LogFlag.DL, "onClipThumbnailClick() isDoubleClick, clipIndex = " + clipIndex);
            return;
        }

        // seek to clicked clip
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if ((timeline != null) && (timeline.getVideoTrack(0) != null)) {
            IClip clickedClip = timeline.getVideoTrack(0).getClip(clipIndex);
            if (clickedClip != null) {
                if ((mContext instanceof EditorActivity) && (clickedClip instanceof IVideoClip)) {
                    ((EditorActivity) mContext).showPipRect((IVideoClip) clickedClip);
                }
            } else {
                GLog.e(TAG, LogFlag.DL, "onClipThumbnailClick: clickedClip = null");
            }
        } else {
            GLog.e(TAG, LogFlag.DL, "onClipThumbnailClick: iTimeline or videoTrack is null");
        }
        if (getUIController() != null) {
            getUIController().changeTimelineState(STATE_EDITOR);
        }
    }

    /**
     * EditorTrimUIController.OnThumbnailListener监听
     */
    @Override
    public void onTransitionClick(int clipIndex, boolean isTail) {
        GLog.d(TAG, LogFlag.DL, "[onTransitionClick] clipIndex " + clipIndex + (isTail ? "right" : "left") + " is clicked");
        if (ClickUtil.isDoubleClick()) {
            GLog.w(TAG, LogFlag.DL, "[onTransitionClick] isDoubleClick, clipIndex = " + clipIndex + (isTail ? "right" : "left"));
            return;
        }
        // 如果栈顶是转场，则更新一下index即可
        EditorBaseState<?> state = mEditorControlView.getEditorStateManager().getCurrentEditorState();
        int index = isTail ? clipIndex : clipIndex - 1;
        if (state instanceof EditorTransitionState) {
            mIsStartSubState = true;
            EditorTransitionState transitionState = (EditorTransitionState) state;
            transitionState.onTransitionClick(index, isTail);
        } else {
            // 进入二级页面时，一级页面不会销毁
            EditorTransitionState transitionState = new EditorTransitionState(mContext, mEditorControlView, index, getWeakTimelineViewModel());
            mIsStartSubState = true;
            mEditorControlView.getEditorStateManager().changeState(transitionState);
            transitionState.setTransitionListener(new EditorTransitionState.TransitionListener() {
                @Override
                public void onPlayPositionChange(long position) {
                    EditorTrimState.this.onPlayPositionChange(position);
                }
            });
        }
    }

    @Override
    public void onClipLongPress(int clipIndex) {
        // 进入二级页面时剪辑不会销毁，避免从剪辑进入的二级页面也响应长按事件
        if (mEditorControlView.getEditorStateManager().getCurrentEditorState() instanceof EditorTrimState) {
            doSortClip(clipIndex);
        }
    }

    @Override
    public void onScrollChanged(long stamp) {
        mEditorControlView.seekTimeline(stamp, 0, false);
    }

    @Override
    public void onActionUp() {
        mEditorControlView.updateFilter();
    }

    @Override
    public void onSelectClipIndexChange(int index) {
        updateUIControlButtonStatusByClipIndex(index);
    }

    @Override
    public void onPlayPositionChange(long currentPosition) {
        if (getUIController() != null) {
            getUIController().setScrollPressed(false);
        }
        if (mIsDoingOperation) {
            return;
        }
        if (getUIController() != null) {
            getUIController().updateTimePositionChanged(currentPosition);
        }
    }

    @Override
    public void onSeekTimelinePosition(long position, long duration) {
        if (getUIController() != null) {
            getUIController().setFunctionEnable(position);
        }
    }

    @Override
    protected EditorTrackScene getEditorTrackScene() {
        return EditorTrackScene.TRIM;
    }

    /**
     * 进入排序
     *
     * @param clipIndex 选中的片段
     */
    private void doSortClip(int clipIndex) {
        if (mEditorEngine == null) {
            GLog.d(TAG, LogFlag.DL, "[doSortClip] mEditorEngine is null");
            return;
        }

        ITimeline iTimeline = mEditorEngine.getCurrentTimeline();
        if (iTimeline == null) {
            GLog.e(TAG, LogFlag.DL, "[doSortClip] iTimeline is null");
            return;
        }

        IVideoTrack iVideoTrack = iTimeline.getVideoTrack(0);
        if (iVideoTrack == null) {
            GLog.e(TAG, LogFlag.DL, "[doSortClip] iVideoTrack is null");
            return;
        }

        // If only one clip, don't show sort state
        int size = iVideoTrack.getClipList().size();
        if ((size <= 1) || (size <= clipIndex)) {
            GLog.d(TAG, LogFlag.DL, "[doSortClip] video clip count is not bigger than clipIndex");
            return;
        }

        GLog.d(TAG, LogFlag.DL, "[doSortClip] clipIndex" + clipIndex);

        IVideoClip iVideoClip = (IVideoClip) iVideoTrack.getClip(clipIndex);
        if (iVideoClip != null) {
            mEditorControlView.seekTimeline(iVideoClip.getInPoint(), 0, true);
        }
        if (iVideoTrack.getUserClipCount() <= 1) {
            GLog.d(TAG, LogFlag.DL, "[doSortClip] getUserClipCount <= 1");
            return;
        }
        EditorBaseState<EditorSortUIController> state = new EditorSortState(mContext, mEditorControlView, clipIndex, mEffectClipResponderList);
        mIsStartSubState = true;
        mEditorControlView.getEditorStateManager().changeState(state);
    }

    @Override
    public void onButtonClick(View view) {
        GLog.d(TAG, "onButtonClick view = " + view);
        onButtonClick(view.getId());
    }

    public void onButtonClick(int id) {
        if (mClickBlocked) {
            return;
        }
        if (id == R.id.videoeditor_trim_segment_layout) {
            if (getUIController() != null) {
                getUIController().changeTimelineState(STATE_PREVIEW);
                GLog.d(TAG, "onButtonClick.montage_cutting");
                VideoEditorTrackerHelper.appendMenuClickData(VIDEO_SPLIT,
                        VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL);
                int clipIndex = getUIController().getCurrentMultiSequenceSelectedIndex();
                doClipCutting();
                if (getWeakTimelineViewModel() != null) {
                    TimelineViewModel timelineViewModel = getWeakTimelineViewModel().get();
                    if (timelineViewModel != null) {
                        timelineViewModel.performClipSelected(0, clipIndex + 1, true);
                    }
                }
            }
        } else if (id == R.id.videoeditor_trim_delete_layout) {
            if (getUIController() != null) {
                IVideoClip videoClip = getUIController().getCurrentClip();
                if (videoClip == null) {
                    return;
                }
                ITimeline timeline = mEditorEngine.getCurrentTimeline();
                if (timeline != null) {
                    IVideoTrack videoTrack = timeline.getVideoTrack(0);
                    if (videoTrack != null) {
                        if (videoTrack.getUserClipCount() <= 1) {
                            getUIController().updateDeleteAndSortButtonStatus(false);
                            return;
                        }
                    }
                }
                getUIController().changeTimelineState(STATE_PREVIEW);
                GLog.d(TAG, "onButtonClick.montage_delete");
                VideoEditorTrackerHelper.appendMenuClickData(VIDEO_DELETE,
                        VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL);
                doClipDelete();
                if (mContext instanceof EditorActivity) {
                    EditorActivity activity = (EditorActivity) mContext;
                    activity.updateVideoSpecInfo();
                    activity.updateBrightness();
                    activity.showPipRect(null);
                }
            }
        } else if (id == R.id.videoeditor_trim_speed_layout) {
            TimelineViewModel timelineViewModel = getTimelineViewModel();
            if (timelineViewModel != null) {
                EditorSpeederState speederState = new EditorSpeederState(
                        mContext,
                        mEditorControlView,
                        timelineViewModel,
                        mEffectClipResponderList
                );
                speederState.setVideoPlaybackListener(this::onPlayPositionChange);
                mEditorControlView.getEditorStateManager().changeState(speederState);
                VideoEditorTrackerHelper.appendMenuClickData(VIDEO_SPEED,
                        VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL);
            }
        } else if (id == R.id.videoeditor_trim_tailor_layout) {
            if (getCurrentClip() != null) {
                final EditorTransformState transformState = new EditorTransformState(mContext, mEditorControlView, getCurrentClip());
                mEditorControlView.getEditorStateManager().changeState(transformState);
                VideoEditorTrackerHelper.appendMenuClickData(VIDEO_ROTATION,
                        VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL);
            }
        } else if (id == R.id.videoeditor_trim_sort_layout) {
            if (getUIController() != null) {
                getUIController().changeTimelineState(STATE_PREVIEW);
                GLog.d(TAG, "onButtonClick.montage_sort");
                int currentClipIndex = getUIController().getCurrentMultiSequenceSelectedIndex();
                doSortClip(currentClipIndex);
                updateZoomInOutFrameLayout();
                VideoEditorTrackerHelper.appendMenuClickData(VIDEO_SORT,
                        VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL);
            }
        } else if ((id == R.id.montage_rotation) || (id == R.id.montage_rotation_layout)) {
            if (getUIController() != null) {
                getUIController().changeTimelineState(STATE_PREVIEW);
                GLog.d(TAG, "onButtonClick.montage_rotation");
                mIsDoingOperation = true;
                doClipRotate();
                mIsDoingOperation = false;
            }
        } else if ((id == R.id.montage_volume) || (id == R.id.montage_volume_layout)) {
            if (getUIController() != null) {
                GLog.d(TAG, "onButtonClick.montage_volume");
                IVideoClip currentClip = getUIController().getCurrentClip();
                if (currentClip == null) {
                    return;
                }
                EditorMusicTrackVolumeState state = new EditorMusicTrackVolumeState(mContext, mEditorControlView, currentClip);
                mEditorControlView.getEditorStateManager().changeState(state);
            }
        } else if (id == R.id.videoeditor_trim_replace_layout) {
            if (getUIController() != null) {
                doClipReplace();
                VideoEditorTrackerHelper.appendMenuClickData(VIDEO_REPLACE,
                        VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL);
            }
        } else {
            GLog.d(TAG, "onButtonClick default");
        }
    }

    /**
     * 获取时间线视图模型实例
     */
    @Nullable
    private TimelineViewModel getTimelineViewModel() {
        WeakReference<TimelineViewModel> timelineViewModelWeakReference = getWeakTimelineViewModel();
        if (timelineViewModelWeakReference == null) {
            GLog.e(TAG, LogFlag.DL, "[getTimelineViewModel] timelineViewModelWeakReference is null");
            return null;
        }
        return timelineViewModelWeakReference.get();
    }

    /**
     * 点击替换
     */
    private void doClipReplace() {
        IVideoClip currentClip = getUIController().getCurrentClip();
        if ((getUIController() == null) || currentClip == null) {
            return;
        }
        stopEngine();
        EditorReplaceHelper.gotoGallerySelection(
                mContext,
                mEditorControlView,
                currentClip,
                new EditorReplaceHelper.ReplaceSelectionListener() {

                    @Override
                    public void onSelectedMediaItem(@NonNull MediaItem mediaItem, @Nullable EditorReplaceEntry replaceEntry) {
                        EditorReplaceHelper.replaceVideoClip(
                                mediaItem,
                                getEditorControlView(),
                                currentClip.getClipIndex(),
                                replaceEntry,
                                mEffectClipResponderList);
                    }
                });
    }

    @Override
    public void stopTimelineViewFling() {
        getUIController().stopFling();
    }

    /**
     * 结束片段调节
     * @param index 调节的片段索引
     * @param isLeftHand 是否为做把手
     * @param trim 剪辑点（起始把手为trimIn，末尾把手为trimOut）
     */
    public void finishClipAdjust(int index, boolean isLeftHand, long trim) {
        if (getUIController() == null) {
            GLog.e(TAG, LogFlag.DL, "[finishClipAdjust] in onHandActionUp: getUIController is null");
            return;
        }
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            GLog.e(TAG, LogFlag.DL, "[finishClipAdjust] in onHandActionUp: timeline is null");
            return;
        }
        IVideoTrack videoTrack = timeline.getVideoTrack(0);
        if (videoTrack == null) {
            return;
        }
        IVideoClip videoClip = getCurrentClipFromIndex(index);
        if (videoClip == null) {
            GLog.e(TAG, LogFlag.DL, "[finishClipAdjust] in onHandActionUp: index is error:   " + index);
            return;
        }

        GLog.d(TAG, LogFlag.DL, "[finishClipAdjust] in onHandActionUp: videoClip inTime:   "
            + videoClip.getInPoint() + " videoClip outTime:" + videoClip.getOutPoint());
        long preTrimIn = videoClip.getTrimIn();
        long preTrimOut = videoClip.getTrimOut();
        long preOutTime = videoClip.getOutPoint();
        double speedOfCurrentClip = videoClip.getSpeed();

        long offset = 0;
        boolean isClipAV = (videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV);
        if (isLeftHand && (isClipAV || videoClip.getIsOlivePhoto())) {
            videoClip.setTrimInPoint(trim, true);
            offset = trim - preTrimIn;
        } else {
            videoClip.setTrimOutPoint(trim, true);
            offset = preTrimOut - trim;
        }

        List<BaseVideoFx> fxList = timeline.getVideoFxs();
        for (int i = fxList.size() - 1; i >= 0; i--) {
            BaseVideoFx fx = fxList.get(i);
            if (fx.getInTime() + MIN_TIME_OF_ITEM > timeline.getDuration()) {
                timeline.removeVideoFx(fx);
            }
        }

        // operate the clips behind the current clip
        changeAfterVideoClipCaptionInAndOutTime(timeline, preOutTime, offset, speedOfCurrentClip);
        ITimeline currentTimeline = mEditorEngine.getCurrentTimeline();
        if (currentTimeline != null) {
            mEditorEngine.restoreByTimeline(currentTimeline, false);
        }
        mOperationHasDone = true;
        // 调整视频片段的特效时长
        updateEffects();
        mUIController.checkMainTrackKeyframe(videoClip);
        if (mOperationHasDone) {
            saveCurrentTimeline(OperationType.VIDEO_ADJUSTMENT);
        }
    }

    private void changeTimelineToChangPip(ITimeline timeline) {
        if (timeline == null) {
            GLog.e(TAG, "changeTimelineToChangPip timeline is null");
            return;
        }
        IVideoTrack mainVideoTrack = timeline.getVideoTrack(0);
        if (mainVideoTrack == null) {
            GLog.e(TAG, "changeTimelineToChangPip mainVideoTrack is null");
            return;
        }
        // 主轨道的长度就应该是时间线的长度
        int count = timeline.getVideoTrackCount();
        if (count <= 1) {
            GLog.e(TAG, "changeTimelineToChangPip count is " + count);
            return;
        }
        long duration = mainVideoTrack.getDuration();
        for (int i = 1; i < count; i++) {
            IVideoTrack videoTrack = timeline.getVideoTrack(i);
            if (videoTrack == null) {
                continue;
            }
            int clipCount = videoTrack.getClipCount();
            for (int j = 0; j < clipCount; j++) {
                IVideoClip videoClip = (IVideoClip) videoTrack.getClip(j);
                if (videoClip != null) {
                    long inPoint = videoClip.getInPoint();
                    if (inPoint >= duration) {
                        videoTrack.removeClip(j, false);
                    } else {
                        long outPoint = videoClip.getOutPoint();
                        if (outPoint > duration) {
                            long trimIn = videoClip.getTrimIn();
                            long trimOut = videoClip.getTrimOut();
                            long offset = outPoint - duration;
                            trimOut -= offset;
                            if (trimOut < (trimIn + EditorEngine.MIN_CLIP_DURATION)) {
                                videoTrack.removeClip(j, false);
                            } else {
                                videoClip.setTrimOutPoint(trimOut, false);
                            }
                        }
                    }
                }

            }
        }
    }

    private void changeAfterVideoClipCaptionInAndOutTime(ITimeline timeline, long preOutTime, long offset, double speed) {
        // this var is used to find the first caption which need to move (that is to find the first caption in the clip which behind the
        // operated clip)
        int startChangeIndex = -1;
        if (timeline != null) {
            timeline.sortCaption();
            List<BaseCaption> captions = timeline.getCaptionList();
            if ((captions != null) && (captions.size() > 0)) {
                for (int i = 0; i < captions.size(); i++) {
                    if (captions.get(i).getInTime() >= preOutTime) {
                        startChangeIndex = i;
                        break;
                    }
                }
            }
        }

        if (offset <= 0) {
            // become longer
            if ((startChangeIndex > -1) && (timeline != null)) {
                timeline.addCaptionInAndOutTime(startChangeIndex, (long) (Math.abs(offset) / speed));
            }
        } else {
            // become shorter
            if ((startChangeIndex > -1) && (timeline != null)) {
                timeline.reduceCaptionInAndOutTime(startChangeIndex, (long) (Math.abs(offset) / speed));
            }
        }
    }

    private void doClipRotate() {
        if (getUIController() == null) {
            GLog.e(TAG, "current ui controller is invalid!");
            return;
        }
        stopEngine();
        IVideoClip videoClip = getCurrentClipFromIndex(getUIController().getCurrentMultiSequenceSelectedIndex());
        if (videoClip == null) {
            GLog.e(TAG, "current edit clip is invalid!");
            return;
        }

        BaseVideoClipEffect clipEffect = IVideoClip.getTransformEffect(videoClip);
        if (clipEffect != null) {
            if ((videoClip.hasKeyframeEffect()) && (videoClip.getKeyFrameList() != null)) {
                for (BaseKeyFrameInfo keyFrame : videoClip.getKeyFrameList()) {
                    keyFrame.setRotationZ(keyFrame.getRotationZ() - VideoUtils.ROTATION_90);
                }
                getUIController().updateKeyframeEffect(videoClip, false);
            } else {
                float rotation = videoClip.getTransFormFloatValue(clipEffect, StreamingConstant.VideoTransform.PARAM_ROTATION);
                rotation -= VideoUtils.ROTATION_90;
                videoClip.setTransFormFloatValue(clipEffect, StreamingConstant.VideoTransform.PARAM_ROTATION, rotation);
            }
        } else {
            int rotation = videoClip.getExtraVideoRotation();
            rotation = (rotation + INT_1) % MAX_DIRECTION_COUNT;
            videoClip.setExtraVideoRotation(rotation);
        }

        videoClip.setPipInitPointFList(null);
        updateBackground(videoClip);
        mEditorEngine.resetMaskEffect(false, 0);
        mOperationHasDone = true;
        seekCurrentTimelinePosition();
        if (mOperationHasDone) {
            saveCurrentTimeline(OperationType.ROTATION);
        }
    }

    private void updateBackground(IVideoClip videoClip) {
        List<BaseVideoClipEffect> videoClipEffects = videoClip.getEffectList();
        if (videoClipEffects != null) {
            for (BaseVideoClipEffect clipEffect : videoClipEffects) {
                if (clipEffect == null) {
                    continue;
                }
                if ((clipEffect.getEffectType() == BaseStoryBoardVideoClipEffect.StoryboardType.TYPE_BACKGROUND) && (clipEffect.isMainEffect())) {
                    changeBackground(videoClip, clipEffect);
                }
            }
        }
    }

    private void changeBackground(IVideoClip videoClip, BaseVideoClipEffect clipEffect) {
        if (!(clipEffect instanceof BaseStoryBoardVideoClipEffect)) {
            return;
        }
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        BaseStoryBoardVideoClipEffect storyBoardEffect = (BaseStoryBoardVideoClipEffect) clipEffect;
        storyBoardEffect.buildEffectParam(storyBoardEffect.getDirPath(),
            storyBoardEffect.getSourceFileName(), storyBoardEffect.getEffectStrength(),
            timeline.getWidth(), timeline.getHeight(),
            videoClip.getWidth(), videoClip.getHeight(),
            videoClip.getDuration(), videoClip.getExtraVideoRotation(),
            storyBoardEffect.getSubType(), videoClip.hasKeyframeEffect());
    }

    private void seekCurrentTimelinePosition() {
        mEditorControlView.seekTimeline(mEditorEngine.getTimelineCurrentPosition(), 0, true);
    }

    private void refreshWhenSelectClipChanged(boolean needStop) {
        GLog.d(TAG, "refreshWhenSelectClipChanged");
        if (needStop) {
            stopEngine();
        }
        IVideoClip videoClip = getCurrentClip();
        if (videoClip == null) {
            GLog.e(TAG, "refreshWhenSelectClipChanged: index is error");
            return;
        }

        getUIController().updateButtonStatusForTailClip(false);
        if (TextUtils.equals(getUIController().getTimelineState(), STATE_EDITOR) && (mContext instanceof EditorActivity)) {
            ((EditorActivity) mContext).showPipRect(videoClip);
        }
    }

    /**
     * 调整视频长短后去更新滤镜和调节的特效时长，因为滤镜和调节需要跟随视频的时长变化而变化
     */
    public void updateEffects() {
        mEffectClipResponderList.forEach((clip) ->
            clip.update(mEditorEngine.getCurrentTimeline())
        );
    }

    private void doClipCutting() {
        IVideoClip videoClip = getCurrentClip();
        if (videoClip == null) {
            GLog.e(TAG, "doClipCutting current edit clip is invalid!");
            return;
        }
        buildTrackVideoData(videoClip.getFilePath(), videoClip.getDuration(), videoClip.getFileDuration());
        stopEngine();
        EditorBaseUIController editorBaseUIController = getUIController();
        if (editorBaseUIController instanceof EditorTrimUIController) {
            EditorTrimUIController editorTrimUIController = (EditorTrimUIController) editorBaseUIController;
            editorTrimUIController.stopFling();
        }

        if (videoClip.getDuration() < MIN_SPLIT_CLIP_DURATION) {
            ToastUtil.showShortToast(mContext.getString(R.string.videoeditor_trim_split_min_duration_text));
            return;
        }

        long currentPosition = mEditorEngine.getTimelineCurrentPosition();
        if (((videoClip.getInPoint() + MIN_CLIP_DURATION) > currentPosition)
            || ((currentPosition + MIN_CLIP_DURATION) >= videoClip.getOutPoint())) {
            ToastUtil.showShortToast(mContext.getString(R.string.videoeditor_trim_split_error_text));
            return;
        }

        IVideoTrack videoTrack = mEditorEngine.getCurrentTimeline().getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "[doClipCutting] videoTrack is null");
            return;
        }
        int clipIndex = videoTrack.getClipIndex(videoClip);
        if (clipIndex < 0) {
            GLog.e(TAG, "can not find current clip index!");
            return;
        }

        List<BaseKeyFrameInfo> keyframeList = new ArrayList<>(videoClip.getKeyFrameList());
        if (keyframeList.size() > 0) {
            videoClip.removeAllKeyFrame();
        }

        //start to cut caption
        long splitPosition = mEditorEngine.getTimelineCurrentPosition();
        long endPosition = videoClip.getOutPoint();
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            return;
        }

        timeline.sortCaption();
        if (!videoTrack.splitClip(clipIndex, splitPosition, timeline.isNeedPicMove())) {
            GLog.e(TAG, "split clip failed for position:" + mEditorEngine.getTimelineCurrentPosition() + " at index:" + clipIndex);
            return;
        }

        //start delete new clip cartoon if exist
        IVideoClip newVideoClip = (IVideoClip) videoTrack.getClip(clipIndex + 1);
        if (newVideoClip != null) {
            List<BaseVideoClipEffect> videoClipEffects = newVideoClip.getEffectList();
            if (videoClipEffects != null) {
                for (BaseVideoClipEffect clipEffect : videoClipEffects) {
                    if (clipEffect == null) {
                        continue;
                    }
                    if (clipEffect.getEffectType() == TYPE_CARTOON_EFFECT) {
                        newVideoClip.removeEffect(clipEffect);
                    }
                }
            }

            updateKeyframeInfo(keyframeList, videoClip, newVideoClip, splitPosition);
        } else {
            GLog.d(TAG, "cut after get new video clip is null");
        }

        //update UI
        updateUIControlButtonStatus();
        mEditorEngine.restoreByTimeline(timeline, false);
        mEditorEngine.notifyTimelineChanged(true);
        if (mContext instanceof EditorActivity) {
            ((EditorActivity) mContext).showPipRect(null);
        }
        mUIController.updateKeyframeEffect(videoClip, false, false, true);
        if (newVideoClip != null) {
            mUIController.updateKeyframeEffect(newVideoClip, true, false, true);
        }

        // 更新特效
        updateEffects();
        saveCurrentTimeline(OperationType.CLIP_SEGMENT);
    }

    private void updateKeyframeInfo(List<BaseKeyFrameInfo> keyframeList, IVideoClip firstClip, IVideoClip secondClip, long splitPosition) {
        if ((firstClip == null) || (secondClip == null)) {
            return;
        }
        if ((keyframeList != null) && (keyframeList.size() > 0)) {
            long firstClipTrimIn = firstClip.getTrimIn();
            if (firstClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
                firstClipTrimIn = firstClip.getImageTrimIn();
            }
            long secondClipTrimIn = secondClip.getTrimIn();
            if (secondClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
                secondClipTrimIn = ClipModel.TRIM_INFINITE;
            }
            secondClip.setImageTrimIn(secondClipTrimIn);
            KeyFrameUtil.updateKeyframeInfo(keyframeList, firstClip, secondClip, splitPosition, firstClipTrimIn, secondClipTrimIn);
        }
    }

    private void doClipDelete() {
        stopEngine();
        EditorBaseUIController editorBaseUIController = getUIController();
        if (editorBaseUIController instanceof EditorTrimUIController) {
            EditorTrimUIController editorTrimUIController = (EditorTrimUIController) editorBaseUIController;
            editorTrimUIController.stopFling();
        }
        IVideoClip videoClip = getCurrentClip();
        if (videoClip == null) {
            GLog.e(TAG, "doClipDelete current edit clip is invalid!");
            return;
        }
        long currentVideoClipInPoint = videoClip.getInPoint();
        long currentVideoClipOutPoint = videoClip.getOutPoint();
        long videoClipDuration = videoClip.getDuration();
        IVideoTrack videoTrack = mEditorEngine.getCurrentTimeline().getVideoTrack(0);
        if (videoTrack == null) {
            return;
        }
        int clipCount = videoTrack.getClipCount();
        if (clipCount <= 1) {
            GLog.d(TAG, "only one clip in track!");
            return;
        }

        int clipIndex = videoTrack.getClipIndex(videoClip);

        //remove selected clip
        videoTrack.removeClip(clipIndex, false);
        VideoParser.Companion.getInstance().removeFilePath(videoClip.getSrcFilePath());

        long seekPosition = 0;
        IVideoClip currentClip = null;
        if (clipIndex >= videoTrack.getClipCount()) {
            currentClip = (IVideoClip) videoTrack.getClip(clipIndex - 1);
            if (currentClip != null) {
                seekPosition = currentClip.getOutPoint() - MIN_SEEK_TOLERANCE;
            }
        } else {
            currentClip = (IVideoClip) videoTrack.getClip(clipIndex);
            if (currentClip != null) {
                seekPosition = currentClip.getInPoint();
            }
        }

        //start to delete caption
        long timelineDuration = mEditorEngine.getCurrentTimeline().getDuration();
        List<BaseCaption> captionList = mEditorEngine.getCurrentTimeline().getCaptionList();
        int indexFirstNotDeleteCaption = -1;
        boolean isContainCaption = false;
        List<BaseCaption> waitDeleteCaptionList = new ArrayList<>();
        for (int i = 0; i < captionList.size(); i++) {
            BaseCaption caption = captionList.get(i);
            if (caption.getInTime() >= timelineDuration) {
                mEditorEngine.removeCaption(caption);
            } else if (caption.getOutTime() > timelineDuration) {
                caption.setOutTime(timelineDuration);
            }

        }

        if (indexFirstNotDeleteCaption == -1) {
            //delete clip not contain AI caption
            for (int i = 0; i < captionList.size(); i++) {
                if (captionList.get(i).getInTime() >= currentVideoClipOutPoint) {
                    indexFirstNotDeleteCaption = i;
                    break;
                }
            }
        }

        //start to delete fx and filter
        List<BaseVideoFx> fxList = mEditorEngine.getCurrentTimeline().getVideoFxs();
        List<BaseVideoFx> curFilterList = mEditorEngine.getCurrentTimeline().getFilterVideoFxs();
        for (int i = fxList.size() - 1; i >= 0; i--) {
            BaseVideoFx videoFx = fxList.get(i);
            if (videoFx == null) {
                continue;
            }
            if (videoFx.getInTime() >= timelineDuration) {
                mEditorEngine.getCurrentTimeline().removeVideoFx(videoFx);
            } else if (videoFx.getOutTime() > timelineDuration) {
                videoFx.setOutTime(timelineDuration);
            }
        }

        for (int i = curFilterList.size() - 1; i >= 0; i--) {
            BaseVideoFx videoFilterFx = curFilterList.get(i);
            if (videoFilterFx == null) {
                continue;
            }
            if (videoFilterFx.getInTime() >= timelineDuration) {
                mEditorEngine.getCurrentTimeline().removeFilterVideoFx(videoFilterFx);
            } else if (videoFilterFx.getOutTime() > timelineDuration) {
                videoFilterFx.setOutTime(timelineDuration);
            }
        }

        ITimeline currentTimeline = mEditorEngine.getCurrentTimeline();
        if (currentTimeline != null) {
            // delete pip
            changeTimelineToChangPip(currentTimeline);
            currentTimeline.reduceCaptionInAndOutTime(indexFirstNotDeleteCaption, videoClipDuration);

            // this code only to delete the upper captions because the lower captions have bean deleted by remove clip
            for (int i = 0; i < waitDeleteCaptionList.size(); i++) {
                BaseCaption captionToDelete = waitDeleteCaptionList.get(i);
                currentTimeline.removeFromCaptionList(captionToDelete);
            }
        }
        mEditorControlView.seekTimeline(seekPosition, 0, false);
        mEditorEngine.notifyTimelineChanged(true);
        getUIController().clearSelectClip();
        updateEffects();
        // refresh UI
        updateUIControlButtonStatus();
        saveCurrentTimeline(OperationType.DELETE_MATERIAL);
        GLog.d(TAG, "doClipDelete clip index:" + clipIndex + " seek position:" + seekPosition);
    }

    public void updateUIControlButtonStatus() {
        if ((mEditorEngine == null) || (mEditorEngine.getCurrentTimeline() == null)) {
            GLog.e(TAG, "updateUIControlButtonStatus current time line is invalid!");
            return;
        }

        IVideoTrack videoTrack = mEditorEngine.getCurrentTimeline().getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "updateUIControlButtonStatus current track is invalid!");
            return;
        }

        boolean enabled = (videoTrack.getUserClipCount() > 1);
        getUIController().updateDeleteAndSortButtonStatus(enabled);
    }

    private void updateUIControlButtonStatusByClipIndex(int clipIndex) {
        if ((mEditorEngine == null) || (mEditorEngine.getCurrentTimeline() == null)) {
            GLog.e(TAG, "updateUIControlButtonStatusByClipIndex current time line is invalid!");
            return;
        }

        IVideoTrack videoTrack = mEditorEngine.getCurrentTimeline().getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "updateUIControlButtonStatusByClipIndex current track is invalid!");
            return;
        }
        IVideoClip clip = (IVideoClip) videoTrack.getClip(clipIndex);
        if (clip == null) {
            GLog.e(TAG, "updateUIControlButtonStatusByClipIndex current clip is invalid!");
            return;
        }
        boolean enabled = (videoTrack.getUserClipCount() > 1);
        getUIController().updateDeleteAndSortButtonStatus(enabled);
    }

    @Nullable
    private IVideoClip getCurrentClip() {
        if (mEditorEngine == null) {
            return null;
        }
        return getUIController().getCurrentClip();
        //        IVideoClip clip = getUIController().getCurrentClip();
        //        if (clip == null) {
        //            ITimeline iTimeline = mEditorEngine.getCurrentTimeline();
        //            if ((iTimeline == null) || (iTimeline.getVideoTrack(0) == null)) {
        //                Debugger.e(TAG, "getCurrentClip: iTimeline or videoTrack is null");
        //                return null;
        //            }
        //            long currentPosition = mEditorEngine.getTimelineCurrentPosition();
        //            return (IVideoClip) iTimeline.getVideoTrack(0).getClipByTimelinePostion(currentPosition);
        //        } else {
        //            return clip;
        //        }
    }

    private void rebuildTimeline() {
        EditorEngine editorEngine = getEditorEngine();
        if (editorEngine == null) {
            return;
        }
        ITimeline timeline = editorEngine.getCurrentTimeline();
        if (timeline != null) {
            editorEngine.restoreByTimeline(timeline, true);
        }
    }

    private IVideoClip getCurrentClipFromIndex(int clipIndex) {
        if (mEditorEngine == null) {
            return null;
        }
        ITimeline iTimeline = mEditorEngine.getCurrentTimeline();
        if ((iTimeline == null) || (iTimeline.getVideoTrack(0) == null)) {
            GLog.e(TAG, "getCurrentClipFromIndex: iTimeline or videoTrack is null");
            return null;
        }
        return (IVideoClip) iTimeline.getVideoTrack(0).getClip(clipIndex);
    }

    @Override
    public void destroy() {
        super.destroy();
        if (mContext instanceof EditorActivity) {
            ((EditorActivity) mContext).showPipRect(null);
        }
    }

    @Override
    public void onPanelClick() {
        if (getUIController() != null) {
            getUIController().changeTimelineState(STATE_PREVIEW);
            if (mContext instanceof EditorActivity) {
                ((EditorActivity) mContext).showPipRect(null);
            }
        }
    }

    @Override
    public void onPipClipSelected(boolean isSelected, IVideoClip videoClip) {
        if (getUIController() == null) {
            return;
        }
        if (isSelected) {
            getUIController().changeTimelineState(STATE_EDITOR);
        } else {
            getUIController().changeTimelineState(STATE_PREVIEW);
        }

        //修复564007 片段选中状态与预览画面状态不一致
        if (getWeakTimelineViewModel() != null) {
            TimelineViewModel timelineViewModel = getWeakTimelineViewModel().get();
            if (timelineViewModel != null) {
                timelineViewModel.performClipSelected(0, (videoClip == null) ? CLEAR_SELECTED_STATE_INDEX : videoClip.getInPoint(), isSelected);
            }
        }
    }

    @Override
    public void updateUndo(SaveInfo saveInfo) {
        updateZoomInOutFrameLayout();
        OperationType operationType = saveInfo.getOperationType();
        if (operationType != null && (getOperationSaveHelper() != null)) {
            String canvasDrag = getOperationSaveHelper().getUndoTips(OperationType.CANVAS_DRAG);
            String canvasRotateZoom = getOperationSaveHelper().getUndoTips(OperationType.CANVAS_ROTATE_ZOOM);
            String operationName = getOperationSaveHelper().getUndoTips(operationType);
            if ((operationName.equals(canvasDrag)) || (operationName.equals(canvasRotateZoom))) {
                updateSelectedClip(saveInfo.getExtra());
            }
        }
        // 更新封面
        if (getTimeline() != null) {
            updateCover(getTimeline().getCoverMode(), getTimeline().getCoverTimestamp(), getTimeline().getCoverFilePath());
        }
    }

    private void updateSelectedClip(String clipTrackIndexInfo) {
        if (TextUtil.isEmpty(clipTrackIndexInfo)) {
            GLog.e(TAG, "updateSelectedClip, clipTrackIndexInfo is empty");
            return;
        }
        String[] pipClipLocation = clipTrackIndexInfo.split(INFO_LINK);
        if ((pipClipLocation != null) && (pipClipLocation.length > 1)) {
            int trackIndex = Integer.parseInt(pipClipLocation[0]);
            int clipIndex = Integer.parseInt(pipClipLocation[1]);
            ITimeline timeline = mEditorEngine.getCurrentTimeline();
            if (timeline == null) {
                GLog.e(TAG, "updateSelectedClip timeline is null");
                return;
            }
            List<IVideoTrack> videoTrackList = (List<IVideoTrack>) timeline.getVideoTrackList();
            if ((videoTrackList == null) || (trackIndex >= videoTrackList.size())) {
                GLog.e(TAG, "updateSelectedClip, videoTrackList is null");
                return;
            }
            IVideoTrack videoTrack = videoTrackList.get(trackIndex);
            if (videoTrack == null) {
                GLog.e(TAG, "updateSelectedClip videoTrack is null");
                return;
            }
            if (clipIndex < videoTrack.getUserClipCount()) {
                IVideoClip videoClip = (IVideoClip) videoTrack.getClip(clipIndex);
                boolean isSelected = (videoClip != null);
                onPipClipSelected(isSelected, videoClip);
                if (mContext instanceof EditorActivity) {
                    ((EditorActivity) mContext).showPipRect(videoClip);
                }
            }
        }
    }

    public void updateZoomInOutFrameLayout() {
        updateTrack(ScrollMode.INVOCATION);
    }

    public void updateTrack(ScrollMode scrollMode) {
        if (mEditorEngine == null) {
            return;
        }
        if (getUIController() == null) {
            return;
        }
        if (mClickBlocked) {
            GLog.w(TAG, LogFlag.DL, "[updateZoomInOutFrameLayout] track is updating.");
            return;
        }
        mClickBlocked = true;
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        getUIController().addVideoView(getClipArrayFromTimeline(timeline), scrollMode);
        if (mContext instanceof EditorActivity) {
            ((EditorActivity) mContext).runOnUiThread(() -> mClickBlocked = false);
        }
    }

    /**
     * 菜单跳转音乐tab
     */
    @Override
    public void onGotoMusic() {
        mEditorControlView.getEditorStateManager().selectMenuItem(R.id.id_editor_preview_music, CLIP_STRONG_STATE_FLAG);
    }

    /**
     * 菜单跳转字幕tab
     */
    public void onGotoCaption() {
        mEditorControlView.getEditorStateManager().selectMenuItem(R.id.id_editor_preview_caption, CLIP_STRONG_STATE_FLAG);
    }

    @Override
    public boolean onBackPressed() {
        return super.onBackPressed();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == EditorActivity.REQUEST_CODE_CLIP_CUTTING) {
                IVideoClip targetClip = getCurrentClip();
                if (targetClip == null) {
                    return;
                }
                saveCurrentTimeline(OperationType.TAILOR);

            }
        }
    }

    @Override
    public List<IEffectClipResponder> getClipResponders() {
        return mEffectClipResponderList;
    }

    /**
     * 通用封面更新
     *
     * @param coverMode      封面模式
     * @param coverTimestamp 封面时间戳
     * @param coverFilePath  封面文件路径
     */
    private void updateCover(EditorCoverHelper.CoverMode coverMode, long coverTimestamp, String coverFilePath) {
        EditorCoverHelper.INSTANCE.coverBitmapJob(mContext, mEditorEngine,
                coverMode, coverTimestamp, coverFilePath, true,
                (coverBitmap, frameTime, filePath) -> {
                    if (coverBitmap == null) {
                        GLog.e(TAG, LogFlag.DL, "coverBitmap is null.");
                        return Unit.INSTANCE;
                    }
                    if ((getWeakTimelineViewModel() != null)) {
                        getWeakTimelineViewModel().get().updateCover(coverBitmap);
                        mEditorEngine.getCurrentTimeline().setCoverBitmap(coverMode, frameTime, filePath);
                    }
                    return Unit.INSTANCE;
                });
    }
}