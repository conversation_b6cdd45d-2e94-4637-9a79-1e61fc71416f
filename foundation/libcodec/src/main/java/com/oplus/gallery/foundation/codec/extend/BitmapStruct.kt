/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BitmapStruct
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/5/30 11:58
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2025/5/30		  1.0		 BitmapStruct
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.extend

import android.graphics.Bitmap
import android.graphics.BitmapFactory

/**
 * 文件扩展信息中解码的byte[]
 * bitmap 的元信息struct 结构
 */
class BitmapStruct(private val byteArray: ByteArray) : ExtendStruct<Bitmap>(byteArray) {
    override fun toData(): Bitmap? {
        return BitmapFactory.decodeByteArray(byteArray, 0, byteArray.size)
    }
}