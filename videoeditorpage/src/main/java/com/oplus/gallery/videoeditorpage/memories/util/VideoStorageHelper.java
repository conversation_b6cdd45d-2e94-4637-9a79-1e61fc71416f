/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - StorageLimitAlert.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/12/22
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/22    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper;
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper;
import com.oplus.gallery.basebiz.helper.StorageTipsHelper;
import com.oplus.gallery.framework.abilities.caching.CacheOperation;
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory;
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions;
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.Dir;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment.StorageType;

public final class VideoStorageHelper {

    private static final String TAG = "VideoStorageHelper";
    private static final int NO_ID = -1;

    private static File sSaveMemoriesDir = null;
    private static File sSaveMemoriesSdDir = null;
    private static File sSaveVideoDir = null;
    private static File sSaveVideoSdDir = null;

    private VideoStorageHelper() {

    }

    public enum VideoType {
        MEMORIES,
        VIDEO_EDITOR
    }

    public static File checkStorageEnough(Context context, VideoType type, long storageLimit, String relativePath, int tipRes) {
        checkSaveDir(type, StorageType.PHONE_STORAGE, relativePath);
        File result = (type == VideoType.MEMORIES) ? sSaveMemoriesDir : sSaveVideoDir;
        // check internal storage enough or not
        boolean isFull = !StorageLimitHelper.hasEnoughStorageSpace(StorageType.PHONE_STORAGE, storageLimit);
        if (isFull) {
            boolean hasExternal = OplusEnvironment.isExternalMounted();
            if (!hasExternal) {
                if (tipRes != NO_ID) {
                    GLog.w(TAG, "checkStorageEnough show phone storage no space dialog.");
                    StorageTipsHelper.show(context, StorageType.PHONE_STORAGE, StorageLimitHelper.State.NO_SPACE, tipRes);
                } else {
                    GLog.w(TAG, "checkStorageEnough phone storage no space");
                }
                return null;
            }
            checkSaveDir(type, StorageType.SDCARD_STORAGE, relativePath);
            result = (type == VideoType.MEMORIES) ? sSaveMemoriesSdDir : sSaveVideoSdDir;
            // check external storage enough or not
            isFull = !StorageLimitHelper.hasEnoughStorageSpace(StorageType.SDCARD_STORAGE, storageLimit);
            if (isFull) {
                if (tipRes != NO_ID) {
                    GLog.w(TAG, "checkStorageEnough show sdcard storage no space dialog.");
                    StorageTipsHelper.show(context, StorageType.SDCARD_STORAGE, StorageLimitHelper.State.NO_SPACE, tipRes);
                } else {
                    GLog.w(TAG, "checkStorageEnough sdcard storage no space");
                }
                return null;
            }
        }
        return result;
    }

    public static File checkStorageEnough(Context context, VideoType type, long storageLimit, String relativePath) {
        return checkStorageEnough(context, type, storageLimit, relativePath, NO_ID);
    }

    public static void deleteTemporaryVideoFile(Context context) {
        deleteVideoEditTempFile(context);
        deleteMemoriesTempFile(context);
    }

    private static void deleteVideoEditTempFile(Context context) {
        String temporaryVideoName = VideoEditorHelper.getStringPref(context, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF, null);
        String temporaryVideoDir = VideoEditorHelper.getStringPref(context, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_DIR_PREF, null);
        String temporaryVideoUri = VideoEditorHelper.getStringPref(context, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_URI_PREF, null);
        if (!TextUtils.isEmpty(temporaryVideoName) && !TextUtils.isEmpty(temporaryVideoDir)) {
            try {
                File temporaryFile = new File(temporaryVideoDir, temporaryVideoName);
                DeleteFileRequest deleteFileRequest = new DeleteFileRequest.Builder()
                        .setFile(temporaryFile)
                        .setUri(TextUtils.isEmpty(temporaryVideoUri) ? null : Uri.parse(temporaryVideoUri))
                        .setImage(false)
                        .builder();
                boolean result = FileAccessManager.getInstance().delete(ContextGetter.context, deleteFileRequest);
                if (result) {
                    GLog.d(TAG, "deleteTemporaryVideoFile found and delete success");
                    VideoEditorHelper.setStringPref(context, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF, null);
                    VideoEditorHelper.setStringPref(context, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_DIR_PREF, null);
                    VideoEditorHelper.setStringPref(context, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_URI_PREF, null);
                } else {
                    GLog.w(TAG, "deleteTemporaryVideoFile, delete temporary file failed. temporaryFile = " + temporaryFile);
                }
            } catch (Exception e) {
                GLog.w(TAG, "deleteTemporaryVideoFile, e: ", e);
            }
        }
    }

    private static void deleteMemoriesTempFile(Context context) {
        checkSaveDir(VideoType.MEMORIES, StorageType.PHONE_STORAGE, Dir.getCAMERA().getRelativePath());
        final String name = VideoEditorHelper.getStringPref(context, VideoEditorHelper.KEY_MEMORIES_VIDEO_PREF, null);
        final String temporaryVideoUri = VideoEditorHelper.getStringPref(context, VideoEditorHelper.KEY_MEMORIES_VIDEO_URI_PREF, null);
        if ((sSaveMemoriesDir != null) && !TextUtils.isEmpty(name)) {
            try {
                File temporaryFile = new File(sSaveMemoriesDir, name);
                DeleteFileRequest deleteFileRequest = new DeleteFileRequest.Builder()
                        .setFile(temporaryFile)
                        .setUri(TextUtils.isEmpty(temporaryVideoUri) ? null : Uri.parse(temporaryVideoUri))
                        .setImage(false)
                        .builder();
                boolean result = FileAccessManager.getInstance().delete(ContextGetter.context, deleteFileRequest);
                if (result) {
                    GLog.d(TAG, "deleteMemoriesTempFile found and delete success");
                    VideoEditorHelper.setStringPref(context, VideoEditorHelper.KEY_MEMORIES_VIDEO_PREF, null);
                    VideoEditorHelper.setStringPref(context, VideoEditorHelper.KEY_MEMORIES_VIDEO_URI_PREF, null);
                } else {
                    GLog.w(TAG, "deleteMemoriesTempFile, delete temporary file failed. temporaryFile = " + temporaryFile);
                }
            } catch (Exception e) {
                GLog.w(TAG, "deleteMemoriesTempFile, e: ", e);
            }
        }
    }

    public static void deleteOldVideoFile(Context context, VideoType type) {
        deleteOldVideoFile(context, type, null);
    }

    public static void deleteOldVideoFile(Context context, VideoType type, String oldName) {
        String oldVideoName = oldName;
        if (TextUtils.isEmpty(oldName)) {
            oldVideoName = VideoEditorHelper.getStringPref(context,
                    (type == VideoType.MEMORIES)
                            ? VideoEditorHelper.KEY_MEMORIES_VIDEO_PREF
                            : VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF,
                    null);
        }
        GLog.d(TAG, "deleteOldVideoFile1, oldVideoName = " + oldVideoName);
        if (!TextUtils.isEmpty(oldVideoName)) {
            try {
                File oldFile = new File((type == VideoType.MEMORIES) ? sSaveMemoriesDir : sSaveVideoDir, oldVideoName);
                if (oldFile.exists()) {
                    GLog.d(TAG, "deleteOldVideoFile1, delete oldFile = " + oldFile);
                    boolean result = oldFile.delete();
                    if (!result) {
                        GLog.w(TAG, "deleteOldVideoFile1, delete old file failed. oldFile = " + oldFile);
                    }
                    return;
                }
                oldFile = new File((type == VideoType.MEMORIES) ? sSaveMemoriesSdDir : sSaveVideoSdDir, oldVideoName);
                if (oldFile.exists()) {
                    GLog.d(TAG, "deleteOldVideoFile2, delete oldFile = " + oldFile);
                    boolean result = oldFile.delete();
                    if (!result) {
                        GLog.w(TAG, "deleteOldVideoFile2, delete old file failed. oldFile = " + oldFile);
                    }
                }
            } catch (Exception e) {
                GLog.w(TAG, "deleteOldVideoFile, e: ", e);
            }
        }
    }

    /**
     * 创建保存回忆视频和编辑视频的目录
     *
     * @param videoType        视频类型，表示回忆还是视频编辑
     * @param type             存储类型，表示手机存储还是外置存储卡
     * @param saveRelativePath 保存的相对路径，如果传null，则保存到根目录
     */
    private static void checkSaveDir(VideoType videoType, StorageType type, String saveRelativePath) {
        switch (videoType) {
            case MEMORIES:
                if (StorageType.PHONE_STORAGE.equals(type)) {
                    if (sSaveMemoriesDir == null) {
                        sSaveMemoriesDir = FileOperationUtils.createDir(type, saveRelativePath);
                    }
                } else {
                    if (sSaveMemoriesSdDir == null) {
                        sSaveMemoriesSdDir = FileOperationUtils.createDir(type, saveRelativePath);
                    }
                }
                break;

            case VIDEO_EDITOR:
                if (StorageType.PHONE_STORAGE.equals(type)) {
                    if (saveRelativePath != null) {
                        sSaveVideoDir = FileOperationUtils.createDir(type, saveRelativePath);
                    } else if (sSaveVideoDir == null) {
                        sSaveVideoDir = FileOperationUtils.createDir(type, null);
                    }
                } else {
                    if (saveRelativePath != null) {
                        sSaveVideoSdDir = FileOperationUtils.createDir(type, saveRelativePath);
                    } else if (sSaveVideoSdDir == null) {
                        sSaveVideoSdDir = FileOperationUtils.createDir(type, null);
                    }
                }
                break;

            default:
                break;
        }
    }

    /**
     * 加载一张用于页面过渡的图片
     * @return 过渡图bitmap
     */
    public static Bitmap decodeTransitionBitmap(Context context, Uri uri) {
        MediaItem mediaItem = LocalMediaDataHelper.preloadMediaItem(uri);
        if (mediaItem == null) {
            GLog.e(TAG, "[decodeTransitionBitmap] mediaItem is null. decode failed. black flash may happen in photo page.");
            return null;
        }

        ResourceKey resourceKey = ResourceKeyFactory.createResourceKey(mediaItem);
        if (resourceKey == null) {
            GLog.e(TAG, "[decodeTransitionBitmap] resourceKey is null. decode failed. black flash may happen in photo page.");
            return null;
        }

        ResourceGetOptions options = new ResourceGetOptions();
        options.setInThumbnailType(ThumbnailSizeUtils.getFullThumbnailKey());
        options.setInCacheOperation(CacheOperation.ReadWriteAllCache.INSTANCE);

        Bitmap transBitmap = null;
        try (IResourcingAbility ability = ((GalleryApplication) context.getApplicationContext()).getAppAbility(IResourcingAbility.class)) {
            if (ability == null) {
                GLog.e(TAG, "[decodeTransitionBitmap] get IResourcingAbility failed. black flash may happen in photo page.");
                return null;
            }
            ImageResult<Bitmap> result = ability.requestBitmap(resourceKey, options, null, null);

            if (result == null) {
                GLog.e(TAG, "[decodeTransitionBitmap] decode result is null?? black flash may happen in photo page.");
                return null;
            }
            transBitmap = result.getResult();

        } catch (Exception ex) {
            GLog.e(TAG, "[decodeTransitionBitmap] decode transition bitmap failed. black flash may happen in photo page.", ex);
            return null;
        }

        return transBitmap;
    }

    public static File getSaveVideoDir() {
        return sSaveVideoDir;
    }

    public static File getSaveMemoriesDir() {
        return sSaveMemoriesDir;
    }
}