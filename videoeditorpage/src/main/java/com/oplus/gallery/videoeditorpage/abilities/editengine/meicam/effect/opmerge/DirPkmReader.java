/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - DirPkmReader.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.opmerge;

import android.content.Context;
import android.content.res.AssetManager;
import android.opengl.ETC1;
import android.opengl.ETC1Util;

import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.utlis.FileUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.file.Files;
import java.nio.file.Paths;

public class DirPkmReader {
    private static final String TAG = "DirPkmReader";
    private static final String FILE_INDEX_START = "00000";
    private static final String FILE_END = ".pkm";
    private static final String FILE_ALPHA_END = "_alpha.pkm";

    private static final int REPEAT_TYPE_NONE = 0;
    private static final int REPEAT_TYPE_REPEAT = 1;
    private static final int REPEAT_TYPE_LAST_FRAME = 2;

    private int mFramePkmNumber;
    private int mFrameCount;
    private static final long FRAME_SHOW_TIME = 1000000L / 30;
    private ByteBuffer mHeaderBuffer;
    private int mWidth;
    private int mHeight;
    private AssetManager mAssetManager;
    private String mDir;
    private boolean mIsInAssets = false;
    private Context mContext;
    private int mEtcTextureSize = 0;
    private boolean mIsSameSize;
    private ByteBuffer mDataBuffer;
    private byte[] mIoBuffer = new byte[FileUtils.READ_FILE_BUFFER_SIZE];

    /**
     * @param context
     * @param dir        pkm dictionary
     * @param isSameSize is the pkmes are same width and height
     */
    public DirPkmReader(Context context, String dir, boolean isSameSize, int framePkmNumber) {
        String[] pkmFileNameList = getPkmFileNameList(context, dir);
        mFramePkmNumber = framePkmNumber;
        if (pkmFileNameList == null) {
            GLog.e(TAG, "DirPkmReader,the dir is null !");
            return;
        }
        mFrameCount = pkmFileNameList.length / mFramePkmNumber;
        mContext = context;
        mIsSameSize = isSameSize;
        getDir(dir);
        if (mIsSameSize) {
            getPkmSize(getPkmInputStream(0, false));
        }
    }

    public void getDir(String path) {
        if (FileUtils.isFileInAssets(path)) {
            mDir = path.substring(FileUtils.ASSETS_HEAD_LENGTH);
            mIsInAssets = true;
            mAssetManager = mContext.getAssets();
        } else {
            mDir = path;
        }
    }

    public static String[] getPkmFileNameList(Context context, String path) {
        if (FileUtils.isFileInAssets(path)) {
            String dir = path.substring(FileUtils.ASSETS_HEAD_LENGTH);
            try {
                String[] fileList = context.getAssets().list(dir);
                return fileList;
            } catch (IOException e) {
                GLog.e(TAG, LogFlag.DL, "getPkmFileNameList, fail:" + e.getMessage());
            }
        } else {
            File file = new File(path);
            String[] fileList = file.list();
            return fileList;
        }
        return null;
    }

    private String getFilePath(int index, boolean isAlpha) {
        String indexStr = String.valueOf(index);
        int len = indexStr.length();
        int size = FILE_INDEX_START.length();
        String start = FILE_INDEX_START;
        if (isAlpha) {
            return mDir + start.substring(0, size - len) + indexStr + FILE_ALPHA_END;
        }
        return mDir + start.substring(0, size - len) + indexStr + FILE_END;
    }

    private InputStream getPkmInputStream(int index, boolean isAlpha) {
        InputStream fileInputStream;
        try {
            if (mIsInAssets) {
                fileInputStream = mAssetManager.open(getFilePath(index, isAlpha));
                return fileInputStream;
            } else {
                fileInputStream = Files.newInputStream(Paths.get(getFilePath(index, isAlpha)));
                return fileInputStream;
            }
        } catch (IOException e) {
            GLog.e(TAG, LogFlag.DL, "getPkmInputStream, fail:" + e.getMessage());
        }
        return null;
    }

    public ETC1Util.ETC1Texture[] getETC1Textures(long timeTemp, int repeatType) {
        int size = mFrameCount;
        int index = (int) (timeTemp / FRAME_SHOW_TIME);
        if (repeatType == REPEAT_TYPE_NONE) {
            if (index >= size) {
                return null;
            }
        } else if (repeatType == REPEAT_TYPE_REPEAT) {
            index = index % size;
        } else if (repeatType == REPEAT_TYPE_LAST_FRAME) {
            index = (index >= size) ? size - 1 : index;
        }
        InputStream fileInputStream = getPkmInputStream(index, false);
        InputStream fileInputStreamAlpha = getPkmInputStream(index, true);
        ETC1Util.ETC1Texture texture, textureAlpha;
        if (mIsSameSize) {
            texture = readTexture(fileInputStream);
            textureAlpha = readTexture(fileInputStreamAlpha);
        } else {
            texture = createTexture(fileInputStream);
            textureAlpha = createTexture(fileInputStreamAlpha);
        }
        return new ETC1Util.ETC1Texture[]{texture, textureAlpha};
    }

    public ETC1Util.ETC1Texture getETC1Texture(long timeTemp, int repeatType) {
        int size = mFrameCount;
        int index = (int) (timeTemp / FRAME_SHOW_TIME);

        if (repeatType == REPEAT_TYPE_NONE) {
            if (index >= size) {
                return null;
            }
        } else if (repeatType == REPEAT_TYPE_REPEAT) {
            index = index % size;
        } else if (repeatType == REPEAT_TYPE_LAST_FRAME) {
            index = (index >= size) ? size - 1 : index;
        }

        InputStream fileInputStream = getPkmInputStream(index, false);
        ETC1Util.ETC1Texture texture, textureAlpha;
        if (mIsSameSize) {
            texture = readTexture(fileInputStream);
        } else {
            texture = createTexture(fileInputStream);
        }
        return texture;
    }

    /**
     * @param inputStream if the pkms has the same size ,we can  get size in constructor,
     *                    if difference size ,we should use  createTexture
     * @throws IOException
     */
    private void getPkmSize(InputStream inputStream) {
        if (inputStream == null) {
            GLog.e(TAG, "getPkmSize,failed the inputStream is null");
            return;
        }

        byte[] ioBuffer = new byte[FileUtils.READ_FILE_BUFFER_SIZE];
        try {
            if (inputStream.read(ioBuffer, 0, ETC1.ETC_PKM_HEADER_SIZE) != ETC1.ETC_PKM_HEADER_SIZE) {
                GLog.e(TAG, "getPkmSize, Unable to read PKM file header.");
            }
            if (mHeaderBuffer == null) {
                mHeaderBuffer = ByteBuffer.allocateDirect(ETC1.ETC_PKM_HEADER_SIZE)
                    .order(ByteOrder.nativeOrder());
            }
            mHeaderBuffer.put(ioBuffer, 0, ETC1.ETC_PKM_HEADER_SIZE).position(0);
            if (!ETC1.isValid(mHeaderBuffer)) {
                GLog.e(TAG, "getPkmSize,mHeaderBuffer is not valid");
                throw new IOException("Not a PKM file.");
            }
            mWidth = ETC1.getWidth(mHeaderBuffer);
            mHeight = ETC1.getHeight(mHeaderBuffer);
            mEtcTextureSize = ETC1.getEncodedDataSize(mWidth, mHeight);
            inputStream.close();
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getPkmSize, fail:" + e.getMessage());
        }
    }

    /**
     * we should getPkmSize() first to init size
     *
     * @param input
     * @return
     * @throws IOException
     */
    private ETC1Util.ETC1Texture readTexture(InputStream input) {
        if (mEtcTextureSize == 0) {
            GLog.e(TAG, "readTexture,the mEtcTextureSize is 0 , please getPkmSize first !");
            return null;
        }
        if (mDataBuffer == null) {
            mDataBuffer = ByteBuffer.allocateDirect(mEtcTextureSize).order(ByteOrder.nativeOrder());
        }
        int len;
        try {
            if (input.read(mIoBuffer, 0, ETC1.ETC_PKM_HEADER_SIZE) != ETC1.ETC_PKM_HEADER_SIZE) {
                GLog.e(TAG, "readTexture,Unable to read PKM file header.");
            }
            while ((len = input.read(mIoBuffer)) != -1) {
                mDataBuffer.put(mIoBuffer, 0, len);

            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "readTexture, fail:" + e.getMessage());
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    GLog.e(TAG, LogFlag.DL, "readTexture, fail:" + e.getMessage());
                }
            }
        }

        mDataBuffer.position(0);
        return new ETC1Util.ETC1Texture(mWidth, mHeight, mDataBuffer);
    }

    private ETC1Util.ETC1Texture createTexture(InputStream input) {
        int width;
        int height;
        byte[] ioBuffer = new byte[FileUtils.READ_FILE_BUFFER_SIZE];
        try {
            if (input.read(ioBuffer, 0, ETC1.ETC_PKM_HEADER_SIZE) != ETC1.ETC_PKM_HEADER_SIZE) {
                throw new IOException("Unable to read PKM file header.");
            }
            if (mHeaderBuffer == null) {
                mHeaderBuffer = ByteBuffer.allocateDirect(ETC1.ETC_PKM_HEADER_SIZE)
                    .order(ByteOrder.nativeOrder());
            }
            mHeaderBuffer.put(ioBuffer, 0, ETC1.ETC_PKM_HEADER_SIZE).position(0);
            if (!ETC1.isValid(mHeaderBuffer)) {
                throw new IOException("Not a PKM file.");
            }
            width = ETC1.getWidth(mHeaderBuffer);
            height = ETC1.getHeight(mHeaderBuffer);

            int encodedSize = ETC1.getEncodedDataSize(width, height);
            ByteBuffer dataBuffer = ByteBuffer.allocateDirect(encodedSize).order(ByteOrder.nativeOrder());
            int len;
            while ((len = input.read(ioBuffer)) != -1) {
                dataBuffer.put(ioBuffer, 0, len);
            }
            dataBuffer.position(0);
            return new ETC1Util.ETC1Texture(width, height, dataBuffer);
        } catch (IOException e) {
            GLog.e(TAG, LogFlag.DL, "createTexture, fail:" + e.getMessage());
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    GLog.e(TAG, LogFlag.DL, "createTexture, fail:" + e.getMessage());
                }
            }
        }
        return null;
    }
}
