/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuSectionImpl.kt
 ** Description : 大图页面 - 菜单栏页面切片
 ** Version     : 1.0
 ** Date        : 2021/11/23
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2021/11/23  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.photomenu

import android.content.Intent
import android.content.res.Resources
import android.graphics.Rect
import android.graphics.RectF
import android.util.Size
import android.view.Display
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.business.renderer.olive.OLiveState
import com.oplus.gallery.business_lib.helper.PhotoDataHelper
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.ui.helper.BackgroundBlurUtils
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.isValid
import com.oplus.gallery.foundation.util.ext.minLen
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.isolatedlayer.PhotoBottomMenu
import com.oplus.gallery.photo_page.ui.isolatedlayer.PhotoTopMenu
import com.oplus.gallery.photo_page.ui.section.pagedecoration.IBubbleWindow
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoBottomMenuDecoration
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoBubbleWindowDecoration
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoTopMenuDecoration
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuSection.Companion.MAX_WINDOW_INSET_CALL_TIMES_ON_ROTATE
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuSection.Companion.MAX_WINDOW_WIDTH_SHOW_BOTTOM_MENU
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.ui.theme.PhotoPageTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataDistinctId
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.details.DetailsPanelTransitionStatus
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuExecutor
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState
import com.oplus.gallery.photopager.animationcontrol.AnimationPropertyProvider
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.app.AppConstants
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.system.measureTimeMillis

/**
 * 大图页菜单页面切片。
 * 菜单可见逻辑：
 * - 顶部菜单： 顶部菜单一直允许显示。
 * - 底部菜单： 底部菜单会根据屏幕宽高变化，满足条件时才允许显示。
 */
@ExperimentalUnsignedTypes
internal class PhotoMenuSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
    photoTopMenu: PhotoTopMenu,
    photoBottomMenu: PhotoBottomMenu,
    private val photoMenuExecutor: PhotoMenuExecutor,
) : AbstractPhotoSection(sectionPage) {

    private val isShowedOnDefaultDisplay: Boolean
        get() = viewModel.inputArguments.features.value?.isShowedOnDefaultDisplay ?: true

    /**
     * 大图页菜单的提示语，只允许同时存在一个提示，因此上下菜单需要共用同一个提示。
     */
    private val textTips: IPhotoMenuTextTips by lazy {
        PhotoMenuTextTips(
            context = sectionPage.pageInstance.requireContext(),
            allowShowTips = {
                val photoDecorationVisibility = viewModel.pageManagement.photoDecorationState.value?.shouldShow ?: false
                val menuActionExecuting = viewModel.menuControl.menuActionExecuting.value ?: false
                // 只有当装饰器可见，且没有菜单项在执行时，才允许展示文本提示
                photoDecorationVisibility && menuActionExecuting.not()
            }
        )
    }

    /**
     * 顶部菜单
     */
    private val topMenu: PhotoTopMenuDecoration by lazy {
        PhotoTopMenuDecoration(
            sectionPage = sectionPage,
            isShowAllMenuItems = { couldEnableBottomMenu().not() },
            photoTopMenu = photoTopMenu,
            textTips = textTips,
            onItemClicked = { action ->
                sectionPage.pageViewModel.menuControl.notifyMenuItemClicked(action)
            },
            onItemLongClickUp = { action ->
                sectionPage.pageViewModel.menuControl.notifyMenuItemClicked(action, isLongClicked = true)
            },
            photoMenuExecutor = photoMenuExecutor
        )
    }

    /**
     * 底部菜单
     */
    private val bottomMenu: PhotoBottomMenuDecoration by lazy {
        PhotoBottomMenuDecoration(
            sectionPage = sectionPage,
            photoBottomMenu = photoBottomMenu,
            textTips = textTips,
            onItemClicked = { action ->
                sectionPage.pageViewModel.menuControl.notifyMenuItemClicked(action)
            },
            photoMenuExecutor = photoMenuExecutor
        )
    }

    /**
     * 气泡弹窗
     */
    private val bubbleWindow: IBubbleWindow by lazy { PhotoBubbleWindowDecoration(sectionPage) }

    /**
     * 当前大图是否已经达到稳定状态
     */
    private val isPagePresented
        get() = viewModel.pageManagement.photoPageTransitionState.value == PageTransitionState.PRESENTED

    /**
     * 大图section
     */
    private val pagerSection: PhotoPagerSection? get() = sectionPage.pageInstance.requireSection()

    /**
     * 记录上一次焦点数据ID
     */
    private var lastFocusSlotDataId: String? = null

    /**
     * 当前需要推迟菜单的显示，几个条件全部要满足：
     * 1. 当前机型需要推迟菜单UI加载
     * 2. 入场动画还在跑，没有结束
     * 3. 菜单UI加载还未被允许
     */
    private val needPostponeMenuInflateNow: Boolean
        get() {
            return (viewModel.menuControl.enablePostponeMenuInflate)
                    && (viewModel.menuControl.couldInflateMenuIfPostponed.not())
                    && (viewModel.pageManagement.photoPageTransitionState.value?.isPresented() != true)
        }

    /**
     * 大图滑动监听。
     * - 产品设计要求，滑动时隐藏文字提示，停止之后恢复显示
     */
    private val onPageChangeCallback = object : ViewPager.OnPageChangeCallback() {
        private var draggingStartFocusSlot: Int? = null
        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
            when (state) {
                ViewPager.SCROLL_STATE_DRAGGING -> {
                    // 记录滑动开始时的焦点
                    draggingStartFocusSlot = viewModel.dataLoading.focusSlot
                    textTips.temporaryDismiss()
                }

                ViewPager.SCROLL_STATE_IDLE -> {
                    // 滑动结束时，如果焦点没有改变，主动恢复提示，如果改变了，则由菜单重新刷新提示
                    if (viewModel.dataLoading.focusSlot == draggingStartFocusSlot) {
                        textTips.restoreTemporaryDismiss()
                    }
                }

                else -> Unit
            }
        }
    }

    /**
     * 旋转时判断是否需要更新菜单栏样式的数据
     */
    private val rotationDependData = RotationDependData()

    /**
     * 记录当前 slot 的触摸事件的位置，用于将 click 事件筛除。
     * 此处初始化的值无意义，会在 DOWN 事件中重新赋值。
     *
     * @see slotDragListener
     */
    private var preMovePosition: Pair<Float, Float> = Pair(-AppConstants.Number.NUMBER_1f, -AppConstants.Number.NUMBER_1f)

    /**
     * 在半沉浸模式下监听 slot 图是否被 drag
     */
    private val slotDragListener: (slot: Int, event: MotionEvent) -> Boolean = ::onSlotTouchWhileHalfImmersive

    /**
     * 半沉浸模式的图片顶部位置阈值，图片顶部高于此值时进入半沉浸模式（图片放大过）
     */
    private var halfImmersiveTopThreshold: Int = 0

    /**
     * [onViewCreated] 时传入的 view
     */
    private var rootView: View? = null

    /**
     * 会在下一次 diffedFocusViewDataDistinctId 被调用，调用后会移除。
     */
    private var doOnNextDiffedFocusIdEvent: (() -> Unit)? = null

    /**
     * focus 被删除监听，
     * 当被删除时设置一个一次性的更新事件，在删除完成 focus 变更时执行一次。
     */
    private val focusDeletedListener = {
        doOnNextDiffedFocusIdEvent = {
            updateMenuTheme(UPDATE_MENU_THEME_DELETE, true)
        }
    }

    /**
     * 缓存上次 [calcFocusAabbAndZoom] 的结果
     */
    private var focusAabbAndZoomCache: Pair<RectF, Boolean>? = null

    /**
     * 强制使用记录上一次计算的 focus Aabb 和是否放大过，在进入详情阻断后退出时，使用此值进行计算
     * 会在进入详情动画开始前赋值（[focusAabbAndZoomCache]），在检测到动画完成时置空
     */
    private var forceFocusAabbAndZoom: Pair<RectF, Boolean>? = null

    private val touchSlop: Int = context?.let {
        ViewConfiguration.get(it).scaledTouchSlop
    } ?: 1

    /////////////////////// 以下为内部类定义 ////////////////////////

    override fun onCreate() {
        super.onCreate()
        viewModel.menuControl.setupMenuExecutor(photoMenuExecutor)
    }

    override fun onViewCreated(view: View) {
        super.onViewCreated(view)
        rootView = view
        pagerSection?.registerOnPageChangeCallback(onPageChangeCallback)
        pagerSection?.registerFocusSlotRemovedListener(focusDeletedListener)
    }

    override fun onStart() {
        super.onStart()
        subscribeLiveDataFromViewModel()
    }

    override fun onWindowInsetsChanged(windowInsets: WindowInsetsCompat) {
        super.onWindowInsetsChanged(windowInsets)
        bubbleWindow.onWindowInsetsChanged()
        updateMenuThemeByScreenRotateIfNeed()
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)

        updateMenuTheme(UPDATE_MENU_THEME_SCREEN_MODE, false)
        if (couldShowBottomMenu()) {
            bottomMenu.onAppUiStateChanged(config)
        }
        if (couldShowTopMenu()) {
            /**
             * 横竖屏切换后，顶部菜单会重新创建刷新。
             * 需要让textTips的刷新时机，在顶部菜单重建、刷新之后，避免显示位置错乱。
             */
            topMenu.doOnMenuPrepared {
                textTips.invalidate(forceInvalidate = true)
            }
            topMenu.onAppUiStateChanged(config)
        }
        refreshMenuVisibility()
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        pagerSection?.unregisterOnPageChangeCallback(onPageChangeCallback)
        pagerSection?.unregisterRemovableUninterruptedTouchEventListener(slotDragListener)
        pagerSection?.unregisterFocusSlotRemovedListener(focusDeletedListener)
        rootView = null
    }

    override fun onDestroy() {
        super.onDestroy()
        textTips.destroy()
        systemBarController.showStatusBar()
    }

    override fun onResponsePageResult(requestCode: Int, resultCode: Int, data: Intent?) {
        viewModel.menuControl.notifyMenuHandleResult(requestCode, resultCode, data)
    }

    /**
     * 传入的逻辑将在菜单创建刷新完成后执行。
     * 执行时机：底部菜单创建、刷新完成后。业务方需要在底部菜单创建前调用，否则不会生效
     */
    internal fun doOnBottomMenuPrepared(action: () -> Unit) {
        bottomMenu.doOnMenuPrepared(action)
    }

    /**
     * 更新图片顶部位置的阈值，如果图片的 Top 超过此阈值会进入半沉浸模式（图片放大过）。
     * 目前根据标签的底部位置来更新。
     */
    internal fun updateHalfImmersiveTopThreshold(threshold: Int) {
        halfImmersiveTopThreshold = threshold
    }

    /**
     * 是否需要使用半沉浸模式：
     * 1. 需要当前 menuPresent 支持
     * 2. 图片/视频的顶部位置高于角标的底部位置
     *
     * 使用场景：
     * 1. 判断菜单主题样式
     * 2. 判断是否显示半沉浸式的背景移轴模糊
     * 3. 判断缩图轴显示与隐藏
     * 4. 判断系统状态栏颜色
     *
     * @return Pair(顶部菜单是否半沉浸模式，底部菜单是否半沉浸模式)
     */
    internal fun shouldUseHalfImmersiveMode(): Pair<Boolean, Boolean> {
        val (aabb, zoomed) = calcFocusAabbAndZoom().getOrLog(TAG, "calcFocusAabbAndZoom is null") ?: return Pair(false, false)
        return shouldUseHalfImmersiveMode(aabb, zoomed)
    }

    /**
     * 图片是否在上下菜单中间
     *
     * @return
     */
    internal fun isPhotoTopBelowMenu(): Boolean {
        val (aabb, _) = calcFocusAabbAndZoom().getOrLog(TAG, "[subscribeLiveDataFromViewModel] calcFocusAabbAndZoom for photoBugAnimationEvent")
            ?: Pair(RectF(), false)
        val isTopBelowMenu = viewModel.menuControl.topMenuHeight.value?.let { it < aabb.top } ?: false
        return isTopBelowMenu
    }

    /**
     * 是否需要使用半沉浸模式
     *
     * @see shouldUseHalfImmersiveMode
     */
    private fun shouldUseHalfImmersiveMode(slotAabb: RectF, isZoomed: Boolean): Pair<Boolean, Boolean> {
        // 未放大不使用, 下拉拖拽过程中不使用
        if (isZoomed.not() || (viewModel.pageManagement.isInSlideDown.value == true)) return Pair(false, false)

        // 不支持半沉浸模式，不使用
        if (viewModel.inputArguments.menuPresent.value?.isSupportHalfImmersive != true) return Pair(false, false)

        // 根据详情模式判断是否能使用半沉浸模式
        if (!canUseHalfImmersiveByDetailsMode()) return Pair(false, false)

        val pageHeight = rootView?.height.getOrLog(TAG, "rootView.height") ?: return Pair(false, false)
        val slotHeight = slotAabb.height()
        val isHeightMet = slotHeight > (pageHeight - halfImmersiveTopThreshold * 2)
        if (!isHeightMet) return Pair(false, false)
        return Pair(true, true)
    }

    /**
     * 更新菜单的主题、样式。内部会做去重
     * 主题优先级
     * 1. 半沉浸式
     * 2. 透明背景的普通菜单（[transparentThemeCondition]）
     * 3. 普通菜单
     *
     * @param shouldInvalidate 是否需要调用 MenuDecoration 的 invalidate
     * @param transparentThemeCondition 是否允许使用透明主题的条件配置（null 则不使用）
     */
    private fun updateMenuTheme(
        reason: String,
        shouldInvalidate: Boolean,
        transparentThemeCondition: TransparentThemeCondition? = null,
    ) {
        val (aabb, zoomed) = calcFocusAabbAndZoom().getOrLog(TAG, "[updateMenuTheme] calcFocusAabbAndZoom") ?: Pair(RectF(), false)
        val (topHalfImmersive, bottomHalfImmersive) = shouldUseHalfImmersiveMode(aabb, zoomed)
        val isNotInnerAlbum = PhotoDataHelper.isFromInnerAlbum(viewModel.inputArguments.inputIntentAction)

        fun getMenuTheme(useHalfImmersive: Boolean, useTransparent: Boolean?): MenuDecorationTheme {
            val shouldUseTransparent = transparentThemeCondition?.let {
                it.isAllowed && ((useTransparent == true) || it.forceUse)
            } ?: false

            return when {
                useHalfImmersive -> MenuDecorationTheme.HalfImmersive
                shouldUseTransparent -> {
                    if (viewModel.pageManagement.pageTheme.value == PhotoPageTheme.Light) {
                        MenuDecorationTheme.LightTransparent
                    } else {
                        MenuDecorationTheme.DarkTransparent
                    }
                }
                (BackgroundBlurUtils.isPhotoBlurEnable() && (isSurfaceViewShowing() || isNotInnerAlbum)) -> {
                    if (viewModel.pageManagement.pageTheme.value == PhotoPageTheme.Light) {
                        MenuDecorationTheme.LightNotBlur
                    } else {
                        MenuDecorationTheme.DarkNotBlur
                    }
                }

                else -> viewModel.pageManagement.pageTheme.value.menuDecorationTheme
            }
        }

        val isTopBelowMenu = viewModel.menuControl.topMenuHeight.value?.let { it < aabb.top }
        val topMenuTheme = getMenuTheme(topHalfImmersive, isTopBelowMenu)
        val isTopUpdated = topMenu.updateTheme(topMenuTheme, shouldInvalidate)

        val screenBound = getMultiWindowUsableBounds()
        val isBottomAboveMenu = viewModel.menuControl.bottomMenuHeight.value?.let {
            val thumbLineHeight = sectionPage.pageViewModel.pageManagement.thumbLineHeight.value ?: 0
            screenBound.second - screenBound.first - thumbLineHeight - it > aabb.bottom
        }
        val bottomMenuTheme = getMenuTheme(bottomHalfImmersive, isBottomAboveMenu)
        val isBottomUpdated = bottomMenu.updateTheme(bottomMenuTheme, shouldInvalidate)

        if (!isTopUpdated && !isBottomUpdated) {
            return
        }

        GLog.d(TAG, LogFlag.DL) {
            "[updateMenuTheme] reason: $reason, topMenuTheme: $topMenuTheme, bottomMenuTheme: $bottomMenuTheme (" +
                    "topHalfImmersive: $topHalfImmersive, bottomHalfImmersive: $bottomHalfImmersive, " +
                    "transparentThemeCondition: $transparentThemeCondition, isTopBelowMenu: $isTopBelowMenu, isBottomAboveMenu: $isBottomAboveMenu)"
        }
        // 通知更新菜单
        viewModel.menuControl.notifyMenuThemeUpdate(topMenuTheme, bottomMenuTheme)
    }

    private fun isSurfaceViewShowing(): Boolean {
        return (viewModel.dataLoading.focusItemViewData?.isVideo == true)
                || viewModel.olive.focusSlotOLiveState.value == OLiveState.VIDEO
    }

    /**
     * 获取App窗口的可用高度（排除状态栏和导航栏，包括分屏和小屏）
     * @return Pair<top, bottom> 窗口可用区域的上边界和下边界
     */
    private fun getMultiWindowUsableBounds(): Pair<Int, Int> {
        if (sectionPage.pageInstance.window?.decorView == null) {
            return 0 to ScreenUtils.displayScreenHeight
        }
        val rect = Rect()
        sectionPage.pageInstance.window?.decorView?.getWindowVisibleDisplayFrame(rect)
        return rect.top to rect.bottom
    }

    /**
     * 获取 focus 目前的大小，
     * 用于判断当前是否需要展示半沉浸式的 menu,
     *
     * @return Pair(focus 的 Aabb 范围，是否放大过)
     */
    private fun calcFocusAabbAndZoom(): Pair<RectF, Boolean>? {
        // 优先使用先前记录的姿态（详情模式阻断场景）；其次使用当前 focus 的最终姿态
        if (forceFocusAabbAndZoom != null) {
            GLog.d(TAG, LogFlag.DL) { "[calcFocusAabbAndZoom] uses prePose: $forceFocusAabbAndZoom" }
            return forceFocusAabbAndZoom
        }

        val provider = getAnimationPropertiesProvider(viewModel.dataLoading.focusItemViewData) ?: return null

        if (!provider.finalAabb.isValid() || provider.finalAabb.isEmpty) {
            GLog.w(TAG, LogFlag.DL, "[updateFocusCurrentSize] provider.finalAabb is invalid")
            return null
        }
        if (provider.displaySize.minLen() <= 0) {
            GLog.w(TAG, LogFlag.DL, "[updateFocusCurrentSize] provider.displaySize is invalid")
            return null
        }

        val isZoomed = pagerSection?.isPhotoSlotScaled()?.getOrLog("[calcFocusSize] isPhotoSlotScaled") ?: return null

        return Pair(provider.finalAabb.copy(), isZoomed).also {
            focusAabbAndZoomCache = it
            GLog.d(TAG, LogFlag.DL) { "[calcFocusSize] result: $it" }
        }
    }

    private fun getAnimationPropertiesProvider(focusViewData: PhotoItemViewData?): AnimationPropertyProvider? {
        focusViewData ?: return null
        val pagerSection: PhotoPagerSection = sectionPage.pageInstance.requireSection<PhotoPagerSection>() ?: return null
        return pagerSection.findPhotoSlotBySlot(focusViewData.position)?.animationPropertiesProvider
            .getOrLog(TAG, "[getAnimationPropertiesProvider] provider")
    }

    /**
     * 判断是否是屏幕方向变更，且判断是否需要更新菜单栏的样式
     * 调用方：[onWindowInsetsChanged]
     */
    private fun updateMenuThemeByScreenRotateIfNeed() {
        // 屏幕方向变更后由此通知菜单变更
        val newRotation = viewModel.pageManagement.appUiRotation.value
        if (rotationDependData.rotation != newRotation) {
            // 屏幕旋转后的第 1 次 onWindowInsetsChanged，重置 rotationDependData
            rotationDependData.apply {
                rotation = newRotation
                isRotating = true
                sizeOnRotate = calcFocusAabbAndZoom()?.first?.toSize()
                callCount = 1
            }
            return
        } else if (rotationDependData.isRotating) {
            val focusSize = calcFocusAabbAndZoom()?.first?.toSize()
            if (rotationDependData.sizeOnRotate != focusSize) {
                // 屏幕旋转后，focusSize 变化时，更新 menu
                rotationDependData.apply {
                    isRotating = false
                }
                updateMenuTheme(UPDATE_MENU_THEME_ROTATE, true)
                tryRegisterSlotDragListenerForScaled()
                return
            }

            rotationDependData.callCount += 1
            // 单次旋转最多支持 MAX_WINDOW_INSET_CALL_TIMES_ON_ROTATE 次判断
            if (rotationDependData.callCount >= MAX_WINDOW_INSET_CALL_TIMES_ON_ROTATE) {
                rotationDependData.apply {
                    isRotating = false
                }
            }
        }
    }

    /**
     * 订阅ViewModel中的数据源
     */
    private fun subscribeLiveDataFromViewModel() {
        viewModel.inputArguments.menuPresent.observe(this) {
            if (needPostponeMenuInflateNow) {
                GLog.w(TAG, LogFlag.DL) {
                    "[subscribeLiveDataFromViewModel] menuInflate need postpone. " +
                            "menuPresent.invalidateMenu cancel! "
                }
                // 此处底部菜单UI可以先行把模板加载出来，否则会导致底部布局跳变
                if (couldShowBottomMenu()) {
                    bottomMenu.invalidateMenu()
                }
                return@observe
            }
            tryInvalidateMenu("menuPresent")
        }

        viewModel.menuControl.clearTips.observe(this) {
            textTips.clearTextTips(it)
        }

        // 隐藏水印的角标
        viewModel.menuControl.clearWatermarkCorner.observe(this) { isHideCorner ->
            if (isHideCorner) {
                bottomMenu.hideWatermarkTip()
                topMenu.hideWatermarkTip()
            }
        }

        lifecycleScope.launch {
            viewModel.menuControl.photoMenu.collectNotNull { photoMenuState ->
                topMenu.currentMenuState = photoMenuState
                bottomMenu.currentMenuState = photoMenuState
                if (needPostponeMenuInflateNow) {
                    // 在需要菜单加载错峰的机型上，如果入场动画还在跑，数据先安排好，菜单暂不加载，等待别处安排
                    GLog.w(TAG, LogFlag.DL) {
                        "[subscribeLiveDataFromViewModel] menuInflate need postpone. " +
                                "photoMenu.invalidateMenu cancel! "
                    }
                    return@collectNotNull
                }
                tryInvalidateMenu("photoMenu")
            }
        }

        viewModel.menuControl.menuActionExecuting.observe(this) { menuActionExecuting ->
            if (menuActionExecuting) {
                // 当前有菜单项正在执行，隐藏文本提示
                textTips.dismiss()
            }
        }

        viewModel.pageManagement.photoPageTransitionState.observe(this) {
            if (needPostponeMenuInflateNow) {
                // 在需要菜单加载错峰的机型上，如果入场动画还在跑，那菜单就不要加载了，由动画时机来控制
                GLog.w(TAG, LogFlag.DL) {
                    "[subscribeLiveDataFromViewModel] menuInflate need postpone. " +
                            "photoPageTransitionState.invalidateMenu cancel! "
                }
                return@observe
            }
            // 避免动画EXITING和DESTROYED状态反复走tryInvalidateMenu，导致反复dismiss和show TextTips导致闪TextTips
            if (it == PageTransitionState.EXITING) {
                GLog.w(TAG, LogFlag.DL) { "[subscribeLiveDataFromViewModel] no need invalidate menu when exiting" }
                return@observe
            }
            tryInvalidateMenu("photoPageTransitionState-$it")
        }

        viewModel.menuControl.bubbleWindow.observe(this) { bubbleWindowState ->
            (bubbleWindow as PhotoBubbleWindowDecoration).currentWindowState = bubbleWindowState
            bubbleWindow.showIfNecessary()
        }

        viewModel.inputArguments.title.observe(this) { title ->
            systemBarController.setStatusBarAppearance(title.useLightStatusBar)
            refreshMenuVisibility()
        }

        viewModel.outputChannels.castingManager.isVideoCasting.observe(this) {
            refreshMenuVisibility()
        }


        viewModel.pageManagement.photoDecorationState.observe(this) { decorationState ->
            if (decorationState.shouldShow) {
                GLog.d(TAG) { "[subscribeLiveDataFromViewModel] photoDecorationVisibility update will show decoration." }
                updateMenuTheme(UPDATE_MENU_THEME_DECORATION_SHOW, true)

                if (isPagePresented) {
                    textTips.restoreTemporaryDismiss()
                }
                // 需要显示时，主动刷新菜单的可见性，保证其正确显示
                refreshMenuVisibility()
            } else {
                GLog.d(TAG) { "[subscribeLiveDataFromViewModel] photoDecorationVisibility update should hide decoration." }
                // 气泡弹窗仅会此主动隐藏，并不是在[needVisible]时主动显示
                bubbleWindow.dismiss()

                // 临时隐藏文本提示
                textTips.temporaryDismiss()
            }
            tryRegisterSlotDragListenerForScaled()
        }

        viewModel.dataLoading.diffedFocusViewDataDistinctId.collect(this) {
            doOnNextDiffedFocusIdEvent?.invoke()
            doOnNextDiffedFocusIdEvent = null
            tryRegisterSlotDragListenerForScaled()
        }

        viewModel.pageManagement.pageTheme.map { it.menuDecorationTheme }.collect(this) {
            updateMenuTheme(UPDATE_MENU_THEME_PAGE_THEME, true)
        }

        viewModel.details.transitionPanelEffectState.observe(this) { state ->
            val inDetailsMode = viewModel.details.isDetailModeByTransition(state)
            if (!inDetailsMode) {
                updateMenuTheme(UPDATE_MENU_THEME_PANEL_EXIT, true)
            }
        }

        viewModel.details.isInDetailsModeInclTransition.observe(this) { inDetailsMode ->
            if (!inDetailsMode) {
                // 退出详情模式动画结束，更新一次主题
                updateMenuTheme(UPDATE_MENU_THEME_PANEL_EXIT, true)
            }
        }

        viewModel.details.transitionStatus.observe(this) {
            if (it == DetailsPanelTransitionStatus.EnterStart) {
                // 进入详情模式的 transition 开始，记录进入前的姿态，移除监听
                forceFocusAabbAndZoom = focusAabbAndZoomCache
                tryRegisterSlotDragListenerForScaled()
            } else {
                // 进入动画结束时置空，表示此次非阻断
                forceFocusAabbAndZoom = null
            }

            if (it == DetailsPanelTransitionStatus.ExitEnd) {
                // 阻断场景下，图片会还原成进入时的状态，此时可能是放大状态
                tryRegisterSlotDragListenerForScaled()
            }
        }

        viewModel.menuControl.canClickTopMenu.observe(this) { canClick ->
            topMenu.canItemClick = canClick
        }

        viewModel.olive.focusSlotOLiveState.observe(this) {
            if (BackgroundBlurUtils.isPhotoBlurEnable()) {
                updateMenuTheme(UPDATE_MENU_THEME_BLUR_MODE, true)
            }
        }

        viewModel.menuControl.canClickBottomMenu.observe(this) { canClick ->
            bottomMenu.canItemClick = canClick
        }

        viewModel.pageManagement.photoBgAnimationEvent.observe(this) {
            val isPhotoTopBelowMenu = isPhotoTopBelowMenu()
            topMenu.changeMenuColorByEvent(it, isPhotoTopBelowMenu)
            bottomMenu.changeMenuColorByEvent(it, isPhotoTopBelowMenu)
        }
    }

    private fun tryInvalidateMenu(entrance: String) {
        measureTimeMillis {
            updateMenuTheme(UPDATE_MENU_THEME_INVALIDATE, false)
            if (couldShowTopMenu()) {
                topMenu.invalidateMenu()
            }
            if (couldShowBottomMenu()) {
                bottomMenu.invalidateMenu()
            }
        }.apply {
            GLog.d(TAG) { "[tryInvalidateMenu] end, cost=$this. entrance=$entrance" }
        }
        val currentViewData = viewModel.dataLoading.focusItemViewData
        dismissCurrentTextTipsIfNeed(currentViewData)
        lastFocusSlotDataId = currentViewData?.id
        /**
         * 注意：此invalidate是显示textTips队列里最上面那个提示，不是单纯的重新画一把
         */
        textTips.invalidate(true)
    }

    /**
     * 当焦点数据切换时需要消费掉已显示的气泡
     */
    private fun dismissCurrentTextTipsIfNeed(currentViewData: PhotoItemViewData?) {
        if (currentViewData?.id != lastFocusSlotDataId) {
            textTips.temporaryDismiss()
        }
    }

    /**
     * 重新刷新菜单的可见性。
     * - 顶部菜单： 一直显示
     * - 底部菜单： [couldShowBottomMenu] 为 [true] 时，才会显示。
     */
    private fun refreshMenuVisibility() {
        viewModel.menuControl.notifyBottomMenuVisibility(couldShowBottomMenu())
        if (couldShowTopMenu()) topMenu.show() else topMenu.hide()
        if (couldShowBottomMenu()) bottomMenu.show() else bottomMenu.hide()
    }

    /**
     * 定义底部菜单栏是否可用，不同的窗口模式、不同的分辨率 其菜单显示的位置是有差异的。
     * AppUiStateChanged变化时，重新判定大图的菜单项是否需要显示在底部菜单栏，否则应显示在顶部菜单栏。
     * - 当前的显示宽度在[MAX_WINDOW_WIDTH_SHOW_BOTTOM_MENU]之内，将菜单显示在底部菜单
     */
    private fun couldEnableBottomMenu(): Boolean {
        sectionPage.pageInstance.getCurrentAppUiConfig().let { config ->
            return config.windowWidthDp.current < MAX_WINDOW_WIDTH_SHOW_BOTTOM_MENU // 当前的显示宽度足够小，允许显示底部菜单
        }
    }

    /**
     * 根据是否需要隐藏，决定当前顶部及底部菜单的主题
     *
     * markBy: 马上铠龙会根据需求把这个方法删除并修改
     *
     * @param needHide false时主题不为透明，true时透明，null时根据场景判断
     */
    internal fun updateMenuThemeWhenAnimate(needHide: Boolean?) {
        updateMenuTheme(
            UPDATE_MENU_THEME_TRANSITION_ANIM,
            shouldInvalidate = true,
            transparentThemeCondition = TransparentThemeCondition(isAllowed = needHide != false, forceUse = needHide == true)
        )
    }

    internal fun doOnTopMenuPrepared(func: () -> Unit) {
        topMenu.doOnMenuPrepared(func)
    }

    /**
     * 是否展示底部的菜单栏。
     * - 底部菜单栏不可用时，不要展示
     */
    internal fun couldShowBottomMenu(): Boolean {
        /**
         * 如果当前[activity]不在默认[Display.DEFAULT_DISPLAY]上显示，如在外屏显示
         * 底部会有独立的删除按钮，暂不显示底部菜单栏，后续有需求变更再放开
         */
        if (isShowedOnDefaultDisplay.not()) {
            return false
        }
        // 如果menu没有设置有效的resID，那么也不应该展示
        if (viewModel.inputArguments.menuPresent.value?.bottomMenuResId == Resources.ID_NULL) {
            return false
        }
        // 当前页面底部菜单可用时，显示底部菜单
        return couldEnableBottomMenu()
    }

    /**
     * 是否展示顶部菜单，由外部指定。
     * @see PhotoInputArgumentsViewModel.Title.usePhotoPageToolbar
     */
    private fun couldShowTopMenu(): Boolean =
        viewModel.inputArguments.title.value?.usePhotoPageToolbar ?: false

    /**
     * 尝试在半沉浸模式下注册 slot 显示位置变化监听。非半沉浸模式会进行移除
     * 注册监听的条件：
     * 1. 需要非详情模式
     * 2. 需要显示菜单
     * 3. 需要图片放大过（放大时会隐藏菜单，再次显示菜单时注册监听即可）
     */
    private fun tryRegisterSlotDragListenerForScaled() {
        rootView?.post {
            val showMenu = viewModel.pageManagement.photoDecorationState.value?.shouldShow == true
            val isInDetailsMode = viewModel.details.isInDetailsModeInclTransition.value == true
            val isSlotScaled = pagerSection?.isPhotoSlotScaled() ?: false
            if (!isInDetailsMode && showMenu && isSlotScaled) {
                // 符合条件，设置监听
                pagerSection?.registerRemovableUninterruptedTouchEventListener(slotDragListener)
            } else {
                // 其余情况，移除监听
                pagerSection?.unregisterRemovableUninterruptedTouchEventListener(slotDragListener)
            }
        }
    }

    /**
     * 在半沉浸模式下监听 slot 图是否被 drag，
     * ONESHOT 触发即移除
     *
     * @return 是否需要移除此监听
     */
    private fun onSlotTouchWhileHalfImmersive(slot: Int, event: MotionEvent): Boolean {
        // 此方法会在 listeners.forEach 中调用，所以需要 post 一帧再执行 remove，避免 ConcurrentModificationException

        // slot 需为当前 focus
        val isSlotValid = slot == currentFocus
        // 详情模式中，取消监听且不触发隐藏
        val isInDetailsMode = viewModel.details.isInDetailsModeInclTransition.value == true
        if (!isSlotValid || isInDetailsMode || (viewModel.pageManagement.isInSlideDown.value == true)) {
            return true
        }

        var shouldRemoveListener = false
        when (event.action) {
            // DOWN 事件重置 move position。
            MotionEvent.ACTION_DOWN -> preMovePosition = Pair(event.x, event.y)

            // 仅处理单指 move，其余如点击、多指等由 PhotoPagerSection 处理
            MotionEvent.ACTION_MOVE -> {
                val isMove = ((abs(preMovePosition.first - event.x) > touchSlop)
                        || (abs(preMovePosition.second - event.y) > touchSlop))
                if (isMove
                    && (viewModel.olive.isLongPressHandling.value != true)
                ) {
                    shouldRemoveListener = true
                    viewModel.pageManagement.changeImmersionInteractive(true)
                }
            }
        }

        return shouldRemoveListener
    }

    /**
     * 根据详情模式的状态判断是否允许使用半沉浸主题。
     * 1. 进入详情模式、或再详情模式中，不允许使用
     * 2. 退出时，如果时被阻断（状态未变成 EnterEnd 或 ExitStart）导致的退出，允许
     * 3. 退出时，如果时普通退出，会执行动画，动画结束后才允许
     */
    private fun canUseHalfImmersiveByDetailsMode(): Boolean {
        val isInDetailsMode = viewModel.details.isDetailModeByTransition()
        // transitionStatus 未被赋值过，表示没进入过详情模式
        val transitionStatus = viewModel.details.transitionStatus.value ?: return true

        // 进入详情模式
        if (isInDetailsMode) return false

        // 退出详情，进入时被阻断 (状态未变成 EnterEnd 或 ExitStart) 导致的退出，允许
        if (transitionStatus == DetailsPanelTransitionStatus.EnterStart) return true

        // 退出详情，普通退出，会执行动画，动画结束后才允许
        return viewModel.details.isInDetailsModeInclTransition.value == false
    }

    private fun RectF.toSize() = Size(width().toInt(), height().toInt())

    /**
     * 深复制，因 RectF 内部可变，所以需要深复制。
     */
    private fun RectF.copy() = RectF(left, top, right, bottom)

    /**
     * 旋转判断是否需要更新菜单栏的样式的数据持有类
     *
     * @param rotation 当前屏幕旋转度数，由此判断是否进入旋转。
     * @param isRotating 当前是否处于当次旋转判断中
     * @param callCount 当次旋转判断时调用判断方法的次数，在达到最大 [MAX_WINDOW_INSET_CALL_TIMES_ON_ROTATE] 次后退出当次旋转判断。
     * @param sizeOnRotate 当前 focus 的大小，如在旋转过程中变更，则通知更新菜单样式
     */
    private data class RotationDependData(
        var rotation: Int? = null,
        var isRotating: Boolean = false,
        var callCount: Int = 0,
        var sizeOnRotate: Size? = null,
    )

    /**
     * 透明背景菜单的启用条件
     */
    private data class TransparentThemeCondition(
        /**
         * 是否允许使用（总开关）
         */
        val isAllowed: Boolean = false,

        /**
         * 是否强制启用（前提条件：isAllowed = true），忽略 top、bottom 的判断
         */
        val forceUse: Boolean = false
    )

    companion object {
        private const val TAG = "PhotoMenuSection"

        /**
         * 应该展示底部菜单的最大窗口宽度，大于该宽度时应该隐藏底部菜单，顶部展示包括底部菜单项在内的全部菜单。
         */
        const val MAX_WINDOW_WIDTH_SHOW_BOTTOM_MENU = 600

        /**
         * 屏幕旋转后最多允许走 2 次 [updateMenuThemeByScreenRotateIfNeed]（含旋转的初始化那次）
         */
        private const val MAX_WINDOW_INSET_CALL_TIMES_ON_ROTATE = 2

        private const val UPDATE_MENU_THEME_DECORATION_SHOW = "decoration_show"
        private const val UPDATE_MENU_THEME_ROTATE = "rotate"
        private const val UPDATE_MENU_THEME_DELETE = "delete"
        private const val UPDATE_MENU_THEME_PAGE_THEME = "page_theme"
        private const val UPDATE_MENU_THEME_PANEL_EXIT = "panel_exit"
        private const val UPDATE_MENU_THEME_INVALIDATE = "invalidate"
        private const val UPDATE_MENU_THEME_TRANSITION_ANIM = "transition_anim"
        private const val UPDATE_MENU_THEME_SCREEN_MODE = "screen_mode"
        private const val UPDATE_MENU_THEME_BLUR_MODE = "blur_mode"
    }
}