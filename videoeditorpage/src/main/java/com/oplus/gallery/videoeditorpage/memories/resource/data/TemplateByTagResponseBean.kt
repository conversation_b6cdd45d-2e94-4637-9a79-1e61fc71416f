/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThemesByTagResponseBean.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/26
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/26		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.data

import com.google.gson.annotations.SerializedName

class TemplateByTagResponseBean {

    @SerializedName("version")
    var totalVersion: Long? = null

    @SerializedName("list")
    var templateList: List<TemplateListBean>? = null

    class TemplateListBean : BaseResourceBean() {

        @SerializedName("templateId")
        var templateId: String? = null

        @SerializedName("category")
        var category: String? = null

        @SerializedName("templateName")
        var zhName: String? = null

        @SerializedName("chName")
        var chName: String? = null

        @SerializedName("enName")
        var enName: String? = null

        @SerializedName("iconPath")
        var iconPath: String? = null

        @SerializedName("zipPath")
        var filePath: String? = null

        @SerializedName("zipMd5")
        var fileHash: String? = null

        @SerializedName("zipSize")
        var fileSize: Long = 0

        @SerializedName("position")
        var position: Int = -1

        @SerializedName("externalDataList")
        var externalData: List<ExternalDataDto>? = null

        @SerializedName("isPreset")
        var isPreset = false

        @SerializedName("tag")
        var tag: String? = null

        @SerializedName("updateTime")
        var updateTime: String? = null

        @SerializedName("version")
        var version: Long = -1

        class ExternalDataDto {

            @SerializedName("type")
            var type: String? = null

            @SerializedName("resourceID")
            var resourceID: String? = null

            @SerializedName("version")
            var version: Long = -1

            @SerializedName("data")
            var data: String? = null

            override fun toString(): String {
                return "ExternalDataDto(type=$type, resourceID=$resourceID, version=$version, data=$data)"
            }
        }

        override fun toString(): String {
            return "TemplateListBean(templateId=$templateId, zhName=$zhName, fileSize=$fileSize, position=$position," +
                    " externalData=$externalData)"
        }
    }
}