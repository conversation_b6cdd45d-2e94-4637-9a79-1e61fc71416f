/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditorCaptionState.kt
 ** Description : 字幕状态管理接口
 ** Version     : 1.0
 ** Date        : 2025/5/19 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/5/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption

import android.content.Context
import android.graphics.PointF
import android.view.View
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ISeekingListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IStreamingEngineListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.MeicamCaption
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.player.IVideoPlayerListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams
import com.oplus.gallery.videoeditorpage.video.business.base.EditorEffectTrackState
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum
import com.oplus.gallery.videoeditorpage.video.business.caption.util.AbstractAutoAlignment
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.EditTextPreviewView
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.output.SaveInfo
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorEffectTrackConfig
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel
import com.oplus.gallery.videoeditorpage.video.business.track.util.EffectTrackRangeHelper
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import java.lang.ref.WeakReference
import kotlin.math.abs

/**
 * 编辑字幕状态
 */
class EditorCaptionState(
    context: Context,
    editorControlView: EditorControlView,
    private val liveWindow: EditorPreviewView,
    weakTimelineViewModel: WeakReference<TimelineViewModel>
) : EditorEffectTrackState<EditorCaptionUIController>(
    TAG,
    context,
    editorControlView,
    weakTimelineViewModel
),
    IVideoPlayerListener,
    IStreamingEngineListener,
    ISeekingListener,
    EditorPreviewView.OnPreviewSizeChangedListener,
    EditTextPreviewView.OnCaptionEditTextListener {


    /**
     * initCaption
     * 1.在第一次进入文字编辑模块时，为了绘制文字边框会使用
     * 2.同时新建字幕的时候赋值给initCaption
     *
     * 场景一：首次进入文字编辑模块时，
     *        默认显示的字幕内容不会被修改，且轨道上不显示字幕元素。
     *        此时 curCaption 的值来源于 initCaption。
     *
     * 场景二：在输入框中输入内容后，
     *        若选中了轨道上的某个字幕项，则 initCaption 会被更新，
     *        并保持与轨道选中项数据同步。
     *        此时 curCaption 的值来源于 uiController.selectedFx。
     */
    private var initCaption: BaseCaption? = null

    /**
     * 当前选中字幕
     */
    var curCaption: BaseCaption?
        get() {
            /**
             * 在第一次进入文字编辑模块时，因为是默认显示的字幕，不会进去轨道
             * 所以 initCaption 会被赋值，此时 curCaption 的值来源于 initCaption。
             */
            initCaption?.let { if (it.isDefaultCaption) return initCaption }
            return uiController?.selectedFx as? BaseCaption
        }
        set(value) {
            uiController?.selectedFx = value
            editPageState?.caption = value
            // 字幕发生变化时需要更新一下预览编辑框坐标
            value?.let {
                updateCaptionCoordinatesIfNeeded(it)
            } ?: run { showEditRect = false }
        }

    /**
     * 标记当前编辑框是否需要显示
     */
    var showEditRect: Boolean = false
        set(value) {
            if (field == value) return
            field = value
            val drawFlag = if (value) {
                EditTextPreviewView.DRAW_FLAG_DELETE or EditTextPreviewView.DRAW_FLAG_SCALE_ROTATE
            } else {
                0
            }
            liveWindow.apply {
                setDrawFlag(drawFlag)
                setDrawLineFlag(value)
            }
        }

    /**
     * 标记当前是否执行过拖拽缩放旋转操作，需要记录编辑栈
     */
    private var shouldSaveDragScaleRotationOperation: Boolean = false

    private var editPageState: EditorSubCaptionState? = null

    /**
     * 自动旋转对齐工具
     */
    private var rotateAbstractAutoAlignment: AbstractAutoAlignment? = null

    /**
     * 拖拽X轴自动对齐工具
     */
    private var dragXAbstractAutoAlignment: AbstractAutoAlignment? = null

    /**
     * 拖拽Y轴自动对齐工具
     */
    private var dragYAbstractAutoAlignment: AbstractAutoAlignment? = null

    /**
     * 标记x轴或y轴方向是否发生位移移动
     */
    private var needMove: Boolean = false

    /**
     * 记录x轴方向发生的位移量
     */
    private var xFinalDet: Float = 0f

    /**
     * 进入二级编辑页是否显示键盘
     */
    private var shouldShowKeyboard: Boolean = false

    /**
     * 记录当前是否是播放状态
     */
    private var isPlaying: Boolean = false

    init {
        mEditorPreviewView = liveWindow
        initListeners()
        registerEditorEngineListener()
    }

    override val trackConfig: EditorEffectTrackConfig
        get() = EditorEffectTrackConfig.getConfig(EditorTrackScene.CAPTION)

    /**
     * 处理响应点击删除按钮
     */
    private fun processDelete() {
        editorEngine.removeCaption(curCaption)
        curCaption?.let {
            if (it.isDefaultCaption.not()) {
                // 当前字幕不是默认字幕才记录操作
                operationSaveHelper.saveCurrentTimeline(OperationType.CAPTION_UPDATE)
            }
        }
        initCaption = null
        curCaption = null
        showEditRect = false
        editPageState?.exitEditPage()
        // 更新字幕轨道
        updateCaptionRenderingForPreview()
        updateZoomInOutFrameLayout()
    }

    /**
     * 处理响应点击字幕编辑框外
     */
    private fun processOutOfCaptionRectClick() {
        if (editorEngine.isPlaying) editorEngine.stopPlayer()
        showEditRect = false
        // 设置轨道为未选中状态
        uiController.onBlankAreaClick()
    }

    override fun <T : EditorTrackBaseState<EditorTrackBaseUIController<Any>>> getState(clickType: Int): T {
        /**
         * 1.编辑文字默认是开始播放状态
         * 2.开始播放边框需要隐藏
         */
        isPlaying = true
        showEditRect = false
        val subState = EditorSubCaptionState(
            mContext,
            mEditorControlView,
            weakTimelineViewModel,
            curCaption,
            clickType,
            shouldShowKeyboard
        ) { newCaption ->
            curCaption = newCaption
            initCaption = newCaption
            /**
             * 1.新增字幕时设置显示编辑框
             * 2.播放状态需要隐藏编辑框
             * 3.删除字幕时隐藏编辑框
             */
            showEditRect = if (isPlaying.not()) {
                (newCaption != null)
            } else {
                false
            }
            // 更新字幕轨道
            updateZoomInOutFrameLayout()
        }
        editPageState = subState
        // 创建完二级页后将是否显示键盘状态置为false
        shouldShowKeyboard = false
        return subState as T
    }

    /**
     * 更新字幕效果显示,当有字幕的样式、字体、文本、颜色等发生修改后如果需要实时预览，则要调用该函数
     */
    private fun updateCaptionRenderingForPreview() {
        updateCaptionRenderingForPreview(editorEngine.timelineCurrentPosition)
    }

    /**
     * 初始化字幕业务操作类
     */
    private fun initListeners() {
        mEditorPreviewView?.addPreviewSizeChangedListener(this)
        mEditorPreviewView?.addEditTextListener(this)
    }

    override fun create() {
        super.create()
        liveWindow.setDrawRectVisible(View.VISIBLE)
        showEditRect = false
    }

    override fun resume(isActivityResume: Boolean) {
        super.resume(isActivityResume)
        checkAndUpdateCaption()
        updateCaptionRenderingForPreview()
    }

    override fun destroy() {
        super.destroy()
        showEditRect = false
        liveWindow.setDrawRectVisible(View.INVISIBLE)
        unregisterEditorEngineListener()
        mEditorPreviewView?.removePreviewSizeChangedListener(this)
        mEditorPreviewView?.removeEditTextListener(this)
    }

    /**
     * 视频流状态发生变化回调
     */
    override fun onStreamingEngineStateChanged(state: Int) = Unit

    override fun onSeekingTimelinePosition(position: Long) {
        // 字幕发生位置移动时会触发更新编辑框位置
        if (isPlaying.not()) {
            updateEditRectVisibility(position)
        }
    }

    override fun onFirstVideoFramePresented() = Unit

    override fun getEditorTrackScene(): EditorTrackScene {
        return EditorTrackScene.CAPTION
    }

    /**
     * 检测所有字幕，删除时间大于视频时间的字幕
     */
    private fun checkAndUpdateCaption() {
        val timeline = mEditorEngine.currentTimeline ?: return
        val needDelCaption: MutableList<BaseCaption> = ArrayList()
        timeline.captionList?.let { captionList ->
            val timeLineDuration = timeline.duration
            captionList.forEach { caption ->
                val inTime = caption.inTime
                val outTime = caption.outTime
                if (outTime <= timeLineDuration) return@forEach
                if (inTime >= timeLineDuration) {
                    needDelCaption.add(caption)
                } else {
                    caption.outTime = timeLineDuration
                }
            }
        }
        needDelCaption.forEach { timeline.removeCaption(it) }
    }

    /**
     * 更新字幕效果显示,当有字幕的样式、字体、文本、颜色等发生修改后如果需要实时预览，则要调用该函数
     */
    private fun updateCaptionRenderingForPreview(position: Long) {
        editorEngine.seekTo(position, EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER)
        menuState?.onPlayPositionChange(position)
        onPlayPositionChange(position)
    }

    /**
     * 注册编辑引擎监听器
     */
    private fun registerEditorEngineListener() {
        mEditorEngine.addSeekingListener(this)
        mEditorEngine.addStreamingEngineListener(this)
        mEditorEngine.addVideoPlayerListener(this)
    }

    /**
     * 反注册编辑引擎监听器
     */
    private fun unregisterEditorEngineListener() {
        mEditorEngine.removeSeekingListener(this)
        mEditorEngine.removeStreamingEngineListener(this)
        mEditorEngine.removeVideoPlayerListener(this)
    }

    override fun isPreview(): Boolean {
        return true
    }

    override fun createUIController(): EditorCaptionUIController {
        val hasFxs = getVideoFxs().isNotEmpty()
        val editorCaptionUIController = EditorCaptionUIController(
            mContext,
            mEditorControlView,
            this,
            liveWindow,
            if ((weakTimelineViewModel != null)) weakTimelineViewModel.get() else null,
            this,
            hasFxs
        ) { curTimelinePosition, selectedCaption ->
            // 点击轨道上某个字幕后把当前选中的字幕回传给state使用
            curCaption = selectedCaption
            /**
             * 1.当前点击位置不在文字轨道中，则设置预览编辑框不可见
             * 2.文字边框显示交给onSeekingTimelinePosition里面的逻辑处理
             */
            showEditRect = isCaptionVisible(curTimelinePosition, curCaption)
        }
        editorCaptionUIController.onScrollListener = this
        return editorCaptionUIController
    }

    override fun getVideoFxs(): List<BaseCaption> {
        // 要过滤掉“请输入文字”默认字幕不在轨道上显示
        return editorEngine.currentTimeline.captionList
            .filter { it.isDefaultCaption.not() }
            .sortedBy { it.inTime }
    }

    override fun getClipModelType(): Int {
        return ClipModel.ClipType.CLIP_TEXT
    }

    /**
     * 配置一级页是否显示撤销/重做按钮
     */
    override fun showOperaIcon(): Boolean {
        return true
    }

    /**
     * 撤销、重做操作成功后出发回调
     */
    override fun updateUndo(saveInfo: SaveInfo?) {
        updateZoomInOutFrameLayout()
        updateEditRectVisibility(editorEngine.timelineCurrentPosition)
    }

    override fun onPlayPositionChange(currentPosition: Long) {
        uiController?.updateTimelinePosition(currentPosition)
    }

    /**
     * 更新设置字幕到对应的字幕轨道片段中
     */
    override fun setClipModel(clipModel: ClipModel, baseVideoFx: IBaseClip) {
        super.setClipModel(clipModel, baseVideoFx)
        clipModel.caption = baseVideoFx as? BaseCaption
        // 设置字幕轨道显示内容
        clipModel.clipDescription = clipModel.caption.text
    }

    override fun onCurrentTimelineChanged(timeline: ITimeline?, updateTimelineView: Boolean) {
        super.onCurrentTimelineChanged(timeline, updateTimelineView)
    }

    /**
     * 配置页面层级，用于baseUIController控制页面通用布局
     */
    override fun getPageLevel(): PageLevelEnum {
        return if (isSkipAnim) {
            PageLevelEnum.PAGE_LEVEL_SECOND
        } else {
            PageLevelEnum.PAGE_LEVEL_FIRST
        }
    }

    override fun onPositionChange(absolutePosition: Long) = Unit

    override fun onPreloadingCompletion() = Unit

    override fun onStopped() {
        isPlaying = false
    }

    override fun onStartPlay(success: Boolean) {
        showEditRect = false
        isPlaying = true
    }

    override fun onEOF(absolutePosition: Long) {
        // 停止播放后刷新字幕避免带消失的动画样式的字幕停留在最后一帧不显示的问题
        liveWindow.post {
            editorEngine.seekTo(editorEngine.timelineCurrentPosition, EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER)
            updateEditRectVisibility(editorEngine.timelineCurrentPosition)
        }
    }

    override fun onException(i: Int, s: String) = Unit

    override fun onHardwareError(i: Int, s: String) = Unit

    override fun onPlaybackDelayed(l: Long, b: Boolean) = Unit

    /**
     * 预览区域大小发生变化的回调
     */
    override fun onPreviewSizeChanged() {
        // 更新编辑框位置
        showCaptionTextPreviewViewIfNeed(curCaption)
    }

    /**
     * 处理复制业务
     */
    override fun processFxWhenCopy(fx: IBaseClip, newInTime: Long, newOutTime: Long): Pair<IBaseClip, OperationType>? {
        editorEngine?.stopPlayer()
        (curCaption as? MeicamCaption)?.let {
            val params = CaptionParams(newInTime, newOutTime)
                .withTrackIndex(it.getTrackIndex())
                .withText(it.text)
                .withCaptionType(it.captionType)
            it.copy(timeline, params)
        }?.also {
            return Pair(it, OperationType.CAPTION_UPDATE)
        }
        return null
    }

    /**
     * 处理删除业务
     */
    override fun processFxWhenDelete(fx: IBaseClip) {
        processDelete()
    }
    /**
     * EditorPreviewView中字幕编辑框的手势事件响应处理
     * OnEditRectViewClickListener接口响应处理
     */

    /**
     * 响应预览字幕编辑弹窗手指按下
     */
    override fun onTouchDown(curPoint: PointF) {
        processTouchDown(curPoint)
    }

    /**
     * 响应预览字幕编辑弹窗手指抬起
     */
    override fun onTouchUp(curPoint: PointF) {
        processTouchUp(curPoint)
    }

    /**
     * 响应预览字幕编辑弹窗旋转缩放
     */
    override fun onScaleAndRotate(scalePercent: Float, anchor: PointF, rotateDegree: Float) {
        processScaleAndRotate(scalePercent, anchor, rotateDegree)
    }

    /**
     * 响应预览字幕编辑弹窗删除
     */
    override fun onDelete() {
        processDelete()
    }

    /**
     * 响应预览字幕编辑弹窗拖拽
     */
    override fun onDrag(prePointF: PointF, nowPointF: PointF) {
        processDrag(prePointF, nowPointF)
    }

    /**
     * 响应预览字幕编辑弹窗点击弹窗外
     */
    override fun onOutOfCaptionRectClick() {
        processOutOfCaptionRectClick()
    }

    /**
     * 响应预览字幕编辑弹窗点击
     */
    override fun onCaptionClick() {
        if (showEditRect.not()) {
            // 编辑框未显示，则显示
            showEditRect = true
        } else {
            // 编辑框已显示，则打开编辑页
            shouldShowKeyboard = true
            handleClick(CLICK_TYPE_EDIT)
        }
    }

    /**
     * 响应预览字幕编辑弹窗双击
     */
    override fun onDoubleClick(pointF: PointF) {
        processDoubleClick(pointF)
    }

    override fun isSkipAnim(): Boolean {
        return uiController?.needShowContent()?.not() ?: videoFxs.isEmpty() && isCurrentTimeHasEnoughEmptyRange()
    }

    /**
     * 重置自动对齐工具
     */
    private fun resetAutoAlignUtil() {
        rotateAbstractAutoAlignment?.reset()
        dragXAbstractAutoAlignment?.reset()
        dragYAbstractAutoAlignment?.reset()
        needMove = false
        xFinalDet = 0f
        liveWindow.setHorizontalReferenceLineVisibility(false)
        liveWindow.setVerticalReferenceLineVisibility(false)
    }

    private fun processTouchDown(curPoint: PointF) {
        resetAutoAlignUtil()
        selectingCaptionByClickingLiveWindow(curPoint)
    }

    private fun processTouchUp(curPoint: PointF) {
        resetAutoAlignUtil()
        if (shouldSaveDragScaleRotationOperation) {
            curCaption?.let {
                if (it.isDefaultCaption.not()) {
                    // 当前操作的不是默认字幕才记录操作
                    operationSaveHelper?.saveCurrentTimeline(OperationType.CAPTION_UPDATE)
                }
            }
            // 执行保存操作后重置标记位
            shouldSaveDragScaleRotationOperation = false
        }
    }

    /**
     * 处理字幕的缩放和旋转操作
     * @param scalePercent 缩放比例 (1.0表示原始大小)
     * @param anchor 缩放锚点坐标
     * @param rotateDegree 旋转角度(度)
     */
    private fun processScaleAndRotate(scalePercent: Float, anchor: PointF, rotateDegree: Float) {
        // 1. 前置条件检查
        val safeAnchor = editorEngine.mapLiveWindowViewToCanonical(anchor) ?: return
        curCaption ?: return

        // 2. 处理缩放
        processScaling(scalePercent, safeAnchor)

        // 3. 处理旋转
        if (rotateDegree != 0f) {
            processRotation(rotateDegree)
        }

        // 4. 更新预览
        updateCaptionRenderingForPreview()
    }

    /**
     * 处理字幕缩放逻辑
     */
    private fun processScaling(scalePercent: Float, anchor: PointF) {
        curCaption?.let {
            val shouldScaleUp = scalePercent > BASIC_CAPTION_SCALE_FACTOR &&
                    it.scaleX < MAX_CAPTION_SCALE_FACTOR
            val shouldScaleDown = scalePercent < BASIC_CAPTION_SCALE_FACTOR &&
                    it.scaleX > MIN_CAPTION_SCALE_FACTOR

            if (shouldScaleUp || shouldScaleDown) {
                it.scaleCaption(scalePercent, anchor)
                shouldSaveDragScaleRotationOperation = true
            }
        }
    }

    /**
     * 处理字幕旋转逻辑
     */
    private fun processRotation(rotateDegree: Float) {
        val rotationUtil = rotateAbstractAutoAlignment ?: createRotationAutoAlignUtil().also {
            rotateAbstractAutoAlignment = it
        }
        curCaption?.let {
            val currentRotation = it.rotation
            rotationUtil.handlePositionChange(
                currentRotation,
                rotateDegree,
                findNearestSnapRotation(currentRotation)
            )
            it.boundingRectangleVertices?.let { vertices ->
                refreshReferenceLineVisibility(calculateBoundingBoxCenter(vertices))
            }
            shouldSaveDragScaleRotationOperation = true
        }
    }

    /**
     * 创建旋转自动对齐工具
     */
    private fun createRotationAutoAlignUtil(): AbstractAutoAlignment {
        return object :
            AbstractAutoAlignment(ROTATE_AUTO_ALIGN_THRESHOLD, ROTATE_AUTO_ALIGN_SNAP_RANGE) {
            override fun triggerAutoAlignAction(
                isNeedMove: Boolean,
                finalDelta: Float,
                isAutoAlign: Boolean
            ) {
                if (isNeedMove) {
                    curCaption?.rotateCaption(finalDelta)
                }
            }
        }
    }

    /**
     * 处理响应拖拽
     */
    private fun processDrag(prePointF: PointF, nowPointF: PointF) {
        // 1. 提前返回检查
        val preP = editorEngine.mapLiveWindowViewToCanonical(prePointF) ?: return
        val nowP = editorEngine.mapLiveWindowViewToCanonical(nowPointF) ?: return

        // 2. 计算位移和边界框
        val caption = curCaption ?: return
        val vertices = caption.getBoundingRectangleVertices()
        val timeLinePointF = refreshAndClampTranslationPoint(
            PointF(nowP.x - preP.x, nowP.y - preP.y),
            vertices
        ) ?: return
        val curCenter = calculateBoundingBoxCenter(vertices)

        // 3. 初始化自动对齐工具（惰性初始化）
        val dragXUtil = dragXAbstractAutoAlignment ?: createDragXAutoAlignUtil().also {
            dragXAbstractAutoAlignment = it
        }
        val dragYUtil = dragYAbstractAutoAlignment ?: createDragYAutoAlignUtil().also {
            dragYAbstractAutoAlignment = it
        }

        // 4. 处理自动对齐逻辑
        dragXUtil.handlePositionChange(
            curCenter.x,
            timeLinePointF.x,
            DRAG_AUTO_ALIGN_POSITION_VALUE
        )

        dragYUtil.handlePositionChange(
            curCenter.y,
            timeLinePointF.y,
            DRAG_AUTO_ALIGN_POSITION_VALUE
        )

        // 5. 更新UI
        refreshReferenceLineVisibility(calculateBoundingBoxCenter(vertices))
        updateCaptionRenderingForPreview()
        shouldSaveDragScaleRotationOperation = true
    }

    /**
     * 处理双击字幕
     */
    private fun processDoubleClick(pointF: PointF) {
        handleClick(CLICK_TYPE_EDIT)
    }

    /**
     * 响应处理点击视频预览区域选择字幕
     *
     * @param curPoint 当前点击的位置
     */
    private fun selectingCaptionByClickingLiveWindow(curPoint: PointF) {
        val baseCaption: BaseCaption = selectCaptionByHandClick(curPoint) ?: return
        if (baseCaption != curCaption) {
            curCaption = baseCaption
        }
    }

    /**
     * 更新当前字幕的坐标（如果需要）
     * 如果当前没有选中字幕或字幕不可见，则清除绘制矩形
     */
    private fun updateCaptionCoordinatesIfNeeded(caption: BaseCaption) {
        // 1. 获取并转换边界坐标
        caption.boundingRectangleVertices?.let { vertices ->
            val mappedVertices = vertices.map { point ->
                editorEngine.mapCanonicalToLiveWindowView(point)
            }
            liveWindow.setDrawRect(mappedVertices)
        }
    }


    /**
     * 根据时间轴位置更新编辑矩形的可见性和状态
     *
     * @param position 当前时间轴位置(微秒)
     */
    private fun updateEditRectVisibility(position: Long) {
        var caption: BaseCaption? = null
        if (editorEngine.getCaptionsByTimelinePosition(position).isNotEmpty()) {
            caption = editorEngine.getCaptionsByTimelinePosition(position).first()
        }
        /**
         * 1.curCaption:当前轨道上面选中的字幕caption
         * 2.当前的位置是否在选中的字幕的区间内
         */
        showEditRect = isCaptionVisible(position, curCaption)
        caption?.let { cap ->
             // 更新字幕的编辑框位置
            if (isCaptionVisible(position, cap)) updateCaptionCoordinatesIfNeeded(cap)
        } ?: run { showEditRect = false }
    }

    /**
     * 返回预览区域点击某个点是否命中某个字幕
     */
    private fun selectCaptionByHandClick(curPoint: PointF): BaseCaption? {
        val position: Long = editorEngine.getTimelineCurrentPosition()
        val timeline: ITimeline = editorEngine.getCurrentTimeline() ?: return null
        val captionList = timeline.captionList ?: return null
        if (captionList.size == 0) return null
        captionList.asReversed().forEach { caption ->
            if (isCaptionVisible(position, caption).not()) return@forEach
            val list = caption.boundingRectangleVertices ?: return@forEach

            // 获取字幕矩形的4个顶点坐标
            val newList: MutableList<PointF> = ArrayList()
            list.forEach { point ->
                val pointF: PointF? = editorEngine.mapCanonicalToLiveWindowView(point)
                pointF?.let { newList.add(pointF) }
            }
            if (liveWindow.editTextPreviewView.curPointIsInRect(
                    curPoint.x.toInt(),
                    curPoint.y.toInt(),
                    newList
                )
            ) return caption
        }
        return null
    }

    /**
     * 创建X轴拖拽对齐工具
     */
    private fun createDragXAutoAlignUtil(): AbstractAutoAlignment {
        return object : AbstractAutoAlignment(
            DRAG_AUTO_ALIGN_THRESHOLD,
            DRAG_AUTO_ALIGN_SNAP_RANGE
        ) {
            override fun triggerAutoAlignAction(
                isNeedMove: Boolean,
                finalDelta: Float,
                isAutoAlign: Boolean
            ) {
                // 记录x轴方向是否发生位移
                needMove = isNeedMove
                xFinalDet = finalDelta
            }
        }
    }

    /**
     * 创建Y轴拖拽对齐工具
     */
    private fun createDragYAutoAlignUtil(): AbstractAutoAlignment {
        return object : AbstractAutoAlignment(
            DRAG_AUTO_ALIGN_THRESHOLD,
            DRAG_AUTO_ALIGN_SNAP_RANGE
        ) {
            override fun triggerAutoAlignAction(
                isNeedMove: Boolean,
                finalDelta: Float,
                isAutoAlign: Boolean
            ) {
                // 记录y轴方向是否发生位移
                needMove = needMove or isNeedMove
                if (needMove) {
                    curCaption?.translateCaption(PointF(xFinalDet, finalDelta))
                }
            }
        }
    }

    /**
     * 设置某个字幕预览区域是否显示编辑框
     */
    private fun showCaptionTextPreviewViewIfNeed(baseCaption: BaseCaption?) {
        baseCaption ?: return
        val position: Long = editorEngine.getTimelineCurrentPosition()
        if (isCaptionVisible(position, baseCaption).not()) {
            showEditRect = false
            return
        }
        updateCaptionCoordinatesIfNeeded(baseCaption)
    }

    /**
     * 字幕在当前时间轴位置是否可见
     */
    private fun isCaptionVisible(position: Long, caption: BaseCaption?): Boolean {
        return caption?.let { position in it.inTime until it.outTime } ?: false
    }

    /**
     * 刷新参考线
     */
    private fun refreshReferenceLineVisibility(centerPoint: PointF?) {
        centerPoint ?: return
        // 字幕时间轴坐标转换成窗口坐标
        val windowCenterPoint = editorEngine.mapCanonicalToLiveWindowView(centerPoint)
        val liveWindowCenterX = liveWindow.liveWindowWidth / EditorPreviewView.NUMBER_TWO
        val liveWindowCenterY = liveWindow.liveWindowHeight / EditorPreviewView.NUMBER_TWO
        var isVibrate = false
        if (abs(windowCenterPoint.x - liveWindowCenterX) <= AUTO_ALIGN_PIXEL_THRESHOLD) {
            // 当前编辑框中心点X轴坐标与窗口中心点X轴坐标相差小于等于阈值
            isVibrate = true
            liveWindow.setVerticalReferenceLineVisibility(true)
        } else {
            liveWindow.setVerticalReferenceLineVisibility(false)
        }
        if (abs(windowCenterPoint.y - liveWindowCenterY) <= AUTO_ALIGN_PIXEL_THRESHOLD) {
            // 当前编辑框中心点Y轴坐标与窗口中心点Y轴坐标相差小于等于阈值
            isVibrate = true
            liveWindow.setHorizontalReferenceLineVisibility(true)
        } else {
            liveWindow.setHorizontalReferenceLineVisibility(false)
        }
        if (isVibrate) {
           VibratorUtils.vibrateWithAutoCheck(mContext)
        }
    }

    /**
     * 获取最接近的自动对齐旋转角度
     *
     * @param currentDegree 当前旋转角度(单位:度)
     * @return 最接近的自动对齐角度
     */
    private fun findNearestSnapRotation(currentDegree: Float): Float {
        val normalizedDegree = currentDegree % FULL_CIRCLE_DEGREES
        val baseDegree = currentDegree - normalizedDegree
        return ROTATION_SNAP_POINTS
            .map { snapPoint ->
                val adjustedSnapPoint = if (currentDegree < 0) -snapPoint else snapPoint
                baseDegree + adjustedSnapPoint
            }
            .minByOrNull { snapDegree ->
                abs(snapDegree - currentDegree)
            } ?: currentDegree // 默认返回当前角度如果没有对齐点
    }

    /**
     * 刷新并限制时间线点的平移位置，确保不超出边界范围
     *
     * @param timelinePoint 当前时间线点坐标(可为null)
     * @param boundingBox 边界框的四个顶点坐标列表(可为null)
     * @return 调整后的时间线点坐标，如果输入为null则返回null
     */
    private fun refreshAndClampTranslationPoint(
        timelinePoint: PointF?,
        boundingBox: List<PointF>?
    ): PointF? {
        if ((timelinePoint == null) || (boundingBox == null) || (boundingBox.size < BOX_POINTS)) {
            return null
        }

        val currentTimeline = editorEngine.currentTimeline
        val maxTranslationX = currentTimeline.width / EditorPreviewView.NUMBER_TWO
        val maxTranslationY = currentTimeline.height / EditorPreviewView.NUMBER_TWO

        val boundingCenter = calculateBoundingBoxCenter(boundingBox)
        val newCenter = PointF(
            boundingCenter.x + timelinePoint.x,
            boundingCenter.y + timelinePoint.y
        )

        // 限制X轴平移范围
        timelinePoint.x = when {
            newCenter.x > maxTranslationX -> (maxTranslationX - boundingCenter.x)
            newCenter.x < -maxTranslationX -> (-maxTranslationX - boundingCenter.x)
            else -> timelinePoint.x
        }

        // 限制Y轴平移范围
        timelinePoint.y = when {
            newCenter.y > maxTranslationY -> maxTranslationY - boundingCenter.y
            newCenter.y < -maxTranslationY -> -maxTranslationY - boundingCenter.y
            else -> timelinePoint.y
        }
        return timelinePoint
    }

    /**
     * 计算边界框的中心点
     */
    private fun calculateBoundingBoxCenter(boundingBox: List<PointF>?): PointF {
        if ((boundingBox == null) || (boundingBox.size <= EditTextPreviewView.RIGHT_BOTTOM_INDEX)) {
            return PointF(0f, 0f)
        }
        return PointF(
            (boundingBox[EditTextPreviewView.LEFT_TOP_INDEX].x + boundingBox[EditTextPreviewView.RIGHT_BOTTOM_INDEX].x)
                    / EditorPreviewView.NUMBER_TWO,
            (boundingBox[EditTextPreviewView.LEFT_TOP_INDEX].y + boundingBox[EditTextPreviewView.RIGHT_BOTTOM_INDEX].y)
                    / EditorPreviewView.NUMBER_TWO
        )
    }

    companion object {

        /**
         * 编辑框的顶点数
         */
        private const val BOX_POINTS = 4

        /**
         * 自动旋转对齐的最大距离阈值
         */
        private const val ROTATE_AUTO_ALIGN_THRESHOLD: Float = 5f

        /**
         * 自动旋转已经对齐的相等距离阈值，小于这个差值就默认相等
         */
        private const val ROTATE_AUTO_ALIGN_SNAP_RANGE: Float = 0.1f

        /**
         * 全角度
         */
        private const val FULL_CIRCLE_DEGREES: Float = 360f

        /**
         * 旋转的角度阈值
         */
        private val ROTATION_SNAP_POINTS: FloatArray = floatArrayOf(0f, 90f, 180f, 270f)

        /**
         * 拖拽自动对齐最大阈值
         */
        private const val DRAG_AUTO_ALIGN_THRESHOLD: Float = 20f

        /**
         * 拖拽自动对齐相等阈值，小于这个差值就默认相等
         */
        private const val DRAG_AUTO_ALIGN_SNAP_RANGE: Float = 0.1f

        /**
         * 拖拽自动对齐的初始位置值
         */
        private const val DRAG_AUTO_ALIGN_POSITION_VALUE: Float = 0f

        /**
         * 默认的缩放比例值
         */
        private const val BASIC_CAPTION_SCALE_FACTOR = 1f

        /**
         * 最大缩放因子
         */
        private const val MAX_CAPTION_SCALE_FACTOR: Float = 15f

        /**
         * 最小缩放因子
         */
        private const val MIN_CAPTION_SCALE_FACTOR: Float = 0.5f

        /**
         * 自动对齐的像素阈值
         */
        private const val AUTO_ALIGN_PIXEL_THRESHOLD = 2

        private const val TAG = "EditorCaptionState"
    }

    /**
     * 当前时码线所在位置是否有足够的轨道空间做特效 文字需要大于100ms
     *
     * @return
     */
    override fun isCurrentTimeHasEnoughEmptyRange(): Boolean {
        return mUIController?.isCurrentTimeHasEnoughEmptyRange
            ?: EffectTrackRangeHelper.isCurrentTimeHasEnoughEmptyRange(
                mEditorEngine.timelineDuration,
                mEditorEngine.timelineCurrentPosition,
                videoFxs
            )
    }
}