/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - OnLoadingListenerProxy.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/6/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/6/19  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.videoeditorpage.memories.resource.listener;


import com.oplus.gallery.foundation.util.thread.MainSwitchHandler;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListener;

import java.util.List;

public class OnLoadingListenerProxy<T> implements OnLoadingListener<T> {
    private OnLoadingListener<T> mOnLoadingListener;

    public OnLoadingListenerProxy(OnLoadingListener<T> onLoadingListener) {

        mOnLoadingListener = onLoadingListener;
    }

    @Override
    public void onLoadingFinish(int code, List<T> tList) {
        if (mOnLoadingListener != null) {
            MainSwitchHandler.getInstance().post(new Runnable() {
                @Override
                public void run() {
                    mOnLoadingListener.onLoadingFinish(code, tList);
                }
            });
        }
    }

    @Override
    public void onIconDownloadFinish(T t) {
        if (mOnLoadingListener != null) {
            MainSwitchHandler.getInstance().post(new Runnable() {
                @Override
                public void run() {
                    mOnLoadingListener.onIconDownloadFinish(t);
                }
            });
        }
    }

    @Override
    public void onIconDownloadError(int errCode, T t) {
        if (mOnLoadingListener != null) {
            MainSwitchHandler.getInstance().post(new Runnable() {
                @Override
                public void run() {
                    mOnLoadingListener.onIconDownloadError(errCode, t);
                }
            });
        }
    }

    @Override
    public void onLoadingError(int errCode) {
        if (mOnLoadingListener != null) {
            MainSwitchHandler.getInstance().post(new Runnable() {
                @Override
                public void run() {
                    mOnLoadingListener.onLoadingError(errCode);
                }
            });
        }
    }
}
