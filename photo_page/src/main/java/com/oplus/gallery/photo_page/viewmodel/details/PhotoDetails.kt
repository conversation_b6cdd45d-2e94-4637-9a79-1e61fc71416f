/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetails.kt
 ** Description : 大图内容详情信息加载类
 ** Version     : 1.0
 ** Date        : 2025/3/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>                  2025/3/17       1.0     create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.details

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Environment
import android.util.Rational
import androidx.core.content.ContextCompat
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIEW_GALLERY_EXIT_ALPHA_DISABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIEW_GALLERY_THEME
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_VIEW_GALLERY_THEME_COMMON
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper.getBoolean
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_DATE_MODIFIED_IN_SEC
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_FILE_SIZE
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_LAT_LONG
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.business_lib.model.data.base.MediaDetails
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem.EMPTY_ITEM
import com.oplus.gallery.business_lib.model.data.base.item.getMediaUri
import com.oplus.gallery.business_lib.model.data.base.item.isCShotPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_FAST_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_CSHOT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_FAST_VIDEO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_GIF
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_NONE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_OLD_SLOW_MOTION
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_PANORAMA
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_RAW
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_SLOW_MOTION
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.model.data.local.set.LocalAlbum
import com.oplus.gallery.business_lib.model.data.location.api.DetailsAddressResolver
import com.oplus.gallery.business_lib.model.data.location.api.MatchLevel
import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.foundation.util.brandconfig.ProductBrand
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.geo.GPS
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.isGif
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.BLANK_STRING
import com.oplus.gallery.foundation.util.text.TextUtil.DOT
import com.oplus.gallery.foundation.util.text.TextUtil.DOWN_LINE
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_COMMA_SEPARATOR
import com.oplus.gallery.foundation.util.text.TextUtil.STRIKE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter
import com.oplus.gallery.framework.datatmp.R
import com.oplus.gallery.photo_page.ui.section.details.PhotoDetailsCardViewManager.Companion.MAP_WINDOW_SIZE
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.CoroutineScope
import java.io.File
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.NumberFormat
import kotlin.math.abs
import kotlin.math.pow

/**
 * 大图内容详情信息加载类
 */
internal class PhotoDetails(
    private val pageViewModel: PhotoViewModel,
    private var photoItemViewData: PhotoItemViewData,
    private val viewModelScope: CoroutineScope
) {
    /**
     * context为PhotoViewModel的ApplicationContext
     */
    private val context: Context = pageViewModel.viewModelApplication

    /**
     * mediaItem是从photoItemViewData中通过dataLoading获取的mediaItem，在创建前判过空，所以这里可以！！，强制不为空
     */
    private var mediaItem: MediaItem = pageViewModel.dataLoading.getMediaItem(photoItemViewData) ?: EMPTY_ITEM

    /**
     * 存储用于展示的字段、经纬度、icon的Id等能直接被加载到view中的数据；
     */
    internal val photoDetailsViewData: PhotoDetailsViewData = PhotoDetailsViewData()

    /**
     * 若该mediaItem为图片则该对象为图片的缩略图，若为视频则为null
     *
    private var thumbnail: Bitmap? = null
     */

    /**
     * 加载MediaItem的details。
     */
    private var mediaDetails: AsyncObj<Any?>? = null

    /**
     * 加载Image MediaItem的直方图。
     */
    /**
    private val imageHistogramData = AsyncObj(viewModelScope) {
    photoDetailsViewData.histogram = getItemHistogram(thumbnail)
    thumbnail = null
    onItemDataChangeListener?.onDataChange(this)
    }
     */

    /**
     * [PhotoDetails]的数据加载完成后的数据监听通知
     */
    internal var onItemDataChangeListener: OnItemDataChangeListener? = null

    /**
     * 创建对象时调用
     * 加载数据。
     */
    init {
        //[PhotoDetails]初始化时开始加载数据
        loadPhotoDetails()
    }

    /**
     * 只要加载或者重新数据就会运行
     * 获取当前mediaItem对象的详情信息
     */
    internal fun loadPhotoDetails(newItem: PhotoItemViewData? = null): PhotoDetailsViewData? {
        if (newItem != null) {
            mediaItem = pageViewModel.dataLoading.getMediaItem(newItem) ?: EMPTY_ITEM
            photoItemViewData = newItem
        }
        //如果mediaItem没有加载完毕
        if (mediaItem.isLoaded.not()) {
            GLog.w(TAG, LogFlag.DL) { "<loadPhotoDetails> mediaItem is not loaded: ${mediaItem.path}" }
            return null
        }

        //同步加载的内容
        loadPhotoDetailsSynchronously()

        //配置异步任务，保证每次都能重新加载
        initPhotoDetailsAsynchronously()

        //异步加载的内容
        loadPhotoDetailsAsynchronously()

        //数据变化时调用数据变化监听
        onItemDataChangeListener?.onDataChange(this@PhotoDetails)
        return photoDetailsViewData
    }

    /**
     * 只要加载数据就会运行
     * 同步加载的详情信息
     */
    private fun loadPhotoDetailsSynchronously() {
        //同步加载日期
        photoDetailsViewData.date = getItemDate()
        //同步加载云备份
        photoDetailsViewData.isCloudGlobal = isCloudFileExist()
        //同步加载文件全名
        photoDetailsViewData.fullName = getItemFullName()
        //同步加载经纬度
        photoDetailsViewData.latLong = getItemLatLong()
        //同步判断经纬度是否符合获取地址的规则，异步加载地址
        getItemLocation()

        /**
        //同步加载缩略图,startLoadContent会切回到主线程，因此在主线程直接进行获取了
        if (!mediaItem.isVideo) {
        pageViewModel.contentLoading.startLoadContent(photoItemViewData, SizeType.FullThumb(LOAD_TYPE_FOCUS_THUMBNAIL)) { drawable ->
        //获取缓存中的缩略图
        thumbnail = drawable?.toThumbnailDrawable()?.bitmap
        //进行算法时，异步加载直方图
        imageHistogramData.getIt {}
        }
        }
         */
    }

    /**
     * 只要加载数据就会运行；按mediaItem类型有不同的加载数据内容（视频与图片加载数据不一致）
     * 初始化异步任务
     */
    private fun initPhotoDetailsAsynchronously() {
        mediaDetails = AsyncObj(viewModelScope) {
            val details = mediaItem.getDetails(context) ?: return@AsyncObj
            //异步加载格式
            photoDetailsViewData.mimeType = getItemMimetype(details)
            //异步加载宽高
            photoDetailsViewData.widthHeight = getItemWidthHeight(details)
            //异步加载像素
            photoDetailsViewData.resolution = getItemResolution(details)
            //异步加载大小
            photoDetailsViewData.size = getItemSize(details)
            //异步加载存储路径
            photoDetailsViewData.pathString = getItemPath(details)
            //异步加载存储路径值
            photoDetailsViewData.pathValue = getItemPathValue(details)
            //异步加载来源
            photoDetailsViewData.fromAlbumName = getItemFromAlbumName()
            //异步加载来源值
            photoDetailsViewData.fromAlbumBundle = getItemFromAlbumBundle()
            //判断mediaItem是否为视频
            if (mediaItem.isVideo) {
                //异步加载视频镜头信息
                getVideoLensInfo(details)
                //加载镜头固定焦距和镜头固定光圈后判断两个参数是否有值
                photoDetailsViewData.itemHasFocalLengthAndAperture = itemHasFocalLengthAndAperture()
                //异步加载镜头信息
                photoDetailsViewData.cameraLensValue = getItemCameraLensValue(details)
                //异步加载视频类型
                photoDetailsViewData.videoModel = getItemVideoModel()
                //异步加载HDR类型
                photoDetailsViewData.hdrFormat = getItemHDRFormat()
                //异步加载帧率
                photoDetailsViewData.fps = getItemFPS(details)
                //异步加载时长
                photoDetailsViewData.duration = getItemDurationInSec(details)
            } else {
                //异步加载闪光灯
                photoDetailsViewData.hasFlash = getItemFlash(details)
                //异步加载图片类型
                photoDetailsViewData.imageModel = getItemImageModel()
                //异步加载拍摄设备
                val model = details.getDetail(MediaDetails.INDEX_MODEL) as? String
                photoDetailsViewData.shootingInfo = getItemShootingInfo(model)
                //异步加载镜头固定焦距
                photoDetailsViewData.focalLength = getItemFocalLength(details)
                //异步加载镜头固定光圈
                photoDetailsViewData.aperture = getItemAperture(details)
                //加载镜头固定焦距和镜头固定光圈后判断两个参数是否有值
                photoDetailsViewData.itemHasFocalLengthAndAperture = itemHasFocalLengthAndAperture()
                //异步加载镜头信息
                photoDetailsViewData.cameraLens = getItemCameraLens(details)
                //异步加载镜头数值
                photoDetailsViewData.cameraLensValue = getItemCameraLensValue(details)
                //异步加载ISO
                photoDetailsViewData.iso = getItemISO(details)
                //异步加载曝光值
                photoDetailsViewData.exposureValue = getItemEv(details)
                //异步加载快门速度
                photoDetailsViewData.shutterSpeed = getItemShutterSpeed(details)
                //异步加载拍摄时实际光圈
                photoDetailsViewData.actualAperture = getItemActualAperture(details)
                //异步加载拍摄时实际焦距
                photoDetailsViewData.actualFocalLength = getItemActualFocalLength(details)
            }
        }
    }

    /**
     * 只要加载数据就会运行
     * 异步加载的详情信息
     */
    private fun loadPhotoDetailsAsynchronously() {
        //异步加载details，以下为details加载后获取的内容；主要是exif和metadata
        mediaDetails?.getIt {
            onItemDataChangeListener?.onDataChange(this@PhotoDetails)
        }
    }

    /**
     * 根据经纬度加载地址。
     * 判断经纬度是否可用是同步不耗时的，加载地址是耗时的，因此加载地址需要在异步线程内完成，在回调里返回数值
     * 由于可能会出现异步线程中加载完数据的情况，并且回调里有刷新页面的操作，因此需要在onDataChange里加上runOnUiThread
     */
    private fun getItemLocation() {
        //获取经纬度
        val longitudeAndLatitude = DoubleArray(2)
        mediaItem.getLatLong(longitudeAndLatitude)
        //判断经纬度是否可用
        if (GPS.isLatLngValid(longitudeAndLatitude)) {
            //独立创建地址获取器对象，避免多个PhotoDetails加载时冲突
            var sAddressResolver: DetailsAddressResolver? = DetailsAddressResolver(context)
            //异步加载地址
            sAddressResolver?.resolveAddress(longitudeAndLatitude) { _, configAddress ->
                //从完整地址中只获取从城市到门牌号，避免地缘政治风险
                var address = configAddress?.getFullAddress(MatchLevel.MATCH_CITY, MatchLevel.MATCH_STREET_NO)
                // 某些照片的地点信息获取不到，添加兜底策略，按照地点精细度优先级获取标题
                if (address.isNullOrEmpty()) {
                    address = configAddress?.getTitleByMatchPriority()
                }
                //确实是没有地址，兜底策略，显示暂无地点信息
                if (address.isNullOrEmpty()) {
                    address = ContextCompat.getString(context, com.oplus.gallery.photo_page.R.string.photopage_details_no_location_information)
                }
                //赋值
                photoDetailsViewData.address = address
                //释放AddressResolver地址获取器
                sAddressResolver?.cancel()
                sAddressResolver?.destroy()
                sAddressResolver = null
                //数据回调，注意这时候是在异步线程里的，由于onDataChange里有刷新页面的操作，因此需要在onDataChange里加上runOnUiThread
                onItemDataChangeListener?.onDataChange(this@PhotoDetails)
            }
        } else {
            //异步加载地址
            photoDetailsViewData.address =
                ContextCompat.getString(context, com.oplus.gallery.photo_page.R.string.photopage_details_no_location_information)
            //这里是同步回调
            onItemDataChangeListener?.onDataChange(this@PhotoDetails)
        }
    }

    /**
     * 只要加载数据就会运行
     * 加载日期。
     * - 具体日期：比如2025年1月1日 15:00
     */
    private fun getItemDate(): String {
        //如果没有date信息
        if (mediaItem.dateTakenInMs <= 0L) {
            GLog.w(TAG, LogFlag.DL) { "<getItemDate> dateTakenInMs is zero: ${mediaItem.path}" }
            return STRIKE
        }
        return TimeUtils.getFormatYMDHMDate(context, mediaItem.dateTakenInMs)
    }

    /**
     * 只要加载数据就会运行
     * 是否云同步了。
     */
    private fun isCloudFileExist(): Boolean {
        return mediaItem.isCloudFileExist
    }

    /**
     * 只要加载数据就会运行
     * 获取文件名全称。
     */
    private fun getItemFullName(): String {
        //如果没有path信息
        if (mediaItem.filePath.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemFullName> path is empty: ${mediaItem.path}" }
            return STRIKE
        }
        val lastSlashIndex = mediaItem.filePath.lastIndexOf(File.separatorChar.toString())
        val fullName = mediaItem.filePath.substring(lastSlashIndex + 1)
        return if (fullName != mediaItem.filePath) {
            val nameInfo = fullName.split(DOT)
            if (nameInfo.size > 1) {
                //截图和录屏优化处理
                val name = doNotDisplayTheHashValueAtTheEndOfTheScreenshotFileName(nameInfo[0])
                val value = fullName.replace(nameInfo[0], name)
                //大写文件后缀名
                val mimeType = nameInfo[nameInfo.size - 1].uppercase()
                value.replace(nameInfo[nameInfo.size - 1], mimeType)
            } else {
                //截图优化处理
                doNotDisplayTheHashValueAtTheEndOfTheScreenshotFileName(fullName)
            }
        } else {
            STRIKE
        }
    }

    /**
     * 只要是OPPO手机截屏和录屏都会优化
     * 不展示截屏和录屏名称最后的hash值。（不影响资源文件原名称）
     */
    private fun doNotDisplayTheHashValueAtTheEndOfTheScreenshotFileName(nameOrigin: String): String {
        var name = nameOrigin
        if (name.startsWith(SCREENSHOT_FIRST_NAME) || name.startsWith(RECORD_FIRST_NAME)) {
            val lastUnderlineIndex = name.lastIndexOf(DOWN_LINE)
            name = name.substring(0, lastUnderlineIndex - 1)
        }
        return name
    }

    /**
     * 只要加载数据就会运行
     * 获取经纬度。
     */
    private fun getItemLatLong(): DoubleArray? {
        val longitudeAndLatitude = DoubleArray(2)
        //获取经纬度
        mediaItem.getLatLong(longitudeAndLatitude)
        //经纬度是否合理
        if (GPS.isLatLngInvalid(longitudeAndLatitude)) {
            return null
        }
        return longitudeAndLatitude
    }

    /**
     * 只要加载数据就会运行
     * 取值范围：
     * 设备信息--exif中model的数据 eg:OPPO FIND N5 或者编号 PKZ110
     * 截屏--在screenShooting目录下的文件
     * 无相机信息--null
     */
    private fun getItemShootingInfo(model: String?): String {
        //获取拍摄设备
        if (model.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemShootingInfo> model is empty: ${mediaItem.path}" }
            return EMPTY_STRING
        }
        return model
    }

    /**
     * 只要加载数据就会运行
     * 像素信息
     */
    private fun getItemResolution(mediaDetails: MediaDetails): String {
        val withHeight = mediaDetails.getDetail(MediaDetails.INDEX_WIDTH_HEIGHT) as? String
        if (withHeight.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemResolution> width or height is empty: ${mediaItem.path}" }
            return STRIKE
        }
        val strings = if (withHeight.contains(MediaDetails.MULTIPLICATION_SIGN)) {
            //若存在乘号，切割
            withHeight.split(MediaDetails.MULTIPLICATION_SIGN)
        } else {
            null
        }
        //像素计算
        val resolution = if (strings?.isNotEmpty() == true && strings.size == 2) {
            val with = (strings[0].replace(BLANK_STRING, EMPTY_STRING)).toLong()
            val height = (strings[1].replace(BLANK_STRING, EMPTY_STRING)).toLong()
            val number = with * height
            when {
                number >= DIGIT_BILLION -> "${formatResolution(number / DIGIT_BILLION)}BP"
                number >= DIGIT_MILLION -> "${formatResolution(number / DIGIT_MILLION)}MP"
                number >= DIGIT_THOUSAND -> "${formatResolution(number / DIGIT_THOUSAND)}KP"
                number >= DIGIT_ONES -> "${formatResolution(number / DIGIT_ONES)}P"
                else -> number.toString()
            }
        } else {
            STRIKE
        }
        return resolution
    }

    /**
     * 只要加载数据就会运行
     * 尺寸
     */
    private fun getItemWidthHeight(mediaDetails: MediaDetails): String {
        val withHeight = mediaDetails.getDetail(MediaDetails.INDEX_WIDTH_HEIGHT) as? String
        if (withHeight.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemWidthHeight> width or height is empty: ${mediaItem.path}" }
            return STRIKE
        }
        return withHeight
    }

    /**
     * 只要加载数据就会运行
     * 大小
     */
    private fun getItemSize(mediaDetails: MediaDetails): String {
        val size = mediaDetails.getDetail(MediaDetails.INDEX_SIZE) as? Long
        if (size == null) {
            GLog.w(TAG, LogFlag.DL) { "<getItemSize> size is empty: ${mediaItem.path}" }
            return STRIKE
        }
        return FilePathUtils.getUnitValue(context, size)
    }

    /**
     * 只要加载数据就会运行
     * 编码格式
     */
    private fun getItemMimetype(mediaDetails: MediaDetails): String {
        val strings = if (mediaItem.isVideo) {
            //视频获取编码格式
            (mediaDetails.getDetail(MediaDetails.INDEX_VIDEO_CODE_MIMETYPE) as? String)?.split(TextUtil.LEFT_SLASH)
        } else {
            //图片返回mimeType信息
            if (mediaItem.mimeType.isNullOrEmpty()) {
                GLog.w(TAG, LogFlag.DL) { "<getItemMimetype> mimeType is empty: ${mediaItem.path}" }
                return STRIKE
            }
            mediaItem.mimeType?.split(TextUtil.LEFT_SLASH)
        }
        return strings?.getOrNull(AppConstants.Number.NUMBER_1)?.uppercase() ?: STRIKE
    }

    /**
     * 只要加载数据就会运行
     * 存储文件夹路径，展示到文件所在文件夹
     */
    private fun getItemPath(mediaDetails: MediaDetails): String {
        //如果没有path信息
        if (mediaItem.filePath.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemPath> filePath is empty: ${mediaItem.path}" }
            return EMPTY_STRING
        }
        //展示文件路径
        val pathValue = mediaDetails.getDetail(MediaDetails.INDEX_PATH) as? String ?: EMPTY_STRING
        val isTablet = getBoolean(ConfigID.Common.SystemInfo.IS_TABLET, defValue = false, expDefValue = false)
        val path = OplusEnvironment.transformPath(pathValue, isTablet)
        val lastSlashIndex = path.lastIndexOf(File.separatorChar.toString())
        val fullName = path.substring(lastSlashIndex + 1)
        return path.replace(fullName, EMPTY_STRING)
    }

    /**
     * 只要加载数据就会运行
     * 存储路径的string值
     */
    private fun getItemPathValue(mediaDetails: MediaDetails): String {
        //如果没有path信息
        if (mediaItem.filePath.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemPathValue> filePath is empty: ${mediaItem.path}" }
            return EMPTY_STRING
        }
        return mediaDetails.getDetail(MediaDetails.INDEX_PATH) as? String ?: EMPTY_STRING
    }

    /**
     * 只要加载数据就会运行
     * 图集名称
     */
    private fun getItemFromAlbumName(): String {
        //如果没有path信息
        if (mediaItem.path == null) {
            GLog.w(TAG, LogFlag.DL) { "<getItemFromAlbumName> path is null: ${mediaItem.path}" }
            return EMPTY_STRING
        }
        //获取父目录图集路径
        val albumParentPathType = getItemFromAlbumParentPathType()
        return when (albumParentPathType) {
            //卡证档案图集
            Local.PATH_ALBUM_CARD_CASE_ANY -> ContextCompat.getString(context, com.oplus.gallery.basebiz.R.string.main_title_card_case)
            //相机也是继承于本地图集，因此可以与其他本地图集一同获取
            Local.PATH_ALBUM_CAMERA_ANY, Local.PATH_ALBUM_ANY_ALL -> {
                /**
                 * 相机图集只展示中文简体，中文翻体，英文三种语言
                 * 为了与图集列表的名称保持一致，同时相机图集也是本地图集，因此将相机图集放在这里一起处理
                 *
                 * 合并图集的情况：给LocalAlbum的Path中的bucketId如果是合并图集中的一个bucketId
                 * 那LocalAlbum在创建时会将该合并图集下的所有bucketId一起带上
                 * 因此不需要额外对合并图集进行额外操作
                 */
                val bucketId = FilePathUtils.getBucketIdWithParentPath(getItemParentPath())
                //获取的bucketId有效
                if (bucketId != AppConstants.Number.NUMBER_MINUS_1) {
                    //获取LocalAlbum的名称
                    getLocalAlbumName(bucketId)
                } else {
                    EMPTY_STRING
                }
            }

            else -> EMPTY_STRING
        }
    }

    /**
     * 只要加载数据就会运行
     * 图集跳转用Bundle的信息
     */
    private fun getItemFromAlbumBundle(): Bundle? {
        //如果没有path信息
        if (mediaItem.path == null) {
            GLog.w(TAG, LogFlag.DL) { "<getItemFromAlbumBundle> path is null: ${mediaItem.path}" }
            return null
        }

        //获取父目录图集路径
        val albumParentPathType = getItemFromAlbumParentPathType()
        //获取根据类型设置id
        var id = albumParentPathType.toString()
        //如果是本地图集，则需要获取bucketId，指定目录
        if (albumParentPathType == Local.PATH_ALBUM_ANY_ALL) {
            /**
             * 合并图集的情况：给LocalAlbum的Path中的bucketId如果是合并图集中的一个bucketId
             * 那LocalAlbum在创建时会将该合并图集下的所有bucketId一起带上
             * 因此不需要额外对合并图集进行额外操作
             */
            val bucketId = FilePathUtils.getBucketIdWithParentPath(getItemParentPath())
            //获取的bucketId有效
            if (bucketId != AppConstants.Number.NUMBER_MINUS_1) {
                //设置跳转id
                id = "${Local.PATH_ALBUM_ANY_ALL}${File.separatorChar}$bucketId"
                //优先初始化LocalAlbum且设置LocalAlbum的BucketPath
                setLocalAlbumBucketPath(bucketId)
            }
        }

        val modelType = when (albumParentPathType) {
            //相机图集
            Local.PATH_ALBUM_CAMERA_ANY -> LocalAlbumModelGetter.TYPE_CAMERA_ALBUM
            //卡证档案图集
            Local.PATH_ALBUM_CARD_CASE_ANY -> LocalAlbumModelGetter.TYPE_CARD_CASE_ALBUM
            //其他本地图集
            Local.PATH_ALBUM_ANY_ALL -> LocalAlbumModelGetter.TYPE_LOCAL_ALBUM
            else -> null
        }
        return if (modelType != null) {
            //通用跳转用AlbumViewData
            val viewData = AlbumViewData(
                id = id,
                position = 0,
                modelType = modelType,
                isMediaAlbum = true,
                version = 0,
                title = getItemFromAlbumName(),
            )

            Bundle().apply {
                //通用跳转配置
                putInt(KEY_VIEW_GALLERY_THEME, VALUE_VIEW_GALLERY_THEME_COMMON)
                putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
                putBoolean(KEY_VIEW_GALLERY_EXIT_ALPHA_DISABLE, true)
            }
        } else {
            null
        }
    }

    /**
     * 只要加载数据就会运行
     * 所有照片和视频都会判断
     * 获取当前文件的父目录路径
     */
    private fun getItemParentPath(): String? {
        //如果没有path信息
        if (mediaItem.filePath.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemParentPath> filePath is null: ${mediaItem.path}" }
            return null
        }
        val lastIndex = if (mediaItem.isCShotPhoto()) {
            mediaItem.filePath.lastIndexOf(File.separatorChar.toString() + Dir.CSHOT_DIR_NAME)
        } else {
            mediaItem.filePath.lastIndexOf(File.separatorChar.toString())
        }
        return mediaItem.filePath.substring(0, lastIndex)
    }

    /**
     * 只要加载数据就会运行
     * 所有照片和视频都会判断
     * 获取当前文件的父目录图集路径类型
     */
    private fun getItemFromAlbumParentPathType(): Path? {
        //获取当前mediaItem的父目录
        val relativePath = FilePathUtils.getRelativePath(getItemParentPath())
        //判断父目录是什么类型
        return when (relativePath) {
            //相机
            Dir.CAMERA.relativePath -> Local.PATH_ALBUM_CAMERA_ANY
            //卡证档案
            Constants.Album.CardCase.CARD_CASE_DIR.relativePath -> Local.PATH_ALBUM_CARD_CASE_ANY
            //卡证档案
            Constants.Album.CardCase.CARD_ID_DIR.relativePath -> Local.PATH_ALBUM_CARD_CASE_ANY
            //本地图集
            else -> Local.PATH_ALBUM_ANY_ALL
        }
    }

    /**
     * 只要加载数据就会运行
     * 所有照片和视频都会判断
     * 如果是localAlbum，需要优先初始化，这样做的原因有两个：
     * 1：在图集初始化完成之前，点击进入大图，并点击跳转图集，此时对于localAlbum来说bucketPath还未设置，会判定为空
     * 为空的情况会跳过查询连拍图，详情可看[LocalAlbum]的[appendCShotWhereClause]方法
     * 避免这种情况发生，获取LocalAlbum对象，如果对象的bucketPath为空则赋值，不为空则采用缓存的值
     * 2：图集名称在重复命名时有截断机制，为了与图集列表保持一致，采用localAlbum.name获取名称
     *
     * 以上两种情况都需要获取localAlbum，DataManager.getMediaSet(path)里有缓存，因此不会创建过多localAlbum
     * @param bucketId 当前文件的bucketId
     */
    private fun initLocalAlbum(bucketId: Int): MediaSet? {
        //由于Path没有new方法，因此通过mediaItem的Path先找到根目录的Path
        var path = mediaItem.path
        while (path?.parent != null) {
            path = path.parent
        }
        //将要查询目录的Path切割
        val children = Local.PATH_ALBUM_ANY_ALL.toString().substring(1).split(TextUtil.LEFT_SLASH)
        children.forEach { child ->
            //建造对应的Path
            path = path?.getChild(child)
        }
        //给Path指定bucketId，只展示文件所在的目录下的图片和视频
        path = path?.getChild(bucketId)
        //获取准确的LocalAlbum
        return path?.let { DataManager.getMediaSet(it) }
    }

    /**
     * 只要加载数据就会运行
     * 所有照片和视频都会判断
     * * 用于给LocalAlbum设置BucketPath
     * 用以解决在图集初始化完成之前，点击进入大图，并点击跳转图集，此时对于localAlbum来说bucketPath还未设置，会判定为空
     * 为空的情况会跳过查询连拍图，详情可看[LocalAlbum]的[appendCShotWhereClause]方法
     * @link [com.oplus.gallery.photo_page.viewmodel.details.PhotoDetails.initLocalAlbum]
     * @param bucketId 当前文件的bucketId
     */
    private fun setLocalAlbumBucketPath(bucketId: Int) {
        val localAlbum = initLocalAlbum(bucketId)
        //判断是否获取正确的LocalAlbum且判断bucketPath是否为空
        if ((localAlbum != null) && (((localAlbum.inputEntry as? LocalAlbum.LocalAlbumEntry)?.bucketPath).isNullOrEmpty())) {
            //为空则赋值，让后续跳转时拿到里面的缓存LocalAlbum是包含连拍图的
            (localAlbum.inputEntry as LocalAlbum.LocalAlbumEntry).bucketPath =
                "${File.separatorChar}${FilePathUtils.getRelativePath(getItemParentPath())}"
        }
    }

    /**
     * 只要加载数据就会运行
     * 所有照片和视频都会判断
     * * 用于获取LocalAlbum的名称
     * 1.用以解决图集名称在重复命名时有截断机制，为了与图集列表保持一致，采用[LocalAlbum.name]获取名称
     * 2.拼成GIF或者拼图时，之前没有创建对应的文件夹时，保存结束会自动进入大图页，但对应的图集对象可能未初始化完成，此时点开详情，获取图集名称会为空，所以要用文件夹名称代替
     * @link [com.oplus.gallery.photo_page.viewmodel.details.PhotoDetails.initLocalAlbum]
     * @param bucketId 当前文件的bucketId
     */
    private fun getLocalAlbumName(bucketId: Int): String {
        val localAlbum = initLocalAlbum(bucketId)
        val bucketName = FilePathUtils.getBucketNameByBucketPath(getItemParentPath())
        return localAlbum?.name ?: bucketName
    }

    /**
     * 只要加载数据就会运行
     * 所有照片都会判断
     * 参考exif中TAG_FLASH的值，有值就会展示，如果为null则不展示（如截图、网存图片等无闪光灯信息）
     * 闪光灯
     */
    private fun getItemFlash(mediaDetails: MediaDetails): Boolean? {
        val flash = mediaDetails.getDetail(MediaDetails.INDEX_FLASH) as? MediaDetails.FlashState
        if (flash == null) {
            GLog.w(TAG, LogFlag.DL) { "<getItemFlash> flash is empty: ${mediaItem.path}" }
            return null
        }
        return flash.isFlashFired
    }

    /**
     * 只要加载数据就会运行
     * 所有照片都会判断、展示
     * 获取拍摄类型
     * 取值范围：
     * 无特殊模式--FORMAT_NONE
     * 截图--PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_IMAGE
     * 人像模式--FORMAT_PORTRAIT_BLUR
     * 连拍模式--FORMAT_CSHOT
     * olive模式--FORMAT_OLIVE
     * gif模式--FORMAT_GIF
     * raw模式--FORMAT_RAW
     * 全景模式--FORMAT_PANORAMA
     */
    private fun getItemImageModel(): Long {
        if (context.resources.getStringArray(R.array.model_srceenshots_folders).find { mediaItem.filePath.contains(it) }.isNullOrEmpty().not()) {
            return PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_IMAGE
        }
        var format = 0L
        imageModelList.forEach { imageModel ->
            //将可能出现的图片模式or起来
            format = format or imageModel
        }
        //获取该mediaItem包含的formation，可能存在多个，先判断需要单独判断的类型
        val model = mediaItem.getSupportFormat(format)
        //判断可能出现的图片模式
        imageModelList.forEach { imageModel ->
            when ((model and imageModel)) {
                //返回RAW的情况
                FORMAT_RAW -> {
                    if (ImageTypeUtils.isRawFilePath(mediaItem.path)) {
                        return FORMAT_RAW
                    }
                }

                //返回GIF的情况
                FORMAT_GIF -> {
                    if (isGif(mediaItem.mimeType)) {
                        return FORMAT_GIF
                    }
                }

                //返回连拍的情况
                FORMAT_CSHOT -> {
                    if (ImageTypeUtils.isCShot(mediaItem.path)) {
                        return FORMAT_CSHOT
                    }
                }

                // 返回Olive的情况
                FORMAT_OLIVE -> return FORMAT_OLIVE

                // 返回全景的情况
                FORMAT_PANORAMA -> return FORMAT_PANORAMA

                // 返回人像的情况
                FORMAT_PORTRAIT_BLUR -> return FORMAT_PORTRAIT_BLUR
            }
        }
        GLog.w(TAG, LogFlag.DL) { "<getItemImageModel> image model is NONE: ${mediaItem.path}" }
        return FORMAT_NONE
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 镜头类型：
     * 参考exif中TAG_LENS_MODEL的值（注意该值是androidx包中的ExifInterface）
     * 根据是什么类型的镜头、还有该镜头的焦距来展示该镜头是超广角、微距还是其他等；
     * back的话，还要根据实际等效焦距和数码变焦判断是哪种镜头；
     * 具体逻辑参考下面的每行备注
     */
    private fun getItemCameraLens(mediaDetails: MediaDetails): String {
        //获取镜头信息
        val lensModel = mediaDetails.getDetail(MediaDetails.INDEX_LENS_MODEL) as? String
        //为空或无值
        if (lensModel.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemCameraLens> lensModel is empty: ${mediaItem.path}" }
            //暂无镜头信息
            return PhotoDetailsConstants.PhotoDetailsLensModelKeys.NONE
        }
        return if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.FRONT)) {
            //前置摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.FRONT
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.MICRO)) {
            //显微摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.MICRO
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.MACRO)) {
            //微距摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.MACRO
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE)) {
            //超广角像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE)) {
            //广角摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO)) {
            //长焦摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK)) {
            //后置摄像头，根据镜头固定的等效焦距判断是什么镜头
            val focalLength = getItemFocalLengthValue(mediaDetails)
            if (focalLength == null || !itemHasFocalLengthAndAperture()) {
                GLog.w(TAG, LogFlag.DL) { "<getItemFocalLength> focalLength is empty: ${mediaItem.path}" }
                //主摄像头
                return PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK
            }
            if (focalLength < PhotoDetailsConstants.PhotoDetailsLensModelKeys.ZERO) {
                //主摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK
            } else if (focalLength < PhotoDetailsConstants.PhotoDetailsLensModelKeys.TWENTY) {
                //超广角摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE
            } else if (focalLength < PhotoDetailsConstants.PhotoDetailsLensModelKeys.FORTY) {
                //广角摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE
            } else {
                //长焦摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO
            }
        } else {
            //有摄像头信息，但是不符合产品要求的展示逻辑，直接只展示内容
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.HAS_INFO
        }
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值，且有镜头信息，exif中TAG_LENS_MODEL有值才会展示
     * 仅在[getItemCameraLens]中返回 #PhotoDetailsConstants.PhotoDetailsLensModelKeys.HAS_INFO 时使用
     * 代表的意思是有摄像头信息，但是不符合产品对镜头范围要求的展示逻辑，即不符合[getItemCameraLens]中对镜头类型的定义时，直接展示exif中TAG_LENS_MODEL的值；常用于适配第三方厂家对该值有信息录入
     * 获取exif中TAG_LENS_MODEL的String值
     */
    private fun getItemCameraLensValue(mediaDetails: MediaDetails): String {
        //获取镜头信息
        return mediaDetails.getDetail(MediaDetails.INDEX_LENS_MODEL) as? String ?: EMPTY_STRING
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值，且有镜头信息，exif中TAG_LENS_MODEL有值才会展示
     * 镜头固定焦距信息的数值
     * 展示情况：
     * 1.该图片有实际拍摄焦距的35mm等效焦距，即exif中TAG_FOCAL_LENGTH_IN_35MM_FILM有double值；且有数码变焦值，即exif中TAG_DIGITAL_ZOOM_RATIO有double值且不为0;用等效35mm等效焦距除以数码变焦值，得到该镜头固定焦距的等效焦距值
     * 2.当没有实际拍摄焦距的35mm等效焦距或者没有数码变焦值时；检查exif中TAG_LENS_MODEL是否包含镜头固定焦距等效焦距信息，如iPhone 16 Pro back camera 24mm f/1.2，其中24mm是等效焦距，返回数值24；但是有可能出错:如OPPO Find X8 back camera 8.93mm f/1.8此时8.93是原始固定焦距，不是等效固定焦距
     * 因此第2中情况中要排除OPPO品牌；产品周思雨表示暂时不好推动相机修改情况2的信息错误，因此如果OPPO第1中情况没有计算出数值，就不展示焦距和光圈信息
     */
    private fun getItemFocalLengthValue(mediaDetails: MediaDetails): Double? {
        try {
            //获取镜头信息
            var focalLength: String? = null
            //优先计算镜头固定等效焦距
            val focalLength35mm = getItemActualFocalLengthValue(mediaDetails)
            if (focalLength35mm != null) {
                //获取数码焦距
                val digitalZoomRatio = mediaDetails.getDetail(MediaDetails.INDEX_DIGITAL_ZOOM_RATIO) as? String
                if (!digitalZoomRatio.isNullOrEmpty()) {
                    val value = digitalZoomRatio.toDouble()
                    if (value > AppConstants.Number.NUMBER_0) {
                        focalLength = (focalLength35mm.toDouble().div(value)).toString()
                    }
                }
            }

            //如果无法计算
            if (focalLength.isNullOrEmpty()) {
                //再尝试从镜头信息中获取焦距信息
                val cameraLens = mediaDetails.getDetail(MediaDetails.INDEX_LENS_MODEL) as? String
                val maker = mediaDetails.getDetail(MediaDetails.INDEX_MAKE) as? String
                //是否OPPO品牌，存在MAKE字段，且字段包含OPPO品牌
                val isOPPOBrand = !maker.isNullOrEmpty() &&
                        (maker.contains(ProductBrand.OPPO_BRAND.brand) ||
                                maker.contains(ProductBrand.ONEPLUS_BRAND.brand) ||
                                maker.contains(ProductBrand.REALME_BRAND.brand))
                //以下判断会排除OPPO品牌，因为正常情况OPPO品牌会通过上面计算得到焦距；而OPPO品牌从镜头信息中获取到的焦距信息有误
                val shouldGetFocalLengthFromCameraLens = !isOPPOBrand && !cameraLens.isNullOrEmpty() && cameraLens.contains(UNIT_MM)
                //应该从镜头信息中获取焦距信息的情况
                if (shouldGetFocalLengthFromCameraLens) {
                    //在有镜头信息，且不是oppo品牌下，且有mm单位的，就读取镜头信息中的焦距信息
                    focalLength = extractDigitsBefore(cameraLens ?: EMPTY_STRING, UNIT_MM, START_FORM_BEFORE)
                }
            }

            return if (focalLength?.toDouble() == AppConstants.Number.NUMBER_0.toDouble()) null else focalLength?.toDouble()
        } catch (e: NumberFormatException) {
            return null
        }
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值，且有镜头信息，exif中TAG_LENS_MODEL有值才会展示
     * 镜头固定焦距信息的string
     * 规整镜头固定焦距的数值，且加上单位
     */
    private fun getItemFocalLength(mediaDetails: MediaDetails): String {
        val focalLength = getItemFocalLengthValue(mediaDetails)
        //规范各地方的十进制
        val focalLengthValue = if (focalLength != null) {
            TextUtil.decimalFormat(formatFocalLength(focalLength.toDouble()))
        } else {
            STRIKE
        }
        return "$focalLengthValue$BLANK_STRING$UNIT_MM"
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值，且有镜头信息，exif中TAG_LENS_MODEL有值才会展示
     * 镜头最大光圈的数值
     * 展示情况：
     * 1.检查exif中TAG_LENS_MODEL是否包含镜头固定焦距等效焦距信息，如iPhone 16 Pro back camera 24mm f/1.2，其中f/1.2是等效焦距，返回数值1.2
     * 2.获取固定焦距，即exif中TAG_MAX_APERTURE_VALUE
     */
    private fun getItemAperture(mediaDetails: MediaDetails): String {
        try {
            var aperture: String? = null
            //优先尝试从镜头信息中获取光圈信息
            val cameraLens = mediaDetails.getDetail(MediaDetails.INDEX_LENS_MODEL) as? String
            if (!cameraLens.isNullOrEmpty() && cameraLens.contains(UNIT_F_LEFT_SLASH)) {
                aperture = extractDigitsBefore(cameraLens, UNIT_F_LEFT_SLASH, START_FORM_AFTER)
            }

            //若镜头信息没有写，则获取镜头最大光圈
            if (aperture.isNullOrEmpty()) {
                //raw文件和普通图片一样，MAX_APERTURE_VALUE是Rational类型的String值，需要先转为String，然后再转为Rational
                val apertureValue = mediaDetails.getDetail(MediaDetails.INDEX_MAX_APERTURE_VALUE) as? String
                if (!apertureValue.isNullOrEmpty()) {
                    val value = Rational.parseRational(apertureValue.toString())
                    //光圈要展示成标准F光圈数值公式
                    aperture = apertureValueToFNumber(value.toDouble())
                }
            }

            //规范要展示的数字或者符号
            aperture = if (!aperture.isNullOrEmpty() && aperture.toDouble() != AppConstants.Number.NUMBER_0.toDouble()) {
                formatAperture(aperture.toDouble())
            } else {
                STRIKE
            }
            return "$ZERO_WIDTH_SPACE$UNIT_F$BLANK_STRING${TextUtil.decimalFormat(aperture)}"
        } catch (e: NumberFormatException) {
            return "$UNIT_F$BLANK_STRING$STRIKE"
        }
    }

    /**
     * 只要加载数据就会运行
     * 判断镜头固定焦距信息或者镜头固定光圈信息其中一个是否为空
     */
    private fun itemHasFocalLengthAndAperture(): Boolean {
        return (photoDetailsViewData.focalLength != "$STRIKE$BLANK_STRING$UNIT_MM") &&
                (photoDetailsViewData.focalLength != STRIKE) &&
                (photoDetailsViewData.aperture != "$UNIT_F$BLANK_STRING$STRIKE") &&
                (photoDetailsViewData.aperture != STRIKE)
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 照片ISO值，即exif中TAG_ISO_SPEED_RATINGS
     */
    private fun getItemISO(mediaDetails: MediaDetails): String {
        var iso = mediaDetails.getDetail(MediaDetails.INDEX_ISO) as? String
        if (iso.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemISO> iso is empty: ${mediaItem.path}" }
            return "$UNIT_ISO$BLANK_STRING$STRIKE"
        }
        iso = iso.replace(SPLIT_COMMA_SEPARATOR, DOT)
        return try {
            //规整iso值
            val isoValue = formatISO(TextUtil.decimalFormat(iso).toInt()).replace(BLANK_STRING, EMPTY_STRING)
            "$UNIT_ISO$BLANK_STRING$isoValue"
        } catch (e: NumberFormatException) {
            "$UNIT_ISO$BLANK_STRING$iso"
        }
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * EV曝光值，即exif中TAG_EXPOSURE_BIAS_VALUE
     */
    private fun getItemEv(mediaDetails: MediaDetails): String {
        val ev = mediaDetails.getDetail(MediaDetails.INDEX_EXPOSURE_BIAS_VALUE) as? String
        if (ev.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemEv> ev is empty: ${mediaItem.path}" }
            return "$UNIT_EV$BLANK_STRING$STRIKE"
        }
        val evValue = Rational.parseRational(ev)?.toDouble()
        val value = formatEV(evValue).replace(BLANK_STRING, EMPTY_STRING)
        return "$UNIT_EV$BLANK_STRING${TextUtil.decimalFormat(value)}"
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 快门速度，即exif中TAG_EXPOSURE_TIME
     *
     */
    private fun getItemShutterSpeed(mediaDetails: MediaDetails): String {
        val shutterSpeed = mediaDetails.getDetail(MediaDetails.INDEX_EXPOSURE_TIME) as? String
        if (shutterSpeed.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemShutterSpeed> shutterSpeed is empty: ${mediaItem.path}" }
            return "$STRIKE$BLANK_STRING$UNIT_S"
        }
        var value: String = shutterSpeed
        try {
            val time = value.toDouble()
            if ((time <= EXPOSURE_TIME_CONVERT_FRACTION) && (time > 0)) {
                val offset = OFFSET_HALF
                value = String.format(LocaleUtils.getLocale(ContextGetter.context), "%d", 1) + String.format(
                    LocaleUtils.getLocale(ContextGetter.context),
                    "/%d",
                    (offset + 1f / time).toInt()
                )
            } else {
                if (time % 1 == 0.0) {
                    value = time.toInt().toString()
                } else {
                    val format = NumberFormat.getNumberInstance(LocaleUtils.getLocale(ContextGetter.context))
                    format.maximumFractionDigits = 1
                    value = format.format(time)
                }
            }
            value = value.replace(BLANK_STRING, EMPTY_STRING)
            return "$value$BLANK_STRING$UNIT_S"
        } catch (e: NumberFormatException) {
            GLog.w(TAG, LogFlag.DL, "[getItemShutterSpeed]case:INDEX_EXPOSURE_TIME：$e")
            return "$STRIKE$BLANK_STRING$UNIT_S"
        }
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 实际图片焦距的35mm等效焦距数值，即exif中TAG_FOCAL_LENGTH_IN_35MM_FILM的数值
     */
    private fun getItemActualFocalLengthValue(mediaDetails: MediaDetails): Double? {
        val fovFocalLength = mediaDetails.getDetail(MediaDetails.INDEX_FOCAL_LENGTH_IN_35MM_FILM)
        //获取镜头实际焦距；raw中FOCAL_LENGTH是Int，普通图片是String
        val fovFocalLengthValue = try {
            //因此需要先获取值，然后采用toString的方式转为string进行后续处理
            if (!fovFocalLength?.toString().isNullOrEmpty()) {
                fovFocalLength.toString().replace(BLANK_STRING, EMPTY_STRING).toDouble()
            } else {
                GLog.w(TAG, LogFlag.DL) { "<getItemActualFocalLength> fovFocalLength is empty: ${mediaItem.path}" }
                null
            }
        } catch (e: NumberFormatException) {
            GLog.w(TAG, LogFlag.DL) { "<getItemActualFocalLength> fovFocalLength is error: ${mediaItem.path}" }
            null
        }
        return fovFocalLengthValue
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 优先获取35mm等效焦点的数值，没有时用实际焦距的数值兜底
     * 规整获取到的数值
     */
    private fun getItemActualFocalLength(mediaDetails: MediaDetails): String {
        val fovFocalLengthValue = getItemActualFocalLengthValue(mediaDetails)
        val fovFocalLength = if (fovFocalLengthValue != null) {
            //优先展示35mm等效焦距，手机拍摄会写的值，但是相机不会
            formatFocalLength(fovFocalLengthValue.toString().replace(BLANK_STRING, EMPTY_STRING).toDouble())
        } else if (!(mediaDetails.getDetail(MediaDetails.INDEX_FOCAL_LENGTH) as? String).isNullOrEmpty()) {
            //再展示实际等效焦距，相机拍摄的图片这个值才准
            try {
                val fovFocalLength = Rational.parseRational(mediaDetails.getDetail(MediaDetails.INDEX_FOCAL_LENGTH) as String).toDouble()
                formatFocalLength(fovFocalLength)
            } catch (e: NumberFormatException) {
                STRIKE
            }
        } else {
            GLog.w(TAG, LogFlag.DL) { "<getItemActualFocalLength> fovFocalLength is empty: ${mediaItem.path}" }
            STRIKE
        }
        return "$fovFocalLength$BLANK_STRING$UNIT_MM"
    }

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 展示实际光圈值，即exif中的TAG_F_NUMBER
     * [MediaDetails]中已做数据格式规整处理，直接展示即可
     */
    private fun getItemActualAperture(mediaDetails: MediaDetails): String {
        val aperture = mediaDetails.getDetail(MediaDetails.INDEX_APERTURE) as? String
        if (aperture == null) {
            GLog.w(TAG, LogFlag.DL) { "<getItemActualAperture> aperture is empty: ${mediaItem.path}" }
            return "$UNIT_F$BLANK_STRING$STRIKE"
        }
        val apertureValue = formatAperture(aperture.toDouble()).replace(BLANK_STRING, EMPTY_STRING)
        val value = TextUtil.decimalFormat(apertureValue)
        return "$ZERO_WIDTH_SPACE$UNIT_F$BLANK_STRING$value"
    }

    /**
     * 只要加载数据就会运行
     * 所有图片都会加载
     * 直方图
     */
    /**
    private fun getItemHistogram(bitmap: Bitmap?): Array<IntArray>? {
    if (bitmap == null) {
    GLog.w(TAG, LogFlag.DL) { "<getItemHistogram> thumbnail bitmap is empty: ${mediaItem.path}" }
    return null
    }

    //增加GTrace查看耗时情况
    return GTrace.trace<Array<IntArray>?>("$TAG.getItemHistogram") {
    //直方图数组
    val histogram = Array(RGB_COUNT_CONSTANT) { IntArray(RGB_NUMBER_COUNT_CONSTANT) { AppConstants.Number.NUMBER_0 } }
    //获取bitmap宽高
    val width = bitmap.width
    val height = bitmap.height
    //遍历所有像素
    for (x in 0 until width) {
    for (y in 0 until height) {
    val pixel = bitmap[x, y]
    //获取每个像素对应的RGB值
    (histogram[0][Color.red(pixel)])++
    (histogram[1][Color.green(pixel)])++
    (histogram[2][Color.blue(pixel)])++
    }
    }
    GTrace.traceEnd()
    histogram
    }
    }
     */

    /**
     * 只要加载数据就会运行
     * 只有当有相机信息时，即exif中TAG_MODEL有值才会展示
     * 主要从USER_DATA中获取各厂家的定制信息；里面包含的字段内容可以看[PhotoDetailsVideoDetailParser]中的注释
     * @param mediaDetails 媒体详情数据
     */
    private fun getVideoLensInfo(mediaDetails: MediaDetails) {
        //获取视频
        val lensInfo = mediaDetails.getDetail(MediaDetails.INDEX_VIDEO_USER_DATA) as? String
        val videoViewData = PhotoDetailsVideoDetailParser.getVideoLensViewData(lensInfo)
        //异步加载拍摄设备
        val model = videoViewData.shootingInfo
        photoDetailsViewData.shootingInfo = getItemShootingInfo(model)
        //异步加载镜头固定焦距
        val focalLength = videoViewData.focalLength
        photoDetailsViewData.focalLength = getVideoItemFocalLength(focalLength)
        //异步加载镜头固定光圈
        val aperture = videoViewData.aperture
        photoDetailsViewData.aperture = getVideoItemAperture(aperture)
        //异步加载镜头信息
        val lensModel = videoViewData.cameraLens
        photoDetailsViewData.cameraLens = getVideoItemCameraLens(lensModel, focalLength)
    }

    /**
     * 只要加载数据就会运行
     * 进行视频镜头焦距数值的规整
     * @param focalLength 镜头固定焦距
     */
    private fun getVideoItemFocalLength(focalLength: String?): String {
        //获取镜头固定焦距
        if (focalLength.isNullOrEmpty() || focalLength == STRIKE || focalLength.toDouble() == AppConstants.Number.NUMBER_0.toDouble()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemFocalLength> focalLength is empty: ${mediaItem.path}" }
            return "$STRIKE$BLANK_STRING$UNIT_MM"
        }
        return "${TextUtil.decimalFormat(formatFocalLength(focalLength.toDouble()))}$BLANK_STRING$UNIT_MM"
    }

    /**
     * 只要加载数据就会运行
     * 进行视频镜头光圈数值的规整
     * @param aperture 镜头固定光圈
     */
    private fun getVideoItemAperture(aperture: String?): String {
        //获取镜头固定光圈
        if (aperture.isNullOrEmpty() || aperture == STRIKE) {
            GLog.w(TAG, LogFlag.DL) { "<getItemAperture> aperture is empty: ${mediaItem.path}" }
            return "$UNIT_F$BLANK_STRING$STRIKE"
        }
        return "$ZERO_WIDTH_SPACE$UNIT_F$BLANK_STRING${TextUtil.decimalFormat(formatAperture(aperture.toDouble()))}"
    }

    /**
     * 只要加载数据就会运行
     * 获取镜头的规格，可参考方法[getItemCameraLens]，里面逻辑是一致的
     * @param lensModel 镜头型号
     * @param focalLength 焦距
     */
    private fun getVideoItemCameraLens(lensModel: String?, focalLength: String?): String {
        //为空或无值
        if (lensModel.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemCameraLens> lensModel is empty: ${mediaItem.path}" }
            //暂无镜头信息
            return PhotoDetailsConstants.PhotoDetailsLensModelKeys.NONE
        }
        return if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.FRONT)) {
            //前置摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.FRONT
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.MICRO)) {
            //显微摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.MICRO
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.MACRO)) {
            //微距摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.MACRO
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE)) {
            //超广角像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE)) {
            //广角摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO)) {
            //长焦摄像头
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO
        } else if (lensModel.contains(PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK)) {
            if (focalLength.isNullOrEmpty() || !itemHasFocalLengthAndAperture()) {
                GLog.w(TAG, LogFlag.DL) { "<getItemFocalLength> focalLength is empty: ${mediaItem.path}" }
                //主摄像头
                return PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK
            }
            val focalLengthValue = focalLength.toDouble()
            if (focalLengthValue < PhotoDetailsConstants.PhotoDetailsLensModelKeys.ZERO) {
                //主摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.BACK
            } else if (focalLengthValue < PhotoDetailsConstants.PhotoDetailsLensModelKeys.TWENTY) {
                //超广角摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.ULTRA_WIDE
            } else if (focalLengthValue < PhotoDetailsConstants.PhotoDetailsLensModelKeys.FORTY) {
                //广角摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.WIDE
            } else {
                //长焦摄像头
                PhotoDetailsConstants.PhotoDetailsLensModelKeys.TELEPHOTO
            }
        } else {
            //有摄像头信息，但是不符合产品要求的展示逻辑，直接只展示内容
            PhotoDetailsConstants.PhotoDetailsLensModelKeys.HAS_INFO
        }
    }

    /**
     * 只要加载数据就会运行
     * 拍摄模式
     * 取值范围：
     * 视频模式--FORMAT_NONE
     * 录屏--PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_VIDEO
     * 延时摄影--FORMAT_FAST_VIDEO
     * 慢动作--FORMAT_SLOW_MOTION
     * 慢动作--FORMAT_OLD_SLOW_MOTION
     */
    private fun getItemVideoModel(): Long {
        if (context.resources.getStringArray(R.array.model_srceenshots_folders).find { mediaItem.filePath.contains(it) }.isNullOrEmpty().not()) {
            return PhotoDetailsConstants.PhotoDetailsModelKeys.FORMAT_SCREENSHOT_VIDEO
        }
        var format = 0L
        videoModelList.forEach { videoModel ->
            //将可能出现的视频模式or起来
            format = format or videoModel
        }
        //获取该mediaItem包含的formation，可能存在多个，先判断需要单独判断的类型
        val model = mediaItem.getSupportFormat(format)
        //判断可能出现的图片模式
        videoModelList.forEach { videoModel ->
            when ((model and videoModel)) {
                //返回慢动作的情况
                FORMAT_SLOW_MOTION -> {
                    if (VideoTypeUtils.isSlowMotion(mediaItem.name)) {
                        return FORMAT_SLOW_MOTION
                    }
                }

                //返回慢动作的情况
                FORMAT_OLD_SLOW_MOTION -> {
                    if (VideoTypeUtils.isOldSlowMotion(mediaItem.name)) {
                        return FORMAT_OLD_SLOW_MOTION
                    }
                }

                //返回延时摄影的情况
                FORMAT_FAST_VIDEO -> {
                    if (mediaItem.tagFlags and FLAG_FAST_VIDEO != 0L) {
                        return FORMAT_FAST_VIDEO
                    }
                }
            }
        }
        GLog.w(TAG, LogFlag.DL) { "<getItemVideoModel> video model is NONE: ${mediaItem.path}" }
        return FORMAT_NONE
    }

    /**
     * 只要加载数据就会运行
     * 详细HDR类型
     * @return 杜比--HDR_TYPE_DOLBY； HLG--HDR_TYPE_HLG； HDR10--HDR_TYPE_HDR10; HDR10PLUS--HDR_TYPE_HDR10_PLUS; 无HDR--VIDEO_HDR_TYPE_UNKNOWN
     */
    private fun getItemHDRFormat(): Int {
        //里面就判断了是否是HDR类型
        return VideoTypeUtils.getOriginVideoHdrType(context, mediaItem.getMediaUri()?.toString(), mediaItem)
    }

    /**
     * 只要加载数据就会运行
     * 帧率，即mediaDetails中[setVideoFps]方法
     */
    private fun getItemFPS(mediaDetails: MediaDetails): String {
        /**
         * 对于慢动作视频，15.0.2之前的OS版本不展示fps，15.0.2及以后os版本展示fps，背景：
         * 相册带入了大图显示帧率的新需求，而老版本相机未升级（高帧慢动作的帧率的显示，是相册和相机一起修改完成的，
         * 但是相机的升级范围是15.0.2及之后，相册是通用），这样会导致一些场景下相册显示的帧率与录制的不一致。
         * 1.老版本15.0.2之前的版本，如果相册升级了，相机未升级(录制480，但相机只会写入tag: Oplus_0slow_motion_hfr_120:0,0,0,0)，就会有帧率显示不一致的问题。
         * 2.老版本录制高帧慢动作(录制480，但相机只会写入120)，传到高版本上，会存在帧率显示不一致的问题。
         *
         * 基于以上背景，与产品沟通确认最终结论：仅15.0.2及之后的OS版本展示慢动作的fps值
         * 即第一个判断条件 若为慢动作视频且OS版本不大于15.0.1时返回"- FPS"
         */
        if (OSVersionUtils.isAtMost(OSVersionUtils.OPLUS_OS_15_0_1) && (VideoTypeUtils.isSlowMotion(mediaItem.name))) {
            return "$STRIKE$BLANK_STRING$UNIT_FPS"
        }
        val fps = mediaDetails.getDetail(MediaDetails.INDEX_FPS) as? String
        if (fps.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemFPS> fps is empty: ${mediaItem.path}" }
            return "$STRIKE$BLANK_STRING$UNIT_FPS"
        }
        return "${formatFrameRate(fps).replace(BLANK_STRING, EMPTY_STRING)}$BLANK_STRING$UNIT_FPS"
    }

    /**
     * 视频使用
     * 时长，即mediaDetails中[TimeUtils.formatDuration(mApplication, mDurationInSec)]方法
     */
    private fun getItemDurationInSec(mediaDetails: MediaDetails): String {
        val duration = mediaDetails.getDetail(MediaDetails.INDEX_DURATION) as? String
        if (duration.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "<getItemDurationInSec> duration is empty: ${mediaItem.path}" }
            return STRIKE
        }
        return duration.replace(BLANK_STRING, EMPTY_STRING)
    }

    /**
     * 通过固定光圈数值计算固定光圈的FNumber
     * 物理公式为：aperture = 2* ln(FNumber) / ln(2.0) ->对数计算变换后：2* log2(FNumber)
     * 因此变换位置后的计算公式为：FNumber = 2^(aperture/2)
     * @param aperture 来自于元数据中的光圈double值
     * @return 计算后的FNumber的String值
     */
    private fun apertureValueToFNumber(aperture: Double): String {
        //备注中的//FNumber = 2^(aperture/2)
        val fNumber = 2.0.pow(aperture / 2)
        return fNumber.toString()
    }

    /**
     * 格式化像素字符串：格式化为整数
     * @param resolution 来自于元数据中的像素
     * @return 格式化后的像素字符串
     */
    private fun formatResolution(resolution: Long): String {
        return try {
            // 不保留小数点
            val df = DecimalFormat("#0")
            df.roundingMode = RoundingMode.HALF_UP
            df.format(resolution)
        } catch (e: NumberFormatException) {
            STRIKE
        }
    }

    /**
     * 格式化ISO字符串：格式化为整数
     * @param iso 来自于元数据中的ISO
     * @return 格式化后的ISO字符串
     */
    private fun formatISO(iso: Int): String {
        return try {
            // 不保留小数点
            val df = DecimalFormat("#0")
            df.roundingMode = RoundingMode.HALF_UP
            df.format(iso)
        } catch (e: NumberFormatException) {
            STRIKE
        }
    }

    /**
     * 格式化帧率字符串：格式化为整数
     * @param fps 来自于元数据中的帧率
     * @return 格式化后的帧率字符串
     */
    @SuppressLint("DefaultLocale")
    private fun formatFrameRate(fps: String): String {
        return try {
            // 不保留小数点
            val df = DecimalFormat("#0")
            df.roundingMode = RoundingMode.HALF_UP
            df.format(fps.toDouble())
        } catch (e: NumberFormatException) {
            STRIKE
        }
    }

    /**
     * 格式化曝光值double：只保留至少一位小数，与相机展示保持一致
     * @param ev 来自于元数据中的曝光值
     * @return 格式化后的曝光值字符串
     */
    private fun formatEV(ev: Double?): String {
        return if (ev != null) {
            try {
                // 只保留1位小数
                val df = DecimalFormat("#0.0")
                df.roundingMode = RoundingMode.HALF_UP
                df.format(ev)
            } catch (e: NumberFormatException) {
                STRIKE
            }
        } else {
            STRIKE
        }
    }

    /**
     * 格式化光圈double：只保留一位小数
     * @param aperture 来自于元数据中的光圈
     * @return 格式化后的光圈字符串
     */
    private fun formatAperture(aperture: Double): String {
        return try {
            // 保留1位小数
            val df = DecimalFormat("#0.0")
            df.roundingMode = RoundingMode.HALF_UP
            df.format(aperture)
        } catch (e: NumberFormatException) {
            EMPTY_STRING
        }
    }

    /**
     * 格式化焦距double：格式化为整数
     * @param focalLength 来自于元数据中的焦距
     * @return 格式化后的焦距字符串
     */
    private fun formatFocalLength(focalLength: Double?): String {
        return if (focalLength != null) {
            try {
                // 不保留小数点
                val df = DecimalFormat("#0")
                df.roundingMode = RoundingMode.HALF_UP
                df.format(focalLength)
            } catch (e: NumberFormatException) {
                EMPTY_STRING
            }
        } else {
            EMPTY_STRING
        }
    }

    /**
     * 用于找镜头信息中的数字参数，因为相机跟苹果等写入不一致，只能通过单位获取
     * 当前只用于找焦距和光圈的情况；镜头信息一般为：【镜头型号】【焦距】【光圈】焦距eg:22mm，光圈eg:f/1.2；因此需要根据单位往前或往后找到数值并返回其toString的结果。
     * @param text 原始数据
     * @param unit 要找的单位
     * @param startForm 数据在前面还是后面
     * @return 获取后只含数字的字符串
     */
    private fun extractDigitsBefore(text: String, unit: String, startForm: String): String {
        val lastIndex = text.lastIndexOf(unit)
        if (lastIndex < 0 || lastIndex >= text.length) return EMPTY_STRING

        val result = StringBuilder()
        if (startForm == START_FORM_BEFORE) {
            //往前找数字
            var i = lastIndex - 1
            while (i >= 0 && (text[i].isDigit() || text[i].toString() == DOT)) {
                result.insert(0, text[i]) // 向前插入，保证顺序正确
                i--
            }
        } else {
            //往后找数字
            var i = lastIndex + unit.length
            while (i < text.length && (text[i].isDigit() || text[i].toString() == DOT)) {
                result.append(text[i])
                i++
            }
        }
        return result.toString()
    }

    /**
     * 返回当前PhotoDetails内MediaItem的isVideo。
     */
    internal fun isVideo(): Boolean {
        return mediaItem.isVideo
    }

    /**
     * 判断需要刷新数据的情况
     * 当前以下数据变化会刷新PhotoDetails的情况：
     * id变化；mediaId变化；mime类型变化；文件路径变化；文件大小变化；修改时间变化；定位变化；云端同步状态变化
     */
    internal fun needUpdateData(newItem: PhotoItemViewData?): Boolean {
        if (newItem !is PhotoItemViewData) return false
        return ((newItem.id != photoItemViewData.id) ||
                (newItem.mediaId != photoItemViewData.mediaId) ||
                (newItem.mimeType != photoItemViewData.mimeType) ||
                (newItem.filePath != photoItemViewData.filePath) ||
                (newItem.supportedAbilities[SUPPORT_FILE_SIZE] != photoItemViewData.supportedAbilities[SUPPORT_FILE_SIZE]) ||
                (newItem.supportedAbilities[SUPPORT_DATE_MODIFIED_IN_SEC] != photoItemViewData.supportedAbilities[SUPPORT_DATE_MODIFIED_IN_SEC]) ||
                ((newItem.supportedAbilities[SUPPORT_LAT_LONG] as? DoubleArray)?.contentEquals(
                    photoItemViewData.supportedAbilities[SUPPORT_LAT_LONG] as? DoubleArray
                ) == false) ||
                (newItem.isCloudFileExist != photoItemViewData.isCloudFileExist))
    }

    /**
     * 返回当前PhotoDetails内MediaItem的MediaPath
     */
    internal fun getMediaPath(): Path {
        return mediaItem.path
    }

    /**
     * 获取当前PhotoDetails内MediaItem媒体库的id
     */
    internal fun getMediaId(): Int {
        return mediaItem.mediaId
    }

    /**
     * 判断当前PhotoDetails是否在地图的的Window中
     */
    internal fun isCurrentInFocusWindow(): Boolean {
        val position = photoItemViewData.position
        val focusSlot = pageViewModel.dataLoading.focusSlot
        val offset = (position - focusSlot)
        val absOffset = abs(offset)
        val result = absOffset <= MAP_WINDOW_SIZE
        GLog.d(
            TAG, LogFlag.DL, "isCurrentInFocusWindow this MediaId ${getMediaId()}, positon $position, focusSlot $focusSlot, " +
                    "offset $offset, absOffset $absOffset, result $result"
        )
        return result
    }


    companion object {
        private const val TAG = "PhotoDetails"

        //初始曝光值偏移值
        private const val OFFSET_HALF: Float = 0.5f

        //曝光时间覆盖率
        private const val EXPOSURE_TIME_CONVERT_FRACTION: Double = 0.25

        //RGB二阶数组个数
        private const val RGB_COUNT_CONSTANT = 3

        //RGB数值个数
        private const val RGB_NUMBER_COUNT_CONSTANT = 256

        //位数十亿
        private const val DIGIT_BILLION = 1000_000_000

        //位数百万
        private const val DIGIT_MILLION = 1_000_000

        //位数千
        private const val DIGIT_THOUSAND = 1_000

        //位数1
        private const val DIGIT_ONES = 1

        //单位FPS
        private const val UNIT_FPS = "FPS"

        //单位s
        private const val UNIT_S = "s"

        //单位f
        private const val UNIT_F = "f"

        //单位f/
        private const val UNIT_F_LEFT_SLASH = "f/"

        //单位MM
        private const val UNIT_MM = "mm"

        //单位ISO
        private const val UNIT_ISO = "ISO"

        //单位ev
        private const val UNIT_EV = "ev"

        /**
         * 零宽度空格
         * 用于解除阿拉伯的数字导致的换行,eg:例如f 1.8在阿拉伯语言情况下会变成f\n1.8，大概率是因为阿拉伯的数字和英文字符连字导致的宽度计算出错
         * 当前仅有光圈单位需要使用
         * 使用规则：放在有光圈数值的结果光圈string中单位f前面（仅能放在前面，其他地方会失效）；eg:f 1.8->\u200Bf 1.8，f -即没有光圈值的情况不用处理
         */
        private const val ZERO_WIDTH_SPACE = "\u200B"

        //sdcard/DCIM/下除了camera其他图集mediaSet不返回名字
        private val PATH_DCIM = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).path + File.separatorChar.toString()

        //camera文件夹名称
        private const val CAMERA_ALBUM_NAME = "camera"

        //截图文件名开头
        private const val SCREENSHOT_FIRST_NAME = "Screenshot_"

        //录屏文件名开头
        private const val RECORD_FIRST_NAME = "Record_"

        //需要展示的图片模式列表
        private val imageModelList = listOf(
            FORMAT_PORTRAIT_BLUR, FORMAT_CSHOT, FORMAT_OLIVE, FORMAT_GIF, FORMAT_RAW, FORMAT_PANORAMA
        )

        //需要展示的视频模式列表
        private val videoModelList = listOf(
            FORMAT_FAST_VIDEO, FORMAT_SLOW_MOTION, FORMAT_OLD_SLOW_MOTION
        )

        //向前找数字模式
        private const val START_FORM_BEFORE = "before"

        //向后找数字模式
        private const val START_FORM_AFTER = "after"
    }
}

/**
 * 监听[PhotoDetails]数据变化的监听器。
 */
internal interface OnItemDataChangeListener {
    /**
     * [PhotoDetails]数据变化后调用的方法。
     * @param photoDetails 数据变化后的PhotoDetails
     */
    fun onDataChange(photoDetails: PhotoDetails)
}