/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorMusicUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/21
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.music;

import static com.oplus.gallery.business_lib.template.editor.EditorUIConfig.CLEAR_COLOR;
import static com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolderKt.setViewSelectedState;
import static com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager.MUSIC_LOCAL;
import static com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager.MUSIC_NONE;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.coui.appcompat.progressbar.COUICircularProgressBar;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuItemBorderAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable;
import com.oplus.gallery.business_lib.template.editor.anim.EditorMenuItemBorderAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListener;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.LocalSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.memories.base.EditorMemoriesBaseUiController;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.memories.business.data.EditorAssetsMenuAdapter;

import java.util.List;

public class EditorMusicUIController extends EditorMemoriesBaseUiController {
    private static final String TAG = "EditorMusicUIController";
    private static final int HAS_BG_ANIM = 0;
    private static final int NO_BG_ANIM = 1;
    private List<MusicItem> mData;
    private ArrayMap<Integer, Integer> mMusicPositionInfoMap = new ArrayMap<>();
    private int mCurrentSelectedPosition = 0;
    private int mLastApplyItemPosition = 0;

    public EditorMusicUIController(Context context, ViewGroup rootView, EditorBaseState state) {
        super(context, rootView, state, MONITOR_DOWNLOAD);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_memories_editor_sub_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_video_editor_menu_list_layout;
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_text_preview_editor_music;
    }

    @Override
    public void onShow() {
        super.onShow();
        hidePlayButtonTimeContainer();
        setMenuListView(mContainer.findViewById(R.id.horizontal_list));
        getMenuListView().keepLastFocusItem(true);
        initData();
        initAdapter();
        updateNetworkSourceLists();
        super.onShow();
    }

    public void refresh(List<MusicItem> entityList) {
        if (entityList != null) {
            mData.clear();
            mData.addAll(entityList);
            updatePositionInfo();
            mAdapter.select(mCurrentSelectedPosition);
            mAdapter.notifyDataSetChanged();
            mEditorState.setHasUpdateList(true);
        }
    }

    public void updateNetworkSourceLists() {
        MusicSourceManager.getInstance().requestNetworkResource(new OnLoadingListener<MusicItem>() {
            @Override
            public void onLoadingFinish(int code, List<MusicItem> allEntityList) {
                GLog.d(TAG, "updateNetworkSourceLists, loading finish, code = " + code);
                refresh(allEntityList);
            }

            @Override
            public void onIconDownloadFinish(MusicItem item) {
                GLog.e(TAG, "onIconDownloadFinish, item = " + item);
            }

            @Override
            public void onIconDownloadError(int errCode, MusicItem item) {
                GLog.e(TAG, "onIconDownloadError,errCode:" + errCode
                        + " , item = " + item);
            }

            @Override
            public void onLoadingError(int errCode) {
                GLog.e(TAG, "onLoadingError,errCode:" + errCode);
            }
        });
    }


    private void initAdapter() {
        closeDefaultMoveAnimator(getMenuListView());
        mAdapter = new EditorAssetsMenuAdapter<MusicItem>(mContext, mData, 0) {
            private float mUnselectedAlpha = 0f;
            private float mSelectedAlpha = Color.alpha(
                mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_out_border_stroke_color)
            ) * 1f / EditorUIConfig.MAX_ALPHA_VALUE;
            private int mDefaultBgColor = mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask);

            @Override
            public int getItemViewType(int position) {
                return ((position == 0) || (position == getItemCount() - 1)) ? HAS_BG_ANIM : NO_BG_ANIM;
            }

            @Override
            public void bindData(BaseRecycleViewHolder viewHolder, int position, EditorMenuItemViewData data) {
                super.bindData(viewHolder, position, data);

                MusicItem item = (MusicItem) data;
                TextView text = getNameTextView(viewHolder);
                EditorMenuItemView preview = getMenuItemView(viewHolder);
                String itemSourcePath = item.getSourcePath();
                if (TextUtils.equals(itemSourcePath, MUSIC_NONE) || TextUtils.equals(itemSourcePath, MUSIC_LOCAL)) {
                    preview.setDrawBorder(false);
                } else {
                    preview.setDrawBorder(true);
                }
                COUICircularProgressBar progressbar = viewHolder.itemView.findViewById(R.id.download_progress_bar);
                text.setSelected(!isLocalItem(position) && item.isSelected());
                preview.setSelected(!isLocalItem(position) && item.isSelected());
                text.setText(item.getName());

                boolean isAutoDownload = MusicSourceManager.getInstance().isAutoDownload(item.getMusicId());
                boolean isNeedDownloadFile = false;
                MusicItem musicItem = MusicSourceManager.getInstance().getMusic(item.getMusicId());
                if (musicItem != null) {
                    isNeedDownloadFile = musicItem.isNeedDownloadFile();
                } else {
                    isNeedDownloadFile = item.isNeedDownloadFile();
                }
                int progress = MusicSourceManager.getInstance().getDownloadProgress(item.getMusicId());
                boolean isDrawForegroundColor = false;
                if (!isAutoDownload && isNeedDownloadFile
                        && (LocalSourceManager.MIN_PROGRESS <= progress)
                        && (progress < LocalSourceManager.MAX_PROGRESS)) {
                    isDrawForegroundColor = true;
                    progressbar.setVisibility(View.VISIBLE);
                    progressbar.setProgress(progress);
                } else {
                    isDrawForegroundColor = false;
                    progressbar.setVisibility(View.GONE);
                }

                preview.setIconResource(R.drawable.videoeditor_ic_downloadable);

                if (isAutoDownload
                        || (isNeedDownloadFile && (progress < LocalSourceManager.MIN_PROGRESS))) {
                    isDrawForegroundColor = true;
                    preview.setDrawCenterIcon(true);
                } else {
                    preview.setDrawCenterIcon(false);
                }

                preview.setDrawForegroundColor(isDrawForegroundColor);
                boolean isSelected = item.isSelected() && !isNeedDownloadFile && !isLocalItem(position);
                preview.setSelected(isSelected);
                setViewSelectedState(viewHolder, isSelected ? Selectable.SELECTED : Selectable.UNSELECTED);
            }

            @Override
            public BaseRecycleViewHolder createViewHolder(View itemView, int viewType) {
                EditorMenuItemBorderAnimViewHolder holder = new EditorMenuItemBorderAnimViewHolder(
                    itemView,
                    new EditorMenuItemBorderAnimation(),
                    new EditorPressAnimation());
                setSupportPressAnim(holder);
                holder.setSelectedAnimEnable(true);
                holder.setSelectedAnimView(holder);
                holder.setDisableAlpha(mUnselectedAlpha);
                holder.setUnselectedAlpha(mUnselectedAlpha);
                holder.setSelectedAlpha(mSelectedAlpha);
                EditorMenuItemView menuItemView = getMenuItemView(holder);
                if (menuItemView != null) {
                    // mark by sunwenli 在bindData设置选中状态时会绘制外边框，这里就不另外设置了
                    if (viewType == HAS_BG_ANIM) {
                        holder.setPressAnimListener(progress -> {
                            int srcColor = mDefaultBgColor;
                            if (srcColor != CLEAR_COLOR) {
                                menuItemView.setItemBackgroundColor(EditorUIConfig.getAnimAlphaColor(progress, srcColor));
                            }
                        });
                    }
                }
                return holder;
            }
        };
        mAdapter.setHasStableIds(true);
        mAdapter.setCanUnselectCurrentPosition(false);
        int currentPos = mEditorState.getEngineManager().getThemeCurrentMusicPos();
        GLog.d(TAG, "show currentPos = " + currentPos
                + ", curMusic = " + mEditorState.getEngineManager().getCurrentThemeMusic());
        mAdapter.select(currentPos);
        mCurrentSelectedPosition = currentPos;
        mLastApplyItemPosition = currentPos;
        mAdapter.setItemClickListener(this);
        getMenuListView().setAdapter(mAdapter);
        getMenuListView().scrollToPosition(currentPos);
        getMenuListView().setKeepFocusItemPosition(currentPos);
    }

    private boolean isLocalItem(int position) {
        MusicItem item = mData.get(position);
        return (item != null) && TextUtils.equals(item.getSourcePath(), MUSIC_LOCAL);
    }

    private void initData() {
        mData = MusicSourceManager.getInstance().queryIconExistedMusic();
        if ((mData == null) || (mData.size() == 0)) {
            MusicSourceManager.getInstance().checkBuiltinItem(true);
            mData = MusicSourceManager.getInstance().queryIconExistedMusic();
        }
        if (mData == null) {
            GLog.e(TAG, "getResourceLists mData is null!");
            return;
        }
        updatePositionInfo();
        updateDownloadSource();
    }

    private void updatePositionInfo() {
        if (mData != null) {
            for (int i = 0; i < mData.size(); i++) {
                mMusicPositionInfoMap.put(mData.get(i).getMusicId(), i);
            }
        }
    }

    private void updateDownloadSource() {
        if (mData != null) {
            for (int i = 0; i < mData.size(); i++) {
                MusicItem item = mData.get(i);
                if (!item.isBuiltin() && !item.isNeedDownloadFile()) {
                    MusicSourceManager.getInstance().resetSource(item.getMusicId());
                }
            }
        }
    }


    @Override
    public void onDownloadBroadcastReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
            int state = intent.getIntExtra(LocalSourceManager.DOWNLOAD_STATE, LocalSourceManager.DOWNLOAD_STATE_INVALID);
            int resourceId = intent.getIntExtra(LocalSourceManager.DOWNLOAD_RESOURCE_ID, LocalSourceManager.ID_INVALID);
            if ((state == LocalSourceManager.DOWNLOAD_STATE_INVALID) || (resourceId == LocalSourceManager.ID_INVALID)) {
                return;
            }
            boolean isAutoDownload = MusicSourceManager.getInstance().isAutoDownload(resourceId);
            if (isAutoDownload) {
                return;
            }
            Integer position = mMusicPositionInfoMap.get(resourceId);
            GLog.d(TAG, "position = " + position + ", resourceId = " + resourceId + ", state = " + state);
            switch (state) {
                case LocalSourceManager.DOWNLOAD_STATE_DOWNLOADING:
                    if (position != null) {
                        notifyDataSetChanged();
                    }
                    break;
                case LocalSourceManager.DOWNLOAD_STATE_FINISH:
                    if ((position != null) && (position < mData.size())) {
                        mData.set(position, MusicSourceManager.getInstance().getMusic(resourceId));
                        if (position == mCurrentSelectedPosition) {
                            mAdapter.select(position);
                            mOnIconClickListener.onIconClick(null, position, mData.get(position));
                        }
                        notifyDataSetChanged();
                        boolean needDownloadTheme = intent.getBooleanExtra(LocalSourceManager.DOWNLOAD_THEME, false);
                        if (!needDownloadTheme) {
                            MusicSourceManager.getInstance().setDownloadProgress(resourceId, LocalSourceManager.DEFAULT_PROGRESS);
                        }
                    }
                    break;
                case LocalSourceManager.DOWNLOAD_STATE_ERROR:
                    mEditorState.checkDownloadFailType();
                    selectedLastApplyItem();
                    MusicSourceManager.getInstance().resetDownloadProgress(resourceId);
                    notifyDataSetChanged();
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public IntentFilter getDownloadIntentFilter() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE);
        return intentFilter;
    }

    private MusicItem getLastApplyItem() {
        MusicItem lastApplyItem = null;
        if ((mData != null) && (mData.size() > mLastApplyItemPosition)) {
            lastApplyItem = mData.get(mLastApplyItemPosition);
            if ((lastApplyItem == null) && (mData.size() > 0)) {
                lastApplyItem = mData.get(0);
                mLastApplyItemPosition = 0;
            }
        }
        return lastApplyItem;
    }

    public void selectedLastApplyItem() {
        MusicItem lastApplyItem = getLastApplyItem();
        if (lastApplyItem != null) {
            mAdapter.select(mLastApplyItemPosition);
        }
    }

    public void setCurrentSelectedPosition(int currentSelectedPosition) {
        mCurrentSelectedPosition = currentSelectedPosition;
    }

    public void setLastApplyItem(MusicItem item) {
        if (item == null) {
            return;
        }
        int position = mData.indexOf(item);
        if ((position >= 0) && (position < mData.size())) {
            mLastApplyItemPosition = position;
        }
    }
}
