/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * File: PreviewInterpolatorStrategy.kt
 * Description: tangzhibin created
 * Version: 1.0
 * Date: 2025/5/19
 * Author: tangzhibin
 *
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * tangzhibin      2025/5/19        1.0         NEW
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.preview

import android.view.animation.Interpolator
import com.coui.appcompat.animation.COUISpringInterpolator
import com.oplus.gallery.foundation.ui.animationcontrol.AnimateOperation
import com.oplus.gallery.foundation.ui.animationcontrol.AnimationInterpolateStrategy
import com.oplus.gallery.foundation.ui.animationcontrol.DefaultInterpolateStrategy
import com.oplus.gallery.foundation.ui.animationcontrol.IInterpolatorConfig
import com.oplus.gallery.foundation.ui.animationcontrol.IInterpolatorConfig.InterpolatorConfig
import kotlin.math.pow

/**
 * 预览界面动画插值策略，用于控制动画插值器。
¬*/
class PreviewInterpolatorStrategy : AnimationInterpolateStrategy {

    private val defaultInterpolatorStrategy: AnimationInterpolateStrategy = DefaultInterpolateStrategy()

    private val snapBackInterpolator: Interpolator = Interpolator { input -> 1F - (1F - input).pow(SNAP_BACK_POWER) }

    private val commonSpringInterpolator: Interpolator =
        COUISpringInterpolator(SPRING_RESPONSE, SPRING_BOUNCE)

    private val croppedSpringInterpolator: Interpolator = Interpolator { input ->
        if (input > SPRING_INTERPOLATOR_CROPPED_PROGRESS) 1F else commonSpringInterpolator.getInterpolation(input)
    }

    override fun getInterpolatorConfig(animateOperation: AnimateOperation): IInterpolatorConfig =
        when (animateOperation) {
            AnimateOperation.SnapBackOperation -> InterpolatorConfig(snapBackInterpolator, SNAP_BACK_DURATION)

            AnimateOperation.ZoomToCenterOperation -> InterpolatorConfig(commonSpringInterpolator, ZOOM_TO_CENTER_DURATION)

            AnimateOperation.UpdateClipRect -> InterpolatorConfig(commonSpringInterpolator, UPDATE_CLIP_RECT_DURATION)

            AnimateOperation.FlipOperation -> InterpolatorConfig(croppedSpringInterpolator, FLIP_DURATION)

            AnimateOperation.ChangeOrientationOperation -> InterpolatorConfig(croppedSpringInterpolator, CHANGE_ORIENTATION_DURATION)

            else -> defaultInterpolatorStrategy.getInterpolatorConfig(animateOperation)
        }

    companion object {
        /**
         * SnapBack 动画时长，单位ms
         */
        private const val SNAP_BACK_DURATION = 250L

        /**
         * ZoomToCenter 动画时长，单位ms
         */
        private const val ZOOM_TO_CENTER_DURATION = 600L

        /**
         * 更新裁剪区域动画时长，单位ms
         */
        private const val UPDATE_CLIP_RECT_DURATION = 500L

        /**
         * 内容镜像动画时长，单位ms
         */
        private const val FLIP_DURATION = 700L

        /**
         * 旋转朝向动画时长，单位ms
         */
        private const val CHANGE_ORIENTATION_DURATION = 620L

        /**
         * 动画阻尼系数，越小动画越快
         */
        private const val SPRING_RESPONSE = 0.8

        /**
         * 动画反弹系数
         */
        private const val SPRING_BOUNCE = 0.0

        /**
         * 回弹动画的幂指数
         */
        private const val SNAP_BACK_POWER = 2.5F

        /**
         * 裁剪动画的进度阈值，超过此值不使用弹簧插值器
         *
         * markby <EMAIL> 2025/07/2 裁剪框的渐显动画，需要与内容动画并行，目前通过插值器截断做个折衷方案，随后替换为更好的方案
         */
        private const val SPRING_INTERPOLATOR_CROPPED_PROGRESS = 0.80F
    }
}