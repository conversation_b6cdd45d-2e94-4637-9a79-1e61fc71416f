/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - LiveDataBus.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.event;

import static java.util.Objects.requireNonNull;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 取代EventBus的LiveDataBus
 * <pre>
 * // 注册订阅
 * LiveDataBus.get()
 *          .with("key_test", String.class)
 *          .observe(this, new Observer<String>() {
 *              @Override
 *              public void onChanged(@Nullable String s) {
 *                  // process event
 *              }
 *          });
 * </pre>
 *
 * <pre>
 * // 发送消息
 * LiveDataBus.get().with("key_test").setValue(s);
 * </pre>
 *
 * <AUTHOR>
 */
public final class LiveDataBus {

    private final static String TAG = "LiveDataBus";

    private final Map<String, BusMutableLiveData<Object>> mBus;

    private LiveDataBus() {
        mBus = new HashMap<>();
    }

    private static class SingletonHolder {

        private static final LiveDataBus DEFAULT_BUS = new LiveDataBus();
    }

    public static LiveDataBus get() {
        return SingletonHolder.DEFAULT_BUS;
    }

    public <T> MutableLiveData<T> with(String key, Class<T> type) {
        if (!mBus.containsKey(key)) {
            mBus.put(key, new BusMutableLiveData<>());
        }
        return (MutableLiveData<T>) mBus.get(key);
    }

    public MutableLiveData<Object> with(String key) {
        return with(key, Object.class);
    }

    public void clearBusMap() {
        if (mBus != null) {
            mBus.clear();
        }
    }

    public void clearBusMapByKey(String key) {
        if (mBus != null) {
            mBus.remove(key);
        }
    }

    private static class ObserverWrapper<T> implements Observer<T> {

        private Observer<T> mObserver;

        public ObserverWrapper(Observer<T> observer) {
            this.mObserver = observer;
        }

        @Override
        public void onChanged(@Nullable T t) {
            if (mObserver != null) {
                if (isCallOnObserve()) {
                    return;
                }
                mObserver.onChanged(t);
            }
        }

        private boolean isCallOnObserve() {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            if (stackTrace != null && stackTrace.length > 0) {
                for (StackTraceElement element : stackTrace) {
                    if ("androidx.lifecycle.LiveData".equals(element.getClassName())
                        && "observeForever".equals(element.getMethodName())) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    private static class BusMutableLiveData<T> extends MutableLiveData<T> {

        private Map<Observer, Observer> mObserverMap = new HashMap<>();

        @Override
        public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer) {
            super.observe(owner, observer);
            try {
                hook(observer);
            } catch (Exception e) {
                GLog.e(TAG, LogFlag.DL, "BusMutableLiveData, fail:" + e.getMessage());
            }
        }

        @Override
        public void observeForever(@NonNull Observer<? super T> observer) {
            if (!mObserverMap.containsKey(observer)) {
                mObserverMap.put(observer, new ObserverWrapper(observer));
            }
            super.observeForever(mObserverMap.get(observer));
        }

        @Override
        public void removeObserver(@NonNull Observer<? super T> observer) {
            Observer realObserver = null;
            if (mObserverMap.containsKey(observer)) {
                realObserver = mObserverMap.remove(observer);
            } else {
                realObserver = observer;
            }
            super.removeObserver(realObserver);
        }

        private void hook(@NonNull Observer<? super T> observer) throws Exception {
            //get wrapper's version
            Class<LiveData> classLiveData = LiveData.class;
            Field fieldObservers = classLiveData.getDeclaredField("mObservers");
            fieldObservers.setAccessible(true);
            Object objectObservers = fieldObservers.get(this);
            Class<?> classObservers = objectObservers.getClass();
            Method methodGet = classObservers.getDeclaredMethod("get", Object.class);
            methodGet.setAccessible(true);
            Object objectWrapperEntry = methodGet.invoke(objectObservers, observer);
            Object objectWrapper = null;
            if ((objectWrapperEntry instanceof Map.Entry)) {
                objectWrapper = ((Map.Entry<?, ?>) objectWrapperEntry).getValue();
            }
            Object wrapper = requireNonNull(objectWrapper, "Wrapper can not be bull!");
            Class<?> classObserverWrapper = wrapper.getClass().getSuperclass();
            Field fieldLastVersion = classObserverWrapper.getDeclaredField("mLastVersion");
            fieldLastVersion.setAccessible(true);
            //get livedata's version
            Field fieldVersion = classLiveData.getDeclaredField("mVersion");
            fieldVersion.setAccessible(true);
            Object objectVersion = fieldVersion.get(this);
            //set wrapper's version
            fieldLastVersion.set(wrapper, objectVersion);
        }
    }
}