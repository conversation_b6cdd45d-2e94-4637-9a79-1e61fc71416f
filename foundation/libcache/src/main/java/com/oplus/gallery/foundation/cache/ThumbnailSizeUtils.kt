/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThumbnailSizeUtils.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/12
 ** Author: huang.linpeng@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** huang.linpeng@Apps.Gallery3D      2020/9/12      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.foundation.cache

import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.standard_lib.util.os.ContextGetter

object ThumbnailSizeUtils {
    const val THUMBNAIL_TYPE_NONE: Int = 0
    const val MIDDLE_THUMB_HEIGHT_WIDTH_RATIO = 2
    /** 固定960分辨率的缩图类型 */
    const val TYPE_THUMBNAIL = 1
    const val TYPE_LARGE_THUMBNAIL = 5          //该参数不支持10bit图解bitmap，若有10bit图需要解bitmap,不能使用该参数
    const val TYPE_LARGE_THUMBNAIL_VIDEO = 6
    const val TYPE_SUPER_LARGE_THUMBNAIL = 7
    const val TYPE_FACE_THUMBNAIL = 8//加载人脸区域缩图
    /**
     * 加载GIF合成预览图的尺寸类型，Gif合成需求要求预览图最短边 >= 1280
     * */
    const val TYPE_LARGE_SHORTEST_SIZE_1280 = 14
    /** 视频小缩图 */
    private const val TYPE_MICRO_THUMBNAIL_FRAME_VIDEO = 3
    /** 列表小缩图 */
    private const val TYPE_MICRO_THUMBNAIL = 2

    /**
     * 自定义分辨率的抠图大缩图类型
     * 该类型的尺寸不固定，业务方自行判断，主要用于生成CacheKey
     */
    const val TYPE_LARGE_LNS_CUSTOM_THUMBNAIL = 15

    /**
     * 合影优化后的face图
     */
    const val TYPE_GROUP_PHOTO_FACE_THUMBNAIL = 16

    /** 大图页缩图类型 */
    private const val TYPE_FULL_THUMBNAIL = 10
    /** 扩展缩图类型 */
    private const val TYPE_EXTEND_THUMBNAIL = 11
    /** 自定义贴纸类型 */
    const val TYPE_CUSTOM_STICKER = 18

    /**
     * bugid：8082524，限制HDR解码时尺寸最大值，以X8 PRO系统相机最大HDR尺寸4928 * 7268(前置 + 哈苏水印拍摄)为基准，
     * 打印其增益图大小为2448*3616，大概为原图尺寸的一半
     */
    const val HDR_GAIN_MAP_MAX_HEIGHT = 3616

    /**
     * 大缩图尺寸，能够覆盖到目前各种机型场景。
     *
     * 引入原因：
     * 1. 相册本身的缩图比较小，再不同机型上尺寸很乱
     *
     * 使用场景：
     * 1. 相机共享的缩图的缓存尺寸。统一给1440，可以兼容到平板、折叠屏、直板机。
     *
     * 相册大缩图压缩规则：targetSize=960.coerceAtLeast((短边* 0.8).toInt())
     * - pad竖屏：   2372*1720，targetSize=1720*0.8 = 1376
     * - pad横屏：   2408*1684，targetSize=1684*0.8 = 1347
     * - findN5展开：2480*2248，targetSize=2248*0.8 = 1798
     * - findN5折叠：2392*1140，targetSize=1140*0.8 = 912，取960
     * - konka竖屏： 2486*1264，targetSize=1264*0.8 = 1011
     */
    const val THUMB_SIZE_1440 = 1440

    /**
     * 加载GIF合成预览图的具体尺寸，Gif合成需求要求预览图最短边 >= 1280
     * */
    private const val LARGE_SHORTEST_SIZE_1280 = 1280

    /**
     * 高清缩图默认尺寸
     */
    const val LARGE_THUMBNAIL_DEFAULT_TARGET_SIZE = 4096

    /**
     * 2K缩图默认尺寸
     */
    const val MEDIUM_THUMBNAIL_DEFAULT_TARGET_SIZE = 2048

    /**
     * quick bitmap缩图默认尺寸
     */
    private const val QUICK_BITMAP_THUMBNAIL_DEFAULT_TARGET_SIZE = 1280

    private val resources = ContextGetter.context.resources

    /**
     * 小缩图View 89dp~122dp之间， 分开两段，小于中间值的105dp的使用尺寸72dp*72dp，否则使用尺寸118dp*118
     * 对于轻量低机型，日视图默认列数为 4 列，因此使用 85.3dp*85.3dp 的缩图尺寸
      */
    private val MICRO_THUMBNAIL_ITEM_WIDTH_THRESHOLD_105DP =
        resources.getDimensionPixelSize(R.dimen.common_micro_thumbnail_item_width_threshold_105dp)
    private val MICRO_THUMBNAIL_TARGET_SIZE_72DP = resources.getDimensionPixelSize(R.dimen.common_microthumbnail_target_size_72dp)
    private val MICRO_THUMBNAIL_TARGET_SIZE_85DP = resources.getDimensionPixelSize(R.dimen.common_microthumbnail_target_size_85dp)
    private val MICRO_THUMBNAIL_TARGET_SIZE_118DP = resources.getDimensionPixelSize(R.dimen.common_microthumbnail_target_size_118dp)

    private var microThumbnailSizeSmall = MICRO_THUMBNAIL_TARGET_SIZE_72DP
    private val microThumbnailSizeLarge = MICRO_THUMBNAIL_TARGET_SIZE_118DP

    private const val THUMBNAIL_SIZE_VIDEO = 960
    private const val THUMBNAIL_SIZE_LARGE_PROGRESSIVE = 1440
    const val THUMBNAIL_SIZE_PHOTO: Int = 960
    private const val CUSTOM_STICKER_SIZE_2156 = 2156
    private val microThumbnailFrameSize: Int = resources.getDimensionPixelSize(R.dimen.common_video_player_thumbnail_size)
    private var largeThumbnailTargetSize: Int = LARGE_THUMBNAIL_DEFAULT_TARGET_SIZE

    /**
     * 相机quick bitmap(就是从intent中带过来的缩图)的尺寸，每次quick都会变更，需要跟随vm保存缓存的流程同步更新
     */
    private var quickBitmapThumbnailKey: Int = QUICK_BITMAP_THUMBNAIL_DEFAULT_TARGET_SIZE

    private const val SIZE_SHIFT = 6
    private const val TYPE_MASK = (Int.MAX_VALUE shl SIZE_SHIFT).inv()
    // 屏幕短边的乘积系数，用于大图缩图的最大分辨率，如果屏幕短边像素是1080px，则大图最大为1440 * 0.8 = 1152px
    private val FULL_THUMBNAIL_MAX_SIZE_MULTI = GallerySystemProperties.get("debug.thumbnail.full.multi", "0.8").toFloat()
    // 屏幕短边的乘积系数，用于扩展的大中小图的区分
    private const val EXTEND_LARGE_THRESHOLD_MULTI = 0.75f
    private const val EXTEND_MIDDLE_THRESHOLD_MULTI = 0.5f
    private const val EXTEND_SMALL_THRESHOLD_MULTI = 0.25f
    // 小缩图分辨率 * 该值，如果还小于业务要求的分辨率，则使用扩展类型的小图
    private const val SPLIT_MICRO_SMALL_THRESHOLD_MULTI = 1.25f
    // 屏幕短边的乘积系数，用于扩展的大中小图的最大分辨率
    private val EXTEND_MIDDLE_MAX_SIZE_MULTI = GallerySystemProperties.get("debug.thumbnail.middle.multi", "0.625").toFloat()
    private val EXTEND_SMALL_MAX_SIZE_MULTI = GallerySystemProperties.get("debug.thumbnail.small.multi", "0.375").toFloat()

    @JvmStatic
    fun initMicroThumbnailSize(immersivePresentationSupported: Boolean) {
        microThumbnailSizeSmall = if (immersivePresentationSupported) MICRO_THUMBNAIL_TARGET_SIZE_72DP else MICRO_THUMBNAIL_TARGET_SIZE_85DP
    }

    /**
     * 是否是最短边的缩图类型
     */
    @JvmStatic
    fun isShortestSideThumbnail(type: Int): Boolean {
        return type == TYPE_LARGE_SHORTEST_SIZE_1280
    }

    @JvmStatic
    fun getTargetSizeInType(key: Int): Int {
        return when (getType(key)) {
            TYPE_THUMBNAIL -> THUMBNAIL_SIZE_PHOTO
            TYPE_LARGE_SHORTEST_SIZE_1280 -> LARGE_SHORTEST_SIZE_1280
            TYPE_LARGE_THUMBNAIL -> largeThumbnailTargetSize
            TYPE_LARGE_THUMBNAIL_VIDEO -> THUMBNAIL_SIZE_VIDEO
            TYPE_SUPER_LARGE_THUMBNAIL -> THUMBNAIL_SIZE_LARGE_PROGRESSIVE
            TYPE_CUSTOM_STICKER -> CUSTOM_STICKER_SIZE_2156
            THUMBNAIL_TYPE_NONE -> 0
            else -> {
                val size = getSize(key)
                if (size > 0) {
                    return size
                } else {
                    throw IllegalArgumentException("getTargetSizeInType size error, key:$key, type:${getType(key)}")
                }
            }
        }
    }

    @JvmStatic
    fun setQuickBitmapThumbnailKey(key: Int) {
        quickBitmapThumbnailKey = key
    }

    /**
     * 保存当前quick bitmap写入缓存的尺寸，方便在小缩图共享时复用，见[FastCaptureBitmapRequester#getBitmapFromCache]
     *
     * 加这个接口的原因：
     * 1、quick bitmap的大小目前是由相机决定的，在不同的机器、不同的拍摄模式都不一样
     * 2、以前处理quick bitmap缓存也是动态尺寸为key写入到缓存的，在vm层保留了一个尺寸信息，用来保存和读取quick bitmap
     * 3、小缩图访问时，尺寸不会服用大缩图，但是要服用大缩图缓存，就必须要知道大缩图的尺寸，故在此做缓存
     *
     * 后续计划：相机相册全链路bitmap共享方案中解决
     * 1、原则上quick bitmap应该保持一个统一的尺寸
     * 2、quick bitmap保存缓存使用特殊key，无需按照分辨率存储
     */
    @JvmStatic
    fun getQuickBitmapThumbnailKey(): Int {
        return quickBitmapThumbnailKey
    }

    @JvmStatic
    fun getLargeThumbnailTargetSize(): Int {
        return largeThumbnailTargetSize
    }

    /**
     * liye@MediaCenter modify for bmp decode size 2014.4.22 start
     */
    @JvmStatic
    fun setLargeThumbnailTargetSize(s: Int) {
        /*
         * <EMAIL>, for : 14005 GL Texture Max Size will be 16K,
         * it too large for Small Phone
         */
        if (s < largeThumbnailTargetSize) {
            largeThumbnailTargetSize = s
        }
    }

    /**
     * 创建缩图key，这个类型用于在缓存里做区分。
     * 窗口展示分辨率变化时重新加载对应分辨率的缩图，需要用key进行区分，且因为历史问题需要判断缩图的类型进行不同的业务操作
     * marked by zhongxuechang 后续ThumbnailType重命名为ThumbnailKey
     * @param type 缩图类型
     * @param size 缩图尺寸
     */
    @JvmStatic
    private fun createCacheKey(type: Int, size: Int) = (size shl SIZE_SHIFT) or type

    /**
     * 获取Thumbnail key的打印信息
     */
    @JvmStatic
    fun getThumbKeyString(key: Int): String = "$key[${getType(key)},${getSize(key)}]"

    /**
     * 获取缩图类型
     * @param key 缩图key
     */
    @JvmStatic
    private fun getType(key: Int) = key and TYPE_MASK

    /**
     * 获取缩图尺寸
     * @param key 缩图key
     */
    @JvmStatic
    private fun getSize(key: Int) = key shr SIZE_SHIFT

    /**
     * 默认视频截帧小缩图key。还有java类使用，得保留该方法
     */
    @JvmStatic
    fun getVideoMicroThumbnailKey(): Int = getVideoMicroThumbnailKey(0)

    /**
     * 依据需要的宽度获取视频截帧小缩图key
     * 每种类型对应一种尺寸
     */
    @JvmStatic
    fun getVideoMicroThumbnailKey(itemWidth: Int): Int {
        return createCacheKey(
            TYPE_MICRO_THUMBNAIL_FRAME_VIDEO,
            if (itemWidth > microThumbnailFrameSize) microThumbnailSizeSmall else microThumbnailFrameSize
        )
    }

    /**
     * 是否是视频截帧小缩图
     * @param key
     */
    @JvmStatic
    fun isVideoMicroThumbnailKey(key: Int): Boolean {
        return getType(key) == TYPE_MICRO_THUMBNAIL_FRAME_VIDEO
    }

    /**
     * 默认小缩图key。还有java类使用，得保留该方法
     */
    @JvmStatic
    fun getMicroThumbnailKey(): Int = getMicroThumbnailKey(0)

    /**
     * 依据需要的宽度获取小缩图key
     */
    @JvmStatic
    fun getMicroThumbnailKey(itemWidth: Int): Int {
        return createCacheKey(
            TYPE_MICRO_THUMBNAIL,
            if (itemWidth > MICRO_THUMBNAIL_ITEM_WIDTH_THRESHOLD_105DP) microThumbnailSizeLarge else microThumbnailSizeSmall
        )
    }

    /**
     * 是否是小缩图key
     */
    @JvmStatic
    fun isMicroThumbnailKey(key: Int): Boolean {
        return getType(key) == TYPE_MICRO_THUMBNAIL
    }

    /**
     * 获取大图缩图key
     */
    @JvmStatic
    fun getFullThumbnailKey(): Int = getFullThumbnailKey(defaultFullThumbnailSize)

    /**
     * 获取大图缩图key
     *
     * @param targetSize 缩图尺寸，通常为默认值，在某些特殊场景需要使用定制尺寸，如相机点缩图进相册大图时
     *                   为避免出现清晰变模糊情形，需要缩图分辨率>=相机缩图分辨率
     */
    @JvmStatic
    fun getFullThumbnailKey(targetSize: Int): Int = createCacheKey(TYPE_FULL_THUMBNAIL, targetSize)

    /**
     * 判断是否为自定义尺寸的大图缩图类型
     */
    @JvmStatic
    fun isSpecialFullThumbnailSize(key: Int): Boolean = isFullThumbnailKey(key) && (getSize(key) != defaultFullThumbnailSize)

    /**
     * 默认的大缩图尺寸。适用于大图、时间轴年月日精选大图等场景。
     *
     * 折叠屏大屏、pad等大尺寸显示界面，大缩图尺寸不固定，与具体的显示尺寸有关。
     */
    @JvmStatic
    private val defaultFullThumbnailSize: Int
        get() = THUMBNAIL_SIZE_PHOTO.coerceAtLeast((ScreenUtils.displayScreenShortSide * FULL_THUMBNAIL_MAX_SIZE_MULTI).toInt())

    /**
     * 是否为大图缩图的key
     */
    @JvmStatic
    fun isFullThumbnailKey(key: Int): Boolean {
        return getType(key) == TYPE_FULL_THUMBNAIL
    }

    /**
     * 缩图类型是否支持gainmap，支持的话就能显示HDR效果
     * 注：缩图类型是否支持gainmap，会决定缩图解码时是否向磁盘缓存写入gainmap，有类型范围变化需要更新对应缩图缓存Blob的版本号
     */
    @JvmStatic
    fun isSupportThumbnailGainmap(key: Int): Boolean {
        val type = getType(key)
        return (type == TYPE_FULL_THUMBNAIL) || (type == TYPE_LARGE_THUMBNAIL)
    }

    /**
     * 扩展的大图类型
     * 场景：当前仅用于首页年月日页面（2023.5.12）
     * @return
     */
    @JvmStatic
    fun isExtendThumbnailKey(key: Int): Boolean = getType(key) == TYPE_EXTEND_THUMBNAIL

    /**
     * 扩展的缩图类型
     * 场景：当前仅用于首页年月日页面（2023.5.12）
     * @param itemWidth 绘制目标的宽度
     * @param itemHeight 绘制目标的高度
     * @return item的ThumbnailKey
     */
    @JvmStatic
    fun getExtendThumbnailKey(itemWidth: Int, itemHeight: Int): Int {
        val maxSize = itemWidth.coerceAtLeast(itemHeight)
        return when {
            maxSize > (ScreenUtils.realScreenShortSide * EXTEND_LARGE_THRESHOLD_MULTI).toInt() ->
                createCacheKey(TYPE_EXTEND_THUMBNAIL, getTargetSizeInType(getFullThumbnailKey()))

            maxSize > (ScreenUtils.realScreenShortSide * EXTEND_MIDDLE_THRESHOLD_MULTI).toInt() ->
                createCacheKey(TYPE_EXTEND_THUMBNAIL, (ScreenUtils.realScreenShortSide * EXTEND_MIDDLE_MAX_SIZE_MULTI).toInt())

            canUseExtendSmallKey(itemWidth, itemHeight) ->
                createCacheKey(TYPE_EXTEND_THUMBNAIL, (ScreenUtils.realScreenShortSide * EXTEND_SMALL_MAX_SIZE_MULTI).toInt())

            else -> getMicroThumbnailKey(itemWidth)
        }
    }

    /**
     * 判断能否使用扩展的小图，分辨率足够情况下尽量复用小缩图
     */
    @JvmStatic
    private fun canUseExtendSmallKey(itemWidth: Int, itemHeight: Int): Boolean {
        // 普通列表的小缩图不支持1x1以外的裁剪比例，必须用扩展小图
        if (itemWidth != itemHeight) return true

        val maxSize = itemWidth.coerceAtLeast(itemHeight)
        val microSize = getTargetSizeInType(getMicroThumbnailKey(itemWidth))
        val smallThresholdSize = (ScreenUtils.realScreenShortSide * EXTEND_SMALL_THRESHOLD_MULTI).toInt()
        // 如果要求尺寸符合扩展小图阈值，扩展小图的阈值也比小缩图大，则使用扩展小图
        if ((maxSize > smallThresholdSize) && (smallThresholdSize > microSize)) return true

        // 如果小缩图比要求的分辨率差太多，也得用扩展小图
        return (maxSize > (microSize * SPLIT_MICRO_SMALL_THRESHOLD_MULTI))
    }
}