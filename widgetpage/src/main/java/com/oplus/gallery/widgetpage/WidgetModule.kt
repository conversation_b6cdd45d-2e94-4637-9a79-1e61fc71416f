/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetModule.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/11        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage

import android.app.Application
import com.oplus.gallery.foundation.gstartup.GStartupLifecycle
import com.oplus.gallery.foundation.gstartup.task.TaskCollector
import com.oplus.gallery.router_lib.annotations.AppInit
import com.oplus.gallery.router_lib.center.BaseAppInit
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.widgetpage.image.WidgetImageFileCacheService
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AppInit(process = ["com.coloros.gallery3d", "com.oneplus.gallery"])
class WidgetModule(app: Application) : BaseAppInit(app), GStartupLifecycle {
    override fun onCPUIdle(taskCollector: TaskCollector, entrance: Int) {
        AppScope.launch {
            delay(DELAY_TASK)
            WidgetImageFileCacheService.cleanCache(app)
        }
    }

    companion object {
        private const val DELAY_TASK = 5000L
    }
}