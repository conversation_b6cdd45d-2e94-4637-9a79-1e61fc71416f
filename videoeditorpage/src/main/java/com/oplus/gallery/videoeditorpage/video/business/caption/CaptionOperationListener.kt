/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : CaptionOperationListener.kt
 ** Description : 字幕编辑变化监听器
 ** Version     : 1.0
 ** Date        : 2025/6/4 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/5/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.MeicamCaption
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewData

/**
 * 字幕编辑变化监听器，主要用于字幕发生编辑时，需要通知state做对应的操作及渲染效果
 */
interface CaptionOperationListener {

    /**
     * 字幕样式发生变更，需要重新创建新类型字幕做替换
     *
     * @param captionType 字幕类型
     * @param assetPackageId 样式资源包id
     */
    fun onCaptionTypeChanged(captionType: Int, assetPackageId: String): MeicamCaption?

    /**
     * 字幕颜色、阴影、描边等属性发生修改后回调，用于通知渲染预览
     */
    fun onCaptionPropertyUpdated()

    /**
     * 字幕二级编辑页点击完成时的回调，用于通知退出编辑页保存操作记录
     * @param state 字幕二级编辑页状态
     * @param isAddCaption 点击完成时是否是新增字幕
     */
    fun onCaptionDialogStateChanged(state: CaptionDialogState, isAddCaption: Boolean)

    /**
     * 默认样式准备好后回调通知去创建添加默认字幕
     * @param captionStyle 字幕样式数据
     * @param assetPackageId 样式资源安装后的id
     */
    fun onDefaultCaptionCreate(captionStyle: StyleViewData, assetPackageId: String): MeicamCaption?

    /**
     * 默认字幕的内容有变化时进行回调，字幕内容不是“请输入文字”时进行回调，用于更新当前字幕实例
     * @param caption 字幕
     */
    fun onDefaultCaptionTextChanged(caption: MeicamCaption)

    /**
     * 字幕播放开始回调，切换样式时进行播放预览样式效果
     */
    fun onCaptionStartPlay()
}

enum class CaptionDialogState {
    FINISHED,   // 正常完成
    DISMISSED   // 非正常关闭（中断）
}