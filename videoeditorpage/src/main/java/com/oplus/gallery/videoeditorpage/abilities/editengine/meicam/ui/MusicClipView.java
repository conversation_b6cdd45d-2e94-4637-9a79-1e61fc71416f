/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MusicClipView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.waveform.WaveformDataCacheManager.waveformByteArrayToFloatArray;
import static com.oplus.gallery.videoeditorpage.video.business.track.view.ClipView.CORNER;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.TextUtils;

import com.meicam.sdk.NvsWaveformDataGenerator;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.audio.MeicamAudioClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.waveform.WaveformDataCacheManager;
import com.oplus.gallery.videoeditorpage.utlis.BitmapUtils;
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper;
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewDrawImpl;
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewWrapper;

import java.util.ArrayList;

/**
 * 音乐片段
 */
public class MusicClipView extends ClipViewDrawImpl {
    private static final String TAG = "MusicClipView";
    private static final int MAX_WAVEFORM_HEIGHT = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_14);
    private static final int BEAT_POINT_RADIUS = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_1_5);
    private static final int BEAT_POINT_BG_RADIUS = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_2_5);
    private static final int WAVE_COLOR_MUSIC = ContextGetter.context.getColor(R.color.color_DBB9F2);
    private static final int BG_MUSIC = ContextGetter.context.getColor(R.color.edit_waveform_color);
    private static final int LINE_WIDTH = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_1);
    private static final float FLOAT_ONE = 1.0f;
    private static final float FLOAT_TWO = 2.0f;
    private static final int NO_AUDIO_DATA_OFFSET = 1;

    private static final int TEXT_SIZE = (int) (
            ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.editor_time_line_filter_clip_text_size)
                    / ContextGetter.context.getResources().getConfiguration().fontScale
    );
    private static final int TEXT_LEFT_AND_RIGHT = ContextGetter.context.getResources().getDimensionPixelSize(
            R.dimen.editor_time_line_filter_clip_text_margin_left_and_right
    );
    private static final int TEXT_TOP_AND_BOTTOM = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.videoeditor_song_logo_margin);
    private static final int DATA_NUM_PER_LINE = LINE_WIDTH + ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_1_33);
    private static final int TEXT_BG_COLOR = ContextGetter.context.getColor(R.color.text_in_clip_background);
    private static final int FADE_BG_COLOR = ContextGetter.context.getColor(R.color.fade_area_background);
    private static final int ICON_WIDTH = ContextGetter.context.getResources().getDimensionPixelSize(
            R.dimen.editor_time_line_caption_clip_icon_width
    );
    private static final int ICON_WIDTH_MARGIN_HORIZONTAL = ContextGetter.context.getResources().getDimensionPixelSize(
            R.dimen.videoeditor_song_wave_logo_padding
    );
    private static final int CORNER_RADIUS = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.videoeditor_song_wave_corner_radius);
    private static final int ANGLE_90 = 90;
    private static final int ANGLE_180 = 180;
    private static final int ANGLE_270 = 270;

    // 文本绘制相关属性
    private float mTextHeight; // 文本的高度
    private float mBaseLine;   // 文本的基线位置

    // 绘制工具
    private Paint mPaint;      // 通用绘制工具
    private Paint mTextPaint;  // 文本绘制工具

    // 波形数据生成器
    private NvsWaveformDataGenerator mNvsWaveformDataGenerator; // 用于生成音频波形数据的工具

    // 波形数据更新标志
    private boolean mNeedUpdateWaveformData = true; // 标记是否需要更新波形数据

    // 当前任务ID
    private long mCurrentTaskId = 0; // 当前任务的唯一标识符

    // 左右声道波形数据
    private float[] mLeftWaveformData;  // 左声道的波形数据

    // 波形数据分组相关属性
    private long mSamplesPerGroup; // 每组波形数据包含的样本数

    // 音频文件样本总数
    private long mAudioFileSampleCount; // 音频文件中的总样本数

    // 绘制区域
    private Rect mRect; // 波形绘制的矩形区域

    // 音频裁剪相关属性
    private long mTrimInAdjusted; // 调整后的裁剪起始点
    /*调整后的裁剪终点*/
    private long mTrimOutAdjusted;


    // 原始宽度
    private int mOriginWidth; // 原始波形图的宽度

    // 文本宽度
    private float mTextWidth; // 文本的宽度

    // 绘制路径
    private Path mPath; // 用于绘制波形的路径

    // 渐变绘制相关属性
    private Paint mFadePaint; // 渐变绘制工具
    private RectF mFadeRectF; // 渐变绘制的矩形区域
    private Path mFadePath;   // 渐变绘制的路径

    // 位图
    private Bitmap mBitmap; // 用于绘制波形的位图
    /**
     * 文本 logo 背景高度
     */
    private float mTextBackgroundHeight;

    public MusicClipView() {
        initBitmap();
    }

    private void initBitmap() {
        mBitmap = BitmapFactory.decodeResource(ContextGetter.context.getResources(), R.drawable.ic_music_logo, null);
        mBitmap = BitmapUtils.resizeAndCropCenter(mBitmap, ICON_WIDTH, false);
    }

    /**
     * 在给定的Canvas上绘制音频波形图及相关信息。
     *
     * @param canvas          用于绘制的Canvas对象。
     * @param clipViewWrapper 包含音频剪辑数据的ClipViewWrapper对象。
     */
    @Override
    public void draw(Canvas canvas, ClipViewWrapper clipViewWrapper) {
        canvas.drawColor(BG_MUSIC);  // 绘制背景颜色
        // 获取音频剪辑的模型数据
        ClipModel clipModel = clipViewWrapper.getRelativeData();
        // 如果音频剪辑的实际时长为0，则直接返回
        if (clipModel.getRealDuration() <= 0) {
            return;
        }
        // 初始化绘制区域的宽度和高度
        if (mOriginWidth == 0) {
            mOriginWidth = (int) (canvas.getWidth() / TrackHelper.INSTANCE.getScale());
        }
        String filePath = clipModel.getFilePath();
        // 如果需要更新波形数据且波形数据生成器不为空，则生成新的波形数据
        if ((mLeftWaveformData == null) && mNeedUpdateWaveformData && (mNvsWaveformDataGenerator != null)) {
            mNeedUpdateWaveformData = false;
            mCurrentTaskId = mNvsWaveformDataGenerator.generateWaveformData(filePath, calcExpectedSamplesPerGroup(clipModel, mOriginWidth), 0, 0, 0);
        }
        // 如果每组的样本数小于等于0或左声道波形数据为空，则直接返回
        if ((mSamplesPerGroup <= 0) || (mLeftWaveformData == null)) {
            return;
        }
        // 计算左声道和右声道的数据数量
        final int leftDataCount = mLeftWaveformData.length / 2;
        if (leftDataCount == 0) {
            return;
        }
        // 获取音频剪辑对象，并检查是否需要读取文本
        MeicamAudioClip clip = (MeicamAudioClip) clipModel.getClip();
        // 获取音频剪辑的节拍数组，并判断是否为音效类轨道
        ArrayList<Long> beats = clip.getBeatArray(clip.getCurUsedBeatType());
        int w = canvas.getWidth();
        int h = canvas.getHeight();
        initPaintAndRect();  // 初始化绘制矩形和画笔
        int realWidth = (int) TrackHelper.INSTANCE.getLengthByCurrentDuration(clipModel.getRealDuration(), ContextGetter.context);
        drawWaveform(canvas, w, realWidth, leftDataCount);  // 遍历每个像素点，绘制波形图
        initTextPaint();  // 初始化文本画笔
        drawBeats(canvas, beats, w);  // 绘制节拍点
        drawFadeArea(canvas, clip, w, h);  // 绘制淡入淡出区域
        String text = clipModel.getClipDescription();  // 获取剪辑描述文本
        if (text == null) {
            return;
        }
        // 计算图标和文本的偏移量
        int iconHorizontal = ICON_WIDTH_MARGIN_HORIZONTAL + ICON_WIDTH;
        // 计算文本的高度和宽度
        calculateTextDimensions(text);
        // 初始化路径并绘制文本背景
        drawTextBackground(canvas, iconHorizontal);
        // 绘制图标和文本
        drawIconAndText(canvas, iconHorizontal, text);
    }

    private void initPaintAndRect() {
        if (mRect == null) {
            mRect = new Rect();
        } else {
            mRect.setEmpty();
        }
        if (mPaint == null) {
            mPaint = new Paint();
            mPaint.setStyle(Paint.Style.FILL);
            mPaint.setAntiAlias(false);
            mPaint.setColor(WAVE_COLOR_MUSIC);
            mPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC));
        }
    }

    /**
     * 在画布上绘制波形图。
     *
     * @param canvas        用于绘制的画布对象。
     * @param w             画布的宽度。
     * @param realWidth   原始宽度(片段的原始长度)，用于计算波形绘制的起始位置。
     * @param leftDataCount 左声道波形数据的数量。
     */
    private void drawWaveform(Canvas canvas, int w, int realWidth, int leftDataCount) {
        // 获取画布总高度
        int totalHeight = canvas.getHeight();
        // 计算拖动偏移距离
        int trimInPosition = (int) TrackHelper.INSTANCE.getLengthByCurrentDuration(mTrimInAdjusted, ContextGetter.context);
        int trimOutPosition = (int) TrackHelper.INSTANCE.getLengthByCurrentDuration(mTrimOutAdjusted, ContextGetter.context);
        // 遍历每个像素点，绘制波形图
        for (int x = trimInPosition; x < realWidth; ++x) {
            // 每 DATA_NUM_PER_LINE 个像素点绘制一个波形矩形
            if ((x % DATA_NUM_PER_LINE) != 0) {
                continue;
            }
            if (x > trimOutPosition) {
                GLog.d(TAG, LogFlag.DL, "[drawWaveform] trimOutPosition= " + trimOutPosition + " x= " + x);
                break;
            }
            // 计算当前像素点对应音频文件中的采样点索引
            double r = (double) x / realWidth;
            long sampleIndex = (long) (r * mAudioFileSampleCount);
            // 计算当前采样点所属的波形数据组索引
            int groupIndex = (int) (sampleIndex / mSamplesPerGroup);

            // 如果当前组索引在左声道波形数据范围内，则绘制波形矩形
            if (groupIndex < leftDataCount) {
                // 计算实际绘制位置
                int drawX = 0;
                // 根据是否为RTL模式，计算绘制位置
                if (isRTL()) {
                    // RTL模式下，绘制位置需要镜像并考虑distance偏移
                    drawX = w - (x - trimInPosition);
                } else {
                    drawX = x - trimInPosition;
                }
                // 绘制波形矩形
                drawWaveformRect(canvas, totalHeight, drawX, mLeftWaveformData, groupIndex);
            }
        }
    }

    /**
     * 绘制单个波形矩形
     *
     * @param canvas       Canvas 对象
     * @param x            当前 X 坐标
     * @param waveformData 波形数据数组
     * @param groupIndex   当前组索引
     */
    private void drawWaveformRect(Canvas canvas, int totalHeight, int x, float[] waveformData, int groupIndex) {
        // 根据波形数据计算顶部和底部位置，并加上垂直偏移实现居中
        int top = (int) (MAX_WAVEFORM_HEIGHT * (FLOAT_ONE - (waveformData[groupIndex * 2 + 1] + FLOAT_ONE) / FLOAT_TWO));
        int bottom = (int) (MAX_WAVEFORM_HEIGHT * (FLOAT_ONE - (waveformData[groupIndex * 2] + FLOAT_ONE) / FLOAT_TWO));
        int middle = totalHeight / 2;

        int waveHeight = bottom - top;
        int newTop = (totalHeight - waveHeight) / 2;
        int newBottom = (totalHeight + waveHeight) / 2;
        if (newTop == newBottom) {
            newTop = middle - NO_AUDIO_DATA_OFFSET;
            newBottom = middle + NO_AUDIO_DATA_OFFSET;
        }
        // 设置矩形并绘制
        mRect.set(x, newTop, x + LINE_WIDTH, newBottom);
        canvas.drawRect(mRect, mPaint);
    }

    private void initTextPaint() {
        if (mTextPaint == null) {
            mTextPaint = new Paint();
            mTextPaint.setAntiAlias(true);
            mTextPaint.setTextSize(TEXT_SIZE);
            mTextPaint.setColor(Color.WHITE);
        }
    }

    private void drawBeats(Canvas canvas, ArrayList<Long> beats, int canvasWidth) {
        if (beats != null) {
            for (Long l : beats) {
                long position = l - mTrimInAdjusted;
                if (position >= 0) {
                    long distance = TrackHelper.INSTANCE.getLengthByCurrentDuration(position, ContextGetter.context);
                    if (distance <= canvasWidth) {
                        if (isRTL()) {
                            distance = (canvasWidth - distance);
                        }
                        mTextPaint.setColor(BG_MUSIC);
                        canvas.drawCircle(distance, (float) canvas.getHeight() / 2, BEAT_POINT_BG_RADIUS, mTextPaint);

                        mTextPaint.setColor(Color.WHITE);
                        canvas.drawCircle(distance, (float) canvas.getHeight() / 2, BEAT_POINT_RADIUS, mTextPaint);
                    }
                }
            }
        }
    }

    private void calculateTextDimensions(String text) {
        if (Float.compare(mTextHeight, 0) == 0) {
            Paint.FontMetrics fontMetrics = mTextPaint.getFontMetrics();
            mTextHeight = fontMetrics.descent - fontMetrics.ascent;
            mBaseLine = Math.abs(fontMetrics.ascent);
        }
        mTextPaint.setTextSize(TEXT_SIZE);
        mTextWidth = mTextPaint.measureText(text);
    }

    /**
     * 在画布上绘制文本背景，并返回分隔距离。
     *
     * @param canvas         用于绘制的画布对象
     * @param iconHorizontal 图标水平位置
     */
    private void drawTextBackground(Canvas canvas, int iconHorizontal) {
        mTextBackgroundHeight = mTextHeight + (TEXT_TOP_AND_BOTTOM << 1);
        // 如果 mPath 为 null，则创建一个新的 Path 对象
        if (mPath == null) {
            mPath = new Path();
        }
        // 设置文本画笔的颜色为文本背景颜色
        mTextPaint.setColor(TEXT_BG_COLOR);
        // 重置 mPath，以便重新绘制路径
        mPath.reset();
        int width = canvas.getWidth();
        int left = (isRTL() ? width : 0);
        int right = Math.round(iconHorizontal + mTextWidth + (TEXT_LEFT_AND_RIGHT << 1));
        right = (isRTL() ? (width - right) : right);
        float[] cornerRadius = isRTL()
                ? new float[]{0, 0, CORNER, CORNER, 0, 0, CORNER_RADIUS, CORNER_RADIUS,}
                : new float[]{CORNER, CORNER, 0, 0, CORNER_RADIUS, CORNER_RADIUS, 0, 0};
        // 添加一个圆角矩形到 mPath，该矩形用于绘制文本背景
        mPath.addRoundRect(left, 0, right, mTextBackgroundHeight, cornerRadius, Path.Direction.CW);
        // 在画布上绘制 mPath，使用 mTextPaint 作为画笔
        canvas.drawPath(mPath, mTextPaint);
        // 将文本画笔的颜色设置为白色，以便后续绘制文本
        mTextPaint.setColor(Color.WHITE);
    }

    private void drawIconAndText(Canvas canvas, int iconHorizontal, String text) {
        int canvasWidth = canvas.getWidth();
        if ((mBitmap != null)) {
            float top = TEXT_TOP_AND_BOTTOM * 2;
            float left = (isRTL() ? (canvasWidth - iconHorizontal) : ICON_WIDTH_MARGIN_HORIZONTAL);
            canvas.drawBitmap(mBitmap, left, top, mTextPaint);
        }
        if (!TextUtils.isEmpty(text)) {
            mTextPaint.setTextSize(ICON_WIDTH);
            float y = mBaseLine + TEXT_TOP_AND_BOTTOM;
            int x = (TEXT_LEFT_AND_RIGHT + iconHorizontal);
            float actualTextWidth = mTextPaint.measureText(text);
            x = (isRTL() ? Math.round((canvasWidth - x - actualTextWidth)) : x);
            canvas.drawText(text, x, y, mTextPaint);
        }
    }

    /**
     * 淡入淡出绘制
     *
     * @param canvas
     * @param clip
     * @param w
     * @param h
     */
    private void drawFadeArea(Canvas canvas, MeicamAudioClip clip, int w, int h) {
        if (mFadeRectF == null) {
            mFadeRectF = new RectF();
        }
        mFadeRectF.setEmpty();

        if (mFadePath == null) {
            mFadePath = new Path();
        }
        mFadePath.reset();
        if (mFadePaint == null) {
            mFadePaint = new Paint();
            mFadePaint.setColor(FADE_BG_COLOR);
            mFadePaint.setStyle(Paint.Style.FILL);
            mFadePaint.setStrokeWidth(2);
            mFadePaint.setAntiAlias(true);
            mFadePaint.setDither(true);
        }
        long fadeInDuration = clip.getFadeInDuration();
        long fadeOutDuration = clip.getFadeOutDuration();

        long fadeInDistance = TrackHelper.INSTANCE.getLengthByCurrentDuration(fadeInDuration * 2, ContextGetter.context);
        long fadeOutDistance = TrackHelper.INSTANCE.getLengthByCurrentDuration(fadeOutDuration * 2, ContextGetter.context);
        // 在RTL模式下交换淡入和淡出区域实现镜像
        if (isRTL()) {
            long temp = fadeInDistance;
            fadeInDistance = fadeOutDistance;
            fadeOutDistance = temp;
        }
        mFadeRectF.set(0, 0, fadeInDistance, h);
        //上左椭圆弧
        mFadePath.arcTo(mFadeRectF, ANGLE_180, ANGLE_90);
        mFadePath.lineTo(w - fadeOutDistance, 0);
        mFadeRectF.setEmpty();
        //上右椭圆弧
        mFadeRectF.set(w - fadeOutDistance, 0, w, h);
        mFadePath.arcTo(mFadeRectF, ANGLE_270, ANGLE_90);
        //下右椭圆弧
        mFadePath.arcTo(mFadeRectF, 0, ANGLE_90);
        mFadePath.lineTo(fadeInDistance, h);
        //下左椭圆弧
        mFadeRectF.setEmpty();
        mFadeRectF.set(0, 0, fadeInDistance, h);
        mFadePath.arcTo(mFadeRectF, ANGLE_90, ANGLE_90);
        mFadePath.close();

        mFadePath.setFillType(Path.FillType.INVERSE_WINDING);
        canvas.drawPath(mFadePath, mFadePaint);
    }

    /**
     * 刷新UI界面，根据给定的路径和修剪时间调整波形数据的显示。
     *
     * @param view            用于显示波形数据的视图包装器。
     * @param path            音频文件的路径，如果为空则不进行任何操作。
     * @param trimInAdjusted  调整后的起始修剪时间，如果为负数则不进行任何操作。
     * @param trimOutAdjusted 调整后的结束修剪时间，如果为负数则不进行任何操作。
     */
    @Override
    public void refreshUi(ClipViewWrapper view, String path, long trimInAdjusted, long trimOutAdjusted) {
        // 如果路径为空或修剪时间为负数，则直接返回
        if (TextUtils.isEmpty(path)) {
            return;
        }
        GLog.d(TAG, LogFlag.DL, "[refreshUi] trimInAdjusted=" + trimInAdjusted);
        if ((trimInAdjusted < 0) || (trimOutAdjusted < 0)) {
            return;
        }

        // 更新调整后的起始修剪时间
        mTrimInAdjusted = trimInAdjusted;
        mTrimOutAdjusted = trimOutAdjusted;
        // 如果波形数据生成器为空，则初始化并设置回调
        if (mNvsWaveformDataGenerator == null) {
            mNvsWaveformDataGenerator = new NvsWaveformDataGenerator();
            mNvsWaveformDataGenerator.setWaveformDataCallback(new NvsWaveformDataGenerator.WaveformDataCallback() {
                @Override
                public void onWaveformDataReady(
                        long taskId,
                        String audioFilePath,
                        long audioFileSampleCount,
                        long samplesPerGroup,
                        byte[] leftWaveformData,
                        byte[] rightWaveformData
                ) {
                    // 将波形数据转换为浮点数组并更新UI
                    mLeftWaveformData = waveformByteArrayToFloatArray(TAG, audioFilePath, audioFileSampleCount, samplesPerGroup, leftWaveformData);
                    mSamplesPerGroup = samplesPerGroup;
                    mCurrentTaskId = 0;
                    if (view != null) {
                        view.postInvalidateClipView();
                    }
                }

                @Override
                public void onWaveformDataGenerationFailed(long taskId, String audioFilePath, long samplesPerGroup) {
                    // 波形数据生成失败时的处理逻辑
                }
            });
        }
        WaveformDataCacheManager.WaveformDataCacheItem cacheItem = WaveformDataCacheManager.INSTANCE.getWaveformDataCacheItem(path);
        if (cacheItem != null) {
            mLeftWaveformData = cacheItem.getLeftWaveformData();
            mSamplesPerGroup = cacheItem.getSamplesPerGroup();
            mAudioFileSampleCount = cacheItem.getAudioFileSampleCount();
        } else {
            // 获取音频文件的样本数量，如果样本数量无效则返回
            long sampleCount = mNvsWaveformDataGenerator.getAudioFileSampleCount(path);
            if (sampleCount <= 0) {
                return;
            }
            mAudioFileSampleCount = sampleCount;
        }
        if ((mLeftWaveformData == null) || (mLeftWaveformData.length == 0)) {
            if (view != null) {
                view.postInvalidateClipView();
            }
        }
    }

    /**
     * 清除当前的绘制数据。该函数会取消当前的任务，并释放与波形数据生成器相关的资源。
     * 具体操作包括：
     * 1. 取消当前正在执行的任务。
     * 2. 如果波形数据生成器（mNvsWaveformDataGenerator）不为空，则移除其回调并释放资源，最后将其置为空。
     */
    @Override
    public void clearDrawData() {
        // 取消当前正在执行的任务
        cancelCurrentTask();

        // 如果波形数据生成器存在，则移除回调并释放资源
        if (mNvsWaveformDataGenerator != null) {
            mNvsWaveformDataGenerator.setWaveformDataCallback(null);
            mNvsWaveformDataGenerator.release();
            mNvsWaveformDataGenerator = null;
        }
    }

    /**
     * 取消当前正在执行的任务。
     * 如果当前任务ID不为0，并且波形数据生成器不为空，则调用波形数据生成器的取消任务方法，
     * 并将当前任务ID重置为0。
     */
    private void cancelCurrentTask() {
        // 检查当前任务ID是否有效
        if (mCurrentTaskId != 0) {
            // 如果波形数据生成器存在，则取消当前任务
            if (mNvsWaveformDataGenerator != null) {
                mNvsWaveformDataGenerator.cancelTask(mCurrentTaskId);
            }
            // 重置当前任务ID为0，表示没有正在执行的任务
            mCurrentTaskId = 0;
        }
    }

    /**
     * 计算每个组的预期样本数。
     * <p>
     * 该函数根据剪辑模型的调整时长和实际时长的比例，计算音频文件的样本数，并根据窗口大小（w）将样本数分组。
     * 最终返回每个组的样本数，确保至少为1。
     *
     * @param clipModel 剪辑模型，包含调整时长和实际时长的信息。
     * @param w         窗口大小，用于将样本数分组。如果小于等于0，则返回1。
     * @return 每个组的预期样本数，至少为1。
     */
    private long calcExpectedSamplesPerGroup(ClipModel clipModel, int w) {
        // 计算调整时长与实际时长的比例
        double ratio = (double) clipModel.getAdjustedDuration() / (double) clipModel.getRealDuration();

        // 根据比例计算调整后的样本数
        long sampleCount = (long) (mAudioFileSampleCount * ratio);
        // 如果窗口大小小于等于0，直接返回1
        if (w <= 0) {
            return 1;
        }
        // 计算每个组的样本数，并确保至少为1
        long samplesPerGroup = (sampleCount + w / 2) / w;
        return Math.max(samplesPerGroup, 1);
    }
}
