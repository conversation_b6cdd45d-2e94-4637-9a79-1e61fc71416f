/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BottomMenuDecoration.kt
 ** Description : 大图底部菜单
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.pagedecoration

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.Gravity
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.annotation.MainThread
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.graphics.ColorUtils
import androidx.core.view.contains
import androidx.core.view.doOnPreDraw
import androidx.core.view.forEach
import androidx.core.view.forEachIndexed
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.Observer
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.bottomnavigation.COUINavigationItemView.TIPS_CIRCLE
import com.coui.appcompat.bottomnavigation.COUINavigationItemView.TIPS_HIDE
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.material.navigation.NavigationBarMenuView
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.oplus.anim.EffectiveAnimationView
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_MORE
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.isActivityInvalid
import com.oplus.gallery.foundation.util.ext.show
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.isolatedlayer.IPhotoBottomMenuAdapter
import com.oplus.gallery.photo_page.ui.isolatedlayer.PhotoBottomMenu
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.PanelContractEffectState
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.ProgressEffectController
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoSubMenuCreator.PopupListWindowItem
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoSubMenuCreator.SubMenuItem
import com.oplus.gallery.photo_page.ui.section.photomenu.IPhotoMenuTextTips
import com.oplus.gallery.photo_page.ui.section.photomenu.MenuIcon
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuIconDrawableManager
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.menucontrol.IconTipType
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuItemState
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuState
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuExecutor
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuItemTips
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageAnimationEvent
import com.oplus.gallery.standard_lib.ui.bottomnavigation.BottomNavigationView
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.tools.createAlphaSpringAnimation
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.removeLast as ktRemoveLast

/**
 * 大图页底部菜单实现。
 * 顶部菜单会在不同的屏幕尺寸下有不同的表现：
 * - 屏幕宽度小于临界值时，显示底部菜单
 * - 屏幕宽度超出临界值时，底部菜单隐藏。
 *
 * 注意： 当底部菜单可见时，底部菜单的菜单项被认为是常驻的，当某菜单项不可用时，应该被禁用而非隐藏菜单项。
 * @see PhotoTopMenuDecoration
 */
@ExperimentalUnsignedTypes
class PhotoBottomMenuDecoration(
    private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
    /**
     * 底层真正的菜单对象
     */
    override var photoBottomMenu: PhotoBottomMenu,

    /**
     * 文本提示
     */
    private val textTips: IPhotoMenuTextTips,

    /**
     * 菜单项点击回调
     */
    private val onItemClicked: (action: Int) -> Unit = {},

    /**
     * PhotoMenuExecutor对象，用于设置锚点view
     */
    private val photoMenuExecutor: PhotoMenuExecutor
) : IBottomMenuSection, IPhotoBottomMenuAdapter {

    /**
     * 底部菜单实现控件
     */
    private var menuView: BottomNavigationView? = null

    /**
     * 子菜单生成器：“更多”
     */
    private val moreMenuCreator = PhotoSubMenuCreator(
        parentMenuItem = R.id.action_more
    )

    /**
     * 上一个mediaItem的菜单的状态记录
     * key = menuItem.itemId
     * value = MenuItemState
     */
    private val lastMenuItemStatesMap: HashMap<Int, MenuItemState?> = HashMap()

    /**
     * 当前收到的菜单的状态数据，收到此数据后并不会刷新菜单，而需要
     * 调用[invalidateMenu]才会根据[MenuState]来刷新菜单。
     */
    var currentMenuState: MenuState? = null
        set(value) {
            val isFreshMoreSubMenuView = isFreshMoreSubMenuView(value?.menuItemStates, field?.menuItemStates)
            field = value
            if (isFreshMoreSubMenuView) {
                updateMoreSubMenuView()
            }
        }

    /**
     * ”更多“ 被点击时弹出的菜单。
     * [BottomNavigationView] 本身有一个 PopupWindow ，
     * 是针对Tab页的业务进行了高度的定制化，但大图需要使用的是默认的 COUIPopupListWindow，因此在此用了一个新的。
     * Marked: lichengli , [BottomNavigationView] 里的东西为什么定制啊。。后面找时间给它干了。
     */
    private var moreSubMenuView: COUIPopupListWindow? = null

    /**
     * Menu 的图标缓存
     *
     * key : Resource id
     *
     * value : Drawable
     */
    private val menuIconCaches: MutableMap<Int, Drawable> = ConcurrentHashMap()

    /**
     * 颜色资源的缓存
     * key：ColorRes
     * value: ColorInt
     */
    private val colorResources: ResourcesCaches<Int, Int> = ResourcesCaches {
        sectionPage.pageContext?.getColor(it)
    }

    /**
     * dimension 资源的缓存
     * key：ColorRes（selector）
     * value: ColorStateList
     */
    private val colorStateListResources: ResourcesCaches<Int, ColorStateList> = ResourcesCaches {
        sectionPage.pageContext?.getColorStateList(it)
    }

    /**
     * Menu 菜单项Icon对应集合
     *
     * key : actionId
     *
     * value : MenuItem 对应地Icon地ImageView
     */
    private val menuItemIconViewMap = ConcurrentHashMap<Int, ImageView>()

    /**
     * Menu 菜单项Icon对应EffectiveAnimationView集合
     *
     * key : actionId
     *
     * value : MenuItem 对应地Icon地EffectiveAnimationView
     */
    private val menuItemAnimationViewMap = ConcurrentHashMap<Int, EffectiveAnimationView>()

    /**
     * 底部菜单MeasuredHeight
     */
    private var bottomMenuMeasuredHeight: Int = 0

    /**
     *  底部菜单需要设置的高度，
     *  单位：px
     *  默认值： -1
     *  注：手动维护，[onBottomMenuLayout]调用处在onMeasure和onLayout时机中间，直接使用View.height,或者post获取，会闪高度和或者获取错误
     */
    private var currentBottomMenuHeight: Int = -1

    private val mRefCounterLock = Any()

    private var watermarkTipView: View? = null

    /**
     * Menu 菜单项Icon对应替换动画map
     *
     * key : actionId
     *
     * value : Pair<fadeOut, fadeIn> 替换动画对应的pair
     */
    private val menuItemReplaceAnimationMap = ConcurrentHashMap<Int, Pair<COUISpringAnimation, COUISpringAnimation>>()

    /**
     * 装饰器主题
     */
    internal var theme: MenuDecorationTheme = sectionPage.pageViewModel.pageManagement.pageTheme.value.menuDecorationTheme
        private set

    /**
     * 透明Spring动画的参数
     */
    private var alphaSpringForce: COUISpringForce? = null

    /**
     * 透明度的Spring动画
     */
    private var alphaSpringAnimation: COUISpringAnimation? = null

    /**
     * MenuItem 的高度
     */
    private val menuItemHeight by lazy {
        sectionPage.pageContext?.resources?.getDimensionPixelSize(R.dimen.photopage_bottom_menu_item_height) ?: 0
    }

    /**
     * MenuItem 图标的顶部间距
     */
    private val iconTopSpace by lazy {
        sectionPage.pageContext?.resources?.getDimensionPixelSize(R.dimen.photopage_bottom_menu_icon_top_space) ?: 0
    }

    /**
     * 菜单栏按键能否被点击，true为可以点击，false为不可以点击
     */
    var canItemClick: Boolean = true

    /**
     * 菜单创建/刷新完成后的回调列表。
     * 底部菜单初进大图、旋转屏幕后，都会重新创建、刷新菜单项。
     * 如果有逻辑需要菜单项ready，请将逻辑注册到此列表中，待菜单刷新后统一在主线程回调。
     *
     * 滞留栈式设计，执行即弹出，不会再次被执行。
     * @see onBottomMenuPrepared
     */
    private val onMenuPreparedCallbacks: MutableList<(() -> Unit)> = mutableListOf()

    /**
     * 当前菜单使用的是否是暗色主题
     */
    private var currentPopupWindowNightMode: Boolean? = null

    /**
     * 详情页状态变化监听
     */
    private val panelEffectStateListener = Observer<PanelContractEffectState?> { updateBottomMenuStyle(it) }

    init {
        photoBottomMenu.adapter = this
    }

    @SuppressLint("RestrictedApi")
    override fun onBottomMenuLayout(bottomMenu: View) {
        val bottomNaviBarHeight = if (sectionPage.pageInstance.couldShowSystemBar() || sectionPage.pageInstance.isInMultiWindow()) {
            sectionPage.pageInstance.bottomNaviBarHeight()
        } else 0

        //这里需要加同步锁的原因：刚进大图的时候此方法会被回调多次，bottomMenuHeight只需要测量view原来的高度，避免bottomMenuHeight被赋值为和底部导航栏相加后的高度
        synchronized(mRefCounterLock) {
            if (bottomMenuMeasuredHeight == 0) {
                bottomMenuMeasuredHeight = bottomMenu.measuredHeight
            }
            //如果是折叠屏大图全屏展示的情况下底部菜单栏不会展示,此时测量到的height是0，避免把bottomNaviBarHeight的值赋值给bottomMenuHeight造成显示问题
            if (bottomMenuMeasuredHeight == 0) return@synchronized
            // COUINavigationMenuView 的高度被内部写死成 56dp 了，此处重新设置其父 View 的高度做限制
            bottomMenuMeasuredHeight = menuItemHeight
            val newHeight = bottomNaviBarHeight + bottomMenuMeasuredHeight
            sectionPage.pageViewModel.menuControl.updateBottomMenuHeight(newHeight)
            GLog.d(TAG, LogFlag.DL) { "[onBottomMenuLayout] newHeight:$newHeight,currentBottomMenuHeight:$currentBottomMenuHeight" }
            if (newHeight != currentBottomMenuHeight) {
                currentBottomMenuHeight = newHeight
                bottomMenu.updateLayoutParams { height = newHeight }
            }
        }

        // 尝试直接更新 MenuItem 的样式，避免闪出文字，可能会使用默认的数值：[defaultFlRootHeight]、[defaultIconGroupHeight]
        setupAllMenuItemStyle(bottomMenu)

        (bottomMenu as? COUINavigationView)?.couiNavigationMenuView?.let { menuView ->
            (menuView.layoutParams as? FrameLayout.LayoutParams)?.let {
                it.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
            }
        }
    }

    override fun onBottomMenuCreated(navigationView: BottomNavigationView) {
        menuView = navigationView
        alphaSpringAnimation?.cancel()
        alphaSpringAnimation = null
        sectionPage.pageViewModel.details.transitionPanelEffectState.observeForever(panelEffectStateListener)
        // 移除内置的默认 tint，使用我们自己的图标颜色。
        navigationView.itemIconTintList = null
    }

    override fun onInflateBottomMenu(): Int =
        sectionPage.pageViewModel.inputArguments.menuPresent.value?.bottomMenuResId ?: Resources.ID_NULL

    /**
     * Menu的图标加载任务
     *
     * key : menuItem的action id
     *
     * value : AsyncObj异步加载任务
     */
    private val runningPrepareMenuJobs = mutableMapOf<Int, AsyncObj<Drawable?>>()

    override fun onPrepareBottomMenu(menuView: BottomNavigationView) {
        setupBottomMenuStyle(menuView)
        onPrepareBottomMenu(menuView.menu)
    }

    private fun onPrepareBottomMenu(menu: Menu) = GTrace.trace("$TAG - onPrepareBottomMenu") {
        if ((currentMenuState?.isMenuItemDataValid != true) || (menuView == null)) {
            return@trace
        }
        val theme = this.theme

        /*
         * 注意： 底部菜单的菜单项被认为是常驻菜单，只允许更改其 `isEnabled` 属性来启用/禁用，
         * 不要更改 `isVisible` 属性，这会使得菜单项不可见。
         */
        menu.forEachIndexed { index, menuItem ->
            val currentMenuItemState = currentMenuState?.menuItemStates?.get(menuItem.itemId)
            val lastMenuItemState = lastMenuItemStatesMap[menuItem.itemId]
            if (currentMenuItemState != null) {
                if (currentMenuItemState.tips != lastMenuItemState?.tips) {
                    /**
                     * 更新提示
                     * - 数字/红点提示： 每个菜单项都可能有红点提示，且允许存在多个。
                     * - 文字提示： 只允许同时存在一个文字提示。
                     */
                    menuItem.updateTips(currentMenuItemState.tips, index)
                }

                //动画 或者 drawable 资源更改 或者亮暗模式修改
                configureMenuItemDisplay(
                    menuItem,
                    theme,
                    currentMenuItemState,
                    lastMenuItemState
                )
                // 启用/禁用菜单项
                if (menuItem.isEnabled != currentMenuItemState.isEnabled) {
                    // MenuItem.setEnabled()巨耗时（MT6853平台上可以耗时1+ms），必须加上判断来减少set次数
                    menuItem.isEnabled = currentMenuItemState.isEnabled
                    menuItemAnimationViewMap[menuItem.itemId]?.alpha = if (currentMenuItemState.isEnabled) ALPHA_FULL_DISPLAY else ALPHA_DISABLED
                }
            } else {
                if (menuItem.isEnabled) {
                    // MenuItem.setEnabled()巨耗时（MT6853平台上可以耗时1+ms），必须加上判断来减少set次数
                    menuItem.isEnabled = false
                    menuItemAnimationViewMap[menuItem.itemId]?.alpha = ALPHA_DISABLED
                }
                //这里禁用的menu也需要根据主题设置对应的icon
                sectionPage.pageViewModel.menuControl.queryMenuIconWithActionId(menuItem.itemId)?.let {
                    if (it is MenuIcon.Static) {
                        updateMenuIconDrawable(menuItem, it.getIconRes(theme))
                    } else {
                        // 目前此处不会返回其他类型，添加 log 为后续变更做提醒。
                        GLog.w(TAG, LogFlag.DL) { "[onPrepareBottomMenu] unimplemented for MenuIcon: ${it::class.simpleName}" }
                    }
                }

                // 菜单项不存在或不支持（isEnable=false）时，也应该隐藏所有提示
                menuItem.hideTips(index)
            }

            lastMenuItemStatesMap[menuItem.itemId] = currentMenuItemState?.also {
                it.iconTheme = theme
            }
        }
    }

    override fun onBottomMenuDestroyed() {
        sectionPage.pageViewModel.details.transitionPanelEffectState.removeObserver(panelEffectStateListener)
        lastMenuItemStatesMap.clear()
        menuIconCaches.clear()
        menuItemAnimationViewMap.forEach {
            it.value.cancelAnimation()
            it.value.removeAllAnimatorListeners()
        }
        menuItemAnimationViewMap.clear()
        menuItemIconViewMap.clear()
        colorResources.clear()
        colorStateListResources.clear()
        // 清空所有正在执行的菜单准备任务
        runningPrepareMenuJobs.forEach { _, asyncObj -> asyncObj.destroy() }
        runningPrepareMenuJobs.clear()
        menuItemReplaceAnimationMap.forEach { _, (fadeOut, fadeIn) ->
            fadeOut.cancel()
            fadeIn.cancel()
        }
        menuItemReplaceAnimationMap.clear()
        onMenuPreparedCallbacks.clear()
    }

    override fun onBottomMenuPrepared() {
        while (onMenuPreparedCallbacks.isEmpty().not()) {
            onMenuPreparedCallbacks.ktRemoveLast().invoke()
        }
    }

    /**
     * 传入的逻辑将在菜单创建刷新完成后执行。
     * 执行时机：底部菜单创建、刷新完成后。业务方需要在底部菜单创建前调用，否则不会生效
     * @see [onBottomMenuPrepared]
     */
    @MainThread
    internal fun doOnMenuPrepared(action: () -> Unit) {
        onMenuPreparedCallbacks.add(action)
    }

    /**
     * 更新菜单的主题
     *
     * @param theme 新的主题
     * @param shouldInvalidate 是否需要 invalidate 菜单
     */
    internal fun updateTheme(theme: MenuDecorationTheme, shouldInvalidate: Boolean): Boolean {
        if (this.theme == theme) return false
        this.theme = theme
        if (shouldInvalidate) {
            alphaSpringAnimation?.cancel()
            invalidateMenu()
        }
        return true
    }

    /**
     *菜单颜色变化监听
     *不是菜单主题颜色变化，临时动画过渡需要更改颜色，事件是成对的，更改后需要依赖后续事件恢复
     */
    internal fun changeMenuColorByEvent(event: PhotoPageAnimationEvent, isPhotoTopBelowMenu: Boolean) {
        if (isPhotoTopBelowMenu.not()) {
            return
        }
        val menuView = menuView.getOrLog("[changeMenuColorByEvent] menuView is null") ?: return
        when (event) {
            PhotoPageAnimationEvent.ANIMATION_START -> menuView.setBackgroundColor(Color.TRANSPARENT)
            PhotoPageAnimationEvent.ANIMATION_END -> setupBottomMenuStyle(menuView)
        }
    }

    /**
     * 刷新更多菜单
     */
    private fun updateMoreSubMenuView() {
        // 如果”更多“ 被点击时弹出的菜单正在显示，则刷新，这里需先隐藏后显示，直接显示会隐藏当前菜单
        if (moreSubMenuView?.isShowing == true) {
            GLog.d(TAG, LogFlag.DF) { "[updateMoreSubMenuView] moreSubMenuView dismiss and showMoreSubMenu." }
            moreSubMenuView?.dismiss()
            showMoreSubMenu()
        }
    }

    /**
     * 是否刷新更多菜单弹窗:当前仅在系统播放器中打开的id需要刷新更多菜单弹窗
     *
     * @param currentMenuItemStates 当前菜单项状态
     * @param oldMenuItemStates 旧菜单项状态
     * @return 是否需要刷新更多菜单弹窗，true：需要刷新，false：不需要刷新
     */
    private fun isFreshMoreSubMenuView(currentMenuItemStates: Map<Int, MenuItemState>?, oldMenuItemStates: Map<Int, MenuItemState>?): Boolean {
        oldMenuItemStates ?: return false
        currentMenuItemStates ?: return false
        // 获取在系统播放器中打开的action
        val oldMenuItemState = oldMenuItemStates[R.id.action_open_in_system_player]
        val currentMenuItemState = currentMenuItemStates[R.id.action_open_in_system_player]
        // 如果之前显示，后为空或不显示的情况，就刷新”更多“ 被点击时弹出的菜单弹窗
        if ((oldMenuItemState?.isVisible == true) && (currentMenuItemState == null || !currentMenuItemState.isVisible)) {
            return true
        }
        return false
    }

    /**
     * 此方法使用[AsyncObj]加载drawable，如果同时异步加载多个drawable可能存在多线程之间资源锁竞争的问题
     * 导致性能下降，如果存在这种情况考虑更改此方法的实现
     */
    private fun asyncLoadDrawable(iconRes: Int): AsyncObj<Drawable?> = AsyncObj(sectionPage.pageViewModel) {
        menuIconCaches[iconRes] ?: sectionPage.pageContext?.let { context ->
            AppCompatResources.getDrawable(context, iconRes)?.also { drawable ->
                menuIconCaches[iconRes] = drawable
            }
        }
    }

    override fun onBottomMenuItemClicked(item: MenuItem) {
        if (!canItemClick) {
            GLog.w(TAG, LogFlag.DL) { "[onBottomMenuItemClicked] item clicked, but canItemClick is false" }
            return
        }
        if (DoubleClickUtils.isFastDoubleClick()
            && (sectionPage.pageViewModel.menuControl.shouldSuppressedItems(item.itemId).not())
        ) {
            return
        }
        if (item.itemId == R.id.action_more) {
            showMoreSubMenu()
            // 埋点： 更多菜单被点击。只要被点击就会触发
            sectionPage.pageViewModel.track.trackPictureClick(value = MENU_ITEM_MORE)
        } else {
            onItemClicked(item.itemId)
        }
    }

    /**
     * 显示更多菜单
     */
    private fun showMoreSubMenu() {
        val context = sectionPage.pageContext ?: return
        currentMenuState?.let { state ->
            moreMenuCreator.createSubMenu(
                presentMenuItems = moreMenuCreator.createAllMoreMenuItems(sectionPage.pageViewModel),
                menuState = state
            ).let { subMenu ->
                menuView?.findViewById<View>(subMenu.parentMenuItem)?.let { anchorView ->
                    if ((context !is Activity) || (context.isActivityInvalid())) {
                        GLog.d(TAG) { "[triggerMoreSubMenu] the context is not activity or activity is invalid, skip show ensureMoreSubMenuView" }
                        return
                    }

                    ensureMoreSubMenuView(context).show(
                        anchor = anchorView,
                        items = subMenu.menuItems.map { it.toPopupListWindowItem(anchorView.resources) },
                        onItemClick = { position, _ ->
                            onItemClicked(subMenu.menuItems[position].action)
                            moreSubMenuView?.dismiss()
                        }
                    )
                }
            }
        }
    }

    override fun invalidateMenu() {
        photoBottomMenu.invalidateMenu()
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        lastMenuItemStatesMap.clear()
        invalidateMenu()
        photoMenuExecutor.onAppUiStateChanged(config)
    }

    override fun show() {
        photoBottomMenu.show()
    }

    override fun hide() {
        photoBottomMenu.hide()
    }

    /**
     * 隐藏水印的角标
     */
    @SuppressLint("RestrictedApi")
    fun hideWatermarkTip() {
        val tipsView = watermarkTipView ?: return
        val itemView = menuView?.couiNavigationMenuView?.findItemView(R.id.action_edit) ?: return
        if (itemView.contains(tipsView).not()) return
        itemView.removeView(tipsView)
        watermarkTipView = null
    }

    /**
     * 把底部菜单的风格设置为大图暗色 性冷淡风
     * 更新底部菜单样式(主要包含菜单背景、diverLine)
     */
    private fun updateBottomMenuStyle(panelContractEffectState: PanelContractEffectState?) {
        val isExtendDetailsPage = ProgressEffectController.isDetailsMode(panelContractEffectState)
        // 详情模式变化时，需要使用Spring动画更新menu样式
        animateMenuStyleOnDetailModeChanged(isExtendDetailsPage, sectionPage.pageViewModel.details.needAnimOnDetailsModeChanged)
    }

    /**
     * 设置底部菜单的风格
     */
    private fun setupBottomMenuStyle(navigationView: BottomNavigationView) {
        val panelEffectState = sectionPage.pageViewModel.details.transitionPanelEffectState.value
        val isExtendDetailsPage = ProgressEffectController.isDetailsMode(panelEffectState)
        val bottomMenuBackgroundRes = if (isExtendDetailsPage) {
            R.color.photopage_menu_detail_display_background_color
        } else {
            theme.background
        }
        navigationView.dividerView.alpha = if (isExtendDetailsPage) ALPHA_FULL_DISPLAY else ALPHA_TRANSPARENT
        navigationView.setBackgroundColor(navigationView.context.getColor(bottomMenuBackgroundRes))
    }

    /**
     * 设置所有 MenuItem 的样式，
     * @see setupMenuItemStyle
     */
    @SuppressLint("RestrictedApi")
    private fun setupAllMenuItemStyle(bottomMenu: View) {
        (bottomMenu as? BottomNavigationView)?.let { bottomNavigationView ->
            (bottomNavigationView.menuView as? NavigationBarMenuView)?.let { menuView ->
                bottomNavigationView.menu.forEach { menuItem ->
                    setupMenuItemStyle(menuView, menuItem)
                }
            }
        }
    }

    /**
     * 设置 MenuItem 的样式
     * 1. 隐藏 label
     * 2. 位移图标和红点
     */
    @SuppressLint("RestrictedApi")
    private fun setupMenuItemStyle(menuView: NavigationBarMenuView, menuItem: MenuItem) {
        // 自行隐藏 label，原布局逻辑详见：COUINavigationItemView.topToBottom
        val itemView = menuView.findItemView(menuItem.itemId) ?: return
        val labelsGroup = itemView.findViewById<View>(com.google.android.material.R.id.navigation_bar_item_labels_group)
        labelsGroup.visibility = View.GONE

        val flRoot = itemView.findViewById<ViewGroup>(com.support.bottomnavigation.R.id.fl_root)
        val originalMargin = menuView.context.resources.getDimensionPixelSize(
            com.support.bottomnavigation.R.dimen.coui_navigation_icon_margin_top
        )
        val targetTopPadding = iconTopSpace - originalMargin

        val iconGroup = itemView.findViewById<View>(com.google.android.material.R.id.navigation_bar_item_icon_container)
        iconGroup.updatePadding(top = targetTopPadding)
        val redDotView = flRoot.findViewById<View>(com.support.bottomnavigation.R.id.red_dot)
        redDotView.translationY = targetTopPadding.toFloat()
    }

    /**
     * 根据详情页状态做相应动画
     *
     * @param isDetailDisplaying 详情页是否展开
     * @param isNeedAnimOnDetailsModeChanged
     */
    private fun animateMenuStyleOnDetailModeChanged(isDetailDisplaying: Boolean, isNeedAnimOnDetailsModeChanged: Boolean) {

        val menuView = menuView ?: return
        val defaultColor =
            menuView.context.getColor(if (isDetailDisplaying) theme.background else R.color.photopage_menu_detail_display_background_color)
        val currentColor = (menuView.background as? ColorDrawable)?.color ?: defaultColor
        val targetColor =
            menuView.context.getColor(if (isDetailDisplaying) R.color.photopage_menu_detail_display_background_color else theme.background)
        alphaSpringAnimation?.cancel()

        if (isNeedAnimOnDetailsModeChanged.not()) {
            menuView.dividerView.alpha = if (isDetailDisplaying) ALPHA_FULL_DISPLAY else ALPHA_TRANSPARENT
            menuView.setBackgroundColor(targetColor)
            return
        }
        alphaSpringAnimation = COUISpringAnimation(menuView.dividerView, COUIDynamicAnimation.ALPHA).apply {
            addUpdateListener { _, value, _ ->
                val ratio = if (isDetailDisplaying) value else ALPHA_FULL_DISPLAY - value
                menuView.setBackgroundColor(ColorUtils.blendARGB(currentColor, targetColor, ratio))
            }
            setStartVelocity(ALPHA_VELOCITY)
            alphaSpringForce = (alphaSpringForce ?: COUISpringForce().setBounce(ALPHA_BOUNCE)).apply {
                setResponse(if (isDetailDisplaying) FADE_IN_ALPHA_RESPONSE else FADE_OUT_ALPHA_RESPONSE)
                finalPosition = if (isDetailDisplaying) ALPHA_FULL_DISPLAY else ALPHA_TRANSPARENT
            }
            spring = alphaSpringForce
            start()
        }
    }

    /**
     * 获取展示更多菜单的视图
     */
    private fun ensureMoreSubMenuView(context: Context): COUIPopupListWindow {
        fun createPopWindow(context: Context): COUIPopupListWindow {
            return COUIPopupListWindow(context).also { popWindow ->
                // 开启模糊背景效果
                popWindow.setUseBackgroundBlur(true)
                // 禁用弹窗外区域的点击事件
                popWindow.setDismissTouchOutside(true)
                // 关闭视窗发生变化是自动隐藏更多菜单功能,原因:在小屏幕横屏情况下,点击更多视窗大小会发生变化,默认会自动隐藏更多菜单,导致更多菜单无法显示问题;
                popWindow.setDismissWhenWindowSizeChange(false)
                //菜单弹窗优先级高于普通子窗口
                popWindow.windowLayoutType = WindowManager.LayoutParams.TYPE_APPLICATION_SUB_PANEL
                // 启用滑动条
                popWindow.listView.isVerticalScrollBarEnabled = true
            }
        }

        // app 亮暗色变更，则重新创建更多菜单
        val isNightMode = COUIDarkModeUtil.isNightMode(context)
        if (isNightMode != currentPopupWindowNightMode) {
            moreSubMenuView?.setOnItemClickListener(null)
            moreSubMenuView?.setSubMenuClickListener(null)
            moreSubMenuView?.setOnDismissListener(null)

            moreSubMenuView = createPopWindow(context)
            currentPopupWindowNightMode = isNightMode
        }

        return moreSubMenuView ?: createPopWindow(context).also {
            moreSubMenuView = it
        }
    }

    /**
     * 更新红点提示
     */
    private fun MenuItem.updateTips(tips: PhotoMenuItemTips, position: Int) {
        when (tips) {
            is PhotoMenuItemTips.ItemTipsNumber -> {
                hideIconTips()
                showRedNumberTips(tips, position)
            }
            is PhotoMenuItemTips.ItemTipsText -> showTextTips(tips)
            // 文字提示不在这里取消，而是所有菜单都没有文本提示时，才会取消。
            is PhotoMenuItemTips.ItemTipsNone -> hideTips(position)
            is PhotoMenuItemTips.ItemIconTips -> showIconTips(tips)
        }
    }

    /**
     * 隐藏菜单项所有提示
     */
    private fun MenuItem.hideTips(position: Int) {
        hideRedNumberTips(position)
        hideIconTips()
    }

    /**
     * 显示文字提示
     */
    private fun MenuItem.showTextTips(tips: PhotoMenuItemTips.ItemTipsText) {
        textTips.show(this@showTextTips, tips) { menuView?.findViewById(it) }
    }

    /**
     * 显示红点数字提示
     */
    private fun showRedNumberTips(
        tips: PhotoMenuItemTips.ItemTipsNumber,
        position: Int
    ) {
        menuView?.run {
            setTipsView(position, tips.number, TIPS_CIRCLE)
        }
    }

    /**
     * 隐藏数字提示
     */
    private fun hideRedNumberTips(position: Int) {
        menuView?.run {
            setTipsView(position, 0, TIPS_HIDE)
        }
    }

    @SuppressLint("RestrictedApi")
    private fun MenuItem.showIconTips(
        tips: PhotoMenuItemTips.ItemIconTips
    ) {
        menuView?.couiNavigationMenuView?.findItemView(itemId)?.let { itemView ->
            itemView.doOnPreDraw {
                val iconTipView = itemView.findViewById<ImageView>(ID_VIEW_ICON_TIP)
                if (iconTipView == null) {
                    createIconTipView(tips)?.apply {
                        itemView.addView(this)
                        if (tips.type == IconTipType.RESTRICT_WATERMARK) {
                            watermarkTipView = this
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("RestrictedApi")
    private fun MenuItem.hideIconTips() {
        menuView?.couiNavigationMenuView?.findItemView(itemId)?.let { itemView ->
            itemView.findViewById<View>(ID_VIEW_ICON_TIP)?.let { iconView ->
                itemView.removeView(iconView)
            }
        }
    }

    private fun createIconTipView(tips: PhotoMenuItemTips.ItemIconTips): View? {
        if (tips.type != IconTipType.RESTRICT_WATERMARK) return null
        val context = sectionPage.pageContext ?: return null
        val tipIcon = tips.tipsDrawable ?: return null
        val view = ImageView(context)
        view.id = ID_VIEW_ICON_TIP

        // 高度固定为18，宽度与父布局一致，角标图片靠右对齐，当图片宽度变化时，高度不变，宽度自适应变化，最宽不会超出父布局宽度
        val height = context.resources.getDimensionPixelSize(R.dimen.photopage_bottom_menu_icon_tips_height)
        // tipsView 的中点位于图标的 top，所以：marginTop = 菜单项图标marginTop - tipsView高度/2
        val marginTop = iconTopSpace - height / 2
        val marginRight = context.resources.getDimensionPixelSize(R.dimen.photopage_bottom_menu_icon_tips_margin_right)
        view.layoutParams = FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, height)
        view.updateMargin(marginRight, marginTop, marginRight, 0)
        view.scaleType = ImageView.ScaleType.FIT_END
        view.setImageDrawable(tipIcon)
        return view
    }

    /**
     * 将 SubMenuItem 转换为 PopupListWindowItem
     */
    private fun SubMenuItem.toPopupListWindowItem(resources: Resources): PopupListWindowItem {
        val itemTitle = moreMenuCreator.makeMoreMenuItemTitleId(sectionPage.pageViewModel, this)
            .takeIf { it != Resources.ID_NULL }
            ?.let(resources::getString)
            ?: TextUtil.EMPTY_STRING

        return PopupListWindowItem(
            itemTitle = itemTitle,
            isItemEnable = true,
            tipsCount = if (hasTips) PopupListWindowItem.SHOW_TIPS_WITH_RED_DOT else PopupListWindowItem.HIDE_TIPS,
            mGroupId = groupId
        )
    }

    /**
     * 配置菜单项显示内容（动画优先）
     * @param menuItem 菜单项
     * @param currentState 当前菜单状态
     * @param previousState 先前菜单状态（可为null）
     */
    private fun configureMenuItemDisplay(
        menuItem: MenuItem,
        theme: MenuDecorationTheme,
        currentState: MenuItemState,
        previousState: MenuItemState?
    ) {
        val currentIcon = currentState.menuIcon
        val previousIcon = previousState?.menuIcon

        when (currentIcon) {
            is MenuIcon.NULL -> Unit
            // 如果为静态图标，直接更新图标, 这里涉及主题变化，需要更新图标
            is MenuIcon.Static -> configureMenuItemDisplayForStatic(menuItem, theme, currentIcon, previousIcon, previousState)
            // Lottie 动画图标
            is MenuIcon.Lottie -> configureMenuItemDisplayForLottie(menuItem, theme, currentIcon, previousIcon, previousState)
        }
    }

    private fun configureMenuItemDisplayForStatic(
        menuItem: MenuItem,
        theme: MenuDecorationTheme,
        menuIcon: MenuIcon.Static,
        previousIcon: MenuIcon?,
        previousState: MenuItemState?
    ) {
        val iconRes = menuIcon.getIconRes(theme)
        val previousIconRes = previousState?.iconTheme?.let { (previousIcon as? MenuIcon.Static)?.getIconRes(it) }

        if (iconRes == previousIconRes) return
        updateMenuIconDrawable(menuItem, iconRes)
    }

    private fun configureMenuItemDisplayForLottie(
        menuItem: MenuItem,
        theme: MenuDecorationTheme,
        menuIcon: MenuIcon.Lottie,
        previousIcon: MenuIcon?,
        previousState: MenuItemState?
    ) {
        val currentAni = menuIcon.getAsset(theme)
        val previousAni = previousState?.iconTheme?.let { (previousIcon as? MenuIcon.Lottie)?.getAsset(it) }

        // 获取先前是否是选中状态
        val isPreSelected = previousIcon?.let {
            PhotoMenuIconDrawableManager.isSelectStateForMenuIcon(menuItem.itemId, previousIcon)
        } ?: false

        when {
            // 从选中状态回到普通状态，无需开始动画
            isPreSelected -> setupLottieAnimation(menuItem, currentAni, true)
            // 动画没变，但是主题变更了，无需开始动画，MarkedBy linkailong 如果上一个动画还在进行中要如何处理？写此行时资源暂时未区分亮暗色
            (menuIcon == previousIcon) && (theme != previousState?.iconTheme) -> setupLottieAnimation(menuItem, currentAni, true)
            // 动画变更，重新开始动画
            currentAni != previousAni -> setupLottieAnimation(menuItem, currentAni, false, iconChangeWithAni = true)
        }
    }

    /**
     * 更新菜单项图标资源（含异步加载逻辑）
     */
    private fun updateMenuIconDrawable(menuItem: MenuItem, drawableResId: Int) {
        if (drawableResId == Resources.ID_NULL) {
            GLog.d(TAG, LogFlag.DL) { "[updateMenuIconDrawable] drawableResId ID_NULL " }
            return
        }
        val asyncLoadDrawable = asyncLoadDrawable(drawableResId)
        runningPrepareMenuJobs[menuItem.itemId]?.destroy()
        runningPrepareMenuJobs[menuItem.itemId] = asyncLoadDrawable
        asyncLoadDrawable.getIt { cacheDrawable ->
            runningPrepareMenuJobs.remove(menuItem.itemId)
            cancelMenuItemReplaceAnimation(menuItem)
            val view = menuItemAnimationViewMap[menuItem.itemId]
            view?.apply {
                (parent as? ViewGroup)?.removeView(this)
                removeAllAnimatorListeners()
            }
            cacheDrawable?.let(menuItem::setIcon) ?: let {
                menuItem.setIcon(drawableResId)
            }
        }
    }

    /**
     * 配置动画参数
     */
    private fun setupLottieAnimation(
        menuItem: MenuItem,
        animationRes: String,
        isScrollToMaxFrame: Boolean,
        iconChangeWithAni: Boolean = false
    ) {
        getOrCreateAnimationView(menuItem)?.apply {
            GLog.d(TAG, LogFlag.DL) { "[setupLottieAnimation] isScrollToMaxFrame$isScrollToMaxFrame " }
            runningPrepareMenuJobs.remove(menuItem.itemId)?.destroy()
            cancelMenuItemReplaceAnimation(menuItem)
            setAnimation(animationRes)
            repeatCount = 1
            if (iconChangeWithAni) {
                doMenuItemReplaceAnimation(menuItem, this)
            } else {
                menuItem.icon = null
            }

            if (isScrollToMaxFrame) {
                frame = maxFrame.toInt()
            } else {
                playAnimation()
            }
        }
    }

    /**
     * 获取菜单项的EffectiveAnimationView（用于动效展示）
     * @param menuItem 底部菜单项
     * @return EffectiveAnimationView对象
     */
    @MainThread
    private fun getOrCreateAnimationView(menuItem: MenuItem): EffectiveAnimationView? {

        val effectiveAnimationView = menuItemAnimationViewMap[menuItem.itemId] ?: EffectiveAnimationView(sectionPage.pageContext)
        effectiveAnimationView.apply {
            cancelAnimation()
            removeAllAnimatorListeners()
            if ((parent as? ViewGroup) == null) {
                val iconImageView = getMenuIconView(menuItem)
                if (iconImageView == null) {
                    GLog.e(TAG, LogFlag.DL) { "[getOrCreateAnimationView] Could not find icon view " }
                    return null
                }
                val parentViewGroup = iconImageView.parent as? ViewGroup

                if (parentViewGroup == null) {
                    GLog.e(TAG, LogFlag.DL) { "[getOrCreateAnimationView] Could not find icon view parent" }
                    return null
                }
                // 复制原图标的布局参数
                layoutParams = iconImageView.layoutParams
                // 添加到原图标同一位置
                parentViewGroup.addView(this)
            }
            scaleType = ImageView.ScaleType.FIT_CENTER
            // 映射关系存储
            menuItemAnimationViewMap[menuItem.itemId] = this
            contentDescription = if (menuItem.contentDescription.isNullOrEmpty()) {
                menuItem.title
            } else {
                menuItem.contentDescription
            }
        }
        return effectiveAnimationView
    }

    /**
     * 执行menuItem的替换动画
     * @param menuItem 底部菜单项
     * @param effectiveAnimationView EffectiveAnimationView对象，用于Lottie动效展示
     */
    private fun doMenuItemReplaceAnimation(menuItem: MenuItem, effectiveAnimationView: EffectiveAnimationView) {
        val menuIconView = getMenuIconView(menuItem) ?: return

        val fadeOutAnimation = menuIconView.createAlphaSpringAnimation(ALPHA_TRANSPARENT, ALPHA_BOUNCE, FADE_OUT_ALPHA_RESPONSE)
        val fadeInAnimation = effectiveAnimationView.createAlphaSpringAnimation(ALPHA_FULL_DISPLAY, ALPHA_BOUNCE, FADE_IN_ALPHA_RESPONSE)
        menuItemReplaceAnimationMap[menuItem.itemId] = Pair(fadeOutAnimation, fadeInAnimation)
        effectiveAnimationView.alpha = ALPHA_TRANSPARENT
        fadeOutAnimation.addEndListener { _, _, _, _ ->
            menuItem.icon = null
            menuIconView.alpha = ALPHA_FULL_DISPLAY
        }
        fadeOutAnimation.start()
        fadeInAnimation.start()
    }

    /**
     * 取消当前menuIcon的动画效果，并恢复原图标的显示状态
     */
    private fun cancelMenuItemReplaceAnimation(menuItem: MenuItem) {
        menuItemReplaceAnimationMap[menuItem.itemId]?.apply {
            first.cancel()
            second.cancel()
        }
        getMenuIconView(menuItem)?.alpha = ALPHA_FULL_DISPLAY
        menuItemAnimationViewMap[menuItem.itemId]?.alpha = ALPHA_FULL_DISPLAY
    }

    /**
     * 获取菜单项的icon ImageView
     */
    private fun getMenuIconView(menuItem: MenuItem): ImageView? {
        var iconImageView = menuItemIconViewMap[menuItem.itemId]
        if (iconImageView == null) {
            val viewFromMenuItem = menuView?.findViewById<View>(menuItem.itemId)
            if (viewFromMenuItem == null) {
                GLog.e(TAG, LogFlag.DL) { "[getMenuIconView] Could not find view with id ${menuItem.itemId} in menu view." }
                return null
            }
            iconImageView = viewFromMenuItem.findViewById<ImageView>(com.google.android.material.R.id.navigation_bar_item_icon_view)
            if (iconImageView == null) {
                GLog.e(TAG, LogFlag.DL) { "[getMenuIconView] Could not find icon view in view with id ${menuItem.itemId}." }
                return null
            }
            menuItemIconViewMap[menuItem.itemId] = iconImageView
        }
        return iconImageView
    }

    companion object {
        private const val TAG = "PhotoBottomMenuDecoration"
        private val ID_VIEW_ICON_TIP = R.id.view_icon_tip
        private const val ALPHA_VELOCITY = 0f
        private const val ALPHA_BOUNCE = 0f
        private const val FADE_IN_ALPHA_RESPONSE = 0.3f
        private const val FADE_OUT_ALPHA_RESPONSE = 0.25f
        private const val ALPHA_FULL_DISPLAY = 1f
        private const val ALPHA_TRANSPARENT = 0f
        private const val ALPHA_DISABLED = 0.3f
        private const val DEFAULT_ICON_HEIGHT_DP = 24
    }
}
