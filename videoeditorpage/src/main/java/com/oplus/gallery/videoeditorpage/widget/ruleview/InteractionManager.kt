/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  StateManager
 ** Description: add
 ** Version: 1.0
 ** Date : 2025/4/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/4/28      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ruleview

import android.view.MotionEvent
import com.oplus.gallery.videoeditorpage.utlis.CollectionUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.widget.ruleview.SlideDirection.Companion.LEFT
import com.oplus.gallery.videoeditorpage.widget.ruleview.SlideDirection.Companion.NONE
import com.oplus.gallery.videoeditorpage.widget.ruleview.SlideDirection.Companion.RIGHT
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 刻度尺RuleView类的交互处理管理类（处理点击，滑动，吸附等逻辑）
 */
class InteractionManager(
    private val ruleView: RuleView,
    private val gradationGapRules: List<GradationGapRule>,
    private val specialGradations: List<SpecialGradationRule>,
    private val ruleViewAttributes: RuleViewAttributes,
    private val longGradationPositionInfoList: MutableList<GradationDistanceInfo>,
) {
    /** 当前值  */
    var currentValue: Float = 0f

    /**
     * 当前数值，放大10倍：currentValue * 10
     * 用于内部计算，避免浮点精度问题
     */
    var currentNumber: Int = 0
    /**
     * 上一次数值，oldNumber和currentNumber比较，避免重复震动
     */
    private var oldNumber: Int = 0

    /**
     * 最大数值与最小数值间的距离：(mMaxNumber - mMinNumber) / mNumberUnit * gradationGap
     * 表示整个刻度范围在屏幕上对应的像素距离
     */
    var numberRangeDistance: Float = 0f

    /**
     * 当前数值与最小值的距离：(mCurrentNumber - mMinNumber) / mNumberUnit * gradationGap
     * 当前指针位置与最小值位置的像素距离
     */
    var currentDistance: Float = 0f

    /** 触摸事件起始X坐标  */
    private var downX: Int = 0

    /** 上次触摸事件坐标  */
    private var lastX: Int = 0
    private var lastY: Int = 0

    /** 是否已经开始滑动（区分点击和滑动）  */
    private var isMoved: Boolean = false
    /** 是否按下  */
    var isPressed: Boolean = false
    /** 按下时间  */
    private var touchDownTime: Long = 0L
    /** 吸附时开始的x坐标 */
    private var snapStartX: Float = INVALID_VALUE
    /** 吸附时的滑动方向 */
    @SlideDirection
    private var snapSlideDirection: Int = NONE

    /** 是否在当前滑动过程中禁用吸附效果，直到经过某个吸附点  true 表示禁用吸附功能  false 表示打开吸附功能 */
    private var disableSnapForThisSlide: Boolean = false

    /**
     * 根据distance距离，计算当前刻度值
     * 并触发值变化回调
     */
    fun calculateCurrentIndicatorValue() {
        // 限定滑动范围：在最小值与最大值之间
        currentDistance = min(
            max(currentDistance.toDouble(),
                DISTANCE_RANGE_MIN
            ),
            numberRangeDistance.toDouble()
        ).toFloat()
        // 保存旧值，用于比较是否变化
        oldNumber = currentNumber
        // 根据当前距离计算刻度值
        currentNumber = calculateNumberFromDistance(currentDistance, false)
        // 转回浮点值
        currentValue = currentNumber / VALUE_TO_NUMBER_SCALE

        // 如果有监听器，通知值变化
        if (oldNumber != currentNumber) {
            ruleView.ruleChangedListener?.onValueChanged(currentValue)
        }
    }

    /**
     * 根据当前距离计算对应的值，并返回刻度值
     * 考虑不同区间的刻度间隔
     *
     * @param distance 当前距离
     * @param roundMode 取整模式: true表示四舍五入到最近刻度, false表示向下取整
     * @return 对应的刻度值（放大10倍的整数）
     */
    fun calculateNumberFromDistance(distance: Float, roundMode: Boolean = true): Int {
        if (CollectionUtils.isEmpty(gradationGapRules)) {
            return NUMBER_VALUE_DEFAULT
        }
        var remainingDistance = distance
        for (rule in gradationGapRules) {
            val startNum = (rule.startValue * VALUE_TO_NUMBER_SCALE).toInt()
            val endNum = (rule.endValue * VALUE_TO_NUMBER_SCALE).toInt()
            val rangeNumber = endNum - startNum
            val ruleDistance = (rangeNumber / ruleViewAttributes.numberUnit.toFloat()) * rule.gapPx
            if (remainingDistance <= ruleDistance) {
                // 计算在当前规则下的精确刻度位置
                val exactScale = remainingDistance / rule.gapPx
                val scaleNumber = if (roundMode) {
                    Math.round(exactScale)  // 四舍五入到最近的刻度
                } else {
                    Math.floor(exactScale.toDouble()).toInt()  // 向下取整到最近的刻度
                }
                val exactNumber = startNum + (scaleNumber * ruleViewAttributes.numberUnit)
                // 确保结果在当前规则范围内
                return min(max(exactNumber, startNum), endNum)
            } else {
                // 超过当前规则范围
                remainingDistance -= ruleDistance
            }
        }
        // 如果超出范围，返回最大值
        return ruleViewAttributes.maxNumber
    }

    /**
     * 事件处理方法
     */
    fun onTouchEvent(event: MotionEvent): Boolean {
        // 获取事件类型和坐标
        val action = event.action
        val x = if (ruleView.isRtl) (ruleView.getContentWidth() - event.x.toInt()) else event.x.toInt()
        val y = event.y.toInt()

        when (action) {
            MotionEvent.ACTION_DOWN -> handleActionDown(x)
            MotionEvent.ACTION_MOVE ->  handleActionMove(x, y)
            MotionEvent.ACTION_UP -> {
                // 处理滑动结束（手指抬起）
                resetAndLockSnapState()
                isPressed = false
                if (!isMoved) {
                    // 只有在触摸时间小于阈值时才处理点击事件
                    ruleView.invalidate()
                    return if ((System.currentTimeMillis() - touchDownTime) <= CLICK_EVENT_DURATION_MAX) {
                        ruleView.handleClickEvent(x)
                    } else {
                        true
                    }
                }
                ruleView.handleScrollFling()
                ruleView.invalidate()
            }
            MotionEvent.ACTION_CANCEL -> {
                resetAndLockSnapState()
                isPressed = false
                ruleView.handleScrollFling()
                ruleView.invalidate()
            }
        }
        // 更新上次触摸位置
        lastX = x
        lastY = y
        return true
    }

    /**
     * 把真实数值转换成绘制数值
     * 转换为整数进行计算，提高精度和性能
     */
    fun convertValue2Number() {
        // 将浮点值放大10倍转为整数
        ruleViewAttributes.minNumber = (ruleViewAttributes.minValue * VALUE_TO_NUMBER_SCALE).toInt()
        ruleViewAttributes.maxNumber = (ruleViewAttributes.maxValue * VALUE_TO_NUMBER_SCALE).toInt()
        currentNumber = (currentValue * VALUE_TO_NUMBER_SCALE).toInt()
        recalculateDistances()
    }

    /**
     * 根据值计算对应的距离
     * 考虑不同区间的刻度间隔
     *
     * @param number 值（放大10倍的整数）
     * @return 对应的距离
     */
    fun calculateDistanceFromNumber(number: Int): Float {
        if (CollectionUtils.isEmpty(gradationGapRules)) {
            return DISTANCE_VALUE_DEFAULT
        }
        var distance = DISTANCE_VALUE_DEFAULT
        for (rule in gradationGapRules) {
            val startNum = (rule.startValue * VALUE_TO_NUMBER_SCALE).toInt()
            val endNum = (rule.endValue * VALUE_TO_NUMBER_SCALE).toInt()
            if (number <= endNum) {
                // 在当前规则范围内
                distance += ((number - startNum) / ruleViewAttributes.numberUnit.toFloat() * rule.gapPx)
                return distance
            } else {
                // 累加这个规则范围的全部距离
                distance += ((endNum - startNum) / ruleViewAttributes.numberUnit.toFloat() * rule.gapPx)
            }
        }

        // 如果超出范围，返回最大值对应的距离
        return numberRangeDistance
    }

    /**
     * 重置吸附状态并添加临时锁定逻辑，防止立即再次吸附
     */
    private fun resetAndLockSnapState() {
        snapSlideDirection = NONE
        disableSnapForThisSlide = false
        snapStartX = INVALID_VALUE  // 重置吸附点距离
    }

    /**
     * 检查当前交互状态是否处于已吸附状态
     *
     * @return Boolean 返回true表示当前处于已吸附状态，返回false表示未吸附
     */
    private fun isSnapping(): Boolean {
        return snapStartX >= 0
    }

    /**
     * 通过滑动前后的distance位置，判断是否经过了某一个吸附点
     *
     * 该方法用于判断当前距离是否在两个吸附点之间，根据滑动方向进行判断。
     * 如果当前距离在两个吸附点之间，则返回true，否则返回false。
     *
     * @param currentDistance 当前距离
     * @param lastCheckedDistance 上一次检查的距离
     * @param slideDirection 滑动方向
     * @return 如果当前距离在两个吸附点之间则返回true，否则返回false
     */
    fun isPassedOneSnapPoint(currentDistance: Float, lastCheckedDistance: Float, slideDirection: Int): Boolean {
        if ((currentDistance == lastCheckedDistance) || (slideDirection == NONE)) {
            return false
        }
        if (CollectionUtils.isEmpty(longGradationPositionInfoList)) {
            return false
        }
        if ((currentDistance == longGradationPositionInfoList.first().distance) ||
            (currentDistance == longGradationPositionInfoList.last().distance)) {
            return true
        }

        // 遍历排序后的吸附点列表，检查 currentDistance 是否在两个吸附点之间
        for (i in 0 until longGradationPositionInfoList.size - 1) {
            val currentLongGradationPositionInfo = longGradationPositionInfoList[i]

            //通过判断长刻度元素的位置是否在currentDistance和lastCheckedDistance之间，决定是否需要震动
            val isWithinRange = when (slideDirection) {
                LEFT -> {
                    /**
                     *  向左滑动（手指右滑，刻度左移， 刻度越来越小）  currentDistance必须要大于lastCheckedDistance
                     *   当前吸附点在范围 (lastCheckedDistance， currentDistance]，表示经过吸附点
                     */
                    currentDistance > lastCheckedDistance &&
                            currentLongGradationPositionInfo.distance <= currentDistance &&
                            currentLongGradationPositionInfo.distance > lastCheckedDistance
                }
                /**
                 * 向右滑动（手指左滑，刻度右移， 刻度越来越大） currentDistance必须要小于lastCheckedDistance
                 * 当前吸附点在范围 (currentDistance， lastCheckedDistance]，表示经过吸附点
                 */
                RIGHT -> {
                    currentDistance < lastCheckedDistance &&
                            currentLongGradationPositionInfo.distance >= currentDistance &&
                            currentLongGradationPositionInfo.distance < lastCheckedDistance
                }
                else -> false // 其他情况直接返回false
            }
            //在范围内，表示经过吸附点
            if (isWithinRange) {
                return true
            }
        }

        return false
    }

    /**
     * 重新计算基于规则的距离值
     */
    fun recalculateDistances() {
        // 校验输入参数是否为空
        if (gradationGapRules.isEmpty()) {
            return
        }
        // 初始化变量
        numberRangeDistance = DISTANCE_VALUE_DEFAULT
        val longGradationDistanceMap = LinkedHashMap<Float, Int>() // 使用 LinkedHashMap 保持插入顺序
        for (rule in gradationGapRules) {
            val startNum = (rule.startValue * VALUE_TO_NUMBER_SCALE).toInt()
            val endNum = (rule.endValue * VALUE_TO_NUMBER_SCALE).toInt()
            // 校验范围是否有效
            if (startNum > endNum) {
                throw IllegalArgumentException("Invalid range: startNum ($startNum) is greater than endNum ($endNum)")
            }
            // 处理普通规则
            processRuleRange(startNum, endNum, rule, longGradationDistanceMap)
            // 处理特殊规则
            processSpecialGradations(startNum, endNum, rule, longGradationDistanceMap)
            // 更新总距离
            numberRangeDistance += calculateRangeDistance(startNum, endNum, rule)
        }
        // 转换并更新位置信息列表
        longGradationPositionInfoList.clear()
        longGradationPositionInfoList.addAll(transformGradationPositionMapToList(longGradationDistanceMap))
        // 重新计算当前距离
        currentDistance = DISTANCE_VALUE_DEFAULT
        for (rule in gradationGapRules) {
            val startNum = (rule.startValue * VALUE_TO_NUMBER_SCALE).toInt()
            val endNum = (rule.endValue * VALUE_TO_NUMBER_SCALE).toInt()

            if (currentNumber <= endNum) {
                // 当前值在这个规则范围内
                currentDistance += calculateCurrentDistance(startNum, currentNumber, rule)
                break
            } else {
                // 累加这个规则范围的全部距离
                currentDistance += calculateRangeDistance(startNum, endNum, rule)
            }
        }
    }

    /**
     * 处理移动事件
     */
    private fun handleActionMove(x: Int, y: Int) {
        val offsetX = x - lastX
        // 判断是否已经开始滑动（区分点击和滑动）
        if (!isMoved) {
            // 计算Y方向移动距离
            val dy = y - lastY
            // 滑动的触发条件：水平滑动大于垂直滑动；滑动距离大于阈值
            if ((abs(offsetX.toDouble()) < abs(dy.toDouble())) || (abs((x - downX).toDouble()) < ruleViewAttributes.touchSlop)) {
                return
            }
            // 设置已开始滑动标志
            isMoved = true
        }
        // 处理方向性吸附
        if (!disableSnapForThisSlide && handleSnapEvent(offsetX, x)) {
            // 当前处理吸附状态，刻度条不需要滑动
            return
        }
        currentDistance -= offsetX.toFloat()

        if (disableSnapForThisSlide) {
            // 在吸附点按下，吸附功能会被禁用，直到离开了这个吸附点，重新打开吸附功能（背景： 在吸附点按下，此时不需要吸附功能）
            checkAndEnableSnapIfMovedAway(x)
        } else {
            // 非禁用状态，正常检查吸附(设置吸附状态，找到最近的吸附点)
            checkAndSnapToClosestLongGradation(offsetX, x)
        }
        // 计算新的当前值
        calculateCurrentIndicatorValue()
        // 重绘视图
        ruleView.invalidate()
        ruleView.checkVibrate()
    }

    /**
     * 处理刻度吸附状态和相关滑动行为
     *
     * 该方法负责管理已经处于吸附状态的刻度尺的行为:
     * 1. 检测用户滑动方向变化，若方向改变则立即解除吸附
     * 2. 计算用户在同方向上的滑动距离，超过阈值则解除吸附
     * 3. 当吸附状态被解除时，记录相关信息防止短时间内重新吸附
     * 4. 返回true表示事件已被处理，当前滑动应被忽略，维持在吸附位置
     *
     * @param offsetX 当前X方向滑动的增量，负值表示向左滑动，正值表示向右滑动
     * @param x 当前触摸事件的X坐标
     * @param y 当前触摸事件的Y坐标
     * @return 如果事件已被吸附处理则返回true，否则返回false继续处理常规滑动
     */
    private fun handleSnapEvent(offsetX: Int, currentX: Int): Boolean {
        if (!isSnapping()) return false

        // 计算当前滑动方向
        val currentDirection = SlideDirection.fromDelta(offsetX)
        if ((currentDirection == NONE) || (snapSlideDirection == NONE)) {
            return false // 无效或无滑动方向，直接返回
        }

        // 缓存移动距离计算结果
        val movedDistance = abs(currentX - snapStartX)
        return when {
            // 反向滑动，立即解除吸附
            currentDirection != snapSlideDirection -> {
                resetAndLockSnapState()
                false
            }
            // 移动距离未超过阈值，保持吸附状态
            movedDistance < ruleViewAttributes.snapEscapeDistance -> true
            // 超过阈值，解除吸附
            else -> {
                resetAndLockSnapState()
                false
            }
        }
    }

    /**
     * 处理普通规则范围
     */
    private fun processRuleRange(
        startNum: Int,
        endNum: Int,
        rule: GradationGapRule,
        distanceMap: MutableMap<Float, Int>
    ) {
        for (i in startNum..endNum step ruleViewAttributes.numberUnit * ruleViewAttributes.numberPerCount) {
            val distance = calculateDistance(i, startNum, rule)
            distanceMap[distance] = i
        }
    }

    /**
     * 根据给定的起始值、结束值、规则和距离映射表，处理特殊规则。
     * 遍历特殊刻度值，计算其距离并将其添加到距离映射表中。
     */
    private fun processSpecialGradations(
        startNum: Int,
        endNum: Int,
        rule: GradationGapRule,
        distanceMap: MutableMap<Float, Int>
    ) {
        for (specialGradation in specialGradations) {
            val specialGradationNumber = (specialGradation.value * VALUE_TO_NUMBER_SCALE).toInt()
            if (specialGradationNumber < startNum || specialGradationNumber > endNum) {
                continue
            }
            val distance = calculateDistance(specialGradationNumber, startNum, rule)
            distanceMap[distance] = specialGradationNumber
        }
    }

    /**
     * 根据给定的起始值、结束值和规则，计算控件绘制范围内的距离。
     *
     * @param startNum 范围的起始值
     * @param endNum 范围的结束值
     * @param rule 包含计算规则的 GradationGapRule 对象
     * @return 计算得到的范围距离，单位为像素
     */
    private fun calculateRangeDistance(startNum: Int, endNum: Int, rule: GradationGapRule): Float {
        val rangeNumber = endNum - startNum
        return rangeNumber / ruleViewAttributes.numberUnit.toFloat() * rule.gapPx
    }

    /**
     * 提取重复逻辑：计算当前距离
     */
    private fun calculateCurrentDistance(startNum: Int, currentNumber: Int, rule: GradationGapRule): Float {
        return (currentNumber - startNum) / ruleViewAttributes.numberUnit.toFloat() * rule.gapPx
    }

    /**
     * 提取重复逻辑：计算单个距离
     */
    private fun calculateDistance(number: Int, startNum: Int, rule: GradationGapRule): Float {
        return (number - startNum) / ruleViewAttributes.numberUnit.toFloat() * rule.gapPx + numberRangeDistance
    }

    /**
     * 将渐变位置映射转换为列表
     */
    private fun transformGradationPositionMapToList(longGradationDistanceMap: HashMap<Float, Int>): ArrayList<GradationDistanceInfo> {
        val gradationPositionInfoList: ArrayList<GradationDistanceInfo> = ArrayList()
        // 遍历映射，对于每一对距离和位置，创建一个 GradationDistanceInfo 对象并添加到列表中
        for ((distance, number) in longGradationDistanceMap) {
            gradationPositionInfoList.add(GradationDistanceInfo(distance, number, number / VALUE_TO_NUMBER_SCALE))
        }
        // 按照 distance 属性升序排序
        gradationPositionInfoList.sortBy { it.number }
        // 返回转换并排序后的列表
        return gradationPositionInfoList
    }

    /**
     * 检查并吸附到长刻度或特殊刻度
     * 1. 当用户滑动经过长刻度或特殊刻度点时，检测是否满足吸附条件
     * 2. 使用距离阈值(mSnapTriggerDistance)判断是否足够接近刻度点
     * 3. 找到最佳吸附点后，记录相关状态并将视图位置调整到精确的刻度点
     *
     * 吸附过程包含:
     * - 计算当前值和可能的吸附点
     * - 确定最近的符合条件的吸附点
     * - 设置吸附状态和记录滑动方向
     * - 直接调整当前位置到吸附点位置
     *
     * @param dx 当前X方向移动的距离，用于判断滑动方向
     */
    private fun checkAndSnapToClosestLongGradation(dx: Int, x: Int, isDownEvent: Boolean = false) {
        // 如果已经处于吸附状态 或 本次滑动禁用吸附，则不检查
        if (isSnapping() || disableSnapForThisSlide) {
            return
        }
        val longGradationInfo = findSnappingLongGradationInfo(currentDistance, ruleViewAttributes.snapTriggerDistance, dx)
        if (longGradationInfo == null) {
            GLog.d(TAG, LogFlag.DL, "[checkAndSnapToClosestLongGradation] checkAndSnapToLongGradation  no close LongGradationInfo")
            return
        }
        snapSlideDirection = SlideDirection.fromDelta(dx)
        // 记录吸附开始时的起始位置
        snapStartX = x.toFloat() + longGradationInfo.distance - currentDistance
        if (!isDownEvent) {
            // 直接将当前距离设为目标距离（吸附）
            currentDistance = longGradationInfo.distance
        }
    }

    /**
     * 根据给定的距离和触发距离阈值，在长刻度信息列表中查找符合吸附条件的长刻度信息
     * 此函数用于处理吸附逻辑，即当用户滚动到接近某个长刻度时，希望自动对齐到该刻度
     *
     * @param distance 用户当前滚动的距离
     * @param snapTriggerDistance 触发吸附的距离阈值
     * @param offsetX x方形的偏移量
     * @return 如果找到符合条件的长刻度信息，则返回该信息；否则返回 null
     */
    private fun findSnappingLongGradationInfo(
        distance: Float,
        snapTriggerDistance: Float,
        offsetX: Int
    ): GradationDistanceInfo? {
        // 如果列表为空，直接返回 null
        if (longGradationPositionInfoList.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "[findSnappingLongGradationInfo] longGradationPositionInfoList is empty")
            return null
        }
        if (offsetX == 0) {
            GLog.d(TAG, LogFlag.DL, "[findSnappingLongGradationInfo] offsetX is zero")
            return null
        }
        for (longGradationInfo in longGradationPositionInfoList) {
            val diff = abs(distance - longGradationInfo.distance)
            // 判断距离差是否在触发范围内，并且靠近刻度点时才匹配，远离刻度点时不匹配
            if (diff < snapTriggerDistance) {
                val isDirectionMatched = if (offsetX < 0) {
                    // 手指向左滑动，刻度点在当前位置的右侧，向刻度点靠近时匹配
                    distance < longGradationInfo.distance
                } else {
                    // 手指向右滑动，刻度点在当前位置的左侧，向刻度点靠近时匹配
                    distance > longGradationInfo.distance
                }

                if (isDirectionMatched) {
                    return longGradationInfo
                }
            }
        }

        return null
    }

    /**
     * 检查是否已离开最初的吸附点，若离开吸附点，需要恢复吸附功能
     * 按下瞬间，当前在吸附点，不需要吸附功能, 当从吸附点开始滑动并离开该点后，恢复吸附功能
     * 使用distance距离变化而非number值来判断，提高精度
     */
    private fun checkAndEnableSnapIfMovedAway(x: Int) {
        // 如果没有禁用吸附功能，无需检查
        if (!disableSnapForThisSlide) {
            return
        }
        // 计算当前位置与吸附点的滑动距离（像素）
        val moveDistance = abs(x - snapStartX)

        // 定义一个适当的像素阈值，当移动超过这个距离时重新启用吸附
        val distanceThreshold = ruleViewAttributes.snapTriggerDistance * DISTANCE_THRESHOLD_PROPORTION

        // 如果已经离开初始吸附点一定距离
        if (moveDistance > distanceThreshold) {
            // 重新启用吸附功能
            disableSnapForThisSlide = false
        }
    }

    /**
     * 处理触摸屏按下事件
     *
     * @param x 触摸位置的x坐标
     * @param y 触摸位置的y坐标
     */
    private fun handleActionDown(x: Int) {
        ruleView.ruleChangedListener?.onStartDragging()
        // 手指按下时，停止当前的滚动动画
        ruleView.stopFling()
        // 记录按下位置
        downX = x
        // 记录按下时间
        touchDownTime = System.currentTimeMillis()
        // 重置移动标志
        isMoved = false
        checkAndSnapToClosestLongGradation(OFFSET_DEFAULT, x, true)
        if (isSnapping()) {
            // 按下瞬间，当前在吸附点，不需要吸附功能
            disableSnapForThisSlide = true
        }
        isPressed = true
        ruleView.invalidate()
    }

    companion object {
        /** 吸附的检测阈值（像素） */
        private const val DISTANCE_THRESHOLD_PROPORTION = 2
        //日志
        private const val TAG = "RuleView"
        // 数字到值的缩放因子，用于将数字转换为实际值
        private const val VALUE_TO_NUMBER_SCALE = 10f
        // 默认距离值，滚动距离或偏移量
        private const val DISTANCE_VALUE_DEFAULT = 0f
        // 默认数字值，初始化数字相关的变量
        private const val NUMBER_VALUE_DEFAULT = 0
        private const val OFFSET_DEFAULT = 0
        //触发吸附功能的最小间隔时间
        private const val SNAP_INTERVAL_MILLISECOND_MIN = 100L
        //距离范围的最小值
        private const val DISTANCE_RANGE_MIN = 0.0
        /** 有效点击的最大时长（毫秒） */
        private const val CLICK_EVENT_DURATION_MAX = 150L
        //无效值
        private const val INVALID_VALUE = -1f
    }
}