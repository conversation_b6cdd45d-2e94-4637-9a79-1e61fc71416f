/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StickerViewPagerAdapter
 ** Description: 贴纸栏viewpager适配器
 ** Version: 1.0
 ** Date : 2022/10/27
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2022/10/27    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.sticker.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GalleryGridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.sticker.StickerVM.Companion.CUSTOM_CATEGORY_INDEX
import com.oplus.gallery.photoeditor.editingvvm.sticker.StickerVM.Companion.RECENT_CATEGORY_INDEX
import com.oplus.gallery.photoeditor.editingvvm.sticker.StickerVM.Companion.TYPE_ENTER_ADD_STICKER
import com.oplus.gallery.photoeditor.editingvvm.sticker.StickerVM.Companion.TYPE_EXIT_MANAGE_STATE
import com.oplus.gallery.photoeditor.editingvvm.sticker.data.StickerDBConfig
import com.oplus.gallery.photoeditor.editingvvm.sticker.data.StickerItem
import com.oplus.gallery.photoeditor.editingvvm.sticker.ui.StickerRecyclerViewConfig
import com.oplus.gallery.photoeditor.util.ThemeColorConfigHelper
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.GridItemGapDecoration
import com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
import kotlinx.coroutines.CoroutineScope
import kotlin.math.ceil

class StickerViewPagerAdapter(
    val context: Context,
    private val onItemClickListener: OnItemClickListener,
    private val lifecycleScope: CoroutineScope
) : RecyclerView.Adapter<StickerViewPagerAdapter.ViewPagerHolder>() {
    var stickerItemList: MutableList<MutableList<StickerItem>> = mutableListOf()
    private lateinit var gridLayoutDetail: GridLayoutDetail
    private var listUIConfig: EditorUIConfig.ListUIConfig = EditorUIConfig.ListUIConfig()
    private var stickerRecyclerViewConfig: StickerRecyclerViewConfig = StickerRecyclerViewConfig()
    private val stickerAdapterMap = mutableMapOf<Int, StickerAdapter>()
    /**
     * 点击相关的手势逻辑
     * 点击RecyclerView空白处要响应退出管理状态
     */
    private val clickGestureDetector: GestureDetector by lazy {
        GestureDetector(context, RecyclerViewClickProcessor())
    }

    inner class ViewPagerHolder(val view: View) : RecyclerView.ViewHolder(view) {
        fun bindView(list: MutableList<StickerItem>) {
            val recyclerView = view.findViewById<EditorLinearListView>(R.id.picture_lib_recyclerview)
            val noStickerTip = view.findViewById<SuitableSizeTextView>(R.id.picture_lib_suitablesizetextview)
            val noCustomStickerTip = view.findViewById<LinearLayout>(R.id.custom_sticker_empty_view)
            val textAddSticker = view.findViewById<TextView>(R.id.tv_add_sticker)

            textAddSticker.setTextColor(ThemeColorConfigHelper.getThemeColor(context))
            fun hideRecyclerView() {
                recyclerView.visibility = View.GONE
                if (isRecentEmpty(list)) {
                    noStickerTip.visibility = View.VISIBLE
                    noCustomStickerTip.visibility = View.GONE
                } else {
                    noStickerTip.visibility = View.GONE
                    noCustomStickerTip.visibility = View.VISIBLE
                    textAddSticker.setOnClickListener {
                        onItemClickListener.onItemClick(null, TYPE_ENTER_ADD_STICKER)
                    }
                }
            }

            fun showRecyclerView() {
                recyclerView.visibility = View.VISIBLE
                recyclerView.setItemChangeAnimationEnable(false)
                noStickerTip.visibility = View.GONE
                noCustomStickerTip.visibility = View.GONE
            }
            if (list.isEmpty()) {
                hideRecyclerView()
            } else {
                if (isRecentEmpty(list) || isCustomEmpty(list)) {
                    hideRecyclerView()
                } else {
                    showRecyclerView()
                }
            }

            updateListUIConfig()
            val gridLayoutDetail = initGridLayoutDetail()
            initRecyclerView(recyclerView, list, gridLayoutDetail)
        }

        private fun isCustomEmpty(list: MutableList<StickerItem>): Boolean {
            return (list.size == MathUtils.ONE) && (list[MathUtils.ZERO].name == StickerDBConfig.ITEM_NAME)
        }

        private fun isRecentEmpty(list: MutableList<StickerItem>): Boolean {
            return list.isEmpty()
        }

        private fun updateListUIConfig() {
            val screenWidth = stickerRecyclerViewConfig.screenWidth
            val padding = EditorUIConfig.getGridWindowHorizontalPadding(screenWidth)
            val viewWidth = screenWidth - padding * 2
            listUIConfig.updateListUIConfig(
                viewWidth,
                0,
                stickerRecyclerViewConfig.stickerGridViewDesignColumn,
                stickerRecyclerViewConfig.stickerGridViewMinItemWidth,
                stickerRecyclerViewConfig.stickerGridViewMaxItemWidth,
                stickerRecyclerViewConfig.stickerGridViewItemGap
            )
        }

        private fun initGridLayoutDetail(): GridLayoutDetail {
            gridLayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
                spanCount = listUIConfig.column
                parentWidth = stickerRecyclerViewConfig.screenWidth
                gapWidth = listUIConfig.horizontalSpacing
                edgeWidth = 0
            }.build().apply {
                itemWidth = listUIConfig.itemWidth
                itemDecorationGapPx.bottom = listUIConfig.verticalSpacing.toFloat()
            }
            return gridLayoutDetail
        }

        @SuppressLint("ClickableViewAccessibility")
        private fun initRecyclerView(
            recyclerView: EditorLinearListView,
            list: MutableList<StickerItem>,
            gridLayoutDetail: GridLayoutDetail
        ) {
            recyclerView.setLongPressAsClick(false)
            val padding = EditorUIConfig.getGridWindowHorizontalPadding(stickerRecyclerViewConfig.screenWidth)
            recyclerView.setPadding(padding, recyclerView.paddingTop, padding, recyclerView.paddingBottom)
            val gridLayoutManager = GalleryGridLayoutManager(recyclerView, gridLayoutDetail)
            val adapter = stickerAdapterMap[layoutPosition] ?: StickerAdapter(
                context,
                layoutPosition,
                onItemClickListener,
                listUIConfig.itemWidth,
                lifecycleScope
            )
            if (stickerAdapterMap[layoutPosition] == null) {
                stickerAdapterMap[layoutPosition] = adapter
            }
            removeAllItemDecorations(recyclerView)
            addItemDecoration(recyclerView, gridLayoutDetail)
            recyclerView.layoutManager = gridLayoutManager
            recyclerView.adapter = adapter
            recyclerView.addOnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                recyclerView.setOverScrollEnable(canScrollVertically(recyclerView, gridLayoutManager, gridLayoutDetail))
            }
            adapter.submitList(list)
            recyclerView.setOnTouchListener { view, event ->
                if (view is EditorLinearListView) {
                    return@setOnTouchListener clickGestureDetector.onTouchEvent(event)
                }
                return@setOnTouchListener false
            }
        }

        /**
         * 根据RecyclerView的控件高度是否能够完整显示下当前tab的所有贴纸来判定是否能够上下滑动。
         * 如果高度不能完整显示完所有贴纸，则禁止上下滑动。反之，可以上下滑动。
         */
        private fun canScrollVertically(
            recyclerView: EditorLinearListView,
            gridLayoutManager: GalleryGridLayoutManager,
            gridLayoutDetail: GridLayoutDetail
        ): Boolean {
            if (gridLayoutManager.itemCount <= 0) return false
            val lineCount = ceil(gridLayoutManager.itemCount / listUIConfig.column.toFloat())
            val height = lineCount * gridLayoutDetail.itemWidth + (lineCount - 1) * listUIConfig.horizontalSpacing
            return (recyclerView.measuredHeight - height) < 0
        }

        private fun addItemDecoration(recyclerView: EditorLinearListView, gridLayoutDetail: GridLayoutDetail) {
            recyclerView.addItemDecoration(GridItemGapDecoration(gridLayoutDetail))
        }

        private fun removeAllItemDecorations(recyclerView: EditorLinearListView) {
            recyclerView.takeIf { it.itemDecorationCount > 0 }?.let {
                for (index in it.itemDecorationCount - 1 downTo 0) {
                    it.removeItemDecoration(it.getItemDecorationAt(index))
                }
            }
        }
    }

    fun updateData(position: Int, stickerList: List<StickerItem>) {
        if ((stickerItemList.size <= position)) return
        val list = stickerItemList[position]
        if (stickerList == list) {
            GLog.d(TAG, LogFlag.DL) { "[updateData] sticker list no change." }
            return
        }
        val newSize = stickerList.size
        val oldSize = list.size
        stickerItemList[position] = stickerList.toMutableList()

        if (shouldNotifyItemChanged(position, newSize, oldSize)) {
            GLog.d(TAG, LogFlag.DL) { "[updateData] should notify item changed, position: $position." }
            notifyItemChanged(position)
            return
        }
        stickerAdapterMap[position]?.submitList(stickerItemList[position]) ?: GLog.d(TAG, LogFlag.DL) { "[updateData] stickerAdapter is null." }
    }

    /**
     * submitList不会将空页面刷新到有数据的页面、有数据的页面刷新到空页面，所以需要手动notify
     * 需要手动notify的情况：
     * 1、“最近使用”贴纸全部删除了
     * 2、“自定义”贴纸全部删除了
     * 3、“最近使用”贴纸空页面刷新到有数据
     * 4、“自定义”贴纸空页面刷新到有数据
     * @param position 当前贴纸页面位置
     * @param newSize 更新的贴纸列表大小
     * @param oldSize 原本的贴纸列表大小
     * @return 是否需要手动notify
     */
    private fun shouldNotifyItemChanged(position: Int, newSize: Int, oldSize: Int): Boolean {
        return ((newSize == 0) && (position == RECENT_CATEGORY_INDEX)) ||
                ((newSize == 1) && (position == CUSTOM_CATEGORY_INDEX)) ||
                ((oldSize == 0) && (position == RECENT_CATEGORY_INDEX)) ||
                ((oldSize == 1) && (position == CUSTOM_CATEGORY_INDEX))
    }

    fun updateStickerRecyclerViewConfig(stickerRecyclerViewConfig: StickerRecyclerViewConfig) {
        this.stickerRecyclerViewConfig = stickerRecyclerViewConfig
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewPagerHolder {
        val rootView = LayoutInflater.from(parent.context)
            .inflate(R.layout.picture3d_editor_sticker_grid_view, parent, false)
        return ViewPagerHolder(rootView)
    }

    override fun onBindViewHolder(holder: ViewPagerHolder, position: Int) {
        holder.bindView(stickerItemList[position])
    }

    override fun getItemCount() = stickerItemList.size

    inner class RecyclerViewClickProcessor : GestureDetector.SimpleOnGestureListener() {
        override fun onSingleTapUp(e: MotionEvent): Boolean {
            onItemClickListener.onItemClick(stickerItem = null, TYPE_EXIT_MANAGE_STATE)
            return super.onSingleTapUp(e)
        }
    }

    companion object {
        const val TAG = "StickerViewPagerAdapter"
    }
}