/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IEffectManager.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;

import java.util.Map;

public interface IEffectManager {
    BaseVideoClipEffect createVideoClipEffect(String filePath);

    BaseVideoClipEffect createCartoonStoryBoardEffect(String dirPath, String xmlFilePath,
                                               float timelineRation, long duration,
                                               int playType, Map<String, Object> extendsData);

    BaseVideoClipEffect createBackGroundStoryBoardEffect(int width, int height, long clipDuration);

    BaseVideoClipEffect getVideoClipTransform(String transformName);

    BaseVideoClipEffect createVideoClipEffect(String name, int type);
}
