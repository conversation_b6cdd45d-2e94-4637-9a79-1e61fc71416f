/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamTimelineEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.timeline;

import com.google.gson.Gson;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.track.MeicamTrackEffect;

import java.util.HashMap;

/**
 * 基于视频时间线的特效类
 * 可以修改特效生效范围和强度
 */
public class MeicamTimelineEffect extends BaseVideoFx {
    private static final float DEFAULT_STRENGTH = 0.8F;
    protected transient NvsTimelineVideoFx mNvsVideoFx;
    private static final String TAG = "MeicamTimelineEffect";

    private MeicamTimelineEffect() {
        super("", TYPE_PACKAGED_FX);
    }

    public MeicamTimelineEffect(String name, int type) {
        super(name, type);
        super.setStrength(DEFAULT_STRENGTH);
        stringParams = new HashMap<>();
    }

    public NvsTimelineVideoFx getNvsVideoFx() {
        return mNvsVideoFx;
    }

    public void setNvsVideoFx(NvsTimelineVideoFx nvsVideoFx) {
        mNvsVideoFx = nvsVideoFx;
    }

    @Override
    protected MeicamTimelineEffect clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamTimelineEffect result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamTimelineEffect(getName(), getType());
        }
        return result;
    }

    public String getInstalledName() {
        return mName;
    }

    @Override
    public void setOutTime(long outTime) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.changeOutPoint(outTime);
        }
        super.setOutTime(outTime);
    }

    @Override
    public void setInTime(long inTime) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.changeInPoint(inTime);
        }
        super.setInTime(inTime);
    }

    @Override
    public void setStrength(float strength) {
        if (mNvsVideoFx == null) {
            GLog.d(TAG, "mNvsVideoFx is null");
            return;
        }
        mNvsVideoFx.setFilterIntensity(strength);
        super.setStrength(strength);
    }

    @Override
    public void setStringValue(String paramName, String value) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setStringVal(paramName, value);
        }

        if ((stringParams != null) && stringParams.containsKey(paramName)) {
            stringParams.remove(paramName);
        }
        super.setStringValue(paramName, value);
    }

    public MeicamTrackEffect copy() {
        MeicamTrackEffect meicamTrackEffect = new MeicamTrackEffect(getName(), getType());
        meicamTrackEffect.setFxId(getFxId());
        meicamTrackEffect.setStrength(getStrength());
        meicamTrackEffect.setInTime(getInTime());
        meicamTrackEffect.setOutTime(getOutTime());
        HashMap<String, String> map = stringParams;
        if (stringParams != null) {
            meicamTrackEffect.stringParams = (HashMap<String, String>)map.clone();
        }
        meicamTrackEffect.setZhName(getZhName());
        meicamTrackEffect.setChName(getChName());
        meicamTrackEffect.setEnName(getEnName());
        return meicamTrackEffect;
    }
}
