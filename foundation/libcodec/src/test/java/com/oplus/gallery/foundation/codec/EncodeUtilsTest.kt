/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EncodeUtilsTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/24
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/24    1.0                create
 ******************************************************************************/

package com.oplus.gallery.foundation.codec

import android.graphics.Bitmap
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized

class EncodeUtilsTest {

    @After
    fun after() {
        unmockkAll()
    }

    @Test
    fun `should return empty array when compressToBytes smaller than 0`() {
        //Given
        val quality = -1
        val bitmap = mockk<Bitmap>()
        mockkStatic(EncodeUtils::class)

        //When
        val result = EncodeUtils.compressToBytes(bitmap, Bitmap.CompressFormat.PNG, quality)

        //Then
        Assert.assertTrue(result.isEmpty())
    }

    @Test
    fun `should return empty array when compressToBytes with quality greater than MAX_JPEG_QUALITY`() {
        //Given
        val quality = 101
        val bitmap = mockk<Bitmap>()

        //When
        val result = EncodeUtils.compressToBytes(bitmap, Bitmap.CompressFormat.PNG, quality)

        //Then
        Assert.assertTrue(result.isEmpty())
    }

    @Test
    fun `should call bitmap_compress when compressToBytes`() {
        //Given
        val quality = 100
        val bitmap = mockk<Bitmap>()
        val format = Bitmap.CompressFormat.PNG
        every { bitmap.compress(format, quality, any()) } returns true

        //When
        val result = EncodeUtils.compressToBytes(bitmap, format, quality)

        //Then
        verify { bitmap.compress(format, quality, any()) }
    }
}

@RunWith(Parameterized::class)
class BitmapExTest(
    private val inQuality: CompressQuality,
    private val outFormat: Bitmap.CompressFormat,
    private val outQuality: Int
) {

    @Test
    fun test_compressToBytes() {
        //Given
        val bitmap = mockk<Bitmap>()
        val byteArray = ByteArray(2)
        mockkStatic(EncodeUtils::class)
        every { EncodeUtils.compressToBytes(any(), any(), any()) } returns byteArray

        //When
        bitmap.compressToBytes(inQuality)

        //Then
        verify { EncodeUtils.compressToBytes(bitmap, outFormat, outQuality) }
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        fun getParameters(): Collection<*> {
            return listOf(
                arrayOf(CompressQuality.LOSSLESS, Bitmap.CompressFormat.PNG, EncodeUtils.HIGH_JPEG_QUALITY),
                arrayOf(CompressQuality.HIGH_QUALITY, Bitmap.CompressFormat.JPEG, EncodeUtils.HIGH_JPEG_QUALITY),
                arrayOf(CompressQuality.LOW_QUALITY, Bitmap.CompressFormat.JPEG, EncodeUtils.LOW_JPEG_QUALITY)
            )
        }
    }
}