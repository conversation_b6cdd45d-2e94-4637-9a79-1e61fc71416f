/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.standard_lib.codec;

import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapFactory.Options;
import android.graphics.BitmapRegionDecoder;
import android.graphics.ImageDecoder;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import android.util.Size;

import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.util.ext.BitmapFactoryKt;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.math.MathUtil;
import com.oplus.gallery.foundation.util.storage.SandBoxUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import static com.oplus.breakpad.BreakpadUtil.generateKey;
import static com.oplus.breakpad.NativeCrashGuardKt.runNativeGuarding;

import androidx.annotation.VisibleForTesting;

public class DecodeUtils {
    private static final String TAG = "DecodeUtils";
    public static final Bitmap.Config DECODE_CONFIG = Bitmap.Config.ARGB_8888;

    private static final long UNCONSTRAINED = -1;
    private static final int NUM_7 = 7;
    private static final int NUM_8 = 8;

    public static Drawable decodeDrawable(AssetManager assets, String filePath) {
        try {
            return new ImageDecoderWrapper(ImageDecoder.createSource(assets, filePath)).decodeDrawable();
        } catch (Exception e) {
            GLog.e(TAG, "decodeDrawable fail!", e);
            return null;
        }
    }

    public static Bitmap decode(AssetManager assets, String filePath) {
        try {
            return new ImageDecoderWrapper(ImageDecoder.createSource(assets, filePath)).decodeBitmap();
        } catch (Exception e) {
            GLog.e(TAG, "decode fail!", e);
            return null;
        }
    }

    public static Drawable decodeDrawable(Resources res, int resId) {
        try {
            return new ImageDecoderWrapper(ImageDecoder.createSource(res, resId)).decodeDrawable();
        } catch (Exception e) {
            GLog.e(TAG, "decode resId=" + resId + " fail.", e);
            return null;
        }
    }

    public static Bitmap decode(Resources res, int resId) {
        try {
            return new ImageDecoderWrapper(ImageDecoder.createSource(res, resId)).decodeBitmap();
        } catch (Exception e) {
            GLog.e(TAG, "decode resId=" + resId + " fail.", e);
            return null;
        }
    }

    public static Bitmap decode(Context context, Uri uri) {
        try {
            return new ImageDecoderWrapper(ImageDecoder.createSource(context.getContentResolver(), uri))
                    .setAllocator(ImageDecoder.ALLOCATOR_SOFTWARE)
                    .decodeBitmap();
        } catch (Exception e) {
            GLog.e(TAG, "decode uri=" + uri + " fail=", e);
            return null;
        }
    }

    /**
     * 根据给定的宽高裁剪图片，并返回裁剪后的bitmap
     * @param filePath 待裁剪转换为bitmap的文件名
     * @param width 目标宽度
     * @param height 目标高度
     * @param imagesInfo 原图宽高数据
     * @return 返回图片文件裁剪后的Bitmap
     */
    public static Bitmap decode(File filePath, int width, int height, List<String> imagesInfo) {
        try {
            return new ImageDecoderWrapper(ImageDecoder.createSource(filePath))
                    .setAllocator(ImageDecoder.ALLOCATOR_SOFTWARE)
                    .setTargetSampleSize(width, height, imagesInfo)
                    .decodeBitmap();
        } catch (Exception e) {
            GLog.e(TAG, "decode filePath=" + filePath + " fail=", e);
            return null;
        }
    }

    public static Bitmap decode(InputStream ins) {
        try {
            return BitmapFactory.decodeStream(ins);
        } catch (Exception e) {
            GLog.e(TAG, "decode uri=" + ins + " fail=", e);
            return null;
        }
    }

    public static Bitmap decode(String filePath) {
        try {
            return BitmapFactory.decodeFile(filePath);
        } catch (Exception e) {
            GLog.e(TAG, "decode filePath=" + filePath + " fail=", e);
            return null;
        }
    }

    public static Bitmap decode(FileDescriptor fd, Options options) {
        if (options == null) {
            options = new Options();
        }
        setOptionsMutable(options);
        return ensureGLCompatibleBitmap(BitmapFactory.decodeFileDescriptor(fd, null, options));
    }

    public static Bitmap decode(byte[] bytes, int offset, int length, Options options) {
        if (options == null) {
            options = new Options();
        }
        setOptionsMutable(options);
        return ensureGLCompatibleBitmap(BitmapFactory.decodeByteArray(bytes, offset, length,
                options));
    }

    /**
     * 后续不要调用此接口
     */
    public static Bitmap decode(String filePath, long maxNumOfPixels) {
        BitmapRegionDecoder bitmapRegionDecoder = null;
        try {
            bitmapRegionDecoder = BitmapRegionDecoder.newInstance(filePath, false);
            Options options = new Options();
            int width = bitmapRegionDecoder.getWidth();
            int height = bitmapRegionDecoder.getHeight();
            options.inSampleSize = computeSampleSize(width, height, UNCONSTRAINED, maxNumOfPixels);
            BitmapRegionDecoder finalBitmapRegionDecoder = bitmapRegionDecoder;
            return runNativeGuarding(OriginalRegionDecoder.TAG, generateKey(filePath, 0L), () ->
                    finalBitmapRegionDecoder.decodeRegion(new Rect(0, 0, width, height), options))
                    .onNativeFailure(() -> {
                                GLog.w(TAG, "decode: crash file = " + PathMask.INSTANCE.mask(filePath)
                                        + " , options = " + BitmapFactoryKt.asString(options));
                            }
                    )
                    .getOrNull();
        } catch (IOException e) {
            GLog.e(TAG, "decode, decodeRegion fail, e:" + e);
        } finally {
            if (bitmapRegionDecoder != null) {
                bitmapRegionDecoder.recycle();
            }
        }
        return null;
    }

    public static Options decodeBounds(FileDescriptor fd, Options options) {
        if (options == null) {
            options = new BitmapFactory.Options();
        }
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFileDescriptor(fd, null, options);
        options.inJustDecodeBounds = false;
        return options;
    }

    public static Options decodeBounds(String filePath, Options options) {
        if (options == null) {
            options = new BitmapFactory.Options();
        }
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(filePath, options);
        options.inJustDecodeBounds = false;
        return options;
    }

    public static Options decodeBounds(InputStream ins, Options options) {
        if (options == null) {
            options = new BitmapFactory.Options();
        }
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeStream(ins, null, options);
        options.inJustDecodeBounds = false;
        return options;
    }

    public static Options decodeBounds(byte[] bytes, int offset, int length, Options options) {
        if (options == null) {
            options = new BitmapFactory.Options();
        }
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeByteArray(bytes, offset, length, options);
        options.inJustDecodeBounds = false;
        return options;
    }

    public static Options decodeBounds(Context context, Uri uri, String filePath, Options options) {
        if (options == null) {
            options = new Options();
        }
        options.inJustDecodeBounds = true;
        try (ParcelFileDescriptor pfd = FileAccessManager.getInstance().openFile(context, uri)) {
            if (pfd != null) {
                BitmapFactory.decodeFileDescriptor(pfd.getFileDescriptor(), null, options);
            }
        } catch (Exception e) {
            GLog.e(TAG, "FileAccessManager open file fail e=${e.message}");
        } finally {
            options.inJustDecodeBounds = false;
        }
        return options;
    }

    public static ImageData decodeVideoThumbnail(String filePath, Uri uri, boolean forceFirstFrame) {
        Bitmap bitmap = null;
        ParcelFileDescriptor fileDescriptor = null;
        try {
            fileDescriptor = FileAccessManager.getInstance().openFile(ContextGetter.context, uri);
            bitmap = decodeVideoThumbnail(fileDescriptor, forceFirstFrame);
        } catch (FileNotFoundException e) {
            GLog.e(TAG, "decodeVideoThumbnail");
        } finally {
            IOUtils.closeQuietly(fileDescriptor);
        }

        return new ImageData(ensureGLCompatibleBitmap(bitmap));
    }

    public static Bitmap decodeVideoThumbnail(ParcelFileDescriptor parcelFileDescriptor, boolean forceFirstFrame) {
        if (parcelFileDescriptor == null) {
            return null;
        }
        Bitmap bitmap = createVideoThumbnail(parcelFileDescriptor, forceFirstFrame);
        return ensureGLCompatibleBitmap(bitmap);
    }

    public static Bitmap createVideoThumbnail(ParcelFileDescriptor parcelFileDescriptor, boolean forceFirstFrame) {
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            if (parcelFileDescriptor != null) {
                retriever.setDataSource(parcelFileDescriptor.getFileDescriptor());
                byte[] data = retriever.getEmbeddedPicture();
                if (data != null) {
                    Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);
                    if (bitmap != null) {
                        return bitmap;
                    }
                }
                if (forceFirstFrame) {
                    return retriever.getFrameAtTime(0);
                } else {
                    return retriever.getFrameAtTime();
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "createVideoThumbnail", e);
        } finally {
            IOUtils.closeQuietly(parcelFileDescriptor);
            try {
                retriever.release();
            } catch (Exception e) {
                GLog.e(TAG, "createVideoThumbnail,release exception", e);
            }
        }
        return null;
    }

    public static IRegionDecoder getBitmapRegionDecoder(String filePath, FileDescriptor descriptor, boolean isYuvAndLargePhoto) {
        IRegionDecoder bitmapRegionDecoder = null;
        if (descriptor != null) {
            bitmapRegionDecoder = DecoderWrapper.getIRegionDecoder(descriptor);
        } else {
            if (!TextUtils.isEmpty(filePath)) {
                bitmapRegionDecoder = DecoderWrapper.getIRegionDecoder(filePath, false);
            }
        }
        return bitmapRegionDecoder;
    }

    public static BitmapRegionDecoder getRegionDecoder(FileDescriptor descriptor, boolean isShareable) {
        try {
            return BitmapRegionDecoder.newInstance(descriptor, isShareable);
        } catch (Exception e) {
            GLog.e(TAG, "getRegionDecoder fail; " + e);
            return null;
        }
    }

    // This function should not be called directly from
    // DecodeUtils.requestDecode(...), since we don't have the knowledge
    // if the bitmap will be uploaded to GL.
    public static Bitmap ensureGLCompatibleBitmap(Bitmap bitmap) {
        //modify for: if the bitmap will be uploaded to GLES 2.0,  bitmap config must match ARGB_8888
        //if (bitmap == null || bitmap.getConfig() != null)
        if (bitmap == null || bitmap.getConfig() == Bitmap.Config.ARGB_8888) {
            return bitmap;
        }
        Bitmap newBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, false);
        bitmap.recycle();
        return newBitmap;
    }

    public static void setOptionsMutable(Options opts) {
        opts.inMutable = true;
    }

    // Find the min x that 1 / x >= scale
    public static int computeSampleSizeLarger(float scale) {
        if (Float.compare(scale, 0f) == 0) {
            return 1;
        }
        int initialSize = (int) Math.floor(1f / scale);
        if (initialSize <= 1) {
            return 1;
        }

        return (initialSize <= NUM_8)
                ? MathUtil.prevPowerOf2(initialSize)
                : initialSize / NUM_8 * NUM_8;
    }

    // Find the max x that 1 / x <= scale.
    public static int computeSampleSize(float scale) {
        if (Float.compare(scale, 0f) == 0) {
            return 1;
        }
        int initialSize = Math.max(1, (int) Math.ceil(1 / scale));
        return (initialSize <= NUM_8)
                ? MathUtil.nextPowerOf2(initialSize)
                : (initialSize + NUM_7) / NUM_8 * NUM_8;
    }

    public static int computeSampleSize(int width,
                                        int height,
                                        long minSideLength,
                                        long maxNumOfPixels) {
        int initialSize = computeInitialSampleSize(
                width, height, minSideLength, maxNumOfPixels);
        if (initialSize <= 0) {
            return 1;
        }

        return (initialSize <= NUM_8)
                ? MathUtil.nextPowerOf2(initialSize)
                : (initialSize + NUM_7) / NUM_8 * NUM_8;
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    protected static int computeInitialSampleSize(int w,
                                                int h,
                                                long minSideLength,
                                                long maxNumOfPixels) {
        if ((maxNumOfPixels == UNCONSTRAINED) && (minSideLength == UNCONSTRAINED)) {
            return 1;
        }
        if ((maxNumOfPixels == 0) || (minSideLength == 0)) {
            return 1;
        }

        int lowerBound = (maxNumOfPixels == UNCONSTRAINED) ? 1 : (int) Math.ceil(Math.sqrt((float) (w * h) / maxNumOfPixels));

        if (minSideLength == UNCONSTRAINED) {
            return lowerBound;
        } else {
            long sampleSize = Math.min(w / minSideLength, h / minSideLength);
            return (int) Math.max(sampleSize, lowerBound);
        }
    }
}
