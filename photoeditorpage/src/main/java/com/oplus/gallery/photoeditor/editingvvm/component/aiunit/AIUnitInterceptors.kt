/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - AIUnitInterceptors.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/4/25        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.aiunit

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.aiunit.toolkits.callback.AuthorizeCallback
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.notification.NoNetworkToastAction
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.ui.notification.NotificationAction.ToastAction.Duration
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.ConfirmInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.ToastInterceptor
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_AIUNIT
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.notification.EditorConfirmDialogAction
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 未登录时触发登录，弹窗拦截
 */
internal open class AIUnitLoginInterceptor(private val context: Context, private val detectName: String) : ConditionInterceptor() {

    override fun onCheckCondition(param: Bundle): Boolean {
        return ApiDmManager.getCloudSyncDM().isLogin()
    }

    override fun onConditionFailed(chain: IInterceptor.IChain<Bundle, Unit>) {
        val callback = ChainAuthorizeCallback()
        callback.setChain(chain)
        AISettings.startAuthorizeGuide(context, detectName, callback)
    }

    /**
     * 改成静态内部类，否则会泄露
     */
    private class ChainAuthorizeCallback : AuthorizeCallback {
        private var chain: WeakReference<IInterceptor.IChain<Bundle, Unit>>? = null

        fun setChain(interceptorChain: IInterceptor.IChain<Bundle, Unit>) {
            chain = WeakReference<IInterceptor.IChain<Bundle, Unit>>(interceptorChain)
        }

        override fun onFail(code: Int) {
            chain?.get()?.apply {
                // 账号那边的问题，回调onFail的时候也可能登录成功了，以[isLogin]的结果为准
                if (ApiDmManager.getCloudSyncDM().isLogin()) {
                    proceed(param)
                } else {
                    fail(param)
                }
            }
        }

        override fun onSuccess() {
            chain?.get()?.apply {
                proceed(param)
            }
        }
    }
}

/**
 * AIUnit外销上获取域名的拦截器，拦截策略如下：
 * 1. 如果未获取域名则拦截，否则不拦截
 * 2. 拦截后进行等待，每隔1s进行一次轮询，如果查到下载域名了，则结束，否则5s之后弹出“加载失败，请重试”的Toast，结束流程
 * @param context Context
 */
internal abstract class AIUnitURLWaitingInterceptor(
    private val context: Context,
    private val postNotificationAction: (NotificationAction) -> Unit
) : ConditionInterceptor() {
    protected abstract val state: Int
    protected abstract val detectName: String

    override fun onCheckCondition(param: Bundle): Boolean {
        val state = state
        val isIntercept = state == UnitState.STATE_UNAVAILABLE_URL_EMPTY
        GLog.d(TAG) { "[onCheckCondition] isIntercept:$isIntercept, state:$state" }
        return isIntercept.not()
    }

    override fun onConditionFailed(chain: IInterceptor.IChain<Bundle, Unit>) {
        if (NetworkMonitor.isNetworkValidated().not()) {
            postNotificationAction(NoNetworkToastAction())
            return
        }
        postNotificationAction(NotificationAction.LoadingDialogAction(isShowing = true))
        AppScope.launch(Dispatchers.IO) {
            val startTime = System.currentTimeMillis()
            for (i in 1..WAITING_COUNT) {
                delay(ONCE_DURATION_MS)
                if (isUrlAvailable()) {
                    withContext(Dispatchers.UI) {
                        GLog.w(
                            TAG,
                            "[onConditionFailed] url is available, index:$i, cost time:${GLog.getTime(startTime)}ms, do next action"
                        )
                        chain.proceed(chain.param)
                        postNotificationAction(NotificationAction.LoadingDialogAction(isShowing = false))
                    }
                    return@launch
                }
            }
            withContext(Dispatchers.UI) {
                GLog.w(TAG, "[onConditionFailed] timeout, toast error message")
                postNotificationAction(NotificationAction.LoadingDialogAction(isShowing = false))
                postNotificationAction(NotificationAction.ToastAction(R.string.verify_captcha_text_load_fail, duration = Duration.LONG))
                chain.fail(chain.param)
            }
        }
    }

    private fun isUrlAvailable(): Boolean {
        return AISettings.getDetectData(context, detectName).state != UnitState.STATE_UNAVAILABLE_URL_EMPTY
    }

    companion object {
        private const val TAG = "AIUnitURLWaitingInterceptor"
        private const val ONCE_DURATION_MS = 1000L
        private const val WAITING_COUNT = 6
    }
}

/**
 * AIUnit相关功能下线拦截器
 */
internal class AIUnitOfflineInterceptor(
    private val titleResId: Int,
    private val stateConfigId: String,
    private val postNotificationAction: (NotificationAction) -> Unit
) : ConfirmInterceptor(postNotificationAction) {
    override fun onCheckCondition(param: Bundle): Boolean {
        val state = ConfigAbilityWrapper.getInt(stateConfigId)
        GLog.d(TAG, LogFlag.DL, "[onCheckCondition] function is offline， state is $state")
        return state != UnitState.STATE_UNAVAILABLE_OFFLINE
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): EditorConfirmDialogAction {
        return EditorConfirmDialogAction(
            titleResId = titleResId,
            messageResId = R.string.picture3d_editor_text_ai_repair_offline_desc,
            negativeButtonTextResId = com.oplus.gallery.basebiz.R.string.common_ok,
            confirmCallback = confirmCallback
        )
    }

    companion object {
        private const val TAG = "AIUnitOfflineInterceptor"
    }
}

/**
 * AIUnit 能力暂时不可用
 */
internal class AIUnitAvailableInterceptor(
    private val titleResId: Int,
    private val stateConfigId: String,
    postNotificationAction: (NotificationAction) -> Unit
) : ToastInterceptor(postNotificationAction)  {
    override fun createToastAction(): NotificationAction.ToastAction {
        return NotificationAction.ToastAction(titleResId)
    }

    override fun onCheckCondition(param: Bundle): Boolean {
        val state = ConfigAbilityWrapper.getInt(stateConfigId)
        GLog.d(TAG, LogFlag.DL, "[onCheckCondition] function is unavailable， state is $state")
        return state != UnitState.STATE_UNAVAILABLE
    }

    companion object {
        private const val TAG = "AIUnitAvailableInterceptor"
    }
}

/**
 * AIUnit停用拦截
 */
internal class AIUnitDisabledInterceptor(
    private val context: Context,
    private val postNotificationAction: (NotificationAction) -> Unit
) : ConfirmInterceptor(postNotificationAction) {

    override fun onCheckCondition(param: Bundle): Boolean {
        return PackageInfoUtils.isPackageEnabled(context, PACKAGE_NAME_AIUNIT)
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): EditorConfirmDialogAction {
        val packageLabel = PackageInfoUtils.getPackageLabel(context, PACKAGE_NAME_AIUNIT)
        return EditorConfirmDialogAction(
            titleString = context.getString(
                BasebizR.string.base_dialog_app_forbidden_title,
                packageLabel
            ),
            messageString = context.getString(
                BasebizR.string.base_dialog_app_forbidden_detail_this_function,
                packageLabel
            ),
            cancelable = false,
            positiveButtonTextResId = BasebizR.string.base_dialog_app_setting,
            negativeButtonTextResId = BasebizR.string.base_cancel,
            confirmCallback = confirmCallback
        )
    }

    override fun onAgreed(activity: Activity) {
        val intent = Intent()
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.data = Uri.fromParts("package", PACKAGE_NAME_AIUNIT, null)
        context.startActivity(intent)
    }
}