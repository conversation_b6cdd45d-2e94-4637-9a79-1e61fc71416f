/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IAVFileInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/


package com.oplus.gallery.videoeditorpage.abilities.engine.base.interfaces;

public interface IAVFileInfo {

    int getVideoWidth();

    int getVideoHeidht();

    int getVideoRotation();

    long getDuration();

    int getAVFileType();

    /**
     * 获取码率
     *
     * @return 码率
     */
    long getBitRate();

    /**
     * 获取帧率
     *
     * @return 帧率
     */
    float getFps();
}
