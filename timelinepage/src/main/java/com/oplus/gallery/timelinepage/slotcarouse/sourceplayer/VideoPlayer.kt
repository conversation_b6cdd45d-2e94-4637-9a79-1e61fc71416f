/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : VideoPlayer.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/2/2 10:25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2023/2/2  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.timelinepage.slotcarouse.sourceplayer

import android.content.Context
import android.view.SurfaceView
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.player.MediaPlayerFactory
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.timelinepage.slotcarouse.CarouselSource
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 视频播放器，用于播放视频资源
 */
class VideoPlayer(
    private val context: Context,
    private val surfaceView: SurfaceView
) : AbsSourcePlayer() {

    private var playerJob: Job? = null

    override var looping: Boolean = false
        set(value) {
            field = value
            avPlayer?.setLooping(value)
        }

    private val eventListener = object : AVController.OnEventListener {
        override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
            GLog.d(TAG, "onError. $what, $extra, $details")
            curStartAction?.invoke(false)
            curStartAction = null
        }

        override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            GLog.d(TAG, "onInfo. $what, $extra, $details")
            if (what == AVController.INFO_PLAYING_INFO_READY) {
                avController.play(playAfterPrepare = true, needPrepare = true)
                curStartAction?.invoke(true)
                curStartAction = null
            }
        }

        override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
            GLog.d(TAG, "onPlaybackStateChanged. state=$state")
            when (state) {
                AVController.PlaybackState.PREPARED -> {
                    val playingInfo = avController.getCurrentPlayingInfo()
                    val aspectRatio = playingInfo.videoWidth / playingInfo.videoHeight.toFloat()
                    surfaceView.apply {
                        post {
                            if (aspectRatio > (width.toFloat() / height)) {
                                scaleX = aspectRatio * height / width
                                scaleY = 1f
                            } else {
                                scaleX = 1f
                                scaleY = 1 / aspectRatio * width / height
                            }
                        }
                    }
                }

                else -> {}
            }
        }
    }

    private var avPlayer: AVPlayer? = null
    private var curStartAction: ((success: Boolean) -> Unit)? = null

    override fun play(source: CarouselSource, startAction: ((success: Boolean) -> Unit)?) {
        GLog.d(TAG, "play. $source")
        stop()

        val localVideo = (source.mediaItem as? LocalVideo) ?: let {
            GLog.e(TAG, "play. Unsupported source. $source. skip...")
            startAction?.invoke(false)
            return
        }
        playerJob = AppScope.launch(Dispatchers.Main + CoroutineName(TAG)) {
            if (avPlayer == null) {
                withContext(Dispatchers.CPU + CoroutineName(TAG)) {
                    GLog.d(TAG, "play. first init avPlayer")
                    avPlayer = MediaPlayerFactory.createAVPlayer(context, localVideo)
                    avPlayer?.addOnEventListener(eventListener)
                }
            } else {
                val videoSource = MediaPlayerFactory.createVideoSource(context, localVideo)
                videoSource?.let { avPlayer?.setDataSource(it) }
            }
            curStartAction = startAction
            avPlayer?.apply {
                setSurfaceView(surfaceView)
                /*
                小窗模式下特效
                1，帧率降低：60帧以下不变，60帧以上降为60帧
                2，内存要求减少：只申请视频流播放内存
                */
                enableMiniView(true)
                setMute(true)
                setLooping(looping)
                preparePlayer()
            }
        }
    }

    override fun pause() {
        GLog.d(TAG, "pause video")
        avPlayer?.pause()
    }

    override fun resume() {
        GLog.d(TAG, "resume video")
        avPlayer?.play(playAfterPrepare = true, needPrepare = true)
    }

    override fun release() {
        GLog.d(TAG, "release")
        playerJob?.cancel()
        avPlayer?.release()
        avPlayer = null
    }

    override fun stop() {
        GLog.d(TAG, "stop")
        avPlayer?.apply {
            stop()
            clearSurfaceView(surfaceView)
            reset()
        }
        curStartAction = null
    }

    companion object {
        private const val TAG = "VideoPlayer"
    }
}