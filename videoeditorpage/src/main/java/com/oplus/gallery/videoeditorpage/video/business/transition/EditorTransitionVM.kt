/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorTransitionVM
 ** Description:编辑视频转场ViewModel
 ** Version: 1.0
 ** Date: 2025-07-09
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2025-07-09     1.0
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.transition

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener
import com.oplus.gallery.videoeditorpage.resource.data.TransitionResponseBean
import com.oplus.gallery.videoeditorpage.resource.listener.SimpleLoadDataListener
import com.oplus.gallery.videoeditorpage.resource.manager.TransitionResourceManager
import com.oplus.gallery.videoeditorpage.resource.room.bean.TransitionItem
import com.oplus.gallery.videoeditorpage.resource.room.entity.TransitionEntity
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.video.business.base.BaseResourceViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class EditorTransitionVM(application: Application) : BaseResourceViewModel<TransitionItem, TransitionResponseBean>(application) {


    override val currentResourceManager = TransitionResourceManager(application)
    // 列表加载监听
    val itemList: LiveData<MutableList<TransitionEntity>> get() = _itemList
    private val _itemList = MutableLiveData<MutableList<TransitionEntity>>(mutableListOf())
    // 单项变化监听
    val changedItem: LiveData<TransitionEntity> get() = _changedItem
    private val _changedItem = MutableLiveData<TransitionEntity>()

    private val onLoadFileListener by lazy {
        object : OnLoadFileListener<TransitionItem> {
            override fun onProgress(progress: Int, item: TransitionItem) {
                AppScope.launch(Dispatchers.UI) {
                    _changedItem.value = item.toEntity()
                }
            }

            override fun onFinish(item: TransitionItem) {
                AppScope.launch(Dispatchers.UI) {
                    _changedItem.value = item.toEntity()
                }
            }

            override fun onError(errCode: ErrorCode, item: TransitionItem?) {
                item?.let {
                    AppScope.launch(Dispatchers.UI) {
                        _changedItem.value = it.toEntity()
                    }
                }
            }

            override fun onCancel(item: TransitionItem) {
                AppScope.launch(Dispatchers.UI) {
                    _changedItem.value = item.toEntity()
                }
            }
        }
    }

    private val onLoadDataListener by lazy {
        object : SimpleLoadDataListener<TransitionItem>() {
            override fun onLoadRefresh(items: List<TransitionItem>) {
                _itemList.postValue(items.map { it.toEntity() }.toMutableList())
            }
        }
    }

    fun init(editorEngine: EditorEngine) {
        currentResourceManager.init(editorEngine)
    }

    fun loadData(requestNetwork: Boolean) {
        currentResourceManager.loadDefault(requestNetwork, onLoadDataListener, onLoadFileListener)
    }

    /**
     * 清除下载状态，将资源状态设置为空闲，并清空已下载和正在下载的映射表。
     */
    override fun clearDownloadStatus() {
        super.clearDownloadStatus()
        currentResourceManager.release()
    }

    companion object {
        private const val TAG = "EditorTransitionVM"
    }
}