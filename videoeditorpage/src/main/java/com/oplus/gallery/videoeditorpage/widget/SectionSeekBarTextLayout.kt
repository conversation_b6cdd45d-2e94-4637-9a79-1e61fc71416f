/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SectionSeekBarTextLayout
 ** Description: 分级滚动条下方，显示分级文字的容器
 ** Version: 1.0
 ** Date : 2025/04/21
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/04/21    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils.ONE
import com.oplus.gallery.foundation.util.math.MathUtils.TWO
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.common.ui.SuitableSizeG2TextView

/**
 * 分级滚动条下方，显示分级文字的容器
 */
class SectionSeekBarTextLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 需要显示的分级文本列表
     */
    var textList: List<String> = emptyList()
        set(value) {
            if (value == null) return
            field = value
            modifyChildViewNumber(value.size)
            updateTexts(value)
            needUpdateTextLayout = true
            updateTextLayout()
        }

    /**
     * 是否需要更新所占比重
     */
    private var needUpdateTextLayout: Boolean = false

    private val layoutInflater: LayoutInflater = LayoutInflater.from(context)

    /**
     * 最边上的节点的中心，到边界的偏移量
     */
    private val sidePointOffset = resources.getDimension(R.dimen.videoeditor_save_resolution_seek_bar_side_point_offset)

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        updateTextLayout()
    }

    /**
     * 更新文本
     */
    private fun updateTexts(list: List<String>) {
        val tempTextList = ArrayList<String>(list)
        for (index in 0 until childCount) {
            if (tempTextList.size <= 0) {
                GLog.e(TAG, LogFlag.DL) { "[updateTexts] textListSize: ${tempTextList.size}" }
                return
            }
            val view = getChildAt(index)
            if (view is SuitableSizeG2TextView) {
                view.text = tempTextList[0]
                tempTextList.removeAt(0)
            }
        }
    }

    /**
     * 更新文本位置
     */
    private fun updateTextLayout() {
        if (needUpdateTextLayout.not()) {
            GLog.w(TAG,LogFlag.DL) { "[updateTextLayout] no need update weight" }
            return
        }

        if (width <= 0) {
            GLog.w(TAG,LogFlag.DL) { "[updateTextLayout] width is not ready" }
            return
        }

        needUpdateTextLayout = false
        // 每个节点中心间距
        val pointSpace = (width - sidePointOffset * TWO) / childCount - ONE

        for (index in 0 until childCount) {
            val view = getChildAt(index)
            (view.layoutParams as? LayoutParams)?.let {
                val sideWeight = sidePointOffset + pointSpace / TWO
                val (weight, alignment) = when (index) {
                    0 -> Pair(sideWeight, View.TEXT_ALIGNMENT_VIEW_START)

                    (childCount - ONE) -> Pair(sideWeight, View.TEXT_ALIGNMENT_VIEW_END)

                    else -> Pair(pointSpace, View.TEXT_ALIGNMENT_CENTER)
                }
                GLog.d(TAG, LogFlag.DL) { "weight: $weight, alignment: $alignment" }
                it.width = 0
                it.weight = weight
                view.textAlignment = alignment
                it
            }.also { view.layoutParams = it }
        }
    }

    /**
     * 根据当前需要，添加或删除文本组件数量
     *
     * @param newCount 需要更新的文本节点数量
     */
    private fun modifyChildViewNumber(newCount: Int) {
        GLog.d(TAG, LogFlag.DL) { "[modifyChildViewNumber] childCount: $childCount, newCount: $newCount" }
        if (childCount > newCount) {
            removeTextItems(childCount - newCount)
        } else if (childCount < newCount) {
            addTextItems(newCount - childCount)
        }
    }

    /**
     * 删除n个文本选项
     *
     * @param count 移除的数量
     */
    private fun removeTextItems(count: Int) {
        if (count <= 0) return

        for (num in 0 until  count) {
            lastOrNull()?.also { removeView(it) }
        }
    }

    /**
     * 添加n个文本组件
     *
     * @param count 添加的数量
     */
    private fun addTextItems(count: Int) {
        if (count <= 0) return

        for (num in 0 until count) {
            layoutInflater.inflate(R.layout.videoeditor_save_resolution_textbar_text, this, true)
        }
    }

    internal companion object {
        private const val TAG = "SectionSeekBarTextLayout"
    }
}

/**
 * 获取最后一个子组件
 */
private fun LinearLayout.lastOrNull(): View? {
    if (childCount <= 0) return null

    return getChildAt(childCount - ONE)
}