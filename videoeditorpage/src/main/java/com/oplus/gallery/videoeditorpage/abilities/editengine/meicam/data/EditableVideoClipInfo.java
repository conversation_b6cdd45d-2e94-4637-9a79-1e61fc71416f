/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditableVideoClipInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class EditableVideoClipInfo extends EditableClipInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @SerializedName("width")
    protected int mWidth;
    @SerializedName("height")
    protected int mHeight;
    @SerializedName("timelineWidth")
    protected float mTimelineWidth;
    @SerializedName("timelineHeight")
    protected float mTimelineHeight;
    @SerializedName("scaleType")
    protected int mScaleType;
    @SerializedName("resolution")
    protected String mResolution;

    public String getResolution() {
        return mResolution;
    }

    public void setResolution(String resolution) {
        this.mResolution = resolution;
    }

    public float getTimelineWidth() {
        return mTimelineWidth;
    }

    public void setTimelineWidth(float timelineWidth) {
        mTimelineWidth = timelineWidth;
    }

    public float getTimelineHeight() {
        return mTimelineHeight;
    }

    public void setTimelineHeight(float timelineHeight) {
        mTimelineHeight = timelineHeight;
    }

    public int getWidth() {
        return mWidth;
    }

    public void setWidth(int width) {
        mWidth = width;
    }

    public int getHeight() {
        return mHeight;
    }

    public void setHeight(int height) {
        mHeight = height;
    }

    public int getScaleType() {
        return mScaleType;
    }

    public void setScaleType(int scaleType) {
        mScaleType = scaleType;
    }

    @Override
    public String toString() {
        return "EditableVideoClipInfo{"
                + "mWidth=" + mWidth
                + ", mHeight=" + mHeight
                + ", mScaleType=" + mScaleType
                + ", mTrackIndex=" + mTrackIndex
                + ", mClipIndex=" + mClipIndex
                + '}';
    }
}

