/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CustomPictureManagerFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/28        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.picturemanager

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.imageview.COUIRoundImageView
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.cardwidget.util.getCardType
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_CODE
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_MODE
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.selection.PathSelectionManager
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectType
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.ui.helper.enablePressFeedback
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.widgetpage.R
import com.oplus.gallery.widgetpage.WidgetConstants
import com.oplus.gallery.widgetpage.WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2
import com.oplus.gallery.widgetpage.WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2
import com.oplus.gallery.widgetpage.WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4
import com.oplus.gallery.widgetpage.WidgetConstants.Type.WIDGET_TYPE_CUSTOM_2X2
import com.oplus.gallery.widgetpage.WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X2
import com.oplus.gallery.widgetpage.WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X4
import com.oplus.gallery.widgetpage.track.WidgetTrackHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CustomPictureManagerFragment : BaseFragment() {
    private var parentPanelFragment: PanelFragment? = null
    private var widgetCode: String = TextUtil.EMPTY_STRING
    private var viewModel: CustomPictureManagerViewModel? = null
    private var widgetMode: Int = GalleryStore.WidgetSetColumns.MODE_DEFAULT
    private var customView: View? = null
    private var customSubtitleTextView: TextView? = null
    private var defaultTypePic: COUIRoundImageView? = null
    private var customCount = 0

    override fun getLayoutId() = R.layout.widget_mode_selection_dialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parentPanelFragment = parentFragment as PanelFragment

        /**
         * arguments:bundle会携带type&cardId&hostId信息给业务方
        const val CARD_TYPE = "cardType"  // 卡片type
        const val CARD_ID = "cardId"      // 卡片Id，默认为1，当重复添加卡片时，Id递增
        const val HOST_ID = "hostId"      // 0：添加在负一屏的卡片；1：添加在桌面的卡片
         */
        widgetCode = arguments?.getString(KEY_WIDGET_CODE) ?: TextUtil.EMPTY_STRING
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        initDialogContent(view)
        viewModel = ViewModelProvider(this).get(CustomPictureManagerViewModel::class.java)
        viewModel?.liveData?.observe(this) { viewData -> refresh(viewData) }
        viewModel?.setWidgetCode(widgetCode)
    }

    private fun initDialogContent(view: View) {
        parentPanelFragment?.apply {
            setDragViewVisibility(View.GONE)
            setIsShowInMaxHeight(false)
        }
        view.findViewById<COUIToolbar>(R.id.toolbar)?.apply {
            title = context?.getString(R.string.widget_edit_custom_pictures)
            isTitleCenterStyle = true
            inflateMenu(com.oplus.gallery.foundation.ui.R.menu.common_menu_cancel_save)
            menu.findItem(com.oplus.gallery.business_lib.R.id.cancel).apply {
                setOnMenuItemClickListener {
                    dismiss()
                    true
                }
            }
            menu.findItem(com.oplus.gallery.basebiz.R.id.save).apply {
                setOnMenuItemClickListener {
                    dismiss()
                    true
                }
            }
        }

        // 自定义卡片无数据时,点击显示的插图
        defaultTypePic = view.findViewById<COUIRoundImageView?>(R.id.default_type_pic).apply {
            GLog.d(TAG, "setImageResource widgetCode:$widgetCode")
            val resId = when (widgetCode.getCardType()) {
                WIDGET_TYPE_CHOICE_2x2 -> R.drawable.widget_image_choice_2x2
                WIDGET_TYPE_CHOICE_4x2 -> R.drawable.widget_image_choice_4x2
                WIDGET_TYPE_CHOICE_4x4 -> R.drawable.widget_image_choice_4x4
                WIDGET_TYPE_CUSTOM_2X2 -> R.drawable.widget_image_custom_2x2
                WIDGET_TYPE_CUSTOM_4X2 -> R.drawable.widget_image_custom_4x2
                WIDGET_TYPE_CUSTOM_4X4 -> R.drawable.widget_image_custom_4x4
                else -> R.drawable.widget_image_custom_2x2
            }
            setImageResource(resId)
        }
        view.findViewById<ImageView>(R.id.default_add_icon).apply {
            when (widgetCode.getCardType()) {
                WIDGET_TYPE_CHOICE_4x4,
                WIDGET_TYPE_CUSTOM_4X4 -> {
                    updateLayoutParams {
                        height = ContextGetter.context.resources.getDimensionPixelOffset(R.dimen.widget_custom_default_add_icon_width_small)
                        width = ContextGetter.context.resources.getDimensionPixelOffset(R.dimen.widget_custom_default_add_icon_height_small)
                    }
                }
            }
        }
        customView = view.findViewById<View?>(R.id.view_custom)?.apply {
            COUIPressFeedbackHelper(this, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK)
                .enablePressFeedback(this)
            setOnClickListener {
                if (DoubleClickUtils.isFastDoubleClick(CLICK_INTERVAL)) return@setOnClickListener
                if (customCount > 0) {
                    goToAlbumPage()
                    dismiss()
                } else {
                    goToSelectPage()
                }
            }
        }
        customSubtitleTextView = view.findViewById(R.id.tv_custom_subtitle)
    }

    private fun refresh(viewData: CustomPictureManagerViewData) {
        widgetMode = viewData.mode
        customCount = viewData.customCount
        customSubtitleTextView?.let {
            it.text = resources.getQuantityString(com.oplus.gallery.basebiz.R.plurals.common_item_count, customCount, customCount)
        }
    }

    private fun goToSelectPage() {
        val selectionData = PathSelectionManager.createSelectionData()
        val activity = activity ?: run {
            GLog.d(TAG, "goToSelectPage: activity is null.")
            return
        }
        Starter.DialogFragmentStarter<PanelDialog>(
            fm = activity.supportFragmentManager,
            bundle = SelectInputData(
                selectMulti = true,
                selectType = SelectType.WIDGET,
                countAtMost = WidgetConstants.DisplayList.MAX_CUSTOM_LIST_SIZE,
                countAtMostHintResId = R.string.widget_custom_tip_num_limit,
                selectionDataId = selectionData.selectionDataId
            ).createBundle().apply {
                putString(KEY_REQUEST_KEY, KEY_WIDGET_MODE_SELECTION)
            },
            postCard = PostCard(SELECTION_PANEL_DIALOG)
        ).start()?.apply {
            setFragmentResultListenerSafety(KEY_WIDGET_MODE_SELECTION) { _, bundle ->
                onSelectionFinished(bundle)
            }
        }
    }

    private fun goToAlbumPage() {
        Starter.ActivityStarter(
            startContext = this,
            bundle = Bundle().apply {
                putString(KEY_WIDGET_CODE, widgetCode)
                putInt(KEY_WIDGET_MODE, widgetMode)
            },
            postCard = PostCard(RouterConstants.RouterName.WIDGET_DISPLAY_LIST_ACTIVITY)
        ) {
            it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        }.start()
        activity?.overridePendingTransition(
            com.support.appcompat.R.anim.coui_push_up_enter_activitydialog,
            com.support.appcompat.R.anim.coui_zoom_fade_enter
        )
    }

    private fun onSelectionFinished(resultBundle: Bundle?) {
        resultBundle ?: return
        if (resultBundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
            GLog.w(TAG, "onSelectionFinished: cancel select items.")
            return
        }
        val pathStrArray = resultBundle.getStringArray(KEY_RESULT_DATA_LIST)
        if (pathStrArray.isNullOrEmpty()) {
            GLog.w(TAG, "onSelectionFinished: no selected items.")
            return
        }
        val paths = pathStrArray.map { Path.fromString(it) }
        activity?.also {
            FileProcessTaskBuilder(
                it,
                paths,
                object : FinishListener {
                    override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                        if (success) {
                            // 这里不能使用activity.lifecycleScope，因为设置完就会关闭页面
                            AppScope.launch(Dispatchers.IO) {
                                viewModel?.let {
                                    if (it.addToDisplayListByGalleryId(paths.map { it.suffix.toInt() }, widgetCode) > 0) {
                                        WidgetTrackHelper.trackSelectWidgetMode(widgetMode, GalleryStore.WidgetSetColumns.MODE_CUSTOM)
                                        it.updateWidgetMode(widgetCode, GalleryStore.WidgetSetColumns.MODE_CUSTOM)
                                    }
                                }
                                // 一定要先执行完上面才能去dismiss，否则会因为弱引用可能执行不到，造成下载失败
                                withContext(Dispatchers.UI) {
                                    dismiss()
                                }
                            }
                        } else {
                            GLog.e(TAG, "onFinished: failed, errCode=$errCode, errMsg=$errMsg")
                        }
                    }
                },
                scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_MULTI_SELECT
            )
                .addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
                .build()?.let {
                    it.execute()
                    it.autoCancelWhenOnDestroy(this.lifecycle)
                }
        }
    }

    private fun dismiss() {
        parentPanelFragment?.dismiss()
    }

    companion object {
        const val TAG = "WidgetModeSelectionFragment"
        const val KEY_WIDGET_MODE_SELECTION = "widgetModeSelection.requestKey"
        const val CLICK_INTERVAL = 500
    }
}