/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoThumbnailDecoderUtilsTest.kt
 ** Description:VideoThumbnailDecoderUtils的测试类
 ** Version: 1.0
 ** Date : 2022/12/15
 ** Author: xiewu<PERSON>e@Apps.Gallery3D
 ** TAG: VideoThumbnailDecoderUtilsTest
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON><PERSON>@Apps.Gallery3D      2022/12/15    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.standard_lib.codec.videoframe

import org.junit.Test

class VideoThumbnailDecoderUtilsTest {

    @Test
    fun should_return_meicam_thumbnail_decode_when_input_tbl() {
        // given
        val decoderConfig = DecoderConfig(DecoderVendor.MEICAM)
        // when
        val result = VideoThumbnailDecoderUtils.getVideoThumbnailDecoder(decoderConfig)
        // then
        assert(result is MeicamVideoThumbnailDecoder)
    }

    @Test
    fun should_return_media_thumbnail_decode_when_input_tbl() {
        // given
        val decoderConfig = DecoderConfig(DecoderVendor.SYSTEM)
        // when
        val result = VideoThumbnailDecoderUtils.getVideoThumbnailDecoder(decoderConfig)
        // then
        assert(result is PlatformVideoThumbnailDecoder)
    }
}