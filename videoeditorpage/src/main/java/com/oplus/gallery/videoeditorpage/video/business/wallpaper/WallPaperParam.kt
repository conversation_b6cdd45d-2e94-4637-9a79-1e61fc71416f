/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WallPaperManager.kt
 ** Description: for Wallpaper.
 ** Version: 1.0
 ** Date : 2025/4/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2025/4/8    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.content.Intent
import android.os.Bundle
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FINISH_VIDEO_WALLPAPER_CUT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_KEEP_HDR
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_MAX_DURATION
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_MIN_DURATION
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_CODEC_FORMAT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_CONTAINER_FORMAT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_MAX_BIT_RATE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_MAX_FRAME_RATE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_MAX_HEIGHT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_MAX_SIDE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_VIDEO_TRIM_TARGET_MAX_WIDTH
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallPaperParam.Companion.DEFAULT_MAX_BIT_RATE
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallPaperParam.Companion.DEFAULT_MAX_FRAME_RATE
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallPaperParam.Companion.DEFAULT_VIDEO_TRIM_MAX_DURATION
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallPaperParam.Companion.DEFAULT_VIDEO_TRIM_MIN_DURATION
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallPaperParam.Companion.SIZE_M
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallpaperHelper.queryParamFromWallpaper

/**
 * 视频壁纸剪辑参数类
 */
class WallPaperParam {

    /**
     * 标识是否需要主动跳转壁纸
     * true 通过startActivity主动跳转
     * false 通过setResult返回生成的视频uri
     */
    var isFinishVideoWallPaperCut: Boolean = false

    /**
     * 视频剪辑最小时长 单位ms
     */
    var videoTrimMinDuration: Long = 0L

    /**
     * 视频剪辑最大时长 单位ms
     */
    var videoTrimMaxDuration: Long = 0L

    /**
     * 生成视频的目标宽度
     */
    var videoTrimTargetMaxWidth: Int = Int.MIN_VALUE

    /**
     * 生成视频的目标高度
     */
    var videoTrimTargetMaxHeight: Int = Int.MIN_VALUE

    /**
     * 生成视频的目标边长
     * 高>宽 为目标高度
     * 宽>高 为目标宽度
     */
    var videoTrimTargetMaxLength: Int = Int.MIN_VALUE

    /**
     * 生成视频目标帧率
     */
    var videoTrimTargetMaxFrameRate: Int = DEFAULT_MAX_FRAME_RATE

    /**
     * 生成视频目标码率
     */
    var videoTrimTargetMaxBitRate: Int = DEFAULT_MAX_BIT_RATE * SIZE_M

    /**
     * 生成视频目标压缩格式，如video/mp4
     */
    var videoTrimTargetContainerFormat: String? = null

    /**
     * 视频编码格式
     * 美摄目前支持
     * "hevc"(h.265)
     * "vp8"(audio格式必须是vorbis，文件格式必须是Webm)
     * default(h.264)
     */
    var videoTrimTargetCodecFormat: String? = null

    /**
     * 生成视频时是否保持HDR
     */
    var videoTrimKeepHdr: Boolean = false

    fun dump() {
        GLog.d(TAG, LogFlag.DL) {
            "Wallpaper param, finish_VideoWallPaperCut:$isFinishVideoWallPaperCut," +
                    "video-trim-min-duration:$videoTrimMinDuration," +
                    "video-trim-max-duration:$videoTrimMaxDuration," +
                    "video-trim-target-max-width:$videoTrimTargetMaxWidth," +
                    "video-trim-target-max-frame-rate:$videoTrimTargetMaxFrameRate," +
                    "video-trim-target-max-bit-rate:$videoTrimTargetMaxBitRate," +
                    "video-trim-target-container-format:$videoTrimTargetContainerFormat," +
                    "video-trim-target-codec-format:$videoTrimTargetCodecFormat," +
                    "video-trim-keep-hdr:$videoTrimKeepHdr"
        }
    }

    /**
     * 是否需要setResult返回给调用页
     */
    fun isNeedToSetResult(): Boolean {
        return isFinishVideoWallPaperCut
    }

    companion object {
        const val TAG = "WallPaperParam"

        /**
         * 默认视频剪辑最小时长 单位ms
         */
        const val DEFAULT_VIDEO_TRIM_MIN_DURATION = 1000L

        /**
         * 默认视频剪辑最大时长 单位ms
         */
        const val DEFAULT_VIDEO_TRIM_MAX_DURATION = 6000L

        /**
         * 默认视频剪辑最大帧率
         */
        const val DEFAULT_MAX_FRAME_RATE: Int = 30

        /**
         * 默认视频剪辑最大码率
         */
        const val DEFAULT_MAX_BIT_RATE: Int = 20

        /**
         * 码率单位换算base
         */
        const val SIZE_M: Int = 1000000

        /**
         * 从壁纸应用去剪辑和设置壁纸视频
         */
        const val VALUE_INVOKER_WALLPAPER_PAGE = "wallpaper"

        /**
         * 从相册-大图-更多-设为壁纸，去剪辑和设置壁纸视频
         */
        const val VALUE_INVOKER_GALLEY_WALLPAPER_PAGE = "gallery_wallpaper"

        /**
         * 当前壁纸参数
         */
        var curInstance: WallPaperParam? = null

        private var invoker: String? = null

        /**
         * 根据intent和invoker获取壁纸参数
         */
        fun newInstance(intent: Intent, invoker: String?) {
            curInstance = when (invoker) {
                VALUE_INVOKER_WALLPAPER_PAGE -> intent.toWallpaperParam()
                VALUE_INVOKER_GALLEY_WALLPAPER_PAGE -> queryParamFromWallpaper().toWallPaperParam()
                else -> null
            }
        }

        /**
         * 是否是壁纸业务
         */
        fun isWallpaperBusiness(invoker: String?): Boolean {
            Companion.invoker = invoker
            return when (invoker) {
                VALUE_INVOKER_WALLPAPER_PAGE,
                VALUE_INVOKER_GALLEY_WALLPAPER_PAGE -> true

                else -> false
            }
        }

        fun isFromWallpaperApp(): Boolean {
            return invoker == VALUE_INVOKER_WALLPAPER_PAGE
        }
    }
}

fun Intent.toWallpaperParam(): WallPaperParam = WallPaperParam().apply {
    isFinishVideoWallPaperCut =
        IntentUtils.getBooleanExtra(this@toWallpaperParam, KEY_FINISH_VIDEO_WALLPAPER_CUT, false)
    videoTrimMinDuration = IntentUtils.getLongExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_MIN_DURATION,
        DEFAULT_VIDEO_TRIM_MIN_DURATION
    )
    videoTrimMaxDuration = IntentUtils.getLongExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_MAX_DURATION,
        DEFAULT_VIDEO_TRIM_MAX_DURATION
    )
    videoTrimTargetMaxWidth = IntentUtils.getIntExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_TARGET_MAX_WIDTH,
        Int.MIN_VALUE
    )
    videoTrimTargetMaxHeight = IntentUtils.getIntExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_TARGET_MAX_HEIGHT,
        Int.MIN_VALUE
    )
    videoTrimTargetMaxLength = IntentUtils.getIntExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_TARGET_MAX_SIDE,
        Int.MIN_VALUE
    )
    videoTrimTargetMaxFrameRate = IntentUtils.getIntExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_TARGET_MAX_FRAME_RATE,
        DEFAULT_MAX_FRAME_RATE
    )
    videoTrimTargetMaxBitRate = IntentUtils.getIntExtra(
        this@toWallpaperParam,
        KEY_VIDEO_TRIM_TARGET_MAX_BIT_RATE,
        DEFAULT_MAX_BIT_RATE
    ) * SIZE_M
    videoTrimTargetContainerFormat =
        IntentUtils.getStringExtra(this@toWallpaperParam, KEY_VIDEO_TRIM_TARGET_CONTAINER_FORMAT)
    videoTrimTargetCodecFormat =
        IntentUtils.getStringExtra(this@toWallpaperParam, KEY_VIDEO_TRIM_TARGET_CODEC_FORMAT)
    videoTrimKeepHdr =
        IntentUtils.getBooleanExtra(this@toWallpaperParam, KEY_VIDEO_TRIM_KEEP_HDR, true)

    dump()
}

fun Bundle.toWallPaperParam(): WallPaperParam = WallPaperParam().apply {
    isFinishVideoWallPaperCut = getBoolean(KEY_FINISH_VIDEO_WALLPAPER_CUT, false)
    videoTrimMinDuration = getLong(KEY_VIDEO_TRIM_MIN_DURATION, DEFAULT_VIDEO_TRIM_MIN_DURATION)
    videoTrimMaxDuration = getLong(KEY_VIDEO_TRIM_MAX_DURATION, DEFAULT_VIDEO_TRIM_MAX_DURATION)
    videoTrimTargetMaxWidth = getInt(KEY_VIDEO_TRIM_TARGET_MAX_WIDTH, Int.MIN_VALUE)
    videoTrimTargetMaxHeight = getInt(KEY_VIDEO_TRIM_TARGET_MAX_HEIGHT, Int.MIN_VALUE)
    videoTrimTargetMaxLength = getInt(KEY_VIDEO_TRIM_TARGET_MAX_SIDE, Int.MIN_VALUE)
    videoTrimTargetMaxFrameRate =
        getInt(KEY_VIDEO_TRIM_TARGET_MAX_FRAME_RATE, DEFAULT_MAX_FRAME_RATE)
    videoTrimTargetMaxBitRate =
        getInt(KEY_VIDEO_TRIM_TARGET_MAX_BIT_RATE, DEFAULT_MAX_BIT_RATE) * SIZE_M
    videoTrimTargetContainerFormat = getString(KEY_VIDEO_TRIM_TARGET_CONTAINER_FORMAT)
    videoTrimTargetCodecFormat = getString(KEY_VIDEO_TRIM_TARGET_CODEC_FORMAT)
    videoTrimKeepHdr = getBoolean(KEY_VIDEO_TRIM_KEEP_HDR, true)

    dump()
}
