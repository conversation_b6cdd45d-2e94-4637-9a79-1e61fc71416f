/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : BaseSelector.kt
 ** Description : 字幕面板组件基类
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import android.view.ViewGroup
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.CaptionEffectsChangedListener
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType

/**
 * 字幕面板组件基类
 */
internal abstract class BaseSelector(
    /**
     * 页面类型：样式模板、字体、文本、描边、阴影、背景
     */
    val pageType: PageType
) {

    /**
     * 组件根视图容器
     */
   var container: ViewGroup? = null
       set(value) {
           if (field != value) {
               field = value
               initViews()
               initData()
           }
       }

    /**
     * 字幕效果调整变化监听器
     */
    var captionEffectsChangedListener: CaptionEffectsChangedListener? = null

    /**
     * 初始化view
     */
    abstract fun initViews()

    /**
     * 数据初始化
     */
    abstract fun initData()

    /**
     * 资源销毁
     */
    open fun destroy() {
        captionEffectsChangedListener = null
    }

    companion object {
        private const val TAG = "BaseSelector"
    }
}
