/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ResourceUtils
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.util;

import android.content.Context;
import android.text.TextUtils;

import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.utlis.TextUtil;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.resource.data.File;
import com.oplus.gallery.videoeditorpage.utlis.FileUtil;

import java.util.Locale;

public class ResourceUtils {
    private static final String TAG = "ResourceUtils";

    public static final String RESOURCE_ROOT_DIRECTORY = "resource";
    public static final String VERSION_NAME = "net_version_name";
    public static final String DEFAULT_VERSION_NAME = "none";
    public static final String TRANSITION_SUFFIX = "videotransition";
    public static final String LANGUAGE_CHINESE = "zh";
    public static final String LANGUAGE_ENGLISH = "en";
    public static final String LANGUAGE_CHINESE_SIMP = "zh_cn";
    public static final String LANGUAGE_CHINESE_TRAD = "zh_tw";
    public static final String COUNTRY_CHINA = "CN";
    public static final int REQUEST_INTERVAL_TIME = 30 * 1000;//todo
    public static final int NETWORK_RESOURCE = 0;
    public static final int BUILTIN_RESOURCE = 1;
    public static final int NONE_RESOURCE = 2;
    public static final int LOCAL_RESOURCE = 3;
    public static final int LOCAL_TYPE_IMAGE = 1;
    public static final int LOCAL_TYPE_VIDEO = 3;
    private static String sCurrentLanguage = "zh_cn";

    public static boolean isCurrentLanguageChinese() {
        String language = Locale.getDefault().getLanguage();
        if (LANGUAGE_CHINESE.equals(language) || LANGUAGE_CHINESE_TRAD.equals(language)
                || LANGUAGE_CHINESE_SIMP.equals(language)) {
            return true;
        }
        return false;
    }

    public static String getDownloadPath(String resource) {
        Context context = ContextGetter.context;
        if (context == null) {
            GLog.e(TAG, "getDownloadPath context is null, resource = " + resource);
            return null;
        }
        String filesDir = FileUtil.getFilesDir();
        if (TextUtils.isEmpty(filesDir)) {
            GLog.e(TAG, "getDownloadPath filesDir is null, resource = " + resource);
            return null;
        }
        StringBuilder path = new StringBuilder();
        path.append(filesDir);
        path.append(File.SEPARATOR);
        path.append(RESOURCE_ROOT_DIRECTORY);
        path.append(File.SEPARATOR);
        path.append(resource);
        return path.toString();
    }

    public static String splitStringToGetId(String path) {
        String id = "";
        if (path.endsWith(TRANSITION_SUFFIX)) {
            String firstStr = path.substring(path.lastIndexOf("/") + 1);
            id = firstStr.substring(0, firstStr.indexOf('.'));
        }
        return id;
    }

    public static String getCurrentLanguage() {
        return sCurrentLanguage;
    }

    public static boolean isCurrentLanguageSimp() {
        return TextUtils.equals(ResourceUtils.getCurrentLanguage(), ResourceUtils.LANGUAGE_CHINESE_SIMP);
    }

    public static boolean isCurrentLanguageTrad() {
        return TextUtils.equals(ResourceUtils.getCurrentLanguage(), ResourceUtils.LANGUAGE_CHINESE_TRAD);
    }


    public static void setCurrentLanguage(String sCurrentLanguage) {
        ResourceUtils.sCurrentLanguage = sCurrentLanguage;
    }

    public static void setCurrentLanguage() {
        Locale locale = Locale.getDefault();
        if (locale != null) {
            String language = locale.getLanguage();
            String country = locale.getCountry();
            if (!TextUtil.isEmpty(language)) {
                if (language.equals(LANGUAGE_CHINESE)) {
                    if (!TextUtil.isEmpty(country)) {
                        if (country.equals(COUNTRY_CHINA)) {
                            ResourceUtils.setCurrentLanguage(LANGUAGE_CHINESE_SIMP);
                        } else {
                            ResourceUtils.setCurrentLanguage(LANGUAGE_CHINESE_TRAD);
                        }
                    }
                } else {
                    ResourceUtils.setCurrentLanguage(LANGUAGE_ENGLISH);
                }
            }
        } else {
            ResourceUtils.setCurrentLanguage(LANGUAGE_ENGLISH);
        }

    }

    public static boolean judgeStringHasSuffix(String fileName, String suffix) {
        if ((!TextUtil.isEmpty(fileName)) && (!TextUtil.isEmpty(suffix))) {
            if (fileName.endsWith(suffix)) {
                return true;
            }
        }
        return false;
    }
}
