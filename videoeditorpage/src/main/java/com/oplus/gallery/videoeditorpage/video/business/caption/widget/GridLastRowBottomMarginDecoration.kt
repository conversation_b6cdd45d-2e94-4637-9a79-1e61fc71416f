/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : GridLastRowBottomMarginDecoration.kt
 ** Description : 最后一行item添加底部间距
 ** Version     : 1.0
 ** Date        : 2025/7/14 21:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  GridLastRowBottomMarginDecoration     2025/7/14  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView

/**
 * 为 GridLayoutManager 的最后一行 item 添加底部 margin
 */
class GridLastRowBottomMarginDecoration(private val bottomMargin: Int) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)

        val layoutManager = parent.layoutManager as? GridLayoutManager ?: return
        val spanCount = layoutManager.spanCount
        val position = parent.getChildAdapterPosition(view)
        val itemCount = state.itemCount

        // 判断是否是最后一行的 item
        if (isLastRow(position, spanCount, itemCount)) {
            outRect.bottom = bottomMargin
        }
    }

    /**
     * 判断当前 position 是否属于最后一行
     * @param position 当前 item 的 position
     * @param spanCount 每行 item 的数量
     * @param itemCount item 总数
     * @return true: 属于最后一行
     */
    private fun isLastRow(position: Int, spanCount: Int, itemCount: Int): Boolean {
        val rows = itemCount / spanCount + if (itemCount % spanCount > 0) 1 else 0
        val row = (position + 1) / spanCount + if ((position + 1) % spanCount > 0) 1 else 0
        return row == rows
    }
}