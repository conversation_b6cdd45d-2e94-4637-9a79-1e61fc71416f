/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : IntercepableBaseView
 ** Description : IntercepableBaseView
 ** Version     : 1.0
 ** Date        : 2025/8/1 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/8/1  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.widget.base;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

public abstract class IntercepableBaseView extends View {
    private boolean mClickDoNothing = false;

    public IntercepableBaseView(Context context) {
        super(context);
    }

    public IntercepableBaseView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public IntercepableBaseView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public IntercepableBaseView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void setClickDoNothing(boolean mClickDoNothing) {
        this.mClickDoNothing = mClickDoNothing;
    }

    public boolean onTouchEventImpl(MotionEvent event) {
        return mClickDoNothing;
    }
}
