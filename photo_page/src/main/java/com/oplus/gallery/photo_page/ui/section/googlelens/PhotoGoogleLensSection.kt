/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PhotoGoogleLensSection.kt
 * Description: 大图页面 - GoogleLens 展示页面切片
 * Version: 1.0
 * Date: 2025/7/15
 * Author: yanghaoge@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * yanghaoge@Apps.Gallery3D 2022/5/6     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 **************************************************************************************************/
package com.oplus.gallery.photo_page.ui.section.googlelens

import android.content.res.ColorStateList
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.RippleDrawable
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import androidx.core.graphics.ColorUtils
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.textview.COUITextView
import com.coui.appcompat.uiutil.ShadowUtils
import com.oplus.gallery.basebiz.widget.WindowFocusAwareLinearLayout
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.DynamicVisibility
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.isVisible
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.FuncPresent
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoAnimationConfig
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncDetectInfo
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.Status
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.defaultFabCenterToTargetDistanceValue
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.thumbLineHeightValue
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.tools.addShadowLV2
import com.oplus.gallery.tools.setCircularOutline
import kotlinx.coroutines.flow.map

/**
 * 图页面 - GoogleLens 展示页面切片
 * @param sectionPage 被此页面切片依附的主页面
 */
internal class PhotoGoogleLensSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
) : AbstractPhotoSection(sectionPage) {

    /**
     * 是否启用缩图轴
     */
    private val isThumbLineEnabled: Boolean
        get() = (sectionPage.pageViewModel.inputArguments.features.value?.isThumbLineEnabled == true)

    /**
     * 缩图轴是否显示
     */
    private val isThumbLineVisible: Boolean
        get() = sectionPage.pageViewModel.pageManagement.isThumbLineVisible.value

    /**
     * 是否支持ThumbnailSeekBar（显示视频多帧背景的seekbar）
     */
    private val isPlaybackPreviewEnabled: Boolean
        get() = (sectionPage.pageViewModel.inputArguments.features.value?.isPlaybackPreviewEnabled == true)

    /**
     * 缩图轴高度
     */
    private val thumbLineHeight: Int
        get() = sectionPage.pageViewModel.pageManagement.thumbLineHeightValue

    /**
     * 默认底部margin
     */
    private val defaultBottomMargin: Int
        get() = sectionPage.pageViewModel.pageManagement.defaultFabCenterToTargetDistanceValue - (googleLensViewHeight / 2)

    /**
     * GoogleLens 功能视图
     */
    private var googleLensView: View? = null

    /**
     * GoogleLens 功能视图中的控件Icon ImageView
     */
    private var googleLensImageView: ImageView? = null

    /**
     * GoogleLens 功能视图中的控件TextView
     */
    private var googleLensTextView: COUITextView? = null

    /**
     * GoogleLens 功能视图中的控件触摸效果
     */
    private var googleLensWLView: WindowFocusAwareLinearLayout? = null

    /**
     * GoogleLens 默认高度
     */
    private val googleLensViewDefaultHeight by lazy { googleLensView?.resources?.getDimensionPixelSize(R.dimen.photopage_lens_height) }

    /**
     * GoogleLens 功能视图的高度
     */
    private val googleLensViewHeight: Int
        get() = googleLensView?.run {
            height.takeIf { it != 0 } ?: googleLensViewDefaultHeight
        } ?: 0

    /**
     * GoogleLens功能
     */
    private val googleLensFuncPresent: FuncPresent = FuncPresent.GoogleLens(sectionPage.pageViewModel)

    /**
     * 当前的智能推荐信息
     */
    private var currentFuncDetectInfo: FuncDetectInfo? = null

    /**
     * 当前正在执行的动画
     */
    private var currentAnimation: COUISpringAnimation? = null

    override fun onViewCreated(view: View) {
        super.onViewCreated(view)
        subscribeLiveDataFromViewModel()
        view.findViewById<ViewStub>(R.id.vs_google_lens).inflate()?.let {
            googleLensView = it
            googleLensImageView = it.findViewById<ImageView>(R.id.iv_lens)
            googleLensTextView = it.findViewById<COUITextView>(R.id.tv_lens)
            googleLensWLView = it.findViewById<WindowFocusAwareLinearLayout>(R.id.wl_lens)
            updateViewVisibility(DynamicVisibility.HIDE_IMMEDIATELY)
            it.setOnClickListener {
                if (DoubleClickUtils.isFastDoubleClick()) {
                    return@setOnClickListener
                }
                googleLensFuncPresent.clickFunc.invoke()
            }
            applyFloatingButtonTheme()
        }
    }

    /**
     * 屏幕状态发生改变
     */
    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        if (config.windowWidth.isChanged()) {
            googleLensView?.post {
                googleLensView?.let {
                    adaptScreen(it)
                }
            }
        }
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        currentAnimation?.cancel()
        currentAnimation = null
        googleLensView?.handler?.removeCallbacksAndMessages(null)
        googleLensView = null
    }

    private fun adaptScreen(compareView: View) {
        val context = compareView.context
        val marginEnd = if (ResourceUtils.isRTL(compareView.context)) {
            sectionPage.pageInstance.leftNaviBarHeight()
        } else {
            sectionPage.pageInstance.rightNaviBarHeight()
        } + compareView.context.resources.getDimensionPixelSize(R.dimen.photopage_text_ocr_margin_end)

        val isVideo = sectionPage.pageViewModel.dataLoading.focusItemViewData?.isVideo == true
        val extBottomPadding = when {
            // 缩图轴高度
            isThumbLineEnabled && isThumbLineVisible -> thumbLineHeight
            // 视频播放时的ThumbnailSeekBar高度
            isVideo && isPlaybackPreviewEnabled -> context.resources.getDimensionPixelSize(R.dimen.photopage_playback_thumbnail_seekbar_height)
            // 视频播放时的默认SeekBar高度
            isVideo -> context.resources.getDimensionPixelSize(R.dimen.photopage_playback_normal_seekbar_height)
            else -> 0
        }

        // 避免导航栏和其他控件挡住推荐功能区
        compareView.updateMarginRelative(
            end = marginEnd,
            bottom = defaultBottomMargin + extBottomPadding
        )
        GLog.d(TAG, LogFlag.DL) {
            "[adaptScreen], end:$marginEnd, " +
                    "defaultBottomMargin:$defaultBottomMargin, extBottomPadding:$extBottomPadding"
        }
    }

    private fun subscribeLiveDataFromViewModel() {

        sectionPage.pageViewModel.pageManagement.thumbLineHeight.observe(this) {
            googleLensView?.let {
                adaptScreen(it)
            }
        }

        sectionPage.pageViewModel.photoFunc.funcDetectInfo.observe(this) {
            onFuncDetectInfoChanged(it)
        }

        sectionPage.pageViewModel.photoFunc.funcDetectInfo.value?.let {
            onFuncDetectInfoChanged(it)
        }

        sectionPage.pageViewModel.photoFunc.visible.observe(this) {
            onFuncDetectInfoChanged(forceUpdate = true)
        }

        sectionPage.pageViewModel.details.transitionPanelEffectState.observe(this) { panelEffectState ->
            val isInDetailsMode = sectionPage.pageViewModel.details.isDetailModeByTransition(panelEffectState)
            googleLensView?.apply {
                if (isInDetailsMode) {
                    updateViewVisibility(DynamicVisibility.HIDE)
                } else {
                    onFuncDetectInfoChanged(forceUpdate = true)
                }
            }
        }

        sectionPage.pageViewModel.pageManagement.pageTheme.map { it.floatingButtonTheme }.collect(this) {
            applyFloatingButtonTheme()
        }

        sectionPage.pageViewModel.pageManagement.isThumbLineVisible.collect(this) {
            // 进入详情模式时无需进行变更
            if (sectionPage.pageViewModel.details.isDetailModeByTransition()) return@collect
            googleLensView?.let(::adaptScreen)
        }

        sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.observe(this) {
            if (it) {
                updateViewVisibility(DynamicVisibility.HIDE)
            }
        }
    }

    private fun onFuncDetectInfoChanged(info: FuncDetectInfo? = currentFuncDetectInfo, forceUpdate: Boolean = false) {
        if (!forceUpdate) {
            if ((info?.status != Status.END) || info == currentFuncDetectInfo) {
                GLog.d(TAG, LogFlag.DL) { "onFuncDetectInfoChanged status not changed by info.status = ${info?.status}" }
                return
            }
            currentFuncDetectInfo = info
        }
        val supportGoogleLens = info?.funcItems?.find { it is FuncItem.GoogleLens } != null
        if (supportGoogleLens
            && (sectionPage.pageViewModel.details.isDetailModeByTransition().not())
            && (sectionPage.pageViewModel.photoFunc.visible.value == true)
        ) {
            updateViewVisibility(DynamicVisibility.SHOW)
        } else {
            updateViewVisibility(
                if (supportGoogleLens
                    && (sectionPage.pageViewModel.details.isDetailModeByTransition().not())
                ) DynamicVisibility.HIDE else DynamicVisibility.HIDE_IMMEDIATELY
            )
        }
    }

    private fun updateViewVisibility(dynamicVisibility: DynamicVisibility) {
        GLog.d(TAG, LogFlag.DL) {
            "updateViewVisibility: dynamicVisibility = ${dynamicVisibility.name}"
        }
        var internalDynamicVisibility = dynamicVisibility
        val shouldAnimateWhenRemoved = sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.value == true
        currentAnimation?.cancel()
        if (internalDynamicVisibility.isVisible() && isFloatingWindowMode().not()) {
            if (internalDynamicVisibility == DynamicVisibility.SHOW_IMMEDIATELY && !shouldAnimateWhenRemoved) {
                showGoogleLensView()
                googleLensView?.alpha = ALPHA_SHOW
            } else {
                val delayTime = if (shouldAnimateWhenRemoved) {
                    REMOVED_DELAY_ALPHA_SHOW_TIME
                } else {
                    0L
                }
                googleLensView?.apply {
                    postDelayed({
                        showGoogleLensView()
                        fadeInOrOutAnimation(true, this)
                    }, delayTime)
                }
            }
        } else {
            //执行淡出动画之前功能区就不可见 ,不支持消失动画
            if (googleLensView?.isShown == false) {
                internalDynamicVisibility = DynamicVisibility.HIDE_IMMEDIATELY
                GLog.d(TAG, LogFlag.DL) {
                    "updateViewVisibility: googleLensView isShown = false, unsupported HIDE animation."
                }
            }
            if (internalDynamicVisibility == DynamicVisibility.HIDE_IMMEDIATELY && !shouldAnimateWhenRemoved) {
                hideGoogleLensView()
                googleLensView?.alpha = ALPHA_HIDE
            } else {
                googleLensView?.let {
                    fadeInOrOutAnimation(false, it)
                }
            }
        }
    }

    /**
     * 淡入淡出动画
     * @param show 是否展示 true:淡入展示，false:淡出隐藏
     * @param view 要做动画的View
     */
    private fun fadeInOrOutAnimation(show: Boolean, view: View) {
        val targetAlpha = if (show) ALPHA_SHOW else ALPHA_HIDE
        val response = if (show) ALPHA_FADE_IN_RESPONSE else ALPHA_FADE_OUT_RESPONSE
        currentAnimation = COUISpringAnimation(view, COUIDynamicAnimation.ALPHA).apply {
            setStartVelocity(ALPHA_ANIM_VELOCITY)
            val alphaSpringForce = COUISpringForce().setBounce(ALPHA_ANIM_BOUNCE).apply {
                setResponse(response)
                finalPosition = targetAlpha
            }
            spring = alphaSpringForce
            addEndListener { _, _, _, _ ->
                if (show.not()) {
                    hideGoogleLensView()
                }
            }
            start()
        }
    }

    private fun showGoogleLensView() {
        googleLensView?.visibility = View.VISIBLE
    }

    private fun hideGoogleLensView() {
        googleLensView?.visibility = View.INVISIBLE
    }

    private fun isFloatingWindowMode(): Boolean = sectionPage.pageInstance.isFloatingWindowMode()

    private fun applyFloatingButtonTheme() {
        val theme = sectionPage.pageViewModel.pageManagement.pageTheme.value.floatingButtonTheme
        googleLensView?.apply {
            val pressedColorStateList = ColorStateList.valueOf(context.getColor(R.color.photopage_intelli_func_item_bg_pressed))
            val contentColor = ColorUtils.setAlphaComponent(context.getColor(theme.contentColor), AppConstants.Number.NUMBER_0xFF)
            val backgroundColor = context.getColor(theme.background)
            // 添加描边效果
            val borderColor = context.getColor(theme.borderColor)
            val shapeDrawable = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                setColor(backgroundColor)
                setStroke(1, borderColor)
                val cornerRadius = googleLensViewDefaultHeight?.div(2f)
                cornerRadius?.let { setCornerRadius(it) }
            }
            background = shapeDrawable
            ShadowUtils.clearShadow(this)
            val shadow = context.getColor(theme.shadowColor)
            setCircularOutline()
            addShadowLV2(shadow)

            googleLensImageView?.imageTintList = ColorStateList.valueOf(contentColor)
            googleLensTextView?.setTextColor(context.getColor(theme.textColor))
            googleLensWLView?.background = RippleDrawable(pressedColorStateList, null, null)
        }
    }

    companion object {
        private const val TAG = "PhotoGoogleLensSection"

        /**
         * Spring动画BOUNCE
         */
        private const val ALPHA_ANIM_BOUNCE = 0f

        /**
         * 透明淡入RESPONSE
         */
        private const val ALPHA_FADE_IN_RESPONSE = 0.3f

        /**
         * 透明淡出RESPONSE
         */
        private const val ALPHA_FADE_OUT_RESPONSE = 0.25f

        /**
         * Spring动画初始速度
         */
        private const val ALPHA_ANIM_VELOCITY = 0f

        /**
         * 透明度1
         */
        private const val ALPHA_SHOW = 1f

        /**
         * 透明度0
         */
        private const val ALPHA_HIDE = 0f

        /**
         * 延迟600ms后，才开始执行淡入动画
         */
        private const val REMOVED_DELAY_ALPHA_SHOW_TIME = PhotoAnimationConfig.PHOTO_REMOVED_DELAY_ALPHA_SHOW_TIME
    }
}