/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  CurveSpeedViewAdapter
 ** Description:  曲线变速recyclerview的adapter
 ** Version: 1.0
 ** Date : 2025/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/5/7      1.0     NEW
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.R

/**
 * 曲线变速recyclerview的adapter
 */
class CurveSpeedRecyclerViewAdapter(
    private val context: Context,
    private val changeSpeedCurveInfoList: List<SpeederCurveInfo>
) : RecyclerView.Adapter<CurveSpeedRecyclerViewAdapter.ViewHolder>() {

    //点击事件的监听器
    var onItemClickListener: ((view: View?, pos: Int) -> Unit)? = null

    /**
     * 查找第一个被选中的位置
     * @return 返回第一个被选中的元素在列表中的位置，如果没有找到被选中的元素则返回-1
     */
    fun findFirstSelectedPosition(): Int {
        return changeSpeedCurveInfoList.indexOfFirst { it.isSelected }
    }

    /**
     * 更改选中的位置
     * @param newIndex 新的选中位置的索引
     */
    fun changeSelectedPosition(newIndex: Int) {
        val oldSelectedIndex = findFirstSelectedPosition()
        //如果前后选中的index相同，不处理
        if (oldSelectedIndex == newIndex) return
        // 如果找到了被选中的元素，取消其选中状态并通知视图更新
        if (oldSelectedIndex in changeSpeedCurveInfoList.indices) {
            changeSpeedCurveInfoList[oldSelectedIndex].isSelected = false
        }
        // 设置新的项为选中状态，通知视图更新新的选中项
        if (newIndex in changeSpeedCurveInfoList.indices) {
            changeSpeedCurveInfoList[newIndex].isSelected = true
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.videoeditor_speeder_recycler_view_item_layout, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val info = changeSpeedCurveInfoList[position]
        holder.icon.setImageDrawable(info.imageDrawable)
        holder.name.text = info.name
        (holder.name.text as? TextView)?.textDirection = View.TEXT_DIRECTION_LOCALE
        if (info.isSelected) {
            holder.name.setTextColor(ContextCompat.getColor(context, R.color.white))
            holder.icon.isSelected = true
        } else {
            holder.name.setTextColor(
                ContextCompat.getColor(
                    context, R.color.videoeditor_speed_tv_unselect_color
                )
            )
            holder.icon.isSelected = false
        }

        holder.itemView.setOnClickListener { v ->
            onItemClickListener?.invoke(v, position)
        }
    }

    override fun getItemCount(): Int {
        return changeSpeedCurveInfoList.size
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val icon: ImageView = itemView.findViewById(R.id.speeder_recycler_item_icon)
        val name: TextView = itemView.findViewById(R.id.speeder_recycler_item_name_tv)
    }
}