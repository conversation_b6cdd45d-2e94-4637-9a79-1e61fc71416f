/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RingtoneInvoker
 ** Description:视频铃声编辑 Invoker
 ** Version: 1.0
 ** Date : 2025/06/10
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.ringtone

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.view.View
import android.widget.RelativeLayout
import android.widget.RelativeLayout.CENTER_IN_PARENT
import androidx.core.os.bundleOf
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.api.ApiDmManager.getMediaDBSyncDM
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.EDIT_VIDEO_DATA
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.videoedit.EditVideoData
import com.oplus.gallery.business_lib.videoedit.EditVideoType
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import com.oplus.gallery.videoeditorpage.video.invoker.BaseInvoker

/**
 * 视频铃声定制 invoker，供 editorActivity 差异化调用
 */
class RingtoneInvoker(
    val activity: BaseActivity,
    val editorEngine: EditorEngine
) : BaseInvoker() {
    private val controlBar by lazy {
        activity.findViewById<EditorControlView>(R.id.main_control_view).apply {
            editorEngine = <EMAIL>
        }
    }

    /**
     * 预览区
     */
    private val previewContainer by lazy {
        activity.findViewById<EditorPreviewView>(R.id.engine_preview_layout)
    }

    /**
     * 视频铃声业务state
     */
    private val state: EditorRingtoneState by lazy {
        RingtoneParam.curInstance.let {
            EditorRingtoneState(
                activity,
                controlBar,
                it?.videoTrimMinDuration,
                it?.videoTrimMaxDuration
            )
        }
    }

    /**
     * 视频播放区变化监控
     */
    private val layoutChangeListener =
        View.OnLayoutChangeListener { _, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            if ((left != oldLeft) || (right != oldRight) || (top != oldTop) || (bottom != oldBottom)) {
                GLog.d(TAG, LogFlag.DL, "[layoutChange] liveWindow")
                updateViewParamsToSize(
                    VideoRatio(editorEngine.width, editorEngine.height),
                    right - left,
                    bottom - top
                )
            }
        }

    override fun handleCreateCheckFail() {
        ToastUtil.showShortToast(R.string.videoeditor_notif_not_supported)
        activity.finish()
    }

    override fun getCustomizedState(): EditorBaseState<*>? {
        return state
    }

    override fun onDataImportReady() {
        previewContainer?.let { container ->
            container.addOnLayoutChangeListener(layoutChangeListener)
            updateViewParamsToSize(
                VideoRatio(editorEngine.width, editorEngine.height),
                container.width,
                container.height
            )
        }
    }

    override fun isNeedSeekToCurrentPlayStation(): Boolean {
        return false
    }

    override fun release() {
        previewContainer?.removeOnLayoutChangeListener(layoutChangeListener)
    }

    override fun parseIntent(intent: Intent): Intent {
        val uri = intent.data ?: return intent
        val item = getMediaItem(uri) ?: return intent
        val type = if (item.mediaType == MEDIA_TYPE_VIDEO) EditVideoType.VIDEO else EditVideoType.IMAGE
        GLog.d(TAG, "parseIntent uri:$uri, item:$item")
        val newIntent = (intent.clone() as Intent).apply {
            putExtras(bundleOf(
                EDIT_VIDEO_DATA to EditVideoData(uri.toString(), item.duration, item.width, item.height, type)
            ))
        }
        return newIntent
    }

    override fun handleStateResult(activity: Activity, intent: Intent) {
        activity.setResult(Activity.RESULT_OK, intent)
        activity.finish()
        activity.overridePendingTransition(0, 0)
    }

    override fun isFromOuterApp(): Boolean {
        return true
    }

    private fun getMediaItem(uri: Uri?): MediaItem? {
        uri ?: return null
        return LocalMediaDataHelper.getLocalMediaItem(uri) ?: let {
            GLog.d(TAG, "getMediaItem query local fail, start sync:$uri")
            getMediaDBSyncDM().executeUrisSync(arrayOf(uri))
            LocalMediaDataHelper.getLocalMediaItem(uri)
        }
    }

    private fun updateViewParamsToSize(ratio: VideoRatio, width: Int, height: Int) {
        activity.runOnUiThread {
            val size = VideoRatio.calculateFitSize(ratio, width.toFloat(), height.toFloat())
            val destWidth = size[0]
            val destHeight = size[1]
            val view = previewContainer.liveWindow
            if ((destWidth > 0) && (destHeight > 0)) {
                val lp = (view.layoutParams as? RelativeLayout.LayoutParams)
                    ?: RelativeLayout.LayoutParams(0, 0)
                lp.width = destWidth.toInt()
                lp.height = destHeight.toInt()
                lp.addRule(CENTER_IN_PARENT)
                view.layoutParams = lp
            }
        }
    }

    companion object {
        private const val TAG = "RingtoneInvoker"
    }
}