/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoFilterManager.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IVideoFilterManager
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoFilter.Companion.FILTER_COLOR_PROPERTY
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoFilter.Companion.FILTER_LUT
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoFilter.Companion.FILTER_SHARPEN
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoFilter.Companion.FILTER_VIGNETTE

class MeicamVideoFilterManager : IVideoFilterManager {

    private var mVideoFilters: HashMap<String, MeicamVideoClipEffect> = HashMap()

    constructor() {
        mVideoFilters[FILTER_COLOR_PROPERTY] = MeicamVideoClipEffect(FILTER_COLOR_PROPERTY, BaseVideoEffect.TYPE_BUILT_IN_FX)
        mVideoFilters[FILTER_VIGNETTE] = MeicamVideoClipEffect(FILTER_VIGNETTE, BaseVideoEffect.TYPE_BUILT_IN_FX)
        mVideoFilters[FILTER_SHARPEN] = MeicamVideoClipEffect(FILTER_SHARPEN, BaseVideoEffect.TYPE_BUILT_IN_FX)
        mVideoFilters[FILTER_LUT] = MeicamVideoClipEffect(FILTER_LUT, BaseVideoEffect.TYPE_BUILT_IN_FX)
    }

    override fun getVideoClipFilter(filterName: String?): BaseVideoClipEffect? {
        return mVideoFilters[filterName]?.let { it.clone() }
    }
}