/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NativeCrashCatch.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/12/29
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/12/29		1.0		NativeCrashGuard
 *********************************************************************************/
package com.oplus.breakpad

import com.oplus.breakpad.BreakpadMaster.RESULT_APP_FAIL
import com.oplus.breakpad.BreakpadMaster.RESULT_NATIVE_FAIL
import com.oplus.breakpad.BreakpadMaster.RESULT_SUCCESS

/**
 * 此方法无法catch住当次native异常，但会记录该异常，并在累计2次相同异常后，不再执行异常代码块
 */
fun <T> runNativeGuarding(scene: String, key: String, block: () -> T?): NativeResult<T> {
    val guardResult: GuardResult<T> = BreakpadMaster.guardScene(
        scene,
        key,
        DefaultNativeCrashGuardRule(key)
    ) {
        block.invoke()
    }
    return NativeResult(guardResult.result, guardResult.resultCode)
}

class NativeResult<out T>(val value: T?, val resultCode: Int) {

    /**
     * for java code
     */
    fun onSuccess(i: INativeResult): NativeResult<T> {
        if (resultCode == RESULT_SUCCESS) {
            i.callback()
        }
        return this
    }

    /**
     * for kotlin code
     */
    fun onSuccess(block: () -> Unit): NativeResult<T> {
        if (resultCode == RESULT_SUCCESS) {
            block.invoke()
        }
        return this
    }

    /**
     * for java code
     */
    fun onNativeFailure(i: INativeResult): NativeResult<T> {
        if (resultCode == RESULT_NATIVE_FAIL) {
            i.callback()
        }
        return this
    }

    /**
     * for kotlin code
     */
    fun onNativeFailure(block: () -> Unit): NativeResult<T> {
        if (resultCode == RESULT_NATIVE_FAIL) {
            block.invoke()
        }
        return this
    }

    fun getOrNull(): T? =
        if ((resultCode == RESULT_NATIVE_FAIL) || (resultCode == RESULT_APP_FAIL)) {
            null
        } else {
            value
        }
}

interface INativeResult {
    fun callback()
}





