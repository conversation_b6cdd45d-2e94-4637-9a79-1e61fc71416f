/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ImageIconLoader
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.memories.imageloader;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import com.oplus.gallery.basebiz.dfm.PluginName;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.graphic.BitmapLoadUtil;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.R;

import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;

import java.io.InputStream;

public final class ImageIconLoader extends BaseThumbnailLoader<Object> {

    private static final String TAG = "ImageIconLoader";
    private static volatile ImageIconLoader sInstance;

    private ImageIconLoader() {
        super();
    }

    public static ImageIconLoader getInstance() {
        if (sInstance == null) {
            synchronized (ImageIconLoader.class) {
                if (sInstance == null) {
                    sInstance = new ImageIconLoader();
                }
            }
        }
        return sInstance;
    }

    public void init(WorkerSession session) {
        mWorkerSession = session;
    }

    static class ImageIconLoaderCallback implements ILoaderCallback<Object> {
        @Override
        public Bitmap onThumbNailLoad(Object item, JobContext jc) {
            Bitmap imageBitmap = null;
            Context context = ContextGetter.context;
            if (item instanceof MusicItem) {
                MusicItem musicItem = (MusicItem) item;
                String path = musicItem.getThumbnailPath();
                if (!TextUtils.isEmpty(path)
                        && (musicItem.isBuiltin() || musicItem.isDefaultIcon())) {
                    imageBitmap = BitmapLoadUtil.getBitmapByVectorImagePath(context, path, PluginName.VIDEO_EDITOR.getPluginName());
                } else {
                    if (!musicItem.isNeedDownloadIcon()
                            && !TextUtils.isEmpty(musicItem.getThumbnailPath())) {
                        imageBitmap = BitmapFactory.decodeFile(musicItem.getThumbnailPath());
                    } else {
                        imageBitmap = BitmapFactory.decodeResource(context.getResources(),
                                R.drawable.videoeditor_editor_ic_sticker_default_thumbnail);
                    }
                }
            } else if (item instanceof ThemeItem) {
                ThemeItem themeItem = (ThemeItem) item;
                String path = themeItem.getThumbnailPath();
                if (!TextUtils.isEmpty(path)
                        && (themeItem.isBuiltin() || themeItem.isDefaultIcon())) {
                    imageBitmap = BitmapLoadUtil.getBitmapByVectorImagePath(context, path, PluginName.VIDEO_EDITOR.getPluginName());
                } else {
                    if (!themeItem.isNeedDownloadIcon()
                            && !TextUtils.isEmpty(themeItem.getThumbnailPath())) {
                        imageBitmap = BitmapFactory.decodeFile(themeItem.getThumbnailPath());
                    } else {
                        imageBitmap = BitmapFactory.decodeResource(context.getResources(),
                                R.drawable.videoeditor_editor_ic_sticker_default_thumbnail);
                    }
                }
            }
            if (imageBitmap == null) {
                imageBitmap = BitmapFactory.decodeResource(context.getResources(),
                        R.drawable.videoeditor_editor_ic_sticker_default_thumbnail);
            }
            return imageBitmap;
        }

        @Override
        public void recycleBitmap(Bitmap bitmap) {
            GLog.e(TAG, LogFlag.DL, "[recycleBitmap] this method is empty");
        }

        @Override
        public String getDataUniqueTag(Object item) {
            return item.toString();
        }

        private Bitmap loadAssertIcon(String path, Context context) {
            Bitmap imageBitmap = null;
            InputStream in = null;
            try {
                in = context.getAssets().open(path);
                imageBitmap = BitmapFactory.decodeStream(in);
            } catch (Exception e) {
                GLog.e(TAG, "loadAssertIcon e = " + e);
            } finally {
                IOUtils.closeQuietly(in);
            }
            return imageBitmap;
        }
    }

    @Override
    protected ILoaderCallback<Object> buildThumbnailCallback() {
        return new ImageIconLoaderCallback();
    }
}
