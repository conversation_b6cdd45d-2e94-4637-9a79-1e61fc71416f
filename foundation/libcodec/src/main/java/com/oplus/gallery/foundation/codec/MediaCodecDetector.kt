/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaCodecDetector.kt
 ** Description: 用于判断是否支持某种类型的编解码
 **
 ** Version: 1.0
 ** Date: 2023/04/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2023/04/19    1.0                create
 ******************************************************************************/

package com.oplus.gallery.foundation.codec

import android.media.MediaCodecInfo.CodecCapabilities.COLOR_FormatYUVP010
import android.media.MediaCodecInfo.CodecProfileLevel.HEVCProfileMain
import android.media.MediaCodecInfo.CodecProfileLevel.HEVCProfileMain10
import android.media.MediaCodecList
import android.media.MediaFormat
import com.oplus.gallery.foundation.codec.MediaCodecDetector.MtkPlatform.VIDEO_ENCODE_CANONICAL_NAME_C2_MTK_HEVC_ENCODER
import com.oplus.gallery.foundation.codec.MediaCodecDetector.QualcommPlatform.VIDEO_ENCODE_CANONICAL_NAME_C2_QTI_HEVC_ENCODER
import com.oplus.gallery.foundation.codec.MediaCodecDetector.QualcommPlatform.VIDEO_ENCODE_CANONICAL_NAME_OMX_QCOM_VIDEO_ENCODER_HEVC
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil

/**
 * 该类主要用于判断当前机型是否支持指定媒体类型编解码。
 */
object MediaCodecDetector {
    private const val TAG = "MediaCodecDetector"

    /**
     * 判断当前是否支持指定媒体类型的编码
     * @return Boolean
     */
    fun isSupportCodecForMediaType(mediaType: CodecMediaType): Boolean {
        val mediaCodecFilterInfoMap = when (mediaType) {
            CodecMediaType.HLG -> formatHLGVideoEncoderFilters()
        }

        if (mediaCodecFilterInfoMap.isEmpty()) {
            return false
        }

        return isSupportCodecWithProfileType(mediaCodecFilterInfoMap)
    }

    /**
     * 创建HLG硬件编码格式匹配信息
     * @param nameList List<String>
     * @return List<MediaCodecFilterInfo>
     */
    @JvmStatic
    private fun formatHLGVideoEncoderFilters(): Map<String, MediaCodecFilterInfo> {
        val canonicalNameList = getHLGVideoEncoderCanonicalNameList()
        val resultMap = mutableMapOf<String, MediaCodecFilterInfo>()
        canonicalNameList.forEach { name ->
            when (name) {
                VIDEO_ENCODE_CANONICAL_NAME_C2_QTI_HEVC_ENCODER, VIDEO_ENCODE_CANONICAL_NAME_OMX_QCOM_VIDEO_ENCODER_HEVC -> {
                    val detailInfoList = mutableListOf<MediaCodecFilterDetailInfo>()
                    detailInfoList.add(MediaCodecFilterDetailInfo(HEVCProfileMain10))
                    resultMap[name] = MediaCodecFilterInfo(name = name, mimeType = MediaFormat.MIMETYPE_VIDEO_HEVC, detailInfoList)
                }
                VIDEO_ENCODE_CANONICAL_NAME_C2_MTK_HEVC_ENCODER -> {
                    val detailInfoList = mutableListOf<MediaCodecFilterDetailInfo>()
                    detailInfoList.add(MediaCodecFilterDetailInfo(HEVCProfileMain10))
                    if (ApiLevelUtil.isAtLeastAndroidT()) {
                        detailInfoList.add(MediaCodecFilterDetailInfo(HEVCProfileMain, COLOR_FormatYUVP010))
                    }
                    resultMap[name] = MediaCodecFilterInfo(name = name, mimeType = MediaFormat.MIMETYPE_VIDEO_HEVC, detailInfoList)
                }
            }
        }

        return resultMap
    }

    /**
     * Hlg等种类视频需要在编辑后保存HEVC编码格式，可通过该方法获取平台对应的HEVC硬件编码器名称
     * @return 返回平台对应的HLG编码器标准名称
     */
    private fun getHLGVideoEncoderCanonicalNameList(): List<String> {
        // Marked by zhangwenming 平台判断暂时使用 [FeatureUtils]中的方法，后续整改判定平台类型时，再替换此处
        return when {
            FeatureUtils.isMTKPlatform -> MtkPlatform.HLG_VIDEO_ENCODE_CANONICAL_NAME_LIST
            FeatureUtils.isQualcommPlatform -> QualcommPlatform.HLG_VIDEO_ENCODE_CANONICAL_NAME_LIST
            else -> {
                GLog.e(TAG, "[getHLGVideoEncoderCanonicalNameList] is Empty.")
                listOf()
            }
        }
    }

    @JvmStatic
    private fun isSupportCodecWithProfileType(filterInfoMap: Map<String, MediaCodecFilterInfo>): Boolean {
        if (filterInfoMap.isEmpty()) {
            GLog.w(TAG, "[isSupportCodecWithProfileType], filterInfoList can't be null or empty")
            return false
        }

        val supportCodecInfoArray = MediaCodecList(MediaCodecList.REGULAR_CODECS).codecInfos
        supportCodecInfoArray.forEach { supportCodecInfo ->
            filterInfoMap[supportCodecInfo.canonicalName]?.let { filterInfo ->
                val codecCapabilities = supportCodecInfo.getCapabilitiesForType(filterInfo.mimeType) ?: return@forEach
                val codecProfileLevels = codecCapabilities.profileLevels ?: return@forEach
                codecProfileLevels.forEach { codecProfileLevel ->
                    filterInfo.filterList.forEach filterListLoop@{ filterDetailInfo ->
                        if (codecProfileLevel.profile != filterDetailInfo.profile) {
                            return@filterListLoop
                        }

                        if ((filterDetailInfo.colorFormat != null) &&
                            codecCapabilities.colorFormats.contains(filterDetailInfo.colorFormat).not()
                        ) {
                            return@filterListLoop
                        }
                        return true
                    }
                }
            }
        }
        return false
    }

    object MtkPlatform {
        internal const val VIDEO_ENCODE_CANONICAL_NAME_C2_MTK_HEVC_ENCODER = "c2.mtk.hevc.encoder"

        // MTK机型HLG视频编码器名称
        val HLG_VIDEO_ENCODE_CANONICAL_NAME_LIST = listOf(VIDEO_ENCODE_CANONICAL_NAME_C2_MTK_HEVC_ENCODER)
    }

    object QualcommPlatform {
        internal const val VIDEO_ENCODE_CANONICAL_NAME_C2_QTI_HEVC_ENCODER = "c2.qti.hevc.encoder"
        internal const val VIDEO_ENCODE_CANONICAL_NAME_OMX_QCOM_VIDEO_ENCODER_HEVC = "OMX.qcom.video.encoder.hevc"

        // 高通机型HLG视频编码器名称
        val HLG_VIDEO_ENCODE_CANONICAL_NAME_LIST = listOf(
            VIDEO_ENCODE_CANONICAL_NAME_C2_QTI_HEVC_ENCODER,
            VIDEO_ENCODE_CANONICAL_NAME_OMX_QCOM_VIDEO_ENCODER_HEVC
        )
    }
}

/**
 * 编解码相关业务的媒体类型
 */
enum class CodecMediaType {
    /**
     * HLG视频
     */
    HLG
}

/**
 * 匹配支持的编码类型
 * Marked by zhangwenming。该方案不好，最有的方案应该是由系统提供接口来判定，相册不应该关心某个机型这么复杂的编码类型
 * 拉群沟通，让系统添加接口支持。
 */
private data class MediaCodecFilterInfo(
    /**
     * 编码类型名称
     */
    val name: String,
    /**
     * 文件类型
     */
    val mimeType: String,
    /**
     * 需要匹配的详细信息
     */
    val filterList: List<MediaCodecFilterDetailInfo>
)

/**
 * 支持的编码类型详细匹配信息，如profile，color format等。
 */
private data class MediaCodecFilterDetailInfo(
    /**
     * 需要指定的profile，比如:HEVCProfileMain10，取值详见[MediaCodecInfo.CodecProfileLevel]
     */
    val profile: Int,
    /**
     * 支持的色彩模式，如COLOR_FormatYUVP010。取值详见[MediaCodecInfo.CodecCapabilities]。如果为空，则表示不需要匹配该项。
     */
    val colorFormat: Int? = null,
)