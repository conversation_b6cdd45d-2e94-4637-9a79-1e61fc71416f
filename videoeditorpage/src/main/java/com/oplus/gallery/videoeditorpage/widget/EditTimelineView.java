/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditTimelineView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget;

import android.app.Service;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.CommonConstants;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.EditTimelineRecyclerView;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.EditTimelineSpanView;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.MultiThumbnailSequenceView.ThumbnailSequenceDesc;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.MultiThumbnailSequenceView;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.adapter.EditorTimeCoverAdapter;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.adapter.EditorTimeCoverWithTailAdapter;
import com.oplus.gallery.videoeditorpage.abilities.engine.ui.adapter.EditorTimeTransitionAdapter;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.utlis.TimelineUtil;
import com.oplus.gallery.videoeditorpage.video.business.track.view.EditTimelineTrailView;
import com.oplus.gallery.videoeditorpage.video.business.track.view.PipSelector;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class EditTimelineView extends RelativeLayout {
    private static final String TAG = "EditTimelineView";
    public static final int ERROR_CORRECTION = 1;
    public static final int VIRBATE_MILLISECONDS = 70;
    public static final int MINUTES_60 = 60;
    public static final int SIX_MINUTES_3600 = 3600;
    public static final double ONE_SQUARE_SECOND = 1.5;
    public static final String STATE_PREVIEW = "preview";
    public static final String STATE_EDITOR = "editor";
    public static final String STATE_DISPLAY = "display";
    public static final int LIMIT_FRAME_ERROR = 10000;
    /**
     * min duration 0.5s one picture
     */
    private static final long MIN_DURATION = (long) (EditorEngine.TIME_BASE * 0.5F);
    private static final long MIN_SHOW_LENGTH_DURATION = (long) (EditorEngine.TIME_BASE * 0.5);
    private static final int HAND_TOTAL = 2;
    private static final int DURATION_TEXT_REPAIR_VALUE = 10;
    private static final int TOUCH_ID = -100001;
    private static final int TIME_DELAY = 40;
    private static final int CHECK_COUNT = 20;
    private static final int LONG_PRESS = 10086;
    private static final int LIMIT_INTERCEPT_MOVE = 10;
    private static final int LIMIT_INTERCEPT_DURATION_CHANGE = 1000;
    private static final float THUMBNAIL_ASPECT_RATIO = 1F;
    private static final long TIME_SECOND = 1 * 1000 * 1000;
    private static final long TIME_MINUTE = 60 * TIME_SECOND;
    private static final int SEEK_OUT_POINT_ERROR = 10;
    private static final int FACTOR_NUM = 25;
    private static final int SCROLL_MIDDLE_DURATION_10 = 10;
    private static final int SCROLL_MIDDLE_DURATION_20 = 20;

    private final Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case LONG_PRESS:
                    int index = getClipIndexOnTap(mOnInterceptDownX);
                    if ((index < 0) || (!mReceiveMoveEvent) || (mTotalMoveXOnIntercept > LIMIT_INTERCEPT_MOVE)
                            || (Math.abs(getNowTime() - mDurationOnIntercept) > LIMIT_INTERCEPT_DURATION_CHANGE)) {
                        return;
                    }
                    if (mOnSelectItemListener != null) {
                        mMultiThumbnailSequenceView.setScrollEnabled(false);
                        mOnSelectItemListener.onLongPressClip(index);
                        Vibrator vib = (Vibrator) getContext().getSystemService(Service.VIBRATOR_SERVICE);
                        if (vib != null) {
                            vib.vibrate(VIRBATE_MILLISECONDS);
                        }
                    }

                    break;
                default:
                    break;
            }
        }
    };

    private int mMiddleLimit = ScreenUtils.getDisplayScreenWidth() / 2;
    private int mFactor;
    private String mState;
    private double mPixelPerMicrosecond = 0D;
    private double mLengthForTimeRule = 0D;
    private RelativeLayout mTimeSpanRelativeLayout;
    private LinearLayout mLinearContainer;
    private MultiThumbnailSequenceView mMultiThumbnailSequenceView;
    private int mHandWidth;
    private int mTransIconWidth;
    private int mSequenceLeftPadding = 0;
    private ITimeline mTimeline;
    private OnScrollPosChangeListener mScrollListener;
    private boolean mScrollPressed = false;
    private boolean mIsAligningMiddle = false;
    private long mDownTimeNow;
    private List<Long> mAllOutPoint = new ArrayList<>();
    private Long[] mAvFileInfos;
    private EditTimelineSpanView mTimeSpanView;
    private int mViewMarginLeft = 0;
    private int mSelectClipIndex;
    private OnSelectItemListener mOnSelectItemListener;
    private OnPipSelectorClickListener mOnPipSelectorClickListener;
    private OnMuteClickListener mMuteClickListener;
    private ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> mThumbnailSequenceDescArrayListOnHandDown = new ArrayList<>();
    private List<Pair<Integer, Integer>> mClipPositionList = new ArrayList<>();
    private float mOnInterceptDownX;
    private boolean mScrollToClickClip = false;
    private int mTotalMoveXOnIntercept = 0;
    private long mDurationOnIntercept = 0;
    private boolean mReceiveMoveEvent = false;
    private Button mAddButton;
    private EditTimelineRecyclerView mCoverRecycleView;
    private EditTimelineRecyclerView mTransitionRecycleView;
    private EditorTimeCoverAdapter mEditorTimeCoverAdapter;
    private EditTimelineTrailView mEditTrailView;
    private PipSelector mPipSelectorView;
    private EditorTimeTransitionAdapter mEditorTimeTransitionAdapter;
    private int mOldScrollX;
    private boolean mDragEnd = false;
    private int mDownLeftSelectClipViewLeft = 0;
    private boolean mIgnoreCurrentIndexChange = false;
    private boolean mIsFirstLoad = false;
    private int mOffset;
    private boolean mSequenceLoadSuccess = false;
    private int mFirstToPosition;
    private int mFirstScrollDx;
    private int mAddButtonMargin;
    private int mAddButtonSize;
    private boolean mFromTrimSpeedChange;
    private long mPositionOnPlay;
    private String mMaxCountString;
    private int mAddParentLengthOnLeftHandDown;
    private boolean mSpanLeftTextState = true;
    private TextView mTxtDuration;
    private int mTxtDurationMargin;
    private StringBuilder mDurationStringBuilder;
    private boolean mHasDragEnd = true;
    private View mCenterView;
    private boolean mCheckOffset;
    private int mTargetPosition;
    private boolean mIsSpeedMode;
    private boolean mNeedShowThumbnailSpread = false;
    private Handler mScrollToMiddleHandler = new Handler();
    private OnTimelineScrollViewListener mOnTimelineScrollViewListener;
    private OnCustomTouchEvent mOnCustomTouchEvent;
    private ThumbnailLoadFinishListener mThumbnailLoadFinishListener;
    private boolean mNeedShowPipTab = false;
    private IVideoClip mSelectClip;
    private int mTrackIndex;
    private long mMinShowLengthDuration = MIN_SHOW_LENGTH_DURATION;


    public EditTimelineView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mHandWidth = getResources().getDimensionPixelSize(R.dimen.engine_edit_timeline_view_hand_width);
        mTransIconWidth = getResources().getDimensionPixelSize(R.dimen.edit_timeline_preview_cut_to_button_height);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.EditTimelineView);
        try {
            mNeedShowPipTab = a.getBoolean(R.styleable.EditTimelineView_need_show_pip_tab, false);
        } finally {
            a.recycle();
        }
        init(context);
    }

    public void setOnMuteClickListener(OnMuteClickListener listener) {
        mMuteClickListener = listener;
    }

    public void setMuteButtonState(boolean isMute) {
        if (mEditorTimeCoverAdapter != null) {
            mEditorTimeCoverAdapter.setMuteButtonState(isMute);
        }
    }

    public void setMuteButtonVisible(boolean isShow) {
        if (mEditorTimeCoverAdapter != null) {
            mEditorTimeCoverAdapter.setMuteButtonVisible(isShow);
        }
    }

    public void setMuteIconRes(String muteText,
                               ColorStateList unmuteColor,
                               ColorStateList muteColor,
                               Drawable unmuteDrawable,
                               Drawable muteDrawable) {
        if (mEditorTimeCoverAdapter != null) {
            mEditorTimeCoverAdapter.setMuteIconRes(muteText, unmuteColor, muteColor, unmuteDrawable, muteDrawable);
        }
    }

    private void moveDurationText() {
        Pair<Integer, Integer> selectedPosition = getClipPosition(mSelectClipIndex);
        if (selectedPosition == null) {
            GLog.e(TAG, "moveDurationText,selectedPosition == null:" + mSelectClipIndex);
            return;
        }
        int durationX = selectedPosition.first + mTxtDurationMargin
                - mMultiThumbnailSequenceView.getScrollX() + mSequenceLeftPadding;
        if (durationX < mTxtDurationMargin) {
            durationX = mTxtDurationMargin;
        }
        mTxtDuration.setPadding(durationX, 0, 0, 0);
    }

    private void init(Context context) {
        mMinShowLengthDuration = MIN_SHOW_LENGTH_DURATION;
        LayoutInflater.from(context).inflate(R.layout.engine_timeline_editor_view, this);
        findViews();
        setupPipSelectorVisibility();
        setMinSequenceDuration();
        setupAddButtonListener();
        setupScrollListener();
        setupTouchListener();
        setupPipSelectorListener();
        initializeDimensions(context);
    }

    /**
     * 查找所有子控件引用
     */
    private void findViews() {
        mMultiThumbnailSequenceView = findViewById(R.id.edit_multi_thumbnail_sequence_view);
        mTimeSpanRelativeLayout = findViewById(R.id.edit_timeline_view_container_relative);
        mLinearContainer = findViewById(R.id.edit_timeline_view_linear_layout);
        mAddButton = findViewById(R.id.edit_timeline_view_add_button);
        mCoverRecycleView = findViewById(R.id.edit_timeline_view_cover_recycler);
        mTransitionRecycleView = findViewById(R.id.edit_timeline_view_trans_recycler);
        mTxtDuration = findViewById(R.id.edit_timeline_view_text);
        mEditTrailView = findViewById(R.id.edit_timeline_trail_view);
        mPipSelectorView = findViewById(R.id.edit_pip_selector);
    }

    /**
     * 根据配置设置 PipSelector 显示/隐藏
     */
    private void setupPipSelectorVisibility() {
        if (mNeedShowPipTab) {
            mPipSelectorView.setVisibility(VISIBLE);
        } else {
            mPipSelectorView.setVisibility(GONE);
        }
    }

    /**
     * 设置多缩略图序列的最小显示时长
     */
    private void setMinSequenceDuration() {
        mMultiThumbnailSequenceView.setMinSequenceShowDuration(mMinShowLengthDuration);
    }

    /**
     * 设置添加按钮点击事件
     */
    private void setupAddButtonListener() {
        mAddButton.setOnClickListener(v -> {
            if (mOnSelectItemListener == null) {
                return;
            }
            IVideoTrack videoTrack = mTimeline.getVideoTrack(0);
            if (videoTrack == null) {
                return;
            }
            List<IVideoClip> clips = videoTrack.getClipList();
            if (!TimelineUtil.checkTimelineNull(mTimeline) && clips.size() >= CommonConstants.MAX_CLIP_NUM) {
                ToastUtil.showShortToast(mMaxCountString);
                enableAddMaterial(true);
                return;
            }

            if (mOnSelectItemListener.onAddButtonClick(v)) {
                enableAddMaterial(false);
            }
        });
    }

    /**
     * 设置多缩略图序列的滚动监听
     */
    private void setupScrollListener() {
        mMultiThumbnailSequenceView.setOnScrollChangeListenser(new MultiThumbnailSequenceView.OnScrollChangeListener() {
            @Override
            public void onScrollChanged(MultiThumbnailSequenceView view, int newDx, int oldDx) {
                if (TimelineUtil.checkTimelineNull(mTimeline)) {
                    return;
                }

                handleScrollVisibility(newDx, oldDx);
                updateContainerScroll(newDx);
                updatePipAndTrailScroll(newDx);
                updateSelectedClipIndex();
                moveDurationText();
                notifyScrollListener(newDx, oldDx);
            }
        });
    }

    /**
     * 处理滚动过程中的 UI 状态变化
     */
    private void handleScrollVisibility(int newDx, int oldDx) {
        if (TextUtils.equals(mState, STATE_PREVIEW) && !mIsSpeedMode) {
            mTransitionRecycleView.setVisibility(VISIBLE);
        }

        long stamp = getNowTime();
        if (mOldScrollX >= 0 && mOldScrollX != newDx) {
            if (mIsFirstLoad) {
                firstScroll(stamp);
            }
            mCoverRecycleView.scrollBy(newDx - oldDx, 0);
            if (TextUtils.equals(mState, STATE_PREVIEW)) {
                mTransitionRecycleView.scrollBy(newDx - oldDx, 0);
            }
        }
        mOldScrollX = newDx;
    }

    /**
     * 更新主容器滚动位置
     */
    private void updateContainerScroll(int newDx) {
        if (TextUtils.equals(mState, STATE_EDITOR)) {
            if (!mIsAligningMiddle) {
                EditTimelineSpanView span = getTimelineTimeSpan();
                if ((span == null) || (span.getDragDirection() == EditTimelineSpanView.CENTER)) {
                    mLinearContainer.scrollTo(newDx, 0);
                }
            }
        } else {
            mLinearContainer.scrollTo(newDx, 0);
        }
    }

    /**
     * 同步 PipSelector 和 TrailView 的滚动
     */
    private void updatePipAndTrailScroll(int newDx) {
        if (mPipSelectorView != null) {
            mPipSelectorView.scrollToPosition(newDx, 0);
        }
        if (mEditTrailView != null) {
            mEditTrailView.scrollToPosition(newDx, 0);
        }
    }

    /**
     * 根据滚动位置更新当前选中的片段
     */
    private void updateSelectedClipIndex() {
        long stamp = getNowTime();
        boolean checkIndexCanChange = false;

        if (TextUtils.equals(mState, STATE_PREVIEW)) {
            checkIndexCanChange = !mScrollToClickClip && !mIgnoreCurrentIndexChange;
        } else if (TextUtils.equals(mState, STATE_EDITOR)) {
            EditTimelineSpanView span = getTimelineTimeSpan();
            checkIndexCanChange = !mScrollToClickClip
                    && (span != null)
                    && (span.getDragDirection() == EditTimelineSpanView.CENTER)
                    && !mIgnoreCurrentIndexChange && !mFromTrimSpeedChange;
        }

        if (checkIndexCanChange) {
            if (!mScrollPressed
                    && TextUtils.equals(mState, STATE_EDITOR)
                    && (mPositionOnPlay > stamp)
            ) {
                stamp = mPositionOnPlay;
            }

            int clipIndex = getClipIndexByTime(stamp);
            if (clipIndex < 0) {
                return;
            }

            int beforePosition = mSelectClipIndex;
            if ((clipIndex != mSelectClipIndex) && !mCheckOffset) {
                mSelectClipIndex = clipIndex;
                refreshPreviewTimeSpanChanged(beforePosition, mSelectClipIndex);
                if (mScrollListener != null) {
                    mScrollListener.onSelectClipIndexChange(mSelectClipIndex);
                }
            }
        }
    }

    /**
     * 通知外部监听器滚动事件
     */
    private void notifyScrollListener(int newDx, int oldDx) {
        if (mOnTimelineScrollViewListener != null) {
            mOnTimelineScrollViewListener.onTimelineScrollChanged(mMultiThumbnailSequenceView, newDx, oldDx);
        }

        if (!mScrollPressed || (mScrollListener == null)) {
            return;
        }

        long stamp = getNowTime();
        if (stamp > (mTimeline.getDuration() - LIMIT_FRAME_ERROR)) {
            stamp = mTimeline.getDuration() - LIMIT_FRAME_ERROR;
        }
        mScrollListener.onScrollChanged(stamp);
    }

    /**
     * 设置多缩略图序列的触摸监听
     */
    private void setupTouchListener() {
        mMultiThumbnailSequenceView.setOnTouchListener(new OnTouchListener() {
            private int mCount = 0;
            private Handler mMultiThumbnailHandler = new Handler(msg -> {
                View scroller = (View) msg.obj;
                if (msg.what == TOUCH_ID) {
                    boolean finish = isfinishScroll();
                    if (finish) {
                        if (mCount > CHECK_COUNT) {
                            handleStop();
                        } else {
                            mCount++;
                            sendMessageDelayed(TOUCH_ID, scroller, TIME_DELAY);
                        }
                    } else {
                        mCount = 0;
                        sendMessageDelayed(TOUCH_ID, scroller, TIME_DELAY);
                    }
                }
                return true;
            });

            private void sendMessageDelayed(int what, View obj, long delay) {
                mMultiThumbnailHandler.sendMessageDelayed(mMultiThumbnailHandler.obtainMessage(what, obj), delay);
            }

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (mOnCustomTouchEvent != null) {
                    mOnCustomTouchEvent.onTouch(v, event);
                }

                mMultiThumbnailHandler.removeMessages(TOUCH_ID);

                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mMultiThumbnailSequenceView.setScrollEnabled(true);
                    mScrollPressed = true;
                } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                    mScrollPressed = true;
                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    mCount = 0;
                    sendMessageDelayed(TOUCH_ID, v, 0);
                }
                return false;
            }
        });
    }

    /**
     * 设置 PipSelector 项点击监听
     */
    private void setupPipSelectorListener() {
        mPipSelectorView.setOnItemClickedListener(videoClip -> {
            if (mOnPipSelectorClickListener != null) {
                mOnPipSelectorClickListener.onPipSelectorClicked(videoClip);
            }
        });
    }

    /**
     * 初始化所有尺寸相关参数
     */
    private void initializeDimensions(Context context) {
        Resources res = context.getResources();
        mTxtDurationMargin = (int) res.getDimension(R.dimen.edit_timeline_view_span_text_margin);
        mPixelPerMicrosecond = res.getDimensionPixelSize(R.dimen.edit_timeline_view_size_for_compute)
                / (float) EditorEngine.TIME_BASE / ONE_SQUARE_SECOND;
        mLengthForTimeRule = res.getDimensionPixelOffset(R.dimen.edit_timeline_view_size_for_compute) / ONE_SQUARE_SECOND;
        mAddButtonMargin = res.getDimensionPixelOffset(R.dimen.edit_ic_time_edit_add_margin_start);
        mAddButtonSize = res.getDimensionPixelOffset(R.dimen.edit_ic_time_edit_add_height);
    }

    public void setMinSequenceShowDuration(long duration) {
        mMinShowLengthDuration = duration;
        if (mMultiThumbnailSequenceView != null) {
            mMultiThumbnailSequenceView.setMinSequenceShowDuration(duration);
        }
        if (mEditorTimeCoverAdapter != null) {
            mEditorTimeCoverAdapter.setMinThumbnailWidth(duringToLength(duration));
        }
    }

    public void setTrackIndex(int trackIndex) {
        mTrackIndex = trackIndex;
    }

    public interface OnCustomTouchEvent {
        void onTouch(View v, MotionEvent event);
    }

    public void setOnCustomTouchEvent(OnCustomTouchEvent onCustomTouchEvent) {
        this.mOnCustomTouchEvent = onCustomTouchEvent;
    }

    private boolean checkSpanViewIsEditor() {
        if (getTimelineTimeSpan() == null) {
            return false;
        }
        return getTimelineTimeSpan().getDragDirection() != EditTimelineSpanView.CENTER;
    }

    public void enableAddMaterial(boolean enable) {
        GLog.d(TAG, "enableAddMaterial: " + enable);
        mAddButton.setEnabled(enable);
    }

    public void setCenterView(View centerView) {
        this.mCenterView = centerView;
    }


    private void firstScroll(long stamp) {
        mIsFirstLoad = false;
        if ((stamp == 0) && !mIsSpeedMode) {
            mTransitionRecycleView.setVisibility(VISIBLE);
            return;
        }
        int position = (int) (stamp / EditorEngine.TIME_BASE);
        int lastScroll = (int) ((int) (stamp - position * EditorEngine.TIME_BASE) * mLengthForTimeRule / EditorEngine.TIME_BASE);
        position += 1;
        mFirstScrollDx = lastScroll;
        mFirstToPosition = position;
    }

    private void smoothMoveToPosition(RecyclerView recyclerView, final int position, int lastScroll) {
        int firstItem = recyclerView.getChildLayoutPosition(recyclerView.getChildAt(0));
        int lastItem = recyclerView.getChildLayoutPosition(recyclerView.getChildAt(recyclerView.getChildCount() - 1));
        if (position < firstItem) {
            recyclerView.scrollToPosition(position);
        } else if (position <= lastItem) {
            int movePosition = position - firstItem;
            if ((movePosition >= 0) && (movePosition < recyclerView.getChildCount())) {
                int left = recyclerView.getChildAt(movePosition).getLeft() - ScreenUtils.getDisplayScreenWidth() / 2 + lastScroll;
                recyclerView.scrollBy(left, 0);
            }
        } else {
            recyclerView.scrollToPosition(position);
        }
    }

    private void handleStop() {
        mScrollPressed = false;
        if (mScrollListener != null) {
            mScrollListener.onEditTimelineViewActionUp();
        }
    }

    public void setTimeline(ITimeline timeline) {
        if (TimelineUtil.checkTimelineNull(timeline)) {
            GLog.e(TAG, "setTimeline: timeline is null");
        }
        this.mTimeline = timeline;
    }

    public void initTimelineEditor(int sequenceLeftPadding, int sequenceRightPadding, ITimeline iTimeline, String state) {
        if (TimelineUtil.checkTimelineNull(iTimeline)) {
            GLog.e(TAG, "setTimeline: timeline is null");
            return;
        }
        this.mSequenceLeftPadding = sequenceLeftPadding;
        this.mTimeline = iTimeline;
        this.mState = state;
        int clipListSize = 0;
        if (getCurrentVideoTrack() != null) {
            clipListSize = getCurrentVideoTrack().getClipList().size();
        }
        GLog.d(TAG, "initTimelineEditor: clipListSize = " + clipListSize);
        mAvFileInfos = new Long[clipListSize];
        setAllOutPoint();
        this.mViewMarginLeft = 0;
        ArrayList<ThumbnailSequenceDesc> sequenceDesc = getThumbNailArray();
        if ((sequenceDesc == null) || sequenceDesc.isEmpty()) {
            GLog.e(TAG, "initTimelineEditor: ThumbNailArray is empty");
            return;
        }

        if (mMultiThumbnailSequenceView.getThumbnailSequenceDescArray() != null) {
            mMultiThumbnailSequenceView.updatePortionThumbnails(sequenceDesc);
        } else {
            mMultiThumbnailSequenceView.setThumbnailSequenceDescArray(sequenceDesc);
            mMultiThumbnailSequenceView.setPixelPerMicrosecond(mPixelPerMicrosecond);
            mMultiThumbnailSequenceView.setStartPadding(mSequenceLeftPadding);
            mMultiThumbnailSequenceView.setEndPadding(sequenceRightPadding);
            mMultiThumbnailSequenceView.setThumbnailAspectRatio(THUMBNAIL_ASPECT_RATIO);
            mMultiThumbnailSequenceView.setThumbnailImageFillMode(MultiThumbnailSequenceView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);
            mMultiThumbnailSequenceView.setOnScrollLoaderData(new SequenceLoadListener());
        }
        bindAndRefreshChildView(mTimeline);
        initTimeSpanParentLayout();
        if (TextUtils.equals(mState, STATE_PREVIEW) && !mIsSpeedMode) {
            mAddButton.setVisibility(VISIBLE);
            mTransitionRecycleView.setVisibility(VISIBLE);
        } else {
            mAddButton.setVisibility(GONE);
            mTransitionRecycleView.setVisibility(INVISIBLE);
        }
        // state of display no show duration
        if (TextUtils.equals(mState, STATE_DISPLAY)) {
            mTxtDuration.setVisibility(INVISIBLE);
        }
        bindPipSelectorView();
    }

    public void bindAndRefreshChildView(ITimeline iTimeline) {
        mEditTrailView.setPixelPerMicrosecond(mPixelPerMicrosecond);
        mEditTrailView.setTimeLine(iTimeline);
        mEditTrailView.initSelectView(ScreenUtils.getDisplayScreenWidth());
    }

    public void setEditTrailViewVisibility(int visibility) {
        if (mEditTrailView != null) {
            mEditTrailView.setVisibility(visibility);
        }
    }

    public void setShowThumbnailSpreadVisibility(boolean visibility) {
        mNeedShowThumbnailSpread = visibility;
    }

    public void refreshTimelineCover(int position) {
        if (mEditorTimeCoverAdapter != null) {
            mEditorTimeCoverAdapter.refresh(position);
        }
    }

    public void changeTimelineState(String state) {
        mState = state;
        initTimeSpanParentLayout();
        if (TextUtils.equals(mState, STATE_PREVIEW) && !mIsSpeedMode) {
            mAddButton.setVisibility(VISIBLE);
            mTransitionRecycleView.setVisibility(VISIBLE);
        } else {
            mAddButton.setVisibility(GONE);
            mTransitionRecycleView.setVisibility(INVISIBLE);
        }

        refreshAllEditTimeSpan();
    }

    public void changeViewIntoSpeedMode(boolean isSpeedMode) {
        mIsSpeedMode = isSpeedMode;
        if (isSpeedMode) {
            mAddButton.setVisibility(INVISIBLE);
            mTransitionRecycleView.setVisibility(INVISIBLE);
        } else {
            mAddButton.setVisibility(VISIBLE);
            mTransitionRecycleView.setVisibility(VISIBLE);
        }
    }

    public String getTimelineState() {
        return mState;
    }

    private void initDurationText() {
        IVideoClip videoClip = getClipByIndex(mSelectClipIndex);
        if (videoClip == null) {
            GLog.e(TAG, "initDurationText got null clip:" + mSelectClipIndex);
        } else {

            mTxtDuration.setText(getClipDurationText(videoClip));
        }
        moveDurationText();
    }

    private void bindPipSelectorView() {
        mPipSelectorView.initData(mTimeline.getDuration(), mPixelPerMicrosecond);
        mPipSelectorView.setScreenWidth(ScreenUtils.getDisplayScreenWidth());
        mPipSelectorView.setTimeLine(mTimeline);
    }

    public void setIgnoreCurrentIndexChange(boolean ignoreCurrentIndexChange) {
        mIgnoreCurrentIndexChange = ignoreCurrentIndexChange;
    }

    private void initTimelineCover(Context context) {
        EditorTimeCoverAdapter editorTimeCoverAdapter = (EditorTimeCoverAdapter) mCoverRecycleView.getAdapter();
        if (editorTimeCoverAdapter == null) {
            LinearLayoutManager layout = new LinearLayoutManager(context);
            layout.setOrientation(LinearLayoutManager.HORIZONTAL);
            mCoverRecycleView.setLayoutManager(layout);
            mCoverRecycleView.setItemAnimator(null);
            refreshTimelineCoverAdapter();
        } else {
            EditorTimeCoverAdapter.MuteButtonInfo info = editorTimeCoverAdapter.getMuteButtonInfo();
            refreshTimelineCoverAdapter();
            if ((mEditorTimeCoverAdapter != null) && (info != null)) {
                mEditorTimeCoverAdapter.setMuteIconRes(info.mMuteText, info.mUnmuteColor,
                        info.mMuteColor, info.mUnmuteDrawable, info.mMuteDrawable);
                mEditorTimeCoverAdapter.setMuteButtonState(info.mIsVideoMute);
            }
            mCoverRecycleView.scrollBy(mMultiThumbnailSequenceView.getScrollX(), 0);
        }
    }

    private void refreshTimelineCoverAdapter() {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            return;
        }
        if (TextUtils.equals(mState, STATE_DISPLAY)) {
            mEditorTimeCoverAdapter = new EditorTimeCoverAdapter(getContext(), mMultiThumbnailSequenceView.getStartPadding(),
                    mMultiThumbnailSequenceView.getEndPadding(), videoTrack.getClipList(), mPixelPerMicrosecond);
        } else {
            mEditorTimeCoverAdapter = new EditorTimeCoverWithTailAdapter(getContext(), mMultiThumbnailSequenceView.getStartPadding(),
                    mMultiThumbnailSequenceView.getEndPadding(), videoTrack.getClipList(), mPixelPerMicrosecond);
        }
        mEditorTimeCoverAdapter.showThumbnailSpread(mNeedShowThumbnailSpread);
        mEditorTimeCoverAdapter.setMinThumbnailWidth(getMinClipShownWidth());
        mCoverRecycleView.setAdapter(mEditorTimeCoverAdapter);
        mEditorTimeCoverAdapter.setHaveTailClip(hasExistTailClip());
        mEditorTimeCoverAdapter.setOnMuteClickListener(new EditorTimeCoverAdapter.OnMuteClickListener() {
            @Override
            public void onMuteIconClick(boolean isMuteHandle) {
                if (mMuteClickListener != null) {
                    mMuteClickListener.onMuteIconClick(isMuteHandle);
                }
            }
        });

        if (!TextUtils.equals(mState, STATE_DISPLAY)) {
            ((EditorTimeCoverWithTailAdapter) mEditorTimeCoverAdapter).setTimelineViewState(mState);
            ((EditorTimeCoverWithTailAdapter) mEditorTimeCoverAdapter).onItemScrollListener(new EditorTimeCoverWithTailAdapter.OnItemScrollListener() {
                @Override
                public void onItemScroll(int scrollX) {
                    if (mMultiThumbnailSequenceView != null) {
                        mScrollPressed = true;
                        mMultiThumbnailSequenceView.scrollBy(scrollX, 0);
                    }
                }
            });
        }
    }

    public int getMultiThumbnailSequenceViewScrollX() {
        return mMultiThumbnailSequenceView.getScrollX();
    }

    /**
     * speed change make timeline is smaller than before position will touch off mMultiThumbnailSequenceView
     * on OnScrollChangeListener. so need use a special method.
     */
    public void refreshTimelineForEditorFromSpeedChange(long position) {
        mFromTrimSpeedChange = true;
        refreshTimelineForEditor(position);
        mFromTrimSpeedChange = false;
        mSpanLeftTextState = true;
    }

    public void refreshTimelineForEditor(long position) {
        if ((mState == null) || TimelineUtil.checkTimelineNull(mTimeline)) {
            GLog.e(TAG, "timeline view is not init");
            return;
        }
        if (!mDragEnd) {
            initTimelineEditor(mSequenceLeftPadding, mMultiThumbnailSequenceView.getEndPadding(), mTimeline, mState);
            refreshAllEditTimeSpan();

        }
    }

    public void setAllOutPoint() {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "setAllOutPoint: videoTrack is null");
            return;
        }
        mAllOutPoint.clear();
        List<IVideoClip> videoClipList = videoTrack.getClipList();
        IVideoClip videoClip = null;
        for (int i = 0; i < videoClipList.size(); i++) {
            videoClip = videoClipList.get(i);
            if (!videoClip.haveBindObj()) {
                GLog.d(TAG, "setAllOutPoint: videoClip have not bindNvsObj");
                continue;
            }
            mAllOutPoint.add(videoClip.getOutPoint() - videoClip.getInPoint());
        }
    }

    private void refreshTimelineTransitionAdapter() {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "refreshTimelineTransitionAdapter: videoTrack is null");
            return;
        }
        int startPadding = mMultiThumbnailSequenceView.getStartPadding();
        mEditorTimeTransitionAdapter = new EditorTimeTransitionAdapter(getContext(), startPadding,
                mMultiThumbnailSequenceView.getEndPadding(), videoTrack.getClipList(), videoTrack, mClipPositionList);
        mTransitionRecycleView.setAdapter(mEditorTimeTransitionAdapter);
    }

    private void addSwitchLayout() {
        EditorTimeTransitionAdapter editorTimeTransitionAdapter = (EditorTimeTransitionAdapter) mTransitionRecycleView.getAdapter();
        if (editorTimeTransitionAdapter == null) {
            LinearLayoutManager layout = new LinearLayoutManager(getContext());
            layout.setOrientation(LinearLayoutManager.HORIZONTAL);
            mTransitionRecycleView.setLayoutManager(layout);
            mTransitionRecycleView.setItemAnimator(null);
            refreshTimelineTransitionAdapter();
        } else {
            refreshTimelineTransitionAdapter();
            mTransitionRecycleView.scrollBy(mMultiThumbnailSequenceView.getScrollX(), 0);
        }
    }

    private void initTimeSpanParentLayout() {
        if (TimelineUtil.checkTimelineNull(mTimeline)) {
            GLog.e(TAG, "initTimeSpanParentLayout: mTimeline is null or videoTrack.size or clips.size is 0");
            return;
        }
        View marginView = findViewById(R.id.edit_timeline_view_margin_view);
        int marginViewWidth = mSequenceLeftPadding - mHandWidth;
        if (TextUtils.equals(mState, STATE_PREVIEW)) {
            marginViewWidth = mSequenceLeftPadding;
        }
        LinearLayout.LayoutParams paddingParams = new LinearLayout.LayoutParams(marginViewWidth, LinearLayout.LayoutParams.MATCH_PARENT);
        marginView.setLayoutParams(paddingParams);
        int totalWidth = 0;
        if (TextUtils.equals(mState, STATE_PREVIEW)) {
            totalWidth = timelinePositionToLength(mTimeline.getDuration());
        } else if (TextUtils.equals(mState, STATE_EDITOR)) {
            totalWidth = timelinePositionToLength(mTimeline.getDuration()) + HAND_TOTAL * mHandWidth;
        } else {
            // preview state no set
        }
        LinearLayout.LayoutParams timeSpanRelativeParams = new LinearLayout.LayoutParams(totalWidth, LayoutParams.MATCH_PARENT);
        mTimeSpanRelativeLayout.setLayoutParams(timeSpanRelativeParams);
    }

    private ArrayList<ThumbnailSequenceDesc> getNewThumbNailArray(IVideoClip videoClip,
                                                                  ArrayList<ThumbnailSequenceDesc> thumbnailSequenceDescArrayList,
                                                                  long changeValue, int index, boolean isLeftHand, boolean isImage) {
        ArrayList<ThumbnailSequenceDesc> newArray = new ArrayList<>();
        for (ThumbnailSequenceDesc thumbnailSequenceDesc : thumbnailSequenceDescArrayList) {
            newArray.add(thumbnailSequenceDesc);
        }
        ThumbnailSequenceDesc thumbnailSequenceDesc = newArray.get(index);
        if (isLeftHand) {
            if (isImage) {
                newArray = getNewThumbNailArrayOnOutChange(videoClip, newArray, thumbnailSequenceDesc, -changeValue, index);
            } else {
                newArray = getNewThumbNailArrayOnInChange(videoClip, newArray, thumbnailSequenceDesc, changeValue, index);
            }
        } else {
            newArray = getNewThumbNailArrayOnOutChange(videoClip, newArray, thumbnailSequenceDesc, changeValue, index);
        }
        return newArray;
    }

    private ArrayList<ThumbnailSequenceDesc> getNewThumbNailArrayOnInChange(IVideoClip videoClip, ArrayList<ThumbnailSequenceDesc> newArray,
                                                                            ThumbnailSequenceDesc thumbnailSequenceDesc, long changeValue,
                                                                            int index) {
        long adjustedChangeValue = changeValue;
        ArrayList<ThumbnailSequenceDesc> adjustedNewArray = newArray;
        if (thumbnailSequenceDesc.outPoint - changeValue - thumbnailSequenceDesc.inPoint - MIN_DURATION < 0) {
            changeValue = thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint - MIN_DURATION;
        }
        thumbnailSequenceDesc.trimIn += changeValue * videoClip.getSpeed();
        thumbnailSequenceDesc.trimIn = Math.max(thumbnailSequenceDesc.trimIn, 0);
        thumbnailSequenceDesc.outPoint -= adjustedChangeValue;
        adjustedNewArray.set(index, thumbnailSequenceDesc);
        GLog.d(TAG,
                "getNewThumbNailArrayOnInChange: inPoint=" + thumbnailSequenceDesc.inPoint + " outPoint: " + thumbnailSequenceDesc.outPoint
                        + "  trimIn  " + thumbnailSequenceDesc.trimIn + "  trimOut: " + thumbnailSequenceDesc.trimOut);
        adjustedNewArray = changeInAndOutWhenAheadClipChange(adjustedNewArray, adjustedChangeValue, index, true);
        return adjustedNewArray;
    }

    private ArrayList<ThumbnailSequenceDesc> getNewThumbNailArrayOnOutChange(IVideoClip videoClip,
                                                                             ArrayList<ThumbnailSequenceDesc> newArray,
                                                                             ThumbnailSequenceDesc thumbnailSequenceDesc,
                                                                             long changeValue, int index) {
        if (thumbnailSequenceDesc.outPoint + changeValue - thumbnailSequenceDesc.inPoint - MIN_DURATION < 0) {
            changeValue = thumbnailSequenceDesc.inPoint + MIN_DURATION - thumbnailSequenceDesc.outPoint;
        }
        thumbnailSequenceDesc.trimOut += changeValue * videoClip.getSpeed();
        thumbnailSequenceDesc.outPoint += changeValue;
        newArray.set(index, thumbnailSequenceDesc);
        GLog.d(TAG, "getNewThumbNailArrayOnOutChange: inPoint="
                + thumbnailSequenceDesc.inPoint + " outPoint: " + thumbnailSequenceDesc.outPoint
                + "  trimIn  " + thumbnailSequenceDesc.trimIn + "  trimOut: " + thumbnailSequenceDesc.trimOut);
        newArray = changeInAndOutWhenAheadClipChange(newArray, changeValue, index, false);
        return newArray;
    }

    private ArrayList<ThumbnailSequenceDesc> changeInAndOutWhenAheadClipChange(ArrayList<ThumbnailSequenceDesc> newArray,
                                                                               long changeValue, int index, boolean isLeftHand) {
        long pointChange = 0L;
        if (isLeftHand) {
            pointChange = -changeValue;
        } else {
            pointChange = changeValue;
        }
        ThumbnailSequenceDesc thumbnailSequenceDescAfter = null;
        for (int i = index + 1; i < newArray.size(); i++) {
            thumbnailSequenceDescAfter = newArray.get(i);
            thumbnailSequenceDescAfter.inPoint += pointChange;
            thumbnailSequenceDescAfter.outPoint += pointChange;
            newArray.set(i, thumbnailSequenceDescAfter);
            GLog.d(TAG, "changeInAndOutWhenAheadClipChange: " + thumbnailSequenceDescAfter.inPoint
                    + "   " + thumbnailSequenceDescAfter.outPoint);
        }
        return newArray;
    }

    private ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> getThumbNailArray() {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "getThumbNailArray: video Track is null");
            return null;
        }
        int clipCount = videoTrack.getClipList().size();
        if (clipCount == 0) {
            GLog.e(TAG, "getThumbNailArray: clipCount is 0");
            return null;
        }
        ArrayList<ThumbnailSequenceDesc> sequenceDescArray = new ArrayList<>();
        for (int index = 0; index < clipCount; ++index) {
            IVideoClip videoClip = videoTrack.getClipList().get(index);
            if (videoClip == null) {
                GLog.e(TAG, "getThumbNailArray: videoClip is null!");
                continue;
            }
            if (!videoClip.haveBindObj()) {
                GLog.e(TAG, "getThumbNailArray: videoClip have not bindNvsObj");
                continue;
            }
            ThumbnailSequenceDesc sequenceDescs = new ThumbnailSequenceDesc();
            sequenceDescs.mediaFilePath = videoClip.getFilePath();
            sequenceDescs.trimIn = videoClip.getTrimIn();
            sequenceDescs.trimOut = videoClip.getTrimOut();
            sequenceDescs.inPoint = videoClip.getInPoint();
            sequenceDescs.outPoint = videoClip.getOutPoint();
            GLog.d(TAG, "getThumbNailArray: trimIn" + videoClip.getTrimIn() + " InPoint: " + videoClip.getInPoint()
                    + "  OutPoint: " + videoClip.getOutPoint() + " srcFilePath:  " + videoClip.getSrcFilePath());
            sequenceDescs.stillImageHint = false;
            sequenceDescs.onlyDecodeKeyFrame = true;
            sequenceDescArray.add(sequenceDescs);
        }
        return sequenceDescArray;
    }

    public void freshDurationTextOnHandDown() {
        long duration = 0;
        if ((mThumbnailSequenceDescArrayListOnHandDown != null)
                && (mThumbnailSequenceDescArrayListOnHandDown.size() > mSelectClipIndex)
                && (mSelectClipIndex >= 0)) {
            duration = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex).outPoint
                    - mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex).inPoint;
        }

        String durationText = "";
        IVideoClip currentClip = getCurrentClip();
        if (currentClip.getClipType() != IVideoClip.ClipType.TYPE_PLACE_HOLDER_CLIP) {
            if (duration == 0) {
                duration = currentClip.getDuration();
            }
            durationText = getDurationText(duration);
        }
        mTxtDuration.setText(durationText);
    }

    private String getClipDurationText(IVideoClip videoClip) {
        String durationText = "";
        if (videoClip.getClipType() != IVideoClip.ClipType.TYPE_PLACE_HOLDER_CLIP) {
            durationText = getDurationText(videoClip.getDuration());
        }
        return durationText;
    }

    private void refreshPreviewTimeSpanChanged(int before, int after) {
        if ((before < 0) || (after < 0)) {
            GLog.e(TAG, "index of before or after is invalid.before=" + before + " after=" + after);
            return;
        }
        mEditorTimeCoverAdapter.refresh(before, after);
        IVideoClip videoClip = getClipByIndex(after);
        if (TextUtils.equals(mState, STATE_EDITOR)) {
            if ((videoClip != null) && (mTimeSpanView != null)) {
                Pair<Integer, Integer> currentPosition = getClipPosition(after);
                if (currentPosition != null) {
                    mTimeSpanView.setClipPosition(currentPosition.first, currentPosition.second);
                    mTimeSpanView.setAllowTrim(true);
                } else {
                    GLog.e(TAG, "refreshPreviewTimeSpanChanged currentPosition got null");
                }
            }
        }
        if (videoClip != null) {
            mTxtDuration.setText(getClipDurationText(videoClip));
        }
    }

    private void refreshAllEditTimeSpan() {
        refreshClipPositions();
        deleteAllViews();
        initTimelineCover(getContext());
        if (!TextUtils.equals(mState, STATE_DISPLAY)) {
            if (mSelectClipIndex >= 0) {
                if (mEditorTimeCoverAdapter != null) {
                    mEditorTimeCoverAdapter.refresh(mSelectClipIndex);
                }
            }
        }
        if (TextUtils.equals(mState, STATE_PREVIEW)) {
            addSwitchLayout();
            initDurationText();
        } else if (TextUtils.equals(mState, STATE_EDITOR)) {
            addEditorPreviewTimeSpan(mSelectClipIndex);
        } else {
            // only to preview
        }
    }

    public boolean hasExistTailClip() {
        if (mTimeline == null) {
            GLog.e(TAG, "hasExistTailClip: timeline is null");
            return false;
        }
        IVideoTrack videoTrack = mTimeline.getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "hasExistTailClip: video track is null");
            return false;
        }
        List<IVideoClip> clips = videoTrack.getClipList();
        if ((null == clips) || (clips.size() == 0)) {
            GLog.e(TAG, "hasExistTailClip: clips == null");
            return false;
        }
        return false;
    }

    private void refreshClipPositions() {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "refreshClipPositions, current video track is null:");
            return;
        }
        GLog.d(TAG, "refreshClipPositions start");
        mClipPositionList.clear();
        int clipCount = videoTrack.getClipCount();
        int startPosition = 0;
        int endPosition = 0;
        for (int i = 0; i < clipCount; i++) {
            IClip clip = videoTrack.getClip(i);
            long duration = (clip == null) ? 0 : clip.getDuration();
            endPosition = startPosition + durationToShownLength(duration);
            mClipPositionList.add(new Pair(startPosition, endPosition));
            startPosition = endPosition;
        }

        ViewGroup.LayoutParams layoutParams = mTimeSpanRelativeLayout.getLayoutParams();
        layoutParams.width = endPosition + HAND_TOTAL * mHandWidth;
        mTimeSpanRelativeLayout.setLayoutParams(layoutParams);
    }

    private void addEditorPreviewTimeSpan(int clipIndex) {
        EditTimelineSpanView timelineTimeSpan = addEditTimelineSpanView(clipIndex);
        if (timelineTimeSpan == null) {
            return;
        }
        timelineTimeSpan.setViewState();
        mTimeSpanRelativeLayout.addView(timelineTimeSpan);
        mTimeSpanView = timelineTimeSpan;
    }

    private String getDurationText(long duration) {
        String result = "";
        double secondDuration = (double) duration / TIME_SECOND;
        if (secondDuration < MINUTES_60) {
            result = String.format(Locale.getDefault(),"%.1fs", secondDuration);
        } else {
            result = splitLongMilliTime(duration);
        }
        return result;
    }

    private String splitLongMilliTime(long microSecond) {
        if (mDurationStringBuilder == null) {
            mDurationStringBuilder = new StringBuilder();
        } else {
            mDurationStringBuilder.setLength(0);
        }
        long duration = microSecond / TIME_SECOND;
        long hours = duration / SIX_MINUTES_3600;
        long minutes = duration % SIX_MINUTES_3600 / MINUTES_60;
        long seconds = duration % MINUTES_60;
        if (hours != 0) {
            mDurationStringBuilder.append(hours);
            mDurationStringBuilder.append(":");
        }
        if (minutes != 0) {
            if ((minutes < DURATION_TEXT_REPAIR_VALUE)) {
                mDurationStringBuilder.append(0);
                mDurationStringBuilder.append(minutes);
                mDurationStringBuilder.append(":");
            } else {
                mDurationStringBuilder.append(minutes);
                mDurationStringBuilder.append(":");
            }
        }
        if (seconds < DURATION_TEXT_REPAIR_VALUE) {
            mDurationStringBuilder.append(0);
        }
        mDurationStringBuilder.append(seconds);
        return mDurationStringBuilder.toString();
    }

    private int getMinClipShownWidth() {
        int minWidth = duringToLength(mMinShowLengthDuration);
        return minWidth;
    }

    private Pair<Integer, Integer> getClipPosition(int index) {
        if ((index >= mClipPositionList.size()) || (index < 0)) {
            return null;
        }
        return mClipPositionList.get(index);
    }

    private EditTimelineSpanView addEditTimelineSpanView(int clipIndex) {
        IVideoClip videoClip = getClipByIndex(clipIndex);
        if (videoClip == null) {
            return null;
        }
        EditTimelineSpanView timelineTimeSpan = new EditTimelineSpanView(getContext(), true);
        timelineTimeSpan.setHandleWidth(mHandWidth);
        return timelineTimeSpan;
    }

    private boolean setupTimelineSpanView(EditTimelineSpanView timelineTimeSpan, int clipIndex, IVideoClip videoClip) {
        Pair<Integer, Integer> clipPosition = getClipPosition(clipIndex);
        if (clipPosition == null) {
            GLog.e(TAG, "addEditTimelineSpanView clip is null:" + clipIndex);
            return false;
        }

        timelineTimeSpan.setClipPosition(clipPosition.first, clipPosition.second);
        mTxtDuration.setText(getClipDurationText(videoClip));
        timelineTimeSpan.setAllowTrim(true);
        return true;
    }

    private void setupTimelineSpanListener(EditTimelineSpanView timelineTimeSpan) {
        timelineTimeSpan.setOnChangeListener(new EditTimelineSpanView.OnHandChangeListener() {

            @Override
            public void onLeftHandChange(boolean isDown, boolean isDragEnd, int dx, boolean isLeftBorder, boolean center, int addParentLength) {
                handleLeftHandChange(isDown, isDragEnd, dx, isLeftBorder, center, addParentLength);
            }

            @Override
            public void onRightHandChange(boolean isDown, boolean isDragEnd, int dx, boolean inMiddle,
                                          boolean isRightBorder, boolean inMinDuration) {
                handleRightHandChange(isDown, isDragEnd, dx, inMiddle, isRightBorder, inMinDuration);
            }

            @Override
            public void onNeedScrollParentLinearLayout(int dx) {
                mLinearContainer.scrollBy(dx, 0);
            }
        });
    }

    private void handleLeftHandChange(boolean isDown, boolean isDragEnd, int dx, boolean isLeftBorder, boolean center,
                                      int addParentLength) {
        mHasDragEnd = isDragEnd;
        if (isDown) {
            handleLeftHandDown(addParentLength);
        } else if (isDragEnd) {
            handleLeftHandDragEnd();
        } else {
            onLeftHandMove(dx, center, isLeftBorder);
        }
    }

    private void handleLeftHandDown(int addParentLength) {
        mAddParentLengthOnLeftHandDown = addParentLength;
        if (mAddParentLengthOnLeftHandDown > 0) {
            LinearLayout.LayoutParams timeSpanRelativeParams =
                    (LinearLayout.LayoutParams) mTimeSpanRelativeLayout.getLayoutParams();
            timeSpanRelativeParams.width = timeSpanRelativeParams.width + mAddParentLengthOnLeftHandDown;
            mTimeSpanRelativeLayout.setLayoutParams(timeSpanRelativeParams);
        }
        mDownLeftSelectClipViewLeft = getTimelineTimeSpan().getLeft();
        mThumbnailSequenceDescArrayListOnHandDown = mMultiThumbnailSequenceView.getThumbnailSequenceDescArray();
        IVideoClip videoClip = getCurrentClip();
        if (videoClip == null) {
            return;
        }

        if (mAvFileInfos[mSelectClipIndex] != null) {
            ThumbnailSequenceDesc thumbnailSequenceDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);
            setMaxLeftValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
        }
    }

    private void handleLeftHandDragEnd() {
        if (mAddParentLengthOnLeftHandDown > 0) {
            LinearLayout.LayoutParams timeSpanRelativeParams =
                    (LinearLayout.LayoutParams) mTimeSpanRelativeLayout.getLayoutParams();
            timeSpanRelativeParams.width = timeSpanRelativeParams.width - mAddParentLengthOnLeftHandDown;
            mTimeSpanRelativeLayout.setLayoutParams(timeSpanRelativeParams);
        }
        mDragEnd = true;
        seekMultiThumbnailSequenceViewLEFT();
        mDragEnd = false;
    }

    private void handleRightHandChange(boolean isDown, boolean isDragEnd, int dx, boolean inMiddle,
                                       boolean isRightBorder, boolean inMinDuration) {
        mHasDragEnd = isDragEnd;
        if (isDown) {
            handleRightHandDown();
        } else if (isDragEnd) {
            handleRightHandDragEnd();
        } else {
            onRightHandMove(dx, inMiddle, isRightBorder, inMinDuration);
        }
    }

    private void handleRightHandDown() {
        mThumbnailSequenceDescArrayListOnHandDown = mMultiThumbnailSequenceView.getThumbnailSequenceDescArray();
        IVideoClip videoClip = getCurrentClip();
        if (mAvFileInfos[mSelectClipIndex] != null) {
            ThumbnailSequenceDesc thumbnailSequenceDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);
            setMaxRightValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
        }
    }

    private void handleRightHandDragEnd() {
        mDragEnd = true;
        seekMultiThumbnailSequenceViewRight();
        mDragEnd = false;
        mSpanLeftTextState = true;
    }

    private int checkRightHandDx(int dx, IVideoClip videoClip) {
        ThumbnailSequenceDesc currentClipDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);
        long changedDuration = lengthToDuring(dx);
        long outPointAfterDx = changedDuration + currentClipDesc.outPoint;
        long resultDuration = outPointAfterDx - currentClipDesc.inPoint;
        if (resultDuration < MIN_DURATION) {
            long durationCanChange = MIN_DURATION - (currentClipDesc.outPoint - currentClipDesc.inPoint);
            return duringToLength(durationCanChange);
        }

        if (videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
            return dx;
        }

        long trimOutAfterDx = (long) (currentClipDesc.trimOut + changedDuration * videoClip.getSpeed());
        if (trimOutAfterDx > mAvFileInfos[mSelectClipIndex]) {
            long durationCanChange = (long) ((mAvFileInfos[mSelectClipIndex] - currentClipDesc.trimOut) / videoClip.getSpeed());
            return duringToLength(durationCanChange);
        }
        return dx;
    }

    private int computeScrollDx(long currentDuration, long oldDuration) {
        long adjustedCurrentDuration = currentDuration;
        long adjustedOldDuration = oldDuration;
        if (adjustedCurrentDuration < mMinShowLengthDuration) {
            adjustedCurrentDuration = mMinShowLengthDuration;
        }
        if (adjustedOldDuration < mMinShowLengthDuration) {
            adjustedOldDuration = mMinShowLengthDuration;
        }
        return -duringToLength(adjustedCurrentDuration - adjustedOldDuration);
    }

    private int checkLeftHandDx(int dx, IVideoClip videoClip) {

        // we always change out point for image clip
        if (videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
            return checkRightHandDx(dx, videoClip);
        }

        long changedDuration = lengthToDuring(dx);
        ThumbnailSequenceDesc currentClipDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);

        long trimOutAfterDx = (long) (currentClipDesc.trimIn + changedDuration * videoClip.getSpeed());
        if (trimOutAfterDx < 0) {
            return duringToLength((long) ((0 - currentClipDesc.trimIn) * videoClip.getSpeed()));
        }

        long inPointAfterDx = changedDuration + currentClipDesc.inPoint;
        long resultDuration = currentClipDesc.outPoint - inPointAfterDx;
        if (resultDuration < MIN_DURATION) {
            long durationCanChange = (currentClipDesc.outPoint - currentClipDesc.inPoint) - MIN_DURATION;
            return duringToLength(durationCanChange);
        }
        return dx;
    }

    private void onLeftHandMove(int dx, boolean inMiddle, boolean isLeftBorder) {
        long oldDuration = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex).getDuration();
        IVideoClip videoClip = getCurrentClip();
        int adjustedDx = checkLeftHandDx(dx, videoClip);
        boolean isImage = videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE;
        mThumbnailSequenceDescArrayListOnHandDown = getNewThumbNailArray(videoClip,
                mThumbnailSequenceDescArrayListOnHandDown, lengthToDuring(adjustedDx), mSelectClipIndex, true, isImage);

        refreshMultiThumbnailSequenceView(mThumbnailSequenceDescArrayListOnHandDown);
        if (isImage) {
            //image trimIn always is 0,so need change trimOut
            trimOutPointForClipOnThumbnailSequenceView(lengthToDuring(adjustedDx), true);
        } else {
            trimInPointForClipOnThumbnailSequenceView(lengthToDuring(adjustedDx));
        }
        refreshCoverView();

        boolean spanGoLeftThanStart = (getTimelineTimeSpan().getLeft() <= mDownLeftSelectClipViewLeft + LIMIT_INTERCEPT_MOVE);

        long currentDuration = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex).getDuration();
        int scrollDx = computeScrollDx(currentDuration, oldDuration);
        if (!isLeftBorder) {
            if (scrollDx < 0) {
                multiThumbnailSequenceViewScrollTo(scrollDx);
                getTimelineTimeSpan().leftOnDxChange(scrollDx, spanGoLeftThanStart);
            } else if ((scrollDx > 0) && (getTimelineTimeSpan().getRightHandX() != mMiddleLimit)) {
                multiThumbnailSequenceViewScrollTo(scrollDx);
            }
        }
        moveSpanContainerForHandMove();
        freshDurationTextOnHandDown();
        relayoutPipSelector(scrollDx, true);
    }

    private void relayoutPipSelector(int scrollDx, boolean isLeftBorder) {
        if (mPipSelectorView != null) {
            mPipSelectorView.layoutTabs(scrollDx, isLeftBorder);
        }
    }

    private void moveSpanContainerForHandMove() {
        int width = mMultiThumbnailSequenceView.getScrollX() + getWidth();

        LinearLayout.LayoutParams timeSpanRelativeParams = (LinearLayout.LayoutParams) mTimeSpanRelativeLayout.getLayoutParams();
        timeSpanRelativeParams.width = width;
        mTimeSpanRelativeLayout.setLayoutParams(timeSpanRelativeParams);
    }

    private void correctSpanWidthForByThumbnail(ThumbnailSequenceDesc thumbnailSequenceDesc) {
        if (thumbnailSequenceDesc == null) {
            return;
        }
        // we have to correct span width by thumbnail width,
        // because the calculation have limit error, the error will be large if continue operation
        long duration = thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint;
        int spanWidth = durationToShownLength(duration) + 2 * mHandWidth;
        mTimeSpanView.setLayoutWidth(spanWidth);
    }

    private void onRightHandMove(int dx, boolean inMiddle,
                                 boolean isRightBorder, boolean inMinDuration) {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "onRightHandChange: videoTrack is null");
            return;
        }
        IVideoClip videoClip = getCurrentClip();
        dx = checkRightHandDx(dx, videoClip);
        boolean isImage = videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE;
        mThumbnailSequenceDescArrayListOnHandDown = getNewThumbNailArray(videoClip, mThumbnailSequenceDescArrayListOnHandDown,
                lengthToDuring(dx), mSelectClipIndex, false, isImage);

        correctSpanWidthForByThumbnail(mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex));

        GLog.d(TAG, "onChange: " + mThumbnailSequenceDescArrayListOnHandDown.get(0).outPoint + "     " + mThumbnailSequenceDescArrayListOnHandDown.get(0).trimOut);
        refreshMultiThumbnailSequenceView(mThumbnailSequenceDescArrayListOnHandDown);

        trimOutPointForClipOnThumbnailSequenceView(lengthToDuring(dx), false);
        if (dx > 0) {
            refreshCoverView();
        }
        if (inMiddle && (videoTrack.getClipList().size() > 1)
                && (mSelectClipIndex != videoTrack.getClipList().size() - 1)) {
            mMultiThumbnailSequenceView.scrollTo(mMultiThumbnailSequenceView.getScrollX() + dx, 0);
        }
        if (isRightBorder) {
            mMultiThumbnailSequenceView.scrollTo(mMultiThumbnailSequenceView.getScrollX() + dx, 0);
        }
        moveSpanContainerForHandMove();
        if (dx < 0) {
            refreshCoverView();
        }
        freshDurationTextOnHandDown();
        relayoutPipSelector(dx, false);
    }

    private boolean needRefreshThumbnail(ThumbnailSequenceDesc thumbnailSequenceDesc) {
        if (thumbnailSequenceDesc == null) {
            return true;
        }
        long duration = thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint;
        if (duration >= mMinShowLengthDuration) {
            return true;
        } else {
            return false;
        }
    }

    private void refreshCoverView() {
        if ((mThumbnailSequenceDescArrayListOnHandDown != null)
                && (mThumbnailSequenceDescArrayListOnHandDown.size() > mSelectClipIndex)) {
            long duration = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex).outPoint
                    - mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex).inPoint;
            int length = durationToShownLength(duration);
            mEditorTimeCoverAdapter.refreshItemLength(length, mSelectClipIndex);
        }
    }

    public void refreshMultiThumbnailSequenceView(ArrayList<ThumbnailSequenceDesc> sequenceDesc) {
        if ((mSelectClipIndex < 0) || (mSelectClipIndex >= sequenceDesc.size())) {
            GLog.e(TAG, "mSelectClipIndex is invalid!");
            return;
        }
        if (needRefreshThumbnail(sequenceDesc.get(mSelectClipIndex))) {
            mMultiThumbnailSequenceView.updatePortionThumbnails(sequenceDesc);
        }
    }

    private IVideoClip getClipByIndex(int index) {
        IVideoTrack iVideoTrack = getCurrentVideoTrack();
        if (iVideoTrack == null) {
            return null;
        }
        int clipSize = iVideoTrack.getClipList().size();
        if ((index < 0) || (index >= clipSize)) {
            return null;
        }
        return iVideoTrack.getClipList().get(index);
    }

    public IVideoClip getCurrentClip() {
        IVideoTrack iVideoTrack = getCurrentVideoTrack();
        if (iVideoTrack == null) {
            GLog.e(TAG, "getCurrentClip: videoTrack is null");
            return null;
        }
        if (mSelectClipIndex < 0) {
            mSelectClipIndex = 0;
        } else if (mSelectClipIndex >= iVideoTrack.getClipList().size()) {
            mSelectClipIndex = iVideoTrack.getClipList().size() - 1;
        }
        return iVideoTrack.getClipList().get(mSelectClipIndex);
    }

    private void multiThumbnailSequenceViewScrollTo(int dx) {
        if (getTimelineTimeSpan() == null) {
            return;
        }
        if (!getTimelineTimeSpan().isOnMiddleState()) {
            int canScroll = mMultiThumbnailSequenceView.getScrollX() - dx;
            if (canScroll < 0) {
                canScroll = -mMultiThumbnailSequenceView.getScrollX();
            }
            mMultiThumbnailSequenceView.scrollTo(canScroll, 0);
        } else {
            if (getCurrentClip() == null) {
                return;
            }
            mMultiThumbnailSequenceView.scrollTo(timelinePositionToLength(getCurrentClip().getInPoint()), 0);
        }
    }

    private void trimOutPointForClipOnThumbnailSequenceView(long trimOutPoint, boolean isImage) {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "trimOutPointForClipOnThumbnailSequenceView: videoTrack is null");
            return;
        }
        IVideoClip videoClip = videoTrack.getClipList().get(mSelectClipIndex);
        if (videoClip == null) {
            return;
        }
        ThumbnailSequenceDesc thumbnailSequenceDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);
        if (isImage) {
            setMaxLeftValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
        } else {
            setMaxRightValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
        }
    }

    private void refreshMaxLengthForTwoHand(IVideoClip videoClip) {
        ThumbnailSequenceDesc thumbnailSequenceDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);
        setMaxRightValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
        setMaxLeftValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
    }

    private void setMaxLeftValue(EditTimelineSpanView timelineTimeSpan, IVideoClip videoClip, ThumbnailSequenceDesc thumbnailSequenceDesc) {

        if ((mAvFileInfos[mSelectClipIndex] != null) && (videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE)) {
            long maxImageResidueDuration = (mAvFileInfos[mSelectClipIndex] - (thumbnailSequenceDesc.trimOut - thumbnailSequenceDesc.trimIn)) / 2;
            timelineTimeSpan.setMaxLeftToLeft(duringToLength(maxImageResidueDuration));
        } else {
            timelineTimeSpan.setMaxLeftToLeft(duringToLength((long) (thumbnailSequenceDesc.trimIn / videoClip.getSpeed())));
        }
        timelineTimeSpan.setMaxLeftToRight(duringToLength((thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint) - mMinShowLengthDuration));
    }

    private void setMaxRightValue(EditTimelineSpanView timelineTimeSpan, IVideoClip videoClip,
                                  ThumbnailSequenceDesc thumbnailSequenceDesc) {
        if (thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint < mMinShowLengthDuration) {
            timelineTimeSpan.setMaxLeftToLeft(0);
            timelineTimeSpan.setMaxLeftToRight(0);
            timelineTimeSpan.setMaxRightToRight(0);
            timelineTimeSpan.setMaxRightToLeft(0);
            return;
        }

        if (mAvFileInfos[mSelectClipIndex] != null) {
            if (videoClip.getVideoType() == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
                long maxImageResidueDuration = (mAvFileInfos[mSelectClipIndex] - (thumbnailSequenceDesc.trimOut - thumbnailSequenceDesc.trimIn)) / 2;
                timelineTimeSpan.setMaxRightToRight(duringToLength(maxImageResidueDuration));
            } else {
                timelineTimeSpan.setMaxRightToRight(duringToLength((long) (mAvFileInfos[mSelectClipIndex] / videoClip.getSpeed()
                        - thumbnailSequenceDesc.trimOut / videoClip.getSpeed())));
            }
        } else {
            timelineTimeSpan.setMaxRightToRight(duringToLength((long) (mAllOutPoint.get(mSelectClipIndex) / videoClip.getSpeed()
                    - (thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint))));
        }
        timelineTimeSpan.setMaxRightToLeft(duringToLength((thumbnailSequenceDesc.outPoint - thumbnailSequenceDesc.inPoint) - mMinShowLengthDuration));
    }

    private void trimInPointForClipOnThumbnailSequenceView(long trimInPoint) {
        IVideoTrack iVideoTrack = getCurrentVideoTrack();
        if (iVideoTrack == null) {
            GLog.e(TAG, "trimInPointForClipOnThumbnailSequenceView: iVideoTrack is null");
            return;
        }
        IVideoClip videoClip = iVideoTrack.getClipList().get(mSelectClipIndex);
        if (videoClip == null) {
            return;
        }
        ThumbnailSequenceDesc thumbnailSequenceDesc = mThumbnailSequenceDescArrayListOnHandDown.get(mSelectClipIndex);
        setMaxLeftValue(getTimelineTimeSpan(), videoClip, thumbnailSequenceDesc);
    }

    private EditTimelineSpanView getTimelineTimeSpan() {
        return mTimeSpanView;
    }

    private void updateMultiThumbnailSequenceViewByNowTime() {
        setAligningMiddle(true);
        int scrollX = timelinePositionToLength(getNowTime());
        mMultiThumbnailSequenceView.scrollTo(scrollX, 0);
        mLinearContainer.scrollTo(scrollX, 0);
        if (mScrollListener != null) {
            mScrollListener.onScrollChanged(getNowTime());
        }
        refreshAllEditTimeSpan();
        setAligningMiddle(false);
    }

    private void seekMultiThumbnailSequenceViewRight() {
        if (mMultiThumbnailSequenceView != null) {
            setAligningMiddle(true);
            try {
                ArrayList<ThumbnailSequenceDesc> thumbnailSequenceDescArray = mMultiThumbnailSequenceView.getThumbnailSequenceDescArray();
                if ((thumbnailSequenceDescArray != null) && (thumbnailSequenceDescArray.size() > mSelectClipIndex)) {
                    ThumbnailSequenceDesc thumbnailSequenceDesc = thumbnailSequenceDescArray.get(mSelectClipIndex);
                    IVideoClip currentClip = getCurrentClip();
                    int number = 0;
                    long outPoint = 0L;
                    if (currentClip != null) {
                        long duration = currentClip.getDuration();
                        if (duration > mMinShowLengthDuration) {
                            outPoint = thumbnailSequenceDesc.outPoint;
                        } else {
                            outPoint = thumbnailSequenceDesc.inPoint + duration;
                        }
                        int outPointX = timelinePositionToLength(outPoint);
                        int scrollX = mMultiThumbnailSequenceView.getScrollX();
                        int offset = outPointX - scrollX;
                        mFactor = FACTOR_NUM;
                        mCheckOffset = true;
                        mTargetPosition = outPointX;
                        number = offset / FACTOR_NUM;
                        SequenceRunnable runnable = new SequenceRunnable(number, true);
                        runnable.run();
                    }
                }
            } catch (Exception e) {
                mCheckOffset = false;
                GLog.e(TAG, LogFlag.DL, "seekMultiThumbnailSequenceViewRight, fail:" + e.getMessage());
                return;
            }
            refreshAllEditTimeSpan();
            setAligningMiddle(false);
        }

    }

    private void seekMultiThumbnailSequenceViewLEFT() {
        if (mMultiThumbnailSequenceView != null) {
            setAligningMiddle(true);
            try {
                ArrayList<ThumbnailSequenceDesc> thumbnailSequenceDescArray = mMultiThumbnailSequenceView.getThumbnailSequenceDescArray();
                if ((thumbnailSequenceDescArray != null) && (thumbnailSequenceDescArray.size() > mSelectClipIndex)) {
                    ThumbnailSequenceDesc thumbnailSequenceDesc = thumbnailSequenceDescArray.get(mSelectClipIndex);
                    int scrollX = timelinePositionToLength(getNowTime());
                    mLinearContainer.scrollTo(scrollX, 0);
                    int number = 0;
                    int outPointX = 0;
                    mFactor = -FACTOR_NUM;
                    mCheckOffset = true;
                    if (mSelectClipIndex == 0) {
                        outPointX = 0;
                        mTargetPosition = outPointX;
                        scrollX = mMultiThumbnailSequenceView.getScrollX();
                        int offset = outPointX - scrollX;
                        number = offset / mFactor;
                    } else {
                        scrollX = timelinePositionToLength(thumbnailSequenceDesc.inPoint);
                        outPointX = scrollX + ERROR_CORRECTION;
                        mTargetPosition = outPointX;
                        scrollX = mMultiThumbnailSequenceView.getScrollX();
                        int offset = outPointX - scrollX;
                        number = offset / mFactor;
                    }
                    SequenceRunnable runnable = new SequenceRunnable(number, true);
                    runnable.run();
                }
            } catch (Exception e) {
                mCheckOffset = false;
                GLog.e(TAG, LogFlag.DL, "seekMultiThumbnailSequenceViewLEFT, fail:" + e.getMessage());
                return;
            }
            refreshAllEditTimeSpan();
            setAligningMiddle(false);
        }
    }

    private class SequenceRunnable implements Runnable {

        private int mNumber;
        private boolean mIsRight;

        public SequenceRunnable(int number, boolean isRight) {
            this.mNumber = number;
            this.mIsRight = isRight;
        }

        @Override
        public void run() {
            if (mNumber > 0) {
                mNumber--;
                mMultiThumbnailSequenceView.scrollBy(mFactor, 0);
                mScrollToMiddleHandler.postDelayed(this, SCROLL_MIDDLE_DURATION_10);
            } else {
                if (mCheckOffset) {
                    mMultiThumbnailSequenceView.scrollTo(mTargetPosition, 0);
                    mScrollToMiddleHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mCheckOffset = false;
                        }
                    }, SCROLL_MIDDLE_DURATION_20);
                }

            }
        }
    }


    private boolean checkInterceptTouchEvent() {
        if (getTimelineTimeSpan() == null) {
            return false;
        }
        return TextUtils.equals(mState, STATE_EDITOR) && getTimelineTimeSpan().checkHandler() && !TimelineUtil.checkTimelineNull(mTimeline);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (checkInterceptTouchEvent()) {
            GLog.d(TAG, "onInterceptTouchEvent: checkInterceptTouchEvent = true");
            return true;
        }

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                return handleActionDown(ev);
            case MotionEvent.ACTION_MOVE:
                return handleActionMove(ev);
            case MotionEvent.ACTION_UP:
                return handleActionUp(ev);
            default:
                handleDefaultAction();
                break;
        }
        return super.onInterceptTouchEvent(ev);
    }

    private boolean handleActionDown(MotionEvent ev) {
        mTotalMoveXOnIntercept = 0;
        mDurationOnIntercept = getNowTime();
        mDownTimeNow = System.currentTimeMillis();
        mOnInterceptDownX = ev.getX();
        mReceiveMoveEvent = false;

        if (mCheckOffset) {
            mCheckOffset = false;
            mScrollToMiddleHandler.removeCallbacksAndMessages(null);
        }

        mHandler.removeCallbacksAndMessages(null);

        if (((ev.getX() < (ScreenUtils.getDisplayScreenWidth() - mAddButtonSize - mAddButtonMargin))
                || (ev.getX() > (ScreenUtils.getDisplayScreenWidth() - mAddButtonMargin)))
                && TextUtils.equals(mState, STATE_PREVIEW)) {
            mHandler.sendEmptyMessageDelayed(LONG_PRESS, ViewConfiguration.getLongPressTimeout());
        }

        return false;
    }

    private boolean handleActionMove(MotionEvent ev) {
        mReceiveMoveEvent = true;
        mTotalMoveXOnIntercept += Math.abs(ev.getX() - mOnInterceptDownX);
        return false;
    }

    private boolean handleActionUp(MotionEvent ev) {
        handleStop();
        mHandler.removeCallbacksAndMessages(null);

        if (System.currentTimeMillis() - mDownTimeNow > ViewConfiguration.getTapTimeout()) {
            GLog.d(TAG, "onInterceptTouchEvent: clickTime is too short " + (System.currentTimeMillis() - mDownTimeNow));
            return false;
        } else {
            return processActionUpEvent(ev);
        }
    }

    private boolean processActionUpEvent(MotionEvent ev) {
        if ((mPipSelectorView != null) && (mPipSelectorView.isClicked(ev.getRawX(), ev.getRawY()))) {
            return false;
        }

        int[] local = new int[2];
        mTimeSpanRelativeLayout.getLocationInWindow(local);

        if ((ev.getRawX() >= (ScreenUtils.getDisplayScreenWidth() - mAddButtonMargin - mAddButtonSize))
                && (ev.getRawX() <= (ScreenUtils.getDisplayScreenWidth() - mAddButtonMargin))
                && TextUtils.equals(mState, STATE_PREVIEW)) {
            if (mAddButton.isEnabled()) {
                if (!mIsSpeedMode) {
                    mAddButton.performClick();
                }
            }
            return false;
        }

        return processScrollAndClickEvents(ev, local);
    }

    private boolean processScrollAndClickEvents(MotionEvent ev, int[] local) {
        int scrollX = (int) (ev.getRawX() + mViewMarginLeft - local[0]);

        if (scrollX < 0) {
            return handleNegativeScrollX(ev, scrollX);
        }

        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            return false;
        }

        if (lengthBigToTimelineDurtion(scrollX)) {
            if (mOnSelectItemListener != null) {
                mOnSelectItemListener.onItemOutRectClick();
            }
            GLog.d(TAG, "onInterceptTouchEvent: scrollX is too big " + scrollX);
            return false;
        }

        return processClickPosition(ev, scrollX, videoTrack);
    }

    private boolean handleNegativeScrollX(MotionEvent ev, int scrollX) {
        RecyclerView.ViewHolder viewHolder = mCoverRecycleView.findViewHolderForAdapterPosition(0);
        if (viewHolder instanceof EditorTimeCoverAdapter.HeadHolder) {
            int[] muteIconLocal = new int[2];
            ((EditorTimeCoverAdapter.HeadHolder) viewHolder).mMuteIcon.getLocationOnScreen(muteIconLocal);
            if ((ev.getRawX() - muteIconLocal[0]) < 0) {
                if (mOnSelectItemListener != null) {
                    mOnSelectItemListener.onItemOutRectClick();
                }
            }
        }
        GLog.d(TAG, "onInterceptTouchEvent: scrollX " + scrollX + "   " + ev.getRawX());
        return false;
    }

    private boolean processClickPosition(MotionEvent ev, int scrollX, IVideoTrack videoTrack) {
        // 先检查 mTimeline 是否为 null
        if (mTimeline == null) {
            GLog.d(TAG, "onInterceptTouchEvent: mTimeline is null");
            return false;
        }
        long clickPos = lengthToTimelinePosition(scrollX);
        if (clickPos > mTimeline.getDuration()) {
            GLog.d(TAG, "onInterceptTouchEvent: clickPos is too big " + clickPos + "   " + mTimeline.getDuration());
            return false;
        }

        int index = getClipIndexByTime(clickPos);
        if (index < 0) {
            GLog.d(TAG, "onInterceptTouchEvent: index " + index);
            return false;
        }

        return processStateSpecificActions(ev, videoTrack, clickPos, index);
    }

    private boolean processStateSpecificActions(MotionEvent ev, IVideoTrack videoTrack, long clickPos, int index) {
        if (TextUtils.equals(mState, STATE_PREVIEW)) {
            return processPreviewStateActions(videoTrack, clickPos, index);
        } else if (TextUtils.equals(mState, STATE_EDITOR)) {
            return processEditorStateActions(ev, videoTrack, index);
        }

        return processDefaultSelection(index);
    }

    private boolean processPreviewStateActions(IVideoTrack videoTrack, long clickPos, int index) {
        // transition
        int transIndex = getTransitionIndex(clickPos, index, videoTrack);
        boolean isShowView = false;

        if (mEditorTimeTransitionAdapter != null) {
            isShowView = mEditorTimeTransitionAdapter.isItemViewShow(transIndex);
        }

        if ((transIndex >= 0) && isShowView) {
            if (!mIsSpeedMode) {
                mOnSelectItemListener.onSelectTransition(transIndex);
            }
            return false;
        }

        return processDefaultSelection(index);
    }

    private boolean processEditorStateActions(MotionEvent ev, IVideoTrack videoTrack, int index) {
        long clickTime = getNewTimeOnTap(ev.getRawX());
        IVideoClip videoClip = videoTrack.getClipByTimelinePostion(clickTime);

        if (videoClip == null) {
            GLog.e(TAG, "scrollViewWhenClick: videoClip is null,time = " + clickTime);
            return false;
        }

        int clickIndex = videoTrack.getClipIndex(videoClip);
        if (clickIndex == mSelectClipIndex) {
            return false;
        }

        mScrollToClickClip = true;
        refreshPreviewTimeSpanChanged(mSelectClipIndex, index);
        int oldIndex = mSelectClipIndex;
        mSelectClipIndex = index;

        if (mScrollListener != null) {
            mScrollListener.onSelectClipIndexChange(mSelectClipIndex);
        }

        scrollViewWhenClick(clickIndex, oldIndex);
        mScrollToClickClip = false;

        return processDefaultSelection(index);
    }

    private boolean processDefaultSelection(int index) {
        GLog.d(TAG, "onInterceptTouchEvent: mOnSelectItemListener " + (mOnSelectItemListener != null) + "    index:" + index);

        if (mOnSelectItemListener != null) {
            mOnSelectItemListener.onSelectItem(index);
        }
        return false;
    }

    private void handleDefaultAction() {
        mHandler.removeCallbacksAndMessages(null);
        mDownTimeNow = System.currentTimeMillis();
    }

    private int getClipIndexOnTap(float getX) {
        if (TimelineUtil.checkTimelineNull(mTimeline)) {
            return -1;
        }
        long currentTime = getNewTimeOnTap(getX);
        if (currentTime >= 0) {
            return getClipIndexByTime(currentTime);
        }
        return -1;
    }

    private long getNewTimeOnTap(float getX) {
        int[] local = new int[2];
        mTimeSpanRelativeLayout.getLocationInWindow(local);
        int scrollX = (int) (getX + mViewMarginLeft - local[0]);
        if (scrollX >= 0) {
            return lengthToTimelinePosition(scrollX);
        }
        return -1;
    }

    private void scrollViewWhenClick(int clickIndex, int oldIndex) {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "scrollViewWhenClick: videoTrack is null");
            return;
        }
        IVideoClip videoClip = (IVideoClip) videoTrack.getClip(clickIndex);
        if (videoClip == null) {
            GLog.e(TAG, "scrollViewWhenClick: videoClip is null,index = " + clickIndex);
            return;
        }
        long inPoint = videoClip.getInPoint();
        long outPoint = videoClip.getOutPoint();
        long needScrollToPoint = 0;

        if (clickIndex > oldIndex) {
            needScrollToPoint = inPoint;
        } else if (clickIndex < oldIndex) {
            needScrollToPoint = outPoint - ERROR_CORRECTION;
        } else {
            return;
        }

        mMultiThumbnailSequenceView.scrollTo(timelinePositionToLength(needScrollToPoint), 0);
        if ((mScrollListener != null) && (mTimeline != null)) {
            if (needScrollToPoint > mTimeline.getDuration() - LIMIT_FRAME_ERROR) {
                needScrollToPoint = mTimeline.getDuration() - LIMIT_FRAME_ERROR;
            }
            mScrollListener.onScrollChanged(needScrollToPoint);
        }
    }

    private int getClipIndexByTime(long timeNow) {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "getClipIndexByTime: videoTrack is null");
            return -1;
        }
        IVideoClip videoClip = videoTrack.getClipByTimelinePostion(timeNow);
        if (videoClip != null) {
            return videoTrack.getClipIndex(videoClip);
        }
        return -1;
    }

    private int getTransitionIndex(long clickPos, int clipIndex, IVideoTrack videoTrack) {
        if ((clipIndex < 0) || (clipIndex >= videoTrack.getClipCount())) {
            return -1;
        }
        IVideoClip videoClip = (IVideoClip) videoTrack.getClip(clipIndex);
        if (videoClip == null) {
            GLog.e(TAG, "video clip is null");
            return -1;
        }
        long transIconDuration = lengthToDuring(mTransIconWidth);
        int transIndex = -1;
        if (clickPos >= (videoClip.getOutPoint() - transIconDuration / 2)) {
            transIndex = clipIndex;
        } else if (clickPos <= (videoClip.getInPoint() + transIconDuration / 2)) {
            transIndex = clipIndex - 1;
        }
        if ((transIndex < 0) || (transIndex >= videoTrack.getClipCount() - 1)) {
            transIndex = -1;
        }
        return transIndex;
    }

    public void onTrimStateOpen(long duration) {
        mSelectClipIndex = getClipIndexByTime(duration);
        refreshAllEditTimeSpan();
        initDurationText();
    }

    public void moveScrollViewTo(int x) {
        if (mMultiThumbnailSequenceView != null) {
            mMultiThumbnailSequenceView.scrollTo(x, 0);
        }
    }

    public void moveScrollViewSmoothTo(int x) {
        if (mMultiThumbnailSequenceView != null) {
            mMultiThumbnailSequenceView.smoothScrollTo(x, 0);
        }
    }

    public void scrollTo(long duration) {
        mTransitionRecycleView.setVisibility(INVISIBLE);
        mIsFirstLoad = true;
        mOffset = timelinePositionToLength(duration);
        if (mSequenceLoadSuccess) {
            int curScrollX = mMultiThumbnailSequenceView.getScrollX();
            GLog.d(TAG, "scrollTo getScrollX: " + curScrollX + " mOffset: " + mOffset);
            if (curScrollX != mOffset) {
                mMultiThumbnailSequenceView.scrollTo(mOffset, 0);
            }
        }
    }

    public void scrollViewScrollBy(int offset) {
        if (mMultiThumbnailSequenceView != null) {
            mScrollPressed = true;
            mMultiThumbnailSequenceView.scrollBy(offset, 0);
        }
    }

    public void setScrollPressed(boolean scrollPressed) {
        mScrollPressed = scrollPressed;
    }

    public boolean getScrollPressed() {
        return mScrollPressed;
    }

    private class SequenceLoadListener implements MultiThumbnailSequenceView.OnSequenceLoadDataListener {
        @Override
        public void sequenceLoadFinish() {
            mSequenceLoadSuccess = true;
            scrollToTargetPosition();
            if (mThumbnailLoadFinishListener != null) {
                mThumbnailLoadFinishListener.onThumbnailLoadFinishListener();
            }
        }
    }


    private void scrollToTargetPosition() {
        if (mIsFirstLoad) {
            if (mMultiThumbnailSequenceView != null) {
                int curScrollX = mMultiThumbnailSequenceView.getScrollX();
                GLog.d(TAG, "scrollToTargetPosition getScrollX: " + curScrollX + " mOffset: " + mOffset);
                if (curScrollX != mOffset) {
                    mMultiThumbnailSequenceView.scrollTo(mOffset, 0);
                }
                if (mOffset == 0) {
                    firstScroll(0);
                }
            }
        }
    }


    private void deleteAllViews() {
        mTimeSpanRelativeLayout.removeAllViews();
        mTimeSpanView = null;
    }

    public void stopFling() {
        if ((mMultiThumbnailSequenceView != null) && mScrollPressed) {
            mMultiThumbnailSequenceView.fling(0);
            handleStop();
        }
    }

    public void sequenceViewStop() {
        mMultiThumbnailSequenceView.fling(0);
    }

    public void smoothSequenceView(long stamp) {
        if (mScrollPressed || mIsFirstLoad || !mHasDragEnd) {
            return;
        }
        if ((mMultiThumbnailSequenceView != null) && (mTimeline != null) && (mTimeline.getDuration() > 0) && !mDragEnd) {
            mPositionOnPlay = stamp;
            int x = timelinePositionToLength(stamp);
            mMultiThumbnailSequenceView.scrollTo(x, 0);
        }
    }

    public long lengthToDuring(int length) {
        return (long) Math.floor(length / mPixelPerMicrosecond + 0.5D);
    }

    public int duringToLength(long during) {
        return (int) Math.floor(during * mPixelPerMicrosecond + 0.5D);
    }

    private int timelinePositionToLength(long timelinePosition) {

        IVideoTrack videoTrack = getCurrentVideoTrack();
        if (videoTrack == null) {
            GLog.e(TAG, "timelinePositionToLength getCurrentVideoTrack is null");
            return 0;
        }
        if (timelinePosition <= 0) {
            return 0;
        }

        int result = 0;
        int clipCount = videoTrack.getClipCount();
        long remainingTimelinePosition = timelinePosition;
        long clipuration = 0;
        long cliplength = 0;
        IClip videoClip = null;
        for (int i = 0; i < clipCount; i++) {
            videoClip = videoTrack.getClip(i);
            clipuration = (videoClip == null) ? 0 : videoClip.getDuration();
            cliplength = durationToShownLength(clipuration);
            if ((remainingTimelinePosition <= clipuration) && (clipuration != 0)) {
                result += (int) (cliplength * remainingTimelinePosition / clipuration);
                break;
            } else {
                remainingTimelinePosition -= clipuration;
                result += cliplength;
            }
        }

        return result;
    }

    private int durationToShownLength(long duration) {
        return duringToLength((duration > mMinShowLengthDuration) ? duration : mMinShowLengthDuration);
    }

    private boolean lengthBigToTimelineDurtion(int x) {
        IVideoTrack videoTrack = getCurrentVideoTrack();
        if ((mTimeline == null) || (videoTrack == null)) {
            return true;
        }
        long result = 0;
        int clipCount = videoTrack.getClipCount();
        for (int i = 0; i < clipCount; i++) {
            long clipDuration = videoTrack.getClip(i).getDuration();
            long clipLength = durationToShownLength(clipDuration);
            result += clipLength;
        }
        if (x > result) {
            return true;
        }
        return false;
    }


    private long lengthToTimelinePosition(int x) {

        IVideoTrack videoTrack = getCurrentVideoTrack();
        if ((mTimeline == null) || (videoTrack == null)) {
            return 0;
        }
        long result = 0;
        int clipCount = videoTrack.getClipCount();
        int currentX = 0;
        long clipDuration = 0;
        long clipLength = 0;
        for (int i = 0; i < clipCount; i++) {
            clipDuration = videoTrack.getClip(i).getDuration();
            clipLength = durationToShownLength(clipDuration);
            if (currentX + clipLength > x) {
                result += (x - currentX) * clipDuration / clipLength;
                break;
            } else {
                currentX += clipLength;
                result += clipDuration;
            }
        }
        return result;
    }

    private long getNowTime() {
        long stamp = lengthToTimelinePosition(mMultiThumbnailSequenceView.getScrollX());
        return stamp;
    }

    public IVideoTrack getCurrentVideoTrack() {
        if (TimelineUtil.checkTimelineNull(mTimeline)) {
            GLog.e(TAG, "getCurrentVideoTrack: timeline is null ");
            return null;
        }
        return mTimeline.getVideoTrack(mTrackIndex);
    }

    public int getClipSize() {
        if (TimelineUtil.checkTimelineNull(mTimeline)) {
            GLog.e(TAG, "getCurrentVideoTrack: timeline is null ");
            return 0;
        }
        IVideoTrack iVideoTrack = mTimeline.getVideoTrack(0);
        if (iVideoTrack == null) {
            return 0;
        }
        return iVideoTrack.getClipCount();
    }

    private boolean isfinishScroll() {
        boolean isfinish = false;
        Class scrollview = MultiThumbnailSequenceView.class.getSuperclass();
        try {
            Field scrollField = scrollview.getDeclaredField("mScroller");
            scrollField.setAccessible(true);
            Object scroller = scrollField.get(mMultiThumbnailSequenceView);
            Class overscroller = scrollField.getType();
            Method finishField = overscroller.getMethod("isFinished");
            finishField.setAccessible(true);
            isfinish = (boolean) finishField.invoke(scroller);
        } catch (NoSuchFieldException
                 | IllegalAccessException
                 | NoSuchMethodException
                 | InvocationTargetException e) {
            GLog.e(TAG, LogFlag.DL, "isfinishScroll, fail:" + e.getMessage());
            return false;
        }
        return isfinish;
    }

    public void setClipFileDuration(Long duration, boolean isLeftHand) {
        mAvFileInfos[mSelectClipIndex] = duration;
        IVideoClip videoClip = getCurrentClip();
        refreshMaxLengthForTwoHand(videoClip);
        int newWidth = mTimeSpanRelativeLayout.getWidth() + duringToLength((long) (duration / videoClip.getSpeed() - mAllOutPoint.get(mSelectClipIndex)));
        if (isLeftHand) {
            newWidth += mAddParentLengthOnLeftHandDown;
        }
        LinearLayout.LayoutParams timeSpanRelativeParams = new LinearLayout.LayoutParams(newWidth, LayoutParams.MATCH_PARENT);
        mTimeSpanRelativeLayout.setLayoutParams(timeSpanRelativeParams);
    }

    public void refreshTransitionButton(boolean hasTransition, int index, ITimeline newTimeline) {
        if (TimelineUtil.checkTimelineNull(newTimeline)) {
            GLog.e(TAG, "refreshTransitionButton: timeline is null");
            return;
        }
        this.mTimeline = newTimeline;
        if ((getCurrentVideoTrack() != null) && (index > (getCurrentVideoTrack().getClipList().size() - 1))) {
            GLog.e(TAG, "refreshTransitionButton: index is too big:" + index);
            return;
        }
        mEditorTimeTransitionAdapter.refresh(index, hasTransition);
    }

    public void setAligningMiddle(boolean aligningMiddle) {
        this.mIsAligningMiddle = aligningMiddle;
    }

    public void setOnSelectItemListener(OnSelectItemListener mOnSelectItemListener) {
        this.mOnSelectItemListener = mOnSelectItemListener;
    }

    public void setOnPipSelectorClickListener(OnPipSelectorClickListener onPipSelectorClickListener) {
        this.mOnPipSelectorClickListener = onPipSelectorClickListener;
    }

    public int getSelectClipIndex() {
        return mSelectClipIndex;
    }

    public IVideoClip getSelectClip() {
        return mSelectClip;
    }

    public void setSelectClip(IVideoClip selectClip) {
        this.mSelectClip = selectClip;
    }

    public void setOnScrollChangeListener(OnScrollPosChangeListener listener) {
        mScrollListener = listener;
    }

    public interface OnSelectItemListener {
        /**
         * interceptTouchEvent trigger when click on thumbnailView
         *
         * @param index return clip index in timeline
         */
        void onSelectItem(int index);

        void onSelectTransition(int transitionIndex);

        void onLongPressClip(int index);

        boolean onAddButtonClick(View view);

        void onItemOutRectClick();
    }

    public interface OnScrollPosChangeListener {
        void onScrollChanged(long stamp);

        void onEditTimelineViewActionUp();

        void onSelectClipIndexChange(int index);
    }

    public interface OnPipSelectorClickListener {
        void onPipSelectorClicked(IVideoClip videoClip);
    }

    public void setMaxCountString(String maxCountString) {
        mMaxCountString = maxCountString;
    }

    public interface OnTimelineScrollViewListener {
        void onTimelineScrollChanged(Object o, int x, int oldX);
    }

    public interface OnMuteClickListener {
        void onMuteIconClick(boolean isMuteHandle);
    }

    public void setOnTimelineScrollViewListener(OnTimelineScrollViewListener onTimelineScrollViewListener) {
        this.mOnTimelineScrollViewListener = onTimelineScrollViewListener;
    }

    public interface ThumbnailLoadFinishListener {
        void onThumbnailLoadFinishListener();
    }

    public void setThumbnailLoadFinishListener(ThumbnailLoadFinishListener thumbnailLoadFinishListener) {
        this.mThumbnailLoadFinishListener = thumbnailLoadFinishListener;
    }
}
