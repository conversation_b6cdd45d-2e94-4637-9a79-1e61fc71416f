/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  SimpleGestureListener
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/2
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/2      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.preview

import android.view.MotionEvent
import com.oplus.gallery.foundation.ui.animationcontrol.GestureListener

/**
 * 可以按需实现的手势监听器
 */
interface SimpleGestureListener : GestureListener {
    override fun onUp(event: MotionEvent): Boolean = false

    override fun onDown(event: MotionEvent): Boolean = false

    override fun onMove(event: MotionEvent): Boolean = false

    override fun onDoubleTap(): Boolean = false

    override fun onFling(): Boolean = false

    override fun onLongPressBegin(event: MotionEvent): Boolean = false

    override fun onLongPressFinish() = Unit

    override fun onScroll(distanceX: Float, distanceY: Float): Boolean = false

    override fun onScaleRotateBegin(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        deltaAngle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        deltaScale: Float,
    ): Boolean = false


    override fun onScaleRotate(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        deltaAngle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        deltaScale: Float,
    ): Boolean = false


    override fun onScaleRotateEnd(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        deltaAngle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        deltaScale: Float,
    ): Boolean = false

    override fun onSingleTapConfirmed(event: MotionEvent): Boolean = false

    override fun onSingleTapUp(event: MotionEvent): Boolean = false
}