/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IHdrImageContent.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2023/10/10
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery       2023/10/10        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.standard_lib.codec.drawable

import android.graphics.Bitmap

interface IHdrImageContent {
    /**
     * 获取HDR文件的Linear Mask灰图信息，以Bitmap的形式返回。
     */
    val grayImage: Bitmap

    /**
     * 获取HDR文件的Meta信息。
     */
    val metadata: IHdrMetadataPack
}