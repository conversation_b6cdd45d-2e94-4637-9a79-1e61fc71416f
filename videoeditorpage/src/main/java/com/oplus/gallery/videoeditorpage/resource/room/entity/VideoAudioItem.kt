/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: VideoAudioItem
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.room.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.ResourceType
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item
import com.oplus.gallery.videoeditorpage.resource.util.ResourceUtils

@Entity(tableName = "resource_video_audio")
class VideoAudioItem : BaseMusicItem() {
    @ColumnInfo(name = "original_video_path")
    var originalVideoPath: String? = null

    @ColumnInfo(name = "date_taken")
    var dateTaken: Long = 0
    fun setTitle(title: String?) {
        super.chName = title
        super.enName = title
        super.zhName = title
    }

    fun getTitle(): String? {
        var title: String? = ""
        title = if (ResourceUtils.isCurrentLanguageSimp()) {
            super.zhName
        } else if (ResourceUtils.isCurrentLanguageTrad()) {
            super.chName
        } else {
            super.enName
        }
        return title
    }
    init {
        this.songId = ResourceType.MusicId.LOCAL_MUSIC_ID
        this.songType = SONG_TYPE_LOCAL_VIDEO
        this.downloadState = Item.TYPE_DOWNLOAD_FILE
    }
}
