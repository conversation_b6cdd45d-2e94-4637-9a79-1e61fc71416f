/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CutVideoTask.kt
 ** Description: 保存指定片段视频任务
 ** Version: 1.0
 ** Date: 2025/2/12
 ** Author: zhouzihao
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhouzihao                       2025/2/12       1.0       created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.output.task

import android.text.TextUtils
import android.util.Size
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.video.business.output.ExportListener
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode.Companion.CUT_VIDEO_TASK_ERROR
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 保存指定片段视频任务
 * @param videoEngine: 视频源操作对象
 * @param saveVideoData: 保存视频数据体
 * @param outputTmpVideoPath: 保存视频的临时路径
 * @param exportListener: 视频保存任务监听器
 */
class CutVideoTask<T>(
    private val videoEngine: EditorEngine,
    private val saveVideoData: SaveVideoData,
    private val outputTmpVideoPath: String,
    private val exportListener: ExportListener<T>
) : ISaveTask<Unit> {
    override val name: String = TAG

    /**
     * 保存视频到临时路径(当前限制60帧)
     */
    override fun run() {
        if (TextUtils.isEmpty(outputTmpVideoPath)) {
            GLog.e(TAG, LogFlag.DL) { "[run] outputTmpVideoPath is null" }
            return
        }
        // 美摄接口需要保证在主线程中运行
        AppScope.launch(Dispatchers.Main) {
            if (videoEngine.saveVideo(saveVideoData, outputTmpVideoPath).not()) {
                GLog.e(TAG, LogFlag.DL) { "[run] save video fail" }
                exportListener.onExportComplete(OutputData(CUT_VIDEO_TASK_ERROR, ResultCode.toString(CUT_VIDEO_TASK_ERROR)))
            }
        }
    }

    private companion object {
        private const val TAG = "CutVideoTask"
    }
}

/**
 * 保存视频数据体
 * @param startTime: 保存起始时刻
 * @param endTime: 保存结束时刻
 * @param fps: 保存时的帧率
 * @param videoType: 视频类型 [com.oplus.gallery.videoeditorpage.video.business.input.VideoType]
 * @param videoHeight: 保存视频高度
 * @param createTimeStamp: 视频的创建时间
 * @param location: 定位信息，格式为：经度+纬度 精确度为最多为6位小数，视频原定位信息为4位小数 eg：22.165354+116.277666
 * @param oPlusUserData: 视频保存的镜头信息，为自定义信息 格式为 key1=value1;key2=value2;key3=value3;...
 */
data class SaveVideoData(
    val startTime: Long = 0L,
    val endTime: Long = 0L,
    val fps: Float,
    var videoType: Int,
    var videoSize: Size = Size(0, 0),
    val createTimeStamp: Long = 0L,
    var location: String = TextUtil.EMPTY_STRING,
    var oPlusUserData: String = TextUtil.EMPTY_STRING,
)