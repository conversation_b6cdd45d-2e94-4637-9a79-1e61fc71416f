/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorControlView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.controler;
import android.animation.IntEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.utlis.ScreenUtils;
import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorChoreography;
import com.oplus.gallery.videoeditorpage.video.business.caption.EditorCaptionState;
import com.oplus.gallery.videoeditorpage.video.business.manager.EnginePlayingTimeManager;
import com.oplus.gallery.videoeditorpage.video.business.trim.EditorTrimState;
import com.oplus.gallery.videoeditorpage.video.business.output.OperationSaveHelper;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;

/**
 * 菜单控制类
 */
public class EditorControlView extends FrameLayout {
    private static final String TAG = "EditorControlView";
    private static final long TIME_BASE = 1000000L;

    //the sShowNavigationBar is a global sharing value, must defined as static
    private static final long DEFAULT_CAPTION_MIN_SECOND = (long) (0.5 * TIME_BASE);
    private static final int DEFAULT_CAPTION_SECOND = 3;

    private OnPipListener mOnPipListener;
    private OnUISeekingTimelineListener mUISeekingiTimelineListener;
    private EditorEngine mEditorEngine;
    private OperationSaveHelper mOperationSaveHelper;
    private EditorStateManager mEditorStateManager;
    /**
     * 动画编排者，用于在state切换时，处理淡入淡出动画的控制逻辑
     */
    private AnimatorChoreography mAnimatorChoreography;
    /**
     * 引擎播放时间管理器，用于获取视频播放引擎的播放位置和时长等
     */
    private EnginePlayingTimeManager mEnginePlayingTimeManager;

    private ValueAnimator mHeightValueAnimator;

    public EditorControlView(Context context) {
        this(context, null);
    }

    public EditorControlView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EditorControlView(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public EditorControlView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mEditorStateManager = new EditorStateManager(context);
        mAnimatorChoreography = new AnimatorChoreography();
        mEditorStateManager.regisStateChangeLifecycle(mAnimatorChoreography);
    }

    public EditorStateManager getEditorStateManager() {
        return mEditorStateManager;
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
    }

    public void seekTimeline(long position, int flag, boolean scrollTimelineView) {
        if (mEditorEngine == null) {
            return;
        }
        EditorBaseState currentState = null;
        int seekFlag = flag; // 创建局部变量保存flag值
        if (mEditorStateManager != null) {
            currentState = mEditorStateManager.getCurrentEditorState();
            if (currentState instanceof EditorCaptionState) {
                seekFlag = EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
                        | EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER;
            }
        }
        long seek = Math.min(position, mEditorEngine.getTimelineDuration());
        mEditorEngine.seekTo(seek, seekFlag);
        if ((mEditorStateManager != null) && scrollTimelineView) {
            EditorBaseState menuState = mEditorStateManager.getMenuState();
            if (menuState != null) {
                menuState.onPlayPositionChange(seek);
            }
            if (currentState != null) {
                currentState.onPlayPositionChange(seek);
            }
            if ((currentState instanceof EditorTrimState) && (seek == 0)) {
                EditorTrimState montageState = (EditorTrimState) currentState;
                montageState.updateUIControlButtonStatus();
            }
        }

        if (mUISeekingiTimelineListener != null) {
            mUISeekingiTimelineListener.onUISeekingTimelinePositionChange(seek);
        }
    }

    public void setOnPipListener(OnPipListener mOnPipListener) {
        this.mOnPipListener = mOnPipListener;
    }

    public void setOnUISeekingTimelineListener(OnUISeekingTimelineListener listener) {
        mUISeekingiTimelineListener = listener;
    }

    public void setEditorEngine(EditorEngine editorEngine) {
        this.mEditorEngine = editorEngine;
    }

    public void setOperationSaveHelper(OperationSaveHelper saveHelper) {
        mOperationSaveHelper = saveHelper;
    }

    public OperationSaveHelper getOperationSaveHelper() {
        return mOperationSaveHelper;
    }
    /**
     * 获取引擎播放时间管理器实例的方法。
     */
    public EnginePlayingTimeManager getEnginePlayingTimeManager() {
        return mEnginePlayingTimeManager;
    }
    /**
     * 设置引擎播放管理器的方法
     */
    public void setEnginePlayingTimeManager(EnginePlayingTimeManager mEnginePlayingTimeManager) {
        this.mEnginePlayingTimeManager = mEnginePlayingTimeManager;
    }

    public EditorEngine getEditorEngine() {
        return mEditorEngine;
    }

    public void onLayoutChange(EditorBaseState state, boolean isShowingNavigationBar, boolean needAnim) {
        // changed control view height
        ViewGroup.LayoutParams lp = getLayoutParams();
        if (lp instanceof RelativeLayout.LayoutParams) {
            int height = getContext().getResources().getDimensionPixelSize(R.dimen.videoeditor_overlap_container_size);
            int showingNavigationBarHeight = 0;
            if (state != null) {
                height = state.getHeight();
                showingNavigationBarHeight = ScreenUtils.getShowingNavigationBarHeight(state.requireEditorActivity());
            }

            int newHeight = height + showingNavigationBarHeight;
            GLog.d(TAG, "onLayoutChange,newHeight:" + newHeight);
            if (needAnim) {
                startHeightMarginAnimator(lp.height, newHeight, EditorBaseUIController.ANIMATION_FADE_IN_DURATION, lp);
            } else {
                ((RelativeLayout.LayoutParams) lp).height = isShowingNavigationBar ? newHeight : height;
                setLayoutParams(lp);
            }
        }
    }

    @Override
    public void setLayoutParams(ViewGroup.LayoutParams params) {
        super.setLayoutParams(params);
    }

    public void stopTimelineViewFling() {
        if (mEditorStateManager.getMenuState() != null) {
            mEditorStateManager.getMenuState().stopTimelineViewFling();
        }
        if (mEditorStateManager.getCurrentEditorState() != null) {
            mEditorStateManager.getCurrentEditorState().stopTimelineViewFling();
        }
    }

    public void updateFilter() {
        if (mEditorStateManager.getCurrentEditorState() != null) {
            mEditorStateManager.getCurrentEditorState().updateFilter();
        }
    }

    public void onPlayPositionChange(long currentPosition) {
        if (mEditorStateManager.getMenuState() != null) {
            mEditorStateManager.getMenuState().onPlayPositionChange(currentPosition);
        }

        if (mEditorStateManager.getCurrentEditorState() != null) {
            mEditorStateManager.getCurrentEditorState().onPlayPositionChange(currentPosition);
        }
    }

    public void setPipRectVisible(int visible) {
        if (mOnPipListener != null) {
            mOnPipListener.onSetPipRectVisible(visible);
        }
    }

    public interface OnPipListener {
        void onSetPipRectVisible(int visible);
    }

    private void startHeightMarginAnimator(int from, int to, int duration, ViewGroup.LayoutParams layoutParams) {
        if (mHeightValueAnimator != null) {
            mHeightValueAnimator.cancel();
        }

        mHeightValueAnimator = ValueAnimator.ofObject(new IntEvaluator(), from, to);
        mHeightValueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                layoutParams.height = (int) animation.getAnimatedValue();
                EditorControlView.this.setLayoutParams(layoutParams);

            }
        });
        mHeightValueAnimator.setDuration(duration);
        mHeightValueAnimator.start();
    }

    public void onAppUiConfigChange(AppUiResponder.AppUiConfig config) {
        if (mEditorStateManager != null) {
            mEditorStateManager.onAppUiConfigChange(config);
        }
    }

    public interface OnUISeekingTimelineListener {
        void onUISeekingTimelinePositionChange(long position);
    }
}
