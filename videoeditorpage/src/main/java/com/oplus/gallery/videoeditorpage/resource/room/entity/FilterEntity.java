/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: FilterEntity
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.room.entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import android.graphics.Bitmap;

import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item;

@Entity(tableName = "filterEntity",
        indices = {@Index(value = "filter_id", unique = true)})
public class FilterEntity {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    private int mId;
    @ColumnInfo(name = "filter_id")
    private int mFilterId;
    @ColumnInfo(name = "category_id")
    private int mCategoryId;
    @ColumnInfo(name = "zh_name")
    private String mZhName;
    @ColumnInfo(name = "ch_name")
    private String mChName;
    @ColumnInfo(name = "en_name")
    private String mEnName;
    @ColumnInfo(name = "package_id")
    private String mPackageId;
    @ColumnInfo(name = "icon_url")
    private String mIconUrl;
    @ColumnInfo(name = "icon_path")
    private String mIconPath;
    @ColumnInfo(name = "file_url")
    private String mFileUrl;
    @ColumnInfo(name = "file_path")
    private String mFilePath;
    @ColumnInfo(name = "download_state")
    private int mDownloadState = 0;
    @ColumnInfo(name = "type")
    private int mType;
    @ColumnInfo(name = "is_builtin")
    private int mIsBuiltin;
    @ColumnInfo(name = "remark")
    private String mRemark;
    @ColumnInfo(name = "version")
    private String mVersion;
    @ColumnInfo(name = "update_time")
    private String mUpdateTime;
    @Ignore
    private int mProgress = -1;

    @Ignore
    private Bitmap mIcon;
    @Ignore
    private String mIconName;
    @Ignore
    private String mFilterName;

    public int getId() {
        return mId;
    }

    public void setId(int id) {
        this.mId = id;
    }

    public int getFilterId() {
        return mFilterId;
    }

    public void setFilterId(int id) {
        this.mFilterId = id;
    }

    public int getCategoryId() {
        return mCategoryId;
    }

    public void setCategoryId(int categoryId) {
        this.mCategoryId = categoryId;
    }

    public String getZhName() {
        return mZhName;
    }

    public void setZhName(String mZhName) {
        this.mZhName = mZhName;
    }

    public String getChName() {
        return mChName;
    }

    public void setChName(String mChName) {
        this.mChName = mChName;
    }

    public String getEnName() {
        return mEnName;
    }

    public void setEnName(String mEnName) {
        this.mEnName = mEnName;
    }

    public String getPackageId() {
        return mPackageId;
    }

    public void setPackageId(String mPackageId) {
        this.mPackageId = mPackageId;
    }

    public String getIconUrl() {
        return mIconUrl;
    }

    public void setIconUrl(String mIconUrl) {
        this.mIconUrl = mIconUrl;
    }

    public String getIconPath() {
        return mIconPath;
    }

    public void setIconPath(String mIconPath) {
        this.mIconPath = mIconPath;
    }

    public String getFileUrl() {
        return mFileUrl;
    }

    public void setFileUrl(String mFileUrl) {
        this.mFileUrl = mFileUrl;
    }

    public String getFilePath() {
        return mFilePath;
    }

    public void setFilePath(String mFilePath) {
        this.mFilePath = mFilePath;
    }

    public int getDownloadState() {
        return mDownloadState;
    }

    public void setDownloadState(int mDownloadState) {
        this.mDownloadState = mDownloadState;
    }

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        this.mType = type;
    }

    public int getIsBuiltin() {
        return mIsBuiltin;
    }

    public void setIsBuiltin(int mIsBuiltin) {
        this.mIsBuiltin = mIsBuiltin;
    }

    public String getRemark() {
        return mRemark;
    }

    public void setRemark(String mRemark) {
        this.mRemark = mRemark;
    }

    public String getVersion() {
        return mVersion;
    }

    public void setVersion(String mVersion) {
        this.mVersion = mVersion;
    }

    public String getUpdateTime() {
        return mUpdateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.mUpdateTime = updateTime;
    }
    @Ignore
    public boolean isDownloaded() {
        return (mDownloadState & Item.TYPE_DOWNLOAD_FILE)
                == Item.TYPE_DOWNLOAD_FILE;
    }

    @Ignore
    public int getProgress() {
        return mProgress;
    }

    @Ignore
    public void setProgress(int progress) {
        this.mProgress = progress;
    }

    @Ignore
    public Bitmap getIcon() {
        return mIcon;
    }

    @Ignore
    public void setIcon(Bitmap icon) {
        this.mIcon = icon;
    }

    @Ignore
    public String getIconName() {
        return mIconName;
    }

    @Ignore
    public void setIconName(String iconName) {
        this.mIconName = iconName;
    }

    public String getFilterName() {
        return mFilterName;
    }

    public void setFilterName(String filterName) {
        this.mFilterName = filterName;
    }

    @Override
    public String toString() {
        return "FilterEntity{"
                + "mId=" + mId
                + ", mFilterId=" + mFilterId
                + ", mZhName='" + mZhName + '\''
                + ", mChName='" + mChName + '\''
                + ", mEnName='" + mEnName + '\''
                + ", mPackageId='" + mPackageId + '\''
                + ", mIconUrl='" + mIconUrl + '\''
                + ", mIconPath='" + mIconPath + '\''
                + ", mFileUrl='" + mFileUrl + '\''
                + ", mDownloadState=" + mDownloadState
                + ", mType=" + mType
                + ", mIsBuiltin=" + mIsBuiltin
                + ", mRemark='" + mRemark + '\''
                + ", mVersion='" + mVersion + '\''
                + ", mUpdateTime='" + mUpdateTime + '\''
                + ", mCategoryId=" + mCategoryId + '\''
                + ", mFilterName=" + mFilterName + '\''
                + ", mFilePath=" + PathMask.INSTANCE.mask(mFilePath)
                + '}';
    }
}
