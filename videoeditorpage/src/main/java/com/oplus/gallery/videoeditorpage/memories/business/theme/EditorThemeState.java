/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorThemeState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.theme;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesManager;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListenerImpl;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.LocalSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;
import com.oplus.gallery.videoeditorpage.memories.autodownload.ThemeAutoDownloadHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EditorThemeState extends EditorBaseState implements EditorBaseUIController.OnIconClickListener {
    private static final String TAG = "EditorThemeState";

    private static final String TYPE_NAME = "VideoEditor_MemoriesTheme";

    private String mPrevTheme = "";
    private String mPrevMusic = "";
    private ThemeItem mCurrentSelectedItem;
    private HashMap<Integer, ThemeItem> mWaitingDownloadMap = new HashMap<>();

    public EditorThemeState(Context context, ControlBarView controlBarView) {
        super(TYPE_NAME, context, controlBarView, MONITOR_NET_STATE);
        if (mEngineManager != null) {
            mPrevTheme = mEngineManager.getCurrentTheme();
            mPrevMusic = mEngineManager.getCurrentThemeMusic();
            GLog.d(TAG, "EditorThemeState mPrevTheme = " + mPrevTheme + ", mPrevMusic = " + mPrevMusic);
            mEngineManager.play();
        }
    }

    @Override
    protected EditorBaseUIController createUIController() {
        EditorThemeUIController themeUIController = new EditorThemeUIController(mContext, mControlBarView, this);
        themeUIController.setOnIconClickListener(this);
        return themeUIController;
    }

    @Override
    public boolean done() {
        if (mEngineManager != null) {
            String curTheme = mEngineManager.getCurrentTheme();
            GLog.d(TAG, "done mPrevTheme = " + mPrevTheme + ", curTheme = " + curTheme);
            if (!TextUtils.equals(mPrevTheme, curTheme)) {
                MemoriesManager.getCurMemoriesInfo().mTheme = curTheme;
                MemoriesManager.getCurMemoriesInfo().mMusic = mEngineManager.getCurrentThemeMusic();
            }
        }
        return super.done();
    }

    @Override
    public void cancel() {
        super.cancel();
        GLog.d(TAG, "cancel");
        if (mEngineManager != null) {
            String curTheme = mEngineManager.getCurrentTheme();
            GLog.d(TAG, "cancel mPrevMusic = " + mPrevMusic + ", mPrevTheme = " + mPrevTheme + ", curTheme = " + curTheme);
            boolean isPlaying = mEngineManager.isPlaying();
            if (!TextUtils.isEmpty(mPrevTheme)) {
                if (!TextUtils.equals(mPrevTheme, curTheme)) {
                    ThemeItem item = ThemeSourceManager.getInstance().getThemeByPath(mPrevTheme);
                    if (item != null) {
                        mEngineManager.addTheme(item.convertToThemeInfo());
                        if (!TextUtils.equals(mPrevMusic, mPrevTheme)) {
                            mEngineManager.addThemeMusic(mPrevMusic);
                        }
                        mEngineManager.play();
                    }
                } else {
                    if (isPlaying && !mEngineManager.isPlaying()) {
                        mEngineManager.resume();
                    }
                }
            } else if (!TextUtils.isEmpty(curTheme)) {
                GLog.w(TAG, "cancel error. mPrevTheme = " + mPrevTheme + ", curTheme = " + curTheme);
            }
        }
        if (hasUserDownloadTask()) {
            ToastUtil.showShortToast(R.string.videoeditor_source_cancel_download);
        }
        LocalSourceManager.resetAllDownloadQueueStatus();
        ThemeAutoDownloadHelper.release();
    }

    @Override
    public boolean onBackPressed() {
        if (!hideContinueDownloadOnMobileDialog() && !hideRequestUseNetworkDialog() && !hideRequestDownloadOnMobileDialog()) {
            cancel();
        }
        return super.onBackPressed();
    }

    @Override
    public void onIconClick(View view, int position, Object item) {
        GLog.d(TAG, "onIconClick pos = " + position + ", item = " + item);
        if (item instanceof ThemeItem) {
            final ThemeItem data = (ThemeItem) item;
            EditorThemeUIController uiController = (EditorThemeUIController) getUIController();
            uiController.setCurrentSelectedPosition(position);
            if ((mEngineManager != null)) {
                if (data.isNeedDownloadFile()) {
                    boolean requestNetwork = !NetworkPermissionManager.isUseOpenNetwork();
                    if (checkIfNeedShowPrivacyDialog()) {
                        showPrivacyDialog(mContext);
                        mWaitingDownloadMap.put(position, data);
                    } else if (requestNetwork) {
                        showNetworkPermissionDialog();
                        mWaitingDownloadMap.put(position, data);
                    } else if (!NetworkMonitor.isNetworkValidated()) {
                        handleNoNetworkConnection(view, uiController);
                    } else if (!NetworkMonitor.isWifiValidated()
                            && NetworkMonitor.isMobileValidated()
                            && !NetworkPermissionManager.isAllowDownloadOnMobile()) {
                        showRequestDownloadOnMobileDialog();
                        mWaitingDownloadMap.put(position, data);
                    } else {
                        ThemeSourceManager.getInstance().downloadMemoriesTheme(data,
                                LocalSourceManager.DOWNLOAD_MANUAL);
                    }
                } else if (!data.equals(mCurrentSelectedItem)) {
                    uiController.setLastApplyItem(data);
                    GLog.d(TAG, "onIconClick resourceId =  " + data.getThemeId());
                    ThemeSourceManager.getInstance().resetSource(data.getThemeId());
                    GLog.d(TAG, "onIconClick click curTheme = " + mEngineManager.getCurrentTheme() + ", mPrevTheme = " + mPrevTheme);
                    mEngineManager.stop();
                    if (mContext instanceof Activity) {
                        GLog.d(TAG, "onIconClick pos = " + position + ", item = " + item);
                        mCurrentSelectedItem = data;
                        ((Activity) mContext).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (mEngineManager.addTheme(data.convertToThemeInfo())) {
                                    if (ActivityLifecycle.isRunningForeground()) {
                                        mEngineManager.play();
                                    }
                                }
                            }
                        });
                    } else {
                        GLog.w(TAG, "onIconClick change theme failed, pos = " + position + ", item = " + item);
                    }
                }
            }
        }
    }

    private void handleNoNetworkConnection(View view, EditorThemeUIController uiController) {
        ToastUtil.showShortToast(R.string.videoeditor_editor_no_network);
        if (view != null) {
            view.post(new Runnable() {
                @Override
                public void run() {
                    uiController.selectedLastApplyItem();
                    uiController.getAdapter().notifyDataSetChanged();
                }
            });
        }
    }

    @Override
    public void onAllowOpenNetwork() {
        updateNetworkData();
    }

    private void updateNetworkData() {
        if (!NetworkMonitor.isNetworkValidated()) {
            EditorThemeUIController uiController = (EditorThemeUIController) getUIController();
            handleNoNetworkConnection(mControlBarView, uiController);
            return;
        }
        OnLoadingListenerImpl themeLoadingListener = new OnLoadingListenerImpl<ThemeItem>() {
            @Override
            public void onLoadingFinish(int code, @Nullable List<ThemeItem> allEntityList) {
                if (mUIController instanceof EditorThemeUIController) {
                    ((EditorThemeUIController) mUIController).refresh(allEntityList);
                }
                ThemeSourceManager.getInstance().retryDownload();
                startDownloadRemain();
            }
        };
        OnLoadingListenerImpl musicLoadingListener = new OnLoadingListenerImpl<MusicItem>() {
            @Override
            public void onLoadingFinish(int code, @Nullable List<MusicItem> allEntityList) {
                ThemeSourceManager.getInstance().requestNetworkResource(themeLoadingListener);
            }
        };
        MusicSourceManager.getInstance().requestNetworkResource(musicLoadingListener);

    }

    @Override
    public void onAllowUseMobileData() {
        ThemeSourceManager.getInstance().retryDownload();
        startDownloadRemain();
    }

    @Override
    public void onDisallowOpenNetwork() {
        ((EditorThemeUIController) getUIController()).selectedLastApplyItem();
        mWaitingDownloadMap.clear();
        LocalSourceManager.clearAllDownloadInfo();
        getUIController().notifyDataSetChanged();
    }

    @Override
    public void onDisallowUseMobileData() {
        ((EditorThemeUIController) getUIController()).selectedLastApplyItem();
        mWaitingDownloadMap.clear();
        LocalSourceManager.clearAllDownloadInfo();
        getUIController().notifyDataSetChanged();
    }

    @Override
    public void onNetworkError() {
        ThemeSourceManager.getInstance().resetDownloadQueueStatus();
        ((EditorThemeUIController) getUIController()).selectedLastApplyItem();
    }

    @Override
    public void retryRequestSource() {
        GLog.d(TAG, "retryDownload");
        ThemeSourceManager.getInstance().retryDownload();
    }

    @Override
    public int requestDownloadOnMobileDialogTitle() {
        return R.string.videoeditor_video_editor_confirm_download_theme;
    }

    @Override
    public int requestDownloadOnMobileDialogMsg() {
        return R.string.videoeditor_video_editor_network_change_tip;
    }

    @Override
    public int requestUseNetworkDialogTitle() {
        return com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_title;
    }

    @Override
    public int requestUseNetworkDialogMessage() {
        return com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_theme;
    }

    @Override
    public int networkErrorToastMsg() {
        return R.string.videoeditor_editor_theme_download_network_disconnect;
    }

    @Override
    public int downloadFailureToastMsg() {
        return R.string.videoeditor_editor_theme_download_fail;
    }

    @Override
    public boolean hasUserDownloadTask() {
        int themeDownloadTaskSize = ThemeSourceManager.getInstance().getDownloadTaskSize();
        int musicDownloadTaskSize = MusicSourceManager.getInstance().getDownloadTaskSize();
        return (themeDownloadTaskSize + musicDownloadTaskSize) > 0;
    }

    @Override
    public void onChangeToNetwork() {
        super.onChangeToNetwork();
        EditorThemeUIController controller = (EditorThemeUIController) getUIController();
        if ((controller != null) && !isHasUpdateList()) {
            controller.updateNetworkSourceLists();
        }
    }

    private void startDownloadRemain() {
        for (Map.Entry<Integer, ThemeItem> entry : mWaitingDownloadMap.entrySet()) {
            int id = entry.getValue().getThemeId();
            ThemeItem item = ThemeSourceManager.getInstance().getThemeEntity(id);
            onIconClick(null, entry.getKey(), item);
        }
    }

}
