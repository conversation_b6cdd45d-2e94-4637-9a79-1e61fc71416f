/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ExitConfirmDialog.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.ui

import android.content.Context
import android.content.DialogInterface
import android.view.View
import androidx.annotation.ColorRes
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper

/**
 * 退出编辑二次确认弹框
 */
class ExitConfirmDialog(val context: Context) {

    /**
     * 退出编辑确认弹框监听器
     */
    var exitDialogListener: ExitDialogListener? = null

    /**
     * 退出编辑二次确认弹框
     */
    private var exitDialog: AlertDialog? = null

    /**
     * 显示进度弹框
     */
    fun show() {
        if (exitDialog == null) {
            val builder = COUIAlertDialogBuilder(
                context,
                com.support.appcompat.R.style.Theme_COUI_Dark_Yellow,
                com.support.dialog.R.style.COUIAlertDialog_BottomWarning
            ).apply {
                setTitle(R.string.videoeditor_editor_text_abandon_current_modify)
                setPositiveButton(R.string.videoeditor_editor_text_abandon_amend) { _, _ ->
                    exitDialogListener?.onAbandonAmendClick()
                }
                setNegativeButton(R.string.videoeditor_cancel) { _, _ ->
                    exitDialogListener?.onCancelClick()
                }
            }
            exitDialog = builder.show()
            val posBtn = exitDialog?.getButton(DialogInterface.BUTTON_POSITIVE) as? COUIButton
            val negBtn = exitDialog?.getButton(DialogInterface.BUTTON_NEGATIVE) as? COUIButton
            posBtn?.setTextColor(ContextCompat.getColor(context, R.color.color_FF6C61))
            negBtn?.setTextColor(ThemeHelper.getCouiColorContainerTheme(context))
            exitDialog?.setDialogBackgroundColor()
        } else if (exitDialog?.isShowing?.not() == true) {
            GLog.d(TAG, LogFlag.DL) { "[show] show exit dialog" }
            exitDialog?.show()
        }
    }

    /**
     * 设置对话框背景颜色
     * 此函数通过接受一个颜色资源ID来更改对话框的背景颜色
     * 它首先找到对话框中的根视图，然后设置其背景颜色为指定的颜色
     * @param colorRes 颜色资源ID，默认值为R.color.coui_textview_title_text_color 这个参数指定了要设置的背景颜色
     */
    private fun AlertDialog?.setDialogBackgroundColor(@ColorRes colorRes: Int = com.support.appcompat.R.color.coui_textview_title_text_color) {
        this?.findViewById<View>(com.support.dialog.R.id.rootView)?.setBackgroundColor(ContextCompat.getColor(context, colorRes))
    }

    /**
     * 结束弹框
     */
    fun dismiss() {
        exitDialog?.dismiss()
    }

    internal companion object {
        private const val TAG = "ExitConfirmDialog"
    }
}

/**
 * 退出编辑确认弹框监听器
 */
interface ExitDialogListener {
    /**
     * 点击取消回调
     */
    fun onCancelClick()

    /**
     * 点击放弃并退出回调
     */
    fun onAbandonAmendClick()
}