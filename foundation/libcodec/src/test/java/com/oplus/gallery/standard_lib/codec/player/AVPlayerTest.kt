/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - AVPlayerTest.kt
 * Description:
 * Version: 1.0
 * Date: 2020/09/04
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D  2020/09/04       1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec.player

import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter
import io.mockk.every
import io.mockk.just
import io.mockk.mockkClass
import io.mockk.runs
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AVPlayerTest {

    private lateinit var avPlayer: AVPlayer
    private var playbackStateAns = AVController.PlaybackState.IDLE

    @Before
    fun setUp() {
        avPlayer = mockkClass(AVPlayer::class)

        every { avPlayer.runOnPlayerThread(any(), any(), any()) } answers {
            (this.args[2] as (() -> Unit)).invoke()
        }
        every { avPlayer.updatePlaybackState(any()) } answers {
            playbackStateAns = this.args[0] as AVController.PlaybackState
        }
        every { avPlayer.debugInfo } returns "null"
    }

    @Test
    fun should_do_function_when_start_with_different_state() {
        //given
        every { avPlayer.play(playAfterPrepare = true, needPrepare = true) } answers { callOriginal() }
        every { avPlayer.preparePlayer() } answers { callOriginal() }
        every { avPlayer.startPlayer() } answers { callOriginal() }
        every { avPlayer.player.start() } just runs
        every { avPlayer.player.prepare() } just runs
        every { avPlayer.getCurrentPosition() } returns -1
        every { avPlayer.getDuration() } returns 0

        //when
        for (currentState in AVController.PlaybackState.values()) {
            playbackStateAns = AVController.PlaybackState.IDLE
            every { avPlayer.playbackState } returns currentState
            avPlayer.play(playAfterPrepare = true, needPrepare = true)

            //then
            when (currentState) {
                AVController.PlaybackState.INITIALIZED,
                AVController.PlaybackState.STOPPED,
                AVController.PlaybackState.ERROR -> Assert.assertEquals(playbackStateAns, AVController.PlaybackState.PREPARING)
                AVController.PlaybackState.PREPARED,
                AVController.PlaybackState.PAUSED,
                AVController.PlaybackState.COMPLETED -> Assert.assertEquals(playbackStateAns, AVController.PlaybackState.STARTED)
                else -> Assert.assertEquals(playbackStateAns, AVController.PlaybackState.IDLE)
            }
        }
    }

    @Test
    fun should_do_function_when_pause_with_player_isPlaying() {
        //given
        every { avPlayer.pause() } answers { callOriginal() }
        every { avPlayer.isPlaying() } returns true
        every { avPlayer.isPlayerPlaying() } returns true
        every { avPlayer.player.pause() } just runs

        //when
        avPlayer.pause()

        //then
        Assert.assertEquals(playbackStateAns, AVController.PlaybackState.PAUSED)
    }

    @Test
    fun should_do_function_when_pause_with_player_not_playing() {
        //given
        every { avPlayer.pause() } answers { callOriginal() }
        every { avPlayer.isPlaying() } returns false
        every { avPlayer.player.pause() } just runs

        //when
        avPlayer.pause()

        //then
        Assert.assertEquals(playbackStateAns, AVController.PlaybackState.IDLE)
    }

    @Test
    fun should_do_function_when_stop_with_player_not_playing() {
        //given
        val playingInfo = AVController.PlayingInfo()
        every { avPlayer.playingInfo } returns playingInfo
        every { avPlayer.stop() } answers { callOriginal() }
        every { avPlayer.reset() } answers { callOriginal() }
        every { avPlayer.player.stop() } just runs
        every { avPlayer.player.reset() } just runs

        //when
        for (currentState in AVController.PlaybackState.values()) {
            playbackStateAns = AVController.PlaybackState.IDLE
            every { avPlayer.playbackState } returns currentState
            avPlayer.stop()

            //then
            when (currentState) {
                AVController.PlaybackState.PREPARED,
                AVController.PlaybackState.PREPARING,
                AVController.PlaybackState.STARTED,
                AVController.PlaybackState.PAUSED,
                AVController.PlaybackState.COMPLETED -> Assert.assertEquals(playbackStateAns, AVController.PlaybackState.STOPPED)
                else -> Assert.assertEquals(playbackStateAns, AVController.PlaybackState.IDLE)
            }
        }
    }

    @Test
    fun should_send_report_when_add_listener_with_error_state() {
        //given
        every { avPlayer.addOnEventListener(any(), any()) } answers { callOriginal() }
        var onEventListener: AVController.OnEventListener? = null
        every { avPlayer.onEventListener.set(any(), any()) } answers {
            onEventListener = args[0] as AVController.OnEventListener
        }
        every { avPlayer.sendEvent(any(), any()) } answers {
            onEventListener?.let {
                (this.args[1] as (AVController.OnEventListener) -> Unit).invoke(it)
            }
        }

        val listener = object : AVController.OnEventListener {
            override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
                Assert.assertEquals(what, AVController.ERROR_PLAYBACK_FAILURE)
            }

            override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            }

            override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
                Assert.assertEquals(state, AVController.PlaybackState.ERROR)
            }
        }

        //when
        every { avPlayer.playbackState } returns AVController.PlaybackState.ERROR
        avPlayer.addOnEventListener(listener, null)
    }

    @Test
    fun should_send_report_when_add_listener_with_ready_state() {
        //given
        every { avPlayer.addOnEventListener(any(), any()) } answers { callOriginal() }
        var onEventListener: AVController.OnEventListener? = null
        every { avPlayer.onEventListener.set(any(), any()) } answers {
            onEventListener = args[0] as AVController.OnEventListener
        }
        every { avPlayer.sendEvent(any(), any()) } answers {
            onEventListener?.let {
                (this.args[1] as (AVController.OnEventListener) -> Unit).invoke(it)
            }
        }

        val listener = object : AVController.OnEventListener {
            override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
            }

            override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
                Assert.assertEquals(what, AVController.INFO_PLAYING_INFO_READY)
            }

            override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
                Assert.assertEquals(state, AVController.PlaybackState.STARTED)
            }
        }

        //when
        every { avPlayer.playbackState } returns AVController.PlaybackState.STARTED
        avPlayer.addOnEventListener(listener, null)
    }

    @Test
    fun should_become_prepared_when_onPrepared() {
        //given
        every { avPlayer.onPrepared(any()) } answers { callOriginal() }
        every { avPlayer.setMute(any()) } just runs
        val playingInfo = AVController.PlayingInfo()
        every { avPlayer.playingInfo } returns playingInfo
        every { avPlayer.player.getVideoHeight() } returns 1
        every { avPlayer.player.getVideoWidth() } returns 2
        every { avPlayer.player.getDuration() } returns 3
        every { avPlayer.onInfo(any(), any(), any()) } just runs
        val player = mockkClass(PlayerAdapter::class)

        //when
        avPlayer.onPrepared(player)

        //then
        Assert.assertEquals(playbackStateAns, AVController.PlaybackState.PREPARED)
    }
}