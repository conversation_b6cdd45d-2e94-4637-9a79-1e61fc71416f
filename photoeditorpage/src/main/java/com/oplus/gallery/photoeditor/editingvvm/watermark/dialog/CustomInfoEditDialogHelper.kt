/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CustomInfoEditDialogHelper
 ** Description: 水印自定义信息弹框
 ** Version: 1.0
 ** Date : 2023/2/11
 ** Author: zhangshangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  z<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D    2023/2/11    1.0         first created
 **  xing<PERSON>fan@Apps.Gallery3D        2023/6/13    1.1         support CountEnable and showError
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.watermark.dialog

import android.content.Context
import android.text.Spanned
import android.text.TextUtils
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.widget.doAfterTextChanged
import androidx.core.widget.doOnTextChanged
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.edittext.COUIInputView
import com.oplus.gallery.foundation.ui.dialog.EditDialog
import com.oplus.gallery.foundation.util.ext.isActivityInvalid
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.text.TextUtil.addInputFilter
import com.oplus.gallery.foundation.util.text.TextUtil.addLengthInputFilter
import com.oplus.gallery.foundation.util.text.TextUtil.containsEmoji
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.util.setDialogBackgroundColor
import com.oplus.gallery.basebiz.R as BaseR

class CustomInfoEditDialogHelper @JvmOverloads constructor(
    context: Context,
    colorThemeResId: Int = com.support.appcompat.R.style.Theme_COUI_Dark_Yellow,
    dialogStyleResId: Int = com.support.dialog.R.style.COUIAlertDialog_BottomWarning
) : COUIAlertDialogBuilder(context, colorThemeResId, dialogStyleResId) {
    private var editDialog: EditDialog? = null
    private var inputView: COUIInputView? = null

    private var title: String = EMPTY_STRING
    private var content: String = EMPTY_STRING
    private var hintText: String = EMPTY_STRING
    private var inputCountEnable = false
    private var inputMaxCount: Int = COUNT_ZERO
    private var maxCountIgnoreLanguage = false
    private var enableError = false
    private var isCanceledOnTouchOutside = true
    private var cancelable = true
    private var supportEmoji = true

    private var inputErrorType = INPUT_DEFAULT

    private var confirmListener: ConfirmListener? = null
    private var inputListener: InputListener? = null

    /**
     * 显示弹框
     */
    override fun show(): AlertDialog? {
        if (context.isActivityInvalid() || (editDialog?.isShowing() == true)) {
            return null
        }
        val style = if (inputCountEnable) R.style.CustomEditDialogWithInputCount else R.style.CustomEditDialogWithoutInputCount

        editDialog = EditDialog.Builder(context, style).setPositiveButton(BaseR.string.common_save) { _, _ ->
            onConfirmed()
        }.setNegativeButton(BaseR.string.common_cancel) { _, _ ->
            confirmListener?.onCancelled()
        }.setOnCancelListener {
            confirmListener?.onCancelled()
        }.setTitle(title)
            .setCancelable(cancelable)
            .setCanceledOnTouchOutside(isCanceledOnTouchOutside).build().show()
        setEditText(content, hintText, true)
        inputView?.editText?.autoShowKeyboard()
        return editDialog?.getRealDialog().apply {
            setDialogBackgroundColor()
        }
    }

    /**
     * 显示输入框下面的错误提示
     * @param errorMsg 提示内容
     */
    fun showError(errorMsg: CharSequence) {
        if (inputView?.editText?.isErrorState == true) {
            inputView?.editText?.isErrorState = false
        }
        inputView?.showError(errorMsg)
    }

    /**
     * 弹框消失
     */
    fun dismiss() {
        if (editDialog?.isShowing() == true) {
            editDialog?.dismiss()
        }
        removeAllListener()
    }

    /**
     * 移除弹框所有监听
     */
    private fun removeAllListener() {
        confirmListener = null
        inputListener = null
    }

    private fun onConfirmed() {
        val newText: String = inputView?.editText?.text.toString().trim { it <= ' ' }
        confirmListener?.onConfirmed(newText)
    }

    class Builder {
        private var context: Context? = null
        private var title: String = EMPTY_STRING
        private var content: String = EMPTY_STRING
        private var hintText: String = EMPTY_STRING
        private var inputCountEnable = false
        private var inputMaxCount: Int = COUNT_ZERO
        private var maxCountIgnoreLanguage = false
        private var enableError = false
        private var isCanceledOnTouchOutside = true
        private var cancelable = true
        private var supportEmoji = true

        private var confirmListener: ConfirmListener? = null
        private var inputListener: InputListener? = null

        fun setTitle(title: String?): Builder {
            this.title = title ?: EMPTY_STRING
            return this
        }

        fun setHintText(hint: String?): Builder {
            this.hintText = hint ?: EMPTY_STRING
            return this
        }

        fun setContent(content: String?): Builder {
            this.content = content ?: EMPTY_STRING
            return this
        }

        fun setContext(context: Context): Builder {
            this.context = context
            return this
        }

        fun setInputCountEnable(inputCountEnable: Boolean): Builder {
            this.inputCountEnable = inputCountEnable
            return this
        }

        fun setInputMaxCount(inputMaxCount: Int, ignoreLanguage: Boolean = false): Builder {
            this.inputMaxCount = inputMaxCount
            this.maxCountIgnoreLanguage = ignoreLanguage
            return this
        }


        fun setEnableError(enableError: Boolean): Builder {
            this.enableError = enableError
            return this
        }

        fun setConfirmListener(listener: ConfirmListener?): Builder {
            this.confirmListener = listener
            return this
        }

        fun setInputListener(listener: InputListener?): Builder {
            this.inputListener = listener
            return this
        }

        fun setCancelable(cancel: Boolean): Builder {
            this.cancelable = cancel
            return this
        }

        fun setCanceledOnTouchOutside(cancel: Boolean): Builder {
            this.isCanceledOnTouchOutside = cancel
            return this
        }

        fun setSupportEmoji(supportEmoji: Boolean): Builder {
            this.supportEmoji = supportEmoji
            return this
        }

        fun create(): CustomInfoEditDialogHelper? {
            val dialog = context?.let { CustomInfoEditDialogHelper(it) }
            dialog?.apply {
                title = <EMAIL>
                content = <EMAIL>
                hintText = <EMAIL>
                inputCountEnable = <EMAIL>
                inputMaxCount = <EMAIL>
                maxCountIgnoreLanguage = <EMAIL>
                enableError = <EMAIL>
                confirmListener = <EMAIL>
                inputListener = <EMAIL>
                cancelable = <EMAIL>
                isCanceledOnTouchOutside = <EMAIL>
                supportEmoji = <EMAIL>
            }
            return dialog
        }
    }

    interface ConfirmListener {
        fun onConfirmed(text: String?)
        fun onCancelled()
        fun onTextChanged(text: String?)
    }

    interface InputListener {
        fun onInputVerified(type: Int)
    }

    @Suppress("LongMethod")
    private fun setEditText(
        content: CharSequence = EMPTY_STRING,
        hintText: String = EMPTY_STRING,
        isSelectAll: Boolean = false
    ) {
        inputView = editDialog?.findViewById<COUIInputView>(R.id.input_first)
        inputView?.let {
            it.editText?.apply {
                this.hint = hintText
                this.ellipsize = TextUtils.TruncateAt.END
                setText(content)
                setSingleLine()
                if (isSelectAll) {
                    selectAll()
                }
                // 表情包过滤器， 隐私水印不支持输入表情包
                addInputFilter(this) { source: CharSequence, start: Int, end: Int, dest: Spanned, dstart: Int, dend: Int ->
                    return@addInputFilter when {
                        !supportEmoji && containsEmoji(source) -> {
                            inputErrorType = INPUT_CONTAINS_EMOJI
                            dest.subSequence(dstart, dend)
                        }

                        else -> {
                            inputErrorType = INPUT_DEFAULT
                            source
                        }
                    }
                }
                // 最大输入过滤器，根据通用水印，新春水印，隐私水印等传进来的最大输入过滤
                addLengthInputFilter(this, inputMaxCount, null, false)
                doAfterTextChanged {
                    /**
                     * 必须在doAfterTextChanged回调，因为控件sdk COUIErrorEditTextHelper监听了afterTextChanged，会重置setErrorState
                     * 如果在afterTextChanged之前的回调中调用inputListener，就会出现业务层调用showError显示错误提示，但是紧接着COUIErrorEditTextHelper
                     * 调用setErrorState重置错误状态，错误提示又立刻消失的问题
                     */
                    notifyInputError(inputErrorType)
                    editDialog?.setPositiveButtonEnable(this.text?.isNotEmpty() == true)
                }
                doOnTextChanged { text, _, _, _ ->
                    val newText: String? = text?.toString()?.trim { s -> s <= ' ' }
                    confirmListener?.onTextChanged(newText)
                }
            }

            if (inputCountEnable) {
                it.maxCount = inputMaxCount
            }
            it.setEnableError(enableError)
        }
        editDialog?.setPositiveButtonEnable(content.isNotEmpty())
    }

    private fun COUIEditText.autoShowKeyboard() {
        isFocusable = true
        requestFocus()
        editDialog?.getWindow()?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    private fun notifyInputError(errorType: Int) {
        if (errorType != INPUT_DEFAULT) {
            inputListener?.onInputVerified(errorType)
        }
    }

    companion object {
        private const val TAG = "CustomInfoEditDialogHelper"
        private const val COUNT_ZERO = 0

        const val INPUT_DEFAULT = 0
        const val INPUT_CONTAINS_EMOJI = 1
    }
}