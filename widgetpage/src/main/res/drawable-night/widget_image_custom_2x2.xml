<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="148dp"
    android:height="148dp"
    android:viewportWidth="148"
    android:viewportHeight="148">
  <group>
    <clip-path
        android:pathData="M0,0h148v148h-148z"/>
    <path
        android:pathData="M0,0h148v148h-148z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="27"
            android:startY="-17"
            android:endX="123"
            android:endY="159"
            android:type="linear">
          <item android:offset="0" android:color="#FF282929"/>
          <item android:offset="1" android:color="#FF252627"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M141.52,63.1L-27.28,63.1L-27.28,62.1L141.52,62.1L141.52,63.1Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="-27.28"
            android:startY="63.6"
            android:endX="141.52"
            android:endY="63.6"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.37" android:color="#FF36393B"/>
          <item android:offset="0.62" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M141.52,127.9L-111.28,127.9L-111.28,126.9L141.52,126.9L141.52,127.9Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="-111.28"
            android:startY="128.4"
            android:endX="141.52"
            android:endY="128.4"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.22" android:color="#FF36393B"/>
          <item android:offset="0.56" android:color="#FF36393B"/>
          <item android:offset="0.85" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.36,95.2H-51.92V94.2H19.36V95.2Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="-51.92"
            android:startY="95.2"
            android:endX="19.36"
            android:endY="95.2"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.22" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M142.48,94.96L84.4,94.96L84.4,93.96L142.48,93.96L142.48,94.96Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="84.4"
            android:startY="94.46"
            android:endX="130.57"
            android:endY="94.46"
            android:type="linear">
          <item android:offset="0" android:color="#0044484B"/>
          <item android:offset="0.14" android:color="#FF36393B"/>
          <item android:offset="0.6" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0044484B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M169.76,31.22H18.08V30.22H169.76V31.22Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="91.26"
            android:startY="31.22"
            android:endX="169.76"
            android:endY="31.72"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.36" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M116,118L116,0L117,0L117,118L116,118Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="115.5"
            android:startY="0"
            android:endX="115.67"
            android:endY="83.22"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.17" android:color="#FF36393B"/>
          <item android:offset="0.58" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.12,193.34L19.12,32.06L20.12,32.06L20.12,193.34L19.12,193.34Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="19.12"
            android:startY="45.83"
            android:endX="18.62"
            android:endY="148.23"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.32" android:color="#FF36393B"/>
          <item android:offset="0.83" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M51.42,62.54L51.42,32.06L52.42,32.06L52.42,62.54L51.42,62.54ZM51.42,193.34L51.42,127.58L52.42,127.58L52.42,193.34L51.42,193.34Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="51.92"
            android:startY="38.18"
            android:endX="51.42"
            android:endY="193.34"
            android:type="linear">
          <item android:offset="0" android:color="#5636393B"/>
          <item android:offset="0.68" android:color="#8936393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M84.4,33.07L84.4,32.06L85.4,32.06L85.4,33.07L84.4,33.07ZM84.4,37.1L84.4,35.09L85.4,35.09L85.4,37.1L84.4,37.1ZM84.4,41.13L84.4,39.12L85.4,39.12L85.4,41.13L84.4,41.13ZM84.4,45.17L84.4,43.15L85.4,43.15L85.4,45.17L84.4,45.17ZM84.4,49.2L84.4,47.18L85.4,47.18L85.4,49.2L84.4,49.2ZM84.4,53.23L84.4,51.21L85.4,51.21L85.4,53.23L84.4,53.23ZM84.4,57.26L84.4,55.25L85.4,55.25L85.4,57.26L84.4,57.26ZM84.4,61.29L84.4,59.28L85.4,59.28L85.4,61.29L84.4,61.29ZM84.4,65.33L84.4,63.31L85.4,63.31L85.4,65.33L84.4,65.33ZM84.4,69.36L84.4,67.34L85.4,67.34L85.4,69.36L84.4,69.36ZM84.4,73.39L84.4,71.37L85.4,71.37L85.4,73.39L84.4,73.39ZM84.4,77.42L84.4,75.41L85.4,75.41L85.4,77.42L84.4,77.42ZM84.4,81.45L84.4,79.44L85.4,79.44L85.4,81.45L84.4,81.45ZM84.4,85.49L84.4,83.47L85.4,83.47L85.4,85.49L84.4,85.49ZM84.4,89.52L84.4,87.5L85.4,87.5L85.4,89.52L84.4,89.52ZM84.4,93.55L84.4,91.53L85.4,91.53L85.4,93.55L84.4,93.55ZM84.4,97.58L84.4,95.57L85.4,95.57L85.4,97.58L84.4,97.58ZM84.4,101.61L84.4,99.6L85.4,99.6L85.4,101.61L84.4,101.61ZM84.4,105.65L84.4,103.63L85.4,103.63L85.4,105.65L84.4,105.65ZM84.4,109.68L84.4,107.66L85.4,107.66L85.4,109.68L84.4,109.68ZM84.4,113.71L84.4,111.69L85.4,111.69L85.4,113.71L84.4,113.71ZM84.4,117.74L84.4,115.73L85.4,115.73L85.4,117.74L84.4,117.74ZM84.4,121.77L84.4,119.76L85.4,119.76L85.4,121.77L84.4,121.77ZM84.4,125.81L84.4,123.79L85.4,123.79L85.4,125.81L84.4,125.81ZM84.4,129.84L84.4,127.82L85.4,127.82L85.4,129.84L84.4,129.84ZM84.4,133.87L84.4,131.85L85.4,131.85L85.4,133.87L84.4,133.87ZM84.4,137.9L84.4,135.88L85.4,135.88L85.4,137.9L84.4,137.9ZM84.4,141.93L84.4,139.92L85.4,139.92L85.4,141.93L84.4,141.93ZM84.4,145.96L84.4,143.95L85.4,143.95L85.4,145.96L84.4,145.96ZM84.4,150L84.4,147.98L85.4,147.98L85.4,150L84.4,150ZM84.4,154.03L84.4,152.01L85.4,152.01L85.4,154.03L84.4,154.03ZM84.4,158.06L84.4,156.04L85.4,156.04L85.4,158.06L84.4,158.06ZM84.4,162.09L84.4,160.08L85.4,160.08L85.4,162.09L84.4,162.09ZM84.4,166.13L84.4,164.11L85.4,164.11L85.4,166.13L84.4,166.13ZM84.4,170.16L84.4,168.14L85.4,168.14L85.4,170.16L84.4,170.16ZM84.4,174.19L84.4,172.17L85.4,172.17L85.4,174.19L84.4,174.19ZM84.4,178.22L84.4,176.21L85.4,176.21L85.4,178.22L84.4,178.22ZM84.4,182.25L84.4,180.24L85.4,180.24L85.4,182.25L84.4,182.25ZM84.4,186.29L84.4,184.27L85.4,184.27L85.4,186.29L84.4,186.29ZM84.4,190.32L84.4,188.3L85.4,188.3L85.4,190.32L84.4,190.32ZM84.4,193.34L84.4,192.33L85.4,192.33L85.4,193.34L84.4,193.34Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="83.9"
            android:startY="32.06"
            android:endX="84.4"
            android:endY="154.07"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.13" android:color="#FF36393B"/>
          <item android:offset="0.59" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M84.4,127.58L84.4,62.78L85.2,62.78L85.2,127.58L84.4,127.58Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M62.05,87.74L78.16,118.46H46L62.05,87.74Z"
        android:fillColor="#2A2C2D"/>
    <path
        android:pathData="M61.12,84.87L79.94,120.68L79.05,121.15L60.24,85.33L61.12,84.87Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="61.12"
            android:startY="84.87"
            android:endX="79.94"
            android:endY="120.69"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.07" android:color="#FF36393B"/>
          <item android:offset="0.92" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M63.67,86.05L57.48,97.96L56.6,97.5L62.79,85.59L63.67,86.05Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="63.67"
            android:startY="86.05"
            android:endX="57.48"
            android:endY="97.96"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.16" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M46,76.46L67.84,118.22H24.25L46,76.46Z"
        android:fillColor="#2E3031"/>
    <path
        android:pathData="M81.52,118.46L20.56,118.46L20.56,117.46L81.52,117.46L81.52,118.46Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="20.56"
            android:startY="118.96"
            android:endX="81.52"
            android:endY="118.96"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.06" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M45.03,73.11L69.68,120.63L68.79,121.09L44.14,73.57L45.03,73.11Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="45.03"
            android:startY="73.11"
            android:endX="69.68"
            android:endY="120.63"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M46.71,73.59L22.06,121.11L22.95,121.57L47.6,74.05L46.71,73.59Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="46.71"
            android:startY="73.59"
            android:endX="22.06"
            android:endY="121.11"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M68.56,83.74C71.65,83.74 74.16,81.23 74.16,78.14C74.16,75.05 71.65,72.54 68.56,72.54C65.47,72.54 62.96,75.05 62.96,78.14C62.96,81.23 65.47,83.74 68.56,83.74ZM68.56,84.38C72.01,84.38 74.8,81.59 74.8,78.14C74.8,74.7 72.01,71.9 68.56,71.9C65.11,71.9 62.32,74.7 62.32,78.14C62.32,81.59 65.11,84.38 68.56,84.38Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M21,62.5C21,63.33 20.33,64 19.5,64C18.67,64 18,63.33 18,62.5C18,61.67 18.67,61 19.5,61C20.33,61 21,61.67 21,62.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M19.5,63.4C20,63.4 20.4,63 20.4,62.5C20.4,62 20,61.6 19.5,61.6C19,61.6 18.6,62 18.6,62.5C18.6,63 19,63.4 19.5,63.4ZM19.5,64C20.33,64 21,63.33 21,62.5C21,61.67 20.33,61 19.5,61C18.67,61 18,61.67 18,62.5C18,63.33 18.67,64 19.5,64Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M21,95C21,95.83 20.33,96.5 19.5,96.5C18.67,96.5 18,95.83 18,95C18,94.17 18.67,93.5 19.5,93.5C20.33,93.5 21,94.17 21,95Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M19.5,95.9C20,95.9 20.4,95.5 20.4,95C20.4,94.5 20,94.1 19.5,94.1C19,94.1 18.6,94.5 18.6,95C18.6,95.5 19,95.9 19.5,95.9ZM19.5,96.5C20.33,96.5 21,95.83 21,95C21,94.17 20.33,93.5 19.5,93.5C18.67,93.5 18,94.17 18,95C18,95.83 18.67,96.5 19.5,96.5Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M21.16,127.5C21.16,128.33 20.49,129 19.66,129C18.83,129 18.16,128.33 18.16,127.5C18.16,126.67 18.83,126 19.66,126C20.49,126 21.16,126.67 21.16,127.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M19.66,128.4C20.16,128.4 20.56,128 20.56,127.5C20.56,127 20.16,126.6 19.66,126.6C19.16,126.6 18.76,127 18.76,127.5C18.76,128 19.16,128.4 19.66,128.4ZM19.66,129C20.49,129 21.16,128.33 21.16,127.5C21.16,126.67 20.49,126 19.66,126C18.83,126 18.16,126.67 18.16,127.5C18.16,128.33 18.83,129 19.66,129Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M53.5,127.5C53.5,128.33 52.83,129 52,129C51.17,129 50.5,128.33 50.5,127.5C50.5,126.67 51.17,126 52,126C52.83,126 53.5,126.67 53.5,127.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M52,128.4C52.5,128.4 52.9,128 52.9,127.5C52.9,127 52.5,126.6 52,126.6C51.5,126.6 51.1,127 51.1,127.5C51.1,128 51.5,128.4 52,128.4ZM52,129C52.83,129 53.5,128.33 53.5,127.5C53.5,126.67 52.83,126 52,126C51.17,126 50.5,126.67 50.5,127.5C50.5,128.33 51.17,129 52,129Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M53.5,62.5C53.5,63.33 52.83,64 52,64C51.17,64 50.5,63.33 50.5,62.5C50.5,61.67 51.17,61 52,61C52.83,61 53.5,61.67 53.5,62.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M52,63.4C52.5,63.4 52.9,63 52.9,62.5C52.9,62 52.5,61.6 52,61.6C51.5,61.6 51.1,62 51.1,62.5C51.1,63 51.5,63.4 52,63.4ZM52,64C52.83,64 53.5,63.33 53.5,62.5C53.5,61.67 52.83,61 52,61C51.17,61 50.5,61.67 50.5,62.5C50.5,63.33 51.17,64 52,64Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M86.5,62.5C86.5,63.33 85.83,64 85,64C84.17,64 83.5,63.33 83.5,62.5C83.5,61.67 84.17,61 85,61C85.83,61 86.5,61.67 86.5,62.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M85,63.4C85.5,63.4 85.9,63 85.9,62.5C85.9,62 85.5,61.6 85,61.6C84.5,61.6 84.1,62 84.1,62.5C84.1,63 84.5,63.4 85,63.4ZM85,64C85.83,64 86.5,63.33 86.5,62.5C86.5,61.67 85.83,61 85,61C84.17,61 83.5,61.67 83.5,62.5C83.5,63.33 84.17,64 85,64Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M86.44,95C86.44,95.83 85.77,96.5 84.94,96.5C84.11,96.5 83.44,95.83 83.44,95C83.44,94.17 84.11,93.5 84.94,93.5C85.77,93.5 86.44,94.17 86.44,95Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M84.94,95.9C85.44,95.9 85.84,95.5 85.84,95C85.84,94.5 85.44,94.1 84.94,94.1C84.44,94.1 84.04,94.5 84.04,95C84.04,95.5 84.44,95.9 84.94,95.9ZM84.94,96.5C85.77,96.5 86.44,95.83 86.44,95C86.44,94.17 85.77,93.5 84.94,93.5C84.11,93.5 83.44,94.17 83.44,95C83.44,95.83 84.11,96.5 84.94,96.5Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M86.44,127.5C86.44,128.33 85.77,129 84.94,129C84.11,129 83.44,128.33 83.44,127.5C83.44,126.67 84.11,126 84.94,126C85.77,126 86.44,126.67 86.44,127.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M84.94,128.4C85.44,128.4 85.84,128 85.84,127.5C85.84,127 85.44,126.6 84.94,126.6C84.44,126.6 84.04,127 84.04,127.5C84.04,128 84.44,128.4 84.94,128.4ZM84.94,129C85.77,129 86.44,128.33 86.44,127.5C86.44,126.67 85.77,126 84.94,126C84.11,126 83.44,126.67 83.44,127.5C83.44,128.33 84.11,129 84.94,129Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
  </group>
</vector>
