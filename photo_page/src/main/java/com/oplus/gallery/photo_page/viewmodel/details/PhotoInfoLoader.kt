/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoSlotLoadingProxy.kt
 ** Description : 大图页详情加载
 ** Version     : 1.0
 ** Date        : 2021/09/23
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jisong.<PERSON>@Apps.Gallery           2021/09/23  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.details

import android.content.Context
import android.icu.text.DateFormat
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isAISceneryPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.KEY_SHARED_FILE
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.ui.dialog.DetailsHelper
import com.oplus.gallery.business_lib.ui.dialog.DetailsHelper.resolveAddress
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.geo.GPS
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_DOLBY_BRIGHTEN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import java.util.Date
import kotlin.coroutines.resume

/**
 * 大图详情
 */
data class PhotoInfo internal constructor(
    val index: Int,
    val id: String,
    val details: Bundle,
    internal val infoCompleteness: InfoCompleteness = InfoCompleteness.BASIC
) {
    // 当前详情是否已加载完整
    internal val isCompletion: Boolean
        get() = infoCompleteness == InfoCompleteness.COMPLETION

    internal enum class InfoCompleteness {
        BASIC, // 基础信息，一般从 MediaItem 推到出来的信息
        MISSING, // 相对完善的信息，不包含通过访问网络获取的信息，如地址信息
        COMPLETION // 完整的信息，包含所有信息
    }

    override fun toString(): String {
        return "[index=$index, id=$id, isCompletion=$isCompletion]"
    }
}

/**
 * 大图页详情加载器。
 *
 * 内部加载流程：
 * 1. 从MediaItem加载数据，同时会尝试加载位置信息。（因为大图标题需要立即显示，最多等待位置请求100ms）
 * 2. 从本地文件和数据库加载数据。
 * 3. 通过网络获取详细信息.(若第一步时位置信息未加载成功，会在此处重新加载)。
 *
 * 注意：内部加载流程只会执行一次（ 首次 [requestStartLoadPhotoInfo] 时执行 ）,后续调用不会重新触发任务。
 *      可通过向[photoInfo]注册监听的方式获取已加载的图片详情。（LiveData注册监听时，会监听到之前已有的数据）
 */
internal class PhotoInfoLoader internal constructor(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
    internal val mediaItem: MediaItem,
    internal val index: Int
) {

    /**
     * [photoInfo] 的监听器,此处不用 LiveData ,因为 LiveData 需要等待主线程的调度,时间太久
     */
    private var photoInfoListener: ((PhotoInfo) -> Unit)? = null

    private var photoInfo: PhotoInfo? = null

    private val isRegionCN: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN)
    }

    /**
     * 日期/时间格式，因为获取时耗时较长，在使用时保证单例。
     */
    private val dateFormat: DateFormat by lazy {
        TimeUtils.getTimeToMinute(context)
    }
    /**
     * 当前加载数据的job
     */
    private var curLoadPhotoInfoJob: Job? = null

    /**
     * 上一次成功解析[PhotoInfo]后的[MediaItem.getDataVersion]，用以判定当前数据是否有变更
     */
    private var mediaItemDataVersion = 0L

    /**
     * 上一次成功解析[PhotoInfo]后的FORMAT_TYPE，用以判定当前tag数据是否有变更
     */
    private var mediaItemFormatType = 0

    /**
     * 请求开始加载 PhotoInfo, 会立即开始加载数据。
     */
    internal fun requestStartLoadPhotoInfo() {
        if ((mediaItemDataVersion == mediaItem.dataVersion)
            && (photoInfo?.isCompletion == true)
            && (mediaItemFormatType == loadFormatType(mediaItem))
        ) {
            GLog.d(TAG) { "[requestStartLoadPhotoInfo], interrupt by same version: $mediaItemDataVersion" }
            // 如果当前的信息已经完善的，且FormatType没有改变直接退出
            return
        }
        mediaItemDataVersion = mediaItem.dataVersion
        curLoadPhotoInfoJob?.cancel()
        curLoadPhotoInfoJob = coroutineScope.launch(Dispatchers.CPU) {
            GTrace.traceBegin("$TAG.requestStartLoadPhotoInfo")
            // 从 MediaItem 获取信息
            loadPhotoInfoFromMediaItem(this).also { photoTag ->
                GLog.d(TAG, LogFlag.DL) { "[loadPhotoInfoFromMediaItem] photoInfo = $photoTag" }
                if (postPhotoInfoIfActive(photoTag).not()) {
                    GTrace.traceEnd()
                    // 任务被取消，退出
                    return@launch
                }
            }?.loadPhotoInfoFromLocalDb()?.also { localDetailInfo ->
                // 此阶段从 数据库 获取信息。
                GLog.d(TAG, LogFlag.DL) { "[loadPhotoInfoFromLocalDb] photoInfo = $localDetailInfo" }
                if (postPhotoInfoIfActive(localDetailInfo).not()) {
                    GTrace.traceEnd()
                    // 任务被取消，退出
                    return@launch
                }
                if (localDetailInfo.isCompletion) {
                    GTrace.traceEnd()
                    // 从本地数据库加载的数据已经是完整的了，没有必要再访问网络获取一些信息了
                    return@launch
                }
            }?.loadPhotoInfoFromNetwork()?.also { networkDetailInfo ->
                // 此阶段从 网络 获取信息。
                GLog.d(TAG, LogFlag.DL) { "[loadPhotoInfoFromNetwork] photoInfo = $networkDetailInfo" }
                postPhotoInfoIfActive(networkDetailInfo)
            } ?: let {
                GLog.w(TAG, "[requestStartLoadPhotoInfo] loadPhotoInfo is null")
            }
            GTrace.traceEnd()
        }
    }

    /**
     * 终止当前任务
     */
    internal fun cancel() {
        curLoadPhotoInfoJob?.cancel()
        // 停止地址解析任务，避免[DetailsAddressResolver]持有解析回调mListener，导致内存泄露
        DetailsHelper.pause()
    }

    /**
     * 注册 PhotoInfo 的变化监听，如果传了 comparePhotoInfo 则会与当前 photoInfo 做比较，如果不同则将 photoInfo 回调。
     */
    internal fun setPhotoInfoListener(photoInfoListener: ((PhotoInfo) -> Unit)?, comparePhotoInfo: PhotoInfo? = null) {
        this.photoInfoListener = photoInfoListener
        photoInfo?.let { currentPhotoInfo ->
            if ((photoInfoListener != null) && (comparePhotoInfo != null) && (comparePhotoInfo != currentPhotoInfo)) {
                photoInfoListener.invoke(currentPhotoInfo)
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal suspend fun loadPhotoInfoFromMediaItem(scope: CoroutineScope): PhotoInfo? {
        val id = mediaItem.normalizedPath
        if (id.isNullOrEmpty()) {
            GLog.w(TAG, "[loadPhotoInfoFromMediaItem] mediaItem's path is empty")
            return null
        }

        /**
         * 加载日期。
         * - 今天、昨天
         * - 具体日期：比如1月1日
         */
        val loadDate = fun(mediaItem: MediaItem): String {
            if (mediaItem.isLoaded.not() || (mediaItem.dateTakenInMs <= 0L)) {
                GLog.w(TAG, "loadPhotoInfoFromMediaItem currentMediaItem is empty or dateTakenInMs is zero: ${mediaItem.path}")
                return EMPTY_STRING
            }
            return if (TimeUtils.isToday(mediaItem.dateTakenInMs)) {
                context.getString(com.oplus.gallery.basebiz.R.string.common_today)
            } else if (TimeUtils.isYesterday(mediaItem.dateTakenInMs)) {
                context.getString(com.oplus.gallery.basebiz.R.string.common_yesterday)
            } else {
                /*
                 * 此处获取字符串逻辑需要0.5ms/次
                 * 如果是今天昨天的item,那么这部分逻辑完全没必要执行
                 */
                TimeUtils.getFormatMDDate(context, mediaItem.dateTakenInMs)
            }
        }

        /**
         * 加载具体时间。如：12:00。
         */
        val loadTime = fun(mediaItem: MediaItem): String {
            if (mediaItem.isLoaded.not() || (mediaItem.dateTakenInMs <= 0L)) {
                GLog.w(TAG, "loadPhotoInfoFromMediaItem currentMediaItem is empty or dateTakenInMs is zero: ${mediaItem.path}")
                return EMPTY_STRING
            }
            return dateFormat.format(Date(mediaItem.dateTakenInMs))
        }

        /**
         * 大图标题栏
         * 加载按照规则格式化的日期和时间
         */
        val formatDateAndTime = fun(mediaItem: MediaItem): String {
            if (mediaItem.isLoaded.not() || (mediaItem.dateTakenInMs <= 0L)) {
                GLog.w(TAG, "loadPhotoInfoFromMediaItem currentMediaItem is empty or dateTakenInMs is zero: ${mediaItem.path}")
                return EMPTY_STRING
            }
            return TimeUtils.getFormatMDHMDate(context, mediaItem.dateTakenInMs)
        }

        // 使用async并行执行所有独立任务
        val deferredLocation = scope.async {
            withTimeoutOrNull(FIRST_LOADING_TIME_OUT_FOR_LOAD_LOCATION) {
                loadLocation(mediaItem)
            }
        }

        // 并行加载日期、时间和格式化日期时间
        val (date, time, formattedDateTime) = scope.async {
            Triple(
                loadDate(mediaItem),
                loadTime(mediaItem),
                formatDateAndTime(mediaItem)
            )
        }.await()

        // 等待location结果
        val location = deferredLocation.await()

        val isSharedFile = mediaItem.getSpecifiedAttributes(Bundle()).getBoolean(KEY_SHARED_FILE, false)
        val isVideo = mediaItem.isVideo
        mediaItemFormatType = loadFormatType(mediaItem)
        val details = Bundle().apply {
            // 清数据启动后，还未同步媒体库前，loadFormatType需要从视频文件中解析出来，耗时较长
            putInt(PhotoDetailsConstants.Key.FORMAT_TYPE, mediaItemFormatType)
            putString(PhotoDetailsConstants.Key.LOCATION, location)
            putString(PhotoDetailsConstants.Key.DATE, date)
            putString(PhotoDetailsConstants.Key.TIME, time)
            putString(PhotoDetailsConstants.Key.FORMAT_DATE_TIME, formattedDateTime)
            putBoolean(PhotoDetailsConstants.Key.IS_SHARED_FILE, isSharedFile)
            putBoolean(PhotoDetailsConstants.Key.IS_VIDEO, isVideo)
        }

        return PhotoInfo(
            index = index,
            id = id,
            details = details,
            infoCompleteness = PhotoInfo.InfoCompleteness.BASIC
        )
    }

    private suspend fun PhotoInfo.loadPhotoInfoFromLocalDb(): PhotoInfo {
        // Marked by: 2021/9/23 加载媒体详细信息，涉及到文件读取和数据库
        return this
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    suspend fun PhotoInfo.loadPhotoInfoFromNetwork(): PhotoInfo {
        // Marked by: 2021/9/23 加载媒体详细信息，涉及到网络

        if (details.getString(PhotoDetailsConstants.Key.LOCATION) == null) {
            details.putString(PhotoDetailsConstants.Key.LOCATION, loadLocation(mediaItem))
        }
        return copy(infoCompleteness = PhotoInfo.InfoCompleteness.COMPLETION)
    }

    /**
     * 获取可作为大图Title位置信息。如：深圳市。
     * @param mediaItem 用于加载位置的 [MediaItem]
     */
    private suspend fun loadLocation(
        mediaItem: MediaItem
    ): String = suspendCancellableCoroutine {
        val longitudeAndLatitude = DoubleArray(2)
        mediaItem.getLatLong(longitudeAndLatitude)
        if (GPS.isLatLngValid(longitudeAndLatitude)) {
            resolveAddress(context, longitudeAndLatitude) { _, address ->
                if (it.isActive) {
                    /**
                     * 因为 resolveAddress 内部处理问题，可能会多次回调,
                     * 所以此处要对 isActive 进行判断后才 resume，避免多次resume导致异常。
                     */
                    var title = address?.getTitle()
                    // 某些照片的地点信息获取不到，添加兜底策略，按照地点精细度优先级获取标题
                    if (title.isNullOrEmpty()) {
                        title = address?.getTitleByMatchPriority()
                    }
                    it.resume(title ?: EMPTY_STRING)
                }
            }
        } else {
            if (it.isActive) {
                it.resume(EMPTY_STRING)
            }
        }
    }

    /**
     * 如果当前协程时 active 状态，则将 photoInfo 发送出去。
     * @return 是否 post 数据
     */
    private fun CoroutineScope.postPhotoInfoIfActive(photoInfo: PhotoInfo?): Boolean {
        if (isActive) {
            postPhotoInfo(photoInfo)
        }
        return isActive
    }

    private fun postPhotoInfo(photoInfo: PhotoInfo?) {
        photoInfo?.let {
            this.photoInfo = photoInfo
            photoInfoListener?.invoke(photoInfo)
        } ?: GLog.w(TAG, "[postPhotoInfo] photoInfo is null.")
    }

    private val MediaItem?.normalizedPath: String?
        get() = this?.path?.toString()

    companion object {
        private const val TAG = "PhotoInfoLoader"

        /**
         * 首次加载详情时，等待位置信息加载的超时时长
         * Marked : lichengli 后续放到配置服务里
         */
        private const val FIRST_LOADING_TIME_OUT_FOR_LOAD_LOCATION = 100L

        /**
         * 加载媒体格式，
         * 用于获取 资源类型和 photoTag 标签的字段
         *
         * 注意：HDR 的 tag 标签数据目前只能通过 AVPlayer preparing 之后才能获取到，所以委托给 PlaybackVM 去处理，这里只处理常规标签
         */
        internal fun loadFormatType(mediaItem: MediaItem): Int {
            val isVideo = mediaItem.mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
            return when {
                //是视频 且是杜比视频
                isVideo && VideoTypeUtils.isDolbyVideo(mediaItem) -> {
                    val isSupportDolbyDecode = ConfigAbilityWrapper.getBoolean(IS_SUPPORT_DOLBY_DECODE)
                    val isSupportDolbyBrighten = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_DOLBY_BRIGHTEN)
                    when {
                        // 机型同时支持杜比视频提亮能力及杜比视界解码、编解码：显示为「ProXDR-dolbyVision」
                        isSupportDolbyDecode && isSupportDolbyBrighten -> PhotoDetailsConstants.FormatType.DOLBY
                        // 可能存在支持杜比提亮，但不支持解码的，暂时给HDR
                        isSupportDolbyBrighten -> PhotoDetailsConstants.FormatType.HDR
                        // 其他场景都不显示图标
                        else -> PhotoDetailsConstants.FormatType.OTHER
                    }
                }
                //是视频且是LOG视频，LOG视频和HLG视频是同一个格式，格式上无法区分，所以优先判断LOG，OS16.0会更新meta信息，可以不用title区分
                isVideo && VideoTypeUtils.isLogVideo(mediaItem.name) -> PhotoDetailsConstants.FormatType.LOG_VIDEO
                //是视频且是HLG视频
                isVideo && VideoTypeUtils.isHlgVideo(mediaItem) -> PhotoDetailsConstants.FormatType.HDR
                isVideo -> PhotoDetailsConstants.FormatType.OTHER
                isOlivePhoto(mediaItem) -> PhotoDetailsConstants.FormatType.OLIVE
                ImageTypeUtils.isRawFilePath(mediaItem.path) -> PhotoDetailsConstants.FormatType.RAW
                isAISceneryPhoto(mediaItem) -> PhotoDetailsConstants.FormatType.AI_SCENERY
                isYuv(mediaItem) -> PhotoDetailsConstants.FormatType.YUV
                else -> PhotoDetailsConstants.FormatType.OTHER
            }
        }

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        internal fun isYuv(mediaItem: MediaItem): Boolean = FeatureUtils.isSupport10Bit && mediaItem.isYuvFormat

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        internal fun isOlivePhoto(mediaItem: MediaItem): Boolean = mediaItem.isOlivePhoto()

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        internal fun isAISceneryPhoto(mediaItem: MediaItem): Boolean = mediaItem.isAISceneryPhoto()
    }
}