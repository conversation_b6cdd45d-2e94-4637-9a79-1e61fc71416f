/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamTrackEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.track;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsTrackVideoFx;

public class MeicamTrackEffect extends BaseVideoTrackEffect {
    public static final String JSON_TYPE_NAME = "video_track_effect";
    private static final String TAG = "MeicamTrackEffect";
    @SerializedName("class_type")
    protected String mClassType = JSON_TYPE_NAME;// use to mark the type in saved json file
    protected transient NvsTrackVideoFx mNvsVideoFx;

    public MeicamTrackEffect(String name, int type) {
        super(name, type);
    }

    @Override
    public void setOutTime(long outTime) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.changeOutPoint(outTime);
        }
        super.setOutTime(outTime);
    }

    @Override
    public void setInTime(long inTime) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.changeInPoint(inTime);
        }
        super.setInTime(inTime);
    }

    @Override
    protected MeicamTrackEffect clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamTrackEffect result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamTrackEffect(getName(), getType());
        }
        return result;
    }

    public NvsTrackVideoFx getNvsVideoFx() {
        return mNvsVideoFx;
    }

    public void setNvsVideoFx(NvsTrackVideoFx nvsVideoFx) {
        mNvsVideoFx = nvsVideoFx;
    }
}
