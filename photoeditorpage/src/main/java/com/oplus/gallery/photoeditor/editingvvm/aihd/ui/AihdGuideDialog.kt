/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AihdGuideDialog
 ** Description: 功能介绍弹窗.
 ** Version: 1.0
 ** Date : 2024/5/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** chenshimin   2024/5/13       1.0        created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aihd.ui

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.viewpager.COUIViewPager2
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.util.ThemeColorConfigHelper
import com.oplus.gallery.photoeditor.util.setDialogBackgroundColor
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils

/**
 * Ai超清介绍弹窗
 */
internal class AihdGuideDialogBuilder @JvmOverloads constructor(
    private val context: Context,
    colorThemeResId: Int = com.support.appcompat.R.style.Theme_COUI_Dark_Yellow,
    dialogStyleResId: Int = com.support.dialog.R.style.COUIAlertDialog_BottomWarning
) : COUIAlertDialogBuilder(context, colorThemeResId, dialogStyleResId) {

    /**
     * 自定义布局容器
     */
    private var contentView: View? = null
    private var realDialog: AlertDialog? = null
    private var viewPager: COUIViewPager2? = null

    init {
        initView()
    }

    private fun initView() {
        contentView = LayoutInflater.from(context).inflate(R.layout.picture3d_editor_aihd_guide_layout, null).apply {
            viewPager = findViewById<COUIViewPager2?>(R.id.pager_aihd).apply {
                adapter = AihdGuidePagerAdapter()
                isUserInputEnabled = false
            }
            findViewById<COUIButton?>(R.id.btn_aihd_ok).apply {
                // 配置背景色（类型Color）
                drawableColor = ThemeColorConfigHelper.getThemeColor(context)
                setOnClickListener {
                    if (DoubleClickUtils.isFastDoubleClick().not()) {
                        dismiss()
                    }
                }
            }
        }
    }

    override fun show(): AlertDialog? {
        if (isDestroy()) return null
        setView(contentView)
        // 开启模糊背景效果 mark by zhongfonan: 有Otest crash问题，先临时关闭高斯模糊
        setBlurBackgroundDrawable(false)
        realDialog = super.show()
        realDialog?.setCanceledOnTouchOutside(false)
        realDialog?.setDialogBackgroundColor()
        return realDialog
    }

    private fun dismiss() {
        realDialog?.takeIf { it.isShowing }?.dismiss()
    }

    private fun isDestroy(): Boolean {
        return (context as? Activity)?.let {
            it.isFinishing || it.isDestroyed
        } ?: true
    }
}