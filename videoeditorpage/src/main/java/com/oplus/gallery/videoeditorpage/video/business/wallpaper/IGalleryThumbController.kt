/***********************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - IGalleryThumbController.java
 * Description: interface for thumb control.
 * Version: 1.0
 * Date : 2018/01/05
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
 * <EMAIL>    2018/01/05    1.0    build this module
</desc></version></data></author> */
package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.view.View.OnTouchListener

/**
 * 缩图轴控制器接口 用于提供控制缩图轴的滚动能力 目前只有壁纸业务使用
 */
interface IGalleryThumbController {
    /**
     * get video time position with given x position in view
     */
    fun mapTimelinePosFromX(x: Int): Long

    /**
     * get x position in view with given video time
     */
    fun mapXFromTimelinePos(time: Long): Int

    val pixelPerMicrosecond: Double

    val scrollState: Int

    fun setScrollListener(scrollerListener: ThumbScrollerListener)

    fun setTouchListener(touchListener: OnTouchListener)

    fun smoothScrollTo(x: Int, y: Int)

    fun scrollTo(x: Int, y: Int)

    fun fullScroll(pos: Int)

    fun stopScroll()

    interface ThumbScrollerListener {
        fun onScrolled(pos: Int)
        fun onScrollStateChanged(newState: Int)
    }
}
