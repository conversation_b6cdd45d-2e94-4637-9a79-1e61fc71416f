/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigDataTypeMap.kt
 ** Description: 配置的KEY与其数据类型的映射表，由APT在编译期生成
 ** Version: 1.0
 ** Date: 2023/5/11
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/5/11      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.codecheck.detector

object ConfigDataTypeMap {

    @JvmStatic
    @Suppress("LongMethod", "MagicNumber")
    fun buildMap(): Map<String, Int> {
        val configDataTypeMap = mutableMapOf<String, Int>()
        configDataTypeMap["test_environment"] = 0
        configDataTypeMap["gallery_account_test"] = 0
        configDataTypeMap["test_aiidphoto_resp_code"] = 0
        configDataTypeMap["test_gallery_db_wal"] = 0
        configDataTypeMap["region"] = 4
        configDataTypeMap["region_mark"] = 4
        configDataTypeMap["is_region_india"] = 0
        configDataTypeMap["is_region_vietnam"] = 0
        configDataTypeMap["is_region_cn"] = 0
        configDataTypeMap["after_sale_region"] = 4
        configDataTypeMap["operator"] = 4
        configDataTypeMap["is_cmcc"] = 0
        configDataTypeMap["is_softbank"] = 0
        configDataTypeMap["market_name"] = 4
        configDataTypeMap["is_mtk_platform"] = 0
        configDataTypeMap["is_qualcomm_platform"] = 0
        configDataTypeMap["is_low_performance_platform"] = 0
        configDataTypeMap["os_version"] = 4
        configDataTypeMap["ota_version"] = 4
        configDataTypeMap["rom_version"] = 4
        configDataTypeMap["android_version"] = 4
        configDataTypeMap["product_brand"] = 4
        configDataTypeMap["is_realme_brand"] = 0
        configDataTypeMap["is_oppo_brand"] = 0
        configDataTypeMap["is_oneplus_brand"] = 0
        configDataTypeMap["board_platform"] = 4
        configDataTypeMap["cpu_info_part_number"] = 4
        configDataTypeMap["cpu_info"] = 4
        configDataTypeMap["product_name"] = 4
        configDataTypeMap["build_time"] = 4
        configDataTypeMap["phone_model"] = 4
        configDataTypeMap["is_tablet"] = 0
        configDataTypeMap["is_fold"] = 0
        configDataTypeMap["is_fold_remap_display_disabled"] = 0
        configDataTypeMap["is_multi_display"] = 0
        configDataTypeMap["api_level"] = 1
        configDataTypeMap["time_zone"] = 4
        configDataTypeMap["max_brightness"] = 1
        configDataTypeMap["is_support_backlight_optimize"] = 0
        configDataTypeMap["is_support_auto_brightness_animation"] = 0
        configDataTypeMap["is_support_high_brightness"] = 0
        configDataTypeMap["is_primary_user"] = 0
        configDataTypeMap["is_container_user"] = 0
        configDataTypeMap["is_children_mode"] = 0
        configDataTypeMap["is_fbe_version"] = 0
        configDataTypeMap["is_user_unlocked"] = 0
        configDataTypeMap["is_multi_system_user"] = 0
        configDataTypeMap["display_corner_radius"] = 4
        configDataTypeMap["secondary_display_corner_radius"] = 4
        configDataTypeMap["is_show_special_company_name"] = 0
        configDataTypeMap["brighten_version_local_hdr"] = 1
        configDataTypeMap["brighten_version_dolby"] = 1
        configDataTypeMap["is_support_dolby_decode"] = 0
        configDataTypeMap["is_support_dolby_encode"] = 0
        configDataTypeMap["is_support_dolby_encode_accelerate"] = 0
        configDataTypeMap["is_support_hlg_encode"] = 0
        configDataTypeMap["is_support_brightness_enhance"] = 0
        configDataTypeMap["local_hdr_switch"] = 0
        configDataTypeMap["brighten_compare_switch"] = 0
        configDataTypeMap["video_auto_play"] = 0
        configDataTypeMap["show_safe_box_album"] = 0
        configDataTypeMap["cloud_sync_accept_region"] = 4
        configDataTypeMap["privacy_statement_version"] = 1
        configDataTypeMap["authorize_request_server_update_and_download"] = 0
        configDataTypeMap["authorize_location_resolve"] = 0
        configDataTypeMap["authorize_ai_repair"] = 0
        configDataTypeMap["authorize_ai_id_photo"] = 0
        configDataTypeMap["authorize_get_usual_location"] = 0
        configDataTypeMap["authorize_share_album"] = 0
        configDataTypeMap["authorize_enhance_text"] = 0
        configDataTypeMap["authorize_text_ocr_service"] = 0
        configDataTypeMap["authorize_face_scan"] = 0
        configDataTypeMap["authorize_gallery_map"] = 0
        configDataTypeMap["authorize_cloud_sync"] = 0
        configDataTypeMap["feature_is_support_p3_wcg"] = 0
        configDataTypeMap["feature_is_support_backlight_optimize"] = 0
        configDataTypeMap["feature_is_support_high_brightness"] = 0
        configDataTypeMap["feature_is_phone_clone_disabled"] = 0
        configDataTypeMap["feature_is_support_super_text_v2"] = 0
        configDataTypeMap["feature_is_support_super_text_v1_ocr"] = 0
        configDataTypeMap["feature_is_support_timeline_page_multi_view_switch"] = 0
        configDataTypeMap["feature_is_support_eliminate_pen"] = 0
        configDataTypeMap["feature_is_support_blurring"] = 0
        configDataTypeMap["feature_is_support_highlight"] = 0
        configDataTypeMap["feature_is_support_super_text_v1_convert_doc"] = 0
        configDataTypeMap["feature_is_support_super_text_v1_convert_ppt_doc"] = 0
        configDataTypeMap["feature_is_support_super_text_v1_convert_word_doc"] = 0
        configDataTypeMap["feature_is_support_cast_adaption"] = 0
        configDataTypeMap["feature_is_support_cast_presentation"] = 0
        configDataTypeMap["feature_is_support_gif_synthesis"] = 0
        configDataTypeMap["feature_is_support_map_page"] = 0
        configDataTypeMap["feature_is_support_privacy_protection_assistant"] = 0
        configDataTypeMap["feature_is_support_ai_id_photo"] = 0
        configDataTypeMap["feature_is_support_local_hdr"] = 0
        configDataTypeMap["feature_is_support_brighten_compare"] = 0
        configDataTypeMap["feature_is_support_dolby_brighten"] = 0
        configDataTypeMap["feature_is_support_safe_box_album"] = 0
        configDataTypeMap["feature_is_support_album_safe_box"] = 0
        configDataTypeMap["feature_is_support_user_custom_gallery_share"] = 0
        configDataTypeMap["feature_is_support_user_custom_oshare"] = 0
        configDataTypeMap["feature_is_support_user_custom_safe_box"] = 0
        configDataTypeMap["feature_is_support_hdr"] = 0
        configDataTypeMap["feature_is_support_portraitblur"] = 0
        configDataTypeMap["feature_is_support_hassel_watermark"] = 0
        configDataTypeMap["feature_is_camera_config_new_project"] = 0
        configDataTypeMap["feature_is_support_photo_editor_watermark"] = 0
        configDataTypeMap["feature_is_support_photo_thumb_line"] = 0
        configDataTypeMap["feature_is_support_senior_picked"] = 0
        configDataTypeMap["feature_is_support_side_pane"] = 0
        configDataTypeMap["sticker_url"] = 4
        configDataTypeMap["first_using_auto_mosaic"] = 0
        configDataTypeMap["AUTO_MOSAIC_TIPS_SHOW_COUNT"] = 1
        configDataTypeMap["AUTO_MOSAIC_TIPS_SHOW_TIME"] = 4
        configDataTypeMap["need_delete_sticker_file_lower_than_version3"] = 0
        configDataTypeMap["mosaic_detect_sensitive_timeout"] = 2
        configDataTypeMap["mosaic_parse_sensitive_region_timeout"] = 2
        configDataTypeMap["first_using_hasselblad_watermark"] = 0
        configDataTypeMap["first_using_photo_editor_watermark"] = 0
        configDataTypeMap["eliminate_pen_component_version"] = 1
        configDataTypeMap["eliminate_pen_component_refuse_count"] = 4
        configDataTypeMap["eliminate_pen_component_current_component_dir"] = 4
        configDataTypeMap["watermark_custom_info_key"] = 4
        configDataTypeMap["video_editor_url"] = 4
        configDataTypeMap["first_using_super_text"] = 0
        configDataTypeMap["first_using_portrait_blur"] = 0
        configDataTypeMap["need_fetch"] = 0
        configDataTypeMap["last_metadata_sync_success_time"] = 2
        configDataTypeMap["clear_sys_version_for_config_inconsistent"] = 0
        configDataTypeMap["last_show_cloud_space_not_enough_tips_time"] = 2
        configDataTypeMap["available_Amount"] = 1
        configDataTypeMap["is_single_codec_device"] = 0
        configDataTypeMap["is_single_4k_dolby_codec_device"] = 0
        configDataTypeMap["is_single_4k_codec_device"] = 0
        configDataTypeMap["is_wide_color_gamut_enabled"] = 1
        configDataTypeMap["is_p3_wcg_supported"] = 0
        configDataTypeMap["is_display_support_wcg_v2"] = 0
        configDataTypeMap["gamut_node_path"] = 4
        configDataTypeMap["is_support_show_red_dot_local"] = 0
        configDataTypeMap["is_support_art_show_local"] = 0
        configDataTypeMap["is_support_art_show_cloud"] = 0
        configDataTypeMap["support_art_show_api_level"] = 4
        configDataTypeMap["toast_duration"] = 2
        configDataTypeMap["no_more_remind_when_edit_local_hdr"] = 0
        configDataTypeMap["first_using_self_splitting"] = 0
        configDataTypeMap["first_using_rename_file"] = 0
        configDataTypeMap["first_using_hdr_transform"] = 0
        configDataTypeMap["first_using_save_to_gif"] = 0
        configDataTypeMap["first_using_generate_gif"] = 0
        configDataTypeMap["first_using_dolby_vision_video"] = 0
        configDataTypeMap["first_using_local_hdr_compare"] = 0
        configDataTypeMap["local_hdr_rus_content"] = 4
        configDataTypeMap["feature_o_cloud_sync_offline"] = 0
        configDataTypeMap["offline_orig_files_download_guide_had_shown"] = 0
        configDataTypeMap["offline_download_guide_click_ignore_time"] = 2
        configDataTypeMap["offline_ensure_download_original_file"] = 0
        configDataTypeMap["local_offline_done"] = 0
        return configDataTypeMap
    }
}