/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * File: SpacingItemDecoration.kt
 * Description: tangzhibin created
 * Version: 1.0
 * Date: 2025/4/30
 * Author: tangzhibin
 *
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * tangzhibin      2025/4/30        1.0         NEW
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.graphics.Rect
import android.view.View
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView

/**
 * 为 RatioSelectorView 添加间距和内边距
 *
 * @param itemSpacing 每个 item 的间距（单位：像素）
 * @param isHorizontal 当前 RecyclerView 是否为水平排列
 * @param paddingStart 起始内边距
 * @param paddingTop 顶部内边距
 * @param paddingEnd 结束内边距
 * @param paddingBottom 底部内边距
 */
class SpacingItemDecoration(
    private val itemSpacing: Int,
    private val isHorizontal: Boolean,
    private val paddingStart: Int = 0,
    private val paddingTop: Int = 0,
    private val paddingEnd: Int = 0,
    private val paddingBottom: Int = 0
) : RecyclerView.ItemDecoration() {
    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        val itemCount = parent.adapter?.itemCount ?: 0
        val isRtl = ViewCompat.getLayoutDirection(parent) == ViewCompat.LAYOUT_DIRECTION_RTL

        if (isHorizontal) {
            // 水平方向处理
            outRect.apply {
                if (itemCount == 1) {
                    left = if (isRtl) paddingEnd else paddingStart
                    right = if (isRtl) paddingStart else paddingEnd
                } else {
                    val isStart = position == 0
                    val isEnd = position == itemCount - 1

                    left = when {
                        isStart -> if (isRtl) 0 else paddingStart
                        isEnd -> if (isRtl) paddingEnd else 0
                        else -> 0
                    }
                    right = when {
                        isStart -> if (isRtl) paddingStart else itemSpacing
                        isEnd -> if (isRtl) itemSpacing else paddingEnd
                        else -> itemSpacing
                    }
                }
                // 垂直方向内边距
                top = paddingTop
                bottom = paddingBottom
            }
        } else {
            // 垂直方向处理
            outRect.apply {
                top = if (position == 0) paddingTop else 0
                bottom = when (position) {
                    itemCount - 1 -> paddingBottom
                    else -> itemSpacing
                }
                // 水平方向内边距
                left = if (isRtl) paddingEnd else paddingStart
                right = if (isRtl) paddingStart else paddingEnd
            }
        }
    }
}
