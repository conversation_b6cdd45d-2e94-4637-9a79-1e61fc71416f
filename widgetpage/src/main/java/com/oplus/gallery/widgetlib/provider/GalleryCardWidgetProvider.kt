/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - GalleryCardWidgetProvider.kt
 ** Description:接收卡片相关回调的provider，在MVVM层级中把这个类视为View
 ** Version: 1.0
 ** Date : 2021/10/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2021/10/11        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetlib.provider

import android.app.Application
import android.content.Context
import android.content.UriMatcher
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.os.SystemClock
import androidx.annotation.MainThread
import androidx.lifecycle.Observer
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import com.oplus.gallery.bus_lib.Bus
import com.oplus.gallery.bus_lib.annotations.Subscribe
import com.oplus.gallery.basebiz.event.UserOpPermittedEvent
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.widgetpage.WidgetConstants
import com.oplus.gallery.widgetpage.ui.cardwidget.item.WidgetViewGetter
import com.oplus.gallery.widgetpage.ui.cardwidget.set.WidgetSetViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import androidx.core.net.toUri
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 卡片商店需要验证provider名称，且暂时不支持多provider（计划4月底覆盖支持），所以暂时不能修改provider包名
 */
class GalleryCardWidgetProvider : AppCardWidgetProvider() {

    private var isContentChangedListenerRegistered = false

    private val application: Application get() =  context as Application

    private val widgetSetViewModel by lazy {
        WidgetSetViewModel(application)
    }

    private val renderFailRecord by lazy { HashMap<String, Int>() }
    private var lastResetRetryRecordTime = SystemClock.uptimeMillis()

    private val observer = Observer<List<String>> { widgetCodeList ->
        GLog.d(TAG, "onChanged: size=${widgetCodeList.size}")
        widgetCodeList.forEach { widgetCode ->
            WidgetViewGetter.getWidgetView(application, widgetCode).forceUpdate()
        }
    }

    override fun onCreate(): Boolean {
        Bus.register(this)
        GTrace.traceBegin("GalleryCardWidgetProvider.onCreate")
        val result = super.onCreate()
        GTrace.traceEnd()
        return result
    }

    override fun getCardLayoutName(widgetCode: String): String {
        GLog.d(TAG, "getCardLayoutName widgetCode = $widgetCode")
        return WidgetConstants.Layout.IMAGE
    }

    /**
     * 卡片变为可见且满足最小刷新时间间隔时回调
     * 非主线程
     */
    override fun onResume(context: Context, widgetCode: String) {
        GLog.d(TAG, "onResume widgetCode = $widgetCode")
        AppScope.launch(Dispatchers.UI) {
            widgetSetViewModel.onResume()
            WidgetViewGetter.getWidgetView(application, widgetCode).onResume()
        }
    }

    /**
     * 卡片变为不可见时回调
     * 非主线程
     */
    override fun onPause(context: Context, widgetCode: String) {
        super.onPause(context, widgetCode)
        GLog.d(TAG, "onPause widgetCode = $widgetCode")
        AppScope.launch(Dispatchers.UI) {
            widgetSetViewModel.onPause()
            WidgetViewGetter.getWidgetViewIfPresent(widgetCode)?.onPause()
        }
    }

    /**
     * 卡片被订阅（添加卡片/负一屏进程启动）时回调
     * 非主线程
     */
    override fun subscribed(context: Context, widgetCode: String) {
        super.subscribed(context, widgetCode)
        GLog.d(TAG, "subscribed widgetCode = $widgetCode")
        widgetSetViewModel.addWidget(widgetCode)
    }

    /**
     * 卡片被取消订阅（删除卡片）时回调
     * 非主线程
     */
    override fun unSubscribed(context: Context, widgetCode: String) {
        super.unSubscribed(context, widgetCode)
        GLog.d(TAG, "unSubscribed widgetCode = $widgetCode")
        widgetSetViewModel.removeWidget(widgetCode)
    }

    /**
     * 应用启动时和卡片增删时回调，返回已添加的卡片列表
     * 非主线程
     */
    override fun onCardsObserve(context: Context, widgetCodes: List<String>) {
        super.onCardsObserve(context, widgetCodes)
        GLog.d(TAG, "onCardsObserve widgetCodes = $widgetCodes")
        AppScope.launch(Dispatchers.UI) {
            if (widgetCodes.isNotEmpty()) {
                registerContentChangedListener()
            } else {
                unregisterContentChangedListener()
            }
        }
    }

    @Subscribe
    @Suppress("UNUSED_PARAMETER")
    fun onUserOpPermitted(userOpPermittedEvent: UserOpPermittedEvent) {
        widgetSetViewModel.updateAllWidget()
    }

    override fun onRenderFail(context: Context, widgetCode: String) {
        super.onRenderFail(context, widgetCode)
        GLog.d(TAG, "onRenderFail widgetCode = $widgetCode")
        AppScope.launch(Dispatchers.UI) {
            if ((SystemClock.uptimeMillis() - lastResetRetryRecordTime) > RETRY_RESET_INTERVAL) {
                renderFailRecord.clear()
                lastResetRetryRecordTime = SystemClock.uptimeMillis()
                GLog.d(TAG, "onRenderFail reset retry records")
            }
            val count = renderFailRecord[widgetCode] ?: 0
            if (count >= RENDER_RETRY_COUNT) {
                GLog.e(TAG, "onRenderFail retry count limit!")
                return@launch
            }
            renderFailRecord[widgetCode] = count + 1
            WidgetViewGetter.getWidgetView(application, widgetCode).forceUpdate()
        }
    }

    override fun openFile(uri: Uri, mode: String): ParcelFileDescriptor? {
        GLog.d(TAG, LogFlag.DL) { "openFile uri = $uri mode = $mode" }
        val context = context ?: return null
        when (MATCH_WIDGET_IMAGE) {
            URI_MATCHER.match(uri) -> uri.lastPathSegment?.let { File(WidgetConstants.Dir.getWidgetImageDir(context), it) }
            else -> null
        }?.let { file ->
            return ParcelFileDescriptor.open(file.file, ParcelFileDescriptor.MODE_READ_ONLY)
        }
        GLog.e(TAG, LogFlag.DL) { "openFile uri = $uri mode = $mode not match" }
        return super.openFile(uri, mode)
    }

    /**
     * 注册数据变化监听
     */
    @MainThread
    private fun registerContentChangedListener() {
        GLog.d(TAG, "registerContentChangedListener")
        if (!isContentChangedListenerRegistered) {
            widgetSetViewModel.liveData.observeForever(observer)
            widgetSetViewModel.registerContentChangedListener()
            isContentChangedListenerRegistered = true
        }
    }

    /**
     * 反注册数据变化监听
     */
    @MainThread
    private fun unregisterContentChangedListener() {
        GLog.d(TAG, "unregisterContentChangedListener")
        if (isContentChangedListenerRegistered) {
            widgetSetViewModel.liveData.removeObserver(observer)
            widgetSetViewModel.unregisterContentChangedListener()
            isContentChangedListenerRegistered = false
        }
    }

    companion object {
        private const val TAG = "GalleryCardWidgetProvider"
        /**
         *  渲染失败的话，每张卡片一段时间内最多重试三次
         *  */
        private const val RENDER_RETRY_COUNT = 3
        /**
         * 渲染失败技数的重置间隔，如A/B/C三张卡片几秒内失败了[RENDER_RETRY_COUNT]次，那么至少等10分钟后才能重新发送数据尝试刷新
         * */
        private const val RETRY_RESET_INTERVAL = TimeUtils.TIME_10_MIN_IN_MS

        private const val AUTHORITY = "com.oplus.gallery.widget"
        private const val PATH_WIDGET_IMAGE = "widget_image"
        private const val MATCH_WIDGET_IMAGE = 1

        private val URI_MATCHER: UriMatcher = UriMatcher(UriMatcher.NO_MATCH).apply {
            addURI(AUTHORITY, "$PATH_WIDGET_IMAGE/*", MATCH_WIDGET_IMAGE)
        }

        fun getWidgetImageFileUri(file: File): Uri = "content://$AUTHORITY/$PATH_WIDGET_IMAGE/${file.name}".toUri()
    }
}