/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextDecorationAdapter.kt
 ** Description : 文字装饰适配器
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecoration
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecorationViewHolder
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecorationItem

/**
 * 颜色选择器适配器
 */
class TextDecorationAdapter(recyclerView: RecyclerView) : BaseAdapter<TextDecorationItem, TextDecorationViewHolder>(
    recyclerView,
    /**
     * 创建VH的回调接口
     */
    { parent: ViewGroup, viewType: Int -> TextDecorationViewHolder(parent) }
) {

    /**
     * 配置多选控件
     */
    override var multipleSelectable = true

    init {
        // 设置item数据差异计算回调
        diffCallback = TextDecorationDiffCallback()
    }

    /**
     * 更新显示文本装饰到UI控件
     */
    fun displayTextDecorationValues(
        isItalic: Boolean,
        isBold: Boolean,
        isUnderLine: Boolean,
        isLineThrough: Boolean
    ) {
        data.forEachIndexed { index, item ->
            when (item.textDecoration) {
                TextDecoration.ITALIC -> item.selected = isItalic
                TextDecoration.BOLD -> item.selected = isBold
                TextDecoration.UNDERLINE -> item.selected = isUnderLine
                TextDecoration.LINE_THROUGH -> item.selected = isLineThrough
            }
            notifyItemChanged(index)
        }
    }

    /**
     * 文字样式数据变化差异回调器
     */
    private class TextDecorationDiffCallback : DiffCallback<TextDecorationItem>() {

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return (oldItem.textDecoration == newItem.textDecoration)
        }
    }
}