/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ResourceManager.kt
 ** Description : 字幕样式、字体资源管理器
 ** Version     : 1.0
 ** Date        : 2025/5/19 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/5/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.resource

import android.content.Context
import androidx.lifecycle.LifecycleCoroutineScope
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle.isRunningForeground
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.authorizing.R
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.AssetPackageType
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.asset.IAssetProcessListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.asset.MeicamAssetManager
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption.CaptionType
import com.oplus.gallery.videoeditorpage.resource.manager.CaptionFontResourceManager
import com.oplus.gallery.videoeditorpage.resource.manager.CaptionStyleResourceManager
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.utlis.NetworkPermissionHelper
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.DownloadItem
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.FontViewData
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewData
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.toFont
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.toStyle
import com.oplus.gallery.videoeditorpage.video.business.caption.util.CaptionResourceHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 字幕样式、字体资源管理器
 */
internal class ResourceManager(
    /**
     * 上下文
     */
    private val context: Context,

    /**
     * 文字协程作用域
     */
    private val captionScope: LifecycleCoroutineScope,
    /**
     * 通知资源下载进度变化更新
     */
    private val notifyDownloadProgressCallback: (pageType: PageType, position: Int) -> Unit
) {
    /**
     * 美摄资源包管理器
     */
    private val  assetManager: MeicamAssetManager?
        get() = EditorEngineGlobalContext.getInstance().assetManager as? MeicamAssetManager

    /**
     * 管理器当前管理的下载的资源item
     */
    private val installedDownloadItems: MutableMap<String, DownloadItem> = mutableMapOf()

    /**
     * 资源安装成功回调集合，记录当前触发了多少个资源安装任务安装成功时的结果回调
     */
    private val resourceInstalledCallback: MutableMap<String, AssertPackageInstalledCallback> = mutableMapOf()

    /**
     * 字幕样式资源类型索引map，用于查询某个样式资源属于哪种类型字幕
     */
    private val resourceTypeIndex = mapOf(
        CAPTION_NORMAL_SUFFIX to CaptionResourceType.NORMAL_CAPTION,
        CAPTION_RENDERER_SUFFIX to CaptionResourceType.MODULAR_RENDERER_CAPTION,
        CAPTION_CONTEXT_SUFFIX to CaptionResourceType.MODULAR_CONTEXT_CAPTION,
        CAPTION_ANIMATION_SUFFIX to CaptionResourceType.MODULAR_ANIMATION_CAPTION,
        CAPTION_IN_ANIMATION_SUFFIX to CaptionResourceType.MODULAR_IN_ANIMATION_CAPTION,
        CAPTION_OUT_ANIMATION_SUFFIX to CaptionResourceType.MODULAR_OUT_ANIMATION_CAPTION
    )

    /**
     * 样式网络请求资源管理
     */
    private val styleResourceManager: CaptionStyleResourceManager = CaptionStyleResourceManager(context, captionScope)

    /**
     * 字体网络请求资源管理
     */
    private val fontResourceManager: CaptionFontResourceManager = CaptionFontResourceManager(context, captionScope)

    /**
     * 字幕字体列表结果回调，回调本地数据和网络请求结果数据列表
     */
    private var fontItemsLoadedCallback: LocalResourceLoadedCallback? = null

    /**
     * 字幕样式列表结果回调，回调本地数据和网络请求结果数据列表
     */
    private var styleItemsLoadedCallback: LocalResourceLoadedCallback? = null

    /**
     * 获取样式数据(本地内置+网络)
     */
    fun fetchStylesData(itemsLoadedCallback: LocalResourceLoadedCallback) {
        styleItemsLoadedCallback = itemsLoadedCallback
        captionScope.launch(Dispatchers.IO) {
            runCatching {
                val localStyleItems = fetchLocalStyleData()
                if (localStyleItems.isNotEmpty()) {
                    captionScope.launch(Dispatchers.Main) {
                        // 回调本地数据显示
                        styleItemsLoadedCallback?.invoke(localStyleItems)
                    }
                }
                captionScope.launch(Dispatchers.Main) {
                    if (NetworkPermissionManager.isUseOpenNetwork.not()) {
                        NetworkPermissionHelper.showNetworkPermissionDialog(
                            context,
                            R.string.authorizing_request_network_title,
                            R.string.authorizing_request_network_caption,
                            onAllowOpenNetwork = {
                                captionScope.launch(Dispatchers.IO) {
                                    handleNetworkStyleData()
                                }
                            },
                            scene = NetworkPermissionHelper.Scene.CAPTION_LIST
                        )
                    } else {
                        handleNetworkStyleData()
                    }
                }
            }.onFailure { error ->
                GLog.e(TAG, LogFlag.DL) { "fetchStylesData failed: $error" }
            }
        }
    }

    /**
     * 获取网络样式数据
     */
    private suspend fun handleNetworkStyleData() {
        val data = styleResourceManager.loadDataSync(true)
        val errCode = data.first
        if (errCode == ErrorCode.SUCCESS) {
            val styles: List<StyleViewData> = data.second.map { styleItem ->
                styleItem.toStyle().also { style ->
                    if (styleItem.fontId.isNotEmpty()) {
                        // 根据绑定的fontId从数据库里面查询字体信息
                        style.fontViewData = fontResourceManager.getItem(styleItem.fontId)?.toFont()
                    }
                }
            }
            if (styles.isNotEmpty()) {
                // 回调本地数据和网络数据合并显示
                captionScope.launch(Dispatchers.Main) {
                    styleItemsLoadedCallback?.invoke(styles)
                }
            }
        } else {
            if (isRunningForeground()) {
                val stringRes = when (errCode) {
                    ErrorCode.NO_NETWORK_WHEN_DOWNLOADING -> com.oplus.gallery.videoeditorpage.R.string.videoeditor_download_network_disconnect
                    ErrorCode.NO_NETWORK -> com.oplus.gallery.videoeditorpage.R.string.videoeditor_editor_no_network
                    else -> com.oplus.gallery.videoeditorpage.R.string.videoeditor_download_fail
                }
                captionScope.launch(Dispatchers.UI) {
                    ToastUtil.showShortToast(stringRes)
                }
            }
        }
    }

    /**
     * 获取字体数据(本地内置+网络)
     */
    fun fetchFontsData(itemsLoadedCallback: LocalResourceLoadedCallback) {
        fontItemsLoadedCallback = itemsLoadedCallback
        captionScope.launch(Dispatchers.IO) {
            runCatching {
                val localFontItems = fetchLocalFontData()
                if (localFontItems.isNotEmpty()) {
                    fontResourceManager.requestNameFilesForFonts(localFontItems)
                    captionScope.launch(Dispatchers.Main) {
                        // 将本地数据库里面的数据先回调展示
                        fontItemsLoadedCallback?.invoke(localFontItems)
                    }
                }
                captionScope.launch(Dispatchers.Main) {
                    if (NetworkPermissionManager.isUseOpenNetwork.not()) {
                        NetworkPermissionHelper.showNetworkPermissionDialog(
                            context,
                            R.string.authorizing_request_network_title,
                            R.string.authorizing_request_network_caption,
                            onAllowOpenNetwork = {
                                captionScope.launch(Dispatchers.IO) {
                                    handleNetworkFontData()
                                }
                            },
                            scene = NetworkPermissionHelper.Scene.CAPTION_LIST
                        )
                    } else {
                        handleNetworkFontData()
                    }
                }
            }.onFailure { error ->
                GLog.e(TAG, LogFlag.DL) { "fetchFontsData failed: $error" }
            }
        }
    }

    /**
     * 网络请求字体数据
     */
    private suspend fun handleNetworkFontData() {
        val data = fontResourceManager.loadDataSync(true)
        val errCode = data.first
        if (errCode == ErrorCode.SUCCESS) {
            val totalFontItems = data.second.map { it.toFont() }
            if (totalFontItems.isNotEmpty()) {
                fontResourceManager.requestNameFilesForFonts(totalFontItems)
                captionScope.launch(Dispatchers.Main) {
                    fontItemsLoadedCallback?.invoke(totalFontItems)
                }
            }
        } else {
            if (isRunningForeground()) {
                val stringRes = when (errCode) {
                    ErrorCode.NO_NETWORK_WHEN_DOWNLOADING -> com.oplus.gallery.videoeditorpage.R.string.videoeditor_download_network_disconnect
                    ErrorCode.NO_NETWORK -> com.oplus.gallery.videoeditorpage.R.string.videoeditor_editor_no_network
                    else -> com.oplus.gallery.videoeditorpage.R.string.videoeditor_download_fail
                }
                captionScope.launch(Dispatchers.UI) {
                    ToastUtil.showShortToast(stringRes)
                }
            }
        }
    }

    /**
     * 安装样式资源包
     */
    fun applyCaptionStyle(captionStyle: StyleViewData, position: Int, installedCallback: AssertPackageInstalledCallback) {
        val fontLocalPath = captionStyle.fontViewData?.localPath
        val fontFileExist = if (fontLocalPath != null) {
            CaptionResourceHelper.isFileExists(fontLocalPath)
        } else {
            true
        }
        val styleFileExist = CaptionResourceHelper.isFileExists(captionStyle.localPath)
        if (fontFileExist && styleFileExist) {
            // 本地存在样式和字体文件，直接安装资源文件
            installCaptionStyleAssert(captionStyle, installedCallback)
            fontLocalPath?.let { assetManager?.registerFontByFilePath(it) }
        } else {
            val captionStyleItem = captionStyle.toStyleItem()
            if (captionStyleItem.resourceUrl.startsWith(HTTPS_PREFIX).not()) {
                return
            }
            styleResourceManager.loadStyleAndFont(
                captionStyleItem,
                StyleDownloadListenerImpl(captionScope, captionStyleItem, position) { item, pos, finished ->
                    // 资源文件下载更新UI
                    val fontDownloadProgress = item.fontItem?.progress ?: 0
                    // 样式和字体下载的平均进度显示更新在UI上
                    captionStyle.progress = item.progress
                    captionStyle.fontViewData?.let { it.progress = fontDownloadProgress }
                    captionScope.launch(Dispatchers.Main) {
                        notifyDownloadProgressCallback(PageType.PAGE_TYPE_STYLE, pos)
                    }
                    if (finished) {
                        // 更新数据库
                        captionScope.launch(Dispatchers.IO) {
                            // 更新保存样式资源
                            captionStyle.localPath = item.localPath
                            captionStyle.downloadState = item.downloadState
                            styleResourceManager.save(item)
                            captionStyle.fontViewData?.let {
                                // 更新保存字体资源
                                val fontItem = item.fontItem ?: return@let
                                it.localPath = fontItem.localPath
                                it.downloadState = fontItem.downloadState
                                fontResourceManager.save(fontItem)
                            }
                            captionScope.launch(Dispatchers.Main) {
                                // 安装样式字体资源
                                installCaptionStyleAssert(captionStyle, installedCallback)
                                // 下载完成的时候再补偿一次下载状态UI更新
                                notifyDownloadProgressCallback(PageType.PAGE_TYPE_STYLE, pos)
                            }
                        }
                    }
                })
        }
    }

    /**
     * 注册字体
     */
    fun applyCaptionFont(captionFont: FontViewData, position: Int, installedCallback: AssertPackageInstalledCallback) {
        if (CaptionResourceHelper.isFileExists(captionFont.localPath)) {
            val fontFamily = assetManager?.registerFontByFilePath(captionFont.localPath) ?: TextUtil.EMPTY_STRING
            installedCallback.invoke(fontFamily, captionFont)
        } else {
            val captionFontItem = captionFont.toFontItem()
            if (captionFontItem.resourceUrl.startsWith(HTTPS_PREFIX).not()) {
                installedCallback.invoke(TextUtil.EMPTY_STRING, captionFont)
                return
            }
            fontResourceManager.loadFile(
                captionFont.resourceId,
                FontDownloadListenerImpl(captionScope, position) { item, pos, finished ->
                    // 资源文件下载更新UI
                    captionFont.progress = item.progress
                    captionScope.launch(Dispatchers.Main) {
                        notifyDownloadProgressCallback(PageType.PAGE_TYPE_FONT, pos)
                    }
                    if (finished) {
                        // 更新数据库
                        captionScope.launch(Dispatchers.IO) {
                            captionFont.downloadState = item.downloadState
                            captionFont.localPath = item.localPath
                            fontResourceManager.save(item)
                            captionScope.launch(Dispatchers.Main) {
                                val fontFamily = assetManager?.registerFontByFilePath(captionFont.localPath) ?: TextUtil.EMPTY_STRING
                                installedCallback.invoke(fontFamily, captionFont)
                                // 下载完成的时候再补偿一次下载状态UI更新
                                notifyDownloadProgressCallback(PageType.PAGE_TYPE_FONT, pos)
                            }
                        }
                    }
                })
        }
    }

    /**
     * 获取本地字幕样式数据回调并显示
     */
    private suspend fun fetchLocalStyleData(): List<StyleViewData> {
        // 从数据库里面取样式数据
        val data = styleResourceManager.loadDataSync(false)
        val styles: List<StyleViewData> = data.second.map { styleItem ->
            styleItem.toStyle().also { style ->
                if (styleItem.fontId.isNotEmpty()) {
                    // 根据绑定的fontId从数据库里面查询字体信息
                    style.fontViewData = fontResourceManager.getItem(styleItem.fontId)?.toFont()
                }
            }
        }
        return styles
    }

    /**
     * 获取本地内置字体数据
     */
    private suspend fun fetchLocalFontData(): List<FontViewData> {
        // 从数据库里面取样式数据
        val fonts: List<FontViewData> = fontResourceManager.loadDataSync(false).second.map { it.toFont() }
        return fonts
    }

    /**
     * 安装下载好的样式资源
     */
    private fun installCaptionStyleAssert(captionStyle: StyleViewData, installedCallback: AssertPackageInstalledCallback) {
        installedDownloadItems[captionStyle.localPath] = captionStyle
        resourceInstalledCallback[captionStyle.localPath] = installedCallback
        val assetType = getAssetType(captionStyle.localPath)
        val captionType = getCaptionType(captionStyle.localPath)
        // 设置item对应的样式需要的字幕类型
        captionStyle.captionType = captionType
        assetManager?.installAsset(captionStyle.localPath, null, assetType, object : IAssetProcessListener {
            override fun onFinishAssetPackageInstallation(assetPackageId: String?, assetPackageFilePath: String?, assetPackageType: Int) {
                onFinishAssetPackageInstallation(
                    assetPackageId ?: TextUtil.EMPTY_STRING,
                    assetPackageFilePath ?: TextUtil.EMPTY_STRING
                )
            }

            override fun onFinishAssetPackageUpgrading(assetPackageId: String?, assetPackageFilePath: String?, assetPackageType: Int) {
                onFinishAssetPackageInstallation(
                    assetPackageId ?: TextUtil.EMPTY_STRING,
                    assetPackageFilePath ?: TextUtil.EMPTY_STRING
                )
            }

            override fun onAssetProcessError(code: Int) = Unit
        })
    }

    private fun onFinishAssetPackageInstallation(assetPackageId: String, assetPackageFilePath: String) {
        installedDownloadItems[assetPackageFilePath]?.let { item ->
            if (item.selected) {
                // 当前item被选中时才回调结果
                resourceInstalledCallback[assetPackageFilePath]?.invoke(assetPackageId, item)
                // 安装成功后将id赋值给item
                item.assetPackageId = assetPackageId
                // 回调完成后删除对应的回调函数
                installedDownloadItems.remove(assetPackageFilePath)
                resourceInstalledCallback.remove(assetPackageFilePath)
            }
        }
    }

    /**
     * 获取资源类型
     */
    private fun getAssetType(filePath: String): Int {
        val suffixName = CaptionResourceHelper.getFileExtension(filePath)
        return resourceTypeIndex[suffixName]?.resourceType ?: AssetPackageType.ASSET_PACKAGE_TYPE_CAPTIONSTYLE
    }

    /**
     * 根据资源文件路径获取需要的字幕类型
     */
    private fun getCaptionType(filePath: String): Int {
        val suffixName = CaptionResourceHelper.getFileExtension(filePath)
        return resourceTypeIndex[suffixName]?.captionType ?: CaptionType.TYPE_NORMAL_CAPTION
    }


    /**
     * 资源销毁
     */
    fun destroy() {
        installedDownloadItems.clear()
        resourceInstalledCallback.clear()
        fontItemsLoadedCallback = null
        styleItemsLoadedCallback = null
        styleResourceManager.release()
        fontResourceManager.release()
    }

    companion object {

        private const val TAG = "ResourceManager"

        /**
         * http协议
         */
        private const val HTTPS_PREFIX = "https"

        /**
         * 拼装字幕-花字的资源文件后缀名
         */
        private const val CAPTION_RENDERER_SUFFIX = ".captionrenderer"

        /**
         * 拼装字幕-气泡资源文件后缀名
         */
        private const val CAPTION_CONTEXT_SUFFIX = ".captioncontext"

        /**
         * 拼装字幕-组合动画资源文件后缀名
         */
        private const val CAPTION_ANIMATION_SUFFIX = ".captionanimation"

        /**
         * 拼装字幕-入动画资源文件后缀名
         */
        private const val CAPTION_IN_ANIMATION_SUFFIX = ".captioninanimation"

        /**
         * 拼装字幕-出动画资源文件后缀名
         */
        private const val CAPTION_OUT_ANIMATION_SUFFIX = ".captionoutanimation"

        /**
         * 标准字幕类型资源文件后缀名,包含：普通、边框、遂字、动态底图
         */
        private const val CAPTION_NORMAL_SUFFIX = ".captionstyle"
    }
}

/**
 * 本地资源加载成功的回调
 */
typealias LocalResourceLoadedCallback = (resourceItems: List<DownloadItem>) -> Unit

/**
 * 字幕资源包或字体安装是否成功的回调
 * @param assetPackageId: 如果是样式，则为style id，如果是字体，则为字体fontFamily
 */
typealias AssertPackageInstalledCallback = (assetPackageId: String, item: DownloadItem) -> Unit

/**
 * 字幕资源枚举类型
 *
 * @param captionType 字幕类型
 * @param resourceType 字幕样式资源类型
 */
enum class CaptionResourceType(val captionType: Int, val resourceType: Int) {
    /**
     * 标准字幕
     */
    NORMAL_CAPTION(CaptionType.TYPE_NORMAL_CAPTION, AssetPackageType.ASSET_PACKAGE_TYPE_CAPTIONSTYLE),

    /**
     * 拼装字幕-气泡
     */
    MODULAR_CONTEXT_CAPTION(CaptionType.TYPE_MODULAR_CONTEXT_CAPTION, AssetPackageType.ASSET_PACKAGE_TYPE_CAPTION_CONTEXT),

    /**
     * 拼装字幕-花字
     */
    MODULAR_RENDERER_CAPTION(CaptionType.TYPE_MODULAR_RENDERER_CAPTION, AssetPackageType.ASSET_PACKAGE_TYPE_CAPTION_RENDERER),

    /**
     * 拼装字幕-组合动画
     */
    MODULAR_ANIMATION_CAPTION(CaptionType.TYPE_MODULAR_ANIMATION_CAPTION, AssetPackageType.ASSET_PACKAGE_TYPE_CAPTION_ANIMATION),

    /**
     * 拼装字幕-入动画
     */
    MODULAR_IN_ANIMATION_CAPTION(CaptionType.TYPE_MODULAR_IN_ANIMATION_CAPTION, AssetPackageType.ASSET_PACKAGE_TYPE_CAPTION_IN_ANIMATION),

    /**
     * 拼装字幕-出动画
     */
    MODULAR_OUT_ANIMATION_CAPTION(CaptionType.TYPE_MODULAR_OUT_ANIMATION_CAPTION, AssetPackageType.ASSET_PACKAGE_TYPE_CAPTION_OUT_ANIMATION)
}
