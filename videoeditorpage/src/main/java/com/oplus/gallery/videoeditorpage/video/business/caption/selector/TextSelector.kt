/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextSelector.kt
 ** Description : 文字文本效果面板组件
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import android.view.View
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.TextDecorationAdapter
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecoration
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecorationItem
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.LinearSpacingItemDecoration
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.ProgressSeekbar

/**
 * 文字文本效果面板组件
 */
internal class TextSelector(
    pageType: PageType
) : TextEffectsSelector(pageType) {

    /**
     * 文字加粗、下划线、斜体、中划线配置数据
     */
    private val decorationData: List<TextDecorationItem>
        get() {
            return listOf(
                TextDecorationItem(
                    TextDecoration.BOLD,
                    R.drawable.videoeditor_caption_item_decoration_bold_selector
                ),
                TextDecorationItem(
                    TextDecoration.ITALIC,
                    R.drawable.videoeditor_caption_item_decoration_italic_selector
                ),
                TextDecorationItem(
                    TextDecoration.UNDERLINE,
                    R.drawable.videoeditor_caption_item_decoration_underline_selector
                ),
                TextDecorationItem(
                    TextDecoration.LINE_THROUGH,
                    R.drawable.videoeditor_caption_item_decoration_line_through_selector
                )
            )
        }

    /**
     * 文字装饰适配器
     */
    private var decorationAdapter: TextDecorationAdapter? = null

    override fun initViews() {
        super.initViews()
        initDecorationSelector()
    }

    override fun initData() = Unit

    override fun getTextEffectsTypeConfig(): List<TextEffectsType> {
        return listOf(TextEffectsType.TRANSPARENT)
    }

    /**
     * 更新显示文本装饰到UI控件
     */
    fun showTextDecorationValue(
        isItalic: Boolean,
        isBold: Boolean,
        isUnderLine: Boolean,
        isLineThrough: Boolean
    ) {
        decorationAdapter?.displayTextDecorationValues(isItalic, isBold, isUnderLine, isLineThrough)
    }

    private fun initDecorationSelector() {
        container?.let {
            // 初始化文字装饰选择器
            val decorationRecyclerView = RecyclerView(it.context).apply {
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                // 设置尺寸参数
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT,

                    ).apply {
                    if (isMiddleAndLargeWindow(context).not()) {
                        // 设置padding
                        val padding = resources.getDimensionPixelSize(R.dimen.videoeditor_caption_decoration_padding_start)
                        var paddingLeft = 0
                        var paddingRight = 0
                        if (ResourceUtils.isRTL(context)) {
                            paddingRight = padding
                        } else {
                            paddingLeft = padding
                        }
                        setPadding(paddingLeft, 0, paddingRight, 0)
                    }
                    // 设置margin
                    setMargins(
                        0,
                        resources.getDimensionPixelSize(R.dimen.videoeditor_caption_decoration_margin_top),
                        0,
                        0
                    )
                }
                clipChildren = false
                clipToPadding = false
                val itemHorizontalSpacing =
                    resources.getDimensionPixelSize(R.dimen.videoeditor_item_caption_decoration_spacing)
                addItemDecoration(LinearSpacingItemDecoration(itemHorizontalSpacing, false))
                decorationAdapter = TextDecorationAdapter(this).apply {
                    setDataSource(decorationData)
                    onItemClickCallback = { v: View?, position: Int, data: TextDecorationItem? ->
                        //拿到被选中的TextDecorationItem进行设置
                        data?.let { decorationItem ->
                            captionEffectsChangedListener?.textDecorationChanged(
                                pageType,
                                decorationItem
                            )
                        }
                    }
                }
                adapter = decorationAdapter
            }
            // 将文字装饰控件添加到父视图中
            addView(decorationRecyclerView)
        }
    }

    /**
     * 滑动取值后进行更新字幕设置
     */
    override fun onChanged(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) {
        when (seekBar.progressId) {
            TextEffectsType.TRANSPARENT.progressId -> updateTransparent(progress, isIncreasing)
        }
    }

    private fun updateTransparent(progress: Float, isIncreasing: Boolean) {
        // 更新透明度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.TRANSPARENT,
            progress,
            isIncreasing
        )
    }

    /**
     * 资源销毁
     */
    override fun destroy() {
        decorationAdapter?.destroy()
        decorationAdapter = null
        super.destroy()
    }

    companion object {
        private const val TAG = "TextSelector"
    }
}
