/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalHdrInfoStruct
 ** Description: LocalHdrInfo 信息
 **
 ** Version: 1.0
 ** Date: 2025/07/19
 ** Author: 80348695
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80348695                          2025/07/19  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.extend

/**
 * LocalHdrInfo 信息 目前只读取版本号等基本信息
 */
class LocalHdrInfoStruct(byteArray: ByteArray) : ExtendStruct<LocalHdrInfoData>(byteArray) {
    val version: Float32
    val length: Float32
    val metaSize: Float32
    val offset: Float32
    init {
        version = Float32()
        length = Float32()
        metaSize = Float32()
        offset = Float32()
    }

    override fun toData(): LocalHdrInfoData {
        return LocalHdrInfoData().also { data ->
            data.version = version.get()
            data.length = length.get()
            data.metaSize = metaSize.get()
            data.offset = offset.get()
        }
    }
}

/**
 * LocalHdrInfo 的基本信息
 *
 * @property version 版本号
 * @property length 文件长度
 * @property metaSize 元数据大小
 * @property offset 元数据偏移
 */
data class LocalHdrInfoData(
    var version: Float = 0f,
    var length: Float = 0f,
    var metaSize: Float = 0f,
    var offset: Float = 0f,
)