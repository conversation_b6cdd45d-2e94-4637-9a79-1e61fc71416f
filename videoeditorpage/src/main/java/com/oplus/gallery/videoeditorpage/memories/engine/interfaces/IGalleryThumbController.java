/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IGalleryThumbController.java
 ** Description: interface for thumb control.
 ** Version: 1.0
 ** Date : 2018/01/05
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2018/01/05    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

import android.view.View;

public interface IGalleryThumbController {

    // get video time position with given x position in view
    long mapTimelinePosFromX(int x);

    // get x position in view with given video time
    int mapXFromTimelinePos(long time);

    double getPixelPerMicrosecond();

    int getScrollState();

    void setScrollListener(ThumbScrollerListener scrollerListener);

    void setTouchListener(View.OnTouchListener touchListener);

    void smoothScrollTo(int x, int y);

    void scrollTo(int x, int y);

    void fullScroll(int pos);

    void stopScroll();

    interface ThumbScrollerListener {
        void onScrolled(int pos);
        void onScrollStateChanged(int newState);
    }
}
