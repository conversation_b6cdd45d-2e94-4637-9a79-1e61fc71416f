/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SLRectF.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class SLRectF implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @SerializedName("left")
    public float mLeft;
    @SerializedName("top")
    public float mTop;
    @SerializedName("right")
    public float mRight;
    @SerializedName("bottom")
    public float mBottom;

    public SLRectF(float l, float t, float r, float b) {
        this.mLeft = l;
        this.mTop = t;
        this.mRight = r;
        this.mBottom = b;
    }

    public float width() {
        return mRight - mLeft;
    }

    public float height() {
        return mBottom - mTop;
    }

    @Override
    public String toString() {
        return "SLRectF{"
                + "mLeft=" + mLeft
                + ", mTop=" + mTop
                + ", mRight=" + mRight
                + ", mBottom=" + mBottom
                + '}';
    }
}

