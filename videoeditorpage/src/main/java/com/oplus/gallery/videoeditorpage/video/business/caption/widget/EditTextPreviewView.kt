/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditTextPreviewView.kt
 ** Description : 字幕编辑预览显示框
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.CornerPathEffect
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PointF
import android.graphics.RectF
import android.graphics.Region
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.appcompat.content.res.AppCompatResources
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.widget.base.IntercepableBaseView
import com.oplus.gallery.videoeditorpage.video.business.caption.util.ViewUtils
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.sqrt
import kotlin.math.pow

class EditTextPreviewView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : IntercepableBaseView(context, attrs) {

    /**
     * 记录单指点击的位置状态：删除、旋转缩放、预览区域内、预览区域外
     * 取值为静态整型常量DOWN_DELETE、DOWN_SCALE_ROTATE、DOWN_IN_CAPTION、DOWN_OUT_CAPTION
     */
    private var downState = 0

    /**
     * 编辑框手势响应监听器
     */
    private val captionEditTextListeners: MutableList<OnCaptionEditTextListener> = mutableListOf()

    /**
     * 记录上一次的单指位置，用于移动的时候和当前的位置进行角度和距离的计算
     */
    private val downPointF = PointF(0f, 0f)

    /**
     * 旋转缩放按钮矩形区域
     */
    private val rotateScaleButtonRectF = RectF()

    /**
     * 删除按钮矩形区域
     */
    private val deleteButtonRectF = RectF()

    /**
     * 绘制显示矩形区域
     */
    private var drawRect: List<PointF>? = null

    /**
     * 主矩形path
     */
    private val rectPath = Path()

    /**
     * 外描边矩形path
     */
    private val rectOuterStrokePath = Path()

    /**
     * 内描边矩形path
     */
    private val rectInnerStrokePath = Path()

    /**
     * 删除按钮图标
     */
    private val deleteIcon: Drawable?

    /**
     * 旋转缩放按钮图标
     */
    private val scaleIcon: Drawable?

    /**
     * 按钮的宽度
     */
    private val iconWidth: Int

    /**
     * 矩形框的边距
     */
    private val rectPaddingLeftRight: Int =
        resources.getDimensionPixelSize(R.dimen.videoeditor_caption_edit_text_preview_rect_padding_left_right)
    private val rectPaddingTopBottom: Int =
        resources.getDimensionPixelSize(R.dimen.videoeditor_caption_edit_text_preview_rect_padding_top_bottom)

    /**
     * 双指点击的开始位置x
     */
    private var twoFingerStartX = 0f

    /**
     * 双指点击的开始位置y
     */
    private var twoFingerStartY = 0f

    /**
     *  记录上一次点击的时间，用于判定是否双击
     */
    private var preClickTimeMs = 0L

    /**
     * 记录单指点击移动的距离，用于判定当前移动的距离是否符合移动处理
     */
    private var clickMoveDistance = 0.0

    /**
     * 显示矩形区域主边框画刷
     */
    private val rectPaint: Paint

    /**
     * 显示矩形区域主边框外描边画刷
     */
    private val outerStrokeRectPaint: Paint

    /**
     * 显示矩形区域主边框内描边画刷
     */
    private val innerStrokeRectPaint: Paint

    /**
     * 双指起始的间距
     */
    private var twoFingerStartLength = 0.0

    /**
     * 双指结束的间距
     */
    private var twoFingerEndLength = 0.0

    /**
     * 双指的上一次相对位置坐标，用于计算上一次的双指角度
     */
    private val twoFingerLastPoint = PointF()

    /**
     * 单指的当前位置x
     */
    private var oneFingerTargetX = 0f

    /**
     * 单指的当前位置y
     */
    private var oneFingerTargetY = 0f

    /**
     *  矩形预览区域中心点坐标
     */
    private val centerPointOfRect = PointF()

    /**
     * 标识是否需要显示删除、缩放旋转按钮
     * 值为两个静态整形常量值 DRAW_FLAG_DELETE和DRAW_FLAG_SCALE_ROTATE
     */
    var drawFlag: Int = 0
        set(value) {
            if (field != value) {
               field = value
                invalidate()
            }
        }

    /**
     * 标识是否需要画矩形框
     */
    private var enabledDrawLine = false

    /**
     * 标识是否双指变单指，主要记录最近一次为双指的状态
     */
    private var fromTwoFingerToOneFinger = false

    /**
     * 标识是否需要处理响应双击事件
     */
    private var needDealDoubleTap = false

    init {
        rectPaint = Paint().apply {
            color = resources.getColor(R.color.white, null)
            isAntiAlias = true
            strokeWidth = resources.getDimension(R.dimen.videoeditor_caption_edit_text_preview_rect_stroke_width)
            style = Paint.Style.STROKE
            pathEffect = CornerPathEffect(resources.getDimension(R.dimen.videoeditor_caption_edit_text_preview_rect_radius))
        }

        iconWidth = resources.getDimensionPixelSize(R.dimen.videoeditor_caption_edit_text_preview_icon_width)
        deleteIcon = AppCompatResources.getDrawable(context, R.drawable.videoeditor_caption_edit_text_delete)
        scaleIcon = AppCompatResources.getDrawable(context, R.drawable.videoeditor_caption_edit_text_arrow)

        outerStrokeRectPaint = Paint().apply {
            color = resources.getColor(R.color.videoeditor_edit_text_preview_stroke_color, null)
            isAntiAlias = true
            strokeWidth = ONE_PIXEL
            style = Paint.Style.STROKE
            pathEffect = CornerPathEffect(resources.getDimension(R.dimen.videoeditor_caption_edit_text_preview_rect_radius) + ONE_PIXEL)
        }

        innerStrokeRectPaint = Paint().apply {
            color = resources.getColor(R.color.videoeditor_edit_text_preview_stroke_color, null)
            isAntiAlias = true
            strokeWidth = ONE_PIXEL
            style = Paint.Style.STROKE
            pathEffect = CornerPathEffect(resources.getDimension(R.dimen.videoeditor_caption_edit_text_preview_rect_radius) - ONE_PIXEL)
        }
    }

    /**
     * 添加编辑预览框手势响应监听器
     */
    fun addEditTextListener(listener: OnCaptionEditTextListener) {
        if (captionEditTextListeners.contains(listener).not()) {
            captionEditTextListeners.add(listener)
        }
    }

    /**
     * 移除编辑预览框手势响应监听器
     */
    fun removeEditTextListener(listener: OnCaptionEditTextListener) {
        captionEditTextListeners.remove(listener)
    }

    /**
     * 设置是否显示预览矩形区域边框线
     */
    fun setDrawLineFlag(flag: Boolean) {
        enabledDrawLine = flag
        invalidate()
    }

    /**
     * 设置绘制预览矩形区域
     */
    fun setDrawRect(list: List<PointF>?, needExpandRect: Boolean) {
        drawRect = list ?: return
        if (needExpandRect) {
            ViewUtils.expandCaptionRectPointF(drawRect, rectPaddingLeftRight.toFloat(), rectPaddingTopBottom.toFloat())
        }
        invalidate()
    }

    /**
     * 设置预览矩形区域的大小
     *
     * @param list     矩形4个顶点坐标集合
     * @param drawFlag 预览区域显示标识：是否显示删除、旋转缩放按钮
     * @param drawLine 预览矩形区域边框线标识：是否显示矩形边框
     */
    fun setDrawRect(list: List<PointF>, drawFlag: Int, drawLine: Boolean) {
        this.drawFlag = drawFlag
        this.enabledDrawLine = drawLine
        setDrawRect(list, true)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawRect?.let { rect ->
            if (rect.size >= POINT_LIST_INDEX_SIZE) {

                // 绘制圆角矩形主边框
                drawRectPath(rect)

                // 绘制圆角矩形外描边边框
                drawRectOuterStrokePath(rect)

                // 绘制圆角矩形内描边边框
                drawRectInnerStrokePath(rect)

                if (enabledDrawLine) {
                    // 绘制矩形边框主边框路径
                    canvas.drawPath(rectPath, rectPaint)
                    // 绘制矩形边框主边框外描边路径
                    canvas.drawPath(rectOuterStrokePath, outerStrokeRectPaint)
                    // 绘制矩形边框主边框内描边路径
                    canvas.drawPath(rectInnerStrokePath, innerStrokeRectPaint)
                }

                if ((drawFlag and DRAW_FLAG_DELETE) != 0) {
                    drawDeleteButtonIfNeed(canvas, rect[LEFT_TOP_INDEX])
                }
                if ((drawFlag and DRAW_FLAG_SCALE_ROTATE) != 0) {
                    drawScaleRotateButtonIfNeed(canvas, rect[RIGHT_BOTTOM_INDEX])
                }
            }
        }
    }

    private fun drawRectPath(rect: List<PointF>) {
        rectPath.reset()
        rectPath.moveTo(rect[LEFT_TOP_INDEX].x, rect[LEFT_TOP_INDEX].y)
        rectPath.lineTo(rect[LEFT_BOTTOM_INDEX].x, rect[LEFT_BOTTOM_INDEX].y)
        rectPath.lineTo(rect[RIGHT_BOTTOM_INDEX].x, rect[RIGHT_BOTTOM_INDEX].y)
        rectPath.lineTo(rect[RIGHT_TOP_INDEX].x, rect[RIGHT_TOP_INDEX].y)
        rectPath.close()
    }

    private fun drawRectOuterStrokePath(rect: List<PointF>) {
        rectOuterStrokePath.reset()
        rectOuterStrokePath.moveTo(
            rect[LEFT_TOP_INDEX].x - rectPaint.strokeWidth,
            rect[LEFT_TOP_INDEX].y - rectPaint.strokeWidth
        )
        rectOuterStrokePath.lineTo(
            rect[LEFT_BOTTOM_INDEX].x - rectPaint.strokeWidth,
            rect[LEFT_BOTTOM_INDEX].y + rectPaint.strokeWidth
        )
        rectOuterStrokePath.lineTo(
            rect[RIGHT_BOTTOM_INDEX].x + rectPaint.strokeWidth,
            rect[RIGHT_BOTTOM_INDEX].y + rectPaint.strokeWidth
        )
        rectOuterStrokePath.lineTo(
            rect[RIGHT_TOP_INDEX].x + rectPaint.strokeWidth,
            rect[RIGHT_TOP_INDEX].y - rectPaint.strokeWidth
        )
        rectOuterStrokePath.close()
    }

    private fun drawRectInnerStrokePath(rect: List<PointF>) {
        rectInnerStrokePath.reset()
        rectInnerStrokePath.moveTo(
            rect[LEFT_TOP_INDEX].x + rectPaint.strokeWidth,
            rect[LEFT_TOP_INDEX].y + rectPaint.strokeWidth
        )
        rectInnerStrokePath.lineTo(
            rect[LEFT_BOTTOM_INDEX].x + rectPaint.strokeWidth,
            rect[LEFT_BOTTOM_INDEX].y - rectPaint.strokeWidth
        )
        rectInnerStrokePath.lineTo(
            rect[RIGHT_BOTTOM_INDEX].x - rectPaint.strokeWidth,
            rect[RIGHT_BOTTOM_INDEX].y - rectPaint.strokeWidth
        )
        rectInnerStrokePath.lineTo(
            rect[RIGHT_TOP_INDEX].x - rectPaint.strokeWidth,
            rect[RIGHT_TOP_INDEX].y + rectPaint.strokeWidth
        )
        rectInnerStrokePath.close()
    }

    /**
     * 绘制删除按钮
     */
    private fun drawDeleteButtonIfNeed(canvas: Canvas, pointF: PointF) {
        val length = iconWidth / TO_HALF
        deleteIcon?.let {
            it.setBounds(
                (pointF.x - length).toInt(),
                (pointF.y - length).toInt(),
                (pointF.x + length).toInt(),
                (pointF.y + length).toInt(),
            )
            it.draw(canvas)
        }
        deleteButtonRectF.set(pointF.x - length, pointF.y - length, pointF.x + length, pointF.y + length)
    }

    /**
     * 绘制缩放旋转按钮
     */
    private fun drawScaleRotateButtonIfNeed(canvas: Canvas, pointF: PointF) {
        val length = iconWidth / TO_HALF
        scaleIcon?.let {
            it.setBounds(
                (pointF.x - length).toInt(),
                (pointF.y - length).toInt(),
                (pointF.x + length).toInt(),
                (pointF.y + iconWidth / TO_HALF).toInt(),
            )
            it.draw(canvas)
        }
        rotateScaleButtonRectF.set(pointF.x - length, pointF.y - length, pointF.x + length, pointF.y + length)
    }

    /**
     * 判断某个点是否在矩形显示区域内
     *
     * @param xPos 某个点x坐标
     * @param yPos 某个点y坐标
     *
     */
    private fun curPointIsInnerDrawRect(xPos: Int, yPos: Int): Boolean {
        if ((drawFlag != 0) && enabledDrawLine.not()) {
            return false
        }
        return drawRect?.let { rect ->
            curPointIsInRect(xPos, yPos, rect)
        } ?: false
    }

    /**
     * 判断某个点是否在某个矩形显示区域内
     *
     * @param xPos 某个点x坐标
     * @param yPos 某个点y坐标
     *
     */

    fun curPointIsInRect(xPos: Int, yPos: Int, rectPoints: List<PointF>): Boolean {
        val r = RectF()
        val path = Path().apply {
            moveTo(rectPoints[LEFT_TOP_INDEX].x, rectPoints[LEFT_TOP_INDEX].y)
            lineTo(rectPoints[LEFT_BOTTOM_INDEX].x, rectPoints[LEFT_BOTTOM_INDEX].y)
            lineTo(rectPoints[RIGHT_BOTTOM_INDEX].x, rectPoints[RIGHT_BOTTOM_INDEX].y)
            lineTo(rectPoints[RIGHT_TOP_INDEX].x, rectPoints[RIGHT_TOP_INDEX].y)
            close()
            computeBounds(r, true)
        }
        val region = Region().apply {
            setPath(
                path,
                Region(r.left.toInt(), r.top.toInt(), r.right.toInt(), r.bottom.toInt())
            )
        }
        return region.contains(xPos, yPos)
    }

    /**
     * 判断字幕、删除、旋转缩放按钮是否被点击
     */
    private fun checkButtonOnDown(targetX: Float, targetY: Float): Int {
        drawRect?.let { rect ->
            if (rect.size >= POINT_LIST_INDEX_SIZE) {
                val inRotateAndScale = rotateScaleButtonRectF.contains(targetX, targetY)
                if (inRotateAndScale) return DOWN_SCALE_ROTATE
                val inDelete = deleteButtonRectF.contains(targetX, targetY)
                if (inDelete) return DOWN_DELETE
                val inCaption = curPointIsInnerDrawRect(targetX.toInt(), targetY.toInt())
                if (inCaption) return DOWN_IN_CAPTION
                return DOWN_OUT_CAPTION
            }
        }
        return DOWN_OUT_CAPTION
    }

    /**
     * 处理点击手势事件
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (onTouchEventImpl(event)) {
            return false
        }
        if (event.action != MotionEvent.ACTION_DOWN) {
            val rect = drawRect ?: return false
            if (rect.size < POINT_LIST_INDEX_SIZE) return false
        }
        val pointerCount = event.pointerCount
        if (pointerCount > TWO_FINGER) {
            // 超过双指则不响应处理
            return false
        }
        if (((event.action and MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN)
            && (pointerCount == ONE_FINGER)) {
            fromTwoFingerToOneFinger = false
        }

        if (pointerCount == TWO_FINGER) {
            fromTwoFingerToOneFinger = true
            twoFingerTouch(event)
        } else {
            oneFingerTouch(event)
        }
        return true
    }

    /**
     * 处理双指事件
     */
    private fun twoFingerTouch(event: MotionEvent) {
        if ((event.action and MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            processTwoFingerTouchDown(event)
        } else if (((event.action and MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE)
            && ((drawFlag and DRAW_FLAG_SCALE_ROTATE) != 0)) {
            processTwoFingerTouchMove(event)
        }
    }

    /**
     * 处理双指Touch Down事件
     */
    private fun processTwoFingerTouchDown(event: MotionEvent) {
        twoFingerStartX = event.x
        twoFingerStartY = event.y
        val xLen = event.getX(0) - event.getX(1)
        val yLen = event.getY(0) - event.getY(1)
        twoFingerStartLength = sqrt((xLen * xLen + yLen * yLen).toDouble())
        twoFingerLastPoint.set(xLen, yLen)
    }

    /**
     * 处理双指Touch Move事件
     */
    private fun processTwoFingerTouchMove(event: MotionEvent) {
        val xLen = event.getX(0) - event.getX(1)
        val yLen = event.getY(0) - event.getY(1)
        val oldDegree = Math.toDegrees(
            atan2(
                twoFingerLastPoint.x.toDouble(),
                twoFingerLastPoint.y.toDouble()
            )
        ).toFloat()
        val newDegree = Math.toDegrees(
            atan2(
                (event.getX(0) - event.getX(1)).toDouble(),
                (event.getY(0) - event.getY(1)).toDouble()
            )
        ).toFloat()
        twoFingerEndLength = sqrt((xLen * xLen + yLen * yLen).toDouble())
        val rect = drawRect ?: return
        centerPointOfRect.x = (rect[LEFT_TOP_INDEX].x + rect[RIGHT_BOTTOM_INDEX].x) / TO_HALF
        centerPointOfRect.y = (rect[LEFT_TOP_INDEX].y + rect[RIGHT_BOTTOM_INDEX].y) / TO_HALF
        var degree = newDegree - oldDegree
        if (abs(degree.toDouble()) < MIN_ROTATE_DEGREE) {
            degree = 0f
        }
        captionEditTextListeners.forEach {
            it.onScaleAndRotate(
                (twoFingerEndLength / twoFingerStartLength).toFloat(),
                PointF(centerPointOfRect.x, centerPointOfRect.y),
                degree
            )
        }
        twoFingerStartLength = twoFingerEndLength
        twoFingerLastPoint.set(xLen, yLen)
    }

    /**
     * 处理响应单指事件
     */
    private fun oneFingerTouch(event: MotionEvent) {
        if (fromTwoFingerToOneFinger) {
            if (event.action == MotionEvent.ACTION_UP) {
                fromTwoFingerToOneFinger = false
                captionEditTextListeners.forEach { it.onTouchUp(PointF(event.x - twoFingerStartX, event.y - twoFingerStartY))  }
            }
            return
        }
        oneFingerTargetX = event.x
        oneFingerTargetY = event.y
        when (event.action) {
            MotionEvent.ACTION_DOWN -> processOneFingerTouchDown(event)
            MotionEvent.ACTION_UP -> processOneFingerTouchUp(event)
            MotionEvent.ACTION_MOVE -> processOneFingerTouchMove(event)
            else -> {}
        }
    }

    /**
     * 处理单指Touch Down事件
     */
    private fun processOneFingerTouchDown(event: MotionEvent) {
        if ((System.currentTimeMillis() - preClickTimeMs) < ViewConfiguration.getDoubleTapTimeout()) {
            needDealDoubleTap = true
        }
        preClickTimeMs = System.currentTimeMillis()
        downState = checkButtonOnDown(oneFingerTargetX, oneFingerTargetY)
        downPointF.set(oneFingerTargetX, oneFingerTargetY)
        captionEditTextListeners.forEach { it.onTouchDown(PointF(oneFingerTargetX, oneFingerTargetY)) }
    }

    /**
     * 处理单指Touch Up事件
     */
    private fun processOneFingerTouchUp(event: MotionEvent) {
        val upState = checkButtonOnDown(oneFingerTargetX, oneFingerTargetY)
        var saveFlag = true
        if ((clickMoveDistance < HAND_MOVE_DISTANCE) && (downState == upState)) {
            if (downState == DOWN_DELETE) {
                captionEditTextListeners.forEach { it.onDelete() }
                saveFlag = false
            } else if (downState == DOWN_IN_CAPTION) {
                if (needDealDoubleTap) {
                    captionEditTextListeners.forEach { it.onDoubleClick(PointF(oneFingerTargetX, oneFingerTargetY)) }
                } else {
                    captionEditTextListeners.forEach { it.onCaptionClick() }
                }
            } else if (downState == DOWN_OUT_CAPTION) {
                captionEditTextListeners.forEach { it.onOutOfCaptionRectClick() }
            }
        }
        if (saveFlag) {
            captionEditTextListeners.forEach { it.onTouchUp(PointF(oneFingerTargetX, oneFingerTargetY))  }
        }
        clickMoveDistance = 0.0
        needDealDoubleTap = false
    }

    /**
     * 处理单指Touch Move事件
     */
    private fun processOneFingerTouchMove(event: MotionEvent) {
        clickMoveDistance += (sqrt(
            (oneFingerTargetX - downPointF.x).pow(SQUARE_NUM2) + (oneFingerTargetY - downPointF.y).pow(SQUARE_NUM2)
        ))
        val rect = drawRect ?: return
        centerPointOfRect.x = (rect[LEFT_TOP_INDEX].x + rect[RIGHT_BOTTOM_INDEX].x) / TO_HALF
        centerPointOfRect.y = (rect[LEFT_TOP_INDEX].y + rect[RIGHT_BOTTOM_INDEX].y) / TO_HALF
        if ((downState == DOWN_SCALE_ROTATE) && ((drawFlag and DRAW_FLAG_SCALE_ROTATE) != 0)) {
            val offset = getScalePercentage(centerPointOfRect, oneFingerTargetX, oneFingerTargetY)
            val angle = getRotateDegree(centerPointOfRect, oneFingerTargetX, oneFingerTargetY)
            captionEditTextListeners.forEach {
                it.onScaleAndRotate(
                    offset,
                    PointF(centerPointOfRect.x, centerPointOfRect.y),
                    -angle
                )
            }
        } else if ((downState == DOWN_IN_CAPTION) && enabledDrawLine) {
            // 检查是否移出屏幕外
            if ((oneFingerTargetX >= width)
                || (oneFingerTargetY >= height)
                || (clickMoveDistance < HAND_MOVE_DISTANCE)) return

            captionEditTextListeners.forEach { it.onDrag(downPointF, PointF(oneFingerTargetX, oneFingerTargetY)) }
        }
        downPointF.set(oneFingerTargetX, oneFingerTargetY)
    }

    override fun setOnGenericMotionListener(listener: OnGenericMotionListener) {
        super.setOnGenericMotionListener(listener)
    }

    /**
     * 缩放比例值计算：大于1表示放大，小于1表示缩小
     * 计算从 centerPointF（中心点）到 (targetX, targetY)（目标点）的距离，与之前存储的 downPointF（初始点）到中心点距离的 比例
     */
    private fun getScalePercentage(centerPointF: PointF, targetX: Float, targetY: Float): Float {
        val preLength =
            sqrt(((downPointF.x - centerPointF.x).pow(SQUARE_NUM2) + (downPointF.y - centerPointF.y).pow(SQUARE_NUM2)))
        val length = sqrt(((targetX - centerPointF.x).pow(SQUARE_NUM2) + (targetY - centerPointF.y).pow(SQUARE_NUM2)))
        return (length / preLength)
    }

    /**
     * 计算从 初始点 (downPointF) 到 目标点 (targetX, targetY) 相对于 中心点 (centerPointF) 的旋转角度（顺时针或逆时针方向）
     */
    private fun getRotateDegree(centerPointF: PointF, targetX: Float, targetY: Float): Float {
        val radian = (atan2(
            (targetY - centerPointF.y).toDouble(),
            (targetX - centerPointF.x).toDouble()
        ) - atan2(
            (downPointF.y - centerPointF.y).toDouble(),
            (downPointF.x - centerPointF.x).toDouble()
        )).toFloat()
        return (radian * PI_TO_RADIUS / Math.PI).toFloat()
    }

    /**
     * 文字预览框响应事件监听器
     */
    interface OnCaptionEditTextListener {
        /**
         * Touch Down回调
         *
         */
        fun onTouchDown(curPoint: PointF)

        /**
         * Touch Up回调
         */
        fun onTouchUp(curPoint: PointF)

        /**
         * 缩放旋转时回调
         *
         * @param scalePercent 缩放值
         * @param anchor       旋转中心点
         * @param rotateDegree 旋转角度
         */
        fun onScaleAndRotate(scalePercent: Float, anchor: PointF, rotateDegree: Float)

        /**
         * 点击删除按钮回调
         */
        fun onDelete()

        /**
         * 点击预览框拖拽回调
         *
         * @param prePointF 拖拽时旧坐标
         * @param nowPointF 拖拽时新坐标
         */
        fun onDrag(prePointF: PointF, nowPointF: PointF)

        /**
         * 点击文字预览区域外回调
         */
        fun onOutOfCaptionRectClick()

        /**
         * 点击文字预览区域内回调
         */
        fun onCaptionClick()

        /**
         * 双击回调
         */
        fun onDoubleClick(pointF: PointF)
    }

    companion object {
        private const val TAG = "EditTextPreviewView"

        /**
         * 设置是否显示删除按钮标识
         */
        const val DRAW_FLAG_DELETE: Int = 1

        /**
         * 设置是否显示缩放旋转按钮标识
         */
        const val DRAW_FLAG_SCALE_ROTATE: Int = 1 shl 1

        /**
         * 判断是否移动的临界距离值
         */
        private const val HAND_MOVE_DISTANCE = 10.0

        /**
         * 左上角顶点的索引
         */
        const val LEFT_TOP_INDEX = 0

        /**
         * 左下角顶点索引
         */
        const val LEFT_BOTTOM_INDEX = 1

        /**
         * 右上角顶点索引
         */
        const val RIGHT_TOP_INDEX = 3

        /**
         * 右下角顶点索引
         */
        const val RIGHT_BOTTOM_INDEX = 2


        /**
         * 绘制矩形框4个点数组大小
         */
        private const val POINT_LIST_INDEX_SIZE = 4

        /**
         * 角度转弧度参数值
         */
        private const val PI_TO_RADIUS = 180


        private const val NUMBER_TOW = 2

        /**
         * 判断是否旋转的角度临界值
         */
        private const val MIN_ROTATE_DEGREE = 0.5f

        /**
         * 单指
         */
        private const val ONE_FINGER = 1

        /**
         * 双指
         */
        private const val TWO_FINGER = 2

        /**
         * 点击删除按钮标识
         */
        private const val DOWN_DELETE = 0x110

        /**
         * 点击缩放旋转按钮标识
         */
        private const val DOWN_SCALE_ROTATE = 0x120

        /**
         * 点击文字预览区域内标识
         */
        private const val DOWN_IN_CAPTION = 0x130

        /**
         * 点击文字预览区域外标识
         */
        private const val DOWN_OUT_CAPTION = 0x140

        /**
         * 被除数2
         */
        private const val TO_HALF = 2f

        /**
         * 1像素单位
         */
        private const val ONE_PIXEL = 1f

        /**
         * 平方指数2
         */
        private const val SQUARE_NUM2 = 2
    }
}