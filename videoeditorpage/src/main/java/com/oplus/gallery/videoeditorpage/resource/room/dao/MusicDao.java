/*
 * ********************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File        :  - MusicDao.java
 *  ** Description : xxxxxxxxxxxx
 *  ** Version     : 1.0
 *  ** Date        : 2019/49/19
 *  ** Author      : <EMAIL>
 *  **
 *  ** ---------------------Revision History: ----------------------------
 *  **  <author>                 <data>      <version>  <desc>
 *  **  <EMAIL>  2019/7/19  1.0        build this module
 *  **********************************************************************
 */

package com.oplus.gallery.videoeditorpage.resource.room.dao;

import androidx.room.Dao;
import androidx.room.Query;

import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;

import java.util.List;

@Dao
public interface MusicDao extends SimpleDao<MusicItem> {

    @Query("SELECT * FROM source_music")
    List<MusicItem> queryAll();

    @Query("SELECT * FROM source_music where download_state & " + FILE_DOWNLOADED + " = " + FILE_DOWNLOADED)
    List<MusicItem> queryEnableMusic();

    @Query("SELECT * FROM source_music where download_state & " + FILE_DOWNLOADED + " = " + NOT_DOWNLOADED)
    List<MusicItem> queryNotDownloadedItem();

    @Query("SELECT * FROM source_music where download_state & " + (ICON_DOWNLOADED | ICON_DEFAULT)
            + " > " + NOT_DOWNLOADED)
    List<MusicItem> queryIconExistedMusic();

    @Query("SELECT * FROM source_music WHERE builtin = 1")
    List<MusicItem> getAllBuiltin();

    @Query("DELETE FROM source_music WHERE builtin = 1")
    void clearBuiltin();

    @Query("SELECT * FROM source_music WHERE position = :position")
    MusicItem getEntityByPosition(int position);

    @Query("SELECT * FROM source_music WHERE music_id = :musicId")
    MusicItem getEntityByMusicId(int musicId);

    @Query("SELECT * FROM source_music WHERE download_state & " + ICON_DOWNLOADED + " = " + NOT_DOWNLOADED)
    List<MusicItem> getNoIconEntityList();

    @Query("SELECT * FROM source_music WHERE position > :maxPosition")
    List<MusicItem> getInvalidEntityList(int maxPosition);

    @Query("DELETE FROM source_music WHERE position > :maxPosition")
    int deleteInvalidEntity(int maxPosition);

    @Query("SELECT * FROM source_music WHERE source_path = :musicPath")
    MusicItem getMusicBySourcePath(String musicPath);
}
