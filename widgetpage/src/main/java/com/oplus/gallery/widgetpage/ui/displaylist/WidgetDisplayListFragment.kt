/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WidgetAlbumFragment
 ** Description:
 ** Version: 1.0
 ** Date: 2020-11-11
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG: OPLUS_WIDGET
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2020/11/11     1.0        ADD
 ********************************************************************************/
package com.oplus.gallery.widgetpage.ui.displaylist

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_CODE
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_MODE
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.selection.PathSelectionManager
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectType
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns.MODE_DEFAULT
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.widgetpage.R
import com.oplus.gallery.widgetpage.WidgetConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@RouterNormal(RouterConstants.RouterName.WIDGET_DISPLAY_LIST_FRAGMENT)
class WidgetDisplayListFragment : BaseAlbumFragment() {
    override val needExitWhenEmpty: Boolean = false
    private var widgetCode: String = TextUtil.EMPTY_STRING
    private var widgetMode = MODE_DEFAULT

    private var widgetViewModel: WidgetDisplayListViewModel? = null

    override fun showSearchMenu(): Boolean = false

    override fun showToolbarSubtitle(): Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        needScrollToBottom = false
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        widgetViewModel = baseListViewModel as? WidgetDisplayListViewModel
        widgetCode = arguments?.getString(KEY_WIDGET_CODE) ?: return
        widgetMode = arguments?.getInt(KEY_WIDGET_MODE) ?: MODE_DEFAULT
        recyclerView?.layoutManager?.apply {
            if (this is GridLayoutManager) {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return 1
                    }
                }
            }
        }
    }

    override fun onCreateViewModel(): ListViewModel<MediaItem, ItemViewData> {
        return ViewModelProvider(this).get(WidgetDisplayListViewModel::class.java).apply {
            isSupportedPersonalFilter = <EMAIL>
        }
    }

    override fun initLayoutDetail(context: Context): LayoutDetail = super.initLayoutDetail(context).apply {
        headerCount = 1
    }

    private fun goToSelectPage() {
        lifecycleScope.launch(Dispatchers.UI) {
            val activity = activity ?: return@launch
            val selectionData = PathSelectionManager.createSelectionData()
            collectSelectedItems().apply {
                selectionData.selectItems(this.toSet())
                selectionData.immutableItems = this.toSet()
            }
            Starter.DialogFragmentStarter<PanelDialog>(
                fm = activity.supportFragmentManager,
                bundle = SelectInputData(
                    selectMulti = true,
                    selectType = SelectType.WIDGET,
                    countAtMost = WidgetConstants.DisplayList.MAX_CUSTOM_LIST_SIZE,
                    countAtMostHintResId = R.string.widget_custom_tip_num_limit,
                    selectionDataId = selectionData.selectionDataId
                ).createBundle().also {
                    it.putString(KEY_REQUEST_KEY, KEY_WIDGET_DISPLAY_LIST)
                },
                postCard = PostCard(SELECTION_PANEL_DIALOG),
            ).start()?.apply {
                setFragmentResultListenerSafety(KEY_WIDGET_DISPLAY_LIST) { _, bundle ->
                    onSelectionFinished(bundle)
                }
            }
        }
    }

    private suspend fun collectSelectedItems(): List<Path> {
        return widgetViewModel?.let { it.getItemPaths(0, it.getCount()) } ?: emptyList()
    }

    private fun onSelectionFinished(bundle: Bundle) {
        bundle ?: return
        if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
            GLog.w(TAG, "onSelectionFinished: cancel select items.")
            return
        }
        val pathStrArray = bundle.getStringArray(KEY_RESULT_DATA_LIST)
        if (pathStrArray.isNullOrEmpty()) {
            GLog.w(TAG, "onSelectionFinished: no selected items.")
            return
        }
        val paths = pathStrArray.map { Path.fromString(it) }
        activity?.also {
            FileProcessTaskBuilder(
                it,
                paths,
                object : FinishListener {
                    override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                        if (success) {
                            lifecycleScope.launch(Dispatchers.IO) {
                                (baseListViewModel as? WidgetDisplayListViewModel)?.run {
                                    addToDisplayListByGalleryId(pathStrArray.map { Path.fromString(it).suffix.toInt() }, widgetCode)
                                    // 如果用户移除全部后直接退出页面，会在卡片刷新时改为默认模式，但如果移除全部后没有退出，而是又继续添加了，那需要保留已选择的模式
                                    if (widgetMode != MODE_DEFAULT) {
                                        updateWidgetMode(widgetMode)
                                    }
                                }
                            }
                        } else {
                            GLog.e(TAG, "onFinished: failed, errCode=$errCode, errMsg=$errMsg")
                        }
                    }
                },
                scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_MULTI_SELECT
            )
                .addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
                .build()?.let {
                    it.execute()
                    it.autoCancelWhenOnDestroy(this.lifecycle)
                }
        }
    }

    override fun getBottomBarMenuId(): Int = R.menu.widget_selection

    override fun updateBottomBar(count: Int) {
        super.updateBottomBar(count)
        bottomMenuBar?.let { bottomMenuHelper.setItemEnable(R.id.action_moved_out, count > 0, it) }
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        val selectedItems = getSelectedItems()?.toList()
        selectedItems?.let {
            bottomMenuHelper.removeFromWidgetDisplayList(it, widgetCode, trackCallerEntry) {
                lifecycleScope.launch(Dispatchers.UI) {
                    exitSelectionMode()
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            com.oplus.gallery.basebiz.R.id.action_add_to_album -> {
                if (baseListViewModel?.totalSize ?: 0 >= WidgetConstants.DisplayList.MAX_CUSTOM_LIST_SIZE) {
                    ToastUtil.showShortToast(
                        String.format(getString(R.string.widget_custom_tip_num_limit), WidgetConstants.DisplayList.MAX_CUSTOM_LIST_SIZE)
                    )
                } else {
                    goToSelectPage()
                }
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    companion object {
        private const val TAG = "WidgetDisplayListFragment"
        private const val KEY_WIDGET_DISPLAY_LIST = "widgetDisplayList.requestKey"
    }
}