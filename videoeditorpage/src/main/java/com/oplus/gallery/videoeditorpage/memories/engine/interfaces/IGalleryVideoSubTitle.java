/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryVideoFilter.java
 * * Description: IGalleryVideoFilter interface.
 * * Version: 1.0
 * * Date : 2017/12/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/29    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

import android.content.Context;
import android.graphics.PointF;

import com.oplus.gallery.business_lib.model.data.location.api.ConfigAddress;
import com.oplus.gallery.framework.abilities.videoedit.data.SubTitleInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public interface IGalleryVideoSubTitle {
    /*---------------- Video filter start ----------------*/
    void installSubTitleStyleRes();

    boolean setWaterMark(String model, String timeAndAddress);

    void updateWaterMarkDuration();

    void updateWaterMarkPosition(boolean hideWaterMark);

    boolean hadHideWaterMark();

    boolean hasWaterMark();

    long addSubTitle(String text, Context context);

    void addSubTitleInfo(SubTitleInfo info, Context context);

    void renameSubTitle(long subTitleIndex, String newText);

    SubTitleInfo removeSubTitle(long subTitleTime, boolean needSeek);

    void moveSubTitle(long subTitleIndex, PointF prePointF, PointF nowPointF);

    List<SubTitleInfo> checkAndGetSubTitleEditPos(long currentTime);

    ArrayList<SubTitleInfo> getSubTitleList();

    void moveAllSubTitle(long offset);

    void moveAllSubTitle(Function<Long, Long> offsetCalculateFunction);

    void moveAllSubTitle(float speed);

    void computeLocationInfo(double latitude, double longitude);

    void clearLocationInfo();

    ConfigAddress getAddress();

    /*---------------- Video filter end ----------------*/
}
