/***********************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - EditorWallpaperTrimUIController.kt
 * Description: 壁纸业务 剪辑控件控制器
 * Version: 1.0
 * Date : 2025/3/18
 * Author: <EMAIL>
 * -
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
 * <EMAIL>    2025/3/18    1.0    build this module
</desc></version></data></author> */
package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.app.Activity
import android.app.Activity.RESULT_CANCELED
import android.app.Activity.RESULT_OK
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnLayoutChangeListener
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtil.clamp
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.player.IVideoPlayerListenerImpl
import com.oplus.gallery.videoeditorpage.abilities.interfaces.ITimelineCompileListener
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil
import com.oplus.gallery.videoeditorpage.utlis.VideoEditorHelper
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.IGalleryThumbController.ThumbScrollerListener
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallpaperHelper.ACTION_SET_VIDEO_WALLPAPER
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallpaperHelper.KEY_IS_VIDEO_WALLPAPER
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.WallpaperHelper.SET_VIDEO_WALLPAPER_REQUEST_CODE
import com.oplus.gallery.videoeditorpage.widget.ProgressDialog
import com.oplus.gallery.videoeditorpage.widget.VideoTrimTouchView
import com.oplus.gallery.videoeditorpage.widget.VideoTrimTouchView.TrimPositionChangeListener
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min

/**
 * 壁纸业务 剪辑控件控制器
 */
class EditorWallpaperTrimUIController(
    private val context: Context,
    private val editorEngine: EditorEngine,
    state: EditorWallpaperTrimState
) : EditorBaseUIController<Any>(context, null, state) {
    private val videoSpeed = 1f

    /**
     * 顶部返回按钮
     */
    private val cancelBtn by lazy {
        mRootView.findViewById<View>(R.id.action_bar_back)
    }

    /**
     * 顶部继续按钮
     */
    private val continueBtn by lazy {
        mRootView.findViewById<View>(R.id.action_bar_done)
    }
    private val playbackContainer =
        mRootView.findViewById<ViewGroup>(R.id.toolbar_playback_container)
    private val toolbarContainer = mRootView.findViewById<FrameLayout>(R.id.toolbar_container)
    private val playButton: ImageView = playbackContainer.findViewById(R.id.editor_btn_play)
    private val currentTimeTextView: TextView =
        playbackContainer.findViewById(R.id.video_current_position)
    private val videoDurationTextView: TextView =
        playbackContainer.findViewById(R.id.video_duration)
    private val onThumbScrollViewLayoutChangeListener: OnLayoutChangeListener =
        OnLayoutChangeListener { _, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            if (left != oldLeft || right != oldRight || top != oldTop || bottom != oldBottom) {
                toolbarContainer.post {
                    GLog.d(TAG, LogFlag.DL) {
                        "onThumbScrollViewLayoutChangeListener onLayoutChange"
                    }
                    kotlin.runCatching {
                        thumbScrollView.stopScroll()
                    }
                    videoClip?.apply {
                        setTrimInPoint(0, true)
                        setTrimOutPoint(videoDuration * MILLIS_TIME_BASE, true)
                    }
                    editorEngine.showCustomTrimThumbLoader(
                        thumbScrollView,
                        currentDuration,
                        thumbScrollViewPadding
                    )
                    thumbScrollView.setScrollListener(scrollChangeListener)
                    currentScrollStartTime = min(currentScrollStartTime, videoDuration - currentDuration)
                    val scrollX = (currentScrollStartTime * MILLIS_TIME_BASE * thumbScrollView.pixelPerMicrosecond).toInt()
                    thumbScrollView.scrollTo(scrollX, 0)
                }
            }
        }
    private val onTrimTouchViewLayoutChangeListener =
        OnLayoutChangeListener { _, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            GLog.d(TAG, LogFlag.DL) {
                "[OnLayoutChange] ${videoTrimView.width}, ${right - left}"
            }
            if (left != oldLeft || right != oldRight || top != oldTop || bottom != oldBottom) {
                toolbarContainer.post {
                    videoTrimView.setInitParam(trimMinTime, trimMaxTime, videoDuration)
                    videoTrimView.setLeftAndRightPos(leftPosPercent, rightPosPercent)
                    onPlayPositionChange()
                    if (isFirstAutoPlay) {
                        isFirstAutoPlay = false
                        autoPlay()
                    }
                }
            }
        }
    private val trimPositionChangeListener: TrimPositionChangeListener =
        object : TrimPositionChangeListener {
            override fun onSeek(pos: Float, trimLeft: Float, trimRight: Float) {
                GLog.d(TAG, LogFlag.DL) {
                    "[onSeek] pos:$pos mGalleryVideoEngine.videoDuration:$videoDuration"
                }
                startPlayPercent = pos
                editorEngine.seekTo(
                    startPlayPosition * MILLIS_TIME_BASE,
                    EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
                )
                val currentTime: Long =
                    getPlayTime(currentScrollStartTime) + (pos * playCurrentDuration).toLong()
                videoTrimView.updateTrimTime(
                    VideoEditorHelper.formatTimeWithMillis(context, currentTime)
                )
                updateShowTime(0)
            }

            override fun onTrimLeft(pos: Float) {
                GLog.d(TAG, LogFlag.DL) {
                    "[onTrimLeft] pos:$pos mGalleryVideoEngine.mVideoDuration:$videoDuration"
                }
                startPlayPercent = pos
                leftPosPercent = pos
                editorEngine.seekTo(
                    startPlayPosition * MILLIS_TIME_BASE,
                    EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
                )
                updateTrimViewText(true)
                updateShowTime(0)
                autoPlay()
            }

            override fun onTrimRight(pos: Float) {
                GLog.d(TAG, LogFlag.DL) {
                    "[onTrimRight] pos:$pos mGalleryVideoEngine.mVideoDuration:$videoDuration"
                }
                startPlayPercent = pos
                rightPosPercent = pos
                editorEngine.seekTo(
                    startPlayPosition * MILLIS_TIME_BASE,
                    EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
                )
                updateTrimViewText(false)
                updateShowTime(0)
                autoPlay()
            }
        }
    private val scrollChangeListener: ThumbScrollerListener = object : ThumbScrollerListener {
        override fun onScrolled(pos: Int) {
            if (isThumbScrollViewScrollFromUserOp) {
                val tmpTimeStamp =
                    floor(pos / thumbScrollView.pixelPerMicrosecond + PIXEL_OFFSET).toLong() / MILLIS_TIME_BASE
                refreshTimeAfterScroll(tmpTimeStamp)
            }
        }

        override fun onScrollStateChanged(newState: Int) {
            /*
             * 此次操作为用户触发：
             * 1.当前状态处于SCROLL_STATE_DRAGGING
             * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
             */
            if ((newState == GalleryHorizontalScrollView.SCROLL_STATE_DRAGGING)
                || ((thumbScrollViewLastScrollState == GalleryHorizontalScrollView.SCROLL_STATE_DRAGGING)
                        && (newState == GalleryHorizontalScrollView.SCROLL_STATE_SETTLING))
            ) {
                if (!isThumbScrollViewScrollFromUserOp) {
                    editorEngine.stopPlayer()
                }
                isThumbScrollViewScrollFromUserOp = true
            }

            /*
             * 用户触发的操作停止：当前状态为SCROLL_STATE_IDLE 且 变更前处于用户触发态 mIsThumbScrollViewScrollFromUserOp = true
             */
            if (isThumbScrollViewScrollFromUserOp && (newState == GalleryHorizontalScrollView.SCROLL_STATE_IDLE)) {
                isThumbScrollViewScrollFromUserOp = false
                val tmpTimeStamp = thumbScrollView.mapTimelinePosFromX(thumbScrollViewPadding)
                refreshTimeAfterScroll(tmpTimeStamp)
                startPlayPercent = 0f
                autoPlay()
            }

            thumbScrollViewLastScrollState = newState
        }
    }

    private val trimInPlayTime: Long
        get() {
            val realTrimInTime = videoTrimView.getTrimInTime(currentScrollStartTime)
            return getPlayTime(realTrimInTime)
        }
    private val trimOutPlayTime: Long
        get() {
            val realTrimOutTime = videoTrimView.getTrimOutTime(currentScrollStartTime)
            return getPlayTime(min(realTrimOutTime, videoDuration))
        }

    /**
     * 剪辑后视频时长，会随着移动剪辑把手动态改变
     */
    private val trimmedPlayVideoDuration: Long
        get() = trimOutPlayTime - trimInPlayTime
    private val initTrimmedPlayVideoDuration: Long
        get() {
            val trimTime = initTrimOutTime - initTrimInTime
            return getPlayTime(trimTime)
        }
    private val playCurrentDuration: Long
        get() = getPlayTime(currentDuration)
    private val videoClip: IClip?
        get() = try {
            editorEngine.currentTimeline.getVideoTrack(0).getClip(0)
        } catch (ignore: Exception) {
            null
        }
    private val currentTime: Long
        get() = editorEngine.timelineCurrentPosition / MILLIS_TIME_BASE

    /**
     * 缩图轴当前是否处于自由静止状态 即没有被拖拽 也没有在滚动
     */
    private val isScrollIdle: Boolean
        get() = !isThumbScrollViewScrollFromUserOp
    private val currentDuration: Long
        get() = min(trimMaxTime.toDouble(), videoDuration.toDouble()).toLong()
    private val startPlayPosition: Long
        get() = getStartPlayPosition(startPlayPercent)
    private val thumbScrollViewPadding: Int
        get() = videoTrimView.trimSpaceMargin

    private var videoDuration: Long = 0
    private var haveReset = false
    private var initTrimInTime: Long = 0
    private var initTrimOutTime: Long = 0
    private var startPlayPercent = 0f
    private var trimMinTime: Long = 0
    private var trimMaxTime: Long = 0
    private var leftPosPercent: Float = 0f
    private var rightPosPercent: Float = 1f
    private lateinit var thumbScrollView: GalleryVideoThumbnailView
    private lateinit var videoTrimView: VideoTrimTouchView

    /**
     * 这里标识的是一个手势操作后，带来的一些列连带效应（跟手滚动、惯性滑动、极限阻尼），一直持续到禁止态结束
     * 即：当前可滚动view的内容位置变更（scroll、fling、overScroll）是否为用户触发。
     *
     *
     * true - 用户触发：
     * 1.当前状态处于SCROLL_STATE_DRAGGING
     * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
     *
     *
     * false - 非用户触发。
     */
    private var isThumbScrollViewScrollFromUserOp = false
    private var thumbScrollViewLastScrollState = GalleryHorizontalScrollView.SCROLL_STATE_IDLE
    private var currentScrollStartTime: Long = 0
    private var isFirstAutoPlay = true

    /**
     * 保存视频进度弹窗
     */
    private var compileDialog: ProgressDialog? = null

    init {
        playbackContainer.findViewById<View>(R.id.editor_btn_play_and_time)
            .setOnClickListener { play() }
        editorEngine.addVideoPlayerListener(object : IVideoPlayerListenerImpl {
            override fun onPreloadingCompletion() {
                GLog.d(TAG, LogFlag.DL) {
                    "IVideoPlayerListenerImpl [onPreloadingCompletion]"
                }
                onPlayStatusChange()
            }

            override fun onStopped() {
                GLog.d(TAG, LogFlag.DL) {
                    "IVideoPlayerListenerImpl [onStopped]"
                }
                onPlayStatusChange()
            }

            override fun onEOF(absolutePosition: Long) {
                GLog.d(TAG, LogFlag.DL) {
                    "IVideoPlayerListenerImpl [onEOF]"
                }
                onPlayStatusChange()
                haveReset = true
                onPlayFinish()
            }

            override fun onPositionChange(absolutePosition: Long) {
                onPlayPositionChange()
            }
        })
        editorEngine.addTimelineCompileListener(object : ITimelineCompileListener {
            override fun onCompileProgress(progress: Int) {
                compileDialog?.setProgress(progress)
            }

            override fun onCompileFinished() = Unit

            override fun onCompileFailed() {
                if (compileDialog?.isShowing() == true) {
                    compileDialog?.dismiss()
                }
                if (!WallpaperHelper.checkStorageEnough(mActivity)) {
                    ToastUtil.showShortToast(R.string.videoeditor_editor_cancel_compile_error)
                }
            }

            override fun onCompileCompleted(isCanceled: Boolean) {
                if (isCanceled) {
                    if (compileDialog?.isShowing() == true) {
                        compileDialog?.dismiss()
                    }
                    ToastUtil.showShortToast(R.string.videoeditor_editor_cancel_compile_cancel)
                } else {
                    onExportCompleted()
                }
            }

            override fun onCompileFloatProgress(progress: Float) = Unit
        })
    }

    override fun createView() {
        show()
    }

    /**
     * 显示剪辑控件
     */
    private fun show() {
        GLog.d(TAG, LogFlag.DL) { "[show]" }
        videoDuration = editorEngine.timelineDuration / MILLIS_TIME_BASE
        val initTrimInPlayTime = getPlayTime(initTrimInTime)
        editorEngine.seekTo(
            initTrimInPlayTime * MILLIS_TIME_BASE,
            EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
        )

        val view = LayoutInflater.from(context).inflate(
            R.layout.videoeditor_video_editor_custom_trim_bottom_menu_layout,
            toolbarContainer,
            false
        )
        thumbScrollView = view.findViewById(R.id.video_thumb_view)
        videoTrimView = view.findViewById(R.id.trim_touch_view)
        videoTrimView.setTrimPositionChangeListener(trimPositionChangeListener)
        videoTrimView.addOnLayoutChangeListener(onTrimTouchViewLayoutChangeListener)
        thumbScrollView.addOnLayoutChangeListener(onThumbScrollViewLayoutChangeListener)
        toolbarContainer.addView(view)

        //重置为壁纸业务的点击事件
        cancelBtn.setOnClickListener(this)
        continueBtn.setOnClickListener(this)

        initShowTime()
        onPlayStatusChange()
    }

    private fun refreshTimeAfterScroll(time: Long) {
        currentScrollStartTime = clamp(time, 0L, videoDuration)
        editorEngine.seekTo(
            trimInPlayTime * MILLIS_TIME_BASE,
            EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
        )
        updateShowTime(0)
        haveReset = false
    }

    private fun onPlayPositionChange() {
        val currentTime = editorEngine.timelineCurrentPosition / MILLIS_TIME_BASE
        updateCurrentPosition(currentTime)
        val startTime = max((currentTime - trimInPlayTime).toDouble(), 0.0).toLong()
        updateShowTime(startTime)
    }

    fun onPlayStatusChange() {
        playbackContainer.postDelayed({
            val window = (context as Activity).window
            if (editorEngine.isPlaying) {
                GLog.d(TAG, LogFlag.DL) {
                    "[onPlayStatusChange] playing"
                }
                playButton.isSelected = true
                playButton.contentDescription =
                    context.getString(R.string.videoeditor_editor_pause_description)
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            } else {
                GLog.d(TAG, LogFlag.DL) {
                    "[onPlayStatusChange] not playing"
                }
                playButton.isSelected = false
                playButton.contentDescription =
                    context.getString(R.string.videoeditor_editor_play_description)
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }, STATUS_CHANGE_REFRESH_DELAY.toLong())
    }

    private fun onPlayFinish() {
        GLog.d(TAG, LogFlag.DL, "onPlayFinish()")
        updateCurrentPosition(trimOutPlayTime)
        updateShowTime(trimmedPlayVideoDuration)
    }

    private fun updateCurrentPosition(currentTime: Long) {
        val currentPlayDuration = playCurrentDuration
        if (currentPlayDuration > 0) {
            startPlayPercent =
                (checkPosition(currentTime) - getPlayTime(currentScrollStartTime)).toFloat() / currentPlayDuration.toFloat()
            videoTrimView.updateCurrentPosition(startPlayPercent)
        }
    }

    private fun updateTrimViewText(isTrimIn: Boolean) {
        val time = if (isTrimIn) {
            trimInPlayTime
        } else {
            trimOutPlayTime
        }
        videoTrimView.updateTrimTime(
            VideoEditorHelper.formatTimeWithMillis(
                context,
                VideoEditorHelper.getVideoCurrentUnitTime(time)
            )
        )
    }

    private fun initShowTime() {
        updateCurrentTimeTextView(0)
        videoDurationTextView.text = VideoEditorHelper.formatTimeWithMillis(
            context,
            VideoEditorHelper.getVideoCurrentUnitTime(initTrimmedPlayVideoDuration)
        )
    }

    private fun updateShowTime(startTime: Long) {
        updateCurrentTimeTextView(startTime)
        videoDurationTextView.text = VideoEditorHelper.formatTimeWithMillis(
            context,
            VideoEditorHelper.getVideoCurrentUnitTime(trimOutPlayTime)
        )
    }

    private fun updateCurrentTimeTextView(startTime: Long) {
        currentTimeTextView.text = VideoEditorHelper.formatTimeWithMillisByDuration(
            context,
            startTime + trimInPlayTime,
            trimOutPlayTime
        )
    }

    private fun getStartPlayPosition(percent: Float): Long {
        val time = (getPlayTime(currentScrollStartTime) + percent * playCurrentDuration).toLong()
        return checkPosition(time)
    }

    private fun checkPosition(position: Long): Long {
        return if (position < trimInPlayTime) {
            trimInPlayTime
        } else if (position > trimOutPlayTime) {
            trimOutPlayTime
        } else {
            position
        }
    }

    private fun play() {
        videoClip?.apply {
            isMuted = true
            setTrimInPoint(0, true)
            setTrimOutPoint(videoDuration * MILLIS_TIME_BASE, true)
        }

        if (editorEngine.isPlaying) {
            editorEngine.stopPlayer()
        } else {
            GLog.d(
                TAG, LogFlag.DL,
                "[play], TrimInPlayTime:" + trimInPlayTime
                        + ", TrimOutPlayTime:" + trimOutPlayTime
                        + ",getCurrentTime:" + editorEngine.timelineCurrentPosition / MILLIS_TIME_BASE
                        + ",mStartPlayPosition: " + startPlayPosition
            )
            if (haveReset || (currentTime > (trimOutPlayTime - VIDEO_PLAY_END_CHECK_GAP))) {
                startPlayPercent = videoTrimView.getBorderPosPercent(true)
                haveReset = false
            }
            editorEngine.startPlayer(
                startPlayPosition * MILLIS_TIME_BASE,
                trimOutPlayTime * MILLIS_TIME_BASE
            )
        }
    }

    private fun autoPlay() {
        play()
    }

    /**
     * 用播放时间转换实际时间
     */
    private fun getRealTime(time: Long): Long {
        return getTime(time, false)
    }

    /**
     * 用实际时间转为播放时间
     */
    private fun getPlayTime(time: Long): Long {
        return getTime(time, true)
    }

    private fun getTime(time: Long, isPlayTimeType: Boolean): Long {
        if (time <= 0) {
            return 0
        }
        // 平台视频做变速映射
        return if (isPlayTimeType) {
            (time.toFloat() / videoSpeed).toLong()
        } else {
            (time * videoSpeed).toLong()
        }
    }

    /**
     * 设置剪辑最大和最小时长
     *
     * @param trimMinTime 最小时长 ms
     * @param trimMaxTime 最大时长 ms
     */
    fun setTrimMinMaxTime(trimMinTime: Long, trimMaxTime: Long) {
        this.trimMinTime = trimMinTime
        this.trimMaxTime = trimMaxTime
        this.initTrimInTime = 0
        this.initTrimOutTime = this.currentDuration
    }

    /**
     * @return true-当前可以继续执行后续操作  false-终止后续操作
     */
    fun done(): Boolean {
        if (isScrollIdle) {
            videoClip?.apply {
                setTrimInPoint(getRealTime(trimInPlayTime) * MILLIS_TIME_BASE, true)
                setTrimOutPoint(getRealTime(trimOutPlayTime) * MILLIS_TIME_BASE, true)
                return true
            }
        }
        return false
    }

    override fun resume(isActivityResume: Boolean) {
        super.resume(isActivityResume)
        onPlayStatusChange()
    }

    override fun pause(isActivityPause: Boolean) {
        super.pause(isActivityPause)
        if (editorEngine.isPlaying) {
            editorEngine.stopPlayer()
        }
    }

    override fun destroyView() {
        super.destroyView()
        videoTrimView.removeOnLayoutChangeListener(onTrimTouchViewLayoutChangeListener)
        thumbScrollView.removeOnLayoutChangeListener(onThumbScrollViewLayoutChangeListener)
    }

    override fun onUiConfigChanged(
        baseActivity: BaseActivity,
        layoutId: Int,
        config: EditorAppUiConfig
    ) {
        onPlayStatusChange()
    }

    override fun onClick(v: View) {
        if (ClickUtil.isDoubleClick()) {
            return
        }
        when (v.id) {
            R.id.action_bar_back -> {
                mActivity.setResult(RESULT_CANCELED)
                mActivity.finish()
            }

            R.id.action_bar_done -> {
                if (done()) {
                    WallpaperHelper.saveVideo(mActivity, editorEngine, ::showCompileDialog)
                }
            }
        }
    }

    private fun showCompileDialog() {
        mActivity.apply {
            if (isFinishing || isDestroyed) {
                GLog.d(TAG, LogFlag.DL) { "[showCompileDialog], activity is destroyed" }
                return
            }
            if (compileDialog?.isShowing() == true) {
                return
            }
            val builder = ProgressDialog.Builder(this, true)
            builder.setTitle(R.string.videoeditor_editor_msg_processing)
            builder.setCancelable(false)
            builder.setPositiveButton(getResources().getString(android.R.string.cancel)) { _, _ ->
                DoubleClickUtils.updateLastClickTime()
                editorEngine.forceStopPlayer()
            }
            compileDialog = builder.build().show()
        }
    }

    private fun onExportCompleted() {
        WallpaperHelper.curFile?.let {
            val uri = GalleryFileProvider.fromFile(mActivity.applicationContext, it)

            if (compileDialog?.isShowing() == true) {
                compileDialog?.dismiss()
            }

            if (WallPaperParam.curInstance?.isNeedToSetResult() == true) {
                Intent().apply {
                    data = uri
                    flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
                    putExtra(KEY_IS_VIDEO_WALLPAPER, true)
                    mActivity.setResult(RESULT_OK, this)
                }
                mActivity.finish()
            } else {
                startWallpaperPageByIntent(uri)
            }
        }
    }

    private fun startWallpaperPageByIntent(uri: Uri) {
        GLog.d(TAG, LogFlag.DL) { "[startWallpaperPageByIntent] uri=$uri" }
        try {
            Intent(ACTION_SET_VIDEO_WALLPAPER).apply {
                setData(uri)
                putExtra(KEY_IS_VIDEO_WALLPAPER, true)
                WallpaperHelper.grantUriPermission(context as Activity, uri)
                mActivity.startActivityForResult(this, SET_VIDEO_WALLPAPER_REQUEST_CODE)
            }
        } catch (e: ActivityNotFoundException) {
            GLog.e(TAG, LogFlag.DL, "[startWallpaperPageByIntent]", e)
        }
    }

    companion object {
        private const val TAG = "EditorWallpaperTrimUIController"
        private const val PIXEL_OFFSET = 0.5
        private const val MILLIS_TIME_BASE = 1000
        private const val VIDEO_PLAY_END_CHECK_GAP = 100
        private const val STATUS_CHANGE_REFRESH_DELAY = 50
        private val authority = "${ContextGetter.context.packageName}.FileProvider"
    }
}