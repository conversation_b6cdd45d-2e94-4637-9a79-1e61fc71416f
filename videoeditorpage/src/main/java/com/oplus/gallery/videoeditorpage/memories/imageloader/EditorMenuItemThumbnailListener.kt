/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EditorMenuItemThumbnailListener.kt
 * Description:
 * Version: 1.0
 * Date: 2021/5/10
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>   2021/5/10          1.0         OPLUS_FEATURE_APP_LOG_MONITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.imageloader

import com.oplus.gallery.basebiz.widget.EditorMenuItemView
import java.lang.ref.WeakReference

class EditorMenuItemThumbnailListener(imageView: EditorMenuItemView) : ThumbnailListener {

    private val mItemViewWr: WeakReference<EditorMenuItemView> = WeakReference(imageView)

    override fun updateThumbnail(thumbnailInfo: ThumbnailRespond<*>?) {
        val itemView = mItemViewWr.get() ?: return
        if (thumbnailInfo == null) {
            return
        }
        val tag = itemView.tag
        if ((tag != null) && (tag != thumbnailInfo.item)) {
            return
        }
        itemView.setImageBitmap(thumbnailInfo.result)
    }

    val target: EditorMenuItemView?
        get() = mItemViewWr.get()
}
