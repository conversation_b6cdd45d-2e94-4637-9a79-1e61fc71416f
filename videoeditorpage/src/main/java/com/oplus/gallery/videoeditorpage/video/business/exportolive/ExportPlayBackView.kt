/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PreviewPlayBack.kt
 ** Description: 视频导出实况片段预览播放控件
 ** Version: 1.0
 ** Date: 2025/2/28
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/2/28        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.exportolive

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.oplus.gallery.videoeditorpage.R

/**
 * 视频导出实况片段预览播放控件
 */
class ExportPlayBackView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    private val playLayout: LinearLayout
    private val playIcon: ImageView
    private val playText: TextView

    init {
        val playBackView = LayoutInflater.from(context).inflate(R.layout.videoeditor_button_export_playback_layout, this, true)

        playLayout = playBackView.findViewById<LinearLayout>(R.id.play_layout)
        playIcon = playBackView.findViewById<ImageView>(R.id.play_icon)
        playText = playBackView.findViewById<TextView>(R.id.play_text)

        context.obtainStyledAttributes(attrs, R.styleable.ExportPlayBackView).apply {
            playIcon.setImageResource(
                getResourceId(
                    R.styleable.ExportPlayBackView_playIcon,
                    R.drawable.videoeditor_button_export_playback_icon
                )
            )
            playText.text = getString(R.styleable.ExportPlayBackView_playText)
            recycle()
        }
    }

    /**
     *  重写该方法，实现子view的enable
     *  @param enabled 是否可用
     */
    override fun setEnabled(enabled: Boolean) {
        playLayout.isEnabled = enabled
        playIcon.isEnabled = enabled
        playText.isEnabled = enabled
    }
}