/********************************************************************************
 ** Copyright (C), 2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IpuFilterManager
 ** Description: ipu滤镜管理类
 ** Version: 1.0
 ** Date : 2025/4/1
 ** Author: kangshuwen
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** ka<PERSON><PERSON><PERSON>                      2025/4/1       1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.manager.filter;



import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Pair;

import com.oplus.ocs.camera.ipuapi.IPUClient;
import com.oplus.ocs.camera.ipuapi.process.ProcessConfig;
import com.oplus.ocs.camera.ipuapi.process.filter.FilterProcessUnit;
import com.oplus.ocs.camera.ipuapi.process.filter.entity.FilterInitParameter;
import com.oplus.ocs.camera.ipuapi.process.filter.entity.FilterProcessParameter;
import com.oplus.ocs.camera.ipuapi.process.filter.key.FilterProcessKey;
import com.oplus.ocs.camera.ipuapi.process.filter.result.FilterProcessResult;

/**
 * ipu滤镜管理类
 */
public class IpuFilterManager {
    private final static String TAG = "IpuFilterManager";
    /**
     * 默认缩放倍数
     */
    private static final float DEFAULT_SCALE_SIZE = 1.0f;

    /**
     * 原图滤镜对应的类型，用于标识原图滤镜的类型，不参与滤镜处理过程。
     */
    private static final String FILTER_TYPE_ORIGIN = "filter.type.origin";
    private final Context mContext;
    private ProcessConfig mProcessConfig = null;
    private FilterProcessUnit mFilterProcessUnit = null;

    public IpuFilterManager(Context context) {
        mContext = context;
        mProcessConfig = getProcessConfig();
    }

    public void initFilterProcessUnit() {
        mFilterProcessUnit = IPUClient.createFeature(mContext, FilterProcessUnit.class);
        FilterInitParameter parameter = getFilterInitParam(FilterProcessParameter
                .ProcessType.PREVIEW);
        mFilterProcessUnit.init(parameter, null);
    }

    /**
     * 获取ipu滤镜列表数据
     *
     * @return 滤镜列表
     */
    public Pair<List<HashMap<String, Object>>, Boolean> getIpuFilterItems() {
        FilterProcessUnit processUnit = IPUClient.createFeature(mContext, FilterProcessUnit.class);
        FilterInitParameter itemParameter = getFilterInitParam(FilterProcessParameter
                .ProcessType.GET_ALL_FILTERS);
        processUnit.init(itemParameter, null);

        FilterProcessParameter parameter = new FilterProcessParameter();
        parameter.setProcessType(FilterProcessParameter.ProcessType.GET_ALL_FILTERS);
        FilterProcessResult result = processUnit.processImage(parameter, mProcessConfig);
        List<HashMap<String, Object>> filterItems = result.getFilterItems();
        // 判断滤镜时，是否支持hdr的处理。即支持处理F16格式的图（这个需要美射sdk支持。IPU-SDK版本号更新为：4.101.67 以上包含接口）
        boolean supportHdr = processUnit.isFunctionSupported(FilterProcessUnit.Functions.HDR);
        processUnit.uninit();

        return new Pair<>(filterItems, supportHdr);
    }

    /**
     * 获取滤镜缩略图
     *
     * @param filterTypes 滤镜类型类别列表
     * @param bitmap      输入的bitmap
     * @return 返回滤镜缩略图
     */
    public List<Bitmap> getIpuFilterBitmaps(List<String> filterTypes, Bitmap bitmap) {
        FilterProcessUnit processUnit = IPUClient.createFeature(mContext, FilterProcessUnit.class);
        FilterInitParameter parameter = getFilterInitParam(FilterProcessParameter
                .ProcessType.CAPTURE);
        processUnit.init(parameter, bitmap);
        List<Bitmap> bitmapList = null;
        ArrayList<HashMap<String, Object>> drawItem = new ArrayList<>();
        FilterProcessParameter param = new FilterProcessParameter();
        for (int i = 0; i < filterTypes.size(); i++) {
            HashMap<String, Object> currentMap = new HashMap<>();
            currentMap.put(FilterProcessKey.DRAWING_ITEM_KEY_FILTER_TYPE, filterTypes.get(i));
            drawItem.add(currentMap);
            param.setDrawingItem(drawItem);
            param.setInputBitmap(bitmap);
            param.setOutputBitmapConfig(Bitmap.Config.ARGB_8888);
            param.setProcessType(FilterProcessParameter.ProcessType.PROCESS_BITMAP);
        }
        FilterProcessResult result = processUnit.processImage(param, mProcessConfig);
        bitmapList = result.getResultBitmaps();
        processUnit.uninit();

        return bitmapList;
    }

    public void release() {
        if (mFilterProcessUnit != null) {
            mFilterProcessUnit.uninit();
            mFilterProcessUnit = null;
        }
        mProcessConfig = null;
    }

    private FilterInitParameter getFilterInitParam(int processType) {
        FilterInitParameter builder = new FilterInitParameter();
        builder.setProcessType(processType);
        return builder;
    }

    private ProcessConfig getProcessConfig() {
        ProcessConfig config = new ProcessConfig();
        config.setRenderMode(ProcessConfig.RenderMode.SAVE);
        config.setFormat(ProcessConfig.Format.BITMAP);
        config.setScaleSize(DEFAULT_SCALE_SIZE);
        return config;
    }
}
