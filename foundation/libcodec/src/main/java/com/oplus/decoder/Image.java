/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ImageProcess.java
 ** Description:
 ** Version: 1.0
 ** Date : 2018/01/30
 ** Author: kexiuhua@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                 <data>     <version>  <desc>
 **  kexiuhua@Apps.Gallery3D  2018/01/30        build this module
 ****************************************************************/

package com.oplus.decoder;

import android.graphics.Bitmap;

import com.oplus.gallery.standard_lib.util.loadLibrary.LoadLibraryTool;

/**
 * 注意：该类不能随意修改包名。因为存在于native层的交互，一旦修改包名会导致native逻辑异常，从而导致解码异常。
 */
public class Image {
    static {
        LoadLibraryTool.loadLibrary("oplus_gifdecoder");
    }

    /**
     * Bitmap MUST be ARGB_8888
     *
     * @param bitmap   config MUST be ARGB_8888
     * @param filePath file path
     * @return true save succeed
     */
    public static native boolean saveBitmapAsStream(Bitmap bitmap, String filePath);

    /**
     * Bitmap MUST be ARGB_8888
     *
     * @param bitmap   config MUST be ARGB_8888
     * @param filePath file path
     * @return true read succeed
     */
    public static native boolean readBitmapFromStream(Bitmap bitmap, String filePath);

    /**
     * 保存bitmap为bmp文件
     *
     * @param bitmap   bitmap
     * @param filePath 文件路径
     * @return 是否成功
     */
    public static native boolean saveBitmapAsBmp(Bitmap bitmap, String filePath);
}
