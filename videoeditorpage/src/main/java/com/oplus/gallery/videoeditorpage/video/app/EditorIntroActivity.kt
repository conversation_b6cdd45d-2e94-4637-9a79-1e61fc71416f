/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorIntroActivity.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/7/25
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/7/25        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.app

import android.app.ComponentCaller
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.VIDEO_EDITOR_INTRO_ACTIVITY
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.START_TIME_MILLIS
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode.VIDEO_EDIT
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.DIRECT_EXIT_FORMAT_NOT_SUPPORT
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.EXCEPTION_DIRECT_EXIT
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.router_lib.utils.Constants.INTENT_EXTRA_REQUEST_CODE
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper
import com.oplus.gallery.videoeditorpage.utlis.IntentDataHolder
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.picker.PickerHelper
import com.oplus.gallery.videoeditorpage.video.invoker.InvokerManager
import com.oplus.gallery.videoeditorpage.widget.ImportDialogListener
import com.oplus.gallery.videoeditorpage.widget.ImportVideoDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 视频编辑导入弹窗页面，负责初始化引擎、创建时间线
 */
@RouterNormal(path = VIDEO_EDITOR_INTRO_ACTIVITY)
class EditorIntroActivity : BaseActivity() {
    private var invokerManager: InvokerManager? = null

    private val importingDialog: ImportVideoDialog by lazy {
        ImportVideoDialog(this).apply { importDialogListener = importingListener }
    }

    private val importingListener = object : ImportDialogListener {
        override fun onCancelClick() = finish()

        override fun onFinished() {
            Starter.ActivityStarter(
                startContext = getStartContext(),
                postCard = PostCard(RouterConstants.RouterName.VIDEO_EDITOR_ACTIVITY),
                requestCode = intent.getIntExtra(INTENT_EXTRA_REQUEST_CODE, VIDEO_EDIT),
                onIntentCreated = fun(outIntent: Intent) {
                    intent.extras?.let { outIntent.putExtras(it) }
                },
            ).start()
            // 当外部应用跳转时，需要接受editorActivity的result，此时不能finish
            if (invokerManager == null || invokerManager?.isFromOuterApp() == false) {
                finish()
            }
        }
    }

    private val editorEngine: EditorEngine by lazy {
        EditorEngineGlobalContext.getInstance().createEditorEngine().apply {
            maxCountText = getString(R.string.videoeditor_import_over_max_count)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setScreenOrientation()
        overridePendingTransition(0, 0)
        importingDialog.showIfNeed(true, IntentUtils.getLongExtra(intent, START_TIME_MILLIS, 0))
        val isSuccess = editorEngine.init(applicationContext)
        if (isSuccess.not()) {
            ToastUtil.showShortToast(R.string.videoeditor_start_senior_editor_failed)
            VideoEditorTrackerHelper.appendExceptionData(EXCEPTION_DIRECT_EXIT, DIRECT_EXIT_FORMAT_NOT_SUPPORT)
            return releaseAndFinish()
        }
        editorEngine.initVideoFilterManager()
        invokerManager = InvokerManager(this@EditorIntroActivity, editorEngine,
            IntentUtils.getStringExtra(intent, EditorActivity.KEY_PHOTO_EDITOR_INVOKER))
        lifecycleScope.launch(Dispatchers.IO) {
            handleTimelineCreation()
        }
    }

    /**
     * 当外部应用跳转时，将editor activity的结果返回，在此处 finish 页面
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?, caller: ComponentCaller) {
        super.onActivityResult(requestCode, resultCode, data, caller)
        GLog.d(TAG, LogFlag.DL) { "[onActivityResult] requestCode:$requestCode, resultCode:$resultCode" }
        if (invokerManager?.isFromOuterApp() == true) {
            setResult(RESULT_OK, data)
            finish()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        setScreenOrientation()
    }

    private suspend fun handleTimelineCreation() {
        val timeline = createTimeline()
        withContext(Dispatchers.Main) {
            if (timeline == null) {
                GLog.e(TAG, LogFlag.DL) { "[handleTimelineCreation] create timeline failed!" }
                return@withContext releaseAndFinish()
            }
            IntentDataHolder.getInstance().setBigData(IntentDataHolder.DATA_TIMELINE, timeline)
            checkMediaAvailability()
        }
    }

    private fun checkMediaAvailability() {
        if (PickerHelper.pickerList.isEmpty()) {
            ToastUtil.showShortToast(R.string.videoeditor_notif_not_supported)
            VideoEditorTrackerHelper.appendExceptionData(EXCEPTION_DIRECT_EXIT, DIRECT_EXIT_FORMAT_NOT_SUPPORT)
            return releaseAndFinish()
        }
        importingDialog.unSupportCount = PickerHelper.unsupportedMediaCount
        importingDialog.finishIfNeed()
    }

    private fun createTimeline(): ITimeline? {
        VideoParser.getInstance().editorEngine = editorEngine
        // PickerHelper中数据集，需要再这里先解析出来，才能触发后续检查
        val timelineIntent = invokerManager?.parseIntent(intent) ?: intent
        return TimelineGenerator(applicationContext).generate(timelineIntent, null)
    }

    private fun releaseAndFinish() {
        VideoParser.getInstance().release()
        editorEngine.unInit(applicationContext)
        IntentDataHolder.getInstance().getBigDataAndRemove(IntentDataHolder.DATA_TIMELINE, ITimeline::class.java)
        importingDialog.destroy()
        EditorEngineGlobalContext.releaseInstance()
        finish()
    }

    private fun setScreenOrientation() {
        requestedOrientation = if ((getCurrentAppUiConfig().orientation.current == Configuration.ORIENTATION_LANDSCAPE)
            && (getCurrentAppUiConfig().windowHeightDp.current <= WindowSizeForm.STANDARD_LANDSCAPE_WINDOW_HEIGHT)
        ) {
            // 如果进视频编辑之前是直板机横屏，则设置成竖屏
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            // 小屏不能旋转屏幕
            if (isMiddleAndLargeWindow(this)) ActivityInfo.SCREEN_ORIENTATION_SENSOR else ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        importingDialog.destroy()
        finish()
    }

    /**
     * 在由外部传输视频uri跳转到相册编辑时，getActivityStarterContext这个时候返回的是selection fragment的context，
     * 这个fragment此时没有被添加到activity上，所以无法作为启动下一个activity的context
     */
    private fun getStartContext(): Any {
        return if (invokerManager?.isFromOuterApp() == true) {
            this@EditorIntroActivity
        } else {
            Starter.getActivityStarterContext() ?: this@EditorIntroActivity
        }
    }

    private companion object {
        const val TAG = "EditorIntroActivity"
    }
}