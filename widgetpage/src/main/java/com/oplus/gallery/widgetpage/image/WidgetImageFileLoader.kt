/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetImageFileLoader.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/15        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.image

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.Rect
import android.net.Uri
import com.oplus.gallery.basebiz.task.ThumbnailJob
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.security.Md5Utils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrNull
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.scheduler.FutureListener
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import java.lang.StringBuilder
import java.util.Hashtable
import java.util.concurrent.Future

object WidgetImageFileLoader {
    private const val TAG = "WidgetImageFileLoader"
    private const val CACHE_FILE_KEY_SPLITTER = "-"

    private val callbackListMap = Hashtable<String, MutableList<LoadCallback>>()

    /**
     * 从文件路径加载卡片图片，获取智能裁剪的共享文件，在回调中返回文件Uri
     * 强制智能裁剪的处理流程优先顺序： 智能裁剪的缓存文件 -> 智能裁剪并保存新文件
     * 不强制裁剪的处理流程优先顺序：  智能裁剪的缓存文件 -> 未智能裁剪的缓存文件 -> 保存未智能裁剪的新文件
     * @param context Context
     * @param session WorkerSession
     * @param filePath 图片文件路径
     * @param targetWidth 目标宽，图片宽高大于目标宽高时会进行缩小
     * @param targetHeight 目标高，图片宽高大于目标宽高时会进行缩小
     * @param forceAutoCrop 是否强制智能裁剪，非强制裁剪则只尝试获取智能裁剪缓存而不主动进行智能裁剪
     * @param callback Uri回调，预加载等情况下为null（只保存文件但不立即使用）
     */
    fun loadImageFile(
        context: Context,
        session: WorkerSession,
        filePath: String,
        targetWidth: Int,
        targetHeight: Int,
        forceAutoCrop: Boolean,
        callback: LoadCallback? = null
    ) {
        val path = LocalMediaDataHelper.getPathByFilePath(filePath)
        // 重新查询MediaItem，否则旋转等操作后缓存的MediaItem可能还没更新属性
        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(path, true)
        mediaItem ?: let {
            GLog.w(TAG, LogFlag.DL, "loadImageFile: no media item, data=${PathMask.mask(filePath)} path=$path")
            return
        }
        val autoCropKey = makeKey(
            mediaItem,
            targetWidth,
            targetHeight,
            autoCrop = true
        )
        GLog.d(TAG, LogFlag.DL, "loadImageFile: mediaItem=$mediaItem forceAutoCrop=$forceAutoCrop autoCropKey=$autoCropKey")
        WidgetImageFileCacheService.cleanCache(context)
        getUriFromCache(context, autoCropKey)?.run {
            GLog.d(TAG, LogFlag.DL, "loadImageFile: file exists")
            callback?.onFileLoaded(this)
            return
        }
        GLog.d(TAG, LogFlag.DL, "loadImageFile: file not exists")
        if (forceAutoCrop) {
            SaveImageParam(mediaItem, targetWidth, targetHeight, callback, autoCropKey, needAutoCrop = true).run {
                loadImageWhenNoCache(context, session, this)
            }
            return
        }
        val notAutoCropKey = makeKey(
            mediaItem,
            targetWidth,
            targetHeight,
            autoCrop = false
        )
        GLog.d(TAG, LogFlag.DL, "loadImageFile: mediaItem=$mediaItem notAutoCropKey=$notAutoCropKey")
        getUriFromCache(context, notAutoCropKey)?.run {
            GLog.d(TAG, LogFlag.DL, "loadImageFile: not-auto-crop file exists")
            callback?.onFileLoaded(this)
            return
        }
        GLog.d(TAG, LogFlag.DL, "loadImageFile: not-auto-crop file not exists")
        SaveImageParam(mediaItem, targetWidth, targetHeight, callback, autoCropKey, needAutoCrop = false).run {
            loadImageWhenNoCache(context, session, this)
        }
    }

    private fun getUriFromCache(
        context: Context,
        key: String
    ): Uri? = WidgetImageFileCacheService.getCache(context, key)

    /**
     * 没有缓存文件，需要生成，但有可能正在生成
     * 发起一个加载任务，或者等待正在执行的相同key的加载任务（如有）加载完成后一起回调
     */
    private fun loadImageWhenNoCache(
        context: Context,
        session: WorkerSession,
        saveImageParam: SaveImageParam
    ) {
        val key = saveImageParam.key
        var taskExists = true
        callbackListMap.computeIfAbsent(key) {
            taskExists = false
            mutableListOf()
        }.run {
            saveImageParam.callback?.let(this::add)
        }
        GLog.d(TAG, LogFlag.DL, "loadImageWhenNoCache: taskExists=$taskExists")
        if (taskExists.not()) {
            session.submit(
                object : Job<Uri?> {
                    override fun call(jc: JobContext): Uri? {
                        return saveImage(context, jc, saveImageParam)
                    }
                },
                object : FutureListener<Uri?> {
                    override fun onFutureDone(future: Future<Uri?>) {
                        future.getOrNull()?.let { uri ->
                            callbackListMap.remove(key)?.forEach {
                                it.onFileLoaded(uri)
                            }
                        } ?: run {
                            GLog.w(TAG, LogFlag.DL, "<loadImageWhenNoCache> uri is null, need remove callback!")
                            callbackListMap.remove(key)
                        }
                    }
                }
            )
        }
    }

    private fun saveImage(
        context: Context,
        jc: JobContext,
        saveImageParam: SaveImageParam
    ): Uri? {
        val mediaItem = saveImageParam.mediaItem
        ThumbnailJob(
            context,
            mediaItem,
            ResourceGetOptions(inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey())
        ).call(jc)?.let {
            val rotatedBitmap = BitmapUtils.rotateBitmap(it, mediaItem.rotation, true)
            GLog.d(TAG, LogFlag.DL, "saveImage: rotatedBitmap=${rotatedBitmap.width},${rotatedBitmap.height}")
            val targetWidth = saveImageParam.targetWidth
            val targetHeight = saveImageParam.targetHeight
            var cropRect: Rect? = null
            if (saveImageParam.needAutoCrop) {
                cropRect = ApiDmManager.getScanDM().getCropRects(
                    context,
                    rotatedBitmap,
                    mediaItem.filePath,
                    floatArrayOf(targetWidth.toFloat() / targetHeight.toFloat())
                ).firstOrNull()
                GLog.d(TAG, LogFlag.DL, "saveImage: cropRect=$cropRect")
            }
            val finalBitmap = if ((cropRect != null) && (cropRect.width() > 0) && (cropRect.height() > 0)) {
                cropAndScaleBitmap(
                    rotatedBitmap,
                    cropRect,
                    targetWidth,
                    targetHeight
                )
            } else {
                scaleBitmap(rotatedBitmap, targetWidth, targetHeight)
            }
            if (finalBitmap != rotatedBitmap) {
                rotatedBitmap.recycle()
            }
            val uri = WidgetImageFileCacheService.putCache(context, saveImageParam.key, finalBitmap)
            finalBitmap.recycle()
            return uri
        } ?: let {
            GLog.d(TAG, LogFlag.DL, "saveImage: failed to load thumb")
        }
        return null
    }

    private fun cropAndScaleBitmap(
        originBitmap: Bitmap,
        cropRect: Rect,
        targetWidth: Int,
        targetHeight: Int
    ): Bitmap {
        val scale = computeScale(
            originWidth = cropRect.width(),
            originHeight = cropRect.height(),
            targetWidth = targetWidth,
            targetHeight = targetHeight
        )
        val matrix = if (scale < 1) {
            Matrix().apply {
                setScale(scale, scale)
            }
        } else null
        return Bitmap.createBitmap(
            originBitmap,
            cropRect.left,
            cropRect.top,
            cropRect.width(),
            cropRect.height(),
            matrix,
            false
        )
    }

    private fun scaleBitmap(
        originBitmap: Bitmap,
        targetWidth: Int,
        targetHeight: Int
    ): Bitmap {
        val scale = computeScale(
            originWidth = originBitmap.width,
            originHeight = originBitmap.height,
            targetWidth = targetWidth,
            targetHeight = targetHeight
        )
        return if (scale < 1) {
            BitmapUtils.resizeBitmapByScale(
                originBitmap,
                scale,
                true,
                false
            )
        } else originBitmap
    }

    private fun computeScale(
        originWidth: Int,
        originHeight: Int,
        targetWidth: Int,
        targetHeight: Int
    ): Float {
        return if ((originWidth.toFloat() / originHeight.toFloat()) >= (targetWidth.toFloat() / targetHeight.toFloat())) {
            targetHeight.toFloat() / originHeight.toFloat()
        } else {
            targetWidth.toFloat() / originWidth.toFloat()
        }
    }

    private fun makeKey(
        mediaItem: MediaItem,
        targetWidth: Int,
        targetHeight: Int,
        autoCrop: Boolean
    ): String {
        // 图片旋转之后dateModified不会马上变，所以需要把旋转角度也作为key，避免错用旋转前生成的文件
        return StringBuilder()
            .append(mediaItem.filePath).append(CACHE_FILE_KEY_SPLITTER)
            .append(mediaItem.fileSize).append(CACHE_FILE_KEY_SPLITTER)
            .append(mediaItem.dateModifiedInSec).append(CACHE_FILE_KEY_SPLITTER)
            .append(mediaItem.rotation).append(CACHE_FILE_KEY_SPLITTER)
            .append(targetWidth).append(CACHE_FILE_KEY_SPLITTER)
            .append(targetHeight).append(CACHE_FILE_KEY_SPLITTER)
            .append(autoCrop)
            .toString()
            .let {
                Md5Utils.getMd5(it)
            }
    }

    fun interface LoadCallback {
        fun onFileLoaded(uri: Uri)
    }

    private data class SaveImageParam(
        val mediaItem: MediaItem,
        val targetWidth: Int,
        val targetHeight: Int,
        val callback: LoadCallback?,
        val key: String,
        val needAutoCrop: Boolean
    )
}
