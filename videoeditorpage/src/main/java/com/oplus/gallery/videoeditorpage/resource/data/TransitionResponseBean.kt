/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransitionResponseBean
 ** Description:转场资源网络请求返回的数据载体
 ** Version: 1.0
 ** Date: 2025-07-09
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2025-07-09     1.0
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.data

import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.util.text.TextUtil

class TransitionResponseBean {

    /**
     * 唯一id
     */
    @SerializedName("transitionId")
    var id: String = TextUtil.EMPTY_STRING

    /**
     * 资源包请求地址
     */
    @SerializedName("transitionMaterialUrl")
    var resourceUrl: String? = null

    /**
     * 资源包MD5，用于校验文件完整性。
     */
    @SerializedName("transitionMaterialMd5")
    var resourceMd5: String? = null

    /**
     * 词条id。
     */
    @SerializedName("nameEntryId")
    var nameId: String? = null

    /**
     * 词条包请求地址
     */
    @SerializedName("nameEntryUrl")
    var nameUrl: String? = null

    /**
     * 缩图请求地址
     */
    @SerializedName("transitionThumbnailMaterialUrl")
    var iconUrl: String? = null

    /**
     * 缩图存放路径
     */
    @SerializedName("iconPath")
    var iconPath: String? = null

    /**
     * 文件路径。
     */
    @SerializedName("filePath")
    var filePath: String? = null

    /**
     * 大小，单位为字节。
     */
    @SerializedName("fileSize")
    var fileSize: Int = 0

    /**
     * 在列表中的位置。
     */
    @SerializedName("position")
    var position: Int = -1

    /**
     * 标识是否为预设资源
     */
    @SerializedName("isPreset")
    var isPreset = false

    /**
     * 所属分类
     */
    @SerializedName("category")
    var category: String? = null

    /**
     * 来源
     */
    @SerializedName("source")
    var source: String? = null

    /**
     * 最后更新时间
     */
    @SerializedName("updateTime")
    var updateTime: String? = null

    /**
     * 版本号，用于标识歌曲的更新状态
     */
    @SerializedName("version")
    var version: String = TextUtil.EMPTY_STRING

    override fun toString(): String {
        return "TransitionResponseBean(id=$id, resourceUrl=$resourceUrl, resourceMd5=$resourceMd5, nameId=$nameId, nameUrl=$nameUrl, " +
                "iconUri=$iconUrl, iconPath=$iconPath, filePath=$filePath, fileSize=$fileSize, position=$position, isPreset=$isPreset, " +
                "category=$category, source=$source, updateTime=$updateTime, version=$version)"
    }
}