/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorTimeCoverWithTailAdapter.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.engine.ui.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;

import java.util.List;

public class EditorTimeCoverWithTailAdapter extends EditorTimeCoverAdapter {
    private static final int LAST_POSITION_SPACE = 3;
    private static final int SCROLL_OFFSET_VALUE = 5;
    private OnItemScrollListener mOnItemScrollListener;
    private String mTimelineViewState;

    public EditorTimeCoverWithTailAdapter(Context context, int leftPadding, int rightPadding, List<IVideoClip> clipList, double pixelPerMicrosecond) {
        super(context, leftPadding, rightPadding, clipList, pixelPerMicrosecond);
        mItemLengthList.add(1);
    }

    public void onItemScrollListener(OnItemScrollListener listener) {
        this.mOnItemScrollListener = listener;
    }

    public void setTimelineViewState(String timelineViewState) {
        this.mTimelineViewState = timelineViewState;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return TYPE_HEAD;
        } else if (position == (getItemCount() - 1)) {
            return TYPE_FOOT;
        } else {
            return TYPE_MID;
        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        return super.onCreateViewHolder(viewGroup, viewType);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        super.onBindViewHolder(holder, position);
    }

    @Override
    protected int getLastClipPosition() {
        return getItemCount() - LAST_POSITION_SPACE;
    }

    public interface OnItemScrollListener {
        void onItemScroll(int scrollX);
    }
}
