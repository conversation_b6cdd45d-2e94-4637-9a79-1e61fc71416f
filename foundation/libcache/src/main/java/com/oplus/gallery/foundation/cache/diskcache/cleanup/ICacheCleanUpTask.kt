/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ICacheCleanUpTask.kt
 ** Description : 对磁盘缓存临时文件进行清理的任务接口
 ** Version     : 1.0
 ** Date        : 2023/07/09
 ** Author      : <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2023/07/09      1.0
 *********************************************************************************/
package com.oplus.gallery.foundation.cache.diskcache.cleanup

import androidx.annotation.WorkerThread
import com.oplus.gallery.standard_lib.file.File

/**
 * 缓存清理任务接口，需要实现[retrieveCacheFiles]方法获取待清理的缓存文件，通过[checkNeedCacheCleanUp]对每个文件
 * 进行check，符合条件的进行清理
 */
interface ICacheCleanUpTask : Runnable {
    /**
     * 获取待清理的缓存文件
     * @return 缓存文件
     */
    @WorkerThread
    fun retrieveCacheFiles(): List<File>

    /**
     * 对获取的缓存文件进行check，符合条件的返回true的需要进行清理
     * @param cacheFileToCheck 待检查的缓存文件
     * @return 是否需要清理该缓存文件
     */
    @WorkerThread
    fun checkNeedCacheCleanUp(cacheFileToCheck: File): Boolean

    /**
     * 执行缓存文件的清理
     * @param cacheFileToClean 确认需要进行清理的文件
     */
    @WorkerThread
    fun performCacheCleanUp(cacheFileToClean: File)
}