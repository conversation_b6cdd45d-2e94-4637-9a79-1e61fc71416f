/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : YuvUtils.kt
 ** Description : YuvUtils.kt.
 ** Version     : 1.0
 ** Date        : 2020/10/24
 ** Author      : Yong.Ding@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  Yong.Ding@Apps.Gallery3D  2020/10/02  1.0       YuvUtils
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec.yuv.common

import android.graphics.BitmapFactory
import android.graphics.Rect
import com.oplus.gallery.addon.media.HeifConverterWrapper
import com.oplus.gallery.addon.media.HeifDecodeFrameWrapper
import com.oplus.gallery.standard_lib.codec.yuv.decoder.YuvData
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.display.ColorModelManager.adjustColorSpaceIfNeed
import com.oplus.gallery.standard_lib.util.io.IOUtils
import java.io.FileDescriptor
import java.io.FileInputStream
import java.io.InputStream

object YuvUtils {
    private const val TAG = "YuvUtils"
    private const val IN_SAMPLE_SIZE_DEFAULT = 1
    private const val YUV_FORMAT = 1
    private const val BITMAP_FORMAT = 0

    @JvmStatic
    fun getInSampleSize(options: BitmapFactory.Options?): Int {
        var inSampleSize: Int = IN_SAMPLE_SIZE_DEFAULT
        options?.let {
            inSampleSize = it.inSampleSize
        }
        return inSampleSize
    }

    @JvmStatic
    fun getYuvData(decodedFrame: HeifDecodeFrameWrapper?, rect: Rect?, options: BitmapFactory.Options?, isDirectBuffer: Boolean): YuvData? {
        if (decodedFrame == null) {
            GLog.e(TAG, "getYuvData decodedFrame is null")
            return null
        }
        logDecodeFrame("getYuvData", decodedFrame, rect, options, isDirectBuffer)
        return YuvData(decodedFrame).apply {
            this.colorSpaceAdjuster = ::adjustColorSpaceIfNeed
        }
    }

    @JvmStatic
    fun getYuvData(decodedFrame: HeifDecodeFrameWrapper?): YuvData? {
        if (decodedFrame == null) {
            GLog.e(TAG, "getYuvData decodedFrame is null")
            return null
        }
        logDecodeFrame("getYuvData", decodedFrame)
        return YuvData(decodedFrame).apply {
            this.colorSpaceAdjuster = ::adjustColorSpaceIfNeed
        }
    }


    @JvmStatic
    fun getYuvFormat(fileDescriptor: FileDescriptor): Int {
        if (!FeatureUtils.isSupport10Bit) {
            return BITMAP_FORMAT
        }
        val converter = HeifConverterWrapper()
        converter.createDecoder()
        val time = System.currentTimeMillis()
        val format = converter.getFormat(fileDescriptor)
        converter.destroyDecoder()
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, "isYuvFormat fileDescriptor result = $format, cost time = ${(System.currentTimeMillis() - time)}")
        }
        return format
    }

    @JvmStatic
    fun getYuvFormat(inputStream: InputStream): Int {
        if (!FeatureUtils.isSupport10Bit) {
            return BITMAP_FORMAT
        }
        return getOriginalYuvFormat(inputStream)
    }

    /**
     * 图片原本是否支持10亿色，不考虑feature
     * @param inputStream 图片流
     * @return 返回是否支持10亿色
     */
    @JvmStatic
    fun getOriginalYuvFormat(inputStream: InputStream): Int {
        val converter = HeifConverterWrapper()
        converter.createDecoder()
        val time = System.currentTimeMillis()
        val format = converter.getFormat(inputStream)
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, "isYuvFormat inputStream result = $format ,  cost time = ${(System.currentTimeMillis() - time)}")
        }
        converter.destroyDecoder()
        return format
    }

    /**
     * 图片原本是否支持10亿色，不考虑feature
     * @param filePath 图片文件路径
     * @return 返回是否支持10亿色
     */
    @JvmStatic
    fun getOriginalYuvFormat(filePath: String): Int {
        var format = BITMAP_FORMAT
        kotlin.runCatching {
            FileInputStream(filePath).use {
                format = getOriginalYuvFormat(it)
            }
        }.onFailure {
            GLog.w(TAG) { "[getOriginalYuvFormat] get getYuvFormat fail, ${it.message}" }
        }
        return format
    }

    @JvmStatic
    fun getYuvFormat(filePath: String, isUserFd: Boolean): Int {
        var inputStream: FileInputStream? = null
        try {
            inputStream = FileInputStream(filePath)
            return if (isUserFd) {
                getYuvFormat(inputStream.fd)
            } else {
                getYuvFormat(inputStream)
            }
        } catch (e: Exception) {
            GLog.e(TAG, "isYuvFormat", e)
        } finally {
            IOUtils.closeQuietly(inputStream)
        }
        return 0
    }

    @JvmStatic
    private fun logDecodeFrame(
        tag: String,
        decodedFrame: HeifDecodeFrameWrapper,
        rect: Rect?,
        options: BitmapFactory.Options?,
        isDirectBuffer: Boolean
    ) {
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(
                TAG, tag
                        + ",decodedFrame.m_buffer_id = " + decodedFrame.bufferId
                        + ", decodedFrame.m_yuvdata.length = " + (if (decodedFrame.yuvData == null) 0 else decodedFrame.yuvData?.size)
                        + ", decodedFrame.m_frame_width = " + decodedFrame.frameWidth
                        + ", decodedFrame.m_frame_height = " + decodedFrame.frameHeight
                        + ", rect = " + rect.toString()
                        + ", options.inSampleSize = " + options?.inSampleSize
                        + ", isDirectBuffer = " + isDirectBuffer
            )
        }
    }

    @JvmStatic
    private fun logDecodeFrame(tag: String, decodedFrame: HeifDecodeFrameWrapper) {
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(
                TAG, tag
                        + ",d ecodedFrame.m_buffer_id = " + decodedFrame.bufferId
                        + ", decodedFrame.m_yuvdata.length = " + (if (decodedFrame.yuvData == null) 0 else decodedFrame.yuvData?.size)
                        + ", decodedFrame.m_frame_width = " + decodedFrame.frameWidth
                        + ", decodedFrame.m_frame_height = " + decodedFrame.frameHeight
            )
        }
    }
}