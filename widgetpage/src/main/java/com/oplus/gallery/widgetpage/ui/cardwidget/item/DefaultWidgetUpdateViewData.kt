/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - DefaultWidgetUpdateViewData.kt
 ** Description:默认样式等不显示照片的卡片样式
 ** Version: 1.0
 ** Date : 2021/11/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON>ir<PERSON>@Rom.Apps.Gallery    2021/11/26        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item

class DefaultWidgetUpdateViewData(
    layoutName: String,
    val widgetCode: String,
    val targetAction: String,
    val defaultImageResId: Int? = null
) : WidgetUpdateViewData(
    layoutName
)