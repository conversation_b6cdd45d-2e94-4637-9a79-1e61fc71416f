package com.oplus.gallery.videoeditorpage.memories.resource.manager;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.oplus.gallery.basebiz.permission.NetSendPrivacyPermissionRule;
import com.oplus.gallery.foundation.database.helper.InsertFileNameContentType;
import com.oplus.gallery.foundation.database.helper.PrivacyPersonalInfoTableHelper;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusResponseData;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.security.EncryptUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.resource.callback.DaoResultCallback;
import com.oplus.gallery.videoeditorpage.memories.resource.data.TemplateResponseBean;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.DownloadListener;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListener;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListenerProxy;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;
import com.oplus.gallery.videoeditorpage.resource.room.helper.ResourceDatabaseHelper;
import com.oplus.gallery.videoeditorpage.resource.room.helper.ThemeTableHelper;
import com.oplus.gallery.videoeditorpage.memories.util.AsyncDbCommand;
import com.oplus.gallery.videoeditorpage.memories.autodownload.ErrorCode;
import com.oplus.gallery.videoeditorpage.memories.autodownload.RtnCode;

import java.util.ArrayList;
import java.util.List;

import static com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_DOWNLOAD_FILE;
import static com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_DOWNLOAD_ICON;
import static com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_NOT_DOWNLOADED;
import static com.oplus.gallery.videoeditorpage.memories.autodownload.RtnCode.DOWNLOAD_ABILITY_NOT_EXIST;

public class ThemeSourceManager extends LocalSourceManager<ThemeItem,
        List<TemplateResponseBean.TemplateListBean>> {
    private static final String TAG = "Memories.ThemeSourceManager";
    public static final String ASSETS_THEME_CONFIG = "videoeditor_theme_config.json";
    public static final String VIDEO_THEME_SUFFIX = ".theme";
    public static final String VIDEO_LIC_SUFFIX = ".lic";
    public static final int VIDEO_THEME_SOURCE_FILE_NUM = 2;
    public static final int VIDEO_THEME_SUFFIX_LENGTH = 8;
    public static final String ACTION_THEME_DOWNLOAD_STATE = "com.oplus.gallery.videoeditorpage.themeDownloadState";
    public static final String KEY_TEMPLATE_LAST_REQUEST_TIME = "key_template_last_request_time";
    private static final String RESOURCE_TEMPLATE_DIRECTORY = "template";
    private static final String CATEGORY_MEMORY = "memory";

    private static volatile ThemeSourceManager sInstance;

    public static ThemeSourceManager getInstance() {
        if (sInstance == null) {
            synchronized (ThemeSourceManager.class) {
                if (sInstance == null) {
                    sInstance = new ThemeSourceManager();
                }
            }
        }
        return sInstance;
    }

    private ThemeSourceManager() {
        super(TYPE_UPDATE_BUILTIN | TYPE_UPDATE_DOWNLOAD,
                KEY_TEMPLATE_LAST_REQUEST_TIME, new ThemeTableHelper(), ACTION_THEME_DOWNLOAD_STATE);
    }

    public ThemeItem getThemeEntity(int resourceId) {
        return ((ThemeTableHelper) mTableHelper).getEntityByResourceId(resourceId);
    }

    public ThemeItem getThemeBySongId(int songId) {
        return ((ThemeTableHelper) mTableHelper).getItemBySongId(songId);
    }

    @Override
    public String requestNetworkResource(OnLoadingListener listener, boolean needDownloadIcon, boolean forceRequest) {
        GLog.d(TAG, "requestNetworkResource");
        OnLoadingListenerProxy listenerProxy = new OnLoadingListenerProxy(listener);
        if (!permitUpdateFromDownload()) {
            listenerProxy.onLoadingError(ErrorCode.Theme.NO_PERMISSION);
            return null;
        }
        if (!NetworkPermissionManager.isUseOpenNetwork()) {
            listenerProxy.onLoadingError(ErrorCode.Theme.NO_ALLOW_OPEN_NETWORK);
            return null;
        }
        if (!(forceRequest || !isInRequestInterval())) {
            GLog.d(TAG, "requestNetworkResource, at intervals");
            new AsyncDbCommand<List<ThemeItem>>() {
                @Override
                protected List<ThemeItem> doInBackground() {
                    return getResourceLists();
                }
            }.setResultCallback(new DaoResultCallback<List<ThemeItem>>() {
                @Override
                public void onResult(List<ThemeItem> entityList) {
                    listenerProxy.onLoadingFinish(RtnCode.Theme.AT_INTERVALS, entityList);
                }
            }).execute();

            return null;
        }
        String requestTag = null;
        IDownloadAbility downloadAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(IDownloadAbility.class);
        if (downloadAbility == null) {
            listenerProxy.onLoadingError(DOWNLOAD_ABILITY_NOT_EXIST);
            return null;
        }
        requestTag = downloadAbility.enqueue(ContextGetter.context,
                new GetThemeRequest(ContextGetter.context, new NetSendPrivacyPermissionRule()),
                new AppResultCallback<OplusResponseData<TemplateResponseBean>>() {
                    @Override
                    public void onSuccess(OplusResponseData<TemplateResponseBean> responseBean) {
                        GLog.d(TAG, "onSuccess, requestNetworkResource ");
                        if ((responseBean == null) || (responseBean.getData() == null)) {
                            GLog.e(TAG, "onSuccess, OplusResponseData is null");
                            listenerProxy.onLoadingError(ErrorCode.Theme.RESPONSE_DATA_ERROR);
                            return;
                        }
                        List<TemplateResponseBean.TemplateListBean> templateList = responseBean.getData().getTemplateList();
                        if (templateList == null) {
                            GLog.e(TAG, "onSuccess, templateList is null");
                            listenerProxy.onLoadingError(ErrorCode.Theme.RESPONSE_DATA_ERROR);
                            return;
                        }
                        // 获取主题列表，提供回忆编辑使用:保存主题名称
                        templateList.forEach(templateListBean -> PrivacyPersonalInfoTableHelper.INSTANCE.
                                insertPersonalInfoCollectionFileNameContentInIO(templateListBean.getTemplateName(), templateListBean.getEnName(),
                                        InsertFileNameContentType.THEME_SOURCE));
                        GLog.d(TAG, "onSuccess, templateList.size=" + templateList.size());
                        int counter = mTableHelper.getAllBuiltinSize();
                        if (counter == 0) {
                            GLog.d(TAG, "onSuccess, checkBuiltin maybe not yet called");
                        }
                        int newSize = counter + templateList.size();
                        int oldSize = mTableHelper.getAllResourceSize();
                        GLog.d(TAG, "onSuccess, newSize = " + newSize + " , oldSize = " + oldSize);

                        List<ThemeItem> templateEntityList = localizeData(templateList, counter);
                        GLog.d(TAG, "onSuccess, templateEntityList = " + templateEntityList);

                        if ((templateEntityList == null) || (templateEntityList.size() == 0)) {
                            GLog.d(TAG, "onSuccess, There is no need to update.");
                            if (oldSize > newSize) {
                                removeInvalidEntity(null, newSize);

                            } else {
                                GLog.d(TAG, "onSuccess, And size is no change.");
                            }
                            updateRequestInterval();
                            listenerProxy.onLoadingFinish(RtnCode.Theme.SUCCESS, getResourceLists());
                            if (needDownloadIcon) {
                                checkIcon(listener);
                            }
                            return;
                        }

                        List<ThemeItem> coveredEntityList = getCoveredEntity(templateEntityList, oldSize);
                        GLog.d(TAG, "onSuccess, coveredEntityList = " + coveredEntityList);

                        List ret = mTableHelper.insertAll(templateEntityList);
                        if (ret == null) {
                            GLog.e(TAG, "onSuccess, requestNetworkResource database update failed!");
                            listenerProxy.onLoadingError(ErrorCode.Theme.DATABASE_INSERT_ERROR);
                            return;
                        }
                        removeInvalidEntity(coveredEntityList, newSize);
                        updateRequestInterval();
                        listenerProxy.onLoadingFinish(RtnCode.Theme.SUCCESS, getResourceLists());

                        if (needDownloadIcon) {
                            checkIcon(listener);
                        }
                    }

                    @Override
                    public void onFailed(int code, String msg) {
                        GLog.e(TAG, "onFailed, code = " + code + " , msg = " + msg);
                        listenerProxy.onLoadingError(code);
                    }
                });
        downloadAbility.close();
        return requestTag;
    }

    @Override
    protected List<ThemeItem> localizeData(List<TemplateResponseBean.TemplateListBean> data, int builtinSize) {
        if (data == null) {
            GLog.e(TAG, "templateListBean is null");
            return null;
        }
        int counter = builtinSize;
        List<ThemeItem> entityList = new ArrayList<>();
        for (TemplateResponseBean.TemplateListBean templateBean : data) {
            GLog.d(TAG, "templateBean = " + templateBean);
            counter++;
            ThemeItem currentEntity = new ThemeItem();
            ThemeItem oldEntity =
                    ((ThemeTableHelper) mTableHelper).getEntityByResourceId(templateBean.getTemplateId());
            if (oldEntity != null) {
                GLog.d(TAG, "oldEntity = " + oldEntity);
                int oldDownloadState = oldEntity.getDownloadState();
                String oldUpdateTime = oldEntity.getUpdateTime();
                String newUpdateTime = templateBean.getUpdateTime();
                if (TextUtils.equals(newUpdateTime, oldUpdateTime)) {
                    if (counter == oldEntity.getPosition()) {
                        continue;
                    }
                    currentEntity.setThumbnailPath(oldEntity.getThumbnailPath());
                    currentEntity.setDownloadState(oldEntity.getDownloadState());
                    currentEntity.setSourcePath(oldEntity.getSourcePath());
                } else if (oldEntity.isDefaultIcon()) {
                    currentEntity.setThumbnailPath(oldEntity.getThumbnailPath());
                    currentEntity.setSourcePath(oldEntity.getSourcePath());
                    currentEntity.setDownloadState(oldDownloadState);
                } else {
                    int downloadState = TYPE_NOT_DOWNLOADED;
                    if (TextUtils.equals(oldEntity.getThumbnailUrl(), templateBean.getIconPath())
                            && !oldEntity.isNeedDownloadIcon()) {
                        currentEntity.setThumbnailPath(oldEntity.getThumbnailPath());
                        downloadState |= TYPE_DOWNLOAD_ICON;
                    }
                    if (TextUtils.equals(oldEntity.getZipUrl(), templateBean.getZipPath())
                            && !oldEntity.isNeedDownloadFile()) {
                        currentEntity.setSourcePath(oldEntity.getSourcePath());
                        downloadState |= TYPE_DOWNLOAD_FILE;
                    }
                    currentEntity.setDownloadState(downloadState);
                }
            } else {
                currentEntity.setDownloadState(TYPE_NOT_DOWNLOADED);
            }
            currentEntity.setPosition(counter);
            currentEntity.setThemeId(templateBean.getTemplateId());
            currentEntity.setZhName(templateBean.getTemplateName());
            currentEntity.setChName(templateBean.getChName());
            currentEntity.setEnName(templateBean.getEnName());
            currentEntity.setIsMemoryTheme(TextUtils.equals(templateBean.getCategory(), CATEGORY_MEMORY));
            currentEntity.setSongId(templateBean.getSongId());
            currentEntity.setThumbnailUrl(templateBean.getIconPath());
            currentEntity.setZipUrl(templateBean.getZipPath());
            currentEntity.setUpdateTime(templateBean.getUpdateTime());
            currentEntity.setIsBuiltin(false);
            GLog.d(TAG, "currentEntity = " + currentEntity);
            entityList.add(currentEntity);
        }
        return entityList;
    }

    @Override
    protected List<ThemeItem> getCoveredEntity(List<ThemeItem> nowEntityList, int oldSize) {
        if (nowEntityList == null) {
            return new ArrayList<>();
        }
        List<ThemeItem> coveredEntityList = new ArrayList<>();
        for (ThemeItem nowEntity : nowEntityList) {
            int currentPosition = nowEntity.getPosition();
            if (currentPosition <= oldSize) {
                ThemeItem coveredEntity = ((ThemeTableHelper) mTableHelper).getEntityByPosition(currentPosition);
                if ((coveredEntity != null) && (coveredEntity.getDownloadState() > TYPE_NOT_DOWNLOADED)) {
                    coveredEntityList.add(coveredEntity);
                }
            }
        }
        return coveredEntityList;
    }

    @Override
    protected List<ThemeItem> removeInvalidEntity(List<ThemeItem> coveredEntityList, int maxPosition) {
        List<ThemeItem> entityList = ((ThemeTableHelper) mTableHelper).getInvalidEntityList(maxPosition);
        ArrayList<ThemeItem> rtnList = new ArrayList<>();
        if (entityList != null) {
            for (ThemeItem entity : entityList) {
                FileOperationUtils.deleteFolderFile(entity.getThumbnailPath(), true);
                FileOperationUtils.deleteFolderFile(entity.getSourcePath(), true);
                rtnList.add(entity);
            }
            mTableHelper.deleteInvalidEntity(maxPosition);
        }

        if ((coveredEntityList == null) || (coveredEntityList.size() == 0)) {
            return rtnList;
        }
        for (ThemeItem coveredEntity : coveredEntityList) {
            ThemeItem nowEntity = ((ThemeTableHelper) mTableHelper)
                    .getEntityByResourceId(coveredEntity.getThemeId());
            GLog.d(TAG, "nowEntity = " + nowEntity);
            if (nowEntity == null) {
                FileOperationUtils.deleteFolderFile(coveredEntity.getThumbnailPath(), true);
                FileOperationUtils.deleteFolderFile(coveredEntity.getSourcePath(), true);
                rtnList.add(coveredEntity);
            }
        }
        return rtnList;
    }

    @Override
    public void checkIcon(OnLoadingListener listener) {
        GLog.d(TAG, "checkIcon begin");
        OnLoadingListenerProxy listenerProxy = new OnLoadingListenerProxy(listener);

        List<ThemeItem> noIconEntityList = ((ThemeTableHelper) mTableHelper).getNoIconEntityList();
        if (noIconEntityList == null) {
            GLog.e(TAG, "getNoIconEntityList failed!");
            listenerProxy.onLoadingError(ErrorCode.Theme.DATABASE_QUERY_ERROR);
            return;
        }
        if (noIconEntityList.size() == 0) {
            GLog.d(TAG, "there is no entity without icon!");
            return;
        }
        GLog.d(TAG, "noIconEntityList = " + noIconEntityList);
        for (ThemeItem entity : noIconEntityList) {
            final int resourceId = entity.getThemeId();
            downloadFile(entity, TYPE_DOWNLOAD_ICON, new DownloadListener() {

                @Override
                public void onProgress(int progress) {

                }

                @Override
                public void onFinish(int code, String destFilePath) {
                    ThemeItem templateEntity = ((ThemeTableHelper) mTableHelper)
                            .getEntityByResourceId(resourceId);
                    listenerProxy.onIconDownloadFinish(templateEntity);
                }

                @Override
                public void onError(int errCode) {
                    GLog.e(TAG, "downloadFile Icon errCode = " + errCode);
                    ThemeItem templateEntity = ((ThemeTableHelper) mTableHelper)
                            .getEntityByResourceId(resourceId);
                    listenerProxy.onIconDownloadError(errCode, templateEntity);
                }
            }, true);
        }
    }

    @Override
    public void retryDownload() {
        GLog.d(TAG, "retryDownload");
        retryDownloadFromMusic();
        List<Integer> idList = getNeedRetryItem();
        if (idList == null) {
            return;
        }
        List<Integer> themeIdList = new ArrayList<>(idList);
        for (int resourceId : themeIdList) {
            ThemeItem themeItem = getThemeEntity(resourceId);
            if ((themeItem == null) || (mAutoDownloadResourceId == resourceId)) {
                continue;
            }
            retryDownloadTheme(themeItem);
        }
    }

    private void retryDownloadFromMusic() {
        GLog.d(TAG, "retryDownloadFromMusic");
        List<Integer> idList = MusicSourceManager.getInstance().getNeedRetryItem();
        if (idList == null) {
            return;
        }
        List<Integer> musicIdList = new ArrayList<>(idList);
        for (int resourceId : musicIdList) {
            ThemeItem themeItem = getThemeBySongId(resourceId);
            if ((themeItem == null)
                    || (MusicSourceManager.getInstance().getAutoDownloadId() == resourceId)) {
                continue;
            }
            retryDownloadTheme(themeItem);
        }
    }

    @Override
    public String downloadFile(ThemeItem themeItem, final int type, DownloadListener listener, boolean filterContentType) {
        if (themeItem == null) {
            GLog.e(TAG, "musicEntity is null!");
            if (listener != null) {
                listener.onError(ErrorCode.Theme.ENTITY_IS_NULL);
            }
            return null;
        }
        final int resourceId = themeItem.getThemeId();
        final int downloadState =
                mTableHelper.getDownloadState(themeItem.getThemeId());
        if (downloadState < 0) {
            if (listener != null) {
                listener.onError(ErrorCode.Theme.ID_INVALID);
            }
            return null;
        }
        if (!themeItem.isNeedDownload(downloadState, type)) {
            String path =
                    (type == TYPE_DOWNLOAD_ICON) ? themeItem.getThumbnailPath() : themeItem.getSourcePath();
            if (listener != null) {
                listener.onFinish(RtnCode.Theme.ALREADY_DOWNLOADED, path);
            }
            return null;
        }
        String fileUrl;
        if (type == TYPE_DOWNLOAD_ICON) {
            fileUrl = themeItem.getThumbnailUrl();
        } else if (type == TYPE_DOWNLOAD_FILE) {
            fileUrl = themeItem.getZipUrl();
        } else {
            GLog.e(TAG, "Error downloadFile type = " + type);
            if (listener != null) {
                listener.onError(ErrorCode.Theme.PARAM_INVALID);
            }
            return null;
        }
        String fileName = EncryptUtils.encryptMD5ToString(fileUrl);
        if (type == TYPE_DOWNLOAD_FILE) {
            fileName = fileName + TEMP_ZIP_SUFFIX;
        }
        File file = new File(getDownloadPath(RESOURCE_TEMPLATE_DIRECTORY), fileName);

        return downloadNormal(fileUrl, file.getAbsolutePath(), new DownloadListener() {

            @Override
            public void onProgress(int progress) {
                if (listener != null) {
                    listener.onProgress(progress);
                }
            }

            @Override
            public void onFinish(int code, String destFilePath) {
                if (!FileOperationUtils.isFileExist(destFilePath)) {
                    GLog.e(TAG, "downloadNormal destFilePath is invalid!");
                    if (listener != null) {
                        listener.onError(ErrorCode.Theme.FILE_PATH_INVALID);
                    }
                    return;
                }
                if (type == TYPE_DOWNLOAD_ICON) {
                    if (!updateDownloadIconState(resourceId, destFilePath, listener)) {
                        return;
                    }
                } else if (type == TYPE_DOWNLOAD_FILE) {
                    if (!updateDownloadZipState(resourceId, destFilePath, listener)) {
                        return;
                    }
                }

                if (listener != null) {
                    listener.onFinish(code, destFilePath);
                }
            }

            @Override
            public void onError(int errCode) {
                GLog.e(TAG, "downloadNormal File errCode = " + errCode);
                if (listener != null) {
                    listener.onError(errCode);
                }
            }
        }, filterContentType);
    }

    private boolean updateDownloadIconState(int resourceId, String destFilePath, DownloadListener listener) {
        Boolean isUpdateSuccessful = ResourceDatabaseHelper.getInstance().runInTransaction(() -> {
            ThemeItem entity = ((ThemeTableHelper) mTableHelper).getEntityByResourceId(resourceId);
            if (entity == null) {
                if (listener != null) {
                    listener.onError(ErrorCode.Theme.ID_INVALID);
                }
                return false;
            }
            entity.setThumbnailPath(destFilePath);
            entity.updateDownloadState(TYPE_DOWNLOAD_ICON);
            int ret = mTableHelper.update(entity);
            if (ret < 0) {
                GLog.e(TAG, "updateDownloadIconState: downloadNormal update data failed!");
                if (listener != null) {
                    listener.onError(ErrorCode.Theme.DATABASE_UPDATE_ERROR);
                }
                return false;
            }
            GLog.d(TAG, "updateDownloadIconState update themeItem (" + entity + ")success!");
            return true;
        });
        if (isUpdateSuccessful != null) {
            return isUpdateSuccessful;
        } else {
            return false;
        }
    }

    private boolean updateDownloadZipState(int resourceId, String destFilePath, DownloadListener listener) {
        ThemeItem entity = ((ThemeTableHelper) mTableHelper).getEntityByResourceId(resourceId);
        if (entity == null) {
            if (listener != null) {
                listener.onError(ErrorCode.Theme.ID_INVALID);
            }
            return false;
        }
        File zipFile = new File(destFilePath);
        GLog.d(TAG, "updateDownloadZipState: ThemeItem = " + entity);
        String themeSourcePath = destFilePath.substring(0, destFilePath.length() - TEMP_ZIP_SUFFIX.length());
        try {
            FileOperationUtils.unzipFolder(destFilePath, themeSourcePath);
        } catch (Exception e) {
            if (listener != null) {
                listener.onError(ErrorCode.Theme.UNZIP_ERROR);
            }
            GLog.e(TAG, "updateDownloadZipState: downloadNormal finish, unZipFolder exception", e);
            return false;
        } finally {
            zipFile.delete();
        }

        // update theme item
        Boolean isUpdateSuccessful = ResourceDatabaseHelper.getInstance().runInTransaction(() -> {
            ThemeItem currentItem = ((ThemeTableHelper) mTableHelper).getEntityByResourceId(resourceId);
            if (currentItem == null) {
                if (listener != null) {
                    listener.onError(ErrorCode.Theme.ID_INVALID);
                }
                return false;
            }
            currentItem.setSourcePath(themeSourcePath);
            currentItem.updateDownloadState(TYPE_DOWNLOAD_FILE);
            int ret = mTableHelper.update(currentItem);
            if (ret < 0) {
                GLog.e(TAG, "updateDownloadZipState: downloadNormal update data failed!");
                if (listener != null) {
                    listener.onError(ErrorCode.Theme.DATABASE_UPDATE_ERROR);
                }
                return false;
            }
            GLog.d(TAG, "updateDownloadZipState success! currentItem = " + currentItem);
            return true;
        });
        if (isUpdateSuccessful != null) {
            return isUpdateSuccessful;
        } else {
            return false;
        }
    }

    @Override
    public List<ThemeItem> getResourceLists() {
        return ((ThemeTableHelper) mTableHelper).getAll();
    }

    public List<ThemeItem> queryAllMemoriesTheme() {
        return ((ThemeTableHelper) mTableHelper).queryAllMemoriesTheme();
    }

    public List<ThemeItem> queryAllVideoTheme() {
        return ((ThemeTableHelper) mTableHelper).queryAllVideoTheme();
    }

    public String downloadMemoriesTheme(ThemeItem themeItem, int source) {
        if (themeItem == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Theme.ENTITY_IS_NULL, -1);
            return null;
        }
        changeSource(themeItem.getThemeId(), source);
        MusicItem musicItem = MusicSourceManager.getInstance().getMusic(themeItem.getSongId());
        if (musicItem == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Theme.MUSIC_ENTITY_IS_NULL, themeItem.getThemeId());
            return null;
        }
        return MusicSourceManager.getInstance().downloadMusicByTheme(musicItem, source);
    }

    public String retryDownloadTheme(ThemeItem themeItem) {
        if (themeItem == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Theme.ENTITY_IS_NULL, ID_INVALID);
            return null;
        }
        if (themeItem.isMemoryTheme()) {
            return downloadMemoriesTheme(themeItem, DOWNLOAD_RETRY);
        } else {
            return downloadTheme(themeItem, DOWNLOAD_RETRY);
        }
    }

    public String manualDownloadTheme(ThemeItem themeItem) {
        return downloadTheme(themeItem, DOWNLOAD_MANUAL);
    }

    public String downloadThemeByMusicId(int resourceId) {
        ThemeItem themeItem = getThemeBySongId(resourceId);
        if (themeItem == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Theme.ENTITY_IS_NULL, ID_INVALID);
            return null;
        }
        return downloadTheme(themeItem, getSource(themeItem.getThemeId()));
    }

    private String downloadTheme(ThemeItem themeItem, int source) {
        if (themeItem == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Theme.ENTITY_IS_NULL, ID_INVALID);
            return null;
        }
        final int resourceId = themeItem.getThemeId();
        changeSource(resourceId, source);
        if ((source != DOWNLOAD_RETRY) && isDownloadingStateInQueue(resourceId)) {
            sendBroadcast(DOWNLOAD_STATE_DOWNLOADING, RtnCode.Theme.DOWNLOADING, resourceId);
            return null;
        }
        mProgressLock = false;
        sendDownloadProgress(resourceId, MIN_PROGRESS);
        return downloadFile(themeItem, TYPE_DOWNLOAD_FILE, new DownloadListener() {
            @Override
            public void onProgress(int progress) {
                sendDownloadProgress(resourceId, progress);
            }

            @Override
            public void onFinish(int code, String destFilePath) {
                GLog.d(TAG, "downloadTheme onFinish " + destFilePath);
                if (code == RtnCode.NETWORK_SUCCESS) {
                    code = RtnCode.Theme.SUCCESS;
                }
                sendBroadcast(DOWNLOAD_STATE_FINISH, code, resourceId);
            }

            @Override
            public void onError(int errCode) {
                GLog.d(TAG, "downloadZip onError errCode = " + errCode);
                sendBroadcast(DOWNLOAD_STATE_ERROR, errCode, resourceId);
            }
        }, true);
    }

    @Override
    public boolean checkBuiltinItem(boolean forceUpdate) {
        GLog.d(TAG, "[checkBuiltinItem]  " + forceUpdate + "; size : " + mTableHelper.getAllBuiltinSize());
        if (!permitUpdateFromBuiltin()) {
            GLog.d(TAG, "[checkBuiltinItem] permitUpdateFromBuiltin  " + true);
            return true;
        }
        if ((!forceUpdate) && (mTableHelper.getAllBuiltinSize() > 0)) {
            return true;
        }
        List<ThemeItem> items = getBuiltinItems();
        if (items == null) {
            GLog.d(TAG, "[checkBuiltinItem] items is null");
            return false;
        }
        if (items.size() > 0) {
            mTableHelper.clearBuiltin();
            List ret = mTableHelper.insertAll(items);
            resetRequestInterval();
            if (ret != null) {
                return true;
            }
            GLog.e(TAG, "checkBuiltinItem insertAll failed!");
            return false;
        }
        GLog.e(TAG, "checkBuiltinItem parseConfig failed!");
        return false;
    }

    public List<ThemeItem> queryEnableMemoriesTheme() {
        return ((ThemeTableHelper) mTableHelper).queryEnableMemoriesTheme();
    }

    private static List<ThemeItem> getBuiltinItems() {
        Context context = ContextGetter.context;
        if (context == null) {
            GLog.e(TAG, "context is null");
            return null;
        }
        List<ThemeItem> items = parseConfig(context, ASSETS_THEME_CONFIG,
                new TypeToken<List<ThemeItem>>() {
                }.getType());

        return items;
    }

    public int getMusicIdByThemeId(int id) {
        ThemeItem themeItem = getThemeEntity(id);
        if (themeItem == null) {
            return ID_INVALID;
        }
        MusicItem musicItem = MusicSourceManager.getInstance().getMusic(themeItem.getSongId());
        if (musicItem == null) {
            return ID_INVALID;
        }
        return musicItem.getMusicId();
    }

    public ThemeItem getThemeByPath(String path) {
        return ((ThemeTableHelper) mTableHelper).getThemeByPath(path);
    }

    public static String getVideoThemeFile(ThemeItem item) {
        String theme = "";
        if (item != null) {
            if (item.isBuiltin()) {
                theme = item.getSourcePath();
            } else {
                if (!TextUtils.isEmpty(item.getSourcePath())) {
                    File parentFile = new File(item.getSourcePath());
                    File[] files = parentFile.listFiles();
                    if (files == null || files.length < VIDEO_THEME_SOURCE_FILE_NUM) {
                        GLog.d(TAG, "getVideoThemeFileArray, source file is empty");
                    } else {
                        theme = files[0].getName().contains(VIDEO_THEME_SUFFIX) ?
                                files[0].getAbsolutePath() :
                                files[1].getAbsolutePath();
                    }
                }
            }
        }
        return theme;
    }

    public static String getVideoThemeLic(ThemeItem item) {
        String lic = "";
        if (item != null) {
            if (item.isBuiltin()) {
                if (!TextUtils.isEmpty(item.getSourcePath())) {
                    lic = item.getSourcePath().substring(0, item.getSourcePath().length() - VIDEO_THEME_SUFFIX_LENGTH) + VIDEO_LIC_SUFFIX;
                }
            } else {
                if (!TextUtils.isEmpty(item.getSourcePath())) {
                    File parentFile = new File(item.getSourcePath());
                    File[] files = parentFile.listFiles();
                    if ((files == null) || (files.length < VIDEO_THEME_SOURCE_FILE_NUM)) {
                        GLog.d(TAG, "getVideoThemeLicArray, source file is empty");
                        return null;
                    }
                    lic = files[0].getName().contains(VIDEO_LIC_SUFFIX) ?
                            files[0].getAbsolutePath() :
                            files[1].getAbsolutePath();
                }
            }
        }
        return lic;
    }

    public List<ThemeItem> queryIconExistedMemoriesTheme() {
        return ((ThemeTableHelper) mTableHelper).queryIconExistedMemoriesTheme();
    }

    public List<ThemeItem> queryNeedDownloadMemoriesTheme() {
        return ((ThemeTableHelper) mTableHelper).queryNeedDownloadMemoriesTheme();
    }

    public ThemeItem getNoneThemeItem() {
        ThemeItem noneItem = new ThemeItem();
        String name = ContextGetter.context
                .getResources().getString(R.string.videoeditor_editor_text_video_none);
        noneItem.setEnName(name);
        noneItem.setChName(name);
        noneItem.setZhName(name);
        noneItem.setIsBuiltin(true);
        noneItem.setThumbnailPath(NONE_ITEM_THUMBNAIL);
        noneItem.setDownloadState(TYPE_DOWNLOAD_ICON | TYPE_DOWNLOAD_FILE);
        noneItem.setSourcePath("");
        return noneItem;
    }
}
