/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TextOcrComponent.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/05/08
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     yanghaoge       2025/05/08    1.0                create
 ******************************************************************************/
package com.oplus.gallery.photo_page.ui.section.supertext

import android.content.res.ColorStateList
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.View
import android.view.ViewStub
import android.widget.ImageView
import androidx.core.graphics.ColorUtils
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.uiutil.ShadowUtils
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.addon.utils.OSVersionUtils.OPLUS_OS_16_0_0
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.ui.helper.FeedbackAnimatorHelper
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_OCR_SCANNER
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.DynamicVisibility
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.isVisible
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.FuncPresent
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoAnimationConfig
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataWhenPresentAndIdle
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncDetectInfo
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.Status
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.defaultFabCenterToTargetDistanceValue
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.thumbLineHeightValue
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.tools.addShadowLV2
import com.oplus.gallery.tools.setCircularOutline
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * 文本识别
 */
internal class TextOcrComponent(
    private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
) : LifecycleOwner {
    /**
     * 是否启用缩图轴
     */
    private val isThumbLineEnabled: Boolean
        get() = (sectionPage.pageViewModel.inputArguments.features.value?.isThumbLineEnabled == true)

    /**
     * 缩图轴是否显示
     */
    private val isThumbLineVisible: Boolean
        get() = sectionPage.pageViewModel.pageManagement.isThumbLineVisible.value

    /**
     * 是否支持ThumbnailSeekBar（显示视频多帧背景的seekbar）
     */
    private val isPlaybackPreviewEnabled: Boolean
        get() = (sectionPage.pageViewModel.inputArguments.features.value?.isPlaybackPreviewEnabled == true)

    /**
     * 缩图轴高度
     */
    private val thumbLineHeight: Int
        get() = sectionPage.pageViewModel.pageManagement.thumbLineHeightValue

    /**
     * 默认底部margin
     */
    private val defaultBottomMargin: Int
        get() = sectionPage.pageViewModel.pageManagement.defaultFabCenterToTargetDistanceValue - (textOcrViewHeight / 2)

    private var textOcrView: View? = null
    private var animationHelper: FeedbackAnimatorHelper? = null

    private val textOcrViewDefaultHeight by lazy { textOcrView?.context?.resources?.getDimensionPixelSize(R.dimen.photopage_text_ocr_height) }
    private val textOcrViewHeight: Int
        get() = textOcrView?.run {
            height.takeIf { it != 0 } ?: textOcrViewDefaultHeight
        } ?: 0

    /**
     * 文本编辑功能,文本识别，跳转内部编辑扫一扫
     */
    private val textOcrFuncPresent: FuncPresent = FuncPresent.TextOcr(sectionPage.pageViewModel)

    /**
     * 当前的智能推荐信息
     */
    private var currentFuncDetectInfo: FuncDetectInfo? = null

    /**
     * diffedFocus流。下发时机：
     * 1. 当图片 <-> 视频切换时才会下发
     * 2. 当入场动画结束 & 大图滑动idle时才下发
     */
    private val diffedFocusMediaTypeTimingFlow by lazy {
        sectionPage.pageViewModel.dataLoading.diffedFocusViewDataWhenPresentAndIdle.distinctUntilChanged { _, diffed ->
            // 只关心mimeType的变化
            diffed?.newItem?.mediaType == diffed?.oldItem?.mediaType
        }
    }

    /**
     * 当前正在执行的动画
     */
    private var currentAnimation: COUISpringAnimation? = null

    /**
     * 初始化当前组件，需要与[releaseComponent]成对调用，在页面加载时初始化，在页面销毁时release。
     */
    internal fun initComponent(view: View) {
        view.findViewById<ViewStub>(R.id.vs_text_ocr).inflate()?.let {
            textOcrView = it
            updateTextOcrViewVisibility(DynamicVisibility.HIDE_IMMEDIATELY)
            it.setOnClickListener {
                if (DoubleClickUtils.isFastDoubleClick()) {
                    return@setOnClickListener
                }
                textOcrFuncPresent.clickFunc.invoke()
            }
            animationHelper = FeedbackAnimatorHelper(it, FeedbackAnimatorHelper.Companion.CARD_PRESS_FEEDBACK)
            applyFloatingButtonTheme()
        }

        subscribeLiveDataFromViewModel()
    }

    /**
     * 屏幕状态发生改变
     */
    internal fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        if (config.windowWidth.isChanged()) {
            textOcrView?.post {
                textOcrView?.let {
                    adaptScreen(it)
                }
            }
        }
    }

    private fun adaptScreen(compareView: View) {
        val context = compareView.context
        val marginEnd = if (ResourceUtils.isRTL(compareView.context)) {
            sectionPage.pageInstance.leftNaviBarHeight()
        } else {
            sectionPage.pageInstance.rightNaviBarHeight()
        } + compareView.context.resources.getDimensionPixelSize(R.dimen.photopage_text_ocr_margin_end)

        val isVideo = sectionPage.pageViewModel.dataLoading.focusItemViewData?.isVideo == true
        val extBottomPadding = when {
            // 缩图轴高度
            isThumbLineEnabled && isThumbLineVisible -> thumbLineHeight
            // 视频播放时的ThumbnailSeekBar高度
            isVideo && isPlaybackPreviewEnabled -> context.resources.getDimensionPixelSize(R.dimen.photopage_playback_thumbnail_seekbar_height)
            // 视频播放时的默认SeekBar高度
            isVideo -> context.resources.getDimensionPixelSize(R.dimen.photopage_playback_normal_seekbar_height)
            else -> 0
        }

        // 避免导航栏和其他控件挡住推荐功能区
        compareView.updateMarginRelative(
            end = marginEnd,
            bottom = defaultBottomMargin + extBottomPadding
        )
        GLog.d(TAG, LogFlag.DL) {
            "[adaptScreen], end:$marginEnd, " +
                    "defaultBottomMargin:$defaultBottomMargin, extBottomPadding:$extBottomPadding"
        }
    }

    /**
     * release当前组件的资源。需要在页面销毁时调用，避免产生泄漏。
     */
    internal fun releaseComponent() {
        currentAnimation?.cancel()
        currentAnimation = null
        textOcrView?.handler?.removeCallbacksAndMessages(null)
        textOcrView = null
    }

    private fun subscribeLiveDataFromViewModel() {

        diffedFocusMediaTypeTimingFlow.collect(this@TextOcrComponent) {
            GTrace.trace({ "$TAG.diffedFocusViewData" }) {
                textOcrView?.let {
                    adaptScreen(it)
                }
            }
        }
        sectionPage.pageViewModel.pageManagement.thumbLineHeight.observe(this) {
            textOcrView?.let {
                adaptScreen(it)
            }
        }

        sectionPage.pageViewModel.photoFunc.funcDetectInfo.observe(this) {
            onFuncDetectInfoChanged(it)
        }

        sectionPage.pageViewModel.photoFunc.funcDetectInfo.value?.let {
            onFuncDetectInfoChanged(it)
        }

        sectionPage.pageViewModel.photoFunc.visible.observe(this) {
            onFuncDetectInfoChanged(forceUpdate = true)
        }

        sectionPage.pageViewModel.details.transitionPanelEffectState.observe(this) { panelEffectState ->
            val isInDetailsMode = sectionPage.pageViewModel.details.isDetailModeByTransition(panelEffectState)
            textOcrView?.apply {
                if (isInDetailsMode) {
                    updateTextOcrViewVisibility(DynamicVisibility.HIDE)
                } else {
                    onFuncDetectInfoChanged(forceUpdate = true)
                }
            }
        }

        sectionPage.pageViewModel.pageManagement.pageTheme.map { it.floatingButtonTheme }.collect(this) {
            applyFloatingButtonTheme()
        }

        sectionPage.pageViewModel.pageManagement.isThumbLineVisible.collect(this) {
            // 进入详情模式时无需进行变更
            if (sectionPage.pageViewModel.details.isDetailModeByTransition()) return@collect
            textOcrView?.let(::adaptScreen)
        }

        sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.observe(this) {
            if (it) {
                updateTextOcrViewVisibility(DynamicVisibility.HIDE)
            }
        }
    }

    private fun onFuncDetectInfoChanged(info: FuncDetectInfo? = currentFuncDetectInfo, forceUpdate: Boolean = false) {
        if (!forceUpdate) {
            if ((info?.status != Status.END) || info == currentFuncDetectInfo) {
                GLog.d(TAG, LogFlag.DL) { "onFuncDetectInfoChanged status not changed by info.status = ${info?.status}" }
                return
            }
            currentFuncDetectInfo = info
        }
        // 判断当前是否支持文本识别，支持则展示，否则隐藏文本识别按钮
        val supportOcrScanner = info?.funcItems?.find { it is FuncItem.OcrScanner }
        val supportTextOcr = info?.funcItems?.find { it is FuncItem.TextOcr }
        val support = (((supportOcrScanner is FuncItem.OcrScanner) && supportOcrScanner.hasSuperTextMetadata()) || (supportTextOcr != null))
        val notSupportGoogleLens = info?.funcItems?.find { it is FuncItem.GoogleLens } == null
        if (support
            && (sectionPage.pageViewModel.details.isDetailModeByTransition().not())
            && (notSupportGoogleLens)
            && (sectionPage.pageViewModel.photoFunc.visible.value == true)
        ) {
            updateTextOcrViewVisibility(DynamicVisibility.SHOW)
        } else {
            updateTextOcrViewVisibility(
                if (support
                    && (sectionPage.pageViewModel.details.isDetailModeByTransition().not())
                ) DynamicVisibility.HIDE else DynamicVisibility.HIDE_IMMEDIATELY
            )
        }
    }

    private fun updateTextOcrViewVisibility(dynamicVisibility: DynamicVisibility) {
        GLog.d(TAG, LogFlag.DL) {
            "updateTextOcrViewVisibility: dynamicVisibility = ${dynamicVisibility.name}"
        }
        var internalDynamicVisibility = dynamicVisibility
        val shouldAnimateWhenRemoved = sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.value == true
        currentAnimation?.cancel()
        if (internalDynamicVisibility.isVisible() && isFloatingWindowMode().not()) {
            if (internalDynamicVisibility == DynamicVisibility.SHOW_IMMEDIATELY && !shouldAnimateWhenRemoved) {
                showTextOcrView()
                textOcrView?.alpha = ALPHA_SHOW
            } else {
                val delayTime = if (shouldAnimateWhenRemoved) {
                    REMOVED_DELAY_ALPHA_SHOW_TIME
                } else {
                    0L
                }
                textOcrView?.apply {
                    postDelayed({
                        showTextOcrView()
                        fadeInOrOutAnimation(true, this)
                    }, delayTime)
                }
            }
        } else {
            //执行淡出动画之前功能区就不可见 ,不支持消失动画
            if (textOcrView?.isShown == false) {
                internalDynamicVisibility = DynamicVisibility.HIDE_IMMEDIATELY
                GLog.d(TAG, LogFlag.DL) {
                    "updateTextOcrViewVisibility: textOcrView isShown = false, unsupported HIDE animation."
                }
            }
            if (internalDynamicVisibility == DynamicVisibility.HIDE_IMMEDIATELY && !shouldAnimateWhenRemoved) {
                hideTextOcrView()
                textOcrView?.alpha = ALPHA_HIDE
            } else {
                textOcrView?.let {
                    fadeInOrOutAnimation(false, it)
                }
            }
        }
    }

    /**
     * 淡入淡出动画
     * @param show 是否展示 true:淡入展示，false:淡出隐藏
     * @param view 要做动画的View
     */
    private fun fadeInOrOutAnimation(show: Boolean, view: View) {
        val targetAlpha = if (show) ALPHA_SHOW else ALPHA_HIDE
        val response = if (show) ALPHA_FADE_IN_RESPONSE else ALPHA_FADE_OUT_RESPONSE
        currentAnimation = COUISpringAnimation(view, COUIDynamicAnimation.ALPHA).apply {
            setStartVelocity(ALPHA_ANIM_VELOCITY)
            val alphaSpringForce = COUISpringForce().setBounce(ALPHA_ANIM_BOUNCE).apply {
                setResponse(response)
                finalPosition = targetAlpha
            }
            spring = alphaSpringForce
            addEndListener { _, _, _, _ ->
                if (show.not()) {
                    hideTextOcrView()
                }
            }
            start()
        }
    }

    private fun showTextOcrView() {
        textOcrView?.visibility = View.VISIBLE
    }

    private fun hideTextOcrView() {
        textOcrView?.visibility = View.INVISIBLE
    }

    private fun isFloatingWindowMode(): Boolean = sectionPage.pageInstance.isFloatingWindowMode()
    override val lifecycle: Lifecycle
        get() = sectionPage.pageLifecycle

    private fun applyFloatingButtonTheme() {
        val theme = sectionPage.pageViewModel.pageManagement.pageTheme.value.floatingButtonTheme
        textOcrView?.apply {
            val backgroundColor = context.getColor(theme.background)
            // 添加描边效果
            val borderColor = context.getColor(theme.borderColor)
            val shapeDrawable = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                setColor(backgroundColor)
                setStroke(1, borderColor)
                val cornerRadius = textOcrViewDefaultHeight?.div(2f)
                cornerRadius?.let { setCornerRadius(it) }
            }
            background = shapeDrawable

            ShadowUtils.clearShadow(this)
            val shadow = context.getColor(theme.shadowColor)
            setCircularOutline()
            addShadowLV2(shadow)

            (this as? ImageView)?.apply {
                val contentColor = ColorUtils.setAlphaComponent(context.getColor(theme.contentColor), AppConstants.Number.NUMBER_0xFF)
                imageTintList = ColorStateList.valueOf(contentColor)
            }
        }
    }

    companion object {
        private const val TAG = "TextOcrComponent"

        /**
         * icon图标Spring动画BOUNCE
         */
        private const val ALPHA_ANIM_BOUNCE = 0f

        /**
         * icon图标，透明淡入RESPONSE
         */
        private const val ALPHA_FADE_IN_RESPONSE = 0.3f

        /**
         * icon图标，透明淡出RESPONSE
         */
        private const val ALPHA_FADE_OUT_RESPONSE = 0.25f

        /**
         * icon图标Spring动画初始速度
         */
        private const val ALPHA_ANIM_VELOCITY = 0f

        /**
         * 透明度1
         */
        private const val ALPHA_SHOW = 1f

        /**
         * 透明度0
         */
        private const val ALPHA_HIDE = 0f

        /**
         * 延迟600ms后，才开始执行淡入动画
         */
        private const val REMOVED_DELAY_ALPHA_SHOW_TIME = PhotoAnimationConfig.PHOTO_REMOVED_DELAY_ALPHA_SHOW_TIME

        /**
         * 扫一扫应用支持超级文本的meta-data name:support_super_text
         */
        const val META_DATA_KEY_SUPPORT_SUPER_TEXT = "support_super_text"

        /**
         * 大图识别文本 跳转到 扫一扫APK
         * 1、非内销，不跳转 扫一扫 APK
         * 2、APK未安装，不跳转 扫一扫
         * 3、OS 16 及其以上，跳转扫一扫
         * 4、OS 15 及其一下 且配置了 识文feature，跳转扫一扫
         * 5、否则 不跳转扫一扫
         */
        fun shouldJumpOcrScannerSuperText(): Boolean {
            // 如果不是内销，则不跳转
            if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not()) {
                return false
            }
            // 没安装扫一扫APK，则不跳转
            if (TextUtils.isEmpty(PACKAGE_OCR_SCANNER)) {
                return false
            }
            // OS 16.0 及其以上，进行跳转
            if (OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0)) {
                return true
            }
            // OS 15 以下 且 支持超级文本 meta-data配置
            return PackageInfoUtils.getMetaData<Boolean>(PACKAGE_OCR_SCANNER, META_DATA_KEY_SUPPORT_SUPER_TEXT) == true
        }
    }
}