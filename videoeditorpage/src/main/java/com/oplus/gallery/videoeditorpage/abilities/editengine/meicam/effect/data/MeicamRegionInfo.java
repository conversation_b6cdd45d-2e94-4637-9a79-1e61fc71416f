/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamRegionInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data;


import android.graphics.PointF;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamEllipse2D;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamTransform2D;

import java.util.ArrayList;
import java.util.List;

public class MeicamRegionInfo {
    public float mHorizontalTranslationRelative;//适配裁剪用，相对于中心点，画面上的距离[-0.5, 0.5]
    public float mVerticalTranslationRelative;//适配裁剪用，相对于中心点，画面上的距离 [-0.5, 0.5]
    //类型：多边形、椭圆、三阶贝塞尔曲线
    private int mType;
    //三界贝塞尔曲线点集合
    private List<PointF> mCubicBezierPoints = new ArrayList<>();
    //椭圆
    private MeicamEllipse2D mMeicamEllipse2D = new MeicamEllipse2D();
    //椭圆变换
    private MeicamTransform2D mMeicamTransform2D = new MeicamTransform2D();
    //蒙版旋转角度
    private float mAngle;
    //蒙版横向移动比例
    private float mHorizontalTranslationRatio;
    //蒙版纵向移动比例
    private float mVerticalTranslationRatio;
    //蒙版横向缩放比例（默认值1）
    private float mHorizontalScaleRatio = 1;
    //蒙版纵向缩放比例（默认值1）
    private float mVerticalScaleRatio = 1;
    //蒙版圆角比例
    private float mCornerRatio;

    public void syncParams(MeicamRegionInfo regionInfo) {
        if (regionInfo == null) {
            return;
        }
        setRotation(regionInfo.getRotation());
        setHorizontalTranslationRatio(regionInfo.getHorizontalTranslationRatio());
        setVerticalTranslationRatio(regionInfo.getVerticalTranslationRatio());
        setHorizontalScaleRatio(regionInfo.getHorizontalScaleRatio());
        setVerticalScaleRatio(regionInfo.getVerticalScaleRatio());
        setCornerRatio(regionInfo.getCornerRatio());
    }

    public int getType() {
        return mType;
    }

    public void setType(int mType) {
        this.mType = mType;
    }

    public List<PointF> getCubicBezierPoints() {
        return mCubicBezierPoints;
    }

    public void setCubicBezierPoints(List<PointF> mPoints) {
        this.mCubicBezierPoints = mPoints;
    }

    public MeicamEllipse2D getMeicamEllipse2D() {
        return mMeicamEllipse2D;
    }

    public void setMeicamEllipse2D(MeicamEllipse2D mMeicamEllipse2D) {
        this.mMeicamEllipse2D = mMeicamEllipse2D;
    }

    public MeicamTransform2D getMeicamTransform2D() {
        return mMeicamTransform2D;
    }

    public void setMeicamTransform2D(MeicamTransform2D mMeicamTransform2D) {
        this.mMeicamTransform2D = mMeicamTransform2D;
    }

    //蒙版旋转角度
    public void setRotation(float angle) {
        this.mAngle = angle;
    }

    public float getRotation() {
        return mAngle;
    }

    //蒙版横向移动比例
    public void setHorizontalTranslationRatio(float ratio) {
        this.mHorizontalTranslationRatio = ratio;
    }

    public float getHorizontalTranslationRatio() {
        return mHorizontalTranslationRatio;
    }

    //蒙版纵向移动比例
    public void setVerticalTranslationRatio(float ratio) {
        this.mVerticalTranslationRatio = ratio;
    }

    public float getVerticalTranslationRatio() {
        return mVerticalTranslationRatio;
    }

    //蒙版横向缩放比例
    public void setHorizontalScaleRatio(float ratio) {
        this.mHorizontalScaleRatio = ratio;
    }

    public float getHorizontalScaleRatio() {
        return mHorizontalScaleRatio;
    }

    //蒙版纵向缩放比例
    public void setVerticalScaleRatio(float ratio) {
        this.mVerticalScaleRatio = ratio;
    }

    public float getVerticalScaleRatio() {
        return mVerticalScaleRatio;
    }

    //蒙版圆角比例
    public void setCornerRatio(float ratio) {
        this.mCornerRatio = ratio;
    }

    public float getCornerRatio() {
        return mCornerRatio;
    }

    @Override
    public String toString() {
        return "MeicamRegionInfo{"
                + "mType=" + mType
                + ", mCubicBezierPoints="
                + mCubicBezierPoints
                + ", mMeicamEllipse2D="
                + mMeicamEllipse2D
                + ", mMeicamTransform2D="
                + mMeicamTransform2D
                + ", mAngle=" + mAngle
                + ", mHorizontalTranslationRatio="
                + mHorizontalTranslationRatio
                + ", mVerticalTranslationRatio="
                + mVerticalTranslationRatio
                + ", mHorizontalScaleRatio="
                + mHorizontalScaleRatio
                + ", mVerticalScaleRatio="
                + mVerticalScaleRatio
                + ", mCornerRatio="
                + mCornerRatio
                + '}';
    }
}
