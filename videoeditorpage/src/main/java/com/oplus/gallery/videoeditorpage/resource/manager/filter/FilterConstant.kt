/********************************************************************************
 ** Copyright (C), 2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FilterConstant
 ** Description:
 ** Version: 1.0
 ** Date : 2025/6/20
 ** Author: kangshuwen
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** ka<PERSON><PERSON><PERSON>                      2025/6/20       1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.manager.filter

import com.oplus.gallery.basebiz.R

/**
 * 滤镜相关常量
 */
object FilterConstant {
    // 美摄滤镜key
    /*清新*/
    private const val TBL_KEY_FILTER_FRESH = "key_f_common_1"
    /*通透*/
    private const val TBL_KEY_FILTER_CLEAR = "key_f_common_3"
    /*正片*/
    private const val TBL_KEY_FILTER_WARM = "key_g_gr3slide_2020"
    /*胶片*/
    private const val TBL_KEY_FILTER_FILM = "key_g_ssx04_2020"
    /*摩登*/
    private const val TBL_KEY_FILTER_MODERN = "key_oplus_black_gold_2020"
    /*黑白*/
    private const val TBL_KEY_FILTER_BKANDWH = "key_f_common_5"
    /*锐黑*/
    private const val TBL_KEY_FILTER_CONTRAST = "key_g_gr3hc_2020"
    /*灰调*/
    private const val TBL_KEY_FILTER_GRAY = "key_f_common_6"
    /*冷调*/
    private const val TBL_KEY_FILTER_COOL = "key_f_back_4"
    /*复古*/
    private const val TBL_KEY_FILTER_VINTAGE = "key_f_common_4"
    /*褪色*/
    private const val TBL_KEY_FILTER_FADE = "key_f_back_2"
    /*薄雾*/
    private const val TBL_KEY_FILTER_MIST = "key_bowu_2020"
    /*美味*/
    private const val TBL_KEY_FILTER_FOOD = "key_meiwei_2020"
    /*秋日*/
    private const val TBL_KEY_FILTER_AUTUMN = "key_qiuri_2020"
    /*城市*/
    private const val TBL_KEY_FILTER_CITY = "key_f_back_3"
    /*田野*/
    private const val TBL_KEY_FILTER_COUNTRY = "key_jiari_2020"
    /*暖阳*/
    private const val TBL_KEY_FILTER_SUNSET = "key_yuanqi_2020"
    /*旅途*/
    private const val TBL_KEY_FILTER_VOYAGE = "key_lvtu_2020"
    /*菲林*/
    private const val TBL_KEY_FILTER_FOREST = "key_senlin_2020"
    /*燃情*/
    private const val TBL_KEY_FILTER_FLAMINGO = "key_oplus_infra_2020"
    /*赛博朋克*/
    private const val TBL_KEY_FILTER_CYBERPUNK = "key_oplus_cyberpunk_2020"

    /**
     * 美摄滤镜key和string的映射关系
     */
    val FILTER_CONFIG = mapOf(
        TBL_KEY_FILTER_FRESH to R.string.picture3d_new_editor_text_filter_common_1,
        TBL_KEY_FILTER_CLEAR to R.string.picture3d_new_editor_text_filter_common_3,
        TBL_KEY_FILTER_WARM to R.string.picture3d_new_editor_text_filter_g_gr3slide_2020,
        TBL_KEY_FILTER_FILM to R.string.picture3d_new_editor_text_filter_c_g_ssx04_2020,
        TBL_KEY_FILTER_MODERN to R.string.picture3d_new_editor_text_filter_oplus_black_gold_2020,
        TBL_KEY_FILTER_BKANDWH to R.string.picture3d_new_editor_text_filter_common_5,
        TBL_KEY_FILTER_CONTRAST to R.string.picture3d_new_editor_text_filter_g_gr3hc_2020,
        TBL_KEY_FILTER_GRAY to R.string.picture3d_new_editor_text_filter_common_6,
        TBL_KEY_FILTER_COOL to R.string.picture3d_new_editor_text_filter_back_4,
        TBL_KEY_FILTER_VINTAGE to R.string.picture3d_new_editor_text_filter_common_4,
        TBL_KEY_FILTER_FADE to R.string.picture3d_new_editor_text_filter_back_2,
        TBL_KEY_FILTER_MIST to R.string.picture3d_new_editor_text_filter_baowu_2020,
        TBL_KEY_FILTER_FOOD to R.string.picture3d_new_editor_text_filter_meiwei_2020,
        TBL_KEY_FILTER_AUTUMN to R.string.picture3d_new_editor_text_filter_qiuri_2020,
        TBL_KEY_FILTER_CITY to R.string.picture3d_new_editor_text_filter_back_3,
        TBL_KEY_FILTER_COUNTRY to R.string.picture3d_new_editor_text_filter_front_5,
        TBL_KEY_FILTER_SUNSET to R.string.picture3d_new_editor_text_filter_front_3,
        TBL_KEY_FILTER_VOYAGE to R.string.picture3d_new_editor_text_filter_front_1,
        TBL_KEY_FILTER_FOREST to R.string.picture3d_new_editor_text_filter_back_5,
        TBL_KEY_FILTER_FLAMINGO to R.string.picture3d_new_editor_text_filter_oplus_infra_2020,
        TBL_KEY_FILTER_CYBERPUNK to R.string.picture3d_new_editor_text_filter_oplus_cyberpunk_2020,
    )
}