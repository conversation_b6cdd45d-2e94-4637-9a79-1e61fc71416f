/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamAssetManager.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.asset;

import android.text.TextUtils;

import com.oplus.gallery.foundation.networkaccess.other.NonNull;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.utlis.PhoneUtils;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsStreamingContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class MeicamAssetManager implements IAssetManager, NvsAssetPackageManager.AssetPackageManagerCallback {

    private static final String TAG = "MeicamAssetManager";
    private final HashMap<String, ArrayList<IAssetProcessListener>> mAssetProcessListeners = new HashMap<>();
    private final NvsStreamingContext mStreamingContext;
    private NvsAssetPackageManager mAssetPackageManager;

    public MeicamAssetManager(@NonNull NvsStreamingContext streamingContext) {
        mStreamingContext = streamingContext;
        mAssetPackageManager = mStreamingContext.getAssetPackageManager();
        mAssetPackageManager.setCallbackInterface(this);
    }

    @Override
    public void installAsset(String assetPackageFilePath, String licenseFilePath, Integer type, IAssetProcessListener listener) {
        if (TextUtils.isEmpty(assetPackageFilePath) || (type == null)) {
            listener.onAssetProcessError(StreamingConstant.AseetInstallError.ASSET_PACKAGE_MANAGER_ERROR_IO);
            return;
        }

        try {
            final String assetPackageId = mAssetPackageManager.getAssetPackageIdFromAssetPackageFilePath(assetPackageFilePath);
            int code = StreamingConstant.AseetInstallError.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED;
            if (mAssetPackageManager.getAssetPackageStatus(assetPackageId, type) == NvsAssetPackageManager.ASSET_PACKAGE_STATUS_NOTINSTALLED) {
                code = mAssetPackageManager.installAssetPackage(assetPackageFilePath, licenseFilePath, type, false, new StringBuilder());
            } else {
                boolean isNeedUpdate = mAssetPackageManager.getAssetPackageVersion(assetPackageId, type)
                        < mAssetPackageManager.getAssetPackageVersionFromAssetPackageFilePath(assetPackageFilePath);
                if (isNeedUpdate) {
                    code = mAssetPackageManager.upgradeAssetPackage(assetPackageFilePath, licenseFilePath, type, false, new StringBuilder());
                }
            }

            if (isProcessFinished(code)) {
                listener.onFinishAssetPackageInstallation(assetPackageId, assetPackageFilePath, type);
            } else {
                addAssetProcessCallback(assetPackageId, listener);
            }
        } catch (Throwable e) {
            GLog.e(TAG, LogFlag.DL, "installAsset error:" + e);
        }
    }

    @Override
    public int uninstallAsset(String assetPackageId, Integer type) {
        if (TextUtils.isEmpty(assetPackageId) || (type == null)) {
            return StreamingConstant.AseetInstallError.ASSET_PACKAGE_MANAGER_ERROR_IO;
        }
        return mAssetPackageManager.uninstallAssetPackage(assetPackageId, type);
    }

    private void addAssetProcessCallback(String assetPackageId, IAssetProcessListener assetProcessListener) {
        if (assetProcessListener == null) {
            return;
        }
        synchronized (mAssetProcessListeners) {
            mAssetProcessListeners.computeIfAbsent(assetPackageId, key -> new ArrayList<>())
                    .add(assetProcessListener);
        }
    }

    @Override
    public String registerFontByFilePath(String fontPath) {
        if (mStreamingContext == null) {
            GLog.e(TAG, "createVideoFrameRetriever, mNvsStreamingContext == null");
            return null;
        }
        return mStreamingContext.registerFontByFilePath(fontPath);
    }

    @Override
    public boolean needInstall(String filePath, int type) {
        String id = mAssetPackageManager.getAssetPackageIdFromAssetPackageFilePath(filePath);
        return (mAssetPackageManager.getAssetPackageStatus(id, type) == NvsAssetPackageManager.ASSET_PACKAGE_STATUS_NOTINSTALLED);
    }

    @Override
    public String getIdByPath(String filePath) {
        return mAssetPackageManager.getAssetPackageIdFromAssetPackageFilePath(filePath);
    }

    public static boolean isRequiredAssetType(String assetPackeageId, Integer type) {
        NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
        if (streamingContext == null) {
            return false;
        }
        if (TextUtils.equals(assetPackeageId, BaseCaption.DEFAULT_CAPTION_STYLE_ID)
            && (type == PhoneUtils.getNormalCaptionStyle())) {
            return true;
        }
        NvsAssetPackageManager assetPackageManager = streamingContext.getAssetPackageManager();
        if (assetPackageManager != null) {
            List<String> list = assetPackageManager.getAssetPackageListOfType(type);
            if (list != null) {
                for (String id : list) {
                    if (TextUtils.equals(id, assetPackeageId)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    @Override
    public void onFinishAssetPackageInstallation(String assetPackageId, String assetPackageFilePath, int assetPackageType, int error) {
        synchronized (mAssetProcessListeners) {
            if (mAssetProcessListeners.isEmpty()) {
                return;
            }
            ArrayList<IAssetProcessListener> listeners = mAssetProcessListeners.remove(assetPackageId);
            if (listeners != null) {
                for (IAssetProcessListener assetProcessListener : listeners) {
                    if (isProcessFinished(error)) {
                        assetProcessListener.onFinishAssetPackageInstallation(assetPackageId, assetPackageFilePath, assetPackageType);
                    } else {
                        assetProcessListener.onAssetProcessError(error);
                    }
                }
            }
        }
    }

    @Override
    public void onFinishAssetPackageUpgrading(String assetPackageId, String assetPackageFilePath, int assetPackageType, int error) {
        synchronized (mAssetProcessListeners) {
            if (mAssetProcessListeners.isEmpty()) {
                return;
            }
            ArrayList<IAssetProcessListener> listeners = mAssetProcessListeners.remove(assetPackageId);
            if (listeners != null) {
                for (IAssetProcessListener assetProcessListener : listeners) {
                    if (isProcessFinished(error)) {
                        assetProcessListener.onFinishAssetPackageUpgrading(assetPackageId, assetPackageFilePath, assetPackageType);
                    } else {
                        assetProcessListener.onAssetProcessError(error);
                    }
                }
            }
        }
    }

    private boolean isProcessFinished(int code) {
        return (code == StreamingConstant.AseetInstallError.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED)
                || (code == StreamingConstant.AseetInstallError.ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR);
    }

    @Override
    public void release() {
        if (mAssetPackageManager != null) {
            mAssetPackageManager.setCallbackInterface(null);
            mAssetPackageManager = null;
        }
        synchronized (mAssetProcessListeners) {
            mAssetProcessListeners.clear();
        }
    }
}
