/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CustomPictureManagerActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/10/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/10/28        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.picturemanager

import android.os.Bundle
import androidx.fragment.app.DialogFragment
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_CODE
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_MODE
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.permission.helper.CTAHelper
import com.oplus.gallery.basebiz.uikit.activity.BasePermissionsActivity
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns.MODE_DEFAULT
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.widgetpage.WidgetConstants.ParamKey.CARD_ID
import com.oplus.gallery.widgetpage.WidgetConstants.ParamKey.CARD_TYPE
import com.oplus.gallery.widgetpage.WidgetConstants.ParamKey.HOST_ID

@RouterNormal(path = RouterConstants.RouterName.WIDGET_MODE_SELECTION_ACTIVITY)
class CustomPictureManagerActivity : BasePermissionsActivity() {

    private var dialogFragment: DialogFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setNaviBarColor(getColor(com.oplus.gallery.basebiz.R.color.common_transparent))
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        if (!CTAHelper.isPermissionDialogShowing()) {
            GLog.d(TAG, LogFlag.DL) { "[onCreate] showCustomPictureManagerDialog" }
            showCustomPictureManagerDialog()
        } else {
            GLog.d(TAG, LogFlag.DL) { "[onCreate] not showCustomPictureManagerDialog" }
        }
        widgetCheckUpdate()
    }

    /**
     * 设置页长按会分三个参数传，直接点击卡片会合成一个widgetCode来传
     */
    private fun getWidgetCode(): String = IntentUtils.getStringExtra(intent, KEY_WIDGET_CODE) ?: run {
        getWidgetCode(
            IntentUtils.getIntExtra(intent, CARD_TYPE, INVALID_VALUE),
            IntentUtils.getIntExtra(intent, CARD_ID, INVALID_VALUE),
            IntentUtils.getIntExtra(intent, HOST_ID, INVALID_VALUE)
        )
    }

    /**
     * 参考com.oplus.cardwidget.util.CardDataTranslaterKt.getWidgetId
     * 根据cardType、cardId、hostId拼出widgetCode
     */
    private fun getWidgetCode(cardType: Int, cardId: Int, hostId: Int): String {
        return "$cardType&$cardId&$hostId"
    }

    override fun fromUserOp(op: CTAHelper.CTAUserOp, region: CTAHelper.CTARegion, tag: Int) {
        super.fromUserOp(op, region, tag)
        if ((op == CTAHelper.CTAUserOp.OP_PERMITTED) || (op == CTAHelper.CTAUserOp.OP_CANCELED)) {
            GLog.d(TAG, LogFlag.DL) { "[fromUserOp] showCustomPictureManagerDialog" }
            showCustomPictureManagerDialog()
        } else {
            GLog.d(TAG, LogFlag.DL) { "[fromUserOp] op=$op" }
        }
    }

    /**
     * 自选卡片样式下也需要进行刷新
     * 避免自选卡片样式下清除数据后，点击卡片不同意授权，卡片不会刷新为未授权样式
     */
    private fun widgetCheckUpdate() {
        IntentUtils.getStringExtra(intent, KEY_WIDGET_CODE)?.let { widgetCode ->
            val mode = IntentUtils.getIntExtra(intent, KEY_WIDGET_MODE, MODE_DEFAULT)
            // 清除相册数据后，卡片的mode参数默认为0。同意授权后才会再次赋值。
            if (mode == 0) {
                ApiDmManager.getWidgetDM().let { widgetDM ->
                    widgetDM.forceUpdate(this.application, widgetCode)
                    GLog.d(TAG, LogFlag.DL) { "[widgetCheckUpdate]___forceUpdate, code = $widgetCode" }
                }
            }
        }
    }

    private fun showCustomPictureManagerDialog() {
        if (dialogFragment != null) {
            GLog.d(TAG, LogFlag.DL) { "[showCustomPictureManagerDialog] dialogFragment not null return" }
            return
        }
        val widgetCode = getWidgetCode()
        if (widgetCode.isEmpty() || (widgetCode == getWidgetCode(INVALID_VALUE, INVALID_VALUE, INVALID_VALUE))) {
            GLog.e(TAG, LogFlag.DL) { "[showCustomPictureManagerDialog] widgetCode=$widgetCode invalid!" }
            return
        }
        dialogFragment = Starter.DialogFragmentStarter<PanelDialog>(
            supportFragmentManager,
            Bundle().apply { putString(KEY_WIDGET_CODE, widgetCode) },
            PostCard(RouterConstants.RouterName.WIDGET_MODE_SELECTION_DIALOG)
        ).start()
    }

    companion object {
        const val TAG = "CustomPictureManagerActivity"
        const val INVALID_VALUE = -1
    }
}