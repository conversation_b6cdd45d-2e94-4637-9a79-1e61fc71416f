/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - WatermarkMasterVM.kt
 ** Description: 大师水印编辑页的ViewModel.
 ** Version: 1.0
 ** Date : 2024/8/15
 ** Author: <EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2024/8/15      1.0        created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.watermarkmaster

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.res.Resources
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.IBinder
import android.text.TextUtils
import android.util.Size
import android.view.View
import com.google.gson.Gson
import com.oplus.camera.watermark.GalleryConnection
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.function.restrict.MaterialSupportCheckStrategy
import com.oplus.gallery.basebiz.helper.function.restrict.WatermarkSupportCheckStrategy
import com.oplus.gallery.basebiz.helper.restrict.RestrictButtonConfigUtils
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.util.SystemConfigs.isExport
import com.oplus.gallery.basebiz.util.SystemConfigs.isOnePlus
import com.oplus.gallery.basebiz.util.SystemConfigs.isRealme
import com.oplus.gallery.basebiz.util.SystemConfigs.productBrand
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.location.camera.CameraDetailsHelper
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.business_lib.template.editor.data.ItemStatus
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.archv2.bus.ReplierChannel
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUIThreadImmediate
import com.oplus.gallery.foundation.util.geo.GPS
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_LUMO_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PRIVACY_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.CAMERA_WATERMARK_PREVIEW_MARKET_NAME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_OPPO_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_LOCATION_RESOLVE
import com.oplus.gallery.framework.abilities.download.ProcessResultCode
import com.oplus.gallery.framework.abilities.download.processor.ProcessResult
import com.oplus.gallery.framework.abilities.editing.asset.ExifDataKey.TAG_DATETIME
import com.oplus.gallery.framework.abilities.editing.asset.ExifDataKey.TAG_DATETIME_ORIGINAL
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType.EXIF
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.AI_MASTER_WATERMARK
import com.oplus.gallery.framework.abilities.watermark.BOOL_ATTR_VISIBLE
import com.oplus.gallery.framework.abilities.watermark.FileStyleSource
import com.oplus.gallery.framework.abilities.watermark.INT_ATTR_TEXT_SOURCE
import com.oplus.gallery.framework.abilities.watermark.IRestrictWatermarkDownloadAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkStyleLoader
import com.oplus.gallery.framework.abilities.watermark.IWatermarkStyleOperator
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.BACKGROUND_IMAGE
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.BUTTON_FONT
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.CAMERA_THUMBNAIL
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.FONT
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.ICON_IMAGE
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.LARGE_THUMBNAIL
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.REVIEW_IMAGE
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.SMALL_THUMBNAIL
import com.oplus.gallery.framework.abilities.watermark.conifg.RestrictWatermarkResourceType.TRANS_FILE
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl.Companion.LAST_SAVE_CAMERA_STYLE_ID_KEY
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl.Companion.LAST_SAVE_STYLE_ID_KEY
import com.oplus.gallery.framework.abilities.watermark.file.AiWatermarkMasterParams
import com.oplus.gallery.framework.abilities.watermark.file.PhoneInfo
import com.oplus.gallery.framework.abilities.watermark.file.PrivacyWatermarkParams
import com.oplus.gallery.framework.abilities.watermark.file.PrivacyWatermarkStyle
import com.oplus.gallery.framework.abilities.watermark.file.ShootingInfo
import com.oplus.gallery.framework.abilities.watermark.file.ShootingInfo.Companion.isValid
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkCode
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkContent
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkParams
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkPattern
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkPosition
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkTextSize
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_TEXTURE
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_URL
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BITMAP_TYPE_FILEPATH
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BITMAP_TYPE_URL
import com.oplus.gallery.framework.abilities.watermark.masterstyle.TEXT_SOURCE_IMAGE_PARAMS
import com.oplus.gallery.framework.abilities.watermark.masterstyle.TEXT_SOURCE_NONE
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkElement
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle.Companion.TYPE_STYLE_NONE
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle.Companion.isRestrictStyle
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle.StyleLogo
import com.oplus.gallery.framework.abilities.watermark.masterstyle.toJsonString
import com.oplus.gallery.framework.abilities.watermark.util.WatermarkMasterInfoUtils
import com.oplus.gallery.framework.abilities.watermark.util.WatermarkStyleDataProcessor
import com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkMasterGreetingDataHelper
import com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkMasterResourceDataHelper
import com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkMasterSeriesDataHelper
import com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkMasterStyleDataHelper
import com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkMasterWordDataHelper
import com.oplus.gallery.framework.abilities.watermarkmaster.entry.RestrictWaterMarkTypeButtonEntry
import com.oplus.gallery.framework.abilities.watermarkmaster.entry.RestrictWatermarkResourceEntry
import com.oplus.gallery.framework.abilities.watermarkmaster.entry.RestrictWatermarkSeriesEntry
import com.oplus.gallery.framework.abilities.watermarkmaster.listener.MateriaDownloadListener
import com.oplus.gallery.framework.abilities.watermarkmaster.net.MateriaDownloadLoader
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseWatermarkVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EmptyReplier
import com.oplus.gallery.photoeditor.editingvvm.StringReplier
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.REPLY_TOPIC_REMOVE_WATERMARK_IF_NEED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.REPLY_TOPIC_RESTORE_WATERMARK_IF_NEED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_CAMERA_WATERMARK_SETTING
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_IMAGE_PACK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.PersonalizedEdit.TOPIC_PERSONALIZED_EDIT_UI_BEAN
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.TOPIC_MENU_RESTRICT_WATERMARK_CORNER_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_USE_P3_COLOR_GAMUT
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.InputArgumentsVM.Companion.AI_MASTER_WATERMARK_FORBIDDEN_STYLES
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.InputArgumentsVM.Companion.KEY_CAMERA_WATERMARK_GROUP_NAME
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.operating.ExitPageListener
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingAction
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.watermark.PrivacyDataStatus
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord.Companion.CMD_WATERMARK_ADD
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkVM
import com.oplus.gallery.photoeditor.editingvvm.watermark.track.WatermarkTrackConstant.Value.RESULT_TYPE_EXIT
import com.oplus.gallery.photoeditor.editingvvm.watermark.track.WatermarkTrackConstant.Value.RESULT_TYPE_SAVE
import com.oplus.gallery.photoeditor.editingvvm.watermark.track.WatermarkTrackHelper
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.WatermarkCameraAutoTestUtil
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.WatermarkCameraPreviewDrawer
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.WatermarkCameraSettingOperator
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterTopic.TOPIC_STYLE_VIEW_DATA
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.ButtonInfo
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkStyleItemViewData
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.WatermarkPersonalizedEditUiBean
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.WatermarkPersonalizedEditUiBeanId
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkTypeItemViewData
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.ocs.camera.ipuapi.process.watermark.WatermarkLocationParser
import com.oplus.ocs.camera.ipuapi.process.watermark.entity.WatermarkAddressBean
import com.oplus.ocs.ipu.watermark.util.MarketNameInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.InputStreamReader
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.collections.ArrayList
import kotlin.coroutines.resume

/**
 * 大师水印编辑页的ViewModel
 * @param editingVM 主ViewModel
 */
@Suppress("LargeClass")
internal class WatermarkMasterVM(editingVM: EditingVM) : BaseWatermarkVM(editingVM) {
    private var watermarkUiPub: PublisherChannel<WatermarkMasterUIBean>? = null
    private var personalizedDataPub: PublisherChannel<Map<String, Any?>>? = null

    // 当前有新的系列第一次展示，发送当前所属系列的水印样式Id
    private var restrictWatermarkSeriesStyleIdPub: PublisherChannel<String>? = null

    // 当前系列di变更时通知页面
    private var restrictWatermarkCurrentSeriesIdPub: PublisherChannel<String>? = null

    /**
     * 是否已触发过限定水印下载
     */
    private var isRestrictWatermarkDownloaded: Boolean
        get() = MultiProcessSpUtil.getBoolean(IS_RESTRICT_WATERMARK_DOWNLOADED, false)
        set(allowDownloadOnMobile) {
            MultiProcessSpUtil.save(IS_RESTRICT_WATERMARK_DOWNLOADED, allowDownloadOnMobile)
        }

    private val isFromCameraEditType: Boolean by lazy {
        PhotoEditorType.WATERMARK_CAMERA.tag == vmBus?.get<String>(TopicID.InputArguments.TOPIC_INPUTS_EDIT_TYPE)
    }

    /**
     * 限定水印样式列表数据（包含内置的限定水印，和网络下载的限定水印）
     */
    private val restrictStyleListViewData = CopyOnWriteArrayList<WatermarkStyleItemViewData>()

    /**
     * 限定水印样式列表数据是否已添加到列表
     */
    private var isRestrictStyleListViewDataAdded = false

    /**
     * 已失效的限定水印样式Id列表（例如限时水印已超期）
     */
    private val invalidRestrictStyleIds = mutableListOf<String>()

    /**
     * 水印依赖素材下载器
     */
    private var downloadLoader = MateriaDownloadLoader(app.applicationContext)
    private val materialStrategy = MaterialSupportCheckStrategy()
    private val restrictStrategy = WatermarkSupportCheckStrategy()
    private var phoneInfo = PhoneInfo()
    private var preWatermarkPattern: WatermarkPattern? = WatermarkPattern.PATTERN_NO_WATERMARK
    private var watermarkStyleLoader: IWatermarkStyleLoader? = null
    private var privacyStatsPub: PublisherChannel<PrivacyDataStatus>? = null
    /**
     * 添加水印通知loading弹窗显示或隐藏
     */
    private var addWatermarkLoadingPub: PublisherChannel<Pair<Boolean, Long>>? = null

    private var cameraWatermarkServiceClient: GalleryConnection? = null

    private val cameraWatermarkServiceConnection = CameraWatermarkServiceConnection()

    /**
     * 是否提交过水印操作
     */
    private var hasEmitWatermarkRecord = false

    /**
     * 是否使用横屏布局
     */
    private var isLayoutLandscape = false

    private var watermarkPreviewDrawer: WatermarkCameraPreviewDrawer? = null
    private var currentStyleId: String? = null
    private var currentSeriesId: String? = null
    private var watermarkOperator: IWatermarkStyleOperator? = null

    private var currentRestrictWatermarkTitle = TextUtil.EMPTY_STRING
    private var currentRestrictWatermarkDesc = TextUtil.EMPTY_STRING
    private var currentRestrictWatermarkDescr1 = TextUtil.EMPTY_STRING
    private var currentRestrictWatermarkDescr2 = TextUtil.EMPTY_STRING
    /**
     * 临时记录需要下载的水印字体
     * 水印字体资源可复用，避免重复下载
     */
    private var fontUnDownloadEntityListTemp = mutableListOf<Pair<RestrictWatermarkResourceEntry, String>>()

    /**
     * 原来图片水印样式
     */
    private var originalPattern: WatermarkPattern? = null

    /**
     * 背景：横竖屏切换类型按钮不被选中 样式不显示，
     * 原因：viewId 随机生成导致type选中失去焦点
     * 限定水印viewId typeButtonId映射表
     * */
    private var restrictViewButtonMap: MutableMap<String?, Int> = mutableMapOf()

    /**
     * 是否可以使用限定水印数据
     * 从相机进入或者当前图片是支持水印编辑的则可以使用
     * */
    private val isCanUseRestrictWatermarkStyle: Boolean by lazy { isFromCameraEditType || isPhotoSupportWatermark() }

    /**
     * 图片品牌与设备品牌是否一致
     * 从相机进入或者当前图片图片品牌与设备品牌一致，则可以使用品牌力量系列水印，否则不显示
     * */
    private val isSameBrand: Boolean by lazy {
        val photoBrand = originalWatermarkInfo?.content?.make
        if (isFromCameraEditType.not() && (photoBrand?.isNotBlank() == true) &&
            (productBrand?.isNotBlank() == true) && (photoBrand != productBrand)
        ) {
            GLog.d(TAG, LogFlag.DL) { "[isSameBrand] photoBrand: $photoBrand, productBrand: $productBrand, brand is not same!" }
            false
        } else true
    }

    /**
     * 相机需要禁用的水印样式
     * 由于2025毕业季未配置相机直出所需的资源，且上线时间在OS16.0有重叠，故暂时在相机中禁用毕业季水印样式。
     */
    private val cameraForbiddenStyles: String? by lazy {
        val forbiddenStyles = vmBus?.get<MutableMap<String, Any?>>(TOPIC_CAMERA_WATERMARK_SETTING)?.getOrDefault(
            AI_MASTER_WATERMARK_FORBIDDEN_STYLES, null
        ) as? String
        if (forbiddenStyles.isNullOrBlank()) {
            DEFAULT_CAMERA_FORBIDDEN_STYLES
        } else {
            "$forbiddenStyles,$DEFAULT_CAMERA_FORBIDDEN_STYLES"
        }
    }

    private fun createWatermarkPreviewDrawer(appUiConfig: AppUiResponder.AppUiConfig?): WatermarkCameraPreviewDrawer? {
        watermarkPreviewDrawer = if (isFromCameraEditType) {
            val isMedium = (appUiConfig?.screenMode?.current == AppUiResponder.ScreenMode.LARGE)
            val targetResource: Int = if (isMedium) {
                R.drawable.watermark_camera_personalized_preview
            } else {
                R.drawable.watermark_camera_setting_preview
            }
            val previewBitmap = BitmapFactory.decodeResource(app.resources, targetResource)
            WatermarkCameraPreviewDrawer(app, previewBitmap) {
                runOnUIThreadImmediate {
                    // 经过管线处理返回Bitmap，刷新预览View
                    val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)
                        ?: WatermarkMasterUIBean()
                    bean.id = WatermarkMasterUiBeanId.WATERMARK_CAMERA_GET_BITMAP
                    bean.cameraPreviewBitmap = it
                    watermarkUiPub?.publish(bean)
                }
            }
        } else null

        return watermarkPreviewDrawer
    }

    private val imagePackObserver: TObserver<EditingImagePack> = {
        if (it.watermarkInfo != null) {
            onImagePackChange(it)
        }
    }

    private val isPrivacyWatermarkEditType: Boolean by lazy {
        PhotoEditorType.PRIVACY_WATERMARK.tag == vmBus?.get<String>(TopicID.InputArguments.TOPIC_INPUTS_EDIT_TYPE)
    }

    private val styleViewDataReq = ReplierChannel<MutableList<WatermarkStyleItemViewData>?> { args ->
        val titleId = (args.getOrNull(0) as? Int) ?: Resources.ID_NULL
        val viewData = when (titleId) {
            R.string.picture_editor_text_watermark_master_hasselblad -> {
                // 如果参数1为true，表示只要视频哈苏文字水印，给视频水印使用
                if (isRealmeBrand()) {
                    return@ReplierChannel ArrayList<WatermarkStyleItemViewData>()
                }
                if (args.getOrNull(1) == true) {
                    getVideoHasselTextStyleListViewData()
                } else {
                    getHasselStyleListViewData()
                }
            }

            R.string.picture_editor_text_watermark_master_frame -> getFrameStyleListViewData()
            R.string.picture_editor_text_watermark_master_realme_brand -> getRealmeBrandStyleListViewData()
            R.string.picture_editor_text_watermark_master_restrict -> restrictStyleListViewData
            R.string.picture_editor_text_watermark_master_text -> {
                // 如果参数1为true，表示只要视频文字水印，给视频水印使用
                if (args.getOrNull(1) == true) {
                    getVideoTextStyleListViewData()
                } else {
                    getTextStyleListViewData()
                }
            }

            R.string.pitcure_edtior_text_watermark_master_seal -> getMasterSignStyleListViewData()
            R.string.picture_editor_text_watermark_master_submission_series -> getFilmStyleListViewData()
            R.string.pitcure_edtior_text_watermark_classic_camera -> getRetroCameraStyleListViewData()
            R.string.pitcure_edtior_text_watermark_brand_power -> getBrandStyleListViewData()
            R.string.pitcure_edtior_text_watermark_realme_brand_power -> getRealmeMomentStyleListViewData()
            R.string.pitcure_edtior_text_watermark_inspiration_photo -> {
                if (ConfigAbilityWrapper.getBoolean(
                        ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_WATERMARK_MASTER,
                        defValue = false,
                        expDefValue = false
                    )) {
                    getInspationStyleListViewData()
                } else {
                    return@ReplierChannel ArrayList<WatermarkStyleItemViewData>()
                }
            }
            R.string.pitcure_edtior_text_watermark_exclusive_memory -> getMomeryStyleListViewData()
            else -> null
        }

        launch {
            viewData?.forEach {
                updateStyleItemStatus(it.styleId, checkMaterialDownloadedStatus(it.styleId))
            }
        }
        viewData
    }

    private val actionTopicsRep = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            if (arg) registerActionTopics() else unregisterActionTopics()
        }
    }

    private val layoutIsLandscapeRep = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            isLayoutLandscape = arg
        }
    }

    private val loadRestrictStyleListRep = object : EmptyReplier<Boolean>() {
        override fun onEmptyReply(): Boolean {
            val dataList = loadRestrictStyleListViewDataSync()
            return (dataList?.isNotEmpty() == true)
        }
    }

    private val isStyleInvalidRep = object : StringReplier<Boolean>() {
        override fun onSingleReply(styleId: String): Boolean {
            return if (invalidRestrictStyleIds.contains(styleId)) {
                GLog.w(TAG, LogFlag.DL, "[isStyleInvalidRep] restrict is overdue")
                true
            } else if (isRestrictStyleListViewDataAdded && isRestrictStyle(styleId)
                && (restrictStyleListViewData.find { it.styleId == styleId } == null)
            ) {
                //限定水印下线之前，选中了限定水印，限定水印下线后清除相册缓存重新进入相机水印设置页的场景，if判断会失效，故增加else if判断
                GLog.w(TAG, LogFlag.DL, "[isStyleInvalidRep] restrictStyleListViewData not contains styleId")
                true
            } else false
        }
    }

    private val updateStyleRep = object : UnitReplier<String>() {
        override fun onSingleReply(arg: String) {
            updateOperatorByStyleId(arg)
        }
    }

    /**
     * 操作栏的完成按钮点击回调
     */
    private val confirmRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            notifyConfirm(arg)
        }
    }

    /**
     * 操作栏的取消按钮点击回调
     */
    private val cancelRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            notifyCancel(arg)
        }
    }

    /**
     * 自动化测试回调
     */
    private val cameraWatermarkAutoTestRep = object : UnitReplier<String>() {
        override fun onSingleReply(arg: String) {
            val watermarkCameraAutoTestUtil = WatermarkCameraAutoTestUtil(app)
            loadRestrictStyleListViewDataSync()?.let {
                watermarkCameraAutoTestUtil.getAllPhotoAndVideoListViewData(
                    watermarkStyleLoader,
                    isFromCameraEditType,
                    it,
                    getAllVideoListViewData()
                ) {
                    watermarkCameraAutoTestUtil.sendAllStyleDataToCamera(
                        arg,
                        watermarkStyleLoader,
                        it,
                        ::getMaterialEntitys,
                        ::checkMaterialDownloadedStatus,
                        ::getUriStringList
                    )
                }
            }
        }
    }

    /**
     *  水印祝福语标题Map，用于小布送福签的文本设置控件标题。
     */
    private val styleGreetingTitleMap = mutableMapOf<String, String>()

    /**
     * 是否是从下载水印样式传输
     * 当下载完后，将此值通知给section用来判断是否需要自动获取type焦点
     */
    private var isFromWatermarkDownloadLoader = false

    private val watermarkUiBeanReq = object : UnitReplier<WatermarkMasterUIBean>() {

        @Suppress("WhenExpressionFormattingRule")
        override fun onSingleReply(arg: WatermarkMasterUIBean) {
            GLog.d(TAG, LogFlag.DF) { "watermarkUiBeanReq onSingleReply ${arg.id}" }
            when (arg.id) {
                WatermarkMasterUiBeanId.CHECK_PRIVACY_AUTHORIZE -> checkPrivacyAuthorized(true)
                WatermarkMasterUiBeanId.PRIVACY_DIALOG_POSITIVE_CLICK -> notifyClickedPrivacyDialogPositiveBtn()
                WatermarkMasterUiBeanId.ON_EDITOR_CONFIG_CHANGE -> {
                    isFromWatermarkDownloadLoader = false
                    updateUIState()
                }
                WatermarkMasterUiBeanId.INIT_DATA -> initListViewData()
                WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED -> onTypeSelected(arg.selectTypeId)
                WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_SELECTED -> onStyleSelected(arg.watermarkStyleId)
                WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_MASK_CLICK -> onStyleMaskClick(arg.watermarkStyleId, arg.watermarkStyleStatus)
                WatermarkMasterUiBeanId.WATERMARK_CAMERA_FINISH -> watermarkCameraFinish()
                WatermarkMasterUiBeanId.ON_PRIVACY_WATERMARK_STYLE_SELECTED -> onPrivacyWatermarkStyleSelected(arg.privacyLastSelectPosition)
                WatermarkMasterUiBeanId.CHANGE_PRIVACY_WATERMARK_CUSTOM_INFO -> changePrivacyWatermarkCustomInfo(arg.privacyContentText)
                WatermarkMasterUiBeanId.UPDATE_CURRENT_WATERMARK_INFO -> updateCurrentWatermarkInfo(arg.currentWatermarkInfo)
                WatermarkMasterUiBeanId.BROADCAST_CURRENT_SELECTED_STYLE -> sendStyleDataToCameraIfNeed(
                    arg.watermarkStyleId,
                    arg.videoStyleId,
                    arg.needMergeUserConfig
                )

                WatermarkMasterUiBeanId.PERSONALIZED_EDIT_DONE -> onPersonalizedEditDone(arg.applyStyleId)
                WatermarkMasterUiBeanId.CAMERA_PREVIEW_SIZE_CHANGE -> onCameraPreviewSizeChange(arg.cameraPreviewSize, arg.appUiConfig)
                WatermarkMasterUiBeanId.REMOVE_WATERMARK -> removeWatermark()
                else -> GLog.d(TAG, LogFlag.DF) { "watermarkUiBeanReq onSingleReply $arg" }
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        registerAndSubscribeTopic()
        if (isFromCameraEditType) {
            bindCameraAidl()
            AppScope.launch(Dispatchers.IO) {
                MarketNameInfo.initMarketNameInfo(isRealmeBrand(), ConfigAbilityWrapper.getBoolean(IS_REGION_CN))
            }
        }
        init()
    }

    override fun onResume() {
        super.onResume()
        notifyRefreshRestrictWatermarkStyle(currentSeriesId)
    }

    /**
     * 注册Action话题
     */
    private fun registerActionTopics() {
        vmBus?.apply {
            register(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            register(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
        }
    }

    /**
     * 反注册Action话题
     */
    private fun unregisterActionTopics() {
        vmBus?.apply {
            unregister(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            unregister(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        watermarkStyleLoader = null
        materialStrategy.destroy()
        restrictStrategy.destroy()
        unsubscribeAndUnregisterTopic()
        watermarkPreviewDrawer?.closeDrawer()
        if (isFromCameraEditType) {
            app.unbindService(cameraWatermarkServiceConnection)
        }
    }

    private fun init() {
        val imagePack = vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)
        if (imagePack?.watermarkInfo == null) {
            vmBus?.subscribeT(TOPIC_IMAGE_PACK, imagePackObserver)
        } else {
            onImagePackChange(imagePack)
        }

        if (isPrivacyWatermarkEditType.not()) {
            // 首次进入水印编辑页，判断是否需要弹框确认地理位置权限
            checkPrivacyAuthorized(false)
        }
    }

    /**
     * 旧水印需要更新pattern为PATTERN_AI_MASTER，使得添加水印的时候属于AI大师水印添加样式
     */
    private fun updateParamsPattern() {
        if (originalPattern == WatermarkPattern.PATTERN_NORMAL || originalPattern == WatermarkPattern.PATTERN_FRAME) {
            originalWatermarkInfo = originalWatermarkInfo?.copy(
                params = originalWatermarkInfo?.params?.copy(pattern = WatermarkPattern.PATTERN_AI_MASTER)
            )
        }
    }

    private fun onImagePackChange(imagePack: EditingImagePack) {
        vmBus?.unsubscribe(TOPIC_IMAGE_PACK, imagePackObserver)
        originalWatermarkInfo = imagePack.watermarkInfo
        originalPattern = originalWatermarkInfo?.params?.pattern
        updateParamsPattern()
        currentWatermarkInfo = originalWatermarkInfo
        updatePreWatermarkPattern(currentWatermarkInfo)
        vmBus?.get<PhoneInfo>(TopicID.InputArguments.TOPIC_PHONE_INFO)?.also { phoneInfo = it }
        if (isPrivacyWatermarkEditType) {
            if (currentWatermarkInfo?.hasWatermark() == true) {
                // 进入隐私水印页面时，如果有水印，则需要先移除水印
                postOneIdleFrame {
                    addPrivacyWatermark()
                    vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                        it.watermarkInfo = currentWatermarkInfo?.copy()
                    }
                }
            } else {
                // 无水印场景：若是水印信息获取比较晚，这时需要再通知管线刷新一帧，确保能再sink出一帧获取无水印的图
                GLog.i(TAG, LogFlag.DL) { "has not get withoutWatermarkBitmap,send request to pipeline" }
                addPrivacyWatermark()
                editingVM.sessionProxy.invalidate()
                postOneIdleFrame {
                    vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                        it.watermarkInfo = currentWatermarkInfo?.copy()
                    }
                }
            }
        }

        // 从随身卡包隐私水印进入时不需要刷新样式和增加水印, 直接添加隐私水印
        if (isPrivacyWatermarkEditType.not()) {
            //进二级页去掉水印，在这里更新style和添加水印
            updateStyleAndAddWatermark()
            if (currentWatermarkInfo?.hasWatermark() == true) {
                // 进入水印页面后，将当前的水印效果入栈，避免进入三级页再返回这里时，水印效果丢失
                ensureWatermarkRemovedOnce(false)
                addWatermark()
            }
        }

        initStyleBlessTitle(currentWatermarkInfo?.aiWatermarkMasterParams?.styleId)
    }

    /**
     * 判断限定水印资源是否下载完整
     * 清除数据后进入，限定水印素材未下载完整，需要先验证，不完整不重新加载水印
     */
    private fun getRestrictWatermarkResourceIsComplete(style: WatermarkMasterStyle?): Boolean {
        style?.let {
            if (WatermarkMasterStyle.isRestrictStyle(it.styleId).not()) {
                return true
            }

            val unDownloadMaterial = getMaterialEntitys(it.styleId)
            val status = checkMaterialDownloadedStatus(it.styleId, unDownloadMaterial)
            if (status == ItemStatus.DEFAULT) {
                return true
            } else {
                GLog.d(
                    TAG, LogFlag.DL,
                    "[getRestrictWatermarkResourceIsComplete] restrict style resource is not complete, styleId:${it.styleId}" +
                            ", unDownloadMaterial:${unDownloadMaterial.map { material -> material.toString() }}"
                )
            }
        } ?: run {
            GLog.d(TAG, LogFlag.DL, "[getRestrictWatermarkResourceIsComplete] restrict style is null")
        }
        return false
    }

    private fun updateStyleAndAddWatermark() {
        if (preWatermarkPattern != WatermarkPattern.PATTERN_NO_WATERMARK) {
            currentWatermarkInfo?.aiWatermarkMasterParams?.styleId?.let { styleId ->
                val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: return
                ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
                    val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
                    val pair = loader.loadStyleSync(source)
                    //限定水印素材未下载完整，需要先验证，不完整不重新加载水印
                    if (getRestrictWatermarkResourceIsComplete(pair?.first).not()) {
                        GLog.d(TAG, LogFlag.DL, "[updateStyleAndAddWatermark] restrict style resource is not complete, styleId:$styleId")
                        return
                    }
                    //先更新成当前水印的watermarkMasterStyle
                    currentWatermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle?.let {
                        pair?.second?.updateWatermarkStyle(it, isFromCameraEditType)
                    }
                    //复制一个新的currentWatermarkInfo，避免对当前watermarkMasterStyle修改后不保存，但是实际上watermarkMasterStyle被修改了
                    val aiWatermarkMasterParams =
                        pair?.second?.edit()?.toJsonObject()?.let {
                            AiWatermarkMasterParams(
                                styleId = styleId, styleJsonObject = it,
                                watermarkMasterStyle = currentWatermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle?.deepCopy()
                            )
                        }
                    currentWatermarkInfo = currentWatermarkInfo?.copy(aiWatermarkMasterParams = aiWatermarkMasterParams)
                    //再将缓存中style更新成当前水印的watermarkMasterStyle
                    currentWatermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle?.let {
                        pair?.second?.updateWatermarkStyle(it, isFromCameraEditType)
                    }
                }
            }
        } else {
            //如果是无水印照片进入水印编辑页，并且有用户操作记录，直接应用上最后一次用户操作记录的水印样式
            WatermarkStyleUserConfigLoaderImpl.getInstance().queryUserConfig(
                if (isFromCameraEditType) {
                    LAST_SAVE_CAMERA_STYLE_ID_KEY
                } else {
                    LAST_SAVE_STYLE_ID_KEY
                }, isFromCameraEditType
            )?.also { config ->
                config.styleId?.let { styleId ->
                    if (shouldAddWatermarkWithConfig(styleId)) {
                        addWatermarkForStyleSync(styleId, forceUseUserConfig = true)
                    }
                }
            }
        }
    }

    private fun shouldAddWatermarkWithConfig(styleId: String): Boolean {
        //一个周期内的去水印需要记忆无，此时不需要走用户样式记忆
        if (styleId == TYPE_STYLE_NONE) {
            return false
        }
        val dataSource = vmBus?.get<DataSource>(TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE)
        val item = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(dataSource?.filePath)) ?: let {
            GLog.e(TAG, "updateUIState getLocalMediaItem result is null,filePath=${PathMask.mask(dataSource?.filePath)}")
            null
        }
        val isSupportFormatForHassel = isSupportFormatForHassel(item)
        val isSupportAddHasselOrFrame = currentWatermarkInfo?.isSupportAddHasselOrFrame(app) == true
        val deviceNameValid = currentWatermarkInfo?.device?.deviceName?.isNotBlank() == true
        val shootingInfoValid = currentWatermarkInfo?.content?.shootingInfo.isValid()
        return when (WatermarkMasterStyle.getStyleType(styleId)) {
            WatermarkMasterStyle.TYPE_HASSEL_FRAME,
            WatermarkMasterStyle.TYPE_HASSEL_TEXT -> {
                if ((currentWatermarkInfo?.hasselWatermarkEditable() != true) || isSupportFormatForHassel.not() ||
                    isSupportAddHasselOrFrame.not()) {
                    return false
                } else true
            }
            WatermarkMasterStyle.TYPE_RESTRICT -> {
                if (deviceNameValid.not() || shootingInfoValid.not() || isSupportFormatForHassel.not() || isSupportAddHasselOrFrame.not()) {
                    return false
                }
                ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
                    val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: let {
                        GLog.e(TAG, LogFlag.DL, "[shouldAddWatermarkWithConfig] styleId:$styleId, styleSource is null")
                        return false
                    }
                    val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
                    val pair = loader.loadStyleSync(source)
                    if (pair?.first?.isWithinTheValidTime(Date()) == false) {
                        return false
                    }

                    // 当前设备IPU能力是否支持该款水印，不支持则不添加水印
                    if (pair?.first?.isIpuSupportFeature(isFromCameraEditType) == false) { return false }

                    // 水印是否在当前发布的系列的样式列表中，不是则表示限定水印系列已下线不添加水印
                    val seriesList = app.getAppAbility<IRestrictWatermarkDownloadAbility>()?.use { it.getAllPublishedSeries() } ?: return false
                    val styleIds = seriesList.joinToString(COMMA) { it.styleIds }
                    if (styleIds.contains(styleId).not()) { return false }
                }
                return true
            }

            WatermarkMasterStyle.TYPE_REALME_BRAND,
            WatermarkMasterStyle.TYPE_PERSONALIZE_FRAME -> {
                if (deviceNameValid.not() || shootingInfoValid.not() || isSupportFormatForHassel.not() || isSupportAddHasselOrFrame.not()) {
                    return false
                } else true
            }

            WatermarkMasterStyle.TYPE_INVALID -> false

            else -> true
        }
    }

    private fun notifyConfirm(callback: ExitPageListener) {
        trackWatermarkData(RESULT_TYPE_SAVE)
        if (currentWatermarkInfo?.hasWatermark() == false) {
            WatermarkStyleUserConfigLoaderImpl.getInstance().resetLastUserConfig()
        }
        if (WatermarkInfo.isContentChanged(originalWatermarkInfo, currentWatermarkInfo)) {
            vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                GLog.d(
                    TAG, LogFlag.DL, "[notifyConfirm] watermarkInfo contentChanged, exit true, " +
                            "currentWatermarkInfo:$currentWatermarkInfo"
                )
                it.watermarkInfo = currentWatermarkInfo?.copy()
            }
            callback.onExitPage(isConfirm = true)
        } else {
            GLog.d(TAG, LogFlag.DL, "[notifyConfirm] watermarkInfo not change, exit false")
            exitPageWithCancel(callback)
        }
    }

    private fun notifyCancel(callback: ExitPageListener) {
        exitPageWithCancel(callback)
    }

    private fun exitPageWithCancel(callback: ExitPageListener) {
        trackWatermarkData(RESULT_TYPE_EXIT)

        //退出则将修改过后的水印样式改回原来的水印样式还原
        if (originalPattern != originalWatermarkInfo?.params?.pattern) {
            originalWatermarkInfo = originalWatermarkInfo?.copy(
                params = originalPattern?.let { originalWatermarkInfo?.params?.copy(pattern = it) })
        }
        vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
            it.watermarkInfo = originalWatermarkInfo?.copy()
        }
        callback.onExitPage(isConfirm = false)
        // 退出页面前，如果有修改，则需要把水印加回去（因为首次修改会先去水印）
        restoreWatermark()
    }


    /**
     * 上报水印数据
     */
    private fun trackWatermarkData(resultType: Int) {
        val dataSource = vmBus?.get<DataSource>(TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE)
        WatermarkTrackHelper.trackWatermarkData(
            resultType,
            currentWatermarkInfo,
            0,
            dataSource?.filePath ?: TextUtil.EMPTY_STRING
        )
    }

    /**
     * 更新水印模式
     */
    private fun updatePreWatermarkPattern(watermarkInfo: WatermarkInfo?) {
        preWatermarkPattern = if ((watermarkInfo?.hasWatermark() == false) || (watermarkInfo == null)) {
            WatermarkPattern.PATTERN_NO_WATERMARK
        } else {
            watermarkInfo.params?.pattern
        }
    }

    private fun checkPrivacyAuthorized(isOnlyCheckPrivacy: Boolean) {
        val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
        if (isOnlyCheckPrivacy) {
            bean.needShowPrivacyDialog = isPrivacyAuthorized().not()
        } else {
            bean.needShowPrivacyDialog = isNeedPrivacyAuthorized() && isPrivacyAuthorized().not()
            if (bean.needShowPrivacyDialog) {
                bean.id = WatermarkMasterUiBeanId.CHECK_PRIVACY_AUTHORIZE
                watermarkUiPub?.publish(bean)
            } else {
                downloadAllMaterialAndWatermark(null)
            }
        }
    }

    /**
     * 全量下载本地所有水印素材和限定水印
     *
     * @param params 照片位置信息
     */
    private fun downloadAllMaterialAndWatermark(locationParams: Pair<WatermarkAddressBean?, String?>?) {
        checkDownloadAllSupportability { downloadMap ->
            GLog.i(
                TAG,
                LogFlag.DL,
                "[downloadAllMaterialAndWatermark] locationParams: $locationParams, downloadMapSize: ${downloadMap.size}"
            )

            // 记录等待同步下载状态的样式集合，有的样式由于与其他样式下载素材相同，所以不需要下载，等待其他样式下载时直接同步下载状态
            val waitingSynchronousDownloadStatusStyleMap = ConcurrentHashMap<String, ArrayList<String>>()
            downloadMap.forEach { (styleId, entityList) ->
                // 字体可以复用，如果有重复下载可以移除，只下载一次
                val entityListTemp = removeOrAddFontUnDownloadEntityListTemp(styleId, entityList, waitingSynchronousDownloadStatusStyleMap)
                if (entityListTemp.isNotEmpty()) {
                    downloadLoader.downloadMaterial(
                        entityListTemp,
                        newMateriaDownloadListener(false, styleId, entityListTemp, waitingSynchronousDownloadStatusStyleMap)
                    )
                }
            }
            // 当条件都满足后，尝试下载限定水印，限定水印依赖照片位置解析结果
            if (locationParams == null) {
                AppScope.launch(Dispatchers.Main) {
                    val location = async { loadLocation() }
                    updateRestrictAreaCode(location.await())
                    downloadRestrictWatermark()
                }
            } else {
                updateRestrictAreaCode(locationParams)
                downloadRestrictWatermark()
            }
        }
    }

    /**
     * 检查全量下载条件
     *
     * @param callback 回调触发下载，仅条件都满足时，才会回调
     */
    private fun checkDownloadAllSupportability(callback: (downloadMap: MutableMap<String, MutableList<RestrictWatermarkResourceEntry>>) -> Unit) {
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
            val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
            val isNeedRestrictStyle = isFromCameraEditType.not()
                    && RestrictButtonConfigUtils.isOnLineTime().not()
            loader.loadBuiltInAllStyles(
                AppScope,
                isFromCameraEditType,
                ConfigAbilityWrapper.getBoolean(IS_OPPO_BRAND),
                isRealmeBrand(),
                ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND),
                ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not(),
                isNeedRestrictStyle,
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)
            ) { styleMap ->
                // 需要下载素材的水印样式合集
                val downloadMap = mutableMapOf<String, MutableList<RestrictWatermarkResourceEntry>>()
                styleMap.keys.forEach {
                    val entityList = getMaterialEntitys(it.styleId)
                    val status = checkMaterialDownloadedStatus(it.styleId, entityList)
                    updateStyleItemStatus(it.styleId, status)
                    if (status != ItemStatus.READY_LOAD) return@forEach

                    downloadMap[it.styleId] = entityList
                }
                // 没有需要下载的素材，并且已经非首次限定水印检查，则需要静默下载
                val needSilence = downloadMap.isEmpty() && isRestrictWatermarkDownloaded
                materialStrategy.checkSupportability(
                    app,
                    { isSupport ->
                        GLog.i(TAG, LogFlag.DL) { "[checkDownloadAllSupportability] isSupport: $isSupport" }
                        materialStrategy.destroy()
                        if (isSupport) callback.invoke(downloadMap)
                    },
                    { action ->
                        GLog.i(TAG, LogFlag.DL) {
                            "[checkDownloadAllSupportability] action: $action, needSilence: $needSilence, isDestroyed: $isDestroyed"
                        }
                        if (needSilence || isDestroyed) {
                            materialStrategy.destroy()
                        } else {
                            launch(Dispatchers.UI) { vmBus?.notifyOnce(TopicID.Notification.TOPIC_NOTIFICATION_ACTION, action) }
                        }
                    }
                )
            }
        }
    }

    /**
     * 检查水印素材下载条件，条件都满足后，执行素材的下载
     *
     * @param needSilence 是否需要静默下载。true-静默下载，false-正常下载。静默下载则不会有异常提示
     * @param styleId 需下载素材的水印样式id
     */
    private fun singleDownloadMaterial(needSilence: Boolean, styleId: String) {
        var entityList = getMaterialEntitys(styleId)
        if (checkMaterialDownloadedStatus(styleId, entityList) == ItemStatus.DEFAULT) {
            updateStyleItemStatus(styleId, ItemStatus.DEFAULT)
            return
        }
        entityList = removeOrAddFontUnDownloadEntityListTemp(styleId, entityList)
        materialStrategy.checkSupportability(
            app,
            { isSupport ->
                GLog.i(TAG, LogFlag.DL, "[singleDownloadMaterial] isSupport: $isSupport")
                materialStrategy.destroy()
                if (isSupport) downloadLoader.downloadMaterial(entityList, newMateriaDownloadListener(needSilence, styleId, entityList))
            },
            { action ->
                GLog.i(TAG, LogFlag.DL, "[singleDownloadMaterial] action: $action, needSilence: $needSilence, isDestroyed: $isDestroyed")
                if (needSilence || isDestroyed) {
                    materialStrategy.destroy()
                } else {
                    launch(Dispatchers.UI) { vmBus?.notifyOnce(TopicID.Notification.TOPIC_NOTIFICATION_ACTION, action) }
                }
            }
        )
    }

    private fun newMateriaDownloadListener(
        needSilence: Boolean,
        styleId: String,
        entityList: MutableList<RestrictWatermarkResourceEntry>,
        waitingSynchronousDownloadStatusStyleMap: MutableMap<String, ArrayList<String>>? = null
    ): MateriaDownloadListener {
        return object : MateriaDownloadListener(styleId, entityList) {
            override fun onStart() {
                GLog.d(TAG, LogFlag.DL, "[newMateriaDownloadListener][onStart] styleId: ${this.styleId}")
                updateStyleItemStatus(this.styleId, ItemStatus.LOADING)
                synchronousStyleDownloadStatus(this.styleId, waitingSynchronousDownloadStatusStyleMap, ItemStatus.LOADING)
            }

            override fun onProgress(progress: Int) {
                if (GProperty.DEBUG_NETWORK) GLog.d(TAG, LogFlag.DL, "[onProgress] styleId: ${this.styleId}, progress: $progress")
                updateStyleItemProgress(this.styleId, progress)
                synchronousStyleDownloadStatus(this.styleId, waitingSynchronousDownloadStatusStyleMap, progress = progress)
            }

            override fun onSuccess() {
                GLog.d(TAG, LogFlag.DL, "[onSuccess] styleId: ${this.styleId}")
                updateStyleItemStatus(this.styleId, ItemStatus.DEFAULT)
                synchronousStyleDownloadStatus(this.styleId, waitingSynchronousDownloadStatusStyleMap, ItemStatus.DEFAULT)
            }

            override fun onFailure(result: ProcessResult) {
                GLog.e(TAG, LogFlag.DL, "[newMateriaDownloadListener][onFailure] styleId: ${this.styleId}, result: $result")
                updateStyleItemStatus(this.styleId, ItemStatus.READY_LOAD)
                synchronousStyleDownloadStatus(this.styleId, waitingSynchronousDownloadStatusStyleMap, ItemStatus.READY_LOAD)
                // 下载失败的字体，移除临时记录，重新下载
                this.entityList.forEach { entity ->
                    if (entity.resType == FONT.value) {
                        fontUnDownloadEntityListTemp.removeAll {
                            // md5、resId、resUrl任一属性相同，则认为是同一个字体文件
                            (entity.resMd5.isNotBlank() && (it.first.resMd5 == entity.resMd5)) ||
                                    (it.first.resId == entity.resId) || (it.first.resUrl == entity.resUrl)
                        }
                    }
                }
                if (needSilence.not() && isDestroyed.not()) {
                    launch(Dispatchers.UI) {
                        val msgStrId = when (result.code) {
                            ProcessResultCode.FAIL_DOWNLOAD_WIFI_ONLY,
                            ProcessResultCode.FAIL_DOWNLOAD_RESULT_ERROR -> R.string.picture_editor_toast_download_fail_and_check_network

                            else -> R.string.picture_editor_toast_download_fail
                        }
                        vmBus?.notifyOnce(TopicID.Notification.TOPIC_NOTIFICATION_ACTION, NotificationAction.ToastAction(msgStrId))
                    }
                }
            }
        }
    }

    /**
     * 同步样式下载状态
     * 部分样式只下载字体素材且已被其他样式下载，则在此处进行同步下载状态
     * @param styleId 水印样式id
     * @param waitingSynchronousMap 记录等待同步下载的样式集合
     * @param status 下载状态
     * @param progress 下载进度
     */
    private fun synchronousStyleDownloadStatus(
        styleId: String,
        waitingSynchronousMap: MutableMap<String, ArrayList<String>>?,
        status: ItemStatus? = null,
        progress: Int? = null
    ) {
        waitingSynchronousMap?.let { map ->
            map[styleId]?.forEach {  id ->
                status?.let { updateStyleItemStatus(id, it) } ?: run { progress?.let { updateStyleItemProgress(id, it) } }
            }
        }
    }

    /**
     * 移除或添加临时字体未下载记录
     * 通过记录避免重复下载相同的字体文件，造成字体下载失败
     * 如果样式只需要下载字体且已被其他样式下载，则只记录进waitingSynchronousDownloadStatusStyleMap，等待其他样式下载完成后同步下载状态
     * @param styleId 水印样式id
     * @param entityList 水印下载资源
     * @param waitingSynchronousDownloadStatusStyleMap 记录等待同步下载的样式集合
     */
    private fun removeOrAddFontUnDownloadEntityListTemp(
        styleId: String,
        entityList: MutableList<RestrictWatermarkResourceEntry>,
        waitingSynchronousDownloadStatusStyleMap: MutableMap<String, ArrayList<String>>? = null
    ): MutableList<RestrictWatermarkResourceEntry> {
        var foundStyleId: String? = null
        val result = entityList.filter { entity ->
            if (entity.resType == FONT.value) {
                // 如果md5、resId、resUrl任一属性相同，则认为是同一个字体文件，不重复下载，将其计入waitingSynchronousDownloadStatusStyleMap
                val foundEntity = fontUnDownloadEntityListTemp.find {
                    (entity.resMd5.isNotBlank() && (it.first.resMd5 == entity.resMd5)) ||
                            (it.first.resId == entity.resId) || (it.first.resUrl == entity.resUrl)
                }
                if (foundEntity == null) {
                    fontUnDownloadEntityListTemp.add(Pair(entity, styleId))
                    waitingSynchronousDownloadStatusStyleMap?.put(styleId, ArrayList())
                    true
                } else {
                    foundStyleId = foundEntity.second
                    false
                }
            } else {
                true
            }
        }.toMutableList()

        // 如果result为空，说明该样式只需要下载字体，且已被其他样式下载，则记录进waitingSynchronousDownloadStatusStyleMap
        if (foundStyleId != null && result.isEmpty()) {
            waitingSynchronousDownloadStatusStyleMap?.let { it[foundStyleId]?.add(styleId) }
        }

        return result
    }

    /**
     * 当相册清除数据是，限定水印下载的素材会被清除，再打开已经添加了限定水印的图片，需要校验一下当前水印元素是否缺失，
     * 如果缺失，需要根据新的水印JSON中的元素进行重新绘制水印
     * 判断方法：如果缺失素材，再合并缓存水印时不会修改bitmapSourceType，所以可以根据bitmapSourceType来判断是否缺失
     */
    private fun isCurrentRestrictWatermarkResourceMissing(): Boolean {
        val style = currentWatermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle
        style?.background?.let {
            if (it.backgroundType == BG_TYPE_URL) {
                return true
            } else if (it.backgroundType == BG_TYPE_TEXTURE) {
                if (File(it.backgroundPath).exists().not()) {
                    return true
                }
            }
        }
        style?.getImageElements()?.forEach {
            if (it.content?.bitmapSourceType == BITMAP_TYPE_URL) {
                return true
            }
        }
        return false
    }

    private fun updateStyleItemStatus(styleId: String, status: ItemStatus) {
        GLog.d(TAG, LogFlag.DL) { "[updateStyleItemStatus] styleId: $styleId, status: $status" }
        launch(Dispatchers.UI) {
            val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)?.copy() ?: WatermarkMasterUIBean()

            bean.id = WatermarkMasterUiBeanId.UPDATE_STYLE_ITEM_STATUS
            bean.watermarkStyleId = styleId
            bean.watermarkStyleStatus = status
            watermarkUiPub?.publish(bean)
        }
    }

    private fun updateStyleItemProgress(styleId: String, progress: Int) {
        launch(Dispatchers.UI) {
            val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)?.copy() ?: WatermarkMasterUIBean()

            bean.id = WatermarkMasterUiBeanId.UPDATE_STYLE_ITEM_LOADING_PROGRESS
            bean.watermarkStyleId = styleId
            bean.watermarkStyleProgress = progress.toFloat()
            watermarkUiPub?.publish(bean)
        }
    }

    private fun updateRestrictAreaCode(params: Pair<WatermarkAddressBean?, String?>?) {
        var areaCode = TextUtil.EMPTY_STRING

        val countryCode = params?.first?.mCountryCode
        val localityName = params?.first?.mLocality
        val localityId = if ((countryCode != null) && (localityName != null)) {
            LocaleUtils.getLocalityIdByName(countryCode, localityName)
        } else null
        if (countryCode != null) areaCode += countryCode
        if (localityId != null) {
            if (areaCode.isNotEmpty()) areaCode += TextUtil.STRIKE
            areaCode += "$localityId"
        }
        GLog.d(TAG, LogFlag.DL) { "[updateRestrictAreaCode] city info: [$countryCode, $localityName], areaCode: $areaCode" }
        /* 与产品SE对齐，城市水印暂不上线。先屏蔽下载城市水印逻辑，不设置地理位置信息。
         * if (areaCode.isNotEmpty()) restrictDownloadLoader.areaCode = areaCode */
    }

    /**
     * 检查限定水印下载条件，条件都满足后，执行水印下载
     */
    private fun downloadRestrictWatermark() {
        // 位置更新后，开始授权静默下载限定水印
        restrictStrategy.checkSupportability(
            app,
            { isSupport ->
                GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] isSupport: $isSupport" }
                restrictStrategy.destroy()
                if (isSupport) isRestrictWatermarkDownloaded = true
                loadRestrictStyleListViewDataAsync { dataList ->
                    notifyRefreshRestrictWatermarkStyle(currentSeriesId)
                    isFromWatermarkDownloadLoader = true
                    updateUIState()
                    dataList?.forEach { data ->
                        singleDownloadMaterial(true, data.styleId)
                    }
                }
            },
            { action ->
                GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] action: $action" }
            }
        )
    }

    private fun notifyRestrictTypeVisibilityChanged(needShow: Boolean) {
        launch(Dispatchers.UI) {
            val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)
                ?: WatermarkMasterUIBean()
            GLog.i(
                TAG,
                LogFlag.DL
            ) { "[notifyRestrictTypeVisibilityChanged] lastNeedShow: ${bean.needShowRestrictType}, needShow: $needShow" }
            bean.id = WatermarkMasterUiBeanId.RESTRICT_TYPE_VISIBILITY_CHANGED
            bean.needShowRestrictType = needShow
            bean.watermarkTypeListViewData = getWatermarkTypeListViewData(true)
            watermarkUiPub?.publish(bean)
        }
    }

    private fun notifyClickedPrivacyDialogPositiveBtn() {
        GLog.d(TAG, LogFlag.DL) { "[notifyClickedPrivacyDialogPositiveBtn]" }
        app.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            it.authorizePrivacy(AUTHORIZE_LOCATION_RESOLVE)
        }
        NetworkPermissionManager.openNetwork(app)
        AppScope.launch(Dispatchers.Main) {
            val location = async { loadLocation() }
            val params = location.await()
            downloadAllMaterialAndWatermark(params)
            refreshAddressByLatLngArray(params)
        }
        val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
        bean.needShowPrivacyDialog = isPrivacyAuthorized().not()
    }

    /**
     * 根据经纬度联网获取地理位置
     * 将地理位置按 “XX省，XX市” 格式返回
     */
    private suspend fun loadLocation(): Pair<WatermarkAddressBean?, String> = suspendCancellableCoroutine {
        val latLngArray = currentWatermarkInfo?.content?.locationArray ?: doubleArrayOf(Double.NaN, Double.NaN)
        if (GPS.isLatLngValid(latLngArray)) {
            CameraDetailsHelper.resolveAddressWithCamera(app, latLngArray) { cameraAddress, address ->
                if (it.isActive) {
                    /**
                     * 因为 resolveAddress 内部处理问题，可能会多次回调,
                     * 所以此处要对 isActive 进行判断后才 resume，避免多次resume导致异常。
                     */
                    val bean = WatermarkLocationParser.convertToWatermarkAddressBean(cameraAddress)
                    it.resume(bean to address)
                }
                CameraDetailsHelper.pause()
            }
        } else {
            if (it.isActive) {
                it.resume(null to TextUtil.EMPTY_STRING)
            }
        }
    }

    /**
     * 用户在水印编辑页同意联网获取地理位置权限后，需要将水印信息中的location更新
     * 该方法只会在权限弹框同意后调用，所以需要将原始的originalWatermarkInfo也同步更新
     * @param params  first:ipu水印位置信息， second:经纬度通过联网转换的地理位置字符串
     */
    private fun refreshAddressByLatLngArray(params: Pair<WatermarkAddressBean?, String?>?) {
        GLog.d(TAG, LogFlag.DL) { "refreshAddressByLatLngArray ${PathMask.mask(params?.second)}" }
        if (params?.second?.isNotBlank() == true) {
            originalWatermarkInfo = originalWatermarkInfo?.copy(
                content = originalWatermarkInfo?.content?.copy(
                    location = params.second,
                    cameraAddress = params.first
                )
            )
            vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                it.watermarkInfo = originalWatermarkInfo?.copy()
            }
            currentWatermarkInfo = currentWatermarkInfo?.copy(
                content = currentWatermarkInfo?.content?.copy(
                    location = params.second,
                    cameraAddress = params.first
                )
            )
        }
    }

    private fun registerAndSubscribeTopic() {
        vmBus?.apply {
            watermarkUiPub = registerDuplex(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN, watermarkUiBeanReq)
            personalizedDataPub = register(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_PERSONALIZED_DATA_TRANSFER)
            register(TOPIC_STYLE_VIEW_DATA, styleViewDataReq)
            register(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_CAMERA_AUTO_TEST, cameraWatermarkAutoTestRep)
            privacyStatsPub = register(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS)
            register(TopicID.WatermarkMaster.REPLY_TOPIC_WATERMARK_MASTER_ACTION_TOPICS, actionTopicsRep)
            register(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_LOAD_RESTRICT_STYLE_LIST, loadRestrictStyleListRep)
            register(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_IS_STYLE_INVALID, isStyleInvalidRep)
            register(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_UPDATE_OPERATOR_BY_STYLE_ID, updateStyleRep)
            register(TopicID.WatermarkMaster.TOPIC_WATERMARK_LAYOUT_LANDSCAPE, layoutIsLandscapeRep)
            restrictWatermarkSeriesStyleIdPub = register(TopicID.WatermarkMaster.TOPIC_RESTRICT_WATERMARK_SERIES_STYLE_ID)
            restrictWatermarkCurrentSeriesIdPub = register(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_CURRENT_SERIES_ID)
            addWatermarkLoadingPub = register(TopicID.WatermarkMaster.TOPIC_ADD_WATERMARK_LOADING)
        }
    }

    private fun unsubscribeAndUnregisterTopic() {
        vmBus?.apply {
            unregisterDuplexT(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN, watermarkUiBeanReq, watermarkUiPub)
            unregisterT(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_PERSONALIZED_DATA_TRANSFER, personalizedDataPub)
            unregister(TOPIC_STYLE_VIEW_DATA, styleViewDataReq)
            unregister(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_CAMERA_AUTO_TEST, cameraWatermarkAutoTestRep)
            unregisterT(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS, privacyStatsPub)
            unregister(TopicID.WatermarkMaster.REPLY_TOPIC_WATERMARK_MASTER_ACTION_TOPICS, actionTopicsRep)
            unregister(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_LOAD_RESTRICT_STYLE_LIST, loadRestrictStyleListRep)
            unregister(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_IS_STYLE_INVALID, isStyleInvalidRep)
            unregister(TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_UPDATE_OPERATOR_BY_STYLE_ID, updateStyleRep)
            unregister(TopicID.WatermarkMaster.TOPIC_WATERMARK_LAYOUT_LANDSCAPE, layoutIsLandscapeRep)
            unregisterT(TopicID.WatermarkMaster.TOPIC_RESTRICT_WATERMARK_SERIES_STYLE_ID, restrictWatermarkSeriesStyleIdPub)
            unregisterT(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_CURRENT_SERIES_ID, restrictWatermarkCurrentSeriesIdPub)
            unregisterT(TopicID.WatermarkMaster.TOPIC_ADD_WATERMARK_LOADING, addWatermarkLoadingPub)
        }
    }

    private fun initListViewData() {
        launch(Dispatchers.Main) {
            if (isPrivacyWatermarkEditType) {
                vmBus?.get<PrivacyDataStatus>(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS) ?: let {
                    privacyStatsPub?.publish(PrivacyDataStatus.LOADING)
                }
            }
            val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
            bean.id = WatermarkMasterUiBeanId.INIT_DATA

            loadAndProcessData(bean)
        }
    }

    private fun loadAndProcessData(bean: WatermarkMasterUIBean) {
        if (isRestrictStyle(currentWatermarkInfo?.aiWatermarkMasterParams?.styleId)) {
            val seriesList = app.getAppAbility<IRestrictWatermarkDownloadAbility>()?.use { it.getAllPublishedSeries() }
            val tempSeriesId = getSeriesIdByStyleId(currentWatermarkInfo?.aiWatermarkMasterParams?.styleId)

            currentSeriesId = seriesList?.find { it.seriesId == tempSeriesId }?.seriesId
                ?: seriesList?.firstOrNull()?.seriesId
            GLog.i(TAG, LogFlag.DL) { "[loadAndProcessData] currentSeriesId: $currentSeriesId" }
        } else {
            val series = app.getAppAbility<IRestrictWatermarkDownloadAbility>()?.getCurrentPublishedSeries()
            currentSeriesId = series?.seriesId
        }
        bean.seriesId = currentSeriesId

        // 发布当前系列Id
        currentSeriesId?.let { restrictWatermarkCurrentSeriesIdPub?.publish(it) }

        val dataList = loadRestrictStyleListViewDataSync()
        bean.needShowRestrictType = dataList?.isNotEmpty() == true
        if (!isPrivacyWatermarkEditType) {
            bean.watermarkTypeListViewData = getWatermarkTypeListViewData(bean.needShowRestrictType)
        }
        if (isCanUseRestrictWatermarkStyle) {
            bean.watermarkStyleListViewData = dataList
        }
        bean.watermarkStyleId = currentWatermarkInfo?.aiWatermarkMasterParams?.styleId
        bean.privacyWatermarkListViewData = getPrivacyWatermarkViewData()

        if (isRestrictStyle(bean.watermarkStyleId) && isCanUseRestrictWatermarkStyle) {
            val buttonId = getCurrentTypeButtonId(bean, currentSeriesId)
            bean.restrictTitle = currentRestrictWatermarkTitle
            bean.restrictDescription = currentRestrictWatermarkDesc
            bean.restrictDescription1 = currentRestrictWatermarkDescr1
            bean.restrictDescription2 = currentRestrictWatermarkDescr2
            bean.typeButtonId = buttonId
        }

        watermarkUiPub?.publish(bean)
        isFromWatermarkDownloadLoader = false
        GLog.i(TAG, LogFlag.DL) { "[loadAndProcessData] updateUIState" }
        updateUIState()
    }

    override fun updateUIState() {
        GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] updateUIState: ${restrictStyleListViewData.size}" }
        launch(Dispatchers.UI) {
            val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
            bean.id = WatermarkMasterUiBeanId.REFRESH_WATERMARK_TYPE_STATE
            bean.currentWatermarkInfo = currentWatermarkInfo
            bean.isSupportFormatForHassel ?: run {
                val dataSource = vmBus?.get<DataSource>(TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE)
                val item = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(dataSource?.filePath)) ?: let {
                    GLog.e(TAG, "updateUIState getLocalMediaItem result is null,filePath=${PathMask.mask(dataSource?.filePath)}")
                    null
                }
                val isSupportFormatForHassel = isSupportFormatForHassel(item)
                bean.isSupportFormatForHassel = isSupportFormatForHassel
            }
            bean.isFromWatermarkDownloadLoader = isFromWatermarkDownloadLoader
            watermarkUiPub?.publish(bean)
        }
    }

    /**
     * 是否支持哈苏水印的格式
     */
    private fun isSupportFormatForHassel(item: MediaItem?): Boolean {
        item ?: return true
        var isSupportFormat = true
        // 相机全景图模式拍摄的照片不支持哈苏水印，相册保持相同逻辑
        if (item.tagFlags and Constants.CameraMode.FLAG_PANORAMA == Constants.CameraMode.FLAG_PANORAMA) {
            isSupportFormat = false
        }
        return isSupportFormat
    }

    /**
     * 判断地理位置个人信息的使用是否已被授权
     * ability 为空，说明是外销，没有配置授权能力，返回true
     */
    private fun isPrivacyAuthorized(): Boolean {
        app.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            return it.isPrivacyAuthorized(AUTHORIZE_LOCATION_RESOLVE) ?: run {
                GLog.d(TAG, LogFlag.DF) { "isPrivacyAuthorized, isPrivacyAuthorized get null" }
                false
            }
        } ?: return true
    }

    /**
     * 图片带有地理位置水印，或者图片有拍摄地址信息（用于下载限定水印），则需要获取地理位置权限
     *
     * @return 是否需要联网获取地理位置权限
     */
    private fun isNeedPrivacyAuthorized(): Boolean {
        return currentWatermarkInfo?.params?.showLocation == true
    }

    /**
     * 通过styleId 查询系列id
     * */
    private fun getSeriesIdByStyleId(styleId: String?): String {
        return styleId.takeIf { isRestrictStyle(it) }
            ?.let {
                restrictStyleListViewData.firstOrNull { data -> data.styleId == it }?.seriesId
                    ?: RestrictWatermarkMasterStyleDataHelper.queryStyleEntityByStyleId(it)?.seriesId
            }.orEmpty()
    }

    /**
     * 通过系列id匹配buttonId
     * */
    private fun getCurrentTypeButtonId(bean: WatermarkMasterUIBean, seriesId: String?): String? {
        return bean.watermarkTypeListViewData?.find { it.buttonInfo?.seriesId == seriesId }?.buttonInfo?.buttonId
    }

    private fun getWatermarkTypeListViewData(showRestrictType: Boolean): MutableList<WatermarkTypeItemViewData> {
        val data = ArrayList<WatermarkTypeItemViewData>()
        ///查询限定水印按钮
        val typeButtonList = mutableListOf<RestrictWaterMarkTypeButtonEntry?>()
        typeButtonList.addAll(RestrictButtonConfigUtils.getAllWatermarkTypeButtonResources())

        val ids = ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_master_id_array)
        val texts = ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_master_text_array)

        // 照片是否支持当前水印，个性画框、限定水印、realme品牌、哈苏水印用此判断
        val isPhotoSupportWatermark = isPhotoSupportWatermark()

        for (i in ids.indices) {
            val watermarkTypeItemViewData = WatermarkTypeItemViewData(viewId = ids[i], textId = texts[i])
            if (ids[i] == HASSELBLAD) {
                // 当前机型为哈苏联名机型，或当前图片是哈苏机型拍摄的图片，都显示哈苏水印type按钮; realme 全系列屏蔽哈苏
                if ((isFeatureSupportHasselblad() || (currentWatermarkInfo?.isHasselDevice() == true)) && !isRealmeBrand()) {
                    watermarkTypeItemViewData.isSelectable = isPhotoSupportWatermark(true)
                    data.add(watermarkTypeItemViewData)
                }
            } else if (ids[i] == PRIVACY) {
                if (isSupportPrivacyWatermark()) {
                    data.add(watermarkTypeItemViewData)
                }
            } else if (ids[i] == REALME_BRAND) {
                if (isRealmeBrand()) {
                    watermarkTypeItemViewData.isSelectable = isPhotoSupportWatermark
                    data.add(watermarkTypeItemViewData)
                }
            }  else if (ids[i] == FRAME) {
                watermarkTypeItemViewData.isSelectable = isPhotoSupportWatermark
                data.add(watermarkTypeItemViewData)
            } else {
                data.add(watermarkTypeItemViewData)
            }
        }

        if (showRestrictType) {
            resetTypeViewList()
            val newData = typeButtonList.mapIndexed { index, button ->
                val viewId = restrictViewButtonMap.getOrPut(button?.typeButtonId) {
                    View.generateViewId()
                }
                val seriesId =
                    RestrictWatermarkMasterSeriesDataHelper.getSeriesIdByTypeButtonId(
                        button?.typeButtonId
                            ?: TextUtil.EMPTY_STRING
                    )
                val watermarkTypeItemViewData = WatermarkTypeItemViewData(
                    viewId = viewId,
                    text = button?.typeButtonTextId?.let { RestrictWatermarkMasterWordDataHelper.getWordTextByWordId(it) },
                    buttonInfo = ButtonInfo(
                        button?.typeButtonId, button?.typeButtonTextId,
                        seriesId ?: TextUtil.EMPTY_STRING
                    )
                )
                watermarkTypeItemViewData.isSelectable = isPhotoSupportWatermark
                RESTRICT_BUTTON_LIST.add(watermarkTypeItemViewData)
                RESTRICT_BUTTON_VIEW_ID_LIST.add(viewId)
                watermarkTypeItemViewData
            }
            data.addAll((if (data.size > 0) 1 else 0), newData)
        }
        return data
    }

    /**
     * 判断当前照片是否支持水印编辑
     * @param isHasselType 是否是哈苏水印类型
     * 1. 设备名称不能为空
     * 2. 拍摄信息不能为空
     * 3. 图片格式支持哈苏水印
     * 4. 图片支持添加画框和哈苏水印
     * 5. 是否支持哈苏水印编辑，非哈苏机型不支持
     * 哈苏水印需要全部支持，限定水印、个性画框、realme品牌需要支持前四项。
     */
    private fun isPhotoSupportWatermark(isHasselType: Boolean = false): Boolean {
        // 设备名称不能为空
        val deviceNameValid = currentWatermarkInfo?.device?.deviceName?.isNotBlank() == true

        // 拍摄信息不能为空
        val shootingInfoValid = currentWatermarkInfo?.content?.shootingInfo.isValid()

        val dataSource = vmBus?.get<DataSource>(TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE)
        val item = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(dataSource?.filePath)) ?: let {
            GLog.e(TAG, LogFlag.DL) { "updateUIState getLocalMediaItem result is null,filePath=${PathMask.mask(dataSource?.filePath)}" }
            null
        }
        // 图片格式支持哈苏水印
        val isSupportFormatForHassel = isSupportFormatForHassel(item)

        // 图片支持添加画框和哈苏水印
        val isSupportAddHasselOrFrame = currentWatermarkInfo?.isSupportAddHasselOrFrame(app) ?: false

        // 是否支持哈苏水印编辑，非哈苏机型不支持，只有哈苏水印类型需要
        val isHasselWatermarkEditable = currentWatermarkInfo?.hasselWatermarkEditable() ?: false

        val isPhotoSupportWatermark = deviceNameValid && shootingInfoValid && isSupportFormatForHassel && isSupportAddHasselOrFrame

        GLog.d(TAG, LogFlag.DL) {
            "[isPhotoSupportWatermark], isHasselType: $isHasselType, deviceNameValid: $deviceNameValid, shootingInfoValid: $shootingInfoValid, " +
                    "isSupportFormatForHassel: $isSupportFormatForHassel, isSupportAddHasselOrFrame: $isSupportAddHasselOrFrame, " +
                    "isHasselWatermarkEditable: $isHasselWatermarkEditable"
        }

        return if (isHasselType) {
            isPhotoSupportWatermark && isHasselWatermarkEditable
        } else {
            isPhotoSupportWatermark
        }
    }

    private fun resetTypeViewList() {
        RESTRICT_BUTTON_LIST.clear()
        RESTRICT_BUTTON_VIEW_ID_LIST.clear()
    }

    private fun getHasselStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInHasselStyles().map { it.first }
        val icons = if (isFromCameraEditType) {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_hassel_sketch_array)
        } else if (isLayoutLandscape) {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_hassel_sketch_land_array)
        } else {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_hassel_sketch_array)
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getVideoHasselTextStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInVideoHasselTextStyles().map { it.first }
        val icons = if (isFromCameraEditType) {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_hassel_text_sketch_array)
        } else if (isLayoutLandscape) {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_hassel_text_sketch_land_array)
        } else {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_hassel_text_sketch_array)
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getTextStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInTextStyles().map { it.first }
        val icons = if (isRealmeBrand()) {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_text_icon_realme_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_land_realme_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_realme_array)
            }
        } else {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_text_icon_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_array)
            }
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getVideoTextStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInVideoTextStyles().map { it.first }
        val icons = if (isRealmeBrand()) {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_text_icon_realme_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_land_realme_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_realme_array)
            }
        } else {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_text_icon_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_text_icon_array)
            }
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getFrameStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInPersonalStyles(
            isOppoBrand(),
            isRealmeBrand(),
            ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND),
            ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not(),
            ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK),
            isSameBrand
        ).map { it.first }
        var icons = getMasterSignStyleIconArray() + getRetroCameraStyleIconArray() + getFilmStyleIconArray()
        if (isSameBrand) {
            icons += getBrandStyleIconArray()
        }

        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getRealmeBrandStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val isSupportWatermarkMaster = ConfigAbilityWrapper.getBoolean(
            ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_WATERMARK_MASTER,
            defValue = false,
            expDefValue = false
        )
        val ids = WatermarkMasterStyle.getBuiltInRealmeBrandStyles(isSupportWatermarkMaster).map { it.first }
        val icons = if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN)) {
            if (isSupportWatermarkMaster) {
                if (isFromCameraEditType) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_realme_brand_array)
                } else if (isLayoutLandscape) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_brand_land_array)
                } else {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_brand_array)
                }
            } else {
                if (isFromCameraEditType) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_realme_brand_array_without_inspation)
                } else if (isLayoutLandscape) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_brand_land_array_without_inspation)
                } else {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_brand_array_without_inspation)
                }
            }
        } else {
            if (isSupportWatermarkMaster) {
                if (isFromCameraEditType) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_camera_realme_brand_array)
                } else if (isLayoutLandscape) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_realme_brand_land_array)
                } else {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_realme_brand_array)
                }
            } else {
                if (isFromCameraEditType) {
                    ResourceUtils.getResourceIdArrays(
                        app,
                        R.array.picture3d_editor_array_export_watermark_camera_realme_brand_array_without_inspation
                    )
                } else if (isLayoutLandscape) {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_realme_brand_land_array_without_inspation)
                } else {
                    ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_realme_brand_array_without_inspation)
                }
            }
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getRetroCameraStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInRetroCameraStyles(isRealmeBrand()).map { it.first }
        val icons = getRetroCameraStyleIconArray()
        return getWatermarkStyleItemViewData(ids, icons)
    }

    /**
     * 获取经典相机缩略图资源列表
     */
    private fun getRetroCameraStyleIconArray(): IntArray {
        return if (isRealmeBrand()) {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(
                    app,
                    R.array.picture3d_editor_array_watermark_camera_section_retro_camera_sketch_realme_array
                )
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(
                    app,
                    R.array.picture3d_editor_array_watermark_camera_retro_camera_sketch_realme_land_array
                )
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_retro_camera_sketch_realme_array)
            }
        } else {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_retro_camera_sketch_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_retro_camera_sketch_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_retro_camera_sketch_array)
            }
        }
    }

    private fun getFilmStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInFilmStyles(isRealmeBrand()).map { it.first }
        val icons = getFilmStyleIconArray()
        return getWatermarkStyleItemViewData(ids, icons)
    }

    /**
     * 获取复古胶卷系列缩略图资源列表
     */
    private fun getFilmStyleIconArray(): IntArray {
        return if (isRealmeBrand()) {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_film_sketch_realme_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_film_sketch_realme_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_film_sketch_realme_array)
            }
        } else {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_film_sketch_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_film_sketch_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_film_sketch_array)
            }
        }
    }

    private fun getMasterSignStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInMasterSignStyles(isRealmeBrand()).map { it.first }
        val icons = getMasterSignStyleIconArray()
        return getWatermarkStyleItemViewData(ids, icons)
    }

    /**
     * 获取大师印记缩略图资源列表
     */
    private fun getMasterSignStyleIconArray(): IntArray {
        return if (isRealmeBrand()) {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(
                    app,
                    R.array.picture3d_editor_array_watermark_camera_section_master_sign_sketch_realme_array
                )
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_master_sign_sketch_realme_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_master_sign_sketch_realme_array)
            }
        } else {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_master_sign_sketch_array)
            } else if (isLayoutLandscape) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_master_sign_sketch_land_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_master_sign_sketch_array)
            }
        }
    }

    private fun getBrandStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInBrandStyles(
            isOppoBrand(),
            ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND),
            ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not(),
            ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK),
        ).map { it.first }

        val icons = getBrandStyleIconArray()
        return getWatermarkStyleItemViewData(ids, icons)
    }

    /**
     * 获取品牌系列缩略图资源列表
     */
    private fun getBrandStyleIconArray(): IntArray {
        return if (isRealmeBrand()) {
            // realme没有该系列
            intArrayOf()
        } else if (isFromCameraEditType) {
            val arrayFirst = if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_brand_sketch_array_first)
            } else {
                intArrayOf()
            }
            val arrayLast = ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_section_brand_sketch_array_last)

            arrayFirst + arrayLast
        } else if (isLayoutLandscape) {
            val arrayFirst = if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_brand_sketch_land_array_first)
            } else {
                intArrayOf()
            }
            val arrayLast = ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_brand_sketch_land_array_last)
            arrayFirst + arrayLast
        } else {
            val arrayFirst = if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_brand_sketch_array_first)
            } else {
                intArrayOf()
            }
            val arrayLast = ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_brand_sketch_array_last)

            arrayFirst + arrayLast
        }
    }

    private fun getInspationStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInInspationStyles().map { it.first }
        val icons = if (isFromCameraEditType) {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_realme_inspration_array)
        } else {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_inspration_array)
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getRealmeMomentStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInRealmeMomentStyles().map { it.first }
        val icons = if (isFromCameraEditType) {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_realme_moment_array)
        } else {
            ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_moment_array)
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun getMomeryStyleListViewData(): MutableList<WatermarkStyleItemViewData> {
        val ids = WatermarkMasterStyle.getBuiltInMemoryStyles().map { it.first }
        val icons = if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN)) {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_camera_realme_memory_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_watermark_realme_memory_array)
            }
        } else {
            if (isFromCameraEditType) {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_camera_realme_memory_array)
            } else {
                ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_export_watermark_realme_memory_array)
            }
        }
        return getWatermarkStyleItemViewData(ids, icons)
    }

    private fun loadRestrictStyleListViewDataSync(): MutableList<WatermarkStyleItemViewData>? {
        val watermarkMasterAbility = ContextGetter.context.getAppAbility<IWatermarkMasterAbility>() ?: run {
            GLog.e(TAG, LogFlag.DL, "[loadRestrictStyleListViewDataSync] ability is null")
            return null
        }
        return watermarkMasterAbility.use { ability ->
            if (watermarkStyleLoader == null) {
                watermarkStyleLoader = ability.newWatermarkStyleLoader()
            }
            val styleMap = watermarkStyleLoader?.loadRestrictStyleSync(isFromCameraEditType)
            val dataList: MutableList<WatermarkStyleItemViewData>  = ArrayList<WatermarkStyleItemViewData>()
            val invalidStyleIds = mutableListOf<String>()

            styleMap?.let { map ->
                if (RestrictButtonConfigUtils.isOnLineTime()) {
                    loadRestrictStyle(map, dataList, invalidStyleIds, true)
                } else {
                    // 如果所有的限定水印下线，则将所有的限定水印Id加入到无效的列表中
                    invalidStyleIds.addAll(map.keys.map { it.styleId }.toMutableList())
                }
            }
            restrictStyleListViewData.apply {
                clear()
                addAll(dataList)
                isRestrictStyleListViewDataAdded = true
            }
            invalidRestrictStyleIds.clear()
            invalidRestrictStyleIds.addAll(invalidStyleIds)
            GLog.i(TAG, LogFlag.DL, "[loadRestrictStyleListViewDataSync] dataListSize: ${dataList.size}")
            dataList
        }
    }

    private fun loadRestrictStyle(
        map: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>,
        dataList: MutableList<WatermarkStyleItemViewData>,
        invalidStyleIds: MutableList<String>,
        isNeedSetCurrentTitle: Boolean = false
    ) {
        val thumbnailEntityList = if (isFromCameraEditType) {
            RestrictWatermarkMasterResourceDataHelper.queryEntityByType(CAMERA_THUMBNAIL).filter {
                isDownloadThumbnailFile(it.resFilepath)
            }
        } else {
            when (isLayoutLandscape) {
                false -> {
                    RestrictWatermarkMasterResourceDataHelper.queryEntityByType(SMALL_THUMBNAIL).filter {
                        isDownloadThumbnailFile(it.resFilepath)
                    }
                }

                true -> {
                    RestrictWatermarkMasterResourceDataHelper.queryEntityByType(LARGE_THUMBNAIL).filter {
                        isDownloadThumbnailFile(it.resFilepath)
                    }
                }
            }
        }

        val currentDate = Date(System.currentTimeMillis())
        // 获取当前发布的水印系列，根据系列的水印id列表进行判断是否需要展示水印
        val seriesList = app.getAppAbility<IRestrictWatermarkDownloadAbility>()?.use { it.getAllPublishedSeries() } ?: return
        val seriesMap = seriesList.associateBy { it.seriesId }
        val styleIds = seriesList.joinToString(COMMA) { it.styleIds }
        map.forEach { (style, _) ->
            // 1、水印是否在当前发布的系列的样式列表中；2、水印是否在发布时间范围内；3、当前IPU是否支持绘制该水印
            val isInStyleList = styleIds.contains(style.styleId)
            val isWithinTheValidTime = style.isWithinTheValidTime(currentDate)
            val isIpuSupportFeature = style.isIpuSupportFeature(isFromCameraEditType)
            val styleCanShow = isInStyleList && isWithinTheValidTime && isIpuSupportFeature
            GLog.d(TAG, LogFlag.DL) {
                "[loadRestrictStyle] isCanShowStyle: $styleCanShow, isInStyleList: $isInStyleList, isWithinTheValidTime: $isWithinTheValidTime, " +
                        "isIpuSupportFeature: $isIpuSupportFeature, styleId: ${style.styleId}, styleIds: $styleIds"
            }
            // 相机进入时是否禁用该水印，如果禁用则不展示，允许条件：1、是从相机进入；2、该水印支持相机直出；3、该水印不在相机禁用的样式中
            val isCameraNeedForbidden = cameraForbiddenStyles?.contains(style.styleId) ?: false

            val styleSupportForCamera = isFromCameraEditType && (style.isSupportCameraShoot != false) && isCameraNeedForbidden.not()
            GLog.d(TAG, LogFlag.DL, "[loadRestrictStyleListViewDataAsync] styleId:${style.styleId}, " +
                "isFromCameraEditType:$isFromCameraEditType, isSupportCameraShoot:${style.isSupportCameraShoot}, " +
                    "isCameraNeedForbidden:$isCameraNeedForbidden")

            if (styleCanShow && (isFromCameraEditType.not() || styleSupportForCamera)) {
                //限定水印信息添加水印样式说明文字
                style.updateTitleAndDescByLanguageType(app)
                // 限定水印缩略图未下载时先不展示水印样式
                thumbnailEntityList.find { it.belongToStyleId == style.styleId }?.resFilepath?.also { path ->
                    //避免重复添加相同水印
                    dataList.none { it.styleId == style.styleId }.let {
                        val thumbnail = BitmapFactory.decodeFile(path)
                        if (thumbnail != null && (thumbnail.width > 0) && (thumbnail.height > 0)) {
                            // 获取上线时间，先获取水印的上线时间，没有就复用系列的上线时间，没有就使用空字符串，主要用于水印按照上线时间排序
                            val publishTime = style.publishTime ?: getSeriesPublishTimeByStyleId(style.styleId, seriesMap) ?: TextUtil.EMPTY_STRING
                            val styleEntry = RestrictWatermarkMasterStyleDataHelper.queryEntityByStyleId(style.styleId).firstOrNull()
                            dataList.add(WatermarkStyleItemViewData(
                                style.styleId, style.title,
                                style.description, style.descr1, style.descr2,
                                publishTime, seriesId = styleEntry?.seriesId
                            ).apply {
                                image = thumbnail
                                status = checkMaterialDownloadedStatus(style.styleId)
                                if (isNeedSetCurrentTitle && (style.styleId == currentWatermarkInfo?.aiWatermarkMasterParams?.styleId)) {
                                    currentRestrictWatermarkTitle = style.title ?: TextUtil.EMPTY_STRING
                                    currentRestrictWatermarkDesc = style.description ?: TextUtil.EMPTY_STRING
                                    currentRestrictWatermarkDescr1 = style.descr1 ?: TextUtil.EMPTY_STRING
                                    currentRestrictWatermarkDescr2 = style.descr2 ?: TextUtil.EMPTY_STRING
                                }
                            })
                        }
                    }
                }
            } else {
                invalidStyleIds.add(style.styleId)
            }
        }

        // 限定水印列表按照上线时间倒序排列，时间相同按照styleId正序排列
        if (dataList.isNotEmpty()) {
            dataList.sortWith(compareByDescending<WatermarkStyleItemViewData> { it.publishTime }
                .thenComparing(compareBy<WatermarkStyleItemViewData> { it.styleId }))

            // 根据角标获取系列的样式id
            seriesList.firstOrNull { it.cornerIconId == vmBus?.get<String>(TOPIC_MENU_RESTRICT_WATERMARK_CORNER_ID) }?.seriesId?.let { seriesId ->
                // 根据系列的Id获取列表中第一个系列水印的styleId
                dataList.firstOrNull { seriesId == RestrictWatermarkMasterStyleDataHelper.queryStyleEntityByStyleId(it.styleId)?.seriesId }?.let {
                    // 发布styleId，让水印列表能够滚动到对应的水印样式位置
                    restrictWatermarkSeriesStyleIdPub?.publish(it.styleId)
                    vmBus?.notifyOnce(TOPIC_MENU_RESTRICT_WATERMARK_CORNER_ID)
                }
            }
        }
    }

    private fun getSeriesPublishTimeByStyleId(styleId: String, seriesMap: Map<String?, RestrictWatermarkSeriesEntry>): String? {
        val seriesId = RestrictWatermarkMasterStyleDataHelper.queryStyleEntityByStyleId(styleId)?.seriesId ?: return null
        return seriesMap[seriesId]?.publishTime
    }

    private fun loadRestrictStyleListViewDataAsync(callback: (dataList: MutableList<WatermarkStyleItemViewData>?) -> Unit) {
        val watermarkMasterAbility = ContextGetter.context.getAppAbility<IWatermarkMasterAbility>() ?: run {
            GLog.e(TAG, LogFlag.DL) { "[loadRestrictStyleListViewDataAsync] ability is null" }
            callback.invoke(null)
            return
        }
        watermarkMasterAbility.use { ability ->
            if (watermarkStyleLoader == null) {
                watermarkStyleLoader = ability.newWatermarkStyleLoader()
            }
            watermarkStyleLoader?.loadRestrictStyleAsync(AppScope, isFromCameraEditType) { map ->
                val dataList: MutableList<WatermarkStyleItemViewData>  = ArrayList<WatermarkStyleItemViewData>()
                val invalidStyleIds = mutableListOf<String>() //根据布局选择对应的缩略图

                if (RestrictButtonConfigUtils.isOnLineTime()) {
                    loadRestrictStyle(map, dataList, invalidStyleIds)
                } else {
                    // 如果所有的限定水印下线，则将所有的限定水印Id加入到无效的列表中
                    invalidStyleIds.addAll(map.keys.map { it.styleId }.toMutableList())
                }
                restrictStyleListViewData.apply {
                    clear()
                    addAll(dataList)
                    isRestrictStyleListViewDataAdded = true
                }
                invalidRestrictStyleIds.clear()
                invalidRestrictStyleIds.addAll(invalidStyleIds)
                GLog.i(TAG, LogFlag.DL, "[loadRestrictStyleListViewDataAsync] dataListSize: ${dataList.size}")
                callback.invoke(dataList)
            }
        }
    }

    private fun getAllVideoListViewData(): List<WatermarkStyleItemViewData> {
        return  getVideoHasselTextStyleListViewData() + getVideoTextStyleListViewData()
    }

    /**
     * 缩略图文件是否一下载
     * 小概率用户在数据保存未完成便退出导致文件丢失
     */
    private fun isDownloadThumbnailFile(filePath: String?): Boolean {
        return filePath?.let {
            File(it).exists()
        } ?: false
    }

    private fun notifyRefreshRestrictWatermarkStyle(seriesId: String?) {
        loadRestrictStyleListViewDataAsync { dataList ->
            launch(Dispatchers.UI) {
                notifyRestrictTypeVisibilityChanged(dataList?.isNotEmpty() == true)
                GLog.d(TAG, LogFlag.DL, "[restrictDownloadLoader] dataList.size: ${dataList?.size}")
                //通过typeButton的系列id做筛选
                dataList?.filter { it.seriesId == seriesId }
                val bean =
                    vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
                bean.id = WatermarkMasterUiBeanId.REFRESH_RESTRICT_WATERMARK_STYLE
                if (isCanUseRestrictWatermarkStyle) {
                    bean.watermarkStyleListViewData = dataList
                }
                bean.watermarkTypeListViewData = getWatermarkTypeListViewData(true)
                bean.seriesId = currentSeriesId
                watermarkUiPub?.publish(bean)
            }
        }
    }

    /**
     * 解析照片拍摄日期
     * 解析方式，与 WatermarkReadUtil.createWatermarkBean() 中，构建 watermarkInfo.content.time 的方式一致
     *
     * 说明：
     * 与产品沟通的最新结论，还是使用系统时间，来判断限时水印是否可用。
     * 此处，照片拍摄日期的逻辑暂时保留，看后续设计是否要更改，使用照片拍摄时间。
     */
    private fun parsePictureDate(): Date? {
        // 从相机跳转的水印设置页面，没有照片拍摄时间。和产品沟通，使用当前系统时间即可
        if (isFromCameraEditType) {
            return Date(System.currentTimeMillis())
        }

        val originDateTime = (editingVM.sessionProxy.getMetadata(EXIF, TAG_DATETIME_ORIGINAL) as? String)
        var date = originDateTime?.takeIf { it.isNotEmpty() }?.let {
            GLog.d(TAG, LogFlag.DL) { "[parsePictureDate] originDateTime: $originDateTime" }
            kotlin.runCatching {
                SimpleDateFormat(TimeUtils.FORMAT_YYYY_MM_DD_HH_MM_SS_COLON_SPLIT, Locale.ENGLISH).parse(it)
            }.onFailure {
                GLog.w(TAG, LogFlag.DL) { "[parsePictureDate] parse date failed. reason: $it" }
            }.getOrNull()
        }

        if (date == null) {
            val dateTime = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.dateTakenInMs
                ?: (editingVM.sessionProxy.getMetadata(EXIF, TAG_DATETIME) as? String)?.toLongOrNull()
            GLog.d(TAG, LogFlag.DL) { "[parsePictureDate] dateTime: $dateTime" }
            if (dateTime != null) {
                date = Date(dateTime)
            }
        }

        return date
    }

    /**
     * 获取样式列表的viewData，传入对应的styleId，和示意图
     * @param ids 样式id，对应 WatermarkMasterStyle里定义的样式
     * @param icons 样式示意图
     */
    private fun getWatermarkStyleItemViewData(ids: List<String>, icons: IntArray): MutableList<WatermarkStyleItemViewData> {
        val data = ArrayList<WatermarkStyleItemViewData>()
        for (i in ids.indices) {
            data.add(WatermarkStyleItemViewData(ids[i]).apply {
                icons.getOrNull(i)?.let { iconResId = it }
            })
        }
        return data
    }

    private fun getStyleBackgroundMaterialEntities(
        style: WatermarkMasterStyle,
        entityList: MutableList<RestrictWatermarkResourceEntry>,
        version: String
    ) {
        style.background?.takeIf {
            (it.backgroundType == BG_TYPE_URL) && TextUtils.isEmpty(it.backgroundPath).not()
        }?.also { background ->
            val resId = background.backgroundResName ?: return@also
            val md5 = background.md5
            val url = background.backgroundPath
            if ((md5 != null) && (url != null) && (entityList.find { it.resId == resId } == null)) {
                entityList.add(getStyleJsonMaterialEntity(resId, url, md5, style.styleId, version, BACKGROUND_IMAGE.value))
            }
        }

        // 可编辑背景纹理素材、标签多语言、缩略图
        style.supportBg.forEach { texture ->
            // 背景纹理
            val resId = texture.backgroundResName ?: return@forEach
            val md5 = texture.backgroundMd5
            val url = texture.backgroundPath
            if ((md5 != null) && (url != null) && (entityList.find { it.resId == resId } == null)) {
                entityList.add(getStyleJsonMaterialEntity(resId, url, md5, style.styleId, version, BACKGROUND_IMAGE.value))
            }

            // 标签多语言
            val titleId = texture.titleId ?: return@forEach
            val titleMd5 = texture.titleMd5
            val titleUrl = texture.titleUrl
            if ((titleMd5 != null) && (titleUrl != null) && (entityList.find { it.resMd5 == titleMd5 } == null)) {
                entityList.add(getStyleJsonMaterialEntity(titleId, titleUrl, titleMd5, style.styleId, version, TRANS_FILE.value))
            }

            // 缩略图
            val thumbnailId = texture.thumbnailResName ?: return@forEach
            val thumbnailMd5 = texture.thumbnailMd5
            val thumbnailUrl = texture.thumbnailPath
            if ((thumbnailMd5 != null) && (thumbnailUrl != null) && (entityList.find { it.resId == thumbnailId } == null)) {
                entityList.add(getStyleJsonMaterialEntity(thumbnailId, thumbnailUrl, thumbnailMd5, style.styleId, version, BACKGROUND_IMAGE.value))
            }
        }
    }

    private fun getStyleJsonMaterialEntity(
        resId: String,
        url: String,
        md5: String,
        styleId: String,
        version: String,
        resType: Int
    ): RestrictWatermarkResourceEntry {
        return  RestrictWatermarkResourceEntry(
            resId = resId,
            resType = resType,
            resUrl = url,
            resMd5 = md5,
            belongToStyleId = styleId,
            version = version
        )
    }

    private fun getStyleFontMaterialEntities(
        style: WatermarkMasterStyle,
        entityList: MutableList<RestrictWatermarkResourceEntry>,
        version: String
    ) {
        style.getFontUrlElements().forEach { fontUrlElement ->
            val resId = fontUrlElement.paint?.fontName ?: return@forEach
            val fontUrl = fontUrlElement.paint?.font
            val md5 = fontUrlElement.paint?.md5
            if ((fontUrl != null) && (md5 != null) && (entityList.find { it.resMd5 == md5 } == null)) {
                entityList.add(getStyleJsonMaterialEntity(resId, fontUrl, md5, style.styleId, version, FONT.value))
            }
        }
    }

    private fun getStyleIconMaterialEntities(
        style: WatermarkMasterStyle,
        entityList: MutableList<RestrictWatermarkResourceEntry>,
        version: String
    ) {
        style.getImageUrlElements().forEach { imageUrlElement ->
            val resId = imageUrlElement.content?.bitmapResName ?: return@forEach
            val imageUrl = imageUrlElement.content?.bitmap
            val md5 = imageUrlElement.content?.md5
            if ((imageUrl != null) && (md5 != null) && (entityList.find { it.resId == resId } == null)) {
                entityList.add(getStyleJsonMaterialEntity(resId, imageUrl, md5, style.styleId, version, ICON_IMAGE.value))
            }
        }

        // logo编辑素材下载
        style.supportLogos.forEach { logo ->
            // 获取supportLogos的数据
            getStyleLogoEntities(logo, style.styleId, version, entityList)

            // 获取themeLogos的数据
            logo.themeLogos?.forEach { themeLogo ->
                getStyleLogoEntities(themeLogo, style.styleId, version, entityList)
            }
        }
    }

    private fun getStyleLogoEntities(logo: StyleLogo, styleId: String, version: String, entityList: MutableList<RestrictWatermarkResourceEntry>) {
        val logoResId = logo.bitmapResName ?: return
        val logoUrl = logo.bitmap
        val logoMd5 = logo.bitmapMd5
        if ((logoUrl != null) && (logoMd5 != null) && (entityList.find { it.resId == logoResId } == null)) {
            entityList.add(getStyleJsonMaterialEntity(logoResId, logoUrl, logoMd5, styleId, version, ICON_IMAGE.value))
        }

        val titleId = logo.titleId
        val titleMd5 = logo.titleMd5
        val titleUrl = logo.titleUrl
        if ((titleId != null) && (titleMd5 != null) && (titleUrl != null) && (entityList.find { it.resMd5 == titleMd5 } == null)) {
            entityList.add(getStyleJsonMaterialEntity(titleId, titleUrl, titleMd5, styleId, version, TRANS_FILE.value))
        }

        val thumbnailResId = logo.thumbnailResName ?: return
        val thumbnailUrl = logo.thumbnailPath
        val thumbnailMd5 = logo.thumbnailMd5
        if ((thumbnailUrl != null) && (thumbnailMd5 != null) && (entityList.find { it.resId == thumbnailResId } == null)) {
            entityList.add(getStyleJsonMaterialEntity(thumbnailResId, thumbnailUrl, thumbnailMd5, styleId, version, ICON_IMAGE.value))
        }
    }

    private fun getStylePreviewMaterialEntities(
        style: WatermarkMasterStyle,
        entityList: MutableList<RestrictWatermarkResourceEntry>,
        version: String
    ) {
        style.previewImage?.let { bitmap ->
            val resId = bitmap.bitmapResName ?: return@let
            val url = bitmap.bitmap
            val md5 = bitmap.md5
            if ((url != null) && (md5 != null) && (entityList.find { it.resId == resId } == null)) {
                entityList.add(getStyleJsonMaterialEntity(resId, url, md5, style.styleId, version, REVIEW_IMAGE.value))
            }
        }
    }

    /**
     * 获取某水印所有素材资源信息
     * 注意：如果在添加新的素材配置时，需要在RestrictWatermarkDBHelper.checkStyleJsonResourceIsValid添加上获取资源信息的逻辑，避免被当成冗余数据处理。
     * @link [com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkDBHelper.checkStyleJsonResourceIsValid]
     *
     * @param styleId 水印样式id
     * @return 所有素材资源信息
     */
    private fun getMaterialEntitys(styleId: String): MutableList<RestrictWatermarkResourceEntry> {
        val entityList = arrayListOf<RestrictWatermarkResourceEntry>()
        (app.applicationContext as? GalleryApplication)?.getAppAbility(IWatermarkMasterAbility::class.java)?.use { ability ->
            var version = TextUtil.EMPTY_STRING
            var style = if (WatermarkMasterStyle.isRemoteRestrictStyle(styleId)) {
                // 点击带水印的图进入，样式缓存数据会被拓展数据替换，可能导致resname和url关系错误，云端限定水印需要优先从源数据json文件获取需要下载的素材信息
                val styleEntry = RestrictWatermarkMasterStyleDataHelper.queryEntityByStyleId(styleId).firstOrNull() ?: return@use
                version = styleEntry.version
                runCatching {
                    val stream = FileStyleSource(styleEntry.jsonFilePath).getInputStream()
                    stream.use { Gson().fromJson(InputStreamReader(it), WatermarkMasterStyle::class.java) }
                }.onFailure {
                    GLog.e(TAG, LogFlag.DL, "[getMaterialEntitys] RemoteRestrictStyle：$styleId, load style json failed. reason: $it")
                }.getOrNull()
            } else null

            if (style == null) {
                val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: return@use
                val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
                style = loader.loadStyleSync(source)?.first ?: return@use
            }

            // 添加待下载的背景url
            getStyleBackgroundMaterialEntities(style, entityList, version)
            // 添加待下载的字体景url，字体为可复用资源，无需每个水印都下载
            getStyleFontMaterialEntities(style, entityList, version)
            // 添加待下载的图片景url
            getStyleIconMaterialEntities(style, entityList, version)
            // 添加预览图下载url
            getStylePreviewMaterialEntities(style, entityList, version)
        }
        return entityList
    }

    /**
     * 检查样式中的文字素材下载状态，已下载，下载中，未下载
     *
     * @param styleId 水印样式id
     * @param list 该水印需要下载的素材列表，可空。
     * @return 文字下载状态
     */
    private fun checkMaterialDownloadedStatus(styleId: String, list: MutableList<RestrictWatermarkResourceEntry>? = null): ItemStatus {
        val entityList = list ?: getMaterialEntitys(styleId)
        // 未配置则无需下载素材
        if (entityList.isEmpty()) return ItemStatus.DEFAULT

        var status = ItemStatus.DEFAULT
        val resEntityList = RestrictWatermarkMasterResourceDataHelper.queryEntityByType(
            ICON_IMAGE, FONT, BACKGROUND_IMAGE, SMALL_THUMBNAIL, LARGE_THUMBNAIL, CAMERA_THUMBNAIL, REVIEW_IMAGE, BUTTON_FONT, TRANS_FILE
        )
        var entityListString = TextUtil.EMPTY_STRING
        entityList.forEach { entity ->
            entityListString += "${TextUtil.LEFT_SQUARE_BRACKETS}$entity${TextUtil.RIGHT_SQUARE_BRACKETS}"
            // 资源通过resId值判断是否已下载, Md5如果不同，则需要重新下载
            resEntityList.find { (it.resId == entity.resId) && (it.resMd5 == entity.resMd5) && (it.resFilepath.isNotEmpty()) } ?: run {
                GLog.d(TAG, LogFlag.DL, "[checkMaterialDownloadedStatus] is entity not find, styleId: $styleId, " +
                        "resName: ${entity.resName} resUrl:${entity.resUrl} filePath:${entity.resFilepath}")
                // 需下载的素材，无法从数据库中查到，则显示未下载状态
                status = ItemStatus.READY_LOAD
                return@forEach
            }
        }
        if (GProperty.DEBUG_NETWORK) {
            GLog.d(TAG, LogFlag.DL, "[checkMaterialDownloadedStatus] styleId: $styleId, entityList: $entityListString")
        }

        // 再校验，未下载状态的素材，是否正在下载中
        if ((status == ItemStatus.READY_LOAD) && downloadLoader.containsDownloadListener(styleId)) {
            status = ItemStatus.LOADING
        }

        return status
    }

    private fun getPrivacyWatermarkViewData(): MutableList<EditorMenuItemViewData> {
        return EditorUIConfig.initEditorMenuAdapterData(
            app,
            R.array.picture3d_editor_array_privacy_watermark_style_id_array,
            R.array.picture3d_editor_array_privacy_watermark_master_style_icon_array,
            R.array.picture3d_editor_array_privacy_watermark_style_text_array
        )
    }

    private fun isSupportPrivacyWatermark(): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PRIVACY_WATERMARK)
    }

    private fun isRealmeBrand(): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND)
    }

    private fun isOppoBrand(): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_OPPO_BRAND)
    }

    private fun onTypeSelected(viewId: Int) {
        if (RESTRICT_BUTTON_VIEW_ID_LIST.contains(viewId) && isCanUseRestrictWatermarkStyle) {
            val viewData = RESTRICT_BUTTON_LIST.find { it.viewId == viewId }
            onTypeMultiRestrictSelected(viewData?.buttonInfo?.seriesId)
            return
        }
        when (viewId) {
            R.id.picture3d_editor_id_watermark_master_none -> removeWatermark()
            R.id.picture3d_editor_id_watermark_master_frame -> onTypeFrameSelected()
            R.id.picture3d_editor_id_watermark_master_realme_brand -> onTypeRealmeBrandSelected()
            R.id.picture3d_editor_id_watermark_master_hasselblad -> onTypeHasselSelected()
            R.id.picture3d_editor_id_watermark_master_text -> onTypeTextSelected()
            R.id.picture3d_editor_id_watermark_master_privacy -> onTypePrivacySelected()
            R.id.picture3d_editor_id_watermark_master_restrict -> onTypeRestrictSelected()
        }
    }

    private fun addWatermarkForStyle(styleId: String, isDownloadUpdate: Boolean = false) {
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
            val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: let {
                GLog.e(TAG, LogFlag.DL, "[addWatermarkForStyle] styleId:$styleId, styleSource is null")
                return@use
            }
            val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
            val loadStyleCallback: (WatermarkMasterStyle?, IWatermarkStyleOperator?) -> Unit = { style, operator ->
                prepareAddWatermark(styleId = styleId, style = style, operator = operator, forceUseUserConfig = isDownloadUpdate)
                watermarkOperator = operator
            }
            currentStyleId = styleId
            currentSeriesId = getSeriesIdByStyleId(styleId)
            // 发布当前系列Id
            currentSeriesId?.let { restrictWatermarkCurrentSeriesIdPub?.publish(it) }
            // 若是相机的设置
            loader.loadStyleAsync(AppScope, source, isFromCameraEditType, loadStyleCallback)
        }
    }

    private fun updateOperatorByStyleId(styleId: String?) {
        GLog.d(TAG, LogFlag.DL, "[updateOperatorByStyleId] styleId:$styleId")
        val styleIdTemp = styleId ?: return
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
            val source = WatermarkStyleDataProcessor.getStyleSource(styleIdTemp) ?: let {
                GLog.e(TAG, LogFlag.DL, "[updateOperatorByStyleId] styleId:$styleId, styleSource is null")
                return@use
            }
            val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
            val pair = loader.loadStyleSync(source, isFromCameraEditType)
            watermarkOperator = pair?.second
            WatermarkStyleDataProcessor.mergeUserConfigStyleDataToDefaultStyle(
                pair?.first,
                pair?.second,
                pair?.second?.edit(),
                isFromCameraEditType
            )
            WatermarkStyleDataProcessor.changeResourceNetUrlToFilePath(
                pair?.second,
                null,
                useP3ColorGamut = isStyleUseP3ColorGamut(pair?.second?.getWatermarkStyle())
            )
            WatermarkStyleDataProcessor.changeBitmapAndFontColorsToP3(
                pair?.second,
                isStyleUseP3ColorGamut(pair?.second?.getWatermarkStyle())
            )
        }
    }

    private fun addWatermarkForStyleSync(styleId: String, forceUseUserConfig: Boolean = false) {
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
            val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: let {
                GLog.e(TAG, LogFlag.DL, "[addWatermarkForStyle] styleId:$styleId, styleSource is null")
                return@use
            }
            val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
            val pair = loader.loadStyleSync(source) ?: run {
                GLog.e(TAG, LogFlag.DL, "[addWatermarkForStyleSync] styleId:$styleId, loadStyleSync failed")
                return@use
            }
            prepareAddWatermark(styleId = styleId, forceUseUserConfig = forceUseUserConfig, style = pair.first, operator = pair.second)
        }
    }

    private fun prepareAddWatermark(
        styleId: String,
        forceUseUserConfig: Boolean = false,
        style: WatermarkMasterStyle?,
        operator: IWatermarkStyleOperator?
    ) {
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
            // 新选择的样式与原图片样式是同一个/之前已经切换过对应样式，直接使用最新的style转为特效参数，刷新水印绘制
            val isSameToOriginalId = (originalWatermarkInfo?.aiWatermarkFileExtendInfo?.styleId == styleId)
            if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                GLog.d(
                    TAG, LogFlag.DL,
                    "MasterStyleDebug:[prepareAddWatermark] styleId: $styleId, isSameToOriginalId: $isSameToOriginalId, " +
                        "forceUseUserConfig: $forceUseUserConfig, hasStyleBeenOperated:${ability.hasStyleBeenOperated(styleId)}"
                )
            }

            val isStyleMaterialDownload = style?.isStyleAvailable(
                isOppoBrand(),
                isRealme,
                isOnePlus,
                isExport,
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)
            ) ?: true
            if ((isSameToOriginalId || ability.hasStyleBeenOperated(styleId)) && forceUseUserConfig.not() && isStyleMaterialDownload) {
                prepareAddWatermarkInternal(operator, styleId)
            } else {
                if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                    GLog.d(TAG, LogFlag.DL, "MasterStyleDebug:[prepareAddWatermark] defaultStyleData:${style?.toJsonString()}")
                }
                WatermarkStyleDataProcessor.mergeUserConfigStyleDataToDefaultStyle(style, operator, operator?.edit(), isFromCameraEditType)
                if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                    GLog.d(
                        TAG, LogFlag.DL, "MasterStyleDebug:[prepareAddWatermark] mergedStyleData:" +
                            "${operator?.getWatermarkStyle()?.toJsonString()}"
                    )
                }
                WatermarkStyleDataProcessor.changeResourceNetUrlToFilePath(
                    operator,
                    operator?.getWatermarkStyle()?.exportExtInfo(),
                    useP3ColorGamut = isStyleUseP3ColorGamut(operator?.getWatermarkStyle())
                )
                WatermarkStyleDataProcessor.changeTextSizeForElement(currentWatermarkInfo, operator)
                WatermarkStyleDataProcessor.changeBitmapAndFontColorsToP3(
                    operator,
                    isStyleUseP3ColorGamut(operator?.getWatermarkStyle())
                )
                operator?.getWatermarkStyle()?.updateStyleDefaultGreeting()
                if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                    GLog.d(
                        TAG, LogFlag.DL, "MasterStyleDebug:[prepareAddWatermark] changeResourceNetUrlToFilePath " +
                            "StyleData:${operator?.getWatermarkStyle()?.toJsonString()}"
                    )
                }
                prepareAddWatermarkInternal(operator, styleId)
            }
            ability.cacheStyleOperating(styleId)
        }
    }

    private fun prepareAddWatermarkInternal(operator: IWatermarkStyleOperator?, styleId: String) {
        if (operator == null) {
            // 水印下线或水印样式不存在时，不添加水印
            GLog.w(TAG, LogFlag.DL, "[prepareAddWatermarkInternal] operator is null")
            return
        }

        memorizePrivacyWatermarkInfoIfNeeded(currentWatermarkInfo)
        val aiWatermarkMasterParams = hideInvalidTextSourceInWatermark(operator, styleId)
        loadLastMemorizedInfoAndUpdateCurrentWatermarkInfo(
            WatermarkPattern.PATTERN_AI_MASTER, currentWatermarkInfo,
            aiWatermarkMasterParams
        )
        if (isFromCameraEditType) {
            watermarkPreviewDrawer?.let {
                val cameraWatermarkInfo = prepareCameraDefaultWatermarkInfo(operator)
                if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                    GLog.d(
                        TAG, LogFlag.DL, "MasterStyleDebug:[prepareAddWatermarkInternal] " +
                            "cameraWatermarkInfo:${cameraWatermarkInfo.aiWatermarkMasterParams?.watermarkMasterStyle?.toJsonString()}"
                    )
                }
                val frameSize = operator?.getWatermarkStyle()?.getFrameSize()
                val baseImageSize = operator?.getWatermarkStyle()?.getBaseImageSize()
                it.doWatermarkEffect(
                    WatermarkRecord.createAddWatermarkRecord(cameraWatermarkInfo, phoneInfo, false),
                    frameSize,
                    baseImageSize
                )
            }
        }

        // 添加水印前延迟300ms展示loading弹窗，如果加水印耗时小于300ms，则不显示loading弹窗
        addWatermarkLoadingPub?.publish(Pair(true, LOADING_DIALOG_SHOW_DELAY_TIME))
        addWatermark {
            // 添加完水印，如果已经展示loading弹窗，则至少200ms后关闭loading弹窗,避免出现闪烁的现象
            addWatermarkLoadingPub?.publish(Pair(false, LOADING_DIALOG_HIDE_STAY_TIME))
        }
    }
    /**
     * 隐藏掉水印中，无效的文字信息
     *
     * 场景：
     * 如截屏照片，没有影像参数。在绘制限定画框水印时，会显示null在水印上。
     * 就需要在这里，让影像参数不进行绘制
     *
     * 说明：
     * 参考个性编辑页-文本中，选项“无”的逻辑，修改 aiWatermarkMasterParams 属性，使插件不进行绘制相应文字
     */
    private fun hideInvalidTextSourceInWatermark(operator: IWatermarkStyleOperator?, styleId: String): AiWatermarkMasterParams? {
        val styleEditor = operator?.edit()
        operator?.checkIsNeedToChangeModelNameTextSize(currentWatermarkInfo?.content?.deviceName)
        val style = operator?.getWatermarkStyle()
        if (isFromCameraEditType.not()) {
            // 可编辑文本元素
            val elementList = style?.getMergedEditableTextElements()

            // 没有影像参数信息时，隐藏影像参数文本元素
            if (currentWatermarkInfo?.content?.shootingInfo?.getAllShootingInfo()?.isEmpty() != false) {
                GLog.w(TAG, LogFlag.DL) { "[hideInvalidTextSourceInWatermark] invalid image params" }
                val imageParamsElement = elementList?.find { it.content?.textSource == TEXT_SOURCE_IMAGE_PARAMS }
                imageParamsElement?.also {
                    styleEditor?.putElementInt(it.id, INT_ATTR_TEXT_SOURCE, TEXT_SOURCE_NONE)
                    styleEditor?.putElementBoolean(it.id, BOOL_ATTR_VISIBLE, false)
                }
            }

            if (currentWatermarkInfo?.content?.deviceName?.isEmpty() == true) {
                GLog.w(TAG, LogFlag.DL) { "[hideInvalidTextSourceInWatermark] invalid device names" }
                operator?.updateAllModelNameElementsVisible(false)
            }
            ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
                val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
                updateUrlImagePathIfFileNameChange(operator?.getWatermarkStyle(), loader)
            }
        }
        return style?.let {
            val jsonString = it.toJsonString()
            AiWatermarkMasterParams(
                styleId = styleId,
                styleJsonObject = it.toJsonObject(jsonString),
                watermarkMasterStyle = it.jsonStringToWatermarkMasterStyle(jsonString)
            )
        }
    }


    /**
     * 更新水印中的图片路径
     * 相册数据清除，水印图片缺失，当新的同名水印的图片名称变更，会获取不到水印图片
     */
    private fun updateUrlImagePathIfFileNameChange(watermarkMasterStyle: WatermarkMasterStyle?, loader: IWatermarkStyleLoader) {
        if (WatermarkMasterStyle.isRestrictStyle(watermarkMasterStyle?.styleId).not()) {
            return
        }
        val baseStyle = watermarkMasterStyle?.styleId?.let { loadOriginalStyleJson(it, loader)?.first }
        if (baseStyle == null) {
            return
        }
        watermarkMasterStyle.getImageUrlElements().forEach { element ->
            val baseElement = baseStyle.getImageElements().find { it.id == element.id }
            baseElement?.let { updateUrlImagePathFromBaseElement(element, baseElement, isStyleUseP3ColorGamut(watermarkMasterStyle)) }
        }
    }

    /**
     * 从新的水印json中更新原始水印中的图片路径
     */
    private fun updateUrlImagePathFromBaseElement(
        element: WatermarkElement,
        baseElement: WatermarkElement,
        isUseP3ColorGamut: Boolean = false
    ): Boolean {
        var isChange = false
        val md5 = element.content?.md5
        val baseMd5 = baseElement.content?.md5
        val baseUrl = baseElement.content?.bitmap
        val bitmapSourceType = element.content?.bitmapSourceType
        val baseBitmapSourceType = baseElement.content?.bitmapSourceType
        val baseBitmapSourceName = baseElement.content?.bitmapResName
        if (bitmapSourceType == BITMAP_TYPE_URL) {
            val path = queryPathByMd5(md5, isUseP3ColorGamut)
            path?.let { filePath ->
                if (java.io.File(filePath).exists().not()) {
                    var newFilePath: String? = null
                    if (baseBitmapSourceType == BITMAP_TYPE_URL) {
                        newFilePath = queryPathByMd5(baseMd5, isUseP3ColorGamut)
                    } else if (baseBitmapSourceType == BITMAP_TYPE_FILEPATH) {
                        newFilePath = baseUrl
                    }
                    newFilePath?.let {
                        element.content?.bitmap = it
                        element.content?.bitmapSourceType = BITMAP_TYPE_FILEPATH
                        element.content?.bitmapResName = baseBitmapSourceName
                        isChange = true
                    }
                } else {
                    element.content?.bitmap = filePath
                    element.content?.bitmapSourceType = BITMAP_TYPE_FILEPATH
                    isChange = true
                }
            }
        }
        return isChange
    }

    /**
     * 获取原始的WatermarkMasterStyle，用来和cache中的比对差异
     */
    private fun loadOriginalStyleJson(styleId: String, loader: IWatermarkStyleLoader): Pair<WatermarkMasterStyle, IWatermarkStyleOperator>? {
        val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: let {
            GLog.e(TAG, LogFlag.DL, "[loadStyleJson] styleId:$styleId, styleSource is null")
            return null
        }

        return loader.loadStyleSync(source, true)
    }

    private fun queryPathByMd5(md5: String?, isP3ColorGamut: Boolean = false): String? {
        md5?.let {
            val entity = RestrictWatermarkMasterResourceDataHelper.queryByMd5(md5, useP3ColorGamut = isP3ColorGamut) ?: return null
            return entity.resFilepath.ifEmpty { null }
        } ?: let {
            GLog.e(TAG, LogFlag.DL, "[queryPathByUrl] url is null")
        }
        return null
    }

    private fun onStyleSelected(styleId: String?) {
        RealShowTimeInstrument.startRecord(AI_MASTER_WATERMARK)
        GLog.d(TAG, LogFlag.DF) { "MasterStyleDebug:[onStyleSelected], styleId:$styleId" }
        styleId?.let {
            initStyleBlessTitle(styleId)
            addWatermarkForStyle(it)
        } ?: run {
            removeCameraPreviewWatermarkIfNeed()
        }
    }

    private fun onStyleMaskClick(styleId: String?, status: ItemStatus) {
        GLog.d(TAG, LogFlag.DL) { "[onStyleMaskClick] styleId:$styleId, status:${status.name}" }
        if (TextUtils.isEmpty(styleId)) {
            GLog.e(TAG, LogFlag.DL) { "[onStyleMaskClick] styleId is empty!" }
            return
        }
        when (status) {
            ItemStatus.READY_LOAD -> {
                styleId?.let {
                    singleDownloadMaterial(false, it)
                }
            }

            ItemStatus.LOADING -> GLog.w(TAG, LogFlag.DF) { "[onStyleMaskClick] is loading, cannot click" }
            else -> {
                val watermarkInfo = if (isFromCameraEditType) {
                    updateOperatorByStyleId(styleId)
                    val cameraWatermarkInfo = prepareCameraDefaultWatermarkInfo(watermarkOperator)
                    WatermarkStyleDataProcessor.changeResourceNetUrlToFilePath(
                        watermarkOperator,
                        watermarkOperator?.getWatermarkStyle()?.exportExtInfo(),
                        useP3ColorGamut = isStyleUseP3ColorGamut(watermarkOperator?.getWatermarkStyle())
                    )
                    WatermarkStyleDataProcessor.changeTextSizeForElement(cameraWatermarkInfo, watermarkOperator)
                    WatermarkStyleDataProcessor.changeBitmapAndFontColorsToP3(
                        watermarkOperator,
                        isStyleUseP3ColorGamut(watermarkOperator?.getWatermarkStyle())
                    )
                    cameraWatermarkInfo.content?.deviceName?.let { name ->
                        WatermarkMasterInfoUtils.getDeviceNameSuffix(name)?.let { suffix ->
                            WatermarkStyleDataProcessor.changeDeviceNameAndSuffix(suffix, watermarkOperator)
                        }
                    }
                    ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
                        val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
                        updateUrlImagePathIfFileNameChange(watermarkOperator?.getWatermarkStyle(), loader)
                    }
                    cameraWatermarkInfo.aiWatermarkMasterParams?.watermarkMasterStyle?.updateStyleDefaultGreeting()
                    cameraWatermarkInfo
                } else currentWatermarkInfo
                personalizedDataPub?.publish(
                    mutableMapOf(
                        PERSONALIZED_DATA_FROM_CAMERA to isFromCameraEditType,
                        PERSONALIZED_DATA_STYLE to styleId,
                        PERSONALIZED_DATA_WATERMARK_INFO to watermarkInfo,
                        PERSONALIZED_DATA_GREETING_TITLE to styleGreetingTitleMap[styleId],
                        PERSONALIZED_DATA_P3_ENABLED to isStyleUseP3ColorGamut(watermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle)
                    )
                )
                if (isFromCameraEditType) {
                    vmBus?.notifyOnce(TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID, R.id.strategy_watermark_personalized_edit)
                } else {
                    vmBus?.notifyOnce(TopicID.Menu.REPLY_TOPIC_MENU_SELECT, R.id.strategy_watermark_personalized_edit)
                }
            }
        }
    }

    private fun watermarkCameraFinish() {
        vmBus?.notifyOnce(TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.action_finish_strategy))
    }

    private fun loadStyleJson(styleId: String, loader: IWatermarkStyleLoader, needMergeUserConfig: Boolean = false): WatermarkCameraSettingOperator? {
        val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: let {
            GLog.e(TAG, LogFlag.DL, "[loadStyleJson] styleId:$styleId, styleSource is null")
            return null
        }
        // 相机编辑不应该受相册编辑的影响
        return loader.loadStyleSync(source, true)?.let {
            val isP3ColorGamut = vmBus?.get<Boolean>(TOPIC_USE_P3_COLOR_GAMUT) ?: false
            WatermarkCameraSettingOperator(app, it.first, isP3ColorGamut, needMergeUserConfig)
        }
    }

    private fun sendStyleDataToCamera(
        photoStyleId: String?,
        videoStyleId: String?,
        photoPair: Pair<ArrayList<Uri>, ArrayList<String>>?,
        videoPair: Pair<ArrayList<Uri>, ArrayList<String>>?
    ) {
        val styleTypeName = getStyleTypeName(photoStyleId, videoStyleId)
        cameraWatermarkServiceClient?.let {
            GLog.d(TAG, LogFlag.DL, "[sendStyleDataToCamera] sendResData by service")
            val photoUriStringList = photoPair?.first?.map { it.toString() }?.toTypedArray()
            val videoUriStringList = videoPair?.first?.map { it.toString() }?.toTypedArray()
            val ret = it.sendResData(styleTypeName, photoStyleId, videoStyleId, photoUriStringList, videoUriStringList)
            if (ret != 0) {
                GLog.e(TAG, LogFlag.DL, "[sendStyleDataToCamera] sendResData failed:$ret")
            }
        } ?: let {
            val intent = Intent()
            intent.setAction(ACTION_CAMERA_WATERMARK_RESOURCE)
            photoStyleId?.let { intent.putExtra(CAMERA_WATERMARK_PHOTO_STYLE, it) }
            videoStyleId?.let { intent.putExtra(CAMERA_WATERMARK_VIDEO_STYLE, it) }
            photoPair?.first?.let { intent.putParcelableArrayListExtra(CAMERA_WATERMARK_PHOTO_URI_KEY, it) }
            photoPair?.second?.let { intent.putStringArrayListExtra(CAMERA_WATERMARK_PHOTO_MD5_KEY, it) }
            videoPair?.first?.let { intent.putParcelableArrayListExtra(CAMERA_WATERMARK_VIDEO_URI_KEY, it) }
            videoPair?.second?.let { intent.putStringArrayListExtra(CAMERA_WATERMARK_VIDEO_MD5_KEY, it) }
            styleTypeName?.let { intent.putExtra(KEY_CAMERA_WATERMARK_GROUP_NAME, it) }
            GLog.d(TAG, LogFlag.DL, "[sendStyleDataToCamera] sendResData by broadcast")
            app.sendBroadcast(intent)
        }
        val bean = vmBus?.get<WatermarkPersonalizedEditUiBean>(TOPIC_PERSONALIZED_EDIT_UI_BEAN)
            ?: WatermarkPersonalizedEditUiBean()
        //目前是相机->设置->水印编辑->应用，这个路径下会需要走这里
        if (bean.isFromWatermarkPersonalizedEdit) {
            bean.id = WatermarkPersonalizedEditUiBeanId.WATERMARK_CAMERA_SEND_DATA_FINISH
            vmBus?.notifyOnce(TOPIC_PERSONALIZED_EDIT_UI_BEAN, bean)
        }

        if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
            GLog.d(
                TAG,
                LogFlag.DL
            ) { "MasterStyleDebug:[sendStyleDataToCamera] photoStyleId:$photoStyleId, videoStyleId:$videoStyleId" }
            GLog.d(TAG, LogFlag.DL) { "MasterStyleDebug:[sendStyleDataToCamera] photoPair:$photoPair" }
            GLog.d(TAG, LogFlag.DL) {
                "MasterStyleDebug:[sendStyleDataToCamera] styleTypeName:" +
                    "${getStyleTypeName(photoStyleId, videoStyleId)}"
            }
        }
    }

    private fun bindCameraAidl() {
        val intent = Intent()
        intent.setAction(ACTION_CAMERA_WATERMARK_RESOURCE_AIDL)
        intent.`package` = CAMERA_PKG
        app.bindService(intent, cameraWatermarkServiceConnection, Context.BIND_AUTO_CREATE)
    }

    private inner class CameraWatermarkServiceConnection : ServiceConnection {

        override fun onServiceConnected(componentName: ComponentName?, service: IBinder?) {
            cameraWatermarkServiceClient = GalleryConnection.Stub.asInterface(service)
        }

        override fun onServiceDisconnected(componentName: ComponentName?) {
            cameraWatermarkServiceClient = null
        }
    }

    private fun getStyleTypeName(photoStyleId: String?, videoStyleId: String?): String? {
        val groupName = vmBus?.get<MutableMap<String, Any?>>(TOPIC_CAMERA_WATERMARK_SETTING)?.getOrDefault(
            KEY_CAMERA_WATERMARK_GROUP_NAME, TextUtil.EMPTY_STRING
        ) as? String
        val styleId = when {
            // 仅选择photoStyleId
            photoStyleId.isNullOrBlank().not() && videoStyleId.isNullOrBlank() -> photoStyleId
            // 仅选择videoStyleId
            photoStyleId.isNullOrBlank() && videoStyleId.isNullOrBlank().not() -> videoStyleId
            // 图片视频都没选择，返回相机传入的groupName
            photoStyleId.isNullOrBlank() && videoStyleId.isNullOrBlank() -> return groupName
            // 图片和视频都有选择，默认图片styleId
            photoStyleId.isNullOrBlank().not() && videoStyleId.isNullOrBlank().not() -> photoStyleId
            else -> null
        }
        val stringId = when {
            WatermarkMasterStyle.isHasselFrame(styleId) -> R.string.picture_editor_text_watermark_master_hasselblad
            WatermarkMasterStyle.isHasselText(styleId) -> R.string.picture_editor_text_watermark_master_hasselblad
            WatermarkMasterStyle.isPersonalizeFrame(styleId) -> R.string.picture_editor_text_watermark_master_frame
            WatermarkMasterStyle.isRealmeBrandImprint(styleId) -> R.string.picture_editor_text_watermark_master_realme_brand
            WatermarkMasterStyle.isTextStyle(styleId) -> R.string.picture_editor_text_watermark_master_text
            WatermarkMasterStyle.isRestrictStyle(styleId) -> R.string.picture_editor_text_watermark_master_restrict
            else -> null
        }
        return stringId?.let { app.getString(it) } ?: groupName
    }

    private fun removeWatermark() {
        GLog.d(TAG, LogFlag.DF) { "removeWatermark" }
        if (currentWatermarkInfo?.hasWatermark() == true) {
            //旧水印进二级编辑页面rendering为false，而lastRecord为null，不会执行去除水印操作，这里手动触发一次管线刷新再出一帧，显示无水印图
            editingVM.sessionProxy.invalidate()
            memorizePrivacyWatermarkInfoIfNeeded(currentWatermarkInfo)
            currentWatermarkInfo = currentWatermarkInfo?.copy(params = null, customInfo = null, aiWatermarkMasterParams = null)
            refreshNonWatermarkUI()
        }
    }

    /**
     * 把图片内容刷新成无水印的状态
     */
    private fun refreshNonWatermarkUI(listener: (EditorResultState) -> Unit = { trackWatermarkError(it, WatermarkCode.REMOVE_WATERMARK) }) {
        GLog.d(WatermarkVM.TAG, LogFlag.DL) { "[refreshNonWatermarkUI]" }
        ensureWatermarkRemovedOnce(true)
        val record = WatermarkRecord(
            command = CMD_WATERMARK_ADD,
            watermarkInfo = currentWatermarkInfo?.copy(),
            phoneInfo = phoneInfo,
            isFillArguments = false
        )

        val lastIsRemoveWatermark = vmBus?.get<OperatingRecordWithInvoker>(TOPIC_OPERATING_RECORD)?.operatingRecord?.let {
            (it.effectName == AvEffect.WatermarkEffect.name) && it.arguments.isEmpty
        } ?: false
        emitRecordInner(record, listener)
        // 首次进入水印编辑已经是去水印的了。若是水印图片之前没切换过水印，上面只是告诉操作栈记录操作。
        if ((hasEmitWatermarkRecord.not() || lastIsRemoveWatermark)) {
            updateWithoutWatermarkPreview()
        } else {
            GLog.d(WatermarkVM.TAG, "[refreshNonWatermarkUI] withoutWatermarkBitmap is null or hasEmitWatermarkRecord=$hasEmitWatermarkRecord")
        }

        hasEmitWatermarkRecord = true
    }

    private fun emitRecordInner(record: WatermarkRecord, listener: (EditorResultState) -> Unit) {
        ensureWatermarkRemovedOnce(false)
        val pattern = record.watermarkInfo?.params?.pattern
        GLog.d(WatermarkVM.TAG, LogFlag.DL, "[emitRecordInner] pattern:$pattern")
        emitRecord(record) {
            vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also { imagePack ->
                imagePack.watermarkInfo = currentWatermarkInfo?.copy()
            }
            it?.addResultListener(listener)
        }
        postUpdatePattern()
    }

    /**
     * 更新当前的水印模式
     */
    private fun postUpdatePattern() {
        updatePreWatermarkPattern(currentWatermarkInfo)
        val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)
            ?: WatermarkMasterUIBean()
        bean.id = WatermarkMasterUiBeanId.CHANGE_SERIES_TITLE_VISIBILITY
        bean.watermarkStyleId = currentWatermarkInfo?.aiWatermarkMasterParams?.styleId
        watermarkUiPub?.publish(bean)
    }

    /**
     * 水印操作前，需要确保原有的水印被移除了，否则会加两次水印
     * @param isRender 是否渲染，即移除水印的帧是否刷新页面
     */
    private fun ensureWatermarkRemovedOnce(isRender: Boolean) {
        val (index, _) = editingVM.sessionProxy.queryEffect {
            (it.key is WatermarkRecord)
        }
        if (index < 0) {
            vmBus?.notifyOnce(REPLY_TOPIC_REMOVE_WATERMARK_IF_NEED, isRender)
        }
    }

    private fun updateWithoutWatermarkPreview() {
        val watermarkInfo = copyWatermarkInfo()
        updatePreWatermarkPattern(watermarkInfo)
    }

    private fun restoreWatermark() {
        vmBus?.notifyOnce(REPLY_TOPIC_RESTORE_WATERMARK_IF_NEED, true)
    }

    /**
     * 每次编辑图片之后可能影响图片水印信息
     * copy一份当前的水印信息
     * 保存在水印操作记录中
     *
     * @return copy的当前水印信息
     */
    private fun copyWatermarkInfo(): WatermarkInfo {
        return currentWatermarkInfo?.copy() ?: WatermarkInfo()
    }

    private interface TextureUpdateCallback {
        fun callback(isSuccess: Boolean, updatedTexture: ITexture?)
    }

    /**
     * 添加隐私水印
     */
    @Suppress("CollapsibleIfStatements")
    private fun addPrivacyWatermark() {
        val watermarkInfo = currentWatermarkInfo
        if (watermarkInfo?.params?.pattern != WatermarkPattern.PATTERN_PRIVACY) {
            if (isPrivacyWatermarkEditType) {
                loadLastMemorizedInfoAndUpdateCurrentWatermarkInfo(
                    WatermarkPattern.PATTERN_PRIVACY,
                    privacyWatermarkInfo
                )
                addWatermark {
                    trackWatermarkError(it, WatermarkCode.ADD_WATERMARK)
                    runOnUIThreadImmediate {
                        privacyStatsPub?.publish(PrivacyDataStatus.READY)
                    }
                }
            }
        }
    }

    private fun onTypeHasselSelected() {
        updateStyleListView(getHasselStyleListViewData())
    }

    private fun onTypeFrameSelected() {
        updateStyleListView(getFrameStyleListViewData())
    }

    private fun onTypeRealmeBrandSelected() {
        updateStyleListView(getRealmeBrandStyleListViewData())
    }

    private fun onTypeTextSelected() {
        updateStyleListView(getTextStyleListViewData())
    }

    private fun onTypePrivacySelected() {
        addPrivacyWatermark()
    }

    private fun onTypeRestrictSelected() {
        //当导致水印缩略图重新下载时，需要重新刷新一下列表数据，清除缩图未加载的水印
        val date = restrictStyleListViewData.firstOrNull { it.image == null && it.iconResId != 0 }
        if (date != null) {
            notifyRefreshRestrictWatermarkStyle(null)
        }
        updateStyleListView(restrictStyleListViewData)
    }

    private fun onTypeMultiRestrictSelected(seriesId: String?) {
        currentSeriesId = seriesId
        // 发布当前系列Id
        currentSeriesId?.let { restrictWatermarkCurrentSeriesIdPub?.publish(it) }
        //当导致水印缩略图重新下载时，需要重新刷新一下列表数据，清除缩图未加载的水印
        val data = restrictStyleListViewData.firstOrNull { it.image == null && it.iconResId != 0 }
        if (data != null) {
            notifyRefreshRestrictWatermarkStyle(seriesId)
        }
        updateStyleListView(restrictStyleListViewData, seriesId = seriesId)
    }

    private fun removeCameraPreviewWatermarkIfNeed() {
        watermarkPreviewDrawer?.removeWatermarkEffect()
    }

    private fun onCameraPreviewSizeChange(previewSize: Size?, appUiConfig: AppUiResponder.AppUiConfig?) {
        previewSize?.let {
            AppScope.launch(Dispatchers.IO) {
                if (watermarkPreviewDrawer == null) createWatermarkPreviewDrawer(appUiConfig)
                val isMedium = (appUiConfig?.screenMode?.current == AppUiResponder.ScreenMode.LARGE)
                val targetResource: Int = if (isMedium) {
                    R.drawable.watermark_camera_personalized_preview
                } else {
                    R.drawable.watermark_camera_setting_preview
                }
                val previewBitmap = BitmapFactory.decodeResource(app.resources, targetResource)
                // 更新使用的基础预览图，更新预览尺寸
                watermarkPreviewDrawer?.updateBitmap(previewBitmap)
                if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                    GLog.d(TAG, LogFlag.DL, "MasterStyleDebug:[onCameraPreviewSizeChange] currentStyleId:$currentStyleId")
                    GLog.d(
                        TAG, LogFlag.DL, "MasterStyleDebug:[onCameraPreviewSizeChange] currentStyleData:" +
                            "${watermarkOperator?.getWatermarkStyle()?.toJsonString()}"
                    )
                }
                val cameraWatermarkInfo = prepareCameraDefaultWatermarkInfo(watermarkOperator)
                if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
                    GLog.d(
                        TAG, LogFlag.DL, "MasterStyleDebug:[onCameraPreviewSizeChange] cameraWatermarkInfo:" +
                            "${cameraWatermarkInfo.aiWatermarkMasterParams?.watermarkMasterStyle?.toJsonString()}"
                    )
                }
                val frameSize = watermarkOperator?.getWatermarkStyle()?.getFrameSize()
                val baseImageSize = watermarkOperator?.getWatermarkStyle()?.getBaseImageSize()
                // 重新
                watermarkPreviewDrawer?.doWatermarkEffect(
                    WatermarkRecord.createAddWatermarkRecord(cameraWatermarkInfo, phoneInfo, false),
                    frameSize,
                    baseImageSize
                )
            }
        }
    }

    override fun addWatermark(listener: (EditorResultState) -> Unit) {
        GLog.d(TAG, LogFlag.DL) { "[addWatermark]" }
        if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
            GLog.d(
                TAG, LogFlag.DL, "MasterStyleDebug:[addWatermark] styleData:" +
                    "${currentWatermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle?.toJsonString()}"
            )
        }
        val record = WatermarkRecord.createAddWatermarkRecord(currentWatermarkInfo?.copy(), phoneInfo, false)
        emitRecordInner(record, listener)
        hasEmitWatermarkRecord = true
    }

    /**
     * 加载上次记忆的水印信息，并添加新的参数来更新当前水印信息，不允许修改device信息
     * @param pattern [WatermarkPattern.PATTERN_AI_MASTER] Ai大师水印 [WatermarkPattern.PATTERN_PRIVACY] 隐私水印
     * @param watermarkInfo 水印信息
     * @param aiWatermarkMasterParamsT 水印大师参数
     */
    override fun loadLastMemorizedInfoAndUpdateCurrentWatermarkInfo(
        pattern: WatermarkPattern,
        watermarkInfo: WatermarkInfo?,
        aiWatermarkMasterParamsT: AiWatermarkMasterParams?
    ) {
        when (pattern) {
            WatermarkPattern.PATTERN_AI_MASTER -> {
                currentWatermarkInfo = currentWatermarkInfo?.copy(
                    params = watermarkInfo?.params?.copy(pattern = WatermarkPattern.PATTERN_AI_MASTER, rotation = 0) ?: WatermarkParams(
                        pattern = WatermarkPattern.PATTERN_AI_MASTER,
                        showDevice = false,
                        showLocation = false,
                        showTime = false,
                        textSize = WatermarkTextSize.MEDIUM,
                        position = WatermarkPosition.BOTTOM,
                        rotation = 0,
                        latitude = 0.0,
                        longitude = 0.0
                    ),
                    aiWatermarkMasterParams = aiWatermarkMasterParamsT
                )
            }

            WatermarkPattern.PATTERN_PRIVACY -> {
                currentWatermarkInfo = currentWatermarkInfo?.copy(
                    params = watermarkInfo?.params?.copy(rotation = 0) ?: WatermarkParams(
                        pattern = WatermarkPattern.PATTERN_PRIVACY,
                        showDevice = false,
                        showLocation = false,
                        showTime = false,
                        textSize = WatermarkTextSize.MEDIUM,
                        position = WatermarkPosition.BOTTOM,
                        rotation = 0,
                        latitude = 0.0,
                        longitude = 0.0
                    ),
                    aiWatermarkMasterParams = null,
                    privacyWatermarkParams = watermarkInfo?.privacyWatermarkParams ?: PrivacyWatermarkParams(
                        style = PrivacyWatermarkStyle.REGISTER,
                        content = ContextGetter.context.resources.getString(R.string.picture_editor_privacy_watermark_for_register),
                        customInfo = TextUtil.EMPTY_STRING
                    )
                )
            }

            else -> {}
        }
    }

    private fun updateStyleListView(
        viewData: MutableList<WatermarkStyleItemViewData>,
        seriesId: String? = TextUtil.EMPTY_STRING
    ) {
        val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
        bean.id = WatermarkMasterUiBeanId.REFRESH_WATERMARK_STYLE_STATE
        val finalViewData = mutableListOf<WatermarkStyleItemViewData>().apply {
            addAll(
                seriesId.takeIf { it.isNullOrBlank().not() }
                    ?.let { sid -> viewData.filter { it.seriesId == sid } }
                    ?: viewData
            )
        }
        bean.watermarkStyleListViewData = finalViewData
        watermarkUiPub?.publish(bean)
        launch {
            viewData.forEach {
                updateStyleItemStatus(it.styleId, checkMaterialDownloadedStatus(it.styleId))
            }
        }
    }

    /**
     * 当次水印编辑调整了通用水印的设置项，「无」「哈苏定制」「个性画框」「隐私水印」 「限定水印」来回切换时,记忆当次设置项
     * @param watermarkInfo 当前水印信息
     */
    private fun memorizePrivacyWatermarkInfoIfNeeded(watermarkInfo: WatermarkInfo?) {
        watermarkInfo?.let {
            if (it.isPrivacyWatermark()) {
                privacyWatermarkInfo = it.copy()
            }
        }
    }

    /**
     * 从三级页面返回需要更新currentWatermarkInfo为最新内容
     */
    private fun updateCurrentWatermarkInfo(watermarkInfo: WatermarkInfo?) {
        currentWatermarkInfo = watermarkInfo
    }

    /**
     * 给相机发送广播，传递水印style信息
     */
    private fun sendStyleDataToCameraIfNeed(photoStyleId: String?, videoStyleId: String?, needMergeUserConfig: Boolean = false) {
        GLog.d(TAG, LogFlag.DL, "[sendStyleDataToCameraIfNeed] photoStyleId:$photoStyleId, videoStyleId:$videoStyleId")
        (app.applicationContext as? GalleryApplication)?.getAppAbility(IWatermarkMasterAbility::class.java)?.use { ability ->
            val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
            AppScope.launch(Dispatchers.IO) {
                GLog.d(TAG, LogFlag.DL) { "[sendStyleDataToCameraIfNeed] AppScope.launch" }
                val photoPair = async { photoStyleId?.let { loadStyleJson(it, loader, needMergeUserConfig)?.getUriAndMd5List() } }
                val videoPair = async { videoStyleId?.let { loadStyleJson(it, loader, needMergeUserConfig)?.getUriAndMd5List() } }
                sendStyleDataToCamera(photoStyleId, videoStyleId, photoPair.await(), videoPair.await())
            }
        }
    }

    private fun getUriStringList(
        operator: WatermarkCameraSettingOperator?
    ): Array<String>? {
        val photoPair = operator?.getUriAndMd5List()
        cameraWatermarkServiceClient?.let {
            return photoPair?.first?.map { it.toString() }?.toTypedArray()
        }
        return null
    }

    /**
     * 准备相机默认的水印参数
     */
    private fun prepareCameraDefaultWatermarkInfo(operator: IWatermarkStyleOperator? = null): WatermarkInfo {
        /**
         * 1,当相机设置页选择水印样式添加水印时，根据传入的operator获取选中的样式信息，构建aiWatermarkMasterParams
         */
        val location = app.getString(R.string.picture_editor_text_watermark_location)
        val deviceName = if (isRealmeBrand()) {
            MarketNameInfo.getMarketName()
        } else {
            ConfigAbilityWrapper.getString(CAMERA_WATERMARK_PREVIEW_MARKET_NAME) ?: Build.MODEL
        }
        operator?.checkIsNeedToChangeModelNameTextSize(deviceName)
        val cameraStyle = operator?.getWatermarkStyle()
        val aiWatermarkMasterParams = AiWatermarkMasterParams(
            styleId = cameraStyle?.styleId,
            styleJsonObject = cameraStyle?.toJsonObject(),
            watermarkMasterStyle = cameraStyle
        )
        return WatermarkInfo(
            content = WatermarkContent(
                deviceName = deviceName,
                location = location,
                time = TimeUtils.getWatermarkTime(ContextGetter.context, System.currentTimeMillis()),
                shootingInfo = ShootingInfo(
                    focalLength = DEFAULT_SHOOT_INFO_FOCAL,
                    focalLengthIn35MM = DEFAULT_SHOOT_INFO_FOCAL,
                    aperture = DEFAULT_SHOOT_INFO_APERTURE,
                    exposureTime = DEFAULT_SHOOT_INFO_EXPOSURE,
                    isoSpeedRatings = DEFAULT_SHOOT_INFO_ISO
                )
            ),
            params = WatermarkParams(
                pattern = WatermarkPattern.PATTERN_AI_MASTER
            ),
            aiWatermarkMasterParams = aiWatermarkMasterParams
        )
    }

    private fun onPersonalizedEditDone(applyStyleId: String? = null) {
        val bean = vmBus?.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN) ?: WatermarkMasterUIBean()
        bean.id = WatermarkMasterUiBeanId.PERSONALIZED_EDIT_DONE
        bean.applyStyleId = applyStyleId
        watermarkUiPub?.publish(bean)
    }

    /**
     *
     * 获取祝福语标题
     * 根据styleId及当前地区获取祝福语标题，如果没有就使用默认标题
     */
    private fun initStyleBlessTitle(styleId: String?) {
        // 涉及数据库操作，在子线程中执行
        AppScope.launch(Dispatchers.IO) {
            val currentStyleId = styleId ?: return@launch
            if (WatermarkMasterStyle.isRemoteRestrictStyle(styleId).not()) { return@launch }
            styleGreetingTitleMap[currentStyleId]?.let { return@launch }
            RestrictWatermarkMasterGreetingDataHelper.getGreetingByStyleId(currentStyleId)?.greetingButtonTextId?.let { textId ->
                RestrictWatermarkMasterWordDataHelper.getWordTextByWordId(textId)?.let {
                    styleGreetingTitleMap[currentStyleId] = it
                    return@launch
                }
            }
            styleGreetingTitleMap[currentStyleId] = app.resources?.getString(getBlessResourceId()) ?: TextUtil.EMPTY_STRING
        }
    }

    /**
     * 是否使用P3色域
     * 注意：是否应该使用P3色域，除了图片是否为P3色域，也需要判断水印资源本身是否包含P3色域的配置，如果资源本身不支持P3色域，则不使用P3色域
     */
    private fun isStyleUseP3ColorGamut(style: WatermarkMasterStyle?): Boolean {
        val isP3ColorGamut = vmBus?.get<Boolean>(TOPIC_USE_P3_COLOR_GAMUT) ?: return false
        return style?.isSupportP3ColorGamut(isP3ColorGamut) ?: false
    }

    companion object {
        private const val DATA_REGEX = "(\\d{4}).(\\d{1,2}).(\\d{1,2})"
        private const val DATA_REGEX_GROUP_YEAR = 1
        private const val DATA_REGEX_GROUP_MONTH = 2
        private const val DATA_REGEX_GROUP_DAY = 3

        // 相机预览图默认影像参数 67mm  f/2.6  1/200s  ISO500
        private const val DEFAULT_SHOOT_INFO_FOCAL = "67"
        private const val DEFAULT_SHOOT_INFO_APERTURE = "2.6"
        private const val DEFAULT_SHOOT_INFO_EXPOSURE = "0.005"
        private const val DEFAULT_SHOOT_INFO_ISO = "500"
        private const val IS_RESTRICT_WATERMARK_DOWNLOADED = "is_restrict_watermark_downloaded"

        /**
         * 默认相机需要禁用的水印样式
         * 由于2025毕业季未配置相机直出所需的资源，且上线时间与OS16.0有重叠，故暂时在相机中禁用毕业季水印样式。
         * 待2025毕业季下线后（2025年7月1日），可删除此处代码  Marked By SuLun
         */
        private const val DEFAULT_CAMERA_FORBIDDEN_STYLES = "restrict_graduation_season_2025_style_1," +
                "restrict_graduation_season_2025_style_2,restrict_graduation_season_2025_style_3"

        const val TAG = "WatermarkMasterVM"
        const val PERSONALIZED_DATA_FROM_CAMERA = "personalized_data_from_camera"
        const val PERSONALIZED_DATA_STYLE = "personalized_data_style"
        const val PERSONALIZED_DATA_WATERMARK_INFO = "personalized_data_watermark_info"
        const val PERSONALIZED_DATA_GREETING_TITLE = "personalized_data_greeting_title"
        const val PERSONALIZED_DATA_P3_ENABLED = "personalized_data_p3_enabled"
        const val ANIM_TYPE_INVALID = -1
        const val INDEX_NONE = -1

        private const val CAMERA_PKG = "com.oplus.camera"
        private const val ACTION_CAMERA_WATERMARK_RESOURCE_AIDL = "com.oplus.ai.master.watermark.aidl"
        const val ACTION_CAMERA_WATERMARK_RESOURCE = "com.oplus.camera.ai.master.watermark.resource"
        const val CAMERA_WATERMARK_PHOTO_STYLE = "camera_watermark_photo_style_id"
        const val CAMERA_WATERMARK_VIDEO_STYLE = "camera_watermark_video_style_id"
        const val CAMERA_WATERMARK_PHOTO_URI_KEY = "camera_watermark_photo_uri_key"
        const val CAMERA_WATERMARK_PHOTO_MD5_KEY = "camera_watermark_photo_md5_key"
        const val CAMERA_WATERMARK_VIDEO_URI_KEY = "camera_watermark_video_uri_key"
        const val CAMERA_WATERMARK_VIDEO_MD5_KEY = "camera_watermark_video_md5_key"

        val NONE = R.id.picture3d_editor_id_watermark_master_none
        val HASSELBLAD = R.id.picture3d_editor_id_watermark_master_hasselblad
        val FRAME = R.id.picture3d_editor_id_watermark_master_frame
        val REALME_BRAND = R.id.picture3d_editor_id_watermark_master_realme_brand
        val TEXT = R.id.picture3d_editor_id_watermark_master_text
        val PRIVACY = R.id.picture3d_editor_id_watermark_master_privacy
        val PRIVACY_CUSTOM = R.id.picture_editor_privacy_watermark_custom
        val RESTRICT = R.id.picture3d_editor_id_watermark_master_restrict

        // 本地水印按钮列表
        val LOCAL_BUTTON_VIEW_ID_LIST = mutableListOf(
            HASSELBLAD,
            FRAME,
            REALME_BRAND,
            TEXT,
            PRIVACY,
            PRIVACY_CUSTOM
        )

        //限定水印按钮列表
        val RESTRICT_BUTTON_LIST = mutableListOf<WatermarkTypeItemViewData>()
        val RESTRICT_BUTTON_VIEW_ID_LIST = mutableListOf<Int>()

        // 水印添加loading延时时间，300ms后展示loading,最少200ms后隐藏loading
        private const val LOADING_DIALOG_SHOW_DELAY_TIME: Long = 300L
        private const val LOADING_DIALOG_HIDE_STAY_TIME: Long = 200L

        /**
         * 获取祝福语词条
         * 内销：小布送福签，外销没有小布改为：随机送祝福
         */
        fun getBlessResourceId(): Int {
            return if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN)) {
                R.string.picture_editor_text_watermark_spring_festival_lots
            } else {
                R.string.photoeditorpage_water_random_bless
            }
        }
    }
}
