/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - DefaultThumbnailListener.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/5/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/5/6  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.videoeditorpage.memories.imageloader;

import android.widget.ImageView;

import java.lang.ref.WeakReference;

public class DefaultThumbnailListener implements ThumbnailListener {
    private WeakReference<ImageView> mImageViewWr;

    public DefaultThumbnailListener(ImageView imageView) {
        this.mImageViewWr = new WeakReference<>(imageView);
    }

    @Override
    public void updateThumbnail(ThumbnailRespond thumbnailInfo) {
        ImageView imageView = mImageViewWr.get();
        if (imageView == null) {
            return;
        }
        if (thumbnailInfo == null) {
            return;
        }
        Object tag = imageView.getTag();
        if ((tag != null) && (!tag.equals(thumbnailInfo.getItem()))) {
            return;
        }
        imageView.setImageBitmap(thumbnailInfo.getResult());
    }

    public ImageView getTarget() {
        return mImageViewWr.get();
    }
}
