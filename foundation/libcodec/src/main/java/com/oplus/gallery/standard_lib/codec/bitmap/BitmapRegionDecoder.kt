/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - BitmapRegionDecoder.kt
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/26
 ** Author      : YongQ<PERSON>.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/26  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec.bitmap

import android.graphics.BitmapFactory
import android.graphics.Rect
import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.foundation.util.display.ColorModelManager
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.RegionDecoder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import java.io.FileDescriptor

class BitmapRegionDecoder() : IRegionDecoder {
    private var regionDecoder: RegionDecoder? = null
    private var state = STATE_NONE
    private var startDecodeCount = 0
    private var tombstone: BreakpadTombstone? = null
    private var srcWidth = 0
    private var srcHeight = 0

    companion object {
        const val TAG = "BitmapRegionDecoder"
        private const val STATE_NONE = 0
        private const val STATE_DECODING = 1
        private const val STATE_RECYCLED = 2
        private const val STATE_DECODED = 3
    }

    constructor(fd: FileDescriptor) : this() {
        regionDecoder = RegionDecoder.decodeFileDescriptor(fd)
    }

    constructor(filePath: String, decodeFileDescriptor: Boolean = false) : this() {
        regionDecoder = if (decodeFileDescriptor) {
            RegionDecoder.decodeFileDescriptor(filePath)
        } else {
            RegionDecoder.decodeFile(filePath)
        }
    }

    constructor(data: ByteArray, offset: Int, length: Int) : this() {
        regionDecoder = RegionDecoder.decodeByteArray(data, offset, length)
    }

    override fun createDecoder() {
    }

    override fun destroyDecoder() {
        if (regionDecoder?.isSupportMultiRegionDecode != false) {
            return
        }
        synchronized(this) {

            if ((state != STATE_DECODING) && (state != STATE_RECYCLED)) {
                regionDecoder?.recycle()
            }
            state = STATE_RECYCLED
        }
    }

    override fun isYuvImage(fd: Int): Boolean {
        return false
    }

    override fun decodeRegion(rect: Rect, options: BitmapFactory.Options?, isDirectBuffer: Boolean): ImageData? {
        val time = System.currentTimeMillis()
        synchronized(this) {
            if (state == STATE_RECYCLED) {
                return null
            }
            state = STATE_DECODING
            startDecodeCount++
        }
        var bitmap = regionDecoder?.decodeRegion(rect, options)
        synchronized(this) {
            startDecodeCount--
            if (state == STATE_RECYCLED) {
                if (startDecodeCount == 0) {
                    destroyDecoder()
                }
            } else {
                state = STATE_DECODED
            }
        }
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, LogFlag.DL) {
                "decodeRegion. rect=$rect, inSampleSize=${options?.inSampleSize}, consume=${System.currentTimeMillis() - time}"
            }
        }
        if ((options != null) && options.inJustDecodeBounds) {
            return null
        }
        if (bitmap == null) {
            GLog.e(TAG, LogFlag.DL, "decodeRegion bitmap is null")
            return null
        }
        // HaiQuan.Li Add for OPLUS_FEATURE_COLOR_SPACE
        if (!bitmap.isMutable) {
            val mutableBitmap = bitmap.copy(bitmap.getConfigSafely(), true)
            bitmap.recycle()
            bitmap = mutableBitmap
        }
        // HaiQuan.Li Add end
        ColorModelManager.adjustBitmapColorSpaceIfNeeded(bitmap)
        return ImageData(bitmap)
    }

    override fun getWidth(): Int {
        regionDecoder?.let {
            synchronized(this) {
                if (state == STATE_RECYCLED) {
                    return 0
                }
                state = STATE_DECODING
                startDecodeCount++
            }
            if (srcWidth <= 0 && !it.isRecycled) {
                srcWidth = it.width
            }
            synchronized(this) {
                startDecodeCount--
                if (state == STATE_RECYCLED) {
                    if (startDecodeCount == 0) {
                        destroyDecoder()
                    }
                } else {
                    state = STATE_DECODED
                }
            }
        }
        return srcWidth
    }

    override fun getHeight(): Int {
        regionDecoder?.let {
            synchronized(this) {
                if (state == STATE_RECYCLED) {
                    return 0
                }
                state = STATE_DECODING
                startDecodeCount++
            }
            if ((srcHeight <= 0) && !it.isRecycled) {
                srcHeight = it.height
            }
            synchronized(this) {
                startDecodeCount--
                if (state == STATE_RECYCLED) {
                    if (startDecodeCount == 0) {
                        destroyDecoder()
                    }
                } else {
                    state = STATE_DECODED
                }
            }
        }
        return srcHeight
    }

    protected fun finalize() {
        destroyDecoder()
    }

    override fun isRecycled(): Boolean {
        synchronized(this) {
            if (state == STATE_RECYCLED) {
                return true
            }
        }
        return !isValid() || (regionDecoder?.isRecycled == true)
    }

    override fun isValid(): Boolean {
        return regionDecoder != null
    }

    override fun getDecoderName(): String = TAG

    override fun isSupportMultiRegionDecode(): Boolean {
        return regionDecoder?.isSupportMultiRegionDecode ?: true
    }

    override fun setTombstone(tombstone: BreakpadTombstone) {
        this.tombstone = tombstone
    }

    override fun getTombstone(): BreakpadTombstone? {
        return this.tombstone
    }
}