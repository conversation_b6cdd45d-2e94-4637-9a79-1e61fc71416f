/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : FilterMenuHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/4/1
 ** Author      : 80412338
 ** TAG         : FilterMenuHelper
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80412338                       2025/4/1       1.0       FilterMenuHelper
 *********************************************************************************/

package com.oplus.gallery.timelinepage.menu

import android.app.Activity
import android.widget.ImageView
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.isVisible
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.poplist.PopupMenuConfigRule
import com.coui.appcompat.poplist.PopupMenuConfigRule.BARRIER_FROM_LEFT
import com.coui.appcompat.poplist.PopupMenuConfigRule.BARRIER_FROM_RIGHT
import com.oplus.gallery.basebiz.helper.setRoundCornerBgColor
import com.oplus.gallery.basebiz.widget.AnchorFrameLayout
import com.oplus.gallery.basebiz.widget.BlurButtonWrapperView
import com.oplus.gallery.business_lib.controller.IPersonalFilter
import com.oplus.gallery.business_lib.model.data.filter.DataFilterType
import com.oplus.gallery.timelinepage.R
import com.oplus.gallery.timelinepage.entry.FilterMenuItemEntry
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 筛选Menu帮助类
 *
 * 基本逻辑：
 *  - 全部
 *    1. 默认展示全部
 *    2. 非“图片/视频/收藏”，自动选中“全部”
 *
 *  - 图片/视频/收藏
 *    1. 选中后自动取消选中“全部”
 *    2. 可以多选:图片和视频是或的关系 ,收藏是且的关系
 */
class FilterMenuHelper(
    activity: Activity?,
    menuAnchorView: AnchorFrameLayout?,
    filterWrapperView: BlurButtonWrapperView?,
    highLightFilterIconView: ImageView?,
    filterListener: IPersonalFilter?
) {
    private var activity: Activity? = null
    private var menuAnchorView: AnchorFrameLayout? = null
    private var filterWrapperView: BlurButtonWrapperView? = null
    private var highLightFilterIconView: ImageView? = null
    private var filterListener: IPersonalFilter? = null
    private var filterPopupWindow: COUIPopupListWindow? = null
    private var filterItemList: MutableList<PopupListItem> = ArrayList()
    private val menuList: MutableList<FilterMenuItemEntry> = mutableListOf()
    private val checkIndexList: MutableList<Int> = mutableListOf()

    /**
     * Called when Menu show or dismiss
     *
     * @param isShow true: show, false: dismiss
     */
    var onMenuStateUpdated: ((Boolean) -> Unit)? = null

    init {
        this.activity = activity
        this.menuAnchorView = menuAnchorView
        this.filterWrapperView = filterWrapperView
        this.filterListener = filterListener
        this.highLightFilterIconView = highLightFilterIconView
        initData()
    }

    fun popupIsShowing(): Boolean {
        return filterPopupWindow?.isShowing == true
    }

    fun dismissPopup() {
        filterPopupWindow?.dismiss()
    }

    /**
     * 显示Popup
     *
     * @param isEnablePopupMenuRule 是否自定义Rule生效（默认不启用自定义Rule，按照COUI默认方式显示）
     * @param alignRule 自定义对齐Rule
     */
    fun showPopup(isEnablePopupMenuRule: Boolean = false, alignRule: AlignRule? = null) {
        if (filterPopupWindow == null) {
            initPopupWindow()
        }
        updateMenu()
        onMenuStateUpdated?.invoke(true)
        if (filterPopupWindow?.isShowing == true) {
            filterPopupWindow?.refresh()
            return
        }

        if (activity?.isFinishing == true || activity?.isDestroyed == true) return

        menuAnchorView.let {
            it?.barrierType = alignRule?.value()
            (it as? PopupMenuConfigRule)?.popupMenuRuleEnabled = isEnablePopupMenuRule
        }
        filterPopupWindow?.show(menuAnchorView)
    }

    private fun updateMenu() {
        filterItemList.clear()
        filterItemList.apply {
            val builder = PopupListItem.Builder()
            for (i in 0 until menuList.size) {
                builder.setTitle(activity?.resources?.getString(menuList[i].contentId) ?: "")
                    .setIsEnable(true)
                    .setSubMenuItemList(null)

                val isChecked = checkIndexList.contains(i)
                builder.setIsChecked(isChecked)
                val iconId = if (isChecked) {
                    menuList[i].selectedIconId
                } else {
                    menuList[i].normalIconId
                }
                builder.setIcon(activity?.let { AppCompatResources.getDrawable(it, iconId) })

                val groupId = if (i == TYPE_ALL) FILTER_GROUP_ALL else FILTER_GROUP_SINGLE_TYPE
                builder.setGroupId(groupId)

                add(builder.build())
            }

            filterPopupWindow?.apply {
                itemList = filterItemList
            }
        }
    }

    /**
     * 根据当前收藏菜单选项更新相应按钮的状态
     */
    fun changeFilterButtonState() {
        if (checkIndexList.isEmpty()) {
            checkIndexList.add(TYPE_ALL)
        }
        val iconId = if (checkIndexList.size > 1) {
            R.drawable.timeline_ic_sort_all_highlight
        } else {
            when (checkIndexList.first()) {
                TYPE_PHOTO -> R.drawable.timeline_ic_sort_picture_highlight
                TYPE_VIDEO -> R.drawable.timeline_ic_sort_video_highlight
                TYPE_FAVOURITE -> R.drawable.timeline_ic_sort_favourite_highlight
                else -> R.drawable.timeline_ic_sort_all_highlight
            }
        }
        val isHighLight = !((checkIndexList.size == 1) && checkIndexList.first() == TYPE_ALL)
        filterWrapperView?.setTag(com.oplus.gallery.foundation.util.R.id.tag_is_highlight, isHighLight)
        if (!isHighLight) {
            // 非高亮状态，显示沉浸式按钮，仅修改显隐逻辑
            filterWrapperView?.isVisible = true
            highLightFilterIconView?.isVisible = false
            return
        }
        filterWrapperView?.isVisible = false
        highLightFilterIconView?.let {
            it.isVisible = true
            it.setImageResource(iconId)
            it.setRoundCornerBgColor(activity?.getColor(com.support.appcompat.R.color.coui_color_primary_blue) ?: 0)
        }
    }

    private fun changeCheckIndex(pos: Int) {
        if (pos == TYPE_ALL) {
            /* 点击全部
               1.1-如果之前已选中全部就不反应 */
            if (checkIndexList.contains(pos)) return
            // 1.2-如果之前未选中就清除其他选中项并选中全部
            checkIndexList.clear()
            checkIndexList.add(pos)
        } else {
            /* 点击其他
               2.1-如果之前已选中，就反选掉 */
            if (checkIndexList.contains(pos)) {
                checkIndexList.remove(pos)
                // (1)-如果反选后无其他选中项，则选中全部 (2)-如果反选后有其他选项，直接结束
                if (checkIndexList.isEmpty()) {
                    checkIndexList.add(TYPE_ALL)
                }
            } else {
                // 2.2-如果之前未选中，直接选中
                checkIndexList.add(pos)
                // (1)-如果之前选中全部，则取消选中全部 (2)-如果之前没选中全部，直接结束
                if (checkIndexList.contains(TYPE_ALL)) {
                    checkIndexList.remove(TYPE_ALL)
                }
            }
        }
    }

    /**
     * 将菜单选择项转换成数据过滤项
     */
    private fun getFilterSelectResult(pos: Int): Map<DataFilterType.DataFilterProperty, Set<Int>> {
        changeCheckIndex(pos)
        val resultFilters: MutableMap<DataFilterType.DataFilterProperty, Set<Int>> = mutableMapOf()
        val selectMediaType: MutableSet<Int> = mutableSetOf()
        val selectOther: MutableSet<Int> = mutableSetOf()
        checkIndexList.forEach {
            when (it) {
                TYPE_PHOTO -> selectMediaType.add(DataFilterType.PropertyMediaTypeValue.VALUE_IMAGES)
                TYPE_VIDEO -> selectMediaType.add(DataFilterType.PropertyMediaTypeValue.VALUE_VIDEOS)
                TYPE_FAVOURITE -> selectOther.add(DataFilterType.PropertyOtherValue.VALUE_FAVORITES)
                else -> {}
            }
        }

        // 图片/视频类型筛选项
        if (selectMediaType.isNotEmpty()) {
            resultFilters[DataFilterType.DataFilterProperty.MEDIA_TYPE] = selectMediaType.toSet()
        }

        // 收藏筛选项
        if (selectOther.isNotEmpty()) {
            resultFilters[DataFilterType.DataFilterProperty.OTHER] = selectOther.toSet()
        }
        return resultFilters
    }

    private fun initPopupWindow() {
        filterPopupWindow = COUIPopupListWindow(activity).apply {
            setUseBackgroundBlur(true)
            setDismissTouchOutside(true)
            setOnItemClickListener { _, _, pos, _ ->
                // 处理菜单点击逻辑
                if (isShowing) {
                    val filterSelects: Map<DataFilterType.DataFilterProperty, Set<Int>> = getFilterSelectResult(pos)
                    filterListener?.handleFilterResult(filterSelects)
                    updateMenu()
                    onMenuStateUpdated?.invoke(true)
                    refresh()
                }
            }

            setOnDismissListener {
                /**
                 * Dismiss 时，禁用自定义规则，避免影响同页面其他COUIPopup的显示规则
                 *
                 * - 这是COUI的一个坑点,自定义规则会对这个页面内所有COUIPopup生效，详见COUI示例问题分析
                 * - 文档路径：https://odocs.myoas.com/docs/klDQAMVcLVk4fPAM?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
                 */
                menuAnchorView.let { (it as? PopupMenuConfigRule)?.popupMenuRuleEnabled = false }
                changeFilterButtonState()
                onMenuStateUpdated?.invoke(false)
            }
        }
    }

    private fun initData() {
        menuList.add(
            FilterMenuItemEntry(
                BasebizR.string.base_media_type_image,
                R.drawable.timeline_ic_sort_picture,
                R.drawable.timeline_ic_sort_picture_selected
            )
        )
        menuList.add(
            FilterMenuItemEntry(
                BasebizR.string.model_camera_video_name,
                R.drawable.timeline_ic_sort_video,
                R.drawable.timeline_ic_sort_video_selected
            )
        )
        menuList.add(
            FilterMenuItemEntry(
                BasebizR.string.base_favorites,
                R.drawable.timeline_ic_sort_favourite,
                R.drawable.timeline_ic_sort_favourite_selected
            )
        )
        menuList.add(
            FilterMenuItemEntry(
                BasebizR.string.main_filter_all,
                R.drawable.timeline_ic_sort_all,
                R.drawable.timeline_ic_sort_all_selected
            )
        )

        /**
         * [initData]时，目前已知可能已存在筛选项的场景:
         *
         * 1. 筛选后切换亮暗色；
         * 2. 筛选后，退出相册，然后在最近任务重清除掉相册(不一定会kill掉相册)，然后重新进相册；
         *
         * 上述场景已存在筛选项的原因是：相册进程没有被kill，但是Fragment会重建，而筛选是全局缓存，也就是在Fragment 创建的时候就可能存在筛选项数据
         */
        filterListener?.getAllPersonalFilters()?.let { parseAllPersonalFiltersToCheckIndexIfNeed(it) }

        if (checkIndexList.isEmpty()) {
            checkIndexList.add(TYPE_ALL)
        }
        changeFilterButtonState()
    }

    /**
     * 将数据筛选项解析成对应的菜单项
     */
    private fun parseAllPersonalFiltersToCheckIndexIfNeed(allFilters: Map<DataFilterType.DataFilterProperty, Set<Int>>) {
        if (allFilters.containsKey(DataFilterType.DataFilterProperty.MEDIA_TYPE)) {
            val values = allFilters[DataFilterType.DataFilterProperty.MEDIA_TYPE]
            if (values?.contains(DataFilterType.PropertyMediaTypeValue.VALUE_IMAGES) == true) {
                checkIndexList.add(TYPE_PHOTO)
            }

            if (values?.contains(DataFilterType.PropertyMediaTypeValue.VALUE_VIDEOS) == true) {
                checkIndexList.add(TYPE_VIDEO)
            }
        }

        if (allFilters.containsKey(DataFilterType.DataFilterProperty.OTHER)) {
            val values = allFilters[DataFilterType.DataFilterProperty.OTHER]
            if (values?.contains(DataFilterType.PropertyOtherValue.VALUE_FAVORITES) == true) {
                checkIndexList.add(TYPE_FAVOURITE)
            }
        }
    }

    /**
     * 自定义对齐规则
     */
    enum class AlignRule(rule: Int) {
        /**
         * 锚点左侧对齐
         */
        ALIGN_ANCHOR_LEFT(BARRIER_FROM_LEFT),

        /**
         * 锚点右侧对齐
         */
        ALIGN_ANCHOR_RIGHT(BARRIER_FROM_RIGHT);

        private val ruleValue: Int = rule

        fun value(): Int = ruleValue
    }

    companion object {

        /**
         * 筛选类型 - 图片
         */
        const val TYPE_PHOTO = 0

        /**
         * 筛选类型 - 视频
         */
        const val TYPE_VIDEO = 1

        /**
         * 筛选类型 - 收藏
         */
        const val TYPE_FAVOURITE = 2

        /**
         * 筛选类型 - 全部
         */
        const val TYPE_ALL = 3

        /**
         * 菜单分组类型 - 除全部以外的项目一组
         */
        const val FILTER_GROUP_SINGLE_TYPE = 1

        /**
         * 菜单分组类型 - 全部单独一组
         */
        const val FILTER_GROUP_ALL = 2
    }
}