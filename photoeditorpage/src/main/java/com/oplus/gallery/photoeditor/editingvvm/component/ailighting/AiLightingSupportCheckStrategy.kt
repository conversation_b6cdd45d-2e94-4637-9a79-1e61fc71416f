/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AiLightingSupportCheckStrategy
 ** Description : AI 补光功能支持性检查策略
 ** Version     : 1.0
 ** Date        : 2025/3/3
 ** Author      : gary@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80411420        2025/3/3    1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.ailighting

import android.content.Context
import android.os.Bundle
import android.util.Size
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkAuthorizationInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.PrivacyInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPrivacyInterceptor
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.foundation.archv2.bus.IViewModelBus
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.DETECTOR_NAME_KEY_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_LIGHTING_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_CLOUD_EDITOR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.framework.abilities.download.AIUnitDownloadWord
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitAvailableInterceptor
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitDisabledInterceptor
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitLoginInterceptor
import com.oplus.gallery.photoeditor.editingvvm.component.aiunit.AIUnitOfflineInterceptor
import com.oplus.gallery.photoeditor.editingvvm.detect.DetectingResult
import com.oplus.gallery.photoeditor.editingvvm.detect.HasPersonState
import com.oplus.gallery.photoeditor.editingvvm.notification.AIFuncExportPrivacyInterceptor
import com.oplus.gallery.photoeditor.editingvvm.notification.MediaRatioLimitInterceptor
import com.oplus.gallery.photoeditor.editingvvm.notification.MediaSizeLimitInterceptor

/**
 * Ai lighting support check strategy
 *
 * @property detectingResult 人物主体检测结果
 * @property imageSize 图片大小
 * @property viewModelBus
 * @constructor Create empty Ai lighting support check strategy
 */
internal class AiLightingSupportCheckStrategy(
    private val detectingResult: DetectingResult,
    private val imageSize: Size,
    private val viewModelBus: IViewModelBus?
) : ISupportCheckStrategy {

    private var chain: RealInterceptorChain? = null

    private val aiLightingPluginState by lazy {
        AIUnitPluginState(
            AIUnitPlugin.AI_LIGHTING.downloadPlugin,
            AI_LIGHTING_DOWNLOAD_STATE,
            UPDATE_AI_LIGHTING_SHOW_TIMESTAMP
        )
    }

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        updateConfig(context, AI_LIGHTING_DETECT_STATE)

        val interceptors = getInterceptors(context, postNotificationAction)
        chain = RealInterceptorChain(interceptors,
            onSuccessCallback = {
                val isModelDownloadSuccess = it.getBoolean(AIUnitModelDownloadInterceptor.MODEL_DOWNLOAD_SUCCESS)
                // 如果当次是模型下载成功的场景，返回false,不做后续进二级页的事务
                if (isModelDownloadSuccess) {
                    function(false)
                } else {
                    if ((detectingResult as? DetectingResult.Person)?.hasPersonState == HasPersonState.NO_PERSON) {
                        // 如果主体检测不满足，则直接弹出 Toast提示信息
                        val toastAction =
                            NotificationAction.ToastAction(R.string.picture3d_editor_text_ai_lighting_photo_not_person_try_again)
                        postNotificationAction(toastAction)
                        function(false)
                    } else {
                        //主体未检测或者检测成功，则继续执行后续流程，在补光界面再做检测
                        function(true)
                    }
                }
            },
            onFailCallback = { function(false) }
        )
        chain?.proceed(Bundle())
    }

    /**
     * 更新配置信息
     *
     * @param context
     * @param pluginState 本地存储的插件状态信息
     */
    private fun updateConfig(context: Context, pluginState: String) {
        context.getAppAbility<ISettingsAbility>()?.updateBlockingConfig(context, pluginState)
    }

    /**
     * mark by dengwenhao:这里需要添加主体预识别的逻辑
     */
    private fun getInterceptors(context: Context, postNotificationAction: (NotificationAction) -> Unit): List<IInterceptor<Bundle, Unit>> =
        listOf(
            // 无网络拦截
            NetworkInterceptor(postNotificationAction),
            // AIUnit停用拦截
            AIUnitDisabledInterceptor(context, postNotificationAction),
            // 功能下线拦截
            AIUnitOfflineInterceptor(
                R.string.picture3d_editor_text_ai_lighting_offline_title,
                AI_LIGHTING_DETECT_STATE,
                postNotificationAction
            ),
            // 用户须知检查
            AIFuncExportPrivacyInterceptor(
                context,
                ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_LIGHTING,
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_ai_lighting_statement,
                postNotificationAction
            ),
            // 隐私权限检查
            PrivacyInterceptor(
                context,
                listOf(AUTHORIZE_CLOUD_EDITOR, AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD),
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_ai_lighting_statement,
                postNotificationAction
            ),
            // 网络权限
            NetworkAuthorizationInterceptor(
                com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_ai_lighting,
                postNotificationAction
            ),
            // 图片尺寸限制弹窗，对于小于256x256的照片
            MediaSizeLimitInterceptor(
                context,
                imageSize,
                Constants.AIFunc.AI_LIGHTING_SUPPORTED_MIN_SIZE,
                Int.MAX_VALUE,
                R.string.picture3d_editor_text_ai_lighting_photo_is_not_supported_reason_too_small,
                postNotificationAction
            ),
            // 图片比例限制弹窗，对于长宽比大于10:1的图片，弹窗提示
            MediaRatioLimitInterceptor(
                context,
                imageSize,
                Constants.AIFunc.AI_LIGHTING_SUPPORTED_MAX_RATIO,
                R.string.picture3d_editor_text_ai_lighting_photo_is_not_supported_reason_illegal_size,
                postNotificationAction
            ),
            // AIUnit用户须知拦截器
            AIUnitPrivacyInterceptor(context),
            // 登录拦截
            AIUnitLoginInterceptor(context, DETECTOR_NAME_KEY_AI_LIGHTING),
            // 功能不可用拦截
            AIUnitAvailableInterceptor(
                R.string.picture3d_editor_text_ai_lighting_offline_title,
                AI_LIGHTING_DETECT_STATE,
                postNotificationAction
            ),
            // 模型是否需要下载拦截
            AIUnitModelDownloadInterceptor(
                context,
                aiLightingPluginState,
                createWord(),
                postNotificationAction
            )
        )

    /**
     * 创建下载提示词
     */
    private fun createWord(): AIUnitDownloadWord {
        return AIUnitDownloadWord(
            R.string.picture3d_editor_text_ai_lighting_install_title,
            R.string.picture3d_editor_text_ai_lighting_update_desce,
            R.string.picture3d_editor_text_ai_lighting_install_desce,
            R.string.picture3d_editor_text_ai_lighting_download_fail,
            R.string.picture3d_editor_text_ai_lighting_install_fail,
            R.string.picture3d_editor_text_ai_lighting_updating,
            R.string.picture3d_editor_text_ai_lighting_downloading,
            R.string.picture3d_editor_text_ai_lighting_update_finish,
            R.string.picture3d_editor_text_ai_lighting_download_finish
        )
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }
}