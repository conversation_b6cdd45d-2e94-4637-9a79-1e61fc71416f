/**********************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File        : xxxxxx.kt
 *  ** Description : xxxxxx
 *  ** Version     : 1.0
 *  ** Date        : 2020/10/26
 *  ** Author      : Yong.Ding@Apps.Gallery3D
 *  **
 *  ** ---------------------Revision History: ----------------------------
 *  **  <author>                 <data>      <version>  <desc>
 *  **  Yong.Ding@Apps.Gallery3D  2020/10/02  1.0       xxxxxx
 *  **********************************************************************
 */

package com.oplus.gallery.standard_lib.codec.yuv.decoder

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.view.Surface
import com.oplus.gallery.addon.media.HeifDecodeFrameWrapper
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.NativeRenderer

private typealias ColorSpaceAdjuster = (ColorSpace) -> ColorSpace

class YuvData() : ImageData(), NativeRenderer {
    var bufferId: Long = 0
    var yuvData: ByteArray? = null
    var colorSpaceAdjuster: ColorSpaceAdjuster? = null

    companion object {
        private const val TAG = "YuvData"
    }

    constructor(decodeFrameWrapper: HeifDecodeFrameWrapper?) : this() {
        this.decodeFrameWrapper = decodeFrameWrapper
    }

    private var decodeFrameWrapper: HeifDecodeFrameWrapper? = null
        set(value) {
            field = value
            field?.let {
                isYuv = true
                frameWidth = it.frameWidth
                frameHeight = it.frameHeight
                yuvData = it.yuvData
                bufferId = it.bufferId
                renderer = this
            }
        }


    override fun isRecycled(): Boolean {
        return if (!isYuv) {
            bitmap?.isRecycled ?: false
        } else {
            decodeFrameWrapper?.isRecycled() ?: false
        }
    }

    @Synchronized
    override fun recycle() {
        decodeFrameWrapper?.let {
            if (!it.isRecycled()) {
                it.recycle()
            }
        }
        decodeFrameWrapper = null
    }

    override fun getConfig(): Bitmap.Config? {

        return null
    }

    override fun render(surface: Surface) {
        decodeFrameWrapper?.apply {
            val isDirectBuffer = (yuvData == null)
            val colorSpace = this.colorSpace
            val adjuster = colorSpaceAdjuster
            if ((colorSpace != null) && (adjuster != null)) {
                render(surface, isDirectBuffer, adjuster.invoke(colorSpace))
            } else {
                render(surface, isDirectBuffer)
            }
        }
    }

    protected fun finalize() {
        recycle()
    }

    override fun getColorSpace(): ColorSpace? {
        if (decodeFrameWrapper?.colorSpace == null) {
            return ColorSpace.get(ColorSpace.Named.SRGB)
        }
        return decodeFrameWrapper?.colorSpace
    }
}