/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  OliveVideoEngine
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2024/7/19
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2024/7/19      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.ColorSpace.Named
import android.graphics.SurfaceTexture
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import android.util.Rational
import android.util.Size
import android.view.SurfaceView
import androidx.annotation.ChecksSdkIntAtLeast
import androidx.annotation.WorkerThread
import com.cdv.utils.NvAndroidUtils
import com.meicam.sdk.NvsAVFileInfo
import com.meicam.sdk.NvsAudioResolution
import com.meicam.sdk.NvsLiveWindow.HDR_DISPLAY_MODE_HLG
import com.meicam.sdk.NvsLiveWindow.HDR_DISPLAY_MODE_PQ
import com.meicam.sdk.NvsLiveWindow.HDR_DISPLAY_MODE_SDR
import com.meicam.sdk.NvsRational
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsStreamingContext.COMPILE_USE_OPERATING_RATE
import com.meicam.sdk.NvsStreamingContext.COMPILE_VIDEO_ENCODER_NAME
import com.meicam.sdk.NvsStreamingContext.COMPILE_VIDEO_HDR_COLOR_TRANSFER
import com.meicam.sdk.NvsStreamingContext.IMAGE_GRABBER_META_DATA_KEY_IMAGE_FROM_SEEK
import com.meicam.sdk.NvsStreamingContext.IMAGE_GRABBER_META_DATA_KEY_TIMESTAMP
import com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_FLAG_COMPACT_MEMORY_MODE
import com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED
import com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoFrameRetriever
import com.meicam.sdk.NvsVideoFrameRetriever.CREATE_RETRIEVER_FLAG_PREFERENTIAL_DISABLE_COLOR_PRIMARIES
import com.meicam.sdk.NvsVideoFrameRetriever.CREATE_RETRIEVER_FLAG_PREFERENTIAL_EXPORT_HLG_RGB10A2_BITMAP
import com.meicam.sdk.NvsVideoFrameRetriever.CREATE_RETRIEVER_FLAG_PREFERENTIAL_USE_HARDWARE_READER
import com.meicam.sdk.NvsVideoResolution
import com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
import com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_AUTO
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_BT2020
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_DISPLAY_P3
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_HLG
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_SDR_VIDEO
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_ST2084
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_DOLBYVISION
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HDR10
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HDR10PLUS
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HLG
import com.meicam.sdk.NvsVideoStreamInfo.VIDEO_ROTATION_270
import com.meicam.sdk.NvsVideoStreamInfo.VIDEO_ROTATION_90
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.abilities.transform.channel.livephoto.OliveHdrVideoFx
import com.oplus.gallery.abilities.transform.channel.livephoto.TransformConfig
import com.oplus.gallery.basebiz.BuildConfig
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper.getBoolean
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.IExportVideoCallback
import com.oplus.gallery.business_lib.util.formatNvmFilePath
import com.oplus.gallery.foundation.hdrtransform.HdrTransformFactory
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_PQ
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.SRGB
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.isHdrGamma
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.math.MathUtil
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_EDIT_VIDEO_HDR_BRIGHTEN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_HDR_VISION_BRIGHTEN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE_ACCELERATE
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.adjust.IOliveVideoAdjustEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.adjust.OliveVideoAdjustEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.IOliveVideoEdgeEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.OliveVideoEdgeEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.VideoSourceDescriptor.*
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter.IOliveVideoFilterEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter.OliveVideoFilterEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.preview.OliveVideoWindow
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform.IOliveVideoTransformEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform.OliveVideoTransformEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.IOliveVideoWatermarkEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.OliveVideoWatermarkEditorImp
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter
import com.oplus.gallery.standard_lib.file.File
import java.util.Hashtable
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.concurrent.ConcurrentHashMap

class OliveVideoEngine(private val context: Context) : IOliveVideoEngine {

    /**
     * 在NvsStreamingContext关闭释放资源后，需要对nvsContext变量进行置null处理
     */
    private val nvsContext: NvsStreamingContext? by lazy {
        MeicamContextAliveCounter.requestContext(context, STREAMING_CONTEXT_FLAG, TAG)
    }
    private var playStartUs = 0L
    private var playEndUs = 0L
    private var nvsTimeline: NvsTimeline? = null
    private var oliveVideoInfo = OliveVideoInfo()

    private var nvsVideoTrack: NvsVideoTrack? = null

    private var nvsVideoClip: NvsVideoClip? = null

    override val hasInit: Boolean get() = (nvsTimeline != null) && (nvsVideoTrack != null) && (nvsVideoClip != null)

    private var hasSetupPreview = false
    private var hasSetupTexture = false
    private var filterEditor: IOliveVideoFilterEditor? = null
    private var adjustEditor: IOliveVideoAdjustEditor? = null
    private var transformEditor: IOliveVideoTransformEditor? = null
    private var watermarkEditor: IOliveVideoWatermarkEditor? = null
    private var videoEdgeEditor: IOliveVideoEdgeEditor? = null
    private var firstFrameReadyListener: (() -> Unit)? = null

    private var seekingListener: ((positionUs: Long) -> Unit)? = null

    private var videoSize: Size = Size(0, 0)

    private val fileInfoMap by lazy { ConcurrentHashMap<String, NvsAVFileInfo?>() }

    /**
     * 是否支持广色域
     */
    private val isWideColorGamut by lazy {
        ActivityLifecycle.getActivityList().lastOrNull()?.get()?.window?.isWideColorGamut ?: false
    }

    /**
     * 系统是否支持P010格式的硬件解码,不支持则考虑切换软解
     */
    private val isPlatformSupportP010Decode by lazy {
        NvAndroidUtils.isColorFormatSupportByMediaCodec(MimeTypeUtils.MIME_TYPE_VIDEO_HEVC, false, FORMAT_PO10).also {
            GLog.d(TAG, LogFlag.DL, "[isPlatformSupportP010Decode] support:$it")
        }
    }

    /**
     *导出视频状态回调
     */
    private var iExportVideoCallback: IExportVideoCallback? = null

    /**
     * 是否支持大图对HDR视频提亮
     */
    private val isSupportPhotoHdrVideoBrighten by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_HDR_VISION_BRIGHTEN)
    }

    /**
     * 设备是否支持hdr显示
     */
    @get:ChecksSdkIntAtLeast(api = Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private val isHdrSupported: Boolean by lazy { getBoolean(FEATURE_IS_SUPPORT_ULTRA_HDR) }

    /**
     * 视频文件保存完成状态
     */
    private val compileCallback = NvsStreamingContext.CompileCallback2 { timeline, isCanceled ->
        if (nvsTimeline !== timeline) {
            return@CompileCallback2
        }
        iExportVideoCallback?.onComplete(true)
    }

    private val nvsStreamingEngineCallback = object : NvsStreamingContext.StreamingEngineCallback {
        override fun onStreamingEngineStateChanged(state: Int) {
            GLog.d(TAG, LogFlag.DL, "[onStreamingEngineStateChanged] $state")
        }

        override fun onFirstVideoFramePresented(p0: NvsTimeline?) {
            GLog.d(TAG, LogFlag.DL, "[onFirstVideoFramePresented]")
            firstFrameReadyListener?.invoke()
            firstFrameReadyListener = null
        }
    }

    private val nvsSeekCallback = NvsStreamingContext.SeekingCallback { _, positionUs ->
        GLog.d(TAG, LogFlag.DL, "[onSeeking] position=$positionUs")
        seekingListener?.invoke(positionUs)
    }

    /**
     * Grabber相关接口的回调
     * 详见：[ImageGrabberEventCallback]
     */
    private val imageGrabberEventCallback by lazy {
        ImageGrabberEventCallback()
    }

    override val sourceUri: Uri?
        get() = _sourceUri
    private var _sourceUri: Uri? = null

    override val sourceDescriptor: VideoSourceDescriptor?
        get() = _sourceDescriptor
    private var _sourceDescriptor: VideoSourceDescriptor? = null

    override val isPlayed: Boolean
        get() = isVideoPlayed
    private var isVideoPlayed = false

    /**
     * 美摄的自定义协议串，不可对外，只与美摄通信使用
     * 详见[formatNvmFilePath]
     */
    private lateinit var meicamPrivatePath: String

    @Suppress("LongMethod")
    override fun initFromVideo(
        sourceUri: Uri,
        sourceDescriptor: VideoSourceDescriptor,
        oliveTransformConfig: OliveTransformConfig
    ): String? {
        if (hasInit) {
            GLog.w(TAG, LogFlag.DL, "[initFromVideo] sourceUri=$sourceUri already init")
            return "already init"
        }

        // 当前仅支持 混合文件（olive）
        if (sourceDescriptor !is MIX) {
            GLog.w(TAG, LogFlag.DL) { "[initFromVideo] unsupported. sourceDescriptor = VideoSourceDescriptor.NORMAL" }
            return "unsupported sourceDescriptor"
        }

        this._sourceUri = sourceUri
        this._sourceDescriptor = sourceDescriptor

        meicamPrivatePath = formatNvmFilePath(sourceUri, sourceDescriptor.offset, sourceDescriptor.length)

        // get video info and check
        val videoInfo: NvsAVFileInfo = nvsContext?.getAVFileInfo(meicamPrivatePath, NvsStreamingContext.AV_FILEINFO_EXTRA_INFO)
            ?: return "getVideoInfo failed from: $_sourceUri"

        val duration = videoInfo.getVideoStreamDuration(0)
        val size = videoInfo.getVideoStreamDimension(0).run { Size(width, height) }
        val rotation = videoInfo.getVideoStreamRotation(0)
        val bitCount = videoInfo.getVideoStreamComponentBitCount(0)
        val codecType = videoInfo.getVideoStreamCodecType(0)
        val hdrType = videoInfo.getVideoStreamHDRType(0)

        val canShowHdr = ApiLevelUtil.isAtLeastAndroidU() && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_EDIT_VIDEO_HDR_BRIGHTEN)

        // get colorspace
        val colorPrimaries = videoInfo.getVideoStreamColorPrimaries(0)
        val colorTranser = videoInfo.getVideoStreamColorTranfer(0)

        // 视频的原始色域
        val videoColorSpace = when {
            (colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTranser == COLOR_TRANSFER_HLG) -> BT2020_HLG
            (colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTranser == COLOR_TRANSFER_ST2084) -> BT2020_PQ
            (colorPrimaries == COLOR_PRIMARIES_DISPLAY_P3) && (colorTranser == COLOR_TRANSFER_SDR_VIDEO) -> DISPLAY_P3
            else -> null
        } ?: SRGB
        // 需要显示的色域。如果能够显示HDR，或者本来就不是HDR的色域，则使用原始的视频色域；否则使用美摄默认支持的SRGB色域
        val displayColorSpace = if ((canShowHdr || !videoColorSpace.isHdrGamma()) && (isWideColorGamut || videoColorSpace != DISPLAY_P3)) {
            videoColorSpace
        } else {
            SRGB
        }
        oliveVideoInfo = OliveVideoInfo(duration, size, rotation, bitCount, codecType, hdrType, videoColorSpace, displayColorSpace)

        // create timeline
        val resolution = NvsVideoResolution().apply {
            val isRotated = rotation == VIDEO_ROTATION_90 || rotation == VIDEO_ROTATION_270
            val imageHeightTemp = if (isRotated) size.width else size.height
            val imageWidthTemp = if (isRotated) size.height else size.width

            //此处代码是为了适配美摄对olive操作时图片要求宽对齐4高对齐2
            imageHeight = MathUtil.getAlignedStrideUpToMultipleOf2(imageHeightTemp)
            imageWidth = MathUtil.getAlignedStrideUpToMultipleOf4(imageWidthTemp)
            videoSize = Size(imageWidth, imageHeight)
            imagePAR = videoInfo.getVideoStreamPixelAspectRatio(0)
            // 如果视频显示的是HDR，并且能够显示HDR时，需要设置成VIDEO_RESOLUTION_BIT_DEPTH_AUTO; 当需要强制设置SDR timeline时，位深为8bit
            bitDepth = if (displayColorSpace.isHdrGamma() && !oliveTransformConfig.forceSdr) {
                VIDEO_RESOLUTION_BIT_DEPTH_AUTO
            } else {
                VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
            }
        }
        val fps = videoInfo.getVideoStreamFrameRate(0)
        val audioEditRes = NvsAudioResolution().apply {
            val hasAudioStream = videoInfo.audioStreamCount > 0
            sampleRate = if (hasAudioStream) videoInfo.getAudioStreamSampleRate(0) else SAMPLE_RETE
            channelCount = if (hasAudioStream) videoInfo.getAudioStreamChannelCount(0) else CHANNEL_COUNT
        }

        val nvsContext = nvsContext ?: return "nvsContext is null"
        nvsTimeline = nvsContext.createTimeline(resolution, fps, audioEditRes)

        // add video clip
        nvsVideoTrack = nvsTimeline?.appendVideoTrack()
        nvsVideoClip = nvsVideoTrack?.appendClip(meicamPrivatePath)

        // 判断是否需要相册自定义的下变换
        if ((oliveTransformConfig.hdrTransformData != null)
            && ApiLevelUtil.isAtLeastAndroidU()
            && displayColorSpace.isHdrGamma()
            && oliveTransformConfig.needFXTransformToSdr
            && HdrTransformFactory.isSupportHdrTransform()
        ) {
            val transformConfig = TransformConfig(true, oliveTransformConfig.hdrSdrRatioFunc)
            nvsVideoClip?.appendCustomFx(OliveHdrVideoFx(oliveTransformConfig.hdrTransformData, transformConfig))
            nvsVideoClip?.disableHDRTonemappingToSDR(true)
        } else if (videoColorSpace == DISPLAY_P3) {
            // 由于美摄当是sdr视频时会转成sRGB，所以需要禁止色域转换
            nvsVideoClip?.disableClipColorPrimariesConvert(true)
        }

        GLog.d(TAG, LogFlag.DL, "nvsTimeline: $nvsTimeline, nvsVideoTrack: $nvsVideoTrack, nvsVideoClip: $nvsVideoClip")

        // setup callback
        nvsContext.setSeekingCallback(nvsSeekCallback)
        nvsContext.setCompileCallback2(compileCallback)
        nvsContext.setStreamingEngineCallback(nvsStreamingEngineCallback)
        nvsContext.setImageGrabberCallback(imageGrabberEventCallback)
        filterEditor = OliveVideoFilterEditorImp(nvsTimeline, nvsVideoTrack, nvsVideoClip)
        adjustEditor = OliveVideoAdjustEditorImp(nvsTimeline, nvsVideoTrack, nvsVideoClip)
        transformEditor = OliveVideoTransformEditorImp(nvsContext, nvsTimeline, nvsVideoTrack, nvsVideoClip)
        watermarkEditor = OliveVideoWatermarkEditorImp(nvsContext, nvsTimeline, nvsVideoTrack, nvsVideoClip, videoSize)
        return null
    }

    /**
     * 获取回调接口
     */
    override fun registerExportVideoCallback(exportVieoCallback: IExportVideoCallback) {
        iExportVideoCallback = exportVieoCallback
    }

    /**
     * 释放回调资源
     */
    override fun unRegisterExportVideoCallback() {
        iExportVideoCallback = null
    }

    override fun destroy() {
        if (!hasInit) return
        GLog.d(TAG, LogFlag.DL) { "destroy: is called." }
        nvsVideoTrack?.removeAllClips()
        nvsTimeline?.videoTrackCount()?.let { trackCount ->
            for (i in 0 until trackCount) {
                nvsTimeline?.removeVideoTrack(i)
            }
        }
        nvsContext?.apply {
            setPlaybackCallback(null)
            setCompileCallback(null)
            setSeekingCallback(null)
            setCompileCallback2(null)
            setImageGrabberCallback(null)
            setStreamingEngineCallback(null)
            removeTimeline(nvsTimeline)
            MeicamContextAliveCounter.tryCloseContext(TAG)
        }
        nvsTimeline = null
        fileInfoMap.clear()
    }

    private fun checkInit() {
        if (!hasInit) {
            if (BuildConfig.DEBUG) {
                throw IllegalStateException("not init")
            } else {
                GLog.e(TAG, LogFlag.DL, "checkInit not init")
            }
        }
    }

    override fun getVideoSize(): Size {
        checkInit()
        return nvsTimeline?.let { timeline ->
            timeline.videoRes?.let { videoRes ->
                Size(videoRes.imageWidth, videoRes.imageHeight)
            }
        } ?: Size(0, 0)
    }

    override fun gatVideoFps(): Rational {
        checkInit()
        return nvsTimeline?.videoFps?.let { Rational(it.num, it.den) } ?: Rational.NaN
    }

    override fun changeVideoFps(fpsRational: Rational): Boolean {
        checkInit()
        val fpsNvsRational = NvsRational(fpsRational.numerator, fpsRational.denominator)
        return nvsTimeline?.changeVideoFps(fpsNvsRational) ?: false
    }

    override fun setupPreviewWindow(): SurfaceView? {
        checkInit()
        if (hasSetupPreview) throw IllegalStateException("setupPreviewWindow already setup")
        val liveWindow = OliveVideoWindow(context.applicationContext)
        val isSuccess = nvsContext?.connectTimelineWithLiveWindow(nvsTimeline, liveWindow) ?: false
        if (!isSuccess) return null
        hasSetupPreview = true
        return liveWindow
    }

    override fun setPreviewSurfaceTexture(surfaceTexture: SurfaceTexture?): Boolean {
        checkInit()
        if (hasSetupTexture) throw IllegalStateException("setPreviewSurfaceTexture already setup")
        return updatePreviewSurfaceTexture(surfaceTexture)
    }

    override fun updatePreviewSurfaceTexture(surfaceTexture: SurfaceTexture?, sizeLevel: Int): Boolean {
        surfaceTexture ?: return false
        // 当前仅针对HLG视频支持设置美摄输出的OES纹理支持显示
        val displayMode = when (oliveVideoInfo.displayColorSpace) {
            BT2020_HLG, DISPLAY_P3_HLG -> HDR_DISPLAY_MODE_HLG
            BT2020_PQ -> HDR_DISPLAY_MODE_PQ
            else -> HDR_DISPLAY_MODE_SDR
        }
        hasSetupTexture =
            nvsContext?.connectTimelineWithSurfaceTexture(nvsTimeline, surfaceTexture, NvsRational(1, sizeLevel.coerceAtLeast(1)), displayMode)
                ?: false
        GLog.d(TAG, LogFlag.DL, "updatePreviewSurfaceTexture connectTimelineWithSurfaceTexture  hasSetupTexture:$hasSetupTexture")
        return hasSetupTexture
    }

    override fun play(onFirstFrameReady: (() -> Unit)?): Boolean {
        checkInit()
        val isSuccess = nvsContext?.playbackTimeline(nvsTimeline!!, playStartUs, playEndUs, PREVIEW_SIZE_MODE, true, 0)
            ?: false
        if (isSuccess) {
            firstFrameReadyListener = onFirstFrameReady
        }
        GLog.d(TAG, LogFlag.DL, "play isSuccess:$isSuccess")
        isVideoPlayed = true
        return isSuccess
    }

    override fun setPlayRange(start: Long, end: Long) {
        checkInit()
        playStartUs = start.coerceIn(0, oliveVideoInfo.duration)
        playEndUs = end.coerceIn(0, oliveVideoInfo.duration)
    }

    override fun pause() {
        checkInit()
        val state: Int = nvsContext?.streamingEngineState ?: NvsStreamingContext.STREAMING_ENGINE_STATE_STOPPED
        if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
            nvsContext?.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_ASYNC)
        }
    }

    override fun resume() {
        checkInit()
        val state: Int = nvsContext?.streamingEngineState ?: NvsStreamingContext.STREAMING_ENGINE_STATE_STOPPED
        val isStopped = (state == NvsStreamingContext.STREAMING_ENGINE_STATE_STOPPED)
        val isSeeking = (state == NvsStreamingContext.STREAMING_ENGINE_STATE_SEEKING)
        if (isStopped || isSeeking) {
            nvsContext?.apply {
                playbackTimeline(
                    nvsTimeline, getTimelineCurrentPosition(nvsTimeline), playEndUs, PREVIEW_SIZE_MODE, true, 0
                )
            }
        }
    }

    override fun setMute(isMute: Boolean) {
        nvsVideoTrack?.let {
            for (i in 0..it.clipCount) {
                val clip = it.getClipByIndex(i)
                if (clip != null) {
                    if (isMute) {
                        clip.setVolumeGain(0f, 0f)
                    } else {
                        clip.setVolumeGain(1.0f, 1.0f)
                    }
                }
            }
        }
    }

    override fun stop() {
        if (!hasInit) return
        nvsContext?.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_ASYNC)
        isVideoPlayed = false
        GLog.d(TAG, LogFlag.DL, "stop")
    }

    override fun seekTo(time: Long): Boolean {
        checkInit()
        val seekToTime = time.coerceAtMost(oliveVideoInfo.duration - 1)
        val result = nvsContext?.seekTimeline(
            nvsTimeline,
            seekToTime,
            PREVIEW_SIZE_MODE,
            STREAMING_ENGINE_SEEK_FLAG_ZERO_TOLERANCE
            or NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SEEK_AND_GRAB_VIDEO_FRAME // 支持seek回帧的flag 详见[ImageGrabberEventCallback]
        ) ?: false
        GLog.d(TAG, LogFlag.DL, "seekToTime = $seekToTime")
        return result
    }

    override fun setSeekingListener(onSeekingListener: ((positionUs: Long) -> Unit)?) {
        seekingListener = onSeekingListener
    }

    override fun setSeekCapturedFrameListener(capturedFrameListener: ((positionUs: Long, Bitmap?) -> Unit)?) {
        imageGrabberEventCallback.capturedSeekFrameListener = capturedFrameListener
    }

    override fun getDuration(): Long {
        return nvsTimeline?.duration ?: 0
    }

    @Volatile
    private var lastGrabCoroutine: Continuation<*>? = null

    /**
     * 需要在UI线程调用
     * 从time line获取指定格式的图片
     * @param timeUs: 取图时刻
     * @param maxLength: 取图最大尺寸边长（影响取图分辨率）
     * @param config: 指定像素格式
     * @param colorSpace: 指定色彩空间
     */
    override suspend fun grabThumbnailAsync(timeUs: Long, maxLength: Int, config: Bitmap.Config?, colorSpace: ColorSpace?): Pair<Long, Bitmap?> {
        checkInit()
        val scale = NvsRational(maxLength, getVideoSize().maxLen())

        /*
           NOTE: 需要保证在获取到图像之前不会调用SeekTimeline(),
           如果在调用这个函数之后调用SeekTimeline的函数有可能收不到图像的回调，
           可以在这个函数之后调用Stop保证Grab image能执行成功
         */
        val time = nvsTimeline?.let {
            val duration = it.duration.takeIf { it - 1 > 0 } ?: oliveVideoInfo.duration
            timeUs.coerceIn(0, (duration - 1))
        } ?: timeUs

        val nvsContext = nvsContext ?: return Pair(timeUs, null)
        while (lastGrabCoroutine != null) {
            GLog.d(TAG, LogFlag.DL) { "grabThumbnailAsync: delay time." }
            delay(GRAB_DELAY_TIME_MS)
        }
        val resultBitmap = suspendCoroutine { continuation ->
            lastGrabCoroutine = continuation
            imageGrabberEventCallback.imageGrabberListener = { timestamp, bitmap ->
                GLog.d(TAG, LogFlag.DL) { "grabThumbnailAsync: $timestamp, colorSpace:${bitmap?.colorSpace}, $continuation" }
                continuation.resume(Pair(timestamp, bitmap))
            }
            // 根据美摄描述，[grabImageFromTimelineAsync]方法调用需要等上次方法调用结束后才能调用，否则其结果的回调将会丢失数据。
            val success = nvsContext.grabImageFromTimelineAsync(nvsTimeline, time, scale, config, getMeicamColorSpaceName(colorSpace), 0)
            if (!success) {
                GLog.e(TAG, LogFlag.DL) { "grabThumbnailAsync: grab false." }
                continuation.resume(Pair(timeUs, null))
            }
        }

        // lastGrabCoroutine关键对象锁最好保持主线程调用
        lastGrabCoroutine = null
        return resultBitmap
    }

    /**
     * 需要在UI线程调用
     */
    override suspend fun grabThumbnailsAsync(timesUS: List<Long>, maxLength: Int): Flow<Pair<Long, Bitmap?>> = flow {
        checkInit()
        timesUS.forEach { time ->
            val thumbnail = grabThumbnailAsync(time, maxLength)
            emit(thumbnail)
        }
    }

    override fun retrieveFrameFromSource(timeUs: Long, isHdrVideoOlive: Boolean): Bitmap? {
        val time = System.currentTimeMillis()

        // 视频是HDR且在平台不支持解码P010格式时，美摄使用硬解会解码出异常帧，此时需要走软解
        var flag = if (isPlatformSupportP010Decode || isHdrVideoOlive.not()) {
            CREATE_RETRIEVER_FLAG_PREFERENTIAL_USE_HARDWARE_READER
        } else {
            0
        }
        if (oliveVideoInfo.videoColorSpace == DISPLAY_P3) {
            // 如果色域是P3的，则解码原始帧，防止解码器做色域转换时丢失色彩
            flag = flag or CREATE_RETRIEVER_FLAG_PREFERENTIAL_DISABLE_COLOR_PRIMARIES
        }
        // 设备不支持hdr显示的话，也指定从retrieve中抽取非hdr帧
        if (isHdrSupported && HdrTransformFactory.isSupportHdrTransform() && isHdrVideoOlive) {
            // 支持下变换和Bitmap.Config.RGBA_1010102时(Android U)解码10bit
            flag = flag or CREATE_RETRIEVER_FLAG_PREFERENTIAL_EXPORT_HLG_RGB10A2_BITMAP
        }
        val nvsFrameRetriever: NvsVideoFrameRetriever? = nvsContext?.createVideoFrameRetriever(meicamPrivatePath, flag)
        nvsFrameRetriever?.setFrameTimeTolerance(FRAME_TIME_TOLERANCE)
        // calc frame height
        val frameHeight = nvsFrameRetriever?.nvVideoStreamInfo?.let { nvVideoStreamInfo ->
            val isRotated = (nvVideoStreamInfo.displayRotation == VIDEO_ROTATION_90)
                || (nvVideoStreamInfo.displayRotation == VIDEO_ROTATION_270)
            if (isRotated) nvVideoStreamInfo.imageWidth else nvVideoStreamInfo.imageHeight
        } ?: kotlin.run {
            GLog.e(TAG, LogFlag.DL, "retrieveFrameFromSource nvsFrameRetriever.nvVideoStreamInfo is null")
            return null
        }
        val bitmap = nvsFrameRetriever.getFrameAtTimeWithCustomVideoFrameHeight(timeUs, frameHeight)
        // 释放资源
        nvsFrameRetriever.release()
        GLog.d(TAG, LogFlag.DL) {
            "<retrieveFrameFromSource> timeUs=$timeUs bmpConfig=${bitmap?.config} bmpCS=${bitmap?.colorSpace} costTime=${GLog.getTime(time)} ms"
        }
        return bitmap
    }

    override fun retrieveInfo(): OliveVideoInfo {
        return oliveVideoInfo
    }

    @WorkerThread
    override fun exportVideo(filePath: String?, configuration: Hashtable<String, Any>?, oliveConfigs: Hashtable<String, Any>?) {
        if (TextUtils.isEmpty(filePath)) {
            GLog.e(TAG, LogFlag.DF, "saveVideo filePath is null")
            return
        }
        // 如果文件已经存在，则删除文件。
        val file = File(filePath)
        if (file.exists()) {
            file.delete()
        }
        videoEdgeEditor?.resetScaleToDefault() //在导出视频时需要将视频缩放恢复为默认
        nvsContext?.customCompileVideoHeight = getVideoSize().height
        nvsContext?.compileConfigurations = buildCompileConfigurations().also { baseConfigs ->
            // 当外部传入有自定义configuration时，merge到baseConfigs
            configuration?.let(baseConfigs::putAll)
        }
        //SOTA:OLive关声音 是否只存视频 默认false
        val onlySaveVideo = (oliveConfigs?.get(SAVE_FLAG_ONLY_VIDEO) as? Boolean) ?: false
        val success = nvsContext?.compileTimeline(
            nvsTimeline,
            0,
            nvsTimeline?.duration ?: 0,
            filePath,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
            buildCompileFlag(false, false, onlySaveVideo)
        ) ?: false
        nvsContext?.compileConfigurations = null
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(TAG, LogFlag.DF, "saveVideo filePath = $filePath, success = $success ")
        }
    }

    /**
     * 获取meicam colorspace name
     * @param colorSpace: 指定色彩空间
     */
    private fun getMeicamColorSpaceName(colorSpace: ColorSpace?): String? {
        if (!ApiLevelUtil.isAtLeastAndroidU()) {
            return null
        }
        return when (colorSpace) {
            SRGB -> Named.SRGB.toString()
            DISPLAY_P3 -> Named.DISPLAY_P3.toString()
            BT2020_HLG -> Named.BT2020_HLG.toString()
            BT2020_PQ -> Named.BT2020_PQ.toString()
            else -> null
        }
    }

    /**
     * 配置视频编码时使用的compile configuration参数，可选项：使用的视频编码器、颜色转换函数、media codec编码提速、
     *
     * @return 包含各种compile configuration参数的Hashmap
     */
    private fun buildCompileConfigurations(): Hashtable<String, Any> {
        val compileConfigurations = Hashtable<String, Any>()
        if (isWideColorGamut && oliveVideoInfo.videoColorSpace == DISPLAY_P3) {
            compileConfigurations[COMPILE_VIDEO_COLOR_PRIMARIES] = COMPILE_VIDEO_COLOR_PRIMARIES_VALUE
        }
        if (!isSupportPhotoHdrVideoBrighten) {
            GLog.d(TAG, LogFlag.DL, "buildCompileConfigurations, not support hdr brighten")
            return compileConfigurations
        }
        val colorTransferValue = when (oliveVideoInfo.hdrType) {
            HDR_TYPE_HLG -> COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE
            HDR_TYPE_HDR10 -> COMPILE_VIDEO_HDR10_COLOR_TRANSFER_VALUE
            HDR_TYPE_HDR10PLUS -> COMPILE_VIDEO_HDR10PLUS_COLOR_TRANSFER_VALUE
            HDR_TYPE_DOLBYVISION -> {
                if (!isSupportDolbyEncodeAccelerate()) {
                    // 由 MediaCodec 选择处理速率，编码时间可能最多增加 1/3
                    compileConfigurations[COMPILE_USE_OPERATING_RATE] = false
                }
                COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE
            }
            else -> null
        }

        colorTransferValue?.let {
            compileConfigurations[COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
            compileConfigurations[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = it
        }

        return compileConfigurations
    }

    /**
     * 判断设备是否支持Dolby视频高速率编码；若设备MediaCodec性能不足以支持高速率的Dolby视频编码，让设备的MediaCodec自行选择处理速率
     *
     * @return 返回是否支持
     */
    private fun isSupportDolbyEncodeAccelerate(): Boolean {
        return context.getAppAbility<IConfigAbility>()?.use {
            it.getBooleanConfig(IS_SUPPORT_DOLBY_ENCODE_ACCELERATE, true)
        } ?: false
    }

    override fun getFilterEditor(): IOliveVideoFilterEditor {
        if (filterEditor == null) {
            filterEditor = OliveVideoFilterEditorImp(nvsTimeline, nvsVideoTrack, nvsVideoClip)
        }
        return filterEditor as IOliveVideoFilterEditor
    }

    override fun getAdjustEditor(): IOliveVideoAdjustEditor {
        if (adjustEditor == null) {
            adjustEditor = OliveVideoAdjustEditorImp(nvsTimeline, nvsVideoTrack, nvsVideoClip)
        }
        return adjustEditor as IOliveVideoAdjustEditor
    }

    override fun getVideoEdgeEditor(): IOliveVideoEdgeEditor? {
        if (videoEdgeEditor == null) {
            nvsContext?.apply {
                videoEdgeEditor = OliveVideoEdgeEditorImp(this, nvsTimeline, nvsVideoTrack, nvsVideoClip)
            } ?: GLog.w(TAG, LogFlag.DL, "getEdgeEditor, nvsContext is null")
        }
        return videoEdgeEditor
    }

    override fun getTransformEditor(): IOliveVideoTransformEditor? {
        if (transformEditor == null) {
            nvsContext?.apply {
                transformEditor = OliveVideoTransformEditorImp(this, nvsTimeline, nvsVideoTrack, nvsVideoClip)
            } ?: GLog.w(TAG, LogFlag.DL, "getTransformEditor, nvsContext is null")
        }
        return transformEditor as? IOliveVideoTransformEditor
    }

    override fun getWatermarkEditor(): IOliveVideoWatermarkEditor? {
        if (watermarkEditor == null) {
            nvsContext?.apply {
                watermarkEditor = OliveVideoWatermarkEditorImp(this, nvsTimeline, nvsVideoTrack, nvsVideoClip, videoSize)
            }
        }
        return watermarkEditor
    }

    override fun getWithoutWatermarkVideoSize(): Size {
        checkInit()
        return watermarkEditor?.getWithoutWatermarkVideoSize() ?: Size(0, 0)
    }

    /**
     * 获取音轨个数
     * @param uri 图片/视频 uri路径
     * @param offset 视频所在文件的offset
     * @param length 视频长度
     * @return 返回音轨个数
     */
    override fun getAudioStreamingCount(uri: Uri, offset: Long, length: Long): Int {
        val start = System.currentTimeMillis()
        val meicamPrivatePath = formatNvmFilePath(uri, offset, length)
        val videoInfo: NvsAVFileInfo? = fileInfoMap.getOrPut(meicamPrivatePath) {
            nvsContext?.getAVFileInfo(meicamPrivatePath, NvsStreamingContext.AV_FILEINFO_EXTRA_INFO)
        }
        val audioCount = videoInfo?.audioStreamCount ?: 0
        val costTime = System.currentTimeMillis() - start
        GLog.d(TAG, LogFlag.DF) { "<getAudioStreamingCount> filepath: $uri audio channel count: $audioCount costTime:${costTime}" }
        return audioCount
    }

    override fun removeAllVideoFx() {
        getFilterEditor().removeAllFx()
        getAdjustEditor().removeAllFx()
        getTransformEditor()?.removeAllFx()
        getVideoEdgeEditor()?.removeAllFx()
    }

    override fun isHasVideoFx(): Boolean {
        return getAdjustEditor().isHasAdjust()
            || getFilterEditor().isHasFilter()
            || (getTransformEditor()?.isHasTransform() == true)
    }

    private fun buildCompileFlag(noUseInputSurface: Boolean, needDisableHardwareEncoder: Boolean, onlySaveVideo: Boolean): Int {
        //生成文件输出的特殊标志，如果没有特殊需求，请填写0
        var compileFlag = 0
        if (noUseInputSurface) {
            compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DONT_USE_INPUT_SURFACE
        }
        if (needDisableHardwareEncoder) {
            compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_HARDWARE_ENCODER
        }
        // 禁用字节对齐，否则去水印时坐标就不准确了
        compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
        // 只保留视频
        if (onlySaveVideo) {
            compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_ONLY_VIDEO
        }
        return compileFlag
    }

    /**
     * 对ImageGrabberCallback的封装/扩展
     * 背景：
     * sdk多处api使用ImageGrabberCallback 进行返回，这里统一归集到一个类处理。
     * 保证 Engine与 callback两实例是一对一，避免ImageGrabberCallback混乱。
     */
    private class ImageGrabberEventCallback : NvsStreamingContext.ImageGrabberCallbackExt {
        /**
         * 单抽帧接口返回Listener
         * 即：grabImageFromTimelineAsync 接口异步返回Listener
         */
        var imageGrabberListener: ((positionUs: Long, grabberBitmap: Bitmap?) -> Unit)? = null

        /**
         * seek后，sdk处理完成后同时返回当前seek帧的截帧图
         * 即：seek后有两个回调，一个是seek动作的完成，一个是帧图处理完成--当前的capturedSeekFrameListener，详见[onImageGrabbedArrived]
         */
        var capturedSeekFrameListener: ((positionUs: Long, frame: Bitmap?) -> Unit)? = null

        override fun onImageGrabbedArrived(timeLine: NvsTimeline?, bitmap: Bitmap?, params: Hashtable<String, Any>?) {
            // 是否seek后的帧图返回
            val isSeekCapture = params?.get(IMAGE_GRABBER_META_DATA_KEY_IMAGE_FROM_SEEK) as? Boolean ?: false
            val timestamp = params?.get(IMAGE_GRABBER_META_DATA_KEY_TIMESTAMP) as? Long ?: 0L
            if (isSeekCapture) {
                capturedSeekFrameListener?.invoke(timestamp, bitmap)
            } else {
                imageGrabberListener?.invoke(timestamp, bitmap)
            }
        }

        override fun onImageGrabbedArrived(bitmap: Bitmap?, positionUs: Long) = Unit
    }

    companion object {
        private const val TAG = "OliveVideoEngine"

        private const val PREVIEW_SIZE_MODE = NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE

        private const val STREAMING_CONTEXT_FLAG = STREAMING_CONTEXT_FLAG_COMPACT_MEMORY_MODE or
            STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT or
            STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED
        private const val FORMAT_PO10 = 0x36

        private const val FRAME_TIME_TOLERANCE = 0L

        private const val SAMPLE_RETE: Int = 44100

        private const val CHANNEL_COUNT: Int = 2

        private const val STREAMING_ENGINE_SEEK_FLAG_ZERO_TOLERANCE = 8

        private const val COMPILE_VIDEO_ENCODER_VALUE = "hevc"
        private const val COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE = "hlg dolby vision"
        private const val COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE = "hlg"
        private const val COMPILE_VIDEO_HDR10_COLOR_TRANSFER_VALUE = "st2084"
        private const val COMPILE_VIDEO_HDR10PLUS_COLOR_TRANSFER_VALUE = "hdr10plus"
        private const val COMPILE_VIDEO_COLOR_PRIMARIES_VALUE = "display p3"

        //marked by yeguangjin 美摄3.10.3基线升级导致，.4线并入.3后可删除
        private const val COMPILE_VIDEO_COLOR_PRIMARIES = "encorder color primaries"

        private const val GRAB_DELAY_TIME_MS = 20L

        /**
         * 只保存视频
         */
        const val SAVE_FLAG_ONLY_VIDEO = "saveOnlyVideo"
    }
}