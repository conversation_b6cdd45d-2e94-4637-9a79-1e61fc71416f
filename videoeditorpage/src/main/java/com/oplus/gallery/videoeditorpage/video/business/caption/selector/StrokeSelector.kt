/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : StrokeSelector.kt
 ** Description : 文字效果描边面板组件
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.ProgressSeekbar

/**
 * 文字效果描边面板组件
 */
internal class StrokeSelector(
    pageType: PageType
) : TextEffectsSelector(pageType) {

    override fun getTextEffectsTypeConfig(): List<TextEffectsType> {
        return listOf(
            TextEffectsType.TRANSPARENT,
            TextEffectsType.THICKNESS
        )
    }

    override fun initData() = Unit

    /**
     * 滑动取值后进行更新字幕设置
     */
    override fun onChanged(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) {
        when (seekBar.progressId) {
            TextEffectsType.TRANSPARENT.progressId -> updateTransparent(progress, isIncreasing)
            TextEffectsType.THICKNESS.progressId -> updateThickness(progress, isIncreasing)
        }
    }

    private fun updateTransparent(progress: Float, isIncreasing: Boolean) {
        // 更新透明度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.TRANSPARENT,
            progress,
            isIncreasing
        )
    }

    private fun updateThickness(progress: Float, isIncreasing: Boolean) {
        // 更新粗细度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.THICKNESS,
            progress,
            isIncreasing
        )
    }

    companion object {
        private const val TAG = "StrokeSelector"
    }
}
