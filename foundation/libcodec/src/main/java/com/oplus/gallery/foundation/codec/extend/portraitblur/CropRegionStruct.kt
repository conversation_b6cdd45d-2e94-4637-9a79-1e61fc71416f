/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CropRegionStruct
 ** Description:
 ** Version: 1.0
 ** Date : 2025/5/20
 ** Author: sunwenli
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  sunwenli                      2025/5/20    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.extend.portraitblur

import android.graphics.RectF
import com.oplus.gallery.foundation.codec.extend.ExtendStruct

/**
 * 裁剪区域参数，映射native层的struct数据结构，不可修改
 */
class CropRegionStruct(byteArray: ByteArray) : ExtendStruct<CropRegionData>(byteArray) {
    val version: Float32
    val left: Signed32
    val top: Signed32
    val right: Signed32
    val bottom: Signed32

    init {
        version = Float32()
        left = Signed32()
        top = Signed32()
        right = Signed32()
        bottom = Signed32()
    }

    override fun toData(): CropRegionData {
        return CropRegionData().also {
            it.version = version.get()
            it.clipRectF = RectF(
                left.get().toFloat(),
                top.get().toFloat(),
                right.get().toFloat(),
                bottom.get().toFloat()
            )
        }
    }
}

/**
 * 裁剪区域信息
 * 1，相机会对成片进行裁剪，景深图,灰度图都是裁剪过的
 * 2，相册调节保存时，会拿原图,原始灰度图去做景深处理，之后也要按这个裁剪区域信息进行裁切
 */
data class CropRegionData(
    /**
     * 版本
     */
    var version: Float = 0f,

    /**
     * 裁剪区域
     */
    var clipRectF: RectF? = null
)
