/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SuitableSizeG2TextView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;

import com.oplus.gallery.videoeditorpage.utlis.ColorSupportUtil;
import com.coui.appcompat.textview.COUITextView;

public class SuitableSizeG2TextView extends COUITextView {


    public SuitableSizeG2TextView(Context context) {
        this(context, null);
    }

    public SuitableSizeG2TextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SuitableSizeG2TextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //init text size with supported text level
        setTextViewSize(getTextSize());
    }


    @Override
    @SuppressWarnings("divzero")
    public void setTextSize(float size) {
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        final float scaleDensity = metrics.scaledDensity;
        float finalDensity = 1f;
        float curDensity = metrics.density;
        if (Float.compare(curDensity, 0f) > 0) {
            finalDensity = curDensity;
        }
        float fontScale = scaleDensity / finalDensity;
        float suitableTextSize = (int) ColorSupportUtil.getSuitableFontSize(size, fontScale, ColorSupportUtil.G2);
        super.setTextSize(TypedValue.COMPLEX_UNIT_PX, suitableTextSize);
    }

    private void setTextViewSize(float size) {
        setTextSize(size);
    }
}
