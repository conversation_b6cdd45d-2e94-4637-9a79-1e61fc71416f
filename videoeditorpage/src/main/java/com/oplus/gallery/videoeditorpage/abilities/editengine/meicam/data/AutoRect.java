/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - AutoRect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import android.graphics.RectF;

public class AutoRect {
    private static final String TAG = "AutoRect";
    private static final float RESERVED_SPACE = 14f / 15;
    private static final float HALF = 2;
    private static final float MIN_ACCURACY = 0.001f;
    private float mImgWidth;
    private float mImgHeight;
    private float mCurMakeRatio;
    private float mPointX;
    private float mPointY;
    private float mLeft;
    private float mTop;
    private float mRight;
    private float mBottom;
    private float mWidth;
    private float mHeight;
    private RectF mLastStepAutoRect;


    public AutoRect() {
        this(0, 0);
    }

    public AutoRect(float mImgWidth, float mImgHeight) {
        this(0, 0, 0, 0, mImgWidth, mImgHeight, 0);
    }

    public AutoRect(float mImgWidth, float mImgHeight, float mCurMakeRatio) {
        this(0, 0, 0, 0, mImgWidth, mImgHeight, mCurMakeRatio);
    }

    public AutoRect(float left, float top, float right, float bottom, float imgWidth, float imgHeight, float curMakeRatio) {
        this.mImgWidth = imgWidth;
        this.mImgHeight = imgHeight;
        this.mWidth = right - left;
        this.mHeight = bottom - top;
        this.mPointX = left + mWidth / HALF;
        this.mPointY = top + mHeight / HALF;
        this.mCurMakeRatio = curMakeRatio;
        this.set(left, top, right, bottom);
    }

    public AutoRect(RectF rectF, float imgWidth, float imgHeight, float curMakeRatio) {
        this.mImgWidth = imgWidth;
        this.mImgHeight = imgHeight;
        this.mCurMakeRatio = curMakeRatio;
        if ((rectF != null) && ((rectF.right - rectF.left) != 0) && ((rectF.bottom - rectF.top) != 0)) {
            float left = (rectF.left + 1) / HALF * mImgWidth;
            float top = (1 - rectF.top) / HALF * mImgHeight;
            float right = (rectF.right + 1) / HALF * mImgWidth;
            float bottom = (1 - rectF.bottom) / HALF * mImgHeight;
            float radio = (right - left) / (bottom - top);
            notifyAutoRect(radio, left, top, right, bottom);
        } else {
            float halfWidth = mImgWidth / HALF;
            float halfHeigth = halfWidth / mCurMakeRatio;
            if (halfHeigth > imgHeight) {
                halfHeigth = halfWidth / HALF;
                halfWidth = halfHeigth * mCurMakeRatio;
            }
            notifyAutoRect(mCurMakeRatio, 0, 0, halfWidth, halfHeigth);
        }
    }

    public void setImgWidth(float imgWidth) {
        this.mImgWidth = imgWidth;
    }

    public void setImgHeight(float imgHeight) {
        this.mImgHeight = imgHeight;
    }

    public final boolean notifyAutoRect(float radio, float left, float top, float right, float bottom) {
        float finalLeft = left;
        float finalTop = top;
        float finalRight = right;
        float finalBottom = bottom;
        if (radio != mCurMakeRatio) {
            if (mImgWidth > mImgHeight) {
                finalTop = 0;
                finalBottom = mImgHeight;
                finalLeft = 0;
                finalRight = mImgHeight * mCurMakeRatio;
            } else {
                finalLeft = 0;
                finalBottom = mImgWidth / mCurMakeRatio;
                finalRight = mImgWidth;
                finalTop = 0;
            }
        }
        this.mHeight = finalBottom - finalTop;
        this.mWidth = finalRight - finalLeft;
        this.set(finalLeft, finalTop, finalRight, finalBottom);
        return moveTo(mImgWidth / 2, mImgHeight / 2);
    }

    public boolean adjustmentRect() {
        float left = 0;
        float right = 0;
        float bottom = 0;
        float top = 0;
        if (mCurMakeRatio > 1) {
            mWidth = mHeight * mCurMakeRatio;
            right = mLeft + mWidth;
            bottom = mTop + mHeight;
            left = mLeft;
            top = mTop;
        } else {
            mHeight = mWidth / mCurMakeRatio;
            left = mLeft;
            top = mTop;
            right = mLeft + mWidth;
            bottom = mTop + mHeight;
        }

        if (isNotCrossingImage(left, top, right, bottom)) {
            set(mLeft, mTop, mRight, mBottom);
            return true;
        }
        return false;
    }

    private boolean isNotCrossingImage(float left, float top, float right, float bottom) {
        if ((left < 0) || (right > mImgWidth) || (bottom > mImgHeight) || (top < 0)) {
            return false;
        }
        return true;
    }

    public boolean setCurMakeRatio(float curMakeRatio) {
        this.mCurMakeRatio = curMakeRatio;
        if (((mBottom - mTop) == 0) && ((mRight - mLeft) == 0)) {
            return false;
        }
        float radio = mImgWidth / mImgHeight;
        return notifyAutoRect(radio, mLeft, mTop, mRight, mBottom);
    }

    public final void set(float left, float top, float right, float bottom) {
        this.mLeft = left;
        this.mTop = top;
        this.mRight = right;
        this.mBottom = bottom;
        this.mWidth = right - left;
        this.mHeight = bottom - mTop;
        this.mPointX = left + mWidth / HALF;
        this.mPointY = top + mHeight / HALF;
        saveCurrentRectF();
    }

    public void set(AutoRect rectF) {
        this.mLeft = rectF.mLeft;
        this.mTop = rectF.mTop;
        this.mRight = rectF.mRight;
        this.mBottom = rectF.mBottom;
        this.mWidth = mRight - mLeft;
        this.mHeight = mBottom - mTop;
        this.mPointX = mLeft + mWidth / HALF;
        this.mPointY = mTop + mHeight / HALF;
        saveCurrentRectF();
    }

    public RectF getVidioROI() {
        float left = this.mLeft / mImgWidth * HALF - 1;
        float top = 1 - this.mTop / mImgHeight * HALF;
        float right = this.mRight / mImgWidth * HALF - 1;
        float bottom = 1 - this.mBottom / mImgHeight * HALF;
        return new RectF(left, top, right, bottom);
    }

    public boolean smallRectF(float scalRadio) {
        float spaceDistanceX = mWidth * (1 - scalRadio);
        float spaceDistanceY = spaceDistanceX / mCurMakeRatio;
        float left = mLeft + spaceDistanceX / HALF;
        float right = mRight - spaceDistanceX / HALF;
        float top = mTop + spaceDistanceY / HALF;
        float bottom = mBottom - spaceDistanceY / HALF;
        if ((bottom - top) <= 0) {
            return false;
        }
        float radio = (right - left) / (bottom - top);

        if ((Math.abs((radio - mCurMakeRatio)) > MIN_ACCURACY) || (mRight < mLeft) || (mBottom < mTop)) {
            return false;
        }
        set(left, top, right, bottom);
        return true;
    }

    public boolean smallRectF(float halfX, float halfY) {
        if ((mHeight < halfY) || (mWidth < halfX)) {
            return false;
        }
        float tempWidth = 0;
        float tempHeight = 0;
        if (Float.compare(mWidth, mImgWidth) == 0) {
            tempWidth = mImgWidth - halfX;
            tempHeight = tempWidth / mCurMakeRatio;
            updateOnCenter(tempWidth, tempHeight);
        } else {
            tempHeight = mImgHeight - halfY;
            tempWidth = tempHeight * mCurMakeRatio;
            updateOnCenter(tempWidth, tempHeight);
        }
        set(this.mLeft, this.mTop, mRight, mBottom);
        return true;
    }

    public boolean updateOnCenter(float width, float height) {
        if (Float.compare((width / height), mCurMakeRatio) == 0) {
            this.mBottom = mTop + height;
            mRight = mLeft + height * mCurMakeRatio;
            moveView((mWidth - width) / 2, (mHeight - height) / 2);
        } else if (width > height) {
            this.mBottom = mTop + height;
            mRight = mLeft + height * mCurMakeRatio;
            moveView((mWidth - width) / 2, (mHeight - height) / 2);
        } else {
            this.mRight = mLeft + width;
            mBottom = mTop + width / mCurMakeRatio;
            moveView((mWidth - width) / 2, (mHeight - height) / 2);
        }
        return false;
    }


    public boolean bigRectF(float halfX, float halfY) {
        if (((mBottom + halfY) > mImgHeight) || ((mRight + halfX) > mImgWidth)) {
            return false;
        }
        this.mBottom = mBottom + halfY;
        this.mRight = mRight + halfX;
        if (!moveView(-halfX / HALF, -halfY / HALF)) {
            return false;
        }
        set(this.mLeft, this.mTop, mRight, mBottom);
        saveCurrentRectF();
        return true;
    }

    public void moveParentTop() {
        moveView(0, -mTop);
    }

    public void moveParentBottom() {
        moveView(0, mImgHeight - getBottom());
    }

    public void moveParentLeft() {
        moveView(-getLeft(), 0);
    }

    public void moveParentRight() {
        moveView(mImgWidth - getRight(), 0);
    }

    public void moveParentTopAndLeft() {
        moveView(-getLeft(), -getTop());
    }

    public void moveParentBottomAndRight() {
        moveView(mImgWidth - getRight(), mImgHeight - getBottom());
    }

    public void moveParentTopAndRight() {
        moveView(mImgWidth - mRight, -mTop);
    }

    public void moveParentBottomAndLeft() {
        moveView(-mLeft, mImgHeight - mBottom);
    }

    public boolean moveToCenterInRectF(AutoRect autoRect) {
        if (autoRect == null) {
            return false;
        }
        return moveTo(autoRect.getPointX(), autoRect.getPointY());
    }

    public boolean moveToCenterInParent() {
        return moveTo(mImgWidth / HALF, mImgHeight / HALF);
    }

    public boolean tryMoveToCenterInParent() {
        float offestX = mImgWidth / HALF - mPointX;
        float offsetY = mImgHeight / HALF - mPointY;
        return tryMoveView(offestX, offsetY);
    }

    public boolean moveTo(float x, float y) {
        float offestX = x - mPointX;
        float offsetY = y - mPointY;
        return moveView(offestX, offsetY);
    }

    public boolean tryMoveView(float dx, float dy) {
        if (((mLeft + dx) < 0) || ((mRight + dx) > mImgWidth) || ((mBottom + dy) > mImgHeight) || ((mTop + dy) < 0)) {
            return false;
        }
        return true;
    }

    public boolean moveView(float dx, float dy) {
        if (!tryMoveView(dx, dy)) {
            return false;
        }
        mLeft += dx;
        if (mLeft < 0) {
            mLeft = 0;
            mRight = (mBottom - mTop) * mCurMakeRatio;
        }
        mRight += dx;
        if (mRight > mImgWidth) {
            mRight = mImgWidth;
            mLeft = mImgWidth - (float) ((mBottom - mTop) * mCurMakeRatio);
        }
        mTop += dy;
        if (mTop < 0) {
            mTop = 0;
            mBottom = (float) ((mRight - mLeft) / mCurMakeRatio);
        }
        mBottom += dy;
        if (mBottom > mImgHeight) {
            mBottom = mImgHeight;
            mTop = mBottom - (float) ((mRight - mLeft / mCurMakeRatio));
        }
        this.mWidth = mRight - mLeft;
        this.mHeight = mBottom - mTop;
        this.mPointX = mLeft + mWidth / HALF;
        this.mPointY = mTop + mHeight / HALF;
        return true;
    }

    public float getLeft() {
        return mLeft;
    }

    public float getTop() {
        return mTop;
    }

    public float getRight() {
        return mRight;
    }

    public float getBottom() {
        return mBottom;
    }

    public float getPointX() {
        return mPointX;
    }

    public float getPointY() {
        return mPointY;
    }

    public void setPointX(float mPointX) {
        this.mPointX = mPointX;
    }

    public void setPointY(float mPointY) {
        this.mPointY = mPointY;
    }

    public float getWidth() {
        return mWidth;
    }

    public float getHeight() {
        return mHeight;
    }

    public boolean backLastAutoRect() {
        if (mLastStepAutoRect == null) {
            mLastStepAutoRect = new RectF();
        }
        if ((mLastStepAutoRect.right - mLastStepAutoRect.left != 0) && (mLastStepAutoRect.bottom - mLastStepAutoRect.top != 0)) {
            this.mLeft = mLastStepAutoRect.left;
            this.mRight = mLastStepAutoRect.right;
            this.mTop = mLastStepAutoRect.top;
            this.mBottom = mLastStepAutoRect.bottom;
            return true;
        }
        return false;
    }

    private void saveCurrentRectF() {
        if (mLastStepAutoRect == null) {
            mLastStepAutoRect = new RectF();
        }
        if ((Float.compare(mLastStepAutoRect.left, mLeft) != 0)
                || (Float.compare(mLastStepAutoRect.right, mRight) != 0)
                || (Float.compare(mLastStepAutoRect.bottom, mBottom) != 0)
                || (Float.compare(mLastStepAutoRect.top, mTop) != 0)) {
            mLastStepAutoRect.left = mLeft;
            mLastStepAutoRect.right = mRight;
            mLastStepAutoRect.bottom = mBottom;
            mLastStepAutoRect.top = mTop;
        }
    }

    @Override
    public String toString() {
        return "AutoRect{"
                + " mLeft=" + mLeft
                + ", mTop=" + mTop
                + ", mRight=" + mRight
                + ", mBottom=" + mBottom
                + ", mImgWidth=" + mImgWidth
                + ", mImgHeight=" + mImgHeight
                + ", mCurMakeRatio=" + mCurMakeRatio
                + ", mPointX=" + mPointX
                + ", mPointY=" + mPointY
                + ", mWidth=" + mWidth
                + ", mHeight=" + mHeight
                + ", mLastStepAutoRect=" + mLastStepAutoRect
                + '}';
    }

    public AutoRect cloneRect() {
        AutoRect newRect = new AutoRect(mLeft, mTop, mRight, mBottom, mImgWidth, mImgHeight, mCurMakeRatio);
        return newRect;
    }
}