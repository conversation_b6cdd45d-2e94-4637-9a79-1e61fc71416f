/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : DownloadableSelector.kt
 ** Description : 字幕需要网络下载的面板组件基类
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.GridLastRowBottomMarginDecoration
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.GridSpacingItemDecoration

/**
 * 字幕需要网络下载的面板组件基类
 */
internal abstract class DownloadableSelector(
    private val isInMultiWindow: Boolean,
    private val bottomNaviBarHeight: Int,
    pageType: PageType
) : BaseSelector(pageType) {

    /**
     * 记录当前选择的资源的id
     */
    var resourceId = TextUtil.EMPTY_STRING

    /**
     * 显示样式的recyclerView
     */
    protected var resourceRecyclerView: RecyclerView? = null


    /**
     * 记录当前选中的item的位置
     */
    protected var currentClickPosition = -1


    override fun initViews() {
        container.let { container ->
            resourceRecyclerView = container?.findViewById<RecyclerView>(R.id.caption_style_font_recycler_view)?.apply {
                val itemColumns = if (isInMultiWindow.not() && isMiddleAndLargeWindow(context)) {
                    ITEM_COLUMNS_MIDDLE_LARGE
                } else {
                    ITEM_COLUMNS_NORMAL
                }
                layoutManager = GridLayoutManager(context, itemColumns)
                setItemAnimator(null)
                val itemHorizontalSpacing = resources.getDimensionPixelSize(R.dimen.videoeditor_item_caption_style_horizontal_spacing)
                val itemVerticalSpacing = resources.getDimensionPixelOffset(R.dimen.videoeditor_item_caption_style_vertical_spacing)
                addItemDecoration(GridSpacingItemDecoration(itemColumns, itemHorizontalSpacing, itemVerticalSpacing, false))
                /**
                 * 适配底部导航栏
                 */
                addItemDecoration(GridLastRowBottomMarginDecoration(bottomNaviBarHeight))
            }
        }
        initAdapter()
    }

    /**
     * 配置适配器
     */
    abstract fun initAdapter()

    companion object {
        private const val TAG = "DownloadableSelector"

        // 中大屏样式列数
        private const val ITEM_COLUMNS_MIDDLE_LARGE = 10

        // 普通屏样式列数
        private const val ITEM_COLUMNS_NORMAL = 5
    }
}
