/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoTemplate.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import android.text.TextUtils
import com.meicam.sdk.NvsTimeline
import com.oplus.gallery.framework.abilities.videoedit.data.TemplateInfo
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoTemplate

class MeicamVideoTemplate : IGalleryVideoTemplate {

    private lateinit var timeline: NvsTimeline
    private var currentTemplateInfo: TemplateInfo? = null

    fun setTimeline(timeline: NvsTimeline) {
        this.timeline = timeline
    }

    override fun setCurrentTemplateInfo(templateInfo: TemplateInfo?) {
        currentTemplateInfo = templateInfo
    }

    override fun getCurrentTemplateInfo(): TemplateInfo? {
        return currentTemplateInfo
    }

    override fun isTemplateChanged(): Boolean {
        return (currentTemplateInfo != null) && !TextUtils.isEmpty(currentTemplateInfo?.filePath)
    }
}