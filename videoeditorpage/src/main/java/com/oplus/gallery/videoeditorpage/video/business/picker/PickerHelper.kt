/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickerHelper.kt
 ** Description: 选图后处理
 ** Version: 1.0
 ** Date : 2025/5/8
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:ActionbarSection
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/5/8    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.picker

import android.util.Size
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_GALLEY_WALLPAPER_PAGE
import com.oplus.gallery.business_lib.videoedit.VideoSpecHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.DEFAULT_VIDEO_RESOLUTION_FPS
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.DEFAULT_VIDEO_RESOLUTION_HEIGHT
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.DEFAULT_VIDEO_RESOLUTION_WIDTH
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.CommonConstants
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.RESOLUTION_RATIO_4K
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.picker.data.PickerItemInfo
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper
import kotlin.math.roundToInt

/**
 * 选图后处理
 */
object PickerHelper {

    private const val TAG = "PickerHelper"

    /**
     * 获取选图数据（只包含可以编辑的媒体）
     */
    val pickerList: ArrayList<PickerItemInfo> by lazy { ArrayList() }

    /**
     * 初始化时不支持导入的媒体数量
     */
    var unsupportedMediaCount: Int = 0
        private set

    fun buildTimelineByItemList(itemList: ArrayList<PickerItemInfo>, editorInvoker: String?): ITimeline? {
        if (itemList.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "buildTimelineByItemList itemList size == 0")
            return null
        }

        val supportInfoList: List<PickerItemInfo> = VideoParser.getInstance().parseByPickerItemInfoList(itemList)
        pickerList.clear()
        pickerList.addAll(supportInfoList)
        unsupportedMediaCount = itemList.size - supportInfoList.size

        val (fps, resolution) = getVideoResolution(supportInfoList)
        GLog.d(TAG, LogFlag.DL, "buildTimelineByItemList fps: $fps, resolution: $resolution")

        val timeline: ITimeline = EditorEngineGlobalContext.getInstance().createTimeline(
            resolution.width, resolution.height, fps.roundToInt()
        ) ?: run {
            GLog.e(TAG, LogFlag.DL, "buildTimelineByItemList failed to initTimeline for timeline is null")
            return null
        }
        timeline.nodeResolution = ResolutionUtil.getResolutionRatio(resolution)
        timeline.nodeFps = fps
        val videoTrack = timeline.appendVideoTrack() ?: run {
            GLog.e(TAG, LogFlag.DL, "buildTimelineByItemList failed to initTimeline for videoTrack is null")
            return null
        }
        for (info in supportInfoList) {
            val videoClip = videoTrack.appendVideoClip(
                info.path,
                info.srcFilePath,
                info.mediaType,
                info.needConvert,
                0,
                if (info.duration != 0L) info.duration else CommonConstants.DEFAULT_IMAGE_DURATION.toLong(),
                info.width,
                info.height,
                info.fileSystemPath
            ).also {
                if (info.isDateTakenValid()) it.fileDate = info.dateTakenInMs
                if (info.isLocationValid()) it.fileLocation = info.location
                it.isOlivePhoto = info.isOlivePhoto
            }
            if ((info.mediaType == StreamingConstant.ClipType.CLIP_TYPE_VIDEO) && (videoClip != null)) {
                val isMainTrack = timeline.isMainTrack(videoTrack)
                videoClip.setPanAndScan(0f, 0f, isMainTrack)
            }
            // 预先请求轨道第一段视频的首帧，避免进入编辑后轨道出现黑图现象
            if (!shouldSkipPreLoadFirstThumbnail(editorInvoker) && (videoTrack.getClipIndex(videoClip) == AppConstants.Number.NUMBER_0)) {
                TrackHelper.getFirstThumbnail(info.path)
            }
        }
        return timeline
    }

    /**
     * 通过来源判定是否跳过预先请求首帧
     * 如果来自菜单-导出实况/菜单-设为壁纸则跳过
     * @param editorInvoker 来源
     * @return
     */
    private fun shouldSkipPreLoadFirstThumbnail(editorInvoker: String?): Boolean {
        if (editorInvoker == null) return false
        return (editorInvoker == VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE) ||
                (editorInvoker == VALUE_INVOKER_GALLEY_WALLPAPER_PAGE)
    }

    /**
     * 获取分辨率
     */
    private fun getVideoResolution(pickerItemList: List<PickerItemInfo>): Pair<Float, Size> {
        if ((pickerItemList == null) || (pickerItemList.isEmpty())) {
            return Pair(
                DEFAULT_VIDEO_RESOLUTION_FPS.toFloat(),
                Size(DEFAULT_VIDEO_RESOLUTION_WIDTH, DEFAULT_VIDEO_RESOLUTION_HEIGHT)
            )
        }

        val fpsList: MutableList<Float> = mutableListOf()
        val resolutionList: MutableList<Size> = mutableListOf()
        pickerItemList.forEach { item ->
            // 帧率和分辨率，优先从已解析map中获取，获取不到才去getAVFileInfo信息
            val videoSpecInfo = VideoParser.getInstance().videoInfoMap[item.path]
            val (fps, resolution) = if (videoSpecInfo != null) {
                Pair(videoSpecInfo.fps, Size(videoSpecInfo.width, videoSpecInfo.height))
            } else {
                val fileInfo = EditorEngineGlobalContext.getInstance().getAVFileInfo(item.path)
                GLog.d(TAG, LogFlag.DL) { "getVideoResolution, fileInfo: $fileInfo" }
                Pair(
                    fileInfo.fps,
                    VideoUtils.coerceAtMost(Size(fileInfo.videoWidth, fileInfo.videoHeidht), RESOLUTION_RATIO_4K).let { size ->
                        VideoSpecHelper.getRotatedMeiCam(fileInfo.videoRotation, size.width, size.height)
                    }
                )
            }
            fpsList.add(fps)
            resolutionList.add(resolution)
        }
        val minFps = ResolutionUtil.getMinFps(fpsList)
        val minResolution = ResolutionUtil.calcTimelineInitResolution(resolutionList)
        return Pair(minFps, minResolution)
    }
}