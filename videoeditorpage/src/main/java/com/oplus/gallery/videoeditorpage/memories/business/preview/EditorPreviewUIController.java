/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorPreviewUIController.java
 * * Description: editor ui controller for preview state.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.preview;

import static com.oplus.gallery.business_lib.template.editor.EditorUIConfig.CLEAR_COLOR;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuAdapter;
import com.oplus.gallery.business_lib.template.editor.anim.IPressAnimation;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.base.EditorMemoriesBaseUiController;

import java.util.ArrayList;

public class EditorPreviewUIController extends EditorMemoriesBaseUiController {
    private boolean mIsHasEntryAnimation = true;

    public EditorPreviewUIController(Context context, ViewGroup rootView, EditorBaseState state) {
        super(context, rootView, state);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_memories_editor_preview_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_video_editor_menu_list_layout;
    }

    @Override
    public int getTitleId() {
        return 0;
    }

    @Override
    public void onShow() {
        hidePlayButtonTimeContainer();
        setMenuListView(mContainer.findViewById(R.id.horizontal_list));
        getMenuListView().keepLastFocusItem(true);
        ArrayList<EditorMenuItemViewData> data = initAdapterData(mContext,
                R.array.videoeditor_memories_editor_preview_state_id_array,
                R.array.videoeditor_memories_editor_preview_state_icon_array,
                R.array.videoeditor_memories_editor_preview_state_text_array);
        mAdapter = new EditorMenuAdapter(mContext, data) {
            private int mDefaultBgColor = mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask);

            @Override
            public BaseRecycleViewHolder createViewHolder(View itemView, int viewType) {
                BaseRecycleViewHolder holder = super.createViewHolder(itemView, viewType);
                if (holder instanceof IPressAnimation) {
                    ((IPressAnimation) holder).setPressAnimListener(progress -> {
                        EditorMenuItemView menuItemView = holder.findViewById(com.oplus.gallery.basebiz.R.id.base_editorMenuItem_item_icon);
                        int srcColor = mDefaultBgColor;
                        if ((menuItemView != null) && (srcColor != CLEAR_COLOR)) {
                            menuItemView.setItemBackgroundColor(EditorUIConfig.getAnimAlphaColor(progress, srcColor));
                        }
                    });
                }
                return holder;
            }
        };
        mAdapter.setCanUnselectCurrentPosition(false);
        mAdapter.setItemClickListener(this);
        getMenuListView().setAdapter(mAdapter);
        super.onShow();
    }

    @Override
    public void adaptTimeSeekBarContainer(int layoutId, View view, AppUiResponder.AppUiConfig config, BaseActivity context) {
        if (!mIsHasEntryAnimation) {
            super.adaptTimeSeekBarContainer(layoutId, view, config, context);
        }
    }

    @Override
    public void adaptPreviewArea(int layoutId, @NonNull View view, @NonNull AppUiResponder.AppUiConfig config, @NonNull BaseActivity context) {
        if (!mIsHasEntryAnimation) {
            super.adaptPreviewArea(layoutId, view, config, context);
        }
    }

    public void setIsHasEntryAnimation(boolean isHasEntryAnimation) {
        this.mIsHasEntryAnimation = isHasEntryAnimation;
    }
}
