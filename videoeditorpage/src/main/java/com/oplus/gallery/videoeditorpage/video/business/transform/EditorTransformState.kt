/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorTransformState
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/2
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/2      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.transform

import android.content.Context
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video.VideoClipPlaybackMode
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 负责实现裁剪旋转的业务逻辑
 */
class EditorTransformState(
    context: Context,
    editorControlView: EditorControlView,
    /**
     * 待裁剪的VideoClip
     */
    private val videoClip: IVideoClip,
) : com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState<EditorTransformUIController>(TAG, context, editorControlView) {

    override fun create() {
        super.create()
        stopEngine()
    }

    override fun createUIController(): EditorTransformUIController =
        EditorTransformUIController(
            mContext,
            mEditorControlView,
            this,
            editorEngine,
            requireEditorActivity().findViewById(R.id.engine_preview_layout),
            videoClip,
        )

    override fun showOperaIcon(): Boolean = false

    override fun getPageLevel(): PageLevelEnum = PageLevelEnum.PAGE_LEVEL_SECOND

    override fun getVideoPlaybackMode(): VideoClipPlaybackMode = VideoClipPlaybackMode.SINGLE_VIDEO_CLIP

    override fun getBackupTimeline(): ITimeline? = mUIController.getOriginalTimeline()

    override fun clickCancel() {
        uiController.clickCancel()
        super.clickCancel()
    }

    override fun clickDone() {
        uiController.clickDone()
        super.clickDone()
    }

    companion object {
        private const val TAG = "EditorTransformState"
    }
}