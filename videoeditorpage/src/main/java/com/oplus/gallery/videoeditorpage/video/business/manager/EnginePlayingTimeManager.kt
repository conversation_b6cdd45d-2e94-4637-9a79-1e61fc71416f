/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EnginePlayTimeManager 类用于获取视频播放引擎的播放位置和时长等。
 ** Description:
 ** Version: 1.0
 ** Date : 2025/6/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/6/3      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.video.business.manager

import com.oplus.gallery.foundation.util.debug.GLog.d
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video.VideoClipPlaybackMode
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip

/**
 * EnginePlayTimeManager 类用于获取视频播放引擎的播放位置和时长等。
 */
class EnginePlayingTimeManager(private val editorEngine: EditorEngine) {
    /**
     * 当前正在播放的片段
     */
    private var currentPlayingClip: IClip? = null

    /**
     * 当前视频片段的播放模式
     */
    private var videoClipPlaybackMode: VideoClipPlaybackMode? = null

    /**
     * 切换engine视频片段播放模式，当前支持单视频播放或者多视频播放，如进入滤镜二级页，仅播放单视频
     * @param videoClipPlaybackMode
     * @param clip 当前处理片段，若不为null，以这个为准赋值给currentPlayingClip，否则走原逻辑查找赋值
     */
    fun switchVideoClipPlaybackMode(videoClipPlaybackMode: VideoClipPlaybackMode?, clip: IClip? = null) {
        this.videoClipPlaybackMode = videoClipPlaybackMode
        clip?.let {
            currentPlayingClip = clip
        } ?: cacheCurrentPlayingVideoClip(videoClipPlaybackMode)
    }

    /**
     * 获取单视频播放模式下的播放起始位置
     * @return
     */
    fun getPlayBackStartPosition(): Long =
        currentPlayingClip?.takeIf { inSingleVideoClipMode() }?.inPoint
            ?: EditorEngine.VIDEO_TIME_START

    /**
     * 如果当前处于单个视频片段播放模式，则返回相对于该片段起始位置的播放位置；
     * 否则，返回绝对播放位置。
     * @return
     */
    fun getCurrentVideoPlaybackPosition(): Long =
        editorEngine.getTimelineCurrentPosition().let { position ->
            currentPlayingClip?.takeIf { inSingleVideoClipMode() }?.run {
                (position - inPoint).coerceIn(0, duration)
            } ?: position
        }

    /**
     * 获取当前视频播放位置
     * 如果当前处于单个视频片段播放模式，则返回相对于该片段起始位置的播放位置；
     * 否则，返回绝对播放位置。
     *
     * @param absolutePosition 视频的绝对播放位置
     * @return 当前视频片段的播放位置或绝对播放位置
     */
    fun getCurrentVideoPlaybackPosition(absolutePosition: Long): Long =
        currentPlayingClip?.takeIf { inSingleVideoClipMode() && absolutePosition >= it.inPoint }
            ?.let { absolutePosition - it.inPoint } ?: absolutePosition

    /**
     * 根据当前的播放视频模式（[VideoClipPlaybackMode]），获取视频播放时长，
     * @return
     */
    fun getVideoPlaybackDuration(): Long {
        return currentPlayingClip?.takeIf { inSingleVideoClipMode() }?.duration
            ?: editorEngine.getTimelineDuration()
    }

    /**
     * 根据当前播放片段计算开始和结束时间，支持音频、视频片段
     *
     * @param startTimeMillis 视频播放的开始时间（毫秒）
     * @param endTimeMillis 视频播放的结束时间（毫秒）
     * @return 一个包含计算后的开始和结束时间的Pair对象
     */
    fun calculateStartEndTimeByPlaybackMode(startTimeMillis: Long, endTimeMillis: Long): Pair<Long, Long> {
        // 当前片段有效则计算并返回对应播放开始结束时间，否则直接返回传入的时间
        return currentPlayingClip?.takeIf { it.isLegal() }?.let { clip ->
            val clipEndTime = (clip.inPoint + clip.duration)
            // 如果开始时间小于等于0或者开始时间大于等于视频结束时间减去一帧的时长，则返回视频在时间轴上的绝对时间位置和结束时间
            if ((startTimeMillis <= EditorEngine.VIDEO_TIME_START) || (startTimeMillis >= (clipEndTime - EditorEngine.FRAME_DURATION))) {
                Pair(clip.inPoint, clipEndTime)
            } else {
                Pair(startTimeMillis, clipEndTime)
            }
        } ?: Pair(startTimeMillis, endTimeMillis)
    }

    /**
     * 是否单视频播放模式
     * @return
     */
    private fun inSingleVideoClipMode(): Boolean {
        return videoClipPlaybackMode == VideoClipPlaybackMode.SINGLE_VIDEO_CLIP
    }

    /**
     * 判断是否合法的片段
     */
    private fun IClip.isLegal(): Boolean = ((inPoint >= EditorEngine.VIDEO_TIME_START) && (duration > EditorEngine.VIDEO_DURATION_EMPTY))

    /**
     * 单片段视频播放模式下，需要缓存当前的视频播放片段信息，视频的播放进度和播放时长要根据这个片段信息转换
     * @param videoClipPlaybackMode 当前的播放视频模式
     */
    private fun cacheCurrentPlayingVideoClip(videoClipPlaybackMode: VideoClipPlaybackMode?) {
        val currentTimeline = editorEngine.currentTimeline ?: return
        if (videoClipPlaybackMode != VideoClipPlaybackMode.SINGLE_VIDEO_CLIP || currentTimeline.videoTrackList.isNullOrEmpty()) {
            d(TAG, LogFlag.DL, "[cacheCurrentPlayingVideoClip] is not single video playback mode" +
                    " or videoTrackList is empty")
            currentPlayingClip = null
            return
        }
        val videoTrack = currentTimeline.videoTrackList.first()
        val clipList = videoTrack.clipList
        if (clipList.isNullOrEmpty()) {
            e(TAG, LogFlag.DL, "[cacheCurrentPlayingVideoClip] clipList is is null or empty")
            return
        }

        val currentTime = editorEngine.timelineCurrentPosition
        var accumulatedTime = 0L
        for (videoClip in clipList) {
            accumulatedTime += videoClip.duration
            if (currentTime < accumulatedTime) {
                currentPlayingClip = videoClip
                return
            }
        }
        // 当前播放时间超出了遍历片段累计的时间，不符合预期逻辑，需要打印日志
        if (accumulatedTime <= currentTime) {
            e(TAG, LogFlag.DL, "[cacheCurrentPlayingVideoClip] currentTime exceeds total duration")
        }
    }

    companion object {
        private const val TAG: String = "EnginePlaybackManager"
    }
}