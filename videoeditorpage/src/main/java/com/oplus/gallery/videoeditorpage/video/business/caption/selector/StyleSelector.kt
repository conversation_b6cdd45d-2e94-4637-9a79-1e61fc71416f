/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : StyleSelector.kt
 ** Description : 字幕样式面板组件
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import android.view.View
import com.oplus.gallery.videoeditorpage.video.business.caption.resource.ResourceManager
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.StylesAdapter
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewData

/**
 * 字幕样式面板组件
 */
internal class StyleSelector(
    isInMultiWindow: Boolean,
    bottomNaviBarHeight: Int,
    private val resManager: ResourceManager,
    pageType: PageType
) : DownloadableSelector(isInMultiWindow, bottomNaviBarHeight, pageType) {

    /**
     * 样式适配器
     */
    private var styleAdapter: StylesAdapter? = null

    /**
     * 配置适配器
     */
    override fun initAdapter() {
        resourceRecyclerView?.let {
            styleAdapter = StylesAdapter(it)
            it.adapter = styleAdapter
        }
    }

    /**
     * 更新样式item
     */
    fun notifyStyleItemChanged(position: Int) {
        styleAdapter?.notifyItemChanged(position)
    }

    /**
     * 初始化数据
     */
    override fun initData() {
        resManager.fetchStylesData { resourceItems ->
            styleAdapter?.let {
                it.setDataSource(resourceItems as List<StyleViewData>)
                it.setSelectedPositionForStyle(resourceId)
                captionEffectsChangedListener?.styleChanged(pageType, resourceItems[it.selectedPosition], it.selectedPosition)
                it.onItemClickCallback = { v: View?, position: Int, data: StyleViewData? ->
                    // 点击item回调
                    data?.let { styleItem ->
                        captionEffectsChangedListener?.styleChanged(pageType, styleItem, position)
                    }
                    currentClickPosition = position
                }
            }
        }
    }

    override fun destroy() {
        styleAdapter?.destroy()
        super.destroy()
    }

    companion object {
        private const val TAG = "StyleSelector"
    }
}
