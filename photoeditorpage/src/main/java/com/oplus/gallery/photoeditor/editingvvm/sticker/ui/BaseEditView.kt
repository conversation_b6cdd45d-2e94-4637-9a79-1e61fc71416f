/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseEditView.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/09/20
 * Author: xiewu<PERSON><PERSON>@Apps.Gallery
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery		2024/09/20		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.sticker.ui

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.view.MotionEvent
import android.view.ViewConfiguration
import com.oplus.gallery.foundation.ui.gesture.detector.ScaleRotateDetector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.base.processor.entry.ColorSizeEntry.SizeEntry
import com.oplus.gallery.photoeditor.common.OnStepChangeListener
import com.oplus.gallery.photoeditor.common.drawable.DrawableBuffer
import com.oplus.gallery.photoeditor.common.drawable.EditableDrawable
import com.oplus.gallery.photoeditor.common.drawable.EditableDrawable.DrawableType
import com.oplus.gallery.photoeditor.common.drawable.OnBufferChangeListener
import com.oplus.gallery.photoeditor.common.drawable.OperationStep
import com.oplus.gallery.photoeditor.common.drawable.OperationStep.OperateType
import com.oplus.gallery.photoeditor.frame.data.OperationStack
import com.oplus.gallery.photoeditor.frame.data.OperationStack.OnOperationStackChangeListener
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizerView2
import java.util.Locale
import java.util.Stack
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.abs

abstract class BaseEditView(context: Context, bufferChangeListener: OnBufferChangeListener?) :
    GestureRecognizerView2(context), OnOperationStackChangeListener<OperationStep> {
    protected var delIconDrawable: Drawable? = null
    protected var rotateIconDrawable: Drawable? = null
    protected var stretchIconDrawable: Drawable? = null

    protected var downX: Float = INVALID_POINT.x
    protected var downY: Float = INVALID_POINT.y
    protected var isClickEvent: Boolean = true
    protected var isRotateScaleEvent: Boolean = false
    protected var showCompareImage: Boolean = false
    private var touchSlop: Int =
        ViewConfiguration.get(getContext()).scaledTouchSlop
    protected var touchAreaWidth: Int
    private var operationCounter: AtomicInteger = AtomicInteger(0)
    private var currSize: SizeEntry? = null

    protected var preTouchPoint: PointF = PointF()
    protected var currTouchPoint: PointF = PointF()
    protected var imageTransformMatrix: Matrix = Matrix()
    protected var curringDrawingImageOutBound: RectF = RectF()
    private var currentImageSize: Rect = Rect()

    val drawableStack: Stack<EditableDrawable> = Stack()

    private var selectedDrawableType: DrawableType = DrawableType.FREE_LINE

    var opIconWidth: Float = 0f

    protected var opStack: OperationStack<OperationStep> = OperationStack()
    protected var opType: OperateType = OperateType.CREATE
    protected var stepChangeListener: OnStepChangeListener? = null
    protected var focusDrawableOpStep: OperationStep? = null
    protected var lastSelectedDrawable: EditableDrawable? = null
    protected var drawableBuff: DrawableBuffer? = null
    protected var bufferChangeListener: OnBufferChangeListener?
    protected var onFocusStepChangeListener: OnFocusStepChangeListener? = null

    var onInvalid: (() -> Unit)? = null

    interface OnFocusStepChangeListener {
        fun onFocusStepChange(step: OperationStep?)
    }

    init {
        opStack.setOnOperationStackChangeListener(this)
        touchAreaWidth = resources.getDimensionPixelOffset(R.dimen.picture3d_doodle_touch_area_width)
        this.bufferChangeListener = bufferChangeListener
    }

    open val isSelectedItem: Boolean
        get() = focusDrawableOpStep?.isSelected == true

    override fun onAnimationPropertySet(properties: PreviewAnimationProperties) {
        super.onAnimationPropertySet(properties)
        updateCurrentMatrix()
        var bufferWidth = 0
        var bufferHeight = 0
        val finalDrawingOutBound = properties.contentDrawingOutBound.final
        GLog.d(TAG, LogFlag.DF, "onAnimationPropertySet rect = $finalDrawingOutBound")
        if (finalDrawingOutBound.isEmpty) {
            bufferWidth = properties.imageWidth
            bufferHeight = properties.imageHeight

            if ((bufferWidth == 0) && (bufferHeight == 0)) {
                /*
                此时说明GestureAnimator还未被ImageSize初始化
                暂时不创建DrawableBuffer，等待原图加载完毕后的再次通知，否则DrawableBuffer将被初始化为（0, 0）尺寸而导致不可用
                */
                GLog.w(TAG, LogFlag.DF, "setGestureAnimator, buffer size is (0, 0), reject and wait for next notify")
            } else {
                createDrawableBuffer(bufferWidth, bufferHeight)
            }
        } else {
            bufferWidth = finalDrawingOutBound.width().toInt()
            bufferHeight = finalDrawingOutBound.height().toInt()
            GLog.d(
                TAG, LogFlag.DF, String.format(
                    Locale.ENGLISH,
                    "setGestureAnimator, use finalDrawingOutBound size(%d, %d) as drawable buffer size",
                    bufferWidth, bufferHeight
                )
            )

            // 此时正常创建DrawableBuffer，因为GestureAnimator已经具备了ImageSize和DisplayRect的数值，可以正常工作
            createDrawableBuffer(bufferWidth, bufferHeight)
        }
    }

    private fun createDrawableBuffer(bufferWidth: Int, bufferHeight: Int) {
        if ((drawableBuff == null) && (bufferWidth > 0) && (bufferHeight > 0)) {
            drawableBuff = DrawableBuffer(bufferWidth, bufferHeight, animationProperties.fullTransform.current)
        }
    }

    /**
     * down事件的坐标命中图层时触发
     */
    abstract fun onDownFocusDrawableOpStep(): Boolean

    @Synchronized
    override fun onUpOrCancel(event: MotionEvent): Boolean {
        val eventX = event.x
        val eventY = event.y
        currTouchPoint.set(unprojection(eventX, eventY))
        if (isClickEvent) {
            onUpClickEvent(eventX, eventY)
        } else {
            onUpNotClickEvent()
        }
        resetTouchPoint()
        if ((focusDrawableOpStep?.mParent != null) && (focusDrawableOpStep?.mParent?.pressState == true)) {
            focusDrawableOpStep?.mParent?.pressState = false
            if ((focusDrawableOpStep?.mOpType == OperateType.DELETE)
                || (focusDrawableOpStep?.mOpType == OperateType.ROTATE)
                || (focusDrawableOpStep?.mOpType == OperateType.STRETCH)
            ) {
                onInvalid?.invoke()
            }
        }
        if ((focusDrawableOpStep != null) && (onFocusStepChangeListener != null)) {
            onFocusStepChangeListener?.onFocusStepChange(focusDrawableOpStep)
        }
        decrementOperationCounter()
        invalidate()
        super.onUpOrCancel(event)
        return true
    }

    private fun onUpClickDelete() {
        if (focusDrawableOpStep == null) {
            return
        }
        val index = opStack.search(focusDrawableOpStep)
        if (GProperty.PHOTOEDIT_DEBUG) {
            GLog.d(TAG, LogFlag.DF, "onUp, search in mOpStack, result = $index")
        }
        val deleteOpStep = OperationStep(focusDrawableOpStep?.mParent, opType)
        if (deleteOpStep.canAddToStack()) {
            //drawable buffer will update in onOpStackChange, so do not update buffer here
            updateSelectedState(focusDrawableOpStep?.mParent, false, false)
            opStack.push(deleteOpStep)
        } else {
            clearFocusDrawableOpStep()
            onInvalid?.invoke()
        }
    }

    protected fun onUpClickCreate(eventX: Float, eventY: Float) {
        val operationStep = focusDrawableOpStep ?: return
        if (checkMoved(downX, downY, eventX, eventY)) {
            operationStep.update(opType, currTouchPoint, preTouchPoint, true)
            /*
            new create drawable, do not update buffer immediately.
            must after operationStep.update. otherwise the end point is invalid, and can not draw on buffer
            */
            updateSelectedState(operationStep.mParent, true, false)
            if (operationStep.canAddToStack()) {
                opStack.push(operationStep, false)
            } else {
                onInvalid?.invoke()
            }
        } else {
            clearFocusDrawableOpStep()
            onInvalid?.invoke()
        }
    }

    protected fun onUpClickEvent(eventX: Float, eventY: Float) {
        if (opType == OperateType.DELETE) {
            onUpClickDelete()
        } else if (opType == OperateType.PRESELECTION) {
            if (focusDrawableOpStep != null) {
                updateSelectedState(focusDrawableOpStep?.mParent, true, true)
                onInvalid?.invoke()
            }
        } else if (opType == OperateType.CREATE) {
            onUpClickCreate(eventX, eventY)
        } else if (opType == OperateType.TRANSLATE) {
            if (focusDrawableOpStep != null) {
                updateSelectedState(focusDrawableOpStep?.mParent, true, false)
            }
        } else {
            if (GProperty.PHOTOEDIT_DEBUG) {
                GLog.d(
                    TAG, LogFlag.DF, "onUp, unHandle event, mOpType = "
                        + opType + ", mFocusDrawableOpStep = " + focusDrawableOpStep
                )
            }
        }
    }

    protected fun onUpNotClickEvent() {
        if (opType == OperateType.PRESELECTION) {
            focusDrawableOpStep = null
        } else {
            val operationStep = focusDrawableOpStep
            if (operationStep != null) {
                if (opType == OperateType.CREATE) {
                    // 这里要同步一下path，确保buffer的显示与真实绘制保持一致
                    updateFreeLinePath(currTouchPoint)
                    updateSelectedState(focusDrawableOpStep?.mParent, true, false)
                }
                if (opType != OperateType.MULTI_TOUCH) {
                    operationStep.update(opType, currTouchPoint, preTouchPoint, true)
                    if (operationStep.canAddToStack()) {
                        opStack.push(operationStep, false)
                    } else {
                        onInvalid?.invoke()
                    }
                }
            }
        }
    }

    protected fun updateFreeLinePath(currPointF: PointF?) {
        if ((focusDrawableOpStep == null) || (focusDrawableOpStep?.mParent == null)) {
            return
        }
        val type = focusDrawableOpStep?.mParent?.type
        if ((type == DrawableType.FREE_LINE)
            || (type == DrawableType.PENCIL)
        ) {
            focusDrawableOpStep?.updatePath(currPointF)
        }
    }

    @Synchronized
    override fun onScaleRotateBegin(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        detector: ScaleRotateDetector
    ): Boolean {
        if (GProperty.PHOTOEDIT_DEBUG) {
            GLog.d(
                TAG, LogFlag.DF, "onScaleRotateBegin, angle = " + angle
                    + ", onScaleRotateBegin ContentScale = " + scale
                    + ", mIsClickEvent = " + isClickEvent
                    + ", mCurrTouchPoint = " + currTouchPoint
                    + ", mPreTouchPoint = " + preTouchPoint
                    + ", mFocusDrawableOpStep = " + focusDrawableOpStep
            )
        }
        if (opType == OperateType.INVALID) {
            return true
        }
        isRotateScaleEvent = true
        if (focusDrawableOpStep != null) {
            focusDrawableOpStep?.update(opType, currTouchPoint, preTouchPoint, true)
            if (!isClickEvent) {
                if (opType == OperateType.CREATE) {
                    //new create drawable, do not update buffer immediately
                    updateSelectedState(focusDrawableOpStep?.mParent, true, false)
                } else if (opType == OperateType.OUT_BOUNDS) {
                    clearFocusDrawableOpStep()
                    return true
                }
                if (focusDrawableOpStep?.canAddToStack() == true) {
                    opStack.push(focusDrawableOpStep, false)
                } else {
                    onInvalid?.invoke()
                }
                opType = OperateType.MULTI_TOUCH
            } else {
                clearFocusDrawableOpStep()
            }
        }
        return super.onScaleRotateBegin(
            scalePivotX,
            scalePivotY,
            angle,
            rotatePivotX,
            rotatePivotY,
            scale,
            detector
        )
    }


    override fun onScaleRotate(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        detector: ScaleRotateDetector
    ): Boolean {
        if (GProperty.PHOTOEDIT_DEBUG) {
            GLog.d(TAG, LogFlag.DF, "onScaleRotate, angle = $angle, scale = $scale")
        }
        if (opType == OperateType.INVALID) {
            return true
        }
        return super.onScaleRotate(
            scalePivotX,
            scalePivotY,
            angle,
            rotatePivotX,
            rotatePivotY,
            scale,
            detector
        )
    }

    override fun onScaleRotateEnd(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        detector: ScaleRotateDetector
    ): Boolean {
        GLog.d(TAG, LogFlag.DF, "onScaleRotateEnd, angle = $angle, scale = $scale")
        if (opType == OperateType.INVALID) {
            return true
        }
        isRotateScaleEvent = false
        return super.onScaleRotateEnd(
            scalePivotX,
            scalePivotY,
            angle,
            rotatePivotX,
            rotatePivotY,
            scale,
            detector
        )
    }

    private fun resetTouchPoint() {
        currTouchPoint.set(INVALID_POINT)
        preTouchPoint.set(INVALID_POINT)
    }

    protected fun touchOnEditableDrawableEdge(currPoint: PointF): EditableDrawable? {
        var selectedType: EditableDrawable? = null
        val size = drawableStack.size
        var found = false
        for (i in size - 1 downTo 0) {
            val editableDrawable = drawableStack[i] ?: continue
            if (GProperty.PHOTOEDIT_DEBUG) {
                GLog.d(
                    TAG, LogFlag.DF, "touchOnEditableDrawableEdge, currPoint = " + currPoint
                        + ", found = " + found + ", editableDrawable = " + editableDrawable.peekOperationStep()
                )
            }

            if (!found) {
                found = editableDrawable.pointOnEdge(
                    currPoint, editableDrawable.startPoint,
                    editableDrawable.lastPoint
                )
                if (found) {
                    selectedType = editableDrawable
                }
            }
        }
        return selectedType
    }

    protected fun checkMoved(downX: Float, downY: Float, currX: Float, currY: Float): Boolean {
        val xDiff = abs((currX - downX).toDouble()).toFloat()
        val yDiff = abs((currY - downY).toDouble()).toFloat()
        return (xDiff > touchSlop) || (yDiff > touchSlop)
    }

    @Synchronized
    fun setStrokeSize(size: SizeEntry?) {
        currSize = size
        if (isSelectedItem) {
            focusDrawableOpStep?.mParent?.setStrokeSize(size, imageTransformMatrix)
            /*
            set the stroke width will not change the drawable position.
            so we can use invalid point instead of startPoint and prePoint here.
            */
            focusDrawableOpStep?.update(
                OperateType.UPDATE_STROKE_WIDTH,
                INVALID_POINT,
                INVALID_POINT,
                false
            )
            onInvalid?.invoke()
        }
    }

    fun setDrawableTypeIndex(index: Int) {
        val setType: DrawableType = when (index) {
            DrawableType.FREE_LINE.ordinal -> DrawableType.FREE_LINE
            DrawableType.PENCIL.ordinal -> DrawableType.PENCIL

            DrawableType.LINE.ordinal -> DrawableType.LINE

            DrawableType.RECTANGLE.ordinal -> DrawableType.RECTANGLE

            DrawableType.OVAL.ordinal -> DrawableType.OVAL

            DrawableType.ARROW.ordinal -> DrawableType.ARROW

            else -> throw IllegalArgumentException("setDrawableTypeIndex, can not support this type, index = $index")
        }

        if (selectedDrawableType == setType) {
            return
        }
        selectedDrawableType = setType
    }

    @Synchronized
    override fun onOpStackChange(step: OperationStep, isForward: Boolean) {
        val index = drawableStack.indexOf(step.mParent)
        if (GProperty.PHOTOEDIT_DEBUG) {
            GLog.d(
                TAG, LogFlag.DF, "onOpStackChange, isForward = " + isForward
                    + ", step = " + step + ", index = " + index
            )
        }
        if (index >= 0) {
            onOpStackChange(step, isForward, index)
        } else {
            drawableStack.push(step.mParent)
            if (step.mParent.isOperationEmpty) {
                step.mParent.pushOperationStep(step)
            }
            //a new drawable added, only add it to buffer
            if (!step.isSelected && (drawableBuff != null) && (drawableBuff?.addDrawableOnBuffer(step.mParent) == true)
            ) {
                notifyBitmapBufferChanged()
            }
        }
        onInvalid?.invoke()
        notifyStepChanged()
    }

    private fun onOpStackChange(step: OperationStep, isForward: Boolean, index: Int) {
        var removedDrawable: EditableDrawable? = null
        if (isForward) {
            if ((step.mOpType == OperateType.DELETE)
                || (step.mOpType == OperateType.FORCE_DELETE)
            ) {
                drawableStack.removeAt(index)
                unSelectFocusOpStep()
                removedDrawable = step.mParent
            } else {
                drawableStack[index].pushOperationStep(step)
            }
        } else {
            if ((step.mOpType == OperateType.DELETE)
                || (step.mOpType == OperateType.FORCE_DELETE)
            ) {
                drawableStack.removeAt(index)
                unSelectFocusOpStep()
                removedDrawable = step.mParent
            } else {
                val opDrawable = drawableStack[index]
                val opStep = opDrawable.popOperationStep()
                clearStepOnStackChange(opStep)
                if (opDrawable.isOperationEmpty) {
                    drawableStack.removeAt(index)
                    removedDrawable = step.mParent
                }
            }
        }

        rebuildBuffer(step, removedDrawable)
    }

    private fun clearStepOnStackChange(opStep: OperationStep?) {
        if ((opStep != null) && (opStep == focusDrawableOpStep)) {
            clearFocusDrawableOpStep()
        }
    }

    private fun unSelectFocusOpStep() {
        if (focusDrawableOpStep != null) {
            focusDrawableOpStep?.mParent?.selected = false
            focusDrawableOpStep = null
        }
    }

    private fun rebuildBuffer(step: OperationStep, removedDrawable: EditableDrawable?) {
        if (step.isSelected) {
            return
        }
        invalidate()
        if ((drawableBuff != null) && drawableBuff?.rebuildBuffer(
                drawableStack,
                removedDrawable
            ) == true
        ) {
            notifyBitmapBufferChanged()
        }
    }

    private fun notifyBitmapBufferChanged() {
        incrementOperationCounter()
        if ((bufferChangeListener != null) && (drawableBuff != null)) {
            bufferChangeListener?.onBitmapBufferChanged(drawableBuff?.bufferBitmap)
        } else {
            decrementOperationCounter()
        }
    }

    override fun isDirty(): Boolean {
        return !drawableStack.empty()
    }

    fun save(srcBitmap: Bitmap?): Bitmap? {
        if ((srcBitmap != null) && !srcBitmap.isRecycled) {
            val localBitmap: Bitmap? = if (!srcBitmap.isMutable) {
                srcBitmap.copy(Bitmap.Config.ARGB_8888, true)
            } else {
                srcBitmap
            }
            val canvas = Canvas(localBitmap!!)
            canvas.save()
            for (i in drawableStack.indices) {
                val drawable = drawableStack[i]
                drawable.draw(canvas, false)
            }
            canvas.restore()
            if (localBitmap != srcBitmap) {
                srcBitmap.recycle()
            }
            return localBitmap
        }
        return null
    }

    @Synchronized
    fun undo() {
        if (focusDrawableOpStep != null) {
            /*
            onOpStackChange will rebuild buffer, do not need invoke updateSelectedState, otherwise it update buffer twice,
            and the screen will flicker
            */
            focusDrawableOpStep?.mParent?.selected = false
            focusDrawableOpStep = null
            lastSelectedDrawable = null
        }
        opStack.undo()
    }

    @Synchronized
    fun redo() {
        clearFocusDrawableOpStep()
        opStack.redo()
    }

    protected fun clearFocusDrawableOpStep() {
        if (focusDrawableOpStep != null) {
            updateSelectedState(focusDrawableOpStep?.mParent, false, true)
            lastSelectedDrawable = null
            focusDrawableOpStep = null
        }
    }

    private fun notifyStepChanged() {
        if (stepChangeListener != null) {
            stepChangeListener?.onStepChanged(opStack.canUndo(), opStack.canRedo())
        }
    }

    fun onCompare(isPreView: Boolean) {
        if (isPreView != showCompareImage) {
            showCompareImage = isPreView
            onInvalid?.invoke()
        }
    }

    protected fun updateCurrentMatrix() {
        val currentOutBound = currentDrawingOutBound
        val imageWidth = animationProperties.imageWidth
        val imageHeight = animationProperties.imageHeight

        val isDrawingOutBoundChanged = curringDrawingImageOutBound != currentOutBound
        val isImageSizeChanged = ((currentImageSize.width() != imageWidth)
            || (currentImageSize.height() != imageHeight))
        if (!isImageSizeChanged && !isDrawingOutBoundChanged) {
            return
        }
        curringDrawingImageOutBound.set(currentOutBound)
        currentImageSize[0, 0, imageWidth] = imageHeight
        imageTransformMatrix = animationProperties.fullPose.current.affine() ?: Matrix()
    }

    protected fun unprojection(x: Float, y: Float): PointF {
        return MathUtils.unprojectionPoint(imageTransformMatrix, x, y)
    }

    /**
     * update editableDrawable selected state and update the drawable buffer
     *
     * @param drawable     the drawable that updated
     * @param isSelected   chang to select state
     * @param updateBuffer need update drawable buffer
     */
    protected fun updateSelectedState(
        drawable: EditableDrawable?,
        isSelected: Boolean,
        updateBuffer: Boolean
    ) {
        GLog.d(
            TAG, LogFlag.DF, "updateSelectedState, drawable = " + drawable + ", isSelected = "
                + isSelected + ", b = " + updateBuffer
        )
        if (drawable == null) {
            GLog.w(TAG, LogFlag.DF, "updateSelectedState, drawable is null!")
            return
        }
        if (drawable.selected == isSelected) {
            return
        }
        var needUploadBuffer = false
        drawable.selected = isSelected
        /*
        Use getSelected() to get the real selected state,
        because freeLine drawable is always unselected not matter that set selected is true
        */
        val realSelectedState = drawable.selected

        //free line drawable selected state always false, so must force update buffer
        if (updateBuffer || (drawable.type == DrawableType.FREE_LINE) /*|| (drawable.getType() == EditableDrawable.DrawableType.MARKPEN)*/
            || (drawable.type == DrawableType.PENCIL)
        ) {
            if (lastSelectedDrawable !== drawable) {
                if (!realSelectedState) {
                    if (drawableBuff != null) {
                        needUploadBuffer = drawableBuff?.addDrawableOnBuffer(drawable) ?: return
                    }
                } else {
                    invalidate()
                    if (drawableBuff != null) {
                        needUploadBuffer = drawableBuff?.rebuildBuffer(drawableStack, null) == true
                    }
                }
            } else if (!realSelectedState) {
                if (drawableBuff != null) {
                    needUploadBuffer = drawableBuff?.addDrawableOnBuffer(drawable) == true
                }
            }
        }
        if (needUploadBuffer) {
            notifyBitmapBufferChanged()
        }
        lastSelectedDrawable = if (isSelected) drawable else null
    }

    fun resume(isActivityResume: Boolean) {
        GLog.d(
            TAG, LogFlag.DF, "resume, isActivityResume = " + isActivityResume + ", mDrawableBuff = "
                + drawableBuff
        )
        if (isActivityResume && (drawableBuff != null)) {
            invalidate()
            if (drawableBuff?.rebuildBuffer(drawableStack, null) == true) {
                notifyBitmapBufferChanged()
            }
        }
    }

    @get:Synchronized
    val isBusying: Boolean
        get() {
            val count = operationCounter.get()
            if (count > 0) {
                GLog.d(TAG, LogFlag.DF, "isBusying, count = $count")
                return true
            } else {
                return false
            }
        }

    @Synchronized
    protected fun incrementOperationCounter() {
        val count = operationCounter.get()
        if (count < 0) {
            operationCounter.set(0)
        }
        operationCounter.getAndIncrement()
    }

    @Synchronized
    protected fun decrementOperationCounter() {
        val count = operationCounter.get()
        if (count > 0) {
            operationCounter.decrementAndGet()
        }
    }

    fun setDelIcon(delIconDrawable: Drawable?) {
        this.delIconDrawable = delIconDrawable
    }

    fun setRotateIcon(rotateIconDrawable: Drawable?) {
        this.rotateIconDrawable = rotateIconDrawable
    }

    fun setStretchIcon(stretchIconDrawable: Drawable?) {
        this.stretchIconDrawable = stretchIconDrawable
    }


    companion object {
        private const val TAG = "BaseEditView"
        val INVALID_POINT: PointF = PointF(-1f, -1f)
        const val MULTI_TOUCH_DETECTED_TIME_OUT: Int = 100
    }
}
