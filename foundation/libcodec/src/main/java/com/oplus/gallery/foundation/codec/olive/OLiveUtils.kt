/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OLiveUtils.kt
 ** Description : OLive相关操作工具类
 ** Version     : 1.0
 ** Date        : 2025/1/3
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D         2025/1/3     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.olive

import com.oplus.gallery.olive_decoder.OLiveDecode

/**
 * OLive相关操作工具类
 */
object OLiveUtils {

    /**
     * 获取 olive 文件中的视频范围信息
     * @return pair first是视频文件开头的偏移量，length是视频文件的长度
     */
    @JvmStatic
    fun getOliveVideoRangeInfo(filePath: String): Pair<Long, Long>? {
        val oLive = OLiveDecode.create(filePath).decode() ?: return null
        val offset: Long = oLive.microVideo?.offset ?: return null
        val length: Long = oLive.microVideo?.length ?: return null
        return Pair(offset, length)
    }
}