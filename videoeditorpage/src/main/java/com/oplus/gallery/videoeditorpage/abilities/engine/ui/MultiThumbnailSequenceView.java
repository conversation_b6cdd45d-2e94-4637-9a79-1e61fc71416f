/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MultiThumbnailSequenceView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.engine.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatImageView;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsIconGenerator;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsUtils;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;


/**
 *   \brief Multiple thumbnail sequence
 *
 *   A multi-thumbnail sequence displays a sequence of thumbnails of multiple segments within a timeline. It supports the adjustment of the thumbnail time scale, and supports scrolling when the effective content is too long.
 *   \warning In the NvsMultiThumbnailSequenceView class, all public APIs are used in the UI thread! ! !
 */
public class MultiThumbnailSequenceView extends HorizontalScrollView {

    //Image zoom to fill the full window without maintaining the original scale (default mode)
    public static final int THUMBNAIL_IMAGE_FILLMODE_STRETCH = 0;

    //The image fills the full window evenly and scales if necessary
    public static final int THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP = 1;
    private static final String TAG = "MultiThumbnailSequenceView";

    // These two flags control the cached keyframe only mode and whether it is still valid
    private static final int THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY = 1;
    private static final int THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID = 2;

    Bitmap mPlaceholderbitmap;

    private NvsIconGenerator mIconGenerator = null;

    private boolean mScrollEnabled = true;

    private OnSequenceLoadDataListener mOnSequenceLoadDataListener;

    private ArrayList<ThumbnailSequenceDesc> mDescArray;
    private float mThumbnailAspectRatio = 9.0f / 16;
    private double mPixelPerMicrosecond = 1080.0 / 15000000;
    private int mStartPadding = 0;
    private int mEndPadding = 0;
    private int mThumbnailImageFillMode = THUMBNAIL_IMAGE_FILLMODE_STRETCH;
    private long mMaxTimelinePosToScroll = 0;
    private ArrayList<ThumbnailSequence> mThumbnailSequenceArray = new ArrayList<ThumbnailSequence>();
    private TreeMap<Integer, ThumbnailSequence> mThumbnailSequenceMap = new TreeMap<Integer, ThumbnailSequence>();
    private int mContentWidth = 0;
    private TreeMap<ThumbnailId, Thumbnail> mThumbnailMap = new TreeMap<ThumbnailId, Thumbnail>();

    private int mMaxThumbnailWidth = 0;
    private boolean mUpdatingThumbnail = false;

    private long mMinSequenceDuration = 0;
    private boolean mScrollLimited = false;
    private int mMaxScrolledPos = Integer.MAX_VALUE;

    private ContentView mContentView;

    private OnScrollChangeListener mScrollChangeListener;

    public MultiThumbnailSequenceView(Context context) {
        super(context);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MultiThumbnailSequenceView(Context context, AttributeSet attrs) {
        super(context, attrs);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MultiThumbnailSequenceView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public interface OnScrollChangeListener {
        void onScrollChanged(MultiThumbnailSequenceView view, int x, int oldx);
    }

    public static class ThumbnailSequenceDesc {
        public String mediaFilePath;        //Video file path
        public long inPoint;                //Timeline in point (in microseconds)
        public long outPoint;               //Timeline out point (in microseconds)
        public long trimIn;                 //Trim in point (in microseconds)
        public long trimOut;                //Trim out point (in microseconds)
        public boolean stillImageHint;      //Whether it is a static picture
        public boolean onlyDecodeKeyFrame;  //Whether decode only key frames
        public float thumbnailAspectRatio;  //Thumbnail's aspect ratio of this sequence, 0 means comply with the thumbnail's aspect ratio of the view

        public ThumbnailSequenceDesc() {
            inPoint = 0;
            outPoint = 4000000;
            trimIn = 0;
            trimOut = 4000000;
            stillImageHint = false;
            onlyDecodeKeyFrame = false;
            thumbnailAspectRatio = 0;
        }

        public long getDuration() {
            return outPoint - inPoint;
        }
    }

    private static class ThumbnailSequence {
        int mIndex;
        String mMediaFilePath;
        long mInPoint;
        long mOutPoint;
        long mTrimIn;
        long mTrimDuration;
        long mShowDuration;
        boolean mStillImageHint;
        boolean mOnlyDecodeKeyFrame;
        public float mThumbnailAspectRatio;

        int mFlags;

        int mX; // Relative to content view
        int mWidth;
        int mThumbnailWidth;

        public ThumbnailSequence() {
            mIndex = 0;
            mInPoint = 0;
            mOutPoint = 0;
            mTrimIn = 0;
            mTrimDuration = 0;
            mShowDuration = 0;
            mStillImageHint = false;
            mOnlyDecodeKeyFrame = false;
            mThumbnailAspectRatio = 0;
            mFlags = 0;
            mX = 0;
            mWidth = 0;
            mThumbnailWidth = 0;
        }

        public long calcTimestampFromXWithThumbnailDuration(float x, long duratinPerThumbnail) {
            long timestamp = mTrimIn + (long) Math.floor((double) (x - mX) / mWidth * mTrimDuration + 0.5);
            long timestamp1 = (long) Math.floor(((double) timestamp / duratinPerThumbnail + 0.01)) * duratinPerThumbnail;
            return timestamp1;
        }
    }


    private static class ThumbnailId implements Comparable<ThumbnailId> {
        public int mSeqIndex;
        public long mTimestamp;

        public ThumbnailId(int seqIndex, long timestamp) {
            mSeqIndex = seqIndex;
            mTimestamp = timestamp;
        }

        @Override
        public int compareTo(ThumbnailId o) {
            if (mSeqIndex < o.mSeqIndex) {
                return -1;
            } else if (mSeqIndex > o.mSeqIndex) {
                return 1;
            } else {
                if (mTimestamp < o.mTimestamp) {
                    return -1;
                } else if (mTimestamp > o.mTimestamp) {
                    return 1;
                } else {
                    return 0;
                }
            }
        }
    }

    private static class Thumbnail {
        ThumbnailSequence mOwner;
        long mTimestamp;
        ImageView mImageView;
        long mIconTaskId;
        boolean mImageViewUpToDate;
        boolean mTouched;

        public Thumbnail() {
            mTimestamp = 0;
            mIconTaskId = 0;
            mImageViewUpToDate = false;
            mTouched = false;
        }
    }

    private class ContentView extends ViewGroup {
        public ContentView(Context context) {
            super(context);
        }

        /**
         * Any layout manager that doesn't scroll will want this.
         */
        @Override
        public boolean shouldDelayChildPressedState() {
            return false;
        }

        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            // NOTE: At the time our size is being measured
            // The content width may not be ready!
            int w = mContentWidth, h;

            int heightMode = MeasureSpec.getMode(heightMeasureSpec);
            int heightSize = MeasureSpec.getSize(heightMeasureSpec);
            if (heightMode == MeasureSpec.EXACTLY || heightMode == MeasureSpec.AT_MOST) {
                h = heightSize;
            } else {
                h = MultiThumbnailSequenceView.this.getHeight(); // Shouldn't reach here
            }

            // Check against our minimum height and width
            w = Math.max(w, getSuggestedMinimumWidth());
            h = Math.max(h, getSuggestedMinimumHeight());

            w = resolveSizeAndState(w, widthMeasureSpec, 0);
            h = resolveSizeAndState(h, heightMeasureSpec, 0);

            setMeasuredDimension(w, h);
        }

        @Override
        protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
            updateThumbnails(true);
        }

        @Override
        protected void onSizeChanged(int w, int h, int oldw, int oldh) {
            if (h != oldh) {
                requestUpdateThumbnailSequenceGeometry();
            }

            super.onSizeChanged(w, h, oldw, oldh);
        }
    }

    public void setMinSequenceShowDuration(long minShowDuration) {
        mMinSequenceDuration = minShowDuration;
    }

    /**
     *   \brief Sets the thumbnail sequence description array
     *   \param descArray The thumbnail sequence describes the array. Note: Once it is set, modifying the contents of the array will not work unless thumbnail sequence description is set array again.
     *   \sa getThumbnailSequenceDescArray
     */
    public void setThumbnailSequenceDescArray(ArrayList<ThumbnailSequenceDesc> descArray) {
        NvsUtils.checkFunctionInMainThread();
        if (descArray == mDescArray) {
            return;
        }

        clearThumbnailSequences();
        mPlaceholderbitmap = null;

        mDescArray = descArray;
        if (descArray != null) {
            int index = 0;
            long lastOutPoint = 0;
            for (ThumbnailSequenceDesc desc : descArray) {
                if ((desc.mediaFilePath == null) || (desc.inPoint < lastOutPoint) ||
                        (desc.outPoint <= desc.inPoint) || (desc.trimIn < 0) ||
                        (desc.trimOut <= desc.trimIn)) {
                    GLog.e(TAG, "Invalid ThumbnailSequenceDesc!");
                    continue;
                }

                ThumbnailSequence thumbnailSequence = new ThumbnailSequence();
                thumbnailSequence.mIndex = index++;
                thumbnailSequence.mMediaFilePath = desc.mediaFilePath;
                thumbnailSequence.mInPoint = desc.inPoint;
                thumbnailSequence.mOutPoint = desc.outPoint;
                thumbnailSequence.mTrimIn = desc.trimIn;
                thumbnailSequence.mTrimDuration = desc.trimOut - desc.trimIn;
                thumbnailSequence.mShowDuration = desc.outPoint - desc.inPoint;
                thumbnailSequence.mStillImageHint = desc.stillImageHint;
                thumbnailSequence.mOnlyDecodeKeyFrame = desc.onlyDecodeKeyFrame;
                thumbnailSequence.mThumbnailAspectRatio = desc.thumbnailAspectRatio;

                mThumbnailSequenceArray.add(thumbnailSequence);

                lastOutPoint = desc.outPoint;
            }
        }

        updateThumbnailSequenceGeometry();
    }

    /**
     *   \brief Gets the thumbnail sequence description array
     *   \return Returns the obtained thumbnail sequence description array.
     *   \sa setThumbnailSequenceDescArray
     */
    public ArrayList<ThumbnailSequenceDesc> getThumbnailSequenceDescArray() {
        return mDescArray;
    }


    /**
     *   \brief Sets the image fill mode of the thumbnail
     *   \param fillMode [image fill mode] (@ref THUMBNAIL_IMAGE_FILLMODE)
     *   \sa getThumbnailImageFillMode
     */
    public void setThumbnailImageFillMode(int fillMode) {
        NvsUtils.checkFunctionInMainThread();
        if ((mThumbnailImageFillMode != THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP) &&
                (mThumbnailImageFillMode != THUMBNAIL_IMAGE_FILLMODE_STRETCH)) {
            mThumbnailImageFillMode = THUMBNAIL_IMAGE_FILLMODE_STRETCH;
        }

        if (mThumbnailImageFillMode == fillMode) {
            return;
        }

        mThumbnailImageFillMode = fillMode;
        updateThumbnailSequenceGeometry();
    }

    /**
     *   \brief Sets thumbnail aspect ratio.
     *   \param thumbnailAspectRatio aspect ratio
     *   \sa getThumbnailAspectRatio
     */
    public void setThumbnailAspectRatio(float thumbnailAspectRatio) {
        NvsUtils.checkFunctionInMainThread();
        if (thumbnailAspectRatio < 0.1f) {
            thumbnailAspectRatio = 0.1f;
        } else if (thumbnailAspectRatio > 10) {
            thumbnailAspectRatio = 10;
        }

        if (Math.abs(mThumbnailAspectRatio - thumbnailAspectRatio) < 0.001f) {
            return;
        }

        mThumbnailAspectRatio = thumbnailAspectRatio;
        updateThumbnailSequenceGeometry();
    }

    /**
     *   \brief Sets the scale.
     *   \param pixelPerMicrosecond The number of pixels per subtle
     *   \sa getPixelPerMicrosecond
     */
    public void setPixelPerMicrosecond(double pixelPerMicrosecond) {
        NvsUtils.checkFunctionInMainThread();
        if ((pixelPerMicrosecond <= 0) || (pixelPerMicrosecond == mPixelPerMicrosecond)) {
            return;
        }

        mPixelPerMicrosecond = pixelPerMicrosecond;
        updateThumbnailSequenceGeometry();
    }

    /**
     *   \brief Gets the current scale.
     *   \return Returns the number of pixels per subtle.
     *   \sa setPixelPerMicrosecond
     */
    public double getPixelPerMicrosecond() {
        return mPixelPerMicrosecond;
    }

    /**
     *   \brief Sets the starting padding.
     *   \param startPadding Starting padding(in pixels)
     *   \sa getStartPadding
     */
    public void setStartPadding(int startPadding) {
        NvsUtils.checkFunctionInMainThread();
        if (startPadding < 0 || startPadding == mStartPadding) {
            return;
        }

        mStartPadding = startPadding;
        updateThumbnailSequenceGeometry();
    }

    /**
     *   \brief Gets the current starting padding.
     *   \return Returns the starting padding(in pixels).
     *   \sa setStartPadding
     */
    public int getStartPadding() {
        return mStartPadding;
    }

    /**
     *   \brief Sets end padding.
     *   \param endPadding Ends padding(in pixels)
     *   \sa getEndPadding
     */
    public void setEndPadding(int endPadding) {
        NvsUtils.checkFunctionInMainThread();
        if (endPadding < 0 || endPadding == mEndPadding) {
            return;
        }

        mEndPadding = endPadding;
        updateThumbnailSequenceGeometry();
    }

    /**
     *   \brief Gets the current ending padding.
     *   \return Returns the ending padding(in pixels)
     *   \sa setEndPadding
     */
    public int getEndPadding() {
        return mEndPadding;
    }

    /**
     *   \brief Maps the X coordinate of the control to the timeline position.
     *   \param x The X coordinate of the control(in pixels)
     *   \return Returns the timeline position of the map(in microseconds).
     *   \sa mapXFromTimelinePos
     */
    public long mapTimelinePosFromX(int x) {
        final int scrollX = getScrollX();
        x = x + scrollX - mStartPadding;
        final long timelinePos = (long) Math.floor(x / mPixelPerMicrosecond + 0.5);
        return timelinePos;
    }

    /**
     *   \brief Maps the timeline position to the X coordinate of the control.
     *   \param timelinePos Timeline position(in microseconds)
     *   \return Returns the X coordinate of the mapped control(in pixels).
     *   \sa mapTimelinePosFromX
     */
    public int mapXFromTimelinePos(long timelinePos) {
        int x = (int) Math.floor(timelinePos * mPixelPerMicrosecond + 0.5);
        final int scrollX = getScrollX();
        // there is a min duration for show, so we can't just timelinePos * m_pixelPerMicrosecond
        int sequenceCount = mThumbnailSequenceArray.size();
        for (int i = 0; i < sequenceCount; i++) {
            if (mThumbnailSequenceArray.get(i).mShowDuration < mMinSequenceDuration) {
                x += (mMinSequenceDuration - mThumbnailSequenceArray.get(i).mShowDuration);
            }
        }

        return x + mStartPadding - scrollX;
    }

    /**
     *   \brief Sets the scroll listener interface.
     *   \param listener Rolling monitor interface
     *   \sa getOnScrollChangeListenser
     */
    public void setOnScrollChangeListenser(OnScrollChangeListener listener) {
        NvsUtils.checkFunctionInMainThread();
        mScrollChangeListener = listener;
    }

    /**
     *   \brief Sets whether to start scroll preview.
     *   \param enable Whether to start scroll preview.
     *   \sa getScrollEnabled
     */
    public void setScrollEnabled(boolean enable) {
        mScrollEnabled = enable;
    }

    public void updatePortionThumbnails(ArrayList<ThumbnailSequenceDesc> sequenceDesc) {
        for (Map.Entry<ThumbnailId, Thumbnail> entry : mThumbnailMap.entrySet()) {
            mContentView.removeView(entry.getValue().mImageView);
        }

        mThumbnailSequenceArray.clear();
        mThumbnailSequenceMap.clear();
        mContentWidth = 0;

        mDescArray = sequenceDesc;
        if (sequenceDesc != null) {
            int index = 0;
            long lastOutPoint = 0;
            for (ThumbnailSequenceDesc desc : sequenceDesc) {
                if (desc.mediaFilePath == null ||
                        desc.inPoint < lastOutPoint || desc.outPoint <= desc.inPoint ||
                        desc.trimIn < 0 || desc.trimOut <= desc.trimIn) {
                    GLog.e(TAG, "Invalid ThumbnailSequenceDesc!");
                    continue;
                }

                ThumbnailSequence thumbnailSequence = new ThumbnailSequence();
                thumbnailSequence.mIndex = index++;
                thumbnailSequence.mMediaFilePath = desc.mediaFilePath;
                thumbnailSequence.mInPoint = desc.inPoint;
                thumbnailSequence.mOutPoint = desc.outPoint;
                thumbnailSequence.mTrimIn = desc.trimIn;
                thumbnailSequence.mTrimDuration = desc.trimOut - desc.trimIn;
                thumbnailSequence.mShowDuration = desc.outPoint - desc.inPoint;
                thumbnailSequence.mStillImageHint = desc.stillImageHint;
                thumbnailSequence.mOnlyDecodeKeyFrame = desc.onlyDecodeKeyFrame;
                thumbnailSequence.mThumbnailAspectRatio = desc.thumbnailAspectRatio;

                mThumbnailSequenceArray.add(thumbnailSequence);

                lastOutPoint = desc.outPoint;
            }
        }

        updateThumbnailSequenceGeometryExt(false);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

        if (!isInEditMode()) {
            mIconGenerator = new NvsIconGenerator();
            mIconGenerator.setIconCallback(new NvsIconGenerator.IconCallback() {
                @Override
                public void onIconReady(Bitmap bitmap, long l, long l1) {
                    if (!mUpdatingThumbnail) {
                        updateThumbnails(false);
                    } else {
                        new Handler().post(new Runnable() {
                            @Override
                            public void run() {
                                updateThumbnails(false);
                            }
                        });
                    }
                }
            });
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelIconTask();

        mScrollChangeListener = null;

        if (mIconGenerator != null) {
            mIconGenerator.release();
            mIconGenerator = null;
        }

        super.onDetachedFromWindow();
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (mScrollChangeListener != null) {
            mScrollChangeListener.onScrollChanged(this, l, oldl);
        }
        cancelIconTask();
        updateThumbnails(true);
    }

    /*! \cond */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (mScrollEnabled) {
            return super.onInterceptTouchEvent(ev);
        } else {
            return false;
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (mScrollEnabled) {
            return super.onTouchEvent(ev);
        } else {
            return false;
        }
    }

    private void init(Context context) {
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);

        // Create the internal content view
        mContentView = new ContentView(context);
        addView(mContentView, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT));
    }

    private void requestUpdateThumbnailSequenceGeometry() {
        new Handler().post(new Runnable() {
            @Override
            public void run() {
                updateThumbnailSequenceGeometry();
            }
        });
    }

    private void updateThumbnailSequenceGeometry() {
        updateThumbnailSequenceGeometryExt(true);
    }

    private void updateThumbnailSequenceGeometryExt(boolean needClear) {
        if (needClear) {
            cancelIconTask();
            // Clear thumbnails since their geometry is subject to change
            clearThumbnails();
        }

        // Calculate thumbnail width in pixel
        final int h = getHeight() - getPaddingBottom() - getPaddingTop();
        if (h <= 0) {
            return;
        }

        mThumbnailSequenceMap.clear();

        int lastX = mStartPadding;
        mMaxThumbnailWidth = 0;
        for (ThumbnailSequence thumbnailSequence : mThumbnailSequenceArray) {
            // Mark cached keyframe only mode as invalid
            thumbnailSequence.mFlags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;

            int x = lastX;
            long duration = thumbnailSequence.mShowDuration;
            if (duration < mMinSequenceDuration) {
                duration = mMinSequenceDuration;
            }
            int x2 = x + (int) Math.floor(duration * mPixelPerMicrosecond + 0.5);

            if (x2 <= x) {
                // For current scale ratio, this thumbnail sequence can't be represented, just ignore it
                continue;
            }

            thumbnailSequence.mX = x;
            thumbnailSequence.mWidth = x2 - x;

            // Calculate thumbnail width in pixel
            final float thumbnailAspectRatio = thumbnailSequence.mThumbnailAspectRatio > 0 ?
                    thumbnailSequence.mThumbnailAspectRatio : mThumbnailAspectRatio;
            thumbnailSequence.mThumbnailWidth = (int) Math.floor(h * thumbnailAspectRatio + 0.5);
            thumbnailSequence.mThumbnailWidth = Math.max(thumbnailSequence.mThumbnailWidth, 1);
            mMaxThumbnailWidth = (int) Math.max(thumbnailSequence.mThumbnailWidth, mMaxThumbnailWidth);

            mThumbnailSequenceMap.put((int) x, thumbnailSequence);

            lastX = x2;
        }

        // Update desired content (view) width
        int contentWidth = (int) lastX;
        if (mMaxTimelinePosToScroll <= 0) {
            contentWidth += mEndPadding;
        } else {
            int len = (int) Math.floor(mStartPadding + mMaxTimelinePosToScroll * mPixelPerMicrosecond + 0.5f);
            if (len < contentWidth) {
                contentWidth = len;
            }
        }
        mContentWidth = contentWidth;
        mContentView.layout(0, 0, mContentWidth, getHeight() - getPaddingTop() - getPaddingBottom());
        mContentView.requestLayout(); // updateThumbnails() will be called during layout

        if (getWidth() + getScrollX() > mContentWidth) {
            final int newScrollX = Math.max(getScrollX() - (getWidth() + getScrollX() - mContentWidth), 0);
            if (newScrollX != getScrollX()) {
                scrollTo(newScrollX, 0);
            }
        }
        if (mOnSequenceLoadDataListener != null) {
            mOnSequenceLoadDataListener.sequenceLoadFinish();
        }
    }

    public interface OnSequenceLoadDataListener {
        void sequenceLoadFinish();
    }

    public void setOnScrollLoaderData(OnSequenceLoadDataListener mOnSequenceLoadDataListener) {
        this.mOnSequenceLoadDataListener = mOnSequenceLoadDataListener;
    }

    private static class ClipImageView extends AppCompatImageView {
        private float mClipWidth;
        private float mLeft = 0;
        private RectF mRectF = new RectF();

        ClipImageView(Context ctx, float left, float clipWidth) {
            super(ctx);
            mLeft = left;
            mClipWidth = clipWidth;
        }

        public void setClipWidth(float clipWidth) {
            mClipWidth = clipWidth;
        }

        public void setClipLeft(float left) {
            mLeft = left;
        }

        @Override
        protected void onDraw(Canvas canvas) {
            mRectF.left = 0;
            mRectF.top = 0;
            mRectF.right = mClipWidth - mLeft;
            mRectF.bottom = getHeight();
            canvas.clipRect(mRectF);
            canvas.translate(-mLeft, 0);
            super.onDraw(canvas);
        }
    }

    private void updateThumbnails(boolean getNewIcon) {
        if (mIconGenerator == null) {
            return;
        }

        if (mThumbnailSequenceMap.isEmpty()) {
            clearThumbnails();
            return;
        }

        final int guardLength = mMaxThumbnailWidth;
        final int scrollX = getScrollX();
        final int width = getWidth();
        final int visibleLeftBound = Math.max(scrollX - guardLength, mStartPadding);
        final int visibleRightBound = visibleLeftBound + width + guardLength;
        if (visibleRightBound <= visibleLeftBound) {
            clearThumbnails();
            return;
        }

        Integer startKey = mThumbnailSequenceMap.floorKey(visibleLeftBound);
        if (startKey == null) {
            startKey = mThumbnailSequenceMap.firstKey();
        }

        TreeMap<ThumbnailId, Thumbnail> updateThumbnailMap = new TreeMap<ThumbnailId, Thumbnail>();
        SortedMap<Integer, ThumbnailSequence> sortedMap = mThumbnailSequenceMap.tailMap(startKey);
        for (Map.Entry<Integer, ThumbnailSequence> entry : sortedMap.entrySet()) {
            ThumbnailSequence seq = entry.getValue();
            if (seq.mX + seq.mWidth < visibleLeftBound) {
                continue;
            }
            if (seq.mX >= visibleRightBound) {
                break;
            }

            boolean outOfBound = false;
            final int seqEndX = seq.mX + seq.mWidth;
            final long timeStep = Math.max((long) ((double) seq.mThumbnailWidth / seq.mWidth * seq.mTrimDuration + 0.5), 1);

            float thumbnailX = seq.mX;
            while (thumbnailX < seqEndX) {
                if (thumbnailX >= visibleRightBound) {
                    outOfBound = true;
                    break;
                }

                // Calculate timestamp of this thumbnail
                final long timestamp = seq.calcTimestampFromXWithThumbnailDuration(thumbnailX, timeStep);

                // Find the thumbnail from the current thumbnail map first
                ThumbnailId tid = new ThumbnailId(seq.mIndex, timestamp);
                Thumbnail thumbnail = mThumbnailMap.get(tid);
                if (thumbnail != null && !seq.mMediaFilePath.equals(thumbnail.mOwner.mMediaFilePath)) {
                    mThumbnailMap.remove(tid);
                    thumbnail = null;
                }

                float thumbnailWidth = seq.mThumbnailWidth;
                int left = 0;
                if (timestamp < seq.mTrimIn) {
                    left = (int) Math.floor((seq.mTrimIn - timestamp) * 1.f / timeStep * thumbnailWidth);
                }
                thumbnailWidth -= left;
                if (thumbnailX + thumbnailWidth > seqEndX) {
                    thumbnailWidth = seqEndX - thumbnailX;
                }

                if (thumbnail == null) {
                    // Create a new thumbnail
                    thumbnail = new Thumbnail();
                    thumbnail.mOwner = seq;
                    thumbnail.mTimestamp = timestamp;
                    thumbnail.mImageViewUpToDate = false;
                    thumbnail.mTouched = true;

                    mThumbnailMap.put(tid, thumbnail);
                    updateThumbnailMap.put(tid, thumbnail);

                    thumbnail.mImageView = new ClipImageView(this.getContext(), left, thumbnailWidth + left);

                    if (mThumbnailImageFillMode == THUMBNAIL_IMAGE_FILLMODE_STRETCH) {
                        thumbnail.mImageView.setScaleType(ImageView.ScaleType.FIT_XY);
                    } else if (mThumbnailImageFillMode == THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP) {
                        thumbnail.mImageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                    }

                    mContentView.addView(thumbnail.mImageView);
                    thumbnail.mImageView.layout((int) (thumbnailX), 0, (int) (thumbnailX + seq.mThumbnailWidth), mContentView.getHeight());
                } else {
                    if (thumbnail.mImageView.getParent() == null) {
                        ((ClipImageView) thumbnail.mImageView).setClipLeft(left);
                        ((ClipImageView) thumbnail.mImageView).setClipWidth(thumbnailWidth + left);
                        mContentView.addView(thumbnail.mImageView);
                        thumbnail.mImageView.layout((int) (thumbnailX), 0, (int) (thumbnailX + seq.mThumbnailWidth), mContentView.getHeight());
                    }

                    updateThumbnailMap.put(tid, thumbnail);
                    thumbnail.mTouched = true;
                }

                thumbnailX += thumbnailWidth;
            }

            if (outOfBound) {
                break;
            }
        }

        mUpdatingThumbnail = true;

        boolean hasDirtyThumbnail = false;
        TreeMap<ThumbnailId, Bitmap> iconMap = new TreeMap<ThumbnailId, Bitmap>();
        Set<Map.Entry<ThumbnailId, Thumbnail>> thumbnailSet = updateThumbnailMap.entrySet();
        Iterator<Map.Entry<ThumbnailId, Thumbnail>> itrThumbnail = thumbnailSet.iterator();
        while (itrThumbnail.hasNext()) {
            Map.Entry<ThumbnailId, Thumbnail> entry = itrThumbnail.next();
            Thumbnail thumbnail = entry.getValue();

            // Update placeholder bitmap
            if (thumbnail.mImageView != null) {
                Drawable drawable = thumbnail.mImageView.getDrawable();
                if (drawable != null) {
                    Bitmap bitmap = ((BitmapDrawable) drawable).getBitmap();
                    if (bitmap != null) {
                        mPlaceholderbitmap = bitmap;
                    }
                }
            }

            if (!thumbnail.mTouched) {
                // These thumbnail hasn't been touched, remove it
                if (thumbnail.mIconTaskId != 0) {
                    mIconGenerator.cancelTask(thumbnail.mIconTaskId);
                }

                if (thumbnail.mImageView != null) {
                    mContentView.removeView(thumbnail.mImageView);
                }
                itrThumbnail.remove();
                continue;
            }

            // Reset touched flag for later use
            thumbnail.mTouched = false;

            if (thumbnail.mImageViewUpToDate) {
                ImageView imageView = thumbnail.mImageView;
                if ((imageView != null) && (imageView.getDrawable() != null)) {
                    Bitmap bitmap = ((BitmapDrawable) imageView.getDrawable()).getBitmap();
                    iconMap.put(entry.getKey(), bitmap);
                }
            } else {
                final long realTimestamp = thumbnail.mOwner.mStillImageHint ? 0 : thumbnail.mTimestamp;
                updateKeyframeOnlyModeForThumbnailSequence(thumbnail.mOwner);
                final int flags = (thumbnail.mOwner.mFlags & THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY) != 0 ? 1 : 0;
                Bitmap bitmap = mIconGenerator.getIconFromCache(thumbnail.mOwner.mMediaFilePath, realTimestamp, flags);
                if (bitmap != null) {
                    iconMap.put(entry.getKey(), bitmap);
                    if (setBitmapToThumbnail(bitmap, thumbnail)) {
                        thumbnail.mImageViewUpToDate = true;
                        thumbnail.mIconTaskId = 0;
                    }
                } else {
                    if (getNewIcon) {
                        hasDirtyThumbnail = true;
                        thumbnail.mIconTaskId = mIconGenerator.getIcon(thumbnail.mOwner.mMediaFilePath, realTimestamp, flags);
                    }
                }
            }
        }

        mUpdatingThumbnail = false;

        if (!hasDirtyThumbnail) {
            return;
        }

        if (iconMap.isEmpty()) {
            // Now we set placeholder image to thumbnail whose ImageView was not up to date yet
            if (mPlaceholderbitmap != null) {
                for (Map.Entry<ThumbnailId, Thumbnail> entry : updateThumbnailMap.entrySet()) {
                    Thumbnail thumbnail = entry.getValue();
                    if (!thumbnail.mImageViewUpToDate) {
                        setBitmapToThumbnail(mPlaceholderbitmap, thumbnail);
                    }
                }
            }

            return;
        }

        // Now we set image to thumbnail whose ImageView was not up to date yet
        for (Map.Entry<ThumbnailId, Thumbnail> entry : updateThumbnailMap.entrySet()) {
            Thumbnail thumbnail = entry.getValue();
            if (thumbnail.mImageViewUpToDate) {
                continue;
            }

            /* We fail to find an image with the given timestamp value,
               To make thumbnail sequence looks better we use an image whose
             timestamp is close to the given timestamp
             */
            Map.Entry<ThumbnailId, Bitmap> ceilingEntry = iconMap.ceilingEntry(entry.getKey());

            if (ceilingEntry != null) {
                setBitmapToThumbnail(ceilingEntry.getValue(), thumbnail);
            } else {
                setBitmapToThumbnail(iconMap.lastEntry().getValue(), thumbnail);
            }
        }
    }

    private void updateKeyframeOnlyModeForThumbnailSequence(ThumbnailSequence seq) {
        if ((seq.mFlags & THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID) != 0) {
            return;
        }

        if (seq.mOnlyDecodeKeyFrame) {
            // We always respect the user's option
            seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY |
                    THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
            return;
        }

        final long timeStep = Math.max((long) (seq.mThumbnailWidth / mPixelPerMicrosecond + 0.5), 1);
        final boolean keyFrameOnly = shouldDecodecKeyFrameOnly(seq.mMediaFilePath, timeStep);
        if (keyFrameOnly) {
            seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY;
        } else {
            seq.mFlags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY;
        }
        seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
    }

    private boolean shouldDecodecKeyFrameOnly(String filePath, long timeStep) {
        NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
        if (streamingContext == null) {
            return false;
        }

        NvsAVFileInfo fileInfo = streamingContext.getAVFileInfo(filePath);
        if (fileInfo == null) {
            return false;
        }

        if ((fileInfo.getVideoStreamCount() < 1)
                || (fileInfo.getAVFileType() == NvsAVFileInfo.AV_FILE_TYPE_IMAGE)) {
            return false;
        }

        NvsRational fps = fileInfo.getVideoStreamFrameRate(0);
        if (fps == null) {
            return false;
        }

        if (fps.den <= 0 || fps.num <= 0) {
            return false;
        }

        long videoDuration = fileInfo.getVideoStreamDuration(0);
        if (videoDuration < timeStep) {
            return false;
        }

        int keyframeInterval = streamingContext.detectVideoFileKeyframeInterval(filePath);
        if (keyframeInterval == 0) {
            keyframeInterval = 30; // We can't detect its GOP size, just guess it
        } else if (keyframeInterval == 1) {
            return false; // Keyframe only, no need to use keyframe only mode
        }

        int keyframeIntervalTime = (int) (keyframeInterval * ((double) fps.den / fps.num) * 1000000);
        if (keyframeInterval <= 30) {
            if (timeStep > keyframeIntervalTime * 0.9) {
                return true;
            }
        } else if (keyframeInterval <= 60) {
            if (timeStep > keyframeIntervalTime * 0.8) {
                return true;
            }
        } else if (keyframeInterval <= 100) {
            if (timeStep > keyframeIntervalTime * 0.7) {
                return true;
            }
        } else if (keyframeInterval <= 150) {
            if (timeStep > keyframeIntervalTime * 0.5) {
                return true;
            }
        } else if (keyframeInterval <= 250) {
            if (timeStep > keyframeIntervalTime * 0.3) {
                return true;
            }
        } else {
            if (timeStep > keyframeIntervalTime * 0.2) {
                return true;
            }
        }

        return false;
    }

    private boolean setBitmapToThumbnail(Bitmap bitmap, Thumbnail thumbnail) {
        if (bitmap == null || thumbnail.mImageView == null) {
            return false;
        }

        thumbnail.mImageView.setImageBitmap(bitmap);
        return true;
    }

    private void clearThumbnailSequences() {
        cancelIconTask();
        clearThumbnails();

        mThumbnailSequenceArray.clear();
        mThumbnailSequenceMap.clear();
        mContentWidth = 0;
    }

    private void clearThumbnails() {
        for (Map.Entry<ThumbnailId, Thumbnail> entry : mThumbnailMap.entrySet()) {
            mContentView.removeView(entry.getValue().mImageView);
        }

        mThumbnailMap.clear();
    }

    private void cancelIconTask() {
        if (mIconGenerator != null) {
            mIconGenerator.cancelTask(0);
        }
    }

    @Override
    protected void onOverScrolled(int scrollX, int scrollY, boolean clampedX, boolean clampedY) {
        if (mScrollLimited && (scrollX >= mMaxScrolledPos)) {
            super.onOverScrolled(mMaxScrolledPos, scrollY, clampedX, clampedY);
            return;
        }
        super.onOverScrolled(scrollX, scrollY, clampedX, clampedY);
    }
}

