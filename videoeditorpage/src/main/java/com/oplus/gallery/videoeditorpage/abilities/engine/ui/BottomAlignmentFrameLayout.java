/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BottomAlignmentFrameLayout.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.engine.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

public class BottomAlignmentFrameLayout extends FrameLayout {

    private boolean mIsMenuClickable = true;

    public BottomAlignmentFrameLayout(Context context) {
        this(context, null);
    }

    public BottomAlignmentFrameLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int paddingLeft = getPaddingLeft();
        int paddingRight = getPaddingRight();
        int paddingBottom = getPaddingBottom();
        int childCount = getChildCount();
        for (int i = 0; i < childCount; i++) {
            final View child = getChildAt(i);
            final int childWidth = child.getMeasuredWidth();
            final int childHeight = child.getMeasuredHeight();
            final int childLeft = (getMeasuredWidth() - paddingLeft - paddingRight - childWidth) / 2;
            int childBottom = bottom - top - paddingBottom;
            MarginLayoutParams lp = (MarginLayoutParams) child.getLayoutParams();
            childBottom -= lp.bottomMargin;
            int childTop = childBottom - childHeight;
            child.layout(childLeft, childTop, childLeft + childWidth, childBottom);
        }
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        setChildrenEnable(this, enabled);
    }

    public void setMenuClickable(boolean clickable) {
        mIsMenuClickable = clickable;
    }

    private void setChildrenEnable(View view, boolean enable) {
        if (view instanceof ViewGroup) {
            int childCount = ((ViewGroup) view).getChildCount();
            for (int i = 0; i < childCount; i++) {
                final View child = ((ViewGroup) view).getChildAt(i);
                child.setEnabled(enable);
                if (child instanceof ViewGroup) {
                    setChildrenEnable(child, enable);
                }
            }
        } else {
            view.setEnabled(enable);
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        if (!mIsMenuClickable) {
            return true;
        } else {
            return super.onInterceptTouchEvent(motionEvent);
        }
    }
}
