/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  SpeederCurveInfo
 ** Description: 曲线变速配置信息类
 ** Version: 1.0
 ** Date : 2025/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/5/12      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.graphics.drawable.Drawable

/**
 * 曲线变速配置信息类
 */
data class SpeederCurveInfo(
    //配置信息名字
    val name: String,
    //变速原件
    val speedCurveRawData: String,
    //图片路径
    val imagePath: String,
    //变速类型
    val type: Int,
    //图片drawable
    val imageDrawable: Drawable,
    //是否选中状态
    var isSelected: Boolean = false
)