/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorPipEditRectView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.Region;
import android.util.AttributeSet;
import android.util.Size;
import android.view.MotionEvent;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.utlis.ScreenUtils;
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.data.BaseKeyFrameInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.utlis.KeyFrameUtil;
import com.oplus.gallery.videoeditorpage.utlis.PipUtil;
import com.oplus.gallery.videoeditorpage.widget.base.IntercepableBaseView;

import java.util.ArrayList;
import java.util.List;

/**
 * 预览拖拽缩放旋转框
 */
public class EditorPipEditRectView extends IntercepableBaseView {
    /**
     * 是否开启拖拽预览功能，暂时关闭
     */
    private final static boolean ENABLE_PIP_EDIT_RECT_VIEW = false;

    private final static String TAG = "EditorPipEditRectView";
    private static final float SCALE_UNIT = 1F;
    private static final float PIP_ROTATION_ADSORB_RANGE = 0.5F;
    private static final float PIP_ROTATION_IGNORE_RANGE = 3F;
    private static final float PIP_SCALE_GAP = 0.01F;
    private final static double HANDMOVE_DISTANCE = 10.0;
    private final static long LONG_PRESS_EVENT_TIME_INTERVAL = 500;
    private final static long POST_DELAY_ACTION_UP_ENVENT_TIME = 10;
    private final static long POST_DELAY_ACTION_MOVE_ENVENT_TIME = 8;
    private final static float RECT_WIDTH_DP_VALUE = 1.3f;
    private final static float RECT_REPAIR_VALUE = 1.7f;
    private final static float MIN_ROTATE_DEGREE = 0.1f;
    private final static float PIP_MAX_RATIO_DEVIATION = 0.025F;
    private final static float PIP_MIM_SCALE = 0.1F;
    private final static float PIP_MAX_SCALE = 10F;
    private final static float PIP_DEFAULT_SCALE = 0.8F;
    private final static float PIP_ROTATION_MAX = 360F;
    private final static float PIP_CENTER_ADSORB_RANGE = 2F;
    private final static float PIP_IGNORE_ADSORB_RANGE = 15F;
    private final static float FLOAT_DEFAULT_VALUE = 0F;

    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;

    private final static int POINT_LIST_INDEX_SIZE = 4;
    private final static int LEFT_TOP_INDEX = 0;
    private final static int LEFT_BOTTOM_INDEX = 1;
    private final static int RIGHT_BOTTOM_INDEX = 2;
    private final static int RIGHT_TOP_INDEX = 3;

    private final static int DOWN_IN_RECT = 0x310;
    private final static int DOWN_OUT_RECT = 0x320;

    private final static float SCALE_DEFAULT = 1.0f;

    private Paint mRectPaint;
    private float mOneFingerTargetX;
    private float mOneFingerTargetY;
    private double mClickMoveDistance = 0.0D;
    private double mTwoFingerStartLength;
    private double mTwoFingerEndLength;
    private int mDownState = 0;
    private PointF mDownPointF = new PointF(0, 0);
    private PointF mMovePointF = new PointF(0, 0);
    private PointF mTwoFingerOldPoint = new PointF();
    private List<PointF> mListPointF;

    private Path mRectPath = new Path();

    private OnEditTouchEventListener mOnTouchEventListener;
    private int mLiveWindowWidth = 0;
    private int mLiveWindowHeight = 0;
    private float mStartX = 0;
    private float mStartY = 0;
    private float mPixTimeLineRatio = 1F;
    private float mCumulativeRotation = 0F;
    private float mLastPointFxWhenAdsorb = 0F;
    private float mLastPointFyWhenAdsorb = 0F;
    private PointF mTransPointF;
    private boolean mIsTwoFingerEvent = false;
    private boolean mIsTouchable = true;
    private int mDisplayRatio = -1;
    private int mStrokeWidth;
    private int mStrokeColor;
    private IVideoClip mSelectVideoClip;
    private IVideoClip mOperationVideoClip;
    private long mDownTime;
    private EditorEngine mEditorEngine;

    public EditorPipEditRectView(Context context) {
        this(context, null);
    }

    public EditorPipEditRectView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EditorPipEditRectView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context, attrs, defStyle);
    }

    public void setEngine(EditorEngine engine) {
        mEditorEngine = engine;
    }

    private void init(Context context, AttributeSet attrs, int defStyle) {
        mRectPaint = new Paint();
        mRectPaint.setAntiAlias(true);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.EditorPipEditRectView, defStyle, 0);


        try {
            mStrokeWidth = a.getDimensionPixelOffset(R.styleable.EditorPipEditRectView_strokeWidth,
                    ScreenUtils.dip2px(context, RECT_WIDTH_DP_VALUE));
            mStrokeColor = a.getColor(R.styleable.EditorPipEditRectView_strokeColor,
                    getContext().getColor(R.color.engine_editor_theme_yellow));
        } finally {
            a.recycle();
        }

        mRectPaint.setColor(mStrokeColor);
        mRectPaint.setStrokeWidth(ScreenUtils.dip2px(context, RECT_WIDTH_DP_VALUE));
        mRectPaint.setStyle(Paint.Style.STROKE);
    }

    @Override
    public void setVisibility(int visibility) {
        if (ENABLE_PIP_EDIT_RECT_VIEW) {
            super.setVisibility(visibility);
        } else {
            super.setVisibility(GONE);
        }
    }

    public void removePipRectView() {
        this.mSelectVideoClip = null;
        this.mListPointF = null;
        this.mTransPointF = null;
        invalidate();
    }

    public void setTouchable(boolean isTouchable) {
        this.mIsTouchable = isTouchable;
    }

    public void setOnEditTouchListener(OnEditTouchEventListener listener) {
        mOnTouchEventListener = listener;
    }

    public void setLiveWindowWidthAndHeight(int liveWindowWidth, int liveWindowHeight) {
        mLiveWindowWidth = liveWindowWidth;
        mLiveWindowHeight = liveWindowHeight;
        mListPointF = null;
        invalidate();
    }

    public int getLiveWindowHeight() {
        return mLiveWindowHeight;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mStartX = (getMeasuredWidth() - mLiveWindowWidth + 1) / 2;
        mStartY = (getMeasuredHeight() - mLiveWindowHeight + 1) / 2;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if ((mListPointF == null) || (mListPointF.size() < POINT_LIST_INDEX_SIZE)) {
            return;
        }

        mRectPath.reset();
        mRectPath.moveTo(mListPointF.get(LEFT_TOP_INDEX).x + RECT_REPAIR_VALUE, mListPointF.get(LEFT_TOP_INDEX).y + RECT_REPAIR_VALUE);
        mRectPath.lineTo(mListPointF.get(LEFT_BOTTOM_INDEX).x + RECT_REPAIR_VALUE, mListPointF.get(LEFT_BOTTOM_INDEX).y - RECT_REPAIR_VALUE);
        mRectPath.lineTo(mListPointF.get(RIGHT_BOTTOM_INDEX).x - RECT_REPAIR_VALUE, mListPointF.get(RIGHT_BOTTOM_INDEX).y - RECT_REPAIR_VALUE);
        mRectPath.lineTo(mListPointF.get(RIGHT_TOP_INDEX).x - RECT_REPAIR_VALUE, mListPointF.get(RIGHT_TOP_INDEX).y + RECT_REPAIR_VALUE);
        float tempOffetX = mStartX;
        float tempOffetY = mStartY;
        if (mTransPointF != null) {
            tempOffetX += mTransPointF.x;
            tempOffetY += mTransPointF.y;
        }
        mRectPath.offset(tempOffetX, tempOffetY);
        mRectPath.close();

        canvas.drawPath(mRectPath, mRectPaint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (onTouchEventImpl(event)) {
            return false;
        }
        if ((!mIsTouchable) || (mOnTouchEventListener == null)) {
            GLog.d(TAG, "onTouchEvent: mIsTouchable:" + mIsTouchable);
            return false;
        }

        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }

        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }

        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            twoFingerTouch(event);
        } else {
            oneFingerTouch(event);
        }
        return true;
    }

    private void twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            float oldDegree = (float) Math.toDegrees(Math.atan2(mTwoFingerOldPoint.x, mTwoFingerOldPoint.y));
            float newDegree = (float) Math.toDegrees(Math.atan2((event.getX(0) - event.getX(1)), (event.getY(0) - event.getY(1))));
            mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);

            float scalePercent = (float) (mTwoFingerEndLength / mTwoFingerStartLength);
            float degree = newDegree - oldDegree;
            GLog.d(TAG, "twoFingerTouch, newDegree:" + newDegree + ", oldDegree:" + oldDegree);

            if (Math.abs(degree) < MIN_ROTATE_DEGREE) {
                degree = 0;
            }

            calculatePipPointFList(scalePercent, degree);
            mTwoFingerStartLength = mTwoFingerEndLength;
            mTwoFingerOldPoint.set(xLen, yLen);
        }
    }

    private void oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            handleTwoFingerEventUp(event);
            return;
        }

        handleOneFingerEvent(event);
    }

    private void handleTwoFingerEventUp(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_UP) {
            mIsTwoFingerEvent = false;
            mOnTouchEventListener.onTouchUp(new PointF(event.getX() - mStartX, event.getY() - mStartY), mOperationVideoClip);
            mOperationVideoClip = null;
        }
    }

    private void handleOneFingerEvent(MotionEvent event) {
        float lastOneFingerTargetX = mOneFingerTargetX;
        float lastOneFingerTargetY = mOneFingerTargetY;
        mOneFingerTargetX = event.getX() - mStartX;
        mOneFingerTargetY = event.getY() - mStartY;

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                handleActionDown();
                break;
            }
            case MotionEvent.ACTION_UP: {
                handleActionUp();
                break;
            }
            case MotionEvent.ACTION_MOVE:
                handleActionMove(lastOneFingerTargetX, lastOneFingerTargetY);
                break;
            default:
                break;
        }
    }

    private void handleActionDown() {
        mDownTime = System.currentTimeMillis();
        mCumulativeRotation = FLOAT_DEFAULT_VALUE;
        mLastPointFxWhenAdsorb = FLOAT_DEFAULT_VALUE;
        mLastPointFyWhenAdsorb = FLOAT_DEFAULT_VALUE;
        mDownState = checkRectOnDown(mOneFingerTargetX, mOneFingerTargetY);
        mDownPointF.set(mOneFingerTargetX, mOneFingerTargetY);
        mOnTouchEventListener.onTouchDown(new PointF(mOneFingerTargetX, mOneFingerTargetY));
    }

    private void handleActionUp() {
        int upState = checkRectOnDown(mOneFingerTargetX, mOneFingerTargetY);
        PointF actionUpPoint = new PointF(mOneFingerTargetX, mOneFingerTargetY);
        if ((mClickMoveDistance < HANDMOVE_DISTANCE)
                && (mDownState == upState)
                && (System.currentTimeMillis() - mDownTime
                < LONG_PRESS_EVENT_TIME_INTERVAL)) {
            if (mDownState == DOWN_IN_RECT) {
                postDelayed(() -> mOnTouchEventListener.onPipRectClick(),
                        POST_DELAY_ACTION_UP_ENVENT_TIME);
            } else if (mDownState == DOWN_OUT_RECT) {
                postDelayed(() -> mOnTouchEventListener.onOutOfPipRectClick(actionUpPoint),
                        POST_DELAY_ACTION_UP_ENVENT_TIME);
            }
        }

        postDelayed(() -> {
            mOnTouchEventListener.onTouchUp(actionUpPoint, mOperationVideoClip);
            mOperationVideoClip = null;
        }, POST_DELAY_ACTION_UP_ENVENT_TIME);
        mClickMoveDistance = 0.0D;
    }

    private void handleActionMove(float lastOneFingerTargetX, float lastOneFingerTargetY) {
        if (!canTranslateX(mDownPointF.x, (mDownPointF.x + mOneFingerTargetX) / 2)) {
            mOneFingerTargetX = lastOneFingerTargetX;
        }

        if (!canTranslateY(mDownPointF.y, (mDownPointF.y + mOneFingerTargetY) / 2)) {
            mOneFingerTargetY = lastOneFingerTargetY;
        }

        if ((Float.compare(mOneFingerTargetX, lastOneFingerTargetX) == 0)
                && (Float.compare(mOneFingerTargetY, lastOneFingerTargetY) == 0)) {
            return;
        }

        mClickMoveDistance = mClickMoveDistance + Math.sqrt(Math.pow(mOneFingerTargetX - mDownPointF.x, 2)
                + Math.pow(mOneFingerTargetY - mDownPointF.y, 2));
        mMovePointF.set((mDownPointF.x + mOneFingerTargetX) / 2, (mDownPointF.y + mOneFingerTargetY) / 2);
        calculateTranslatePointF(mDownPointF, mMovePointF);
        mDownPointF.set(mMovePointF.x, mMovePointF.y);
        postDelayed(new Runnable() {
            @Override
            public void run() {
                mMovePointF.set(mOneFingerTargetX, mOneFingerTargetY);
                calculateTranslatePointF(mDownPointF, mMovePointF);
                mDownPointF.set(mOneFingerTargetX, mOneFingerTargetY);
            }
        }, POST_DELAY_ACTION_MOVE_ENVENT_TIME);
    }

    private int checkRectOnDown(float targetX, float targetY) {
        if ((mListPointF == null) || (mListPointF.size() < POINT_LIST_INDEX_SIZE)) {
            return DOWN_OUT_RECT;
        }
        RectF r = new RectF();
        Path path = new Path();
        path.moveTo(mListPointF.get(LEFT_TOP_INDEX).x, mListPointF.get(LEFT_TOP_INDEX).y);
        path.lineTo(mListPointF.get(LEFT_BOTTOM_INDEX).x, mListPointF.get(LEFT_BOTTOM_INDEX).y);
        path.lineTo(mListPointF.get(RIGHT_BOTTOM_INDEX).x, mListPointF.get(RIGHT_BOTTOM_INDEX).y);
        path.lineTo(mListPointF.get(RIGHT_TOP_INDEX).x, mListPointF.get(RIGHT_TOP_INDEX).y);
        if (mTransPointF != null) {
            path.offset(mTransPointF.x, mTransPointF.y);
        }
        path.close();
        path.computeBounds(r, true);
        Region region = new Region();
        region.setPath(path, new Region((int) r.left, (int) r.top, (int) r.right, (int) r.bottom));
        if (region.contains((int) targetX, (int) targetY)) {
            return DOWN_IN_RECT;
        }
        return DOWN_OUT_RECT;
    }

    private void hideRectView() {
        mListPointF = null;
        invalidate();
    }

    public void drawClipRectView(IVideoClip videoClip) {
        mSelectVideoClip = videoClip;
        if (mSelectVideoClip == null) {
            GLog.e(TAG, "drawClipRectView videoClip is null");
            hideRectView();
            return;
        }
        PointF adsorbPointF = mSelectVideoClip.getPipAdsorbPointF();
        BaseVideoClipEffect transformVideoClipEffect = getTransformEffect(mSelectVideoClip);
        if ((adsorbPointF == null) || (transformVideoClipEffect == null)) {
            GLog.e(TAG, "getCurrentPipPointFList transform effect or adsorbPointF is null");
            hideRectView();
            return;
        }

        float hasRotateDegree = 0;
        float scale = 0;
        float hasTranX = 0;
        float hasTranY = 0;
        if (transformVideoClipEffect.hasKeyframe()) {
            long curTime = 0;
            if (mEditorEngine != null) {
                curTime = mEditorEngine.getTimelineCurrentPosition() - videoClip.getInPoint();
            }
            hasRotateDegree = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_ROTATION, curTime);
            scale = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_X, curTime);
            hasTranX = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_X, curTime);
            hasTranY = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_Y, curTime);
        } else {
            hasRotateDegree = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_ROTATION);
            scale = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_SCALE_X);
            hasTranX = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X);
            hasTranY = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y);
        }

        mListPointF = PipUtil.updatePointFListByDegreeAndScaleInViewCoordinate(videoClip.getPipInitPointFList(), hasRotateDegree, scale);
        float actualTranX = hasTranX * mPixTimeLineRatio;
        float actualTranY = hasTranY * mPixTimeLineRatio;
        mTransPointF = new PointF(actualTranX + adsorbPointF.x, -actualTranY + adsorbPointF.y);
        invalidate();
    }

    public boolean isDrawRectByPointF(IVideoClip videoClip, PointF clickPointF) {
        if (videoClip == null) {
            return false;
        }
        BaseVideoClipEffect transformVideoClipEffect = getTransformEffect(videoClip);
        if (transformVideoClipEffect == null) {
            GLog.e(TAG, "getClipRectPath transform effect is null");
            return false;
        }

        float hasRotateDegree = videoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_ROTATION);
        float scale = videoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_SCALE_X);
        List<PointF> newList = PipUtil.updatePointFListByDegreeAndScaleInViewCoordinate(videoClip.getPipInitPointFList(), hasRotateDegree, scale);
        if ((newList == null) || (newList.size() < POINT_LIST_INDEX_SIZE)) {
            GLog.e(TAG, "isDrawRectByPointF, get pip rect list failed");
            return false;
        }
        float hasTranX = videoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X);
        float hasTranY = videoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y);
        float actualTranX = hasTranX * mPixTimeLineRatio;
        float actualTranY = hasTranY * mPixTimeLineRatio;
        PointF adsorbPointF = videoClip.getPipAdsorbPointF();
        PointF transPointF = new PointF(actualTranX + adsorbPointF.x, -actualTranY + adsorbPointF.y);
        RectF rectF = new RectF();
        Path path = new Path();
        path.moveTo(newList.get(LEFT_TOP_INDEX).x, newList.get(LEFT_TOP_INDEX).y);
        path.lineTo(newList.get(LEFT_BOTTOM_INDEX).x, newList.get(LEFT_BOTTOM_INDEX).y);
        path.lineTo(newList.get(RIGHT_BOTTOM_INDEX).x, newList.get(RIGHT_BOTTOM_INDEX).y);
        path.lineTo(newList.get(RIGHT_TOP_INDEX).x, newList.get(RIGHT_TOP_INDEX).y);
        path.offset(transPointF.x, transPointF.y);
        path.close();
        path.computeBounds(rectF, true);
        Region region = new Region();
        region.setPath(path, new Region((int) rectF.left, (int) rectF.top, (int) rectF.right, (int) rectF.bottom));
        if ((clickPointF != null) && (region.contains((int) clickPointF.x, (int) clickPointF.y))) {
            mTransPointF = transPointF;
            mListPointF = newList;
            mSelectVideoClip = videoClip;
            invalidate();
            return true;
        }
        return false;
    }

    public void updateClipPipData(IVideoClip videoClip, float pixTimeLineRatio, int displayRatio) {
        if (videoClip == null) {
            return;
        }

        // 如果数据已存在且参数未变，跳过
        if (hasValidPipData(videoClip, displayRatio, pixTimeLineRatio)) {
            ensureTransformEffect(videoClip); // 非主轨道确保 effect 存在
            return;
        }

        // 核心：计算并设置 PIP 数据
        computeAndSetPipLayout(videoClip, displayRatio, pixTimeLineRatio);

        mDisplayRatio = displayRatio;
        mPixTimeLineRatio = pixTimeLineRatio;
    }

    private boolean hasValidPipData(IVideoClip videoClip, int displayRatio, float pixTimeLineRatio) {
        return (videoClip.getPipAdsorbPointF() != null)
                && (videoClip.getPipInitPointFList() != null)
                && (mDisplayRatio == displayRatio)
                && (mPixTimeLineRatio == pixTimeLineRatio);
    }

    private void ensureTransformEffect(IVideoClip videoClip) {
        if (!videoClip.isInMainTrack()) {
            BaseVideoClipEffect effect = videoClip.getEffect(StreamingConstant.VideoTransform.TRANSFORM_2D);
            if (effect == null) {
                videoClip.setTransformEffectValue(PIP_DEFAULT_SCALE, FLOAT_DEFAULT_VALUE,
                        FLOAT_DEFAULT_VALUE, FLOAT_DEFAULT_VALUE);
            }
        }
    }

    private void computeAndSetPipLayout(IVideoClip videoClip, int displayRatio, float pixTimeLineRatio) {
        Size size = videoClip.getFileVideoSize();
        if (size == null || size.getHeight() == 0) {
            return;
        }

        float clipFileWidth = size.getWidth();
        float clipFileHeight = size.getHeight();

        // 处理旋转
        if (hasPortraitRotation(videoClip)) {
            float temp = clipFileWidth;
            clipFileWidth = clipFileHeight;
            clipFileHeight = temp;
        }

        // 计算显示区域
        float[] bounds = calculateDisplayBounds(videoClip, clipFileWidth, clipFileHeight, displayRatio);

        // 设置四个角
        List<PointF> pointList = new ArrayList<>();
        pointList.add(new PointF(bounds[0], bounds[1])); // left-top
        pointList.add(new PointF(bounds[0], bounds[3])); // left-bottom
        pointList.add(new PointF(bounds[2], bounds[3])); // right-bottom
        pointList.add(new PointF(bounds[2], bounds[1])); // right-top

        // 设置吸附点（居中或左上）
        PointF adsorbPoint = new PointF(bounds[4], bounds[5]);

        // 更新到 videoClip
        videoClip.setPipInitPointFList(pointList);
        videoClip.setPipAdsorbPointF(adsorbPoint);
    }

    private boolean hasPortraitRotation(IVideoClip videoClip) {
        int rotation = videoClip.getExtraVideoRotation();
        return rotation == StreamingConstant.VideoRotation.VIDEOROTATION_90
                || rotation == StreamingConstant.VideoRotation.VIDEOROTATION_270;
    }

    private float[] calculateDisplayBounds(IVideoClip videoClip, float clipFileWidth, float clipFileHeight, int displayRatio) {
        float clipPixWidth = mLiveWindowWidth;
        float clipPixHeight = mLiveWindowHeight;
        float widthHeightRatio = clipPixWidth / clipPixHeight;
        float clipWidthHeightRatio = clipFileWidth / clipFileHeight;

        boolean useOrigin = shouldUseOrigin(videoClip, displayRatio, clipWidthHeightRatio, widthHeightRatio);

        BoundsInfo boundsInfo = calculateBaseBounds(videoClip, useOrigin, widthHeightRatio, clipWidthHeightRatio);

        // 处理裁剪
        applyCutProcessing(videoClip, clipFileWidth, clipFileHeight, boundsInfo);

        boundsInfo.mRight = boundsInfo.mLeft + boundsInfo.mClipPixWidth;
        boundsInfo.mBottom = boundsInfo.mTop + boundsInfo.mClipPixHeight;

        // 返回 [left, top, right, bottom, adsorbX, adsorbY]
        return new float[]{
                boundsInfo.mLeft,
                boundsInfo.mTop,
                boundsInfo.mRight,
                boundsInfo.mBottom,
                boundsInfo.mAdsorbX,
                boundsInfo.mAdsorbY
        };
    }

    private boolean shouldUseOrigin(IVideoClip videoClip, int displayRatio, float clipWidthHeightRatio, float widthHeightRatio) {
        boolean useOrigin = false;

        if (videoClip.isImage()) {
            if (videoClip.isInMainTrack()
                    && videoClip.getImageMotionMode()
                            == StreamingConstant.ClipMotionMode.CLIP_MOTION_MODE_MOTIONMMODE_ROI) {
                useOrigin = true;
            } else if (displayRatio == VideoUtils.VIDEO_ASPECT_RATIO_ORIGINAL
                    || displayRatio == VideoUtils.VIDEO_ASPECT_RATIO_ORIGINAL_VERTICAL) {
                if (Math.abs(clipWidthHeightRatio - widthHeightRatio) < PIP_MAX_RATIO_DEVIATION) {
                    useOrigin = true;
                }
            }
        }

        if (videoClip.getScan() == 1) {
            useOrigin = true;
        }

        return useOrigin;
    }

    private BoundsInfo calculateBaseBounds(IVideoClip videoClip, boolean useOrigin, float widthHeightRatio, float clipWidthHeightRatio) {
        BoundsInfo boundsInfo = new BoundsInfo();
        boundsInfo.mClipPixWidth = mLiveWindowWidth;
        boundsInfo.mClipPixHeight = mLiveWindowHeight;

        if (!useOrigin) {
            if (widthHeightRatio < clipWidthHeightRatio) {
                boundsInfo.mClipPixHeight = boundsInfo.mClipPixWidth / clipWidthHeightRatio;
                boundsInfo.mAdsorbY = (mLiveWindowHeight - boundsInfo.mClipPixHeight + 1) / 2;
                boundsInfo.mTop = boundsInfo.mAdsorbY;
            } else {
                boundsInfo.mClipPixWidth = mLiveWindowHeight * clipWidthHeightRatio;
                boundsInfo.mAdsorbX = (mLiveWindowWidth - boundsInfo.mClipPixWidth + 1) / 2;
                boundsInfo.mLeft = boundsInfo.mAdsorbX;
            }
        }

        return boundsInfo;
    }

    private void applyCutProcessing(IVideoClip videoClip, float clipFileWidth, float clipFileHeight, BoundsInfo boundsInfo) {
        Size cutSize = videoClip.getCuttedTailorSize();
        if (cutSize != null) {
            if (cutSize.getWidth() == clipFileWidth) {
                float pixHeight = cutSize.getHeight() * boundsInfo.mClipPixWidth / cutSize.getWidth();
                boundsInfo.mTop = (boundsInfo.mClipPixHeight - pixHeight) / TWO_FINGER;
                boundsInfo.mClipPixHeight = pixHeight + boundsInfo.mTop;
            } else {
                float pixWidth = cutSize.getWidth() * boundsInfo.mClipPixHeight / cutSize.getHeight();
                boundsInfo.mLeft = (boundsInfo.mClipPixWidth - pixWidth) / TWO_FINGER;
                boundsInfo.mClipPixWidth = pixWidth + boundsInfo.mLeft;
            }
        }
    }

    public void calculatePipPointFList(float scalePercent, float rotateDegree) {
        if (mSelectVideoClip == null) {
            GLog.e(TAG, "calculatePipPointFList VideoClip is null");
            hideRectView();
            return;
        }
        BaseVideoClipEffect transformVideoClipEffect = getTransformEffect(mSelectVideoClip);
        if (transformVideoClipEffect == null) {
            GLog.e(TAG, "getCurrentPipPointFList transform effect is null");
            hideRectView();
            return;
        }

        float hasRotateDegree = 0;
        float scale = 0;
        if (transformVideoClipEffect.hasKeyframe()) {
            long curTime = 0;
            if (mEditorEngine != null) {
                curTime = mEditorEngine.getTimelineCurrentPosition() - mSelectVideoClip.getInPoint();
            }
            hasRotateDegree = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_ROTATION, curTime);
            scale = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_X, curTime);
            float tranX = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_X, curTime);
            float tranY = transformVideoClipEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_Y, curTime);

            transformVideoClipEffect.removeAllKeyframe();
            mSelectVideoClip.setTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X, tranX);
            mSelectVideoClip.setTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y, tranY);
        } else {
            hasRotateDegree = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_ROTATION);
            scale = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_SCALE_X);
        }

        float degree = hasRotateDegree;

        if (Math.abs(rotateDegree) < PipUtil.PIP_DEGREE_180) {  //防止快速缩放旋转的时候rotateDegree计算错误，会影响旋转的方向
            mCumulativeRotation += rotateDegree;
            degree = (hasRotateDegree + mCumulativeRotation);
        } else {
            GLog.e(TAG, "rotate data error");
        }

        if (((hasRotateDegree % PipUtil.PIP_DEGREE_90 == 0) || (hasRotateDegree % PipUtil.PIP_DEGREE_45 == 0))
                && (Math.abs(degree - hasRotateDegree) <= PIP_ROTATION_IGNORE_RANGE)
                && (Math.abs(scalePercent - SCALE_UNIT) <= PIP_SCALE_GAP)) {
            //Debugger.d(TAG, "calculatePipPointFList ignore touch");
            return;
        }

        degree = PipUtil.getAdsorbDegree(degree, PIP_ROTATION_ADSORB_RANGE);
        if (degree != hasRotateDegree) {
            mCumulativeRotation = 0;
        }
        scale = scale * scalePercent;
        if (scale < PIP_MIM_SCALE) {
            scale = PIP_MIM_SCALE;
        }
        if (scale > PIP_MAX_SCALE) {
            scale = PIP_MAX_SCALE;
        }
        GLog.d(TAG, "EditorPipEditRectView, calculatePipPointFList, degree:" + degree);
        mSelectVideoClip.setTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_ROTATION, degree);
        mSelectVideoClip.setTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_SCALE_X, scale);
        mSelectVideoClip.setTransFormFloatValue(transformVideoClipEffect, StreamingConstant.VideoTransform.PARAM_SCALE_Y, scale);
        mListPointF = PipUtil.updatePointFListByDegreeAndScaleInViewCoordinate(mSelectVideoClip.getPipInitPointFList(), degree, scale);
        mOperationVideoClip = mSelectVideoClip.clone();
        if (mOnTouchEventListener != null) {
            mOnTouchEventListener.onScaleAndRotate(scale, degree, transformVideoClipEffect);
        }
        invalidate();
    }


    private boolean canTranslateX(float pre, float now) {
        if (mSelectVideoClip == null) {
            return true;
        }
        BaseVideoClipEffect transformVideoClipEffect = getTransformEffect(mSelectVideoClip);
        if (transformVideoClipEffect == null) {
            return true;
        }
        float lastPointFx = pre;

        if (mLastPointFxWhenAdsorb != 0) {
            lastPointFx = mLastPointFxWhenAdsorb;
        }
        float tranX = now - lastPointFx;

        float hasTransX = getHasTrans(StreamingConstant.VideoTransform.PARAM_TRANS_X);

        PointF adsorbPointF = mSelectVideoClip.getPipAdsorbPointF();
        float currentTransX = hasTransX * mPixTimeLineRatio;
//        Debugger.d(TAG, "canTranslateX,currentTransX:" + currentTransX
//                + ",tranX:" + tranX);
        if (((currentTransX < 0) && (tranX >= 0)
                || ((currentTransX > 0) && (tranX <= 0)))) {
            return true;
        }

        float actualNextTranX = currentTransX + tranX;
        float finalActualTranX = actualNextTranX + adsorbPointF.x;
        float distanceX = Math.abs(finalActualTranX - adsorbPointF.x);
//                Debugger.d(TAG, "canTranslateX,finalActualTranX:" + finalActualTranX
//                        + ", adsorbPointF.x:" + adsorbPointF.x + ", distanceX:" + distanceX);
        return distanceX >= PIP_IGNORE_ADSORB_RANGE;
    }

    private boolean canTranslateY(float pre, float now) {
        if (mSelectVideoClip == null) {
            return true;
        }
        BaseVideoClipEffect transformVideoClipEffect = getTransformEffect(mSelectVideoClip);
        if (transformVideoClipEffect == null) {
            return true;
        }
        float lastPointFy = pre;

        if (mLastPointFyWhenAdsorb != 0) {
            lastPointFy = mLastPointFyWhenAdsorb;
        }
        float tranY = now - lastPointFy;

        float hasTransY = getHasTrans(StreamingConstant.VideoTransform.PARAM_TRANS_Y);

        PointF adsorbPointF = mSelectVideoClip.getPipAdsorbPointF();
        float actualTranY = hasTransY * mPixTimeLineRatio;
//        Debugger.d(TAG, "canTranslateY,actualTranY:" + actualTranY
//                + ",tranY:" + tranY);
        if (((actualTranY < 0) && (tranY <= 0)
                || ((actualTranY > 0) && (tranY >= 0)))) {
            return true;
        }

        actualTranY = actualTranY - tranY;
        float finalActualTranY = actualTranY - adsorbPointF.y;
        float distanceY = Math.abs(finalActualTranY + adsorbPointF.y);
//      Debugger.d(TAG, "canTranslateY,finalActualTranY:" + finalActualTranY + ", adsorbPointF.y:" + adsorbPointF.y + ", distanceY:" + distanceY);
        return distanceY >= PIP_IGNORE_ADSORB_RANGE;
    }

    private float getHasTrans(String paramTrans) {
        if (mSelectVideoClip == null) {
            return 0;
        }
        BaseVideoClipEffect transformVideoClipEffect = getTransformEffect(mSelectVideoClip);
        if (transformVideoClipEffect == null) {
            return 0;
        }
        float hasTrans = 0;
        if (transformVideoClipEffect.hasKeyframe()) {
            long curTime = 0;
            if (mEditorEngine != null) {
                curTime = mEditorEngine.getTimelineCurrentPosition() - mSelectVideoClip.getInPoint();
            }
            hasTrans = transformVideoClipEffect.getFloatValAtTime(paramTrans, curTime);
        } else {
            hasTrans = mSelectVideoClip.getTransFormFloatValue(transformVideoClipEffect, paramTrans);
        }
        return hasTrans;
    }

    public void calculateTranslatePointF(PointF prePointF, PointF nowPointF) {
        if (mSelectVideoClip == null) {
            return;
        }

        BaseVideoClipEffect transformEffect = getTransformEffect(mSelectVideoClip);
        if (transformEffect == null) {
            GLog.e(TAG, "calculateTranslatePointF transform effect is null");
            mTransPointF = new PointF(0, 0);
            invalidate();
            return;
        }

        // 获取上次吸附点或使用原始点
        float lastPointFx = (mLastPointFxWhenAdsorb != 0) ? mLastPointFxWhenAdsorb : prePointF.x;
        float lastPointFy = (mLastPointFyWhenAdsorb != 0) ? mLastPointFyWhenAdsorb : prePointF.y;

        // 计算相对位移
        float tranX = nowPointF.x - lastPointFx;
        float tranY = nowPointF.y - lastPointFy;

        // 抽出核心逻辑
        PointF finalPoint = computeFinalTranslation(transformEffect, tranX, tranY, nowPointF);

        // 更新状态 & 回调
        mOperationVideoClip = mSelectVideoClip.clone();
        if (mOnTouchEventListener != null) {
            mOnTouchEventListener.onDrag(finalPoint, transformEffect);
        }
        mTransPointF = finalPoint;
        invalidate();
    }

    private PointF computeFinalTranslation(BaseVideoClipEffect transformEffect, float tranX, float tranY, PointF nowPointF) {
        // 获取变换参数
        TransformData transformData = getTransformParams(transformEffect);

        // 计算实际位移
        float[] translationResult = calculateActualTranslation(transformData, tranX, tranY);
        float actualTranX = translationResult[0];
        float actualTranY = translationResult[1];
        float finalActualTranX = translationResult[2];
        float finalActualTranY = translationResult[3];

        // 处理吸附逻辑
        float[] adsorbResult = handleAdsorbLogic(finalActualTranX, finalActualTranY, actualTranX, actualTranY, nowPointF);
        actualTranX = adsorbResult[0];
        actualTranY = adsorbResult[1];
        finalActualTranX = adsorbResult[2];
        finalActualTranY = adsorbResult[3];

        // 保存最终值
        saveFinalTranslationValues(transformEffect, actualTranX, actualTranY);

        // 返回最终用于绘制/回调的点
        return new PointF(finalActualTranX, -finalActualTranY);
    }

    private TransformData getTransformParams(BaseVideoClipEffect transformEffect) {
        TransformData data = new TransformData();
        data.mDegree = 0;
        data.mScaleX = SCALE_DEFAULT;
        data.mScaleY = SCALE_DEFAULT;
        data.mHasTransX = 0;
        data.mHasTransY = 0;

        if (transformEffect.hasKeyframe() && mEditorEngine != null) {
            long curTime = mEditorEngine.getTimelineCurrentPosition() - mSelectVideoClip.getInPoint();
            data.mHasTransX = transformEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_X, curTime);
            data.mHasTransY = transformEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_Y, curTime);

            BaseKeyFrameInfo selectedKeyFrame = KeyFrameUtil.getSelectKeyframe(mSelectVideoClip, mEditorEngine.getTimelineCurrentPosition());
            if (selectedKeyFrame != null) {
                data.mDegree = selectedKeyFrame.getRotationZ();
                data.mScaleX = selectedKeyFrame.getScaleX();
                data.mScaleY = selectedKeyFrame.getScaleY();
            } else {
                data.mDegree = transformEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_ROTATION, curTime);
                data.mScaleX = transformEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_X, curTime);
                data.mScaleY = transformEffect.getFloatValAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_Y, curTime);
            }

            // 清除所有关键帧（临时操作？）
            transformEffect.removeAllKeyframe();
            mSelectVideoClip.setTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_ROTATION, data.mDegree);
            mSelectVideoClip.setTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_SCALE_X, data.mScaleX);
            mSelectVideoClip.setTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_SCALE_Y, data.mScaleY);
        } else {
            data.mHasTransX = mSelectVideoClip.getTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X);
            data.mHasTransY = mSelectVideoClip.getTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y);
        }

        return data;
    }

    private float[] calculateActualTranslation(TransformData data, float tranX, float tranY) {
        // 转换为实际位移
        float actualTranX = data.mHasTransX * mPixTimeLineRatio + tranX;
        float actualTranY = data.mHasTransY * mPixTimeLineRatio - tranY;

        PointF adsorbPoint = mSelectVideoClip.getPipAdsorbPointF();
        float finalActualTranX = actualTranX + adsorbPoint.x;
        float finalActualTranY = actualTranY - adsorbPoint.y;

        GLog.d(TAG, "calculateTranslatePointF TranX: " + finalActualTranX + "  TranY: " + finalActualTranY);

        return new float[]{actualTranX, actualTranY, finalActualTranX, finalActualTranY};
    }

    private float[] handleAdsorbLogic(float finalActualTranX, float finalActualTranY, float actualTranX, float actualTranY, PointF nowPointF) {
        PointF adsorbPoint = mSelectVideoClip.getPipAdsorbPointF();
        // 使用局部变量存储结果
        float resultFinalActualTranX = finalActualTranX;
        float resultActualTranX = actualTranX;
        float resultFinalActualTranY = finalActualTranY;
        float resultActualTranY = actualTranY;

        // X 吸附中心
        if (Math.abs(resultFinalActualTranX - adsorbPoint.x) <= PIP_CENTER_ADSORB_RANGE) {
            resultFinalActualTranX = adsorbPoint.x;
            resultActualTranX = 0;
            if (mLastPointFxWhenAdsorb == 0) {
                mLastPointFxWhenAdsorb = nowPointF.x;
            }
        } else {
            mLastPointFxWhenAdsorb = 0;
        }

        // Y 吸附中心
        if (Math.abs(resultFinalActualTranY + adsorbPoint.y) <= PIP_CENTER_ADSORB_RANGE) {
            resultFinalActualTranY = -adsorbPoint.y;
            resultActualTranY = 0;
            if (mLastPointFyWhenAdsorb == 0) {
                mLastPointFyWhenAdsorb = nowPointF.y;
            }
        } else {
            mLastPointFyWhenAdsorb = 0;
        }

        return new float[]{resultActualTranX, resultActualTranY, resultFinalActualTranX, resultFinalActualTranY};
    }

    private void saveFinalTranslationValues(BaseVideoClipEffect transformEffect, float actualTranX, float actualTranY) {
        // 保存最终值（归一化）
        mSelectVideoClip.setTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X, actualTranX / mPixTimeLineRatio);
        mSelectVideoClip.setTransFormFloatValue(transformEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y, actualTranY / mPixTimeLineRatio);
    }

    @Override
    public void invalidate() {
        super.invalidate();
    }

    private BaseVideoClipEffect getTransformEffect(IVideoClip videoClip) {
        return IVideoClip.getTransformEffect(videoClip);
    }

    // 辅助类用于存储边界信息
    private static class BoundsInfo {
        float mLeft = 0;
        float mTop = 0;
        float mRight = 0;
        float mBottom = 0;
        float mAdsorbX = 0;
        float mAdsorbY = 0;
        float mClipPixWidth;
        float mClipPixHeight;
    }

    // 辅助类用于封装返回数据
    private static class TransformData {
        float mHasTransX;
        float mHasTransY;
        float mDegree;
        float mScaleX;
        float mScaleY;
    }

    public interface OnEditTouchEventListener {
        /**
         * onTouchDown help to check is touch on sticker
         *
         * @param curPoint position when touchDown
         */
        void onTouchDown(PointF curPoint);

        /**
         * scale and rotate when touch on clip
         *
         * @param scale  scale value
         * @param degree rotate value
         */
        void onScaleAndRotate(float scale, float degree, BaseVideoClipEffect transformClipEffect);

        /**
         * touch in pip and drag
         *
         * @param transPoint center location after drag
         */
        void onDrag(PointF transPoint, BaseVideoClipEffect transformClipEffect);

        /**
         * onTouchUp help to save operation
         */
        void onTouchUp(PointF curPoint, IVideoClip operationClip);

        /**
         * click out of pip rect
         */
        void onOutOfPipRectClick(PointF curPoint);

        /**
         * click in pip rect
         */
        void onPipRectClick();
    }

}
