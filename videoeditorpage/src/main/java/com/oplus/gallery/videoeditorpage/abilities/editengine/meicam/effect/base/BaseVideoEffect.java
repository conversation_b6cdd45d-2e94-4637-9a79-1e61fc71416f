/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseVideoEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base;

public abstract class BaseVideoEffect {
    protected String mName;

    public BaseVideoEffect(String name) {
        mName = name;
    }

    public String getName() {
        return mName;
    }

    public String setName(String name) {
        return mName = name;
    }

    public void seekTo(long progress) {
    }

}
