/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.graphics.Bitmap;

import com.oplus.gallery.standard_lib.thread.Future;
import com.oplus.gallery.standard_lib.thread.FutureListener;


/**
 * We use this class to
 * 1.) load bitmaps in background.
 * 2.) as a place holder for the loaded bitmap
 */
public abstract class BitmapTask implements FutureListener<Bitmap> {
    @SuppressWarnings("unused")
    private static final String TAG = "BitmapTask";

    /*
     * Transition Map: INIT -> REQUESTED, RECYCLED REQUESTED -> INIT (cancel),
     * LOADED, ERROR, RECYCLED LOADED, ERROR -> RECYCLED
     */
    private static final int STATE_INIT = 0;
    private static final int STATE_REQUESTED = 1;
    private static final int STATE_LOADED = 2;
    private static final int STATE_ERROR = 3;

    private int mState = STATE_INIT;
    // mTask is not null only when a task is on the way
    private Future<Bitmap> mTask;
    private Bitmap mBitmap;

    @Override
    public void onFutureDone(Future<Bitmap> future) {
        synchronized (this) {
            mTask = null;
            mBitmap = future.get();

            if (future.isCancelled() && mBitmap == null) {
                if (mState == STATE_REQUESTED) {
                    mTask = submitTask(this);
                }else {
                    onCancel();
                }
                return; // don't call callback
            } else {
                mState = (mBitmap == null) ? STATE_ERROR : STATE_LOADED;
            }
            onLoadComplete(mBitmap);
        }
    }

    public synchronized void startLoad() {
        if (mState == STATE_INIT) {
            mState = STATE_REQUESTED;
            if (mTask == null) {
                mTask = submitTask(this);
            }
        }
    }

    public synchronized boolean isLoaded() {
        return mState == STATE_LOADED;
    }

    public synchronized void resetLoadedState() {
        if (isLoaded()) {
            mState = STATE_INIT;
        }
    }

    public synchronized void cancelLoad() {
        if (mState == STATE_REQUESTED) {
            mState = STATE_INIT;
            if (mTask != null) {
                mTask.cancel();
            }
        }
    }

    @Override
    public String toString() {
        return "BitmapTask{" + "mState = " + mState + ", mBitmap = " + mBitmap + '}';
    }

    public synchronized boolean isRequestInProgress() {
        return mState == STATE_REQUESTED;
    }

    public synchronized Bitmap getBitmap() {
        return mBitmap;
    }

    abstract protected Future<Bitmap> submitTask(FutureListener<Bitmap> l);

    abstract protected void onLoadComplete(Bitmap bitmap);

    abstract protected void onCancel();
}
