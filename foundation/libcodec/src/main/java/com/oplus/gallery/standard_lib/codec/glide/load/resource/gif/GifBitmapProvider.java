package com.oplus.gallery.standard_lib.codec.glide.load.resource.gif;

import android.graphics.Bitmap;

import com.oplus.gallery.standard_lib.codec.glide.gifdecoder.GifDecoder;
import com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.BitmapPool;

import androidx.annotation.NonNull;

/**
 * Implements {@link GifDecoder.BitmapProvider} by wrapping Glide's
 * {@link BitmapPool}.
 */
public class GifBitmapProvider implements GifDecoder.BitmapProvider {
    private final BitmapPool bitmapPool;

    /**
     * Constructs an instance without a shared byte array pool. Byte arrays will be always constructed
     * when requested.
     */
    public GifBitmapProvider(BitmapPool bitmapPool) {
        this.bitmapPool = bitmapPool;
    }

    @NonNull
    @Override
    public Bitmap obtain(int width, int height, @NonNull Bitmap.Config config) {
        return bitmapPool.getDirty(width, height, config);
    }

    @Override
    public void release(@NonNull Bitmap bitmap) {
        bitmapPool.put(bitmap);
    }

    @NonNull
    @Override
    public byte[] obtainByteArray(int size) {
        return new byte[size];
    }

    @Override
    public void release(@NonNull byte[] bytes) {
    }

    @NonNull
    @Override
    public int[] obtainIntArray(int size) {
        return new int[size];
    }

    @SuppressWarnings("PMD.UseVarargs")
    @Override
    public void release(@NonNull int[] array) {
    }
}