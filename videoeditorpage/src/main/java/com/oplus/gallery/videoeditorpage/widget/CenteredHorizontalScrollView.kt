/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CenteredHorizontalScrollView.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/3
 * Author: yelongfei
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * yelongfei       2025/8/3        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.HorizontalScrollView

/**
 * 一个可以自动居中内容的HorizontalScrollView
 * 当内容宽度小于容器宽度时，内容会自动居中显示
 * 当内容宽度大于容器宽度时，支持正常横向滚动
 */
class CenteredHorizontalScrollView : HorizontalScrollView {
    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        // 获取子视图
        val child = getChildAt(0)
        if (child != null) {
            val childWidth = child.measuredWidth
            val width = (r - l)
            // 如果子视图宽度小于容器宽度，则需要居中
            if (childWidth < width) {
                val offset = ((width - childWidth) / 2)
                child.left = offset
                child.right = childWidth + offset
            }
        }
    }

    /**
     * 在视图树完成时确保内容居中
     */
    fun centerContent() {
        viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                viewTreeObserver.removeOnGlobalLayoutListener(this)
                centerContentIfNeeded()
            }
        })
    }

    /**
     * 根据内容宽度和容器宽度判断是否需要居中
     */
    private fun centerContentIfNeeded() {
        val child = getChildAt(0)
        if (child != null) {
            val childWidth = child.width
            val width = width

            // 如果子视图宽度小于容器宽度，则居中显示
            if (childWidth < width) {
                val scrollX = ((childWidth - width) / 2)
                smoothScrollTo(-scrollX, 0)
            } else {
                // 如果子视图宽度大于等于容器宽度，则滚动到最左侧
                smoothScrollTo(0, 0)
            }
        }
    }
}