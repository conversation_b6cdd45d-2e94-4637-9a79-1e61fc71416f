/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ImageHandler
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguang<PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.oplus.gallery.foundation.util.debug.GLog;

import java.lang.ref.WeakReference;

public class ImageHandler extends Handler {
    private static final String TAG = "ImageHandler";
    private WeakReference<BaseThumbnailLoader> mLoaderManagerWr;

    public ImageHandler(BaseThumbnailLoader loaderManager) {
        super(Looper.getMainLooper());
        this.mLoaderManagerWr = new WeakReference<>(loaderManager);
    }

    @Override
    public void handleMessage(Message msg) {
        BaseThumbnailLoader loaderManager = mLoaderManagerWr.get();
        if (loaderManager != null) {
            switch (msg.what) {
                case BaseThumbnailLoader.MSG_UPDATE_THUMBNAIL:
                    loaderManager.afterLoadComplete();

                    BaseThumbnailLoader.BaseThumbnailTask task = (BaseThumbnailLoader.BaseThumbnailTask) msg.obj;

                    GLog.d(TAG, "msg_update_thumbnail, task: " + task);

                    if ((task != null) && (task.getRequestData() != null) && (task.getRequestData().getThumbnailListener() != null)) {
                        task.getRequestData().getThumbnailListener().updateThumbnail(
                                new ThumbnailRespond<>(task.getRequestData().getSource(), task.getBitmap()));
                    }
                    break;
                case BaseThumbnailLoader.MSG_CANCEL:
                    loaderManager.afterLoadComplete();
                    break;
                default:
                    break;
            }
        }
    }
}
