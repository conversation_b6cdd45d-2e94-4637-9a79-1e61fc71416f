/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * File: ColorPickerItemData.kt
 * Description: tangzhibin created
 * Version: 1.0
 * Date: 2025/4/30
 * Author: tangzhibin
 *
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * tangzhibin      2025/4/30        1.0         NEW
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.colorpicker

import android.graphics.Color
import android.graphics.drawable.Drawable

/**
 * 颜色选择器项数据
 */
data class ColorPickerItemData(
    /**
     * 颜色值
     */
    val color: Int = Color.TRANSPARENT,

    /**
     * 自定义drawable
     */
    val drawable: Drawable? = null,

    /**
     * 自定义标识，用于区分不同的颜色项
     */
    val tag: Any? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is ColorPickerItemData) return false

        if (color != other.color) return false
        if (tag != other.tag) return false

        return true
    }

    override fun hashCode(): Int {
        var result = color
        result = 31 * result + (tag?.hashCode() ?: 0)
        return result
    }
}
