/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISaveTask
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/16
 ** Author: zhouzihao
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  zhouzihao                   2025/01/16        1.0      first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.output

import android.graphics.Bitmap
import android.net.Uri
import androidx.annotation.IntDef
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.EXPORT_IMAGE_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.EXPORT_OLIVE_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.GET_FRAME_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.MEICAM_FORCE_STOP_SAVE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_SUCCESS
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_NO_SPACE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_DIMENSION_TOO_LARGE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.TASK_STOP_FORCE_STOP_SAVE

/**
 * 保存结果
 * @param resultCode 保存结果码：成功、由于空间不足导致失败等等其他原因导致的失败
 * @param resultDesc 保存结果描述，一般保存失败需要填充
 * @param newFileInfo 保存成功后，保存的文件信息
 */
internal data class SaveResult(@ResultCode val resultCode: Int, val resultDesc: String? = null, val newFileInfo: NewFileInfo? = null)

/**
 * 新文件信息
 * @param resultUri 新文件uri
 * @param itemPath 文件路径
 * @param albumPath 文件集路径
 * @param filePath 文件绝对路径
 * @param savedBitmap 保存到文件的bitmap
 */
internal data class NewFileInfo(
    val resultUri: Uri,
    val itemPath: Path?,
    val albumPath: Path?,
    val filePath: String?,
    val savedBitmap: Bitmap?
)

@IntDef(value = [
    SAVE_SUCCESS,
    SAVE_FAILED_NO_SPACE,
    SAVE_FAILED_DIMENSION_TOO_LARGE,
    SAVE_FAILED_ERROR,
    GET_FRAME_TASK_ERROR,
    EXPORT_IMAGE_TASK_ERROR,
    EXPORT_OLIVE_TASK_ERROR,
    MEICAM_FORCE_STOP_SAVE,
    TASK_STOP_FORCE_STOP_SAVE
])
@Retention(AnnotationRetention.SOURCE)
annotation class ResultCode {
    companion object {
        /**
         * 保存成功
         */
        const val SAVE_SUCCESS = 0

        /**
         * 磁盘空间不足导致失败
         */
        const val SAVE_FAILED_NO_SPACE = 1

        /**
         * 图片过大保存失败
         */
        const val SAVE_FAILED_DIMENSION_TOO_LARGE = 2

        /**
         * 保存文件失败
         */
        const val SAVE_FAILED_ERROR = 3

        /**
         * 取帧无效导致保存文件失败
         */
        const val GET_FRAME_TASK_ERROR = 4

        /**
         * 图片导出异常导致保存文件失败
         */
        const val EXPORT_IMAGE_TASK_ERROR = 5
        /**
         * 实况导出异常导致保存文件失败
         */
        const val EXPORT_OLIVE_TASK_ERROR = 6
        /**
         * 强制停止保存(美摄接口回调)
         */
        const val MEICAM_FORCE_STOP_SAVE = 7
        /**
         * 强制停止保存(用户操作，比如点击取消)
         */
        const val TASK_STOP_FORCE_STOP_SAVE = 8
    }
}
