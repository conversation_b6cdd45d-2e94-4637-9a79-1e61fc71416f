/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorSaveState
 ** Description: 保存页状态
 ** Version: 1.0
 ** Date : 2025/07/14
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/07/14    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.output

import android.content.Context
import android.os.Build
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.uhdr.VideoUhdrEngine
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum
import com.oplus.gallery.videoeditorpage.video.business.cover.EditorCoverHelper
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.output.task.ExportBaseTask
import com.oplus.gallery.videoeditorpage.video.business.output.task.ExportOliveTask
import com.oplus.gallery.videoeditorpage.video.business.output.task.ExportVideoTask
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveType
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveVideoData
import com.oplus.gallery.videoeditorpage.video.business.picker.PickerHelper
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ThreadPoolExecutor

/**
 * 保存页状态
 */
class EditorSaveState(
    context: Context,
    editorControlView: EditorControlView
) : EditorBaseState<EditorSaveUIController>(
    TAG,
    context,
    editorControlView
) {
    /**
     * 导出任务的单线程执行器，
     * 导出的任务会放在此执行器中执行，如果先前任务未完成会进行排队
     * 用户取消导出时会清空排队队列。
     */
    private val executor: ExecutorService = Executors.newSingleThreadExecutor()

    /**
     * 导出的任务
     */
    private var exportTask: ExportBaseTask? = null

    override fun createUIController(): EditorSaveUIController = EditorSaveUIController(mContext, editorControlView, this)

    override fun destroy() {
        super.destroy()
        executor.shutdownNow()
    }

    override fun showOperaIcon(): Boolean = false

    override fun onBackPressed(): Boolean {
        cancelExportTask()
        return super.onBackPressed()
    }

    @Override
    override fun getPageLevel(): PageLevelEnum = PageLevelEnum.PAGE_LEVEL_SECOND

    override fun isSkipAnim(): Boolean = true

    /**
     * 开始保存任务
     */
    fun startSave() {
        val engine = editorEngine ?: run {
            GLog.e(TAG, LogFlag.DL) { "[startSave] mEditorEngine is null" }
            return
        }
        val timeline = engine.currentTimeline ?: run {
            GLog.e(TAG, LogFlag.DL) { "[startSave] timeline is null" }
            return
        }

        val videoType = VideoParser.getInstance().getSupportVideoType()
        val videoSize = ResolutionUtil.getResolutionSize(editorEngine?.originalSizeFromTimeline, timeline.ratioOption, timeline.nodeResolution)
        val createTimeStamp: Long = PickerHelper.pickerList.takeIf { it.size == MathUtils.ONE }?.let {
            // 当仅选择1个图片或视频编辑时，保存时间使用原文件的dateTaken
            it.first().dateTakenInMs
        } ?: System.currentTimeMillis()
        val saveData = SaveVideoData(
            MathUtils.ZERO.toLong(),
            timeline.duration,
            timeline.nodeFps,
            videoType,
            videoSize,
            createTimeStamp,
        )
        if (editorEngine?.saveType == SaveType.OLIVE) {
            saveOlive(engine, saveData)
        } else {
            saveVideo(engine, saveData)
        }
    }

    /**
     * 保存为实况
     *
     * @param engine 编辑器引擎
     * @param saveData 保存数据
     */
    private fun saveOlive(engine: EditorEngine, saveData: SaveVideoData) {
        val uhdrEngine = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) VideoUhdrEngine(mContext, engine) else null
        val newTask = ExportOliveTask(
            engine,
            uhdrEngine,
            saveData,
            uiController.coverBitmap,
            uiController.exportListener
        )
        newTask.coverTimeUs = engine.currentTimeline.coverTimestamp
        executor.submit {
            exportTask = newTask.apply { run() }
        }
    }

    /**
     * 保存为视频
     *
     * @param engine 编辑器引擎
     * @param saveData 保存数据
     */
    private fun saveVideo(engine: EditorEngine, saveData: SaveVideoData) {
        val newTask = ExportVideoTask(
            engine,
            saveData,
            if (mEditorEngine.currentTimeline.coverMode != EditorCoverHelper.CoverMode.DEFAULT_COVER) uiController.coverBitmap else null,
            uiController.exportListener
        )
        executor.submit {
            exportTask = newTask.apply { run() }
        }
    }

    /**
     * 取消导出任务
     */
    fun cancelExportTask() {
        exportTask?.stop()
        exportTask = null

        (executor as? ThreadPoolExecutor)?.also { it.queue.clear() }
    }

    internal companion object {
        private const val TAG = "EditorSaveState"
    }
}