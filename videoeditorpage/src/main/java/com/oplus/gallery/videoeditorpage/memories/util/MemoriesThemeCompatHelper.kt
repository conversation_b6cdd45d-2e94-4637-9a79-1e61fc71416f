/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MemoriesThemeCompatHelper.kt
 ** Description : helper for memories theme style compat
 ** Version     : 1.0
 ** Date        : 2022/12/16
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>       2022/12/16    1.0     build this module
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.util

import android.graphics.PointF
import com.meicam.themehelper.NvsThemeHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo

/**
 * 作用：该类用于回忆主题效果的兼容，解决美摄SDK升级版本引入的主题样式包字幕效果不一致的问题。
 * 使用范围：涉及到利用美摄SDK进行回忆主题更换的场景，需要在[NvsThemeHelper.applyTimelineTheme]方法调用后
 *         进行该类中适配方法的调用，目前仅限于在[MeicamThemeHelper.addTheme]方法中调用。
 * 生命周期：marked by gaojinxian, 等待在测试服务上充分验证，确认可以安全的完成服务器中的样式更新后，需要移除目前临时的兼容逻辑。
 */
object MemoriesThemeCompatHelper {
    const val TAG = "MemoriesThemeCompatHelper"

    /**
     * TITLE_CAPTION - 回忆封面中的标题字幕
     */
    private const val TITLE_CAPTION_COMPAT_X_AXIS_OFFSET = 0.0F
    private const val TITLE_CAPTION_COMPAT_Y_AXIS_OFFSET = 17.5F
    /**
     * HINT_CAPTION - 回忆封面中的日期字幕
     */
    private const val HINT_CAPTION_COMPAT_X_AXIS_OFFSET = 0.0F
    private const val HINT_CAPTION_COMPAT_Y_AXIS_OFFSET = -8.0F

    /**
     * 针对位于服务器中的7个主题，对齐升级SDK前后主题样式包中的字幕偏移效果
     * @param themeInfo 主题数据类
     * @param themeHelper 美摄提供的回忆功能接口
     */
    fun performThemeCaptionCompat(themeInfo: ThemeInfo?, themeHelper: NvsThemeHelper?) {
        themeHelper?.let { themeHelperIt ->
            themeInfo?.let { themeInfoIt ->
                if (themeInfoIt.source == ThemeInfo.THEME_SOURCE_BUILTIN) {
                    return
                }
                // setCaptionTranslation 方法中的 rebuildCaptionTrans 参数设置为true时，添加封面等操作重建timeline不会影响设置的字幕兼容偏移
                themeHelperIt.setCaptionTranslation(
                    PointF(TITLE_CAPTION_COMPAT_X_AXIS_OFFSET, TITLE_CAPTION_COMPAT_Y_AXIS_OFFSET),
                    PointF(HINT_CAPTION_COMPAT_X_AXIS_OFFSET, HINT_CAPTION_COMPAT_Y_AXIS_OFFSET),
                    true
                )
                return
            }
        }
        GLog.e(TAG, "performThemeCaptionCompat, null arguments")
    }
}