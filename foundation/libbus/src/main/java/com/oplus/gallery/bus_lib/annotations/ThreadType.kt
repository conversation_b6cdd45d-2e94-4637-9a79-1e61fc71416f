/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThreadType.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** huca<PERSON><PERSON>@Apps.Gallery		2020/08/14		1.0		OPLUS_ARCH_BUS
 *********************************************************************************/

package com.oplus.gallery.bus_lib.annotations

import androidx.annotation.IntDef

@Retention(AnnotationRetention.SOURCE)
@IntDef(
        ThreadType.CURRENT,
        ThreadType.MAIN,
        ThreadType.THREAD
)
annotation class ThreadType {
    companion object {
        const val CURRENT = 0
        const val MAIN = 1
        const val THREAD = 2
    }
}