/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditTimelineSpanView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.engine.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.videoeditorpage.R;

public class EditTimelineSpanView extends RelativeLayout {
    public static final int LEFT = 0x16;
    public static final int CENTER = 0x17;
    public static final int RIGHT = 0x18;
    private static final String TAG = "EditTimelineSpanView";
    private static final int HAND_LEFT = 0x220;
    private static final int HAND_RIGHT = 0x221;
    private static final int TIME_INTERVAL = 50;
    private static final int HAND_LIMIT_BORDER = 100;
    private static final int HAND_SCROLL_BORDER = 10;
    private static final int DELAY_TEXT_VISIBILITY = 200;
    private float mPreviewRawX = 0;
    private boolean mCanMoveHandle = false;
    private boolean mOnMiddleState = false;
    private int mHandleWidth = 0;
    private int mOriginLeft = 0;
    private int mDragDirection = CENTER;
    private int mLimitLengthOfHand;
    private ImageView mLeftHandView;
    private ImageView mRightHandView;
    private View mColorContentView;
    private View mTimeSpanShadowView;
    private int mMaxLeftToLeft = 0;
    private int mMaxLeftToRight = 0;
    private int mMaxRightToLeft = 0;
    private int mMaxRightToRight = 0;
    private OnHandChangeListener mOnHandChangeListener;
    private int mMinWidth = 0;
    private boolean mIsAllowTrim = true;

    @SuppressLint("HandlerLeak")
    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case HAND_LEFT:
                    onLeftHandToScreenBorder(-HAND_SCROLL_BORDER, true);
                    sendEmptyMessageDelayed(HAND_LEFT, TIME_INTERVAL);
                    break;
                case HAND_RIGHT:
                    onRightHandToScreenBorder(HAND_SCROLL_BORDER, true);
                    sendEmptyMessageDelayed(HAND_RIGHT, TIME_INTERVAL);
                    break;
                default:
                    break;
            }
        }
    };

    public EditTimelineSpanView(Context context) {
        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.engin_timeline_span_view, this);
        mLimitLengthOfHand = ScreenUtils.getDisplayScreenWidth() / 2;
        mLeftHandView = view.findViewById(R.id.edit_timeline_view_leftHandle);
        mRightHandView = view.findViewById(R.id.edit_timeline_view_rightHandle);
        mColorContentView = view.findViewById(R.id.edit_timeline_view_span_color_content);
        mTimeSpanShadowView = view.findViewById(R.id.edit_timeline_view_cover);
    }

    public EditTimelineSpanView(Context context, boolean canDrag) {
        this(context);
        mIsAllowTrim = canDrag;
        setViewBackground(canDrag);
    }

    private void setViewBackground(boolean canDrag) {
        if (canDrag) {
            mLeftHandView.setBackgroundResource(R.drawable.engine_preview_thumbnail_left_hand);
            mRightHandView.setBackgroundResource(R.drawable.engine_preview_thumbnail_right_hand);
            mTimeSpanShadowView.setBackgroundResource(R.drawable.edit_drawable_corner_white);
            mColorContentView.setBackgroundColor(getResources().getColor(R.color.engine_caption_span_shadow_color_transparent));
        } else {
            mLeftHandView.setBackgroundResource(R.drawable.engine_preview_thumbnail_left_hand_gray);
            mRightHandView.setBackgroundResource(R.drawable.engine_preview_thumbnail_right_hand_gray);
            mTimeSpanShadowView.setBackgroundResource(R.drawable.edit_drawable_corner_gray);
            mColorContentView.setBackgroundColor(Color.TRANSPARENT);
        }
    }

    public void setAllowTrim(boolean isAllowTrim) {
        mIsAllowTrim = isAllowTrim;
        setViewBackground(isAllowTrim);
    }

    public int getLeftHandX() {
        int[] local = new int[2];
        mLeftHandView.getLocationOnScreen(local);
        return local[0];
    }

    public int getRightHandX() {
        int[] local = new int[2];
        mRightHandView.getLocationOnScreen(local);
        return local[0];
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (!mIsAllowTrim) {
                    return false;
                }
                mCanMoveHandle = !((mHandleWidth < ev.getX()) && (ev.getX() < (getWidth() - mHandleWidth)));
                if (mLeftHandView.getVisibility() == GONE) {
                    return false;
                }
                onTouchDown(ev);
                break;
            case MotionEvent.ACTION_MOVE:
                if (!mIsAllowTrim) {
                    return false;
                }
                onTouchMove(ev);
                break;
            case MotionEvent.ACTION_UP:
                if (!mIsAllowTrim) {
                    return false;
                }
                onTouchUp();
                break;
            default:
                break;
        }
        return mCanMoveHandle;
    }

    private void setColorChangeReminder(boolean change) {
        if (change) {
            mColorContentView.setBackgroundColor(getResources().getColor(R.color.engine_caption_span_shadow_color));
        } else {
            mColorContentView.setBackgroundColor(getResources().getColor(R.color.engine_caption_span_shadow_color_transparent));
        }
    }

    private void onTouchDown(MotionEvent ev) {
        mOriginLeft = getLeft();
        mPreviewRawX = (int) ev.getRawX();
        mDragDirection = getDirection((int) ev.getX(), (int) ev.getY());
        if ((mOnHandChangeListener != null) && (mDragDirection == LEFT)) {
            requestDisallowIntercept(true);
            setColorChangeReminder(true);
            int[] local = new int[2];
            mLeftHandView.getLocationOnScreen(local);
            if (local[0] < HAND_LIMIT_BORDER) {
                mOnHandChangeListener.onLeftHandChange(true, false, 0, false, false,
                        (HAND_LIMIT_BORDER - local[0]));
            } else {
                mOnHandChangeListener.onLeftHandChange(true, false, 0, false, false,
                        0);
            }
        }
        if ((mOnHandChangeListener != null) && (mDragDirection == RIGHT)) {
            requestDisallowIntercept(true);
            setColorChangeReminder(true);
            mOnHandChangeListener.onRightHandChange(true, false, 0, false, false, false);
        }
    }

    private void onTouchMove(MotionEvent ev) {
        requestDisallowIntercept(true);
        float tempRawX = ev.getRawX();
        int dx = (int) Math.floor(tempRawX - mPreviewRawX + 0.5D);
        GLog.d(TAG, "mPreviewRawX:" + mPreviewRawX + " tempRawX:" + tempRawX);
        mPreviewRawX = tempRawX;

        if (mDragDirection == LEFT) {
            GLog.d(TAG, "mDragDirection:" + mDragDirection);
            if (checkLeftHandCanMove(dx)) {
                onLeftHandMove(dx);
            } else if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onLeftHandChange(false, false, dx, false, false,
                        0);
            }
        }
        if (mDragDirection == RIGHT) {
            if (checkRightHandCanMove(dx)) {
                onRightHandMove(dx);
            } else if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onRightHandChange(false, false, dx, false,
                        false, true);
            }
        }
    }

    private void onTouchUp() {
        mHandler.removeCallbacksAndMessages(null);
        requestDisallowIntercept(false);
        if ((mDragDirection == LEFT) && (mOnHandChangeListener != null)) {
            mOnHandChangeListener.onLeftHandChange(false, true, 0, false, false, 0);
            setColorChangeReminder(false);
        }
        if ((mDragDirection == RIGHT) && (mOnHandChangeListener != null)) {
            mOnHandChangeListener.onRightHandChange(false, true, 0, false, false, false);
            setColorChangeReminder(false);
        }
        mDragDirection = CENTER;
    }

    public boolean checkHandler() {
        boolean hasHandler = mHandler.hasMessages(HAND_LEFT) || mHandler.hasMessages(HAND_RIGHT);
        if (hasHandler) {
            mHandler.removeCallbacksAndMessages(null);
            onTouchUp();
        }
        return hasHandler;
    }

    public void setClipPosition(int start, int end) {
        int spanWidth = end - start + mHandleWidth * 2;
        LayoutParams layoutParams = new LayoutParams(start, LayoutParams.MATCH_PARENT);
        layoutParams.width = spanWidth;
        layoutParams.setMargins(start, 0, 0, 0);
        setLayoutParams(layoutParams);
    }

    public void setLayoutWidth(int width) {
        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        if (layoutParams == null) {
            return;
        }
        layoutParams.width = width;
        setLayoutParams(layoutParams);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    private void requestDisallowIntercept(boolean intercept) {
        ViewParent viewParent = getParent();
        if (viewParent == null) {
            return;
        }
        viewParent.requestDisallowInterceptTouchEvent(intercept);
    }

    private void onLeftHandMove(int dx) {
        final int realDx = getRealDxForLeft(dx);
        mOnMiddleState = false;
        if (realDx > 0) {
            mHandler.removeCallbacksAndMessages(null);
        } else if ((realDx < 0) && mHandler.hasMessages(HAND_LEFT)) {
            return;
        }
        int[] local = new int[2];
        mLeftHandView.getLocationOnScreen(local);
        if (realDx > 0) {
            onLeftHandMovePositive(realDx, local);
        } else if (realDx < 0) {
            onLeftHandMoveNegative(realDx, local);
        }
    }

    private void onLeftHandMovePositive(int dx, int[] local) {
        int tmpDx = dx;
        LayoutParams lp = (LayoutParams) getLayoutParams();
        if (local[0] + tmpDx + mHandleWidth >= mLimitLengthOfHand) {
            int newDx = mLimitLengthOfHand - (local[0] + mHandleWidth);
            mOriginLeft += newDx;
            if (newDx == 0) {
                mOnMiddleState = true;
            } else {
                tmpDx = newDx;
            }
            lp.width = lp.width - tmpDx;
            lp.setMargins(mOriginLeft, 0, 0, 0);
        } else {
            mOriginLeft += tmpDx;
            lp.width = lp.width - tmpDx;
            lp.setMargins(mOriginLeft, 0, 0, 0);
        }
        setLayoutParams(lp);
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onLeftHandChange(false, false, tmpDx, false, mOnMiddleState,
                    0);
        }
    }

    private void onLeftHandMoveNegative(int dx, int[] local) {
        if (local[0] - HAND_LIMIT_BORDER + dx < 0) {
            if (local[0] < HAND_LIMIT_BORDER) {
                if (!mHandler.hasMessages(HAND_LEFT)) {
                    mHandler.sendEmptyMessage(HAND_LEFT);
                }
            } else {
                int newDx = -local[0] + HAND_LIMIT_BORDER;
                if (((mOriginLeft + newDx) < 0) && (mOriginLeft > 0)) {
                    newDx = -mOriginLeft;
                }
                if (mOnHandChangeListener != null) {
                    mOnHandChangeListener.onLeftHandChange(false, false, newDx, false, false, 0);
                }
                if (local[0] - HAND_LIMIT_BORDER + newDx <= 0) {
                    if (!mHandler.hasMessages(HAND_LEFT)) {
                        mHandler.sendEmptyMessage(HAND_LEFT);
                    }
                }
            }
        } else {
            if (mOnHandChangeListener != null) {
                if (((mOriginLeft + dx) < 0) && (mOriginLeft > 0)) {
                    dx = -mOriginLeft;
                }
                mOnHandChangeListener.onLeftHandChange(false, false, dx, false, false, 0);
            }
        }
    }

    /**
     * dx<0 And not middle and screenEdge
     */
    public void leftOnDxChange(int dx, boolean viewLeftEdge) {
        LayoutParams lp = (LayoutParams) getLayoutParams();
        if (viewLeftEdge && (mOriginLeft == 0)) {
            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onNeedScrollParentLinearLayout(-dx);
            }
        }
        if (mOriginLeft > 0) {
            mOriginLeft += dx;
        }
        if (mOriginLeft < 0) {
            mOriginLeft = 0;
            lp.width = lp.width - dx;
            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onNeedScrollParentLinearLayout(-dx);
            }
        } else {
            lp.width = lp.width - dx;
        }
        lp.setMargins(mOriginLeft, 0, 0, 0);
        setLayoutParams(lp);
    }

    private void onLeftHandToScreenBorder(int dx, boolean fromHandler) {
        if (fromHandler) {
            if (!checkLeftHandCanMove(dx)) {
                mHandler.removeCallbacksAndMessages(null);
                return;
            }
        }
        dx = getRealDxForLeft(dx);
        LayoutParams lp = (LayoutParams) getLayoutParams();
        lp.width = lp.width - dx;
        lp.setMargins(mOriginLeft, 0, 0, 0);
        setLayoutParams(lp);
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onLeftHandChange(false, false, dx, true, false, 0);
        }
    }

    private void onRightHandMove(int dx) {
        dx = getRealDxForRight(dx);
        int[] local = new int[2];
        mOnMiddleState = false;
        mRightHandView.getLocationOnScreen(local);
        LayoutParams lp = (LayoutParams) getLayoutParams();
        if (dx < 0) {
            mHandler.removeCallbacksAndMessages(null);
        } else if ((dx > 0) && mHandler.hasMessages(HAND_RIGHT)) {
            return;
        }
        if (local[0] + dx <= mLimitLengthOfHand) {
            int newDx = mLimitLengthOfHand - local[0];
            if (newDx == 0) {
                mOnMiddleState = true;
            }
            if (newDx < 0) {
                dx = newDx;
            }
            lp.width = lp.width + dx;
            setLayoutParams(lp);

            if (mOnMiddleState && (mOnHandChangeListener != null)) {
                mOnHandChangeListener.onNeedScrollParentLinearLayout(dx);
            }

            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onRightHandChange(false, false, dx, mOnMiddleState, false, false);
            }
        } else if (local[0] + HAND_LIMIT_BORDER + mHandleWidth + dx >= ScreenUtils.getDisplayScreenWidth()) {
            if (dx < 0) {
                lp.width = lp.width + dx;
                setLayoutParams(lp);
                if (mOnHandChangeListener != null) {
                    mOnHandChangeListener.onRightHandChange(false, false, dx, mOnMiddleState, false, false);
                }
            } else {
                int newDx = ScreenUtils.getDisplayScreenWidth() - local[0] - HAND_LIMIT_BORDER - mHandleWidth;
                onRightHandToScreenBorder(dx, false);
                if (!mHandler.hasMessages(HAND_RIGHT)) {
                    mHandler.sendEmptyMessage(HAND_RIGHT);
                }
            }

        } else {
            lp.width = lp.width + dx;
            setLayoutParams(lp);
            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onRightHandChange(false, false, dx, mOnMiddleState, false, false);
            }
        }
    }

    private void onRightHandToScreenBorder(int dx, boolean fromHandler) {
        if (fromHandler) {
            if (!checkRightHandCanMove(dx)) {
                mHandler.removeCallbacksAndMessages(null);
                return;
            }
        }
        dx = getRealDxForRight(dx);
        LayoutParams lp = (LayoutParams) getLayoutParams();
        lp.width = lp.width + dx;
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onNeedScrollParentLinearLayout(dx);
        }
        setLayoutParams(lp);
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onRightHandChange(false, false, dx, mOnMiddleState, true, false);
        }
    }

    private int getDirection(int x, int y) {
        int left = getLeft();
        int right = getRight();
        if (x < mHandleWidth) {
            return LEFT;
        }
        if (right - left - x < mHandleWidth) {
            return RIGHT;
        }
        return CENTER;
    }

    public int getDragDirection() {
        return mDragDirection;
    }

    private int getRealDxForRight(int dx) {
        if ((dx > 0) && (dx > mMaxRightToRight)) {
            dx = mMaxRightToRight;
        } else if ((dx < 0) && (-dx > mMaxRightToLeft)) {
            dx = -mMaxRightToLeft;
        }
        return dx;
    }

    private boolean checkRightHandCanMove(int dx) {
        if (dx > 0) {
            return mMaxRightToRight > 0;
        } else if (dx < 0) {
            return mMaxRightToLeft > 0;
        }
        return false;
    }

    private boolean checkLeftHandCanMove(int dx) {
        if (dx > 0) {
            return mMaxLeftToRight > 0;
        } else if (dx < 0) {
            return mMaxLeftToLeft > 0;
        }
        return false;
    }

    private int getRealDxForLeft(int dx) {
        if ((dx > 0) && (dx > mMaxLeftToRight)) {
            dx = mMaxLeftToRight;
        } else if ((dx < 0) && (-dx > mMaxLeftToLeft)) {
            dx = -mMaxLeftToLeft;
        }
        return dx;
    }

    public void setViewState() {
        mLeftHandView.setVisibility(VISIBLE);
        mLeftHandView.setEnabled(true);
        mRightHandView.setVisibility(VISIBLE);
        mRightHandView.setEnabled(true);
    }

    public ImageView getLeftHandView() {
        return mLeftHandView;
    }

    public ImageView getRightHandView() {
        return mRightHandView;
    }

    public void setHandleWidth(int width) {
        this.mHandleWidth = width;
    }

    public void setMaxLeftToLeft(int mMaxLeftToLeft) {
        this.mMaxLeftToLeft = mMaxLeftToLeft;
    }

    public void setMaxLeftToRight(int mMaxLeftToRight) {
        this.mMaxLeftToRight = mMaxLeftToRight;
    }

    public void setMaxRightToLeft(int mMaxRightToLeft) {
        this.mMaxRightToLeft = mMaxRightToLeft;
    }

    public void setMaxRightToRight(int mMaxRightToRight) {
        this.mMaxRightToRight = mMaxRightToRight;
    }

    public boolean isOnMiddleState() {
        return mOnMiddleState;
    }

    public void setOnChangeListener(OnHandChangeListener onChangeListener) {
        this.mOnHandChangeListener = onChangeListener;
    }

    public interface OnHandChangeListener {
        /**
         * on left hand move
         *
         * @param isDown          true when action down
         * @param isDragEnd       true when action up
         * @param dx              move length
         * @param addParentLength when down position less than HAND_LIMIT_BORDER,parentView need add length
         */
        void onLeftHandChange(boolean isDown, boolean isDragEnd, int dx, boolean isLeftBorder, boolean center,
                              int addParentLength);

        /**
         * on left hand move
         *
         * @param isDown    true when action down
         * @param isDragEnd true when action up
         * @param dx        move length
         * @param inMiddle  true when right hand in middle of screen
         */
        void onRightHandChange(boolean isDown, boolean isDragEnd, int dx, boolean inMiddle,
                               boolean isRightBorder, boolean inMinDuration);

        void onNeedScrollParentLinearLayout(int dx);
    }
}