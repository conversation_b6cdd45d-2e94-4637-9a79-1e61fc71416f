/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: MediaPickerThumbnailLoader
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.foundation.util.debug.GLog;


import java.security.MessageDigest;

public class MediaPickerThumbnailLoader implements IThumbnailLoader<MediaItem> {
    private static final String TAG = "MediaPickerThumbnailLoader";
    private RequestOptions mOptions;
    private boolean mUseCache = false;
    private Context mContext;

    public MediaPickerThumbnailLoader(boolean useCache, Context context) {
        mUseCache = useCache;
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        if (mUseCache) {
            mOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.RESOURCE);
        } else {
            mOptions = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.NONE);
        }
    }

    @Override
    public void startLoader(MediaItem item, ThumbnailListener thumbnailListener) {
        startLoader(item, thumbnailListener, null);
    }

    @Override
    public void startLoader(MediaItem item, ImageView target) {
        if (!checkContext()) {
            return;
        }

        try {
            Glide.with(mContext).load(item.getFilePath()).apply(mOptions).centerCrop().into(target);
        } catch (Exception e) {
            GLog.e(TAG, "startLoader: ", e);
        }
    }

    @Override
    public void startLoaderWidthPlace(MediaItem item, ImageView target, Drawable placeholder) {
        if (!checkContext()) {
            return;
        }

        try {
            Glide.with(mContext).load(item.getFilePath()).apply(mOptions).centerCrop().placeholder(placeholder).into(target);
        } catch (Exception e) {
            GLog.e(TAG, "startLoader: ", e);
        }
    }

    @Deprecated
    @Override
    public void removeTask(MediaItem mediaItem) {

    }

    @Override
    public void removeTask(View target) {
        if (!checkContext()) {
            return;
        }
        Glide.with(mContext).clear(target);
    }

    @Override
    public void startLoader(MediaItem item, ImageView target, IBitmapTransformOption bitmapHandle) {
        Glide.with(mContext).asBitmap().load(item.getFilePath()).transform(new CustomBitmapTransform(bitmapHandle)).apply(mOptions).into(target);
    }

    @Override
    public void startLoader(MediaItem item, ThumbnailListener<MediaItem> thumbnailListener, IBitmapTransformOption bitmapHandle) {
        if (!checkContext()) {
            return;
        }
        try {
            Glide.with(mContext).asBitmap().load(item.getFilePath()).apply(mOptions).transform(new CustomBitmapTransform(bitmapHandle))
                    .into(new SimpleTarget<Bitmap>() {
                        @Override
                        public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                            if (thumbnailListener != null) {
                                thumbnailListener.updateThumbnail(new ThumbnailRespond<>(item, resource));
                            }
                        }
                    });
        } catch (Exception e) {
            GLog.e(TAG, "startLoader: ", e);
        }
    }

    @Deprecated
    @Override
    public int getWaitTask() {
        return 0;
    }

    @Deprecated
    @Override
    public void cancelAllWaitTask() {
        //glide会绑定生命周期自己取消。不需要实现
    }

    @Override
    public void destroy() {
        mContext = null;
    }

    private boolean checkContext() {
        if ((mContext == null) || ((mContext instanceof Activity) && ((Activity) mContext).isDestroyed())) {
            GLog.e(TAG, "checkContext context error");
            return false;
        }

        return true;
    }

    private static Bitmap.Config getNonNullConfig(@NonNull Bitmap bitmap) {
        return (bitmap.getConfig() != null) ? bitmap.getConfig() : Bitmap.Config.ARGB_8888;
    }

    private static class CustomBitmapTransform extends BitmapTransformation {
        private static final String TAG = "CustomBitmapTransform";
        IBitmapTransformOption mBitmapTransformOption;

        public CustomBitmapTransform(IBitmapTransformOption bitmapTransformOption) {
            mBitmapTransformOption = bitmapTransformOption;
        }

        @Override
        protected Bitmap transform(@NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
            GLog.d(TAG, "transform");
            if (mBitmapTransformOption != null) {
                Bitmap result = pool.get(toTransform.getWidth(), toTransform.getHeight(), getNonNullConfig(toTransform));
                return mBitmapTransformOption.transform(toTransform, result);
            }

            return toTransform;
        }

        @Override
        public void updateDiskCacheKey(@NonNull MessageDigest messageDigest) {
            messageDigest.update(TAG.getBytes(CHARSET));
        }
    }
}
