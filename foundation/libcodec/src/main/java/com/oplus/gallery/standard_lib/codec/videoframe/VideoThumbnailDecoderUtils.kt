/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : VideoThumbnailDecoderUtils.kt
 ** Description : Util for VideoThumbnailDecoder which converting video images into
 * *              Bitmap objects
 ** Version     : 1.0
 ** Date        : 2020/08/31
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery3D             2020/08/31   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.standard_lib.codec.videoframe

object VideoThumbnailDecoderUtils {
    /**
     * 相册调TBL 解缩图都是走的软解 (解缩图可能会频繁调用，这里直接定义出来)
     */
    val tblDecoderConfig = DecoderConfig(DecoderVendor.TBL, DecoderType.SOFT)

    /**
     * 美摄硬解
     * 1，杜比视频 ： 杜比解码器 > hevc 解码器 > 软解
     * 2，HLG、HDR10 等HDR视频 ：hevc 解码器 > 软解
     */
    val meicamHardwareDecoderConfig = DecoderConfig(DecoderVendor.MEICAM, DecoderType.HARD)

    /**
     * meicamHardwareDolbyGpuDecoderConfig
     * 支持杜比视频GPU方案
     */
    val meicamHardwareDolbyGpuDecoderConfig = DecoderConfig(DecoderVendor.MEICAM, DecoderType.HARD_WITH_DOLBY_GPU)

    /**
     * meicamHardwareDolbyGpuWith10BitDecoderConfig
     * 支持杜比视频GPU+P010格式输出方案
     */
    val meicamHardwareDolbyGpuWithP010DecoderConfig = DecoderConfig(DecoderVendor.MEICAM, DecoderType.HARD_WITH_DOLBY_GPU_P010)

    /**
     * 美摄软解
     * 1，杜比视频 ： 杜比解码器 > hevc 解码器 > 软解
     * 2，HLG、HDR10 等HDR视频 ：hevc 解码器 > 软解
     */
    val meicamSoftwareDecoderConfig = DecoderConfig(DecoderVendor.MEICAM, DecoderType.SOFT)

    /**
     * 系统解码器
     */
    val systemDecoderConfig = DecoderConfig(DecoderVendor.SYSTEM)

    @JvmStatic
    fun getVideoThumbnailDecoder(
        config: DecoderConfig = tblDecoderConfig,
        isSingleDolbyCodeDevice: Boolean = false
    ): VideoThumbnailDecoder =
        when (config.vendor) {
            DecoderVendor.SYSTEM -> PlatformVideoThumbnailDecoder()
            DecoderVendor.TBL -> TBLVideoThumbnailDecoder()
            DecoderVendor.MEICAM -> {
                MeicamVideoThumbnailDecoder.setLimit(isSingleDolbyCodeDevice)
                MeicamVideoThumbnailDecoder(config.type)
            }
        }
}

/**
 * 解码配置
 */
data class DecoderConfig(
    /**
     * 解码厂商
     */
    val vendor: DecoderVendor,

    /**
     * 解码类型
     */
    val type: DecoderType = DecoderType.HARD
)

/**
 * 硬解配置
 */
data class HardwareDecoderConfig(
    /**
     * 支持杜比
     */
    val isSupportDolby: Boolean = false,
    /**
     * 支持杜比 GPU方案
     */
    val isSupportDolbyGPU: Boolean = false,
    /**
     * 支持杜比 GPU方案 + P010格式输出
     */
    val isSupportDolbyGPUWithP010: Boolean = false
)

enum class DecoderVendor {
    SYSTEM, TBL, MEICAM
}

/**
 * 解码类型
 */
enum class DecoderType {

    /**
     * 软解
     */
    SOFT,

    /**
     * 硬解
     */
    HARD,

    /**
     * 硬解 杜比 GPU
     */
    HARD_WITH_DOLBY_GPU,

    /**
     * 硬解 杜比 GPU + P010格式输出
     */
    HARD_WITH_DOLBY_GPU_P010
}