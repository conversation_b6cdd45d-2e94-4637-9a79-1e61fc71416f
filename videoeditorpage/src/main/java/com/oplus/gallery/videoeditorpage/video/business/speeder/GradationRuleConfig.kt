/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  GradationGapRule
 ** Description: 获取刻度规则，配置这些规则用于定义刻度的起始值、结束值以及刻度间的间距
 ** Version: 1.0
 ** Date : 2025/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/4/29      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.content.Context
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils
import com.oplus.gallery.videoeditorpage.widget.ruleview.GradationGapRule
import com.oplus.gallery.videoeditorpage.widget.ruleview.SpecialGradationRule


// 第一个刻度间隔规则的起始值（0.1x）
private const val FIRST_RULE_VALUE_START = 0.1f

// 第一个刻度间隔规则的结束值（1.0x）
private const val FIRST_RULE_VALUE_END = 1.0f

// 第二个刻度间隔规则的起始值（1.0x）
private const val SECOND_RULE_VALUE_START = 1.0f

// 第二个刻度间隔规则的结束值（10.0x）
private const val SECOND_RULE_VALUE_END = 10.0f

// 刻度宽的距离（dp）
private const val GRADATION_WIDE_GAP = 12.0f

// 刻度窄的距离（dp）
private const val GRADATION_NARROW_GAP = 8.0f

// 最小刻度值（用于特殊刻度规则）
private const val SPECIAL_GRADATION_VALUE_MIN = 0.1f

// 中间刻度值（用于特殊刻度规则）
private const val SPECIAL_GRADATION_VALUE_MIDDLE = 0.5f

/**
 * 获取刻度规则配置
 * 获取刻度规则，配置这些规则用于定义刻度的起始值、结束值以及刻度间的间距
 */
fun getGradationRules(context: Context): List<GradationGapRule> = listOf(
    GradationGapRule(
        //起始的刻度值
        FIRST_RULE_VALUE_START,
        //起始的刻度值
        FIRST_RULE_VALUE_END,
        //每一个刻度的间距（宽的）
        VideoUtils.dp2px(context, GRADATION_WIDE_GAP)
    ), GradationGapRule(
        //起始的刻度值
        SECOND_RULE_VALUE_START,
        //起始的刻度值
        SECOND_RULE_VALUE_END,
        //每一个刻度的间距窄的
        VideoUtils.dp2px(context, GRADATION_NARROW_GAP)
    )
)

/**
 * 最小刻度值将显示为长刻度并显示文本
 */
fun getSpecialGradationRules(): List<SpecialGradationRule> = listOf(
    SpecialGradationRule(SPECIAL_GRADATION_VALUE_MIN), SpecialGradationRule(SPECIAL_GRADATION_VALUE_MIDDLE)
)