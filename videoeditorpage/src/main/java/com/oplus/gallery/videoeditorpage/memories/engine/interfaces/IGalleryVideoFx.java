/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IGalleryVideoFx.java
 ** Description: for video editor fx.
 ** Version: 1.0
 ** Date : 2018/07/04
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                  <data>        <version>     <desc>
 **  <EMAIL>    2018/07/04    1.0           build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

import com.oplus.gallery.framework.abilities.videoedit.data.FxInfo;

import java.util.function.Function;

public interface IGalleryVideoFx {

    long getAppliedFxTime();

    void setAppliedFxTime(long time);

    boolean isVideoFxChanged();

    void setCurrentFxInfo(FxInfo fxInfo);

    FxInfo getCurrentFxInfo();

    void removeVideoFx();

    void moveVideoFx(long offsetUs);

    void moveVideoFx(Function<Long, Long> offsetCalculateFunction);

    void moveVideoFx(float speed);

    void clearLastFx();

    int installVideoFx(FxInfo fxInfo);

    void applyVideoFx(FxInfo fxInfo, long startPos);
}
