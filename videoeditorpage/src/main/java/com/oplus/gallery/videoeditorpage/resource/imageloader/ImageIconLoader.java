/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ImageIconLoader
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.thread.ThreadPool;
import com.oplus.gallery.videoeditorpage.utlis.Utils;
import com.oplus.gallery.videoeditorpage.resource.room.entity.FilterEntity;
import com.oplus.gallery.videoeditorpage.resource.room.entity.SongEntity;
import com.oplus.gallery.videoeditorpage.resource.room.entity.TransitionEntity;
import com.oplus.gallery.videoeditorpage.resource.util.ResourceUtils;

import java.io.IOException;
import java.io.InputStream;

public class ImageIconLoader extends BaseThumbnailLoader<Object> {

    private static final String TAG = "ImageIconLoader";
    private Context mContext;

    public ImageIconLoader(Context context) {
        super();
        mContext = context.getApplicationContext();
    }

    @Override
    protected ILoaderCallback<Object> buildThumbnailCallback() {

        return new ILoaderCallback<Object>() {

            @Override
            public Bitmap onThumbNailLoad(Object item, ThreadPool.JobContext jc) {
                Bitmap imageBitmap = null;
                if (item instanceof TransitionEntity) {
                    imageBitmap = BitmapFactory.decodeFile((((TransitionEntity) item).getIconPath()));
                } else if (item instanceof FilterEntity) {
                    if (((FilterEntity) item).getIsBuiltin() == ResourceUtils.BUILTIN_RESOURCE) {
                        InputStream in = null;
                        try {
                            in = mContext.getAssets().open(((FilterEntity) item).getIconPath());
                            imageBitmap = BitmapFactory.decodeStream(in);
                        } catch (IOException e) {
                            GLog.e(TAG, LogFlag.DL, "onThumbNailLoad, fail:" + e.getMessage());
                        } finally {
                            Utils.closeSilently(in);
                        }
                    } else {
                        imageBitmap = BitmapFactory.decodeFile((((FilterEntity) item).getIconPath()));
                    }
                } else if (item instanceof SongEntity) {
                    if (((SongEntity) item).getBuiltin() == ResourceUtils.BUILTIN_RESOURCE) {
                        InputStream in = null;
                        try {
                            in = mContext.getAssets().open(((SongEntity) item).getIconPath());
                            imageBitmap = BitmapFactory.decodeStream(in);
                        } catch (IOException e) {
                            GLog.e(TAG, LogFlag.DL, "onThumbNailLoad, fail:" + e.getMessage());
                        } finally {
                            Utils.closeSilently(in);
                        }
                    } else {
                        imageBitmap = BitmapFactory.decodeFile((((SongEntity) item).getIconPath()));
                    }
                }

                return imageBitmap;
            }

            @Override
            public void recycleBitmap(Bitmap bitmap) {
                GLog.e(TAG, LogFlag.DL, "[recycleBitmap] this method is empty");
            }

            @Override
            public String getDataUniqueTag(Object item) {
                return item.toString();
            }
        };
    }
}
