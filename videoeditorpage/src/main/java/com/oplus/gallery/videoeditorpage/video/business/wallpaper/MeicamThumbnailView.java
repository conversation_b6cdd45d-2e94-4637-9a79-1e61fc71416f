/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** OPLUS Coding Static Checking Skip
 ** File: - MeicamThumbnailView.java
 ** Description:thumbnail view extends NvsMultiThumbnailSequenceView.
 ** Version: 1.0
 ** Date : 2017/12/24
 ** Author:luyao.Tan@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** <EMAIL>       2017/12/24      1.0          build this module
 ** luyao.<PERSON>@Apps.Gallery       2023/07/03      2.0          添加coui动画效果 及 NvsMultiThumbnailSequenceView源码
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.wallpaper;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsIconGenerator;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter;
import com.oplus.gallery.videoeditorpage.widget.RoundedCropImageView;
import com.oplus.gallery.videoeditorpage.widget.GalleryHorizontalScrollView;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;


/**
 * ! \if ENGLISH
 *   \brief Multiple thumbnail sequence
 *
 *   A multi-thumbnail sequence displays a sequence of thumbnails of multiple segments within
 *  a timeline. It supports the adjustment of the thumbnail time scale, and supports scrolling
 * when the effective content is too long.
 *   \warning In the MeicamThumbnailView class, all public APIs are used in the UI thread! ! !
 *   \else
 *   \brief 多缩略图序列
 *
 *   多缩略图序列，可以显示一个时间线内多个片段的缩略图序列。支持缩略图时间比例尺的调节，当有效内容超长时支持滚动浏览。
 *   \warning NvsMultiThumbnailSequenceView类中，所有public API都在UI线程使用！！！
 *   \endif
 *   \since 1.10.0
 */
public class MeicamThumbnailView extends GalleryHorizontalScrollView
        implements NvsIconGenerator.IconCallback {

    /** !< \if ENGLISH Image zoom to fill the full window without maintaining
    the original scale (default mode)
     else 图片缩放来填充满窗口，不保持原比例，可能会导致图片变形 (默认模式) \endif
     */
    public static final int THUMBNAIL_IMAGE_FILLMODE_STRETCH = 0;

    /** !< \if ENGLISH The image fills the full window evenly and scales if necessary
      else 图片按比例均匀填充满窗口，必要时进行裁剪 \endif
     */
    public static final int THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP = 1;

    private static final String TAG = "MeicamThumbnailView";
    /**
     * 时间偏移量, 用于计算当前缩略图所在的时间，这个偏移量是历史逻辑，不太明白为什么要加0.5
     */
    private static final double CALCULATE_TIMESTAMP_OFFSET = 0.5;
    /**
     * 图片圆角初始值
     */
    private static final float NOT_ROUND_RADIUS = 0F;

    // These two flags control the cached keyframe only mode and whether it is still valid
    private static final int THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY = 1;
    private static final int THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID = 2;

    /* !< \if ENGLISH Support HDR video to SDR through tonemapping
     else 支持HDR视频通过色彩映射到SDR \endif
     */
    private static final int THUMBNAIL_SEQUENCE_FLAGS_HDR_TONEMAPPING_SUPPORTED = 4;


    private NvsIconGenerator mIconGenerator = null;
    private boolean mScrollEnabled = true;
    /**
     * 每张缩略图的圆角大小
     */
    private float mImageViewRoundRadius = -1f;


    /**
     * 用于监听水平滚动的接口
     * */
    public interface OnScrollChangeListener {
        void onScrollChanged(MeicamThumbnailView view, int x, int oldx);
    }

    private OnScrollChangeListener mScrollChangeListener;

    /**
     * ! \if ENGLISH
     *   \brief Multi-thumbnail sequence information description
     *   \else
     *   \brief 多缩略图序列信息描述
     *   \endif
     *   \since 1.10.0
     */
    public static class ThumbnailSequenceDesc {
        //!< \if ENGLISH Video file path \else 视频文件路径 \endif
        public String mediaFilePath;

        //!< \if ENGLISH Timeline in point (in microseconds) \else 时间线上入点(单位微秒) \endif
        public long inPoint;

        //!< \if ENGLISH Timeline out point (in microseconds) \else 时间线上出点(单位微秒) \endif
        public long outPoint;

        //!< \if ENGLISH Trim in point (in microseconds) \else 裁剪入点(单位微秒) \endif
        public long trimIn;

        //!< \if ENGLISH Trim out point (in microseconds) \else 裁剪出点(单位微秒) \endif
        public long trimOut;

        //!< \if ENGLISH Whether it is a static picture \else 是否是静态图片 \endif
        public boolean stillImageHint;

        //!< \if ENGLISH Whether decode only key frames \else 是否是只解码关键帧 \endif
        public boolean onlyDecodeKeyFrame;

        //!< \if ENGLISH Whether support hdr video file \else 是否支持HDR视频显示 \endif
        public boolean supportHdr;

        /** !< \if ENGLISH Thumbnail's aspect ratio of this sequence, 0 means comply with
         * the thumbnail's aspect ratio of the view \else 当前序列的缩略图横纵比，
         * 为0表示使用控件的缩略图横纵比 \endif
         */
        public float thumbnailAspectRatio;

        public ThumbnailSequenceDesc() {
            inPoint = 0;
            outPoint = 4000000;
            trimIn = 0;
            trimOut = 4000000;
            stillImageHint = false;
            onlyDecodeKeyFrame = false;
            supportHdr = false;
            thumbnailAspectRatio = 0;
        }
    }

    private ArrayList<ThumbnailSequenceDesc> mDescArray;
    private float mThumbnailAspectRatio = 9.0f / 16;
    private double mPixelPerMicrosecond = 1080.0 / 15000000;
    private int mStartPadding = 0;
    private int mEndPadding = 0;
    private int mThumbnailImageFillMode = THUMBNAIL_IMAGE_FILLMODE_STRETCH;
    private long mMaxTimelinePosToScroll = 0;

    private static class ThumbnailSequence {
        public float mThumbnailAspectRatio;
        int mIndex;
        String mMediaFilePath;
        long mInPoint;
        long mOutPoint;
        long mTrimIn;
        long mTrimDuration;
        boolean mStillImageHint;
        boolean mOnlyDecodeKeyFrame;
        boolean mSupporthdr;

        int mFlags;

        int mX; // Relative to content view
        int mWidth;
        int mThumbnailWidth;

        public ThumbnailSequence() {
            mIndex = 0;
            mInPoint = 0;
            mOutPoint = 0;
            mTrimIn = 0;
            mTrimDuration = 0;
            mStillImageHint = false;
            mOnlyDecodeKeyFrame = false;
            mSupporthdr = false;
            mThumbnailAspectRatio = 0;
            mFlags = 0;
            mX = 0;
            mWidth = 0;
            mThumbnailWidth = 0;
        }

        public long calcTimestampFromX(int x) {
            return mTrimIn + (long) Math.floor((double) (x - mX) / mWidth * mTrimDuration + 0.5);
        }

        /**
         * 计算一张缩略图宽度所占的时间
         *
         * @param thumbnailWidth 一张缩略图的宽
         * @return 一张缩略图宽度对应的时间
         */
        public long calcUnitTimestampFromX(int thumbnailWidth) {
            return (long) Math.floor((double) thumbnailWidth / mWidth * mTrimDuration + CALCULATE_TIMESTAMP_OFFSET);
        }
    }

    private ArrayList<ThumbnailSequence> mThumbnailSequenceArray = new ArrayList<ThumbnailSequence>();
    private TreeMap<Integer, ThumbnailSequence> mThumbnailSequenceMap = new TreeMap<Integer, ThumbnailSequence>();
    private int mContentWidth = 0;

    private static class ThumbnailId implements Comparable<ThumbnailId> {
        public int mSeqIndex;
        public long mTimestamp;

        public ThumbnailId(int seqIndex, long timestamp) {
            mSeqIndex = seqIndex;
            mTimestamp = timestamp;
        }

        @Override
        public int compareTo(ThumbnailId o) {
            if (mSeqIndex < o.mSeqIndex) {
                return -1;
            } else if (mSeqIndex > o.mSeqIndex) {
                return 1;
            } else {
                if (mTimestamp < o.mTimestamp)
                    return -1;
                else if (mTimestamp > o.mTimestamp)
                    return 1;
                else
                    return 0;
            }
        }
    }

    private static class Thumbnail {
        ThumbnailSequence mOwner;
        long mTimestamp;
        ImageView mImageView;
        long mIconTaskId;
        boolean mImageViewUpToDate;
        boolean mTouched;

        public Thumbnail() {
            mTimestamp = 0;
            mIconTaskId = 0;
            mImageViewUpToDate = false;
            mTouched = false;
        }
    }

    private TreeMap<ThumbnailId, Thumbnail> mThumbnailMap = new TreeMap<ThumbnailId, Thumbnail>();
    Bitmap mPlaceholderBitmap;
    private int mMaxThumbnailWidth = 0;
    private boolean mUpdatingThumbnail = false;

    private class ContentView extends ViewGroup {
        public ContentView(Context context) {
            super(context);
        }

        /**
         * Any layout manager that doesn't scroll will want this.
         */
        @Override
        public boolean shouldDelayChildPressedState() {
            return false;
        }

        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            // NOTE: At the time our size is being measured
            // The content width may not be ready!
            int w = mContentWidth, h;

            int heightMode = MeasureSpec.getMode(heightMeasureSpec);
            int heightSize = MeasureSpec.getSize(heightMeasureSpec);
            if (heightMode == MeasureSpec.EXACTLY || heightMode == MeasureSpec.AT_MOST)
                h = heightSize;
            else
                h = MeicamThumbnailView.this.getHeight(); // Shouldn't reach here

            // Check against our minimum height and width
            w = Math.max(w, getSuggestedMinimumWidth());
            h = Math.max(h, getSuggestedMinimumHeight());

            w = resolveSizeAndState(w, widthMeasureSpec, 0);
            h = resolveSizeAndState(h, heightMeasureSpec, 0);

            setMeasuredDimension(w, h);
        }

        @Override
        protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
            updateThumbnails();
        }

        @Override
        protected void onSizeChanged(int w, int h, int oldw, int oldh) {
            if (h != oldh)
                requestUpdateThumbnailSequenceGeometry();

            super.onSizeChanged(w, h, oldw, oldh);
        }
    }

    private ContentView mContentView;

    /* --------- 以下属性为在 NvsMultiThumbnailSequenceView 的基础上添加的代码 start -------------- */

    private OnWidthChangeListener mOnWidthChangeListener;
    private int mLastWidth;
    private boolean mSizeChange = false;

    /* --------- 在 NvsMultiThumbnailSequenceView 的基础上添加的代码 end  -------------- */

    public MeicamThumbnailView(Context context) {
        super(context);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MeicamThumbnailView(Context context, AttributeSet attrs) {
        super(context, attrs);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MeicamThumbnailView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MeicamThumbnailView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    /*! \if ENGLISH
     *   \brief Sets the thumbnail sequence description array
     *   \param descArray The thumbnail sequence describes the array. Note: Once it is set, modifying the contents of the array will not work unless thumbnail sequence description is set array again.
     *   \else
     *   \brief 设置缩略图序列描述数组
     *   \param descArray 缩略图序列描述数组。注意：一旦设置，再修改数组里面的内容是不起作用的，除非再次设置缩略图序列描述数组
     *   \endif
     *   \sa getThumbnailSequenceDescArray
     */
    public void setThumbnailSequenceDescArray(ArrayList<ThumbnailSequenceDesc> descArray) {
        NvsUtils.checkFunctionInMainThread();
        if (descArray == mDescArray)
            return;

        clearThumbnailSequences();
        mPlaceholderBitmap = null;

        mDescArray = descArray;
        if (descArray != null) {
            int index = 0;
            long lastOutPoint = 0;
            for (ThumbnailSequenceDesc desc : descArray) {
                if (desc.mediaFilePath == null ||
                    desc.inPoint < lastOutPoint || desc.outPoint <= desc.inPoint ||
                    desc.trimIn < 0 || desc.trimOut <= desc.trimIn) {
                    Log.e(TAG, "Invalid ThumbnailSequenceDesc!");
                    continue;
                }

                ThumbnailSequence thumbnailSequence = new ThumbnailSequence();
                thumbnailSequence.mIndex = index++;
                thumbnailSequence.mMediaFilePath = desc.mediaFilePath;
                thumbnailSequence.mInPoint = desc.inPoint;
                thumbnailSequence.mOutPoint = desc.outPoint;
                thumbnailSequence.mTrimIn = desc.trimIn;
                thumbnailSequence.mTrimDuration = desc.trimOut - desc.trimIn;
                thumbnailSequence.mStillImageHint = desc.stillImageHint;
                thumbnailSequence.mOnlyDecodeKeyFrame = desc.onlyDecodeKeyFrame;
                thumbnailSequence.mSupporthdr = desc.supportHdr;
                thumbnailSequence.mThumbnailAspectRatio = desc.thumbnailAspectRatio;

                mThumbnailSequenceArray.add(thumbnailSequence);

                lastOutPoint = desc.outPoint;
            }
        }

        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the thumbnail sequence description array
     *   \return Returns the obtained thumbnail sequence description array.
     *   \else
     *   \brief 获取缩略图序列描述数组
     *   \return 返回获取的缩略图序列描述数组
     *   \endif
     *   \sa setThumbnailSequenceDescArray
     */
    public ArrayList<ThumbnailSequenceDesc> getThumbnailSequenceDescArray() {
        return mDescArray;
    }


    /*! \if ENGLISH
     *   \brief Sets the image fill mode of the thumbnail,default:THUMBNAIL_IMAGE_FILLMODE_STRETCH
     *   \param fillMode [image fill mode] (@ref THUMBNAIL_IMAGE_FILLMODE)
     *   \else
     *   \brief 设置缩略图的图片填充模式，默认值：THUMBNAIL_IMAGE_FILLMODE_STRETCH
     *   \param fillMode [图片填充模式] (@ref THUMBNAIL_IMAGE_FILLMODE)
     *   \endif
     *   \sa getThumbnailImageFillMode
     */
    public void setThumbnailImageFillMode(int fillMode) {
        NvsUtils.checkFunctionInMainThread();
        if (mThumbnailImageFillMode != THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP &&
            mThumbnailImageFillMode != THUMBNAIL_IMAGE_FILLMODE_STRETCH) {
            mThumbnailImageFillMode = THUMBNAIL_IMAGE_FILLMODE_STRETCH;
        }

        if (mThumbnailImageFillMode == fillMode)
            return;

        mThumbnailImageFillMode = fillMode;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the image fill mode of the thumbnail
     *   \return Returns the obtained image fill mode of the thumbnail
     *   \else
     *   \brief 获取缩略图的图片填充模式
     *   \return 返回获取的缩略图的图片填充模式
     *   \endif
     *   \sa setThumbnailImageFillMode
     */
    public int getThumbnailImageFillMode() {
        return mThumbnailImageFillMode;
    }

    /*! \if ENGLISH
     *   \brief Sets thumbnail aspect ratio,default:9.0f / 16
     *   \param thumbnailAspectRatio aspect ratio
     *   \else
     *   \brief 设置缩略图横纵比，默认值：9.0f / 16
     *   \param thumbnailAspectRatio 横纵比
     *   \endif
     *   \sa getThumbnailAspectRatio
     */
    public void setThumbnailAspectRatio(float thumbnailAspectRatio) {
        NvsUtils.checkFunctionInMainThread();
        if (thumbnailAspectRatio < 0.1f)
            thumbnailAspectRatio = 0.1f;
        else if (thumbnailAspectRatio > 10)
            thumbnailAspectRatio = 10;

        if (Math.abs(mThumbnailAspectRatio - thumbnailAspectRatio) < 0.001f)
            return;

        mThumbnailAspectRatio = thumbnailAspectRatio;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets thumbnail aspect ratio.
     *   \return Returns the thumbnail aspect ratio.
     *   \else
     *   \brief 获取缩略图横纵比
     *   \return 返回缩略图横纵比值
     *   \endif
     *   \sa setThumbnailAspectRatio
     */
    public float getThumbnailAspectRatio() {
        return mThumbnailAspectRatio;
    }

    /*! \if ENGLISH
     *   \brief Sets the scale,default:1080.0 / 15000000
     *   \param pixelPerMicrosecond The number of pixels per subtle
     *   \else
     *   \brief 设置比例尺，默认值：1080.0 / 15000000
     *   \param pixelPerMicrosecond 每微妙所占用的像素数
     *   \endif
     *   \sa getPixelPerMicrosecond
     */
    public void setPixelPerMicrosecond(double pixelPerMicrosecond) {
        NvsUtils.checkFunctionInMainThread();
        if (pixelPerMicrosecond <= 0 || (Double.compare(pixelPerMicrosecond, mPixelPerMicrosecond) == 0))
            return;

        mPixelPerMicrosecond = pixelPerMicrosecond;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the current scale.
     *   \return Returns the number of pixels per subtle.
     *   \else
     *   \brief 获取当前比例尺
     *   \return 返回每微妙所占用的像素数
     *   \endif
     *   \sa setPixelPerMicrosecond
     */
    public double getPixelPerMicrosecond() {
        return mPixelPerMicrosecond;
    }

    /*! \if ENGLISH
     *   \brief Sets the starting padding.
     *   \param startPadding Starting padding(in pixels)
     *   \else
     *   \brief 设置起始边距
     *   \param startPadding 起始边距（单位是像素）
     *   \endif
     *   \sa getStartPadding
     */
    public void setStartPadding(int startPadding) {
        NvsUtils.checkFunctionInMainThread();
        if (startPadding < 0 || startPadding == mStartPadding)
            return;

        mStartPadding = startPadding;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the current starting padding.
     *   \return Returns the starting padding(in pixels).
     *   \else
     *   \brief 获取当前起始边距
     *   \return 返回起始边距（单位是像素）
     *   \endif
     *   \sa setStartPadding
     */
    public int getStartPadding() {
        return mStartPadding;
    }

    /*! \if ENGLISH
     *   \brief Sets end padding.
     *   \param endPadding Ends padding(in pixels)
     *   \else
     *   \brief 设置结束边距。
     *   \param endPadding 结束边距（单位为像素）
     *   \endif
     *   \sa getEndPadding
     */
    public void setEndPadding(int endPadding) {
        NvsUtils.checkFunctionInMainThread();
        if (endPadding < 0 || endPadding == mEndPadding)
            return;

        mEndPadding = endPadding;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the current ending padding.
     *   \return Returns the ending padding(in pixels)
     *   \else
     *   \brief 获取当前结束边距。
     *   \return 返回结束边距，单位为像素
     *   \endif
     *   \sa setEndPadding
     */
    public int getEndPadding() {
        return mEndPadding;
    }

    /*! \if ENGLISH
     *   \brief Sets the maximum timeline position that allows scrolling.
     *   \param maxTimelinePosToScroll The maximum timeline position that is allowed to scroll(in microseconds).
     *   \else
     *   \brief 设置允许滚动的最大时间线位置
     *   \param maxTimelinePosToScroll 允许滚动的最大时间线位置，单位为微秒
     *   \endif
     *   \sa getMaxTimelinePosToScroll
     *   \since 1.17.0
     */
    public void setMaxTimelinePosToScroll(int maxTimelinePosToScroll) {
        NvsUtils.checkFunctionInMainThread();
        maxTimelinePosToScroll = Math.max(maxTimelinePosToScroll, 0);
        if (maxTimelinePosToScroll == mMaxTimelinePosToScroll)
            return;

        mMaxTimelinePosToScroll = maxTimelinePosToScroll;

        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the maximum timeline position that allows scrolling.
     *   \return Returns the maximum timeline position that is allowed to scroll(in microseconds).
     *   \else
     *   \brief 获取允许滚动的最大时间线位置
     *   \return 返回允许滚动的最大时间线位置，单位为微秒
     *   \endif
     *   \sa setMaxTimelinePosToScroll
     *   \since 1.17.0
     */
    public long getMaxTimelinePosToScroll() {
        return mMaxTimelinePosToScroll;
    }

    /*! \if ENGLISH
     *   \brief Maps the X coordinate of the control to the timeline position.
     *   \param x The X coordinate of the control(in pixels)
     *   \return Returns the timeline position of the map(in microseconds).
     *   \else
     *   \brief 将控件的X坐标映射到时间线位置
     *   \param x 控件的X坐标（单位为像素）
     *   \return 返回映射的时间线位置（单位为微秒）
     *   \endif
     *   \sa mapXFromTimelinePos
     */
    public long mapTimelinePosFromX(int x) {
        NvsUtils.checkFunctionInMainThread();
        final int scrollX = getScrollX();
        x = x + scrollX - mStartPadding;
        final long timelinePos = (long) Math.floor(x / mPixelPerMicrosecond + 0.5);
        return timelinePos;
    }

    /*! \if ENGLISH
     *   \brief Maps the timeline position to the X coordinate of the control.
     *   \param timelinePos Timeline position(in microseconds)
     *   \return Returns the X coordinate of the mapped control(in pixels).
     *   \else
     *   \brief 将时间线位置映射到控件的X坐标
     *   \param timelinePos 时间线位置（单位为微秒）
     *   \return 返回映射的控件的X坐标（单位为像素）
     *   \endif
     *   \sa mapTimelinePosFromX
     */
    public int mapXFromTimelinePos(long timelinePos) {
        NvsUtils.checkFunctionInMainThread();
        int x = (int) Math.floor(timelinePos * mPixelPerMicrosecond + 0.5);
        final int scrollX = getScrollX();
        return x + mStartPadding - scrollX;
    }

    /*! \if ENGLISH
     *   \brief Zooms the current scale.
     *   \param scaleFactor Scale ratio
     *   \param anchorX Scaled anchor X coordinate(in pixels).
     *   \else
     *   \brief 缩放当前比例尺
     *   \param scaleFactor 缩放的比例
     *   \param anchorX 缩放的锚点X坐标（单位为像素）
     *   \endif
     */
    public void scaleWithAnchor(double scaleFactor, int anchorX) {
        NvsUtils.checkFunctionInMainThread();
        if (scaleFactor <= 0)
            return;

        final long anchorTimelinePos = mapTimelinePosFromX(anchorX);
        mPixelPerMicrosecond *= scaleFactor;

        updateThumbnailSequenceGeometry();
        final int newAnchorX = mapXFromTimelinePos(anchorTimelinePos);
        final int scrollX = getScrollX() + newAnchorX - anchorX;
        // According to android developer document, this version of scrollTo()
        // also clamps the scrolling to the bounds of our child.
        scrollTo(scrollX, 0);
    }

    /*! \if ENGLISH
     *   \brief Sets the scroll listener interface.
     *   \param listener Rolling monitor interface
     *   \else
     *   \brief 设置滚动监听接口
     *   \param listener 滚动监听接口
     *   \endif
     *   \sa getOnScrollChangeListenser
     */
    public void setOnScrollChangeListenser(OnScrollChangeListener listener) {
        NvsUtils.checkFunctionInMainThread();
        mScrollChangeListener = listener;
    }

    /**
     * 缩图轴设置圆角
     *
     * @param radius 圆角大小
     */
    public void setRoundedRadius(float radius) {
        mImageViewRoundRadius = radius;
    }

    /*! \if ENGLISH
     *   \brief Gets the current scrolling listener interface.
     *   \return Returns the current scrolling listener interface.
     *   \else
     *   \brief 获取当前滚动监听接口
     *   \return 返回当前滚动监听接口
     *   \endif
     *   \sa setOnScrollChangeListenser
     */
    public OnScrollChangeListener getOnScrollChangeListenser() {
        NvsUtils.checkFunctionInMainThread();
        return mScrollChangeListener;
    }

    /**
     * ! \if ENGLISH
     *   \brief Sets whether to start scroll preview.
     *   \param enable Whether to start scroll preview.
     *   \else
     *   \brief 设置是否开启滚动预览
     *   \param enable 是否开启滚动预览
     *   \endif
     *   \sa getScrollEnabled
     *   \since 1.11.0
     */
    public void setScrollEnabled(boolean enable) {
        mScrollEnabled = enable;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        mSizeChange = (w != oldw) || (h != oldh);
        super.onSizeChanged(w, h, oldw, oldh);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        /*---------- 以下为在 NvsMultiThumbnailSequenceView 的基础上添加的代码 start ---------- */

        if (left >= right || top >= bottom) {
            return;
        }
        /*
         * bug修复：
         * 1.美摄原有View(NvsMultiThumbnailSequenceView)每次滚动基本都会触发layout
         * 2.原生HorizontalScrollView layout后期内容的偏移量最大只允许在 content 宽度范围
         * 3.涉及需要有极限反馈动效，其overScroll阶段是超过content 宽度范围的，
         *
         * 导致：滑动/overScroll 时，概率的位置异常，即：手指拖动时，突然就不跟手了；overScroll时一直跳闪动
         *
         * 详细判断见：isNeedCallSuperOnLayout()
         */
        if (isNeedCallSuperOnLayout(changed)) {
            mSizeChange = false;
            super.onLayout(changed, left, top, right, bottom);
        }

        /*
         * 原有逻辑保留
         */
        if ((mOnWidthChangeListener != null) && changed && (mLastWidth != right - left)) {
            mLastWidth = right - left;
            mOnWidthChangeListener.onWidthChange(mLastWidth);
            GLog.d(TAG, "onLayout width changed! width = " + mLastWidth);
        }

        /*---------- 以下为在 NvsMultiThumbnailSequenceView 的基础上添加的代码 end --------- */
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

        if (!isInEditMode()) {
            mIconGenerator = new NvsIconGenerator();
            mIconGenerator.setIconCallback(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelIconTask();

        mScrollChangeListener = null;

        if (mIconGenerator != null) {
            mIconGenerator.release();
            mIconGenerator = null;
        }

        super.onDetachedFromWindow();
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (mScrollChangeListener != null) {
            mScrollChangeListener.onScrollChanged(this, l, oldl);
        }
        updateThumbnails();
    }

    /*! \cond */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (mScrollEnabled) {
            return super.onInterceptTouchEvent(ev);
        } else {
            return false;
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (mScrollEnabled) {
            return super.onTouchEvent(ev);
        } else {
            return false;
        }
    }

    private void init(Context context) {
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);

        // Create the internal content view
        mContentView = new ContentView(context);
        addView(mContentView, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT));
    }

    private void requestUpdateThumbnailSequenceGeometry() {
        new Handler().post(new Runnable() {
            @Override
            public void run() {
                updateThumbnailSequenceGeometry();
            }
        });
    }

    private void updateThumbnailSequenceGeometry() {
        cancelIconTask();

        // Clear thumbnails since their geometry is subject to change
        clearThumbnails();

        // Calculate thumbnail width in pixel
        final int h = getHeight();
        if (h == 0)
            return;

        mThumbnailSequenceMap.clear();

        int lastX = mStartPadding;
        mMaxThumbnailWidth = 0;
        for (ThumbnailSequence thumbnailSequence : mThumbnailSequenceArray) {
            // Mark cached keyframe only mode as invalid
            thumbnailSequence.mFlags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;

            final int x = (int) Math.floor(thumbnailSequence.mInPoint * mPixelPerMicrosecond + 0.5) + mStartPadding;
            final int x2 = (int) Math.floor(thumbnailSequence.mOutPoint * mPixelPerMicrosecond + 0.5) + mStartPadding;
            if (x2 <= x) {
                // For current scale ratio, this thumbnail sequence can't be represented, just ignore it
                continue;
            }

            thumbnailSequence.mX = x;
            thumbnailSequence.mWidth = x2 - x;

            // Calculate thumbnail width in pixel
            final float thumbnailAspectRatio = thumbnailSequence.mThumbnailAspectRatio > 0 ?
                thumbnailSequence.mThumbnailAspectRatio : mThumbnailAspectRatio;
            thumbnailSequence.mThumbnailWidth = (int) Math.floor(h * thumbnailAspectRatio + 0.5);
            thumbnailSequence.mThumbnailWidth = Math.max(thumbnailSequence.mThumbnailWidth, 1);
            mMaxThumbnailWidth = Math.max(thumbnailSequence.mThumbnailWidth, mMaxThumbnailWidth);

            mThumbnailSequenceMap.put(x, thumbnailSequence);

            lastX = x2;
        }

        // Update desired content (view) width
        int contentWidth = lastX;
        if (mMaxTimelinePosToScroll <= 0) {
            contentWidth += mEndPadding;
        } else {
            int len = (int) Math.floor(mStartPadding + mMaxTimelinePosToScroll * mPixelPerMicrosecond + 0.5f);
            if (len < contentWidth)
                contentWidth = len;
        }
        mContentWidth = contentWidth;

        mContentView.layout(0, 0, mContentWidth, getHeight());
        mContentView.requestLayout(); // updateThumbnails() will be called during layout

        if (getWidth() + getScrollX() > mContentWidth) {
            final int newScrollX = Math.max(getScrollX() - (getWidth() + getScrollX() - mContentWidth), 0);
            if (newScrollX != getScrollX())
                scrollTo(newScrollX, 0);
        }
    }

    private static class ClipImageView extends androidx.appcompat.widget.AppCompatImageView {
        private int mClipWidth;

        ClipImageView(Context ctx, int clipWidth) {
            super(ctx);
            mClipWidth = clipWidth;
        }

        @Override
        protected void onDraw(Canvas canvas) {
            canvas.clipRect(new Rect(0, 0, mClipWidth, getHeight()));
            super.onDraw(canvas);
        }
    }

    public void updateThumbnails() {
        if (mIconGenerator == null)
            return;

        if (mThumbnailSequenceMap.isEmpty()) {
            clearThumbnails();
            return;
        }

        final int guardLength = mMaxThumbnailWidth;
        final int scrollX = getScrollX();
        final int width = getWidth();
        final int visibleLeftBound = Math.max(scrollX - guardLength, mStartPadding);
        final int visibleRightBound = visibleLeftBound + width + guardLength;
        if (visibleRightBound <= visibleLeftBound) {
            clearThumbnails();
            return;
        }

        Integer startKey = mThumbnailSequenceMap.floorKey(visibleLeftBound);
        if (startKey == null)
            startKey = mThumbnailSequenceMap.firstKey();

        SortedMap<Integer, ThumbnailSequence> sortedMap = mThumbnailSequenceMap.tailMap(startKey);
        for (Map.Entry<Integer, ThumbnailSequence> entry : sortedMap.entrySet()) {
            ThumbnailSequence seq = entry.getValue();
            if (seq.mX + seq.mWidth < visibleLeftBound)
                continue;
            if (seq.mX >= visibleRightBound)
                break;

            int thumbnailX;
            if (seq.mX < visibleLeftBound)
                thumbnailX = seq.mX + (visibleLeftBound - seq.mX) / seq.mThumbnailWidth * seq.mThumbnailWidth;
            else
                thumbnailX = seq.mX;

            boolean outOfBound = false;
            final int seqEndX = seq.mX + seq.mWidth;
            while (thumbnailX < seqEndX) {
                if (thumbnailX >= visibleRightBound) {
                    outOfBound = true;
                    break;
                }

                int thumbnailWidth = seq.mThumbnailWidth;
                if (thumbnailX + thumbnailWidth > seqEndX)
                    thumbnailWidth = seqEndX - thumbnailX;

                // Calculate timestamp of this thumbnail
                final long timestamp = seq.calcTimestampFromX(thumbnailX);

                // Find the thumbnail from the current thumbnail map first
                ThumbnailId tid = new ThumbnailId(seq.mIndex, timestamp);
                Thumbnail thumbnail = mThumbnailMap.get(tid);
                if (thumbnail == null) {
                    // Create a new thumbnail
                    thumbnail = new Thumbnail();
                    thumbnail.mOwner = seq;
                    thumbnail.mTimestamp = timestamp;
                    thumbnail.mImageViewUpToDate = false;
                    thumbnail.mTouched = true;

                    mThumbnailMap.put(tid, thumbnail);
                    // 判断当前图片是否设置圆角
                    if (mImageViewRoundRadius > 0) {
                        // 视频无裁切 起始时间0 = m_trimIn；有裁切起始时间是 = m_trimIn
                        boolean isFirst = timestamp == seq.mTrimIn;
                        long totalTime = seq.mTrimIn + seq.mTrimDuration;
                        // 判断最后一张图片: 当前绘制到的时间 <= 总时间 且 当前绘制到的时间 + 每一张图片之间的时间间隔 >= 总时间
                        boolean isLast = totalTime - timestamp >= 0 && totalTime - timestamp <= seq.calcUnitTimestampFromX(thumbnailWidth);

                        RoundedCropImageView cropImageView = new RoundedCropImageView(this.getContext());
                        if (thumbnailWidth != seq.mThumbnailWidth) {
                            // 宽度不足支撑默认的宽度，就用计算得到的宽度
                            cropImageView.setCropWidth(thumbnailWidth);
                        }
                        // 给第一张图片设置左上左下的圆角
                        if (isFirst) {
                            cropImageView.setLeftTopRadius(mImageViewRoundRadius);
                            cropImageView.setLeftBottomRadius(mImageViewRoundRadius);
                        }
                        // 给最后一张图片设置右上右下的圆角
                        if (isLast) {
                            cropImageView.setRightTopRadius(mImageViewRoundRadius);
                            cropImageView.setRightBottomRadius(mImageViewRoundRadius);
                        }
                        thumbnail.mImageView = cropImageView;
                    } else {
                        if (thumbnailWidth == seq.mThumbnailWidth) {
                            thumbnail.mImageView = new ImageView(this.getContext());
                        } else {
                            thumbnail.mImageView = new ClipImageView(this.getContext(), thumbnailWidth);
                        }
                    }

                    if (mThumbnailImageFillMode == THUMBNAIL_IMAGE_FILLMODE_STRETCH) {
                        thumbnail.mImageView.setScaleType(ImageView.ScaleType.FIT_XY);
                    } else if (mThumbnailImageFillMode == THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP) {
                        thumbnail.mImageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                    }

                    mContentView.addView(thumbnail.mImageView);
                    thumbnail.mImageView.layout(thumbnailX, 0, thumbnailX + seq.mThumbnailWidth, mContentView.getHeight());
                } else {
                    thumbnail.mTouched = true;
                }

                thumbnailX += thumbnailWidth;
            }

            if (outOfBound)
                break;
        }

        // Remove untouched thumbnail objects and collect icons from cache
        mUpdatingThumbnail = true;

        boolean hasDirtyThumbnail = false;
        TreeMap<ThumbnailId, Bitmap> iconMap = new TreeMap<ThumbnailId, Bitmap>();
        Set<Map.Entry<ThumbnailId, Thumbnail>> thumbnailSet = mThumbnailMap.entrySet();
        Iterator<Map.Entry<ThumbnailId, Thumbnail>> itrThumbnail = thumbnailSet.iterator();
        while (itrThumbnail.hasNext()) {
            Map.Entry<ThumbnailId, Thumbnail> entry = itrThumbnail.next();
            Thumbnail thumbnail = entry.getValue();

            // Update placeholder bitmap
            if (thumbnail.mImageView != null) {
                Drawable drawable = thumbnail.mImageView.getDrawable();
                if (drawable != null) {
                    Bitmap bitmap = ((BitmapDrawable) drawable).getBitmap();
                    if (bitmap != null)
                        mPlaceholderBitmap = bitmap;
                }
            }

            if (!thumbnail.mTouched) {
                // These thumbnail hasn't been touched, remove it
                if (thumbnail.mIconTaskId != 0)
                    mIconGenerator.cancelTask(thumbnail.mIconTaskId);

                if (thumbnail.mImageView != null) {
                    mContentView.removeView(thumbnail.mImageView);
                }
                itrThumbnail.remove();
                continue;
            }

            // Reset touched flag for later use
            thumbnail.mTouched = false;

            if (thumbnail.mImageViewUpToDate) {
                if (thumbnail.mImageView != null) {
                    Bitmap bitmap = ((BitmapDrawable) thumbnail.mImageView.getDrawable()).getBitmap();
                    iconMap.put(entry.getKey(), bitmap);
                }
            } else {
                final long realTimestamp = thumbnail.mOwner.mStillImageHint ? 0 : thumbnail.mTimestamp;
                updateKeyframeOnlyModeForThumbnailSequence(thumbnail.mOwner);
                int flags = (thumbnail.mOwner.mFlags & THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY) != 0 ? 1 : 0;
                if ((thumbnail.mOwner.mFlags & THUMBNAIL_SEQUENCE_FLAGS_HDR_TONEMAPPING_SUPPORTED) != 0)
                    flags = flags | NvsIconGenerator.GET_ICON_FLAGS_HDR_TONEMAPPING_SUPPORT;
                Bitmap bitmap = mIconGenerator.getIconFromCache(thumbnail.mOwner.mMediaFilePath, realTimestamp, flags);
                if (bitmap != null) {
                    iconMap.put(entry.getKey(), bitmap);
                    if (setBitmapToThumbnail(bitmap, thumbnail)) {
                        thumbnail.mImageViewUpToDate = true;
                        thumbnail.mIconTaskId = 0;
                    }
                } else {
                    hasDirtyThumbnail = true;
                    thumbnail.mIconTaskId = mIconGenerator.getIcon(thumbnail.mOwner.mMediaFilePath, realTimestamp, flags);
                }
            }
        }

        mUpdatingThumbnail = false;

        if (!hasDirtyThumbnail)
            return;

        if (iconMap.isEmpty()) {
            // Now we set placeholder image to thumbnail whose ImageView was not up to date yet
            if (mPlaceholderBitmap != null) {
                for (Map.Entry<ThumbnailId, Thumbnail> entry : mThumbnailMap.entrySet()) {
                    Thumbnail thumbnail = entry.getValue();
                    if (!thumbnail.mImageViewUpToDate)
                        setBitmapToThumbnail(mPlaceholderBitmap, thumbnail);
                }
            }

            return;
        }

        // Now we set image to thumbnail whose ImageView was not up to date yet
        for (Map.Entry<ThumbnailId, Thumbnail> entry : mThumbnailMap.entrySet()) {
            Thumbnail thumbnail = entry.getValue();
            if (thumbnail.mImageViewUpToDate)
                continue;

            // We fail to find an image with the given timestamp value,
            // To make thumbnail sequence looks better we use an image whose
            // timestamp is close to the given timestamp
            Map.Entry<ThumbnailId, Bitmap> ceilingEntry = iconMap.ceilingEntry(entry.getKey());

            if (ceilingEntry != null)
                setBitmapToThumbnail(ceilingEntry.getValue(), thumbnail);
            else
                setBitmapToThumbnail(iconMap.lastEntry().getValue(), thumbnail);
        }
    }

    private void updateKeyframeOnlyModeForThumbnailSequence(ThumbnailSequence seq) {
        if (seq.mSupporthdr) {
            seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_HDR_TONEMAPPING_SUPPORTED;
        }

        if ((seq.mFlags & THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID) != 0)
            return;

        if (seq.mOnlyDecodeKeyFrame) {
            // We always respect the user's option
            seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY |
                THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
            return;
        }

        final long timeStep = Math.max((long) (seq.mThumbnailWidth / mPixelPerMicrosecond + 0.5), 1);
        final boolean keyFrameOnly = shouldDecodecKeyFrameOnly(seq.mMediaFilePath, timeStep);
        if (keyFrameOnly)
            seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY;
        else
            seq.mFlags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY;
        seq.mFlags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
    }

    private boolean shouldDecodecKeyFrameOnly(String filePath, long timeStep) {
        NvsStreamingContext streamingContext = MeicamContextAliveCounter.INSTANCE.requestContext(getContext(), TAG);
        if (streamingContext == null)
            return false;

        NvsAVFileInfo fileInfo = streamingContext.getAVFileInfo(filePath);
        if (fileInfo == null)
            return false;

        if (fileInfo.getVideoStreamCount() < 1)
            return false;

        NvsRational fps = fileInfo.getVideoStreamFrameRate(0);
        if (fps == null)
            return false;

        if (fps.den <= 0 || fps.num <= 0)
            return false;

        long videoDuration = fileInfo.getVideoStreamDuration(0);
        if (videoDuration < timeStep)
            return false;

        int keyframeInterval = streamingContext.detectVideoFileKeyframeInterval(filePath);
        if (keyframeInterval == 0)
            keyframeInterval = 30; // We can't detect its GOP size, just guess it
        else if (keyframeInterval == 1)
            return false; // Keyframe only, no need to use keyframe only mode

        int keyframeIntervalTime = (int) (keyframeInterval * ((double) fps.den / fps.num) * 1000000);
        if (keyframeInterval <= 30) {
            return false;
        } else if (keyframeInterval <= 60) {
            if (timeStep > keyframeIntervalTime * 0.8)
                return true;
        } else if (keyframeInterval <= 100) {
            if (timeStep > keyframeIntervalTime * 0.7)
                return true;
        } else if (keyframeInterval <= 150) {
            if (timeStep > keyframeIntervalTime * 0.5)
                return true;
        } else if (keyframeInterval <= 250) {
            if (timeStep > keyframeIntervalTime * 0.3)
                return true;
        } else {
            if (timeStep > keyframeIntervalTime * 0.2)
                return true;
        }

        return false;
    }

    private boolean setBitmapToThumbnail(Bitmap bitmap, Thumbnail thumbnail) {
        if (bitmap == null || thumbnail.mImageView == null)
            return false;

        thumbnail.mImageView.setImageBitmap(bitmap);
        return true;
    }

    private void clearThumbnailSequences() {
        cancelIconTask();
        clearThumbnails();

        mThumbnailSequenceArray.clear();
        mThumbnailSequenceMap.clear();
        mContentWidth = 0;
    }

    private void clearThumbnails() {
        for (Map.Entry<ThumbnailId, Thumbnail> entry : mThumbnailMap.entrySet())
            mContentView.removeView(entry.getValue().mImageView);

        mThumbnailMap.clear();
    }

    private void cancelIconTask() {
        if (mIconGenerator != null)
            mIconGenerator.cancelTask(0);
    }

    @Override
    public void onIconReady(Bitmap icon, long timestamp, long taskId) {
        if (!mUpdatingThumbnail) {
            updateThumbnails();
        } else {
            new Handler().post(new Runnable() {
                @Override
                public void run() {
                    updateThumbnails();
                }
            });
        }
    }


    /*---------------x----- 以下为在 NvsMultiThumbnailSequenceView 的基础上添加的代码 start -------------------- */


    private boolean isNeedCallSuperOnLayout(boolean isPositionChanged) {
        View child = getChildAt(0);
        boolean childWidthChanged = child.getWidth() != child.getMeasuredWidth();
        boolean childHeightChanged = child.getHeight() != child.getMeasuredHeight();
        boolean childSizeChanged = childWidthChanged || childHeightChanged;

        /*
         *  这里认为：
         *  1.MeicamThumbnailView 位置/大小改变时，需要layout
         *  2.MeicamThumbnailView 的 contentView 的大小发生变化时需要layout
         */
        boolean viewChanged = isPositionChanged || mSizeChange || childSizeChanged;

        /*
         * 缩小影响范围：
         * 只在overScroll阶段不layout，其它保持原逻辑
         */
        return !isOverScrolling() || viewChanged;
    }


    /**
     * 是否为overScrolling 阶段
     *
     * @return true/false
     */
    private boolean isOverScrolling() {
        if (!isLaidOut() || (getChildCount() <= 0)) {
            return false;
        }
        View child = getChildAt(0);
        boolean overScrollViewStart = getScrollX() < 0;
        boolean overScrollViewEnd = getScrollX() > Math.max(0, child.getWidth() - getScrollableRange());
        return overScrollViewStart || overScrollViewEnd;
    }

    /**
     * 设置宽度变更Listener
     */
    public void setOnWidthChangeListener(OnWidthChangeListener onWidthChangeListener) {
        mOnWidthChangeListener = onWidthChangeListener;
    }

    /**
     * 宽度变更Listener
     */
    public interface OnWidthChangeListener {
        void onWidthChange(int width);
    }

    /* --------- 在 NvsMultiThumbnailSequenceView 的基础上添加的代码 end  -------------- */
}