/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoUhdrEngine.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.uhdr

import android.content.Context
import android.graphics.Bitmap
import android.os.Build
import androidx.annotation.RequiresApi
import com.oplus.gallery.addon.graphics.toGainmap
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA
import com.oplus.gallery.foundation.codec.extend.ExtendDataUtils
import com.oplus.gallery.foundation.codec.extend.HdrTransformData
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct
import com.oplus.gallery.foundation.codec.extend.VideoTransformStruct
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest
import com.oplus.gallery.foundation.opengl.glcontext.GLContextRender
import com.oplus.gallery.foundation.opengl2.render.renderer.helper.RenderHelper
import com.oplus.gallery.foundation.opengl2.texture.BitmapTexture
import com.oplus.gallery.foundation.opengl2.texture.RawTexture
import com.oplus.gallery.foundation.opengl2.texture.TexConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3_HLG
import com.oplus.gallery.foundation.util.math.MathUtils.ZERO
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.output.task.GrabImageData
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 视频 UHDR 引擎实现层
 * @param context context
 * @param videoEngine 视频处理 engine 对象，封装对美摄的所有接口
 */
@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class VideoUhdrEngine(
    private val context: Context,
    private val videoEngine: EditorEngine
) : IUhdrEngine {
    /**
     * engine 类型，参照定义 UhdrEngineType
     */
    override var engineType: Int = UhdrEngineType.INVALID

    init {
        engineType = UhdrEngineType.VIDEO
    }

    override fun transformToUhdr(hdrBitmap: Bitmap): Bitmap? {
        val p3Hlg = DISPLAY_P3_HLG ?: return null
        return GLContextRender.runOnGLContext {
            val renderArgs = RenderHelper.obtainArgs()
            val hdrTex = BitmapTexture(hdrBitmap)

            // 将输入的 HLG 纹理 转换为P3_HLG纹理
            val outputHdrTexture = RawTexture(hdrTex.width, hdrTex.height, texConfig = TexConfig.RGBA_F16)
            RenderHelper.transformColorSpace(renderArgs, hdrTex, p3Hlg, outputHdrTexture)
            val pair = RenderHelper.transformToUhdr(
                renderArgs,
                getHdrTransformData(),
                outputHdrTexture
            ) ?: return@runOnGLContext null

            pair.first.toBitmap().apply {
                gainmap = (pair.second.metadataPack as? UHdrMetadataPack)?.metadata?.toGainmap(pair.second.toBitmap())
            }.also {
                GLog.d(TAG, LogFlag.DL) { "<transformToUhdr> bmpConfig=${it.config} colorSpace=${it.colorSpace}" }
                RenderHelper.release(renderArgs)
            }
        }
    }

    private fun getHdrTransformData(): HdrTransformData {
        var hdrTransformData = HdrTransformDataStruct.dolbyHdrTransformData()
        val filePath = getFirstClipFilePath()
        if (filePath.isNullOrEmpty().not()) {
            val openFileRequest = OpenFileRequest.Builder().apply {
                setImage(false)
                setFile(filePath)
            }.builder()
            FileAccessManager.getInstance().openFile(ContextGetter.context, openFileRequest)?.use { pfd ->
                ExtendDataUtils.getExtendData(
                    pfd,
                    EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA,
                    VideoTransformStruct::class.java
                )?.let {
                    hdrTransformData = hdrTransformData.copy(
                        gammaEnable = true,
                        hlgDstGammaTable = it.hlgGamma,
                        srgbDstGammaTable = it.srgbGamma
                    )
                    GLog.d(TAG, LogFlag.DL) { "<transformToUhdr> use gamma from camera" }
                }
            }
        }
        return hdrTransformData
    }

    /**
     * 获取视频轨首个视频文件路径
     */
    private fun getFirstClipFilePath(): String? {
        return videoEngine.currentTimeline?.let { timeline ->
            timeline.getVideoTrack(ZERO)?.let { videoTrack ->
                videoTrack.getClip(ZERO)?.let { clip ->
                    clip.filePath
                }
            }
        }
    }

    override suspend fun getHdrFrameFromSourceAsyncByGpu(timeUs: Long): Bitmap? {
        return suspendCancellableCoroutine { continuation ->
            ColorSpaceExt.BT2020_HLG?.let {
                val currentTimeline = videoEngine.currentTimeline
                if (currentTimeline == null) {
                    continuation.resume(null)
                    return@suspendCancellableCoroutine
                }

                // sdr的时间线
                val videoType = VideoParser.getInstance().getSupportVideoType()
                currentTimeline.grabImageFromTimelineAsync(
                    GrabImageData(
                        timeUs,
                        0,
                        videoType,
                        Bitmap.Config.RGBA_1010102,
                        it
                    )
                ) { bitmap, _ ->
                    continuation.resume(bitmap)
                }
            } ?: also {
                continuation.resume(null)
            }
        }
    }

    companion object {
        private const val TAG = "VideoUhdrEngine"
    }
}