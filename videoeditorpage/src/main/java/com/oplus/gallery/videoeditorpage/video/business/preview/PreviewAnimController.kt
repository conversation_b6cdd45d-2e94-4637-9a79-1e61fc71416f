/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorPreviewAnimController
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/21
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/21      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.preview

import android.graphics.PointF
import android.graphics.RectF
import androidx.lifecycle.LiveData
import com.oplus.gallery.foundation.opengl.transform.AxisAngleTransform
import com.oplus.gallery.foundation.opengl.transform.Pose
import com.oplus.gallery.foundation.ui.animationcontrol.ClipRectChangeListener
import com.oplus.gallery.foundation.ui.animationcontrol.ContentAnimationListener
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties

/**
 * 预览控件对外提供的动画控制接口
 */
interface PreviewAnimController {

    /**
     * 预览动画属性
     */
    val properties: PreviewAnimationProperties

    /**
     * 预览区域，是窗口中可以展示内容的区域，一般为窗口区域去除安全区的部分
     */
    val previewArea: LiveData<RectF>

    /**
     * 获取默认的初始状态
     */
    val defaultContentPose: Pose

    /**
     * 获取当前内容的变换状态（可能是动画中的状态）
     */
    val currentContentPose: Pose

    /**
     * 获取最终内容的变换状态（动画结束后的状态）
     */
    val finalContentPose: Pose

    /**
     * 获取最终内容的变换4*4矩阵（动画结束后的状态），View坐标系
     * 去除了初始偏移量，即初始时为单位矩阵
     */
    val finalContentTransformMatrixStartingIdentity: LiveData<FloatArray>

    /**
     * 获取最终裁剪框
     */
    val finalClipRect: RectF

    /**
     * 是否正在对内容进行动画
     */
    val isContentAnimating: LiveData<Boolean>

    /**
     * 在Seek完成前，禁用矩阵刷新时立即重绘
     */
    fun disableRepaintWhenMatrixChangeUntilNextSeek()

    /**
     * 设置手势交互的启用状态
     *
     * @param enabled true 表示启用手势交互，false 表示禁用手势交互
     */
    fun setGestureEnabled(enabled: Boolean)

    /**
     * 设置展示内容的大小
     *
     * @param width 宽度
     * @param height 高度
     * @param withAnimation 是否使用动画
     * @return 是否成功
     */
    fun setContentSize(width: Int, height: Int, withAnimation: Boolean): Boolean

    /**
     * 设置预览区域，作为内容的默认约束区域
     */
    fun setPreviewAreaMargins(margins: RectF, withAnimation: Boolean)

    /**
     * 通知裁剪框更新
     */
    fun notifyClipFrameChanged(clipRect: RectF, withAnimation: Boolean)

    /**
     * 添加裁剪区域更新监听器
     */
    fun addClipRectChangeListener(listener: ClipRectChangeListener)

    /**
     * 移除裁剪区域更新监听器
     */
    fun removeClipRectChangeListener(listener: ClipRectChangeListener)

    /**
     * 以([pivotX], [pivotY])为中心，旋转[degree]度
     */
    fun rotateBy(degree: Float, pivotX: Float, pivotY: Float, withAnimation: Boolean)

    /**
     * 以裁剪框为约束，旋转[degree]度
     */
    fun rotateByWithConstraint(degree: Float, constraintScale: Float, withAnimation: Boolean)

    /**
     * 缩放并居中
     */
    fun zoomToCenter(withAnimation: Boolean)

    /**
     * 水平镜像
     */
    fun mirrorHorizontal(withAnimation: Boolean)

    /**
     * 逆时针旋转朝向
     */
    fun anticlockwiseRotateOrientation(withAnimation: Boolean)

    /**
     * 对内容按[scale]倍数缩放
     *
     * @param scale 缩放倍数，>1表示放大，<1表示缩小，等值于1无变化
     * @param pivotX 缩放中心点x
     * @param pivotY 缩放中心点y
     * @param withAnimation 是否使用动画
     */
    fun scale(scale: Float, pivotX: Float, pivotY: Float, withAnimation: Boolean)

    /**
     * 复原到初始状态
     */
    fun revert(withAnimation: Boolean)

    /**
     * 使用不带初始变换（开始时为单位矩阵）的矩阵，更新内容变换矩阵
     *
     * @param matrix 变换矩阵(视图坐标系)，4*4矩阵
     * @param axisAngleTransform 旋转轴角变换
     * @param cropCenter 裁剪框中心点
     * @param withAnimation 是否使用动画
     */
    fun updateContentTransformWithStartingIdentity(
        matrix: FloatArray,
        axisAngleTransform: AxisAngleTransform,
        cropCenter: PointF,
        withAnimation: Boolean
    )
    
    /**
     * 回弹到裁剪框内
     */
    fun snapBack(withAnimation: Boolean)

    /**
     * 注册操作结束时的回调
     *
     * @param action 操作结束时要执行的回调
     */
    fun doOnOperationEnd(action: () -> Unit)

    /**
     * 添加内容动画监听器
     */
    fun addContentAnimationListener(listener: ContentAnimationListener)

    /**
     * 移除内容动画监听器
     */
    fun removeContentAnimationListener(listener: ContentAnimationListener)
}