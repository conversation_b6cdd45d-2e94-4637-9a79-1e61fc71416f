/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoMenuGifActionRule
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2023/2/7 10:29
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2023/2/7		  1.0		 PhotoMenuGifActionRule
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import android.app.Activity
import android.content.Intent
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.annotation.IdRes
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.GifSynthesisConstant.JUMP_FORM_CSHOT
import com.oplus.gallery.basebiz.constants.IntentConstant.GifSynthesisConstant.JUMP_FORM_OLIVE
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.PICTURE_FRAGMENT
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.findFullScreenContainerId
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business.contentloading.ContentFactory.Companion.LOAD_TYPE_PAGE_THUMBNAIL
import com.oplus.gallery.business.contentloading.SizeType
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisSpecificationChecker
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_ALBUM_DETAIL_CSHOT
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_GIF_SYNTHESIS
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant
import com.oplus.gallery.foundation.tracing.helper.CreationClickTrackHelper
import com.oplus.gallery.foundation.util.collections.ExtraMap
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.pagecontainer.PhotoContainerSection
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuScope
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 大图连拍页菜单GIF合成操作规则
 */
@Suppress("LongParameterList")
internal class PhotoMenuGifActionRule(
    @IdRes ruleAction: Int,
    viewModel: PhotoViewModel,
    private val fragment: BaseFragment,
    private val activity: BaseActivity,
    private val editType: PhotoEditorType,
    menuScope: MenuScope? = null,
    ruleResponseCodes: IntArray? = null,
    private val invokerToken: String = TextUtil.EMPTY_STRING
) : PhotoMenuActionRule(TAG, ruleAction, viewModel, ruleResponseCodes, menuScope) {
    /**
     * 大图连拍页是否处于锁屏模式
     */
    private var isLockMode: Boolean = false
    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        trackGifClick(MENU_ITEM_GIF_SYNTHESIS)
        //检查quick
        if (checkIfQuickLoading(viewData, mediaItem)) {
            GLog.d(TAG, LogFlag.DL) { "[execute] hasQuick, show toast" }
            ToastUtil.showShortToast(BasebizR.string.base_loading_photo_tip)
            onDone()
            return
        }

        activity.run {
            isLockMode = viewModel.pageManagement.isPageDisplayedAboveKeyguardPage.value ?: false
            doGifSynthesisEdit(mediaItem, onDone)
        }
    }

    /**
     * 埋点： 另存为GIF菜单项被点击。
     */
    private fun trackGifClick(menuItem: String) {
        trackMenuClick(menuItem)
        CreationClickTrackHelper.trackCreationClickUserAction(
            VideoEditorTrackConstant.Value.GENERATE_GIF,
            VideoEditorTrackConstant.Value.ITEM_ENTRANCE_BIG_PICTURE,
            VideoEditorTrackConstant.Value.CREATION_ENTRANCE_NONE
        )
    }

    private fun doGifSynthesisEdit(
        mediaItem: MediaItem,
        onDone: () -> Unit
    ) {
        when {
            DatabaseUtils.isCShotIdValid(mediaItem.cShotID) -> {
                GifSynthesisSpecificationChecker.checkCShotItemMatchGifRule(
                    scope = viewModel.viewModelScope,
                    cShotId = mediaItem.cShotID,
                    onFail = {
                        ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.gif_synthesis_cshot_add_action_waring_tip)
                    }
                ) {
                    viewModel.viewModelScope.launch(Dispatchers.UI) {
                        val paramMap = doPrepareGifSynthesisEditParamForCshot(mediaItem)
                        // 进入连拍大图-GIF合成页
                        doEnterGifEdit(paramMap, onDone)
                    }
                }
            }

            mediaItem.isOlivePhoto() -> {
                viewModel.cloudSync.triggerDownloadOriginal(
                    activity = activity,
                    scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_GIF_SYNTHESIS,
                    onDownloadOriginalFailAction = { _, _ -> onDone.invoke() },
                    onDownloadOriginalSuccessAction = {
                        GifSynthesisSpecificationChecker.checkOliveItemValid(activity, viewModel.viewModelScope, mediaItem,
                            onFail = {
                                ToastUtil.showShortToast(BasebizR.string.gif_synthesis_corrupted_file_cannot_save)
                                onDone.invoke()
                            }) {
                            viewModel.viewModelScope.launch(Dispatchers.UI) {
                                val paramMap = doPrepareGifSynthesisEditParamForOlive(mediaItem)
                                // 进入olive大图-GIF合成页
                                doEnterGifEdit(paramMap, onDone)
                            }
                        }
                    })
            }
        }
    }

    private fun doEnterGifEdit(paramMap: Map<String, Any?>, onDone: () -> Unit) {
        if (MeicamEngineLimiter.getInstance().allowCreateNewEngine().not()) {
            ToastUtil.showShortToast(R.string.photopage_menu_can_not_create_multiple_editor_and_export)
            GLog.d(TAG, LogFlag.DL, "[doEnterGifEdit] have exist a video engine, can't enter. ignore!")
            onDone()
            return
        }

        val enterGifEditor = fun() {
            MenuOperationManager.doAction(
                action = MenuAction.GIF_SYNTHESIS,
                paramMap = paramMap,
                onCompleted = { _, _ ->
                    //去除对应action
                    onDone()
                    viewModel.pageManagement.notifyTransferToGifPage()
                }
            )
        }
        // 进入GIF合成页，先隐藏大图的顶部和底部菜单
        PhotoDecorationState.Hide(
            PhotoDecorationState.DecorationAnimator(
                onEnd = {
                    // 菜单隐藏动画结束后，进入编辑
                    enterGifEditor()
                }
            ), shouldHideNaviBar = false, shouldCancelAnimator = true).let(viewModel.pageManagement::requestDecorationState)
    }

    private suspend fun doPrepareGifSynthesisEditParamForCshot(mediaItem: MediaItem): Map<String, Any?> {
        val (thumbnail: Drawable?, constraintDisplayRect: Rect?, contentDisplayRect: Rect?) = getThumbnailParam()
        return MenuActionGetter.gifSynthesis.builder
            .setFragment(fragment)
            .setImageUri(mediaItem.contentUri.toString())
            .setImagePath(mediaItem.path.toString())
            .setIsCShot(true)
            .setIsEditFromPhoto(true)
            .setInvalidImageCount(invalidImageCount = 0)
            .setThumbnail(thumbnail)
            .setConstraintDisplayRect(constraintDisplayRect)
            .setContentDisplayRect(contentDisplayRect)
            .setInvokerToken(invokerToken)
            .setJumpTye(JUMP_FORM_CSHOT)
            .setBestPicMediaId(mediaItem.mediaId)
            .setCShotId(mediaItem.cShotID)
            .setInLockedMode(isLockMode)
            .setChainFrom(viewModel.inputArguments.features.value?.chainFrom)
            .setOriginSetPath(PATH_ALBUM_DETAIL_CSHOT.getChild(mediaItem.cShotID).toString())
            .setLifecycle(fragment.lifecycle)
            .build()
    }

    private suspend fun doPrepareGifSynthesisEditParamForOlive(mediaItem: MediaItem): Map<String, Any?> {
        val (thumbnail: Drawable?, constraintDisplayRect: Rect?, contentDisplayRect: Rect?) = getThumbnailParam()
        return MenuActionGetter.gifSynthesis.builder
            .setFragment(fragment)
            .setImageUri(mediaItem.contentUri.toString())
            .setImagePath(mediaItem.path.toString())
            .setIsOlive(true)
            .setIsEditFromPhoto(true)
            .setThumbnail(thumbnail)
            .setConstraintDisplayRect(constraintDisplayRect)
            .setContentDisplayRect(contentDisplayRect)
            .setInvokerToken(invokerToken)
            .setJumpTye(JUMP_FORM_OLIVE)
            .setBestPicMediaId(mediaItem.mediaId)
            .setInLockedMode(isLockMode)
            .setChainFrom(viewModel.inputArguments.features.value?.chainFrom)
            .setOriginSetPath(viewModel.inputArguments.dataSource.value?.setPath ?: TextUtil.EMPTY_STRING)
            .setLifecycle(fragment.lifecycle)
            .build()
    }

    private suspend fun getThumbnailParam(): Triple<Drawable?, Rect?, Rect?> {
        var thumbnail: Drawable? = null
        var constraintDisplayRect: Rect? = null
        var contentDisplayRect: Rect? = null

        (fragment as? PhotoFragment)?.let {
            thumbnail = getThumbnailDrawable()

            val containerSection = fragment.requireSection<PhotoContainerSection>() ?: let {
                GLog.e(TAG) { "[doPrepareEditImageParam] why is containerSection null, no config? CHECK CODE!" }
                null
            }
            constraintDisplayRect = containerSection?.onGetPageConstraintRect?.invoke()
            contentDisplayRect = containerSection?.currentSlotContentRect
        }
        return Triple(thumbnail, constraintDisplayRect, contentDisplayRect)
    }

    private suspend fun getThumbnailDrawable() = suspendCoroutine<Drawable?> {
        viewModel.dataLoading.focusItemViewData?.let { viewData ->
            viewModel.contentLoading.startLoadContent(
                viewData = viewData,
                SizeType.FullThumb(LOAD_TYPE_PAGE_THUMBNAIL)
            ) { drawable ->
                it.resume(drawable)
            }
        }
    }

    override fun handleResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        GLog.d(TAG) { "[handleResult], requestCode = $requestCode  resultCode = $resultCode" }
        if (requestCode == MenuRequestCode.GIF_SYNTHESIS_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            handleResultRefresh(intent)
        }
        // 从GIF合成页返回，退出沉浸模式
        viewModel.pageManagement.changeImmersionInteractive(false)
        trackOliveToGifEvent(resultCode)
    }

    /**
     * 上报Olive转GIF的埋点信息，[resultCode]取自[Activity]中的定义
     * @param resultCode Int
     */
    private fun trackOliveToGifEvent(resultCode: Int) {
        val operationResult = when (resultCode) {
            Activity.RESULT_OK -> PictureTrackConstant.Value.MenuOperationResult.SUCCESS
            Activity.RESULT_CANCELED -> PictureTrackConstant.Value.MenuOperationResult.CANCEL
            else -> PictureTrackConstant.Value.MenuOperationResult.FAIL
        }
        val extraMap = ExtraMap().apply {
            this.putString(PictureTrackConstant.Key.PHOTO_OPERATION_RESULT, operationResult.resultCode)
        }
        trackMenuClick(PictureTrackConstant.Value.MENU_ITEM_EXPORT_OLIVE_TO_GIF, extraMap)
    }

    private fun handleResultRefresh(intent: Intent?) {
        GLog.d(TAG, "handleResultRefresh")
        //连拍Gif合成完成回调
        intent?.let {
            viewModel.inputArguments.updateSlotByNewDataSource(
                intent = intent,
                updateSlotAction = ::performUpdateSlot
            )
        } ?: GLog.w(TAG) { "handleResultRefresh, intent is null" }
    }

    private fun performUpdateSlot(data: Intent, isDifferenceDataSource: Boolean, targetItemPath: Path, targetAlbumPath: Path) {
        if (isDifferenceDataSource) {
            //外部打开
            val pageData = data.extras?.let(::Bundle) ?: Bundle()
            pageData.apply {
                putString(IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH, targetAlbumPath.toString())
                putString(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH, targetItemPath.toString())
                putString(IntentConstant.ViewGalleryConstant.KEY_MEDIA_MODEL_TYPE, DataRepository.LocalAlbumModelGetter.TYPE_LOCAL_ALBUM)
                putBoolean(IntentConstant.ViewGalleryConstant.KEY_NOT_DISPLAY_CSHOT_BTN, true)
                putBoolean(IntentConstant.PicturePageConstant.KEY_FROM_TIMELINE_PAGE, true)
                putCharSequence(IntentUtils.NAVIGATE_UP_TITLE_TEXT, null)
                viewModel.inputArguments.features.value?.screenOrientation?.let {
                    putInt(IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION, it)
                }
                putBoolean(IntentConstant.ViewGalleryConstant.KEY_DISPLAY_SET_COVER_MENU, viewModel.dataLoading.isCoverSupported())
            }
            GLog.w(TAG) { "[performUpdateSlot] isDifferenceDataSource, startPage" }
            /**
             * 会另起新大图页，如果底下的大图页有提亮效果，会导致底部大图ProXDR提亮的影透出来。导致出现重影。
             * 该修改同PhotoMenuEditActionRule
             **/
            viewModel.contentLoading.updateSuppressRendererFlag(true)
            menuScope?.startPage(activity.findFullScreenContainerId(), PICTURE_FRAGMENT, pageData)
        } else {
            //相册内部
            GLog.w(TAG) { "[performUpdateSlot] doUpdate PhotoPage, notifyChangeFocusHint" }
            viewModel.inputArguments.notifyChangeFocusHint(
                itemPath = targetItemPath.toString(),
                itemUri = data.data,
                itemType = data.type
            )
        }
    }

    companion object {
        private const val TAG = "PhotoMenuGifActionRule"
    }
}