/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorSpeederVM
 ** Description:
 ** Version: 1.0
 ** Date : 2025/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/5/7      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.TRACK_INDEX_FIRST
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.params.SpeederParams
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.utlis.EditUtils
import com.oplus.gallery.videoeditorpage.video.business.output.OperationSaveHelper
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.trim.IEffectClipResponder
import com.oplus.gallery.videoeditorpage.video.cartoon.VideoClipCartoonInstaller

/**
 * 视频变速相关的ViewModel
 */
class EditorSpeederVM(
    private val context: Context,
    private val operationSaveHelper: OperationSaveHelper,
    private val timelineViewModel: TimelineViewModel,
    private val editorEngine: EditorEngine,
    private val changeSpeedCurveTemplateList: List<SpeederCurveInfo>,
    private val currentPlayingClip: IVideoClip?,
    private val effectClipResponderList: List<IEffectClipResponder?>? = null
) : ViewModel() {
    private val clipList: List<IVideoClip> = getClipList() ?: emptyList()

    // 保存所有片段的原始速度
    private val originalVideoSpeedMap: Map<IVideoClip, SpeederParams> = mutableMapOf<IVideoClip, SpeederParams>().apply {
        clipList.forEach { videoClip ->
            put(videoClip, videoClip.speederParams)
        }
    }

    /**
     * 重置按钮是否可用
     */
    private val _isResetBtnEnabled: MutableLiveData<Boolean> by lazy { MutableLiveData(!isOriginalSpeed()) }
    val isResetBtnEnabled: LiveData<Boolean> = _isResetBtnEnabled

    /**
     * 视频片段的速度
     */
    private val _videoSpeed: MutableLiveData<Double> = MutableLiveData(getCurrentVideoClipSpeed())
    val videoSpeed: LiveData<Double> get() = _videoSpeed

    /**
     * 当前展示的速度（刻度尺滑动时还没有松手时显示的速度，不是最后确认的速度）
     */
    private val _currentDisplaySpeed: MutableLiveData<Double> = MutableLiveData(getCurrentVideoClipSpeed())
    val currentDisplaySpeed: LiveData<Double> get() = _currentDisplaySpeed

    /**
     * 当前是否是曲线变速模式
     */
    private val _isSpeedCurveMode: MutableLiveData<Boolean> = MutableLiveData(isSpeedCurveMode())
    val isSpeedCurveMode: LiveData<Boolean> get() = _isSpeedCurveMode

    /**
     * 曲线变速模板选中的位置
     */
    private val _speedCurveIndexSelected: MutableLiveData<Int> =
        MutableLiveData(getSpeedCurveSelectedIndex(currentPlayingClip?.speederParams?.speedCurveRawData))
    val speedCurveIndexSelected: LiveData<Int> get() = _speedCurveIndexSelected

    /**
     * 视频片段的初始时间对（在时间轴上的开始时间，结束时间）集合
     */
    private val previousClipTimePairMap: Map<IVideoClip, Pair<Long, Long>> = getPreviousClipTimePairMap()

    /**
     * 获取视频片段的初始时间对（在时间轴上的开始时间，结束时间）集合
     */
    private fun getPreviousClipTimePairMap(): Map<IVideoClip, Pair<Long, Long>> {
        return clipList.associateWith { it.inPoint to it.outPoint }
    }

    /**
     * 是否是原始速度
     */
    private fun isOriginalSpeed(): Boolean {
        return clipList.all { it.speed == SPEED_DEFAULT && it.speederParams.speedCurveRawData.isEmpty() }
    }

    /**
     * 播放视频片段
     * @param clipIndex 视频片段的索引
     * @param isPlayRemainingVideoList 是否播放剩余的视频列表
     */
    private fun playbackVideoClip(clipIndex: Int, isPlayRemainingVideoList: Boolean) {
        val videoClip = getVideoClip(clipIndex) ?: return
        var inPoint = videoClip.inPoint
        var outPoint = if (isPlayRemainingVideoList) clipList[clipList.size - 1].outPoint else videoClip.outPoint

        // 校正 inPoint
        val inPointLength = EditUtils.durationToLength(context, videoClip.inPoint)
        val correctedInPoint = EditUtils.lengthToDuration(context, inPointLength)
        if (correctedInPoint < videoClip.inPoint) {
            inPoint = EditUtils.lengthToDuration(context, inPointLength + POINT_ERROR_CORRECTION_LENGTH)
        }

        // 获取 clipSize
        val currentTimeline = editorEngine.currentTimeline
        val mainVideoTrack = currentTimeline?.getVideoTrack(TRACK_INDEX_FIRST) ?: return
        val clipList = mainVideoTrack.clipList ?: emptyList()
        val clipSize = clipList.size

        // 校正 outPoint
        val outPointLength = EditUtils.durationToLength(context, outPoint)
        val correctedOutPoint = EditUtils.lengthToDuration(context, outPointLength)
        if ((correctedOutPoint > videoClip.outPoint) && (clipIndex < (clipSize - 1))) {
            outPoint -= EditorEngine.FRAME_DURATION
        }
        /**
         * 因为第一段视频的结束outPoint和第二段视频的开始inPoint相同，
         * 变速以后第一段视频播放结束会选中第二段视频的开头，这里做一个微秒的调整
         */
        outPoint -= OUT_POINT_ADJUSTMENT_VALUE
        editorEngine.startPlayer(inPoint, outPoint)
    }

    /**
     * 获取当前正在播放的视频片段
     */
    private fun getVideoClip(clipIndex: Int): IVideoClip? {
        val iTimeline = editorEngine.currentTimeline ?: return null
        val videoTrack = iTimeline.getVideoTrack(TRACK_INDEX_FIRST) ?: return null
        return videoTrack.getClip(clipIndex) as? IVideoClip
    }

    /**
     * 当前视频播放的变速参数是否有变化
     */
    private fun isAllVideoClipsSpeedNoChanged(): Boolean {
        return clipList.all { isVideoClipSpeedNoChanged(it, originalVideoSpeedMap[it] ?: SpeederParams.modeLinear()) }
    }

    /**
     * 检查视频剪辑的速度参数是否发生变化
     * @param videoClip 当前的视频剪辑对象
     * @param newSpeederParams 新的速度参数对象
     * @return 如果速度曲线原始数据和速度因子都没有变化，则返回true，否则返回false
     */
    private fun isVideoClipSpeedNoChanged(videoClip: IVideoClip, newSpeederParams: SpeederParams): Boolean {
        return videoClip.speederParams.run {
            (speedCurveRawData == newSpeederParams.speedCurveRawData) &&
                    (speedFactor == newSpeederParams.speedFactor)
        }
    }

    /**
     * 停止播放
     */
    fun stopPlaybackIfPlaying() {
        editorEngine.takeIf { it.isPlaying }?.stopPlayer()
    }

    /**
     * 判断当前时间轴中是否包含多个视频片段。
     * @return 如果片段数量大于1则返回true，否则返回false。
     */
    fun hasMultipleTimelineClips(): Boolean {
        return clipList.size > 1
    }

    /**
     * 当前视频播放的原始速度
     */
    private fun getCurrentVideoClipSpeed(): Double {
        return currentPlayingClip?.getSpeed() ?: SPEED_DEFAULT
    }

    /**
     * 当前是否是曲线变速
     */
    private fun isSpeedCurveMode(): Boolean {
        return currentPlayingClip?.speederParams?.isCurveSpeed() == true
    }

    /**
     * 获取当前曲线变速模板中的位置
     */
    fun getSpeedCurveSelectedIndex(speedCurveRawData: String?): Int {
        return changeSpeedCurveTemplateList.indexOfFirst { it.speedCurveRawData == speedCurveRawData }
    }

    /**
     * 重置变速
     */
    fun resetSpeed(selectClip: IVideoClip?) {
        changeSpeed(selectClip, SpeederParams.modeLinear(), true)
        notifyIsSpeedCurveModeChanged(false)
    }

    /**
     * 做视频做常规变速
     * @param speed 要设置的播放速度
     * @param applyAll 是否将此速度变化应用于所有视频片段
     */
    fun changeSpeed(
        selectClip: IVideoClip?,
        newSpeederParams: SpeederParams,
        applyAll: Boolean
    ) {
        if ((clipList.isEmpty()) || (selectClip == null)) {
            GLog.e(TAG, LogFlag.DL, "[changeClipSpeed]current edit clip is invalid when clip change speed!")
            return
        }

        val timeline: ITimeline = editorEngine.getCurrentTimeline()
        // 获取当前选中的视频片段索引
        val currentClipIndex: Int = clipList.indexOf(selectClip)
        if ((currentClipIndex < 0) || (currentClipIndex >= clipList.size)) {
            GLog.e(TAG, LogFlag.DL, "[changeClipSpeed] Invalid clip index: $currentClipIndex")
            return
        }

        if (applyAll) {
            if (clipList.all { isVideoClipSpeedNoChanged(it, newSpeederParams) }) {
                // 所有 clip 已经是目标速度，无需更改
                return
            }
            // 如果当前有播放，停止播放，以准备进行速度更改
            stopPlaybackIfPlaying()

            clipList.forEachIndexed { clipIndex, videoClip ->
                changeSingleClipSpeed(newSpeederParams, timeline, videoClip, clipIndex)
            }
        } else {
            // 根据索引获取视频片段
            val videoClip: IVideoClip? = getVideoClip(currentClipIndex)
            // 检查其速度是否已经为目标速度
            if ((videoClip == null) || isVideoClipSpeedNoChanged(videoClip, newSpeederParams)) {
                GLog.e(TAG, LogFlag.DL, "[changeClipSpeed] current edit clip is not a video clip!")
                return
            }
            // 如果当前有播放，停止播放，以准备进行速度更改
            stopPlaybackIfPlaying()
            // 改变单个视频片段的速度
            changeSingleClipSpeed(newSpeederParams, timeline, videoClip, currentClipIndex)
        }
        editorEngine.seekTo(selectClip.inPoint, 0)
        timeline.needTrimMusic = false
        // 通过时间线恢复编辑环境，来反映速度更改
        editorEngine.restoreByTimeline(timeline, true)
        // 播放当前剪辑，预览更改
        playbackVideoClip(currentClipIndex, applyAll)
        notifyResetEnabledChanged(!isOriginalSpeed())
        notifySpeedChanged(newSpeederParams.speedFactor)
        notifyDisplaySpeedChanged(newSpeederParams.speedFactor)
        val curveTemplateIndex = getSpeedCurveSelectedIndex(newSpeederParams.speedCurveRawData)
        notifyCurveItemIndexChanged(curveTemplateIndex)
    }

    /**
     * 通知重置按钮的状态是否已改变
     */
    private fun notifyResetEnabledChanged(resetBtnEnabled: Boolean) {
        if (_isResetBtnEnabled.value == resetBtnEnabled) return
        _isResetBtnEnabled.value = resetBtnEnabled
    }

    /**
     * 通知视频速度变化（videoSpeed在刻度尺停止滑动时值才会变化）
     */
    fun notifySpeedChanged(speed: Double) {
        if (_videoSpeed.value == speed) return
        _videoSpeed.value = speed
    }

    /**
     * 通知显示速度发生变化（currentDisplaySpeed在刻度尺滑动时值会变化）
     */
    fun notifyDisplaySpeedChanged(speed: Double) {
        if (_currentDisplaySpeed.value == speed) return
        _currentDisplaySpeed.value = speed
    }

    /**
     *通知是否是曲线变速模式标志改变
     */
    fun notifyIsSpeedCurveModeChanged(isSpeedCurve: Boolean) {
        if (_isSpeedCurveMode.value == isSpeedCurve) return
        _isSpeedCurveMode.value = isSpeedCurve
    }

    /**
     *通知是否是曲线变速模板选中位置改变
     */
    fun notifyCurveItemIndexChanged(speedCurveIndexSelected: Int) {
        if (_speedCurveIndexSelected.value == speedCurveIndexSelected) return
        _speedCurveIndexSelected.value = speedCurveIndexSelected
    }

    /**
     * 改变单个视频片段的速度
     */
    private fun changeSingleClipSpeed(
        newSpeederParams: SpeederParams,
        timeline: ITimeline,
        videoClip: IVideoClip,
        currentClipIndex: Int
    ) {
        // 1.设置视频片段的新变速参数
        videoClip.speederParams = newSpeederParams

        // 2. marked by shuyangyang 对变速进行关键帧处理，后续加入逻辑

        // 3.如果视频片段的持续时间太短，则移除动画效果过渡
        if (videoClip.duration < TimelineViewModel.TRANSLATION_CARTOON_TIME_LIMIT) {
            // 获取时间线上的第一个视频轨道
            val videoTrack = timeline.getVideoTrack(TRACK_INDEX_FIRST)
            if (videoTrack != null) {
                // 如果当前视频片段索引大于等于1，则移除当前片段及其前一个片段的转场效果
                if (currentClipIndex >= CLIP_INDEX_SECOND) {
                    videoTrack.setTransition(currentClipIndex - CLIP_INDEX_SECOND, null)
                }
                videoTrack.setTransition(currentClipIndex, null)
            }
            // 获取当前视频片段上的所有动画效果
            val clipEffects: List<BaseVideoClipEffect>? =
                videoClip.getEffectsByType(StreamingConstant.Cartoon.TYPE_CARTOON_EFFECT)
            if (clipEffects != null) {
                // 遍历并移除所有的动画效果
                for (baseVideoClipEffect in clipEffects) {
                    if (baseVideoClipEffect != null) {
                        videoClip.removeEffect(baseVideoClipEffect)
                    }
                }
            }
        }
        // 4.更新移除动画效果后的视频片段特效
        VideoClipCartoonInstaller.updateEffects(videoClip)
    }

    /**
     * 更新视频关键帧
     */
    private fun updateKeyFrames(videoClip: IVideoClip, speed: Float) {
        val keyFrameList = videoClip.keyFrameList
        if ((keyFrameList != null) && (keyFrameList.size > 0)) {
            for (keyframe in keyFrameList) {
                var inPoint =
                    ((keyframe.inPoint - videoClip.trimIn) * videoClip.speed / speed).toLong() + videoClip.trimIn
                if (videoClip.videoType == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
                    inPoint =
                        ((keyframe.inPoint - videoClip.imageTrimIn) * videoClip.speed / speed + videoClip.imageTrimIn).toLong()
                }
                keyframe.inPoint = inPoint
            }
            //marked by shuyangyang 更新关键帧的特效
        }
    }

    /**
     *完成视频加速操作
     */
    fun finishClipSpeedChange() {
        if (isAllVideoClipsSpeedNoChanged()) {
            return
        }
        effectClipResponderList?.forEach { clipResponder ->
            clipResponder?.update(editorEngine.currentTimeline)
        }
        operationSaveHelper.saveCurrentTimeline(OperationType.SPEEDER, null)
    }

    /**
     * 取消片段变速操作
     */
    fun cancelClipSpeedChange() {
        if (isAllVideoClipsSpeedNoChanged()) {
            return
        }
        operationSaveHelper.restoreUndoByTop(false)
    }

    /**
     * 获取视频片段列表
     */
    private fun getClipList(): List<IVideoClip>? {
        val timeline: ITimeline = editorEngine.getCurrentTimeline()
        val videoTrack = timeline.getVideoTrack(TRACK_INDEX_FIRST)
        if (videoTrack == null) {
            e(TAG, LogFlag.DL, "[getClipList] video track is null")
            return null
        }
        return videoTrack.clipList
    }

    /**
     * 根据滚动时间获取选中的视频片段
     * @param timeScrolled 滚动的时间点
     * @return 返回选中的视频片段，如果没有找到则返回null
     */
    fun getSelectedVideoClip(timeScrolled: Long): IVideoClip? {
        val clipIndex = timelineViewModel.getSelectedMainTrackIndex(timeScrolled)
        return editorEngine.currentTimeline?.getVideoTrack(TRACK_INDEX_FIRST)
            ?.getClip(clipIndex) as? IVideoClip
    }

    companion object {
        //日志
        private const val TAG = "TimelineOperationManager"

        //错误时校正长度
        private const val POINT_ERROR_CORRECTION_LENGTH: Int = 1

        //第二个片段地址
        private const val CLIP_INDEX_SECOND: Int = 1

        //播放结束时间点调整值，用于适配多片段在变速以后播放完成选中的片段异常问题
        private const val OUT_POINT_ADJUSTMENT_VALUE: Long = 1

        //曲线变速默认模板选中位置
        const val SPEED_CURVE_TEMPLATE_INDEX_DEFAULT: Int = 0

        //常规默认速度
        const val SPEED_DEFAULT: Double = 1.0

        //无效索引
        const val INDEX_INVALID: Int = -1

        //无效数值
        const val LONG_VALUE_INVALID: Long = -1
    }
}

class EditorSpeederVMFactory(
    private val context: Context,
    private val operationSaveHelper: OperationSaveHelper,
    private val timelineViewModel: TimelineViewModel,
    private val editorEngine: EditorEngine,
    private val changeSpeedCurveInfoList: List<SpeederCurveInfo>,
    private val currentPlayingClip: IVideoClip?,
    private val effectClipResponderList: List<IEffectClipResponder?>? = null
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(EditorSpeederVM::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return EditorSpeederVM(
                context,
                operationSaveHelper,
                timelineViewModel,
                editorEngine,
                changeSpeedCurveInfoList,
                currentPlayingClip,
                effectClipResponderList
            ) as T
        } else {
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}