/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISuperTextImageCodec
 ** Description: 超級文本編解碼的接口類
 ** Version: 1.0
 ** Date : 2022/5/17
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/05/17    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.supertext

import android.graphics.Bitmap
import java.io.FileDescriptor

/**
 * 超级文本编解码接口
 */
interface ISuperTextImageCodec {

    /**
     * 是否EnhanceText （相机1.0模式拍出来的照片]）
     * @return true or false
     */
    fun isEnhanceText(): Boolean

    /**
     * 该文本图片是否被扩展过(超级文本编码方式)
     * 1.0相机会自动写效果图，超级文本标志位等，自动会被扩展
     * 非1.0的图片在2.0经过超级文本的编辑后上也要保持和1.0一样的格式
     * 使用场景：这种格式的图片不支持瘦身
     */
    fun isExtendedFile(): Boolean

    /**
     * 是否为超级文本图片，包含 相机拍摄的1.0图片和普通文本图片
     * @return true or false
     */
    fun isSuperText(): Boolean

    /**
     * 解码超级文本图片信息
     * @param options SuperTextImage.Options
     * @return bitmap
     */
    fun decodeFile(options: SuperTextOptions?): Bitmap?

    /**
     * 编码图片
     * @param fd FileDescriptor
     * @param options SuperTextImage.Options
     * @return true/false 是否encode成功
     */
    fun encodeFile(fd: FileDescriptor?, options: SuperTextOptions?): Boolean
}