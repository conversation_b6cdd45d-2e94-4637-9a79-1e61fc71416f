/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ICapturing.kt
 ** Description : 画面取帧接口
 ** Version     : 1.0
 ** Date        : 2022/05/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/05/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.media.capturing

import android.graphics.Bitmap
import java.io.Closeable

/**
 * 画面取帧接口
 */
interface ICapturing : Closeable {

    /**
     * 截取指定时间戳所在的画面帧
     * @param frameAtTimeUs 指定画面帧的时间戳（单位：微秒， 1us = 1000ms）
     * @param onCaptured 画面帧截取回调，当截帧失败时会回传null
     */
    fun capture(frameAtTimeUs: Long, onCaptured: (Bitmap?) -> Unit): Unit
}
