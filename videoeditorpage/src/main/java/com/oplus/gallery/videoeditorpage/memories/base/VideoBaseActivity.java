/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - VideoBaseActivity.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2019/02/16
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2019/02/16    1.0     build this module same as AbsPermissionActivity
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.base;

import static com.oplus.gallery.addon.osense.CpuFrequencyManager.TIMEOUT_1S;
import static com.oplus.gallery.basebiz.permission.helper.CTAHelper.DISMISS_DELAY_DURATION;
import static com.oplus.gallery.basebiz.uikit.activity.PermissionsGuideActivity.REQUEST_CODE_PERMISSION_SETTING;
import static com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.PREFERENCE_USE_NETWORK_REMIND;

import android.Manifest;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;

import com.coui.appcompat.theme.COUIThemeOverlay;
import com.oplus.gallery.addon.osense.CpuFrequencyManager;
import com.oplus.gallery.addon.osense.CpuFrequencyManager.Action;
import com.oplus.gallery.basebiz.permission.ExportPermissionManager;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.basebiz.permission.PrivacyPolicyCallback;
import com.oplus.gallery.basebiz.permission.RuntimePermissionAlert;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.basebiz.uikit.activity.PermissionsGuideActivity;
import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter;
import com.oplus.gallery.foundation.libcolordirect.ColorDirectOperator;
import com.oplus.gallery.foundation.networkaccess.NetThreadPool;
import com.oplus.gallery.foundation.ui.dialog.SecurityDialog;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.memories.ui.dialog.RuntimePermissionRationaleAlert;
import com.oplus.gallery.videoeditorpage.memories.imageloader.ImageIconLoader;

import java.util.Arrays;
import java.util.concurrent.FutureTask;


public abstract class VideoBaseActivity extends BaseActivity implements MeicamEngineLimiter.LimitAble {
    private static final String TAG = "VideoBaseActivity";
    private static final int REQUEST_CODE_RUNTIME_PERMISSION_BASE = 0;
    private static final int REQUEST_CODE_RUNTIME_PERMISSION_READ_PHONE_STATE = 1;
    public boolean mHasPermission = true;

    protected boolean mIsLimited = false;

    private boolean mOnCreateCheck = false;
    // after onRequestPermissionRequest will call onResume, use this flag to avoid recycling show
    // permission dialog.
    private boolean mNotRequestPermissionCycling = false;
    private boolean mHasGoInfoPage = false;
    private boolean mIsExportPermissionDialogShowing = false;
    private boolean mIsManagerExternalDialogShowing = false;
    private SecurityDialog mAlertDialog = null;

    private FutureTask<?> mFutureTask = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        CpuFrequencyManager.setAction(Action.ACTIVITY_START, TIMEOUT_1S);
        super.onCreate(savedInstanceState);
        ImageIconLoader.getInstance().init(getDecodeSession());
        GLog.d(TAG, "onCreate");
        if (!FeatureUtils.isRegionCN()) {
            checkExportPermissionDialog();
        }
        mHasPermission = isPermissionsEnable();
        COUIThemeOverlay.getInstance().applyThemeOverlays(this);
        loadMain();

        if (mHasPermission) {
            mOnCreateCheck = true;
            onCreateCheck();
        }
        ResourceUtils.setCurrentLanguage();
        mFutureTask = NetThreadPool.getInstance().submit(() -> {
            ColorDirectOperator.disableColorDirectService(VideoBaseActivity.this);
        });
    }

    @Override
    public boolean shouldFinishWhenUIModeChanged() {
        return true;
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        GLog.d(TAG, "onRestart, mHasPermission=" + mHasPermission);
        if (mHasPermission) {
            onReStartCheck();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        GLog.d(TAG, "onStart, mHasPermission=" + mHasPermission);

        if (mHasPermission) {
            onStartCheck();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        GLog.d(TAG, "onResume, mHasPermission=" + mHasPermission);
        onPermissionResult();
        if (mHasPermission) {
            onResumeCheck();
        }
        ResourceUtils.setCurrentLanguage();
    }

    @Override
    protected void onPause() {
        super.onPause();
        GLog.d(TAG, "onPause, mHasPermission=" + mHasPermission);
        if (mHasPermission) {
            onPauseCheck();
        }
        mIsManagerExternalDialogShowing = false;
    }

    @Override
    protected void onStop() {
        super.onStop();
        GLog.d(TAG, "onStop, mHasPermission=" + mHasPermission);

        if (mHasPermission) {
            onStopCheck();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mHasPermission) {
            onDestroyCheck();
        }
        NetThreadPool.getInstance().cancel(mFutureTask);
        ImageIconLoader.getInstance().release();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        GLog.d(TAG, "onConfigurationChanged, mHasPermission=" + mHasPermission);

        if (mHasPermission) {
            onConfigurationChangedCheck();
        }
    }

    @Override
    public void limitNow() {
        GLog.d(TAG, "limitNow");
        mIsLimited = true;
        finish();
    }

    @Override
    public boolean isActive() {
        return getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.RESUMED);
    }

    public abstract void loadMain();

    public void onCreateCheck() {
    }

    public void onReStartCheck() {
    }

    public void onStartCheck() {
    }

    public void onResumeCheck() {
    }

    public void onPauseCheck() {
    }

    public void onStopCheck() {
    }

    public void onDestroyCheck() {
    }

    public void onConfigurationChangedCheck() {
    }

    @Override
    @SuppressWarnings("deprecation")
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean result = RuntimePermissionUtils.isRequestPermissionAllGranted(grantResults);
        GLog.v(TAG, "onRequestPermissionsResult, requestCode = " + requestCode
                + ", permissions = " + Arrays.toString(permissions)
                + ", grantResults = " + Arrays.toString(grantResults));
        if ((permissions == null) || (permissions.length == 0)) {
            return;
        }
        switch (requestCode) {
            case REQUEST_CODE_RUNTIME_PERMISSION_BASE:
                if (result) {
                    mNotRequestPermissionCycling = false;
                    ApiDmManager.getMediaDBSyncDM().enqueueIncrementSync();
                    onPermissionResult();
                } else {
                    mNotRequestPermissionCycling = true;
                    startActivityForResult(new Intent(VideoBaseActivity.this,
                            PermissionsGuideActivity.class), REQUEST_CODE_PERMISSION_SETTING);
                }
                break;
            case REQUEST_CODE_RUNTIME_PERMISSION_READ_PHONE_STATE:
                if (result) {
                    mNotRequestPermissionCycling = false;
                    onPermissionEnable();
                } else {
                    mNotRequestPermissionCycling = true;
                    new RuntimePermissionRationaleAlert().showDialog(this,
                            new RuntimePermissionRationaleAlert.RationaleCallback() {
                                @Override
                                public void onSettingClick() {
                                    mHasGoInfoPage = true;
                                    RuntimePermissionUtils.goSystemSettingsPermissionPage(VideoBaseActivity.this,
                                            Manifest.permission.READ_PHONE_STATE);
                                }

                                @Override
                                public void onDismissClick() {
                                    finish();
                                }
                            });
                }
                break;
            default:
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        GLog.d(TAG, "onActivityResult: requestCode = " + requestCode);
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_PERMISSION_SETTING) {
            mNotRequestPermissionCycling = false;
        }
    }

    private void onPermissionResult() {
        GLog.d(TAG, "onPermissionResult: ");
        if (mHasGoInfoPage && !isPermissionsEnable()) {
            GLog.d(TAG, "onPermissionResult, mHasGoInfoPage = true, permission not granted, finish.");
            finish();
        }
        if (isPermissionsEnable()) {
            mHasPermission = true;
            onPermissionEnable();
        } else {
            checkPermission();
        }
        onRequestManageExternalStorage();
    }

    private boolean isPermissionsEnable() {
        return RuntimePermissionUtils.isNecessaryPermissionGranted(this);
    }

    private void checkPermission() {
        boolean isNecessaryPermissionGranted = RuntimePermissionUtils.isNecessaryPermissionGranted(this);
        GLog.d(TAG, "checkPermission, isNecessaryPermissionGranted = " + isNecessaryPermissionGranted
                + ", mIsExportPermissionDialogShowing = " + mIsExportPermissionDialogShowing);
        if (mIsExportPermissionDialogShowing) {
            GLog.d(TAG, "checkPermission, mIsExportPermissionDialogShowing return.");
            return;
        }

        if (!isNecessaryPermissionGranted && !mNotRequestPermissionCycling) {
            GLog.d(TAG, "checkPermission, requestPermissions storage");
            mNotRequestPermissionCycling = true;
            GTrace.traceBegin("requestPermissions");
            requestPermissions(RuntimePermissionUtils.getNecessaryPermissionNotGranted(this),
                    REQUEST_CODE_RUNTIME_PERMISSION_BASE);
            GTrace.traceEnd();
        } else {
            if (isNecessaryPermissionGranted && !mNotRequestPermissionCycling) {
                GLog.d(TAG, "checkPermission, requestPermissions READ_PHONE_STATE");
                mNotRequestPermissionCycling = true;
            }
        }
    }

    private void onPermissionEnable() {
        GLog.d(TAG, "onPermissionEnable");
        if (!mOnCreateCheck) {
            mOnCreateCheck = true;
            onCreateCheck();
            onStartCheck();
        }
    }

    protected void checkExportPermissionDialog() {
        boolean isRuntimePermissionAlertFirstShow = RuntimePermissionUtils.isRuntimePermissionAlertFirstShow();
        if (isRuntimePermissionAlertFirstShow) {
            startExportPermissionDialog();
        }
    }

    protected void startExportPermissionDialog() {
        mIsExportPermissionDialogShowing = true;
        ExportPermissionManager alert = new ExportPermissionManager(this,
                new PrivacyPolicyCallback() {
                    @Override
                    public void onDialogShow() {
                        GLog.d(TAG, "onDialogShow: ");
                    }

                    @Override
                    public void onPermittedAccessMedia() {
                        GLog.d(TAG, "onPermittedAccessMedia: ");
                    }

                    @Override
                    public void onPermitted() {
                        mIsExportPermissionDialogShowing = false;
                        checkRuntimePermission(true);
                    }

                    @Override
                    public void onCanceled() {
                        mIsExportPermissionDialogShowing = false;
                        checkRuntimePermission(false);
                    }

                    @Override
                    public void onBack(DialogInterface dialog) {
                        getHandler().postDelayed(() -> {
                            if (dialog != null) {
                                dialog.dismiss();
                            }
                            VideoBaseActivity.this.finish();
                        }, DISMISS_DELAY_DURATION);
                    }
                });

        alert.checkPermitPrivacyPolicy();
    }

    private void checkRuntimePermission(boolean isPermitted) {
        RuntimePermissionUtils.setRuntimePermissionAlertFirstShowFlag(false);
        SPUtils.setBoolean(ContextGetter.context, null,
                RuntimePermissionUtils.PREF_RUNTIME_PERMISSION_ALERT_FIRST_SHOW,
                RuntimePermissionUtils.isRuntimePermissionAlertFirstShow());
        NetworkPermissionManager.setUseOpenNetwork(isPermitted);
        SPUtils.setBoolean(VideoBaseActivity.this, null, PREFERENCE_USE_NETWORK_REMIND, isPermitted);
        onResume();
    }

    private void onRequestManageExternalStorage() {
        if (isFinishing() || isDestroyed()) {
            return;
        }

        if (mIsExportPermissionDialogShowing || mIsManagerExternalDialogShowing) {
            return;
        }
        if (!RuntimePermissionUtils.isNecessaryPermissionGranted(this)) {
            return;
        }
        mAlertDialog = RuntimePermissionAlert.showManageExternalStorageDialog(this, false, mAlertDialog, new RuntimePermissionAlert.ManageExternalStorageListener() {
            @Override
            public void onClick() {
                mIsManagerExternalDialogShowing = false;
            }

            @Override
            public void hasManageExternalCallBack() {
                mIsManagerExternalDialogShowing = false;
            }
        });
        if (mAlertDialog != null) {
            mIsManagerExternalDialogShowing = true;
        }
    }
}
