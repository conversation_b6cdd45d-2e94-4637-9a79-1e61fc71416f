/********************************************************************************
 ** Copyright (C), 2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - COUIWidgetExt
 ** Description: 视频编辑，coui控件扩展方法
 ** Version: 1.0
 ** Date : 2025/6/13
 ** Author: kangshuwen
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** kangshuwen                      2025/6/13       1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import com.coui.appcompat.seekbar.COUISeekBar
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil

private const val TAG = "COUIWidgetExt"

/**
 * 更新 COUISeekBar 的宽度
 * 直板机：使用固定宽度，通过 seekBarWidthResId 获取
 * 中屏：滤镜调节占4栏，其它业务6栏
 * 大屏：滤镜调节占6栏，其它业务8栏
 *
 * @param context 上下文
 * @param currentAppUIConfig 当前 UI 配置
 * @param seekBarWidthResId COUISeekBar 的宽度资源 ID
 * @param isSpecial 是否特殊场景 滤镜调节为特殊场景，其它场景为false
 */
fun COUISeekBar.updateWidth(
    context: Context,
    currentAppUIConfig: AppUiResponder.AppUiConfig,
    seekBarWidthResId: Int,
    isSpecial: Boolean
) {
    layoutParams?.width = if (EditorUIConfig.isEditorLandscape(currentAppUIConfig)
        || ScreenAdaptUtil.isMiddleAndLargeWindow(context)) {
        if (isSpecial) {
            // 特殊业务：滤镜和调节 中屏占4栅格 大屏占6栅格 要比一般情况小一号
            ScreenAdaptUtil.getViewEnableWidthSmallLevel(context)
        } else {
            // 一般就是自适应中屏占6栅格，大屏占8栅格
            ScreenAdaptUtil.getViewEnableWidth(context, context.resources.displayMetrics.widthPixels)
        }
    } else {
        // 直板机固定宽度
        context.resources.getDimensionPixelSize(seekBarWidthResId)
    }
}