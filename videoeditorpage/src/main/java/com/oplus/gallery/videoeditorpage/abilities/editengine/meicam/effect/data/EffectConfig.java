/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EffectConfig.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data;

import com.google.gson.annotations.SerializedName;

public class EffectConfig {
    @SerializedName("sticker")
    private StickerInfo mSticker = null;

    public StickerInfo getSticker() {
        return mSticker;
    }
}
