/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: BaseThumbNailRecycleAdapter
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguangjin@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget.adapter;

import android.content.Context;

import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.resource.imageloader.ImageIconLoader;

import java.util.List;

public abstract class BaseThumbNailRecycleAdapter<T> extends BaseRecycleAdapter<T> {
    protected ImageIconLoader mImageLoaderManager;

    public BaseThumbNailRecycleAdapter(Context context, List<T> data) {
        super(context, data);
        mImageLoaderManager = new ImageIconLoader(context);
    }

    public void release() {
        super.release();
        if (mImageLoaderManager != null) {
            mImageLoaderManager.release();
            mImageLoaderManager.destroy();
        }
    }
}
