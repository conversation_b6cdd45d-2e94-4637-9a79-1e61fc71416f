/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - MemoryCache.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/7/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/7/5        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.foundation.cache

/**
 * 内存缓存
 * @param maxSize 缓存最大数量，超过此数量时会按照LRU策略清除缓存
 */
class MemoryCache<T>(maxSize: Int) : ICache<T> {
    private val cacheMap = CacheMap<String, T>(maxSize)

    /**
     * 添加获取所有缓存键的方法
     */
    fun keys(): Set<String> {
        return cacheMap.keys
    }

    override fun put(key: String, value: T) {
        cacheMap[key] = value
    }

    override fun get(key: String): T? {
        return cacheMap[key]
    }

    override fun remove(key: String) {
        cacheMap.remove(key)
    }

    override fun clear() {
        cacheMap.clear()
    }

    override fun size(): Long {
        return cacheMap.size.toLong()
    }

    private class CacheMap<K, V>(private val maxSize: Int) : LinkedHashMap<K, V>(maxSize, LOAD_FACTOR, true) {
        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<K, V>?): Boolean {
            return size > maxSize
        }
    }

    companion object {
        private const val LOAD_FACTOR = 0.75f
    }
}