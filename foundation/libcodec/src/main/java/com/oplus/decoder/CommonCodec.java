/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : CommonCodec.java
 ** Description : source encoding and decoding.
 ** Version     : 1.0
 ** Date        : 2018/07/01
 ** Author      : biao.chen@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <date>      <version>  <desc>
 **  biao.chen@Apps.Gallery3D  2018/07/01  1.0        build this module
 ***********************************************************************/

package com.oplus.decoder;

import com.oplus.gallery.standard_lib.util.loadLibrary.LoadLibraryTool;

/**
 * 注意：该类不能随意修改包名。因为存在于native层的交互，一旦修改包名会导致native逻辑异常，从而导致解码异常。
 */
public class CommonCodec {
    static {
        LoadLibraryTool.loadLibrary("oplus_gifdecoder");
    }

    /**
     * encode byte array data
     *
     * @param source byte array data
     * @return encoded's byte array data
     */
    public static native byte[] encodeByteArray(byte[] source);

    /**
     * decode byte array data
     *
     * @param source byte array data
     * @return decoded's byte array data
     */
    public static native byte[] decodeByteArray(byte[] source);

    /**
     * dump the LSTAT info of the specified file path
     *
     * @param path
     */
    public static native void dumpLstat(String path);
}
