/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FontSelector.kt
 ** Description : 字幕字体面板组件
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import android.view.View
import com.oplus.gallery.videoeditorpage.video.business.caption.resource.ResourceManager
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.FontsAdapter
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.FontViewData

/**
 * 字幕字体面板组件
 */
internal class FontSelector(
    isInMultiWindow: Boolean,
    bottomNaviBarHeight: Int,
    private val resManager: ResourceManager,
    pageType: PageType
) : DownloadableSelector(isInMultiWindow, bottomNaviBarHeight, pageType) {

    /**
     * 字体适配器
     */
    private var fontAdapter: FontsAdapter? = null

    /**
     * 配置适配器
     */
    override fun initAdapter() {
        resourceRecyclerView?.let {
            fontAdapter = FontsAdapter(it)
            it.adapter = fontAdapter
        }
    }

    /**
     * 数据初始化
     */
    override fun initData() {
        resManager.fetchFontsData { fontItems ->
            fontAdapter?.let {
                it.setDataSource(fontItems as List<FontViewData>)
                it.setSelectedPositionForFontId(resourceId)
                captionEffectsChangedListener?.fontChanged(pageType, fontItems[it.selectedPosition], it.selectedPosition)
                it.onItemClickCallback = { v: View?, position: Int, data: FontViewData? ->
                    // 点击item回调
                    data?.let { fontItem ->
                        captionEffectsChangedListener?.fontChanged(pageType, fontItem, position)
                    }
                    currentClickPosition = position
                }
            }
        }
    }

    /**
     * 更新字体item
     */
    fun notifyFontItemChanged(position: Int) {
        fontAdapter?.notifyItemChanged(position)
    }

    /**
     * 设置选中字体，用于选中样式后更新样式绑定的字体在字体页的下载选中状态
     *
     * @param fontData 字体数据
     */
    fun setSelectedForFont(fontData: FontViewData?) {
        fontAdapter?.setSelectedForFont(fontData)
    }

    override fun destroy() {
        fontAdapter?.destroy()
        fontAdapter = null
        super.destroy()
    }

    companion object {
        private const val TAG = "FontSelector"
    }
}
