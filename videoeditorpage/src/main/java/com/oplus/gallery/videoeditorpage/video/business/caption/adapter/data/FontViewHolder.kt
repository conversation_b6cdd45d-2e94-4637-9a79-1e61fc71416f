/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FontViewHolder.kt
 ** Description : 文字字体item viewHolder
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.ViewAnimator
import com.oplus.gallery.videoeditorpage.widget.RoundProgressView
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.BaseAdapter.BaseVH

/**
 * 字幕字体view holder
 */
class FontViewHolder(parent: ViewGroup) : BaseVH<FontViewData>(parent, R.layout.videoeditor_item_caption_font) {

    /**
     * 字体item显示控件
     */
    private val captionFontItemView: FrameLayout by lazy {
        findViewById(R.id.item_caption_font_parent)
    }

    /**
     * 字体icon显示控件
     */
    private val captionFontItemViewImg: ImageView by lazy {
        findViewById(R.id.iv_item_caption_font_img)
    }

    /**
     * 字体名称显示控件
     */
    private val captionFontNameView: TextView by lazy {
        findViewById(R.id.item_caption_font_name)
    }

    /**
     * 下载状态显示控件
     */
    private val captionFontDownloadStateView: ViewAnimator by lazy {
        findViewById(R.id.va_caption_font_download_state)
    }

    /**
     * 下载进度条控件
     */
    private val downloadProgressView: RoundProgressView by lazy {
        findViewById(R.id.download_progress)
    }

    /**
     * 通用view holder 数据样式绑定的绑定，子类去实现
     */
    override fun bind(captionFont: FontViewData) {
        this.captionFontItemView.isSelected = captionFont.selected
        captionFont.downloadState.let {
            if (it == DownloadItem.NOT_DOWNLOADED) {
                captionFontDownloadStateView.visibility = View.VISIBLE
                if (captionFont.progress  > 0) {
                    captionFontDownloadStateView.displayedChild = INDEX_DOWNLOADING
                    downloadProgressView.progress = captionFont.progress
                } else {
                    captionFontDownloadStateView.displayedChild = INDEX_DOWNLOAD
                }
            } else {
                captionFontDownloadStateView.visibility = View.GONE
            }
        }
        captionFontNameView.text = captionFont.fontDisplayName
        loadImage(captionFont.iconUrl, captionFontItemViewImg, R.drawable.ic_caption_style_default)
    }

    companion object {
        const val INDEX_DOWNLOAD: Int = 0
        const val INDEX_DOWNLOADING: Int = 1
        private const val TAG = "FontViewHolder"
    }
}