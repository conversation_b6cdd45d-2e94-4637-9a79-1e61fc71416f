/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: IThumbnailLoader
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/


package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

public interface IThumbnailLoader<Data> {

    public void startLoader(Data item, ThumbnailListener<Data> thumbnailListener);

    public void startLoader(Data item, ImageView target);

    public void startLoaderWidthPlace(Data item, ImageView target, Drawable palo);

    public void removeTask(Data item);

    public void removeTask(View target);

    public void startLoader(Data item, ImageView target, IBitmapTransformOption bitmapHandle);

    public void startLoader(Data item, ThumbnailListener<Data> thumbnailListener, IBitmapTransformOption bitmapHandle);

    public int getWaitTask();

    public void cancelAllWaitTask();

    public void destroy();
}
