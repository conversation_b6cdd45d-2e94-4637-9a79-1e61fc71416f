/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditableClipInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import com.google.gson.annotations.SerializedName;

import java.util.List;
import java.util.Objects;

public class EditableClipInfo extends ClipIndexInfo {
    public static final int ALLOW_DEDUPLICATION = 1;
    public static final int NOT_DEDUPLICATION = 0;
    public static final int VIDEO = 2;
    public static final int PHOTO = 1;
    public static final int UNKNOWN = 0;
    //是否去除重复0是不去重 1是去重
    @SerializedName("isrepeat")
    protected int mIsRepeat = NOT_DEDUPLICATION;
    //素材的路径md5加密
    @SerializedName("repeatpath")
    protected String mClipPath;
    //片段是否用到表情贴纸
    @SerializedName("useFaceSticker")
    protected boolean mUseFaceSticker;
    //片段是否用抠像（抠头抠像都包含）.id 代表抠像类型
    @SerializedName("portraitType")
    protected int mPortraitType = -1;
    //云效果列表
    @SerializedName("effectInfoList")
    protected List<EditableCloudEffectInfo> mEditableCloudEffectInfoList;
    //素材的类型
    @SerializedName("materialType")
    protected int mMaterialType = UNKNOWN;

    public EditableClipInfo() {
    }

    public EditableClipInfo(int trackIndex, int clipIndex) {
        super(trackIndex, clipIndex);
    }

    public int getTrackIndex() {
        return mTrackIndex;
    }

    public void setTrackIndex(int trackIndex) {
        this.mTrackIndex = trackIndex;
    }

    public int getPortraitType() {
        return mPortraitType;
    }

    public void setUsePortrait(int portraitType) {
        mPortraitType = portraitType;
    }

    public int getClipIndex() {
        return mClipIndex;
    }

    public void setClipIndex(int clipIndex) {
        this.mClipIndex = clipIndex;
    }

    public int getIsRepeat() {
        return mIsRepeat;
    }

    public void setIsRepeat(int isRepeat) {
        this.mIsRepeat = isRepeat;
    }

    public String getClipPath() {
        return mClipPath;
    }

    public void setClipPath(String clipPath) {
        this.mClipPath = clipPath;
    }

    public boolean getUseFaceSticker() {
        return mUseFaceSticker;
    }

    public void setUseFaceSticker(boolean useFaceSticker) {
        this.mUseFaceSticker = useFaceSticker;
    }

    public List<EditableCloudEffectInfo> getEditableCloudEffectInfoList() {
        return mEditableCloudEffectInfoList;
    }

    public void setEditableCloudEffectInfoList(List<EditableCloudEffectInfo> editableCloudEffectInfoList) {
        this.mEditableCloudEffectInfoList = editableCloudEffectInfoList;
    }

    public boolean hasCloudEffect() {
        if ((mEditableCloudEffectInfoList == null) || (mEditableCloudEffectInfoList.size() == 0)) {
            return false;
        }
        return true;
    }

    public int getMaterialType() {
        return mMaterialType;
    }

    public void setMaterialType(int materialType) {
        this.mMaterialType = materialType;
    }

    @Override
    public String toString() {
        return "EditableClipInfo{"
                + "mIsRepeat=" + mIsRepeat
                + ", mClipPath='" + mClipPath + '\''
                + ", mUseFaceSticker=" + mUseFaceSticker
                + ", mPortraitType=" + mPortraitType
                + ", mEditableCloudEffectInfoList=" + mEditableCloudEffectInfoList
                + ", mMaterialType=" + mMaterialType
                + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if ((o == null) || (getClass() != o.getClass())) {
            return false;
        }
        EditableClipInfo that = (EditableClipInfo) o;
        return (mTrackIndex == that.mTrackIndex)
                && (mClipIndex == that.mClipIndex);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mTrackIndex, mClipIndex);
    }
}
