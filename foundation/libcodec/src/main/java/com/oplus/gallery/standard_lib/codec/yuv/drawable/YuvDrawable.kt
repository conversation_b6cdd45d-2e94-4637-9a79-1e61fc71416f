/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - YuvDrawable.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/24
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     yaoweihe       2022/05/24    1.0                create
 ******************************************************************************/

package com.oplus.gallery.standard_lib.codec.yuv.drawable

import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.view.Surface
import com.oplus.gallery.standard_lib.codec.yuv.decoder.YuvData
import com.oplus.gallery.foundation.util.math.Math2DUtil

open class YuvDrawable(val yuvData: YuvData?) : Drawable() {
    /**
     * 接口：对外触发角度变化
     */
    var onContentRotationChanged: ((degrees: Float) -> Unit)? = null

    /**
     * 内容旋转角度
     */
    var contentDegrees: Float = DEGREE_0
        set(value) {
            if (field == value) return

            field = value

            onContentRotationChanged?.invoke(field)
        }

    /**
     * 是否已经被回收
     */
    val isRecycled: Boolean
        get() = yuvData?.isRecycled() ?: true

    /**
     * 将本 [Drawable] 内容绘制到 [Surface] 上
     */
    fun render(surface: Surface): Boolean {
        yuvData?.render(surface)
        return true
    }

    override fun getIntrinsicWidth(): Int = yuvData?.let {
        if ((contentDegrees.toInt()).isLandscape()) {
            it.frameWidth
        } else {
            it.frameHeight
        }
    } ?: 0

    override fun getIntrinsicHeight(): Int = yuvData?.let {
        if ((contentDegrees.toInt()).isLandscape()) {
            it.frameHeight
        } else {
            it.frameWidth
        }
    } ?: 0

    override fun draw(canvas: Canvas) = Unit

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(colorFilter: ColorFilter?) = Unit

    override fun getOpacity(): Int = PixelFormat.OPAQUE

    private fun Int.isLandscape(): Boolean = (this % Math2DUtil.DEG_180I == 0)

    companion object {
        private const val DEGREE_0 = 0F
    }
}