/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PlaySeekBar
 ** Description: 视频播放滚动条
 ** Version: 1.0
 ** Date : 2025/04/03
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/04/03    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.videoeditorpage.R
import kotlin.math.roundToInt

/**
 * 视频播放滚动条
 */
class PlaySeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : View(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 当前进度
     */
    var progress: Int = DEFAULT_PROGRESS
        set(value) {
            if (field != value) {
                field = value
                invalidate()
            } else {
                GLog.d(TAG, LogFlag.DL) { "[progress] is same progress, do nothing" }
            }
        }

    /**
     * 滚动条最大进度
     */
    var maxProgress: Int = PROGRESS_MAX
        set(value) {
            if (field != value) {
                field = value
                invalidate()
            } else {
                GLog.d(TAG, LogFlag.DL) { "[maxProgress] is same progress, do nothing" }
            }
        }

    /**
     * 相关的手势逻辑
     */
    private val gestureDetector: GestureDetector by lazy {
        GestureDetector(context, GestureProcessor()).apply {
            setIsLongpressEnabled(false)
        }
    }

    /**
     * 【滑条最下层的线条】相关：
     * 矩阵、高度、圆角、宽度、颜色、画笔
     */
    private val baseLineRect: RectF = RectF()
    private val baseLineHeight: Float
    private val baseLineRadius: Float
    private var baseLineWidth: Float = 0f
    private val baseLineColor: Int
    private val baseLinePaint: Paint = Paint()

    /**
     * 是否需要按压动画
     */
    private val needPressedAnimator: Boolean

    /**
     * 按压放大倍数
     */
    private var pressedScale: Float = SCALE_NORMAL

    /**
     * 【下载缓存进度条】相关：
     * 是否显示、缓存进度、颜色、画笔
     */
    private val needCacheProgress: Boolean
    private var cacheProgress: Int = 0
    private val cacheProgressColor: Int
    private val cacheProgressPaint: Paint = Paint()

    /**
     * 【播放进度条】相关：
     * 是否显示、颜色、播放进度
     */
    private val needPlayProgress: Boolean
    private val playProgressColor: Int
    private val playProgressPaint: Paint = Paint()

    /**
     * 【滑条拇指按钮】相关：
     * x坐标、中心到左边长度、圆角、颜色、画笔
     */
    private var thumbX: Float = 0f
    private var thumbLeft: Float = 0f
    private val thumbRadius: Float
    private val thumbColor: Int
    private val thumbPaint: Paint = Paint()

    /**
     * 是否要有最小高度
     */
    private val needMinHeight: Boolean

    /**
     * 是否允许拇指按钮滑出两头边缘
     * true：拇指中心可以滑到滚动条左右边界，即拇指按钮，可超出滚动条
     * false：拇指按钮中心，离左右边界保持一个半径的距离
     */
    private val needThumbOutside: Boolean

    /**
     * 滚动条竖向的上间距，或下间距
     * 说明：从拇指按钮的上或下边缘，到组件对应边缘的距离
     */
    private val seekBarVerticalPadding: Int

    /**
     * 上次触摸的x坐标
     */
    private var lastX: Float = 0f

    /**
     * 是否正在滑动中
     */
    private var isDragging: Boolean = false

    /**
     * 触摸前，播放器是否处于播放状态
     */
    private var isPlayingBeforeDown: Boolean = false

    /**
     * 播放器接口
     */
    var player: IPlayer? = null

    /**
     * 滑动滑条事件监听器
     */
    var onSeekBarChangeListener: OnSeekBarChangeListener? = null

    init {
        val array = context.obtainStyledAttributes(attrs, R.styleable.PlaySeekBar, defStyleAttr, defStyleRes)
        val res = context.resources
        try {
            progress = array.getInteger(R.styleable.PlaySeekBar_defaultProgress, DEFAULT_PROGRESS)
            needMinHeight = array.getBoolean(R.styleable.PlaySeekBar_needMinHeight, true)
            needThumbOutside = array.getBoolean(R.styleable.PlaySeekBar_needThumbOutside, true)
            seekBarVerticalPadding = array.getDimensionPixelSize(R.styleable.PlaySeekBar_seekBarVerticalPadding, 0)
            baseLineHeight = array.getDimension(
                R.styleable.PlaySeekBar_baseLineHeight,
                res.getDimension(R.dimen.videoeditor_play_seekbar_base_line_height)
            )
            baseLineRadius = array.getDimension(
                R.styleable.PlaySeekBar_baseLineRadius,
                res.getDimension(R.dimen.videoeditor_play_seekbar_base_line_radius)
            )
            baseLineColor = array.getColor(
                R.styleable.PlaySeekBar_baseLineColor,
                res.getColor(R.color.videoeditor_play_seekbar_base_line, null)
            )
            needCacheProgress = array.getBoolean(R.styleable.PlaySeekBar_needCacheProgress, false)
            cacheProgressColor = array.getColor(
                R.styleable.PlaySeekBar_cacheProgressColor,
                res.getColor(R.color.videoeditor_play_seekbar_cache_progress, null)
            )
            needPlayProgress = array.getBoolean(R.styleable.PlaySeekBar_needPlayProgress, true)
            playProgressColor = array.getColor(
                R.styleable.PlaySeekBar_playProgressColor,
                res.getColor(R.color.videoeditor_play_seekbar_play_progress, null)
            )
            thumbRadius = array.getDimension(
                R.styleable.PlaySeekBar_thumbRadius,
                res.getDimension(R.dimen.videoeditor_play_seekbar_thumb_radius)
            )
            thumbColor = array.getColor(
                R.styleable.PlaySeekBar_thumbColor,
                res.getColor(R.color.videoeditor_play_seekbar_thumb, null)
            )
            needPressedAnimator = array.getBoolean(R.styleable.PlaySeekBar_needPressedAnimator, false)
        } finally {
            array.recycle()
        }
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        baseLinePaint.apply {
            isAntiAlias = true
            isDither = true
            color = baseLineColor
        }
        cacheProgressPaint.apply {
            isAntiAlias = true
            isDither = true
            color = cacheProgressColor
        }
        playProgressPaint.apply {
            isAntiAlias = true
            isDither = true
            color = playProgressColor
        }
        thumbPaint.apply {
            isAntiAlias = true
            isDither = true
            color = thumbColor
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        if (needThumbOutside) {
            val baseLineLeft = paddingLeft + thumbRadius - baseLineRadius
            val baseLineRight = width - paddingRight - thumbRadius + baseLineRadius
            val baseLineCenterY = height - seekBarVerticalPadding - thumbRadius
            baseLineWidth = width - paddingRight - thumbRadius * 2 - paddingLeft
            baseLineRect.set(
                baseLineLeft,
                baseLineCenterY - baseLineHeight / MathUtils.TWO_F,
                baseLineRight,
                baseLineCenterY + baseLineHeight / MathUtils.TWO_F
            )
            thumbLeft = paddingLeft + thumbRadius
        } else {
            val baseLineLeft = paddingLeft.toFloat()
            val baseLineRight = (width - paddingRight).toFloat()
            val baseLineCenterY = height - seekBarVerticalPadding - thumbRadius
            baseLineWidth = (width - paddingRight - paddingLeft).toFloat()
            baseLineRect.set(
                baseLineLeft,
                baseLineCenterY - baseLineHeight / MathUtils.TWO_F,
                baseLineRight,
                baseLineCenterY + baseLineHeight / MathUtils.TWO_F
            )
            thumbLeft = paddingLeft + thumbRadius
        }
    }

    override fun onDraw(canvas: Canvas) {
        calculateData()
        drawBaseLine(canvas)
        drawCacheProgressLine(canvas)
        drawPlayProgressLine(canvas)
        drawThumb(canvas)
    }

    /**
     * 计算绘制时，用到的尺寸值
     */
    private fun calculateData() {
        // 计算实际进度区域的宽度。如果拇指按钮边缘，不允许超出滚动条边界，那需要左右分别减去拇指按钮的半径
        val progressWidth = if (needThumbOutside) baseLineWidth else (baseLineWidth - thumbRadius * 2)
        thumbX = thumbLeft + progress * progressWidth / maxProgress.toFloat()
    }

    /**
     * 绘制滑条最下层的线条
     */
    private fun drawBaseLine(canvas: Canvas) {
        val rectF = if (needPressedAnimator) scaleLineRectHeight(baseLineRect) else baseLineRect
        val radius = if (needPressedAnimator) baseLineRadius * pressedScale else baseLineRadius
        canvas.drawRoundRect(rectF, radius, radius, baseLinePaint)
    }

    /**
     * 绘制缓存的进度条
     */
    private fun drawCacheProgressLine(canvas: Canvas) {
        if (needCacheProgress) {
            val radius = if (needPressedAnimator) baseLineRadius * pressedScale else baseLineRadius
            val progressWidth = if (needThumbOutside) baseLineWidth else (baseLineWidth - thumbRadius * 2)
            val cacheProgressRight = thumbLeft + cacheProgress * progressWidth / maxProgress.toFloat() + radius
            val cacheProgressRect = RectF(baseLineRect.left, baseLineRect.top, cacheProgressRight, baseLineRect.bottom)
            val rectF = if (needPressedAnimator) scaleLineRectHeight(cacheProgressRect) else cacheProgressRect
            canvas.drawRoundRect(rectF, radius, radius, cacheProgressPaint)
        }
    }

    /**
     * 绘制播放进度条
     */
    private fun drawPlayProgressLine(canvas: Canvas) {
        if (needPlayProgress) {
            val radius = if (needPressedAnimator) baseLineRadius * pressedScale else baseLineRadius
            val playProgressRect = RectF(baseLineRect.left, baseLineRect.top, thumbX, baseLineRect.bottom)
            val rectF = if (needPressedAnimator) scaleLineRectHeight(playProgressRect) else playProgressRect
            canvas.drawRoundRect(rectF, radius, radius, playProgressPaint)
        }
    }

    /**
     * 放大线条高度
     *
     * @param oldRectF 原矩阵
     * @return 放大高度后矩阵
     */
    private fun scaleLineRectHeight(oldRectF: RectF): RectF {
        if (pressedScale == SCALE_NORMAL) return oldRectF

        val halfHeight = oldRectF.height() * pressedScale / MathUtils.TWO_F
        return RectF(oldRectF.left, oldRectF.centerY() - halfHeight, oldRectF.right, oldRectF.centerY() + halfHeight)
    }

    /**
     * 绘制拇指按钮
     */
    private fun drawThumb(canvas: Canvas) {
        val radius = if (needPressedAnimator) thumbRadius * pressedScale else thumbRadius
        canvas.drawCircle(thumbX, baseLineRect.centerY(), radius, thumbPaint)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        var viewHeight = MeasureSpec.getSize(heightMeasureSpec)
        val viewWidth = MeasureSpec.getSize(widthMeasureSpec)
        val minHeight = (thumbRadius * 2).roundToInt() + 2 * seekBarVerticalPadding + paddingTop + paddingBottom
        if (needMinHeight) {
            if (MeasureSpec.EXACTLY != heightMode) {
                viewHeight = minHeight
            } else if (viewHeight < minHeight) {
                viewHeight = minHeight
            }
        }

        setMeasuredDimension(viewWidth, viewHeight)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (isEnabled.not()) return false

        val result = gestureDetector.onTouchEvent(event)
        if ((event.action == MotionEvent.ACTION_UP) || (event.action == MotionEvent.ACTION_CANCEL)) {
            doActionUpOrCancel(event)
        }
        return result
    }

    /**
     * 手指开始按压时，滑条的事件处理
     */
    private fun doActionDown(event: MotionEvent): Boolean {
        isPlayingBeforeDown = player?.isPlaying() ?: false
        isPressed = true
        isDragging = true
        if (needPressedAnimator) pressedScale = SCALE_PRESSED
        lastX = event.x
        player?.getCurrentProgress()?.also {
            GLog.d(TAG, LogFlag.DL) { "[doActionDown] initProgress: $progress, currentProgress: $it" }
            progress = it
        }
        onSeekBarChangeListener?.onStartTrackingTouch(this@PlaySeekBar, isPlayingBeforeDown)

        // 请求父组件不拦截事件
        (parent as? ViewGroup)?.requestDisallowInterceptTouchEvent(true)
        return true
    }

    /**
     * 手指开始抬起或取消时，滑条的事件处理
     */
    private fun doActionUpOrCancel(event: MotionEvent): Boolean {
        GLog.d(TAG, LogFlag.DL) { "[doActionUpOrCancel] ACTION_UP" }
        isPressed = false
        isDragging = false
        pressedScale = SCALE_NORMAL
        invalidate()
        onSeekBarChangeListener?.onStopTrackingTouch(this@PlaySeekBar, isPlayingBeforeDown)
        return true
    }

    interface IPlayer {

        /**
         * 获取当前播放的位置
         */
        fun getCurrentProgress(): Int

        /**
         * 获取当前是否为播放状态
         */
        fun isPlaying(): Boolean
    }

    /**
     * 滚动条滑动事件监听器
     */
    interface OnSeekBarChangeListener {
        /**
         * 开始滑动
         *
         * @param seekBar 滚动条
         * @param isPlayingBeforeDown 播放器触摸前，是否处于播放状态
         */
        fun onStartTrackingTouch(seekBar: PlaySeekBar, isPlayingBeforeDown: Boolean)

        /**
         * 滑动过程中
         *
         * @param seekBar 滚动条
         * @param progress 滑动到的位置
         * @param fromUser 是否用户触发
         */
        fun onProgressChanged(seekBar: PlaySeekBar, progress: Int, fromUser: Boolean)

        /**
         * 结束滑动
         *
         * @param seekBar 滚动条
         * @param isPlayingBeforeDown 播放器触摸前，是否处于播放状态
         */
        fun onStopTrackingTouch(seekBar: PlaySeekBar, isPlayingBeforeDown: Boolean)
    }

    inner class GestureProcessor : GestureDetector.OnGestureListener {
        override fun onDown(event: MotionEvent): Boolean = doActionDown(event)

        override fun onShowPress(event: MotionEvent) = Unit

        override fun onSingleTapUp(event: MotionEvent): Boolean {
            GLog.d(TAG, LogFlag.DL) { "ClickGestureProcessor onSingleTapUp" }
            val progressWidth = if (needThumbOutside) baseLineWidth else (baseLineWidth - thumbRadius * 2)
            val newProgress = (maxProgress * (event.x - paddingLeft - thumbRadius) / progressWidth).roundToInt()
            progress = newProgress.coerceIn(0, maxProgress)
            onSeekBarChangeListener?.onProgressChanged(this@PlaySeekBar, progress, true)
            return true
        }

        override fun onScroll(eventDown: MotionEvent?, eventMove: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
            if (isDragging) {
                val eventMoveX = eventMove.x
                val offsetX = eventMoveX - lastX
                var newProgress = progress + (offsetX / baseLineWidth * maxProgress).roundToInt()
                newProgress = newProgress.coerceIn(0, maxProgress)
                val oldProgress = progress
                if (oldProgress != newProgress) {
                    lastX = eventMoveX
                    progress = newProgress
                    onSeekBarChangeListener?.onProgressChanged(this@PlaySeekBar, progress, true)
                }
            }
            return true
        }

        override fun onLongPress(event: MotionEvent) = Unit

        override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean = false
    }

    internal companion object {
        private const val TAG = "PlaySeekBar"

        /**
         * 滑条可滑到的最大值
         */
        private const val PROGRESS_MAX = 100

        /**
         * 播放条位置的默认值
         */
        private const val DEFAULT_PROGRESS = 0

        /**
         * 正常放大比例
         */
        private const val SCALE_NORMAL = 1.0f

        /**
         * 按压时放大比例
         */
        private const val SCALE_PRESSED = 1.5f

        /**
         * 触摸超出偏移范围，不响应事件
         */
        private const val TOUCH_DURING_OFFSET = 40
        /**
         * 播放条位置的刻度值
         */
        private const val DEFAULT_DISPLAY_VALUE = 0L
    }
}