/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - TransformOffsetInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import java.io.Serializable;

public class TransformOffsetInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private float mTransXOffset;
    private float mTransYOffset;
    private float mScaleXOffset = 1;
    private float mScaleYOffset = 1;
    private float mRotationZOffset;

    public float getTransXOffset() {
        return mTransXOffset;
    }

    public void setTransXOffset(float transXOffset) {
        this.mTransXOffset = transXOffset;
    }

    public float getTransYOffset() {
        return mTransYOffset;
    }

    public void setTransYOffset(float transYOffset) {
        this.mTransYOffset = transYOffset;
    }

    public float getScaleXOffset() {
        return mScaleXOffset;
    }

    public void setScaleXOffset(float scaleXOffset) {
        this.mScaleXOffset = scaleXOffset;
    }

    public float getScaleYOffset() {
        return mScaleYOffset;
    }

    public void setScaleYOffset(float scaleYOffset) {
        this.mScaleYOffset = scaleYOffset;
    }

    public float getRotationZOffset() {
        return mRotationZOffset;
    }

    public void setRotationZOffset(float rotationZOffset) {
        this.mRotationZOffset = rotationZOffset;
    }

    public void resetDefault() {
        mTransXOffset = 0;
        mTransYOffset = 0;
        mScaleXOffset = 1;
        mScaleYOffset = 1;
        mRotationZOffset = 0;
    }

    @Override
    public String toString() {
        return "TransformOffsetInfo{"
                + "mTransXOffset=" + mTransXOffset
                + ", mTransYOffset=" + mTransYOffset
                + ", mScaleXOffset=" + mScaleXOffset
                + ", mScaleYOffset=" + mScaleYOffset
                + ", mRotationZOffset=" + mRotationZOffset
                + '}';
    }
}
