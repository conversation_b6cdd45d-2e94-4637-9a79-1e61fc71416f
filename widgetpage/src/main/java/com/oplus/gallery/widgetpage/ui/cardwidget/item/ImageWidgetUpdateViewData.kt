/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ImageWidgetUpdateViewData.kt
 ** Description:显示照片的卡片样式
 ** Version: 1.0
 ** Date : 2021/11/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  zhen<PERSON><EMAIL>    2021/11/26        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item

import android.net.Uri
import android.os.Bundle
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.widgetpage.WidgetConstants

data class ImageWidgetUpdateViewData(
    val currentUri: Uri,
    val previousUri: Uri?,
    val itemPath: Path,
    val setPath: Path,
    val extra: Bundle
) : WidgetUpdateViewData(
    WidgetConstants.Layout.IMAGE
)