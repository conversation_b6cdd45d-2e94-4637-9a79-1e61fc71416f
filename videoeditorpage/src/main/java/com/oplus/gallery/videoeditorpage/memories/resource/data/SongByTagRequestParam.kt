/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SongByTagRequestParam.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/28
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/28		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.data

import com.google.gson.annotations.SerializedName

/**
 * 通过标签获取歌曲的请求参数类
 * 用于封装请求中包含的类别、页码和页面大小等信息
 */
data class SongByTagRequestParam(
    /**
     * 歌曲类别
     * 用于指定请求中的类别，以便获取特定类别下的歌曲
     */
    @SerializedName("category")
    var category: String? = null,

    /**
     * 页码
     * 用于指定请求的页码，以实现分页查询，默认值为-1，表示未设置页码
     */
    @SerializedName("pageNum")
    var pageNum: Int? = -1,

    /**
     * 页面大小
     * 用于指定请求的页面大小，以实现分页查询，默认值为-1，表示未设置页面大小
     */
    @SerializedName("pageSize")
    var pageSize: Int? = -1
)

/**
 * 歌曲类别枚举类
 * 用于定义歌曲类别，包括视频类和回忆类
 */
enum class SongCategory {
    /**
     * 视频类
     */
    VIDEO,

    /**
     * 回忆类
     */
    MEMORY
}