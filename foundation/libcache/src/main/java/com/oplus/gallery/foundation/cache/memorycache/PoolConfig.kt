/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : PoolConfig.kt
 ** Description: 缓存池配置项
 ** Version    : 1.0
 ** Date       : 2025/1/8
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2025/1/8      1.0              init
 *************************************************************************************************/

package com.oplus.gallery.foundation.cache.memorycache

import com.oplus.gallery.foundation.cache.BuildConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 缓存池配置项
 */
data class PoolConfig(
    /** 标记，用于日志打印 */
    val tag: String,
    /** 缓存池最大值，单位字节 */
    val totalMaxSize: Long,
    /** 单个bitmap最大值，单位字节，默认不限制 */
    val singleMaxSize: Long = Long.MAX_VALUE,
    /** 闲置缓存最大值，单位字节，默认全部清除 */
    val idleMaxSize: Long = 0,
    /** 复用bitmap时，允许被复用的bitmap大于要求大小的倍数，参考Glide默认为8 */
    val maxReuseMultiple: Float = DEFAULT_MAX_REUSE_MULTIPLE,
    /** 闲置缓存超时时间，单位毫秒，超时后要裁剪到 idleMaxSize */
    val idleDurationThreshold: Int = 0,
) {
    /**
     * 检查配置信息是否正常
     */
    internal fun checkConfigValid(): Boolean {
        when {
            (totalMaxSize <= 0) -> {
                GLog.e(TAG, LogFlag.DL) {
                    "[checkConfigValid] config invalid, totalMaxSize should be greater than 0"
                }
            }

            (idleMaxSize < 0) -> {
                GLog.e(TAG, LogFlag.DL) {
                    "[checkConfigValid] config invalid, idleMaxSize should be greater than or equal to 0"
                }
            }

            (idleMaxSize > totalMaxSize) -> {
                GLog.e(TAG, LogFlag.DL) {
                    "[checkConfigValid] config invalid, idleMaxSize should be less than totalMaxSize"
                }
            }

            (maxReuseMultiple < MIN_REUSE_MULTIPLE) -> {
                GLog.e(TAG, LogFlag.DL) {
                    "[checkConfigValid] config invalid, maxReuseMultiple should be greater than or equal to $MIN_REUSE_MULTIPLE"
                }
            }

            else -> return true
        }
        if (BuildConfig.DEBUG) {
            throw IllegalArgumentException("config invalid, $this")
        } else {
            return false
        }
    }

    companion object {
        private const val TAG = "PoolConfig"
        const val MIN_REUSE_MULTIPLE = 1f
        private const val DEFAULT_MAX_REUSE_MULTIPLE = 2f
    }
}