/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RoundedCropImageView.kt
 ** Description: A custom ImageView that clips the image to a rounded rectangle shape.
 **
 **
 ** Version: 1.0
 ** Date : 2025/1/18
 *  ** Author: 80410313
 ** ------------------------------- Revision History: ----------------------------
 ** <author>    <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** 80410313   	2025/1/18	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.ui

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

/**
 * 自定义ImageView，将图像裁剪为圆角矩形形状。
 * @property roundRectPath Path 圆角矩形的路径
 * @property leftTopRadius Float 左上圆角
 * @property rightTopRadius Float 右上圆角
 * @property rightBottomRadius Float 右下圆角
 * @property leftBottomRadius Float 左下圆角
 * @property cropWidth Int 如果cropWidth不为0，则此值作为图片的宽度
 * @constructor
 */
@SuppressLint("ViewConstructor")
class RoundedCropImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    /**
     * 圆角矩形的路径
     */
    private val roundRectPath = Path()

    /**
     * 左上角圆角的大小
     */
    private var leftTopRadius: Float = 0f

    /**
     * 右上角圆角的大小
     */
    private var rightTopRadius: Float = 0f

    /**
     * 右下角圆角的大小
     */
    private var rightBottomRadius: Float = 0f

    /**
     * 左下角圆角的大小
     */
    private var leftBottomRadius: Float = 0f

    /**
     * 图片裁切之后的宽度，默认不裁切
     */
    private var cropWidth: Int = 0

    init {
        /*
            关闭硬件加速启用软件渲染
            硬件加速（GPU 渲染）和软件渲染（CPU 渲染）对图形处理的计算精度和抗锯齿策略不同：
            ‌硬件加速‌：
            GPU 使用浮点运算处理图形，但不同设备的 GPU 浮点精度存在差异。裁剪路径（如 clipPath）的边缘计算可能因精度问题导致 ‌亚像素级间隙‌。
            ‌软件渲染‌：
            CPU 采用更稳定的整数运算，路径裁剪的边缘计算更精确，间隙问题消失‌。
         */
        setLayerType(LAYER_TYPE_SOFTWARE, null)
    }

    /**
     * 设置裁切之后的宽度
     * @param cropW Int 裁切后的宽度
     */
    fun setCropWidth(cropW: Int) {
        this.cropWidth = cropW
    }

    /**
     * 设置左上圆角大小
     * @param leftTopRadius Float 圆角大小
     */
    fun setLeftTopRadius(leftTopRadius: Float) {
        this.leftTopRadius = leftTopRadius
    }

    /**
     * 设置右上圆角大小
     * @param rightTopRadius Float 圆角大小
     */
    fun setRightTopRadius(rightTopRadius: Float) {
        this.rightTopRadius = rightTopRadius
    }

    /**
     * 设置右下圆角大小
     * @param rightBottomRadius Float 圆角大小
     */
    fun setRightBottomRadius(rightBottomRadius: Float) {
        this.rightBottomRadius = rightBottomRadius
    }

    /**
     * 设置左下圆角大小
     * @param leftBottomRadius Float 圆角大小
     */
    fun setLeftBottomRadius(leftBottomRadius: Float) {
        this.leftBottomRadius = leftBottomRadius
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        val right = if (cropWidth == 0) width else cropWidth
        canvas.clipRect(0, 0, right, height)
        // 只针对需要设置圆角的视图进行裁剪
        if ((leftTopRadius > 0) || (rightTopRadius > 0) || (leftBottomRadius > 0) || (rightBottomRadius > 0)) {
            val rectF = RectF(0f, 0f, right.toFloat(), height.toFloat())
            roundRectPath.reset()
            roundRectPath.addRoundRect(
                rectF,
                floatArrayOf(
                    leftTopRadius, leftTopRadius,
                    rightTopRadius, rightTopRadius,
                    rightBottomRadius, rightBottomRadius,
                    leftBottomRadius, leftBottomRadius
                ),
                Path.Direction.CW
            )
            canvas.clipPath(roundRectPath)
        }
        super.onDraw(canvas)
    }
}