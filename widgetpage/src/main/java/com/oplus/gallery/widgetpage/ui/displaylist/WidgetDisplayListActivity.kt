/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WidgetDisplayListActivity
 ** Description:
 ** Version: 1.0
 ** Date: 2020-11-11
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG: OPLUS_WIDGET
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2020/11/11     1.0        ADD
 ********************************************************************************/
package com.oplus.gallery.widgetpage.ui.displaylist

import android.os.Bundle
import android.text.TextUtils
import androidx.fragment.app.FragmentManager
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_ALBUM_TITLE
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MEDIA_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MODEL_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_CODE
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_MODE
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.WIDGET_DISPLAY_LIST_ACTIVITY
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.basebiz.uikit.activity.BasePermissionsActivity
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.fragment.transition.SlideFromBottomTransitionAnimation
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns.MODE_DEFAULT
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.widgetpage.R
import com.oplus.gallery.basebiz.R as BasebizR

@RouterNormal(path = WIDGET_DISPLAY_LIST_ACTIVITY)
class WidgetDisplayListActivity : BasePermissionsActivity() {

    companion object {
        const val TAG = "WidgetDisplayListActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView()
        val widgetCode = IntentUtils.getStringExtra(intent, KEY_WIDGET_CODE)
        if (TextUtils.isEmpty(widgetCode)) {
            GLog.e(TAG, "onCreate widgetCode=$widgetCode invalid!")
            finish()
            return
        }
        supportFragmentManager.start(
            resId = BasebizR.id.base_fragment_container,
            postCard = PostCard(RouterConstants.RouterName.WIDGET_DISPLAY_LIST_FRAGMENT),
            fragmentStack = this,
            addToBackStack = true,
            data = Bundle().apply {
                putString(KEY_WIDGET_CODE, widgetCode)
                putInt(KEY_WIDGET_MODE, IntentUtils.getIntExtra(intent, KEY_WIDGET_MODE, MODE_DEFAULT))
                putString(KEY_MEDIA_PATH, SourceConstants.Local.PATH_ALBUM_WIDGET_DISPLAY_LIST.getChild(widgetCode).toString())
                putString(KEY_MODEL_TYPE, DataRepository.LocalAlbumModelGetter.TYPE_WIDGET_DISPLAY_LIST_ALBUM)
                putString(KEY_ALBUM_TITLE, getString(R.string.widget_custom_title))
                putBoolean(AlbumViewData.SUPPORT_IS_SELF_ALBUM, true)
            },
            flags = FragmentManager.POP_BACK_STACK_INCLUSIVE,
            anim = SlideFromBottomTransitionAnimation.SLIDE_FROM_BOTTOM_ANIM_ARRAY
        )
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(BasebizR.anim.base_fragment_enter_empty_anim, BasebizR.anim.oplus_rounded_corners_base_fragment_push_down)
    }
}