/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EliminateIntroductionBuilder.kt
 ** Description: 消除-用户引导弹窗
 ** Version: 1.0
 ** Date: 2024/04/22
 ** Author: <EMAIL>
 *
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** <EMAIL>     2024/04/22        1.0            Add new file.
 *************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.eliminate.view

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.indicator.COUIPageIndicator
import com.coui.appcompat.viewpager.COUIViewPager2
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.util.ext.runOnWorkThread
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.util.ThemeColorConfigHelper
import com.oplus.gallery.photoeditor.util.setDialogBackgroundColor
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils

/**
 * 消除-用户引导弹窗-多数据
 */
class EliminateIntroductionBuilder @JvmOverloads constructor(
    private val context: Context,
    colorThemeResId: Int = com.support.appcompat.R.style.Theme_COUI_Dark_Yellow,
    dialogStyleResId: Int = com.support.dialog.R.style.COUIAlertDialog_BottomWarning
) : COUIAlertDialogBuilder(context, colorThemeResId, dialogStyleResId) {

    /**
     * 自定义布局容器
     */
    private var contentView: View? = null
    private var realDialog: AlertDialog? = null
    private var guidePager: COUIViewPager2? = null
    private var imageIndicator: COUIPageIndicator? = null

    private var viewDataList: List<EliminateIntroductionViewData>? = null

    /**
     * 智能消除路人消除是否支持
     */
    private val isSupportPassersEliminate by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_PASSERS_ELIMINATE)
    }

    init {
        initData()
        createView()
    }

    private fun createView() {
        contentView = LayoutInflater.from(context).inflate(R.layout.picture3d_editor_eliminate_introduction, null).apply {
            guidePager = findViewById(R.id.guide_viewpager)
            viewDataList?.let {
                guidePager?.adapter = EliminateIntroductionPagerAdapter(it)
            }
            imageIndicator = findViewById<COUIPageIndicator?>(R.id.page_indicator_dot).apply {
                dotsCount = viewDataList?.size ?: 0
                setImagePagerCallback(this)
            }
            findViewById<COUIButton>(R.id.btn_introduction_ok)?.apply {
                drawableColor = ThemeColorConfigHelper.getThemeColor(context)
                setTextColor(context.resources.getColor(R.color.photo_editor_text_color_black, null))
                setOnClickListener {
                    if (DoubleClickUtils.isFastDoubleClick().not()) {
                        dismiss()
                    }
                }
            }
        }
        initSelectPage()
    }

    private fun dismiss() {
        realDialog?.takeIf { it.isShowing }?.dismiss()
    }

    override fun show(): AlertDialog? {
        if (isDestroy()) return null
        setView(contentView)
        // 开启模糊背景效果 mark by zhongfonan: 有Otest crash问题，先临时关闭高斯模糊
        setBlurBackgroundDrawable(false)
        realDialog = super.show()
        realDialog?.setCanceledOnTouchOutside(false)
        realDialog?.setDialogBackgroundColor()
        return realDialog
    }

    private fun initSelectPage() {
        val isAIEliminateFirstUse =
            ConfigAbilityWrapper.getBoolean(ConfigID.Business.Editor.PhotoEditor.FIRST_USING_AI_ELIMINATE_INTRODUCTION, true)
        val isPasserbyEliminateFirstUse =
            ConfigAbilityWrapper.getBoolean(ConfigID.Business.Editor.PhotoEditor.FIRST_USING_PASSERBY_ELIMINATE_INTRODUCTION, true)
        if (isPasserbyEliminateFirstUse && isSupportPassersEliminate && isAIEliminateFirstUse.not()) {
            guidePager?.setCurrentItem(PASSERBY_ELIMINATE_PAGE_INDEX, false)
            imageIndicator?.onPageSelected(PASSERBY_ELIMINATE_PAGE_INDEX)
        }
        runOnWorkThread {
            context.getAppAbility<ISettingsAbility>()?.use {
                if (isAIEliminateFirstUse) {
                    it.markAIEliminateIntroductionUsed()
                }
                if (isPasserbyEliminateFirstUse && isSupportPassersEliminate) {
                    it.markPasserbyEliminateIntroductionUsed()
                }
            }
        }
    }


    private fun isDestroy(): Boolean {
        return (context as? Activity)?.let {
            it.isFinishing || it.isDestroyed
        } ?: true
    }

    private fun setImagePagerCallback(imageIndicator: COUIPageIndicator) {
        guidePager?.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                imageIndicator.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }

            override fun onPageSelected(position: Int) {
                imageIndicator.onPageSelected(position)
                updateViewData(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                imageIndicator.onPageScrollStateChanged(state)
            }
        })
    }

    private fun updateViewData(currentPos: Int) {
        viewDataList?.let {
            for ((index, data) in it.withIndex()) {
                data.needPlayAnim = index == currentPos
            }
            guidePager?.adapter?.notifyDataSetChanged()
        }
    }

    private fun initData() {
        viewDataList = mutableListOf<EliminateIntroductionViewData>().apply {
            add(
                EliminateIntroductionViewData(
                    R.string.picture3d_editor_text_eliminate_ai_circle_selection,
                    R.string.picture3d_editor_text_eliminate_introduction_circle_selection_desc_new,
                    R.raw.ai_circle_selection,
                    R.drawable.ai_circle_selection_icon,
                    needPlayAnim = true
                )
            )
            add(
                EliminateIntroductionViewData(
                    R.string.picture3d_editor_text_eliminate_smear,
                    R.string.picture3d_editor_text_eliminate_introduction_smear_desc_new,
                    R.raw.manual_smear,
                    R.drawable.manual_smear_icon,
                    needPlayAnim = false
                )
            )
            if (isSupportPassersEliminate) {
                add(
                    EliminateIntroductionViewData(
                        R.string.picture3d_editor_text_eliminate_passers,
                        R.string.picture3d_editor_text_eliminate_introduction_passers_desc_new,
                        R.raw.passers_eliminate,
                        R.drawable.passers_eliminate_icon,
                        needPlayAnim = false
                    )
                )
            }
        }
    }

    companion object {
        private const val PASSERBY_ELIMINATE_PAGE_INDEX = 2
    }
}

/**
 * 引导弹窗数据对象
 * @param titleResID 标题
 * @param descriptionResID 描述
 * @param rawResId 动图ID
 * @param needPlayAnim 是否执行动画, true:执行, false:不执行
 */
data class EliminateIntroductionViewData(
    val titleResID: Int = Resources.ID_NULL,
    val descriptionResID: Int = Resources.ID_NULL,
    val rawResId: Int = Resources.ID_NULL,
    val iconResId: Int = Resources.ID_NULL,
    // 标记是否播放动画
    var needPlayAnim: Boolean = false
)

