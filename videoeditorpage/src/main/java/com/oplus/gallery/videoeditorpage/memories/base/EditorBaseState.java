/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorBaseState.java
 * * Description: EditorBaseState.
 * * Version: 1.0
 * * Date : 2017/11/07
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/07    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.base;

import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter;
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog;
import com.oplus.gallery.foundation.uikit.app.ZoomWindowManager;
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility;
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.ui.BottomActionBar;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.LocalSourceManager;

public abstract class EditorBaseState {

    protected static final int MONITOR_DEFAULT = 1 << 0;
    protected static final int MONITOR_NET_STATE = 1 << 1;
    private static final int INVALID_RES_ID = 0;
    private static final String TAG = "EditorBaseState";
    private static final int NETWORK_STATE_NONE = 0;
    private static final int NETWORK_STATE_WIFI = 1;
    private static final int NETWORK_STATE_MOBILE = 2;
    protected Context mContext;
    protected ControlBarView mControlBarView;
    protected IVideoEditAbility mEngineManager;
    protected EditorBaseUIController mUIController;
    protected ConfirmDialog mContinueDownloadOnMobileDialog;
    protected ConfirmDialog mRequestDownloadOnMobileDialog;
    protected ConfirmDialog mRequestPermissionDialog;
    private NetworkMonitor.NetworkListener mNetworkListener;
    private boolean mHasUpdateList = false;
    private Handler mNetStateChangeHandler;
    private String mEditorTypeName;
    private int mCurrentNetWorkState = NETWORK_STATE_NONE;
    private int mPermitMonitor;
    private boolean mEnterRetryRequest = false;
    private ZoomWindowManager mZoomWindowManager;

    public EditorBaseState(String editorType, Context context, ControlBarView controlBarView) {
        this(editorType, context, controlBarView, MONITOR_DEFAULT);
    }

    public EditorBaseState(String editorType, Context context, ControlBarView controlBarView,
                           int permitMonitor) {
        mEditorTypeName = editorType;
        mContext = context;
        mControlBarView = controlBarView;
        mEngineManager = mControlBarView.getEngineManager();
        mEngineManager.setVideoBackgroundColor(Color.valueOf(
                context.getColor(com.oplus.gallery.basebiz.R.color.videoeditor_video_editor_background_color_edit)));
        mZoomWindowManager = mControlBarView.getZoomWindowManager();
        mUIController = createUIController();
        mPermitMonitor = permitMonitor;
        initListener();
    }

    public void setHasUpdateList(boolean hasUpdateList) {
        mHasUpdateList = hasUpdateList;
    }

    public boolean isHasUpdateList() {
        return mHasUpdateList;
    }


    private void initListener() {
        if (permitMonitorNetState()) {
            mNetStateChangeHandler = new NetworkChangeHandler();
            mNetworkListener = (connectType, isValidated) -> {
                if (mControlBarView.getCurrentEditor() == null) {
                    return;
                }
                final String currentTag = mControlBarView.getCurrentEditor().getCurrentTAG();
                final int networkState = toNetworkState(connectType, isValidated);
                GLog.d(TAG, "[onStateChange] networkState = " + networkState
                        + "; mEditorTypeName = " + mEditorTypeName
                        + "; currentTag = " + currentTag
                        + "; mCurrentNetWorkState = " + mCurrentNetWorkState);
                if (TextUtils.equals(mEditorTypeName, currentTag)) {
                    int preNetWorkState = mCurrentNetWorkState;
                    mCurrentNetWorkState = networkState;
                    int state = NetworkChangeHandler.DEFAULT_STATE;
                    if (mCurrentNetWorkState == NETWORK_STATE_NONE) {
                        if (preNetWorkState == NETWORK_STATE_WIFI) {
                            state = NetworkChangeHandler.FROM_WIFI_TO_NO_NETWORK;
                        } else if (preNetWorkState == NETWORK_STATE_MOBILE) {
                            state = NetworkChangeHandler.FROM_MOBILE_TO_NO_NETWORK;
                        } else {
                            state = NetworkChangeHandler.FROM_DEFAULT_TO_NO_NETWORK;
                        }
                    } else if (mCurrentNetWorkState == NETWORK_STATE_WIFI) {
                        if (preNetWorkState == NETWORK_STATE_MOBILE) {
                            state = NetworkChangeHandler.FROM_MOBILE_TO_WIFI;
                        }
                    } else if (mCurrentNetWorkState == NETWORK_STATE_MOBILE) {
                        if (preNetWorkState == NETWORK_STATE_WIFI) {
                            state = NetworkChangeHandler.FROM_WIFI_TO_MOBILE;
                        }
                    }
                    GLog.d(TAG, "[onStateChange] state = " + state + "; preNetWorkState = "
                            + preNetWorkState + "; mCurrentNetWorkState = " + mCurrentNetWorkState);
                    if (state != NetworkChangeHandler.DEFAULT_STATE) {
                        LocalSourceManager.cancelAllDownloadTask();
                        Message message = Message.obtain();
                        message.arg1 = state;
                        message.what = NetworkChangeHandler.MSG_RETRY_REQUEST;
                        mNetStateChangeHandler.removeMessages(NetworkChangeHandler.MSG_RETRY_REQUEST);
                        mNetStateChangeHandler.sendMessageDelayed(message, NetworkChangeHandler.RETRY_REQUEST_DELAY);
                    }
                    if ((mCurrentNetWorkState > NETWORK_STATE_NONE)
                            && (mCurrentNetWorkState != preNetWorkState)) {
                        mNetStateChangeHandler.removeMessages(NetworkChangeHandler.MSG_REQUEST_LIST);
                        mNetStateChangeHandler.sendEmptyMessageDelayed(NetworkChangeHandler.MSG_REQUEST_LIST,
                                NetworkChangeHandler.RETRY_REQUEST_DELAY);
                    }
                }
            };
            GLog.d(TAG, "addNetStateListener typeName = " + mEditorTypeName);
            NetworkMonitor.addListener(mNetworkListener);
        }
    }

    private int toNetworkState(NetworkMonitor.ConnectType connectType, boolean isValidated) {
        int state = NETWORK_STATE_NONE;
        if (isValidated) {
            if (connectType == NetworkMonitor.ConnectType.MOBILE) {
                state = NETWORK_STATE_MOBILE;
            } else if (connectType == NetworkMonitor.ConnectType.WIFI) {
                state = NETWORK_STATE_WIFI;
            }
        }
        return state;
    }

    private boolean permitMonitorNetState() {
        return ((mPermitMonitor & MONITOR_NET_STATE) == MONITOR_NET_STATE);
    }

    public IVideoEditAbility getEngineManager() {
        return mEngineManager;
    }

    public ZoomWindowManager getZoomWindowManager() {
        return mZoomWindowManager;
    }

    public void onChangeToNetwork() {
        GLog.d(TAG, "onChangeToNetwork");
    }

    protected abstract EditorBaseUIController createUIController();

    /**
     * 点击右下角按钮时会触发此方法
     *
     * @return 是否需要退出此页面
     */
    public boolean done() {
        return true;
    }

    public void cancel() {
        mUIController.cancel();
        if (mNetworkListener != null) {
            GLog.d(TAG, "removeNetStateListener typeName = " + mEditorTypeName);
            NetworkMonitor.removeListener(mNetworkListener);
            mNetworkListener = null;
        }
    }

    public void click(View view) {
    }

    /**
     * 显示对应Controller
     * @param isNeedAnimator 切换时视频预览view和时间显示/进度条view是否需要做动画
     * @param isMenuNeedAnimator 切换时菜单栏是否需要做动画
     */
    public void show(boolean isNeedAnimator, boolean isMenuNeedAnimator) {
        mUIController.show(isNeedAnimator, isMenuNeedAnimator);
        mUIController.updateVideoDurationTextView(mEngineManager.getTotalTime());
        mUIController.updateCurrentTimeTextView();
    }

    public void setActionDoneEnable(boolean enable) {
        BottomActionBar bottomActionBar = mUIController.getBottomActionBar();
        GLog.d(TAG, "setActionDoneEnable enable = " + enable + ", bottomActionBar = " + bottomActionBar);
        if (bottomActionBar != null) {
            bottomActionBar.setActionDoneEnable(enable);
        }
    }

    public void hide(boolean animate, boolean removeContainer) {
        mUIController.hide(animate, removeContainer);
    }

    public void hide(boolean animate) {
        mUIController.hide(animate);
    }

    public void onDestroy() {
        mUIController.onDestroy();
        GLog.d(TAG, "onDestroy: " + mEditorTypeName);
        if (mNetworkListener != null) {
            NetworkMonitor.removeListener(mNetworkListener);
            mNetworkListener = null;
        }
    }

    public EditorBaseUIController getUIController() {
        return mUIController;
    }

    public int getCurrentSelection() {
        return mUIController.getCurrentSelection();
    }

    public void addExtraViewToRoot(View view, boolean animate) {
        mUIController.addExtraViewToRootView(view, animate);
    }

    public void removeExtraViewFromRoot(View view, boolean animate) {
        mUIController.removeExtraViewFromRootView(view, animate);
    }

    public boolean onBackPressed() {
        GLog.d(TAG, "onBackPressed");
        mControlBarView.changeToFirstEnterState();
        return true;
    }

    @Override
    public int hashCode() {
        return ((!TextUtils.isEmpty(mEditorTypeName)) ? mEditorTypeName.hashCode() : 0);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof EditorBaseState) {
            if (!TextUtils.isEmpty(mEditorTypeName)
                    && mEditorTypeName.equals(((EditorBaseState) obj).mEditorTypeName)) {
                return true;
            }
        }
        return false;
    }

    public String getCurrentTAG() {
        return mEditorTypeName;
    }

    public void onPlayPositionChange() {
        mUIController.updateCurrentTimeTextView();
    }

    public void updateVideoDurationTextView() {
        mUIController.updateVideoDurationTextView(mEngineManager.getTotalTime());
    }

    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig uiConfig) {
        mUIController.onAppUiStateChanged(uiConfig);
    }

    public AppUiResponder.AppUiConfig getAppUiConfig() {
        return mControlBarView.getAppUiConfig();
    }

    public void onPlayStatusChange() {
        mUIController.switchPlayButtonStatus();
    }

    public void onResetPlay() {
    }

    public void onWindowChangeRefresh() {

    }

    public void onPlayFinish() {
    }

    public void createAndShowContinueDownloadOnMobileDialog(int titleId, int msgId) {
        if (mContinueDownloadOnMobileDialog == null) {
            if ((titleId == INVALID_RES_ID) || (msgId == INVALID_RES_ID)) {
                return;
            }
            GLog.d(TAG, "createContinueDownloadOnMobileDialog");
            final ConfirmDialog.Builder builder = new ConfirmDialog.Builder(mContext);
            builder.setMessage(msgId)
                    .setPositiveButton(R.string.videoeditor_video_editor_download_continue, (dialog, whichButton) -> {
                        dialog.dismiss();
                        NetworkPermissionManager.setAllowDownloadOnMobile(true);
                        onAllowUseMobileData();
                    })
                    .setNegativeButton(R.string.videoeditor_component_download_cancel, (dialog, whichButton) -> {
                        dialog.dismiss();
                        onDisallowUseMobileData();
                        final EditorBaseUIController controller = getUIController();
                        if (controller == null) {
                            GLog.e(TAG, "controller is null!");
                            return;
                        }
                        final BaseRecyclerAdapter adapter = controller.getAdapter();
                        if (adapter == null) {
                            GLog.e(TAG, "adapter is null!");
                            return;
                        }
                        adapter.notifyDataSetChanged();
                    })
                    .setOnCancelListener(dialogInterface -> {
                        dialogInterface.dismiss();
                        onDisallowUseMobileData();
                        final EditorBaseUIController controller = getUIController();
                        if (controller == null) {
                            GLog.e(TAG, "controller is null!");
                            return;
                        }
                        final BaseRecyclerAdapter adapter = controller.getAdapter();
                        if (adapter == null) {
                            GLog.e(TAG, "adapter is null!");
                            return;
                        }
                        adapter.notifyDataSetChanged();
                    })
                    .setTitle(titleId);
            mContinueDownloadOnMobileDialog = builder.build().show();
            android.widget.Button button = mContinueDownloadOnMobileDialog.getButton(DialogInterface.BUTTON_POSITIVE);
            if (button != null) {
                button.setTextColor(mContext.getResources().getColor(
                        R.color.videoeditor_delete_alert_dialog_button_warning_color,
                        null));
            }
        }
    }

    public void onAllowOpenNetwork() {
        GLog.d(TAG, "onAllowOpenNetwork");
    }

    public void onAllowUseMobileData() {
        GLog.d(TAG, "onAllowUseMobileData");
    }

    public void onDisallowOpenNetwork() {
        GLog.d(TAG, "onDisallowOpenNetwork");
    }

    public void onDisallowUseMobileData() {
        GLog.d(TAG, "onDisallowUseMobileData");
    }

    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        GLog.d(TAG, "onRequestPermissionsResult");
    }

    public synchronized void showContinueDownloadOnMobileDialog() {
        GLog.d(TAG, "showContinueDownloadOnMobileDialog");
        if (mContext instanceof Activity) {
            if (((Activity) mContext).isFinishing()) {
                return;
            }
        }
        if (mContinueDownloadOnMobileDialog == null) {
            int titleId = requestDownloadOnMobileDialogTitle();
            int msgId = requestDownloadOnMobileDialogMsg();
            if ((titleId == INVALID_RES_ID) || (msgId == INVALID_RES_ID)) {
                return;
            }
            createAndShowContinueDownloadOnMobileDialog(titleId, msgId);
        } else if (!mContinueDownloadOnMobileDialog.isShowing()) {
            mContinueDownloadOnMobileDialog.show();
        }
    }

    public boolean hideContinueDownloadOnMobileDialog() {
        GLog.d(TAG, "hideContinueDownloadOnMobileDialog");
        if ((mContinueDownloadOnMobileDialog != null) && mContinueDownloadOnMobileDialog.isShowing()) {
            mContinueDownloadOnMobileDialog.cancel();
            return true;
        }
        return false;
    }

    public synchronized void showRequestDownloadOnMobileDialog() {
        if (mContext instanceof Activity) {
            if (((Activity) mContext).isFinishing()) {
                return;
            }
        }
        GLog.d(TAG, "showRequestDownloadOnMobileDialog");
        if (mRequestDownloadOnMobileDialog == null) {
            ConfirmDialog.Builder builder = new ConfirmDialog.Builder(mContext);
            builder.setPositiveButton(
                            R.string.videoeditor_video_editor_download_continue,
                            (dialog, whichButton) -> {
                                dialog.dismiss();
                                NetworkPermissionManager.setAllowDownloadOnMobile(true);
                                onAllowUseMobileData();
                            })
                    .setNegativeButton(R.string.videoeditor_component_download_cancel, (dialog, whichButton) -> {
                        dialog.dismiss();
                        onDisallowUseMobileData();
                    })
                    .setOnCancelListener(new DialogInterface.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialogInterface) {
                            dialogInterface.dismiss();
                            onDisallowUseMobileData();
                        }
                    })
                    .setTitle(R.string.videoeditor_video_editor_download_message);
            mRequestDownloadOnMobileDialog = builder.build().show();
            android.widget.Button button = mRequestDownloadOnMobileDialog.getButton(DialogInterface.BUTTON_POSITIVE);
            if (button != null) {
                button.setTextColor(mContext.getResources().getColor(
                        R.color.videoeditor_delete_alert_dialog_button_warning_color, null));
            }
        } else if (!mRequestDownloadOnMobileDialog.isShowing()) {
            mRequestDownloadOnMobileDialog.show();
        }
    }

    public boolean hideRequestDownloadOnMobileDialog() {
        GLog.d(TAG, "hideRequestDownloadOnMobileDialog");
        if ((mRequestDownloadOnMobileDialog != null) && mRequestDownloadOnMobileDialog.isShowing()) {
            mRequestDownloadOnMobileDialog.cancel();
            return true;
        }
        return false;
    }

    public synchronized void showNetworkPermissionDialog() {
        GLog.d(TAG, "showRequestUseNetworkDialog");
        if (mContext instanceof Activity) {
            if (((Activity) mContext).isFinishing()) {
                return;
            }
        }
        int titleId = requestUseNetworkDialogTitle();
        int messageId = requestUseNetworkDialogMessage();
        if ((titleId == INVALID_RES_ID) || (messageId == INVALID_RES_ID)) {
            return;
        }
        ConfirmDialog.Builder builder = new ConfirmDialog.Builder(mContext);
        builder.setPositiveButton(
                        com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_option_allow,
                        (dialog, which) -> {
                            NetworkPermissionManager.setUseOpenNetwork(true);
                            dialog.dismiss();
                            onAllowOpenNetwork();
                        })
                .setNegativeButton(com.oplus.gallery.basebiz.R.string.base_statement_refuse, (dialogInterface, i) -> {
                    dialogInterface.dismiss();
                    onDisallowOpenNetwork();
                })
                .setOnCancelListener(dialogInterface -> {
                    dialogInterface.dismiss();
                    onDisallowOpenNetwork();
                })
                .setTitle(titleId);
        builder.setMessage(messageId);
        mRequestPermissionDialog = builder.build().show();
    }

    public boolean hideRequestUseNetworkDialog() {
        GLog.d(TAG, "hideRequestUseNetworkDialog");
        if ((mRequestPermissionDialog != null) && mRequestPermissionDialog.isShowing()) {
            mRequestPermissionDialog.cancel();
            return true;
        }
        return false;
    }

    protected boolean checkIfNeedShowPrivacyDialog() {
        IPrivacyAuthorizingAbility ability = ((GalleryApplication) ContextGetter.context)
                .getAppAbility(IPrivacyAuthorizingAbility.class);
        if (ability != null) {
            Boolean isPrivacyAuthorized = ability.isPrivacyAuthorized(
                    AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
            );
            IOUtils.closeQuietly(ability);
            if (isPrivacyAuthorized == null) {
                GLog.w(TAG, "checkIfNeedShowPrivacyDialog, isPrivacyAuthorized == null,return.");
                return false;
            }
            return !isPrivacyAuthorized;
        }
        // 没有配置能力，外销，默认不弹
        return false;
    }

    protected void showPrivacyDialog(Context context) {
        PermissionDialogHelper.INSTANCE.showDownloadPrivacyDialog(
                context,
                () -> {
                    IPrivacyAuthorizingAbility ability = ((GalleryApplication) ContextGetter.context)
                            .getAppAbility(IPrivacyAuthorizingAbility.class);
                    if (ability != null) {
                        ability.authorizePrivacy(AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD);
                    }
                    IOUtils.closeQuietly(ability);
                    NetworkPermissionManager.setUseOpenNetwork(true);
                    onAllowOpenNetwork();
                }, null);
    }

    public void retryRequestSource() {
        GLog.d(TAG, "retryRequestSource");
    }

    public void networkErrorToast() {
        GLog.d(TAG, "networkErrorToast");
        ToastUtil.showShortToast(networkErrorToastMsg());
    }

    public void checkDownloadFailType() {
        GLog.d(TAG, "checkDownloadFailType");
        mEnterRetryRequest = false;
        Message message = Message.obtain();
        message.what = NetworkChangeHandler.MSG_DOWNLOAD_FAIL;
        mNetStateChangeHandler.removeMessages(NetworkChangeHandler.MSG_DOWNLOAD_FAIL);
        mNetStateChangeHandler.sendMessageDelayed(message, NetworkChangeHandler.DOWNLOAD_FAILED_DELAY);
    }

    public void onNetworkError() {
        GLog.d(TAG, "onNetworkError");
    }

    public int networkErrorToastMsg() {
        return 0;
    }

    public int downloadFailureToastMsg() {
        return 0;
    }

    public int requestUseNetworkDialogTitle() {
        return 0;
    }

    public int requestUseNetworkDialogMessage() {
        return 0;
    }

    public int requestDownloadOnMobileDialogTitle() {
        return 0;
    }

    public int requestDownloadOnMobileDialogMsg() {
        return 0;
    }

    public boolean hasUserDownloadTask() {
        return false;
    }

    private class NetworkChangeHandler extends Handler {
        static final int DEFAULT_STATE = 1 << 0;
        static final int FROM_WIFI_TO_MOBILE = 1 << 1;
        static final int FROM_WIFI_TO_NO_NETWORK = 1 << 2;
        static final int FROM_MOBILE_TO_WIFI = 1 << 3;
        static final int FROM_MOBILE_TO_NO_NETWORK = 1 << 4;
        static final int FROM_DEFAULT_TO_NO_NETWORK = 1 << 5;
        static final int MSG_DOWNLOAD_FAIL = 10;
        static final int MSG_RETRY_REQUEST = 11;
        static final int MSG_REQUEST_LIST = 12;
        static final int RETRY_REQUEST_DELAY = 2000;
        static final int DOWNLOAD_FAILED_DELAY = 1200;

        public NetworkChangeHandler() {
            super(Looper.myLooper());
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == MSG_RETRY_REQUEST) {
                mEnterRetryRequest = true;
                if (NetworkMonitor.isWifiValidated() || !NetworkMonitor.isNetworkValidated()) {
                    hideContinueDownloadOnMobileDialog();
                    hideRequestDownloadOnMobileDialog();
                }
                boolean resetStatus = false;
                switch (msg.arg1) {
                    case FROM_WIFI_TO_NO_NETWORK:
                    case FROM_WIFI_TO_MOBILE:
                        if (NetworkMonitor.isMobileValidated()) {
                            GLog.d(TAG, "[handleMessage] MSG_RETRY_REQUEST, From wifi to mobile.");
                            if (!NetworkPermissionManager.isAllowDownloadOnMobile() && hasUserDownloadTask()) {
                                showContinueDownloadOnMobileDialog();
                            } else {
                                retryRequestSource();
                            }
                        } else {
                            resetStatus = true;
                            GLog.d(TAG, "[handleMessage] MSG_RETRY_REQUEST, From wifi to no network.");
                        }
                        break;
                    case FROM_MOBILE_TO_WIFI:
                        if (NetworkMonitor.isWifiValidated()) {
                            GLog.d(TAG, "[handleMessage] MSG_RETRY_REQUEST, From mobile to wifi.");
                            retryRequestSource();
                        } else {
                            resetStatus = true;
                            GLog.d(TAG, "[handleMessage] MSG_RETRY_REQUEST, From mobile to no network.");
                        }
                        break;
                    case FROM_MOBILE_TO_NO_NETWORK:
                    case FROM_DEFAULT_TO_NO_NETWORK:
                        if (msg.arg1 == FROM_MOBILE_TO_NO_NETWORK) {
                            GLog.d(TAG, "[handleMessage] MSG_RETRY_REQUEST, From mobile to no network.");
                        } else {
                            GLog.d(TAG, "[handleMessage] MSG_RETRY_REQUEST, From default to no network.");
                        }
                        resetStatus = true;
                        break;
                    default:
                        break;
                }

                if (!NetworkMonitor.isNetworkValidated() && hasUserDownloadTask()) {
                    if (ActivityLifecycle.isRunningForeground()) {
                        networkErrorToast();
                        GLog.d(TAG, "NetworkChangeHandler isRunningForeground, networkErrorToast");
                    }
                    onNetworkError();
                    resetStatus = true;
                }

                if (resetStatus) {
                    LocalSourceManager.resetAllDownloadQueueStatus();
                    final EditorBaseUIController controller = getUIController();
                    if (controller == null) {
                        GLog.e(TAG, "controller is null!");
                        return;
                    }
                    final BaseRecyclerAdapter adapter = controller.getAdapter();
                    if (adapter == null) {
                        GLog.e(TAG, "adapter is null!");
                        return;
                    }
                    GLog.d(TAG, "NetworkChangeHandler notifyDataSetChanged");
                    adapter.notifyDataSetChanged();
                }
            } else if (msg.what == MSG_DOWNLOAD_FAIL) {
                GLog.d(TAG, "[handleMessage] MSG_DOWNLOAD_FAIL");
                if (!(mEnterRetryRequest || mNetStateChangeHandler.hasMessages(MSG_RETRY_REQUEST))) {
                    if (ActivityLifecycle.isRunningForeground()) {
                        ToastUtil.showShortToast(downloadFailureToastMsg());
                    }
                }
            } else if (msg.what == MSG_REQUEST_LIST) {
                GLog.d(TAG, "[handleMessage] MSG_REQUEST_LIST");
                onChangeToNetwork();
            }
        }
    }

    public void hideSoundMuteButton() {
        mUIController.hideSoundMuteButton();
    }

    /**
     * state切换时是否需要进出动画. 包括：<p>
     * 1.视频预览view的位置变动动画 <p>
     * 2.时间显示/进度条view的Alpha动画 <p>
     * 3.菜单栏的TranslateX动画 <p>
     * 默认为false. 进出某些state如果需要上述动画，返回true
     */
    public boolean isNeedAnimator() {
        return false;
    }
}
