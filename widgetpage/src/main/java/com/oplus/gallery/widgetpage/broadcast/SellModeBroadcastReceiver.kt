/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SellModeBroadcastReceiver.kt
 ** Description: 监听卖场模式启动广播
 ** Version: 1.0
 ** Date : 2023/7/12
 ** Author: v-kuangren@oppo.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  v-k<PERSON><PERSON>@oppo.Gallery3D      2023/07/12    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.widgetpage.broadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper
import com.oplus.gallery.business_lib.api.ApiDmManager.getWidgetDM
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.storage.SPUtils.setLong
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.widgetpage.WidgetConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 监听卖场模式启动广播
 */
class SellModeBroadcastReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            WidgetConstants.Action.ACTION_BROADCAST_OPPO_DAYDREAM_VIDEO_CAMERA,
            WidgetConstants.Action.ACTION_BROADCAST_OPLUS_DAYDREAM_VIDEO_CAMERA -> {
                val isSellMode = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SELL_MODE)
                if (!isSellMode) {
                    return
                }
                GLog.d(TAG, "onReceive")
                // 如果没有授权，则不刷新数据，直接return
                if (PermissionHelper.isDisagreeUserPolicy()) {
                    GLog.d(TAG, "onReceive no AgreePolicy so return")
                    return
                }
                AppScope.launch(Dispatchers.IO) {
                    /**
                     * 1、卖场模式重启后，有一定的概率可能数据还没同步好，媒体库最大的延时是10秒，因此这里延时10秒再执行
                     * 2、重置卖场模式后，产品要求是桌面默认精选卡片能够出来，因此延时10秒行对产品体验无影响, 产品已经体验过效果。
                     */
                    delay(WidgetConstants.LastTime.DELAY_GENERATE_RECOMMENDED_LIST_WHEN_SELL_MODE_BR)
                    setLong(context, null, WidgetConstants.SPName.LAST_GENERATE_RECOMMENDED_LIST_TIME, System.currentTimeMillis())
                    getWidgetDM().generateRecommendedDisplayListIfNeeded()
                }
            }
        }
    }

    private companion object {
        private const val TAG = "SellModeBroadcastReceiver"
    }
}