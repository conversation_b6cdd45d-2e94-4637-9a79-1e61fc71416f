/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - AutoRemovedViewAnimator.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/16
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/16    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.ui.animation;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.ViewGroup;

public class AutoRemovedViewAnimator extends AnimatorListenerAdapter {
    private View mView;
    private ObjectAnimator mAnimator;

    public AutoRemovedViewAnimator(View view, ObjectAnimator animator) {
        this.mView = view;
        this.mAnimator = animator;
        mAnimator.addListener(this);
    }

    public View getView() {
        return mView;
    }

    public ObjectAnimator getAnimator() {
        return mAnimator;
    }

    @Override
    public void onAnimationEnd(Animator animation) {
        if (mView == null) {
            return;
        }
        ViewGroup parent = (ViewGroup) mView.getParent();
        if ((parent != null) && (mAnimator != null) && (mAnimator.getTarget() instanceof View)) {
            parent.removeView((View) mAnimator.getTarget());
        }
    }
}
