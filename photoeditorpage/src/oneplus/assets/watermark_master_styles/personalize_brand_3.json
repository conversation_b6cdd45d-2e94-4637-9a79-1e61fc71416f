{"styleId": "personalize_brand_3", "baseImageSize": 360, "size": {"leftMargin": 0, "topMargin": 0, "rightMargin": 50, "bottomMargin": 0}, "imageOffset": {"startX": 0, "startY": 0}, "background": {"backgroundType": 0, "color": "#FFFFFF"}, "bitmaps": [{"direction": 2, "orientation": 0, "rotation": 90, "position": {"borderX": 0, "borderY": 0}, "width": -1, "height": 50, "elements": [{"id": 1, "content": {"type": "elements", "orientation": 0}, "position": {"layoutGravity": "leftVerticalCenter", "layoutGravityEnable": true, "leftMargin": 20, "topMargin": 0, "rightMargin": 0, "bottomMargin": 0}, "elements": [{"id": 2, "visible": true, "editable": false, "content": {"type": "text", "textSource": 0}, "position": {"layoutGravity": "leftVerticalCenter", "layoutGravityEnable": true, "leftMargin": 0, "topMargin": 0, "rightMargin": 0, "bottomMargin": 0}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "fontWeight": 600, "textSize": 8, "letterSpacing": 0.04, "lineHeight": 9.38, "alpha": 0.54, "lightAlpha": 0.54, "darkAlpha": 0.8, "gradientType": -1, "colors": ["#000000"]}}, {"id": 3, "visible": true, "editable": true, "content": {"type": "text", "textSource": 2, "maxWidth": 90}, "position": {"layoutGravity": "verticalCenter", "layoutGravityEnable": true, "leftMargin": 12, "topMargin": 0, "rightMargin": 0, "bottomMargin": 0}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "fontWeight": 500, "textSize": 8, "letterSpacing": 0.03, "lineHeight": 9.38, "alpha": 0.54, "lightAlpha": 0.54, "darkAlpha": 0.8, "gradientType": -1, "colors": ["#000000"]}}]}, {"id": 4, "visible": true, "editable": false, "spaceUse": "occupy", "content": {"type": "space"}, "layoutWeight": 1}, {"id": 5, "visible": true, "editable": true, "content": {"type": "image", "bitmapSourceType": 0, "bitmap": "brand_imagine_dark", "bitmapResName": "brand_imagine_dark", "width": 97, "height": 34, "scaleType": 5}, "position": {"layoutGravity": "rightVerticalCenter", "layoutGravityEnable": true, "leftMargin": 0, "topMargin": 0, "rightMargin": 20, "bottomMargin": 0}}]}]}