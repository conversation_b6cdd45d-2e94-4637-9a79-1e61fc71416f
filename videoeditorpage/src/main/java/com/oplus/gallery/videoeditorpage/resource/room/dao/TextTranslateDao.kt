/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextTranslateDao.kt
 ** Description : 文本翻译实体DAO
 ** Version     : 1.0
 ** Date        : 2025/6/17 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/17  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.oplus.gallery.videoeditorpage.resource.room.bean.TextTranslateItem

/**
 * 文本翻译的增删改查
 */
@Dao
interface TextTranslateDao {
    /**
     * 事务批量插入词条
     */
    @Transaction
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(fonts: List<TextTranslateItem>): List<Long>

    /**
     * 根据词条id和语种查询
     */
    @Query("SELECT language_text FROM text_translate WHERE text_id = :id AND language_code = :languageCode")
    fun getByIdAndLanguageCode(id: String, languageCode: String): String?

    /**
     * 根据词条id查默认文本
     */
    @Query("SELECT language_text FROM text_translate WHERE text_id = :id AND language_code = 'default'")
    fun getDefaultById(id: String): String?

    /**
     * 删除某个词条的所有翻译
     */
    @Query("DELETE FROM text_translate WHERE text_id = :id")
    fun deleteById(id: String): Int
}