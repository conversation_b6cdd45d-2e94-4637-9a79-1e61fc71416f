/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ActionbarSection.kt
 ** Description: actionbar相关业务
 ** Version: 1.0
 ** Date : 2025/5/8
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:ActionbarSection
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/5/8    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.section

import android.content.Context
import android.graphics.Rect
import android.view.TouchDelegate
import android.view.View
import android.view.View.OnClickListener
import android.widget.RelativeLayout
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.seekbar.COUISeekBar
import com.coui.appcompat.seekbar.COUISeekBar.OnSeekBarChangeListener
import com.coui.appcompat.segmentbutton.COUISegmentButtonLayout.OnSelectedSegmentChangeListener
import com.oplus.gallery.business_lib.videoedit.EditVideoType
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.common.ui.SelectResolutionDialog
import com.oplus.gallery.videoeditorpage.common.ui.SelectResolutionDialog.Companion.SEGMENT_POSITION_OLIVE
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.FPS_30
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.FPS_60
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.FpsInfo
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.RESOLUTION_RATIO_1080P
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.RESOLUTION_RATIO_4K
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.ResolutionInfo
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.getResolutionSize
import com.oplus.gallery.videoeditorpage.utlis.SaveUtils
import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorProcessor
import com.oplus.gallery.videoeditorpage.video.business.exportolive.ExportOliveParam
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveType
import com.oplus.gallery.videoeditorpage.video.business.picker.PickerHelper.pickerList
import com.oplus.gallery.videoeditorpage.widget.ResolutionTitleLayout

/**
 * 顶部actionbar相关业务，取消，保存，分辨率弹框
 */
class TopActionbarSection(
    context: Context,
    rootView: View,
    editorEngine: EditorEngine,
    editorInvoker: String?
) : BaseSection(context, rootView, editorEngine, editorInvoker), OnClickListener {

    /**
     * 标题栏
     */
    val actionBar: RelativeLayout by lazy {
        rootView.findViewById(R.id.action_bar)
    }

    /**
     * 取消按钮
     */
    val cancelButton: COUIButton by lazy {
        actionBar.findViewById(R.id.action_bar_back)
    }

    /**
     * 保存按钮
     */
    val doneButton: COUIButton by lazy {
        actionBar.findViewById(R.id.action_bar_done)
    }

    /**
     * 标题栏取消和保存点击事件监听
     */
    var actionBarClickListener: OnClickListener? = null

    /**
     * 当前选中的分辨率标题
     */
    private var currentResolutionTitle: String = TextUtil.EMPTY_STRING

    /**
     * 保存按钮是否常亮
     */
    private var needDoneButtonClickable: Boolean = false

    /**
     * 视频是否已添加特效
     */
    private var hasEffect: Boolean = false

    /**
     * 初始保存类型
     * 用于后续，判断编辑期间，是否有修改过保存类型的
     */
    private val originSaveType: SaveType = editorEngine.saveType

    /**
     * 初始分辨率级别
     * 用于后续，判断编辑期间，是否有修改过分辨率的
     */
    private val originResolution: String = editorEngine.currentTimeline.nodeResolution

    /**
     * 初始帧率
     * 用于后续，判断编辑期间，是否有修改过帧率的
     */
    private val originFps: Float = editorEngine.currentTimeline.nodeFps

    /**
     * 分辨率标题显示文本
     */
    private val resolutionTitleLayout: ResolutionTitleLayout by lazy {
        actionBar.findViewById(R.id.ll_action_bar_adjust)
    }

    /**
     * 保存类型监听器
     */
    private val onVideoSaveTypeChangeListener: OnSelectedSegmentChangeListener = OnSelectedSegmentChangeListener { _, positionTo, _ ->
        if ((positionTo == MathUtils.ZERO) && (editorEngine.saveType !== SaveType.VIDEO)) {
            editorEngine.saveType = SaveType.VIDEO
            updateTitleLayout()
            selectResolutionDialog.setBottomTips(
                String.format(getString(R.string.videoeditor_video_description), estimatedFileSize)
            )
            updateFpsSeekBar()
            updateDoneStatus()
        } else if ((positionTo == MathUtils.ONE) && (editorEngine.saveType !== SaveType.OLIVE)) {
            editorEngine.saveType = SaveType.OLIVE
            updateTitleLayout()
            selectResolutionDialog.setBottomTips(
                String.format(getString(R.string.videoeditor_olive_description), "${MathUtils.FOUR}$SECONDS")
            )
            updateFpsSeekBar()
            updateDoneStatus()
        }
    }

    /**
     * 分辨率切换监听
     */
    private val onVideoResolutionChangeListener: OnSeekBarChangeListener = object : OnSeekBarChangeListener {
        override fun onProgressChanged(couiSeekBar: COUISeekBar, progress: Int, isFromUser: Boolean) {
            GLog.d(TAG, LogFlag.DL) {
                "[onProgressChanged] progress: $progress, resolutionListSize: ${resolutionList.size}, isFromUser: $isFromUser"
            }
            if (progress < resolutionList.size) {
                val nodeInfo = resolutionList[progress]
                currentResolutionTitle = nodeInfo.text
                editorEngine.currentTimeline.nodeResolution = currentResolutionTitle
                selectResolutionDialog.setResolutionDescriptionText(getString(nodeInfo.descriptionId))
                updateTitleLayout()
                updateBottomTips()
                updateDoneStatus()
            }
        }

        override fun onStartTrackingTouch(couiSeekBar: COUISeekBar) = Unit

        override fun onStopTrackingTouch(couiSeekBar: COUISeekBar) = Unit
    }

    /**
     * 帧率切换监听
     */
    private val onFpsResolutionChangeListener: OnSeekBarChangeListener = object : OnSeekBarChangeListener {
        override fun onProgressChanged(couiSeekBar: COUISeekBar, progress: Int, isFromUser: Boolean) {
            GLog.d(TAG, LogFlag.DL) {
                "[onProgressChanged] progress: $progress, fpsListSize: ${currentFpsList.size}, isFromUser: $isFromUser"
            }
            if (progress < currentFpsList.size) {
                val fpsInfo = currentFpsList[progress]
                editorEngine.currentTimeline.nodeFps = fpsInfo.quality.toFloat()
                selectResolutionDialog.setFpsDescriptionText(getString(fpsInfo.descriptionId))
                updateBottomTips()
                updateDoneStatus()
            }
        }

        override fun onStartTrackingTouch(couiSeekBar: COUISeekBar) = Unit

        override fun onStopTrackingTouch(couiSeekBar: COUISeekBar) = Unit
    }

    /**
     * 选择弹框
     */
    private val selectResolutionDialog: SelectResolutionDialog = SelectResolutionDialog(context, resolutionTitleLayout).apply {
        onSaveTypeChangeListener = onVideoSaveTypeChangeListener
        onResolutionChangeListener = onVideoResolutionChangeListener
        onFpsChangeListener = onFpsResolutionChangeListener
    }

    /**
     * 预估视频保存时的文件大小
     */
    private var estimatedFileSize: String = TextUtil.EMPTY_STRING

    /**
     * 可保存分辨率类型列表
     */
    private val resolutionList: List<ResolutionInfo> by lazy {
        ResolutionUtil.getResolutionDataList(RESOLUTION_RATIO_4K)
    }

    /**
     * 视频模式下，可保存帧率类型列表
     */
    private val videoFpsList: List<FpsInfo> by lazy {
        var currentFps = editorEngine.currentTimeline.nodeFps?.takeIf { it > 0 }?.toInt() ?: FPS_30
        ResolutionUtil.getFpsDataList(currentFps)
    }

    /**
     * 实况模式下，可保存帧率类型列表
     */
    private val oliveFpsList: List<FpsInfo> by lazy {
        // 实况保存状态下，最大60帧
        videoFpsList.filter { it.quality <= FPS_60 }
    }

    /**
     * 当前帧率类型列表
     */
    private var currentFpsList: List<FpsInfo> = emptyList()
        get() {
            return if (editorEngine.saveType == SaveType.OLIVE) oliveFpsList else videoFpsList
        }

    override fun onCreate() {
        super.onCreate()
        refreshDoneButtonClickable()
        initView()
        loadDefaultData()
    }

    /**
     * 刷新保存按钮是否常亮
     */
    private fun refreshDoneButtonClickable() {
        if (pickerList.size > MathUtils.ONE) {
            // 多素材编辑，保存常亮
            needDoneButtonClickable = true
        } else if (pickerList.size == MathUtils.ONE) {
            // 单素材编辑，判断帧率、分辨率，是否被自动调整到标准值
            editorEngine.currentTimeline.also { timeline ->
                // 1. 素材是图片时，直接高亮
                if (pickerList.first().mediaType == EditVideoType.IMAGE) {
                    needDoneButtonClickable = true
                    GLog.d(TAG, LogFlag.DL) { "refreshDoneButtonClickable, is image" }
                    return@also
                }
                // 2. 原素材帧率，和面板上对应帧率比较
                val nearestFps: Int? = getNearestFpsNode(timeline.fps)?.second?.quality
                if (timeline.fps != nearestFps) {
                    needDoneButtonClickable = true
                    GLog.d(TAG, LogFlag.DL) { "refreshDoneButtonClickable, fps changed. ${timeline.fps}, $nearestFps" }
                    return@also
                }
                // 3. 原素材分辨率，和面板上对应分辨率比较
                val videoSize = getResolutionSize(editorEngine.originalSizeFromTimeline, timeline.ratioOption, timeline.nodeResolution)
                if ((timeline.width != videoSize.width) || (timeline.height != videoSize.height)) {
                    needDoneButtonClickable = true
                    GLog.d(TAG, LogFlag.DL) { "refreshDoneButtonClickable, fps changed. ${timeline.width}x${timeline.height}, $videoSize" }
                }
            }
            // 编辑来源大图菜单的导出实况，直接高亮
            editorInvoker?.let {
                if (ExportOliveParam.VALUE_INVOKER_GALLEY_EXPORT_OLIVE == it) {
                    needDoneButtonClickable = true
                }
            }
        }
    }

    /**
     * 一二级页面切换
     * @param isEnter 是否是进入二级页
     */
    fun switchPage(isEnter: Boolean) {
        if (isEnter.not()) {
            actionBar.alpha = MathUtils.ZERO_F
            actionBar.visibility = View.VISIBLE
            AnimatorProcessor.createEnterAnim(actionBar, isEnter)
        } else {
            AnimatorProcessor.createExitAnim(actionBar, isEnter)
            AnimatorProcessor.addExitCallback { actionBar.visibility = View.GONE }
        }
    }

    private fun initView() {
        val padding = context.resources.getDimensionPixelOffset(R.dimen.editor_button_icon_click_padding)
        expandTouchView(doneButton, Rect(padding, padding, padding, padding))
        cancelButton.setOnClickListener(this)
        doneButton.setOnClickListener(this)
        resolutionTitleLayout.setOnClickListener(this)
    }

    private fun expandTouchView(view: View, rect: Rect) {
        view.post {
            val clickRect = Rect()
            view.getHitRect(clickRect)
            clickRect.top = clickRect.top - rect.top
            clickRect.bottom = clickRect.bottom + rect.bottom
            clickRect.left = clickRect.left - rect.left
            clickRect.right = clickRect.right + rect.right
            (view.parent as? View)?.let {
                val touchDelegate = TouchDelegate(clickRect, view)
                it.touchDelegate = touchDelegate
            }
        }
    }

    /**
     * 加载数据，设置面板所需的默认值
     */
    private fun loadDefaultData() {
        // 如果所编辑的素材，全是实况图类型，则需默认选中实况图
        if (editorEngine.saveType == SaveType.OLIVE) {
            GLog.d(TAG, LogFlag.DL) { "initView, is all olive" }
            selectResolutionDialog.selectSegmentAt(SEGMENT_POSITION_OLIVE)
        }
        updateResolutionSeekBar()
        updateFpsSeekBar()
        updateTitleLayout()
        updateBottomTips()
        updateDoneStatus()
    }

    /**
     * 更新标题
     */
    private fun updateTitleLayout() {
        if (editorEngine.saveType == SaveType.OLIVE) {
            resolutionTitleLayout.setTitle(getString(R.string.videoeditor_text_olive))
        } else {
            resolutionTitleLayout.setTitle(currentResolutionTitle)
        }
    }

    /**
     * 更新底部提示
     */
    private fun updateBottomTips() {
        val timeline = editorEngine.currentTimeline
        val nodeResolution = timeline.nodeResolution ?: RESOLUTION_RATIO_1080P
        val videoSize = getResolutionSize(editorEngine.originalSizeFromTimeline, timeline.ratioOption, nodeResolution)
        val fps = timeline.nodeFps.takeIf { it > 0 } ?: FPS_30.toFloat()
        val videoFileSize = SaveUtils.estimatedVideoFileSize(editorEngine.getTimelineDuration(), videoSize, fps)
        estimatedFileSize = SaveUtils.getUnitValue(context, videoFileSize)

        if (editorEngine.saveType == SaveType.OLIVE) {
            selectResolutionDialog.setBottomTips(
                String.format(getString(R.string.videoeditor_olive_description), "${MathUtils.FOUR}$SECONDS")
            )
        } else {
            selectResolutionDialog.setBottomTips(
                String.format(getString(R.string.videoeditor_video_description), estimatedFileSize)
            )
        }
    }

    /**
     * 更新分辨率SeekBar
     */
    private fun updateResolutionSeekBar() {
        val currentResolution = editorEngine.currentTimeline.nodeResolution ?: RESOLUTION_RATIO_1080P
        if (resolutionList.isEmpty()) return

        val textList: List<String> = resolutionList.map { it.text }
        val resolutionListSize = resolutionList.size
        var defaultProgress = resolutionListSize / MathUtils.TWO
        resolutionList.forEachIndexed { index, nodeInfo ->
            if (nodeInfo.text == currentResolution) defaultProgress = index
        }
        val lastIndex = resolutionListSize - MathUtils.ONE
        val nodeInfo = resolutionList[defaultProgress]
        GLog.d(TAG, LogFlag.DL) { "[updateResolutionLayout] resolutionListSize: $resolutionListSize, defaultProgress: $defaultProgress" }
        selectResolutionDialog.setResolutionSeekBarMax(lastIndex)
        selectResolutionDialog.setResolutionTextBarList(textList)
        selectResolutionDialog.setResolutionSeekBarProgress(defaultProgress)
        selectResolutionDialog.setResolutionDescriptionText(getString(nodeInfo.descriptionId))

        currentResolutionTitle = nodeInfo.text
        editorEngine.currentTimeline.nodeResolution = nodeInfo.text
    }

    /**
     * 更新帧率SeekBar
     */
    private fun updateFpsSeekBar() {
        var currentFps = editorEngine.currentTimeline.nodeFps?.takeIf { it > 0 }?.toInt() ?: FPS_30
        if (currentFpsList.isEmpty()) return

        currentFps = currentFps.coerceAtMost(currentFpsList.last().quality)
        val textList: List<String> = currentFpsList.map { it.text }
        getNearestFpsNode(currentFps)?.let {
            GLog.d(TAG, LogFlag.DL) { "[updateFpsLayout] defaultProgress: ${it.first}, defaultFpsInfo: ${it.second}" }
            selectResolutionDialog.setFpsSeekBarMax(currentFpsList.lastIndex)
            selectResolutionDialog.setFpsTextBarList(textList)
            selectResolutionDialog.setFpsSeekBarProgress(it.first)
            selectResolutionDialog.setFpsDescriptionText(getString(it.second.descriptionId))

            editorEngine.currentTimeline.nodeFps = it.second.quality.toFloat()
        }
    }

    /**
     * 获取视频分辨率，在最接近的节点位置
     *
     * @param fps 视频分辨率
     * @return 返回最接近的节点位置和节点信息
     */
    private fun getNearestFpsNode(fps: Int): Pair<Int, FpsInfo>? {
        if (currentFpsList.isEmpty()) return null

        var nodeFpsIndex = currentFpsList.lastIndex
        run {
            currentFpsList.forEachIndexed { index, fpsInfo ->
                if (fps <= fpsInfo.quality) {
                    // 1.帧率不高于首个最小值时，取最小值
                    if (index == 0) {
                        nodeFpsIndex = 0
                        return@run
                    }
                    // 2.如果不是首个最小值，再和上个级别对比，取最临近的节点
                    nodeFpsIndex = if ((fpsInfo.quality - fps) > (fps - currentFpsList[index - MathUtils.ONE].quality)) {
                        index - MathUtils.ONE
                    } else index
                    return@run
                }
            }
        }
        return Pair(nodeFpsIndex, currentFpsList[nodeFpsIndex])
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        if (config.isChanged()) selectResolutionDialog.updateDialogLayout()
    }

    override fun onClick(view: View) {
        actionBarClickListener?.onClick(view)
        when (view.id) {
            R.id.ll_action_bar_adjust -> {
                if (ClickUtil.isDoubleClickAtSameKey(DOUBLE_CLICK_KEY_RESOLUTION_TITLE)) {
                    return
                }
                updateBottomTips()
                selectResolutionDialog.show()
            }
        }
    }

    /**
     * 特效栈数量变化回调
     *
     * @param hasEffect 是否已添加特效
     */
    fun onStackCountChanged(hasEffect: Boolean) {
        this.hasEffect = hasEffect
        updateDoneStatus()
    }

    /**
     * 设置封面后，封面图发生变化的回调
     */
    fun onCoverChanged() {
        needDoneButtonClickable = true
        updateDoneStatus()
    }

    /**
     * 判断分辨率面板是否较于最初有变化
     */
    fun isOptionChanged(): Boolean {
        // 保存类型是否修改过
        if (originSaveType != editorEngine.saveType) {
            GLog.d(TAG, LogFlag.DL) { "isResolutionDialogChanged, saveType changed" }
            return true
        }

        // 分辨率是否修改过
        if (originResolution != editorEngine.currentTimeline.nodeResolution) {
            GLog.d(TAG, LogFlag.DL) { "isResolutionDialogChanged, resolution changed" }
            return true
        }

        // 获取帧率最接近的节点位置做比较（例：29fps 最接近的节点是 30fps 的节点）
        val originNearestFps = getNearestFpsNode(originFps.toInt())?.second?.quality ?: 0
        // 帧率是否修改过
        if (originNearestFps != editorEngine.currentTimeline.nodeFps.toInt()) {
            GLog.d(TAG, LogFlag.DL) { "isResolutionDialogChanged, fps changed" }
            return true
        }
        return false
    }

    /**
     * 保存按钮是否可用
     */
    fun isDoneAvailable(): Boolean {
        return doneButton.alpha == ALPHA_NORMAL
    }

    /**
     * 更新保存按钮状态
     */
    private fun updateDoneStatus() {
        /* 1. 非单素材编辑时，保存按钮默认可点击
         * 2. 单素材编辑时，如果设置了封面，保存按钮可点击
         * 3. 单素材编辑时，如果有添加过效果，保存按钮可点击
         * 4. 单素材编辑时，如果分辨率面板有变更（如：保存类型切换、分辨率切换、帧率切换），保存按钮可点击 */
        doneButton.alpha = if (needDoneButtonClickable || this.hasEffect || isOptionChanged()) ALPHA_NORMAL else ALPHA_DISABLE
    }

    private fun getString(stringId: Int): String {
        return kotlin.runCatching {
            context.resources.getString(stringId)
        }.getOrDefault(TextUtil.EMPTY_STRING)
    }

    companion object {
        private const val TAG = "TopActionbarSection"
        private const val DOUBLE_CLICK_KEY_RESOLUTION_TITLE = "resolution_title"

        /**
         * 秒单位
         */
        private const val SECONDS = "s"

        /**
         * 正常透明度
         */
        private const val ALPHA_NORMAL = 1.0f

        /**
         * 按钮不可用时，对应透明度
         */
        private const val ALPHA_DISABLE = 0.3f
    }
}