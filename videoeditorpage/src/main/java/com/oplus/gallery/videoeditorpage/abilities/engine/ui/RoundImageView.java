/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RoundImageView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.engine.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatImageView;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.utlis.ColorSupportUtil;

public class RoundImageView extends AppCompatImageView {
    private static final String TAG = "RoundImageView";
    public static final int CIRCLE = 0;
    public static final int ROUND = 1;
    private static final int DEFAULT_BORDER_RADIUS = 1;
    private static final int BORDER_WIDTH = 2;
    private static final float DEFAULT_RATIO = 1.0f;
    private static final float DEFAULT_RATIO_TWO = 2.0f;
    private int mType = CIRCLE;
    private Context mContext = null;
    private boolean mHasBorder = false;
    private int mBorderRadius = 0;
    private RectF mRoundRect = null;
    private Paint mBitmapPaint = null;
    private Paint mOutCircle = null;
    private Matrix mMatrix = null;
    private BitmapShader mBitmapShader = null;
    private int mWidth = 0;
    private float mRadius = 0;
    private Bitmap mBitmap = null;
    private RectF mOuterRect = null;
    private ScaleType mScaleType = ImageView.ScaleType.FIT_CENTER;

    public RoundImageView(Context context) {
        this(context, null);
    }

    public RoundImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RoundImageView(Context context, AttributeSet attrs, int defStyle) {
        this(context, attrs, defStyle, 0);
    }

    public RoundImageView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr);
        mMatrix = new Matrix();
        mBitmapPaint = new Paint();
        mBitmapPaint.setAntiAlias(true);
        mBitmapPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_OVER));
        mContext = context;
        mOutCircle = new Paint();
        mOutCircle.setAntiAlias(true);
        mOutCircle.setStrokeWidth(BORDER_WIDTH);
        mOutCircle.setStyle(Paint.Style.STROKE);
        mOutCircle.setColor(Color.BLACK);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.RoundImageView);
        mBorderRadius = a.getDimensionPixelSize(R.styleable.RoundImageView_imageRadius,
                (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, DEFAULT_BORDER_RADIUS, getResources().getDisplayMetrics()));
        mType = a.getInt(R.styleable.RoundImageView_imageShape, CIRCLE);
        mHasBorder = a.getBoolean(R.styleable.RoundImageView_hasBorderStroke, false);
        setupShader(getDrawable());
        a.recycle();
        mScaleType = getScaleType();
    }

    public void setHasBorder(boolean hasBorder) {
        mHasBorder = hasBorder;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        float scale = 1.0f;
        final int viewWidth = getWidth() - getPaddingLeft() - getPaddingRight();
        final int viewHeight = getHeight() - getPaddingTop() - getPaddingBottom();
        float translateX = 0f;
        float translateY = 0f;
        if (mBitmap != null) {
            final int bitmapWidth = mBitmap.getWidth();
            final int bitmapHeight = mBitmap.getHeight();
            switch (mType) {
                case CIRCLE:
                    int bSize = 0;
                    bSize = Math.min(bitmapWidth, bitmapHeight);
                    scale = mWidth * DEFAULT_RATIO / bSize;
                    break;
                case ROUND:
                    scale = Math.max(viewWidth * DEFAULT_RATIO / bitmapWidth,
                            viewHeight * DEFAULT_RATIO / bitmapHeight);
                    break;
                default:
                    break;
            }

            if (ScaleType.CENTER_CROP == mScaleType) {
                boolean isWidthLarger = bitmapWidth > bitmapHeight;
                if (isWidthLarger && (bitmapWidth > viewWidth)) {
                    translateX = (viewWidth - bitmapWidth * scale) / DEFAULT_RATIO_TWO;
                } else if (!isWidthLarger && (bitmapHeight > viewHeight)) {
                    translateY = (viewHeight - bitmapHeight * scale) / DEFAULT_RATIO_TWO;
                }
            }
            mMatrix.setScale(scale, scale);
            mMatrix.postTranslate(translateX, translateY);
            if (null != mBitmapShader) {
                mBitmapShader.setLocalMatrix(mMatrix);
                mBitmapPaint.setShader(mBitmapShader);
            }
        }
        if (mType == CIRCLE) {
            drawCircle(canvas);
        } else if (mType == ROUND) {
            drawRound(canvas);
        }
    }

    private void drawCircle(Canvas canvas) {
        if (mHasBorder) {
            canvas.drawCircle(mRadius, mRadius, mRadius, mBitmapPaint);
            canvas.drawCircle(mRadius, mRadius, mRadius - DEFAULT_BORDER_RADIUS / DEFAULT_RATIO_TWO, mOutCircle);
        } else {
            canvas.drawCircle(mRadius, mRadius, mRadius, mBitmapPaint);
        }
    }

    private void drawRound(Canvas canvas) {
        if (mRoundRect == null) {
            mRoundRect = new RectF(0, 0, getWidth(), getHeight());
        }
        if (mHasBorder) {
            Path path = ColorSupportUtil.getPath(mRoundRect, mBorderRadius);
            canvas.drawPath(path, mBitmapPaint);
            Path innerPath = ColorSupportUtil.getPath(mRoundRect, mBorderRadius);
            canvas.drawPath(innerPath, mBitmapPaint);
            Path outPath = ColorSupportUtil.getPath(mOuterRect, mBorderRadius - BORDER_WIDTH / DEFAULT_RATIO_TWO);
            canvas.drawPath(outPath, mOutCircle);
        } else {
            Path path = ColorSupportUtil.getPath(mRoundRect, mBorderRadius);
            canvas.drawPath(path, mBitmapPaint);
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (mType == ROUND) {
            mRoundRect = new RectF(0, 0, getWidth(), getHeight());
            mOuterRect = new RectF(BORDER_WIDTH / 2, BORDER_WIDTH / 2,
                    getWidth() - BORDER_WIDTH / 2, getHeight() - BORDER_WIDTH / 2);
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if (mType == CIRCLE) {
            int minWidth = Math.min(getMeasuredHeight(), getMeasuredWidth());
            if ((minWidth != 0) && (getMeasuredHeight() != getMeasuredWidth())) {
                GLog.w(TAG, "onMeasure, warning! w need equals to h" + ", mType = " + mType
                        + ", mw = " + getMeasuredWidth() + ", mh = " + getMeasuredHeight());
            }
            mWidth = Math.max(getMeasuredHeight(), getMeasuredWidth());
            mRadius = mWidth / DEFAULT_RATIO_TWO;
            setMeasuredDimension(mWidth, mWidth);
        }
    }

    @Override
    public void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        setupShader(drawable);
    }

    @Override
    public void setImageResource(int resId) {
        super.setImageResource(resId);
        Drawable drawable = mContext.getDrawable(resId);
        setupShader(drawable);
    }

    private void setupShader(Drawable d) {
        Drawable drawable = getDrawable();
        if ((drawable == null) || (d == null)) {
            return;
        } else if (drawable != d) {
            drawable = d;
        }
        mBitmap = drawableToBitmap(drawable);

        if (null != mBitmap) {
            mBitmapShader = new BitmapShader(mBitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP);
        }
    }

    private Bitmap drawableToBitmap(Drawable drawable) {
        if (drawable instanceof BitmapDrawable) {
            BitmapDrawable bd = (BitmapDrawable) drawable;
            return bd.getBitmap();
        }
        int h = drawable.getIntrinsicHeight();
        int w = drawable.getIntrinsicWidth();
        Bitmap bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, w, h);
        drawable.draw(canvas);
        return bitmap;
    }

    public void setType(int type) {
        if (mType != type) {
            mType = type;
            invalidate();
        }
    }

    public void setRoundRadius(int radius) {
        mBorderRadius = radius;
        invalidate();
    }
}
