/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MethodInfo.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/08/14		1.0		OPLUS_ARCH_BUS
 *********************************************************************************/

package com.oplus.gallery.bus_lib.beans

import com.oplus.gallery.bus_lib.annotations.ThreadType
import com.oplus.gallery.bus_lib.utils.BusUtil
import java.lang.reflect.Method

class MethodInfo(private val method: Method, val supportSticky: Boolean = false, @ThreadType private val threadType: Int) {

    fun getParamClass(): Class<*> {
        return method.parameterTypes[0]
    }

    fun invokeIt(subscriber: Any, param: Any) {
        when (threadType) {
            ThreadType.MAIN -> BusUtil.mainHandler.post { method.invoke(subscriber, param) }
            ThreadType.THREAD -> BusUtil.threadHandler.post { method.invoke(subscriber, param) }
            else -> method.invoke(subscriber, param)
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MethodInfo

        if (method.name != other.method.name) return false
        if (threadType != other.threadType) return false

        return true
    }

    override fun hashCode(): Int {
        var result = method.hashCode()
        result = HASH_CODE_MAGIC * result + threadType
        return result
    }

    companion object {
        const val HASH_CODE_MAGIC = 31
    }
}