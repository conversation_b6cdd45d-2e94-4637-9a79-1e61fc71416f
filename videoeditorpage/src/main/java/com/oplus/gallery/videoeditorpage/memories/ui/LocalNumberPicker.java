/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - LocalNumberPicker.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/29    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewParent;

import com.coui.appcompat.picker.COUINumberPicker;

public class LocalNumberPicker extends COUINumberPicker {

    private boolean mIsTouchEnd;
    private OnTouchEndListener mOnTouchEndListener;

    public LocalNumberPicker(Context context) {
        super(context);
        mIsTouchEnd = true;
    }

    public LocalNumberPicker(Context context, AttributeSet attrs) {
        super(context, attrs);
        mIsTouchEnd = true;
    }

    public LocalNumberPicker(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mIsTouchEnd = true;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        int action = ev.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mIsTouchEnd = false;
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_OUTSIDE:
                mIsTouchEnd = true;
                if (null != mOnTouchEndListener) {
                    mOnTouchEndListener.onTouchEnd(this.getValue());
                }
                break;
            default:
                break;
        }

        ViewParent viewParent = getParent();
        if (viewParent != null) {
            viewParent.requestDisallowInterceptTouchEvent(true);
        }

        return super.dispatchTouchEvent(ev);
    }

    public boolean isTouchEnd() {
        return mIsTouchEnd;
    }

    public void setOnTouchEndListener(OnTouchEndListener listener) {
        mOnTouchEndListener = listener;
    }

    public interface OnTouchEndListener {
        void onTouchEnd(int value);
    }
}
