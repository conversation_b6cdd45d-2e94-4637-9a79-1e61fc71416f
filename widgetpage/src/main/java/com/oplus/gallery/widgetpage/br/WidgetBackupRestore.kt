/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetBackupRestore.kt
 ** Description:卡片数据手机搬家
 ** Version: 1.0
 ** Date : 2021/12/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/12/28        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.br

import android.content.ContentValues
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetDisplayList
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetDisplayListColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSet
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns
import com.oplus.gallery.foundation.database.util.ConstantUtils.AND
import com.oplus.gallery.foundation.database.util.ConstantUtils.OR
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.FilePathsConvert
import com.oplus.gallery.foundation.dbaccess.convert.StringSetConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.widgetpage.WidgetConstants
import org.json.JSONArray
import org.json.JSONObject
import kotlin.math.min

class WidgetBackupRestore {
    companion object {
        private const val TAG = "WidgetBackupRestore"
    }

    /**
     * 手机搬家 备份
     * @return 搬家的数据打包成json格式
     */
    fun backup(): String {
        val jsonObject = JSONObject()
        jsonObject.put(WidgetSet.TAB, backupWidgetSet())
        jsonObject.put(WidgetDisplayList.TAB, backupWidgetDisplayList())
        return jsonObject.toString()
    }

    private fun backupWidgetSet(): JSONArray {
        return QueryReq.Builder<JSONArray>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setConvert {
                val array = JSONArray()
                val widgetCodeIndex = it.getColumnIndex(WidgetSetColumns.WIDGET_CODE)
                val modeIndex = it.getColumnIndex(WidgetSetColumns.MODE)
                val lastDisplayDataIndex = it.getColumnIndex(WidgetSetColumns.LAST_DISPLAY_DATA)
                val dirtyIndex = it.getColumnIndex(WidgetSetColumns.DIRTY)
                while (it.moveToNext()) {
                    val obj = JSONObject()
                    obj.put(WidgetSetColumns.WIDGET_CODE, it.getString(widgetCodeIndex))
                    obj.put(WidgetSetColumns.MODE, it.getInt(modeIndex))
                    obj.put(WidgetSetColumns.LAST_DISPLAY_DATA, it.getString(lastDisplayDataIndex))
                    obj.put(WidgetSetColumns.DIRTY, it.getInt(dirtyIndex))
                    array.put(obj)
                }
                array
            }.build().exec()
    }

    /**
     * ((display_list_id != 'recommended') AND (invalid != 1)) OR (invalid = 2)
     */
    private fun backupWidgetDisplayList(): JSONArray {
        // 精选轮播列表只搬已移出的项，否则如果新手机没有精选数据，下次刷新精选列表就清空了
        val where = "((${WidgetDisplayListColumns.DISPLAY_LIST_ID} != '${WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED}')" +
                "$AND(${WidgetDisplayListColumns.INVALID} != ${WidgetDisplayListColumns.INVALID_DELETE}))" +
                "$OR(${WidgetDisplayListColumns.INVALID} = ${WidgetDisplayListColumns.INVALID_EXCLUDED})"
        return QueryReq.Builder<JSONArray>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setWhere(where)
            .setOrderBy("${WidgetDisplayListColumns._ID} ASC")
            .setConvert {
                val array = JSONArray()
                val dataIndex = it.getColumnIndex(WidgetDisplayListColumns.DATA)
                val displayListIdIndex = it.getColumnIndex(WidgetDisplayListColumns.DISPLAY_LIST_ID)
                val invalidIndex = it.getColumnIndex(WidgetDisplayListColumns.INVALID)
                while (it.moveToNext()) {
                    val obj = JSONObject()
                    obj.put(WidgetDisplayListColumns.DATA, it.getString(dataIndex))
                    obj.put(WidgetDisplayListColumns.DISPLAY_LIST_ID, it.getString(displayListIdIndex))
                    obj.put(WidgetDisplayListColumns.INVALID, it.getInt(invalidIndex))
                    array.put(obj)
                }
                array
            }.build().exec()
    }

    /**
     * 手机搬家 恢复
     * @param json 搬家的数据打包成json格式，即{@link backup()}返回的数据
     * @return 是否恢复成功，false表示出现异常
     */
    fun restore(json: String): Boolean {
        // false会显示搬家失败，无内容也应返回true
        if (json.isEmpty()) {
            GLog.d(TAG, "restore, no data to restore, skip")
            return true
        }
        runCatching {
            val jsonObject = JSONObject(json)
            restoreWidgetSet(jsonObject.getJSONArray(WidgetSet.TAB))
            restoreWidgetDisplayList(jsonObject.getJSONArray(WidgetDisplayList.TAB))
        }.onFailure {
            GLog.e(TAG, "restore: failed ", it)
            return false
        }
        return true
    }

    private fun restoreWidgetSet(jsonArray: JSONArray) {
        val contentValuesMap = convertToWidgetSetMap(jsonArray)
        if (contentValuesMap.isNotEmpty()) {
            val insertContentValues = mutableListOf<ContentValues>()
            val updateRequests = mutableListOf<UpdateReq>()
            val existsWidget = queryExistsWidget()
            contentValuesMap.forEach { (widgetCode, contentValues) ->
                if (existsWidget.contains(widgetCode)) {
                    updateRequests.add(
                        UpdateReq.Builder()
                            .setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
                            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                            .setWhere("${WidgetSetColumns.WIDGET_CODE} = ?")
                            .setWhareArgs(arrayOf(widgetCode))
                            .setConvert {
                                contentValues.apply {
                                    remove(WidgetSetColumns.WIDGET_CODE)
                                }
                            }
                            .build()
                    )
                } else {
                    insertContentValues.add(contentValues)
                }
            }
            val insertCount = if (insertContentValues.isNotEmpty()) {
                BulkInsertReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.WIDGET_SET)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setConvert {
                        insertContentValues.toTypedArray()
                    }
                    .build().exec()
            } else 0
            val updateCount = if (updateRequests.isNotEmpty()) {
                BatchReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(updateRequests)
                    .build().exec().size
            } else 0
            // 通知表变化
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.WIDGET_SET, IDao.DaoType.GALLERY)
            GLog.d(TAG, "restoreWidgetSet: restore=${jsonArray.length()}," +
                    "insertCount=$insertCount,updateCount=$updateCount")
        } else {
            GLog.d(TAG, "restoreWidgetSet: no restore data")
        }
    }

    private fun queryExistsWidget(): Set<String> {
        return QueryReq.Builder<Set<String>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setProjection(arrayOf(WidgetSetColumns.WIDGET_CODE))
            .setConvert(StringSetConvert())
            .build().exec()
    }

    private fun convertToWidgetSetMap(jsonArray: JSONArray): MutableMap<String, ContentValues> {
        val contentValuesMap = mutableMapOf<String, ContentValues>()
        for (i in 0 until jsonArray.length()) {
            val obj = jsonArray.getJSONObject(i)
            val widgetCode = obj.getString(WidgetSetColumns.WIDGET_CODE)
            val contentValues = ContentValues().apply {
                put(WidgetSetColumns.WIDGET_CODE, widgetCode)
                put(WidgetSetColumns.MODE, obj.getInt(WidgetSetColumns.MODE))
                put(
                    WidgetSetColumns.LAST_DISPLAY_DATA,
                    obj.optString(WidgetSetColumns.LAST_DISPLAY_DATA)
                )
                put(WidgetSetColumns.DIRTY, obj.getInt(WidgetSetColumns.DIRTY))
            }
            contentValuesMap[widgetCode] = contentValues
        }
        return contentValuesMap
    }

    private fun restoreWidgetDisplayList(jsonArray: JSONArray) {
        val insertContentValuesList = mutableListOf<ContentValues>()
        convertToDisplayListMap(jsonArray).forEach { (displayListId, contentValuesList) ->
            if (displayListId == WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED) {
                insertContentValuesList.addAll(contentValuesList)
            } else {
                insertContentValuesList.addAll(getValidDisplayListItems(
                    displayListId,
                    contentValuesList,
                    WidgetConstants.DisplayList.MAX_CUSTOM_LIST_SIZE
                ))
            }
        }
        insertContentValuesList.run {
            if (isNotEmpty()) {
                val insertCount = BulkInsertReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
                    .setConvert {
                        toTypedArray()
                    }
                    .build().exec()
                GLog.d(TAG, "restoreWidgetDisplayList: new=${jsonArray.length()} insertCount=$insertCount")
            } else {
                GLog.d(TAG, "restoreWidgetDisplayList: no new data")
            }
        }
    }

    private fun convertToDisplayListMap(jsonArray: JSONArray): MutableMap<String, MutableList<ContentValues>> {
        val contentValuesListMap = mutableMapOf<String, MutableList<ContentValues>>()
        for (i in 0 until jsonArray.length()) {
            val obj = jsonArray.getJSONObject(i)
            val contentValues = ContentValues().apply {
                put(WidgetDisplayListColumns.DATA, obj.getString(WidgetDisplayListColumns.DATA))
                put(
                    WidgetDisplayListColumns.DISPLAY_LIST_ID,
                    obj.getString(WidgetDisplayListColumns.DISPLAY_LIST_ID)
                )
                put(WidgetDisplayListColumns.INVALID, obj.getInt(WidgetDisplayListColumns.INVALID))
            }
            contentValuesListMap.computeIfAbsent(contentValues.getAsString(WidgetDisplayListColumns.DISPLAY_LIST_ID)) {
                mutableListOf()
            }.add(contentValues)
        }
        return contentValuesListMap
    }

    private fun getValidDisplayListItems(
        displayListId: String,
        contentValuesList: List<ContentValues>,
        maxSize: Int
    ): List<ContentValues> {
        // step 1: 获取当前轮播列表的文件记录项，只搬迁当前轮播不存在的条目
        val existsItems = queryExistsDisplayListItems(displayListId)
        val filePathToContentValueMap = mutableMapOf<String, ContentValues>().apply {
            contentValuesList.forEach { contentValues ->
                contentValues.getAsString(WidgetDisplayListColumns.DATA).run {
                    if (existsItems.contains(this).not()) {
                        put(this, contentValues)
                    }
                }
            }
        }
        if (filePathToContentValueMap.isEmpty()) {
            return emptyList()
        }
        val validDisplayListItems = mutableSetOf<ContentValues>().apply {
            // step 2: 查询local_media，已存在的项可以搬家
            QueryReq.Builder<ArrayList<String>>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(arrayOf(GalleryStore.GalleryColumns.LocalColumns.DATA))
                .setWhere(DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns.DATA, filePathToContentValueMap.size))
                .setWhereArgs(filePathToContentValueMap.keys.toTypedArray())
                .setConvert(FilePathsConvert())
                .build().exec().forEach {
                    filePathToContentValueMap.remove(it)?.run(::add)
                }
            // step 3: local_media不存在的项调用媒体库扫描，扫描到的结果可以搬家
            ApiDmManager.getMediaDBSyncDM().executeFilePathSync(filePathToContentValueMap.keys.toTypedArray())?.forEach {
                filePathToContentValueMap[it]?.run(::add)
            }
        }
        // step 4: 移除搬家数据的非法项，并截断补充到当前轮播列表(目前是上限50张)
        return contentValuesList.filter { validDisplayListItems.contains(it) }.let {
            it.subList(0, min(it.size, maxSize - existsItems.size))
        }
    }

    private fun queryExistsDisplayListItems(displayListId: String): Set<String> {
        return QueryReq.Builder<ArrayList<String>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setProjection(arrayOf(WidgetDisplayListColumns.DATA))
            .setWhere("${WidgetDisplayListColumns.DISPLAY_LIST_ID} = ?")
            .setWhereArgs(arrayOf(displayListId))
            .setConvert(FilePathsConvert()).build().exec().toSet()
    }
}