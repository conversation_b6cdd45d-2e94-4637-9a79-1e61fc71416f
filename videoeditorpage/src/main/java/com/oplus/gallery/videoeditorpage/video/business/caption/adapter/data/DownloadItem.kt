/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : DownloadItem.kt
 ** Description : 文字字体样式实体信息基类
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import com.oplus.gallery.foundation.util.mask.maskPath
import com.oplus.gallery.videoeditorpage.resource.util.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item.NO_BUILTIN

/**
 * 字幕需要网络下载的item
 */
open class DownloadItem(
    /**
     * 素材资源url
     */
    var resourceUrl: String = TextUtil.EMPTY_STRING,

    /**
     * 字体样式等id
     */
    val resourceId: String = TextUtil.EMPTY_STRING,

    /**
     * 中文icon素材url
     */
    protected var zhIconUrl: String = TextUtil.EMPTY_STRING,

    /**
     * 英文icon素材url
     */
    protected var enIconUrl: String = TextUtil.EMPTY_STRING
) : BaseItem() {

    /**
     * 当前的下载状态，0：待下载、1：已下载
     */
    var downloadState: Int = NOT_DOWNLOADED

    /**
     * 当前的进度 100为已完成
     */
    var progress: Int = 0

    /**
     * 素材资源本地路径
     */
    var localPath: String = TextUtil.EMPTY_STRING

    /**
     * 是否内置资源，0：否、1：是
     */
    var builtin: Int = NO_BUILTIN

    /**
     * 美摄sdk安装资源成功后的id，用于美摄去生成对应的字幕样式
     */
    var assetPackageId: String = TextUtil.EMPTY_STRING

    /**
     * icon素材url
     */
    var iconUrl: String
        get() {
           return if (ResourceUtils.isCurrentLanguageChinese()) {
               zhIconUrl
           } else {
               enIconUrl
           }
        }
        /**
         * 提供set方法供读取本地内置样式字体资源icon文件要做文件路径拼接设值
         */
        set(value) {
            if (ResourceUtils.isCurrentLanguageChinese()) {
                zhIconUrl = value
            } else {
                enIconUrl = value
            }
        }

    /**
     * 数据item的版本，标记当前的数据的版本号，用于和线上拉取数据对比是否有版本更新进行数据合并
     */
    var version: String = TextUtil.EMPTY_STRING

    /**
     * 资源文件的MD5值，用于检查已下载文件是否完整
     */
    var resourceMd5: String = TextUtil.EMPTY_STRING

    /**
     * 排序
     */
    var orderNum: Int = 0

    /**
     * 两个资源进行合并，主要是合并本地和云端的数据
     *
     * @param mergeRemoteItem 待合并的云端数据item
     * @return 返回合并后的结果，如果返回null，则表示没有数据被更新
     */
    open fun mergeWith(mergeRemoteItem: DownloadItem): DownloadItem? {
        // 两个资源不是同一个，则不需要做merge
        if (resourceId != mergeRemoteItem.resourceId) return null
        // 如果资源没有新版本更新，则直接返回没有数据被更新 null
        if (version == mergeRemoteItem.version) return null
        return mergeRemoteItem
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if ((other == null) || (this::class != other::class)) return false
        other as DownloadItem
        return resourceId == other.resourceId
    }

    override fun hashCode(): Int {
        return resourceId.hashCode()
    }

    override fun toString(): String {
        return ("DownloadItem{" +
                ", resourceId='" + resourceId + '\'' +
                ", resourceUrl='" + resourceUrl.maskPath() + '\'' +
                ", iconUrl='" + iconUrl.maskPath() + '\'' +
                ", downloadState='" + downloadState + '\'' +
                ", progress='" + progress + '\'' +
                ", localPath='" + localPath.maskPath() + '\''
                + '}'
                )
    }

    companion object {
        private const val TAG = "DownloadItem"

        /**
         * 资源已下载完成状态
         */
        const val DOWNLOADED = 1

        /**
         * 资源未下载完成状态
         */
        const val NOT_DOWNLOADED = 0
    }
}