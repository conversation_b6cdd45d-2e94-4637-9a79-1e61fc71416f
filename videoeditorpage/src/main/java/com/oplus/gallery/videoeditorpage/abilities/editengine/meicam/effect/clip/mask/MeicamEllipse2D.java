/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamEllipse2D.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask;


import android.graphics.PointF;

public class MeicamEllipse2D {

    /**
     * 中心点坐标
     */
    private PointF mCenter = new PointF(0, 0);
    /**
     * 横向长半轴长 对应屏幕宽度的比例 [-1,1]区间
     */
    private float mWidthPercent;
    /**
     * 竖向短半轴长 对应屏幕高度度的比例 [-1,1]区间
     */
    private float mHeightPercent;
    /**
     * 旋转角度
     */
    private float mTheta;

    public PointF getCenter() {
        return mCenter;
    }

    public void setCenter(PointF mCenter) {
        this.mCenter = mCenter;
    }

    public float getWidthPercent() {
        return mWidthPercent;
    }

    public void setWidthPercent(float mWidthPercent) {
        this.mWidthPercent = mWidthPercent;
    }

    public float getHeightPercent() {
        return mHeightPercent;
    }

    public void setHeightPercent(float mHeightPercent) {
        this.mHeightPercent = mHeightPercent;
    }

    public float getTheta() {
        return mTheta;
    }

    public void setTheta(float mTheta) {
        this.mTheta = mTheta;
    }

    @Override
    public String toString() {
        return "MeicamEllipse2D{"
                + "mCenter=" + mCenter
                + ", mWidthPercent="
                + mWidthPercent
                + ", mHeightPercent="
                + mHeightPercent
                + ", mTheta="
                + mTheta
                + '}';
    }
}
