/*
 * ********************************************************************
 *  ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 *  ** VENDOR_EDIT
 *  ** File        :  - SimpleDao.java
 *  ** Description : xxxxxxxxxxxx
 *  ** Version     : 1.0
 *  ** Date        : 2019/57/20
 *  ** Author      : <EMAIL>
 *  **
 *  ** ---------------------Revision History: ----------------------------
 *  **  <author>                 <data>      <version>  <desc>
 *  **  <EMAIL>  2019/7/20  1.0        build this module
 *  **********************************************************************
 */
package com.oplus.gallery.videoeditorpage.resource.room.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Update;

import com.oplus.gallery.videoeditorpage.resource.room.bean.Item;

import java.util.List;

@Dao
public interface SimpleDao<T> {
    int NOT_DOWNLOADED = Item.TYPE_NOT_DOWNLOADED;
    int ICON_DOWNLOADED = Item.TYPE_DOWNLOAD_ICON;
    int FILE_DOWNLOADED = Item.TYPE_DOWNLOAD_FILE;
    int ICON_DEFAULT = Item.TYPE_DEFAULT_ICON;

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(T item);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insert(List<T> items);

    @Delete
    void delete(T item);

    @Delete
    void delete(List<T> items);

    @Update
    int update(T item);

    @Update
    int updateAll(List<T> items);
}
