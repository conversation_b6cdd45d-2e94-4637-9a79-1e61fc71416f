/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CustomPictureManagerViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/01/05
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/01/05        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.picturemanager

import android.app.Application
import android.net.Uri
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.viewmodel.base.BaseViewModel
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.data.model.OnContentChangedListener
import com.oplus.gallery.framework.abilities.data.model.WidgetModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class CustomPictureManagerViewModel(application: Application) : BaseViewModel(application),
    OnContentChangedListener {
    companion object {
        const val TAG = "WidgetModeSelectionViewModel"
    }
    private val internalLiveData = MutableLiveData<CustomPictureManagerViewData>()
    val liveData: LiveData<CustomPictureManagerViewData> = internalLiveData
    private lateinit var widgetCode: String
    private var widgetModel: WidgetModel? = null

    fun setWidgetCode(widgetCode: String) {
        this.widgetCode = widgetCode
        widgetModel = DataRepository.getAlbumModel(DataRepository.LocalAlbumModelGetter.TYPE_WIDGET_ALBUM, Bundle().apply {
            putString(DataRepository.KEY_PATH_STR, SourceConstants.Local.PATH_ALBUM_WIDGET_ANY.getChild(widgetCode).toString())
        }) as WidgetModel
        widgetModel?.registerContentChangedListener(this)
        reload()
    }

    private fun reload() {
        launch(Dispatchers.ReloadTask) {
            val model = widgetModel ?: return@launch
            model.reload()
            if (!model.isContentLoaded()) {
                model.addWidget(widgetCode)
            }
            val widgetMode = model.getWidgetMode()
            var recommendCount = 0
            var customCount = 0
            when (widgetMode) {
                GalleryStore.WidgetSetColumns.MODE_RECOMMENDED -> {
                    recommendCount = model.getCount()
                    customCount = model.queryWidgetItemCount(widgetCode)
                }
                GalleryStore.WidgetSetColumns.MODE_CUSTOM, GalleryStore.WidgetSetColumns.MODE_DEFAULT -> {
                    recommendCount = model.queryRecommendWidgetItemCount()
                    customCount = model.getCount()
                }
                else -> GLog.e(TAG, "reload widgetMode:$widgetMode invalid!")
            }
            internalLiveData.postValue(CustomPictureManagerViewData(widgetMode, recommendCount, customCount))
        }
    }

    override fun onContentChanged(model: BaseModel<*>, uri: Uri?) {
        reload()
    }

    fun addToDisplayListByGalleryId(galleryIds: List<Int>, displayListId: String): Int {
        return widgetModel?.addToDisplayListByGalleryId(galleryIds, displayListId) ?: 0
    }

    fun updateWidgetMode(widgetCode: String, mode: Int): Boolean = widgetModel?.updateWidgetMode(widgetCode, mode) ?: false

    override fun onCleared() {
        super.onCleared()
        widgetModel?.unregisterContentChangedListener(this)
    }

    override fun getTag(): String {
        return TAG
    }
}