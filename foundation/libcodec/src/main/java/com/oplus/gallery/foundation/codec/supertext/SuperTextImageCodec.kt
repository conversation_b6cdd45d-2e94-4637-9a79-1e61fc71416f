/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SuperTextImageCodec
 ** Description: SuperTextImageCodec
 ** Version: 1.0
 ** Date : 2022/5/17
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/05/17    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.codec.supertext

/**
 * 非超级文本1.0图片的编解码（普通文本图片）
 * 定义为 internal，上层业务不能直接new SuperTextImageCodec（），需要通过SuperTextCodecFactory创建来使用
 */
internal class SuperTextImageCodec(filePath: String?, magicNumber: ByteArray) : BaseSuperTextImageCodec(filePath, magicNumber) {

    /**
     * 超级文本的幻数
     * @return  byte[]
     */
    override fun getMagicNumberFlag(): ByteArray {
        return SuperTextProperties.SUPER_MAGIC_NUMBER
    }

    /**
     * 获取标签
     */
    override fun getTag(): String {
        return TAG
    }

    /**
     * 是否EnhanceText （相机1.0模式拍出来的照片]）
     * @return false
     */
    override fun isEnhanceText(): Boolean {
        return false
    }

    private companion object {
        private const val TAG = "SuperTextImageCodec"
    }
}