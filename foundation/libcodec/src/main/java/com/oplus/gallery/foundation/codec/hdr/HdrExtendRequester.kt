/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : HdrExtendRequester.kt
 ** Description: 通过extend获取不同hdr图片的增益图和信息
 ** Version    : 1.0
 ** Date       : 2024/10/11
 ** Author     : <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                        <date>        <version>        <desc>
 ** houdong<PERSON>@Apps.Gallery        2024/10/11      1.0              init
 *************************************************************************************************/

package com.oplus.gallery.foundation.codec.hdr

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import com.oplus.gallery.addon.graphics.OplusImageHdrWrapper
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_LINEAR_MASK
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_META_DATA
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_UHDR_GAINMAP_IMAGE
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_UHDR_GAINMAP_INFO
import com.oplus.gallery.foundation.codec.extend.OplusUhdrInfoStruct
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.mask.maskPath
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.LHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack

/**
 * 通过extend获取不同hdr图片的增益图和信息
 * 主要分为
 * 1. local Hdr
 * 2. uHdr，jpeg等google通用的方案
 * 3. oplusUhdr，oplus自定义heif方案
 */
object HdrExtendRequester {
    private const val TAG = "HdrExtendRequester"
    private val BITMAP_CONFIG_LOCAL_HDR = Bitmap.Config.ALPHA_8
    private val DEBUG by lazy { GProperty.BITMAP_REQUESTER }

    /**
     * 通过fileExtend获取localHdr的增益图和信息
     * @return bitmap即增益图，byteArray即增益信息
     */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun requestLocalHdrResource(context: Context, filePath: String, contentUri: Uri): Pair<Bitmap, ByteArray>? {
        GLog.d(TAG, LogFlag.DL) { "requestLocalHdrResource filePath:${filePath.maskPath()} contentUri:$contentUri" }
        GTrace.traceBegin("requestLocalHdrResource.$filePath")
        try {
            FileExtendedContainer().use { fileExtendedContainer ->
                fileExtendedContainer.setDataSource(context = context, uri = contentUri, openFileMode = OpenFileMode.MODE_READ)
                val metaData = fileExtendedContainer.getExtensionData(EXTEND_KEY_LOCAL_HDR_META_DATA)
                if (metaData == null) {
                    GLog.e(TAG, LogFlag.DL) { "requestLocalHdrResource Meta data is null when parse from file." }
                    return null
                }
                val grayImage = fileExtendedContainer.getExtensionData(EXTEND_KEY_LOCAL_HDR_LINEAR_MASK)?.let { maskData ->
                    val options = BitmapFactory.Options().apply {
                        // Local HDR图片中目前存放的灰图编码为单通道8bit，解码用该config。
                        inPreferredConfig = BITMAP_CONFIG_LOCAL_HDR
                    }
                    // Marked by zhangwenming 后面考虑使用BitmapPool避免内存抖动
                    BitmapFactory.decodeByteArray(maskData, 0, maskData.size, options)
                }
                if (grayImage == null) {
                    GLog.e(TAG, LogFlag.DL) { "requestLocalHdrResource Gray image is null when parse from the file." }
                    return null
                }
                if (DEBUG) GLog.d(TAG, LogFlag.DL) { "requestLocalHdrResource grayImage:${grayImage.toShortString()} hdrInfo:${metaData.size}" }
                return Pair(grayImage, metaData)
            }
        } catch (e: Exception) {
            GLog.e(TAG, LogFlag.DL, "requestLocalHdrResource: ", e)
        } finally {
            // mark dingyong 这里加上log和trace是为了调试方便，上正式版的时候看是否要去掉。
            GTrace.traceEnd()
        }
        return null
    }

    /**
     * 通过fileExtend获取localHdr的增益图和信息，拼接成HdrImageDrawable
     * @param context context
     * @param filePath 文件路径
     * @param contentUri 文件uri
     * @param maxLength 指定增益图长边最大尺寸
     */
    @JvmStatic
    fun requestLocalHdrDrawable(context: Context, filePath: String, contentUri: Uri, maxLength: Int): HdrImageDrawable? {
        return requestLocalHdrResource(context, filePath, contentUri)?.let {
            HdrImageDrawable(scaleBitmap(it.first, maxLength), LHdrMetadataPack(it.second))
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "requestUltraHdrDrawable loadUltraHdrContent is null when pare image info pair:$it" }
            null
        }
    }

    /**
     * 通过fileExtend获取oplus自定义的uhdr图的增益图和信息，只给oplus自定义的heif图使用
     */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun requestOplusUhdrResource(context: Context, filePath: String, contentUri: Uri): Pair<Bitmap, UltraHdrInfo>? {
        GLog.d(TAG, LogFlag.DL) { "requestOplusUhdrResource filePath:${filePath.maskPath()} contentUri:$contentUri" }
        GTrace.traceBegin("requestOplusUhdrResource.$filePath")
        try {
            FileExtendedContainer().use { fileExtendedContainer ->
                fileExtendedContainer.setDataSource(context = context, uri = contentUri, openFileMode = OpenFileMode.MODE_READ)
                val metaData = fileExtendedContainer.getExtensionData(EXTEND_KEY_UHDR_GAINMAP_INFO)
                if (metaData == null) {
                    GLog.e(TAG, LogFlag.DL) { "requestOplusUhdrResource Meta data is null when parse from file." }
                    return null
                }
                val grayImage = fileExtendedContainer.getExtensionData(EXTEND_KEY_UHDR_GAINMAP_IMAGE)?.let { maskData ->
                    val options = BitmapFactory.Options()
                    // Marked by zhangwenming 后面考虑使用BitmapPool避免内存抖动
                    BitmapFactory.decodeByteArray(maskData, 0, maskData.size, options)
                }
                if (grayImage == null) {
                    GLog.e(TAG, LogFlag.DL) { "requestOplusUhdrResource Gray image is null when parse from the file." }
                    return null
                }
                val config = OplusUhdrInfoStruct(metaData).toData() ?: run {
                    GLog.e(TAG, LogFlag.DL) { "requestOplusUhdrResource UltraHdrInfo is null when parse from the file." }
                    return null
                }
                if (DEBUG) GLog.d(TAG, LogFlag.DL) { "requestUhdrDrawable grayImage:${grayImage.toShortString()} hdrInfo:${config.toDetailString()}" }
                return Pair(grayImage, config)
            }
        } catch (e: Exception) {
            GLog.e(TAG, LogFlag.DL, "requestOplusUhdrResource: ", e)
        } finally {
            // mark dingyong 这里加上log和trace是为了调试方便，上正式版的时候看是否要去掉。
            GTrace.traceEnd()
        }
        return null
    }

    /**
     * 通过fileExtend获取oplus自定义的uhdr图的数据，只给oplus自定义的heif图使用拼接成HdrImageDrawable
     *
     * @param maxLength 指定增益图长边最大尺寸
     */
    @JvmStatic
    fun requestOplusUhdrDrawable(context: Context, filePath: String, contentUri: Uri, maxLength: Int): HdrImageDrawable? {
        return requestOplusUhdrResource(context, filePath, contentUri)?.let { pair ->
            if (pair.second.checkIsValid().not()) {
                GLog.w(TAG, LogFlag.DL) { "[requestOplusUhdrDrawable] UltraHdrInfo is invalid for uri:$contentUri" }
                return null
            }
            HdrImageDrawable(scaleBitmap(pair.first, maxLength), UHdrMetadataPack(pair.second))
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "requestOplusUhdrDrawable loadUltraHdrContent is null when pare image info pair:$it" }
            null
        }
    }

    /**
     * 通过google通用的方式获取uhdr的增益图和信息。不能给oplus自定义的heif格式图片
     */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun requestUhdrResource(context: Context, contentUri: Uri): Pair<Bitmap, UltraHdrInfo>? {
        GLog.d(TAG, LogFlag.DL) { "requestUhdrResource contentUri:$contentUri" }
        try {
            GTrace.traceBegin("requestUhdrResource.$contentUri")
            return context.contentResolver.openFileDescriptor(contentUri, "r")?.use {
                OplusImageHdrWrapper.getUhdrImageAndInfo(it.fileDescriptor)?.apply {
                    GLog.d(TAG, LogFlag.DL) { "requestUhdrResource grayImage:${first.toShortString()} hdrInfo:${second.toDetailString()}" }
                }
            }
        } catch (e: Exception) {
            GLog.e(TAG, LogFlag.DL, "requestUhdrResource: ", e)
        } finally {
            GTrace.traceEnd()
        }
        return null
    }

    /**
     * 通过google通用的方式获取uhdr的数据。不能给oplus自定义的heif格式图片
     *
     * @param maxLength 指定增益图长边最大尺寸
     */
    @JvmStatic
    fun requestUhdrDrawable(context: Context, contentUri: Uri, maxLength: Int): HdrImageDrawable? {
        return requestUhdrResource(context, contentUri)?.let { pair ->
            HdrImageDrawable(scaleBitmap(pair.first, maxLength), UHdrMetadataPack(pair.second))
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "requestUhdrDrawable loadUltraHdrContent is null when pare image info pair:$it" }
            null
        }
    }

    /**
     * bugid：8082524，外部导入的大尺寸HDR照片，导致超大的 gainmap 上传，对其进行缩放
     *
     * @param bitmap Bitmap
     * @param maxLength
     * @return Bitmap
     */
    private fun scaleBitmap(bitmap: Bitmap, maxLength: Int): Bitmap {
        if (maxLength <= 0) {
            return bitmap
        }
        return BitmapUtils.resizeByLongSide(bitmap, maxLength, false).also { newBmp ->
            GLog.d(TAG, LogFlag.DL) { "scaleBitmap. maxLength=$maxLength, ${bitmap.toShortString()} -> ${newBmp.toShortString()}" }
        }
    }
}