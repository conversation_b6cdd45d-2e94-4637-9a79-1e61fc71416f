/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MeicamThemeHelper.java
 * * Description: helper for meicam theme.
 * * Version: 1.0
 * * Date : 2017/12/23
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/23    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import static com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo.THEME_SOURCE_BUILTIN;

import android.content.Context;
import android.graphics.RectF;
import android.text.TextUtils;

import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoTrack;
import com.meicam.themehelper.NvsImageFileDesc;
import com.meicam.themehelper.NvsMusicSelected;
import com.meicam.themehelper.NvsParseHelper;
import com.meicam.themehelper.NvsThemeHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper;
import com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.videoeditorpage.memories.engine.GalleryVideoEngineListener;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryThemeHelper;
import com.oplus.gallery.videoeditorpage.memories.util.MemoriesThemeCompatHelper;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

public class MeicamThemeHelper implements IGalleryThemeHelper {
    private static final String TAG = "MeicamThemeHelper";
    public static final String THEME_PREFIX = "theme/";
    //local music
    public static final String LOCAL_MUSIC_PREFIX = "local#";
    public static final String LOCAL_MUSIC_SPLIT = "#";
    public static final int LOCAL_MUSIC_START_TIME_INDEX = 1;
    public static final int LOCAL_MUSIC_END_TIME_INDEX = 2;
    public static final int LOCAL_MUSIC_FILE_PATH_INDEX = 3;

    private static final int CAPTION_TEXT_SIZE_REFERENCE_VIDEO_WIDTH = 720;
    private static final int CAPTION_TITLE_INDEX = 0;
    private static final int CAPTION_HINT_INDEX = 1;
    /**
     * 回忆标题使用的字幕weight值。
     * 美摄字幕更改了底层的渲染机制，字幕加粗的效果有改变，当前字幕加粗的取值范围是[0, 1000]，默认取值为700，目前设置400时视觉认为合适。
     */
    private static final int CAPTION_FONT_WEIGHT = 400;
    private static final float CAPTION_LETTER_SPACE = 105f; // percent, default 100, not changed
    private static final String EMPTY_TEXT = "";

    private static final String MUSIC_MUTE = "none";

    private Context mContext;
    private NvsTimeline mTimeline;
    private NvsThemeHelper mThemeHelper;
    private MeicamVideoEngine mEngine;
    private GalleryVideoEngineListener mListener;
    private String mTitleText;
    private String mHintText;
    private String mCurMusicId;
    private MediaInfo mCurCover;

    public MeicamThemeHelper(Context context, MeicamVideoEngine engine, GalleryVideoEngineListener listener) {
        mContext = context;
        mEngine = engine;
        mListener = listener;
        mThemeHelper = new NvsThemeHelper();
    }

    /**
     * mListener是VideoEditorActivity的匿名内部类，需要清空引用
     */
    public void clearListener() {
        mListener = null;
    }

    public void setTimeline(NvsTimeline timeline) {
        mTimeline = timeline;
    }

    private RectF getFaceRect(RectF face, float width, float height) {
        // x, y pos in (-1.0f, 1.0f)
        //x = faceX / imageW * 2.0f - 1.0f
        //y = 1.0f - faceY / imageH * 2.0f
        RectF faceRect = new RectF();
        faceRect.left = face.left / width * 2f - 1f;
        faceRect.top = 1f - face.top / height * 2f;
        faceRect.right = face.right / width * 2f - 1f;
        faceRect.bottom = 1f - face.bottom / height * 2f;
        return faceRect;
    }

    @Override
    public boolean initThemeVideoClips(ArrayList<MediaInfo> mediaInfos) {
        if (mTimeline == null) {
            GLog.w(TAG, "initThemeVideoClips mTimeline is null.");
            return false;
        }
        if ((mediaInfos == null) || mediaInfos.isEmpty()) {
            GLog.w(TAG, "initThemeVideoClips mediaInfos is null or empty.");
            return false;
        }
        ArrayList<NvsImageFileDesc> fileList = new ArrayList<>();
        for (MediaInfo info : mediaInfos) {
            if ((info != null) && (info.mInVideo || info.mOptimal)) {
                NvsImageFileDesc imageInfo = new NvsImageFileDesc();
                imageInfo.filePath = info.mUri;
                imageInfo.show = info.mInVideo;
                imageInfo.alternative = info.mOptimal;
                imageInfo.score = info.mScore;
                imageInfo.fileLastTime = info.mFileTime;
                imageInfo.hasFace = !info.mFace.isEmpty();
                if (imageInfo.hasFace) {
                    imageInfo.faceRect = getFaceRect(info.mFace, info.mWidth, info.mHeight);
                }

                fileList.add(imageInfo);
                GLog.d(TAG, "initThemeVideoClips size = " + fileList.size()
                        + ",  hasFace = " + imageInfo.hasFace
                        + ",  faceRect = " + imageInfo.faceRect
                        + ",  info = " + info);
            }
        }
        boolean result = mThemeHelper.initHelper(mContext, mTimeline, fileList);
        if (!result) {
            GLog.w(TAG, "initThemeVideoClips initHelper failed. result = " + result);
        }
        return result;
    }

    @Override
    public boolean insertThemeVideoClip(ArrayList<MediaInfo> infoList) {
        if ((infoList == null) || infoList.isEmpty()) {
            GLog.w(TAG, "insertThemeVideoClip infoList is null or empty.");
            return false;
        }
        ArrayList<NvsImageFileDesc> fileList = new ArrayList<>();
        for (MediaInfo info : infoList) {
            if (info != null) {
                NvsImageFileDesc imageInfo = new NvsImageFileDesc();
                imageInfo.filePath = info.mUri;
                imageInfo.show = true;
                imageInfo.alternative = info.mOptimal;
                imageInfo.score = info.mScore;
                imageInfo.fileLastTime = info.mFileTime;
                imageInfo.hasFace = !info.mFace.isEmpty();
                if (imageInfo.hasFace) {
                    imageInfo.faceRect = getFaceRect(info.mFace, info.mWidth, info.mHeight);
                }

                fileList.add(imageInfo);
                GLog.d(TAG, "insertThemeVideoClip size = " + fileList.size()
                        + ",  hasFace = " + imageInfo.hasFace
                        + ",  faceRect = " + imageInfo.faceRect
                        + ",  info = " + info);
            }
        }
        boolean result = mThemeHelper.insertClip(fileList);
        if (result) {
            mEngine.seekTo(0);
        }
        GLog.d(TAG, "insertThemeVideoClip result = " + result + ", size = " + fileList.size());
        return result;
    }

    @Override
    public boolean insertThemeVideoClip(MediaInfo info) {
        if (info == null) {
            GLog.w(TAG, "insertThemeVideoClip info is null.");
            return false;
        }
        NvsImageFileDesc imageInfo = new NvsImageFileDesc();
        imageInfo.filePath = info.mUri;
        imageInfo.show = true;
        imageInfo.alternative = info.mOptimal;
        imageInfo.score = info.mScore;
        imageInfo.fileLastTime = info.mFileTime;
        imageInfo.hasFace = !info.mFace.isEmpty();
        if (imageInfo.hasFace) {
            imageInfo.faceRect = getFaceRect(info.mFace, info.mWidth, info.mHeight);
        }

        ArrayList<NvsImageFileDesc> fileList = new ArrayList<>();
        fileList.add(imageInfo);
        boolean result = mThemeHelper.insertClip(fileList);
        if (result) {
            mEngine.seekTo(0);
        }
        GLog.d(TAG, "insertThemeVideoClip result = " + result + ", info = " + info);
        return result;
    }

    @Override
    public boolean deleteThemeVideoClip(MediaInfo info) {
        if (info == null) {
            GLog.w(TAG, "deleteThemeVideoClip info is null.");
            return false;
        }

        boolean result = mThemeHelper.deleteClip(info.mUri);
        if (result) {
            if (mListener != null) {
                mListener.onPlayPositionChange(mEngine.getCurrentTime());
            }
        }
        GLog.d(TAG, "deleteThemeVideoClip result = " + result + ", info = " + info);
        return result;
    }

    @Override
    public ArrayList<String> getThemeVideoClipList() {
        ArrayList<String> list = mThemeHelper.getTimelineClipPaths();
        GLog.d(TAG, "getThemeVideoClipList size = " + ((list != null) ? list.size() : null));
        return list;
    }

    @Override
    public int getThemeVideoClipCount() {
        int coverCount = (mCurCover == null) ? 0 : 1;
        int count = mThemeHelper.getPhotoCount() - coverCount;
        GLog.d(TAG, "getThemeVideoClipCount count = " + count + ", coverCount = " + coverCount);
        return count;
    }

    @Override
    public boolean addThemeVideoCover(MediaInfo info) {
        if ((info == null) || TextUtils.isEmpty(info.mPath)) {
            GLog.w(TAG, "addThemeVideoCover info is null or mPath is null. info = " + info);
            return false;
        }
        NvsImageFileDesc imageInfo = new NvsImageFileDesc();
        imageInfo.filePath = info.mUri;
        // cover can't used face...
        imageInfo.hasFace = false;//!info.mFace.isEmpty();
        if (imageInfo.hasFace) {
            imageInfo.faceRect = getFaceRect(info.mFace, info.mWidth, info.mHeight);
        }

        boolean result = mThemeHelper.applyCover(imageInfo);
        if (result) {
            mCurCover = info;
            mEngine.reset();
        }
        GLog.d(TAG, "addThemeVideoCover result = " + result
                + ",  hasFace = " + imageInfo.hasFace
                + ",  faceRect = " + imageInfo.faceRect
                + ",  info = " + info);
        return result;
    }

    @Override
    public boolean removeThemeVideoCover() {
        boolean result = mThemeHelper.applyCover(null);
        if (result) {
            mCurCover = null;
            mEngine.reset();
        }
        GLog.d(TAG, "removeThemeVideoCover mCurCover = " + mCurCover + ",  result = " + result);
        return result;
    }

    @Override
    public MediaInfo getThemeVideoCover() {
        return mCurCover;
    }

    @Override
    public void setThemeMusicMute(boolean isMute) {
        float volume = isMute ? 0.0f : 1.0f;
        boolean result = mThemeHelper.changeMusicVolumeGain(volume);
        GLog.d(TAG, "setThemeMusicMute isMute = " + isMute + ",  volume = " + volume + ",  result = " + result);
        if (isMute) {
            mCurMusicId = MUSIC_MUTE;
        }
    }

    private int getMusicType(String musicId) {
        if (TextUtils.isEmpty(musicId)) {
            GLog.w(TAG, "getMusicType musicId is null.");
            return NvsMusicSelected.NVS_THEME_MUSIC_SELECTED;
        }
        int type = NvsMusicSelected.NVS_MUSIC_NO_SELECTED;
        if (MusicSourceManager.getInstance().getMusicBySourcePath(musicId) != null) {
            type = NvsMusicSelected.NVS_MUSIC_PACKAGE_SELECTED;
        } else if (musicId.startsWith(LOCAL_MUSIC_PREFIX)) {
            type = NvsMusicSelected.NVS_LOCAL_MUSIC_SELECTED;
        }
        return type;
    }

    private String parseLocalMusic(String musicId, long[] trimTime) {
        GLog.d(TAG, "parseLocalMusic musicId = " + musicId);
        if (musicId.startsWith(LOCAL_MUSIC_PREFIX)) {
            try {
                String data[] = musicId.split(LOCAL_MUSIC_SPLIT);
                if (data.length > LOCAL_MUSIC_FILE_PATH_INDEX) {
                    long trimStart = Integer.parseInt(data[LOCAL_MUSIC_START_TIME_INDEX]);
                    long trimEnd = Integer.parseInt(data[LOCAL_MUSIC_END_TIME_INDEX]);
                    String filePath = data[LOCAL_MUSIC_FILE_PATH_INDEX];
                    GLog.d(TAG, "parseLocalMusic trimStart = " + trimStart
                            + ", trimEnd = " + trimEnd);
                    trimTime[0] = trimStart;
                    trimTime[1] = trimEnd;
                    if (TextUtils.isEmpty(filePath) || !(new File(filePath)).exists()) {
                        GLog.w(TAG, "parseLocalMusic filePath is not exists");
                        return null;
                    }
                    return filePath;
                }
            } catch (Exception e) {
                GLog.w(TAG, "parseLocalMusic parse local music failed, musicId = " + musicId + ", e:", e);
            }
        } else {
            GLog.w(TAG, "parseLocalMusic local music not match, musicId = " + musicId);
        }
        return null;
    }

    @Override
    public void addThemeMusic(String musicId) {
        if (TextUtils.isEmpty(musicId)) {
            GLog.w(TAG, "addThemeMusic musicId is null.");
            return;
        }
        int type = getMusicType(musicId);
        boolean result = false;
        String curMusic = null;
        GLog.d(TAG, "addThemeMusic type = " + type + ", musicId = " + musicId);
        try {
            if (type == NvsMusicSelected.NVS_LOCAL_MUSIC_SELECTED) {
                long[] trimTime = new long[2];
                String musicFilePath = parseLocalMusic(musicId, trimTime);
                if (!TextUtils.isEmpty(musicFilePath)) {
                    musicFilePath = MediaStoreUriHelper.getMusicUri(mContext, musicFilePath, null).toString();
                    // 此接口会做秒级的取整,再判断是否大于2，所以要大于3s的片段才能正常工作
                    result = mThemeHelper.changeMusic(musicFilePath, type, trimTime[0], trimTime[1]);
                }
            } else {
                curMusic = getCurrentThemeMusic();
                if (!TextUtils.equals(curMusic, musicId)) {
                    result = mThemeHelper.changeMusic(musicId, NvsMusicSelected.NVS_MUSIC_PACKAGE_SELECTED, -1, -1);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "addThemeMusic Exception : ", e);
        }
        GLog.d(TAG, "addThemeMusic type = " + type + ", musicId = " + musicId
                + ", curMusic = " + curMusic + ", result = " + result);
        mCurMusicId = musicId;
    }

    @Override
    public void addThemeTrimMusic(String musicId, long startTime, long endTime) {
        musicId = LOCAL_MUSIC_PREFIX + startTime * mEngine.getTimeBase() + LOCAL_MUSIC_SPLIT
                + endTime * mEngine.getTimeBase() + LOCAL_MUSIC_SPLIT + musicId;
        addThemeMusic(musicId);
    }

    @Override
    public void removeThemeMusic(boolean force) {
        int type = getMusicType(mCurMusicId);
        if (force || TextUtils.isEmpty(mCurMusicId)) {
            mThemeHelper.changeMusic(mThemeHelper.getCurrentThemeName(), NvsMusicSelected.NVS_THEME_MUSIC_SELECTED, -1, -1);
        } else {
            mThemeHelper.changeMusic(mCurMusicId, type, -1, -1);
        }
        GLog.d(TAG, "removeThemeMusic type = " + type + ",  force = " + force
                + ",  mCurMusicId = " + mCurMusicId
                + ",  getCurrentThemeName = " + mThemeHelper.getCurrentThemeName());
        mCurMusicId = null;
    }

    @Override
    public String getCurrentThemeMusic() {
        GLog.d(TAG, "getCurrentThemeMusic mCurMusicId = " + mCurMusicId);
        if (TextUtils.equals(mCurMusicId, MUSIC_MUTE)) {
            return mCurMusicId;
        }
        NvsMusicSelected curMusic = mThemeHelper.getCurrentMusicSelected();
        if ((curMusic != null) && (curMusic.selectedType != NvsMusicSelected.NVS_LOCAL_MUSIC_SELECTED)) {
            GLog.d(TAG, "getCurrentThemeMusic type = " + curMusic.selectedType + ", musicId = " + curMusic.selectedDesc);
            return curMusic.selectedDesc;
        }
        return mCurMusicId;
    }

    @Override
    public int getThemeCurrentMusicPos() {
        int index = 0;
        String musicId = getCurrentThemeMusic(); // music path
        String musicSourcePath = musicId;
        if (musicId.startsWith(MusicSourceManager.ASSETS_MUSIC_PATH)) {
            musicSourcePath = musicId.substring(MusicSourceManager.ASSETS_MUSIC_PATH.length(), musicId.length() - MusicSourceManager.MUSIC_SUFFIX.length());
        } else {
            int fileSeparatorIndex = musicId.lastIndexOf(File.separator);
            if (fileSeparatorIndex > 0) {
                musicSourcePath = musicId.substring(0, fileSeparatorIndex);
            }
        }
        int type = getMusicType(musicSourcePath);
        List<MusicItem> musicList = MusicSourceManager.getInstance().queryIconExistedMusic();

        /*if (type == NvsMusicSelected.NVS_THEME_MUSIC_SELECTED) {
            index = mThemeList.indexOf(music) + 1; // music contains none, so add one
        } else */
        // theme has not music anymore, used inner music instead.
        if (type == NvsMusicSelected.NVS_MUSIC_PACKAGE_SELECTED) {
            for (int i = 0; i < musicList.size(); i++) {
                if (TextUtils.equals(musicList.get(i).getSourcePath(), musicSourcePath)) {
                    index = i;
                    break;
                }
            }
        } else if (type == NvsMusicSelected.NVS_LOCAL_MUSIC_SELECTED) {
            index = musicList.size() - 1;
        }
        GLog.d(TAG, "getThemeCurrentMusicPos index = " + index
                + ", size = " + musicList.size() + ", music = " + musicId);
        return index;
    }

    @Override
    public boolean addTheme(ThemeInfo theme) {
        if (theme == null) {
            GLog.w(TAG, "addTheme path is null.");
            return false;
        }
        boolean result = mThemeHelper.applyTimelineTheme(
                theme.getFilePath(),
                theme.getSource() != THEME_SOURCE_BUILTIN);
        if (result) {
            String musicId = MusicSourceManager.getMemoriesMusicPath(theme);
            GLog.d(TAG, "addTheme success");
            addThemeMusic(musicId);
            /**
             * 美摄SDK升级后修改了底层的字幕渲染逻辑，导致主题样式包中字幕的偏移效果不一致。
             * 目前已更新内建的字幕样式，效果进行了对齐。但是回忆主题中还存在需要从服务器下载的7套样式，由于之前没有尝试过
             * 替换服务器中的样式，预计需要很长时间的测试来保证更新生产服务器中字幕样式的安全性。
             * 因此，目前暂时采用在代码层对涉及到的样式进行对齐的方案。
             */
            MemoriesThemeCompatHelper.INSTANCE.performThemeCaptionCompat(theme, mThemeHelper);
        } else {
            GLog.d(TAG, "addTheme fail");
        }
        return result;
    }

    @Override
    public void removeCurrentTheme() {
        if (mTimeline == null) {
            GLog.w(TAG, "removeCurrentTheme mTimeline is null.");
            return;
        }
        mTimeline.removeCurrentTheme();
    }

    @Override
    public String getCurrentTheme() {
        String themeId = mThemeHelper.getCurrentThemeName();
        GLog.d(TAG, "getCurrentTheme themeId = " + themeId);
        return themeId;
    }

    @Override
    public int getThemeCurrentThemePos() {
        int index = 0;
        List<ThemeItem> themeList = ThemeSourceManager.getInstance().queryIconExistedMemoriesTheme();
        String theme = getCurrentTheme();
        for (int i = 0; i < themeList.size(); i++) {
            ThemeItem item = themeList.get(i);
            if (TextUtils.equals(theme, item.getSourcePath())) {
                index = i;
            }
        }
        GLog.d(TAG, "getThemeCurrentThemePos index = " + index + ", theme:" + theme);
        return index;
    }

    @Override
    public boolean addThemeCaption(String title, String hint) {
        if (TextUtils.isEmpty(title) || TextUtils.isEmpty(hint)) {
            GLog.w(TAG, "addThemeCaption failed. title or hint is null or empty. title = " + title + ", hint = " + hint);
            return false;
        }
        boolean titleResult = mThemeHelper.changeCaptionText(title, CAPTION_TITLE_INDEX);
        setCaptionTitleSize();
        if (!titleResult) {
            GLog.w(TAG, "addThemeCaption add title failed. result = " + titleResult + ", title = " + title);
        } else {
            mTitleText = title;
        }
        boolean hintResult = mThemeHelper.changeCaptionText(hint, CAPTION_HINT_INDEX);
        setCaptionHintSize();
        if (!hintResult) {
            GLog.w(TAG, "addThemeCaption add hint failed. hintResult = " + hintResult + ", hint = " + hint);
        } else {
            mHintText = hint;
        }
        GLog.d(TAG, "addThemeCaption ok, title = " + title + ", hint = " + hint);
        return (titleResult || hintResult);
    }

    @Override
    public boolean updateThemeCaptionTitle(String title) {
        if (TextUtils.isEmpty(title)) {
            GLog.w(TAG, "updateCaptionTitle failed. title is null or empty.");
            return false;
        }
        mThemeHelper.changeCaptionText(title, CAPTION_TITLE_INDEX);
        setCaptionTitleSize();
        mTitleText = mThemeHelper.getCaptionText(CAPTION_TITLE_INDEX);
        if (!TextUtils.equals(title, mTitleText)) {
            GLog.w(TAG, "updateCaptionTitle add title failed. title = " + title + ", mTitleText = " + mTitleText);
            return false;
        }
        GLog.d(TAG, "updateCaptionTitle ok, title = " + title);
        return true;
    }

    private void setCaptionTitleSize() {
        int videoW = mEngine.getVideoWidth();
        float textSize = mContext.getResources().getDimension(R.dimen.memories_editor_theme_title_text_size)
                * videoW / CAPTION_TEXT_SIZE_REFERENCE_VIDEO_WIDTH;
        boolean fontResult = mThemeHelper.changeCaptionFontSize(textSize, CAPTION_TITLE_INDEX);
        boolean letterResult = mThemeHelper.changeLetterSpacing(CAPTION_LETTER_SPACE, CAPTION_TITLE_INDEX);
        /**
         * 美摄的字幕样式文件（captionstyle）实际是加密的xml文件。
         * 在我们使用的样式中包含16:9、9:16和1:1三种类别，其中默认的是16:9，如果没有能够恰好匹配的比例，将使用默认的16:9类别指定的样式参数。
         * 字幕样式中能够设置的参数有偏移位置、字体大小、letter spacing和字体weight等，这些参数与美摄SDK提供的字幕样式调整接口一致，
         * 并且通过接口设置的参数和字幕样式中的参数是可以相互覆盖的，以生成回忆封面字幕前最后设置的参数为准。
         */
        boolean fontWeightResult = mThemeHelper.changeCaptionFontWeight(CAPTION_FONT_WEIGHT, CAPTION_TITLE_INDEX);
        GLog.d(TAG, "setCaptionTitleSize, videoW = " + videoW
                + ", textSize = " + textSize + ", fontResult = " + fontResult
                + ", letterResult = " + letterResult + ", fontWeightResult = " + fontWeightResult);
    }

    private void setCaptionHintSize() {
        int videoW = mEngine.getVideoWidth();
        float textSize = mContext.getResources().getDimension(R.dimen.memories_editor_theme_hint_text_size)
                * videoW / CAPTION_TEXT_SIZE_REFERENCE_VIDEO_WIDTH;
        boolean result = mThemeHelper.changeCaptionFontSize(textSize, CAPTION_HINT_INDEX);
        GLog.d(TAG, "setCaptionHintSize,  videoW = " + videoW
                + ", textSize = " + textSize + ", result = " + result);
    }

    @Override
    public void removeThemeCaption() {
        mThemeHelper.changeCaptionText(EMPTY_TEXT, CAPTION_TITLE_INDEX);
        mThemeHelper.changeCaptionText(EMPTY_TEXT, CAPTION_HINT_INDEX);
    }

    @Override
    public void reAddThemeCaption() {
        GLog.d(TAG, "reAddCaption mTitleText = " + mTitleText + ", mHintText = " + mHintText);
        addThemeCaption(mTitleText, mHintText);
    }

    @Override
    public String getThemeTitleText() {
        return mTitleText;
    }

    @Override
    public String getThemeHintText() {
        return mHintText;
    }

    @Override
    public boolean changeThemeDuration(long time) {
        if (mTimeline == null) {
            GLog.w(TAG, "changeThemeDuration mTimeline is null.");
            return false;
        }
        boolean result = mThemeHelper.changeTimelineDuration(time * mEngine.getTimeBase());
        GLog.d(TAG, "changeThemeDuration duration = " + time + ", result = " + result);
        return result;
    }

    @Override
    public long getThemeMinTotalTime() {
        long time = mThemeHelper.getMinTotalTime() / mEngine.getTimeBase();
        GLog.d(TAG, "getThemeMinTotalTime time = " + time);
        return time;
    }

    @Override
    public long getThemeMaxTotalTime() {
        long time = mThemeHelper.getmaxTotalTime() / mEngine.getTimeBase();
        GLog.d(TAG, "getThemeMaxTotalTime time = " + time);
        return time;
    }

    private long getVideoClipStartTime(int index) {
        if (mTimeline == null) {
            GLog.w(TAG, "getVideoClipStartTime mTimeline is null.");
            return -1;
        }
        if (index < 0) {
            GLog.w(TAG, "getVideoClipStartTime index is invalid. index = " + index);
            return -1;
        }
        NvsVideoTrack track = mTimeline.getVideoTrackByIndex(0);
        GLog.d(TAG, "getVideoClipStartTime index = " + index
                + ", getClipCount = " + ((track != null) ? track.getClipCount() : null));
        if ((track != null) && (track.getClipCount() > index)) {
            NvsVideoClip clip = track.getClipByIndex(index);
            GLog.d(TAG, "getVideoClipStartTime clip = " + clip);
            if (clip != null) {
                return clip.getInPoint();
            }
        }

        return -1;
    }

    // time : milliseconds
    @Override
    public void seekToThemePosition(int index) {
        long startTime = getVideoClipStartTime(index);
        GLog.w(TAG, "seekToThemePosition startTime = " + startTime);
        if (startTime >= 0) {
            mEngine.seekTo(startTime / mEngine.getTimeBase());
            if (mListener != null) {
                mListener.onPlayPositionChange(startTime);
            }
        }
    }

    @Override
    public void cleanBuiltinTransition() {
        if (mTimeline == null) {
            GLog.w(TAG, "cleanBuiltinTransition mTimeline is null.");
            return;
        }
        NvsVideoTrack track = mTimeline.getVideoTrackByIndex(0);
        GLog.d(TAG, "cleanBuiltinTransition"
                + ", getClipCount = " + ((track != null) ? track.getClipCount() : null));
        if ((track != null) && (track.getClipCount() > 0)) {
            try {
                for (int i = 0; i < track.getClipCount(); i++) {
                    track.setBuiltinTransition(i, null);
                }
            } catch (Exception e) {
                GLog.e(TAG, "cleanBuiltinTransition error:", e);
            }
        }
    }

    public void dumpThemeInfo(PrintWriter writer) {
        writer.println("----------------------- Dump GalleryThemeHelper start -----------------------");
        writer.println("dump MaxTotalTime = " + mThemeHelper.getmaxTotalTime());
        writer.println("dump MinTotalTime = " + mThemeHelper.getMinTotalTime());
        writer.println("dump PhotoCount = " + mThemeHelper.getPhotoCount());
        writer.println("dump titleText = " + mThemeHelper.getCaptionText(CAPTION_TITLE_INDEX));
        writer.println("dump timeText = " + mThemeHelper.getCaptionText(CAPTION_HINT_INDEX));
        writer.println("dump CurrentTheme = " + mThemeHelper.getCurrentThemeName());
        NvsMusicSelected music = mThemeHelper.getCurrentMusicSelected();
        if (music != null) {
            writer.println("dump CurrentMusic = " + music.selectedDesc);
        }
        NvsImageFileDesc cover = mThemeHelper.getCover();
        if (cover != null) {
            writer.println("dump CurrentCover = " + cover.filePath);
        }
        ArrayList<String> curVideoClipList = mThemeHelper.getTimelineClipPaths();
        if ((curVideoClipList != null) && (curVideoClipList.size() > 0)) {
            writer.println("-------- dump curVideoClipList --------");
            writer.println("dump VideoClipList.size = " + curVideoClipList.size());
            try {
                for (int i = 0; i < curVideoClipList.size(); i++) {
                    writer.println("dump curVideoClipList i " + i
                            + ", fileLastTime = " + NvsParseHelper.getFileLastTime(curVideoClipList.get(i))
                            + ", info = " + curVideoClipList.get(i));
                }
            } catch (Exception e) {
                GLog.e(TAG, "dumpThemeInfo e = " + e);
            }
        }
        writer.println("----------------------- Dump GalleryThemeHelper end -----------------------");
        writer.println("");
    }
}
