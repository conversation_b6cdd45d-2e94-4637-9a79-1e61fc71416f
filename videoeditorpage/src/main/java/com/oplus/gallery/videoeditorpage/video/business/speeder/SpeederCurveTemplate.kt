/***********************************************************
 ** Copyright (C)(1), 2020-2030(1), OPLUS Mobile Comm Corp.(1), Ltd.
 ** File:  SpeederType
 ** Description: 变速类型的枚举类
 ** Version: 1.0
 ** Date : 2025/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/5/12      1.0             NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.speeder

import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_NONE
import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_BULLET_TIME
import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_FLASH_IN
import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_FLASH_OUT
import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_HEROIC_MOMENT
import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_JUMP
import com.oplus.gallery.videoeditorpage.video.business.speeder.SpeederConstant.CURVE_TEMPLATE_MONTAGE

/**
 * 曲线变速模板的枚举类
 */
enum class SpeederCurveTemplate(val value: Int) {

    /**
     * 无模板
     */
    NONE(CURVE_TEMPLATE_NONE), // 直接使用字面量代替常量引用

    /**
     * 蒙太奇模板
     */
    MONTAGE(CURVE_TEMPLATE_MONTAGE),

    /**
     * 英雄时刻模板
     */
    HEROIC_MOMENT(CURVE_TEMPLATE_HEROIC_MOMENT),

    /**
     * 子弹时刻模板
     */
    BULLET_TIME(CURVE_TEMPLATE_BULLET_TIME),

    /**
     * 跳接模板
     */
    JUMP(CURVE_TEMPLATE_JUMP),

    /**
     * 闪进模板
     */
    FLASH_IN(CURVE_TEMPLATE_FLASH_IN),

    /**
     * 闪出模板
     */
    FLASH_OUT(CURVE_TEMPLATE_FLASH_OUT);

    companion object {
        /**
         * 根据 value 获取对应的枚举值
         */
        fun fromValue(value: Int): SpeederCurveTemplate {
            return entries.firstOrNull { it.value == value } ?: NONE
        }
    }
}