/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PhotoAutoRect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.BOTTOMLEFT_TO_TOPRIGHT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.BOTTOMRIGHT_TO_TOPLEFT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.BOTTOM_TO_TOP;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.ENLARGE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.LEFT_TO_RIGHT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.REDUCE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.RIGHT_TO_LEFT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.TOPLEFT_TO_BOTTOMRIGHT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.TOPRIGHT_TO_BOTTOMLEFT;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.PhotoAutoRect.TransformType.TOP_TO_BOTTOM;

import android.graphics.RectF;

import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.Random;

public class PhotoAutoRect {
    private static final float ONESECOND = 1000000f;
    private static final float RESERVED_SPACE = 12f / 15;
    private static final float MIN_SIMILARITY = 0.01f;
    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final int TWO = 2;
    private static final int THREE = 3;
    private static final int FOUR = 4;
    private static final int FIVE = 5;
    private static final int SIX = 6;
    private static final int SEVEN = 7;
    private static final int EIGHT = 8;
    private static final int NINE = 9;
    private static final int TEN = 10;
    private static final int MIN_STEP = 10;
    private static final float MIN_MOVE_PERCENT = 0.3f;
    private static final float TEN_PRECENT = 0.1f;
    private static final float LARGE_HEIGHT = 9 / 16f;
    private static final float BIG_HEIGHT = 3 / 4f;
    private static final float BIG_WIDTH = 4 / 3f;
    private static final float LARGE_WIDTH = 16 / 9f;
    private static final float GREAT_WIDTH = 1.78f;
    private static final float HALF = 2;
    private static final float SMALL_SCALE = 0.85f;
    private static final float MIN_STEP_PRECENT = 0.05f;
    private static final float MIN_MOVE_TIME = 0.5f;

    public enum TransformType {
        LEFT_TO_RIGHT, RIGHT_TO_LEFT, TOP_TO_BOTTOM, BOTTOM_TO_TOP, ENLARGE, REDUCE,
        TOPLEFT_TO_BOTTOMRIGHT, TOPRIGHT_TO_BOTTOMLEFT, BOTTOMRIGHT_TO_TOPLEFT, BOTTOMLEFT_TO_TOPRIGHT
    }

    private TransformType[] mRandomTransformType = {LEFT_TO_RIGHT, RIGHT_TO_LEFT, TOP_TO_BOTTOM, BOTTOM_TO_TOP, ENLARGE, REDUCE,
            TOPLEFT_TO_BOTTOMRIGHT, TOPRIGHT_TO_BOTTOMLEFT, BOTTOMRIGHT_TO_TOPLEFT, BOTTOMLEFT_TO_TOPRIGHT, REDUCE, REDUCE, REDUCE};
    private Random mRandom;
    private float mCurMakeRatio;
    private float mImageRatio;
    private float mImgWidth;
    private float mImgHeight;
    private static final String TAG = "PhotoAutoRect";
    private AutoRect mStartAutoRect;
    private AutoRect mEndAutoRect;
    private TransformType mTransformType;
    private AutoRect mFinalAutoRect;
    private AutoRect mFaceAutoRect;
    private float mDistanceRadio;
    private float mCenterHeight;
    private float mCenterWidth;
    private boolean mIsFaceCheck;
    private float mDuration;

    /**
     * 使用Builder创建PhotoAutoRect的构造函数
     *
     * @param builder Builder对象
     * @throws Exception 当width或height为0时抛出异常
     */
    private PhotoAutoRect(Builder builder) throws Exception {
        int width = builder.mWidth;
        int height = builder.mHeight;
        float curMakeRatio = builder.mCurMakeRatio;
        int transformType = builder.mTransformType;
        RectF endORI = builder.mEndORI;
        boolean autoMove = builder.mAutoMove;
        float duration = builder.mDuration;

        if ((width != 0) && (height != 0)) {
            this.mRandom = new Random();
            this.mImgHeight = height;
            this.mImgWidth = width;
            this.mImageRatio = mImgWidth / mImgHeight;
            this.mDistanceRadio = TEN_PRECENT * duration / ONESECOND;
            this.mDuration = duration / ONESECOND;
            this.mCurMakeRatio = curMakeRatio;
            this.mFinalAutoRect = new AutoRect(endORI, mImgWidth, mImgHeight, mCurMakeRatio);
            this.mStartAutoRect = new AutoRect(mImgWidth, mImgHeight, mCurMakeRatio);
            this.mEndAutoRect = new AutoRect(mImgWidth, mImgHeight, mCurMakeRatio);
            this.mIsFaceCheck = false;
            if (autoMove) {
                autoSetTransFrom();
            } else {
                this.mTransformType = convertImageTransformType(transformType);
            }
            init();
        } else {
            throw new Exception("width  is not 0 or height is not 0");
        }
    }

    public boolean changeSize(float curMakeRatio, long duration) {
        if ((mStartAutoRect == null) || (mFinalAutoRect == null) || (mEndAutoRect == null)) {
            return false;
        }
        if ((mStartAutoRect.getWidth() == 0) || (mStartAutoRect.getHeight() == 0)) {
            return false;
        }
        if ((mFinalAutoRect.getWidth() == 0) || (mFinalAutoRect.getHeight() == 0)) {
            return false;
        }
        if ((mEndAutoRect.getWidth() == 0) || (mEndAutoRect.getHeight() == 0)) {
            return false;
        }
        GLog.i(TAG, "changeSize   " + curMakeRatio);
        setDuration(duration);
        this.mCurMakeRatio = curMakeRatio;
        mFinalAutoRect.setCurMakeRatio(curMakeRatio);
        mStartAutoRect.setCurMakeRatio(curMakeRatio);
        mEndAutoRect.setCurMakeRatio(curMakeRatio);
        init();
        run();
        if (mIsFaceCheck && (mFaceAutoRect != null)) {
            referenceFaceRect(mFaceAutoRect, isLessMinMoveTime());
        }
        return true;
    }

    public void setDuration(long duration) {
        setDuration(duration, false);
    }

    public void setDuration(long duration, boolean autoRun) {
        this.mDuration = (float) duration / ONESECOND;
        this.mDistanceRadio = TEN_PRECENT * duration / ONESECOND;
        if (autoRun) {
            run();
        }
    }

    private boolean isLessMinMoveTime() {
        return mDuration < MIN_MOVE_TIME;
    }

    public float getCurMakeRatio() {
        return mCurMakeRatio;
    }

    public boolean referenceFaceRect(AutoRect autoRect, boolean lessMinTime) {
        if ((Math.abs(autoRect.getPointX() - mStartAutoRect.getWidth() / 2) < 0)
                && (Math.abs(autoRect.getPointY() - mEndAutoRect.getHeight() / 2) < 0)) {
            return false;
        }
        mFinalAutoRect.moveToCenterInRectF(autoRect);
        float minX = 0;
        float minY = 0;
        if (autoRect.getPointX() - mStartAutoRect.getPointX() > 0) {
            minX = mImgWidth * MIN_SIMILARITY;

        } else {
            minX = -mImgWidth * MIN_SIMILARITY;
        }
        if (autoRect.getPointY() - mStartAutoRect.getPointY() > 0) {
            minY = mImgHeight * MIN_SIMILARITY;
        } else {
            minY = -mImgHeight * MIN_SIMILARITY;
        }
        if (lessMinTime) {
            mEndAutoRect = mStartAutoRect.cloneRect();
        }
        while (mStartAutoRect.tryMoveView(minX, 0) && mEndAutoRect.tryMoveView(minX, 0)
                && (Math.abs(mStartAutoRect.getPointX() - autoRect.getPointX()) >= Math.abs(minX * HALF))) {
            if ((!mStartAutoRect.moveView(minX, 0)) || (!mEndAutoRect.moveView(minX, 0))) {
                break;
            }
        }
        while (mStartAutoRect.tryMoveView(0, minY) && mEndAutoRect.tryMoveView(0, minY)
                && (Math.abs(mStartAutoRect.getPointY() - autoRect.getPointY()) >= Math.abs(minY * HALF))) {
            if ((!mStartAutoRect.moveView(0, minY)) || (!mEndAutoRect.moveView(0, minY))) {
                break;
            }
        }
        mFaceAutoRect = autoRect;
        mIsFaceCheck = true;
        return true;
    }

    private TransformType convertImageTransformType(int transform) {
        TransformType transformType = null;
        switch (transform) {
            case ZERO:
                transformType = ENLARGE;
                break;
            case ONE:
                transformType = RIGHT_TO_LEFT;
                break;
            case TWO:
                transformType = LEFT_TO_RIGHT;
                break;
            case THREE:
                transformType = BOTTOM_TO_TOP;
                break;
            case FOUR:
                transformType = TOP_TO_BOTTOM;
                break;
            case FIVE:
                transformType = TOPLEFT_TO_BOTTOMRIGHT;
                break;
            case SIX:
                transformType = BOTTOMLEFT_TO_TOPRIGHT;
                break;
            case SEVEN:
                transformType = TOPRIGHT_TO_BOTTOMLEFT;
                break;
            case EIGHT:
                transformType = BOTTOMRIGHT_TO_TOPLEFT;
                break;
            case NINE:
                transformType = ENLARGE;
                break;
            case TEN:
                transformType = REDUCE;
                break;
            default:
                transformType = REDUCE;
        }
        return transformType;
    }

    public boolean isNotSlanting() {
        if ((mTransformType == BOTTOMLEFT_TO_TOPRIGHT) || (mTransformType == BOTTOMRIGHT_TO_TOPLEFT) || (mTransformType == TOPLEFT_TO_BOTTOMRIGHT) || (mTransformType == TOPRIGHT_TO_BOTTOMLEFT)) {
            return false;
        }
        return true;
    }

    public boolean isVertical() {
        if ((mTransformType == BOTTOM_TO_TOP) || (mTransformType == TOP_TO_BOTTOM)) {
            return true;
        }
        return false;
    }

    public boolean isHorizontal() {
        if ((mTransformType == LEFT_TO_RIGHT) || (mTransformType == RIGHT_TO_LEFT)) {
            return true;
        }
        return false;
    }

    private void init() {
        calculateCenterDimension();
        if (mImageRatio < 1) {
            mFinalAutoRect.moveTo(mFinalAutoRect.getPointX(), mImgHeight * MIN_MOVE_PERCENT);
        }

        if ((mTransformType == ENLARGE) || (mTransformType == REDUCE)) {
            if (mImageRatio > mCurMakeRatio) {
                mCenterHeight = mImgHeight;
                mCenterWidth = mCenterHeight * mCurMakeRatio;
            } else {
                mCenterWidth = mImgWidth;
                mCenterHeight = mCenterWidth / mCurMakeRatio;
            }
        }
        if (mCenterWidth > mImgWidth) {
            mCenterWidth = mImgWidth * RESERVED_SPACE;
            mCenterHeight = mCenterWidth * mCurMakeRatio;
        }
        if (mCenterHeight > mImgHeight) {
            mCenterHeight = mCenterHeight * RESERVED_SPACE;
            mCenterWidth = mCenterHeight / mCurMakeRatio;
        }
        float left = (mImgWidth - mCenterWidth) / HALF;
        float right = left + mCenterWidth;
        float top = (mImgHeight - mCenterHeight) / HALF;
        float bottom = top + mCenterHeight;
        mStartAutoRect.set(left, top, right, bottom);
        mEndAutoRect.set(left, top, right, bottom);
    }

    private void calculateCenterDimension() {
        if (Math.abs(mImageRatio - mCurMakeRatio) <= MIN_SIMILARITY) {
            mCenterHeight = mImgHeight * RESERVED_SPACE;
            mCenterWidth = (float) (mCenterHeight * mCurMakeRatio);
        } else if ((mImageRatio > GREAT_WIDTH) && isHorizontal()) {
            mCenterHeight = mImgHeight;
            mCenterWidth = (float) (mCenterHeight * mCurMakeRatio);
        } else if (mImageRatio > 1) {
            if (isHorizontal()) {
                mCenterHeight = mImgHeight;
            } else {
                mCenterHeight = (float) (mImgHeight * RESERVED_SPACE);
            }
            mCenterWidth = (float) (mCenterHeight * mCurMakeRatio);
            if (mCenterWidth >= mImgWidth) {
                mCenterWidth = (float) (mImgWidth * RESERVED_SPACE);
                mCenterHeight = (float) (mCenterWidth / mCurMakeRatio);
            }
        } else if ((mImageRatio >= BIG_HEIGHT) && (isVertical())) {
            mCenterWidth = mImgWidth;
            mCenterHeight = (float) (mCenterWidth / mCurMakeRatio);
            if (mCenterHeight >= mImgHeight) {
                mCenterHeight = (float) (mImgHeight * RESERVED_SPACE);
                mCenterWidth = (float) (mCenterHeight * mCurMakeRatio);
            }
        } else if ((mImageRatio >= BIG_HEIGHT) || (!isNotSlanting())) {
            mCenterWidth = (float) (mImgWidth * RESERVED_SPACE);
            mCenterHeight = (float) (mCenterWidth / mCurMakeRatio);
            if (mCenterHeight >= mImgHeight) {
                mCenterHeight = (float) (mImgHeight * RESERVED_SPACE);
                mCenterWidth = (float) (mCenterHeight * mCurMakeRatio);
            }
        } else if ((mImgWidth / mCurMakeRatio) < mImgHeight) {
            if (isVertical()) {
                mCenterWidth = mImgWidth;
            } else {
                mCenterWidth = mImgWidth * RESERVED_SPACE;
            }
            mCenterHeight = (float) (mCenterWidth / mCurMakeRatio);
        } else if ((mImgWidth / mCurMakeRatio) > mImgHeight) {
            mCenterHeight = mImgHeight * RESERVED_SPACE;
            mCenterWidth = mCenterHeight * mCurMakeRatio;
        }
    }

    private void autoSetTransFrom() {
        if (Float.compare(mImageRatio, mCurMakeRatio) == 0) {
            this.mTransformType = randomType();
        } else if (mImageRatio <= LARGE_HEIGHT) {
            this.mTransformType = BOTTOM_TO_TOP;
        } else if (mImageRatio < BIG_HEIGHT) {
            switch (mRandom.nextInt(TWO)) {
                case ONE:
                    this.mTransformType = REDUCE;
                    break;
                default:
                    this.mTransformType = BOTTOM_TO_TOP;
                    break;
            }
        } else if (mImageRatio <= BIG_WIDTH) {
            this.mTransformType = randomType();
        } else if (mImageRatio < LARGE_WIDTH) {
            switch (mRandom.nextInt(FIVE)) {
                case ZERO:
                    this.mTransformType = LEFT_TO_RIGHT;
                    break;
                case ONE:
                    this.mTransformType = RIGHT_TO_LEFT;
                    break;
                case TWO:
                    this.mTransformType = ENLARGE;
                    break;
                case THREE:
                    this.mTransformType = REDUCE;
                    break;
                default:
                    this.mTransformType = REDUCE;
                    break;
            }
        } else {
            switch (mRandom.nextInt(FIVE)) {
                case ZERO:
                    this.mTransformType = LEFT_TO_RIGHT;
                    break;
                case ONE:
                    this.mTransformType = RIGHT_TO_LEFT;
                    break;
                default:
                    break;
            }
        }
        if (this.mTransformType == null) {
            this.mTransformType = REDUCE;
        }
    }

    public TransformType randomType() {
        TransformType transformType = mRandomTransformType[mRandom.nextInt(mRandomTransformType.length)];
        return transformType;
    }


    public void run() {
        float halfX = 0F;
        float halfY = 0F;
        float minDistance = 0F;
        if (mImageRatio < 1) {
            halfX = mImgWidth * mDistanceRadio;
            halfY = halfX;
            minDistance = halfX * TEN_PRECENT;
            if ((minDistance >= mImgWidth * MIN_STEP_PRECENT) || (minDistance <= 0)) {
                minDistance = mImgWidth * MIN_STEP_PRECENT;
            }
        } else {
            halfY = mImgHeight * mDistanceRadio;
            halfX = halfY;
            minDistance = halfY * TEN_PRECENT;
            if ((minDistance >= mImgHeight * MIN_STEP_PRECENT) || (minDistance <= 0)) {
                minDistance = mImgHeight * MIN_STEP_PRECENT;
            }
        }

        if (halfX == 0) {
            halfX = MIN_STEP;
        }
        if (halfY == 0) {
            halfY = MIN_STEP;
        }
        adjustAutoRectByTransformType(halfX, halfY, minDistance);
        if (isLessMinMoveTime()) {
            mEndAutoRect = mStartAutoRect.cloneRect();
        }
        if (!isScale()) {
            GLog.i(TAG, "  mTransformType  " + mTransformType + " mImgWidth  " + mImgWidth + "    +mImgHeight   "
                    + mImgHeight + "+   distanceX  " + Math.abs(mStartAutoRect.getPointX() - mEndAutoRect.getPointX())
                    + "   distanceY " + (Math.abs(mStartAutoRect.getPointY() - mEndAutoRect.getPointY()) + "  "));
        }
    }

    private void adjustAutoRectByTransformType(float halfX, float halfY, float minDistance) {
        switch (mTransformType) {
            case REDUCE:
                if (!mStartAutoRect.smallRectF(SMALL_SCALE)) {
                    mStartAutoRect.smallRectF(HALF * halfX, HALF * halfY);
                }
                break;
            case ENLARGE:
                if (!mEndAutoRect.smallRectF(SMALL_SCALE)) {
                    mEndAutoRect.smallRectF(HALF * halfX, HALF * halfY);
                }
                break;
            case TOP_TO_BOTTOM:
                moveViewVertical(-minDistance, minDistance, minDistance);
                break;
            case BOTTOM_TO_TOP:
                moveViewVertical(minDistance, -minDistance, minDistance);
                break;
            case LEFT_TO_RIGHT:
                moveViewHorizontal(-minDistance, minDistance, minDistance);
                break;
            case RIGHT_TO_LEFT:
                moveViewHorizontal(minDistance, -minDistance, minDistance);
                break;
            case TOPLEFT_TO_BOTTOMRIGHT:
                moveViewVertical(-minDistance, minDistance, minDistance);
                moveViewHorizontal(-minDistance, minDistance, minDistance);
                break;
            case BOTTOMRIGHT_TO_TOPLEFT:
                moveViewVertical(minDistance, -minDistance, minDistance);
                moveViewHorizontal(minDistance, -minDistance, minDistance);
                break;
            case TOPRIGHT_TO_BOTTOMLEFT:
                moveViewVertical(-minDistance, minDistance, minDistance);
                moveViewHorizontal(minDistance, -minDistance, minDistance);
                break;
            case BOTTOMLEFT_TO_TOPRIGHT:
                moveViewVertical(minDistance, -minDistance, minDistance);
                moveViewHorizontal(-minDistance, minDistance, minDistance);
                break;
            default:
                if (mImageRatio < 1) {
                    mStartAutoRect.moveParentTop();
                    mEndAutoRect.moveParentBottom();
                } else {
                    mStartAutoRect.moveParentLeft();
                    mEndAutoRect.moveParentRight();
                }
                break;
        }
    }

    /**
     * 在垂直方向上移动视图
     *
     * @param startMoveDistance 起始视图移动距离
     * @param endMoveDistance   结束视图移动距离
     * @param minDistance       最小距离
     */
    private void moveViewVertical(float startMoveDistance, float endMoveDistance, float minDistance) {
        while (mStartAutoRect.tryMoveView(0, startMoveDistance) && mEndAutoRect.tryMoveView(0, endMoveDistance)
                && isNotVerticalOutDistance(minDistance)) {
            if ((!mStartAutoRect.moveView(0, startMoveDistance)) || (!mEndAutoRect.moveView(0, endMoveDistance))) {
                break;
            }
        }
    }

    /**
     * 在水平方向上移动视图
     *
     * @param startMoveDistance 起始视图移动距离
     * @param endMoveDistance   结束视图移动距离
     * @param minDistance       最小距离
     */
    private void moveViewHorizontal(float startMoveDistance, float endMoveDistance, float minDistance) {
        while (mStartAutoRect.tryMoveView(startMoveDistance, 0) && mEndAutoRect.tryMoveView(endMoveDistance, 0)
                && isNotHorizontalOutDistance(minDistance)) {
            if ((!mStartAutoRect.moveView(startMoveDistance, 0)) || (!mEndAutoRect.moveView(endMoveDistance, 0))) {
                break;
            }
        }
    }

    public boolean isScale() {
        if ((mTransformType == ENLARGE) || (mTransformType == REDUCE)) {
            return true;
        }
        return false;
    }

    private boolean isNotVerticalOutDistance(float distance) {
        if ((Math.abs(mStartAutoRect.getPointY() - mEndAutoRect.getPointY()) >= Math.abs(distance))) {
            return false;
        }
        return true;
    }

    private boolean isNotHorizontalOutDistance(float distance) {
        if ((Math.abs(mStartAutoRect.getPointX() - mEndAutoRect.getPointX()) >= Math.abs(distance))) {
            return false;
        }
        return true;
    }

    public RectF getStartROI() {
        return mStartAutoRect.getVidioROI();
    }

    public RectF getEndROI() {
        return mEndAutoRect.getVidioROI();
    }

    /**
     * Builder类用于构建PhotoAutoRect对象
     */
    public static class Builder {
        private int mWidth;
        private int mHeight;
        private float mCurMakeRatio;
        private int mTransformType;
        private RectF mEndORI;
        private boolean mAutoMove;
        private float mDuration;

        public Builder setDimensions(int width, int height) {
            this.mWidth = width;
            this.mHeight = height;
            return this;
        }

        public Builder setCurMakeRatio(float curMakeRatio) {
            this.mCurMakeRatio = curMakeRatio;
            return this;
        }

        public Builder setTransformType(int transformType) {
            this.mTransformType = transformType;
            return this;
        }

        public Builder setEndORI(RectF endORI) {
            this.mEndORI = endORI;
            return this;
        }

        public Builder setAutoMove(boolean autoMove) {
            this.mAutoMove = autoMove;
            return this;
        }

        public Builder setDuration(float duration) {
            this.mDuration = duration;
            return this;
        }

        public PhotoAutoRect build() throws Exception {
            return new PhotoAutoRect(this);
        }
    }
}