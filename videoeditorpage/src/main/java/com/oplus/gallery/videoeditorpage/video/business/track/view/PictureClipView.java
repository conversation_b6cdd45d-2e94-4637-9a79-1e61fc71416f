/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PictureClipView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.track.view;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.meicam.sdk.NvsIconGenerator;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;

import java.lang.ref.WeakReference;

/**
 * 视频片段视图（资源为图片）
 */
public class PictureClipView extends VideoClipView {
    private static final String TAG = "PictureClipView";

    private int mContentLeftOrigin;
    private WeakReference<ClipViewWrapper> mWeakClipViewWrapper;
    /** 请求缩图的任务<position, taskId>, 为null表示没有任务在请求 */
    private Pair<Long, Long> mTask;

    @Override
    public void draw(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper) {
        if (getLastThumbnail() == null) {
            return;
        }
        super.draw(canvas, clipViewWrapper);
    }

    @Override
    public void refreshUi(ClipViewWrapper view, String path, long trimInAdjusted, long trimOutAdjusted) {
        if (TextUtils.isEmpty(path)) {
            return;
        }

        setTrimOutAdjusted(trimOutAdjusted);
        if ((path.equals(getFilePath())) && (trimInAdjusted == getTrimInAdjusted()) && (getLastThumbnail() != null)) {
            view.postInvalidateClipView();
            return;
        }
        setFilePath(path);
        setTrimInAdjusted(trimInAdjusted);

        if (getNvsIconGenerator() == null) {
            mWeakClipViewWrapper = new WeakReference<>(view);
            setNvsIconGenerator(new NvsIconGenerator());
            getNvsIconGenerator().setIconCallback((bitmap, position, taskId) -> {
                GLog.d(TAG, LogFlag.DL, "[refreshUi] onIconReady: position = " + position + ", taskId = " + taskId + ", bitmap = " + bitmap);
                if (bitmap != null) {
                    setLastThumbnail(bitmap);
                    if (mWeakClipViewWrapper != null) {
                        ClipViewWrapper clipViewWrapper = mWeakClipViewWrapper.get();
                        if (clipViewWrapper != null) {
                            clipViewWrapper.postInvalidateClipView();
                        }
                    }
                }
            });
        }

        long position = AppConstants.Number.NUMBER_0;
        int flag = AppConstants.Number.NUMBER_0;
        if ((view != null) && (view.getRelativeData() != null)
            && (view.getRelativeData().getClip() != null)
            && (view.getRelativeData().getClip() instanceof IVideoClip)
        ) {
            IVideoClip videoClip = (IVideoClip) view.getRelativeData().getClip();
            // 实况图不同静态图片，每帧内容不一样，这里取裁剪后的第一帧渲染，不取一成不变的文件第一帧
            if (videoClip.getIsOlivePhoto()) {
                long stepDuration = getOriginalDurationByLength(ICON_SIZE);
                position = getTrimInAdjusted() / stepDuration * ICON_BASE_DURATION;
                flag = NvsIconGenerator.GET_ICON_FLAGS_KEY_FRAME;
            }
        }
        Bitmap bitmap = getNvsIconGenerator().getIconFromCache(path, position, flag);
        if ((bitmap != null) && (view != null)) {
            setLastThumbnail(bitmap);
            view.postInvalidateClipView();
            return;
        }
        if ((mTask == null) || (mTask.first != position)) {
            if ((mTask != null)) {
                getNvsIconGenerator().cancelTask(mTask.second);
            }
            long taskId = getNvsIconGenerator().getIcon(path, position, flag);
            mTask = new Pair<>(position, taskId);
            GLog.d(TAG, LogFlag.DL, "[refreshUi] getIcon: position = " + position + ", taskId = " + taskId);
        }
    }


    @Override
    public void clearDrawData() {
        if (getLastThumbnail() != null) {
            setLastThumbnail(null);
        }
        super.clearDrawData();
    }

    /**
     * 绘制图片
     * @param canvas 画布
     * @param clipViewWrapper 片段视图
     */
    @Override
    protected void drawBitmap(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper) {
        int contentLeft = clipViewWrapper.getContentMarginLeft();
        int delta = AppConstants.Number.NUMBER_0;
        if ((mContentLeftOrigin != contentLeft) && (mContentLeftOrigin != AppConstants.Number.NUMBER_0)) {
            delta = Math.abs(contentLeft - mContentLeftOrigin) % ICON_SIZE;
        }
        int num = canvas.getWidth() / ICON_SIZE + AppConstants.Number.NUMBER_2;
        float rateW = AppConstants.Number.NUMBER_1f * ICON_SIZE / getLastThumbnail().getWidth();
        float rateH = AppConstants.Number.NUMBER_1f * ICON_SIZE / getLastThumbnail().getHeight();
        float rate = Math.max(rateH, rateW);
        int top = (rateW > rateH)
            ? (int) ((getLastThumbnail().getHeight() * rateW - ICON_SIZE) / AppConstants.Number.NUMBER_2)
            : AppConstants.Number.NUMBER_0;
        for (int i = AppConstants.Number.NUMBER_0; i < num; i++) {
            getMatrix().setScale(rate, rate);
            if (contentLeft - mContentLeftOrigin < AppConstants.Number.NUMBER_0) {
                getMatrix().postTranslate((i - AppConstants.Number.NUMBER_1) * ICON_SIZE + delta, - top);
            } else {
                getMatrix().postTranslate(i * ICON_SIZE - delta, - top);
            }
            canvas.drawBitmap(getLastThumbnail(), getMatrix(), getPaint());
        }
        if (mContentLeftOrigin == AppConstants.Number.NUMBER_0) {
            mContentLeftOrigin = contentLeft;
        }
    }
}
