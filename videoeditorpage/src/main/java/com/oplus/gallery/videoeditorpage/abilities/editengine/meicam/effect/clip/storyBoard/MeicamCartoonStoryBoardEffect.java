/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamCartoonStoryBoardEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard;

import android.text.TextUtils;

import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;

import java.util.Map;

public class MeicamCartoonStoryBoardEffect extends BaseStoryBoardVideoClipEffect {
    public static final String JSON_TYPE_NAME = "cartoon";
    private final static String TAG = "MeicamCartoonStoryBoardEffect";
    private final static float FLOAT_ONE_VALUE = 1.0F;
    private final static float TIME_SECOND = 1000000F;
    private final static int ONE_SECOND = 1000;

    public MeicamCartoonStoryBoardEffect(String dirPath, int playType, String xmlFilePath, float timelineRatio, long duration, Map<String, Object> extendsData) {
        super(dirPath, playType, xmlFilePath, timelineRatio, duration, extendsData);
        mStrength = FLOAT_ONE_VALUE;
        mClassType = JSON_TYPE_NAME;
    }

    @Override
    public BaseVideoClipEffect buildEffectParam(String sourceDir, String sourcePath, float strength, int width, int height,
                                                int imageView, int imageHeight, long clipDuration, int clipRotation, int type, boolean hasKeyframe) {
        return null;
    }

    @Override
    public String getBlurDescription(String keyframeDescription, int width, int height, int imageWidth, int imageHeight, int clipRotation) {
        return null;
    }

    @Override
    public boolean setDuration(long duration, long clipDuration) {
        float strength = duration * FLOAT_ONE_VALUE / TIME_SECOND;
        float scale = (Math.round(strength * ONE_SECOND) * FLOAT_ONE_VALUE / ONE_SECOND);
        if (TextUtils.isEmpty(mXmlFilePath)) {
            return false;
        }
        boolean result = buildEffectParam(this, mXmlFilePath, mDirPath, scale, clipDuration, mExtendsData);
        if (result) {
            String filterFilePath = StoryBoardUtils.getFilterRatioFilePath(mDirPath, mTimelineRatio);
            if (!TextUtils.isEmpty(filterFilePath)) {
                MeicamCartoonStoryBoardEffect subEffect = new MeicamCartoonStoryBoardEffect(mDirPath, mPlayType, filterFilePath, mTimelineRatio, clipDuration, null);
                boolean success = buildEffectParam(subEffect, filterFilePath, mDirPath + "filter/", scale, clipDuration, null);
                if (success) {
                    subEffect.setIsMainEffect(false);
                    this.setAttachment(BaseVideoClipEffect.AttachmentKeys.ATTACHMENT_KEY_SUB_EFFECT, subEffect);
                }
                super.setDuration(duration, clipDuration);
                return success;
            }
        }
        super.setDuration(duration, clipDuration);
        return result;
    }

    private boolean buildEffectParam(MeicamCartoonStoryBoardEffect effect, String xmlFilePath, String dirPath, float strength, long clipDuration,
                                     Map<String, Object> extendsData) {
        boolean isStoryboard3d = StoryBoardUtils.isStoryboard3d(xmlFilePath);
        String storyBoardData = StoryBoardUtils.getStoryboardData(isStoryboard3d, xmlFilePath,
                (long) (clipDuration * FLOAT_ONE_VALUE / ONE_SECOND), mPlayType, strength);


        if (TextUtils.isEmpty(storyBoardData)) {
            return false;
        }
        if (extendsData != null) {
            String backgroundData = (String) extendsData.get(BaseVideoClipEffect.AttachmentKeys.EXTENDS_DATA_KEY_BACKGROUND);
            String composeStory = null;
            if (!TextUtils.isEmpty(backgroundData)) {
                composeStory = StoryBoardUtils.composeStory(backgroundData, storyBoardData);
            }
            if (!TextUtils.isEmpty(composeStory)) {
                storyBoardData = composeStory;
            }
        }

        effect.setEffectType(StoryboardType.TYPE_CARTOON);
        effect.setEffectPlayType(mPlayType);
        if (isStoryboard3d) {
            effect.setName(StreamingConstant.StoryBoard.STORYBOARD_3D_NAME);
            effect.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION_FILE, storyBoardData);
        } else {
            effect.setName(StreamingConstant.StoryBoard.STORYBOARD_NAME);
            effect.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION, storyBoardData);
            effect.setBooleanVal(StreamingConstant.StoryBoard.STORYBOARD_NO_BACKGROUND, true);
            effect.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_RES_DIR, dirPath);
        }
        return true;
    }

    @Override
    public void setStringValue(String paramName, String value) {
        if (mStringParams.containsKey(paramName)) {
            mStringParams.remove(paramName);
        }
        mStringParams.put(paramName, value);
    }

    @Override
    public void setBooleanVal(String paramName, boolean value) {
        if (mBooleanParams.containsKey(paramName)) {
            mBooleanParams.remove(paramName);
        }
        mBooleanParams.put(paramName, value);
    }

    @Override
    public void setFloatValue(String paramName, float value) {
        if (mFloatParams.containsKey(paramName)) {
            mFloatParams.remove(paramName);
        }
        mFloatParams.put(paramName, value);
    }
}
