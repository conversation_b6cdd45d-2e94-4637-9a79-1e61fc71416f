/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FontsAdapter.kt
 ** Description : 文字字体面板适配器
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.FontViewHolder
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.FontViewData

/**
 * 字幕字体适配器
 */
class FontsAdapter(recyclerView: RecyclerView) : BaseAdapter<FontViewData, FontViewHolder>(
    recyclerView,
    /**
     * 创建VH的回调接口
     */
    { parent: ViewGroup, viewType: Int -> FontViewHolder(parent) }
) {

    /**
     * 配置单选控件
     */
    override var multipleSelectable = false

    init {
        // 设置item数据差异计算回调
        diffCallback = FontDiffCallback()
    }

    /**
     * 显示初始默认字体
     */
    fun setSelectedPositionForFontId(fontId: String) {
        data.forEachIndexed { index, fontItem ->
            if (fontId == fontItem.resourceId) {
                // 根据字幕选择的字体id设置选中位置
                selectedPosition = index
                return@forEachIndexed
            }
        }
        // 如果没有设置字体，则默认选中第一个
        if (selectedPosition == -1) selectedPosition = DEFAULT_SELECTED_FONT_POSITION
    }

    /**
     * 设置选中字体，用于选中样式后更新样式绑定的字体在字体页的下载选中状态
     *
     * @param fontData 字体数据
     */
    fun setSelectedForFont(fontData: FontViewData?) {
        // 重置选中位置
        selectedPosition = -1
        fontData?.let {
            data.forEachIndexed { index, fontItem ->
                if (it.resourceId == fontItem.resourceId) {
                    // 根据字幕选择的字体id设置选中位置
                    selectedPosition = index
                    fontItem.downloadState = it.downloadState
                    fontItem.progress = it.progress
                    fontItem.localPath = it.localPath
                    return@forEachIndexed
                }
            }
        }
        // 如果没有设置字体，则默认选中第一个
        if (selectedPosition == -1) selectedPosition = DEFAULT_SELECTED_FONT_POSITION
    }

    /**
     * 文字样式数据变化差异回调器
     */
    private class FontDiffCallback : DiffCallback<FontViewData>() {

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return ((oldItem.iconUrl == newItem.iconUrl)
                    && (oldItem.localPath == newItem.localPath))
        }
    }

    companion object {
        /**
         * 默认选中字体的位置
         */
        const val DEFAULT_SELECTED_FONT_POSITION = 0
    }
}