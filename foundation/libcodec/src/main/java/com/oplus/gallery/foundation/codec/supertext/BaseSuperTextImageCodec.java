/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseSuperTextImageCodec
 ** Description: BaseSuperTextImageCodec
 ** Version: 1.0
 ** Date : 2022/5/17
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/05/17    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.supertext;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainerExt;
import com.oplus.gallery.foundation.security.CrcUtil;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.io.BufferedOutputStream;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Arrays;

abstract class BaseSuperTextImageCodec implements ISuperTextImageCodec {
    /**
     * 超级文本的幻数
     * 1.0 : byteArrayOf('e'.toByte(), 'n'.toByte(), 't'.toByte(), 'x'.toByte()
     * 2.0 : byteArrayOf('t'.toByte(), 'e'.toByte(), 'x'.toByte(), 't'.toByte()
     */
    private byte[] mMagicNumber = null;

    private static final String TAG = "BaseSuperTextImageCodec";

    /**
     * 打开文件的模式  r = 以读的模式打开
     */
    private static final String READ_MODEL = "r";

    /**
     * 文件路径
     */
    private final String mFilePath;

    protected BaseSuperTextImageCodec(String filePath, byte[] magicNumber) {
        this.mFilePath = filePath;
        this.mMagicNumber = magicNumber;
    }

    /**
     * 超级文本的幻数标记,规则：长度为4位，让子类去实现
     * 1.0 : byteArrayOf('e'.toByte(), 'n'.toByte(), 't'.toByte(), 'x'.toByte()
     * 2.0 : byteArrayOf('t'.toByte(), 'e'.toByte(), 'x'.toByte(), 't'.toByte()
     *
     * @return byte[]
     */
    protected abstract byte[] getMagicNumberFlag();

    /**
     * tag
     *
     * @return string
     */
    protected abstract String getTag();

    /**
     * 该文本图片是否被扩展过
     * 1.0相机会自动写效果图，超级文本标志位等，自动会被扩展
     * 非1.0的图片在2.0经过超级文本的编辑后上也要保持和1.0一样的格式
     * 使用场景：这种格式的图片不支持瘦身
     */
    @Override
    public boolean isExtendedFile() {
        return CrcUtil.byteEquals(mMagicNumber, getMagicNumberFlag());
    }

    @Override
    public boolean isSuperText() {
        // fixme 待实现
        return false;
    }

    /**
     * 解碼超級文本圖片信息
     *
     * @param options SuperTextImage.Options
     * @return bitmap
     */
    @Nullable
    @Override
    public Bitmap decodeFile(@Nullable SuperTextOptions options) {
        return decodeFileImpl(mFilePath, options);
    }

    /**
     * 编码图片
     *
     * @param fd      FileDescriptor
     * @param options SuperTextImage.Options
     */
    @Override
    public boolean encodeFile(@Nullable FileDescriptor fd, @Nullable SuperTextOptions options) {
        return encodeFileImpl(mFilePath, fd, options);
    }

    /**
     * 编码超级文本信息
     *
     * @param filePath 文件路径
     * @param fd       FileDescriptor
     * @param options  SuperTextOptions
     */
    private boolean encodeFileImpl(String filePath, FileDescriptor fd, SuperTextOptions options) {
        if (TextUtils.isEmpty(filePath)) {
            GLog.e(getTag(), "encodeFileImpl filePath is null so return false");
            return false;
        }
        File file = new File(filePath);
        BufferedOutputStream stream = null;
        try {
            byte[] data = encodeToByte(options, fd, file);
            if (data == null) {
                GLog.e(getTag(), "encodeFileImpl data is null, return false");
                return false;
            }
            if (file.exists()) {
                file.delete();
            }
            stream = new BufferedOutputStream(new FileOutputStream(file.getFile()));
            stream.write(data);
            stream.flush();
        } catch (IOException exception) {
            GLog.e(getTag(), "encodeFileImpl: ", exception);
            return false;
        } finally {
            IOUtils.closeQuietly(stream);
        }
        return true;
    }

    private Bitmap decodeFileImpl(String filePath, SuperTextOptions options) {
        if (TextUtils.isEmpty(filePath)) {
            GLog.e(getTag(), "decodeFileImpl bitmap is null");
            return null;
        }
        if (options == null) {
            return decodeFileImpl(new SuperTextOptions(), filePath);
        } else {
            return decodeFileImpl(options, filePath);
        }
    }

    private Bitmap decodeFileImpl(SuperTextOptions options, String filePath) {
        // Decode super text details.
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(filePath);
            if (options == null) {
                return BitmapFactory.decodeStream(fileInputStream);
            } else {
                /*
                 * 超级文本模式下拍的图片，可能同时写入扩展数据（水印可编辑后基本都会写入扩展数据）
                 * 这种情况下，为保证正常读取超级文本，文件长度需要使用原始文件长度，不能使用文件的总长度
                 */
                int originDataLength = FileExtendedContainerExt.getOriginDataLength(
                        ContextGetter.context,
                        filePath,
                        -1
                );
                options.setOriginDataLength(originDataLength);
                decodeSuperTextRandomAccessFile(filePath, options);
                if (options.getInJustDecodeType()
                        || options.getInJustDecodeEditData()
                        || (options.getProperties().getEffectBitmap() != null)) {
                    return null;
                } else {
                    Bitmap effectBitmap = BitmapFactory.decodeStream(fileInputStream, null, options);
                    options.getProperties().setEffectBitmap(effectBitmap);
                    return effectBitmap;
                }
            }
        } catch (Exception e) {
            GLog.e(getTag(), "decodeFileImpl", e);
        } finally {
            IOUtils.closeQuietly(fileInputStream);
        }
        return null;
    }

    private void decodeSuperTextRandomAccessFile(String filePath, SuperTextOptions options) {
        RandomAccessFile randomAccessFile = null;
        try {
            randomAccessFile = new RandomAccessFile(filePath, READ_MODEL);
            decodeSuperTextImage(randomAccessFile, options);
        } catch (Exception e) {
            GLog.e(getTag(), "decodeSuperTextImageRandomAccessFile", e);
        } finally {
            IOUtils.closeQuietly(randomAccessFile);
        }
    }

    private void decodeSuperTextImage(RandomAccessFile randomAccessFile, SuperTextOptions options) {
        try {
            int originDataLength = options.getOriginDataLength();
            boolean hasMagicNumber = CrcUtil.byteEquals(
                    SuperTextImageUtils.getMagicNumber(randomAccessFile, originDataLength),
                    getMagicNumberFlag());
            SuperTextProperties properties = options.getProperties();
            properties.setSuperText(hasMagicNumber);
            if (!hasMagicNumber) {
                GLog.d(getTag(), "decodeSuperTextImage hasMagicNumber is false so return ");
                return;
            }
            if (options.getInJustDecodeType()) {
                GLog.d(getTag(), "decodeSuperTextImage inJustDecodeType is true so return ");
                return;
            }
            properties.setSotOffset(getSotOffset(randomAccessFile, originDataLength));
            properties.setDataBlockInfos(readSuperTextDataBlockInfo(randomAccessFile, originDataLength));
            randomAccessFile.seek(0);

            int sotOffsetPos = originDataLength - SuperTextProperties.SOT_OFFSET_BYTE_LENGTH;
            randomAccessFile.seek(sotOffsetPos);
            int sotForwardOffset = originDataLength - randomAccessFile.readInt();
            if (properties.getDataBlockInfos() != null) {
                for (SuperTextProperties.Companion.DataBlockInfo blockInfo : properties.getDataBlockInfos()) {
                    final byte[] tag = blockInfo.getTag();
                    final int offset = blockInfo.getOffset();
                    final int length = blockInfo.getLength();
                    final int version = blockInfo.getVersion();
                    if (CrcUtil.byteEquals(tag, SuperTextProperties.Companion.getTAG_CORRECT_VERTEX())) {
                        float[] vertex = decodeCorrectVertex(randomAccessFile, offset + sotForwardOffset, length, version);
                        if (vertex != null) {
                            properties.setCorrectVertex(vertex);
                        }
                    } else if (CrcUtil.byteEquals(tag, SuperTextProperties.Companion.getTAG_EFFECT_TYPE())) {
                        properties.setEffectType(
                                decodeEffectType(randomAccessFile,
                                        offset + sotForwardOffset, length, version)
                        );
                    } else if (CrcUtil.byteEquals(tag, SuperTextProperties.Companion.getTAG_TEXT_ORIGIN_IMAGE())
                            && !options.getInJustDecodeEditData()) {
                        properties.setOriginBitmap(
                                decodeOriginImage(randomAccessFile,
                                        offset + sotForwardOffset, length, version)
                        );
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(getTag(), "decodeSuperTextImage RandomAccessFile", e);
        }
    }

    /**
     * 解码校正信息
     *
     * @param randomAccessFile RandomAccessFile
     * @param offset           offset 偏移量
     * @param length           长度
     * @param version          版本号
     * @return float[] 校正信息
     */
    private float[] decodeCorrectVertex(RandomAccessFile randomAccessFile, int offset, int length, int version) {
        if ((offset < 0) || (length <= 0) || (randomAccessFile == null)) {
            GLog.e(getTag(), "decodeCorrectVertex, offset = " + offset
                    + ", length = " + length + ", version = " + version);
            return null;
        }
        CorrectVertexCodec codec = createCorrectVertexCodec(version);
        return codec.decode(randomAccessFile, offset, length);
    }

    /**
     * 解码增强信息
     *
     * @param randomAccessFile RandomAccessFile
     * @param offset           offset 偏移量
     * @param length           长度
     * @param version          版本号
     * @return int 增量的类型
     */
    private int decodeEffectType(RandomAccessFile randomAccessFile, int offset, int length, int version) {
        if ((offset < 0) || (length <= 0) || (randomAccessFile == null)) {
            GLog.e(getTag(), "decodeEffectType, offset = " + offset
                    + ", length = " + length + ", version = " + version);
            return 0;
        }
        EffectTypeCodec codec = createEffectTypeCodec(version);
        return codec.decode(randomAccessFile, offset, length);
    }

    /**
     * 解码原图
     *
     * @param randomAccessFile RandomAccessFile
     * @param offset           偏移量
     * @param length           length
     * @param version          version
     * @return Bitmap
     */
    private Bitmap decodeOriginImage(RandomAccessFile randomAccessFile, int offset, int length, int version) {
        if ((offset < 0) || (length <= 0) || (randomAccessFile == null)) {
            GLog.e(getTag(), "decodeOriginImage, offset = " + offset
                    + ", length = " + length + ", version = " + version);
            return null;
        }
        OriginImageCodec codec = createOriginImageCodec(version);
        return codec.decode(randomAccessFile, offset, length);
    }

    private byte[] encodeToByte(SuperTextOptions options, FileDescriptor fd, File file) throws IOException {
        if ((options == null)
                || (options.getProperties().getEffectBitmap() == null)
                || (options.getProperties().getOriginBitmap() == null)
        ) {
            GLog.e(getTag(), "encodeToByte: null param");
            return null;
        }
        SuperTextProperties properties = options.getProperties();
        byte[] superTextImageData = BitmapUtils.compressToBytes(properties.getEffectBitmap(), options.getEncodeFormat());
        int totalContentLength = properties.getSotOffset() + superTextImageData.length;
        int sotForwardOffset = superTextImageData.length;
        ByteBuffer byteBuffer = ByteBuffer.allocate(totalContentLength);
        byteBuffer.rewind();
        byteBuffer.put(superTextImageData);

        // add data block info
        if (!encodeSuperTextDataBlockInfos(byteBuffer, properties.getDataBlockInfos(), sotForwardOffset)) {
            GLog.e(getTag(), "encodeToByte: encodeSuperTextDataBlockInfos failed");
            return null;
        }
        // add edit data and original bitmap
        if (!encodeSuperTextEditData(byteBuffer, options, sotForwardOffset, fd, file)) {
            GLog.e(getTag(), "encodeToByte: encodeSuperTextEditData failed");
            return null;
        }
        // add magic number
        if (!encodeSuperTextMagicNumber(byteBuffer, totalContentLength, properties)) {
            GLog.e(getTag(), "encodeToByte: encodeSuperTextMagicNumber failed");
            return null;
        }
        return byteBuffer.array();
    }

    private boolean encodeSuperTextDataBlockInfos(
            ByteBuffer buffer,
            SuperTextProperties.Companion.DataBlockInfo[] blockInfos,
            int offset) {
        if ((blockInfos == null) || (blockInfos.length == 0)) {
            GLog.e(getTag(), "encodeSuperTextDataBlockInfos: blockInfo items is empty");
            return false;
        }
        buffer.rewind();
        buffer.position(offset);
        //add SOT
        buffer.put(SuperTextProperties.Companion.getSOT());
        // add blockInfo count
        buffer.putInt(blockInfos.length);
        // add blockInfo item
        for (SuperTextProperties.Companion.DataBlockInfo blockInfo : blockInfos) {
            buffer.put(blockInfo.getTag());
            buffer.putInt(blockInfo.getOffset());
            buffer.putInt(blockInfo.getLength());
            buffer.putInt(blockInfo.getVersion());
        }

        // add EOT
        buffer.put(SuperTextProperties.Companion.getEOT());
        return true;
    }

    /**
     * 算法效果图内容（包含校正框，原图，增强类型）
     *
     * @return false/true
     */
    private boolean encodeSuperTextEditData(
            ByteBuffer byteBuffer,
            SuperTextOptions options,
            int sotForwardOffset,
            FileDescriptor fd,
            File file
    ) {
        SuperTextProperties properties = options.getProperties();
        if (properties.getDataBlockInfos() == null) {
            return false;
        }
        for (SuperTextProperties.Companion.DataBlockInfo blockInfo : properties.getDataBlockInfos()) {
            byte[] tag = blockInfo.getTag();
            int offset = blockInfo.getOffset();
            int length = blockInfo.getLength();
            int version = blockInfo.getVersion();
            if (CrcUtil.byteEquals(tag, SuperTextProperties.Companion.getTAG_TEXT_ORIGIN_IMAGE())) {
                byte[] originImageData = null;
                if (options.getProperties().isSuperText()) {
                    originImageData = decodeOriginImageInSuperTextImage(fd, file, length, offset);
                } else {
                    originImageData = decodeOriginImage(fd, file);
                }
                if (originImageData == null) {
                    GLog.e(getTag(), "encodeSuperTextEditData: origin image data is null");
                    return false;
                }
                byteBuffer.rewind();
                byteBuffer.position(sotForwardOffset + offset);
                byteBuffer.put(originImageData);
            } else if (CrcUtil.byteEquals(tag, SuperTextProperties.Companion.getTAG_CORRECT_VERTEX())) {
                // add correct vertex
                encodeCorrectVertex(
                        byteBuffer,
                        sotForwardOffset + offset,
                        length, version,
                        properties.getCorrectVertex()
                );
            } else if (CrcUtil.byteEquals(tag, SuperTextProperties.Companion.getTAG_EFFECT_TYPE())) {
                // add super effect type
                encodeSuperTextEffectType(
                        byteBuffer,
                        sotForwardOffset + offset,
                        length,
                        version,
                        properties.getEffectType()
                );
            }
        }
        return true;
    }

    /**
     * encode 幻数，幻数用于格式校验
     *
     * @param byteBuffer         buffer
     * @param totalContentLength 总长度
     * @param properties         SuperTextProperties
     */
    private boolean encodeSuperTextMagicNumber(ByteBuffer byteBuffer, int totalContentLength, SuperTextProperties properties) {
        if (properties == null) {
            return false;
        }
        // add magic number
        byteBuffer.rewind();
        byteBuffer.position(totalContentLength
                - SuperTextProperties.SOT_OFFSET_BYTE_LENGTH
                - SuperTextProperties.MAGIC_NUMBER_BYTE_LENGTH);
        for (byte charByte : getMagicNumberFlag()) {
            byteBuffer.put(charByte);
        }

        byteBuffer.putInt(properties.getSotOffset());
        return true;
    }

    private void encodeCorrectVertex(ByteBuffer buffer, int offset, int length, int version, float[] vertex) {
        if ((offset < 0) || (length <= 0) || (buffer == null) || (vertex == null)) {
            GLog.e(getTag(), "encodeCorrectVertex, offset = " + offset
                    + ", length = " + length + ", version = " + version + ", vertex = " + Arrays.toString(vertex));
            return;
        }
        CorrectVertexCodec codec = createCorrectVertexCodec(version);
        codec.encode(buffer, offset, length, vertex);
    }

    private void encodeSuperTextEffectType(ByteBuffer buffer, int offset, int length, int version, int param) {
        if ((offset < 0) || (length <= 0) || (buffer == null)) {
            GLog.e(getTag(), "encodeSuperTextEffectType, offset = " + offset
                    + ", length = " + length + ", version = " + version + ", EffectType = " + param);
            return;
        }
        EffectTypeCodec codec = createEffectTypeCodec(version);
        codec.encode(buffer, offset, length, param);
    }

    private byte[] decodeOriginImage(FileDescriptor fd, File file) {
        if ((fd == null) && (file == null)) {
            GLog.e(getTag(), "decodeOriginImage: param is null");
            return null;
        }

        MappedByteBuffer buffer = null;
        byte[] originImageByteBuffer = null;
        FileInputStream stream = null;
        try {
            if (fd != null) {
                stream = new FileInputStream(fd);
            } else {
                stream = new FileInputStream(file.getFile());
            }
            buffer = IOUtils.mapByteBuffer(stream.getChannel(), stream.available(), FileChannel.MapMode.READ_ONLY);
            if (buffer != null) {
                buffer.order(ByteOrder.BIG_ENDIAN);
                buffer.rewind();
                int contentLength = buffer.remaining();
                originImageByteBuffer = new byte[contentLength];
                buffer.get(originImageByteBuffer);
                GLog.d(getTag(), "decodeOriginImage, contentLength " + contentLength);
            } else {
                GLog.d(getTag(), "decodeOriginImage, mapByteBuffer is null");
            }
        } catch (IOException exception) {
            GLog.e(TAG, "decodeOriginImage, error " + exception);
        } finally {
            IOUtils.closeQuietly(stream);
            IOUtils.unmapByteBuffer(buffer);
        }
        return originImageByteBuffer;
    }

    private byte[] decodeOriginImageInSuperTextImage(FileDescriptor fd, File file, int dataLength, int dataOffset) {
        if ((dataLength <= 0)) {
            GLog.e(getTag(), "decodeOriginImageInSuperTextImage: param is null");
            return null;
        }
        MappedByteBuffer buffer = null;
        byte[] originImageByteBuffer = null;
        FileInputStream stream = null;

        try {
            if (fd != null) {
                stream = new FileInputStream(fd);
            } else {
                stream = new FileInputStream(file.getFile());
            }
            buffer = IOUtils.mapByteBuffer(stream.getChannel(), stream.available(), FileChannel.MapMode.READ_ONLY);
            if (buffer != null) {
                buffer.order(ByteOrder.BIG_ENDIAN);
                buffer.rewind();
                int contentLength = buffer.remaining();
                int sotOffsetPos = contentLength - SuperTextProperties.SOT_OFFSET_BYTE_LENGTH;
                buffer.position(sotOffsetPos);
                int sotForwardOffset = contentLength - buffer.getInt();
                int offset = dataOffset + sotForwardOffset;
                originImageByteBuffer = new byte[dataLength];
                buffer.rewind();
                buffer.position(offset);
                buffer.get(originImageByteBuffer);
            } else {
                GLog.e(getTag(), "decodeOriginImageInSuperTextImage, mapByteBuffer is null");
            }
        } catch (IOException exception) {
            GLog.e(TAG, "decodeOriginImageInSuperTextImage, error:" + exception);
        } finally {
            IOUtils.closeQuietly(stream);
            IOUtils.unmapByteBuffer(buffer);
        }
        return originImageByteBuffer;
    }

    private SuperTextProperties.Companion.DataBlockInfo[] readSuperTextDataBlockInfo(
            RandomAccessFile randomAccessFile,
            int originDataLength
    ) {
        try {
            randomAccessFile.seek(0);
            byte[] magicNumber = new byte[SuperTextProperties.MAGIC_NUMBER_BYTE_LENGTH];
            byte[] sot = new byte[SuperTextProperties.TAG_BYTE_LENGTH];
            byte[] eot = new byte[SuperTextProperties.TAG_BYTE_LENGTH];
            SuperTextProperties.Companion.DataBlockInfo[] blockInfos = null;
            // Magic number & SOT mOffset
            int contentEndPos = originDataLength - SuperTextProperties.SOT_OFFSET_BYTE_LENGTH
                    - SuperTextProperties.MAGIC_NUMBER_BYTE_LENGTH;
            randomAccessFile.seek(contentEndPos);
            int readNumLength = randomAccessFile.read(magicNumber, 0, magicNumber.length);
            if (readNumLength < magicNumber.length) {
                GLog.w(getTag(), "readSuperTextDataBlockInfo,there may be a problem with the number of bytes read magicNumber.");
            }
            int sotOffset = originDataLength - randomAccessFile.readInt();
            GLog.d(getTag(), "readSuperTextDataBlockInfo"
                    + ", magicNumber = " + Integer.toHexString(CrcUtil.bytesToInt(magicNumber))
                    + ", originDataLength = " + originDataLength
                    + ", sotOffset = " + Integer.toHexString(sotOffset));
            // Check super text validity
            if (!CrcUtil.byteEquals(magicNumber, getMagicNumberFlag())
                    && (sotOffset >= 0) && (sotOffset < originDataLength)) {
                return null;
            }
            // Decode SOT table
            randomAccessFile.seek(sotOffset);
            int readSotLength = randomAccessFile.read(sot, 0, sot.length);
            if (readSotLength < sot.length) {
                GLog.w(getTag(), "readSuperTextDataBlockInfo,there may be a problem with the number of bytes read by block Info.");
            }
            GLog.d(getTag(), "readSuperTextDataBlockInfo, sot = " + Integer.toHexString(CrcUtil.bytesToShort(sot)));
            if (!CrcUtil.byteEquals(sot, SuperTextProperties.Companion.getSOT())) {
                return null;
            }
            int blockInfoCount = randomAccessFile.readInt();
            GLog.d(getTag(), "readSuperTextDataBlockInfo, blockInfoCount = " + blockInfoCount);
            blockInfos = getDataBlockInfos(randomAccessFile, contentEndPos, blockInfoCount);
            if (blockInfos == null) {
                GLog.e(getTag(), "readSuperTextDataBlockInfo, blockInfos is null");
                return null;
            }
            int readEotLength = randomAccessFile.read(eot);
            if (readEotLength < SuperTextProperties.TAG_BYTE_LENGTH) {
                GLog.w(getTag(), "readSuperTextDataBlockInfo,there may be a problem with the number of bytes read eot.");
            }
            GLog.d(getTag(), "readSuperTextDataBlockInfo, eot = " + Integer.toHexString(CrcUtil.bytesToShort(eot)));
            if (!CrcUtil.byteEquals(eot, SuperTextProperties.Companion.getEOT())) {
                GLog.e(getTag(), "readSuperTextDataBlockInfo end error.");
                return null;
            }
            return blockInfos;
        } catch (Exception e) {
            GLog.e(getTag(), "readSuperTextDataBlockInfo", e);
        }
        return null;
    }

    @Nullable
    private SuperTextProperties.Companion.DataBlockInfo[] getDataBlockInfos(
            RandomAccessFile randomAccessFile,
            int contentEndPos,
            int blockInfoCount
    ) throws IOException {
        SuperTextProperties.Companion.DataBlockInfo[] blockInfos = null;
        if ((blockInfoCount > 0) && ((blockInfoCount * SuperTextProperties.DATA_BLOCK_INFO_ITEM_BYTE_LENGTH) < contentEndPos)) {
            blockInfos = new SuperTextProperties.Companion.DataBlockInfo[blockInfoCount];
            int allTagLen = blockInfoCount * SuperTextProperties.DATA_BLOCK_INFO_ITEM_BYTE_LENGTH;
            byte[] allTagData = new byte[allTagLen];
            int readDataLength = randomAccessFile.read(allTagData);
            if (readDataLength < allTagLen) {
                GLog.w(getTag(), "getDataBlockInfos,there may be a problem with the number of bytes read by block Info.");
            }
            ByteBuffer tagBuffer = ByteBuffer.wrap(allTagData);
            for (int i = 0; i < blockInfoCount; i++) {
                byte[] tagBytes = new byte[SuperTextProperties.TAG_BYTE_LENGTH];
                tagBuffer.get(tagBytes);
                int offset = tagBuffer.getInt();
                int length = tagBuffer.getInt();
                int version = tagBuffer.getInt();
                SuperTextProperties.Companion.DataBlockInfo blockInfo = new SuperTextProperties.Companion.DataBlockInfo(
                        tagBytes, offset, length, version);
                GLog.d(getTag(), "getDataBlockInfos"
                        + ", index = " + i
                        + ", mTag = " + Arrays.toString(blockInfo.getTag())
                        + ", mOffset = " + blockInfo.getOffset()
                        + ", mLength = " + blockInfo.getLength());

                blockInfos[i] = blockInfo;
            }
        }
        return blockInfos;
    }

    private int getSotOffset(RandomAccessFile randomAccessFile, int originDataLength) {
        try {
            randomAccessFile.seek(0);
            int sotOffsetPos = originDataLength - SuperTextProperties.SOT_OFFSET_BYTE_LENGTH;
            randomAccessFile.seek(sotOffsetPos);
            return randomAccessFile.readInt();
        } catch (Exception e) {
            GLog.e(getTag(), "getSotOffset RandomAccessFile", e);
        }
        return 0;
    }

    private static CorrectVertexCodec createCorrectVertexCodec(int version) {
        // 目前没有额外的解码器版本区分, version = 0,1都是一个解码器
        return new CorrectVertexCodec();
    }

    private static EffectTypeCodec createEffectTypeCodec(int version) {
        // 目前没有额外的解码器版本区分, version = 0,1都是一个解码器
        return new EffectTypeCodec();
    }

    private static OriginImageCodec createOriginImageCodec(int version) {
        // 目前没有额外的解码器版本区分, version = 0,1都是一个解码器
        return new OriginImageCodec();
    }

    /**
     * 校正信息xCodec
     */
    private static class CorrectVertexCodec implements IVersionedCodec<float[]> {

        private final static int VERSION = 1;

        @Override
        public float[] decode(ByteBuffer buffer, int offset, int length) {
            GLog.d(TAG, "CorrectVertexCodec, decode offset = "
                    + Integer.toHexString(offset) + ", length = " + length);
            byte[] correctVertexByteBuffer = new byte[length];
            buffer.rewind();
            buffer.position(offset);
            buffer.get(correctVertexByteBuffer);
            int vertexSize = length / (Float.SIZE / Byte.SIZE);
            float[] correctVertex = new float[vertexSize];
            int byteOffset = 0;
            for (int i = 0; i < vertexSize; i++) {
                correctVertex[i] = CrcUtil.bytesToFloat(correctVertexByteBuffer, byteOffset);
                byteOffset += Float.SIZE / Byte.SIZE;
            }
            return correctVertex;
        }

        @Override
        public ByteBuffer encode(ByteBuffer buffer, int offset, int length, float[] vertexArray) {
            buffer.rewind();
            buffer.position(offset);
            for (float vertex : vertexArray) {
                buffer.putFloat(vertex);
            }
            return buffer;
        }

        @Override
        public float[] decode(RandomAccessFile randomAccessFile, int offset, int length) {
            try {
                GLog.d(TAG, "CorrectVertexCodec decode RandomAccessFile, offset = "
                        + Integer.toHexString(offset) + ", length = " + length);
                byte[] correctVertexByteBuffer = new byte[length];
                randomAccessFile.seek(offset);
                int readLength = randomAccessFile.read(correctVertexByteBuffer);
                if (readLength < length) {
                    GLog.w(TAG, "decode,there may be a problem with the number of bytes read correctVertexByteBuffer.");
                }
                int vertexSize = length / (Float.SIZE / Byte.SIZE);
                float[] correctVertex = new float[vertexSize];
                int byteOffset = 0;
                for (int i = 0; i < vertexSize; i++) {
                    correctVertex[i] = CrcUtil.bytesToFloat(correctVertexByteBuffer, byteOffset);
                    byteOffset += Float.SIZE / Byte.SIZE;
                }
                return correctVertex;
            } catch (Exception e) {
                GLog.e(TAG, "decode(RandomAccessFile buffer, int offset, int length)", e);
            }
            return new float[0];
        }
    }

    /**
     * 增强类型信息xCodec
     */
    private static class EffectTypeCodec implements IVersionedCodec<Integer> {

        private final static int VERSION = 1;

        @Override
        public Integer decode(ByteBuffer buffer, int offset, int length) {
            GLog.d(TAG, "EffectTypeCodec, decode offset = "
                    + Integer.toHexString(offset) + ", length = " + length);
            byte[] effectTypeByteBuffer = new byte[length];
            buffer.rewind();
            buffer.position(offset);
            buffer.get(effectTypeByteBuffer);
            return CrcUtil.bytesToInt(effectTypeByteBuffer);
        }

        @Override
        public ByteBuffer encode(ByteBuffer buffer, int offset, int length, Integer object) {
            buffer.rewind();
            buffer.position(offset);
            buffer.putInt(object);
            return buffer;
        }

        @Override
        public Integer decode(RandomAccessFile randomAccessFile, int offset, int length) {
            try {
                GLog.d(TAG, "EffectTypeCodec decode , offset = "
                        + Integer.toHexString(offset) + ", length = " + length);
                byte[] effectTypeByteBuffer = new byte[length];
                randomAccessFile.seek(offset);
                int readLength = randomAccessFile.read(effectTypeByteBuffer);
                if (readLength < length) {
                    GLog.w(TAG, "decode,there may be a problem with the number of bytes read effectTypeByteBuffer.");
                }
                return CrcUtil.bytesToInt(effectTypeByteBuffer);
            } catch (Exception e) {
                GLog.e(TAG, "decode RandomAccessFile ", e);
            }
            return 0;
        }
    }

    /**
     * 原图类型信息xCodec
     */
    private static class OriginImageCodec implements IVersionedCodec<Bitmap> {

        private final static int VERSION = 1;

        @Override
        public Bitmap decode(ByteBuffer buffer, int offset, int length) {
            GLog.d(TAG, "originImageCodec, offset = "
                    + Integer.toHexString(offset) + ", length = " + length);
            byte[] originImageByteBuffer = new byte[length];
            buffer.rewind();
            buffer.position(offset);
            buffer.get(originImageByteBuffer);
            return BitmapFactory.decodeByteArray(originImageByteBuffer, 0, length);
        }

        @Override
        public ByteBuffer encode(ByteBuffer buffer, int offset, int length, Bitmap object) {
            // Don't encode the origin jpeg, just write back the raw data.
            return null;
        }

        @Override
        public Bitmap decode(RandomAccessFile randomAccessFile, int offset, int length) {
            try {
                GLog.d(TAG, "originImageCodec decode , offset = " + Integer.toHexString(offset) + ", length = " + length);
                byte[] originImageByteBuffer = new byte[length];
                randomAccessFile.seek(offset);
                int readLength = randomAccessFile.read(originImageByteBuffer);
                if (readLength < length) {
                    GLog.w(TAG, "decode,there may be a problem with the number of bytes read originImageByteBuffer.");
                }
                return BitmapFactory.decodeByteArray(originImageByteBuffer, 0, length);
            } catch (Exception e) {
                GLog.e(TAG, "decode", e);
            }
            return null;
        }
    }
}
