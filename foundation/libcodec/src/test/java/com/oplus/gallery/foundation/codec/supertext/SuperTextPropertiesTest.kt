/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SuperTextPropertiesTest.kt
 * Description:
 * Version:
 * Date: 2022/4/19
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/4/19     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.foundation.codec.supertext

import io.mockk.MockKAnnotations
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SuperTextPropertiesTest {

    private val properties = SuperTextProperties()

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun should_return_when_buildDataBlockInfo_with_superText1_0_file() {
        // given

        // when
        properties.isSuperText = true
        properties.buildDataBlockInfo(0)

        // then
        Assert.assertNull(properties.dataBlockInfos)
        Assert.assertEquals(properties.sotOffset, 0)
    }

    @Test
    fun should_return_when_buildDataBlockInfo_with_exist_dataBlockInfos() {
        // given

        // when
        properties.isSuperText = false
        properties.dataBlockInfos = arrayOf(
            SuperTextProperties.Companion.DataBlockInfo(
                tag = SuperTextProperties.TAG_CORRECT_VERTEX,
                offset = SuperTextProperties.VERTEX_DATA_BLOCK_OFFSET,
                length = SuperTextProperties.VERTEX_DATA_BLOCK_LENGTH,
                version = SuperTextProperties.DATA_BLOCK_VERSION
            )
        )
        properties.buildDataBlockInfo(0)

        // then
        Assert.assertEquals(properties.sotOffset, 0)
    }

    @Test
    fun should_build_infos_success_when_buildDataBlockInfo_with_normal_image() {
        // given
        val fileLength = 99

        // when
        properties.isSuperText = false
        properties.buildDataBlockInfo(fileLength)
        val dataBlockInfos = properties.dataBlockInfos ?: return
        val vertexInfo = dataBlockInfos[0]
        val effectTypeInfo = dataBlockInfos[1]
        val originBitmapInfo = dataBlockInfos[2]

        // then
        Assert.assertEquals(vertexInfo.length, 32)
        Assert.assertEquals(vertexInfo.offset, 50)
        Assert.assertEquals(vertexInfo.version, 1)

        Assert.assertEquals(effectTypeInfo.length, 4)
        Assert.assertEquals(effectTypeInfo.offset, 82)
        Assert.assertEquals(effectTypeInfo.version, 1)

        Assert.assertEquals(originBitmapInfo.length, fileLength)
        Assert.assertEquals(originBitmapInfo.offset, 86)
        Assert.assertEquals(originBitmapInfo.version, 1)
        Assert.assertEquals(properties.sotOffset, 193)
    }

    @Test
    fun should_return_false_when_equals_with_different_class() {
        // given
        val test1 = 1

        // when
        val ans = properties.equals(test1)

        // then
        Assert.assertEquals(ans, false)
    }

    @Test
    fun should_return_true_when_equals_with_same_object() {
        // given
        val test1 = properties

        // when
        val ans = properties.equals(test1)

        // then
        Assert.assertEquals(ans, true)
    }

    @Test
    fun should_return_true_when_equals_with_copy_object() {
        // given
        properties.correctVertex = floatArrayOf(0f, 0f)
        properties.dataBlockInfos = arrayOf(
            SuperTextProperties.Companion.DataBlockInfo(
                SuperTextProperties.TAG_TEXT_ORIGIN_IMAGE))
        val test1 = properties.copy()

        // when
        val ans = properties.equals(test1)
        val ans2 = properties === test1

        // then
        Assert.assertEquals(ans, true)
        Assert.assertEquals(ans2, false)
    }

    @Test
    fun should_return_false_when_equals_with_different_array() {
        // given
        properties.correctVertex = floatArrayOf(0f, 0f)
        properties.dataBlockInfos = arrayOf(
            SuperTextProperties.Companion.DataBlockInfo(
                SuperTextProperties.TAG_TEXT_ORIGIN_IMAGE))
        val test1 = properties.copy()
        test1.correctVertex = floatArrayOf(1f, 0f)

        // when
        val ans = properties.equals(test1)

        // then
        Assert.assertEquals(ans, false)
    }

    @Test
    fun should_return_constant_when_hashCode() {
        // given

        // when
        val ans = properties.hashCode()

        // then
        val hash = (properties.isSuperText.hashCode() * 31 + properties.correctVertex.contentHashCode()) * 31 * 31 * 31 * 31
        Assert.assertEquals(ans, hash)
    }
}