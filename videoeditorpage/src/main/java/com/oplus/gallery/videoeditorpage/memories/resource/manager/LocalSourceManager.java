package com.oplus.gallery.videoeditorpage.memories.resource.manager;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.oplus.gallery.foundation.networkaccess.base.task.NetTaskManager;
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback;
import com.oplus.gallery.foundation.networkaccess.callback.ProgressListener;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.DownloadListener;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListener;
import com.oplus.gallery.videoeditorpage.resource.room.helper.AbsTableHelper;
import com.oplus.gallery.videoeditorpage.memories.autodownload.RtnCode;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


public abstract class LocalSourceManager<T, U> {
    private static final String TAG = "LocalSourceManager";

    public static final String TEMP_ZIP_SUFFIX = "_tmp";
    public static final String NONE_ITEM_THUMBNAIL = "videoeditor_video_editor_music_none";
    public static final String LOCAL_MUSIC_THUMBNAIL = "videoeditor_ic_music_local";
    public static final String DOWNLOAD_STATE = "download_state";
    public static final String DOWNLOAD_CODE = "download_code";
    public static final String DOWNLOAD_RESOURCE_ID = "download_resource_id";
    public static final String DOWNLOAD_PROGRESS = "download_progress";
    public static final String DOWNLOAD_THEME = "download_theme";
    public static final int ID_INVALID = -1;
    public static final int TYPE_UPDATE_BUILTIN = 1;
    public static final int TYPE_UPDATE_DOWNLOAD = 1 << 1;
    public static final int MAX_PROGRESS = 100;
    public static final int MIN_PROGRESS = 0;
    public static final int DEFAULT_PROGRESS = -1;
    public static final int DOWNLOAD_STATE_INVALID = -1;
    public static final int DOWNLOAD_STATE_DOWNLOADING = 1 << 0;
    public static final int DOWNLOAD_STATE_FINISH = 1 << 1;
    public static final int DOWNLOAD_STATE_ERROR = 1 << 2;
    public static final int DOWNLOAD_DEFAULT_SOURCE = 1;
    public static final int DOWNLOAD_AUTO = 1 << 1;
    public static final int DOWNLOAD_MANUAL = 1 << 2;
    public static final int DOWNLOAD_RETRY = 1 << 3;
    private static final String CHARSET_NAME = "UTF-8";
    private static final String RESOURCE_ROOT_DIRECTORY = "resource";
    private final String mLastRequestTimeKey;
    private final String mActionDownloadState;
    protected AbsTableHelper mTableHelper;
    protected LocalBroadcastManager mLocalBroadcastManager;
    protected Map<Integer, Integer> mDownloadSourceHashMap = new HashMap<>();
    protected int mAutoDownloadResourceId;
    protected boolean mProgressLock = false;
    private Map<Integer, Integer> mDownloadProgressHashMap = new HashMap<>();
    private int mUpdateType;

    protected LocalSourceManager(int updateType, String key, AbsTableHelper helper, String action) {
        mUpdateType = updateType;
        mLastRequestTimeKey = key;
        mTableHelper = helper;
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(ContextGetter.context);
        mActionDownloadState = action;
    }

    protected boolean permitUpdateFromBuiltin() {
        return (mUpdateType & TYPE_UPDATE_BUILTIN) == TYPE_UPDATE_BUILTIN;
    }

    protected boolean permitUpdateFromDownload() {
        return (mUpdateType & TYPE_UPDATE_DOWNLOAD) == TYPE_UPDATE_DOWNLOAD;
    }

    protected boolean isInRequestInterval() {
        Context context = ContextGetter.context;
        if (context == null) {
            GLog.e(TAG, "context is null");
            return false;
        }
        long lastRequestTime = VideoEditorHelper.getLongPref(context,
                mLastRequestTimeKey, 0L);
        long currentTime = System.currentTimeMillis();
        return (currentTime - lastRequestTime) < TimeUtils.TIME_30_MIN_IN_MS;
    }

    protected void updateRequestInterval() {
        VideoEditorHelper.setLongPref(ContextGetter.context,
                mLastRequestTimeKey, System.currentTimeMillis());
    }

    protected void resetRequestInterval() {
        VideoEditorHelper.setLongPref(ContextGetter.context,
                mLastRequestTimeKey, 0);
    }

    protected String downloadNormal(String url, String filePath, DownloadListener listener, boolean filterContentType) {
        Context context = ContextGetter.context;
        String requestTag = null;
        IDownloadAbility downloadAbility = ((GalleryApplication) context).getAppAbility(IDownloadAbility.class);
        if (downloadAbility == null) {
            if (listener != null) {
                listener.onError(RtnCode.DOWNLOAD_ABILITY_NOT_EXIST);
            }
            return null;
        }
        requestTag = downloadAbility.enqueue(context,
                new DownloadFileRequest(context, null, url, filePath, new ProgressListener() {
                    @Override
                    public void progress(long bytesRead, long contentLength, boolean done) {
                        if ((listener != null) && (contentLength != 0)) {
                            listener.onProgress((int) (bytesRead * 1.0f / contentLength * MAX_PROGRESS));
                        }
                    }
                }, filterContentType),
                new AppResultCallback<File>() {
                    @Override
                    public void onSuccess(File file) {
                        Log.i(TAG, "onSuccess, file=" + filePath + ", fileExist=" + file.exists());
                        if (listener != null) {
                            listener.onFinish(RtnCode.NETWORK_SUCCESS, file.getAbsolutePath());
                        }
                    }

                    @Override
                    public void onFailed(int code, String msg) {
                        GLog.d(TAG, "onFailed, code=" + code + ", msg=" + msg);
                        if (listener != null) {
                            listener.onError(code);
                        }
                    }
                }
        );
        downloadAbility.close();
        return requestTag;
    }

    public static List parseConfig(Context context, String config, Type type) {
        List items = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        try {
            inputStreamReader = new InputStreamReader(
                    context.getAssets().open(config), CHARSET_NAME);
            bufferedReader = new BufferedReader(inputStreamReader);
            StringBuffer buffer = new StringBuffer();
            String str = null;
            while (null != (str = bufferedReader.readLine())) {
                buffer.append(str);
                buffer.append("\n");
            }

            Gson gson = new Gson();
            items = gson.fromJson(buffer.toString(), type);
            return items;
        } catch (Exception e) {
            GLog.e(TAG, "parseConfig error", e);
            return items;
        } finally {
             IOUtils.closeQuietly(bufferedReader);
             IOUtils.closeQuietly(inputStreamReader);
        }
    }


    public static void deleteFile(String filePath) {
        File file = new File(filePath);
        file.delete();
    }

    public synchronized List<Integer> getNeedRetryItem() {
        if (mDownloadProgressHashMap.size() == 0) {
            return null;
        }
        List<Integer> resourceIdList = new ArrayList<>();
        Iterator<Integer> iterator = mDownloadProgressHashMap.keySet().iterator();
        while (iterator.hasNext()) {
            Integer key = iterator.next();
            if ((mDownloadProgressHashMap.get(key) != MAX_PROGRESS) && (key != mAutoDownloadResourceId)
                    && (mDownloadProgressHashMap.get(key) != DEFAULT_PROGRESS)) {
                resourceIdList.add(key);
            }
        }
        return resourceIdList;
    }

    public int getDownloadTaskSize() {
        List<Integer> resourceIdList = getNeedRetryItem();
        return (resourceIdList == null) ? 0 : resourceIdList.size();
    }

    protected synchronized boolean isDownloadingStateInQueue(int resourceId) {
        Integer progress = mDownloadProgressHashMap.get(resourceId);
        if ((progress == null)
                || (progress >= MAX_PROGRESS) || (progress < MIN_PROGRESS)) {
            return false;
        } else {
            return true;
        }
    }

    public synchronized void clearDownloadInfo() {
        GLog.d(TAG, "clearDownloadInfo");
        mDownloadProgressHashMap.clear();
    }

    public synchronized void resetDownloadProgress(int resourceId) {
        if (mDownloadProgressHashMap.containsKey(resourceId)) {
            mDownloadProgressHashMap.put(resourceId, DEFAULT_PROGRESS);
        }
    }

    public synchronized void setDownloadProgress(int resourceId, int progress) {
        if (!mProgressLock) {
            mDownloadProgressHashMap.put(resourceId, progress);
        }
    }

    public void cancelDownloadTask() {
        if (!mProgressLock) {
            mProgressLock = true;
            GLog.d(TAG, "cancelAll");
            NetTaskManager.getInstance().cancelAll(true);
        }
    }

    public void resetDownloadQueueStatus() {
        cancelDownloadTask();
        clearDownloadInfo();
    }

    public synchronized int getDownloadProgress(int resourceId) {
        Integer progress = mDownloadProgressHashMap.get(resourceId);
        return progress == null ? DEFAULT_PROGRESS : progress;
    }

    protected void sendDownloadProgress(int resourceId, int progress) {
        setDownloadProgress(resourceId, progress);
        Intent intent = new Intent(mActionDownloadState);
        intent.putExtra(DOWNLOAD_STATE, DOWNLOAD_STATE_DOWNLOADING);
        intent.putExtra(DOWNLOAD_RESOURCE_ID, resourceId);
        intent.putExtra(DOWNLOAD_PROGRESS, progress);
        mLocalBroadcastManager.sendBroadcast(intent);
    }

    protected void sendBroadcast(int state, int code, int resourceId) {
        sendBroadcast(state, code, resourceId, false);
    }

    protected void sendBroadcast(int state, int code, int resourceId, boolean needDownloadTheme) {
        GLog.d(TAG, "[sendBroadcast] state->" + state + ",code->" + code + ",needDownloadTheme->" + needDownloadTheme + ".mActionDownloadState->" + mActionDownloadState);
        Intent intent = new Intent(mActionDownloadState);
        intent.putExtra(DOWNLOAD_STATE, state);
        intent.putExtra(DOWNLOAD_CODE, code);
        intent.putExtra(DOWNLOAD_RESOURCE_ID, resourceId);
        intent.putExtra(DOWNLOAD_THEME, needDownloadTheme);
        mLocalBroadcastManager.sendBroadcast(intent);
        if (state == DOWNLOAD_STATE_FINISH) {
            setDownloadProgress(resourceId, MAX_PROGRESS);
        }
    }

    public synchronized int getSource(int resourceId) {
        Integer source = mDownloadSourceHashMap.get(resourceId);
        return (source == null) ? DOWNLOAD_DEFAULT_SOURCE : source;
    }

    public synchronized void resetAllDownloadSource() {
        mDownloadSourceHashMap.clear();
    }

    public void resetSource(int resourceId) {
        changeSource(resourceId, DOWNLOAD_DEFAULT_SOURCE);
    }

    protected synchronized void changeSource(int resourceId, int source) {
        Integer oldSource = mDownloadSourceHashMap.get(resourceId);
        GLog.d(TAG, "change resourceId " + resourceId + ",from " + oldSource + " to " + source);
        mDownloadSourceHashMap.put(resourceId, source);
        if (source == DOWNLOAD_AUTO) {
            mAutoDownloadResourceId = resourceId;
        } else {
            if (mAutoDownloadResourceId == resourceId) {
                mAutoDownloadResourceId = ID_INVALID;
            }
        }
    }

    public int getAutoDownloadId() {
        return mAutoDownloadResourceId;
    }

    public synchronized boolean isAutoDownload(int resourceId) {
        Integer source = mDownloadSourceHashMap.get(resourceId);
        if (source == null) {
            return false;
        }
        return (source == DOWNLOAD_AUTO);
    }

    public boolean checkBuiltinItem() {
        return checkBuiltinItem(false);
    }

    public String requestNetworkResource(OnLoadingListener listener) {
        return requestNetworkResource(listener, true, false);
    }

    protected abstract boolean checkBuiltinItem(boolean forceUpdate);

    protected abstract String requestNetworkResource(OnLoadingListener listener, boolean needDownloadIcon, boolean forceRequest);

    protected abstract List<T> localizeData(U data, int builtinSize);

    protected abstract List<T> getCoveredEntity(List<T> nowEntityList, int oldSize);

    protected abstract List<T> removeInvalidEntity(List<T> coveredEntityList, int maxPosition);

    protected abstract void checkIcon(OnLoadingListener listener);

    protected abstract void retryDownload();

    protected abstract String downloadFile(T t, int type, DownloadListener listener, boolean filterContentType);

    protected abstract List<T> getResourceLists();

    protected String getDownloadPath(String resource) {
        Context context = ContextGetter.context;
        if (context == null) {
            GLog.e(TAG, "context is null");
            return null;
        }
        StringBuilder path = new StringBuilder();
        path.append(context.getFilesDir().getAbsolutePath());
        path.append(File.separator);
        path.append(RESOURCE_ROOT_DIRECTORY);
        path.append(File.separator);
        path.append(resource);
        return path.toString();
    }

    public static void resetAllDownloadQueueStatus() {
        cancelAllDownloadTask();
        clearAllDownloadInfo();
        clearAllDownloadSource();
    }

    public static void cancelAllDownloadTask() {
        MusicSourceManager.getInstance().cancelDownloadTask();
        ThemeSourceManager.getInstance().cancelDownloadTask();
    }

    public static void clearAllDownloadInfo() {
        MusicSourceManager.getInstance().clearDownloadInfo();
        ThemeSourceManager.getInstance().clearDownloadInfo();
    }

    public static void clearAllDownloadSource() {
        MusicSourceManager.getInstance().resetAllDownloadSource();
        ThemeSourceManager.getInstance().resetAllDownloadSource();
    }
}
