/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - IImageDecoder.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/02
 ** Author      : YongQi.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/02  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec

import android.graphics.BitmapFactory
import android.graphics.Rect
import com.oplus.breakpad.ITombstone

interface IRegionDecoder : ITombstone {
    fun createDecoder()
    fun destroyDecoder()
    fun isYuvImage(fd: Int): Boolean
    fun decodeRegion(rect: Rect, options: BitmapFactory.Options?, isDirectBuffer: Boolean = false): ImageData?
    fun getWidth(): Int
    fun getHeight(): Int
    fun isRecycled(): Boolean
    fun isValid(): Boolean
    fun getDecoderName(): String
    fun isSupportMultiRegionDecode(): Boolean
}