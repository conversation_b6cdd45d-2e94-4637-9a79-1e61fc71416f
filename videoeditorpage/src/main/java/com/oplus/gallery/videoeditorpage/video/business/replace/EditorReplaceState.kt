/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - EditorReplaceState
 ** Description: 替换业务
 ** Version: 1.0
 ** Date : 2025/06/09
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >      <desc>
 **  80320709      2025/06/09  1.0             created
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.replace

import android.content.Context
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * Marked by DuanYibin: 当前替换业务未实现，后面排期开发
 * 替换业务
 */
class EditorReplaceState(
    context: Context,
    editorControlView: EditorControlView,
    /** 替换的新时间线 */
    private val replaceTimeline: ITimeline,
    /** 编辑的视频片段 */
    private val editVideoClip: IVideoClip,
    /** 替换业务选中监听 */
    private val onSelectionListener: EditorReplaceHelper.ReplaceSelectionListener,
    /** 替换的媒体资源 */
    private val mediaItem: MediaItem
) : EditorTrackBaseState<EditorReplaceUIController>(TAG, context, editorControlView) {

    override fun createUIController(): EditorReplaceUIController {
        return EditorReplaceUIController(
            mContext,
            editorControlView,
            this,
            replaceTimeline,
            mediaItem,
            editVideoClip.normalSpeedDuration,
            onSelectionListener
        )
    }

    override fun showOperaIcon(): Boolean {
        return false
    }

    override fun getPageLevel(): PageLevelEnum {
        return PageLevelEnum.PAGE_LEVEL_SECOND
    }

    override fun clickCancel() {
        uiController.clickCancel()
        super.clickCancel()
    }

    override fun clickDone() {
        uiController.clickDone()
        super.clickDone()
    }

    override fun getPlayStartEndTimePair(startPosition: Long): Pair<Long, Long> {
        return Pair(startPosition, uiController.vm.getPlayEndTime())
    }

    override fun isSkipAnim(): Boolean {
        return false
    }

    companion object {
        private const val TAG = "EditorReplaceState"
    }
}
