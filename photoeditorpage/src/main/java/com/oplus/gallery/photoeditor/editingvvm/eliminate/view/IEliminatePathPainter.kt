/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IEliminatePathPainter.kt
 ** Description: 消除的路径
 ** Version: 1.0
 ** Date : 2023/11/1
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/11/01    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.eliminate.view

import com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminatePath
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizerView2

/**
 * 消除路径的监听器
 */
interface EliminatePathListener {

    /**
     * 路径开始的监听
     */
    fun onPathStart()

    /**
     * 路径结束的监听
     * @param path EliminatePath
     */
    fun onPathEnd(path: EliminatePath?)

    /**
     * 取消画笔的监听
     */
    fun onPathCancel()
}

/**
 * 消除路径的Painter
 */
interface IEliminatePathPainter {

    /**
     * 是否可触摸
     */
    var touchable: Boolean

    /**
     * 设置画笔的粗细
     */
    fun setStrokeSize(strokeSize: EliminatePath.Size)

    /**
     * 清除画面的内容
     */
    fun clearContent()

    /**
     * 设置姿势的属性，用于获取图片的显示状态
     * @param previewAnimationProperties PreviewAnimationProperties
     */
    fun setAnimationProperties(previewAnimationProperties: PreviewAnimationProperties)

    /**
     * 设置消除路径的监听器
     * @param listener 监听器
     */
    fun setEliminatePathListener(listener: EliminatePathListener?)

    /**
     * 设置手势的消除路径
     */
    fun setSimpleGestureListener(listener: GestureRecognizerView2.SimpleGestureListener)

    /**
     * 标记绘制路径无效
     */
    fun invalidPath()
}