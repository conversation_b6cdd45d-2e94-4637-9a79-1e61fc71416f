/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IVideoPlayEffect.kt
 * Description:
 * Version: 1.0
 * Date: 2024/12/5
 * Author: zhangweichao@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * zhangweichao@Apps.Gallery3D  2024/12/5          1.0           OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.effect

/**
 * 视频播放特效接口
 */
interface IVideoPlayEffect {

    /**
     * 外部设置特效参数
     */
    fun setPlayEffectParams(effectParams: TBLVideoPlayEffect.PlayEffectParams)

    /**
     * 获取实况图片eis数据对应的特效列表
     */
    fun getEffectList(): List<Any>

    /**
     * 通过key设置视频播放特效列表
     *
     */
    fun setVideoEffects(effectKeys: List<String>)

    /**
     * 获取实况图片eis数据对应的特效列表key
     */
    fun getEffectListKeys(): List<String>
}