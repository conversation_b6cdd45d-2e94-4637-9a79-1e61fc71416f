/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryVideoCutRotate.java
 * * Description: IGalleryVideoCutRotate interface.
 * * Version: 1.0
 * * Date : 2020/03/07
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2020/03/07    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;


import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio;

public interface IGalleryVideoCutRotate {

    boolean isCutRotateChanged();

    void intoCutRotateMode();

    void exitCutRotateMode();

    void setSelectRect(VideoRatio ratio);

    void updateRatio(VideoRatio ratio);

    void cutVideo(boolean isAnimation);

    void rotateVideo(int rotation);

    void setSelectPosition(int position);

    int getSelectPosition();

    int getRotation();

    int getRatioType();

    VideoRatio getRatio();

    void recover(VideoRatio ratio, int rotation, boolean isCut, boolean isAnimation);
}
