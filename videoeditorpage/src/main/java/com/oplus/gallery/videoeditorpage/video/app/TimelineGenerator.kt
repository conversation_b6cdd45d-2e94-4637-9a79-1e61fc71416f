/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TimelineGenerator.kt
 ** Description: 时间线生成器
 **
 ** Version: 1.0
 ** Date:2025/04/28
 ** Author:guangjin.ye@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** guangjin.ye@Apps.Gallery3D   	2025/04/28	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.app

import android.content.Context
import android.content.Intent
import com.google.common.collect.Lists
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.EDIT_VIDEO_DATA
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.IS_MULTIPLE_EDIT_TYPE
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getVideoHdrType
import com.oplus.gallery.business_lib.videoedit.EditVideoData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.utlis.IntentDataHolder
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity.KEY_PHOTO_EDITOR_INVOKER
import com.oplus.gallery.videoeditorpage.video.business.picker.PickerHelper.buildTimelineByItemList
import com.oplus.gallery.videoeditorpage.video.business.picker.data.PickerItemInfo

/**
 * 时间线生成器
 * 负责在不同场景下生成时间线
 */
class TimelineGenerator(private val context: Context) {

    /**
     * timeline生成
     * @param intent 意图
     * @param lastSavedTimeline 上次保存的时间线
     * @return 返回生成的timeline
     */
    fun generate(
        intent: Intent,
        lastSavedTimeline: ITimeline?
    ): ITimeline? {
        return getTimelineByIntent(intent, lastSavedTimeline)?.also {
            initFont(it)
        }
    }

    /**
     * 根据媒体项目构建时间线
     */
    fun buildTimeline(mediaItem: MediaItem): ITimeline? {
        val pickerItemInfo = PickerItemInfo().apply {
            this.srcFilePath = mediaItem.contentUri.toString()
            this.path = this.srcFilePath
            this.duration = mediaItem.duration.toLong() * TimeUtils.TIME_1_MS_IN_US
            this.width = mediaItem.width
            this.height = mediaItem.height
            this.mediaType =  mediaItem.getVideoHdrType()
        }
        return buildTimelineByItemList(Lists.newArrayList(pickerItemInfo), null)
    }

    /**
     * 获取 IntentDataHolder 中保存的时间线的实例
     *
     * @param intent 意图
     * @param lastSavedTimeline 上次保存的时间线
     * @return 返回时间线的实例
     */
    private fun getTimelineByIntent(intent: Intent, lastSavedTimeline: ITimeline?): ITimeline? {
        val timeline = IntentDataHolder.getInstance().getBigDataAndRemove(
            IntentDataHolder.DATA_TIMELINE,
            ITimeline::class.java
        ) ?: run {
            lastSavedTimeline ?: buildTimelineFromGallery(intent)
        }
        if (timeline != null) {
            timeline.isNeedPicMove = false
        }
        return timeline
    }

    /**
     * 从相册调过来，构建timeline
     */
    private fun buildTimelineFromGallery(intent: Intent): ITimeline? {
        val infList = ArrayList<PickerItemInfo>().also {
            if (IntentUtils.getBooleanExtra(intent, IS_MULTIPLE_EDIT_TYPE, false)) {
                it.addAll(intent.toPickerListInfo())
            } else {
                it.add(intent.toPickerItemInfo())
            }
        }
        val editorInvoker = IntentUtils.getStringExtra(intent, KEY_PHOTO_EDITOR_INVOKER)
        GLog.d(TAG, LogFlag.DL, "buildTimelineFromGallery  mEditorInvoker:$editorInvoker")
        return buildTimelineByItemList(infList, editorInvoker)
    }

    /**
     * 初始化字幕相关
     * @param timeline 时间线
     */
    private fun initFont(timeline: ITimeline) {
        GLog.d(TAG, LogFlag.DL, "initFont: captionStyle:" + timeline.aiCaptionStyleId)
        val captionStyleIds = HashSet<String>()
        for (caption in timeline.captionList) {
            captionStyleIds.add(caption.captionStyleId)
        }
        //marked by shixiaohui 0604迁移代码遗留 确认是否需要初始化字幕
        /*val entities = CaptionStyleTableHelper.getInstance()
            .getEntitiesByIds(Arrays.asList(*captionStyleIds.toTypedArray()))
        if (entities != null) {
            for (entity in entities) {
                val captionStyleBean = CaptionStyleBean()
                captionStyleBean.captionStyleEntity = entity
                EditorEngineGlobalContext.getInstance().getAssetManager(context)
                    .registerFontByFilePath(captionStyleBean.fontPath)
            }
        }*/
    }

    /**
     * 单选时，Intent中解析出媒体文件信息
     */
    private fun Intent.toPickerItemInfo(): PickerItemInfo {
        val editVideoData = IntentUtils.getParcelableExtra<EditVideoData>(this, EDIT_VIDEO_DATA)
        return editVideoData.toPickerItemInfo()
    }

    /**
     * 多选时，Intent中解析出媒体文件信息列表
     */
    private fun Intent.toPickerListInfo(): ArrayList<PickerItemInfo> {
        val editVideoDataList = IntentUtils.getParcelableArrayListExtra(this, EDIT_VIDEO_DATA, EditVideoData::class.java)
        val pickerList = ArrayList<PickerItemInfo>()
        editVideoDataList.forEach { editVideoData ->
            pickerList.add(editVideoData.toPickerItemInfo())
        }
        return pickerList
    }

    private fun EditVideoData.toPickerItemInfo(): PickerItemInfo = EditVideoDataConverter.convertToPickerItemInfo(this)

    companion object {
        private const val TAG = "TimelineGenerator"
    }
}