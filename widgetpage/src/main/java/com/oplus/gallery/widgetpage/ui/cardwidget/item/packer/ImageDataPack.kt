/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ImageDataPacker.kt
 ** Description:显示照片的卡片样式
 ** Version: 1.0
 ** Date : 2021/10/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/10/11        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item.packer

import android.content.Intent
import android.content.Intent.ACTION_VIEW
import android.net.Uri
import android.os.Bundle
import android.view.View
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_ANY
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.widgetpage.WidgetConstants
import com.oplus.smartenginehelper.dsl.DSLCoder
import com.oplus.smartenginehelper.entity.AnimEntity
import com.oplus.smartenginehelper.entity.AnimSetEntity
import com.oplus.smartenginehelper.entity.AnimatorEntity
import com.oplus.smartenginehelper.entity.StartActivityClickEntity

data class ImageDataPack(
    // 当前图片的Uri
    private val currentUri: Uri,
    // 上张照片的Uri
    private val previousUri: Uri?,
    // 当前图片的path
    private val itemPath: Path,
    // 当前图片集的path
    private val setPath: Path,
    // 里面包含了卡片宽度、时间、位置、时间字体大小、位置字体大小信息
    private val extra: Bundle
) : BaseDataPack() {
    companion object {
        private const val VIEW_ID_PREVIOUS_IMAGE = "previousImage"
        private const val VIEW_ID_CURRENT_IMAGE = "currentImage"
        private const val VIEW_ID_MAIN_LAYOUT = "mainLayout"
        private const val VIEW_ID_LOCATION_TEXT = "locationText"
        private const val VIEW_ID_TIME_TITLE = "timeTitle"
        private const val VIEW_ID_LOCATION_IMAGE = "locationImage"
        private const val VIEW_ID_MASKING_IMAGE = "maskingImage"
        private const val PROP_NAME_SCALE_X = "scaleX"
        private const val PROP_NAME_SCALE_Y = "scaleY"
        private const val PROP_NAME_TRANSLATION_X = "translationX"
        private const val PROP_NAME_ALPHA = "alpha"
        private const val PROP_VALUE_SCALE = 1.5F
        private const val PROP_VALUE_PREVIOUS_TRANSLATION_PERCENT = 0.25F
        private const val ANIM_DELAY = 300L
        private const val ANIM_DURATION = 900L
        private const val INTERPOLATOR_X1 = 0.3F
        private const val INTERPOLATOR_Y1 = 0F
        private const val INTERPOLATOR_X2 = 0.1F
        private const val INTERPOLATOR_Y2 = 1F
        private const val DEFAULT_INT = 0
        private const val DEFAULT_VALUE = "0"
    }

    override fun onPack(coder: DSLCoder): Boolean {
        GLog.d(TAG, "onPack: $this")
        coder.setImageViewResource(VIEW_ID_CURRENT_IMAGE, currentUri.toString())

        val time = extra.getString(WidgetConstants.BundleKey.KEY_TIME, TextUtil.EMPTY_STRING)
        val location = extra.getString(WidgetConstants.BundleKey.KEY_LOCATION, TextUtil.EMPTY_STRING)
        val timeSize = extra.getString(WidgetConstants.BundleKey.KEY_TIME_SIZE, TextUtil.EMPTY_STRING)
        val maskingLayer = extra.getInt(WidgetConstants.BundleKey.KEY_MASKING_LAYER, DEFAULT_INT)
        val locationSize = extra.getString(WidgetConstants.BundleKey.KEY_LOCATION_SIZE, TextUtil.EMPTY_STRING)
        val widgetWidth = extra.getInt(WidgetConstants.BundleKey.KEY_WIDGET_WIDTH, DEFAULT_INT)
        val widgetMode = extra.getString(IntentConstant.WidgetConstant.KEY_WIDGET_MODE, DEFAULT_VALUE)

        coder.setTextViewTextSize(VIEW_ID_TIME_TITLE, timeSize)
        coder.setTextViewTextSize(VIEW_ID_LOCATION_TEXT, locationSize)
        coder.setImageViewResource(VIEW_ID_MASKING_IMAGE, maskingLayer)
        if (!time.isNullOrEmpty()) {
            coder.setTextViewText(VIEW_ID_TIME_TITLE, time)
            coder.setVisibility(VIEW_ID_TIME_TITLE, View.VISIBLE)
        }
        if (!location.isNullOrEmpty()) {
            coder.setTextViewText(VIEW_ID_LOCATION_TEXT, location)
            coder.setVisibility(VIEW_ID_LOCATION_TEXT, View.VISIBLE)
            coder.setVisibility(VIEW_ID_LOCATION_IMAGE, View.VISIBLE)
        }
        if (!time.isNullOrEmpty() || !location.isNullOrEmpty()) {
            coder.setVisibility(VIEW_ID_MASKING_IMAGE, View.VISIBLE)
        }

        previousUri?.let {
            coder.setImageViewResource(VIEW_ID_PREVIOUS_IMAGE, it.toString())
            coder.setAnim(VIEW_ID_PREVIOUS_IMAGE, getPreviousImageAnim(widgetWidth))
            coder.setAnim(VIEW_ID_CURRENT_IMAGE, getCurrentImageAnim(widgetWidth))
            coder.setTranslationX(VIEW_ID_CURRENT_IMAGE, widgetWidth.toFloat())
        }

        val startActivityClickEntity = StartActivityClickEntity().apply {
            setPackageName(ContextGetter.context.packageName)
            setAction(ACTION_VIEW)
            setCategory(Intent.CATEGORY_DEFAULT)
            // 仅用于适配大图activity启动，实际不使用
            setData(currentUri.toString())
            setIntentType(MIME_TYPE_IMAGE_ANY)
            setParams(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH, itemPath.toString())
            setParams(IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH, setPath.toString())
            setParams(IntentConstant.WidgetConstant.KEY_WIDGET_MODE, widgetMode)
            addFlag(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        }
        coder.setOnClickStartActivity(VIEW_ID_MAIN_LAYOUT, startActivityClickEntity)
        return true
    }

    private fun getCurrentImageAnim(widgetWidth: Int): AnimEntity {
        return AnimEntity("currentImageAnim").apply {
            setAnimSet(AnimSetEntity().apply {
                addAnimatorsTogether(
                    AnimatorEntity(PROP_NAME_SCALE_X, PROP_VALUE_SCALE, 1F).apply {
                        setAnimatorParams(this)
                    },
                    AnimatorEntity(PROP_NAME_SCALE_Y, PROP_VALUE_SCALE, 1F).apply {
                        setAnimatorParams(this)
                    },
                    AnimatorEntity(PROP_NAME_TRANSLATION_X, widgetWidth / 2 * (PROP_VALUE_SCALE + 1), 0F).apply {
                        setAnimatorParams(this)
                    }
                )
            })
        }
    }

    private fun getPreviousImageAnim(widgetWidth: Int): AnimEntity {
        return AnimEntity("previousImageAnim").apply {
            setAnimSet(AnimSetEntity().apply {
                addAnimatorsTogether(
                    AnimatorEntity(PROP_NAME_ALPHA, 1F, 0F).apply {
                        setAnimatorParams(this)
                    },
                    AnimatorEntity(PROP_NAME_TRANSLATION_X, 0F, -widgetWidth.toFloat() * PROP_VALUE_PREVIOUS_TRANSLATION_PERCENT).apply {
                        setAnimatorParams(this)
                    }
                )
            })
        }
    }

    private fun setAnimatorParams(animatorEntity: AnimatorEntity) {
        // 卡片容器本身带有淡入淡出的切换动画，会和相册卡片的切换动画叠加影响效果，目前不支持定制去除，所以通过延迟避免
        animatorEntity.setStartDelay(ANIM_DELAY)
        animatorEntity.setDuration(ANIM_DURATION)
        animatorEntity.setInterpolator(INTERPOLATOR_X1, INTERPOLATOR_Y1, INTERPOLATOR_X2, INTERPOLATOR_Y2)
    }
}