/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseVideoClipEffect.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

abstract class BaseVideoClipEffect(effectName: String?) : BaseVideoEffect(effectName), Cloneable {

    var strength: Float = 1.0f
        set(value) {
            field = value
            setFilterIntensity(value)
        }

    var subEffectList: List<BaseVideoClipEffect> = ArrayList()

    constructor(name: String?, type: Int) : this(name) {
        this.type = type
    }

    /**
     * 设置浮点值参数
     * @param paramName 参数名称
     * @param value 参数值
     */
    abstract fun setFloatValue(paramName: String?, value: Float)

    /**
     * 获取浮点值参数
     * @param paramName 参数名称
     * @return 参数值
     */
    abstract fun getFloatValue(paramName: String?): Double?

    /**
     * 设置字符串值参数
     * @param paramName 参数名称
     * @param value 参数值
     */
    abstract fun setStringValue(paramName: String?, value: String?)

    /**
     * 获取字符串值参数
     * @param paramName 参数名称
     * @return 参数值
     */
    abstract fun getStringValue(paramName: String?): String?

    /**
     * 设置布尔值参数
     * @param paramName 参数名称
     * @param value 参数值
     */
    abstract fun setBooleanVal(paramName: String?, value: Boolean)

    /**
     * 获取布尔值参数
     * @param paramName 参数名称
     * @return 参数值
     */
    abstract fun getBooleanVal(paramName: String?): Boolean?

    open fun setFilterIntensity(value: Float) {
        // do in sub class
    }
}