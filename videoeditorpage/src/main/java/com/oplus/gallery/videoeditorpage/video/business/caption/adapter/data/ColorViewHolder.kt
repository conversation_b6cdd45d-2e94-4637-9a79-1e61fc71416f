/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ColorViewHolder.kt
 ** Description : 文字颜色item viewHolder
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import android.graphics.Color
import android.view.ViewGroup
import android.widget.FrameLayout
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.BaseAdapter.BaseVH
import com.oplus.gallery.videoeditorpage.widget.ColorItemView

/**
 * 颜色选择器view holder
 */
class ColorViewHolder(parent: ViewGroup) : BaseVH<ColorItem>(parent, R.layout.videoeditor_item_caption_color) {

    /**
     * 字体颜色显示控件
     */
    private val colorItemView: ColorItemView by lazy {
        findViewById(R.id.caption_color_view)
    }

    /**
     * 字体颜色选中指示器
     */
    private val colorIndicator: FrameLayout by lazy {
        findViewById(R.id.caption_color_indicator)
    }


    override fun bind(colorItem: ColorItem) {
        colorIndicator.isSelected = colorItem.selected
        runCatching {
            // 如果颜色和背景色相同为黑色，为了区分出来需要设置圆环色
            var ringColor = Color.TRANSPARENT
            if (colorItem.color == BACKGROUND_COLOR_HEX_STRING) {
                ringColor = colorItemView.resources.getColor(R.color.videoeditor_caption_color_item_ring_color, null)
            }
            val res = colorItemView.resources
            colorItemView.itemData = ColorItemView.ColorItemViewData(
                res.getDimension(R.dimen.videoeditor_item_caption_color_inner_stroke_width),
                res.getDimension(R.dimen.videoeditor_item_caption_color_inner_stroke_radius),
                res.getDimension(R.dimen.videoeditor_item_caption_color_outer_stroke_radius),
                Color.parseColor(colorItem.color),
                ringColor
            )
        }.onFailure { e ->
            GLog.e(TAG, 0, "parseColor error")
        }
    }

    companion object {
        private const val TAG = "ColorViewHolder"

        /**
         * 背景色值，用于判断当选色item和背景色一样时设置一个圆环用于区分
         */
        private const val BACKGROUND_COLOR_HEX_STRING = "#FF000000"
    }
}