/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/7/13
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/7/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.autodownload;

public final class ErrorCode {

    public static final int ERROR_DOWNLOAD_FILE = Base.ERROR_CODE_OTHER + 1; //385

    public interface Base {
        int ERROR_CODE_MUSIC = 1 << 7;
        int ERROR_CODE_TEMPLATE = ERROR_CODE_MUSIC * 2;
        int ERROR_CODE_OTHER = ERROR_CODE_MUSIC * 3;
    }

    public interface Music {
        int RESPONSE_DATA_ERROR = Base.ERROR_CODE_MUSIC + 1; //129
        int DATABASE_UPDATE_ERROR = Base.ERROR_CODE_MUSIC + 2; //130
        int DATABASE_INSERT_ERROR = Base.ERROR_CODE_MUSIC + 3; //131
        int DATABASE_DELETE_ERROR = Base.ERROR_CODE_MUSIC + 4; //132
        int DATABASE_QUERY_ERROR = Base.ERROR_CODE_MUSIC + 5; //133
        int PARAM_INVALID = Base.ERROR_CODE_MUSIC + 6; //134
        int ID_INVALID = Base.ERROR_CODE_MUSIC + 7; //135
        int FILE_PATH_INVALID = Base.ERROR_CODE_MUSIC + 8; //136
        int NET_WORK_ERROR = Base.ERROR_CODE_MUSIC + 9; //137
        int ENTITY_IS_NULL = Base.ERROR_CODE_MUSIC + 10; //138
        int NO_PERMISSION = Base.ERROR_CODE_MUSIC + 11; //139
        int UNZIP_ERROR = Base.ERROR_CODE_MUSIC + 12; //140
        int GENERATE_KEY_ERROR = Base.ERROR_CODE_MUSIC + 13; //141
        int ICON_URL_IS_NULL = Base.ERROR_CODE_MUSIC + 14; //142
        int FILE_URL_IS_NULL = Base.ERROR_CODE_MUSIC + 15; //143
        int NO_ALLOW_OPEN_NETWORK = Base.ERROR_CODE_MUSIC + 16; //144
        int UNKNOWN_ERROR = Base.ERROR_CODE_MUSIC + 127; //255
    }

    public interface Theme {
        int RESPONSE_DATA_ERROR = Base.ERROR_CODE_TEMPLATE + 1; //257
        int DATABASE_UPDATE_ERROR = Base.ERROR_CODE_TEMPLATE + 2; //258
        int DATABASE_INSERT_ERROR = Base.ERROR_CODE_TEMPLATE + 3; //259
        int DATABASE_DELETE_ERROR = Base.ERROR_CODE_TEMPLATE + 4; //260
        int DATABASE_QUERY_ERROR = Base.ERROR_CODE_TEMPLATE + 5; //261
        int PARAM_INVALID = Base.ERROR_CODE_TEMPLATE + 6; //262
        int ID_INVALID = Base.ERROR_CODE_TEMPLATE + 7; //263
        int UNZIP_ERROR = Base.ERROR_CODE_TEMPLATE + 8; //264
        int FILE_PATH_INVALID = Base.ERROR_CODE_TEMPLATE + 9; //265
        int NET_WORK_ERROR = Base.ERROR_CODE_TEMPLATE + 10; //266
        int ENTITY_IS_NULL = Base.ERROR_CODE_TEMPLATE + 11; //267
        int NO_PERMISSION = Base.ERROR_CODE_TEMPLATE + 12; //268
        int MUSIC_ENTITY_IS_NULL = Base.ERROR_CODE_TEMPLATE + 13; //269
        int GENERATE_KEY_ERROR = Base.ERROR_CODE_TEMPLATE + 14; //270
        int ICON_URL_IS_NULL = Base.ERROR_CODE_TEMPLATE + 15; //271
        int FILE_URL_IS_NULL = Base.ERROR_CODE_TEMPLATE + 16; //272
        int NO_ALLOW_OPEN_NETWORK = Base.ERROR_CODE_TEMPLATE + 17; //273
        int UNKNOWN_ERROR = Base.ERROR_CODE_TEMPLATE + 127; //383
    }

    public interface Normal {

    }

    private ErrorCode() {
    }
}
