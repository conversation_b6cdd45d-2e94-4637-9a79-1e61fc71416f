/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - VideoSubTitleEditTextView.java
 ** Description: for show editText while add subTitle.
 ** Version: 1.0
 ** Date : 2017/12/03
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/12/03    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.framework.abilities.videoedit.data.SubTitleInfo;

import java.util.ArrayList;
import java.util.List;

public class VideoSubTitleEditTextView extends View {
    private static final String TAG = "VideoSubTitleEditTextView";

    private static final int POINT_F_SIZE_4 = 4;
    private static final int POINT_F_INDEX_0 = 0;
    private static final int POINT_F_INDEX_1 = 1;
    private static final int POINT_F_INDEX_2 = 2;
    private static final int POINT_F_INDEX_3 = 3;
    private static final int WIDTH_OUTSIDE_OFFSET = 3;
    private static final int HEIGHT_OUTSIDE_OFFSET = 3;
    private static final float ONE_HUNDRED_PERCENT = 1.0f;
    private static final int BORDER_OFFSET = 50;
    private static final float SHADOW_RADIUS = 2f;
    private final Rect mTextBorderRect = new Rect();
    private GestureDetector mGestureDetector;
    private OnSubTitleTouchListener mListener;
    private RectF mDeleteRectF = new RectF();
    private List<SubTitleInfo> mSubTitleInfoList = new ArrayList<>();
    private Paint mLinePaint = new Paint();
    private Bitmap mDeleteImgBtn;
    private int mDrawRectLarger = 0;
    private int mCurrentEditorIndex = -1;
    private int mLeftBorder = 0;
    private int mRightBorder = 0;
    private int mUpBorder = 0;
    private int mDownBorder = 0;
    private int mDefaultBlackColor;
    private boolean mCanMove = false;
    private boolean mCanDel = false;
    private boolean mEditorMode = false;
    private float mDeleteBtnHalfHeight;
    private float mDeleteBtnHalfWidth;
    private int mShadowColor;

    public VideoSubTitleEditTextView(Context context) {
        super(context);
        init();
    }

    public VideoSubTitleEditTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public VideoSubTitleEditTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public VideoSubTitleEditTextView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        this.setForceDarkAllowed(false);
        setVisibility(View.GONE);
        mGestureDetector = new GestureDetector(getContext(), mOnGestureListener);
        mDeleteImgBtn = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_editor_subtitle_delete_button);
        mDeleteBtnHalfHeight = mDeleteImgBtn.getHeight() / 2f;
        mDeleteBtnHalfWidth = mDeleteImgBtn.getWidth() / 2f;
        mDrawRectLarger = getResources().getDimensionPixelSize(R.dimen.video_editor_subtitle_rect_larger);
        mDefaultBlackColor = getResources().getColor(R.color.videoeditor_video_editor_background_color, null);
        mShadowColor = getResources().getColor(R.color.videoeditor_subtitle_shadow_color, null);
        mLinePaint.setColor(Color.WHITE);
        mLinePaint.setAntiAlias(true);
        mLinePaint.setStrokeWidth(3);
        mLinePaint.setStyle(Paint.Style.STROKE);
        mLinePaint.setShadowLayer(SHADOW_RADIUS, 0, 0, mShadowColor);
    }

    public void setDrawSubTitleList(List<SubTitleInfo> titleInfoList) {
        mSubTitleInfoList.clear();
        if ((titleInfoList != null) && (titleInfoList.size() > 0)) {
            mSubTitleInfoList.addAll(titleInfoList);
        } else {
            mEditorMode = false;
        }
        invalidate();
    }

    public void updateVideoBorder(int videoWidth, int videoHeight) {
        int width = getLayoutParams().width;
        int height = getLayoutParams().height;
        if ((width > 0) && (height > 0)
                && (videoWidth > 0) && (videoHeight > 0)) {
            if ((videoWidth / (float) videoHeight) < (width / (float) height)) {
                // left and right border
                videoWidth = (videoWidth * height) / videoHeight;
                mLeftBorder = (width - videoWidth) / 2;
                mRightBorder = width - mLeftBorder;
                mUpBorder = 0;
                mDownBorder = 0;
                GLog.d(TAG, "updateVideoBorder() mLeftBorder:" + mLeftBorder + " mRightBorder:" + mRightBorder);
            } else {
                // up and down border
                videoHeight = (videoHeight * width) / videoWidth;
                mLeftBorder = 0;
                mRightBorder = 0;
                mUpBorder = (height - videoHeight) / 2;
                mDownBorder = height - mUpBorder;
                GLog.d(TAG, "updateVideoBorder() mUpBorder:" + mUpBorder + " mDownBorder:" + mDownBorder);
            }
        }
    }

    public void exitEditorMode() {
        if (mEditorMode) {
            mEditorMode = false;
            mDeleteRectF.set(0, 0, 0, 0);
            invalidate();
        }
    }

    public void setCurrentEditorSubTitle(long subTitleIndex) {
        if ((mSubTitleInfoList != null) && (mSubTitleInfoList.size() > 0) && (subTitleIndex > 0)) {
            int index = 0;
            for (SubTitleInfo info : mSubTitleInfoList) {
                if (info.getSubTitleIndex() == subTitleIndex) {
                    mCurrentEditorIndex = index;
                    mEditorMode = true;
                    invalidate();
                    break;
                }
                index++;
            }
        }
    }

    private void enterEditorMode() {
        mEditorMode = true;
        invalidate();
    }

    public void setOnSubTitleTouchListener(OnSubTitleTouchListener listener) {
        mListener = listener;
    }

    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (!mEditorMode
                || (mCurrentEditorIndex < 0)
                || (mSubTitleInfoList == null)
                || (mSubTitleInfoList.size() < (mCurrentEditorIndex + 1))) {
            return;
        }
        List<PointF> pointFList = mSubTitleInfoList.get(mCurrentEditorIndex).getFourPointPos();
        if ((pointFList == null) || (pointFList.size() != POINT_F_SIZE_4)) {
            return;
        }

        // draw text border rect
        int left = (int) pointFList.get(POINT_F_INDEX_0).x - mDrawRectLarger;
        int top = (int) pointFList.get(POINT_F_INDEX_0).y - mDrawRectLarger;
        int right = (int) pointFList.get(POINT_F_INDEX_2).x + mDrawRectLarger;
        int bottom = (int) pointFList.get(POINT_F_INDEX_2).y + mDrawRectLarger;
        mTextBorderRect.set(left, top, right, bottom);
        canvas.drawRect(mTextBorderRect, mLinePaint);

        // draw delete button
        if (ResourceUtils.isRTL(getContext())) {
            canvas.drawBitmap(mDeleteImgBtn, pointFList.get(POINT_F_INDEX_3).x - mDeleteBtnHalfWidth + mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_3).y - mDeleteBtnHalfHeight - mDrawRectLarger, mLinePaint);
            mDeleteRectF.set(pointFList.get(POINT_F_INDEX_3).x - mDeleteBtnHalfWidth + mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_3).y - mDeleteBtnHalfHeight - mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_3).x + mDeleteBtnHalfWidth + mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_3).y + mDeleteBtnHalfHeight - mDrawRectLarger);
        } else {
            canvas.drawBitmap(mDeleteImgBtn, pointFList.get(POINT_F_INDEX_0).x - mDeleteBtnHalfWidth - mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_0).y - mDeleteBtnHalfHeight - mDrawRectLarger, mLinePaint);
            mDeleteRectF.set(pointFList.get(POINT_F_INDEX_0).x - mDeleteBtnHalfWidth - mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_0).y - mDeleteBtnHalfHeight - mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_0).x + mDeleteBtnHalfWidth - mDrawRectLarger,
                    pointFList.get(POINT_F_INDEX_0).y + mDeleteBtnHalfHeight - mDrawRectLarger);
        }

        if ((mLeftBorder > 0) && (mRightBorder > 0)) {
            canvas.save();
            canvas.clipRect(0, 0, mLeftBorder, getHeight());
            canvas.drawColor(mDefaultBlackColor);
            canvas.restore();
            canvas.save();
            canvas.clipRect(mRightBorder, 0, getWidth(), getHeight());
            canvas.drawColor(mDefaultBlackColor);
            canvas.restore();
        } else if ((mUpBorder > 0) && (mDownBorder > 0)) {
            canvas.save();
            canvas.clipRect(0, 0, getWidth(), mUpBorder);
            canvas.drawColor(mDefaultBlackColor);
            canvas.restore();
            canvas.save();
            canvas.clipRect(0, mDownBorder, getWidth(), getHeight());
            canvas.drawColor(mDefaultBlackColor);
            canvas.restore();
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return mGestureDetector.onTouchEvent(event);
    }

    private GestureDetector.OnGestureListener mOnGestureListener = new GestureDetector.OnGestureListener() {

        @Override
        public boolean onDown(MotionEvent motionEvent) {
            float targetX = motionEvent.getX();
            float targetY = motionEvent.getY();
            mCanDel = mDeleteRectF.contains(targetX, targetY);
            // check if touch text view
            if (!mCanDel && (mSubTitleInfoList != null) && (mSubTitleInfoList.size() > 0)) {
                if (mEditorMode) {
                    List<PointF> pointFList = mSubTitleInfoList.get(mCurrentEditorIndex).getFourPointPos();
                    mCanMove = checkRegionContains(pointFList, targetX, targetY);
                } else {
                    int index = 0;
                    for (SubTitleInfo info : mSubTitleInfoList) {
                        List<PointF> pointFList = info.getFourPointPos();
                        mCanMove = checkRegionContains(pointFList, targetX, targetY);
                        if (mCanMove) {
                            mCurrentEditorIndex = index;
                            break;
                        }
                        index++;
                    }
                }
            }
            return true;
        }

        private boolean checkRegionContains(List<PointF> pointFList, float x, float y) {
            boolean ans = false;
            if ((pointFList != null) && (pointFList.size() == POINT_F_SIZE_4)) {
                Region region = new Region();
                Rect rect = new Rect((int) pointFList.get(POINT_F_INDEX_0).x - mDrawRectLarger,
                        (int) pointFList.get(POINT_F_INDEX_0).y - mDrawRectLarger,
                        (int) pointFList.get(POINT_F_INDEX_2).x + mDrawRectLarger,
                        (int) pointFList.get(POINT_F_INDEX_2).y + mDrawRectLarger);
                region.set(rect);
                ans = region.contains((int) x, (int) y);
            }
            return ans;
        }

        @Override
        public void onShowPress(MotionEvent motionEvent) {
        }

        @Override
        public boolean onSingleTapUp(MotionEvent motionEvent) {
            float targetX = motionEvent.getX();
            float targetY = motionEvent.getY();
            if (mCanDel) {
                if (mDeleteRectF.contains(targetX, targetY) && (mSubTitleInfoList != null)
                        && (mSubTitleInfoList.size() >= mCurrentEditorIndex + 1)) {
                    mListener.onDel(mSubTitleInfoList.get(mCurrentEditorIndex).getSubTitleIndex());
                    exitEditorMode();
                }
            } else {
                if (mCanMove) {
                    if (!mEditorMode) {
                        enterEditorMode();
                    } else if ((mListener != null) && (mSubTitleInfoList != null)
                            && (mSubTitleInfoList.size() >= mCurrentEditorIndex + 1)) {
                        mListener.onClickTouch(mSubTitleInfoList.get(mCurrentEditorIndex).getSubTitleIndex());
                    }
                } else {
                    exitEditorMode();
                }
            }
            mCanMove = false;
            return true;
        }

        @Override
        public boolean onScroll(MotionEvent motionEvent, MotionEvent motionEvent1, float v, float v1) {
            if (!mCanMove || !mEditorMode
                    || (mSubTitleInfoList == null)
                    || (mSubTitleInfoList.size() < (mCurrentEditorIndex + 1))) {
                return true;
            }
            List<PointF> pointFList = mSubTitleInfoList.get(mCurrentEditorIndex).getFourPointPos();
            // computer center point
            PointF centerPointF = new PointF();
            float pointLeft = pointFList.get(POINT_F_INDEX_0).x;
            float pointRight = pointFList.get(POINT_F_INDEX_2).x;
            centerPointF.x = (pointLeft + pointRight) / 2;
            centerPointF.y = (pointFList.get(POINT_F_INDEX_0).y + pointFList.get(POINT_F_INDEX_2).y) / 2;
            if (mListener != null) {
                float newX = centerPointF.x - v;
                float newY = centerPointF.y - v1;
                if (!checkBorder(newX, newY, pointLeft - v, pointRight - v)) {
                    mListener.onDrag(mSubTitleInfoList.get(mCurrentEditorIndex).getSubTitleIndex(),
                            centerPointF, new PointF(newX, newY));
                }
            }
            return true;
        }

        private boolean checkBorder(float targetX, float targetY, float pointLeft, float pointRight) {
            if ((mLeftBorder > 0) && (mRightBorder > 0)) {
                return (((targetX <= mLeftBorder) && (pointRight <= mRightBorder - BORDER_OFFSET))
                        || ((targetX >= mRightBorder) && (pointLeft >= mLeftBorder + BORDER_OFFSET))
                        || (targetY >= getHeight() - HEIGHT_OUTSIDE_OFFSET) || (targetY <= HEIGHT_OUTSIDE_OFFSET));
            } else if ((mUpBorder > 0) && (mDownBorder > 0)) {
                return ((targetX <= WIDTH_OUTSIDE_OFFSET) || (targetX >= getWidth() - WIDTH_OUTSIDE_OFFSET)
                        || (targetY >= mDownBorder) || (targetY <= mUpBorder));
            } else {
                return ((targetX <= WIDTH_OUTSIDE_OFFSET) || (targetX >= getWidth() - WIDTH_OUTSIDE_OFFSET)
                        || (targetY >= getHeight() - HEIGHT_OUTSIDE_OFFSET) || (targetY <= HEIGHT_OUTSIDE_OFFSET));
            }
        }

        @Override
        public void onLongPress(MotionEvent motionEvent) {

        }

        @Override
        public boolean onFling(MotionEvent motionEvent, MotionEvent motionEvent1, float v, float v1) {
            return false;
        }
    };

    public interface OnSubTitleTouchListener {
        void onDrag(long subTitleIndex, PointF mPrePointF, PointF nowPointF);

        void onDel(long subTitleIndex);

        void onClickTouch(long subTitleIndex);
    }

    public float getXPercent(float xPos) {
        float ans = 0;
        if ((mLeftBorder > 0) && (mRightBorder > 0)) {
            ans = (xPos - mLeftBorder) / (mRightBorder - mLeftBorder);
        } else {
            ans = xPos / getWidth();
        }
        if (ans < 0) {
            ans = 0;
        } else if (Float.compare(ans, ONE_HUNDRED_PERCENT) > 0) {
            ans = ONE_HUNDRED_PERCENT;
        }
        return ans;
    }

    public float getYPercent(float yPos) {
        float ans = 0;
        if ((mUpBorder > 0) && (mDownBorder > 0)) {
            ans = (yPos - mUpBorder) / (mDownBorder - mUpBorder);
        } else {
            ans = yPos / getHeight();
        }
        if (ans < 0) {
            ans = 0;
        } else if (Float.compare(ans, ONE_HUNDRED_PERCENT) > 0) {
            ans = ONE_HUNDRED_PERCENT;
        }
        return ans;
    }
}
