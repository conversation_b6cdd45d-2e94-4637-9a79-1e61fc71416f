/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryAudioClip.java
 * * Description: IGalleryAudioClip interface.
 * * Version: 1.0
 * * Date : 2017/12/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/29    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

import com.oplus.gallery.framework.abilities.videoedit.data.SongInfo;

public interface IGalleryAudioClip {


    /*---------------- audio clip start ----------------*/

    boolean addSong(SongInfo songItem);

    void setCurrentSongItem(SongInfo songItem);

    SongInfo getCurrentSongInfo();

    boolean addTrimMusic(String filePath, long start, long end, long duration);

    boolean reAlignMusic(long startTime);

    boolean removeMusic();

    boolean removeMusic(long start, long end);

    boolean changeAudioSpeed(float speed);

    int getCurrentMusicIndex();

    void setLocalMusicIndex(int index);

    void resetLocalMusicIndex();

    String getLocalMusicUri();

    long getLocalMusicTrimStart();

    long getLocalMusicTrimEnd();

    long getLocalMusicDuration();

    void saveMusicState();

    void resetMusicState();
    /*---------------- audio clip end ----------------*/
}
