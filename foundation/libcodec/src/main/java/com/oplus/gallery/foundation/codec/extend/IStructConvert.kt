/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IConvert.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/31
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * hucanh<PERSON>@Apps.Gallery		2024/10/31		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.codec.extend

/**
 * [Struct]结构转成java/kt对象
 */
interface IStructConvert<T> {
    /**
     * 转换成对应对象
     */
    fun toData(): T?
}

/**
 * 将数据对象类型转换成[ByteArray]的接口
 */
interface IInverseStructConvert {
    /**
     * 转换成[ByteArray]
     */
    fun toBytes(): ByteArray
}