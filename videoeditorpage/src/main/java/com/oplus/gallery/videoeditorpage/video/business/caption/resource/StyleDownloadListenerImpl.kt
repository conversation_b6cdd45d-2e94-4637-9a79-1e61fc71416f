/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : StyleDownloadListenerImpl.kt
 ** Description : 字幕样式字体资源下载监听器
 ** Version     : 1.0
 ** Date        : 2025/6/20 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/20  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.resource

import androidx.lifecycle.LifecycleCoroutineScope
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle.isRunningForeground
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionDownloadFinishedType
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionStyleItem
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.DownloadItem
import com.oplus.gallery.videoeditorpage.video.business.caption.util.CaptionResourceHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * 样式文件下载资源监听器
 *
 * @param item 下载的资源item
 * @param position 下载item在列表中的位置
 * @param notifyItemChangedCallback 通知列表更新item的回调
 */
class StyleDownloadListenerImpl(
    /**
     * 文字协程作用域
     */
    private val captionScope: LifecycleCoroutineScope,
    private val item: CaptionStyleItem,
    private val position: Int,
    private val notifyItemChangedCallback: (item: CaptionStyleItem, position: Int, finished: Boolean) -> Unit
) : OnLoadFileListener<CaptionStyleItem> {

    /**
     * 标识样式是否下载完成
     */
    @Volatile
    private var isStyleDownloaded: Boolean = false

    /**
     * 标识字体是否下载完成
     * 初始化值看是否有绑定的字体，若存在绑定字体则为false，否则为true
     */
    @Volatile
    private var isFontDownloaded: Boolean = (item.fontItem == null)

    override fun onProgress(progress: Int, item: CaptionStyleItem) {
        // 资源文件下载进度更新后，通知列表更新item
        notifyItemChangedCallback.invoke(item, position, false)
    }

    override fun onFinish(item: CaptionStyleItem) {

        // 解压zip文件到指定的样式字体资源目录
        captionScope.launch(Dispatchers.IO) {
            when (item.downloadFinishedType) {
                CaptionDownloadFinishedType.STYLE_DOWNLOAD_FINISHED -> processDownloadedStyle(item)
                CaptionDownloadFinishedType.FONT_DOWNLOAD_FINISHED -> processDownloadedFont(item)
                else -> Unit
            }

            if (isStyleDownloaded && isFontDownloaded) {
                captionScope.launch(Dispatchers.Main) {
                    // 资源文件下载解压成功后，更新UI
                    notifyItemChangedCallback.invoke(item, position, true)
                }
            }
        }
    }

    /**
     * 处理下载完成的样式资源zip文件
     */
    private fun processDownloadedStyle(item: CaptionStyleItem) {
        if (item.downloadState == DownloadItem.DOWNLOADED) {
            isStyleDownloaded = true
            return
        }
        val filePath = item.zipLocalPath
        val file = File(filePath)
        if (CaptionResourceHelper.isZipFile(file).not()) return
        val actualMd5 = CaptionResourceHelper.calculateMD5(file)
        if (item.resourceMd5 != actualMd5) return
        val unzipPath = CaptionResourceHelper.unzipStyleFile(filePath)
        if (unzipPath.isNotEmpty()) {
            item.downloadState = DownloadItem.DOWNLOADED
            item.localPath = unzipPath
            isStyleDownloaded = true
        }
    }

    /**
     * 处理下载完成的字体资源zip文件
     */
    private fun processDownloadedFont(item: CaptionStyleItem) {
        val fontItem = item.fontItem ?: return
        if (fontItem.downloadState == DownloadItem.DOWNLOADED) {
            isFontDownloaded = true
            return
        }
        val filePath = fontItem.zipLocalPath
        val file = File(filePath)
        if (CaptionResourceHelper.isZipFile(file).not()) return
        val actualMd5 = CaptionResourceHelper.calculateMD5(file)
        if (fontItem.resourceMd5 != actualMd5) return
        val unzipPath = CaptionResourceHelper.unzipFontFile(filePath)
        if (unzipPath.isNotEmpty()) {
            fontItem.downloadState = DownloadItem.DOWNLOADED
            fontItem.localPath = unzipPath
            isFontDownloaded = true
        }
    }

    override fun onError(errCode: ErrorCode, item: CaptionStyleItem?) {
        if (isRunningForeground()) {
            val stringRes = when (errCode) {
                ErrorCode.NO_NETWORK_WHEN_DOWNLOADING -> R.string.videoeditor_download_network_disconnect
                ErrorCode.NO_NETWORK -> R.string.videoeditor_editor_no_network
                else -> R.string.videoeditor_download_fail
            }
            captionScope.launch(Dispatchers.UI) {
                ToastUtil.showShortToast(stringRes)
            }
            item ?: return
            // 下载失败后下载进度需要重置为0并刷新UI
            item.progress = 0
            item.fontItem?.let { it.progress = 0 }
            notifyItemChangedCallback.invoke(item, position, false)
        }
    }

    override fun onCancel(item: CaptionStyleItem) = Unit
}