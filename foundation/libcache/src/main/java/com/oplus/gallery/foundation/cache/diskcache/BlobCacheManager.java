/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.foundation.cache.diskcache;

import static com.oplus.gallery.addon.utils.OSVersionUtils.OPLUS_OS_16_0_0;
import static com.oplus.gallery.foundation.util.debug.LogFlag.DL;

import android.content.Context;
import android.content.SharedPreferences;

import androidx.preference.PreferenceManager;

import com.oplus.gallery.addon.utils.OSVersionUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.file.File;

import java.io.IOException;
import java.util.HashMap;

public class BlobCacheManager {
    private static final String TAG = "CacheManager";
    private static final HashMap<String, BlobCache> sCacheMap = new HashMap<>();

    private static final String BLOB_CACHE_PATH = "blob_cache";

    private static final String KEY_OLD_CACHE_ALREADY_DELETE = "old_cache_already_delete";

    // Return null when we cannot instantiate a BlobCache, e.g.:
    // there is no SD card found.
    // This can only be called from data thread.
    public static BlobCache getCache(Context context, String filename, int maxEntries,
                                     int maxBytes, int version) {
        synchronized (sCacheMap) {
            BlobCache cache = sCacheMap.get(filename);
            if (cache == null) {
                GLog.d(TAG, DL, "getCacheDir, filename:" + filename);
                long startTime = System.currentTimeMillis();
                java.io.File blobCacheDir = getBlobCacheDir(context);
                if (blobCacheDir != null) {
                    File cacheDir = new File(blobCacheDir);
                    String path = cacheDir.getAbsolutePath() + "/" + filename;
                    GLog.d(TAG, DL, "getBlobCacheDir:" + (System.currentTimeMillis() - startTime) + ",cacheDir:" + path);
                    try {
                        cache = new BlobCache(path, maxEntries, maxBytes, false, version);
                        sCacheMap.put(filename, cache);
                    } catch (IOException e) {
                        GLog.e(TAG, DL, "Cannot instantiate cache!", e);
                    }
                } else  {
                    GLog.e(TAG, DL, "getBlobCacheDir() is null");
                }
            }
            return cache;
        }
    }

    /**
     * 删除缓存文件
     *
     * @param context
     * @param fileNames 文件名：需要删除的文件集合
     */
    public static void deleteCacheFiles(Context context, String[] fileNames) {
        if ((null == context) || (null == fileNames) || (fileNames.length == 0)) return;
        java.io.File cacheFile = getBlobCacheDir(context);
        deleteCacheFiles(fileNames, cacheFile);
        if (OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0)) {
            // ColorOS 16上需要删除老的缓存文件（cache目录下）
            deleteOldCacheFilesIfNecessary(context, fileNames);
        }
    }

    private static void deleteCacheFiles(String[] fileNames, java.io.File cacheFile) {
        if (cacheFile == null) {
            GLog.e(TAG, DL, "deleteCacheFiles cacheFile is null");
            return;
        }
        File cacheDir = new File(cacheFile);
        String prefix = cacheDir.getAbsolutePath() + "/";
        for (String fileName : fileNames) {
            BlobCache.deleteFiles(prefix + fileName);
        }
    }

    private static void deleteOldCacheFilesIfNecessary(Context context, String[] fileNames) {
        SharedPreferences pref = PreferenceManager.getDefaultSharedPreferences(context);
        int n = pref.getInt(KEY_OLD_CACHE_ALREADY_DELETE, 0);
        if (n != 0) {
            return;
        }
        pref.edit().putInt(KEY_OLD_CACHE_ALREADY_DELETE, 1).apply();
        deleteCacheFiles(fileNames, context.getExternalCacheDir());
    }

    /**
     * 获取磁盘缓存路径，为防止修改目录后导致ColorOS 16以前的用户覆盖安装后进入相册拿不到缓存，区别返回缓存路径
     *
     * @return ColorOS 16及以上返回file目录，否则返回cache目录
     */
    private static java.io.File getBlobCacheDir(Context context) {
        if (OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0)) {
            return context.getExternalFilesDir(BLOB_CACHE_PATH);
        }
        return context.getExternalCacheDir();
    }
}
