/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseKeyFrameInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.data;

import com.google.gson.annotations.SerializedName;

public class BaseKeyFrameInfo {
    @SerializedName("a")
    private long mInPoint;  //在片段上的时间点
    @SerializedName("b")
    private float mTransX;
    @SerializedName("c")
    private float mTransY;
    @SerializedName("d")
    private float mScaleX;
    @SerializedName("e")
    private float mScaleY;
    @SerializedName("f")
    private float mRotationZ;

    public long getInPoint() {
        return mInPoint;
    }

    public void setInPoint(long inPoint) {
        this.mInPoint = inPoint;
    }

    public float getTransX() {
        return mTransX;
    }

    public void setTransX(float transX) {
        this.mTransX = transX;
    }

    public float getTransY() {
        return mTransY;
    }

    public void setTransY(float transY) {
        this.mTransY = transY;
    }

    public float getScaleX() {
        return mScaleX;
    }

    public void setScaleX(float scaleX) {
        this.mScaleX = scaleX;
    }

    public float getScaleY() {
        return mScaleY;
    }

    public void setScaleY(float scaleY) {
        this.mScaleY = scaleY;
    }

    public float getRotationZ() {
        return mRotationZ;
    }

    public void setRotationZ(float rotationZ) {
        this.mRotationZ = rotationZ;
    }
}
