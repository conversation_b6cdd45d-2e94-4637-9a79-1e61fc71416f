<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <!-- 卡片能力鉴权码，以分号隔开，前两个为oppo包名的鉴权码，后两个为一加包名的鉴权码，规则详情：https://odocs.myoas.com/docs/5xkGMEYM26uBVn3X -->
        <meta-data
            android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBEAiAER850jpCe0yOS5ADv3gNm8OXK2kNclsYs4RmHpKmf0QIgMwX0S05DyjWjQJnnWxvI2ZkbYn7H+/bRrwE5yIL1lOdrvKEX;ADBGAiEAlLhFk0+iXpmYA6qqWQmgFvzN9makbVzGn53y+hLvvLECIQDOD8C3EiHnf9V1ovYIn2WYL1e6jpmgNVt7yrpaFwJ6pmqyVos=;ADBFAiBt76W3nuzsy8KHeJI4+rW0nT9M0G/I09CbW0Wiu4tO3QIhAIE1BxadTMSThEoeKHH9bhXoe9OC6NGiKxHTsAobIdBCbFL3cQ==;ADBEAiABh/lD/GJjlwNLDj3qqJrJovw74w9XYf64wcmJaabhpwIgKtqayr4/2RVW9dQAFhOqgFBn418i3MIl2xdMTZkOmyBsUvdi" />

        <!-- 卡片商店需要验证provider名称，且暂时不支持多provider（计划4月底覆盖支持），所以暂时不能修改provider包名 -->
        <provider
            android:name="com.oplus.gallery.widgetlib.provider.GalleryCardWidgetProvider"
            android:authorities="com.oplus.gallery.widget"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.card.provider.array"
                android:resource="@array/widget_array" />
            <!--精选 start -->
            <meta-data
                android:name="gallery.widget.provider.choice.2x2"
                android:resource="@xml/widget_choice_2x2" />
            <meta-data
                android:name="gallery.widget.provider.choice.4x2"
                android:resource="@xml/widget_choice_4x2" />
            <meta-data
                android:name="gallery.widget.provider.choice.4x4"
                android:resource="@xml/widget_choice_4x4" />
            <!--精选 end -->
            <!--自选 start -->
            <meta-data
                android:name="gallery.widget.provider.custom.2x2"
                android:resource="@xml/widget_custom_2x2" />
            <meta-data
                android:name="gallery.widget.provider.custom.4x2"
                android:resource="@xml/widget_custom_4x2" />
            <meta-data
                android:name="gallery.widget.provider.custom.4x4"
                android:resource="@xml/widget_custom_4x4" />
            <!--自选 end -->
        </provider>

        <activity
            android:name="com.oplus.gallery.widgetpage.ui.picturemanager.CustomPictureManagerActivity"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:exported="true"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|keyboard|mcc|mnc|smallestScreenSize"
            android:theme="@style/TransparentTheme"
            android:launchMode="singleInstance"
            android:noHistory="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="oplus.intent.action.WIDGET_CUSTOM_PICTURE_MANAGER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.gallery.widgetpage.ui.displaylist.WidgetDisplayListActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|keyboard|mcc|mnc|smallestScreenSize"
            android:exported="false"
            android:excludeFromRecents="true">
        </activity>

        <!--对外提供给负一屏授权调用-->
        <activity-alias
            android:name="com.oplus.gallery.business_lib.ui.activity.AssistantPermissionsActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|mcc|mnc"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:targetActivity="com.oplus.gallery.business_lib.ui.activity.AuthorizePermissionsActivity"
            android:theme="@style/TransparentTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="oplus.intent.action.GALLERY_AUTHORIZE_PERMISSIONS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <!--监听卖场模式重启广播, 卖场模式是系统级别的，所以exported false才能接收到广播-->
        <receiver
            android:name="com.oplus.gallery.widgetpage.broadcast.SellModeBroadcastReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.oppo.daydreamvideo.REQUEST_RESTORE_DATA" />
                <action android:name="com.oplus.daydreamvideo.REQUEST_RESTORE_DATA" />
            </intent-filter>
        </receiver>
    </application>
</manifest>