/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: VideoEditorSourceDatabase
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguang<PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/


package com.oplus.gallery.videoeditorpage.resource.room;

import androidx.room.Database;
import androidx.room.RoomDatabase;

import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.SongItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.TransitionItem;
import com.oplus.gallery.videoeditorpage.resource.room.dao.TextTranslateDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.CaptionFontsDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.CaptionStylesDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.MusicDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.SongDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.ThemeDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.TransitionDao;
import com.oplus.gallery.videoeditorpage.resource.room.dao.VideoAudioDao;
import com.oplus.gallery.videoeditorpage.resource.room.entity.VideoAudioItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionFontItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionStyleItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.TextTranslateItem;
import com.oplus.gallery.videoeditorpage.resource.room.helper.ResourceDatabaseHelper;


/**
 * 定义一个名为`VideoEditorSourceDatabase`的抽象类，用于管理视频编辑相关的数据源。
 * 该数据库包含`ThemeItem`、`MusicItem`、`resource_video_audio`和`SongItem`实体，
 * 版本号由`ResourceDatabaseHelper.CURRENT_VERSION`指定，并且不导出数据库模式。
 */
@Database(
        entities = {
        ThemeItem.class,
        MusicItem.class,
        VideoAudioItem.class,
        SongItem.class,
        CaptionStyleItem.class,
        CaptionFontItem.class,
        TextTranslateItem.class,
        TransitionItem.class
        },
        version = ResourceDatabaseHelper.CURRENT_VERSION,
        exportSchema = false
)
public abstract class VideoEditorSourceDatabase extends RoomDatabase {
    /**
     * 获取ThemeDao实例的方法，用于执行与主题相关的数据库操作。
     * @return 主题数据库访问对象。
     */
    public abstract ThemeDao getThemeDao();

    /**
     * 获取MusicDao实例的方法，用于执行与音乐相关的数据库操作。
     * @return 音乐数据库访问对象。
     */
    public abstract MusicDao getMusicDao();

    /**
     * 获取VideoAudioDao实例的方法，用于执行与视频音频相关的数据库操作。
     * @return 提取音乐数据库访问对象。
     */
    public abstract VideoAudioDao getVideoAudioDao();

    /**
     * 获取SongDao实例的方法，用于执行与歌曲相关的数据库操作。
     * @return 歌曲数据库访问对象。
     */
    public abstract SongDao getSongDao();

    /**
     * 获取CaptionStylesDao实例的方法，用于执行与字幕样式相关的数据库操作。
     * @return 字幕样式数据库访问对象。
     */
    public abstract CaptionStylesDao getCaptionStylesDao();

    /**
     * 获取CaptionFontsDao实例的方法，用于执行与字幕字体相关的数据库操作。
     * @return 字幕字体数据库访问对象。
     */
    public abstract CaptionFontsDao getCaptionFontsDao();

    /**
     * 获取{@link TextTranslateDao}实例的方法，用于执行与词条翻译包相关的数据库操作。
     * @return 词条翻译包数据库访问对象。
     */
    public abstract TextTranslateDao getTextTranslateDao();

    /**
     * 获取{@link TransitionDao}实例的方法，用于执行与转场相关的数据库操作。
     * @return 数据库访问对象。
     */
    public abstract TransitionDao getTransitionDao();
}
