/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExportOliveTask
 ** Description: 导出为实况的任务栈
 ** Version: 1.0
 ** Date : 2025/05/08
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/05/08    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.output.task

import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.util.Size
import com.oplus.breakpad.BreakpadUtil
import com.oplus.breakpad.runNativeGuarding
import com.oplus.gallery.addon.graphics.toUltraHdrInfo
import com.oplus.gallery.business_lib.model.config.SaveConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.scaleBitmap
import com.oplus.gallery.foundation.util.ext.size
import com.oplus.gallery.foundation.util.graphic.BitmapSaveUtils
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.jni.UpScale
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.uhdr.VideoUhdrEngine
import com.oplus.gallery.videoeditorpage.abilities.interfaces.ITimelineCompileListener
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoEngine.Companion.MILLIS_TIME_BASE
import com.oplus.gallery.videoeditorpage.utlis.VideoTypeUtils
import com.oplus.gallery.videoeditorpage.utlis.VideoTypeUtils.isSpecifyType
import com.oplus.gallery.videoeditorpage.utlis.VideoTypeUtils.isTypeSupportDolbyEdit
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils
import com.oplus.gallery.videoeditorpage.video.business.exportolive.ExportOliveParam.COVER_OFFSET_TIME
import com.oplus.gallery.videoeditorpage.video.business.exportolive.ExportOliveParam.MAX_EXPORT_TIME
import com.oplus.gallery.videoeditorpage.video.business.input.VideoType.HDR10PLUS_VIDEO_TYPE
import com.oplus.gallery.videoeditorpage.video.business.input.VideoType.HDR10_VIDEO_TYPE
import com.oplus.gallery.videoeditorpage.video.business.input.VideoType.HLG_VIDEO_TYPE
import com.oplus.gallery.videoeditorpage.video.business.output.ExportListener
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode.Companion.FORCE_STOP_SAVE
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode.Companion.GET_FRAME_TASK_ERROR
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode.Companion.SAVE_FAILED_ERROR
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode.Companion.SOURCE_FILE_ERROR
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode.Companion.UNKNOWN_ERROR
import com.oplus.gallery.videoeditorpage.video.business.picker.PickerHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.min

/**
 * 导出为实况的任务栈
 * 步骤1：获取封面图片、视频片段
 * 步骤2：合成实况照片
 * 步骤3：导出实况照片到外部路径
 * @param videoEngine: 视频编辑对象
 * @param videoUhdrEngine: 视频导出uhdr对象
 * @param saveVideoData: 保存视频数据体
 * @param coverBitmap: 封面图，如果入参为null。则会抽首帧作为封面
 * @param exportOliveListener: 导出实况任务进行状态监听器
 */
class ExportOliveTask(
    private val videoEngine: EditorEngine,
    private val videoUhdrEngine: VideoUhdrEngine?,
    private val saveVideoData: SaveVideoData,
    private var coverBitmap: Bitmap? = null,
    private val exportOliveListener: ExportListener<ResultSaveFileData?>
) : ExportBaseTask(videoEngine) {

    override val name = TAG

    override var isStopTask: Boolean = false

    /**
     * 封面帧时间戳（单位：微秒）
     */
    var coverTimeUs: Long = 0L

    /**
     * 是否是菜单导出实况
     */
    var isMenuExportOlive = false

    /**
     * 开始执行视频导出实况任务
     */
    override fun run() {
        kotlin.runCatching {
            // 1. 收集当前视频信息数据
            val videoUris = getVideoUris()
            if (videoUris.isEmpty()) {
                exportOliveListener.onExportComplete(OutputData(SOURCE_FILE_ERROR, ResultCode.toString(SOURCE_FILE_ERROR)))
                return
            }
            val urisString = StringBuilder().apply {
                videoUris.forEach { append("$it${TextUtil.SPLIT_COMMA_SEPARATOR}") }
            }
            /* Mark by W9088894，待后续图片编辑，放开限制，这里删除
             * 场景：
             * 图片编辑，限制实况最大分辨率不能超出 3840*2160，而视频编辑支持4K最大到 3840*3840。
             * 导致视频编辑保存的实况图，存在无法进入图片编辑的情况。
             * 和产品沟通结论，暂时先压缩视频保存的分辨率。待后续图片编辑放开限制，再去掉压缩逻辑 */
            compressionResolutionIfNeed()
            GLog.d(TAG, LogFlag.DL) { "[run] run ExportVideoTask. Uris: $urisString" }

            // 2.准备实况封面图
            var metadata: IHdrMetadataPack? = null
            if (coverBitmap?.isRecycled != false) {
                GLog.i(TAG, LogFlag.DL) { "[run] cover bitmap is invalid, get frame again" }
                val outputData = GetFrameTask(coverTimeUs, videoEngine, videoUhdrEngine).run()
                if (outputData.uhdrBitmap?.isRecycled != false) {
                    exportOliveListener.onExportComplete(OutputData(GET_FRAME_TASK_ERROR, ResultCode.toString(GET_FRAME_TASK_ERROR)))
                    return
                }
                coverBitmap = outputData.uhdrBitmap
                metadata = outputData.metadataPack
            } else if (ApiLevelUtil.isAtLeastAndroidU()) {
                metadata = coverBitmap?.gainmap?.toUltraHdrInfo()?.let { UHdrMetadataPack(it) }
            }
            updateCoverBitmapScale()

            // 3.将封面图保存到olive资源保存临时路径
            BitmapSaveUtils.saveBitmap(
                coverBitmap,
                coverBitmap?.colorSpace,
                File(coverTmpFilePath),
                Bitmap.CompressFormat.JPEG,
                SaveConfig.COMPRESS_QUALITY
            )

            // 4.生成并保存实况图片
            processSaveLivePhoto(metadata, videoUris)
        }.onFailure { error ->
            GLog.e(TAG, LogFlag.DL) { "[run] $TAG run error: $error" }
            val code = if (isStopTask) FORCE_STOP_SAVE else UNKNOWN_ERROR
            exportOliveListener.onExportComplete(OutputData(code, ResultCode.toString(code)))
        }
    }

    /**
     * 更新封面图分辨率。当保存时，调整了保存分辨率，则需要对抽帧的封面图进行缩放
     */
    private fun updateCoverBitmapScale() {
        // 封面图尺寸与保存所需尺寸不一致时，则需要对封面进行缩放
        val bitmap = coverBitmap?.takeUnless { it.isRecycled } ?: run {
            GLog.w(TAG, LogFlag.DL) { "[updateCoverBitmapScale] cover bitmap is invalid" }
            return
        }
        val time = System.currentTimeMillis()
        val scale = saveVideoData.videoSize.height.toFloat() / bitmap.height.toFloat()
        coverBitmap = when {
            // 放大
            (scale > MathUtils.ONE_F) -> {
                runNativeGuarding(TAG, BreakpadUtil.generateKey(TAG, 0)) {
                    UpScale.upScaleBitmap(
                        bitmap,
                        scale,
                        UP_SCALE_BITMAP_MAX_SHARPEN,
                        UpScale.ScaleMethod.BICUBIC,
                        UP_SCALE_BITMAP_MAX_THREAD_COUNT
                    )
                }.onNativeFailure {
                    GLog.e(TAG, LogFlag.DL) { "[updateCoverBitmapScale] up scale failed." }
                }.getOrNull()?.apply {
                    BitmapUtils.trySetBitmapColorSpace(this, bitmap.colorSpace, "$TAG.requestProcess")
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                        GLog.i(TAG, LogFlag.DL) { "[updateCoverBitmapScale] up scale, gainmap: ${bitmap.gainmap}" }
                        gainmap = bitmap.gainmap
                    }
                    bitmap.recycle()
                } ?: bitmap
            }

            // 缩小
            (scale > MathUtils.ZERO_F) && (scale < MathUtils.ONE_F) -> {
                bitmap.scaleBitmap(scale, false)?.apply {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                        GLog.i(TAG, LogFlag.DL) { "[updateCoverBitmapScale] down scale, gainmap: ${bitmap.gainmap}" }
                        gainmap = bitmap.gainmap
                    }
                    bitmap.recycle()
                } ?: bitmap
            }

            else -> bitmap
        }
        GLog.i(TAG, LogFlag.DL) { "[updateCoverBitmapScale] bitmapSize: ${bitmap.size()}, scale: $scale, consuming = ${GLog.getTime(time)}" }
    }

    /**
     * 如果超出3840*2160范围，则压缩分辨率
     */
    private fun compressionResolutionIfNeed() {
        saveVideoData.apply {
            val limitSize = if (videoSize.width > videoSize.height) {
                Size(VideoUtils.VIDEO_RESOLUTION_3840, VideoUtils.VIDEO_RESOLUTION_2160)
            } else {
                Size(VideoUtils.VIDEO_RESOLUTION_2160, VideoUtils.VIDEO_RESOLUTION_3840)
            }
            if ((videoSize.width > limitSize.width) || (videoSize.height > limitSize.height)) {
                val scale = min(limitSize.width.toFloat() / videoSize.width, limitSize.height.toFloat() / videoSize.height)
                var newWidth = (videoSize.width * scale).toInt()
                var newHeight = (videoSize.height * scale).toInt()
                newWidth = ((newWidth - VideoUtils.WIDHT_MULTIPLE) / VideoUtils.WIDHT_MULTIPLE + 1) * VideoUtils.WIDHT_MULTIPLE
                newHeight = ((newHeight - VideoUtils.HEIGHT_MULTIPLE) / VideoUtils.HEIGHT_MULTIPLE + 1) * VideoUtils.HEIGHT_MULTIPLE
                GLog.d(TAG, LogFlag.DL) { "[compressionResolutionIfNeed] videoSize: $videoSize, newVideoSize: $newWidth x $newHeight" }
                videoSize = Size(newWidth, newHeight)
            }
        }
    }

    /**
     * 生成并保存实况照片到外部路径
     * @param metadata: metadata 数据
     * @param uris: 所有视频的uri列表
     */
    private fun processSaveLivePhoto(
        metadata: IHdrMetadataPack?,
        uris: MutableSet<Uri>
    ) {
        // 1.构建导出状态监听器，用于监控视频保存状态
        var timelineCompileListener: ITimelineCompileListener? = null
        timelineCompileListener = object : ITimelineCompileListener {
            override fun onCompileProgress(progress: Int) = Unit

            override fun onCompileFinished() = Unit

            override fun onCompileFailed() {
                videoEngine.removeTimelineCompileListener(timelineCompileListener)
                GLog.d(TAG, LogFlag.DL, "processSaveLivePhoto onVideoSaveFail")
                exportOliveListener.onExportComplete(OutputData(SAVE_FAILED_ERROR, ResultCode.toString(SAVE_FAILED_ERROR)))
            }

            override fun onCompileCompleted(isCanceled: Boolean) {
                videoEngine.removeTimelineCompileListener(timelineCompileListener)
                compileCompleted(isCanceled, metadata, uris)
            }

            override fun onCompileFloatProgress(progress: Float) {
                exportOliveListener.onProcess(progress)
            }
        }

        // 2. 注册并监听livephoto中video的保存情况，即将开始保存视频片段到临时路径下
        videoEngine.addTimelineCompileListener(timelineCompileListener)
        if (stopTaskRunning() { stopTaskCallBack() }) {
            // 当未执行导出但注册了监听也需要移除
            videoEngine.removeTimelineCompileListener(timelineCompileListener)
            return
        }

        // 3.开始获取并导出视频
        costStartTime = System.currentTimeMillis()
        // 根据业务需求决定导出的格式
        val exportVideoType = getExportOliveVideoHdrType(saveVideoData.videoType)
        saveVideoData.videoType = exportVideoType
        CutVideoTask(videoEngine, saveVideoData, videoTmpFilePath, exportOliveListener).run()
    }

    /**
     * 获取导出实况的视频格式
     * @param videoType 视频类型
     * @return 最终视频类型
     */
    private fun getExportOliveVideoHdrType(videoType: Int): Int {
        // 杜比，HDR10，HDR10+ 转为 HLG
        if (isTypeSupportDolbyEdit(videoType) || isSpecifyType(videoType, HDR10_VIDEO_TYPE) || isSpecifyType(videoType, HDR10PLUS_VIDEO_TYPE)) {
            return HLG_VIDEO_TYPE
        }
        return videoType
    }

    /**
     * 视频导出完成回调
     * @param isCanceled: 是否中途取消导致的完成
     * @param metadata: metadata 数据
     * @param uris: 所有视频的uri集合
     */
    private fun compileCompleted(
        isCanceled: Boolean,
        metadata: IHdrMetadataPack?,
        uris: MutableSet<Uri>
    ) {
        AppScope.launch(Dispatchers.IO) {
            // 导出完成后，构建live photo并保存到临时路径下，在继续操作前判断导出是否已经取消
            if (stopTaskRunning(isCanceled) { stopTaskCallBack() }) {
                return@launch
            }

            GLog.d(TAG, LogFlag.DL, "CutVideoTask.run cost time: ${System.currentTimeMillis() - costStartTime}")

            // 实况中封面帧时间
            val coverTimeInUs = if (isMenuExportOlive) getCoverTimeInUs(coverTimeUs / MILLIS_TIME_BASE) else coverTimeUs
            val combineResult = CombineOliveTask(coverTmpFilePath, videoTmpFilePath, coverTimeInUs).run()
            if (combineResult.resultCode != ResultCode.SAVE_SUCCESS) {
                exportOliveListener.onExportComplete(OutputData(combineResult.resultCode, combineResult.resultMsg))
                return@launch
            }

            val (sourceUri, sourceVideoType) = getHighestVideoFromUris(uris)
            val saveResult = SaveOliveFileTask(
                SaveFileTaskData(
                    sourceFileUri = sourceUri,
                    saveType = SaveType.OLIVE,
                    saveFile = combineResult.resultData,
                    sourceVideoType = sourceVideoType,
                    targetVideoType = VideoTypeUtils.getSaveVideoType(saveVideoData.videoType).first,
                    metaData = metadata,
                    isMultiple = (PickerHelper.pickerList.size > MathUtils.ONE),
                    createTimeStamp = saveVideoData.createTimeStamp
                )
            ).run()

            // 通知外部保存结果
            if (stopTaskRunning() { stopTaskCallBack() }) {
                return@launch
            }
            exportOliveListener.onExportComplete(saveResult)
        }
    }

    /**
     * 停止任务结果回调
     */
    private fun stopTaskCallBack() {
        val resultCode = if (isStopTask) {
            ResultCode.TASK_STOP_FORCE_STOP_SAVE
        } else {
            ResultCode.MEICAM_FORCE_STOP_SAVE
        }
        exportOliveListener.onExportComplete(OutputData(resultCode, ResultCode.toString(resultCode)))
    }

    /**
     * 通过传入的视频封面帧时间计算实况图封面帧时间
     * @param coverTime 视频封面帧时间
     * @return 实况图封面帧时间
     */
    private fun getCoverTimeInUs(coverTime: Long): Long {
        val totalTime = videoEngine.getTotalTime()
        val coverTimeInUs = when {
            totalTime <= MAX_EXPORT_TIME -> coverTime * MILLIS_TIME_BASE
            coverTime < COVER_OFFSET_TIME -> coverTime * MILLIS_TIME_BASE
            coverTime.plus(COVER_OFFSET_TIME) > totalTime -> MAX_EXPORT_TIME * (coverTime / totalTime.toFloat()) * MILLIS_TIME_BASE
            else -> COVER_OFFSET_TIME * MILLIS_TIME_BASE
        }
        return coverTimeInUs.toLong()
    }

    companion object {
        private const val TAG = "ExportOliveTask"

        /**
         * 锐化强度
         */
        private const val UP_SCALE_BITMAP_MAX_SHARPEN = 256

        /**
         * 此线程数，指的是同一张SDK会多条线程处理
         */
        private const val UP_SCALE_BITMAP_MAX_THREAD_COUNT = 4
    }
}