/********************************************************************************
 * Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - DefaultCornerBitmapHandler.java
 * Description:
 * Version: 1.0
 * Date : 2019/5/6
 * Author: huca<PERSON><PERSON>@Apps.VideoEditor
 * TAG:
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>        <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.VideoEditor       2019/5/6      1.0          build this module
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.Size;

import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.utlis.AppUtil;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;

public class DefaultCornerBitmapOption implements IBitmapTransformOption {
    private static final String TAG = "DefaultCornerBitmapOption";
    private float[] mRadiusArray;
    private Size mSize;

    public DefaultCornerBitmapOption() {
        int dimension = ContextGetter.context.getResources()
                .getDimensionPixelSize(R.dimen.default_corner_radius);
        mRadiusArray = new float[]{dimension, dimension, dimension, dimension, dimension, dimension, dimension, dimension};
    }

    public DefaultCornerBitmapOption(int radius) {
        mRadiusArray = new float[]{radius, radius, radius, radius, radius,
                radius, radius, radius};
    }

    public DefaultCornerBitmapOption(int radius, int width, int height) {
        mRadiusArray = new float[]{radius, radius, radius, radius, radius,
                radius, radius, radius};
        mSize = new Size(width, height);
    }


    @Override
    public Size getSize() {
        return mSize;
    }

    @Override
    public Bitmap transform(Bitmap inTransformBitmap, Bitmap target) {
        int widthLight = 0;
        int heightLight = 0;

        if ((null != getSize()) && (getSize().getWidth() > 0) && (getSize().getHeight() > 0)) {
            widthLight = getSize().getWidth();
            heightLight = getSize().getHeight();
        } else {
            widthLight = inTransformBitmap.getWidth();
            heightLight = inTransformBitmap.getHeight();
        }

        Bitmap output = null;

        if ((target == null) || (target.getWidth() != inTransformBitmap.getWidth()) || (target.getHeight() != inTransformBitmap.getHeight())) {
            output = Bitmap.createBitmap(inTransformBitmap.getWidth(), inTransformBitmap.getHeight(), Bitmap.Config.ARGB_8888);
        } else {
            output = target;
            output.setHasAlpha(true);
        }

        GLog.d(TAG, "transform, height:" + inTransformBitmap.getHeight() + ",width:" + inTransformBitmap.getWidth()
                + ",widthLight:" + widthLight + ", heightLight:" + heightLight);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        RectF rectF = new RectF(new Rect(0, 0, widthLight, heightLight));
        Path path = new Path();
        path.addRoundRect(rectF, mRadiusArray, Path.Direction.CW);
        Canvas canvas = new Canvas(output);
        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
        canvas.drawPath(path, paint);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        canvas.drawBitmap(inTransformBitmap, 0, 0, paint);
        canvas.setBitmap(null);
        return output;
    }
}
