/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IMediaDetector
 ** Description: 媒体文件检测器接口
 **
 ** Version: 1.0
 ** Date: 2024/10/22
 ** Author: Yegua<PERSON><PERSON>@Apps.Gallery
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2024/10/22  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cvimageprocess.detect.detector

import android.graphics.Bitmap
import com.oplus.gallery.foundation.cvimageprocess.detect.data.FaceDetectInputData
import com.oplus.gallery.foundation.cvimageprocess.detect.data.FaceDetectResult

/**
 * 人脸检测器接口
 */
internal interface IFaceDetector {

    /**
     * 算法需要加载的部分在这里执行
     */
    fun initialize()

    /**
     * 人脸检测
     * @param bitmap 待检测的图片
     * @param inputData 辅助人脸检测的输入数据
     * @return 返回检测结果
     */
    fun detectFace(bitmap: Bitmap, inputData: FaceDetectInputData): FaceDetectResult

    /**
     * 完成算法后，在此处进行资源释放
     */
    fun release()
}