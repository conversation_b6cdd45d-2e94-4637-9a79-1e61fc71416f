/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CustomViewEditorUIScheme.kt
 ** Description: 自定义业务  暂时只有壁纸使用
 ** Version: 1.0
 ** Date : 2025/3/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2025/3/18    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.view.ViewGroup
import android.widget.RelativeLayout
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.video.controler.BaseCustomTrimUIScheme

/**
 * 壁纸业务UI适配
 */
class VideoEditorWallpaperUIScheme(
    activity: BaseActivity,
    rootView: ViewGroup,
    editorEngine: EditorEngine
) : BaseCustomTrimUIScheme(activity, rootView, editorEngine) {
    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        return when {
            isLandscape -> R.layout.videoeditor_frame_custom_trimvideo_layout_landscape
            else -> R.layout.videoeditor_frame_custom_trimvideo_layout
        }
    }

    /**
     * 适配系统导航栏和状态栏间距
     */
    override fun adaptSysBarMargin() {
        (actionContainer.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.topMargin = EditorUIConfig.isEditorLandscape(activity.getCurrentAppUiConfig()).let { isLandScape ->
                if (isLandScape) {
                    0
                } else {
                    activity.resources.getDimensionPixelSize(R.dimen.videoeditor_wallpaper_trim_safe_top_margin)
                }
            }
            actionContainer.requestLayout()
        }
        (bottomSafeView.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.height = activity.bottomNaviBarHeight()
            bottomSafeView.requestLayout()
        }
    }

    override fun setScheme() {
        uiExecutor.setScheme(this@VideoEditorWallpaperUIScheme, null)
    }
}