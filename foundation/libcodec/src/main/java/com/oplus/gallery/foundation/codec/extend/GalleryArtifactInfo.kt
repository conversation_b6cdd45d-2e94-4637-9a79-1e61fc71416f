/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryArtifactInfo.kt
 ** Description:
 **     相册产物内记录的保存信息
 **
 ** Version: 1.0
 ** Date: 2025-03-18
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/03/18  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.extend

import com.google.gson.annotations.SerializedName

/**
 * 相册产物内记录的保存信息
 * 相册编辑产生的文件会写入此信息，为方便追溯图片来源
 *
 * 此类会以 JSON 的形式储存，添加的属性需为可空类型
 *
 * @param phoneModel 保存设备的型号
 * @param sourceVideoType 源视频或源 Olive 中视频的类型 [VideoTypeParser.VideoType]
 * @param appVersionCode 保存时的相册版本
 */
data class GalleryArtifactInfo(
    @SerializedName("model")
    val phoneModel: String?,

    @SerializedName("svType")
    val sourceVideoType: Int?,

    @SerializedName("ver")
    val appVersionCode: Long?,
) {
    override fun toString(): String {
        return "GalleryArtifactInfo(" +
                "phoneModel: $phoneModel, " +
                "sourceVideoType: $sourceVideoType," +
                "appVersionCode: $appVersionCode" +
                ")"
    }
}
