/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamTransform2D.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask;


import android.graphics.PointF;

public class MeicamTransform2D {

    private PointF mAnchor = new PointF();
    private PointF mScale = new PointF(1, 1);
    private float mRotation;
    private PointF mTranslation = new PointF();

    public PointF getAnchor() {
        return mAnchor;
    }

    public void setAnchor(PointF mAnchor) {
        this.mAnchor = mAnchor;
    }

    public PointF getScale() {
        return mScale;
    }

    public void setScale(PointF mScale) {
        this.mScale = mScale;
    }

    public float getRotation() {
        return mRotation;
    }

    public void setRotation(float mRotation) {
        this.mRotation = mRotation;
    }

    public PointF getTranslation() {
        return mTranslation;
    }

    public void setTranslation(PointF mTranslation) {
        this.mTranslation = mTranslation;
    }

    @Override
    public String toString() {
        return "MeicamTransform2D{" +
                "mAnchor=" + mAnchor +
                ", mScale=" + mScale +
                ", mRotation=" + mRotation +
                ", mTranslation=" + mTranslation +
                '}';
    }
}
