/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamCaption.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption;

import android.graphics.PointF;
import android.graphics.RectF;
import android.text.TextUtils;

import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams;
import com.oplus.gallery.videoeditorpage.utlis.TextUtil;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionAlignment;
import com.meicam.sdk.NvsCaption;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsTimelineCaption;
import com.oplus.gallery.videoeditorpage.utlis.ColorUtil;
import java.util.ArrayList;
import java.util.List;

public class MeicamCaption extends BaseCaption {
    private static final String TAG = "MeicamCaption";
    /**
     * 文字默认颜色值
     */
    public static final String CAPTION_DEFAULT_TEXT_COLOR = "#FFFFFFFF";

    /**
     * 默认缩放比例
     */
    public static final float DEFAULT_SCALE = 1;

    /**
     * 默认字体类型
     */
    private static final String DEFAULT_FONT_FAMILY = "";

    /**
     * 默认文本内容
     */
    private static final String DEFAULT_TEXT = "";

    /**
     * 默认浮点值（尺寸大小、位置坐标等使用）
     */
    private static final float DEFAULT_FLOAT_VALUE = 0;

    /**
     * 字幕轨道默认的索引位置
     */
    private static final int CAPTION_DEFAULT_TRACK_INDEX = 1;

    /**
     * 美摄字幕对象实例
     */
    private transient NvsTimelineCaption mNvsCaption;

    /**
     * 字幕内容
     */
    private String mText = DEFAULT_TEXT;

    /**
     * 字幕位置
     */
    private PointF mTranslation = new PointF(DEFAULT_FLOAT_VALUE, DEFAULT_FLOAT_VALUE);

    /**
     * X轴缩放比例
     */
    private float mScaleX = DEFAULT_SCALE;

    /**
     * Y轴缩放比例
     */
    private float mScaleY = DEFAULT_SCALE;

    /**
     * 字幕旋转角度
     */
    private float mRotation = DEFAULT_FLOAT_VALUE;

    /**
     * 字幕轨道位置
     */
    private float mZValue = CAPTION_DEFAULT_TRACK_INDEX;

    private boolean mClipAffinityEnabled = false;

    /**
     * 字体大小
     */
    private float mFontSize = DEFAULT_FLOAT_VALUE;

    /**
     * 字体类型
     */
    private String mFontFamily = DEFAULT_FONT_FAMILY;

    /**
     * 字体本地路径
     */
    private String mFontPath;

    /**
     * 字幕文本颜色
     */
    private String mTextColor = CAPTION_DEFAULT_TEXT_COLOR;

    /**
     * 加粗
     */
    private boolean mBold = false;

    /**
     * 是否描边
     */
    private boolean mDrawOutline = false;

    /**
     * 描边颜色
     */
    private String mOutlineColor = CAPTION_DEFAULT_TEXT_COLOR;

    /**
     * 描边宽度
     */
    private float mOutlineWidth = DEFAULT_FLOAT_VALUE;

    /**
     * 字间距
     */
    private float mLetterSpacing = DEFAULT_FLOAT_VALUE;

    /**
     * 行间距
     */
    private float mLineSpacing = DEFAULT_FLOAT_VALUE;

    /**
     * 是否绘制阴影
     */
    private boolean mDrawShadow = false;

    /**
     * 阴影颜色
     */
    private String mShadowColor = CAPTION_DEFAULT_TEXT_COLOR;

    /**
     * 阴影偏移量
     */
    private PointF mShadowOffset = new PointF(DEFAULT_FLOAT_VALUE, DEFAULT_FLOAT_VALUE);

    /**
     * 阴影羽化
     */
    private  float mShadowFeather = DEFAULT_FLOAT_VALUE;

    /**
     * 文字透明度
     */
    private float mOpacity = DEFAULT_FLOAT_VALUE;

    /**
     * 获取背景颜色
     */
    private String mBackgroundColor = CAPTION_DEFAULT_TEXT_COLOR;

    /**
     * 背景圆角
     */
    private float mBackgroundRadius = DEFAULT_FLOAT_VALUE;

    /**
     * 字幕背景边界扩展比率
     */
    private float mBoundaryPaddingRatio = DEFAULT_FLOAT_VALUE;

    /**
     * 是否设置斜体
     */
    private boolean mItalic = false;

    /**
     * 是否设置下划线
     */
    private boolean mUnderline = false;

    /**
     * 是否设置删除线
     */
    private boolean mStrickeOut = false;

    public MeicamCaption(String name) {
        super(name);
    }
    public MeicamCaption() {
        super(TAG);
    }

    public void setNvsCaption(NvsTimelineCaption nvsCaption) {
        if (nvsCaption == null) {
            GLog.e(TAG, LogFlag.DL, "[setNvsCaption] nvsCaption is null");
            return;
        }
        // 每次初始化生成一个新的nvsCaption要将位置初始化为（0，0）避免切换样式的时候发生位置跳变
        nvsCaption.setCaptionTranslation(new PointF(DEFAULT_FLOAT_VALUE, DEFAULT_FLOAT_VALUE));
        // 加粗属性默认设置为false
        nvsCaption.setBold(false);
        // 设置不记录用户设置字幕的操作属性，避免切换样式的时候发生属性设置引起效果异常显示
        nvsCaption.setRecordingUserOperation(false);
        // 设置完nvsCaption后，提取nvsCaption的默认值初始化caption的属性
        mNvsCaption = nvsCaption;
        updateCaptionProperties();
    }

    public NvsTimelineCaption getNvsCaption() { return mNvsCaption; }

    /**
     * 设置字幕内容
     */
    public void setText(String text) {
        mText = text;
        if (mNvsCaption != null) {
            mNvsCaption.setText(text);
        }
    }

    /**
     * 获取字幕内容
     * 后续需要跟进为什么重建timeline后实例对象在但mNvsCaption.getText() 返回null marked by 施小辉
     */
    public String getText() { return mText; }

    /**
     * 设置字幕的显示入点位置
     * @param inTime 字幕的显示入点位置
     */
    public void setInTime(long inTime) {
        super.setInTime(inTime);
        if (mNvsCaption != null) {
            mNvsCaption.changeInPoint(inTime);
        }
    }

    /**
     * 设置字幕的显示出点位置
     * @param outTime 字幕的显示出点位置
     */
    public void setOutTime(long outTime) {
        super.setOutTime(outTime);
        if (mNvsCaption != null) {
            mNvsCaption.changeOutPoint(outTime);
        }
    }

    /**
     * 获取资源样式id
     * @return 资源样式包id
     */
    public String getCaptionStyleId() { return mCaptionStyleId; }

    /**
     * 设置资源样式id
     * @param captionStyleId 样式资源id
     */
    public void setCaptionStyleId(String captionStyleId) {
        if (TextUtils.isEmpty(captionStyleId)) {
            // 无效的样式资源id，直接返回
            return;
        }
        // 应用样式资源
        applyCaptionStyle(captionStyleId);
        updateCaptionProperties();
    }

    /**
     * 将字幕进行位置平移
     * @param translation 平移量
     */
    public void translateCaption(PointF translation) {
        if (translation == null) {
            return;
        }
        if (mNvsCaption != null) {
            mNvsCaption.translateCaption(translation);
            // 设置完偏移后要更新字幕的绝对偏移量
            mTranslation = mNvsCaption.getCaptionTranslation();
        }
    }

    /**
     * 设置字幕平移量
     * @param translation 平移量
     */
    public void setTranslation(PointF translation) {
        if (translation == null) {
            return;
        }
        mTranslation = translation;
        if (mNvsCaption != null) {
            mNvsCaption.setCaptionTranslation(translation);
        }
    }

    /**
     * 获取字幕的平移量
     */
    public PointF getTranslation() { return mTranslation; }

    /**
     * 缩放字幕
     * @param scaleFactor 缩放比例
     * @param anchor 锚点坐标
     */
    public void scaleCaption(float scaleFactor, PointF anchor) {
        if (mNvsCaption != null) {
            mNvsCaption.scaleCaption(scaleFactor, anchor);
            // 设置了缩放后更新最终缩放比例值
            mScaleX = mNvsCaption.getScaleX();
            mScaleY = mNvsCaption.getScaleY();
        }
    }

    /**
     * 旋转字幕
     *
     * @param angle 旋转角度
     * @param anchor 锚点坐标
     */
    public void rotateCaption(float angle, PointF anchor) {
        if (mNvsCaption != null) {
            if (mNvsCaption.getTextBoundingRect() != null) {
                mNvsCaption.rotateCaption(angle, anchor);
                mRotation = mNvsCaption.getRotationZ();
            }
        }
    }

    /**
     * 旋转字幕
     * @param angle 旋转角度
     */
    public void rotateCaption(float angle) {
        if (mNvsCaption != null) {
            if (mNvsCaption.getTextBoundingRect() != null) {
                mNvsCaption.rotateCaption(angle);
                // 设置选装角度后需要更新最终旋转角度值
                mRotation = mNvsCaption.getRotationZ();
            }
        }
    }

    /**
     * 设置字幕的X轴缩放比例
     * @param scaleX X轴缩放比例
     */
    public void setScaleX(float scaleX) {
        mScaleX = scaleX;
        if (mNvsCaption != null) {
            mNvsCaption.setScaleX(scaleX);
        }
    }

    /**
     * 获取X轴缩放比例
     */
    public float getScaleX() { return mScaleX; }

    /**
     * 设置Y轴缩放比例
     * @param scaleY Y轴缩放比例
     */
    public void setScaleY(float scaleY) {
        mScaleY = scaleY;
        if (mNvsCaption != null) {
            mNvsCaption.setScaleY(scaleY);
        }
    }

    /**
     * 获取字幕的Y轴缩放比例
     */
    public float getScaleY() { return mScaleY; }

    /**
     * 获取字幕的旋转角度
     */
    public float getRotation() { return mRotation; }

    /**
     * 设置字幕的旋转角度
     * @param angle 旋转角度
     */
    public void setRotation(float angle) {
        mRotation = angle;
        if (mNvsCaption != null) {
            mNvsCaption.setRotationZ(angle);
        }
    }

    /**
     * 获取字幕的原始包围矩形框变换后的顶点位置
     * @return 返回List<PointF>对象，包含四个顶点位置，依次分别对应原始边框的左上，左下，右下，右上顶点
     */
    public List<PointF> getBoundingRectangleVertices() {
        if (mNvsCaption != null) {
            int boundingType = mNvsCaption.isFrameCaption()
                    ? NvsCaption.BOUNDING_TYPE_TYPOGRAPHIC_FRAME_TEXT
                    : NvsCaption.BOUNDING_TYPE_TYPOGRAPHIC_TEXT;
            return mNvsCaption.getCaptionBoundingVertices(boundingType);
        }
        return new ArrayList<>();
    }

    /**
     * 获取字幕的原始包围矩形框变换后的顶点位置
     * @param boundingType 边框类型
     * @return 返回List<PointF>对象，包含四个顶点位置，依次分别对应原始边框的左上，左下，右下，右上顶点
     */
    public List<PointF> getCaptionBoundingVertices(int boundingType) {
        if (mNvsCaption != null) {
            return mNvsCaption.getCaptionBoundingVertices(boundingType);
        }
        return new ArrayList<>();
    }

    /**
     * 获取字幕文本矩形框
     */
    public RectF getTextBoundingRect() {
        if (mNvsCaption != null) {
            return mNvsCaption.getTextBoundingRect();
        }
        return new RectF();
    }

    /**
     * 设置字幕的z值，即轨道索引
     * @param zValue 轨道索引
     */
    public void setZValue(float zValue) {
        mZValue = zValue;
        if (mNvsCaption != null) {
            mNvsCaption.setZValue(zValue);
        }
    }

    /**
     * 获取字幕的z值，即轨道索引
     * @return 轨道索引
     */
    public float getZValue() { return mZValue; }

    public void setClipAffinityEnabled(boolean enabled) {
        mClipAffinityEnabled = enabled;
        if (mNvsCaption != null) {
            mNvsCaption.setClipAffinityEnabled(enabled);
        }
    }


    public boolean getClipAffinityEnabled() { return mClipAffinityEnabled; }

    /**
     * 设置字体大小
     * @param fontSize 字体大小
     */
    public void setFontSize(float fontSize) {
        mFontSize = fontSize;
        if ((mNvsCaption != null) && (fontSize > 0)) {
            mNvsCaption.setFontSize(fontSize);
        }
    }

    /**
     * 获取字体大小
     */
    public float getFontSize() { return mFontSize; }

    /**
     * 设置字体
     * @param fontFamily 字体
     */
    public void setFontFamily(String fontFamily) {
        mFontFamily = fontFamily;
        if (mNvsCaption != null) {
            mNvsCaption.setFontFamily(fontFamily);
        }
    }

    /**
     * 获取字体
     */
    public String getFontFamily() { return mFontFamily; }

    /**
     * 设置字体
     * @param fontFilePath 字体本地路径
     */
    public void setFontByFilePath(String fontFilePath) {
        if (mNvsCaption != null) {
            mNvsCaption.setFontByFilePath(fontFilePath);
            mFontPath = mNvsCaption.getFontFilePath();
            mFontFamily = mNvsCaption.getFontFamily();
        }
    }

    /**
     * 获取字体本地路径
     * @return 字体本地路径
     */
    public String getFontPath() { return mFontPath; }

    /**
     * 设置字体本地路径
     * @param fontPath 字体本地路径
     */
    public void setFontPath(String fontPath) {
        mFontPath = fontPath;
        setFontByFilePath(fontPath);
    }

    /**
     * 获取字体颜色
     * @return 字体颜色
     */
    public String getTextColor() { return mTextColor; }

    /**
     * 设置字体颜色
     * @param textColor 字体颜色
     */
    public void setTextColor(String textColor) {
        mTextColor = textColor;
        if ((mNvsCaption != null) && !TextUtil.isEmpty(textColor)) {
            NvsColor color = ColorUtil.colorStringToNvsColor(textColor);
            mNvsCaption.setTextColor(color);
        }
    }

    /**
     * 设置字体是否加粗
     * @param bold 是否加粗
     */
    public void setBold(boolean bold) {
        mBold = bold;
        if (mNvsCaption != null) {
            mNvsCaption.setBold(bold);
        }
    }

    /**
     * 获取字体是否加粗
     */
    public boolean getBold() { return mBold; }

    /**
     * 获取字体是否描边
     */
    public boolean getDrawOutline() { return mDrawOutline; }

    /**
     * 设置字体是否描边
     * @param drawOutline 是否描边
     */
    public void setDrawOutline(boolean drawOutline) {
        mDrawOutline = drawOutline;
        if (mNvsCaption != null) {
            mNvsCaption.setDrawOutline(drawOutline);
        }
    }

    /**
     * 获取字体描边颜色
     */
    public String getOutlineColor() { return mOutlineColor; }

    /**
     * 设置字体描边颜色
     * @param outlineColor 字体描边颜色
     */
    public void setOutlineColor(String outlineColor) {
        mOutlineColor = outlineColor;
        if ((mNvsCaption != null) && !TextUtil.isEmpty(outlineColor)) {
            NvsColor color = ColorUtil.colorStringToNvsColor(outlineColor);
            mNvsCaption.setOutlineColor(color);
        }
    }

    /**
     * 获取字体描边宽度
     */
    public float getOutlineWidth() { return mOutlineWidth; }

    /**
     * 设置字体描边宽度
     * @param outlineWidth 字体描边宽度
     */
    public void setOutlineWidth(float outlineWidth) {
        mOutlineWidth = outlineWidth;
        if (mNvsCaption != null) {
            mNvsCaption.setOutlineWidth(outlineWidth);
        }
    }

    /**
     * 设置字间距
     * @param letterSpacing 字间距
     */
    public void setLetterSpacing(float letterSpacing) {
        if ((mNvsCaption != null) && (letterSpacing > 0)) {
            mLetterSpacing = letterSpacing;
            mNvsCaption.setLetterSpacingType(NvsCaption.LETTER_SPACING_TYPE_ABSOLUTE);
            mNvsCaption.setLetterSpacing(letterSpacing);
        }
    }

    /**
     * 获取字间距
     */
    public float getLetterSpacing() { return mLetterSpacing; }

    /**
     * 获取行间距
     */
    public float getLineSpacing() { return mLineSpacing; }

    /**
     * 设置行间距
     * @param lineSpacing 行间距
     */
    public void setLineSpacing(float lineSpacing) {
        if ((mNvsCaption != null) && (lineSpacing > 0)) {
            mLineSpacing = lineSpacing;
            mNvsCaption.setLineSpacing(lineSpacing);
        }
    }

    /**
     * 设置阴影是否开启
     * @param drawShadow 是否开启
     */
    public void setDrawShadow(boolean drawShadow) {
        mDrawShadow = drawShadow;
        if (mNvsCaption != null) {
            mNvsCaption.setDrawShadow(drawShadow);
        }
    }

    /**
     * 获取是否绘制阴影
     */
    public boolean getDrawShadow() { return mDrawShadow; }

    /**
     * 设置阴影颜色
     * @param shadowColor 阴影颜色
     */
    public void setShadowColor(String shadowColor) {
        mShadowColor = shadowColor;
        if ((mNvsCaption != null) && !TextUtils.isEmpty(shadowColor)) {
            mNvsCaption.setShadowColor(ColorUtil.colorStringToNvsColor(shadowColor));
        }
    }

    /**
     * 获取阴影颜色
     * @return 返回阴影颜色
     */
    public String getShadowColor() { return mShadowColor; }

    /**
     * 设置阴影偏移量
     * @param shadowOffset 偏移量坐标点
     */
    public void setShadowOffset(PointF shadowOffset) {
        mShadowOffset = shadowOffset;
        if (mNvsCaption != null) {
            mNvsCaption.setShadowOffset(shadowOffset);
        }
    }

    /**
     * 获取阴影偏移量
     */
    public PointF getShadowOffset() { return mShadowOffset; }

    /**
     * 设置阴影羽化
     * @param shadowFeather 羽化值
     */
    public void setShadowFeather(float shadowFeather) {
        mShadowFeather = shadowFeather;
        if (mNvsCaption != null) {
            mNvsCaption.setShadowFeather(shadowFeather);
        }
    }

    /**
     * 获取阴影羽化
     */
    public float getShadowFeather() { return mShadowFeather; }

    /**
     * 设置文字透明度
     * @param opacity 透明度
     */
    public void setOpacity(float opacity) {
        if ((mNvsCaption != null) && (opacity > 0)) {
            mOpacity = opacity;
            mNvsCaption.setOpacity(opacity);
        }
    }

    /**
     * 获取文字透明度
     */
    public float getOpacity() { return mOpacity; }

    /**
     * 设置背景色
     * @param backgroundColor 背景色
     */
    public void setBackgroundColor(String backgroundColor) {
        if ((mNvsCaption != null) && !TextUtils.isEmpty(backgroundColor)) {
            mBackgroundColor = backgroundColor;
            mNvsCaption.setBackgroundColor(ColorUtil.colorStringToNvsColor(backgroundColor));
        }
    }

    /**
     * 获取背景颜色
     * @return 背景色
     */
    public  String getBackgroundColor() { return mBackgroundColor; }

    /**
     * 设置背景圆角
     * @param radius 背景圆角
     */
    public void setBackgroundRadius(float radius) {
        mBackgroundRadius = radius;
        if (mNvsCaption != null) {
            mNvsCaption.setBackgroundRadius(radius);
        }
    }

    /**
     * 获取背景圆角
     * @return 返回背景圆角
     */
    public float getBackgroundRadius() { return mBackgroundRadius; }

    /**
     * 设置字幕背景边界扩展比率
     * @param ratio 扩展比率为字体大小的倍数，默认为0.15
     */
    public void setBoundaryPaddingRatio(float ratio) {
        mBoundaryPaddingRatio = ratio;
        if (mNvsCaption != null) {
            mNvsCaption.setBoundaryPaddingRatio(ratio);
        }
    }

    /**
     * 获取字幕背景边界扩展比率
     * @return 扩展比率为字体大小的倍数，默认为0.15
     */
    public float getBoundaryPaddingRatio() { return mBoundaryPaddingRatio; }

    /**
     * 设置斜体
     * @param italic 斜体标识
     */
    public void setItalic(boolean italic) {
        mItalic = italic;
        if (mNvsCaption != null) {
            mNvsCaption.setItalic(italic);
        }
    }

    /**
     * 获取是否设置斜体
     */
    public boolean getItalic() { return mItalic; }

    /**
     * 设置下划线
     * @param underline 下划线标识
     */
    public void setUnderline(boolean underline) {
        mUnderline = underline;
        if (mNvsCaption != null) {
            mNvsCaption.setUnderline(underline);
        }
    }

    /**
     * 获取是否设置下划线
     */
    public boolean getUnderline() { return mUnderline; }

    /**
     * 获取是否设置删除线
     * @return 返回是否设置删除线
     */
    public boolean getStrikeOut() { return mStrickeOut; }

    /**
     * 设置删除线
     * @param strikeOut 删除线标识
     */
    public void setStrikeOut(boolean strikeOut) {
        mStrickeOut = strikeOut;
        if (mNvsCaption != null) {
            mNvsCaption.setStrikeOut(strikeOut);
        }
    }

    @CaptionAlignment
    public int getCationAlignment() {
        return super.getCationAlignment();
    }

    /**
     * 设置字幕对齐方式
     * @param cationAlignment 字幕对齐方式
     */
    public void setCaptionAlignment(@CaptionAlignment int cationAlignment) {
        if (mNvsCaption != null) {
            mNvsCaption.setTextAlignment(cationAlignment);
        }
    }

    /**
     * 重建美摄字幕，主要用于操作栈undo、redo操作后重建字幕
     * @param timeline 时间轴
     */
    public NvsTimelineCaption rebuild(ITimeline timeline) {
        CaptionParams captionParams = new CaptionParams(mInTime, mOutTime)
                .withText(mText)
                .withTrackIndex(mTrackIndex)
                .withCaptionType(getCaptionType());
        NvsTimelineCaption nvsCaption = timeline.rebuildCaption(captionParams);
        if (nvsCaption == null) {
            GLog.e(TAG, LogFlag.DL, "[rebuild] nvsCaption is null");
            return null;
        }
        nvsCaption.setRecordingUserOperation(false);
        mNvsCaption = nvsCaption;
        // 重新从实例对象存的字幕属性值通过set操作设置在重建后的NvsTimelineCaption上
        applyCaptionStyle(mCaptionStyleId);
        setTranslation(mTranslation);
        setScaleX(mScaleX);
        setScaleY(mScaleY);
        setRotation(mRotation);
        setCaptionAlignment(getCationAlignment());
        setCaptionId(getCaptionId());
        setBold(mBold);
        setItalic(mItalic);
        setUnderline(mUnderline);
        setStrikeOut(mStrickeOut);
        setTextColor(mTextColor);
        setDrawOutline(mDrawOutline);
        setOutlineColor(mOutlineColor);
        setOutlineWidth(mOutlineWidth);
        setDrawShadow(mDrawShadow);
        setShadowColor(mShadowColor);
        setShadowFeather(mShadowFeather);
        setShadowOffset(mShadowOffset);
        setBackgroundColor(mBackgroundColor);
        setBackgroundRadius(mBackgroundRadius);
        setBoundaryPaddingRatio(mBoundaryPaddingRatio);
        setFontSize(mFontSize);
        setFontPath(mFontPath);
        return nvsCaption;
    }

    /**
     * 设置字幕所在的轨道位置索引
     * @param mTrackIndex 轨道位置索引
     */
    @Override
    public void setTrackIndex(int mTrackIndex) {
        // 调父类BaseVideoTimelineEffect的setTrackIndex方法将字幕的轨道索引值（1）缓存到父类中，供取字幕在轨道1中的位置显示
        super.setTrackIndex(mTrackIndex);
        setZValue(mTrackIndex);
    }

    /**
     * 应用字幕样式
     * @param styleAssertId 样式安装后的id
     */
    @Override
    public void applyCaptionStyle(String styleAssertId) {
        if (mNvsCaption != null) {
            int captionType = getCaptionType();
            switch (captionType) {
                case CaptionType.TYPE_MODULAR_CONTEXT_CAPTION:
                    mNvsCaption.applyModularCaptionContext(styleAssertId);
                    break;
                case CaptionType.TYPE_MODULAR_RENDERER_CAPTION:
                    mNvsCaption.applyModularCaptionRenderer(styleAssertId);
                    break;
                case CaptionType.TYPE_NORMAL_CAPTION:
                    mNvsCaption.applyCaptionStyle(styleAssertId);
                    break;
                case CaptionType.TYPE_MODULAR_ANIMATION_CAPTION:
                    mNvsCaption.applyModularCaptionAnimation(styleAssertId);
                    break;
                case CaptionType.TYPE_MODULAR_IN_ANIMATION_CAPTION:
                    mNvsCaption.applyModularCaptionInAnimation(styleAssertId);
                    break;
                case CaptionType.TYPE_MODULAR_OUT_ANIMATION_CAPTION:
                    mNvsCaption.applyModularCaptionOutAnimation(styleAssertId);
                    break;
                default:
                    mNvsCaption.applyCaptionStyle(styleAssertId);
                    break;
            }
            // 存储当前生效的样式资源id
            mCaptionStyleId = styleAssertId;
        }
    }

    /**
     * 拷贝一个字幕添加到时间轴上
     *
     * @param timeline 创建字幕的时间轴
     * @param type     字幕类型，可从普通类型字幕复制出一个拼装类型字幕
     */
    public BaseCaption copy(ITimeline timeline, int type) {
        if (timeline == null) {
            return null;
        }
        CaptionParams captionParams = new CaptionParams(mInTime, mOutTime)
                .withText(mText)
                .withTrackIndex(getTrackIndex())
                .withCaptionType(type);
        return copy(timeline, type, captionParams);
    }

    /**
     * 拷贝一个字幕添加到时间轴上
     *
     * @param timeline 创建字幕的时间轴
     * @param type     字幕类型，可从普通类型字幕复制出一个拼装类型字幕
     * @param params   字幕参数
     * @return 拷贝的字幕
     */
    public BaseCaption copy(ITimeline timeline, int type, CaptionParams params) {
        if (params != null) {
            BaseCaption caption = timeline.appendCaption(params);
            boolean typeChanged = (type != getCaptionType());
            // 如果字幕类型相同才拷贝其他的属性，避免字幕类型不同导致出现显示异常
            updateCaptionProperties();
            if ((caption != null) && (!typeChanged)) {
                // 拷贝字幕是否为默认字幕标志
                caption.setIsDefaultCaption(getIsDefaultCaption());
                caption.applyCaptionStyle(mCaptionStyleId);
                caption.setTranslation(mTranslation);
                caption.setScaleX(mScaleX);
                caption.setScaleY(mScaleY);
                caption.setRotation(mRotation);
                caption.setCaptionAlignment(getCationAlignment());
                caption.setCaptionId(getCaptionId());
                caption.setBold(mBold);
                caption.setItalic(mItalic);
                caption.setUnderline(mUnderline);
                caption.setTextColor(mTextColor);
                caption.setDrawOutline(mDrawOutline);
                caption.setOutlineColor(mOutlineColor);
                caption.setOutlineWidth(mOutlineWidth);
                caption.setDrawShadow(mDrawShadow);
                caption.setShadowColor(mShadowColor);
                caption.setShadowFeather(mShadowFeather);
                caption.setShadowOffset(mShadowOffset);
                caption.setBackgroundColor(mBackgroundColor);
                caption.setBackgroundRadius(mBackgroundRadius);
                caption.setBoundaryPaddingRatio(mBoundaryPaddingRatio);
                caption.setFontPath(mFontPath);
                caption.setFontSize(mFontSize);
                caption.setFontFamily(mFontFamily);
                caption.setStyleId(getStyleId());
                caption.setFontId(getFontId());
            }
            return caption;
        }
        return null;
    }

    /**
     * 拷贝一个字幕添加到时间轴上
     *
     * @param timeline 创建字幕的时间轴
     */
    public BaseCaption copy(ITimeline timeline) {
        return copy(timeline, getCaptionType());
    }

    public BaseCaption copy(ITimeline timeline, CaptionParams params) {
        return copy(timeline, getCaptionType(), params);
    }

    /**
     * 提取nvsTimelineCaption的值来更新字幕属性信息
     */
    private void updateCaptionProperties() {
        if (mNvsCaption != null) {
            if (mNvsCaption.getText() != null) { mText = mNvsCaption.getText(); }
            if (mNvsCaption.getCaptionTranslation() != null) { mTranslation = mNvsCaption.getCaptionTranslation(); }
            if (mNvsCaption.getCaptionStylePackageId() != null) { mCaptionStyleId = mNvsCaption.getCaptionStylePackageId(); }
            mRotation = mNvsCaption.getRotationZ();
            mScaleX = mNvsCaption.getScaleX();
            mScaleY = mNvsCaption.getScaleY();
            mFontSize = mNvsCaption.getFontSize();
            if (mNvsCaption.getFontFamily() != null) { mFontFamily = mNvsCaption.getFontFamily(); }
            if (mNvsCaption.getFontFilePath() != null) { mFontPath = mNvsCaption.getFontFilePath(); }
            mTextColor = ColorUtil.nvsColorToHexString(mNvsCaption.getTextColor());
            mBold = mNvsCaption.getBold();
            mItalic = mNvsCaption.getItalic();
            mUnderline = mNvsCaption.getUnderline();
            mDrawOutline = mNvsCaption.getDrawOutline();
            mOutlineColor = ColorUtil.nvsColorToHexString(mNvsCaption.getOutlineColor());
            mOutlineWidth = mNvsCaption.getOutlineWidth();
            mDrawShadow = mNvsCaption.getDrawShadow();
            mShadowColor = ColorUtil.nvsColorToHexString(mNvsCaption.getShadowColor());
            mShadowFeather = mNvsCaption.getShadowFeather();
            if (mNvsCaption.getShadowOffset() != null) { mShadowOffset = mNvsCaption.getShadowOffset(); }
            mBackgroundColor = ColorUtil.nvsColorToHexString(mNvsCaption.getBackgroundColor());
            mBackgroundRadius = mNvsCaption.getBackgroundRadius();
            mBoundaryPaddingRatio = mNvsCaption.getBoundaryPaddingRatio();
            mStrickeOut = mNvsCaption.getStrikeOut();
        }
    }
}
