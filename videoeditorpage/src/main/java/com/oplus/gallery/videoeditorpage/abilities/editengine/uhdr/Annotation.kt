/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Annotation.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.uhdr

import androidx.annotation.IntDef

/**
 * UHDR 引擎类型
 * VIDEO -> 视频抽帧合成 UHDR
 * OLIVE -> livePhoto 抽帧合成 UHDR
 * INVALID -> 初始化非法值，实际使用处需要覆盖此初始化值
 */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.TYPE, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@IntDef(
    UhdrEngineType.VIDEO,
    UhdrEngineType.OLIVE,
    UhdrEngineType.INVALID
)
annotation class UhdrEngineType {
    companion object {
        const val INVALID = -1
        const val VIDEO = 1
        const val OLIVE = 2
    }
}

/**
 * 合成 UHDR 任务中错误码
 * INVALID_TYPE -> 非法的调用类型
 * GET_HDR_FRAME_ERROR -> 抽取 HDR 帧错误
 * GET_SDR_FRAME_ERROR -> 抽取 SDR 帧错误
 * GENERATE_GAIN_ERROR -> 合成增益图错误
 * COMBINE_UHDR_ERROR -> 合成 UHDR 图错误
 * SUCCESS -> 全部流程成功
 */
@Retention(AnnotationRetention.SOURCE)
@IntDef(
    UhdrErrorMessage.GET_HDR_FRAME_ERROR,
    UhdrErrorMessage.GET_SDR_FRAME_ERROR,
    UhdrErrorMessage.INVALID_TYPE,
    UhdrErrorMessage.GENERATE_GAIN_ERROR,
    UhdrErrorMessage.COMBINE_UHDR_ERROR,
    UhdrErrorMessage.SUCCESS
)

annotation class UhdrErrorMessage {
    companion object {
        const val INVALID_TYPE = 0
        const val GET_HDR_FRAME_ERROR = 1
        const val GET_SDR_FRAME_ERROR = 2
        const val GENERATE_GAIN_ERROR = 3
        const val COMBINE_UHDR_ERROR = 4
        const val SUCCESS = 5

        fun toString(type: Int): String {
            return when (type) {
                INVALID_TYPE -> "invalid type"
                GET_HDR_FRAME_ERROR -> "get hdr frame error"
                GET_SDR_FRAME_ERROR -> "get sdr frame error"
                GENERATE_GAIN_ERROR -> "generate gain map error"
                COMBINE_UHDR_ERROR -> "combine uhdr error"
                SUCCESS -> "success"
                else -> ""
            }
        }
    }
}