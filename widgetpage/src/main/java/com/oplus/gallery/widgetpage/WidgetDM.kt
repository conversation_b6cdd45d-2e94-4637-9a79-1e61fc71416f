/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetDM.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/29        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage

import android.app.Application
import android.content.Context
import com.oplus.gallery.business_lib.api.IWidgetDM
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.local.set.WidgetAlbum
import com.oplus.gallery.foundation.util.collections.ExtraMap
import com.oplus.gallery.router_lib.annotations.Component
import com.oplus.gallery.widgetpage.br.WidgetBackupRestore
import com.oplus.gallery.widgetpage.ext.getCardMode
import com.oplus.gallery.widgetpage.ext.getWidgetInfo
import com.oplus.gallery.widgetpage.helper.WidgetDatabaseHelper
import com.oplus.gallery.widgetpage.track.WidgetTrackHelper
import com.oplus.gallery.widgetpage.ui.cardwidget.item.WidgetViewGetter

@Component(interfaceName = "com.oplus.gallery.business_lib.api.IWidgetDM")
class WidgetDM : IWidgetDM {

    private val widgetDatabaseHelper by lazy { WidgetDatabaseHelper() }

    override fun addWidgetToDatabase(widgetCode: String) = widgetDatabaseHelper.addWidget(widgetCode)

    override fun getCardMode(widgetCode: String): Int = widgetCode.getCardMode()

    override fun removeWidgetFromDatabase(widgetCode: String) = WidgetDatabaseHelper().removeWidget(widgetCode)

    override fun generateRecommendedDisplayListIfNeeded() {
        WidgetDatabaseHelper().generateRecommendedDisplayListIfNeeded()
    }

    override fun setAllWidgetDirty() {
        widgetDatabaseHelper.setAllWidgetDirty()
    }

    override fun notifyWidgetDisplayed(
        widgetCode: String,
        displayListId: String,
        currentFilePath: String?
    ) {
        widgetDatabaseHelper.run {
            saveWidgetDisplayedContent(widgetCode, currentFilePath)
            deleteInvalidDisplayList(displayListId)
        }
    }

    override fun getWidgetInfo(context: Context, path: Path): ExtraMap? {
        return (DataManager.getMediaSet(path) as? WidgetAlbum)?.getWidgetInfo()
    }

    override fun removeFromDisplayList(filePathList: List<String>, displayListId: String): Boolean {
        return widgetDatabaseHelper.removeFromDisplayList(filePathList, displayListId)
    }

    override fun addToDisplayListByGalleryId(galleryIds: List<Int>, displayListId: String): Int {
        return widgetDatabaseHelper.addToDisplayListByGalleryId(galleryIds, displayListId)
    }

    override fun queryRecommendWidgetItemCount(): Int = widgetDatabaseHelper.queryRecommendWidgetItemCount()

    override fun queryWidgetItemCount(displayListId: String): Int = widgetDatabaseHelper.queryWidgetItemCount(displayListId)

    override fun updateWidgetMode(widgetCode: String, mode: Int): Boolean = widgetDatabaseHelper.updateWidgetMode(widgetCode, mode)

    override fun backup(): String = WidgetBackupRestore().backup()

    override fun restore(json: String): Boolean = WidgetBackupRestore().restore(json)

    override fun notifyFileRemoved(filePathList: List<String>) {
        widgetDatabaseHelper.notifyFileRemoved()
    }

    override fun trackWidgetClick(mode: Int, itemPath: Path) {
        WidgetTrackHelper.trackWidgetClick(mode, itemPath)
    }

    override fun getWidgetCount(mode: Int): Int {
        return widgetDatabaseHelper.getWidgetCount(mode)
    }

    override fun getEachWidgetDisplayCountOfDate(dayOffset: Int): List<Int> {
        return widgetDatabaseHelper.getEachWidgetDisplayCountOfDate(dayOffset)
    }

    override fun getEachCustomDisplayListSize(): List<Int> {
        return widgetDatabaseHelper.getEachCustomDisplayListSize()
    }

    override fun getExcludedRecommendedDisplayListSize(): Int {
        return widgetDatabaseHelper.getExcludedRecommendedDisplayListSize()
    }

    override fun forceUpdate(application: Application, widgetCode: String) {
        WidgetViewGetter.getWidgetView(application, widgetCode).forceUpdate()
    }
}