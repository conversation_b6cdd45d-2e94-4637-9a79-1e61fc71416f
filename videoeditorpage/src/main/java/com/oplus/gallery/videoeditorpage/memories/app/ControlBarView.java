/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ControlBarView.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/22
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/22    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.app;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.coui.appcompat.tooltips.COUIToolTips;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility;
import com.oplus.gallery.foundation.uikit.app.ZoomWindowManager;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.business.preview.EditorPreviewState;
import com.oplus.gallery.videoeditorpage.memories.business.preview.EditorPreviewUIController;


public class ControlBarView extends FrameLayout implements EditorPreviewUIController.OnBottomTitleClickListener {
    private static final String TAG = "ControlBarView";

    private EditorBaseState mCurrentState;
    private EditorBaseState mFirstEnterState;
    private OnCancelDoneClickListener mOnCancelDoneClickListener;
    private IVideoEditAbility mEngineManager;
    private ZoomWindowManager mZoomWindowManager;

    private AppUiResponder.AppUiConfig mUiConfig;
    private int mToolBarContainerPadding = 0;
    private ViewGroup mEditorVideoView;
    private View mTimeSeekBar;

    public ControlBarView(Context context) {
        this(context, null);
    }

    public ControlBarView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ControlBarView(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public ControlBarView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void setEngineManager(IVideoEditAbility manager) {
        mEngineManager = manager;
    }

    public IVideoEditAbility getEngineManager() {
        return mEngineManager;
    }

    public void setZoomWindowManager(ZoomWindowManager zoomWindowManager) {
        mZoomWindowManager = zoomWindowManager;
    }

    public ZoomWindowManager getZoomWindowManager() {
        return mZoomWindowManager;
    }

    /**
     * 回忆编辑入口
     */
    public void enterEditor(IVideoEditAbility manager) {
        GLog.d(TAG, "enterEditor mCurrentState = " + mCurrentState + ", mFirstEnterState = " + mFirstEnterState);
        if (mFirstEnterState == null) {
            mEngineManager = manager;
            mFirstEnterState = new EditorPreviewState(getContext(), this);
        }
        EditorPreviewUIController controller = (EditorPreviewUIController) mFirstEnterState.getUIController();
        /*
        因为ControlBarView 与 回忆播放界面同生命周期，但是从回忆播放界面进入编辑是有动画的，故当进入时
        changeState相关layout位置的地方进入时不做layout相关，避免闪的问题
        */
        controller.setIsHasEntryAnimation(true);
        changeState(mFirstEnterState);
        controller.setIsHasEntryAnimation(false);
    }

    public void exitEditor() {
        if (mFirstEnterState != null) {
            mFirstEnterState.onDestroy();
        }
        if (mCurrentState != null) {
            mCurrentState.onDestroy();
        }
    }

    public EditorBaseState getCurrentEditor() {
        return mCurrentState;
    }

    public void onUIDataSetChanged() {
        if (mCurrentState != null) {
            mCurrentState.getUIController().onDataSetChanged();
        }
    }

    public void stop() {
        //Do nothing
    }

    public void setActionDoneEnable(boolean enable) {
        if (mCurrentState != null) {
            mCurrentState.setActionDoneEnable(enable);
        }
    }

    public void changeToFirstEnterState() {
        changeState(mFirstEnterState);
    }

    public void changeState(EditorBaseState state) {
        if (state == null) {
            return;
        }
        if (mCurrentState != null) {
            if (mCurrentState.equals(state)) {
                //same state
                GLog.d(TAG, "changeState same state mCurrentState = " + mCurrentState + ", state = " + state);
                return;
            } else {
                GLog.d(TAG, "changeState hide mCurrentState = " + mCurrentState + ", state = " + state);
                mCurrentState.hide(true);
                mCurrentState.onDestroy();
            }
        }

        /**
         * 记录切换前的state，切换时是否需要做动画
         */
        boolean isLastStateNeedAnimator = (mCurrentState != null) && mCurrentState.isNeedAnimator();
        mCurrentState = state;
        state.show(isNeedAnimator(isLastStateNeedAnimator, mCurrentState.isNeedAnimator()),
                isNeedMenuAnimator(isLastStateNeedAnimator));
        state.getUIController().setOnBottomTitleClickListener(this);
    }

    public void destroy() {
        exitEditor();
    }

    public void hidePreviewState() {
        if (mFirstEnterState != null) {
            mFirstEnterState.hide(false, true);
        }
    }

    public boolean isPreviewState() {
        GLog.d(TAG, "isPreviewState mCurrentState = " + mCurrentState + ", mFirstEnterState = " + mFirstEnterState);
        return (mCurrentState != null) && mCurrentState.equals(mFirstEnterState);
    }

    public boolean onBackPressed() {
        if (mCurrentState == null) {
            GLog.w(TAG, "onBackPressed mCurrentState is null");
            return false;
        }

        if (isPreviewState()) {
            GLog.d(TAG, "onBackPressed hide mCurrentState = " + mCurrentState + ", mFirstEnterState = " + mFirstEnterState);
            mCurrentState = null;
            return false;
        } else {
            return mCurrentState.onBackPressed();
        }
    }

    public boolean onVideoEditorBack() {
        if (mCurrentState == null) {
            GLog.w(TAG, "onBackPressed mCurrentState is null");
            return false;
        }
        if (isPreviewState()) {
            return false;
        } else {
            return mCurrentState.onBackPressed();
        }
    }

    @Override
    public void onBottomTitleClick(View view) {
        if (DoubleClickUtils.isFastDoubleClick()) {
            return;
        }
        int id = view.getId();
        if (id == R.id.editor_id_action_cancel) {
            GLog.d(TAG, "onBottomTitleClick editor_id_action_cancel");
            if (mOnCancelDoneClickListener != null) {
                mOnCancelDoneClickListener.onCancelClick(view);
            }
        } else if (id == R.id.editor_id_action_done) {
            GLog.d(TAG, "onBottomTitleClick editor_id_action_done");
            if (mOnCancelDoneClickListener != null) {
                mOnCancelDoneClickListener.onDoneClick();
            }

        } else if (id == R.id.editor_id_text_action_cancel) {
            GLog.d(TAG, "onBottomTitleClick editor_id_text_action_cancel");
            if (mCurrentState != null) {
                mCurrentState.cancel();
            }
            if (!isPreviewState()) {
                changeToFirstEnterState();
            }
        } else if (id == R.id.editor_id_text_action_done) {
            GLog.d(TAG, "onBottomTitleClick editor_id_text_action_done");
            boolean shouldQuitAfterDone = true;
            if (mCurrentState != null) {
                shouldQuitAfterDone = mCurrentState.done();
            }
            if (!isPreviewState() && shouldQuitAfterDone) {
                changeToFirstEnterState();
            }
        } else if (id == R.id.editor_img_action_left) {
            GLog.d(TAG, "onBottomTitleClick editor_img_action_left");
            if (mCurrentState != null) {
                mCurrentState.click(view);
            }
        } else if (id == R.id.editor_img_action_right) {
            GLog.d(TAG, "onBottomTitleClick editor_img_action_right");
            if (mCurrentState != null) {
                mCurrentState.click(view);
            }
        } else if (id == R.id.editor_id_title) {
            GLog.d(TAG, "onBottomTitleClick editor_id_title");
            if (mCurrentState != null) {
                mCurrentState.click(view);
            }
        } else if (id == R.id.video_music_on_off) {
            GLog.d(TAG, "onBottomTitleClick video_music_on_off");
            if (mCurrentState != null) {
                mCurrentState.click(view);
            }
        } else if (id == R.id.editor_btn_play_and_time) {
            GLog.d(TAG, "onBottomTitleClick editor_btn_play_and_time");
            if (mCurrentState != null) {
                mCurrentState.click(view);
            }
        }
    }

    public void updateAppUiConfig(AppUiResponder.AppUiConfig uiConfig) {
        mUiConfig = uiConfig;
        if (getCurrentEditor() != null) {
            getCurrentEditor().onAppUiStateChanged(mUiConfig);
        }
    }

    public AppUiResponder.AppUiConfig getAppUiConfig() {
        return mUiConfig;
    }

    public interface OnCancelDoneClickListener {
        void onCancelClick(View view);

        void onDoneClick();
    }

    public void setCancelDoneClickListener(OnCancelDoneClickListener listener) {
        mOnCancelDoneClickListener = listener;
    }

    public void setToolBarContainerPadding(int padding) {
        mToolBarContainerPadding = padding;
    }

    public int getToolBarContainerPadding() {
        return mToolBarContainerPadding;
    }

    public ViewGroup getEditorVideoView() {
        return mEditorVideoView;
    }

    public void setEditorVideoView(ViewGroup editorVideoView) {
        this.mEditorVideoView = editorVideoView;
    }

    public void setTimeSeekBar(View timeSeekBar) {
        this.mTimeSeekBar = timeSeekBar;
    }

    public View getTimeSeekBar() {
        return mTimeSeekBar;
    }

    /**
     * 是否需要做动画，包含：<p>
     * 1.视频预览view的动画 <p>
     * 2.时间显示view的动画 <p>
     * 当满足条件：<p>
     * 1.中大屏（w >= 600dp）<p>
     * 2.进入 视频-编辑 的剪辑/特效/文本，或进入回忆-编辑的时长 <p>
     * 3.从剪辑/特效/文本返回 视频-编辑 一级页面，或从时长返回 回忆-编辑 一级页面 <p>
     * 时，返回true；否则返回false
     */
    private boolean isNeedAnimator(boolean isLastStateNeedAnimator, boolean isCurrentStateNeedAnimator) {
        return EditorUIConfig.isEditorLandscape(getAppUiConfig())
                && (isLastStateNeedAnimator || isCurrentStateNeedAnimator);
    }

    /**
     * 右侧菜单栏是否需要做动画 <p>
     * 当满足条件：<p>
     * 1.中大屏（w >= 600dp）<p>
     * 2.从剪辑/特效/文本返回 视频-编辑 一级页面，或从时长返回 回忆-编辑 一级页面 <p>
     * 时，返回true；否则返回false
     */
    private boolean isNeedMenuAnimator(boolean isLastStateNeedAnimator) {
        return EditorUIConfig.isEditorLandscape(getAppUiConfig())
                && isLastStateNeedAnimator;
    }

}
