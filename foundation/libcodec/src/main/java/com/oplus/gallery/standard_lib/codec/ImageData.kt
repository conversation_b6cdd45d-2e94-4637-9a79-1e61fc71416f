/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - ImageData.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/08
 ** Author      : YongQi.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/08  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec

import android.graphics.Bitmap
import android.graphics.ColorSpace
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * Image Data for picture
 */
open class ImageData() {
    var isYuv: Boolean = false
    var frameWidth: Int = 0
    get() {
        bitmap?.let {
            return it.width
        }
        return field
    }
    var frameHeight: Int = 0
    get() {
        bitmap?.let {
            return it.height
        }
        return field
    }
    var renderer: NativeRenderer? = null

    constructor(originBitmap: Bitmap?) : this() {
        bitmap = originBitmap
    }

    var bitmap: Bitmap? = null
        set(value) {
            field = value
            field?.let {
                isYuv = false
                frameWidth = it.width
                frameHeight = it.height
            }
        }

    open fun isRecycled(): Boolean {
        return bitmap?.isRecycled ?: false
    }

    open fun recycle() {
    }

    open fun getConfig(): Bitmap.Config? {
        return bitmap?.config
    }

    open fun getColorSpace(): ColorSpace? {
        if (bitmap?.isRecycled == true) {
            GLog.w(javaClass.name, "getColorSpace(): bitmap is recycled")
        }
        return bitmap?.takeIf { it.isRecycled.not() }?.colorSpace
    }
}