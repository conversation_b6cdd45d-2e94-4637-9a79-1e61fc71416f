/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseTransition.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.transition;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoEffect;

public class BaseTransition extends BaseVideoEffect {
    public static final int TYPE_PACKAGED_FX = 0;
    public static final int TYPE_BUILT_IN_FX = 1;

    public BaseTransition(String name) {
        super(name);
    }
}
