/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CaptionTextView.kt
 ** Description: 边框编辑文字操作类
 ** Version: 1.0
 ** Date : 2023/11/07
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>       <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>      2023/10/24        1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.border.caption

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.PointF
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.view.MotionEvent
import androidx.core.graphics.withSave
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photoeditor.base.processor.entry.ColorSizeEntry
import com.oplus.gallery.photoeditor.common.SimpleEditTextDialog
import com.oplus.gallery.photoeditor.common.drawable.EditableDrawable
import com.oplus.gallery.photoeditor.common.drawable.OperationStep
import com.oplus.gallery.photoeditor.common.drawable.TextDrawable
import com.oplus.gallery.photoeditor.common.drawable.TextOperationStep
import com.oplus.gallery.photoeditor.frame.data.OperationStack
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizerView2
import kotlin.math.roundToInt

internal class CaptionTextView(context: Context, private val captionConfig: BorderCaptionConfig) : GestureRecognizerView2(context),
    OperationStack.OnOperationStackChangeListener<TextOperationStep>, TextDrawable.ForceChangeOpStack, IBorderTextView,
    SimpleEditTextDialog.OnTextSubmitListener {
    private var showDeviceName: Boolean = true
    private var opType: OperationStep.OperateType = OperationStep.OperateType.CREATE
    private var focusOpStep: TextOperationStep? = null
    private var tempOpStep: TextOperationStep? = null
    private val imageMatrix = Matrix()
    private var delIconDrawable: Drawable? = null
    private val currTouchPoint: PointF = PointF()
    private val preTouchPoint: PointF = PointF()
    private var touchPoint = PointF()
    private var currentTextDrawable: TextDrawable? = null
    private var iconSize = 0f
    private val invalidPoint = PointF(-1f, -1f)
    private var defaultText: String = EMPTY_STRING
    private var downX = INVALID_NUMBER
    private var downY = INVALID_NUMBER

    /**
     * 控件状态无效时触发，通知外部刷新
     */
    var onInvalid: (() -> Unit)? = null

    override fun calculateTouchPoint(preViewBound: RectF?) {
        val bound = preViewBound ?: currentDrawingOutBound
        val middleX = bound.left + (bound.right - bound.left) / MathUtils.TWO_F
        // 预览图片与实际原图的比例
        val ratio: Float = bound.width() / captionConfig.srcWidth
        captionConfig.previewRatio = ratio
        currentTextDrawable = createTextDrawable(preViewBound)
        val startPointX = middleX - ((currentTextDrawable?.defaultDrawableWidth ?: 0) / MathUtils.TWO_F)
        val startPointY = bound.bottom - captionConfig.captionBackgroundHeight * ratio
        currTouchPoint.set(startPointX, startPointY)
        preTouchPoint.set(currTouchPoint)
        touchPoint.set(currTouchPoint)
        GLog.d(
            TAG,
            "[calculateTouchPoint] srcHeight:${captionConfig.srcWidth} " +
                "captionHeight:${captionConfig.captionBackgroundHeight} " +
                "ratio: $ratio; mDrawingOutBound:$currentDrawingOutBound " +
                "mCurrTouchPoint: $currTouchPoint;"
        )
    }

    /**
     * 删除按钮
     * @param icon 删除按钮Drawable
     */
    fun setIcons(icon: Drawable) {
        delIconDrawable = icon
    }

    /**
     * 创建TextView
     * @param bound 预览图位置
     * @param needInvalidate 是否需要触发绘制
     */
    fun createAndSelectTextView(bound: RectF? = null, needInvalidate: Boolean = true) {
        opType = OperationStep.OperateType.CREATE
        calculateTouchPoint(bound)
        focusOpStep = currentTextDrawable?.peekOperationStep() as TextOperationStep
        focusOpStep.takeIf { focusOpStep != null }?.let {
            it.startPoint = currTouchPoint
            it.update(opType, currTouchPoint, preTouchPoint, true)
            it.mParent.selected = true
            it.setBoldTextState(ColorSizeEntry.BoldStateEntry.NORMAL)
            if (needInvalidate) {
                onInvalid?.invoke()
            }
        }
        // 并不需要所有情况都重新创建，只有点击删除按钮/文字输入后/预览图变化需要重新创建，默认情况下只要同一个step即可
        tempOpStep = focusOpStep
    }

    /**
     * 更新设备名称显示
     * @param isShow 是否显示，true:显示；false:不显示
     */
    fun updateDeviceNameVisible(isShow: Boolean) {
        showDeviceName = isShow
        onInvalid?.invoke()
    }

    /**
     * 横竖屏切换的时候需要刷新ui, 由于变化期间这个预览图是动画变化过程，所以这个bound会一直变化
     * 这里做一个delay，等bound不变后再显示，不然会出现坐标计算错误
     */
    fun refreshSize() {
        GLog.d(TAG, "[refreshSize] needRefreshSize = true")
        focusOpStep = null
        postDelayed({
            createAndSelectTextView()
        }, DISPLAY_DELAY)
    }

    override fun createTextDrawable(preViewBound: RectF?): TextDrawable {
        val textDrawable = CaptionTextDrawable(context, EditableDrawable.DrawableType.TEXT, imageMatrix, defaultText, captionConfig)
        textDrawable.initData()
        textDrawable.setIDrawingOutBound {
            return@setIDrawingOutBound preViewBound ?: currentDrawingOutBound
        }
        textDrawable.resetTextSize()
        textDrawable.setDelIcon(delIconDrawable)
        textDrawable.setForceChangeOpStackListener(this)
        textDrawable.setTextSubmitListener(this)
        return textDrawable
    }

    override fun forceInvalidate() {
        onInvalid?.invoke()
    }

    /**
     * 删除按钮大小
     */
    fun setOpIconSize(size: Float) {
        iconSize = size
    }

    override fun onDraw(canvas: Canvas) {
        canvas.clipRect(currentDrawingOutBound)
        canvas.withSave {
            if (showDeviceName) {
                (currentTextDrawable as CaptionTextDrawable).drawDeviceName(canvas)
            }
            focusOpStep?.mParent?.draw(canvas, focusOpStep, true)
        }
    }

    override fun onUpOrCancel(event: MotionEvent): Boolean {
        if (opType == OperationStep.OperateType.INVALID) {
            return true
        }

        // 抬手的时候才弹出输入框
        if (opType == OperationStep.OperateType.TRANSLATE) {
            focusOpStep?.mParent?.selected = true
        }
        onInvalid?.invoke()
        return true
    }

    override fun onDown(e: MotionEvent): Boolean {
        if (animationProperties.animationState.isAnimating) {
            opType = OperationStep.OperateType.INVALID
            return true
        }
        if (!currentDrawingOutBound.contains(e.x, e.y)) {
            currTouchPoint.set(invalidPoint)
            preTouchPoint.set(invalidPoint)
            opType = OperationStep.OperateType.INVALID
            GLog.d(TAG, "[onDown], out of image outBound. x = " + e.x + ", y = " + e.y + ", outBound = " + currentDrawingOutBound)
            return true
        }
        downX = e.x
        downY = e.y

        currTouchPoint[downX] = downY
        preTouchPoint.set(currTouchPoint)
        opType = getCurrentOperationType(focusOpStep)

        if (opType == OperationStep.OperateType.CREATE) {
            if (tempOpStep == null) {
                createAndSelectTextView()
            } else {
                focusOpStep?.mParent?.selected = true
            }
            focusOpStep = tempOpStep
        } else if (opType == OperationStep.OperateType.OUT_BOUNDS) {
            focusOpStep?.mParent?.selected = false
            if (focusOpStep?.canAddToStack() == false) {
                focusOpStep = null
            }
        } else if (opType == OperationStep.OperateType.DELETE) {
            focusOpStep?.text = EMPTY_STRING
            defaultText = EMPTY_STRING
            (focusOpStep?.mParent as TextDrawable).let {
                it.clearDrawingStaticLayout()
                it.clearText()
            }
            clearFocusDrawableOpStep()
            tempOpStep = null
        }
        focusOpStep?.updateOpStep(opType)
        return true
    }

    private fun clearFocusDrawableOpStep() {
        focusOpStep?.mParent?.selected = false
        focusOpStep = null
    }

    /**
     * 点击down的时候判断触点区域
     */
    private fun getCurrentOperationType(step: TextOperationStep?): OperationStep.OperateType {
        if (step == null) {
            return OperationStep.OperateType.CREATE
        }
        return if (step.pointOnDelIcon(currTouchPoint.x, currTouchPoint.y, iconSize)) {
            OperationStep.OperateType.DELETE
        } else if (step.mParent.inOuterRect(currTouchPoint)) {
            OperationStep.OperateType.TRANSLATE
        } else {
            OperationStep.OperateType.OUT_BOUNDS
        }
    }

    /**
     * 点击保存执行此方法，将文字和设备名称水印绘制到原图中
     * @param srcBitmap 带上下黑边的原图
     */
    fun save(srcBitmap: Bitmap?): Bitmap? {
        if (srcBitmap != null && !srcBitmap.isRecycled) {
            // 这里由于横屏下或者图片很小那么生成的文字就很小，放大绘制到原图上像素就非常低，导致文字失真，所以保存的时候用原图大小作预览图计算
            val targetRect = RectF()
            targetRect.set(0f, 0f, srcBitmap.width.toFloat(), srcBitmap.height.toFloat())
            createAndSelectTextView(targetRect, false)
            val textCacheBitmap =
                Bitmap.createBitmap(targetRect.width().roundToInt(), targetRect.height().roundToInt(), Bitmap.Config.ARGB_8888)
            val textCanvas = Canvas(textCacheBitmap)
            textCanvas.withSave {
                // 如果没有输入任何文字，不需要绘制到图片上
                focusOpStep?.text = defaultText
                if (!focusOpStep?.text.isNullOrEmpty()) {
                    currentTextDrawable?.draw(textCanvas, false)
                }
                // 绘制设备名称
                if (showDeviceName) {
                    (currentTextDrawable as CaptionTextDrawable).drawDeviceName(textCanvas)
                }
            }
            val localBitmap = if (!srcBitmap.isMutable) {
                srcBitmap.copy(Bitmap.Config.ARGB_8888, true)
            } else {
                srcBitmap
            }
            val canvas = Canvas(localBitmap)
            canvas.withSave {
                // 将文字图层和原图合并
                canvas.drawBitmap(textCacheBitmap, 0f, 0f, null)
            }
            if (localBitmap != srcBitmap) {
                srcBitmap.recycle()
            }
            textCacheBitmap?.recycle()
            return localBitmap
        }
        return null
    }

    override fun pushOpStepToStackForce(opStep: TextOperationStep?) {
        if (opStep == null) {
            return
        }
        focusOpStep?.mParent?.selected = false
        onInvalid?.invoke()
    }

    override fun changeFocusOpStepForce(opStep: TextOperationStep?) {
        // do nothing
    }

    override fun onOpStackChange(t: TextOperationStep?, isForward: Boolean) {
        // do nothing
    }

    override fun deleteOpStep(opStep: TextOperationStep?) {
        focusOpStep?.text = EMPTY_STRING
        clearFocusDrawableOpStep()
    }

    override fun onTextChange(newString: String?) {
        // do nothing
    }

    override fun onTextSubmit(text: String?) {
        defaultText = text ?: EMPTY_STRING
        createAndSelectTextView()
    }

    companion object {
        private const val TAG = "CaptionTextView"
        private const val INVALID_NUMBER = Float.NaN
        private const val DISPLAY_DELAY = 300L
    }
}