/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RingtoneParam
 ** Description:视频铃声编辑 params
 ** Version: 1.0
 ** Date : 2025/06/10
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.ringtone

import android.content.Intent
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.videoeditorpage.video.business.ringtone.RingtoneParam.Companion.DEFAULT_VIDEO_TRIM_MAX_DURATION
import com.oplus.gallery.videoeditorpage.video.business.ringtone.RingtoneParam.Companion.DEFAULT_VIDEO_TRIM_MIN_DURATION
import com.oplus.gallery.videoeditorpage.video.business.ringtone.RingtoneParam.Companion.KEY_VIDEO_TRIM_MAX_DURATION
import com.oplus.gallery.videoeditorpage.video.business.ringtone.RingtoneParam.Companion.KEY_VIDEO_TRIM_MIN_DURATION

/**
 * 视频铃声编辑各参数
 */
class RingtoneParam {
    /**
     * 视频剪辑最小时长 单位ms
     */
    var videoTrimMinDuration: Long = 0L

    /**
     * 视频剪辑最大时长 单位ms
     */
    var videoTrimMaxDuration: Long = 0L

    companion object {
        /**
         * 视频裁剪的最短时长，单位ms。
         */
        const val KEY_VIDEO_TRIM_MIN_DURATION: String = "video-ringtone-trim-min-duration"

        /**
         * 视频裁剪的最大时长，单位ms
         */
        const val KEY_VIDEO_TRIM_MAX_DURATION: String = "video-ringtone-trim-max-duration"

        /**
         * 默认视频剪辑最小时长 单位ms
         */
        const val DEFAULT_VIDEO_TRIM_MIN_DURATION = 1000L

        /**
         * 默认视频剪辑最大时长 单位ms
         */
        const val DEFAULT_VIDEO_TRIM_MAX_DURATION = 30 * 1000L

        /**
         * 从电话应用跳转设置视频彩铃
         */
        const val VALUE_INVOKER_RINGTONE = "ringtone"

        /**
         * 当前壁纸参数
         */
        var curInstance: RingtoneParam? = null

        private var invoker: String? = null

        /**
         * 根据intent和invoker获取壁纸参数
         */
        fun newInstance(intent: Intent, invoker: String?) {
            curInstance = when (invoker) {
                VALUE_INVOKER_RINGTONE -> intent.toRingtoneParam()
                else -> null
            }
        }
    }
}

fun Intent.toRingtoneParam(): RingtoneParam = RingtoneParam().apply {
    videoTrimMinDuration = IntentUtils.getLongExtra(
        this@toRingtoneParam,
        KEY_VIDEO_TRIM_MIN_DURATION,
        DEFAULT_VIDEO_TRIM_MIN_DURATION
    )
    videoTrimMaxDuration = IntentUtils.getLongExtra(
        this@toRingtoneParam,
        KEY_VIDEO_TRIM_MAX_DURATION,
        DEFAULT_VIDEO_TRIM_MAX_DURATION
    )
}