/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SuperTextProperties.kt
 * Description:
 * Version:
 * Date: 2022/4/14
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/4/14     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.foundation.codec.supertext

import android.graphics.Bitmap

/**
 * 超级文本属性（超级文本1.0,2.0通用）
 *      普通图片用此属性时，参数都是默认参数，效果图和原图是同一幅图
 */
data class SuperTextProperties(
    var effectBitmap: Bitmap? = null,
    /**
     * 源文件的文件编码是否是超级文本图片（均为超级文本1.0的编码方案）
     * 普通图片保存编码为超级文本图片后，此标志才应该置为true
     */
    var isSuperText: Boolean = false,
    var correctVertex: FloatArray = INITIAL_CORRECT_VERTEX,
    var originBitmap: Bitmap? = null,
    var effectType: Int = EFFECT_ORIGIN,
    var sotOffset: Int = 0,
    var dataBlockInfos: Array<DataBlockInfo>? = null
) {

    companion object {

        const val EFFECT_ORIGIN = 0
        const val EFFECT_AUTO = 1
        const val EFFECT_BLACK_WHITE = 2
        const val EFFECT_COLOR_FIGURE = 3
        const val EFFECT_BRIGHTNESS = 4
        const val EFFECT_GARY_SCALE = 5
        const val EFFECT_COLOR_TEXT = 6
        const val EFFECT_INK_SAVING = 7

        /**
         * 超级文本文件编码结构
         * -----------------------------------------------------
         *                      效果图jpg
         * -----------------------------------------------------
         *  SOT | count
         *  tag | offset | length | version 校正框 数据块信息
         *  tag | offset | length | version 效果类型 数据块信息
         *  tag | offset | length | version 原图 数据块信息
         *  EOT
         * -----------------------------------------------------
         *                      校正框 数据块
         * -----------------------------------------------------
         *                      效果类型 数据块
         * -----------------------------------------------------
         *                      原图 数据块
         * -----------------------------------------------------
         *  MagicNumber | SOT offset
         * -----------------------------------------------------
         */

        const val VERTEX_DATA_BLOCK_OFFSET = 50 // 存储数据块信息的部分的长度，即SOT到EOT的长度
        const val VERTEX_DATA_BLOCK_LENGTH = 4 * 8 // 4个校正点坐标
        const val PARAMS_DATA_BLOCK_LENGTH = 4
        const val DATA_BLOCK_VERSION = 1

        const val SOT_OFFSET_BYTE_LENGTH = 4 // SOT mOffset field mLength
        const val MAGIC_NUMBER_BYTE_LENGTH = 4 // Magic number field mLength
        const val TAG_BYTE_LENGTH = 2 // TAG field mLength
        const val DATA_BLOCK_INFO_ITEM_BYTE_LENGTH = TAG_BYTE_LENGTH + 3 * Integer.SIZE / java.lang.Byte.SIZE

        /**
         * 超级文本1.0图片的幻数标志位：超级文本2.0仍然保持超级文本1.0的标志位
         */
        val ENHANCE_MAGIC_NUMBER = byteArrayOf('e'.toByte(), 'n'.toByte(), 't'.toByte(), 'x'.toByte())

        /**
         * 图片文本的幻数标志位：超级文本2.0图片图片文本图片也按超级文本1.0的格式进行编解码，因此也需要加上幻数标志位
         */
        val SUPER_MAGIC_NUMBER = byteArrayOf('t'.toByte(), 'e'.toByte(), 'x'.toByte(), 't'.toByte())
        val SOT = byteArrayOf(0xFF.toByte(), 0xF0.toByte())
        val EOT = byteArrayOf(0xFF.toByte(), 0xF1.toByte())

        val TAG_CORRECT_VERTEX = byteArrayOf(0xAA.toByte(), 0x04.toByte())
        val TAG_EFFECT_TYPE = byteArrayOf(0xAA.toByte(), 0x05.toByte())
        val TAG_TEXT_ORIGIN_IMAGE = byteArrayOf(0xAA.toByte(), 0x06.toByte())

        val INITIAL_CORRECT_VERTEX = floatArrayOf(
            0f, 0f,
            1f, 0f,
            1f, 1f,
            0f, 1f
        )

        data class DataBlockInfo(
            val tag: ByteArray,
            val offset: Int = 0,
            val length: Int = 0,
            val version: Int = 0
        )
    }

    fun buildDataBlockInfo(fileLength: Int) {
        if (isSuperText || (dataBlockInfos != null)) {
            return
        }
        val vertexInfo = DataBlockInfo(
            tag = TAG_CORRECT_VERTEX,
            offset = VERTEX_DATA_BLOCK_OFFSET,
            length = VERTEX_DATA_BLOCK_LENGTH,
            version = DATA_BLOCK_VERSION
        )
        val paramsOffset = VERTEX_DATA_BLOCK_OFFSET + VERTEX_DATA_BLOCK_LENGTH
        val effectTypeInfo = DataBlockInfo(
            tag = TAG_EFFECT_TYPE,
            offset = paramsOffset,
            length = PARAMS_DATA_BLOCK_LENGTH,
            version = DATA_BLOCK_VERSION
        )
        val originOffset = paramsOffset + PARAMS_DATA_BLOCK_LENGTH
        val originBitmapInfo = DataBlockInfo(
            tag = TAG_TEXT_ORIGIN_IMAGE,
            offset = originOffset,
            length = fileLength,
            version = DATA_BLOCK_VERSION
        )

        dataBlockInfos = arrayOf(
            vertexInfo,
            effectTypeInfo,
            originBitmapInfo
        )
        sotOffset = originOffset + fileLength + SOT_OFFSET_BYTE_LENGTH + MAGIC_NUMBER_BYTE_LENGTH
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is SuperTextProperties) {
            return false
        }

        if (effectBitmap != other.effectBitmap) {
            return false
        }

        if (isSuperText != other.isSuperText) {
            return false
        }

        if (!correctVertex.contentEquals(other.correctVertex)) {
            return false
        }

        if (originBitmap != other.originBitmap) {
            return false
        }

        if (effectType != other.effectType) {
            return false
        }

        if (sotOffset != other.sotOffset) {
            return false
        }

        if ((dataBlockInfos != null) && (other.dataBlockInfos == null) && (!dataBlockInfos.contentEquals(other.dataBlockInfos))) {
            return false
        }

        return true
    }

    override fun hashCode(): Int {
        var result = effectBitmap.hashCode()
        result = 31 * result + isSuperText.hashCode()
        result = 31 * result + correctVertex.contentHashCode()
        result = 31 * result + (originBitmap?.hashCode() ?: 0)
        result = 31 * result + effectType
        result = 31 * result + sotOffset
        result = 31 * result + (dataBlockInfos?.contentHashCode() ?: 0)
        return result
    }

    fun copy(): SuperTextProperties {
        return this.copy(
            effectBitmap = effectBitmap,
            isSuperText = isSuperText,
            correctVertex = correctVertex.clone(),
            originBitmap = originBitmap,
            effectType = effectType,
            sotOffset = sotOffset,
            dataBlockInfos = dataBlockInfos?.clone()
        )
    }
}