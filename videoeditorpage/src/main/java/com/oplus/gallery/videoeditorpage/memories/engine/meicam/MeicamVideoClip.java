/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MeicamVideoClip.java
 ** Description: XXXXXXXXXXXXXXXXXXXXX.
 ** Version: 1.0
 ** Date : 2017/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/13    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import android.content.Context;
import android.text.TextUtils;
import android.util.Rational;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsClip;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoResolution;
import com.meicam.sdk.NvsVideoStreamInfo;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.business_lib.videoedit.VideoEditSpecHelper;
import com.oplus.gallery.standard_lib.app.multiapp.MultiAppUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.memories.engine.GalleryVideoEngineListener;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoClip;
import com.oplus.gallery.videoeditorpage.video.VideoEditorSendActivity;

import java.util.ArrayList;
import java.util.List;

public class MeicamVideoClip implements IGalleryVideoClip {
    public static final int VIDEOROTATION_0 = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_0;
    public static final int VIDEOROTATION_90 = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_90;
    public static final int VIDEOROTATION_180 = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_180;
    public static final int VIDEOROTATION_270 = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_270;
    public static final int VIDEO_DEGREE_90 = 90;
    public static final int VIDEO_DEGREE_180 = 180;
    public static final int VIDEO_DEGREE_270 = 270;

    private static final String TAG = "MeicamVideoClip";

    private static final int MAX_DIRECTION_COUNT = 4;

    private static final int INDEX_0 = 0;
    private static final int INDEX_1 = 1;
    private static final int INDEX_2 = 2;
    private static final int INDEX_3 = 3;

    private static final float SLOW_MOTION_AUDIO_VOLUME = 0;
    private static final float NORMAL_AUDIO_VOLUME = 1.0f;

    private static final int INITNUM = -1;

    private static final int CODE_SOFT_DECODE_SUPPORTED = 1;
    private static final int CODE_SOFT_DECODE_UNKNOWN = 0;
    private static final int CODE_SOFT_DECODE_UNSUPPORTED = -1;
    private final VideoEditSpecHelper mVideoEditSpecHelper;

    private NvsStreamingContext mStreamingContext;
    private MeicamVideoEngine mEngine;
    private GalleryVideoEngineListener mListener;
    private NvsTimeline mTimeline;
    private NvsVideoTrack mVideoTrack;
    private NvsVideoClip mNvsVideoClip;
    private NvsTimeline.PlaybackRateControlRegion[] mSlowMotionRegions;
    private List<String> mVideoUriList = new ArrayList<>();
    private List<String> mVideoFilePathList = new ArrayList<>();
    private List<Long> mVideoDataTakenList = new ArrayList<>();
    private float[] mSlowMotionPercentList;
    private float[] mSlowMotionPercentOriginalList;
    private float mSlowSpeed = 1f;
    private int mSlowMotionPlayFps = VideoEditorSendActivity.SLOW_MOTION_DEFAULT_FPS;
    private float mTrimVideoSpeed = 1f;
    private long mSlowMotionOriginDuration;
    private long mTrimStartTime = INITNUM;
    private long mTrimEndTime = INITNUM;
    private int mVideoWidth = 0;
    private int mVideoHeight = 0;
    private long mDuration = 0;
    private int mVideoCodecType = NvsVideoStreamInfo.VIDEO_CODEC_TYPE_UNKNOWN;
    private int mExtraVideoRotation = MAX_DIRECTION_COUNT;
    private boolean mIsOriginalMusicMuted = false;
    private boolean mIsVideoClipChanged = false;
    private boolean mIsVideoSpeedChanged = false;
    private boolean mIsHfrSlowMotion = false;
    private Rational mFrameRate;
    private int mSupportSoftDecode = CODE_SOFT_DECODE_UNKNOWN;
    private int mVideoType;
    private int mVideoEditHdrType;
    private long mBitRate;
    /**
     * 视频位深
     */
    private int mBitDepth;

    public MeicamVideoClip(MeicamVideoEngine engine, GalleryVideoEngineListener listener, NvsStreamingContext streamingContext) {
        mEngine = engine;
        mListener = listener;
        mStreamingContext = streamingContext;
        mVideoEditSpecHelper = VideoEditSpecHelper.create(VideoEditSpecHelper.EngineType.Meicam);
    }

    /**
     * mListener是VideoEditorActivity的匿名内部类，需要清空引用
     */
    public void clearListener() {
        mListener = null;
    }

    public void setTimeline(NvsTimeline timeline) {
        mTimeline = timeline;
        GLog.d(TAG, "setTimeline videoTrackCount:" + mTimeline.videoTrackCount());
        // the timeline can only have one video track, appendVideoTrack run only once
        mVideoTrack = timeline.appendVideoTrack();
    }

    public NvsVideoResolution getVideoResolution(int width, int height) {
        NvsVideoResolution videoRes = new NvsVideoResolution();
        videoRes.imageWidth = width;
        videoRes.imageHeight = height;
        videoRes.imagePAR = new NvsRational(1, 1);
        return videoRes;
    }

    public NvsRational getFpsRate(int fps) {
        return new NvsRational(fps, 1);
    }

    public NvsVideoClip getNvsVideoClip() {
        return mNvsVideoClip;
    }

    public NvsRational getFpsRate(Rational fps) {
        return new NvsRational(fps.getNumerator(), fps.getDenominator());
    }

    public long getBitRate() {
        return mBitRate;
    }

    /**
     * 获取视频位深
     * @return 视频位深
     */
    public int getBitDepth() {
        return mBitDepth;
    }

    @Override
    public int getVideoCodecType() {
        return mVideoCodecType;
    }

    @Override
    public boolean initVideoFileInfo(String filePath, boolean isHfrSlowMotion, int videoType) {
        if (isHfrSlowMotion) {
            mStreamingContext.enableGetAVFileInfoFromMediaExtractor(filePath);
            mStreamingContext.setMediaCodecIconReaderEnabled(filePath, true);
        }
        /**
         * getVideoStreamColorTranfer 返回当前视频流的颜色转换曲线
         * 要正确的获取这个信息，在调用NvsStreamingContext.getAVFileInfo()必须使用AV_FILEINFO_EXTRA_INFO标志
         */
        NvsAVFileInfo info = mStreamingContext.getAVFileInfo(filePath, NvsStreamingContext.AV_FILEINFO_EXTRA_INFO);
        if (info != null) {
            if (info.getVideoStreamCount() > 0) {
                mVideoCodecType = info.getVideoStreamCodecType(0);
            }
            mDuration = info.getDuration();
            NvsSize size = info.getVideoStreamDimension(0);
            if (size != null) {
                int rotation = info.getVideoStreamRotation(0);
                if ((rotation == NvsVideoStreamInfo.VIDEO_ROTATION_90) || (rotation == NvsVideoStreamInfo.VIDEO_ROTATION_270)) {
                    mVideoHeight = size.width;
                    mVideoWidth = size.height;
                } else {
                    mVideoHeight = size.height;
                    mVideoWidth = size.width;
                }

                NvsRational nvsRational = info.getVideoStreamFrameRate(0);
                mBitRate = info.getDataRate();
                mFrameRate = new Rational(nvsRational.num, nvsRational.den);
                mVideoType = videoType;
                mVideoEditHdrType = VideoEditorHelper.getVideoEditHdrType(filePath);
                mBitDepth = info.getVideoStreamComponentBitCount(0);
                GLog.d(TAG, "initVideoFileInfo()  mVideoHeight:" + mVideoHeight
                        + ", mVideoWidth:" + mVideoWidth
                        + ", rotation:" + rotation
                        + ", avFileType:" + info.getAVFileType()
                        + ", nvsRational.num: " + nvsRational.num
                        + ", nvsRational.den: " + nvsRational.den
                        + ", mVideoType: " + mVideoType
                        + ", fps:" + mFrameRate.floatValue());
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean addVideoClip(String fileUri, String filePath, long dataTaken) {
        return addVideoClip(fileUri, filePath, dataTaken, 0, 0);
    }

    @Override
    public boolean addVideoClip(String fileUri, String filePath, long dataTaken, long trimIn, long trimOut) {
        // 如果美摄不支持软解视频,可能在appendClip消耗大量的时间阻塞主线程,所以软解提前判断
        if (TextUtils.isEmpty(fileUri) || TextUtils.isEmpty(filePath)
                || (mSupportSoftDecode == CODE_SOFT_DECODE_UNSUPPORTED)) {
            return false;
        }
        long time = System.currentTimeMillis();
        boolean ans = false;
        if (mVideoTrack != null) {
            NvsVideoClip clip = mVideoTrack.appendClip(MultiAppUtils.INSTANCE.convertToSystemPath(filePath));
            mNvsVideoClip = clip;
            if (clip != null) {
                if ((trimIn < trimOut) && (trimIn >= 0)) {
                    trimClip(trimIn * MeicamVideoEngine.MILLIS_TIME_BASE, trimOut * MeicamVideoEngine.MILLIS_TIME_BASE, clip, false);
                    mTrimStartTime = clip.getTrimIn() / MeicamVideoEngine.MILLIS_TIME_BASE;
                    mTrimEndTime = clip.getTrimOut() / MeicamVideoEngine.MILLIS_TIME_BASE;
                }
            }
            ans = (clip != null);
        }
        if (ans) {
            mEngine.reset();
        }
        mVideoUriList.add(fileUri);
        if (!TextUtils.isEmpty(filePath)) {
            mVideoFilePathList.add(MultiAppUtils.INSTANCE.convertToSystemPath(filePath));
        }
        if (dataTaken >= 0) {
            mVideoDataTakenList.add(dataTaken);
        }
        GLog.d(TAG, "addVideoClip() time cost:" + (System.currentTimeMillis() - time));
        return ans;
    }

    @Override
    public String getVideoUri(int index) {
        if ((index >= 0) && (index < mVideoUriList.size())) {
            return mVideoUriList.get(index);
        }
        return null;
    }

    @Override
    public String getVideoFilePath(int index) {
        if ((index >= 0) && (index < mVideoFilePathList.size())) {
            return mVideoFilePathList.get(index);
        }
        return null;
    }

    @Override
    public long getVideoDataTaken(int index) {
        if ((index >= 0) && (index < mVideoDataTakenList.size())) {
            return mVideoDataTakenList.get(index);
        }
        return -1;
    }

    @Override
    public long getDuration() {
        return mDuration;
    }

    @Override
    public int getDegree() {
        int degree = 0;
        switch (mExtraVideoRotation) {
            case VIDEOROTATION_90:
                degree = VIDEO_DEGREE_90;
                break;
            case VIDEOROTATION_180:
                degree = VIDEO_DEGREE_180;
                break;
            case VIDEOROTATION_270:
                degree = VIDEO_DEGREE_270;
                break;
            default:
                break;
        }
        return degree;
    }

    @Override
    public boolean checkVideoSoftSupported(String filePath, Context context) {
        boolean isSupport = mVideoEditSpecHelper.checkSoftDecodeSupported(filePath, context);
        mSupportSoftDecode = isSupport ? CODE_SOFT_DECODE_SUPPORTED : CODE_SOFT_DECODE_UNSUPPORTED;
        return isSupport;
    }

    @Override
    public boolean checkVideoSupported(String filePath, Context context) {
        return mVideoEditSpecHelper.checkSupportedByEditor(filePath, context);
    }

    private boolean splitVideoClip(int clipIndex, long splitPoint) {
        boolean success = false;
        if (mVideoTrack != null) {
            success = mVideoTrack.splitClip(clipIndex, splitPoint);
            GLog.d(TAG, "splitVideoClip clipIndex:" + clipIndex
                    + ", splitPoint:" + splitPoint + ", success:" + success);
        }
        return success;
    }

    private int getVideoClipCount() {
        int count = 0;
        if (mVideoTrack != null) {
            count = mVideoTrack.getClipCount();
        }
        return count;
    }

    public boolean splitSlowMotion() {
        boolean spiltResult = true;
        int clipCount = 0;
        ArrayList<Integer> slowMotionClipList = new ArrayList<>();
        for (int i = 0; i < mSlowMotionPercentList.length; i++) {
            long slowTime = (long) (mSlowMotionPercentList[i] * mEngine.getTotalTime());
            clipCount = getVideoClipCount();

            if ((slowTime > 0) && (slowTime < mEngine.getTotalTime())) {
                boolean ans = splitVideoClip(clipCount - 1, slowTime * MeicamVideoEngine.MILLIS_TIME_BASE);
                spiltResult &= ans;
            }
            if (i % 2 != 0) {
                slowMotionClipList.add(clipCount - 1);
            }
            GLog.d(TAG, "splitSlowMotion() list index:" + i + ", slowTime:" + slowTime
                    + ", clipCount:" + clipCount + ", spiltResult:" + spiltResult);
        }
        try {
            if (mIsHfrSlowMotion) {
                for (int i = 0; i < getVideoClipCount(); i++) {
                    if (!slowMotionClipList.contains(i)) {
                        GLog.d(TAG, "splitSlowMotion() clip index:" + i + " slowSpeed:" + mSlowSpeed);
                        NvsVideoClip clip = mVideoTrack.getClipByIndex(i);
                        if (clip != null) {
                            clip.changeSpeed(mSlowSpeed);
                            clip.setVolumeGain(SLOW_MOTION_AUDIO_VOLUME, SLOW_MOTION_AUDIO_VOLUME);
                            // to remove the audio in slow motion area
                            mEngine.getGalleryAudioClip().removeMusic(clip.getInPoint(), clip.getOutPoint());
                        }
                    }
                }
            } else {
                for (int i : slowMotionClipList) {
                    GLog.d(TAG, "splitSlowMotion() clip index:" + i + " slowSpeed:" + mSlowSpeed);
                    NvsVideoClip clip = mVideoTrack.getClipByIndex(i);
                    if (clip != null) {
                        clip.changeSpeed(mSlowSpeed);
                        clip.setVolumeGain(SLOW_MOTION_AUDIO_VOLUME, SLOW_MOTION_AUDIO_VOLUME);
                        // to remove the audio in slow motion area
                        mEngine.getGalleryAudioClip().removeMusic(clip.getInPoint(), clip.getOutPoint());
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "splitSlowMotion() error:" + e);
        }
        return spiltResult;
    }

    @Override
    public boolean removeVideoClip(int index) {
        boolean result = false;
        if ((mVideoTrack != null) && (mVideoTrack.getClipCount() > index)) {
            result = mVideoTrack.removeClip(index, false);
        }
        if (result && (mListener != null)) {
            mListener.onPlayPositionChange(mEngine.getCurrentTime());
        }

        GLog.d(TAG, "removeVideoClip, result:" + result + ",  index:" + index);
        return result;
    }

    @Override
    public boolean removeVideoClip(String filePath) {
        boolean result = false;
        if ((mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            for (int i = 0; i < mVideoTrack.getClipCount(); i++) {
                NvsClip clip = mVideoTrack.getClipByIndex(i);
                if ((clip != null) && TextUtils.equals(filePath, clip.getFilePath())) {
                    result = mVideoTrack.removeClip(i, false);
                    break;
                }
            }
        }
        if (result && (mListener != null)) {
            mListener.onPlayPositionChange(mEngine.getCurrentTime());
        }
//        GLog.d(TAG, "removeVideoClip, result:" + result + ",  filePath:" + filePath);
        return result;
    }

    @Override
    public void trimVideo(long trimIn, long trimOut) {
        if (mVideoTrack == null) {
            GLog.d(TAG, "trimVideo, mVideoTrack is null.");
            return;
        }
        NvsVideoClip clip = mVideoTrack.getClipByIndex(0);
        if (clip == null) {
            GLog.d(TAG, "trimVideo, clip is null.");
            return;
        }
        GLog.d(TAG, "trimVideo(), trimIn:" + trimIn + " trimOut:" + trimOut);
        long time = System.currentTimeMillis();
        mTrimStartTime = INITNUM;
        mTrimEndTime = INITNUM;
        if ((mSlowMotionRegions != null) && (mSlowMotionRegions.length > 0)) {
            if (mTimeline == null) {
                GLog.d(TAG, "trimVideo, mTimeline is null.");
                return;
            }
            String path = clip.getFilePath();
            if (TextUtils.isEmpty(path)) {
                GLog.d(TAG, "trimVideo, path is null.");
                return;
            }
            if (mSlowMotionRegions.length == 1) {
                trimVideoByOneSlowMotionRegion(trimIn * MeicamVideoEngine.MILLIS_TIME_BASE,
                        (long) trimOut * MeicamVideoEngine.MILLIS_TIME_BASE, path, clip);
            } else if (mSlowMotionRegions.length == 2) {
                trimVideoByTwoSlowMotionRegions(trimIn * MeicamVideoEngine.MILLIS_TIME_BASE,
                        trimOut * MeicamVideoEngine.MILLIS_TIME_BASE, path, clip);
            }

            cleanBuiltinTransition();
            cleanSlowMotionMusic();
            mTimeline.setPlaybackRateControl(null);
        } else {
            clip.changeTrimInPoint((trimIn * MeicamVideoEngine.MILLIS_TIME_BASE), true);
            clip.changeTrimOutPoint((trimOut * MeicamVideoEngine.MILLIS_TIME_BASE), true);
            mTrimStartTime = clip.getTrimIn() / MeicamVideoEngine.MILLIS_TIME_BASE;
            mTrimEndTime = clip.getTrimOut() / MeicamVideoEngine.MILLIS_TIME_BASE;
        }
        GLog.d(TAG, "trimVideo, cost time = " + (System.currentTimeMillis() - time)
                + ", clipCount = " + mVideoTrack.getClipCount()
                + ", getTotalTime = " + mEngine.getTotalTime());
    }

    /**
     * 单片段剪辑功能
     * 需要配合videoTrack,mTrimStartTime,mTrimEndTime
     * 只能在trimVideoClip里操作
     *
     * @param trimIn
     * @param trimOut
     * @param clip
     * @param changeSpeed
     */
    private void trimClip(long trimIn, long trimOut, NvsVideoClip clip, boolean changeSpeed) {
        clip.changeTrimInPoint(trimIn, true);
        clip.changeTrimOutPoint(trimOut, true);
        if (changeSpeed) {
            clip.changeSpeed(mSlowSpeed);
            clip.setVolumeGain(SLOW_MOTION_AUDIO_VOLUME, SLOW_MOTION_AUDIO_VOLUME);
        }
    }

    /**
     * 根据列表裁剪视频, 每次操作会覆盖之前的裁剪操作
     *
     * @param trimInfoList
     * @param path
     */
    private void trimVideoClip(TrimInfo[] trimInfoList, String path) {
        mVideoTrack.removeAllClips();
        long tempTrimStartTime = Long.MAX_VALUE;
        long tempTrimEndTime = 0;
        for (int i = 0; i < trimInfoList.length; i++) {
            long trimIn = trimInfoList[i].mTrimIn;
            long trimOut = trimInfoList[i].mTrimOut;
            boolean changeSpeed = trimInfoList[i].mChangeSpeed;
            if (trimIn < trimOut) {
                NvsVideoClip clip = mVideoTrack.appendClip(path, trimIn, trimOut);
                trimClip(trimIn, trimOut, clip, changeSpeed);
                tempTrimStartTime = Math.min(tempTrimStartTime, trimIn);
                tempTrimEndTime = Math.max(tempTrimEndTime, trimOut);
            }
            GLog.d(TAG, "trimVideoFifthClip, trimInfo[ " + i + " ] = " + trimInfoList[i]);
        }
        mTrimStartTime = tempTrimStartTime / MeicamVideoEngine.MILLIS_TIME_BASE;
        mTrimEndTime = tempTrimEndTime / MeicamVideoEngine.MILLIS_TIME_BASE;
        GLog.d(TAG, "trimVideoFifthClip"
                + ", slowSpeed = " + mSlowSpeed
                + ", trimStartTime = " + mTrimStartTime
                + ", trimEndTime = " + mTrimEndTime
                + ", clipCount = " + mVideoTrack.getClipCount()
                + ", getTotalTime = " + mEngine.getTotalTime());
    }

    /**
     * (old)hsr slow motion or (new)hfr slow motion, one slow motion region
     * @param trimIn
     * @param trimOut
     * @param path
     * @param clip
     */
    private void trimVideoByOneSlowMotionRegion(long trimIn, long trimOut, String path, NvsVideoClip clip) {
        long startTime = mSlowMotionRegions[INDEX_0].startTime;
        long endTime = mSlowMotionRegions[INDEX_0].endTime;
        /*
        1. trim in/out before start time
        2. trim in/out after end time
        */
        if ((trimOut <= startTime) || (trimIn >= endTime)) {
            TrimInfo[] trimInfoList = {new TrimInfo(trimIn, trimOut, mIsHfrSlowMotion)};
            trimVideoClip(trimInfoList, path);
        }

        // 3. trim in after start time, and trim out before end time
        else if (((trimIn >= startTime) && (trimOut <= endTime))) {
            TrimInfo[] trimInfoList = {new TrimInfo(trimIn, trimOut, !mIsHfrSlowMotion)};
            trimVideoClip(trimInfoList, path);
        }

        // 4. trim in before start time, and trim out after start time and before end time
        else if ((trimIn <= startTime) && (trimOut >= startTime) && (trimOut <= endTime)) {
            /*
            first clip: trim in --- start time
            second clip: start time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime, mIsHfrSlowMotion),
                    new TrimInfo(startTime, trimOut, !mIsHfrSlowMotion)};
            trimVideoClip(trimInfoList, path);
        }

        // 5. trim in after start time and before end time, and trim out after end time
        else if ((trimIn >= startTime) && (trimIn <= endTime) && (trimOut >= endTime)) {
            /*
            first clip: trim in --- end time
            second clip: end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, endTime, !mIsHfrSlowMotion),
                    new TrimInfo(endTime, trimOut, mIsHfrSlowMotion)};
            trimVideoClip(trimInfoList, path);
        }

        // 6. trim in before start time, and trim out after end time
        else if ((trimIn <= startTime) && (trimOut >= endTime)) {
            /*
            first clip(change speed): trim in --- start time
            second clip: start time --- end time
            third clip(change speed): end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime, mIsHfrSlowMotion),
                    new TrimInfo(startTime, endTime, !mIsHfrSlowMotion),
                    new TrimInfo(endTime, trimOut, mIsHfrSlowMotion)};
            trimVideoClip(trimInfoList, path);
        }
    }

    /**
     * (old)hsr slow motion, two slow motion regions
     * @param trimIn
     * @param trimOut
     * @param path
     * @param clip
     */
    private void trimVideoByTwoSlowMotionRegions(long trimIn, long trimOut, String path, NvsVideoClip clip) {
        long startTime1 = mSlowMotionRegions[INDEX_0].startTime;
        long endTime1 = mSlowMotionRegions[INDEX_0].endTime;
        long startTime2 = mSlowMotionRegions[INDEX_1].startTime;
        long endTime2 = mSlowMotionRegions[INDEX_1].endTime;

        /*
        1. trim in/out before first start time
        2. trim in/out after first end time and before second start time
        3. trim in/out after second end time
        */
        if ((trimOut <= startTime1) // 1
                || ((trimIn >= endTime1) && (trimOut <= startTime2)) // 2
                || (trimIn >= endTime2) // 3
        ) {
            TrimInfo[] trimInfoList = {new TrimInfo(trimIn, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        4. trim in after first start time, and trim out before first end time (change speed)
        5. trim in after second start time, and trim out before second end time (change speed)
        */
        else if (((trimIn >= startTime1) && (trimOut <= endTime1)) // 4
                || ((trimIn >= startTime2) && (trimOut <= endTime2)) // 5
        ) {
            TrimInfo[] trimInfoList = {new TrimInfo(trimIn, trimOut, true)};
            trimVideoClip(trimInfoList, path);
        }

        // 6. trim in before first start time, and trim out after first start time and before first end time
        else if ((trimIn <= startTime1) && (trimOut >= startTime1) && (trimOut <= endTime1)) {
            /*
            first clip: trim in --- start time
            second clip(change speed): start time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime1, false),
                    new TrimInfo(startTime1, trimOut, true)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        7. trim in after first end time and before second start time,
        and trim out after second start time and before second end time
        */
        else if ((trimIn >= endTime1) && (trimIn <= startTime2)
                && (trimOut >= startTime2) && (trimOut <= endTime2)) {
            /*
            first clip: trim in --- start time2
            second clip(change speed): start time2 --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime2, false),
                    new TrimInfo(startTime2, trimOut, true)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        8. trim in after first start time and before first end time,
        and trim out after first end time and before second start time
        */
        else if ((trimIn >= startTime1) && (trimIn <= endTime1)
                && (trimOut >= endTime1) && (trimOut <= startTime2)) {
            /*
            first clip(change speed): trim in --- first end time
            second clip: first end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, endTime1, true),
                    new TrimInfo(endTime1, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        9. trim in after second start time and before second end time,
        and trim out after second end time
        */
        else if ((trimIn >= startTime2) && (trimIn <= endTime2) && (trimOut >= endTime2)) {
            /*
            first clip(change speed): trim in --- sec end time
            second clip: sec end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, endTime2, true),
                    new TrimInfo(endTime2, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }


        // 10. trim in before first start time, and trim out after first end time and before second start time
        else if ((trimIn <= startTime1) && (trimOut >= endTime1) && (trimOut <= startTime2)) {
            /*
            first clip: trim in --- start time
            second clip(change speed): first start time --- first end time
            third clip: first end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime1, false),
                    new TrimInfo(startTime1, endTime1, true),
                    new TrimInfo(endTime1, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        11. trim in after first end time and before second start time,
        and trim out after second end time
        */
        else if ((trimIn >= endTime1) && (trimIn <= startTime2) && (trimOut >= endTime2)) {
            /*
            first clip: trim in --- second start time
            second clip(change speed): second start time --- second end time
            third clip: second end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime2, false),
                    new TrimInfo(startTime2, endTime2, true),
                    new TrimInfo(endTime2, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        12. trim in after first start time and before first end time,
        and trim out after second start time and before second end time
        */
        else if ((trimIn >= startTime1) && (trimIn <= endTime1)
                && (trimOut >= startTime2) && (trimOut <= endTime2)) {
            /*
            first clip(change speed): trim in --- first end time
            second clip: first end time --- second start time
            third clip(change speed): second start time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, endTime1, true),
                    new TrimInfo(endTime1, startTime2, false),
                    new TrimInfo(startTime2, trimOut, true)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        13. trim in after first start time and before first end time,
        and trim out after second end time
        */
        else if ((trimIn >= startTime1) && (trimIn <= endTime1) && (trimOut >= endTime2)) {
            /*
            first clip(change speed): trim in --- first end time
            second clip: first end time --- second start time
            third clip(change speed): second start time --- second end time
            fourth clip: second end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, endTime1, true),
                    new TrimInfo(endTime1, startTime2, false),
                    new TrimInfo(startTime2, endTime2, true),
                    new TrimInfo(endTime2, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }

        /*
        14. trim in before first start time,
        and trim out after second end time
        */
        else if ((trimIn <= startTime1) && (trimOut >= endTime2)) {
            /*
            first clip: trim in --- first start time
            second clip(change speed): first start time --- first end time
            third clip: first end time --- second start time
            fourth clip(change speed): second start time -- second end time
            fifth clip: second end time --- trim out
            */
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime1, false),
                    new TrimInfo(startTime1, endTime1, true),
                    new TrimInfo(endTime1, startTime2, false),
                    new TrimInfo(startTime2, endTime2, true),
                    new TrimInfo(endTime2, trimOut, false)};
            trimVideoClip(trimInfoList, path);
        }

        // 15. 起点在第一段之前,终点在第二段之中
        else if ((trimIn <= startTime1) && (trimOut >= startTime2) && (trimOut <= endTime2)) {
            TrimInfo[] trimInfoList = {
                    new TrimInfo(trimIn, startTime1, false),
                    new TrimInfo(startTime1, endTime1, true),
                    new TrimInfo(endTime1, startTime2, false),
                    new TrimInfo(startTime2, trimOut, true)};
            trimVideoClip(trimInfoList, path);
        }

    }

    private void cleanBuiltinTransition() {
        if ((mVideoTrack == null) || (mVideoTrack.getClipCount() <= 0)) {
            GLog.w(TAG, "cleanBuiltinTransition mVideoTrack is null or count is 0.");
            return;
        }
        GLog.d(TAG, "cleanBuiltinTransition, getClipCount = " + mVideoTrack.getClipCount());
        try {
            for (int i = 0; i < mVideoTrack.getClipCount(); i++) {
                mVideoTrack.setBuiltinTransition(i, null);
            }
        } catch (Exception e) {
            GLog.e(TAG, "cleanBuiltinTransition error:", e);
        }
    }

    private void cleanSlowMotionMusic() {
        if ((mVideoTrack == null) || (mVideoTrack.getClipCount() <= 0)) {
            GLog.w(TAG, "cleanSlowMotionMusic mVideoTrack is null or count is 0.");
            return;
        }
        GLog.d(TAG, "cleanSlowMotionMusic, getClipCount = " + mVideoTrack.getClipCount());
        try {
            for (int i = 0; i < mVideoTrack.getClipCount(); i++) {
                NvsVideoClip clip = mVideoTrack.getClipByIndex(i);
                if ((clip != null) && (Float.compare((float) clip.getSpeed(), 1f) != 0)) {
                    GLog.d(TAG, "cleanSlowMotionMusic"
                            + ", getSpeed = " + clip.getSpeed()
                            + ", getInPoint = " + clip.getInPoint()
                            + ", getOutPoint = " + clip.getOutPoint());
                    // to remove the audio in slow motion area
                    mEngine.getGalleryAudioClip().removeMusic(clip.getInPoint(), clip.getOutPoint());
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "cleanSlowMotionMusic error:", e);
        }
    }

    @Override
    public void setOriginalMusicMute(boolean isMute) {
        if (mVideoTrack != null) {
            GLog.d(TAG, "setOriginalMusicMute"
                    + ", mIsOriginalMusicMuted:" + mIsOriginalMusicMuted
                    + ", isMute:" + isMute);
            mIsOriginalMusicMuted = isMute;
            for (int i = 0; i < mVideoTrack.getClipCount(); i++) {
                NvsClip clip = mVideoTrack.getClipByIndex(i);
                if (clip != null) {
                    if (isMute) {
                        clip.setVolumeGain(0f, 0f);
                    } else {
                        clip.setVolumeGain(NORMAL_AUDIO_VOLUME, NORMAL_AUDIO_VOLUME);
                    }
                }
            }
        }
    }

    @Override
    public boolean getOriginalMusicMuted() {
        return mIsOriginalMusicMuted;
    }

    @Override
    public float getSlowSpeed() {
        return mSlowSpeed;
    }

    @Override
    public float getTrimVideoSpeed() {
        return mTrimVideoSpeed;
    }

    @Override
    public Rational getFps() {
        return mFrameRate;
    }

    @Override
    public int getSlowMotionPlayFps() {
        return mSlowMotionPlayFps;
    }

    @Override
    public int getVideoFileWidth() {
        return mVideoWidth;
    }

    @Override
    public int getVideoFileHeight() {
        return mVideoHeight;
    }

    @Override
    public boolean isHfrSlowMotion() {
        return mIsHfrSlowMotion;
    }

    @Override
    public boolean changeVideoSpeed(float speed) {
        if (mVideoTrack != null) {
            GLog.d(TAG, "[changeVideoSpeed] speed:" + speed + ", mIsOriginalMusicMuted:" + mIsOriginalMusicMuted);
            mTrimVideoSpeed = speed;
            try {
                for (int i = 0; i < mVideoTrack.getClipCount(); i++) {
                    NvsVideoClip clip = mVideoTrack.getClipByIndex(i);
                    if (clip != null) {
                        // 设置视频倍速，且保持音调不变
                        clip.changeSpeed(speed, true);
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, "[changeVideoSpeed] cannot speed video clip: " + e);
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean addSlowMotionVideoClip(String fileUri, String filePath, long dataTaken, int slowfps, int playfps, long[] slowtimelist,
                                          boolean fullSlowMotion, boolean isHfrSlowMotion) {
        if (isHfrSlowMotion) {
            mStreamingContext.setMediaCodecVideoDecodingOperatingRate(fileUri, slowfps);
            GLog.d(TAG, "addSlowMotionVideoClip.setOperatingRate"
                    + ", operatingRate = " + slowfps
                    + ", uri = " + fileUri);
        }
        boolean result = addVideoClip(fileUri, filePath, dataTaken);
        if (!result) {
            return false;
        }
        mSlowMotionOriginDuration = mEngine.getTotalTime();
        int regionSize = 0;
        mIsHfrSlowMotion = isHfrSlowMotion;
        if (!mIsHfrSlowMotion) {
            regionSize = slowtimelist.length / 2;
        } else {
            // new full slow motion
            fullSlowMotion = true;
            slowtimelist = new long[INDEX_2];
        }
        if (fullSlowMotion) {
            regionSize = 1;
            slowtimelist[0] = 0L;
            slowtimelist[1] = mSlowMotionOriginDuration;
            mSlowMotionPercentList = new float[]{0f, 1f};
            mSlowMotionPercentOriginalList = new float[]{0f, 1f};
        } else {
            mSlowMotionPercentList = new float[slowtimelist.length];
            mSlowMotionPercentOriginalList = new float[slowtimelist.length];
            for (int i = 0; i < mSlowMotionPercentList.length; i++) {
                mSlowMotionPercentList[i] = ((float) slowtimelist[i]) / ((float) mSlowMotionOriginDuration);
                mSlowMotionPercentOriginalList[i] = ((float) slowtimelist[i]) / ((float) mSlowMotionOriginDuration);
            }
        }
        mSlowMotionRegions = new NvsTimeline.PlaybackRateControlRegion[regionSize];
        mSlowMotionPlayFps = slowfps;
        if (mIsHfrSlowMotion) {
            mSlowSpeed = (float) slowfps / (float) playfps;
        } else {
            mSlowSpeed = (float) playfps / (float) slowfps;
        }
        if (regionSize == INDEX_1) {
            // one slow motion gap
            mSlowMotionRegions[INDEX_0] = new NvsTimeline.PlaybackRateControlRegion();
            mSlowMotionRegions[INDEX_0].startTime = slowtimelist[INDEX_0] * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionRegions[INDEX_0].endTime = slowtimelist[INDEX_1] * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionRegions[INDEX_0].audioGain = SLOW_MOTION_AUDIO_VOLUME;
            mSlowMotionRegions[INDEX_0].playbackRate = mSlowSpeed;
        } else if (regionSize == 2) {
            // two slow motion gap
            mSlowMotionRegions[INDEX_0] = new NvsTimeline.PlaybackRateControlRegion();
            mSlowMotionRegions[INDEX_0].startTime = slowtimelist[INDEX_0] * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionRegions[INDEX_0].endTime = slowtimelist[INDEX_1] * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionRegions[INDEX_0].audioGain = SLOW_MOTION_AUDIO_VOLUME;
            mSlowMotionRegions[INDEX_0].playbackRate = mSlowSpeed;
            mSlowMotionRegions[INDEX_1] = new NvsTimeline.PlaybackRateControlRegion();
            mSlowMotionRegions[INDEX_1].startTime = slowtimelist[INDEX_2] * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionRegions[INDEX_1].endTime = slowtimelist[INDEX_3] * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionRegions[INDEX_1].audioGain = SLOW_MOTION_AUDIO_VOLUME;
            mSlowMotionRegions[INDEX_1].playbackRate = mSlowSpeed;
        }
        if (mIsHfrSlowMotion) {
            mTimeline.setPlaybackRateControl(getHfrSlowMotionRegion(mSlowMotionRegions));
        } else {
            mTimeline.setPlaybackRateControl(mSlowMotionRegions);
        }
        if (!isHfrSlowMotion) {
            /**
             * 修复 bugId:8731417
             * 1，初始化美摄context时，设置了 FLAG_INTERRUPT_STOP_FOR_INTERNAL_STOP flag，表示中断式停止
             * 1.1，背景是美摄在对timeline做操作时，会先调stop，若不是中断式，会等解帧完成才会继续对应操作
             * 1.2，而视频编辑内美摄暂时都跑主线程，若解帧耗时长会导致ANR
             * 2，对于gop越大的视频，解帧越慢
             * 3，普通视频会判断gop 超过一定值不进入编辑，而慢动作视频本身gop就很大
             * 4，中断式停止会中断解码然后黑屏，只有非全程慢动作初始化的场景会进入这里的判断，其它场景不会进来，所以正常
             * 5，解法：在对非全程慢动作视频解码时，主动调用非中断式的stop
             */
            mStreamingContext.stop();
            //非全程慢动作需要通过trim使从timeline.getDuration拿到播放时间
            trimVideo(0, getDuration() / MeicamVideoEngine.MILLIS_TIME_BASE);
        }
        GLog.d(TAG, "addSlowMotionVideoClip"
                + ", isHfrSlowMotion = " + mIsHfrSlowMotion
                + ", slowSpeed = " + mSlowSpeed
                + ", regionSize = " + regionSize
                + ", playFps = " + playfps
                + ", slowFps = " + slowfps
                + ", uri = " + fileUri);
        return true;
    }

    private NvsTimeline.PlaybackRateControlRegion[] getHfrSlowMotionRegion(NvsTimeline.PlaybackRateControlRegion[] oldregion) {
        NvsTimeline.PlaybackRateControlRegion[] newRegion = new NvsTimeline.PlaybackRateControlRegion[INDEX_2];
        if ((oldregion != null) && (oldregion.length == 1)) {
            GLog.d(TAG, "getHfrSlowMotionRegion() mSlowSpeed = " + mSlowSpeed
                    + ", startTime = " + oldregion[0].startTime
                    + ", endTime = " + oldregion[0].endTime);
            newRegion[INDEX_0] = new NvsTimeline.PlaybackRateControlRegion();
            newRegion[INDEX_0].startTime = 0;
            newRegion[INDEX_0].endTime = oldregion[0].startTime;
            newRegion[INDEX_0].playbackRate = mSlowSpeed;
            newRegion[INDEX_0].audioGain = SLOW_MOTION_AUDIO_VOLUME;
            newRegion[INDEX_1] = new NvsTimeline.PlaybackRateControlRegion();
            newRegion[INDEX_1].startTime = oldregion[0].endTime;
            newRegion[INDEX_1].endTime = mSlowMotionOriginDuration * MeicamVideoEngine.MILLIS_TIME_BASE;
            newRegion[INDEX_1].playbackRate = mSlowSpeed;
            newRegion[INDEX_1].audioGain = SLOW_MOTION_AUDIO_VOLUME;
        }
        return newRegion;
    }

    @Override
    public boolean changeSlowMotion(float enterA, float outA, float enterB, float outB) {
        long time = System.currentTimeMillis();
        GLog.d(TAG, "changeSlowMotion() enterA:" + enterA + " outA:" + outA + " enterB:" + enterB + " outB:" + outB);
        if (enterA >= 0) {
            mSlowMotionRegions[INDEX_0].startTime = (long) (enterA * mSlowMotionOriginDuration) * MeicamVideoEngine.MILLIS_TIME_BASE;
            mSlowMotionPercentList[INDEX_0] = enterA;
        }
        if (outA >= 0) {
            mSlowMotionPercentList[INDEX_1] = outA;
            mSlowMotionRegions[INDEX_0].endTime = (long) (outA * mSlowMotionOriginDuration) * MeicamVideoEngine.MILLIS_TIME_BASE;
        }
        if (mSlowMotionRegions.length > INDEX_1) {
            if ((enterB == 0) && (outB == 0)) {
                NvsTimeline.PlaybackRateControlRegion[] tempRegion = new NvsTimeline.PlaybackRateControlRegion[INDEX_1];
                tempRegion[INDEX_0] = new NvsTimeline.PlaybackRateControlRegion();
                tempRegion[INDEX_0].startTime = mSlowMotionRegions[INDEX_0].startTime;
                tempRegion[INDEX_0].endTime = mSlowMotionRegions[INDEX_0].endTime;
                tempRegion[INDEX_0].audioGain = SLOW_MOTION_AUDIO_VOLUME;
                tempRegion[INDEX_0].playbackRate = mSlowSpeed;
                mSlowMotionRegions = tempRegion;
                mSlowMotionPercentList = new float[]{0f, 1f};
            } else {
                if (enterB > 0) {
                    mSlowMotionPercentList[INDEX_2] = enterB;
                    mSlowMotionRegions[INDEX_1].startTime = (long) (enterB * mSlowMotionOriginDuration)
                            * MeicamVideoEngine.MILLIS_TIME_BASE;
                }
                if (outB > 0) {
                    mSlowMotionPercentList[INDEX_3] = outB;
                    mSlowMotionRegions[INDEX_1].endTime = (long) (outB * mSlowMotionOriginDuration)
                            * MeicamVideoEngine.MILLIS_TIME_BASE;
                }
            }
        }
        if (mIsHfrSlowMotion) {
            mTimeline.setPlaybackRateControl(getHfrSlowMotionRegion(mSlowMotionRegions));
        } else {
            mTimeline.setPlaybackRateControl(mSlowMotionRegions);
        }
        GLog.d(TAG, "changeSlowMotion() cost:" + (System.currentTimeMillis() - time));
        return true;
    }

    @Override
    public boolean isSupportSlowMotionMode() {
        return (mSlowMotionPercentList != null) && (mSlowMotionPercentList.length > 0);
    }

    @Override
    public float[] getSlowMotionList() {
        if ((mSlowMotionPercentList != null) && (mSlowMotionPercentList.length != 0)) {
            return mSlowMotionPercentList.clone();
        } else {
            return null;
        }
    }

    @Override
    public float[] getSlowMotionOriginalList() {
        if ((mSlowMotionPercentOriginalList != null) && (mSlowMotionPercentOriginalList.length != 0)) {
            return mSlowMotionPercentOriginalList.clone();
        } else {
            return null;
        }
    }

    private void updateTrimInOutTime() {
        if (mVideoTrack == null) {
            GLog.d(TAG, "updateTrimInOutTime, mVideoTrack is null.");
            return;
        }
        NvsVideoClip clip = mVideoTrack.getClipByIndex(0);
        if (clip == null) {
            GLog.d(TAG, "updateTrimInOutTime, clip is null.");
            return;
        }
        /*
        普通视频、全程慢动作视频拿到正确的TrimEndTime
        非全程慢动作视频拿到的仍是真实时间，而非播放时间的TrimEndTime
        */
        mTrimStartTime = clip.getTrimIn() / MeicamVideoEngine.MILLIS_TIME_BASE;
        mTrimEndTime = clip.getTrimOut() / MeicamVideoEngine.MILLIS_TIME_BASE;
    }


    @Override
    public long getTrimInTime() {
        if (mTrimStartTime == INITNUM) {
            mTrimStartTime = 0;
        }
        return mTrimStartTime;
    }

    @Override
    public long getTrimOutTime() {
        if (mTrimEndTime == INITNUM) {
            updateTrimInOutTime();
        }
        return mTrimEndTime;
    }

    public boolean isVideoClipChanged() {
        return mIsVideoClipChanged;
    }

    public boolean isVideoSpeedChanged() {
        return mIsVideoSpeedChanged;
    }

    @Override
    public void setVideoClipChanged(boolean changed) {
        mIsVideoClipChanged = changed;
    }

    @Override
    public void setVideoSpeedChanged(boolean changed) {
        mIsVideoSpeedChanged = changed;
    }

    @Override
    public void setExtraVideoRotation(int rotation) {
        if (mVideoTrack == null) {
            GLog.e(TAG, "setExtraVideoRotation--mVideoTrack is null");
            return;
        }

        GLog.d(TAG, "setExtraVideoRotation--rotation: " + rotation);
        mExtraVideoRotation = rotation;
        int size = mVideoTrack.getClipCount();
        for (int i = 0; i < size; i++) {
            NvsVideoClip clip = mVideoTrack.getClipByIndex(i);
            if (clip != null) {
                clip.setExtraVideoRotation(rotation);
            }
        }
    }

    @Override
    public int getExtraVideoRotation() {
        return mExtraVideoRotation;
    }

    @Override
    public void resetExtralVideoRotaion(int rotation) {
        if (mVideoTrack == null) {
            GLog.e(TAG, "resetExtralVideoRotation--mVideoTrack is null");
            return;
        }
        setExtraVideoRotation(rotation);
    }

    @Override
    public void setPanAndScan(float pan, float scan) {
        if (mVideoTrack == null) {
            GLog.e(TAG, "setExtraVideoRotation--mVideoTrack is null");
            return;
        }
        int size = mVideoTrack.getClipCount();
        for (int i = 0; i < size; i++) {
            NvsVideoClip clip = mVideoTrack.getClipByIndex(i);
            if (clip != null) {
                clip.setPanAndScan(pan, scan);
            }
        }
    }

    class TrimInfo {

        long mTrimIn;
        long mTrimOut;
        boolean mChangeSpeed;

        TrimInfo(long trimIn, long trimOut, boolean changeSpeed) {
            this.mTrimIn = trimIn;
            this.mTrimOut = trimOut;
            this.mChangeSpeed = changeSpeed;
        }

        @Override
        public String toString() {
            return "TrimInfo{"
                    + "trimIn=" + mTrimIn
                    + ", trimOut=" + mTrimOut
                    + ", changeSpeed=" + mChangeSpeed + '}';
        }
    }

    @Override
    public int getVideoType() {
        return mVideoType;
    }

    /**
     * 当前的视频类型，来源 [VideoTypeParser.VideoType]
     * @return 当前视频类型
     */
    @Override
    public int getEditVideoHdrType() {
        return mVideoEditHdrType;
    }
}
