/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextTranslateStorage.kt
 ** Description : 词条翻译包的数据库存储类
 ** Version     : 1.0
 ** Date        : 2025/6/30 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/30  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.resource.storage

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.resource.room.bean.TextTranslateItem
import com.oplus.gallery.videoeditorpage.resource.room.dao.TextTranslateDao
import com.oplus.gallery.videoeditorpage.resource.room.helper.ResourceDatabaseHelper

class TextTranslateStorage : BaseResourceStorage<TextTranslateItem>() {

    /**
     * 词条翻译包的数据库访问实例
     */
    private val dao: TextTranslateDao = ResourceDatabaseHelper.getInstance().db.textTranslateDao

    /**
     * 根据词条id和语种查询翻译文本
     */
    fun getByIdAndLanguageCode(id: String, languageCode: String): String {
        return dao.getByIdAndLanguageCode(id, languageCode) ?: dao.getDefaultById(id) ?: TextUtil.EMPTY_STRING
    }

    /**
     * 根据词条id删除对应的数据
     */
    fun deleteById(id: String): Int {
        return dao.deleteById(id)
    }

    /**
     * 保存资源项
     * @param items 资源项列表
     */
    override fun save(items: List<TextTranslateItem>): Boolean {
        runCatching {
            val result = dao.insertAll(items)
            return result.all { it != -1L }
        }.onFailure { error ->
            GLog.e(TAG, LogFlag.DL, "save e:$error")
        }
        return false
    }

    override fun save(item: TextTranslateItem): Long = -1

    override fun clear() = Unit

    override fun clearBuiltin() = Unit

    override fun get(id: String): TextTranslateItem? = null

    override fun getLoaded(): MutableList<TextTranslateItem> = mutableListOf()

    override fun getBuiltin(): MutableList<TextTranslateItem> = mutableListOf()

    override fun getBuiltinCount(): Int = 0

    override fun getNetwork(): MutableList<TextTranslateItem> = mutableListOf()

    override fun getAllOrderByPosition(): MutableList<TextTranslateItem> = mutableListOf()


    companion object {
        private const val TAG = "TextTranslateStorage"
    }
}