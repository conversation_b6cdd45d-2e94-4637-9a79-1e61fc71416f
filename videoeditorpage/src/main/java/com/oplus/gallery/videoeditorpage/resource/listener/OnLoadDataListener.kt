/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OnLoadDataListener.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/19		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.listener

import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode

/**
 * 数据加载监听器接口
 *
 * 用于监听数据加载完成或出错时的回调
 */
interface OnLoadDataListener<in T> {

    /**
     * 加载的列表数据刷新，可能会回调多次，例如缓存加载完通知一次，网络请求回来后通知一次
     * @param items 加载的数据列表
     */
    fun onLoadRefresh(items: List<T>)

    /**
     * 列表彻底加载完成后回调
     * @param items 加载的数据列表
     */
    fun onLoadFinish(items: List<T>)

    /**
     * 数据加载出错时调用
     *
     * @param errCode 错误码
     */
    fun onError(errCode: ErrorCode)
}

/**
 * 简化监听数据加载，也可以传入listener代理传递事件
 */
open class SimpleLoadDataListener<in T>(val listener: OnLoadDataListener<T>? = null) : OnLoadDataListener<T> {
    override fun onLoadRefresh(items: List<T>) {
        listener?.onLoadRefresh(items)
    }

    override fun onLoadFinish(items: List<T>) {
        listener?.onLoadFinish(items)
    }

    override fun onError(errCode: ErrorCode) {
        listener?.onError(errCode)
    }
}