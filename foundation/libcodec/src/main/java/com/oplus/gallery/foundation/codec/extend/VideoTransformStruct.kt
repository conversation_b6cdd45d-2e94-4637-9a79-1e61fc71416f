/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoTransformStruct
 ** Description:VideoTransformStruct 结构体
 ** Version: 1.0
 ** Date : 2025/04/25
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/04/27       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.extend

/**
 * HLG 视频下变换参数中，hlgGamma 和 srgbGamma 两条曲线的读取
 *
 * 对应的key为：[EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA]
 */
class VideoTransformStruct(byteArray: ByteArray) : ExtendStruct<VideoTransformData>(byteArray) {
    companion object {
        private const val SIZEGAMMA = 257
    }

    private var hlgGamma: Array<Signed32> = array(arrayOfNulls<Signed32>(SIZEGAMMA))
    private var srgbGamma: Array<Signed32> = array(arrayOfNulls<Signed32>(SIZEGAMMA))

    override fun toData(): VideoTransformData {
        return VideoTransformData(
            hlgGamma.get(),
            srgbGamma.get()
        )
    }
}

data class VideoTransformData(
    var hlgGamma: IntArray,
    var srgbGamma: IntArray
)