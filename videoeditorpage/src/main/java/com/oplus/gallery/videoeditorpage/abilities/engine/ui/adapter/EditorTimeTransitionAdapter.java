/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorTimeTransitionAdapter.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.engine.ui.adapter;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.TIME_BASE;

import android.content.Context;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.transition.BaseTransition;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;

import java.util.ArrayList;
import java.util.List;

public class EditorTimeTransitionAdapter extends RecyclerView.Adapter {
    private static final String TAG = "EditorTimeTransitionAdapter";
    private static final int TYPE_HEAD = 0;
    private static final int TYPE_MID = 1;
    private static final int TYPE_FOOT = 2;
    private static final long MIN_SHOWN_TRANSITION_TIME = TIME_BASE;
    private int mLeftPadding;
    private int mRightPadding;
    private int mButtonHeightAndWidth;
    private Context mContext;
    private List<TransitionData> mItemLengthList = new ArrayList<>();

    public EditorTimeTransitionAdapter(Context context, int leftPadding, int rightPadding, List<IVideoClip> clipList,
                                       IVideoTrack videoTrack,
                                       List<Pair<Integer,Integer>> clipPointList) {
        this.mContext = context;
        this.mLeftPadding = leftPadding;
        this.mRightPadding = rightPadding;
        mButtonHeightAndWidth = context.getResources().getDimensionPixelOffset(R.dimen.edit_timeline_preview_cut_to_button_height);
        init(clipList, videoTrack, clipPointList);
    }

    private void init(List<IVideoClip> clipList, IVideoTrack videoTrack, List<Pair<Integer,Integer>> clipPointList) {
        mItemLengthList.clear();
        TransitionData transitionData = new TransitionData(mLeftPadding, false);
        mItemLengthList.add(transitionData);

        if (clipPointList.size() != clipList.size()) {
            GLog.e(TAG, LogFlag.DL, "clipLengthList.size() != clipList.size():"
                    + clipPointList.size() + ",clipCount = " + clipList.size());
            return;
        }

        IVideoClip iClip = null;
        boolean hasTrans = false;
        BaseTransition transition = null;
        int start = 0;
        int end = 0;
        boolean lengthGreater = false;
        for (int i = 0; i < clipList.size(); i++) {
            iClip = clipList.get(i);
            start = clipPointList.get(i).first;
            end = clipPointList.get(i).second;
            transition = videoTrack.getTransition(i);
            hasTrans = (transition != null);
            lengthGreater = iClip.getDuration() > MIN_SHOWN_TRANSITION_TIME;
            if (i == 0) {
                transitionData = new TransitionData(end - start + mButtonHeightAndWidth / 2, hasTrans);
            } else if (i == clipList.size() - 1) {
                transitionData = new TransitionData(end - start - mButtonHeightAndWidth / 2, hasTrans);
            } else {
                transitionData = new TransitionData(end - start, hasTrans);
            }
            /* comment this code when need show all transition view
            if (!lengthGreater) {
                mItemLengthList.get(i).setShowView(false);
            }
            transitionData.setShowView(lengthGreater);*/
            mItemLengthList.add(transitionData);
        }
        mItemLengthList.add(new TransitionData(mRightPadding, false));
    }

    public void refresh(int index, boolean hasTrans) {
        mItemLengthList.get(index + 1).setHasTrans(hasTrans);
        notifyItemChanged(index + 1);
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return TYPE_HEAD;
        } else if (position == (getItemCount() - 1)) {
            return TYPE_FOOT;
        } else {
            return TYPE_MID;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType) {
        if (viewType == TYPE_HEAD) {
            View view = new View(mContext);
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mLeftPadding, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            return new HeadHolder(view);
        } else if (viewType == TYPE_FOOT) {
            View view = new View(mContext);
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mRightPadding, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            return new FootHolder(view);
        } else {
            return new MyViewHolder(LayoutInflater.from(mContext).inflate(R.layout.engin_timeline_editor_transition_view, viewGroup, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof HeadHolder) {
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                    mItemLengthList.get(position).getLength(),
                    ViewGroup.LayoutParams.MATCH_PARENT
            );
            holder.itemView.setLayoutParams(layoutParams);
        } else if (holder instanceof MyViewHolder) {
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(
                    mItemLengthList.get(position).getLength(),
                    ViewGroup.LayoutParams.MATCH_PARENT
            );
            ((MyViewHolder) holder).mParentLayout.setLayoutParams(layoutParams);
            if (position == mItemLengthList.size() - 2) {
                ((MyViewHolder) holder).mBackgroundView.setVisibility(View.GONE);
            } else {
                if (mItemLengthList.get(position).isShowView()) {
                    ((MyViewHolder) holder).mBackgroundView.setVisibility(View.VISIBLE);
                    if (mItemLengthList.get(position).mHasTrans) {
                        ((MyViewHolder) holder).mBackgroundView.setBackgroundResource(R.drawable.videoeditor_transition_added);
                    } else {
                        ((MyViewHolder) holder).mBackgroundView.setBackgroundResource(R.drawable.videoeditor_transition_normal);
                    }
                } else {
                    ((MyViewHolder) holder).mBackgroundView.setVisibility(View.INVISIBLE);
                }
            }

        }
    }

    @Override
    public int getItemCount() {
        return (mItemLengthList.size());
    }

    public boolean isItemViewShow(int index) {
        if (mItemLengthList.isEmpty() || (index + 1 < 0 || index + 1 > mItemLengthList.size())) {
            return false;
        }
        return mItemLengthList.get(index + 1).isShowView();
    }

    class MyViewHolder extends RecyclerView.ViewHolder {
        RelativeLayout mParentLayout;
        View mBackgroundView;

        MyViewHolder(@NonNull View itemView) {
            super(itemView);
            mParentLayout = itemView.findViewById(R.id.edit_thumbnail_trans_layout);
            mBackgroundView = itemView.findViewById(R.id.edit_thumbnail_trans_view);
        }
    }

    class HeadHolder extends RecyclerView.ViewHolder {

        HeadHolder(@NonNull View itemView) {
            super(itemView);
        }
    }

    class FootHolder extends RecyclerView.ViewHolder {

        FootHolder(@NonNull View itemView) {
            super(itemView);
        }
    }

    class TransitionData {
        int mLength;
        boolean mHasTrans;
        boolean mShowView;

        TransitionData(int length, boolean hasTrans) {
            this.mLength = length;
            this.mHasTrans = hasTrans;
            this.mShowView = true;
        }

        public boolean isShowView() {
            return mShowView;
        }

        public void setShowView(boolean showView) {
            this.mShowView = showView;
        }

        public int getLength() {
            return mLength;
        }

        public void setLength(int length) {
            this.mLength = length;
        }

        public boolean isHasTrans() {
            return mHasTrans;
        }

        public void setHasTrans(boolean hasTrans) {
            this.mHasTrans = hasTrans;
        }
    }
}
