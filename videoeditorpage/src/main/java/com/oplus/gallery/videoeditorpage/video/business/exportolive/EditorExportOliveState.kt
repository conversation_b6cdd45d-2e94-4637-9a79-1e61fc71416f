/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorExportOliveState.kt
 ** Description: 导出实况业务state
 ** Version: 1.0
 ** Date: 2025/6/24
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/24        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.exportolive

import android.app.Activity.RESULT_CANCELED
import android.content.Context
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 导出实况业务state
 */
class EditorExportOliveState(
    val context: Context,
    editorControlView: EditorControlView,
) : EditorBaseState<EditorExportOliveUIController>(TAG, context, editorControlView) {

    override fun createUIController(): EditorExportOliveUIController {
        return EditorExportOliveUIController(context, editorEngine, this)
    }

    override fun showOperaIcon(): Boolean = true

    override fun isSkipAnim(): Boolean = true

    override fun onBackPressed(): Boolean {
        (context as BaseActivity).run {
            setResult(RESULT_CANCELED)
            finish()
        }
        return false
    }

    companion object {
        private const val TAG = "EditorExportOliveState"
    }
}