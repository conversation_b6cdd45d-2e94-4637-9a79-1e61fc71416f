/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MeicamVideoThumbnail.java
 ** Description: manager meicam sdk thumbnail.
 ** Version: 1.0
 ** Date : 2018/01/05
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2018/01/05    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;

import com.meicam.sdk.NvsClip;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryThumbController;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoThumbnail;

import java.util.ArrayList;

public class MeicamVideoThumbnail implements IGalleryVideoThumbnail {

    private static final String TAG = "MeicamVideoThumbnail";

    /*defalut video thumb ratio width : height = 1 : 1*/
    private static final float DEFAULT_THUMB_RATIO = 1.0f;
    /*example:max width (num) * screenwidth*/
    private static final int SUB_TITLE_THUMB_MAX_SHOW_WIDTH_RATE = 3;
    /*example:init in 1/(num) * screenwidth*/
    private static final int SUB_TITLE_THUMB_INIT_POS_RATE = 2;
    /*1s*/
    private static final int ONE_MINI_SECOND = 1000 * TimeUtils.TIME_1_SEC_IN_MS;
    /*单个缩图时间占比比率*/
    private static final float THUMB_SECOND_RATIO = 1.5f;

    private static final float DEFAULT_DETAIL_MODE_SHOW_PERCENT = 0.04f;

    private Context mContext;
    private NvsTimeline mTimeline;
    private NvsVideoTrack mVideoTrack;
    private int mSubTitleThumbHeight;
    private int mVideoOliveThumbHeight;

    private MeicamThumbnailView mTrimNvsThumbnailView;
    private IGalleryThumbController mTrimThumbController;
    private MeicamThumbnailView mSubtitleNvsThumbnailView;
    private IGalleryThumbController mSubTitleThumbController;
    private MeicamThumbnailView mVideoOliveNvsThumbnailView;
    private IGalleryThumbController mVideoOliveThumbController;
    private MeicamThumbnailView mCustomTrimNvsThumbnailView;
    private IGalleryThumbController mCustomTrimThumbController;
    private MeicamVideoEngine mEngine;
    private boolean mOnlyDecodeKeyFrame = false;

    public MeicamVideoThumbnail(Context context, MeicamVideoEngine engine) {
        mContext = context;
        mSubTitleThumbHeight = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_subtitle_thumbnail_view_height);
        mVideoOliveThumbHeight = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_subtitle_thumbnail_view_height);
        mEngine = engine;
    }

    @Override
    public void configOnlyDecodeKeyFrame(boolean onlyDecodeKeyFrame) {
        mOnlyDecodeKeyFrame = onlyDecodeKeyFrame;
    }

    public void setTimeline(NvsTimeline timeline) {
        mTimeline = timeline;
        mVideoTrack = timeline.getVideoTrackByIndex(0);
    }

    private void checkTrimThumbnailView(ViewGroup parent) {
        if (mTrimNvsThumbnailView == null) {
            View thumbView = mEngine.getThumbnailView(parent);
            if (thumbView != null) {
                mTrimNvsThumbnailView = (MeicamThumbnailView) thumbView;
                mTrimThumbController = new MeicamThumbController(mTrimNvsThumbnailView);
            } else {
                GLog.w(TAG, "checkTrimThumbnailView() thumbView is null");
            }
        } else {
            ViewGroup oldParent = (ViewGroup) mTrimNvsThumbnailView.getParent();
            if (oldParent != null) {
                oldParent.removeAllViews();
            }
        }
        parent.addView(mTrimNvsThumbnailView);
        if (parent instanceof GalleryVideoThumbnailView) {
            ((GalleryVideoThumbnailView) parent).setThumbController(mTrimThumbController);
        }
    }

    private void checkSubtitleThumbnailView(ViewGroup parent) {
        if (mSubtitleNvsThumbnailView == null) {
            View thumbView = mEngine.getThumbnailView(parent);
            if (thumbView != null) {
                mSubtitleNvsThumbnailView = (MeicamThumbnailView) thumbView;
                mSubtitleNvsThumbnailView.setOnWidthChangeListener(new MeicamThumbnailView.OnWidthChangeListener() {
                    @Override
                    public void onWidthChange(int width) {
                        int paddingLeftRight = width / SUB_TITLE_THUMB_INIT_POS_RATE;
                        mSubtitleNvsThumbnailView.setStartPadding(paddingLeftRight);
                        mSubtitleNvsThumbnailView.setEndPadding(paddingLeftRight);
                        GLog.d(TAG, "onWidthChange width = " + width);
                    }
                });
                mSubTitleThumbController = new MeicamThumbController(mSubtitleNvsThumbnailView);
            } else {
                GLog.w(TAG, "checkSubtitleThumbnailView() thumbView is null");
            }
        } else {
            ViewGroup oldParent = (ViewGroup) mSubtitleNvsThumbnailView.getParent();
            if (oldParent != null) {
                oldParent.removeAllViews();
            }
        }
        parent.addView(mSubtitleNvsThumbnailView);
        if (parent instanceof GalleryVideoThumbnailView) {
            ((GalleryVideoThumbnailView) parent).setThumbController(mSubTitleThumbController);
        }
    }

    /**
     * 检查视频导出olive的缩图轴组件是否已经初始化
     *
     * @param parent 父组件
     */
    @MainThread
    private void initVideoOliveThumbnailViewIfNeed(ViewGroup parent) {
        if (mVideoOliveNvsThumbnailView == null) {
            View thumbView = mEngine.getThumbnailView(parent);
            if (thumbView != null) {
                mVideoOliveNvsThumbnailView = (MeicamThumbnailView) thumbView;
                mVideoOliveNvsThumbnailView.setOnWidthChangeListener(new MeicamThumbnailView.OnWidthChangeListener() {
                    @Override
                    public void onWidthChange(int width) {
                        int padding = width / SUB_TITLE_THUMB_INIT_POS_RATE;
                        mVideoOliveNvsThumbnailView.setStartPadding(padding);
                        mVideoOliveNvsThumbnailView.setEndPadding(padding);
                        GLog.d(TAG, "checkVideoOliveThumbnailView width = " + width);
                    }
                });
                mVideoOliveThumbController = new MeicamThumbController(mVideoOliveNvsThumbnailView);
            } else {
                GLog.w(TAG, "checkVideoOliveThumbnailView thumbView is null");
            }
        } else {
            ViewGroup oldParent = (ViewGroup) mVideoOliveNvsThumbnailView.getParent();
            if (oldParent != null) {
                oldParent.removeAllViews();
            }
        }
        parent.addView(mVideoOliveNvsThumbnailView);
        if (parent instanceof GalleryVideoThumbnailView) {
            ((GalleryVideoThumbnailView) parent).setThumbController(mVideoOliveThumbController);
        }
    }

    private void checkCustomTrimThumbnailView(ViewGroup parent, int padding) {
        if (mCustomTrimNvsThumbnailView == null) {
            View thumbView = mEngine.getThumbnailView(parent);
            if (thumbView != null) {
                mCustomTrimNvsThumbnailView = (MeicamThumbnailView) thumbView;
                mCustomTrimNvsThumbnailView.setOnWidthChangeListener(width -> {
                    mCustomTrimNvsThumbnailView.setStartPadding(padding);
                    mCustomTrimNvsThumbnailView.setEndPadding(padding);
                });
                mCustomTrimThumbController = new MeicamThumbController(mCustomTrimNvsThumbnailView);
            } else {
                GLog.w(TAG, "checkTrimExportNvsThumbnailView() thumbView is null");
            }
        } else {
            ViewGroup oldParent = (ViewGroup) mCustomTrimNvsThumbnailView.getParent();
            if (oldParent != null) {
                oldParent.removeAllViews();
            }
        }
        parent.addView(mCustomTrimNvsThumbnailView);
        if (parent instanceof GalleryVideoThumbnailView) {
            ((GalleryVideoThumbnailView) parent).setThumbController(mCustomTrimThumbController);
        }
    }

    @Override
    public void showTrimThumbnail(ViewGroup parent) {
        NvsClip curClip = mVideoTrack.getClipByIndex(0);
        showTrimThumbnail(parent, curClip.getTrimIn(), curClip.getTrimOut(),
                curClip.getInPoint(), curClip.getOutPoint());
    }

    @Override
    public void showVideoThumbnail(ViewGroup parent) {
        showTrimThumbnail(parent, 0, mEngine.getGalleryVideoClip().getDuration(),
                0, mEngine.getGalleryVideoClip().getDuration());
    }

    @Override
    public void showTrimDetailThumbnail(ViewGroup parent, long time) {
        long detailIn = 0;
        long detailOut = 0;
        long duration = mTimeline.getDuration();
        long gap = (long) (duration * DEFAULT_DETAIL_MODE_SHOW_PERCENT);
        GLog.d(TAG, "showTrimDetailThumbnail() time:" + time + " gap:" + gap + " duration:" + duration);
        detailIn = (long) (time * (1f - DEFAULT_DETAIL_MODE_SHOW_PERCENT)) * MeicamVideoEngine.MILLIS_TIME_BASE;
        detailOut = detailIn + gap;
        showTrimThumbnail(parent, detailIn, detailOut, 0, detailOut - detailIn);
    }

    private void showTrimThumbnail(ViewGroup parent, long trimIn, long trimOut, long inPoint, long outPoint) {
        checkTrimThumbnailView(parent);
        int thumbnailWidth = parent.getWidth();
        if ((mTrimNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            ArrayList<MeicamThumbnailView.ThumbnailSequenceDesc> infoDescArray = new ArrayList<>();
            NvsClip curClip = mVideoTrack.getClipByIndex(0);
            MeicamThumbnailView.ThumbnailSequenceDesc infoDesc = new MeicamThumbnailView.ThumbnailSequenceDesc();
            infoDesc.onlyDecodeKeyFrame = mOnlyDecodeKeyFrame;
            infoDesc.mediaFilePath = curClip.getFilePath();
            infoDesc.trimIn = trimIn;
            infoDesc.trimOut = trimOut;
            infoDesc.inPoint = inPoint;
            infoDesc.outPoint = outPoint;
            infoDesc.stillImageHint = false;
            if (VideoEditorHelper.isHdrVideoClipType(mEngine.getGalleryVideoClip().getVideoType())) {
                infoDesc.supportHdr = true;
            }
            infoDescArray.add(infoDesc);
            mTrimNvsThumbnailView.setThumbnailSequenceDescArray(infoDescArray);
            mTrimNvsThumbnailView.setThumbnailImageFillMode(MeicamThumbnailView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);
            double showtime = (double) (infoDesc.outPoint - infoDesc.inPoint);
            double mPixelPerMicrosecond = (showtime > 0) ? (thumbnailWidth / showtime) : 0;
            GLog.d(TAG, "showTrimThumbnail()"
                    + ", thumbnailWidth:" + thumbnailWidth
                    + ", mEngine.getGalleryVideoClip().getDuration():" + mEngine.getGalleryVideoClip().getDuration()
                    + ", infoDesc.trimIn:" + infoDesc.trimIn
                    + ", infoDesc.trimOut :" + infoDesc.trimOut
                    + ", infoDesc.mediaFilePath :" + infoDesc.mediaFilePath
            );
            mTrimNvsThumbnailView.setPixelPerMicrosecond(mPixelPerMicrosecond);
            mTrimNvsThumbnailView.setStartPadding(0);
            mTrimNvsThumbnailView.setEndPadding(0);
            mTrimNvsThumbnailView.setThumbnailAspectRatio(DEFAULT_THUMB_RATIO);
            mTrimNvsThumbnailView.setScrollEnabled(false);
        } else {
            GLog.w(TAG, "showTrimThumbnail() error");
        }
    }

    @Override
    public void showFxThumbnail(ViewGroup parent) {
        checkTrimThumbnailView(parent);
        int thumbnailWidth = parent.getWidth();
        if ((mTrimNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            ArrayList<MeicamThumbnailView.ThumbnailSequenceDesc> infoDescArray = new ArrayList<>();
            int count = mVideoTrack.getClipCount();
            double showtime = 0D;
            for (int i = 0; i < count; i++) {
                NvsClip curClip = mVideoTrack.getClipByIndex(i);
                MeicamThumbnailView.ThumbnailSequenceDesc infoDesc = new MeicamThumbnailView.ThumbnailSequenceDesc();
                infoDesc.onlyDecodeKeyFrame = mOnlyDecodeKeyFrame;
                infoDesc.mediaFilePath = curClip.getFilePath();
                infoDesc.trimIn = curClip.getTrimIn();
                infoDesc.trimOut = curClip.getTrimOut();
                infoDesc.inPoint = curClip.getInPoint();
                infoDesc.outPoint = curClip.getOutPoint();
                infoDesc.stillImageHint = false;
                if (VideoEditorHelper.isHdrVideoClipType(mEngine.getGalleryVideoClip().getVideoType())) {
                    infoDesc.supportHdr = true;
                }
                infoDescArray.add(infoDesc);
                showtime += (double) (infoDesc.outPoint - infoDesc.inPoint);
                GLog.d(TAG, "showFxThumbnail()"
                        + ", index:" + i
                        + ", trimIn:" + infoDesc.trimIn
                        + ", trimOut :" + infoDesc.trimOut
                        + ", inPoint:" + infoDesc.inPoint
                        + ", outPoint :" + infoDesc.outPoint
                        + ", width:" + thumbnailWidth
                        + ", getDuration:" + mTimeline.getDuration()
                        + ", mediaFilePath :" + infoDesc.mediaFilePath);
            }
            mTrimNvsThumbnailView.setThumbnailSequenceDescArray(infoDescArray);
            mTrimNvsThumbnailView.setThumbnailImageFillMode(MeicamThumbnailView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);
            double pixelPerMicrosecond = (showtime > 0) ? (thumbnailWidth / showtime) : 0;
            GLog.d(TAG, "showFxThumbnail()"
                    + ", count:" + count
                    + ", showtime:" + showtime
                    + ", pixelPerMicrosecond:" + pixelPerMicrosecond
                    + ", width:" + thumbnailWidth
                    + ", getDuration:" + mTimeline.getDuration());
            mTrimNvsThumbnailView.setPixelPerMicrosecond(pixelPerMicrosecond);
            mTrimNvsThumbnailView.setStartPadding(0);
            mTrimNvsThumbnailView.setEndPadding(0);
            mTrimNvsThumbnailView.setThumbnailAspectRatio(DEFAULT_THUMB_RATIO);
            mTrimNvsThumbnailView.setScrollEnabled(false);
        } else {
            GLog.w(TAG, "showFxThumbnail() error");
        }
    }

    @Override
    public void showSubtitleThumbnail(ViewGroup parent) {
        checkSubtitleThumbnailView(parent);
        int thumbnailWidth = parent.getWidth();
        if ((mSubtitleNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            double pixelPerMicrosecond = (mSubTitleThumbHeight * DEFAULT_THUMB_RATIO) / (float) ONE_MINI_SECOND;
            setMeicamThumbnailViewParam(mSubtitleNvsThumbnailView, thumbnailWidth, pixelPerMicrosecond, "showSubtitleThumbnail");
            mSubtitleNvsThumbnailView.setScrollEnabled(true);
        }
    }

    /**
     * 加载视频导出olive缩图轴组件
     *
     * @param parent 父容器
     */
    @Override
    public void showVideoOliveThumbnail(ViewGroup parent) {
        initVideoOliveThumbnailViewIfNeed(parent);
        int thumbnailWidth = parent.getWidth();
        if ((mVideoOliveNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            // 如果当前视频不满3s，为了让缩图轴在选帧框撑满，需要修改pixelPerMicrosecond
            double pixelPerMicrosecond = 0;
            // 视频时长
            double duration = mTimeline.getDuration();
            // 视频长度是否小于等于3秒
            boolean isShortVideo = isValidShortVideo();
            // 每个缩图占1.5s
            pixelPerMicrosecond = isShortVideo
                    ? (mVideoOliveThumbHeight * AppConstants.Number.NUMBER_2) / duration
                    : (mVideoOliveThumbHeight * DEFAULT_THUMB_RATIO) / (ONE_MINI_SECOND * THUMB_SECOND_RATIO);
            // 给 MeicamThumbnailView 设置参数
            setMeicamThumbnailViewParam(mVideoOliveNvsThumbnailView, thumbnailWidth, pixelPerMicrosecond, "showVideoOliveThumbnail");
            mVideoOliveNvsThumbnailView.setScrollEnabled(true);
            mVideoOliveNvsThumbnailView.setRoundedRadius(parent.getContext().getResources().getDimension(R.dimen.video_edit_olive_trim_frame_radius));
        }
    }

    /**
     * 视频时长是否小于等于3秒
     *
     * @return true/false
     */
    private boolean isValidShortVideo() {
        double duration = mTimeline.getDuration();
        return (duration <= AppConstants.Number.NUMBER_3 * ONE_MINI_SECOND) && (duration > 0);
    }

    /**
     * 给 MeicamThumbnailView 设置参数
     *
     * @param thumbnailView       MeicamThumbnailView
     * @param thumbnailWidth      每张缩略图的宽度
     * @param pixelPerMicrosecond 每微秒所占的像素
     * @param methodName          方法名
     */
    private void setMeicamThumbnailViewParam(MeicamThumbnailView thumbnailView, int thumbnailWidth, double pixelPerMicrosecond, String methodName) {
        ArrayList<MeicamThumbnailView.ThumbnailSequenceDesc> infoDescArray = new ArrayList<>();
        int count = mVideoTrack.getClipCount();
        for (int i = 0; i < count; i++) {
            NvsClip curClip = mVideoTrack.getClipByIndex(i);
            MeicamThumbnailView.ThumbnailSequenceDesc infoDesc = new MeicamThumbnailView.ThumbnailSequenceDesc();
            infoDesc.onlyDecodeKeyFrame = mOnlyDecodeKeyFrame;
            infoDesc.mediaFilePath = curClip.getFilePath();
            infoDesc.trimIn = curClip.getTrimIn();
            infoDesc.trimOut = curClip.getTrimOut();
            infoDesc.inPoint = curClip.getInPoint();
            infoDesc.outPoint = curClip.getOutPoint();
            infoDesc.stillImageHint = false;
            if (VideoEditorHelper.isHdrVideoClipType(mEngine.getGalleryVideoClip().getVideoType())) {
                infoDesc.supportHdr = true;
            }
            infoDescArray.add(infoDesc);
            GLog.d(TAG, methodName
                    + ", index:" + i
                    + ", trimIn:" + infoDesc.trimIn
                    + ", trimOut :" + infoDesc.trimOut
                    + ", inPoint:" + infoDesc.inPoint
                    + ", outPoint :" + infoDesc.outPoint
                    + ", width:" + thumbnailWidth
                    + ", getDuration:" + mTimeline.getDuration()
                    + ", mediaFilePath :" + infoDesc.mediaFilePath);
        }
        thumbnailView.setThumbnailSequenceDescArray(infoDescArray);
        thumbnailView.setThumbnailImageFillMode(MeicamThumbnailView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);

        GLog.d(TAG, methodName
                + ", count:" + count
                + ", DEFAULT_THUMB_RATIO:" + DEFAULT_THUMB_RATIO
                + ", pixelPerMicrosecond:" + pixelPerMicrosecond);
        thumbnailView.setPixelPerMicrosecond(pixelPerMicrosecond);
        thumbnailView.setThumbnailAspectRatio(DEFAULT_THUMB_RATIO);
    }

    @Override
    public void showCustomTrimThumbnail(ViewGroup parent, long time, int padding) {
        checkCustomTrimThumbnailView(parent, padding);
        int thumbnailWidth = parent.getWidth() - 2 * padding;
        if ((mCustomTrimNvsThumbnailView != null) && (mVideoTrack != null) && (mVideoTrack.getClipCount() > 0)) {
            ArrayList<MeicamThumbnailView.ThumbnailSequenceDesc> infoDescArray = new ArrayList<>();
            int count = mVideoTrack.getClipCount();
            for (int i = 0; i < count; i++) {
                NvsClip curClip = mVideoTrack.getClipByIndex(i);
                MeicamThumbnailView.ThumbnailSequenceDesc infoDesc = new MeicamThumbnailView.ThumbnailSequenceDesc();
                infoDesc.onlyDecodeKeyFrame = mOnlyDecodeKeyFrame;
                infoDesc.mediaFilePath = curClip.getFilePath();
                infoDesc.trimIn = curClip.getTrimIn();
                infoDesc.trimOut = curClip.getTrimOut();
                infoDesc.inPoint = curClip.getInPoint();
                infoDesc.outPoint = curClip.getOutPoint();
                infoDesc.stillImageHint = false;
                if (VideoEditorHelper.isHdrVideoClipType(mEngine.getGalleryVideoClip().getVideoType())) {
                    infoDesc.supportHdr = true;
                }
                infoDescArray.add(infoDesc);
                GLog.d(TAG, "showTrimExportThumbnail()"
                        + ", index:" + i
                        + ", trimIn:" + infoDesc.trimIn
                        + ", trimOut :" + infoDesc.trimOut
                        + ", inPoint:" + infoDesc.inPoint
                        + ", outPoint :" + infoDesc.outPoint
                        + ", width:" + thumbnailWidth
                        + ", getDuration:" + mTimeline.getDuration());
            }
            mCustomTrimNvsThumbnailView.setThumbnailSequenceDescArray(infoDescArray);
            mCustomTrimNvsThumbnailView.setThumbnailImageFillMode(MeicamThumbnailView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);

            double pixelPerMicrosecond = (time > 0) ? (1.0 * thumbnailWidth / (time * TimeUtils.TIME_1_MS_IN_US)) : 0;
            GLog.d(TAG, "showTrimExportThumbnail()"
                    + ", count:" + count
                    + ", DEFAULT_THUMB_RATIO:" + DEFAULT_THUMB_RATIO
                    + ", pixelPerMicrosecond:" + pixelPerMicrosecond);
            mCustomTrimNvsThumbnailView.setPixelPerMicrosecond(pixelPerMicrosecond);
            mCustomTrimNvsThumbnailView.setThumbnailAspectRatio(DEFAULT_THUMB_RATIO);
            mCustomTrimNvsThumbnailView.setScrollEnabled(true);
        }
    }

    private static class MeicamThumbController implements IGalleryThumbController {

        private MeicamThumbnailView mNvsMultiThumbnailSequenceView;
        private ThumbScrollerListener mThumbScrollerListener;

        private MeicamThumbnailView.OnScrollChangeListener mNvsScrollListener
                = new MeicamThumbnailView.OnScrollChangeListener() {

            @Override
            public void onScrollChanged(MeicamThumbnailView view, int x, int oldx) {
                if (mThumbScrollerListener != null) {
                    mThumbScrollerListener.onScrolled(x);
                }
            }
        };

        private MeicamThumbnailView.OnScrollStateChangeListener mScrollStateChangeListener
                = new MeicamThumbnailView.OnScrollStateChangeListener() {
            @Override
            public void onScrollStateChanged(@NonNull GalleryHorizontalScrollView styleHorizontalScrollView, int newState) {
                if (mThumbScrollerListener != null) {
                    mThumbScrollerListener.onScrollStateChanged(newState);
                }
            }
        };

        MeicamThumbController(MeicamThumbnailView view) {
            mNvsMultiThumbnailSequenceView = view;
        }

        @Override
        public long mapTimelinePosFromX(int x) {
            return mNvsMultiThumbnailSequenceView.mapTimelinePosFromX(x) / MeicamVideoEngine.MILLIS_TIME_BASE;
        }

        @Override
        public int mapXFromTimelinePos(long time) {
            return mNvsMultiThumbnailSequenceView.mapXFromTimelinePos(time * MeicamVideoEngine.MILLIS_TIME_BASE);
        }

        @Override
        public double getPixelPerMicrosecond() {
            return mNvsMultiThumbnailSequenceView.getPixelPerMicrosecond();
        }

        @Override
        public int getScrollState() {
            return mNvsMultiThumbnailSequenceView.getScrollState();
        }

        @Override
        public void setScrollListener(ThumbScrollerListener scrollerListener) {
            mThumbScrollerListener = scrollerListener;
            mNvsMultiThumbnailSequenceView.setOnScrollChangeListenser(mNvsScrollListener);
            mNvsMultiThumbnailSequenceView.setOnScrollStateChangeListener(mScrollStateChangeListener);
        }

        @Override
        public void setTouchListener(View.OnTouchListener touchListener) {
            mNvsMultiThumbnailSequenceView.setOnTouchListener(touchListener);
        }

        @Override
        public void smoothScrollTo(int x, int y) {
            mNvsMultiThumbnailSequenceView.smoothCOUIScrollTo(x, y);
        }

        @Override
        public void scrollTo(int x, int y) {
            mNvsMultiThumbnailSequenceView.scrollTo(x, y);
        }

        @Override
        public void fullScroll(int pos) {
            mNvsMultiThumbnailSequenceView.fullScroll(pos);
        }

        @Override
        public void stopScroll() {
            mNvsMultiThumbnailSequenceView.stopScroll();
        }
    }
}
