/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryVideoClip.java
 * * Description: IGalleryVideoClip interface.
 * * Version: 1.0
 * * Date : 2017/12/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017video_editor_action_cancel_text_color/12/29    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;


import android.content.Context;
import android.util.Rational;

public interface IGalleryVideoClip {
    /*---------------- video clip start ----------------*/
    boolean initVideoFileInfo(String path, boolean isHfrSlowMotion, int videoType);

    boolean addVideoClip(String uri, String filePath, long dataTaken);

    boolean addVideoClip(String uri, String filePath, long dataTaken, long trimIn, long trimOut);

    String getVideoUri(int index);

    int getVideoCodecType();

    String getVideoFilePath(int index);

    long getVideoDataTaken(int index);

    long getDuration();

    int getDegree();

    boolean checkVideoSupported(String filePath, Context context);

    boolean checkVideoSoftSupported(String filePath, Context context);

    boolean removeVideoClip(int index);

    boolean removeVideoClip(String filePath);

    void trimVideo(long trimIn, long trimOut);

    boolean addSlowMotionVideoClip(String fileUri, String filePath, long dataTaken, int slowfps, int playfps, long[] slowtimelist,
                                   boolean fullslowmotion, boolean isnewslowmotion);

    boolean changeSlowMotion(float enterA, float outA, float enterB, float outB);

    boolean isSupportSlowMotionMode();

    float[] getSlowMotionList();

    float[] getSlowMotionOriginalList();

    long getTrimInTime();

    long getTrimOutTime();

    void setOriginalMusicMute(boolean isMute);

    boolean getOriginalMusicMuted();

    float getSlowSpeed();

    float getTrimVideoSpeed();

    int getVideoFileWidth();

    int getVideoFileHeight();

    boolean isHfrSlowMotion();

    boolean changeVideoSpeed(float speed);

    void setVideoClipChanged(boolean changed);

    void setVideoSpeedChanged(boolean changed);

    void setExtraVideoRotation(int rotation);

    int getExtraVideoRotation();

    void resetExtralVideoRotaion(int rotation);

    Rational getFps();

    int getSlowMotionPlayFps();

    void setPanAndScan(float pan, float scan);

    /**
     * 得到当前视频类型 比如: Dolby Hlg
     *
     * @return 当前视频类型
     */
    int getVideoType();

    /**
     * 当前的视频类型，来源 [VideoTypeParser.VideoType]
     * @return 当前视频类型
     */
    int getEditVideoHdrType();
    /*---------------- video clip end ----------------*/


}
