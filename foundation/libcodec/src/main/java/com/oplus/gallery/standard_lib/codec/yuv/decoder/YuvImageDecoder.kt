/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : YuvImageDecoder.kt
 ** Description : YuvImageDecoder.kt.
 ** Version     : 1.0
 ** Date        : 2020/10/02
 ** Author      : Yong.Ding@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  Yong.Ding@Apps.Gallery3D  2020/10/02  1.0       YuvImageDecoder
 ***********************************************************************/

package com.oplus.gallery.standard_lib.codec.yuv.decoder

import android.graphics.BitmapFactory
import com.oplus.gallery.addon.media.HeifConverterWrapper
import com.oplus.gallery.standard_lib.codec.yuv.common.YuvUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.io.FileDescriptor
import java.io.InputStream

/**
 * 10-bit图片解码器（也可解码普通Heic/Heif图片）
 * 通过传入 fd:[FileDescriptor]\filePath[String]\inputStream[InputStream]实现解码,
 * 返回封装[YuvData]的[YuvDrawable]
 *
 * 示例：
 * YuvImageDecoder.decode(fd)
 * YuvImageDecoder.decode(filePath)
 * YuvImageDecoder.decode(inputStream)
 *
 * TODO yaoweihe YuvImageDecoder相关需要移动到foundation/libcodec中，目前由于有依赖，暂时不动
 */
class YuvImageDecoder {
    companion object {
        private const val TAG = "YuvImageDecoder"
        private const val IN_SAMPLE_SIZE_DEFAULT = 1
    }

    private val converter = HeifConverterWrapper()
    private var isCreateDecoded = false

    /**
     * 创建解码器
     */
    fun createDecoder() {
        if (!isCreateDecoded) {
            converter.createDecoder()
            isCreateDecoded = true
        }
    }

    /**
     * 销毁解码器
     */
    fun destroyDecoder() {
        if (isCreateDecoded) {
            converter.destroyDecoder()
            isCreateDecoded = false
        }
    }

    /**
     * 执行解码
     * @param inputStream 文件流
     * @param options
     */
    fun decode(inputStream: InputStream, options: BitmapFactory.Options?, isDirectBuffer: Boolean): YuvData? {
        try {
            createDecoder()
            val inSampleSize = options?.inSampleSize ?: IN_SAMPLE_SIZE_DEFAULT
            val time = System.currentTimeMillis()
            val heifDecodedFrameWrapper = converter.decode(inputStream, inSampleSize, isDirectBuffer)
            if (GProperty.LOG_OPEN_DEBUG_DECODE) {
                GLog.d(TAG, LogFlag.DL) { "decodeInternalCompatOS12. consume=${(System.currentTimeMillis() - time)}" }
            }
            return YuvUtils.getYuvData(heifDecodedFrameWrapper)
        } catch (e: Exception) {
            GLog.e(TAG, "decode(inputStream, inSampleSize, isDirectBuffer) ", e)
        } finally {
            destroyDecoder()
        }
        return null
    }
}