/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - GalleryVideoEditCutView.java
 * * Description: XXXXXXXXXXXXXXX
 * * Version: 1.0
 * * Date : 2020/03/06
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2020/03/06    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.Transformation;

import androidx.annotation.Nullable;

import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.standard_lib.app.AppConstants.Number;

public class GalleryVideoEditCutView extends View {

    private static final int NO_PRESS = -3;
    private static final int PRESS_OUT_SELECT_RECT = -2;
    private static final int PRESS_IN_SELECT_RECT = -1;
    private static final int LEFT_PRESS = 0;
    private static final int TOP_PRESS = 1;
    private static final int RIGHT_PRESS = 2;
    private static final int BOTTOM_PRESS = 3;
    private static final int LEFT_TOP_PRESS = 4;
    private static final int LEFT_BOTTOM_PRESS = 5;
    private static final int RIGHT_TOP_PRESS = 6;
    private static final int RIGHT_BOTTOM_PRESS = 7;

    private static final int LINE_WIDTH = 4;
    private static final int BOLD_LINE_WIDTH = 8;
    private static final int BOLD_LENGTH = 60;
    private static final int BOLD_LENGTH_CENTER = 90;
    private static final int PRESS_LENGTH = 80;
    private static final int MIN_SELECT_WIDTH = 240;
    private static final int MIN_SELECT_HEIGHT = 240;
    private static final int LINE_SELECT_WIDTH = 6;
    private static final int LINE_PAINT_COLOR = Color.argb(255, 255, 255, 255);
    private static final int LINE_PAINT_CENTER_COLOR = Color.argb(128, 255, 255, 255);
    private static final int BOLD_LINE_PAINT_COLOR = Color.argb(255, 255, 255, 255);

    private static final long ANIMATION_DURATION = 200;

    private int mPressState = NO_PRESS;
    private Paint mLinePaint;
    private Paint mLineCenterPaint;
    private Paint mBoldLinePaint;
    private Paint mBackgroundPaint;
    private int mDownX;
    private int mDownY;
    private int mDl;
    private int mDt;
    private int mDr;
    private int mDb;
    private Rect mAnimationStartRect;
    private int mAnimationDl;
    private int mAnimationDr;
    private int mAnimationDt;
    private int mAnimationDb;
    private int mLineCenterWidth = 0;

    private Rect mSelectRect = new Rect(0, 0, 0, 0);


    public GalleryVideoEditCutView(Context context) {
        super(context);
        init();
    }

    public GalleryVideoEditCutView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public Rect getSelectRect() {
        return mSelectRect;
    }

    private void init() {
        mLineCenterWidth = getResources().getDimensionPixelSize(R.dimen.editor_cut_view_center_line_width);

        mBackgroundPaint = new Paint();
        int bgColor = getContext().getColor(com.oplus.gallery.basebiz.R.color.videoeditor_video_editor_background_color_edit);
        mBackgroundPaint.setColor(bgColor);

        mLinePaint = new Paint();
        mLinePaint.setColor(LINE_PAINT_COLOR);

        mBoldLinePaint = new Paint();
        mBoldLinePaint.setColor(BOLD_LINE_PAINT_COLOR);

        mLineCenterPaint = new Paint();
        mLineCenterPaint.setColor(LINE_PAINT_CENTER_COLOR);
    }

    public void setSelectRect(int l, int t, int r, int b) {
        mSelectRect.left = l;
        mSelectRect.top = t;
        mSelectRect.right = r;
        mSelectRect.bottom = b;
        invalidate();
    }

    public void setSelectRect(Rect rect) {
        mSelectRect = rect;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        int w = getWidth();
        int h = getHeight();
        // 绘制遮罩
        canvas.drawRect(0, 0, w, mSelectRect.top, mBackgroundPaint);
        canvas.drawRect(0, 0, mSelectRect.left, h, mBackgroundPaint);
        canvas.drawRect(0, mSelectRect.bottom, w, h, mBackgroundPaint);
        canvas.drawRect(mSelectRect.right, 0, w, h, mBackgroundPaint);

        //绘制边框线
        canvas.drawRect(mSelectRect.left, mSelectRect.top, mSelectRect.right,
                mSelectRect.top + LINE_WIDTH, mLinePaint);
        canvas.drawRect(mSelectRect.left, mSelectRect.top + LINE_WIDTH, mSelectRect.left + LINE_WIDTH,
                mSelectRect.bottom - LINE_WIDTH, mLinePaint);
        canvas.drawRect(mSelectRect.right - LINE_WIDTH, mSelectRect.top + LINE_WIDTH, mSelectRect.right,
                mSelectRect.bottom - LINE_WIDTH, mLinePaint);
        canvas.drawRect(mSelectRect.left, mSelectRect.bottom - LINE_WIDTH, mSelectRect.right,
                mSelectRect.bottom, mLinePaint);

//        drawBold(canvas);
        draw3x3Grid(canvas);
    }

    private void drawPressBold(Canvas canvas) {
        if (mPressState == RIGHT_PRESS) {
            canvas.drawRect(mSelectRect.right - BOLD_LINE_WIDTH, mSelectRect.top,
                    mSelectRect.right - BOLD_LINE_WIDTH + LINE_SELECT_WIDTH, mSelectRect.bottom, mBoldLinePaint);
        } else if (mPressState == LEFT_PRESS) {
            canvas.drawRect(mSelectRect.left + BOLD_LINE_WIDTH - LINE_SELECT_WIDTH, mSelectRect.top,
                    mSelectRect.left + BOLD_LINE_WIDTH, mSelectRect.bottom, mBoldLinePaint);
        } else if (mPressState == TOP_PRESS) {
            canvas.drawRect(mSelectRect.left, mSelectRect.top + BOLD_LINE_WIDTH - LINE_SELECT_WIDTH,
                    mSelectRect.right, mSelectRect.top + BOLD_LINE_WIDTH, mBoldLinePaint);
        } else if (mPressState == BOTTOM_PRESS) {
            canvas.drawRect(mSelectRect.left, mSelectRect.bottom - BOLD_LINE_WIDTH, mSelectRect.right,
                    mSelectRect.bottom - BOLD_LINE_WIDTH + LINE_SELECT_WIDTH, mBoldLinePaint);
        }
    }

    private void draw3x3Grid(Canvas canvas) {
        int wight3 = mSelectRect.width() / Number.NUMBER_3;
        int height3 = mSelectRect.height() / Number.NUMBER_3;
        canvas.drawRect(mSelectRect.left + wight3,
                mSelectRect.top + LINE_WIDTH,
                mSelectRect.left + wight3 + mLineCenterWidth,
                mSelectRect.bottom - LINE_WIDTH,
                mLineCenterPaint);
        canvas.drawRect(mSelectRect.left + wight3 * Number.NUMBER_2,
                mSelectRect.top + LINE_WIDTH,
                mSelectRect.left + wight3 * Number.NUMBER_2 + mLineCenterWidth,
                mSelectRect.bottom - LINE_WIDTH,
                mLineCenterPaint);
        canvas.drawRect(mSelectRect.left + LINE_WIDTH,
                mSelectRect.top + height3,
                mSelectRect.right - LINE_WIDTH,
                mSelectRect.top + height3 + mLineCenterWidth,
                mLineCenterPaint);
        canvas.drawRect(mSelectRect.left + LINE_WIDTH,
                mSelectRect.top + height3 * Number.NUMBER_2,
                mSelectRect.right - LINE_WIDTH,
                mSelectRect.top + height3 * Number.NUMBER_2 + mLineCenterWidth,
                mLineCenterPaint);
//            drawPressBold(canvas);
    }

    private void drawBold(Canvas canvas) {
        // draw bold line in 4 corner
        canvas.drawRect(mSelectRect.left, mSelectRect.top, mSelectRect.left + BOLD_LENGTH, mSelectRect.top + BOLD_LINE_WIDTH,
                mBoldLinePaint);
        canvas.drawRect(mSelectRect.left, mSelectRect.top, mSelectRect.left + BOLD_LINE_WIDTH, mSelectRect.top + BOLD_LENGTH,
                mBoldLinePaint);

        canvas.drawRect(mSelectRect.right - BOLD_LENGTH, mSelectRect.top, mSelectRect.right, mSelectRect.top + BOLD_LINE_WIDTH,
                mBoldLinePaint);
        canvas.drawRect(mSelectRect.right - BOLD_LINE_WIDTH, mSelectRect.top, mSelectRect.right, mSelectRect.top + BOLD_LENGTH,
                mBoldLinePaint);

        canvas.drawRect(mSelectRect.left, mSelectRect.bottom - BOLD_LINE_WIDTH, mSelectRect.left + BOLD_LENGTH, mSelectRect.bottom,
                mBoldLinePaint);
        canvas.drawRect(mSelectRect.left, mSelectRect.bottom - BOLD_LENGTH, mSelectRect.left + BOLD_LINE_WIDTH, mSelectRect.bottom,
                mBoldLinePaint);

        canvas.drawRect(mSelectRect.right - BOLD_LENGTH, mSelectRect.bottom - BOLD_LINE_WIDTH, mSelectRect.right, mSelectRect.bottom,
                mBoldLinePaint);
        canvas.drawRect(mSelectRect.right - BOLD_LINE_WIDTH, mSelectRect.bottom - BOLD_LENGTH, mSelectRect.right, mSelectRect.bottom,
                mBoldLinePaint);

        // draw bold line in 4 side line

        canvas.drawRect(mSelectRect.left + mSelectRect.width() / Number.NUMBER_2 - BOLD_LENGTH_CENTER / Number.NUMBER_2, mSelectRect.top,
                mSelectRect.left + mSelectRect.width() / Number.NUMBER_2 + BOLD_LENGTH_CENTER / Number.NUMBER_2,
                mSelectRect.top + BOLD_LINE_WIDTH, mBoldLinePaint);
        canvas.drawRect(mSelectRect.left + mSelectRect.width() / Number.NUMBER_2 - BOLD_LENGTH_CENTER / Number.NUMBER_2,
                mSelectRect.bottom - BOLD_LINE_WIDTH,
                mSelectRect.left + mSelectRect.width() / Number.NUMBER_2 + BOLD_LENGTH_CENTER / Number.NUMBER_2, mSelectRect.bottom, mBoldLinePaint);

        canvas.drawRect(mSelectRect.left, mSelectRect.top + mSelectRect.height() / Number.NUMBER_2 - BOLD_LENGTH_CENTER / Number.NUMBER_2,
                mSelectRect.left + BOLD_LINE_WIDTH,
                mSelectRect.top + mSelectRect.height() / Number.NUMBER_2 + BOLD_LENGTH_CENTER / Number.NUMBER_2, mBoldLinePaint);
        canvas.drawRect(mSelectRect.right - BOLD_LINE_WIDTH,
                mSelectRect.top + mSelectRect.height() / Number.NUMBER_2 - BOLD_LENGTH_CENTER / Number.NUMBER_2,
                mSelectRect.right, mSelectRect.top + mSelectRect.height() / Number.NUMBER_2 + BOLD_LENGTH_CENTER / Number.NUMBER_2, mBoldLinePaint);
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // close touch Event in constant mode
        return false;
        /*switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mIsCutting = true;
                mDownX = (int) event.getX();
                mDownY = (int) event.getY();
                mPressState = getPressState(mDownX, mDownY);
                mDl = mSelectRect.left;
                mDr = mSelectRect.right;
                mDt = mSelectRect.top;
                mDb = mSelectRect.bottom;
                break;
            case MotionEvent.ACTION_MOVE:
                int x = (int) event.getX();
                int y = (int) event.getY();
                cut(x, y);
                invalidate();
                break;
            case MotionEvent.ACTION_UP:
                mIsCutting = false;
                mPressState = NO_PRESS;
                startAnimationSetRect(getTargetSelectRect(mSelectRect));
                break;
        }
        return true;*/
    }


    public void startAnimationSetRect(Rect target) {
        mAnimationStartRect = new Rect(mSelectRect);
        mAnimationDl = target.left - mAnimationStartRect.left;
        mAnimationDr = target.right - mAnimationStartRect.right;
        mAnimationDt = target.top - mAnimationStartRect.top;
        mAnimationDb = target.bottom - mAnimationStartRect.bottom;
        Animation ani = new Animation() {
            @Override
            protected void applyTransformation(float interpolatedTime, Transformation t) {
                updateViewByTime(interpolatedTime);
            }
        };
        ani.setDuration(ANIMATION_DURATION);
        this.startAnimation(ani);
    }

    private void updateViewByTime(float time) {
        mSelectRect.left = mAnimationStartRect.left + (int) (mAnimationDl * time);
        mSelectRect.right = mAnimationStartRect.right + (int) (mAnimationDr * time);
        mSelectRect.top = mAnimationStartRect.top + (int) (mAnimationDt * time);
        mSelectRect.bottom = mAnimationStartRect.bottom + (int) (mAnimationDb * time);
        invalidate();
    }


    private Rect getTargetSelectRect(Rect rect) {
        Rect result = new Rect(0, 0, 0, 0);
        int width = rect.right - rect.left;
        int height = rect.bottom - rect.top;
        float ra = (float) width / height;
        if (ra < (float) getWidth() / getHeight()) {
            height = getHeight();
            width = (int) (height * ra);
        } else {
            width = getWidth();
            height = (int) (width / ra);
        }
        result.left = (getWidth() - width) / Number.NUMBER_2;
        result.right = result.left + width;
        result.top = (getHeight() - height) / Number.NUMBER_2;
        result.bottom = result.top + height;
        return result;
    }

    private int getPressState(int x, int y) {
        if ((x < mSelectRect.left + PRESS_LENGTH) && (x > mSelectRect.left)) {
            if ((y < mSelectRect.top + PRESS_LENGTH) && (y > mSelectRect.top)) {
                return LEFT_TOP_PRESS;
            } else if ((y > mSelectRect.bottom - PRESS_LENGTH) && (y < mSelectRect.bottom)) {
                return LEFT_BOTTOM_PRESS;
            } else if ((y >= mSelectRect.top + PRESS_LENGTH) && (y <= mSelectRect.bottom - PRESS_LENGTH)) {
                return LEFT_PRESS;
            }
        } else if ((x > mSelectRect.right - PRESS_LENGTH) && (x < mSelectRect.right)) {
            if ((y < mSelectRect.top + PRESS_LENGTH) && (y > mSelectRect.top)) {
                return RIGHT_TOP_PRESS;
            } else if ((y > mSelectRect.bottom - PRESS_LENGTH) && (y < mSelectRect.bottom)) {
                return RIGHT_BOTTOM_PRESS;
            } else if ((y >= mSelectRect.top + PRESS_LENGTH) && (y <= mSelectRect.bottom - PRESS_LENGTH)) {
                return RIGHT_PRESS;
            }
        } else if ((x >= mSelectRect.left + PRESS_LENGTH) && (x <= mSelectRect.right - PRESS_LENGTH)) {
            if ((y < mSelectRect.top + PRESS_LENGTH) && (y > mSelectRect.top)) {
                return TOP_PRESS;
            } else if ((y > mSelectRect.bottom - PRESS_LENGTH) && (y < mSelectRect.bottom)) {
                return BOTTOM_PRESS;
            }
        }
        if ((x < mSelectRect.left) || (x > mSelectRect.right) || (y < mSelectRect.top) || (y > mSelectRect.bottom)) {
            return PRESS_OUT_SELECT_RECT;
        }
        return PRESS_IN_SELECT_RECT;
    }

    private int checkSelectNewTop(int newT) {
        newT = ((mSelectRect.bottom - newT) < MIN_SELECT_HEIGHT) ? (mSelectRect.bottom - MIN_SELECT_HEIGHT) : newT;
        newT = Math.max(newT, 0);
        return newT;
    }

    private int checkSelectNewBottom(int newB) {
        newB = ((newB - mSelectRect.top) < MIN_SELECT_HEIGHT) ? (mSelectRect.top + MIN_SELECT_HEIGHT) : newB;
        newB = (newB > getHeight()) ? getHeight() : newB;
        return newB;
    }

    private int checkSelectNewLeft(int newL) {
        newL = Math.min(newL, mSelectRect.right - MIN_SELECT_WIDTH);
        newL = Math.max(newL, 0);
        return newL;
    }

    private int checkSelectNewRight(int newR) {
        newR = Math.max(newR, mSelectRect.left + MIN_SELECT_WIDTH);
        newR = (newR > getWidth()) ? getWidth() : newR;
        return newR;
    }

    private void cut(int cX, int cY) {
        if (mPressState == PRESS_IN_SELECT_RECT) {
            return;
        }
        int dx = cX - mDownX;
        int dy = cY - mDownY;

        switch (mPressState) {
            case BOTTOM_PRESS:
                mSelectRect.bottom = checkSelectNewBottom(mDb + dy);
                break;
            case TOP_PRESS:
                mSelectRect.top = checkSelectNewTop(mDt + dy);
                break;
            case LEFT_PRESS:
                mSelectRect.left = checkSelectNewLeft(mDl + dx);
                break;
            case RIGHT_PRESS:
                mSelectRect.right = checkSelectNewRight(mDr + dx);
                break;
            case LEFT_TOP_PRESS:
                mSelectRect.left = checkSelectNewLeft(mDl + dx);
                mSelectRect.top = checkSelectNewTop(mDt + dy);
                break;
            case LEFT_BOTTOM_PRESS:
                mSelectRect.left = checkSelectNewLeft(mDl + dx);
                mSelectRect.bottom = checkSelectNewBottom(mDb + dy);
                break;
            case RIGHT_TOP_PRESS:
                mSelectRect.right = checkSelectNewRight(mDr + dx);
                mSelectRect.top = checkSelectNewTop(mDt + dy);
                break;
            case RIGHT_BOTTOM_PRESS:
                mSelectRect.right = checkSelectNewRight(mDr + dx);
                mSelectRect.bottom = checkSelectNewBottom(mDb + dy);
                break;
            default:
                break;
        }
    }


}
