/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OnLoadDataListener.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/19		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.listener

interface OnLoadDataListener<T> {

    fun onFinish(code: Int, allItem: List<T>?)

    fun onRefresh(code: Int, item: T)

    fun onError(errCode: Int)
}