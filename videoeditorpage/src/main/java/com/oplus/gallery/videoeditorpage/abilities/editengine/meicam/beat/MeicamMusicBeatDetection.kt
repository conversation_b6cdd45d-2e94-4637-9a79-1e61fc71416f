/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamMusicBeatDetection.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.beat

import com.meicam.sdk.NvsBeatDetection
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import kotlin.system.measureTimeMillis

/**
 * NvsBeatDetectionManager 类用于管理音乐卡点和音乐节拍的检测功能。
 * 该类封装了与音乐节拍检测相关的操作，提供了对音乐节拍的检测、分析和处理功能。
 *
 * @see <a href="https://www.meishesdk.com/android/doc_ch/html/content/classcom_1_1meicam_1_1sdk_1_1NvsBeatDetection.html#ad9da4f9bed5a7536c96891b5405d376a">NvsBeatDetection 文档</a>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/23
 */
class MeicamMusicBeatDetection : IMuscBeatDetection {

    private val nvsBeatCallback by lazy {
        object : NvsBeatDetection.BeatDetectionCallback {
            override fun onBeatDetectionFinished(rhythmPoints: ArrayList<Long?>?, beats: ArrayList<Long?>?, errorType: Int) {
                beatCallback.getOrLog("onBeatDetectionFinished,beatCallback is null")?.also {
                    it.onBeatDetectionFinished(rhythmPoints, beats, errorType)
                }
            }

            override fun onBeatDetectionProgress(progress: Float) {
                beatCallback.getOrLog("onBeatDetectionProgress,beatCallback is null")?.also {
                    it.onBeatDetectionProgress(progress = progress)
                }
            }
        }
    }

    /**
     * 获取音乐节奏检测类 (必须进行初始化后才能使用)
     */
    private val beatDetection by lazy {
        NvsBeatDetection.init(0).apply { setBeatDetectionCallback(nvsBeatCallback) }
    }

    private var beatCallback: MusicBeatDetectionCallback? = null


    /**
     * 开始检测
     *
     * 该函数用于检测输入音乐文件中的节奏点，并根据给定的敏感度参数判断相邻节奏点的时间间隔。
     *
     * @param inputFilePath 输入的音乐文件路径，用于指定需要检测的音乐文件。
     * @param sensitivity 相邻的两个节奏点的最小时间间隔，用于控制检测的敏感度。时间单位：毫秒
     * @return 如果成功检测到节奏点并完成处理，则返回true；否则返回false。
     */
    override fun startDetect(inputFilePath: String, sensitivity: Int): Boolean {
        return beatDetection.startDetect(inputFilePath, sensitivity)
    }

    /**
     * 设置音乐节奏检测回调接口
     */
    override fun setBeatDetectionCallback(callback: MusicBeatDetectionCallback) {
        this.beatCallback = callback
    }


    /**
     * 必须调 否则容易内存泄露
     * 	销毁音乐节奏检测类实例。注意: 销毁之后可以再次创建及获取
     */
    override fun close() {
        release()
    }

    override fun release() {
        val costTime = measureTimeMillis {
            beatDetection.setBeatDetectionCallback(null)
            NvsBeatDetection.close()
            beatCallback = null
        }
        GLog.d(TAG, LogFlag.DL, "release, costTime=$costTime")
    }

    interface MusicBeatDetectionCallback {
        /**
         * 当节拍检测完成时调用的回调函数。
         * 该函数用于处理节拍检测完成后的逻辑，包括更新节拍数据、关闭加载对话框以及在UI线程中更新UI控制器。
         *
         * @param rhythmPoints 检测完成后的节奏时间点列表，表示每个节奏的时间戳。
         * @param beats 检测完成后的节拍时间点列表，表示每个节拍的时间戳。
         * @param errorType 错误类型，表示检测过程中可能出现的错误类型。
         */
        fun onBeatDetectionFinished(rhythmPoints: ArrayList<Long?>?, beats: ArrayList<Long?>?, errorType: Int)

        /**
         * 当节拍检测进度更新时调用的回调函数。
         *
         * @param progress 当前的节拍检测进度，范围为0.0到1.0，表示检测的完成度。
         */
        fun onBeatDetectionProgress(progress: Float) {

        }

    }

    companion object {
        private const val TAG = "MeicamMusicBeatDetection"
    }
}