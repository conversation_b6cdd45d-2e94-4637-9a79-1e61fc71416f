/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SongByTagResponseBean.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/26
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/26		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.data

import com.google.gson.annotations.SerializedName

class SongByTagResponseBean {

    @SerializedName("version")
    var totalVersion: Long? = null

    @SerializedName("list")
    var songList: List<SongListBean>? = null

    class SongListBean : BaseResourceBean() {

        @SerializedName("songId")
        var songId: String? = null

        @SerializedName("zhName")
        var zhName: String? = null

        @SerializedName("chName")
        var chName: String? = null

        @SerializedName("enName")
        var enName: String? = null

        @SerializedName("iconPath")
        var iconPath: String? = null

        @SerializedName("songFilePath")
        var filePath: String? = null

        @SerializedName("songFileMd5")
        var fileHash: String? = null

        @SerializedName("songFileSize")
        var fileSize: Long = 0

        @SerializedName("position")
        var position: Int = -1

        @SerializedName("isPreset")
        var isPreset = false

        @SerializedName("tag")
        var tag: String? = null

        @SerializedName("category")
        var category: String? = null

        @SerializedName("source")
        var source: String? = null

        @SerializedName("updateTime")
        var updateTime: String? = null

        @SerializedName("version")
        var version: Long = -1

        @SerializedName("audioTimeLength")
        var audioTimeLength: Long = 0

        override fun toString(): String {
            return "SongListBean(songId=$songId, zhName=$zhName, fileSize=$fileSize, position=$position, version=$version, " +
                    "audioTimeLength=$audioTimeLength)"
        }
    }

    override fun toString(): String {
        return "SongByTagResponseBean(totalVersion=$totalVersion, songList=$songList)"
    }
}