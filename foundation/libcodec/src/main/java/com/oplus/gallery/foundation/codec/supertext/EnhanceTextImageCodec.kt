/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EnhanceTextImageCodec
 ** Description: EnhanceTextImageCodec
 ** Version: 1.0
 ** Date : 2022/5/17
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/05/17    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.supertext

import com.oplus.gallery.foundation.codec.supertext.SuperTextProperties.Companion.ENHANCE_MAGIC_NUMBER

/**
 * 超级文本1.0图片的编解码
 * 定义为 internal，上层业务不能直接new EnhanceTextImageCodec（），需要通过SuperTextCodecFactory创建来使用
 *
 */
internal class EnhanceTextImageCodec(filePath: String?, magicNumber: ByteArray) : BaseSuperTextImageCodec(filePath, magicNumber) {

    override fun getTag(): String {
        return TAG
    }
    /**
     * 超级文本的幻数标记
     * @return  byte[]
     */
    override fun getMagicNumberFlag(): ByteArray {
        return ENHANCE_MAGIC_NUMBER
    }

    /**
     * 是否EnhanceText （相机1.0模式拍出来的照片]）
     *
     * @return true or false
     */
    override fun isEnhanceText(): Boolean {
        return true
    }

    private companion object {
        private const val TAG = "EnhanceTextImageCodec"
    }
}