/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ClipModel.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.track.model;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.params.SpeederParams;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;

import java.util.List;

/**
 * 片段模型
 */
public class ClipModel {
    public final static long TRIM_INFINITE = 3600 * 100000;
    /**
     * 无效的位置。
     */
    public final static long INVALID_POSITION = -1;
    private static final String TAG = "ClipModel";
    private int mTrackIndex;
    private int mClipInTrackIndex;
    private long mTrimIn;
    private long mTrimOut;
    private long mDuration;
    private long mInPoint;
    private String mFilePath;
    private int mType;
    private IClip mClip;
    private Object mExtra;
    private IBaseClip mVideoFx;
    private String mDescription;
    private double mSpeed = 1.0d;
    /**
     * 变速参数的描述
     */
    private String mSpeederDesc;
    /**
     * 变速参数的描述
     */
    private boolean mIsCurveSpeedEffect;
    private List<TagType> mTags;
    private BaseCaption mCaption;
    private boolean mSelectable = true;
    private boolean mHideTransition = false;
    /**
     * see {@link BusinessAttached}
     */
    private int mBusinessAttached = -1;

    public ClipModel() {
    }

    public ClipModel(@NonNull IClip clip) {
        boolean isCurveSpeedEffect = isCurveSpeedEffect(clip);
        mTrimIn = getActualPositionInClip(clip, clip.getTrimIn(), isCurveSpeedEffect);
        mInPoint = clip.getInPoint();
        mTrimOut = getActualPositionInClip(clip, clip.getTrimOut(), isCurveSpeedEffect);
        mFilePath = clip.getFilePath();
        mSpeed = clip.getSpeed();
        mClip = clip;
        mDuration = isCurveSpeedEffect ? clip.getDuration() : 0;
        setSpeederInfo(clip);
    }

    public IClip getClip() {
        return mClip;
    }

    public boolean getIsInMainTrack() {
        return mTrackIndex == 0;
    }

    public void setVideoFx(IBaseClip fx) {
        mVideoFx = fx;
    }

    public IBaseClip getVideoFx() {
        return mVideoFx;
    }

    public void setTag(List<TagType> tags) {
        mTags = tags;
    }

    public List<TagType> getTags() {
        return mTags;
    }

    public void setClip(@NonNull IClip clip) {
        boolean isCurveSpeedEffect = isCurveSpeedEffect(clip);
        mTrimIn = getActualPositionInClip(clip, clip.getTrimIn(), isCurveSpeedEffect);
        mInPoint = clip.getInPoint();
        mTrimOut = getActualPositionInClip(clip, clip.getTrimOut(), isCurveSpeedEffect);
        mFilePath = clip.getFilePath();
        mSpeed = clip.getSpeed();
        mClip = clip;
        mDuration = isCurveSpeedEffect ? clip.getDuration() : 0;
        setSpeederInfo(clip);
    }

    /**
     * 根据剪辑对象和位置获取实际在时间线上的位置。
     * 如果剪辑应用了曲线速度效果，则根据曲线速度计算实际位置；
     * 否则，直接返回输入的位置。
     *
     * @param clip 剪辑对象，类型为IClip。
     * @param position 在剪辑中的位置。
     * @param isCurveSpeedEffect 布尔值，表示是否应用了曲线速度效果。
     * @return 实际在时间线上的位置，如果计算失败则返回输入的位置。
     */
    private long getActualPositionInClip(IClip clip, long position, boolean isCurveSpeedEffect) {
        if (!isCurveSpeedEffect || !(clip instanceof IVideoClip)) {
            return position;
        }
        IVideoClip videoClip = (IVideoClip) clip;
        long positionInTimeline = videoClip.getTimelinePositionWithSpeedCurves(position);
        return (positionInTimeline != INVALID_POSITION) ? positionInTimeline : position;
    }

    /**
     * 设置视频片段的变速相关信息
     */
    private void setSpeederInfo(IClip clip) {
        if (!(clip instanceof IVideoClip)) {
            return;
        }
        SpeederParams speederParams = ((IVideoClip) clip).getSpeederParams();
        mSpeederDesc = speederParams.getSpeedDesc();
        mIsCurveSpeedEffect = speederParams.isCurveSpeed();
    }

    public int getTrackIndex() {
        return mTrackIndex;
    }

    public void setTrackIndex(int trackIndex) {
        this.mTrackIndex = trackIndex;
    }

    public int getClipInTrackIndex() {
        return mClipInTrackIndex;
    }

    public void setClipInTrackIndex(int clipInTrackIndex) {
        this.mClipInTrackIndex = clipInTrackIndex;
    }

    public long getTrimIn() {
        return mTrimIn;
    }

    public void setTrimIn(long trimIn) {
        this.mTrimIn = trimIn;
    }

    public long getTrimOut() {
        return mTrimOut;
    }

    public void setTrimOut(long trimOut) {
        this.mTrimOut = trimOut;
    }
    /**
     * 判断当前的剪辑是否为曲线变速剪辑。
     */
    public boolean isCurveSpeedEffect(IClip clip) {
        if (!(clip instanceof IVideoClip)) {
            GLog.d(TAG, LogFlag.DL, "[isCurveSpeedEffect] clip is not an instance of IVideoClip or is null");
            return false;
        }
        IVideoClip videoClip = (IVideoClip) clip;
        return videoClip.getSpeederParams().isCurveSpeed();
    }

    public long getRealDuration() {
        if ((mDuration == 0) && (mClip != null)) {
            mDuration = mClip.getFileDuration();
        }
        return (long) (mDuration / mSpeed);
    }

    public void setRealDuration(long duration) {
        this.mDuration = (long) (duration * mSpeed);
    }

    public long getAdjustedDuration() {
        return (long) ((mTrimOut - mTrimIn) / mSpeed);
    }

    public long getInPoint() {
        return mInPoint;
    }

    /**
     * 获取根据倍数变化后的片段结束时间
     * @return 片段结束时间
     */
    public long getAdjustedOutPoint() {
        return (mInPoint + getAdjustedDuration());
    }

    /**
     * 是否包含position
     * @param position 位置
     * @return true 包含，false 不包含
     */
    public boolean contains(long position) {
        return (mInPoint <= position) && (position <= getAdjustedOutPoint());
    }

    /**
     * 是否包含position，左闭右开
     * @param position 位置
     * @return true 包含，false 不包含
     */
    public boolean until(long position) {
        return (mInPoint <= position) && (position < getAdjustedOutPoint());
    }

    public void setInPoint(long inPoint) {
        this.mInPoint = inPoint;
    }

    public String getFilePath() {
        return mFilePath;
    }

    public void setFilePath(String filePath) {
        this.mFilePath = filePath;
    }

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        this.mType = type;
    }

    public Object getClipExtra() {
        return mExtra;
    }

    public void setClipExtra(Object extra) {
        this.mExtra = extra;
    }

    public String getClipDescription() {
        return mDescription;
    }

    public void setClipDescription(String description) {
        this.mDescription = description;
    }

    public BaseCaption getCaption() {
        return mCaption;
    }

    public void setCaption(BaseCaption caption) {
        this.mCaption = caption;
    }


    public double getSpeed() {
        return mSpeed;
    }

    /**
     * 获取变速参数的描述
     */
    public String getSpeederDesc() {
        return mSpeederDesc;
    }

    /**
     * 获取是否应用了曲线速度效果
     *
     * @return boolean true表示应用曲线速度效果，false表示未应用
     */
    public boolean isCurveSpeedEffect() {
        return mIsCurveSpeedEffect;
    }

    public void setSelectable(boolean selectable) {
        this.mSelectable = selectable;
    }

    public boolean getSelectable() {
        return mSelectable;
    }

    public int getBusinessAttached() {
        return mBusinessAttached;
    }

    public void setBusinessAttached(int mBusinessAttached) {
        this.mBusinessAttached = mBusinessAttached;
    }

    /**
     * 是否为实况图片
     * @return 是否为实况图片
     */
    public boolean getIsOlivePhoto() {
        if (mClip instanceof IVideoClip) {
            return ((IVideoClip) mClip).getIsOlivePhoto();
        }
        return false;
    }

    @NonNull
    @Override
    public String toString() {
        return "@" + Integer.toHexString(hashCode()) + ":"
            + " {trackIndex: " + getTrackIndex() + ", clipIndex: " + getClipInTrackIndex()
            + ", inPoint: " + getInPoint() + ", outPoint: " + getAdjustedOutPoint()
            + ", trimIn: " + getTrimIn() + ", trimOut: " + getTrimOut()
            + ", adjustDuration: " + getAdjustedDuration() + ", realDuration: " + getRealDuration() + "}";
    }

    public static class BusinessAttached {
        public static final int NONE = -1;//无应用业务
        public static final int COMMON = 0;//通用业务，仅缩放live window宽度
        public static final int BUSINESS_CARTOON = 1;//动画
        public static final int BUSINESS_MASK = 2;//蒙版遮罩
    }

    public static class ClipType {
        public static final int CLIP_VIDEO = 1;
        public static final int CLIP_PICTURE = 2;
        public static final int CLIP_MUSIC = 3;
        public static final int CLIP_TEXT = 4;
        public static final int CLIP_EFFECT = 5;
        public static final int CLIP_TEXT_AI = 6;
        public static final int CLIP_MUSIC_CAPTION = 7;
        public static final int CLIP_FILTER = 8;
        public static final int CLIP_CAPTION_TEMPLATE = 9;
        public static final int CLIP_ADJUST = 10;
    }

    public static class TransitionType {
        public static final int TRANSITION_LEFT = 1;
        public static final int TRANSITION_RIGHT = 2;
    }

    public static class TagType {
        public static final int FX = 1;
        public static final int FILTER = 2;
        public int mType;
        public String mName;

        public TagType(int type, String name) {
            mType = type;
            mName = name;
        }
    }
}
