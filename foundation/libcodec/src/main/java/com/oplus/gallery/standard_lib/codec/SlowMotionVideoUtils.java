/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - SlowMotionVideoUtils.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2019/12/02
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/12/02  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec;

import android.content.Context;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.text.TextUtils;
import android.util.Range;
import android.util.Size;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.text.TextUtil;

import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

public class SlowMotionVideoUtils {
    private static final String TAG = "SlowMotionVideoUtils";

    public static final String HSR_SLOW_MOTION_SUFFIX = "0slow_motion_hsr";
    public static final String HFR_SLOW_MOTION_SUFFIX = "0slow_motion_hfr";

    /*
    for normal video, we use 25 for a better filter performance
    */
    public static final int DEFAULT_OPTIMAZED_FPS = 25;
    public static final int SUPPORT_SLOW_MOTION_DEFAULT_FPS = 30;
    public static final int SUPPORT_SLOW_MOTION_FPS_120 = 120;
    public static final int SUPPORT_SLOW_MOTION_FPS_240 = 240;
    public static final int SUPPORT_SLOW_MOTION_FPS_480 = 480;

    public static final int MILLIS_TIME = TimeUtils.MILLISECOND_IN_SECOND;

    private static final int SLOW_MOTION_TAG_PARTS = 2;
    private static final int SLOW_MOTION_FPS_PART = 0;
    private static final int SLOW_MOTION_RANGE_PART = 1;

    /**
     * slow motion title example: 0slow_motion_hsr_120:12,220,300,900 or op**_0slow_motion_hsr_120:12,220,300,900
     * 对标题使用":"分割，对分割后的前一个字符串使用"_"分割，有”op**_“前缀时得到5个字符串，没有”op**_“前缀时得到4个
     */
    private static final int SLOW_MOTION_FPS_FOUR_PARTS = 4;
    private static final int SLOW_MOTION_FPS_FIVE_PARTS = 5;
    private static final int SLOW_MOTION_RANGE_PARTS = 4;

    private static final int SLOW_MOTION_SEC_A_START = 0;
    private static final int SLOW_MOTION_SEC_A_END = 1;
    private static final int SLOW_MOTION_SEC_B_START = 2;
    private static final int SLOW_MOTION_SEC_B_END = 3;
    private static final int SLOW_MOTION_FPS_POS = 4;

    /**
     * An enclose class to restore Slow motion property
     */
    public static class SlowMotion {
        public static final int RANG_START = 0;
        public static final int RANG_END = 1;
        public static final int TYPE_NORMAL = 0;
        public static final int TYPE_HSR = 1;
        public static final int TYPE_HFR = 2;
        private static final int RANG_EDGES = 2;

        private String mTag = null;
        private long mDuration = 0L;
        private int mSlowSpeed = 0;
        private long[][] mSlowMotionRangs = null;
        private int mSlowMotionType = TYPE_NORMAL;
        private float mSlowMotionFPS = DEFAULT_OPTIMAZED_FPS;

        private SlowMotion(
                String tag,
                int slowMotionType,
                long duration,
                int slowMotionFPS,
                int slowMotionRangeCount
        ) {
            mTag = tag;
            mDuration = duration;
            mSlowMotionType = slowMotionType;
            mSlowMotionFPS = slowMotionFPS;
            mSlowMotionRangs = new long[slowMotionRangeCount][RANG_EDGES];
            if (slowMotionFPS == SUPPORT_SLOW_MOTION_FPS_120) {
                mSlowSpeed = (SUPPORT_SLOW_MOTION_FPS_120 / SUPPORT_SLOW_MOTION_DEFAULT_FPS);
            } else if (slowMotionFPS == SUPPORT_SLOW_MOTION_FPS_240) {
                mSlowSpeed = SUPPORT_SLOW_MOTION_FPS_240 / SUPPORT_SLOW_MOTION_DEFAULT_FPS;
            } else if (slowMotionFPS == SUPPORT_SLOW_MOTION_FPS_480) {
                mSlowSpeed = SUPPORT_SLOW_MOTION_FPS_480 / SUPPORT_SLOW_MOTION_DEFAULT_FPS;
            }
        }

        public String getTag() {
            return mTag;
        }

        public int getRangeCount() {
            return mSlowMotionRangs.length;
        }

        public int getSlowMotionType() {
            return mSlowMotionType;
        }

        public float getSlowMotionFPS() {
            return mSlowMotionFPS;
        }

        public long getSectionPosition(int section, int edge) {
            return mSlowMotionRangs[section][edge];
        }

        public boolean isFullRangeSlowMotion(long duration) {
            return (mSlowMotionRangs.length == 1)
                    && (mSlowMotionRangs[0][RANG_START] == 0)
                    && (mSlowMotionRangs[0][RANG_END] == duration);
        }

        public static String videoTypeToString(int type) {
            switch (type) {
                case TYPE_NORMAL:
                    return "NORMAL";
                case TYPE_HFR:
                    return "SLOW_MOTION_HFR";
                case TYPE_HSR:
                    return "SLOW_MOTION_HSR";
                default:
                    return "UNKNOWN";
            }
        }

        /**
         * 从播放时间的进度计算对应视频的真实时间
         *
         * @param percent
         * @return
         */
        public long reflectSlowMotionTime(float percent) {
            if ((mSlowMotionType == TYPE_NORMAL) || (mSlowSpeed == 0)) {
                return (long) (percent * mDuration);
            }
            return reflectSlowMotionTime((long) (percent * mapSlowMotionTime(mDuration)));
        }

        /**
         * 从播放时间计算对应视频的真实时间
         *
         * @param playTime
         * @return
         */
        public long reflectSlowMotionTime(long playTime) {
            int slowSpeed = mSlowSpeed;
            if ((mSlowMotionType == TYPE_NORMAL) || (slowSpeed == 0)) {
                return playTime;
            }
            long realTime = 0L;
            if (mSlowMotionRangs.length < 1) {
                return playTime;
            }
            long sectionAStart = mSlowMotionRangs[0][RANG_START];
            long sectionAEnd = mSlowMotionRangs[0][RANG_END];
            if (mSlowMotionType == TYPE_HFR) {
                long playAStart = (long) ((float) sectionAStart / (float) slowSpeed);
                long playAEnd = (long) ((float) sectionAStart / (float) slowSpeed) + sectionAEnd - sectionAStart;
                if ((playAStart > 0) && (playTime <= playAStart)) {
                    realTime = playTime * slowSpeed;
                } else if (playTime <= playAEnd) {
                    realTime = sectionAStart + (playTime - playAStart);
                } else {
                    realTime = sectionAEnd + (playTime - playAEnd) * slowSpeed;
                }
            } else {
                // mSlowMotionType == TYPE_HSR
                long sectionBStart = 0L;
                long sectionBEnd = 0L;
                if (mSlowMotionRangs.length >= SLOW_MOTION_TAG_PARTS) {
                    sectionBStart = mSlowMotionRangs[1][RANG_START];
                    sectionBEnd = mSlowMotionRangs[1][RANG_END];
                }
                if ((sectionAStart > 0) && (playTime <= sectionAStart)) {
                    realTime = playTime;
                } else if ((sectionAEnd > 0) && (playTime <= sectionAEnd + (sectionAEnd - sectionAStart) * (slowSpeed - 1))) {
                    realTime = (playTime + sectionAStart * (slowSpeed - 1)) / slowSpeed;
                } else if ((sectionBStart > 0) && (playTime <= sectionBStart + (sectionAEnd - sectionAStart) * (slowSpeed - 1))) {
                    realTime = playTime - (sectionAEnd - sectionAStart) * (slowSpeed - 1);
                } else {
                    long slowSegmentTime = (sectionAEnd - sectionAStart + sectionBEnd - sectionBStart) * (slowSpeed - 1);
                    if ((sectionBEnd > 0) && (playTime <= sectionBEnd + slowSegmentTime)) {
                        realTime = (playTime - (sectionAEnd - sectionAStart - sectionBStart) * (slowSpeed - 1)) / slowSpeed;
                    } else {
                        realTime = playTime - slowSegmentTime;
                    }
                }
            }
            return realTime;
        }

        /**
         * 从视频的真实时间计算对应播放时间
         * 比如慢动作视频本身存储视频是10s,需要放慢播放10倍,播放时间就是100s
         * 对应真实时间5s的画面就是播放时间50s
         *
         * @param realTime
         * @return
         */
        public long mapSlowMotionTime(long realTime) {
            int slowSpeed = mSlowSpeed;
            if ((mSlowMotionType == TYPE_NORMAL) || (slowSpeed == 0)) {
                return realTime;
            }
            long playTime = 0L;
            if (mSlowMotionRangs.length < 1) {
                return playTime;
            }
            long sectionAStart = mSlowMotionRangs[0][RANG_START];
            long sectionAEnd = mSlowMotionRangs[0][RANG_END];
            if (mSlowMotionType == TYPE_HFR) {
                long playAStart = (long) ((float) sectionAStart / (float) slowSpeed);
                long playAEnd = (long) ((float) sectionAStart / (float) slowSpeed) + sectionAEnd - sectionAStart;
                if ((sectionAStart > 0) && (realTime <= sectionAStart)) {
                    playTime = (long) ((float) realTime / (float) (slowSpeed));
                } else if ((sectionAEnd > 0) && (realTime <= sectionAEnd)) {
                    playTime = playAStart + (realTime - sectionAStart);
                } else if (realTime >= playAEnd) {
                    playTime = sectionAEnd - sectionAStart
                            + (long) ((float) (realTime - (sectionAEnd - sectionAStart)) / (float) slowSpeed);
                }
            } else {
                // mSlowMotionType == TYPE_HSR
                long sectionBStart = 0L;
                long sectionBEnd = 0L;
                if (mSlowMotionRangs.length >= SLOW_MOTION_TAG_PARTS) {
                    sectionBStart = mSlowMotionRangs[1][RANG_START];
                    sectionBEnd = mSlowMotionRangs[1][RANG_END];
                }
                if ((sectionAStart > 0) && (realTime <= sectionAStart)) {
                    playTime = realTime;
                } else if ((sectionAEnd > 0) && (realTime <= sectionAEnd)) {
                    playTime = realTime + (realTime - sectionAStart) * (slowSpeed - 1);
                } else if ((sectionBStart > 0) && (realTime <= sectionBStart)) {
                    playTime = realTime + (sectionAEnd - sectionAStart) * (slowSpeed - 1);
                } else if ((sectionBEnd > 0) && (realTime <= sectionBEnd)) {
                    playTime = realTime + (sectionAEnd - sectionAStart + realTime - sectionBStart) * (slowSpeed - 1);
                } else {
                    playTime = realTime + (sectionAEnd - sectionAStart + sectionBEnd - sectionBStart) * (slowSpeed - 1);
                }
            }
            return playTime;
        }

        @NonNull
        @Override
        public String toString() {
            if (mSlowMotionType == TYPE_NORMAL) {
                return "[INVALID_SLOW_MOTION]";
            } else {
                /**
                 * E.G.
                 * [HFR@30fps:(0, 100)-(200, 300)]
                 */
                StringBuilder builder = new StringBuilder();
                builder.append("[").append(videoTypeToString(mSlowMotionType))
                        .append("@").append(mSlowMotionFPS).append("fps:");

                final int sections = mSlowMotionRangs.length;
                for (int sec = 0; sec < sections; sec++) {
                    builder.append("(").append(mSlowMotionRangs[sec][RANG_START])
                            .append(", ").append(mSlowMotionRangs[sec][RANG_END])
                            .append(")-");
                }
                if (sections > 0) {
                    builder.deleteCharAt(builder.length() - 1);
                }
                return builder.toString();
            }
        }

        /**
         * A builder for building SlowMotion
         */
        private static class Builder {
            private List<long[]> mContent = new LinkedList<>();
            private int mFPS = 0;
            private int mType = TYPE_NORMAL;
            private String mTag = TextUtil.EMPTY_STRING;
            private long mDuration = 0L;

            public Builder setTag(String tag) {
                mTag = tag;
                return Builder.this;
            }

            public Builder setDuration(long duration) {
                mDuration = duration;
                return Builder.this;
            }

            public Builder appendSlowMotion(long start, long end) {
                mContent.add(new long[]{start, end});
                return Builder.this;
            }

            public Builder setSlowMotionFPS(int fps) {
                mFPS = fps;
                return Builder.this;
            }

            public Builder setSlowMotionType(int type) {
                mType = type;
                return Builder.this;
            }

            public SlowMotion build() {
                final int count = mContent.size();
                SlowMotion slowMotion = new SlowMotion(mTag, mType, mDuration, mFPS, count);
                for (int i = 0; i < count; i++) {
                    slowMotion.mSlowMotionRangs[i] = mContent.get(i);
                }
                mContent.clear();
                return slowMotion;
            }
        }

    }

    public static boolean isSlowMotionVideo(String tag) {
        return isHFR(tag) || isHSR(tag);
    }

    public static boolean isHSR(String tag) {
        return !TextUtils.isEmpty(tag) && tag.toLowerCase().contains(HSR_SLOW_MOTION_SUFFIX);
    }

    public static boolean isHFR(String tag) {
        return !TextUtils.isEmpty(tag) && tag.toLowerCase().contains(HFR_SLOW_MOTION_SUFFIX);
    }

    /**
     * 通过慢动作的区间列表解析慢动作格式
     * list长度为2为全程慢动作hfr,长度为4为非全程慢动作hsr
     *
     * @param context
     * @param slowMotionPercentList
     * @param frameRate
     * @param videoWidth
     * @param videoHeight
     * @param duration
     * @return
     */
    public static SlowMotion parseSlowMotion(
            Context context,
            boolean isHFR,
            float[] slowMotionPercentList,
            int frameRate,
            int videoWidth,
            int videoHeight,
            long duration
    ) {
        if (slowMotionPercentList.length < SLOW_MOTION_SEC_A_END + 1) {
            return null;
        } else if (isHFR) {
            int startA = (int) (slowMotionPercentList[SLOW_MOTION_SEC_A_START] * duration / MILLIS_TIME);
            int endA = (int) (slowMotionPercentList[SLOW_MOTION_SEC_A_END] * duration / MILLIS_TIME);
            String tag = HFR_SLOW_MOTION_SUFFIX + "_" + frameRate + ":" + startA + "," + endA + ",0,0";
            return parseSlowMotion(context, tag, videoWidth, videoHeight, duration / MILLIS_TIME);
        } else {
            int startA = (int) (slowMotionPercentList[SLOW_MOTION_SEC_A_START] * duration / MILLIS_TIME);
            int endA = (int) (slowMotionPercentList[SLOW_MOTION_SEC_A_END] * duration / MILLIS_TIME);
            int startB = 0;
            int endB = 0;
            if (slowMotionPercentList.length == SLOW_MOTION_SEC_B_END + 1) {
                startB = (int) (slowMotionPercentList[SLOW_MOTION_SEC_B_START] * duration / MILLIS_TIME);
                endB = (int) (slowMotionPercentList[SLOW_MOTION_SEC_B_END] * duration / MILLIS_TIME);
            }
            String tag = HSR_SLOW_MOTION_SUFFIX + "_" + frameRate + ":" + startA + "," + endA + "," + startB + ',' + endB;
            return parseSlowMotion(context, tag, videoWidth, videoHeight, duration / MILLIS_TIME);
        }
    }

    /**
     * Detect the slow motion properties from the specified tag
     * For now, the slow motion video has 2 types: HSR and HFR
     * HSR is an old version of slow motion video, only R15 can support
     * E.G. "op**_0slow_motion_hsr_120:12,220,300,900"
     * HFR is a standard slow motion video spec and now is using this version all the way
     * E.G. "op**_0slow_motion_hfr_120:0,0,0,0"
     *
     * @param tag
     * @return
     */
    public static SlowMotion parseSlowMotion(Context context, String tag,
                                             int videoWidth, int videoHeight, long duration) {
        final boolean isHFR = isHFR(tag);
        final boolean isHSR = isHSR(tag);

        if (!isHFR && !isHSR) {
            return null;
        }

        long sectionAStart = 0;
        long sectionAEnd = 0;
        long sectionBStart = 0;
        long sectionBEnd = 0;
        int slowMotionFPS = 0;
        int slowMotionType = SlowMotion.TYPE_NORMAL;

        try {
            final SlowMotion.Builder builder = new SlowMotion.Builder()
                    .setTag(tag)
                    .setDuration(duration);

            String[] motion = tag.split(":");
            if (motion.length == SLOW_MOTION_TAG_PARTS) {
                String[] fps = motion[SLOW_MOTION_FPS_PART].split("_");
                String[] time = motion[SLOW_MOTION_RANGE_PART].split(",");
                if ((time.length == SLOW_MOTION_RANGE_PARTS) && ((fps.length == SLOW_MOTION_FPS_FIVE_PARTS) || (fps.length == SLOW_MOTION_FPS_FOUR_PARTS))) {
                    sectionAStart = Long.parseLong(time[SLOW_MOTION_SEC_A_START]);
                    sectionAEnd = Long.parseLong(time[SLOW_MOTION_SEC_A_END]);
                    sectionBStart = Long.parseLong(time[SLOW_MOTION_SEC_B_START]);
                    sectionBEnd = Long.parseLong(time[SLOW_MOTION_SEC_B_END]);
                    slowMotionFPS = Integer.parseInt(fps[fps.length - 1]);
                    slowMotionType = isHFR(tag) ? SlowMotion.TYPE_HFR
                            : (isHSR(tag) ? SlowMotion.TYPE_HSR : SlowMotion.TYPE_NORMAL);

                    if ((slowMotionFPS != SUPPORT_SLOW_MOTION_FPS_120)
                            && (slowMotionFPS != SUPPORT_SLOW_MOTION_FPS_240)
                            && (slowMotionFPS != SUPPORT_SLOW_MOTION_FPS_480)) {
                        GLog.w(TAG, String.format(Locale.ENGLISH,
                                "[parseSlowMotion] slow motion FPS isn't support, current is %dfps adjust to %dfps",
                                slowMotionFPS, DEFAULT_OPTIMAZED_FPS));
                        builder.setSlowMotionType(SlowMotion.TYPE_NORMAL);
                        builder.setSlowMotionFPS(DEFAULT_OPTIMAZED_FPS);
                    } else {
                        builder.setSlowMotionType(slowMotionType);
                        builder.setSlowMotionFPS(slowMotionFPS);
                        /*
                         * because the slow time  writeln by camera into database may be difference with
                         * the time writeln by media coder into the file
                         * we need adjust incorrect slow motion time
                         */
                        if (sectionAStart >= duration) {
                            /*
                             * No section A and no section B => full slow motion
                             *  - sectionAStart = 0;
                             *  - sectionAEnd   = 0;
                             *  - sectionBStart = 0;
                             *  - sectionBEnd   = 0;
                             */
                            builder.appendSlowMotion(0, duration);
                        } else if (sectionAEnd >= duration) {
                            /*
                             * No section B and section A is spread to the end
                             *  - sectionAStart = NO_CHANGE;
                             *  - sectionAEnd   = duration;
                             *  - sectionBStart = 0;
                             *  - sectionBEnd   = 0;
                             */
                            builder.appendSlowMotion(sectionAStart, duration);
                        } else if (sectionBStart >= duration) {
                            /*
                             * No section B
                             *  - sectionAStart = NO_CHANGE;
                             *  - sectionAEnd   = NO_CHANGE;
                             *  - sectionBStart = 0;
                             *  - sectionBEnd   = 0;
                             */
                            builder.appendSlowMotion(sectionAStart, sectionAEnd);
                        } else if (sectionBEnd >= duration) {
                            /*
                             * Section B is spread to the end
                             *  - sectionAStart = NO_CHANGE;
                             *  - sectionAEnd   = NO_CHANGE;
                             *  - sectionBStart = NO_CHANGE;
                             *  - sectionBEnd   = duration;
                             */
                            builder.appendSlowMotion(sectionAStart, sectionAEnd);
                            builder.appendSlowMotion(sectionBStart, duration);
                        } else {
                            if ((sectionAStart == 0) && (sectionAEnd == 0)) {
                                builder.appendSlowMotion(0, duration);
                            } else {
                                builder.appendSlowMotion(sectionAStart, sectionAEnd);
                                if (!((sectionBStart == 0) && (sectionBEnd == 0))) {
                                    builder.appendSlowMotion(sectionBStart, sectionBEnd);
                                }
                            }
                        }
                    }

                    if (slowMotionType == SlowMotion.TYPE_HFR) {
                        if (!isCameraSupportHFR(context, videoWidth, videoHeight, slowMotionFPS)) {
                            builder.setSlowMotionType(SlowMotion.TYPE_NORMAL);
                        }
                    }
                }
            }

            return builder.build();
        } catch (Exception exp) {
            GLog.w(TAG, String.format(Locale.ENGLISH,
                    "[parseSlowMotion] an error occurred during tag parsing, tag is '%s'",
                    tag), exp);
            return null;
        }
    }


    public static long getHSRSlowMotionTime(long timelineCurrent, float[] slowList, long total, int slowExpansion) {
        long current = timelineCurrent;
        if ((slowList == null) || (current == 0)) {
            return current;
        }
        if ((slowList.length > SLOW_MOTION_SEC_B_START) && (slowList[SLOW_MOTION_SEC_B_END] > 0)
                && (current > slowList[SLOW_MOTION_SEC_B_END] * total)) {
            current = current
                    + (long) ((slowList[SLOW_MOTION_SEC_A_END] - slowList[SLOW_MOTION_SEC_A_START]) * total * (slowExpansion - 1))
                    + (long) ((slowList[SLOW_MOTION_SEC_B_END] - slowList[SLOW_MOTION_SEC_B_START]) * total * (slowExpansion - 1));

        } else if ((slowList.length > SLOW_MOTION_SEC_B_START) && (slowList[SLOW_MOTION_SEC_B_START] > 0)
                && (current > slowList[SLOW_MOTION_SEC_B_START] * total)) {
            current = (long) (slowList[SLOW_MOTION_SEC_B_START] * total)
                    + (long) ((slowList[SLOW_MOTION_SEC_A_END] - slowList[SLOW_MOTION_SEC_A_START]) * total) * (slowExpansion - 1)
                    + (long) (current - slowList[SLOW_MOTION_SEC_B_START] * total) * slowExpansion;

        } else if ((slowList[SLOW_MOTION_SEC_A_END] > 0) && (current > slowList[SLOW_MOTION_SEC_A_END] * total)) {
            current = current
                    + (long) ((slowList[SLOW_MOTION_SEC_A_END] - slowList[SLOW_MOTION_SEC_A_START]) * total) * (slowExpansion - 1);

        } else if ((slowList[SLOW_MOTION_SEC_A_START] >= 0) && (current > (slowList[SLOW_MOTION_SEC_A_START] * total))) {
            current = (long) (slowList[SLOW_MOTION_SEC_A_START] * total)
                    + (long) (current - slowList[SLOW_MOTION_SEC_A_START] * total) * slowExpansion;

        }
        return current;
    }

    public static long getHFRSlowMotionTime(long timelineCurrent, float[] slowList, long total, int slowExpansion) {
        long current = timelineCurrent;
        if ((slowList == null) || (current == 0) || (slowList.length != 2)) {
            return current;
        }
        if ((current > slowList[SLOW_MOTION_SEC_A_END] * total)) {
            current = (long) ((current + ((slowList[SLOW_MOTION_SEC_A_START] - slowList[SLOW_MOTION_SEC_A_END]) * total)) / slowExpansion)
                    + (long) ((slowList[SLOW_MOTION_SEC_A_END] - slowList[SLOW_MOTION_SEC_A_START]) * total);
        } else if ((slowList[SLOW_MOTION_SEC_A_START] >= 0) && (current > (slowList[SLOW_MOTION_SEC_A_START] * total))) {
            current = current - (long) (slowList[SLOW_MOTION_SEC_A_START] * total) + (long) ((slowList[SLOW_MOTION_SEC_A_START] * total) / slowExpansion);
        } else {
            current = current / slowExpansion;
        }
        return current;
    }

    public static boolean isCameraSupportHFR(Context context, int width, int height, int fps) {
        if ((width <= 0) || (height <= 0)) {
            return false;
        }

        if (context.getApplicationContext() == null) {
            GLog.w(TAG, LogFlag.DL, "context is null");
            return false;
        }

        try {
            CameraManager manager = (CameraManager)context.getApplicationContext().getSystemService(Context.CAMERA_SERVICE);
            String[] cameraIds = manager.getCameraIdList();
            for (String cameraId : cameraIds) {
                CameraCharacteristics characteristics = manager.getCameraCharacteristics(cameraId);
                if (characteristics == null) {
                   continue;
                }
                StreamConfigurationMap map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
                if (map == null) {
                    continue;
                }
                Size[] sizes = map.getHighSpeedVideoSizes();
                for (Size size : sizes) {
                    Range<Integer>[] ranges = map.getHighSpeedVideoFpsRangesFor(size);
                    for (Range<Integer> range : ranges) {
                        if (range != null) {
                            if ((fps == range.getUpper()) && ((width * height) <= (size.getHeight() * size.getWidth()))) {
                                return true;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, String.format(Locale.ENGLISH,
                    "[isCameraSupportHFR] High speed video FPS ranges for %dfps and size(%d, %d) cannot be found",
                    fps, width, height));
        }
        return false;
    }

}
