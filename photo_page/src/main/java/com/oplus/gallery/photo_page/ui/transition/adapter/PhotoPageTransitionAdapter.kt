/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoPageTransitionAdapter
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/6/4
 ** Author      : <PERSON><PERSON>.Zhuang@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>.<PERSON><PERSON>@Apps.Gallery3D     2025/6/4      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.transition.adapter

import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.Surface
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.business.contentloading.ContentFactory.Companion.LOAD_TYPE_PAGE_THUMBNAIL
import com.oplus.gallery.business.contentloading.SizeType
import com.oplus.gallery.business_lib.model.data.base.item.calculateCropRect
import com.oplus.gallery.business_lib.transition.BasePhotoPageTransitionAdapter
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.ui.animation.AnimationKeyParams
import com.oplus.gallery.foundation.ui.autocrop.CropRect
import com.oplus.gallery.foundation.ui.autocrop.CropRectSet
import com.oplus.gallery.basebiz.transition.IPhotoTransitionThumbnailAdapter
import com.oplus.gallery.basebiz.transition.PageTransitionMaker
import com.oplus.gallery.basebiz.transition.PhotoPageTransitionManager
import com.oplus.gallery.basebiz.transition.PreviewTransition
import com.oplus.gallery.basebiz.transition.adapter.PhotoAutoCropTransitionThumbnailAdapter
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.math.Math2DUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import kotlinx.coroutines.cancel

/**
 * 大图页面转场动画适配器
 */
class PhotoPageTransitionAdapter(
    private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : BasePhotoPageTransitionAdapter() {

    /**
     * 大图 ViewModel
     */
    private val viewModel get() = sectionPage.pageViewModel

    /**
     * 大图入场动画结束，大图内容显示区域
     */
    private lateinit var onGetConstraintRect: () -> Rect

    /**
     * 大图页面过渡动画中使用的图片过渡实例
     */
    private lateinit var previewTransition: PreviewTransition

    init {
        viewModel.pageManagement.firstFrameRenderingStatus.observe(sectionPage.pageInstance) {
            mIsFirstFrameReady = viewModel.pageManagement.firstFrameRenderingStatus.value?.let {
                it.isThumbnailReady || it.isContentReady
            } ?: false
        }
    }

    override fun getExitTransition(): PhotoPageTransitionManager.TransitionArgument? {
        return viewModel.pageManagement.exitTransition.value
    }

    override fun getFocusSlot(): Int {
        var slot = sectionPage.pageViewModel.dataLoading.focusSlot
        if (slot == INVALID_INDEX) {
            // focusSlot 没有时，用传入的 focus（从相册进大图会有值），其余场景默认 -1，不记录此次调用
            slot = sectionPage.pageViewModel.inputArguments.dataSource.value?.focus ?: INVALID_INDEX
        }
        return slot
    }

    override fun getFocusSlotRotation(): Int {
        return viewModel.dataLoading.focusItemViewData
            ?.supportedAbilities
            ?.getInt(SlotOverlayHelper.SUPPORT_THUMB_ROTATION)
            ?: Math2DUtil.DEG_0I
    }

    override fun getFocusSlotCropRect(focusItemRect: Rect?): CropRect? {
        var cropRect: CropRect? = null
        val viewData = viewModel.dataLoading.focusItemViewData ?: run {
            GLog.d(TAG, "getFocusSlotCropRect. focusView is null. skip.")
            return null
        }
        if ((focusItemRect != null) && !focusItemRect.isEmpty) {
            cropRect = viewModel.dataLoading.getMediaItem(viewData)?.calculateCropRect(focusItemRect.width(), focusItemRect.height())
        }
        if (cropRect != null) {
            GLog.d(TAG, "getFocusSlotCropRect. Have found cropRect from mediaItem. cropRect=$cropRect")
        } else {
            GLog.w(TAG, "getFocusSlotCropRect. Can't found cropRect. Use default.")
        }
        return cropRect
    }

    override fun getFocusPagerThumbnail(callback: (Drawable) -> Unit) {
        val defaultTransitionDrawable: Drawable = viewModel.pageManagement.defaultExitTransitionDrawable
        viewModel.dataLoading.focusItemViewData?.let { itemViewData ->
            val previewShot = viewModel.inputArguments.previewShot.value?.thumbnail
            val itemPath = viewModel.inputArguments.previewShot.value?.itemPath
            val viewDataId = viewModel.contentLoading.focusPagerThumbnail.value?.viewData?.id
            val focusPagerThumbnailDrawable = viewModel.contentLoading.focusPagerThumbnail.value?.thumbnail
            if ((focusPagerThumbnailDrawable != null) && (focusPagerThumbnailDrawable !is ColorDrawable)) {
                GLog.d(TAG) { "[getFocusPagerThumbnail] focusPagerThumbnailDrawable viewData=$itemViewData" }
                callback(focusPagerThumbnailDrawable)
            } else if ((previewShot != null) && (previewShot !is ColorDrawable) && (itemPath == viewDataId)) {
                GLog.d(TAG) { "[getFocusPagerThumbnail] currentPreviewThumbnail viewData=$itemViewData" }
                previewShot?.let(callback)
            } else {
                var finished = false
                val timeoutCallback = Runnable {
                    finished = true
                    GLog.d(TAG) {
                        "[getFocusPagerThumbnail] load drawable timeout, show default drawable, viewData=$itemViewData"
                    }
                    callback(defaultTransitionDrawable)
                }
                scheduleTimeoutWatcher(timeoutCallback, PhotoPageTransitionManager.LOADING_FOCUS_THUMBNAIL_TIMEOUT)
                getFocusPagerThumbnailFromStartLoadContent(itemViewData) { drawable ->
                    if (finished) return@getFocusPagerThumbnailFromStartLoadContent
                    unScheduleTimeoutWatcher(timeoutCallback)
                    callback(drawable ?: defaultTransitionDrawable)
                }
            }
        } ?: let {
            callback(defaultTransitionDrawable)
            GLog.d(TAG) {
                "[getFocusPagerThumbnail] focusSlot is null, show default drawable. Maybe PhotoViewModel is not initialized"
            }
        }
    }

    override fun getEnterPositionTransition(): AnimationKeyParams<Rect> {
        return viewModel.pageManagement.enterThumbnailPositionTransition.value
            ?: AnimationKeyParams(
                duration = 0,
                startDelay = 0,
                animations = listOf(Rect(), onGetConstraintRect()).toTypedArray()
            )
    }

    override fun getEnterTransitionMaker(): PageTransitionMaker? {
        return viewModel.pageManagement.enterTransitionMaker.value
    }

    override fun getEnterBackgroundTransitionColor(): AnimationKeyParams<Color>? {
        return viewModel.pageManagement.pageBackgroundTransition.value
    }

    override fun getExitPositionTransition(): AnimationKeyParams<Rect>? {
        return viewModel.inputArguments.transition.value?.exitTransition?.thumbnailPositions
    }

    override fun getPositionControllerKey(): String? {
        return sectionPage.pageViewModel.inputArguments.transition.value?.positionControllerKey
    }

    override fun createTransitionThumbnailAdapter(focusItemRect: Rect?): IPhotoTransitionThumbnailAdapter =
        PhotoAutoCropTransitionThumbnailAdapter().also { adapter ->
            if (viewModel.dataLoading.focusItemViewData != null) {
                val cropRect = getFocusSlotCropRect(focusItemRect)
                adapter.updateParameters(previewTransition.previewThumbnail, cropRect, getFocusSlotRotation())
            } else {
                // 如果还未加载焦点区域，需从预览数据中获取一个默认裁剪区域，避免动画不连续或相机进入相册编辑照片保存后闪黑
                var cropRectSetStr: String = TextUtil.EMPTY_STRING
                var rotation = 0
                viewModel.pageManagement.transitionPreviewData.value?.extra?.let {
                    cropRectSetStr = it.getString(SlotOverlayHelper.SUPPORT_CROP_RECT_KEY) ?: TextUtil.EMPTY_STRING
                    rotation = it.getInt(SlotOverlayHelper.SUPPORT_THUMB_ROTATION)
                }
                val previewCropRect = if (focusItemRect != null) {
                    CropRectSet.parse(cropRectSetStr).getCropRect(focusItemRect.width(), focusItemRect.height(), rotation)?.enlarge()
                } else null
                GLog.d(TAG) { "createTransitionThumbnailAdapter. previewCropRect=$previewCropRect, rotation=$rotation" }
                adapter.updateParameters(previewTransition.previewThumbnail, previewCropRect, rotation)

                // 进大图时会在还没有焦点ViewData情形下创建adapter，这时需要在监听到ViewData创建后再重新准备下adapter参数
                sectionPage.pageInstance.lifecycleScope.launchWhenStarted {
                    viewModel.dataLoading.diffedFocusViewData.collectNotNull {
                        GTrace.trace({ "$TAG.focusSlotViewData" }) {
                            val cropRect = getFocusSlotCropRect(focusItemRect)
                            adapter.updateParameters(previewTransition.previewThumbnail, cropRect, getFocusSlotRotation())
                        }
                        cancel()    // 单次监听，做完业务就把collect协程cancel掉
                    }
                }
            }
        }

    override fun isFirstFrameReady(): Boolean {
        return viewModel.pageManagement.firstFrameRenderingStatus.value?.let {
            it.isThumbnailReady || it.isContentReady
        } ?: false
    }

    override fun shouldWaitRenderReadyForEnterAnimation(): Boolean {
        return viewModel.inputArguments.features.value?.shouldWaitRenderReadyForEnterAnimation ?: true
    }

    override fun hasIntegrationUITransition(): Boolean {
        return viewModel.inputArguments.features.value?.hasIntegrationUITransition ?: false
    }

    override fun updateEnterTransitionProgress(progress: Float) {
        if (viewModel.menuControl.enablePostponeMenuInflate.not()) return
        if (viewModel.menuControl.couldInflateMenuIfPostponed) return
        if (couldInvalidateMenu(progress).not()) return

        // 如果此机型需要推迟菜单UI加载，那之前的所有加载必将被拦截。此处需要放开
        viewModel.menuControl.permitMenuInflateAfterPostponed()
    }

    override fun couldInvalidateMenu(progress: Float): Boolean {
        // 动画进度过半才允许菜单加载
        return (progress >= MENU_INFLATE_ENTER_TRANSITION_PROGRESS)
    }

    override fun notifyEnterTransitionStart() {
        viewModel.pageManagement.notifyEnterTransitionStart()
    }

    override fun notifyEnterTransitionEnd() {
        viewModel.pageManagement.notifyEnterTransitionEnd()
    }

    override fun notifyExitTransitionStart() {
        viewModel.pageManagement.notifyExitTransitionStart()
    }

    override fun notifyExitTransitionEnd(isHidePhotoView: Boolean) {
        viewModel.pageManagement.notifyExitTransitionEnd(isHidePhotoView)
    }

    override fun notifyExit() {
        sectionPage.pageInstance.exitExecutor.exit()
    }

    override fun refreshExitTransitionBeforeExitTransitionStarting(
        suggestingTransitionType: PhotoPageTransitionManager.TransitionType,
        overrideStartPosition: Rect?,
        overrideEndPosition: Rect?,
        exitTransitionFromType: PhotoPageTransitionManager.ExitTransitionFromType
    ) {
        val rotation = viewModel.inputArguments.transitionThumbRegion.value?.let {
            sectionPage.pageInstance.context?.display?.rotation ?: Surface.ROTATION_0
        } ?: Surface.ROTATION_0
        viewModel.pageManagement.refreshExitTransitionBeforeExitTransitionStarting(
            suggestingTransitionType = suggestingTransitionType,
            overrideStartPosition = overrideStartPosition,
            overrideEndPosition = overrideEndPosition,
            exitTransitionFromType = exitTransitionFromType,
            rotation = rotation
        )
    }

    private fun scheduleTimeoutWatcher(timeoutCallback: Runnable, timeout: Long) {
        sectionPage.pageInstance.view?.postDelayed(timeoutCallback, timeout)
    }

    private fun unScheduleTimeoutWatcher(timeoutCallback: Runnable) {
        sectionPage.pageInstance.view?.removeCallbacks(timeoutCallback)
    }

    private fun getFocusPagerThumbnailFromStartLoadContent(
        itemViewData: PhotoItemViewData,
        callback: (Drawable?) -> Unit
    ) {
        viewModel.contentLoading.startLoadContent(
            viewData = itemViewData,
            SizeType.FullThumb(LOAD_TYPE_PAGE_THUMBNAIL)
        ) { drawable ->
            drawable?.let {
                callback(drawable)
                GLog.d(TAG) { "[getFocusPagerThumbnailFromStartLoadContent] focusPagerThumbnailDrawable viewData=$itemViewData" }
            } ?: let {
                callback(null)
                GLog.d(TAG) {
                    "[getFocusPagerThumbnailFromStartLoadContent] loaded drawable is null, show default drawable, viewData=$itemViewData"
                }
            }
        }
    }

    /**
     * 设置大图入场动画结束，大图内容显示区域
     * @param onGetConstraintRect 图内容显示区域
     */
    fun setOnGetConstraintRect(onGetConstraintRect: () -> Rect) {
        this.onGetConstraintRect = onGetConstraintRect
    }

    /**
     * 设置大图页面过渡动画中使用的图片过渡实例
     * @param previewTransition 大图页面过渡动画实例
     */
    fun setPreviewTransition(previewTransition: PreviewTransition) {
        this.previewTransition = previewTransition
    }

    companion object {
        private const val TAG = "PhotoPageTransitionAdapter"

        // 菜单加载的阈值，动画执行过半时才允许菜单加载
        private const val MENU_INFLATE_ENTER_TRANSITION_PROGRESS = 0.5F
    }
}