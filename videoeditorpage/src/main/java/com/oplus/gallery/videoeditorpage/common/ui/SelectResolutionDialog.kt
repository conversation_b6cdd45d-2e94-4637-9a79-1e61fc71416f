/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SelectResolutionDialog.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.ui

import android.content.Context
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
import android.graphics.Paint
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.seekbar.COUISectionSeekBar
import com.coui.appcompat.seekbar.COUISeekBar
import com.coui.appcompat.segmentbutton.COUISegmentButtonLayout
import com.coui.appcompat.segmentbutton.COUISegmentButtonLayout.SegmentButtonDrawDelegate
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper.appendMenuClickData
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper
import com.oplus.gallery.videoeditorpage.widget.ResolutionTitleLayout
import com.oplus.gallery.videoeditorpage.widget.SectionSeekBarTextLayout

/**
 * 选择分辨率弹框
 */
class SelectResolutionDialog(
    val context: Context,
    private val resolutionTitleLayout: ResolutionTitleLayout
) {

    /**
     * 选择条事件监听
     */
    var onSaveTypeChangeListener: COUISegmentButtonLayout.OnSelectedSegmentChangeListener? = null

    /**
     *
     */
    var onResolutionChangeListener: COUISeekBar.OnSeekBarChangeListener? = null

    /**
     * 选择条事件监听
     */
    var onFpsChangeListener: COUISeekBar.OnSeekBarChangeListener? = null

    /**
     * 选择保存的分辨率的dialog
     */
    private var resolutionDialog: AlertDialog? = null

    /**
     * 选择保存的分辨率的dialog构造器
     */
    private var resolutionDialogBuilder: COUIAlertDialogBuilder? = null

    /**
     * 自定义视图容器
     */
    private val customLayout: View by lazy {
        LayoutInflater.from(context).inflate(R.layout.videoeditor_save_resolution_type_layout, null)
    }

    /**
     * 选择保存类型的分段按钮
     */
    private val segmentButton: COUISegmentButtonLayout by lazy { customLayout.findViewById(R.id.save_type_segment_button) }

    /**
     * 选择分辨率描述文本
     */
    private val resolutionDescription: SuitableSizeG2TextView by lazy { customLayout.findViewById(R.id.save_resolution_description) }

    /**
     * 选择分辨率滚动条
     */
    private val resolutionSeekBar: COUISectionSeekBar by lazy { customLayout.findViewById(R.id.save_resolution_seek_bar) }

    /**
     * 选择分辨率滚动条的下方，显示的分级文本条
     */
    private val resolutionTextBar: SectionSeekBarTextLayout by lazy { customLayout.findViewById(R.id.save_resolution_text_bar) }

    /**
     * 选择面板底部提示视图
     */
    private val bottomTips: SuitableSizeG2TextView by lazy { customLayout.findViewById(R.id.save_resolution_tips) }

    /**
     * 选择帧率描述文本
     */
    private val fpsDescription: SuitableSizeG2TextView by lazy { customLayout.findViewById(R.id.save_fps_description) }

    /**
     * 选择帧率滚动条
     */
    private val fpsSeekBar: COUISectionSeekBar by lazy { customLayout.findViewById(R.id.save_fps_seek_bar) }

    /**
     * 选择帧率滚动条的下方，显示的分级文本条
     */
    private val fpsTextBar: SectionSeekBarTextLayout by lazy { customLayout.findViewById(R.id.save_fps_text_bar) }

    /**
     * 弹框顶部间距
     */
    private val marginTop: Int by lazy {
        context.resources.getDimensionPixelOffset(R.dimen.videoeditor_save_resolution_type_dialog_offsetY_standard)
    }

    /**
     * 弹框消失时间戳
     */
    private var dismissTimeMillis: Long = 0L

    init {
        GLog.d(TAG, LogFlag.DL) { "[init]" }
        initSaveTypeButton()
        initResolutionBar()
        initFpsBar()
    }

    /**
     * 初始化保存类型的开关组件
     */
    private fun initSaveTypeButton() {
        segmentButton.setSegmentButtons(
            arrayOf(
                context.resources.getString(R.string.videoeditor_text_video_album),
                context.resources.getString(R.string.videoeditor_text_olive)
            )
        )
        segmentButton.setSegmentButtonDrawDelegate(object : SegmentButtonDrawDelegate {
            override fun needProxy(): Boolean = true

            override fun setCustomBackrgoundPaint(): Paint = Paint().apply {
                isAntiAlias = true
                isDither = true
                style = Paint.Style.FILL
                color = context.resources.getColor(R.color.videoeditor_save_type_segment_button_background_color, null)
            }

            override fun setCustomIndicatorPaint(): Paint = Paint().apply {
                isAntiAlias = true
                isDither = true
                style = Paint.Style.FILL
                color = ThemeHelper.getCouiColorContainerThemeHalftone(context)
            }
        })
        segmentButton.setOnSelectedSegmentChangeListener { positionFrom: Int, positionTo: Int, progress: Float ->
            GLog.d(TAG, LogFlag.DL) { "[onSelectedSegmentChange] positionFrom: $positionFrom, positionTo: $positionTo, progress: $progress" }
            onSaveTypeChangeListener?.onSelectedSegmentChange(positionFrom, positionTo, progress)
        }
        segmentButton.setOnClickListener {
            appendMenuClickData(VideoEditorTrackConstant.Value.VIDEO_SAVE_ADJUST_TYPE, VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL)
        }
    }

    /**
     * 初始化分辨率选择条组件
     */
    private fun initResolutionBar() {
        resolutionSeekBar.setEnableAdaptiveVibrator(true)
        resolutionSeekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(couiSeekBar: COUISeekBar, progress: Int, isFromUser: Boolean) {
                onResolutionChangeListener?.onProgressChanged(couiSeekBar, progress, isFromUser)
            }

            override fun onStartTrackingTouch(couiSeekBar: COUISeekBar) {
                onResolutionChangeListener?.onStartTrackingTouch(couiSeekBar)
            }

            override fun onStopTrackingTouch(couiSeekBar: COUISeekBar) {
                onResolutionChangeListener?.onStopTrackingTouch(couiSeekBar)
                appendMenuClickData(VideoEditorTrackConstant.Value.VIDEO_SAVE_ADJUST_RESOLUT, VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL)
            }
        })
    }

    /**
     * 初始化帧率选择条组件
     */
    private fun initFpsBar() {
        fpsSeekBar.setEnableAdaptiveVibrator(true)
        fpsSeekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(couiSeekBar: COUISeekBar, progress: Int, isFromUser: Boolean) {
                onFpsChangeListener?.onProgressChanged(couiSeekBar, progress, isFromUser)
            }

            override fun onStartTrackingTouch(couiSeekBar: COUISeekBar) {
                onFpsChangeListener?.onStartTrackingTouch(couiSeekBar)
            }

            override fun onStopTrackingTouch(couiSeekBar: COUISeekBar) {
                onFpsChangeListener?.onStopTrackingTouch(couiSeekBar)
                appendMenuClickData(VideoEditorTrackConstant.Value.VIDEO_SAVE_ADJUST_FPS, VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL)
            }
        })
    }

    /**
     * 是否平板横屏
     */
    private fun isTableLandscape(config: AppUiConfig? = null): Boolean {
        val currentConfig = config ?: (context as? BaseActivity)?.getCurrentAppUiConfig() ?: return false
        return (currentConfig.orientation.current != SCREEN_ORIENTATION_PORTRAIT)
                && (WindowSizeForm.getSizeForm(currentConfig) == WindowSizeForm.TABLET_LANDSCAPE)
    }

    /**
     * 选中分段按钮对应下标位置
     *
     * @param position 选中位置下标
     */
    fun selectSegmentAt(position: Int) {
        segmentButton.selectSegmentAt(position)
    }

    /**
     * 设置分辨率描述文本
     */
    fun setResolutionDescriptionText(description: String) {
        resolutionDescription.text = description
    }

    /**
     * 设置帧率描述文本
     */
    fun setFpsDescriptionText(description: String) {
        fpsDescription.text = description
    }

    /**
     * 设置最大分辨率
     */
    fun setResolutionSeekBarMax(max: Int) {
        resolutionSeekBar.max = max
    }

    /**
     * 设置最大帧率
     */
    fun setFpsSeekBarMax(max: Int) {
        fpsSeekBar.max = max
    }

    /**
     * 设置对应位置分辨率
     */
    fun setResolutionSeekBarProgress(progress: Int) {
        resolutionSeekBar.progress = progress
    }

    /**
     * 设置对应位置帧率
     */
    fun setFpsSeekBarProgress(progress: Int) {
        fpsSeekBar.progress = progress
    }

    /**
     * 设置分辨率列表文本显示
     */
    fun setResolutionTextBarList(textList: List<String>) {
        resolutionTextBar.textList = textList
    }

    /**
     * 设置帧率级别列表文本显示
     */
    fun setFpsTextBarList(textList: List<String>) {
        fpsTextBar.textList = textList
    }

    /**
     * 设置提示文本
     */
    fun setBottomTips(text: String) {
        bottomTips.text = text
    }

    /**
     * 显示弹框
     */
    fun show() {
        /* 当前的设计，是弹框显示时，点击外围区域后，先消失，再响应所点View的事件。
         * 而如果此时，点击的外围，是标题中分辨率显示的View。那弹框消失后，立马又响应事件，再次显示的情况。
         * 所以，这里做防护，拦截消失又快速显示 */
        if ((dismissTimeMillis > 0) && (System.currentTimeMillis() - dismissTimeMillis) < SHOW_INTERVAL) {
            GLog.d(TAG, LogFlag.DL) { "[show] dialog has just been dismiss, cannot show" }
            return
        }

        if (resolutionDialog?.isShowing == true) {
            GLog.d(TAG, LogFlag.DL) { "[show] dialog is already showing" }
            return
        }

        GLog.d(TAG, LogFlag.DL) { "[show] init dialog to show" }
        resolutionTitleLayout.setTitleSelected(true)
        if ((resolutionDialog == null) || (resolutionDialogBuilder == null)) {
            createDialog()
        }
        resolutionDialog?.show()
        resolutionDialogBuilder?.updateViewAfterShown()
        resolutionDialog?.findViewById<View>(com.support.dialog.R.id.custom)?.setPadding(0, 0, 0, 0)
    }

    /**
     * 创建弹框
     */
    private fun createDialog() {
        GLog.d(TAG, LogFlag.DL) { "[createDialog] create dialog" }
        val builder = COUIAlertDialogBuilder(context, R.style.ResolutionTypeDialogStyle)
        builder.setView(customLayout)
        builder.setOnDismissListener {
            resolutionTitleLayout.setTitleSelected(false)
            dismissTimeMillis = System.currentTimeMillis()
        }
        builder.setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog_Alpha)
        resolutionDialog = builder.create().apply {
            window?.apply {
                decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION)
                addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)
                addFlags(WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH)
            }
        }
        updateDialogLayout()
        resolutionDialogBuilder = builder
    }

    /**
     * 更新弹框位置
     */
    fun updateDialogLayout() {
        GLog.d(TAG, LogFlag.DL) { "[updateDialogLayout] start" }
        resolutionDialog?.window?.apply {
            val params = attributes
            val (width, gravity) = if (isTableLandscape()) {
                Pair(
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    (Gravity.TOP or if (ResourceUtils.isRTL(context)) Gravity.LEFT else Gravity.RIGHT)
                )
            } else {
                Pair(WindowManager.LayoutParams.MATCH_PARENT, Gravity.TOP)
            }
            params.width = width
            params.gravity = gravity
            params.y = marginTop
            attributes = params
        }
    }

    internal companion object {
        /**
         * 分段按钮，实况选项下标位置
         */
        const val SEGMENT_POSITION_OLIVE = 1

        private const val TAG = "SelectResolutionDialog"

        /**
         * 连续显示的间隔时长
         */
        private const val SHOW_INTERVAL = 500
    }
}