/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoPlaybackSectionImpl.kt
 ** Description : 大图页 - 内容播控页面切片
 ** Version     : 1.0
 ** Date        : 2021/11/23
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2021/11/23  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.playback

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.PathInterpolator
import androidx.annotation.ColorRes
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.addon.view.getSurfaceControl
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business.contentloading.ContentFactory.Companion.LOAD_TYPE_CONTENT
import com.oplus.gallery.business.contentloading.SizeType
import com.oplus.gallery.business.renderer.brighten.IPageHdrVisionBrightenValidCallback
import com.oplus.gallery.business_lib.helper.PhotoDataHelper
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value
import com.oplus.gallery.foundation.ui.helper.BackgroundBlurUtils
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.DynamicVisibility
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.PhotoSlotOverlayScratch
import com.oplus.gallery.photo_page.ui.isVisible
import com.oplus.gallery.photo_page.ui.section.details.transition.controller.DetailsTransitionController.Companion.TRANSITION_STATE_IDLE
import com.oplus.gallery.photo_page.ui.section.pagecontainer.PhotoAlphaSpringAnimation
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuSection
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.ui.section.playback.overlay.PhotoPlaybackOverlay
import com.oplus.gallery.photo_page.ui.section.playback.uicontroller.PlaybackDefaultUIController
import com.oplus.gallery.photo_page.ui.section.playback.uicontroller.PlaybackThumbnailUIController
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoAnimationConfig
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackThumbLineUIController
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.ui.theme.PhotoPageTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataDistinctSlotId
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataWhenPresentAndIdle
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.VisibleFrom
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.PRESENTED
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.RefreshRateRequest
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PhotoPlaybackViewModel.PlaybackUiStyle.DEFAULT_SEEKBAR
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PhotoPlaybackViewModel.PlaybackUiStyle.THUMBNAIL_SEEKBAR
import com.oplus.gallery.photo_page.viewmodel.playbackcontrol.PlaybackInfo
import com.oplus.gallery.photo_page.widget.seekbar.ThumbnailSeekBar
import com.oplus.gallery.photopager.PhotoPager.Companion.SCROLL_STATE_IDLE
import com.oplus.gallery.photopager.PhotoSlotOverlay
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.FPS_120_MIN_AVERAGE_FPS_THRESHOLD
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 大图页内容播控页面切片
 *
 * 详情 UI 结构查看：
 * [PlaybackSection设计](https://odocs.myoas.com/folder/0l3NVmMr9GtlZz3R/)
 */
internal class PhotoPlaybackSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : AbstractPhotoSection(sectionPage) {

    @OptIn(ExperimentalUnsignedTypes::class)
    private val menuSection: PhotoMenuSection? get() = sectionPage.pageInstance.requireSection()

    /**
     * 播控 UI 控制器，如果其他 [AbstractPhotoSection] 需要更改，则调用该接口进行更改
     *
     * 目前默认情况下，使用 [PlaybackDefaultUIController]
     */
    var playbackUIController: IPlaybackUIController? = null
        set(value) {
            val isNullToNonNull = (field == null) && (value != null)
            dispatchDetachEventToUIController()
            field = value
            dispatchAttachEventToUIController()

            if (isNullToNonNull) {
                // 在分发 transitionState 时 playbackUIController 还未被赋值，此时重新分发一次，避免播控layout没有被添加到父容器
                viewModel.pageManagement.photoPageTransitionState.value?.let {
                    GLog.w(TAG, LogFlag.DL) { "[setPlaybackUIController] redispatch photoPageTransitionState($it) to controller" }
                    dispatchPhotoPageTransitionStateToUIController(it)
                }
            }
        }

    /**
     * naviBar占位符，高度为NaviBar的实时高度
     */
    private var naviBarPlaceholder: View? = null

    /**
     * 底部菜单栏的容器,主要是为了获取底部菜单高度
     */
    private var bottomBarContainer: View? = null

    /**
     * 底部菜单栏
     */
    private var vBottomBar: View? = null

    /**
     * 播控的根布局。包括播控的全部控件。
     */
    private var vgPlaybackContainer: ViewGroup? = null

    /**
     * 当前Section的根View.
     */
    private var contentView: View? = null

    /**
     * 用来判断如果当前是HDR视频，那这个HDR视频是否应该设置提亮FLAG
     */
    private var shouldUpdateHdrFlag: Boolean = false
    private val playbackControl: IPlaybackControl by lazy { PlaybackControlImpl() }
    private var pageScrollState: Int = SCROLL_STATE_IDLE
    private var lastPlaybackInfo: PlaybackInfo? = null
    private var isAudioValid: Boolean? = null

    /**
     * 当前无触摸降帧的模式：默认启用
     * marked by caiconghu 此处命名需要考虑<OTI>的具体含义，换一个更容易理解的命名。
     */
    private var currentOtiFrameRate = FRAME_RATE_ENABLE_OTI

    /**
     * 设置无触摸降帧特性的任务
     * marked by caiconghu 此处命名需要考虑<OTI>的具体含义，换一个更容易理解的命名。
     */
    private var otiFrameRateSettingJob: Job? = null

    private val onPageChangeCallback = object : ViewPager.OnPageChangeCallback() {
        override fun onPageScrollStateChanged(state: Int) {
            pageScrollState = state
            updateFreshRateIfNeeded()
        }
    }

    /**
     * 当前是否正在执行动画的三元指示器
     * true: 正在显示动画
     * false: 正在隐藏动画
     * null: 没有进行中的动画
     */
    private var animRunningIndicator: Boolean? = null


    private val updateNaviBarPlaceholderRunnable = Runnable {
        updateNaviBarPlaceholder()
    }

    private val activity: Activity?
        get() = sectionPage.pageContext as? BaseActivity

    private val pagerSection: PhotoPagerSection?
        get() = sectionPage.pageInstance.requireSection()

    private val currentPlaybackInfo: PlaybackInfo?
        get() {
            val isVideoFocused = viewModel.dataLoading.focusItemViewData?.isVideo == true
            return if (isVideoFocused) viewModel.playback.playbackInfo.value else null
        }

    private var playbackContainerAnimation: PhotoAlphaSpringAnimation? = null

    private var alterPlaybackProgressController: AlterPlaybackThumbProgressController? = null

    /**
     * diffedFocus流。下发时机：
     * 1. 当图片 <-> 视频切换时才会下发
     * 2. 当入场动画结束 & 大图滑动idle时才下发
     */
    private val diffedFocusMediaTypeTimingFlow by lazy {
        sectionPage.pageViewModel.dataLoading.diffedFocusViewDataWhenPresentAndIdle.distinctUntilChanged { _, diffed ->
            // 只关心mimeType的变化
            diffed?.newItem?.mediaType == diffed?.oldItem?.mediaType
        }
    }

    override val slotOverlayRecords: List<PhotoSlotOverlayScratch.PhotoSlotOverlayRecord> = listOf(
        PhotoSlotOverlayScratch.PhotoSlotOverlayRecord(
            clazz = PhotoPlaybackOverlay::class.java,
            creator = ::createOverlay
        )
    )

    override fun onKeyDown(keyCode: Int): Boolean {
        return viewModel.outputChannels.notifyKeyDownEvent(keyCode)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        /**
         * onlinePlayerVM必须在playbackControl之前调用,因为playbackControl可能会导致播放器状态变化（如播放->暂停或暂停->播放)
         * isFloatingWindowMode:当前是否处于浮窗状态
         * isMultiWindowMode:当前是否处于多窗口状态
         * isResumed:当前界面是否处于resume状态
         * isForceToContinuePlaying:是否强制继续播放
         *
         * 增加界面失去焦点时不暂停播放即强制继续播放场景：
         * 1. 当前处于浮窗状态或多窗口状态，且界面处于resume状态
         */
        viewModel.onlinePlayer.notifyWindowFocusChanged(hasFocus)

        val isFloatingWindowMode = sectionPage.pageInstance.getCurrentAppUiConfig().isInFloatingWindow.current
        val isMultiWindowMode = sectionPage.pageInstance.getCurrentAppUiConfig().isInMultiWindow.current
        val isResumed = sectionPage.pageInstance.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)
        val isForceToContinuePlaying = (isFloatingWindowMode || isMultiWindowMode) && isResumed
        viewModel.playback.notifyWindowFocusChanged(hasFocus, isForceToContinuePlaying)
    }

    override fun onDestroy() {
        super.onDestroy()
        updateKeepScreenOnStatus()
        pagerSection?.unregisterOnPageChangeCallback(onPageChangeCallback)
    }

    override fun onViewCreated(view: View) {
        contentView = view
        initViews()
        subscribeLiveDataFromViewModel()
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        playbackContainerAnimation?.cancel()
        playbackContainerAnimation = null
        alterPlaybackProgressController?.onDestroy()
        alterPlaybackProgressController = null
        contentView?.removeCallbacks(updateNaviBarPlaceholderRunnable)
        contentView = null
        sectionPage.pageInstance.activity?.let { activity ->
            viewModel.pageManagement.notifyChangeWindowRefreshRate(activity.window, RefreshRateRequest.REQUEST_DEFAULT)
        }
    }

    private fun initViews() {
        naviBarPlaceholder = bindView<View>(R.id.navibar_placeholder)
        bottomBarContainer = bindView<View>(R.id.bottom_bar_container)
        vBottomBar = bindView<View>(com.oplus.supertext.ostatic.R.id.bottom_bar)
        vgPlaybackContainer = bindView<ViewGroup>(R.id.fl_playback_container)
        pagerSection?.registerOnPageChangeCallback(onPageChangeCallback)

        alterPlaybackProgressController = bindView<ViewStub?>(R.id.vs_half_immersive_playback_progress)?.inflate()?.run {
            val container = findViewById<GalleryHorizontalScrollView>(R.id.hsv_playback_progress_container) ?: return@run null
            val seekbar = findViewById<ThumbnailSeekBar>(R.id.tsb_playback_progress) ?: return@run null
            AlterPlaybackThumbProgressController(
                sectionPage, container, seekbar, playbackControl,
                isAllowToShow = { playbackUIController is PlaybackThumbLineUIController }
            )
        }
    }

    @Suppress("UnsafeCast")
    private fun subscribeLiveDataFromViewModel() {
        viewModel.playback.playbackUiStyle.observe(this) { uiStyle ->
            if (uiStyle in setOf(THUMBNAIL_SEEKBAR, DEFAULT_SEEKBAR)) {
                playbackUIController = if (uiStyle == THUMBNAIL_SEEKBAR) PlaybackThumbnailUIController() else PlaybackDefaultUIController()
            }
        }

        // 订阅进入大图动画状态
        viewModel.pageManagement.photoPageTransitionState.observe(this) { state ->
            dispatchPhotoPageTransitionStateToUIController(state)
        }

        viewModel.playback.playbackInfo.observe(this) {
            onPlaybackInfoChanged()
        }

        //本地视频分辨率大于手机支持的最大分辨率  弹出播放卡顿的提示
        viewModel.playback.shouldShowVideoUnsmoothlyTips.observe(this) {
            shouldShowVideoUnSmoothlyTips(it)
        }

        viewModel.playback.shouldAutoPlay.observe(this) {
            onPlaybackInfoChanged()
            adaptPlaybackLayoutToNaviBar()
        }

        lifecycleScope.launch {
            viewModel.menuControl.photoMenu.collectNotNull {
                adaptPlaybackLayoutToNaviBar()
            }
        }

        viewModel.menuControl.bottomMenuVisibility.observe(this) {
            adaptPlaybackLayoutToNaviBar()
        }

        viewModel.pageManagement.photoDecorationState.observe(this) {
            adaptPlaybackLayoutToNaviBar()
        }

        diffedFocusMediaTypeTimingFlow.collect(this@PhotoPlaybackSection) {
            GTrace.trace("$TAG.focusSlotViewDataDiff") {
                // 修复播放视频时Home回到桌面，点击相册图标回到大图立马从视频滑动到图片时播放控件显示在图片上
                if (it?.newItem?.isVideo?.not() == true) {
                    setPlaybackVisible(DynamicVisibility.HIDE_IMMEDIATELY)
                    viewModel.photoFunc.notifyVisible(VisibleFrom.VISIBLE_FROM_PLAYBACK, true)
                    return@trace
                }
                adaptPlaybackLayoutToNaviBar()
            }
        }

        viewModel.pageManagement.isUnderImmersionInteractive.observe(this) {
            updatePlaybackVisibilityStatus()
        }

        viewModel.playback.shouldReloadFocusVideoResource.observe(this) { shouldReload ->
            if (shouldReload.not()) {
                return@observe
            }
            val itemViewData = viewModel.dataLoading.focusItemViewData
            val focusSlot = viewModel.dataLoading.focusSlot
            if (itemViewData != null) {
                pagerSection?.let { section ->
                    GLog.w(TAG) { "[shouldReloadCurrentPlayingResources] notifySlotInvalidate for slot:$shouldReload" }
                    val position = viewModel.playback.getCurrentPosition()
                    if (position > 0) {
                        viewModel.playback.recordPosition(position)
                    }
                    viewModel.contentLoading.releaseLoadedContent(itemViewData, SizeType.FullResolution(LOAD_TYPE_CONTENT))
                    section.notifySlotInvalidate(focusSlot)
                }
            }
        }

        viewModel.pageManagement.thumbLineHeight.observe(this) {
            // 缩图轴高度变更时更新底部 margin（如：折叠屏展开和折叠时）
            updatePlaybackThumbLineUIMarginBottom()
        }

        lifecycleScope.launch {
            viewModel.pageManagement.pageTheme.collect {
                playbackUIController?.onThemeUpdated(it)
                adaptPlaybackLayoutToNaviBar()
            }
        }

        //订阅是否进入详情页，如果进入则隐藏播放设置，没有进入则不处理
        viewModel.details.transitionPanelEffectState.observe(this) {
            updatePlaybackVisibilityStatus()
        }

        viewModel.pageManagement.shouldAnimateWhenRemoved.observe(this) {
            if (it) {
                setPlaybackVisible(DynamicVisibility.HIDE)
            }
        }

        viewModel.playback.isAlterThumbProgressBarVisible.collect(this) {
            updatePlaybackThumbLineUIMarginBottom()
        }

        // 关注详情面板(焦点)过渡状态，当详情过渡停止时做一次回正校验，因为在详情滚动状态时会切换为高刷。以规避视频降刷新率带来的主观卡顿感
        viewModel.details.focusTransitionState.observe(this) { transitionState ->
            // 焦点是非视频资源不做相关响应
            if ((viewModel.dataLoading.focusItemViewData?.isVideo == true).not()) return@observe

            // 当详情面板处于非静止状态下时尝试刷新为默认帧率
            if (TRANSITION_STATE_IDLE != transitionState) {
                // 如果视频本身已经是高刷了，详情就不做修改
                if (shouldKeepFreshRate120Fps()) return@observe

                val window = activity?.takeUnless { it.isDestroyed || it.isFinishing || it.isRestricted }?.window ?: return@observe
                viewModel.pageManagement.notifyChangeWindowRefreshRate(window, RefreshRateRequest.REQUEST_DEFAULT)
                return@observe
            }

            // 焦点详情面板处于静止状态时，按照原始逻辑处理
            updateFreshRateIfNeeded()
        }

        viewModel.dataLoading.diffedFocusViewDataDistinctSlotId.collect(this) {
            if (it?.newItem?.isVideo?.not() == true && vgPlaybackContainer?.isVisible == true) {
                setPlaybackVisible(DynamicVisibility.HIDE_IMMEDIATELY)
                viewModel.photoFunc.notifyVisible(VisibleFrom.VISIBLE_FROM_PLAYBACK, true)
            }
        }

        viewModel.menuControl.menuTheme.collect(this) {
            playbackUIController?.onContainerBackgroundUpdated(it.second.background)
        }
    }


    private fun shouldShowVideoUnSmoothlyTips(shouldShow: Boolean) {
        GLog.d(TAG) { "[subscribeLiveDataFromViewModel] shouldShowVideoUnSmoothlyTips=$shouldShow" }
        if (shouldShow) {
            ToastUtil.showShortToast(R.string.photopage_tips_special_format_video_to_smoothness_reduce)
        }
    }

    private fun onPlaybackInfoChanged() {
        updateInternalPhotoStatus()
        updatePlaybackVisibilityStatus()
        updatePlaybackFeature()
        dispatchPlaybackInfoToUIController()
        updateHdrVisionFlag()
        lastPlaybackInfo = currentPlaybackInfo
    }

    /**
     * 臻彩视界需求，因为HDR视频只能在播放的时候才能判断此视频是否为HDR视频，所以才在此类中下发HDR视频提亮FLAG
     * 方法用于设置HDR视频提亮FLAG
     */
    private fun updateHdrVisionFlag() {
        if (currentPlaybackInfo?.playerToken != lastPlaybackInfo?.playerToken) {
            shouldUpdateHdrFlag = true
        }
        if (shouldUpdateHdrFlag && (currentPlaybackInfo?.isHDRVideo == true) &&
            (currentPlaybackInfo?.playerToken == viewModel.playback.playbackInfo.value?.playerToken)
        ) {
            //接口回调查找currentPlayBackInfo的Render，重置Render
            val focusSlot: Int = viewModel.dataLoading.focusSlot
            if (focusSlot == INVALID_INDEX) {
                return
            }
            (pagerSection?.findPhotoSlotBySlot(focusSlot)?.contentRenderer as? IPageHdrVisionBrightenValidCallback)
                ?.onPageHdrVisionBrightenValidChange(true)
            shouldUpdateHdrFlag = false
        }
    }

    private fun updateInternalPhotoStatus() {
        if (currentPlaybackInfo?.isPlaying != lastPlaybackInfo?.isPlaying) {
            updateFreshRateIfNeeded()
            updateKeepScreenOnStatus()
        }
    }

    private fun updatePlaybackVisibilityStatus() {
        val playbackInfo = currentPlaybackInfo ?: let {
            if (viewModel.dataLoading.focusItemViewData?.isVideo != true) {
                setPlaybackVisible(DynamicVisibility.HIDE_IMMEDIATELY)
                viewModel.photoFunc.notifyVisible(VisibleFrom.VISIBLE_FROM_PLAYBACK, true)
            }
            return
        }

        /**
         * 应该隐藏播控视图，条件如下：
         * - 当前不是视频投屏
         * - 当前不是自动播放 或 视频数据源未就绪
         * - 当前不是正在执行`seek`动作
         * - 当前不是播放状态
         * - 当前进度为0
         * - 当前是否处于详情页
         */
        val sourceNotReady = playbackInfo.mediaSourceState.isNotReady
        val shouldAutoPlay = shouldAutoPlay()
        val isVideoCasting = viewModel.outputChannels.castingManager.isVideoCasting.value == true
        val isUnderImmersionInteractive = viewModel.pageManagement.isUnderImmersionInteractive.value == true
        val isInDetailsMode = sectionPage.pageViewModel.details.isDetailModeByTransition()

        val shouldHidePlaybackViews = isVideoCasting.not()
                && (shouldAutoPlay.not() || sourceNotReady)
                && playbackInfo.isSeeking.not()
                && playbackInfo.isPlaying.not()
                && (playbackInfo.positionInPercent <= 0.0)
        if (shouldHidePlaybackViews || isInDetailsMode) {
            setPlaybackVisible(if (isInDetailsMode) DynamicVisibility.HIDE_IMMEDIATELY else DynamicVisibility.HIDE)
            viewModel.photoFunc.notifyVisible(VisibleFrom.VISIBLE_FROM_PLAYBACK, true)
            return
        }

        /**
         * bugId: 4528721
         *
         * 问题：点击视频中间播放按钮后，会执行到showPlaybackViewsIfCould，导致关闭自动播放视频进入沉浸模式时会闪现一下进度条
         *
         * 方案:点击视频中间播放按钮，进入沉浸式不显示进度条
         */
        val shouldShowPlaybackViews = isUnderImmersionInteractive.not() || isVideoCasting
        if (shouldShowPlaybackViews) {
            setPlaybackVisible(DynamicVisibility.SHOW)
            viewModel.photoFunc.notifyVisible(VisibleFrom.VISIBLE_FROM_PLAYBACK, false)
            return
        }

        // ignore, do nothing
    }

    private fun shouldAutoPlay(): Boolean {
        val shouldAutoPlay = viewModel.playback.shouldAutoPlay.value ?: true
        if (shouldAutoPlay.not()) {
            return false
        }
        if (viewModel.onlinePlayer.shouldNotAutoPlayOnNetwork()) {
            return false
        }
        if (viewModel.onlinePlayer.isOnlineVideoNotPrepared()) {
            // 云端视频:播放器还没准备就绪,不需要显示播放控制器
            return false
        }
        return true
    }

    private fun setPlaybackVisible(dynamicVisibility: DynamicVisibility) {
        if (vgPlaybackContainer?.isVisible == dynamicVisibility.isVisible()) {
            return
        }

        // 如果将要做的动画与当前进行中的动画相同，则跳过
        if (animRunningIndicator == dynamicVisibility.isVisible()) {
            return
        }

        val animation = playbackContainerAnimation ?: vgPlaybackContainer?.let { PhotoAlphaSpringAnimation(it) }?.also {
            playbackContainerAnimation = it
        } ?: return

        GLog.d(TAG) { "setPlaybackVisible: isVisible:$dynamicVisibility" }

        animation.cancel()
        animRunningIndicator = dynamicVisibility.isVisible()

        when (dynamicVisibility) {
            DynamicVisibility.SHOW -> fadeInView(animation)

            DynamicVisibility.HIDE -> fadeOutView(animation)

            DynamicVisibility.SHOW_IMMEDIATELY -> {
                updatePlaybackThumbLineUIMarginBottom()
                vgPlaybackContainer?.isVisible = true
                vgPlaybackContainer?.alpha = ALPHA_SHOW
                updatePlaceholderAlphaInPreviewType(ALPHA_SHOW)
                animRunningIndicator = null
            }

            DynamicVisibility.HIDE_IMMEDIATELY -> {
                vgPlaybackContainer?.isVisible = false
                vgPlaybackContainer?.alpha = ALPHA_HIDE
                updatePlaceholderAlphaInPreviewType(ALPHA_HIDE)
                animRunningIndicator = null
                // 不可见时尝试更新
                updatePlaybackThumbLineUIMarginBottom()
            }
        }

        trackViewVisible(dynamicVisibility)
    }

    /**
     * 渐隐播放控件
     * @param animation 动画对象
     */
    private fun fadeOutView(animation: PhotoAlphaSpringAnimation) {
        animation.hide(
            ANIMATION_BOUNCE,
            ANIMATION_RESPONSE_HIDE,
            onEnd = { canceled ->
                if (!canceled) {
                    vgPlaybackContainer?.isVisible = false
                    /*
                       设置成功后再检测一次可见状态设置
                       解决低于100ms视频偶现播控区域不可见问题 bug：9558658
                     */
                    updatePlaybackVisibilityStatus()
                }
                animRunningIndicator = null
                // 不可见时尝试更新
                updatePlaybackThumbLineUIMarginBottom()
            },
            onUpdate = { _, _ ->
                vgPlaybackContainer?.alpha?.let(::updatePlaceholderAlphaInPreviewType)
            }
        )
    }

    /**
     * 渐显播放控件
     * @param animation 动画对象
     */
    private fun fadeInView(animation: PhotoAlphaSpringAnimation) {
        val delayTime = if (viewModel.pageManagement.shouldAnimateWhenRemoved.value == true) REMOVED_DELAY_ALPHA_SHOW_TIME else 0L
        vgPlaybackContainer?.postDelayed({
            animation.show(
                ANIMATION_BOUNCE,
                ANIMATION_RESPONSE_SHOW,
                onStart = {
                    // 可见前尝试更新
                    updatePlaybackThumbLineUIMarginBottom()
                    vgPlaybackContainer?.isVisible = true
                    vgPlaybackContainer?.alpha = ALPHA_HIDE
                },
                onEnd = {
                    animRunningIndicator = null
                },
                onUpdate = { _, _ ->
                    vgPlaybackContainer?.alpha?.let(::updatePlaceholderAlphaInPreviewType)
                }
            )
        }, delayTime)
    }

    private fun trackViewVisible(dynamicVisibility: DynamicVisibility) {
        // 埋点：播控条可见性
        val playbackTrackAction = Value.VIDEO_PLAYER_ACTION_CLICK_VIEW
        val playbackTrackValue = if (dynamicVisibility.isVisible()) {
            Value.VIDEO_PLAYER_ACTION_RESULT_POSITIVE
        } else {
            Value.VIDEO_PLAYER_ACTION_RESULT_NEGATIVE
        }
        viewModel.track.trackVideoPlayerOperation(playbackTrackAction, playbackTrackValue)
    }

    /**
     * 在大图预览模式下才更新 placeholder 的透明度
     */
    private fun updatePlaceholderAlphaInPreviewType(alpha: Float) {
        if (PhotoDataHelper.isPreviewType(viewModel.inputArguments.features.value?.photoType)) {
            naviBarPlaceholder?.alpha = alpha
        }
    }

    private fun updatePlaybackFeature() {
        val playbackInfo = currentPlaybackInfo ?: let {
            isAudioValid = null
            return
        }

        //慢动作、延时摄影、无效视频、投屏播放模式的音量按钮需置灰
        val shouldEnableAudio = playbackInfo.isAudioValid && (viewModel.outputChannels.castingManager.isCasting.value != true)
        if (shouldEnableAudio != isAudioValid) {
            isAudioValid = shouldEnableAudio
            dispatchPlaybackFeatureToUIController(PlaybackFeature.MutableFeature(shouldEnableAudio))
        }
    }

    override fun onWindowInsetsChanged(windowInsets: WindowInsetsCompat) {
        super.onWindowInsetsChanged(windowInsets)
        GLog.d(TAG) { "[onWindowInsetsChanged]" }
        adaptPlaybackLayoutToNaviBar()
    }

    /**
     * 更新NaviBar占位符高度
     * 高度小于等于0时，隐藏；否则设置为对应高度
     */
    @OptIn(ExperimentalUnsignedTypes::class)
    private fun updateNaviBarPlaceholder() {
        val pageInstance = sectionPage.pageInstance

        /**
         * 设置底部边距，折叠屏展开 && 虚拟导航栏情况下，底部菜单会隐藏掉，SeekBar 会掉到屏幕底部，被虚拟导航栏遮挡
         * 需要获取虚拟导航栏高度，并设置 到占位符，使 Seekbar 浮在虚拟导航栏上方
         * 大图菜单异步加载后，高负载情况下这里使用底部菜单是否可见不准确，会导致播控条被虚拟导航栏遮挡
         */
        val isBottomMenuVisible: Boolean = bottomBarContainer?.let { (it.height > 0) && (it.visibility == View.VISIBLE) } ?: false
        val hasSysNavBar = (sectionPage.pageInstance.couldShowSystemBar() || sectionPage.pageInstance.isInMultiWindow())
        val shouldAdaptBottomNavi = isBottomMenuVisible.not() && hasSysNavBar
        val height = if (shouldAdaptBottomNavi) pageInstance.bottomNaviBarHeight() else 0
        if (naviBarPlaceholder?.height != height) {
            naviBarPlaceholder?.updateLayoutParams {
                this.height = if (height > 0) height else SIZE_ZERO_FOR_CONSTRAINT_LAYOUT
            }
            viewModel.pageManagement.notifyNaviBarHeightChange(height)
        }
        naviBarPlaceholder?.visibility = when {
            // 目标高度非 0，正常显示
            height > 0 -> View.VISIBLE
            // 都为 0，则直接隐藏
            naviBarPlaceholder?.height == 0 -> View.GONE
            // 当前不为 0，但目标为 0，则先设为 INVISIBLE，让 naviBarPlaceholder 的高度能变为 0
            else -> View.INVISIBLE
        }

        if (PhotoDataHelper.isPreviewType(viewModel.inputArguments.features.value?.photoType)) {
            naviBarPlaceholder?.let { placeholder ->
                val backgroundColor = (placeholder.background as? ColorDrawable)?.color
                val targetColor = placeholder.context.getColor(getCurrentBackgroundColor())
                if (backgroundColor != targetColor) {
                    placeholder.setBackgroundColor(targetColor)

                    if (vgPlaybackContainer?.isVisible == true) {
                        placeholder.setAlpha(ALPHA_SHOW)
                    } else {
                        placeholder.setAlpha(ALPHA_HIDE)
                    }
                }
            }
        }
    }

    private fun getCurrentBackgroundColor(): Int {
        return if (BackgroundBlurUtils.isPhotoBlurEnable()) {
            if (viewModel.pageManagement.pageTheme.value == PhotoPageTheme.Light) {
                MenuDecorationTheme.LightNotBlur.background
            } else {
                MenuDecorationTheme.DarkNotBlur.background
            }
        } else {
            viewModel.pageManagement.pageTheme.value.menuDecorationTheme.background
        }
    }

    private fun adaptPlaybackLayoutToNaviBar() {
        contentView?.post {
            if ((viewModel.pageManagement.photoPageTransitionState.value?.isExiting() == true)
                || (lifecycle.currentState < Lifecycle.State.INITIALIZED)
            ) {
                return@post
            }

            if (contentView == null) {
                GLog.d(TAG) { "View is not initialed or destroyed, should ignore." }
                return@post
            }
            contentView?.removeCallbacks(updateNaviBarPlaceholderRunnable)
            contentView?.post(updateNaviBarPlaceholderRunnable)

            val pageInstance = sectionPage.pageInstance
            pageInstance.systemBarController.setNaviBarColor(Color.TRANSPARENT)

            /**
             * 是否设置左右边距。分屏&&有虚拟导航栏情况下，需要适配导航栏的宽到水平padding，
             * 避免进度条被导航栏遮挡。
             *
             * 当不需要适配时，应将padding设为0，而不能去保持 vgPlaybackRoot 的 padding 不变，
             * 因为padding可能已经是适配过得了，此时保存原状是错误的，因此，要保证播控条的根布局布局本身不应有任何padding。
             */
            val isInMultiWindow = pageInstance.isInMultiWindow()
            val hasVirtualKey = pageInstance.hasVirtualKey()
            val shouldAdaptHorizontalPadding = isInMultiWindow && hasVirtualKey
            val playbackPaddingLeft = if (shouldAdaptHorizontalPadding) pageInstance.leftNaviBarHeight() else 0
            val playbackPaddingRight = if (shouldAdaptHorizontalPadding) pageInstance.rightNaviBarHeight() else 0

            // 去重
            if ((playbackPaddingLeft != vgPlaybackContainer?.paddingLeft) || (playbackPaddingRight != vgPlaybackContainer?.paddingRight)) {
                vgPlaybackContainer?.updatePadding(left = playbackPaddingLeft, right = playbackPaddingRight)
            }
        }
    }

    /**
     * 创建视频的大图Overlay附加层
     * @param slot item的位置
     * @param callback 返回一个构建好的overlay对象
     */
    @SuppressLint("InflateParams")
    private fun createOverlay(context: Context, slot: Int, callback: (PhotoSlotOverlay) -> Unit) {
        viewModel.launch(Dispatchers.SINGLE_UN_BUSY) {
            GTrace.traceBegin("$TAG.createOverlay")
            val containerView = LayoutInflater.from(context).inflate(R.layout.photo_overlay_playback, null)
            GTrace.traceEnd()

            withContext(Dispatchers.UI) {
                PhotoPlaybackOverlay(
                    sectionPage = sectionPage,
                    containerView = containerView
                ).let(callback)
            }
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        updateFreshRateIfNeeded()
        adaptPlaybackLayoutToNaviBar()
    }

    /**
     * 为保证视频播放时帧率稳定，需要界面刷新率和视频帧率保持一致或成倍数关系，比如当界面刷新为90帧，视频为60帧，
     * 会出现视频帧耗时为11、22、11、22、11、22ms...的帧间隔不均匀现象
     * 所以需要在视频播放时将界面帧率设置为60，暂停时恢复原帧率。
     *
     * 应该写到 ContainerSection 才对。。。
     */
    private fun updateFreshRateIfNeeded() {
        val activity = activity ?: let {
            GLog.w(TAG) { "[updateFreshRateIfNeeded] no activity!" }
            return
        }

        val isDestroyed = lifecycle.currentState.isAtLeast(Lifecycle.State.INITIALIZED).not()
        if (isDestroyed) {
            GLog.w(TAG) { "[updateFreshRateIfNeeded] ignored, destroyed!" }
            return
        }

        // 判断当前播放器是否正在以120fps播放，设置屏幕刷新率操作
        val shouldKeepFreshRate120Fps = shouldKeepFreshRate120Fps()
        val refreshRateRequest = getRefreshRateRequest(shouldKeepFreshRate120Fps)

        if (activity.run { isDestroyed().not() && isFinishing.not() && isRestricted.not() }) {
            // 变更屏幕刷新率
            viewModel.pageManagement.notifyChangeWindowRefreshRate(activity.window, refreshRateRequest)
            // 禁止显示框架无触控降帧操作
            maintainFrameRate(shouldKeepFreshRate120Fps)
        }
    }

    /**
     * 根据当前播控信息，判断是否应该调整屏幕刷新率至120fps。
     * @return Boolean
     */
    private fun shouldKeepFreshRate120Fps(): Boolean {
        // pass条件：视频播放器以120fps播放 & 视频本身帧率达到120 & 屏幕支持120Hz
        val isPlaybackAt120Fps = viewModel.playback.playbackInfo.value?.isPlaybackAt120Fps ?: false
        if (isPlaybackAt120Fps.not()) {
            return false
        }

        val videoFps = viewModel.playback.playbackInfo.value?.videoFps ?: 0F
        if (videoFps < FPS_120_MIN_AVERAGE_FPS_THRESHOLD) {
            return false
        }

        val isScreenSupportHighFps = viewModel.pageManagement.isCurrentDisplaySupportForRefreshRate(RefreshRateRequest.REQUEST_HIGH)
        if (isScreenSupportHighFps.not()) {
            return false
        }

        return true
    }

    /**
     * 获取刷新率请求设置
     * @param shouldKeepFreshRate120Fps 是否应该保持120fps刷新率
     * @return RefreshRateRequest DEFAULT/LOW/..
     */
    private fun getRefreshRateRequest(shouldKeepFreshRate120Fps: Boolean): RefreshRateRequest {
        // 如果还不允许调整刷新率，维持系统默认不要动
        if (shouldRefreshFrameRate().not()) {
            return RefreshRateRequest.REQUEST_DEFAULT
        }

        // 允许调整刷新率了，判断视频是否支持高帧率播放，而决定需不需要变更刷新率
        return if (shouldKeepFreshRate120Fps) {
            // 能够高帧播放，不要压低刷新率
            RefreshRateRequest.REQUEST_HIGH
        } else {
            // 无法高帧播放，刷新率压低至60hz
            RefreshRateRequest.REQUEST_LOW
        }
    }

    private fun shouldRefreshFrameRate(): Boolean {
        val isPlaying = currentPlaybackInfo?.isPlaying == true
        val isNotScrolling = pageScrollState == SCROLL_STATE_IDLE
        val isPageTransitionFinish = viewModel.pageManagement.photoPageTransitionState.value == PRESENTED
        GLog.d(TAG, LogFlag.DF) {
            "[shouldRefreshFrameRate] isPlaying=$isPlaying, isNotScrolling=$isNotScrolling, isPageTransitionFinish=$isPageTransitionFinish"
        }
        return isPlaying && isNotScrolling && isPageTransitionFinish
    }

    /**
     * 设置无触摸降帧：启用 or 关闭
     * @param shouldKeepFreshRate120Fps 是否应该未维持120fps刷新率。120fps刷新率情况下应关闭无触摸降帧
     *
     */
    private fun maintainFrameRate(shouldKeepFreshRate120Fps: Boolean) {
        val frameRate = if (shouldRefreshFrameRate() && shouldKeepFreshRate120Fps) FRAME_RATE_DISABLE_OTI else FRAME_RATE_ENABLE_OTI
        if (currentOtiFrameRate == frameRate) {
            return
        }

        otiFrameRateSettingJob?.cancel()
        otiFrameRateSettingJob = viewModel.viewModelScope.launch {
            context?.withAbility<IHardwareAbility, Unit> {
                val sf = contentView?.getSurfaceControl() ?: return@launch
                it?.screen?.maintainFrameRate(sf, frameRate)
                currentOtiFrameRate = frameRate
            }
        }
    }

    private fun updateKeepScreenOnStatus() {
        val window = activity?.window ?: let {
            GLog.w(TAG) { "[setupKeepScreenOn] no window" }
            return
        }

        val isPlaying = currentPlaybackInfo?.isPlaying ?: false
        val isNotDestroyed = lifecycle.currentState.isAtLeast(Lifecycle.State.INITIALIZED)
        val shouldKeepScreenOn = isPlaying && isNotDestroyed

        GLog.d(TAG) { "[setupKeepScreenOn] shouldKeepScreenOn=$shouldKeepScreenOn, isNotDestroyed=$isNotDestroyed, isPlaying=$isPlaying" }
        viewModel.playback.notifyScreenStateChanged(window, shouldKeepScreenOn)
    }

    private fun dispatchDetachEventToUIController() {
        playbackUIController?.detach()
    }

    private fun dispatchAttachEventToUIController() {
        vgPlaybackContainer?.let { playbackUIController?.attach(viewModel, playbackControl, it) }
    }

    private fun dispatchPlaybackInfoToUIController() {
        playbackUIController?.dispatchPlaybackInfo(currentPlaybackInfo, lastPlaybackInfo)
    }

    private fun dispatchPlaybackFeatureToUIController(playbackFeature: PlaybackFeature) {
        playbackUIController?.dispatchPlaybackFeature(playbackFeature)
    }

    private fun dispatchPhotoPageTransitionStateToUIController(state: PhotoPageManagementViewModel.PageTransitionState) {
        playbackUIController?.dispatchPageTransitionState(state)
        updateFreshRateIfNeeded()
    }

    private fun updatePlaybackThumbLineUIMarginBottom() {
        val playbackThumbLineUIController = (playbackUIController as? PlaybackThumbLineUIController) ?: return
        playbackThumbLineUIController.updateRootViewBottomMargin()
    }

    /**
     * 播控 UI 控制器，[PhotoPlaybackSection] 会通过该接口控制 UI
     *
     * 详情 UI 结构查看：
     * [PlaybackSection设计](https://odocs.myoas.com/folder/0l3NVmMr9GtlZz3R/)
     */
    interface IPlaybackUIController {

        /**
         * [IPlaybackUIController] 被附加到 [PhotoPlaybackSection] 上时调用该函数
         *
         * 实现者可以在此处做一些初始化操作
         */
        fun attach(viewModel: PhotoViewModel, playbackControl: IPlaybackControl, container: ViewGroup)

        /**
         * [IPlaybackUIController] 从 [PhotoPlaybackSection] 移除时调用该函数
         *
         * 实现者可以在此处做一些回收、销毁操作
         */
        fun detach()

        /**
         * 分发播控功能特性
         *
         * [PhotoPlaybackSection] 作为整体的 UI 逻辑控制器，考虑维度多，需要根据情况禁用、启用个别功能特性
         *
         * @param playbackFeature 播控功能特性
         *
         * @see PlaybackFeature
         */
        fun dispatchPlaybackFeature(playbackFeature: PlaybackFeature)

        /**
         * 分发播控信息
         *
         * @param playbackInfo 最新播控信息，用于更新状态，如果为 null，说明当前无播放器，或者不是视频
         * @param lastPlaybackInfo 上次播控信息，用于对比，如果为 null，说明上次无播放器，或者不是视频
         *
         * @see PlaybackInfo
         */
        fun dispatchPlaybackInfo(playbackInfo: PlaybackInfo?, lastPlaybackInfo: PlaybackInfo?)

        /**
         * 分发进入大图动画状态
         * @param state 进入大图动画状态
         */
        fun dispatchPageTransitionState(state: PhotoPageManagementViewModel.PageTransitionState)

        /**
         * 主题变更
         *
         * @param theme 大图悬浮按钮的主题
         */
        fun onThemeUpdated(theme: PhotoPageTheme)

        /**
         * 背景色变更
         *
         * @param resId color资源ID
         */
        fun onContainerBackgroundUpdated(@ColorRes resId: Int)
    }


    /**
     * 播控功能特性
     */
    sealed class PlaybackFeature {

        /**
         * 播放、暂停的功能特性，使用方根据该值决定是否启用、禁用对应控件
         */
        class PlayableFeature(
            /**
             * 该特性是否启用
             */
            val isEnabled: Boolean
        ) : PlaybackFeature()

        /**
         * 静音的功能特性，使用方根据该值决定是否启用、禁用对应控件
         */
        class MutableFeature(
            /**
             * 该特性是否启用
             */
            val isEnabled: Boolean
        ) : PlaybackFeature()
    }


    /**
     * 播放控制器
     */
    interface IPlaybackControl {

        /**
         * 当前播控信息
         *
         * 可能为 null，如果为 null，说明当前无播放器，或者不是视频
         */
        val playbackInfo: PlaybackInfo?

        /**
         * 请求播放
         */
        fun play()

        /**
         * 请求暂停
         */
        fun pause()

        /**
         * 请求静音
         */
        fun mute()

        /**
         * 请求非静音
         */
        fun unmute()

        /**
         * 请求 Seek 操作
         *
         * @param seekEvent Seek 事件
         *
         * @see SeekEvent
         */
        fun seek(seekEvent: SeekEvent)

        companion object {
            val empty: IPlaybackControl = object : IPlaybackControl {
                override val playbackInfo: PlaybackInfo? = null
                override fun play(): Unit = Unit
                override fun pause(): Unit = Unit
                override fun mute(): Unit = Unit
                override fun unmute(): Unit = Unit
                override fun seek(seekEvent: SeekEvent): Unit = Unit
            }
        }
    }


    /**
     * Seek 事件
     */
    sealed class SeekEvent {

        /**
         * 开始 Seek，一般用于手指触摸进度条时，大图内，需要根据此事件切换播放器 Seek 过程中预览模式
         */
        object StartSeek : SeekEvent()

        /**
         * 结束 Seek，一般用于手指离开进度条时，大图内，需要根据此事件切换播放器 Seek 过程中预览模式
         */
        object EndSeek : SeekEvent()

        /**
         * 切换 Seek 进度
         */
        class Seek(
            /**
             * Seek 到的绝对位置，以百分比表示，值域为 `[0, 1]`
             */
            val progress: Double
        ) : SeekEvent()
    }

    private inner class PlaybackControlImpl : IPlaybackControl {

        override val playbackInfo: PlaybackInfo?
            get() = currentPlaybackInfo

        override fun play() {
            viewModel.playback.play(activity)
        }

        override fun pause() {
            viewModel.playback.pause()
        }

        override fun mute() {
            viewModel.playback.mute()
        }

        override fun unmute() {
            viewModel.playback.unmute()
        }

        override fun seek(seekEvent: SeekEvent) {
            when (seekEvent) {
                is SeekEvent.StartSeek -> viewModel.playback.enterPreviewSeeking()
                is SeekEvent.EndSeek -> viewModel.playback.exitPreviewSeeking()
                is SeekEvent.Seek -> viewModel.playback.seek(seekEvent.progress)
            }
        }
    }

    private companion object {
        private const val TAG = "PhotoPlaybackSection"
        private const val ANIM_DURATION: Long = 400

        /**
         * marked by caiconghu 此处命名需要考虑<OTI>的具体含义，换一个更容易理解的命名。
         */
        private const val FRAME_RATE_DISABLE_OTI = 120F
        private const val FRAME_RATE_ENABLE_OTI = 0F
        private val ANIMATION_INTERPOLATOR = PathInterpolator(0.4f, 0f, 0.51f, 1f)
        private const val REMOVED_DELAY_ALPHA_SHOW_TIME = PhotoAnimationConfig.PHOTO_REMOVED_DELAY_ALPHA_SHOW_TIME

        private const val ANIMATION_BOUNCE = 0f
        private const val ANIMATION_RESPONSE_SHOW = 0.3f
        private const val ANIMATION_RESPONSE_HIDE = 0.25f
        private const val ALPHA_SHOW = 1f
        private const val ALPHA_HIDE = 0f

        /**
         * 因 ConstraintLayout 中大小 0 会变成 MATCH_CONSTRAINT，所以如果目标高度为 0，需设置为无意义的负数
         * （避开 MATCH_PARENT、WRAP_CONTENT 等）
         */
        private const val SIZE_ZERO_FOR_CONSTRAINT_LAYOUT = -5
    }
}
