/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SuperTextOptions
 ** Description: SuperTextOptions
 ** Version: 1.0
 ** Date : 2022/5/19
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/05/19    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.codec.supertext

import android.graphics.Bitmap.CompressFormat
import android.graphics.BitmapFactory

class SuperTextOptions : BitmapFactory.Options {
    /**
     * 仅解析出超级文本的幻数，不会解析图片像素
     */
    var inJustDecodeType: Boolean = false

    /**
     * 仅解析超级文本的编辑数据，如增强效果和校正效果，不解析效果图和原图
     */
    var inJustDecodeEditData: Boolean = false
    var encodeFormat = CompressFormat.JPEG
    var properties: SuperTextProperties

    /**
     * 原始文件长度（除去扩展数据的部分）
     */
    var originDataLength: Int = 0

    constructor() {
        properties = SuperTextProperties()
    }

    constructor(properties: SuperTextProperties) {
        this.properties = properties
    }
}
