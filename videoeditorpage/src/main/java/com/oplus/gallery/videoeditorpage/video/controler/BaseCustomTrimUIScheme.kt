/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseCustomTrimUIScheme
 ** Description:定制视频编辑 UIScheme
 ** Version: 1.0
 ** Date : 2025/06/26
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/26       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.controler

import android.content.res.Configuration
import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewStub
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.ui.EditorUIExecutor
import com.oplus.gallery.foundation.ui.systembar.OnSystemBarChangeListener
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
import com.oplus.gallery.videoeditorpage.controler.IVideoEditorUIScheme
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.FORM_TRIM_PAGE
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView
import kotlinx.coroutines.launch

/**
 * 定制视频编辑 UIScheme，目前仅壁纸和铃声业务使用
 */
abstract class BaseCustomTrimUIScheme(
    protected val activity: BaseActivity,
    private val rootView: ViewGroup,
    private val editorEngine: EditorEngine
) : IVideoEditorUIScheme {
    /**
     * 左侧安全区
     */
    protected val leftSafeArea by lazy {
        rootView.findViewById<View>(R.id.start_safe_area)
    }

    /**
     * 右侧安全区
     */
    protected val rightSafeArea by lazy {
        rootView.findViewById<View>(R.id.end_safe_area)
    }

    /**
     * 预览区
     */
    protected val previewContainer by lazy {
        rootView.findViewById<EditorPreviewView>(R.id.engine_preview_layout)
    }

    /**
     * 顶部操作栏，继续、取消的容器
     */
    protected val actionContainer by lazy {
        rootView.findViewById<RelativeLayout>(R.id.action_bar)
    }

    /**
     * 底部安全间距控件，用来适配导航栏
     */
    protected val bottomSafeView by lazy {
        rootView.findViewById<View>(R.id.bottom_safe)
    }

    /**
     * 剪辑控件容器
     */
    protected val toolbarContainer by lazy {
        rootView.findViewById<FrameLayout>(R.id.toolbar_container)
    }

    /**
     * 系统栏变化监听器
     */
    protected val systemBarChangeListener: OnSystemBarChangeListener =
        object : OnSystemBarChangeListener {
            override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
                adaptSysBarMargin()
            }
        }

    /**
     * UI规则的执行者
     */
    protected val uiExecutor = EditorUIExecutor()

    private val editorAppUiConfig by lazy {
        EditorAppUiConfig(activity.getCurrentAppUiConfig(), false)
    }

    init {
        val viewStub = rootView.findViewById<ViewStub>(R.id.video_editor_container_view_stub)
        if (viewStub != null) {
            viewStub.layoutResource = getContentLayoutId(activity.getCurrentAppUiConfig())
            viewStub.inflate()
        }
        uiExecutor.init(activity)
        setScheme()
        editorAppUiConfig.appUiConfig = activity.getCurrentAppUiConfig()
        uiExecutor.checkUpdateLayout(editorAppUiConfig)
        activity.registerSystemBarChangeListener(systemBarChangeListener)

        previewContainer.getChildAt(0).setBackgroundColor(Color.TRANSPARENT)

        activity.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> release()
                    else -> Unit
                }
            }
        })
    }

    override fun getReconfigureViewIds(): List<Int> {
        return listOf(
            R.id.action_bar,
            R.id.engine_preview_layout,
            R.id.toolbar_playback_container,
            R.id.toolbar_container
        )
    }

    override fun getRootView(): ViewGroup {
        return rootView
    }

    override fun onUiConfigChanged(
        baseActivity: BaseActivity,
        layoutId: Int,
        config: EditorAppUiConfig
    ) {
        adaptSysBarMargin()
        adaptSafeArea(config.appUiConfig)
        adaptToolbar(config.appUiConfig)
    }

    /**
     * 通知UI配置发生了变化
     *
     * @param config UI配置
     */
    override fun notifyAppUiConfigChange(config: AppUiResponder.AppUiConfig) {
        GLog.d(TAG, LogFlag.DL) {
            "[notifyAppUiConfigChange]"
        }
        uiExecutor.onAppUiConfigChanged(editorAppUiConfig.apply { appUiConfig = config })
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        activity.lifecycleScope.launch {
            editorEngine.seekTo(
                editorEngine.timelineCurrentPosition,
                STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
            )
        }
    }

    open fun adaptSysBarMargin() {
        // override by child class
    }

    open fun setScheme() {
        // override by child class
    }

    private fun adaptSafeArea(config: AppUiResponder.AppUiConfig) {
        VideoEditorUIConfig.adaptSafeAreaInVideo(activity, leftSafeArea, rightSafeArea, config)
        leftSafeArea.requestLayout()
        rightSafeArea.requestLayout()
    }

    private fun adaptToolbar(config: AppUiResponder.AppUiConfig) {
        val scale = VideoEditorUIConfig.getViewScaleByWindowSize(config, FORM_TRIM_PAGE)
        val viewSize = ScreenUtils.pixelToDp(config.windowWidth.current)

        VideoEditorUIConfig.getViewSizeByScale(
            R.dimen.videoeditor_custom_trim_touch_view_container_height,
            activity,
            config,
            FORM_TRIM_PAGE
        ).let { toolbarHeight ->
            toolbarContainer.layoutParams.let { lp ->
                lp.width = MATCH_PARENT
                lp.height = toolbarHeight
            }
        }

        val toolbarContentSize = ScreenUtils.dpToPixel(
            VideoEditorUIConfig.getToolBarContentWidthForTrimSize(viewSize, scale)
        )
        toolbarContainer.findViewById<FrameLayout>(R.id.editor_trim_toolbar_menu_layout)?.let {
            (it.layoutParams as FrameLayout.LayoutParams).let { lp ->
                lp.width = toolbarContentSize
                lp.gravity = Gravity.CENTER
            }
        }

        toolbarContainer.findViewById<FrameLayout>(R.id.video_thumb_layout)?.let {
            it.scaleX = scale
            it.scaleY = scale
        }

        toolbarContainer.requestLayout()
    }

    /**
     * 释放、反注册、销毁相关资源
     */
    private fun release() {
        activity.unregisterSystemBarChangeListener(systemBarChangeListener)
    }

    companion object {
        private const val TAG = "BaseCustomTrimUIScheme"
    }
}