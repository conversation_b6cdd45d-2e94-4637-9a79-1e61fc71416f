/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ColorItemView.kt
 ** Description : 选取颜色的item
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.BlendMode
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View

/**
 * 颜色选择器中的单个圆形颜色view
 */
class ColorItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : View(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 圆形颜色view的绘制属性数据
     */
    var itemData: ColorItemViewData = ColorItemViewData(0f, 0f, 0f, Color.TRANSPARENT, Color.TRANSPARENT)
        set(value) {
            if (value != field) {
                field = value
                updatePaints()
                invalidate()
            }
        }

    /**
     * 内圆环画刷
     */
    private val innerRingPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        isDither = true
        blendMode = BlendMode.SRC_OVER
        style = Paint.Style.STROKE
        strokeWidth = itemData.ringWidth
    }

    /**
     * 背景色画刷
     */
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        isDither = true
        style = Paint.Style.FILL
    }

    /**
     * 单个颜色选择view的数据配置项
     */
    data class ColorItemViewData(
        /**
         * 圆环宽度
         */
        val ringWidth: Float,
        /**
         * 内圆半径
         */
        val innerRadius: Float,
        /**
         * 外圆半径
         */
        val outerRadius: Float,
        /**
         * 背景色
         */
        val itemColor: Int,
        /**
         * 圆环色
         */
        val ringColor: Int
    ) {

        /**
         * 重载两个对象相等逻辑,只需要判断两个颜色值相同即相同
         */
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is ColorItemViewData) return false
            return ((this.itemColor == other.itemColor) && (this.ringColor == other.ringColor))
        }

        override fun hashCode() = (itemColor.hashCode() + ringColor.hashCode())
    }

    /**
     * 更新画刷
     */
    private fun updatePaints() {
        backgroundPaint.color = itemData.itemColor
        innerRingPaint.color = itemData.ringColor
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 计算中心点
        val centerX = width / 2f
        val centerY = height / 2f

        // 绘制圆环
        if (itemData.ringColor != Color.TRANSPARENT) {
            canvas.drawCircle(centerX, centerY, itemData.outerRadius, innerRingPaint)
        }

        // 绘制主圆形背景色
        if (itemData.itemColor != Color.TRANSPARENT) {
            canvas.drawCircle(centerX, centerY, itemData.innerRadius, backgroundPaint)
        }
    }
}