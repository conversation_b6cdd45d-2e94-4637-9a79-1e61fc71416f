/********************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GalleryVideoCustomTrimTouchView.java
 * Description: 时码线，左右把手交互，支持设置最大最小剪辑时间
 *
 *
 * Version: 1.0
 * Date:2025/05/14
 * Author:<EMAIL>
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * liu<PERSON>@yeahzee.com             2025/04/14   1.0          build this module
</desc></version></date></author> */
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.NinePatch
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.addon.os.VibratorUtils.vibrate
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditorpage.R
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min

/**
 * 剪辑控件 目前只有壁纸业务使用
 */
class VideoTrimTouchView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    /**
     * 蒙版画笔
     */
    private val borderBGPaint = Paint()

    /**
     * 进度条提示文字与进度条的连接线画笔
     */
    private val trimTimePaint = Paint()

    /**
     * 时间画笔
     */
    private val trimTimeTextPaint = Paint()

    /**
     * 把手区域与与控件的边距
     */
    private var viewPadding = 0

    /**
     * 最小剪辑时长映射的距离
     */
    private var trimMinGap = 0f

    /**
     * trimMinGap/viewWidth
     */
    private var trimMinPercent = 0f

    /**
     * 左边把手所在位置
     */
    private var leftBorderPos = 0f

    /**
     * 右边把手所在位置
     */
    private var rightBorderPos = 0f

    /**
     * 当前播放位置的百分比
     */
    private var currentPlayPercent = 0f

    /**
     * 手指按下的位置
     */
    private var touchDownX = 0f

    /**
     * 手指按下时的区域
     */
    private var currentTouchIndex = 0

    /**
     * 手指位置距边缘的公差
     */
    private var autoMoveGap = 0

    /**
     * 是否显示播放位置
     */
    private var showPlayPosition = true

    /**
     * 是否显示时间
     */
    private var showTrimTime = false

    /**
     * 是否显示高亮效果
     */
    private var showMaxMinGapEffect = false

    /**
     * 缩图轴宽度
     */
    private var viewWidth = 0

    /**
     * 缩图轴高度
     */
    private var viewHeight = 0

    /**
     * 时间区域offset
     */
    private var trimTimeOffset = 0

    /**
     * 时间区域高度
     */
    private var trimTimeViewHeight = 0

    /**
     * 时间区域宽度
     */
    private var trimTimeViewWidth = 0

    /**
     * 时间连接线高度
     */
    private var trimTimeRectangleHeight = 0

    /**
     * 时间连接线宽度
     */
    private var trimTimeRectangleWidth = 0

    /**
     * 时间区域和缩图轴区域的间距
     */
    private var trimTimeViewBottomPadding = 0

    /**
     * 时间文字内容
     */
    private var trimTimeText: String = TextUtil.EMPTY_STRING

    /**
     * 时间文字内容距离时间区域的左右边距
     */
    private var trimTimeTextWidthPadding = 0

    /**
     * 时间文字内容距时间区域的上下边距
     */
    private var trimTimeTextHeightPadding = 0

    /**
     * 蒙版与控件区域的上边距
     */
    private var touchViewMargin = 0

    /**
     * 时间区域的圆角半径
     */
    private var trimTimeRadius = 0

    /**
     * 时间绘制区域
     */
    private var trimTimeRect: RectF = RectF()

    /**
     * 连接线绘制区域
     */
    private var trimTimeRectangleRect: RectF = RectF()

    /**
     * 缩图轴高度
     */
    private var trimFrame = 0

    /**
     * 正常把手
     */
    private var trimNormal: NinePatch

    /**
     * 高亮把手
     */
    private var trimViewMaxMinGap: NinePatch

    /**
     * 进度线bitmap
     */
    private var posBitmap: Bitmap

    /**
     * 把手宽度
     */
    private var trimWindowWidth = 0

    /**
     * 把手区域宽度
     */
    private var trimWindowSpaceWidth = 0

    /**
     * 把手位置监听器
     */
    private var trimPositionChangeListener: TrimPositionChangeListener? = null

    /**
     * 缩图轴宽度映射的时间
     */
    private var duration: Long = 0

    /**
     * 缩图轴距控件的左右边距
     */
    val trimSpaceMargin: Int
        get() = trimWindowWidth + viewPadding

    /**
     * 左把手位置百分比
     */
    private val leftBorderPosPercent: Float
        get() {
            if (viewWidth != 0) {
                return leftBorderPos / viewWidth
            }
            return (-1).toFloat()
        }

    /**
     * 有把手位置百分比
     */
    private val rightBorderPosPercent: Float
        get() {
            if (viewWidth != 0) {
                return (rightBorderPos - 2 * trimWindowWidth) / viewWidth
            }
            return (-1).toFloat()
        }

    init {
        autoMoveGap = resources.getDimensionPixelSize(R.dimen.videoeditor_trim_auto_move_gap)
        trimTimeViewHeight =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_view_height)
        trimTimeViewWidth =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_view_width)
        trimTimeOffset = resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_offset)
        trimTimeRectangleWidth =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_rectangle_width)
        trimTimeRectangleHeight =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_rectangle_height)
        trimTimeViewBottomPadding =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_view_bottom_padding)
        touchViewMargin = trimTimeViewHeight + trimTimeViewBottomPadding
        trimTimeRadius = resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_radius)
        trimTimeTextWidthPadding =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_text_width_padding)
        trimTimeTextHeightPadding =
            resources.getDimensionPixelSize(R.dimen.videoeditor_trim_time_text_height_padding)

        // 进度条提示文字区域
        trimTimeRect = RectF(0f, 0f, trimTimeViewWidth.toFloat(), trimTimeViewHeight.toFloat())
        // 进度条提示文字与进度条的连接线区域
        trimTimeRectangleRect = RectF(
            0f,
            trimTimeViewHeight.toFloat(),
            trimTimeRectangleWidth.toFloat(),
            (trimTimeViewHeight + trimTimeRectangleHeight).toFloat()
        )
        trimTimePaint.color = resources.getColor(R.color.videoeditor_trim_time_background, null)
        trimTimePaint.isAntiAlias = true
        trimTimeTextPaint.color = resources.getColor(
            R.color.videoeditor_trim_time_hint_textcolor, null
        )
        val textSize =
            resources.getDimensionPixelSize(R.dimen.videoeditor_video_editor_suitable_text_size)
        trimTimeTextPaint.textSize = textSize.toFloat()
        trimTimeTextPaint.isAntiAlias = true
        trimTimeTextPaint.textAlign = Paint.Align.LEFT
        trimFrame = resources.getDimensionPixelSize(R.dimen.videoeditor_trim_frame_view_height)
        borderBGPaint.color =
            context.getColor(com.oplus.gallery.basebiz.R.color.videoeditor_video_editor_background_color_edit)
        borderBGPaint.isAntiAlias = true
        borderBGPaint.alpha = TRIM_ALPHA
        val normal =
            BitmapFactory.decodeResource(resources, R.drawable.videoeditor_editor_trim_normal)
        val maxMinGap =
            BitmapFactory.decodeResource(resources, R.drawable.videoeditor_editor_trim_press)
        trimNormal = NinePatch(normal, normal.ninePatchChunk, null)
        trimViewMaxMinGap = NinePatch(maxMinGap, maxMinGap.ninePatchChunk, null)
        // 进度条指示器
        posBitmap = BitmapFactory.decodeResource(resources, R.drawable.videoeditor_trim_play_pos)
        trimWindowWidth = resources.getDimensionPixelSize(R.dimen.videoeditor_trim_window_width)

        viewHeight = resources.getDimensionPixelSize(R.dimen.videoeditor_trim_thumbnail_height)
        viewPadding =
            context.resources.getDimensionPixelSize(R.dimen.videoeditor_trim_to_padding_of_border)
    }

    /**
     * 设置把手位置监听器
     */
    fun setTrimPositionChangeListener(listener: TrimPositionChangeListener?) {
        trimPositionChangeListener = listener
    }

    /**
     * 设置初始化参数
     *
     * @param trimMinTime 剪辑最短时长
     * @param trimMaxTime 剪辑最大时长
     * @param duration    视频时长
     */
    fun setInitParam(trimMinTime: Long, trimMaxTime: Long, duration: Long) {
        this.duration = min(trimMaxTime.toDouble(), duration.toDouble()).toLong()
        var trimRealMinTime = max(
            DEFAULT_TRIM_MIN_TIME.toDouble(), trimMinTime.toDouble()
        ).toLong()

        if (trimRealMinTime + TIME_INTERVAL > this.duration) {
            trimRealMinTime = this.duration
        }

        trimMinPercent = if (trimRealMinTime >= this.duration) {
            ONE_HUNDRED_PERCENT
        } else {
            trimRealMinTime.toFloat() / this.duration
        }

        trimWindowSpaceWidth = width - 2 * viewPadding
        viewWidth = trimWindowSpaceWidth - 2 * trimWindowWidth

        trimMinGap =
            floor((viewWidth * trimMinPercent + ROUND_OFFSET).toDouble()).toInt().toFloat()

        currentPlayPercent = 0f

        leftBorderPos = 0f
        rightBorderPos = trimWindowSpaceWidth.toFloat()
    }

    /**
     * 设置左右把手的位置
     * @param leftPosPercent 左把手位置百分比
     * @param rightPosPercent 右把手位置百分比
     */
    fun setLeftAndRightPos(leftPosPercent: Float, rightPosPercent: Float) {
        leftBorderPos = leftPosPercent * viewWidth
        rightBorderPos = rightPosPercent * viewWidth + 2 * trimWindowWidth
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawBorder(canvas)
        drawTrimWindow(canvas)
        if (showTrimTime) {
            drawTrimTimeView(canvas)
        }
    }

    /**
     * 进度提示
     */
    private fun drawTrimTimeView(canvas: Canvas) {
        canvas.drawRoundRect(
            trimTimeRect, trimTimeRadius.toFloat(), trimTimeRadius.toFloat(), trimTimePaint
        )
        canvas.drawRect(trimTimeRectangleRect, trimTimePaint)
        canvas.drawText(
            trimTimeText,
            trimTimeRect.left + trimTimeTextWidthPadding,
            trimTimeRect.top + trimTimeViewHeight - trimTimeTextHeightPadding,
            trimTimeTextPaint
        )
    }

    /**
     * 绘制左右蒙版
     */
    private fun drawBorder(canvas: Canvas) {
        if (leftBorderPos >= 0) {
            val bg = RectF(
                0f,
                touchViewMargin + (posBitmap.height - viewHeight) / 2f,
                leftBorderPos + viewPadding + MASK_DIMS,
                touchViewMargin + (posBitmap.height - viewHeight) / 2f + viewHeight
            )
            canvas.drawRect(bg, borderBGPaint)
        }
        if (rightBorderPos <= trimWindowSpaceWidth) {
            val bg = RectF(
                rightBorderPos + viewPadding - MASK_DIMS,
                touchViewMargin + (posBitmap.height - viewHeight) / 2f,
                (trimWindowSpaceWidth + 2 * viewPadding).toFloat(),
                touchViewMargin + (posBitmap.height - viewHeight) / 2f + viewHeight
            )
            canvas.drawRect(bg, borderBGPaint)
        }
    }

    /**
     * 绘制把手
     */
    private fun drawTrimWindow(canvas: Canvas) {
        val rect = RectF(
            leftBorderPos + viewPadding,
            touchViewMargin + (posBitmap.height - trimFrame) / 2f,
            rightBorderPos + viewPadding,
            touchViewMargin + (posBitmap.height - trimFrame) / 2f + trimFrame
        )
        if (showMaxMinGapEffect) {
            trimViewMaxMinGap.draw(canvas, rect)
        } else {
            trimNormal.draw(canvas, rect)
        }
    }

    /**
     * 重绘进度指示器
     */
    fun updateCurrentPosition(postPercent: Float) {
        currentPlayPercent = postPercent
        if (showPlayPosition) {
            postInvalidate()
        }
    }

    /**
     * 重绘时间提示
     */
    fun updateTrimTime(text: String?) {
        text ?: return
        if (text.length != trimTimeText.length) {
            updateTrimTimeViewWidth(text)
        }
        this.trimTimeText = text
    }

    private fun updateTrimTimeViewWidth(trimTimeText: String) {
        val lastPosition = trimTimeRect.left + trimTimeOffset + trimTimeViewWidth / 2f
        val textWidth = trimTimeTextPaint.measureText(trimTimeText)
        trimTimeViewWidth = (textWidth + trimTimeTextWidthPadding * 2).toInt()
        updateTrimTimeRect(lastPosition)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val action = event.action
        val touchX = event.x - viewPadding
        when (action) {
            MotionEvent.ACTION_DOWN -> {
                if (event.y < trimTimeViewHeight + trimTimeViewBottomPadding) {
                    //超出热区
                    return true
                }
                touchDownX = touchX
                evalTouchPosition(touchDownX)
                if (currentTouchIndex < EMPTY_TOUCHED) {
                    invalidate()
                    if (currentTouchIndex == DRAG_POSITION_TOUCHED) {
                        return false
                    }
                } else {
                    return true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (
                    (currentTouchIndex < EMPTY_TOUCHED) //手指按下的位置是否在有效范围内
                    && (abs((touchX - touchDownX).toDouble()) > 1) //防止在手指按下的位置重复触发
                    && (event.getPointerId(event.actionIndex) == 0) //只触发pointerId为0的手指的TOUCH_MOVE事件
                ) {
                    moveTouchEvent(event)
                }
            }

            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP -> {
                showTrimTime = false
                changeShowMaxMinGapEffect(false)
                if (currentTouchIndex < EMPTY_TOUCHED) {
                    notifyTrim()
                    showPlayPosition = true
                    invalidate()
                    currentTouchIndex = 0
                }
            }

            else -> {}
        }
        return super.onTouchEvent(event)
    }

    /**
     * 获取把手位置的百分比
     * @param isLeft ture 左 false 右
     */
    fun getBorderPosPercent(isLeft: Boolean): Float {
        val leftPercent = leftBorderPosPercent
        val rightPercent = rightBorderPosPercent
        return if (isLeft) leftPercent else rightPercent
    }

    /**
     * 获取trimIn的绝对时间
     *
     * @param originTime 当前视口的原点时间
     */
    fun getTrimInTime(originTime: Long): Long {
        return originTime + floor((duration * leftBorderPosPercent + ROUND_OFFSET).toDouble()).toLong()
    }

    /**
     * 获取trimOut的绝对时间
     *
     * @param originTime 当前视口的原点时间
     */
    fun getTrimOutTime(originTime: Long): Long {
        return originTime + floor((duration * rightBorderPosPercent + ROUND_OFFSET).toDouble()).toLong()
    }

    private fun notifyTrim() {
        when (currentTouchIndex) {
            LEFT_BORDER_TOUCHED -> trimPositionChangeListener?.onTrimLeft(leftBorderPosPercent)

            RIGHT_BORDER_TOUCHED -> trimPositionChangeListener?.onTrimRight(rightBorderPosPercent)

            else -> Unit
        }
    }

    private fun moveTrimTime(x: Float) {
        showTrimTime = true
        updateTrimTimeRect(x)
    }

    private fun updateTrimTimeRect(x: Float) {
        trimTimeRect.left = x - trimTimeViewWidth / 2f - trimTimeOffset
        trimTimeRect.right = trimTimeRect.left + trimTimeViewWidth
        trimTimeRectangleRect.left = trimTimeRect.left + trimTimeRect.width() / 2
        trimTimeRectangleRect.right = trimTimeRectangleRect.left + trimTimeRectangleWidth
    }

    private fun moveTouchEvent(event: MotionEvent) {
        val touchX = event.x - viewPadding
        var isShowMaxMinGapEffect = false
        when (currentTouchIndex) {
            LEFT_BORDER_TOUCHED -> isShowMaxMinGapEffect = leftBorderTouchMove(touchX)
            RIGHT_BORDER_TOUCHED -> isShowMaxMinGapEffect = rightBorderTouchMove(touchX)
            else -> Unit
        }
        changeShowMaxMinGapEffect(isShowMaxMinGapEffect)
        invalidate()
    }

    private fun leftBorderTouchMove(x: Float): Boolean {
        var touchX = x
        var isShowTrimTime = true
        var trimLeftPercent = 0f
        var trimRightPercent = 0f
        var isShowMaxMinGapEffect = false
        isShowTrimTime = touchX.compareTo(rightBorderPos - trimWindowWidth) <= 0

        if ((touchX.compareTo(rightBorderPos - 2 * trimWindowWidth - trimMinGap) >= 0)
            || ((rightBorderPos - 2 * trimWindowWidth - trimMinGap).compareTo(0f) <= 0)
        ) {
            touchX = rightBorderPos - 2 * trimWindowWidth - trimMinGap
            isShowMaxMinGapEffect = true
        }

        if (touchX.compareTo(autoMoveGap.toFloat()) <= 0) {
            touchX = 0f
            isShowTrimTime = false
        }

        if (isShowTrimTime) {
            moveTrimTime(touchX + viewPadding + trimWindowWidth)
        }

        if ((rightBorderPos.compareTo(trimWindowSpaceWidth.toFloat()) == 0)
            && (touchX.compareTo(0f) == 0)
        ) {
            isShowMaxMinGapEffect = true
        }

        leftBorderPos = Math.round(touchX).toFloat()

        currentPlayPercent = leftBorderPosPercent

        trimLeftPercent = leftBorderPosPercent
        trimRightPercent = rightBorderPosPercent

        trimPositionChangeListener?.onSeek(currentPlayPercent, trimLeftPercent, trimRightPercent)
        return isShowMaxMinGapEffect
    }

    private fun rightBorderTouchMove(x: Float): Boolean {
        var touchX = x
        var isShowTrimTime = true
        var trimLeftPercent = 0f
        var trimRightPercent = 0f
        var isShowMaxMinGapEffect = false
        isShowTrimTime = touchX.compareTo(leftBorderPos + trimWindowWidth) >= 0

        if ((touchX.compareTo(leftBorderPos + 2 * trimWindowWidth + trimMinGap) < 0)
            || ((leftBorderPos + 2 * trimWindowWidth + trimMinGap).compareTo(trimWindowSpaceWidth.toFloat()) >= 0)
        ) {
            touchX = leftBorderPos + 2 * trimWindowWidth + trimMinGap
            isShowMaxMinGapEffect = true
        }

        if (touchX.compareTo(trimWindowSpaceWidth.toFloat()) > 0) {
            touchX = (viewWidth + 2 * trimWindowWidth).toFloat()
            isShowTrimTime = false
        }

        if (isShowTrimTime) {
            moveTrimTime(touchX + viewPadding - trimWindowWidth)
        }

        if ((leftBorderPos.compareTo(0f) == 0)
            && (touchX.compareTo(trimWindowSpaceWidth.toFloat()) == 0)
        ) {
            isShowMaxMinGapEffect = true
        }

        rightBorderPos = Math.round(touchX).toFloat()

        currentPlayPercent = rightBorderPosPercent

        trimLeftPercent = leftBorderPosPercent
        trimRightPercent = rightBorderPosPercent

        trimPositionChangeListener?.onSeek(currentPlayPercent, trimLeftPercent, trimRightPercent)
        return isShowMaxMinGapEffect
    }

    private fun evalTouchPosition(mTouchDownX: Float) {
        if ((mTouchDownX < leftBorderPos - 2 * trimWindowWidth) || (mTouchDownX > rightBorderPos + 2 * trimWindowWidth)) {
            currentTouchIndex = EMPTY_TOUCHED
            return
        }

        currentTouchIndex = DRAG_POSITION_TOUCHED

        if ((mTouchDownX >= leftBorderPos - 2 * trimWindowWidth) && (mTouchDownX <= leftBorderPos + trimWindowWidth)) {
            currentTouchIndex = LEFT_BORDER_TOUCHED
            showPlayPosition = false
        }

        if ((mTouchDownX >= rightBorderPos - trimWindowWidth) && (mTouchDownX <= rightBorderPos + 2 * trimWindowWidth)) {
            currentTouchIndex = RIGHT_BORDER_TOUCHED
            showPlayPosition = false
        }
    }

    private fun changeShowMaxMinGapEffect(isShow: Boolean) {
        if (!showMaxMinGapEffect && isShow) {
            vibrate(context, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK)
        }
        showMaxMinGapEffect = isShow
    }

    interface TrimPositionChangeListener {
        fun onSeek(pos: Float, trimLeft: Float, trimRight: Float)

        fun onTrimLeft(pos: Float)

        fun onTrimRight(pos: Float)
    }

    companion object {
        private const val TAG = "GalleryVideoCustomTrimTouchView"

        private const val TRIM_ALPHA = 180
        private const val LEFT_BORDER_TOUCHED = 1
        private const val RIGHT_BORDER_TOUCHED = 2
        private const val DRAG_POSITION_TOUCHED = 3
        private const val EMPTY_TOUCHED = 4
        private const val DEFAULT_TRIM_MIN_TIME = 1000L
        private const val TIME_INTERVAL = TimeUtils.TIME_1_SEC_IN_MS.toLong()
        private const val ROUND_OFFSET = 0.5f
        private const val ONE_HUNDRED_PERCENT = 1.0f
        private const val MASK_DIMS = 12
    }
}
