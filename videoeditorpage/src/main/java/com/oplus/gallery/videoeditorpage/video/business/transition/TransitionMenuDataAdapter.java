/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: TransitionMenuDataAdapter
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguang<PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.transition;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.content.res.AppCompatResources;

import com.oplus.gallery.videoeditorpage.resource.room.entity.TransitionEntity;
import com.oplus.gallery.videoeditorpage.utlis.ImageLoader;
import com.oplus.gallery.videoeditorpage.widget.RoundProgressView;
import com.oplus.gallery.videoeditorpage.widget.viewholder.BaseRecycleViewHolder;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.widget.adapter.BaseThumbNailRecycleAdapter;

import java.util.List;

/**
 * 转场菜单数据适配器
 */
public class TransitionMenuDataAdapter extends BaseThumbNailRecycleAdapter<TransitionEntity> {
    private static final String TAG = "TransitionMenuDataAdapter";

    public TransitionMenuDataAdapter(Context context, List<TransitionEntity> data) {
        super(context, data);
    }

    @Override
    public int getItemLayoutId() {
        return R.layout.videoeditor_transition_menu_item;
    }

    @Override
    public void bindData(@NonNull BaseRecycleViewHolder viewHolder, int position, @NonNull TransitionEntity item) {
        // 边框
        RelativeLayout iconBorder = viewHolder.getView(R.id.icon_border);
        iconBorder.setBackground((position == mCurrentSelection)
            ? AppCompatResources.getDrawable(mContext, R.drawable.videoeditor_transition_menu_item_selected_foreground)
            : null
        );

        // 图片
        ImageView preview = viewHolder.getView(R.id.icon_image);
        preview.setSelected(position == mCurrentSelection);

        // 是否为“无”效果
        final String iconPath = item.getIconPath();
        if (iconPath.endsWith(".gif")) {
            preview.setScaleType(ImageView.ScaleType.FIT_CENTER);
            ImageLoader.getInstance().startLoaderGif(mContext, iconPath, preview);
            preview.setForeground(AppCompatResources.getDrawable(mContext, R.drawable.videoeditor_transition_menu_icon_foreground));
        } else {
            mImageLoaderManager.startLoader(item, preview);
            preview.setForeground(null);
        }
        // 文本
        TextView text = viewHolder.getView(R.id.icon_text);
        text.setText(item.getName());
        text.setSelected(position == mCurrentSelection);

        // 下载
        RoundProgressView progressView = viewHolder.getView(R.id.download_progress);
        View downloadIconView = viewHolder.getView(R.id.download_icon);
        if (item.isNeedDownloadFile()) {
            if (item.getProgress() > 0) {
                downloadIconView.setVisibility(View.GONE);
                progressView.setVisibility(View.VISIBLE);
                progressView.setProgress(item.getProgress());
            } else {
                downloadIconView.setVisibility(View.VISIBLE);
                progressView.setVisibility(View.GONE);
            }
        } else {
            downloadIconView.setVisibility(View.GONE);
            progressView.setVisibility(View.GONE);
        }
    }

    @Override
    public boolean supportUnselected() {
        return false;
    }

    @Override
    public boolean isSelectable(BaseRecycleViewHolder viewHolder) {
        return true;
    }

    @Override
    public void onBindViewHolder(BaseRecycleViewHolder holder, int position) {
        super.onBindViewHolder(holder, position);
        TextView text = holder.getView(R.id.icon_text);
        if (text != null) {
            text.setTextColor(mContext.getColorStateList(R.color.videoeditor_transition_item_text_color_selector));
            text.setSelected((position == mCurrentSelection) && isSelectable(holder));
        }
    }
}
