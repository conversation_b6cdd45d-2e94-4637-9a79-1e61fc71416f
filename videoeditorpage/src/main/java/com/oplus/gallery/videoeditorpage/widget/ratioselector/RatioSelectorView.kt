/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  RatioSelectorView
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/9
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/9      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ratioselector

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.annotation.IntDef
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.widget.SpacingItemDecoration

/**
 * 实现比例选择控件
 */
class RatioSelectorView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = R.attr.ratioSelectorViewStyle,
    defStyleRes: Int = R.style.Default_RatioSelectorView_Style
) : FrameLayout(context, attrs, defStyleAttr) {

    /**
     * 比例选项列表
     */
    private val ratioOptions = mutableListOf<RatioOption>()

    @ScrollOrientation
    private var scrollOrientation = ORIENTATION_HORIZONTAL

    /**
     * 控件样式配置数据类
     */
    private var config: RatioSelectorConfig

    private val ratioAdapter: RatioAdapter

    private var onRatioSelectedListener: ((RatioOption) -> Unit)? = null

    private val recyclerView: RecyclerView

    init {
        context.obtainStyledAttributes(attrs, R.styleable.RatioSelectorView, defStyleAttr, defStyleRes).apply {
            scrollOrientation = getInt(R.styleable.RatioSelectorView_scrollOrientation, ORIENTATION_HORIZONTAL)
            config = RatioSelectorConfig(
                itemSpacing = getDimensionPixelSize(R.styleable.RatioSelectorView_itemSpacing, 0),
                iconTextSpacing = getDimensionPixelSize(R.styleable.RatioSelectorView_iconTextSpacing, 0),
                contentPaddingHorizontal = getDimensionPixelSize(
                    R.styleable.RatioSelectorView_contentPaddingHorizontal,
                    0
                ),
                contentPaddingVertical = getDimensionPixelSize(R.styleable.RatioSelectorView_contentPaddingVertical, 0),
                contentClipToPadding = getBoolean(R.styleable.RatioSelectorView_contentClipToPadding, true),
                contentFadingEdgeLength = getDimensionPixelSize(R.styleable.RatioSelectorView_contentFadingEdgeLength, 0),
                contentFadingEdgeColor = getColor(R.styleable.RatioSelectorView_contentFadingEdgeColor, 0)
            )
            recycle()
        }
        ratioAdapter = RatioAdapter(config) { selectedOption -> onRatioSelectedListener?.invoke(selectedOption) }

        recyclerView = COUIRecyclerView(context).apply {
            adapter = ratioAdapter
            setHasFixedSize(true)
            // 设置渐变边缘
            if (config.contentFadingEdgeLength > 0) {
                setFadingEdgeLength(config.contentFadingEdgeLength)
                isHorizontalFadingEdgeEnabled = (scrollOrientation == ORIENTATION_HORIZONTAL)
                isVerticalFadingEdgeEnabled = (scrollOrientation == ORIENTATION_VERTICAL)
                // 注意：setEdgeEffectColor方法在当前版本中不可用
            } else {
                // 如果没有渐变边缘，则使用用户设置的 clipToPadding 值
                clipToPadding = config.contentClipToPadding
            }
        }
        addSpacingDecoration()
        setScrollOrientation(scrollOrientation)
        addView(recyclerView)
    }

    /**
     * 根据滚动方向添加 ItemDecoration 实现 item 间距和内边距设置
     */
    private fun addSpacingDecoration() {
        // 移除之前的 decoration（如果存在）
        while (recyclerView.itemDecorationCount > 0) {
            recyclerView.removeItemDecorationAt(0)
        }
        val isHorizontal = (scrollOrientation == ORIENTATION_HORIZONTAL)

        // 使用 SpacingItemDecoration 来实现间距和内边距
        recyclerView.addItemDecoration(
            SpacingItemDecoration(
                itemSpacing = config.itemSpacing,
                isHorizontal = isHorizontal,
                paddingStart = config.contentPaddingHorizontal,
                paddingEnd = config.contentPaddingHorizontal,
                paddingTop = config.contentPaddingVertical,
                paddingBottom = config.contentPaddingVertical
            )
        )

        // 移除 RecyclerView 的直接内边距，因为现在通过 ItemDecoration 实现
        recyclerView.setPadding(0, 0, 0, 0)
    }

    /**
     * 设置滚动方向
     */
    fun setScrollOrientation(@ScrollOrientation orientation: Int) {
        this.scrollOrientation = orientation
        val layoutManager = when (orientation) {
            ORIENTATION_VERTICAL -> LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            else -> LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }
        recyclerView.layoutManager = layoutManager

        // 更新渐变边缘方向
        if (config.contentFadingEdgeLength > 0) {
            recyclerView.isHorizontalFadingEdgeEnabled = (orientation == ORIENTATION_HORIZONTAL)
            recyclerView.isVerticalFadingEdgeEnabled = (orientation == ORIENTATION_VERTICAL)
        }

        addSpacingDecoration()
    }

    /**
     * 更新控件样式配置
     */
    fun setConfig(newConfig: RatioSelectorConfig) {
        this.config = newConfig
        ratioAdapter.updateConfig(newConfig)
        recyclerView.clipToPadding = newConfig.contentClipToPadding

        // 更新渐变边缘属性
        if (newConfig.contentFadingEdgeLength > 0) {
            recyclerView.setFadingEdgeLength(newConfig.contentFadingEdgeLength)
            recyclerView.isHorizontalFadingEdgeEnabled = (scrollOrientation == ORIENTATION_HORIZONTAL)
            recyclerView.isVerticalFadingEdgeEnabled = (scrollOrientation == ORIENTATION_VERTICAL)
            // 注意：setEdgeEffectColor方法在当前版本中不可用
        } else {
            recyclerView.isHorizontalFadingEdgeEnabled = false
            recyclerView.isVerticalFadingEdgeEnabled = false
            // 如果没有渐变边缘，则使用用户设置的 clipToPadding 值
            recyclerView.clipToPadding = newConfig.contentClipToPadding
        }

        // 重新添加 SpacingItemDecoration 来应用新的内边距和间距设置
        addSpacingDecoration()
    }

    /**
     * 设置比例选项列表
     */
    fun setRatioOptions(options: List<RatioOption>) {
        ratioOptions.clear()
        ratioOptions.addAll(options)
        ratioAdapter.submitList(ratioOptions)
    }

    /**
     * 设置比例选项选择监听器
     */
    fun setOnRatioSelectedListener(listener: (RatioOption) -> Unit) {
        this.onRatioSelectedListener = listener
    }

    /**
     * 更新选中项
     * @param ratioId 选项的 ID
     */
    fun setSelected(ratioId: Int?) = ratioAdapter.setSelected(ratioId)

    /**
     * 设置内容水平内边距
     */
    fun setContentPaddingHorizontal(padding: Int) {
        val newConfig = config.copy(contentPaddingHorizontal = padding)
        setConfig(newConfig)
    }

    /**
     * 设置内容垂直内边距
     */
    fun setContentPaddingVertical(padding: Int) {
        val newConfig = config.copy(contentPaddingVertical = padding)
        setConfig(newConfig)
    }

    /**
     * 设置内容是否裁剪到内边距
     */
    fun setContentClipToPadding(clip: Boolean) {
        val newConfig = config.copy(contentClipToPadding = clip)
        setConfig(newConfig)
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        recyclerView.isEnabled = enabled
        recyclerView.isNestedScrollingEnabled = enabled
        recyclerView.overScrollMode = if (enabled) OVER_SCROLL_ALWAYS else OVER_SCROLL_NEVER
        ratioAdapter.setEnabled(enabled)
    }

    companion object {
        const val ORIENTATION_HORIZONTAL = 0
        const val ORIENTATION_VERTICAL = 1

        @IntDef(ORIENTATION_HORIZONTAL, ORIENTATION_VERTICAL)
        @Retention(AnnotationRetention.SOURCE)
        annotation class ScrollOrientation
    }
}
