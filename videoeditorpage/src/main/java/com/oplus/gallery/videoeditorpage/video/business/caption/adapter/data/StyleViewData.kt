/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : StyleViewData.kt
 ** Description : 文字样式实体信息
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption.CaptionType
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionStyleItem

/**
 * 字幕样式item
 */
class StyleViewData(
    /**
     * 样式素材资源url
     */
    styleUrl: String,

    /**
     * 样式的id
     */
    styleId: String,

    /**
     * 中文icon素材url
     */
    zhIconUrl: String,

    /**
     * 英文icon素材url
     */
    enIconUrl: String
) : DownloadItem(styleUrl, styleId, zhIconUrl, enIconUrl) {

    /**
     * 字幕类型：标准字幕、拼装字幕等
     */
    var captionType: Int = CaptionType.TYPE_NORMAL_CAPTION

    /**
     * 样式绑定的字体
     */
    var fontViewData: FontViewData? = null

    fun toStyleItem(): CaptionStyleItem {
        return CaptionStyleItem().also {
            it.resourceId = resourceId
            it.resourceUrl = resourceUrl
            it.zhIconUrl = zhIconUrl
            it.enIconUrl = enIconUrl
            it.downloadState = downloadState
            it.localPath = localPath
            it.builtin = builtin
            it.orderNum = orderNum
            it.resourceMd5 = resourceMd5
            it.version = version
            it.fontId = fontViewData?.resourceId ?: TextUtil.EMPTY_STRING
            it.fontItem = fontViewData?.toFontItem()
        }
    }

    companion object {
        private const val TAG = "StyleViewData"
    }
}

/**
 * 字幕样式数据库实体类转样式item
 */
fun CaptionStyleItem.toStyle(): StyleViewData {
    return StyleViewData(
        this.resourceUrl,
        this.resourceId,
        this.zhIconUrl,
        this.enIconUrl,
    ).also {
        it.downloadState = downloadState
        it.localPath = localPath
        it.orderNum = orderNum
        it.resourceMd5 = resourceMd5
        it.version = version
        it.builtin = builtin
        it.fontViewData = fontItem?.toFont()
    }
}