/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: BaseRecycleAdapter
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.oplus.gallery.videoeditorpage.widget.viewholder.BaseRecycleViewHolder;
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.List;

public abstract class BaseRecycleAdapter<T> extends RecyclerView.Adapter<BaseRecycleViewHolder> {
    private static final String TAG = "BaseRecycleAdapter";
    protected static final int DEFAULT_PROGRESS = -1;
    protected List<T> mData;
    protected Context mContext;
    protected LayoutInflater mInflater;
    protected OnItemClickListener<T> mItemClickListener;
    protected int mCurrentSelection = -1;
    protected int mCurrentSelectedViewId = -1;
    private boolean mCanSelected = true;

    public BaseRecycleAdapter(Context context, List<T> data) {
        mContext = context;
        mInflater = LayoutInflater.from(context);
        mData = data;
    }

    public interface OnItemClickListener<T> {
        void onItemClick(View view, int position, T item);

        void onItemSelected(View view, int position, T item, boolean isInitSelect);

        void onItemUnSelected(int viewId);

        default void onItemUnSelected(View view, int position, T item) {
        }

    }

    public void setItemClickListener(OnItemClickListener<T> listener) {
        mItemClickListener = listener;
    }

    protected BaseRecycleViewHolder createCustomViewHolder(ViewGroup parent, int viewType) {
        return null;
    }

    @Override
    public BaseRecycleViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        BaseRecycleViewHolder holder = createCustomViewHolder(parent, viewType);
        if (holder == null) {
            holder = new BaseRecycleViewHolder(mInflater.inflate(getItemLayoutId(), parent, false));
        }

        BaseRecycleViewHolder viewHolder = holder;

        if (mItemClickListener != null) {
            viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClicked(viewHolder, v, viewHolder.getLayoutPosition());
                }
            });
        }

        return viewHolder;
    }

    protected void onItemClicked(BaseRecycleViewHolder viewHolder, View v, int position) {
        if ((position < 0) || (position >= mData.size())) {
            GLog.d(TAG, "onItemClicked position is invalid");
            return;
        }

        if (ClickUtil.isDoubleClick()) {
            GLog.w(TAG, "onItemClicked isDoubleClick pos = " + position);
            return;
        }
        int id = v.getId();

        T t = mData.get(position);
        mCanSelected = true;
        mItemClickListener.onItemClick(v, position, t);
        int lastSelection = mCurrentSelection;
        if (mCanSelected) {
            if (mCurrentSelection != position) {
                mCurrentSelection = position;
                mItemClickListener.onItemSelected(v, position, t, false);
            } else {
                if (supportUnselected()) {
                    mItemClickListener.onItemUnSelected(mCurrentSelectedViewId);
                    mItemClickListener.onItemUnSelected(v, position, t);
                    mCurrentSelection = -1;
                } else {
                    mCurrentSelection = position;
                }
            }
            mCurrentSelectedViewId = id;
            if (lastSelection != -1) {
                notifyItemChanged(lastSelection);
            }
            notifyItemChanged(position);
//            notifyDataSetChanged();
        }
    }

    public void setCanSelected(boolean can) {
        this.mCanSelected = can;
    }

    @Override
    public void onBindViewHolder(BaseRecycleViewHolder holder, int position) {
        bindData(holder, position, mData.get(position));
        holder.itemView.setSelected((position == mCurrentSelection) && isSelectable(holder));
    }

    @Override
    public int getItemCount() {
        return (mData != null) ? mData.size() : 0;
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    public void setData(List<T> data) {
        mData = data;
        notifyDataSetChanged();
    }

    public void setSelection(int position) {
        mCurrentSelection = position;
        notifyDataSetChanged();
    }

    public int getCurrentSelection() {
        return mCurrentSelection;
    }

    public List<T> getData() {
        return mData;
    }

    public T getItemByPosition(int pos) {
        if ((pos < 0) || (pos >= mData.size())) {
            return null;
        }
        return mData.get(pos);
    }

    public void release() {
    }

    /**
     * 获取列表项的布局资源ID
     *
     * @return 列表项布局资源ID
     */
    public abstract int getItemLayoutId();

    /**
     * 绑定数据到ViewHolder
     *
     * @param viewHolder ViewHolder实例
     * @param position   数据在列表中的位置
     * @param item       当前位置的数据项
     */
    public abstract void bindData(BaseRecycleViewHolder viewHolder, int position, T item);

    /**
     * 是否支持取消选择功能
     *
     * @return true表示支持取消选择，false表示不支持
     */
    public abstract boolean supportUnselected();

    /**
     * 判断指定的ViewHolder是否可选择
     *
     * @param viewHolder ViewHolder实例
     * @return true表示可选择，false表示不可选择
     */
    public abstract boolean isSelectable(BaseRecycleViewHolder viewHolder);

}
