/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PixelCopyCapturing.kt
 ** Description : 画面取帧接口：PixelCopy实现
 ** Version     : 1.0
 ** Date        : 2022/05/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><EMAIL>              2022/05/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.media.capturing

import android.graphics.Bitmap
import android.os.Handler
import android.view.PixelCopy
import android.view.Surface

/**
 * 画面帧截取的[PixelCopy]实现，可用于截取绘制于[Surface]、[Window]上的画面，
 * 并不限定于内容来自于播放器。
 */
class PixelCopyCapturing : ICapturing {

    private val frameWidth: Int
    private val frameHeight: Int
    private val callbackThread: Handler
    private val captureOperation: ICaptureOperation

    constructor(surface: Surface, frameWidth: Int, frameHeight: Int, callbackThread: Handler) {
        this.frameWidth = frameWidth
        this.frameHeight = frameHeight
        this.callbackThread = callbackThread
        this.captureOperation = SurfaceCapturing(surface)
    }

    override fun capture(frameAtTimeUs: Long, onCaptured: (Bitmap?) -> Unit) {
        Bitmap.createBitmap(frameWidth, frameHeight, Bitmap.Config.ARGB_8888).let { frame ->
            this.captureOperation.requestCapturing(frame, callbackThread, onCaptured)
        }
    }

    override fun close() = Unit

    /**
     * 内部
     */
    private interface ICaptureOperation {
        fun requestCapturing(frame: Bitmap, callbackThread: Handler, onCaptured: (Bitmap?) -> Unit)
    }

    /**
     *
     */
    private class SurfaceCapturing(
        private val surface: Surface
    ) : ICaptureOperation {
        override fun requestCapturing(
            frame: Bitmap,
            callbackThread: Handler,
            onCaptured: (Bitmap?) -> Unit
        ) {
            PixelCopy.request(
                surface,
                frame,
                { onCaptured(frame) },
                callbackThread
            )
        }
    }
}