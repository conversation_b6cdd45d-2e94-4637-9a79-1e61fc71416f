/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - BaseFileCacheService.kt
 ** Description: 文件缓存服务，支持存/取/清理
 ** Version: 1.0
 ** Date : 2021/12/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  zhen<PERSON><EMAIL>    2021/12/23        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.foundation.cache.diskcache

import android.content.Context
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 子类尽量定义为单例，否则多实例使用时[minCleanIntervalAfterUse]的限制可能出现失效的情况
 * T: 获取的缓存的类型，把文件转换为该类型返回
 * U: 保存的缓存的类型，把该类型转换为文件保存
 * 例如： BaseFileCacheService<Bitmap, Uri>，即缓存Bitmap并返回Uri
 */
abstract class BaseFileCacheService<T, U> {
    /**
     * 缓存文件数量限制
     * 默认无限制，子类可自行调整
     */
    protected open val maxFileCount = Int.MAX_VALUE

    /**
     * 缓存文件总大小限制
     * 默认无限制，子类可自行调整
     */
    protected open val maxTotalFileSize = Long.MAX_VALUE

    /**
     * 清理缓存文件时，某个缓存文件读写后到删除必须满足的最小时间间隔
     * 防止出现使用较早生成的文件的时候正好被清理的情况
     * 默认1s，子类可自行调整
     */
    protected open val minCleanIntervalAfterUse = DEFAULT_MIN_CLEAN_INTERVAL_AFTER_USE

    private val usageTimeMap = mutableMapOf<String, Long>()

    /**
     * 子类实现，获取缓存文件目录
     * 无需额外创建目录
     * @param context Context
     * @return 缓存文件目录
     */
    protected abstract fun getDir(context: Context): File

    /**
     * 子类实现，如何把缓存文件转换成T
     * @param context Context
     * @return 缓存文件转换成的T，一般无缓存文件时返回null
     */
    protected abstract fun onGetCache(context: Context, file: File): T?

    /**
     * 子类实现，如何把U转换成缓存文件和T
     * @param context Context
     * @param data U类型的原始数据
     * @return 缓存文件或U转换成的T
     */
    protected abstract fun onPutCache(context: Context, file: File, data: U): T

    /**
     * 读缓存
     * @param context Context
     * @param key 缓存的唯一标识，会作为缓存文件的文件名称
     * @return 文件缓存转换成的T，转换逻辑由[onGetCache]决定，一般无文件缓存时返回null
     */
    fun getCache(context: Context, key: String): T? {
        GLog.d(TAG, "getCache")
        usageTimeMap[key] = System.currentTimeMillis()
        return onGetCache(context, File(getDir(context), key))
    }

    /**
     * 写缓存
     * @param context Context
     * @param key 缓存的唯一标识，会作为缓存文件的文件名称
     * @return 缓存文件或U转换成的T，转换逻辑由[onPutCache]决定
     */
    fun putCache(context: Context, key: String, data: U): T {
        GLog.d(TAG, "putCache")
        usageTimeMap[key] = System.currentTimeMillis()
        val dir = getDir(context)
        if (dir.exists() && !dir.isDirectory) {
            dir.delete()
        }
        dir.mkdirs()
        return onPutCache(context, File(dir, key), data)
    }

    /**
     * 清理缓存文件，直到满足文件数量和文件总大小限制为止
     * 清理策略为优先删除修改时间最早的文件
     * 一般在进程启动后过一点和读写缓存前调用
     */
    fun cleanCache(context: Context) {
        GLog.d(TAG, "cleanCache")
        val dir = getDir(context)
        if (!dir.exists()) {
            return
        }
        synchronized(this) {
            val subFiles = dir.listFiles()
            var totalCount = subFiles.size
            var totalSize = 0L
            subFiles.forEach {
                totalSize += it.length()
            }
            val subFileList = mutableListOf<File>()
            var addToList = true
            while ((totalCount > maxFileCount) || (totalSize > maxTotalFileSize)) {
                if (addToList) {
                    addToList = false
                    subFiles.sortBy {
                        it.lastModified()
                    }
                    subFileList.addAll(subFiles.toList())
                }
                if (subFileList.isEmpty()) {
                    break
                }
                val deleteFile = subFileList.removeAt(0)
                val canDelete = usageTimeMap[deleteFile.name]?.let {
                    (System.currentTimeMillis() - it) > minCleanIntervalAfterUse
                } ?: true
                if (canDelete) {
                    val size = deleteFile.length()
                    if (deleteFile.delete()) {
                        GLog.d(TAG, "cleanCache: delete file: $size")
                        totalCount--
                        totalSize -= size
                    } else {
                        GLog.w(TAG, "cleanCache: failed to delete")
                    }
                } else {
                    GLog.d(TAG, "cleanCache: soon after using, skip this file")
                }
            }
        }
    }

    companion object {
        private const val TAG = "BaseFileCacheService"
        private const val DEFAULT_MIN_CLEAN_INTERVAL_AFTER_USE = TimeUtils.TIME_1_SEC_IN_MS
    }
}