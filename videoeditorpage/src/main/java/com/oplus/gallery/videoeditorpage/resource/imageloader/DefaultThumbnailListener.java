/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: DefaultThumbnailListener
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.widget.ImageView;

import java.lang.ref.WeakReference;

public class DefaultThumbnailListener implements ThumbnailListener {
    private WeakReference<ImageView> mImageViewWr;

    public DefaultThumbnailListener(ImageView imageView) {
        this.mImageViewWr = new WeakReference<>(imageView);
    }

    @Override
    public void updateThumbnail(ThumbnailRespond thumbnailInfo) {
        ImageView imageView = mImageViewWr.get();
        if (imageView == null) {
            return;
        }
        if (thumbnailInfo == null) {
            return;
        }
        Object tag = imageView.getTag();
        if ((tag != null) && (!tag.equals(thumbnailInfo.getItem()))) {
            return;
        }
        imageView.setImageBitmap(thumbnailInfo.getResult());
    }

    public ImageView getTarget() {
        return mImageViewWr.get();
    }
}
