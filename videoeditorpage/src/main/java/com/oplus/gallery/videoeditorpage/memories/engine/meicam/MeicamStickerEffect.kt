/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamStickerEffect.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.google.gson.annotations.SerializedName
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.videoedit.data.StickerInfo

class MeicamStickerEffect : MeicamTimelineEffect {

    companion object {
        private const val TAG = "MeicamStickerEffect"
        private const val JSON_TYPE_NAME = "sticker"
        private const val EFFECT_NAME = "Storyboard"
    }

    @SerializedName("class_type")
    private val classType = JSON_TYPE_NAME // use to mark the type in saved json file

    @SerializedName("file")
    private var filePath: String? = null

    @SerializedName("width")
    private var width = 0

    @SerializedName("height")
    private var height = 0

    @SerializedName("trans_x")
    private var transX = 0f

    @SerializedName("trans_y")
    private var transY = 0f

    @SerializedName("repeat")
    private var repeatType = StickerInfo.REPEAT_TYPE_NONE

    constructor(filePath: String?) : super(EFFECT_NAME, BaseVideoEffect.TYPE_BUILT_IN_FX) {
        this.filePath = filePath
    }

    fun setTransition(transX: Float, transY: Float) {
        this.transX = transX
        this.transY = transY
    }

    fun setSize(width: Int, height: Int) {
        this.width = width
        this.height = height
    }

    fun setRepeatType(type: Int) {
        repeatType = type
    }

    fun syncPropertyToNvs(timelineWidth: Int, timelineHeight: Int) {
        if (nvsTimelineVideoFx == null) {
            GLog.e(TAG, "syncPropertyToNvs, nvsVideoFx is null")
            return
        }
        if (filePath == null) {
            GLog.e(TAG, "syncPropertyToNvs, filePath is null")
            return
        }
        val separatorIndex = filePath!!.lastIndexOf(File.separator)
        if ((separatorIndex <= 0) || (separatorIndex >= filePath!!.length - 1)) {
            GLog.e(TAG, "syncPropertyToNvs, mFilePath illegal")
            return
        }
        val dir = filePath!!.substring(0, separatorIndex)
        val fileName = filePath!!.substring(separatorIndex + 1)
        val during: Long = outTime - inTime
        val repeatValue = getRepeatString()
        val descString = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>        " +
                "<storyboard sceneWidth=\"" + timelineWidth + "\" sceneHeight=\"" + timelineHeight + "\">           " +
                "<track source=\"" + fileName + repeatValue + " width=\"" + width + "\" height=\"" + height +
                "\" clipStart=\"0\" clipDuration=\"" + during + "\">" +
                "<effect name=\"transform\">" +
                "<param name=\"opacity\" value=\"1\"/>" +
                "<param name=\"transX\" value=\"0\"/>" +
                "<param name=\"transY\" value=\"0\"/>" +
                "</effect></track></storyboard>"
        nvsTimelineVideoFx?.let {
            it.setStringVal("Resource Dir", dir)
            it.setStringVal("Description String", descString)
            it.setBooleanVal("Is Animated Sticker", true)
            it.setFloatVal("Sticker TransX", transX.toDouble())
            it.setFloatVal("Sticker TransY", -transY.toDouble())
        }
    }

    private fun getRepeatString(): String {
        return when (repeatType) {
            StickerInfo.REPEAT_TYPE_REPEAT -> "\" cafLoopMode=\"repeat\""
            StickerInfo.REPEAT_TYPE_LAST_FRAME -> "\" cafLoopMode=\"repeatLastFrame\""
            else -> "\""
        }
    }
}