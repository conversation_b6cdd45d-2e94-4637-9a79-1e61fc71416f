/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : GradientView
 ** Description : GradientView
 ** Version     : 1.0
 ** Date        : 2025/8/1 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/8/1  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.utlis.ScreenUtils;

/**
 * 渐变视图
 */
public class GradientView extends View {

    public static final int LEFT = 1;
    public static final int RIGHT = 2;
    private static final int NO = 0;

    private int mStartColor;
    private int mEndColor;
    private int mLocation = LEFT;
    private LinearGradient mLinearGradient;
    private Paint mPaint = new Paint();

    public GradientView(Context context) {
        this(context, null);
    }

    public GradientView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GradientView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.GradientView, defStyleAttr, 0);
        try {
            mStartColor = a.getInt(R.styleable.GradientView_startColor, getResources().getColor(R.color.editor_background_start));
            mEndColor = a.getInt(R.styleable.GradientView_endColor, getResources().getColor(R.color.editor_background_end));
            mLocation = a.getInt(R.styleable.GradientView_location, NO);
        } finally {
            a.recycle();
        }

    }

    /**
     * 设置渐变位置
     * @param location 渐变位置
     */
    public void setLocation(int location) {
        mLocation = location;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        boolean isRtl = ScreenUtils.isLayoutRtl(getContext());
        if (isRtl && (mLocation == LEFT)) {
            mLinearGradient = new LinearGradient(getMeasuredWidth(), 0, 0, 0, new int[]{mStartColor, mEndColor}, null, Shader.TileMode.CLAMP);
        } else if (isRtl && (mLocation == RIGHT)) {
            mLinearGradient = new LinearGradient(0, 0, getMeasuredWidth(), 0, new int[]{mStartColor, mEndColor}, null, Shader.TileMode.CLAMP);
        } else if (mLocation == LEFT) {
            mLinearGradient = new LinearGradient(0, 0, getMeasuredWidth(), 0, new int[]{mStartColor, mEndColor}, null, Shader.TileMode.CLAMP);
        } else if (mLocation == RIGHT) {
            mLinearGradient = new LinearGradient(getMeasuredWidth(), 0, 0, 0, new int[]{mStartColor, mEndColor}, null, Shader.TileMode.CLAMP);
        }

        if (mLinearGradient != null) {
            mPaint.setShader(mLinearGradient);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawRect(0, 0, getWidth(), getHeight(), mPaint);
    }
}
