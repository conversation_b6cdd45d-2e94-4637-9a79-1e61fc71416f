/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - DashView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.ui;


import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import com.oplus.gallery.videoeditorpage.R;
public class DashView extends View {

    public static final int DEFAULT_DASH_WIDTH = 100;
    public static final int DEFAULT_LINE_WIDTH = 100;
    public static final int DEFAULT_LINE_HEIGHT = 10;
    public static final int DEFAULT_LINE_COLOR = 0x9E9E9E;

    private static final String TAG = "DashView";

    /**
     * 虚线的方向
     */
    public static final int ORIENTATION_HORIZONTAL = 0;
    public static final int ORIENTATION_VERTICAL = 1;

    /**
     * 默认为水平方向
     */
    public static final int DEFAULT_DASH_ORIENTATION = ORIENTATION_HORIZONTAL;
    /**
     * 间距宽度
     */
    private float mDashWidth;
    /**
     * 线段高度
     */
    private float mLineHeight;
    /**
     * 线段宽度
     */
    private float mLineWidth;
    /**
     * 线段颜色
     */
    private int mLineColor;
    private int mDashOrientation;

    private Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private int mWidthSize;
    private int mHeightSize;

    public DashView(Context context) {
        this(context, null);
    }

    public DashView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.DashView);
        mDashWidth = typedArray.getDimension(R.styleable.DashView_dashWidth, DEFAULT_DASH_WIDTH);
        mLineHeight = typedArray.getDimension(R.styleable.DashView_lineHeight, DEFAULT_LINE_HEIGHT);
        mLineWidth = typedArray.getDimension(R.styleable.DashView_lineWidth, DEFAULT_LINE_WIDTH);
        mLineColor = typedArray.getColor(R.styleable.DashView_lineColor, DEFAULT_LINE_COLOR);
        mDashOrientation = typedArray.getInteger(R.styleable.DashView_dashOrientation, DEFAULT_DASH_ORIENTATION);
        mPaint.setColor(mLineColor);
        mPaint.setStrokeWidth(mLineHeight);
        typedArray.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidthSize = View.MeasureSpec.getSize(widthMeasureSpec) - getPaddingLeft() - getPaddingRight();
        mHeightSize = MeasureSpec.getSize(heightMeasureSpec - getPaddingTop() - getPaddingBottom());
        if (mDashOrientation == ORIENTATION_HORIZONTAL) {
            //不管在布局文件中虚线高度设置为多少，控件的高度统一设置为线段的高度
            setMeasuredDimension(mWidthSize, (int) mLineHeight);
        } else {
            //当为竖直方向时，控件宽度设置为虚线的高度
            setMeasuredDimension((int) mLineHeight, mHeightSize);
        }

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mDashOrientation == ORIENTATION_VERTICAL) {
            drawVerticalLine(canvas);
        } else {
            drawHorizontalLine(canvas);
        }
    }

    public void drawHorizontalLine(Canvas canvas) {
        float totalWidth = 0;
        canvas.save();
        float[] pts = {0, 0, mLineWidth, 0};
        /*在画线之前需要先把画布向下平移办个线段高度的位置，目的就是为了防止线段只画出一半的高度
        //因为画线段的起点位置在线段左下角
         */
        canvas.translate(0, mLineHeight / 2);
        while (totalWidth <= mWidthSize) {
            canvas.drawLines(pts, mPaint);
            canvas.translate(mLineWidth + mDashWidth, 0);
            totalWidth += mLineWidth + mDashWidth;
        }
        canvas.restore();
    }


    public void drawVerticalLine(Canvas canvas) {
        float totalWidth = 0;
        canvas.save();
        float[] pts = {0, 0, 0, mLineWidth};
        //在画线之前需要先把画布向右平移半个线段高度的位置，目的就是为了防止线段只画出一半的高度
        //因为画线段的起点位置在线段左下角
        canvas.translate(mLineHeight / 2, 0);
        while (totalWidth <= mHeightSize) {
            canvas.drawLines(pts, mPaint);
            canvas.translate(0, mLineWidth + mDashWidth);
            totalWidth += mLineWidth + mDashWidth;
        }
        canvas.restore();
    }
}
