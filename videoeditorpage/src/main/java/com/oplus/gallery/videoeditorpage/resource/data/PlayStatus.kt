/**************************************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -   PlayStatus
 ** Description: 播放状态枚举
 ** Version: 1.0
 ** Date :   2025/5/13/14/40
 ** Author: <EMAIL> 80412737
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>                      <version>         <desc>
 ** -----------------------------------------------------------------------------------------------
 **  <EMAIL>    2025/5/13/14/40    1.0              播放状态枚举
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.data

/**
 * 枚举类 PlayStatus 表示播放器的状态。
 * 该枚举类定义了播放器可能处于的几种状态，包括正在播放、暂停、停止和准备中。
 */
enum class PlayStatus {
    /**
     * 表示播放器正在播放媒体内容。
     */
    PLAYING,

    /**
     * 表示播放器当前处于暂停状态。
     */
    PAUSED,

    /**
     * 表示播放器已停止播放媒体内容。
     */
    STOPPED,

    /**
     * 表示播放器正在准备播放媒体内容，但尚未开始播放。
     */
    PREPARE,
}
