/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryVideoCustomTrimTouchView.java
 ** Description: 时码线，左右把手交互，支持设置最大最小剪辑时间
 **
 **
 ** Version: 1.0
 ** Date:2025/04/14
 ** Author:<EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>            2025/04/14     1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ringtone;

import static com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_1_SEC_IN_MS;

import android.content.Context;
import android.util.AttributeSet;

public class GalleryVideoCustomTrimTouchView extends GalleryVideoTrimTouchView {
    private static final String TAG = "GalleryVideoCustomTrimTouchView";
    private static final int DRAG_POSITION_TOUCHED = 4;
    private static final int EMPTY_TOUCHED = 5;
    private static final long TIME_INTERVAL = TIME_1_SEC_IN_MS;
    private static final long DEFAULT_TRIM_MIN_TIME = 1000L;

    private static final float ROUND_OFFSET = 0.5f;

    /**
     * 剪辑把手的最大空间宽度
     */
    private int mTrimWindowSpaceWidth;

    public GalleryVideoCustomTrimTouchView(Context context) {
        super(context);
    }

    public GalleryVideoCustomTrimTouchView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public GalleryVideoCustomTrimTouchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 设置初始化参数
     *
     * @param trimMinTime 剪辑最短时长
     * @param trimMaxTime 剪辑最大时长
     * @param duration    视频时长
     */
    public void setInitParam(long trimMinTime, long trimMaxTime, long duration) {
        mDuration = Math.min(trimMaxTime, duration);
        long trimRealMinTime = Math.max(DEFAULT_TRIM_MIN_TIME, trimMinTime);

        if (trimRealMinTime + TIME_INTERVAL > mDuration) {
            trimRealMinTime = mDuration;
        }

        if (trimRealMinTime >= mDuration) {
            mTrimMinPercent = ONE_HUNDRED_PERCENT;
        } else {
            mTrimMinPercent = (float) trimRealMinTime / mDuration;
        }

        mTrimWindowSpaceWidth = getWidth() - 2 * mViewPadding;
        mViewWidth = mTrimWindowSpaceWidth - 2 * mTrimWindowWidth;

        mTrimMinGap = (int) Math.floor(mViewWidth * mTrimMinPercent + 2 * mTrimWindowWidth + ROUND_OFFSET);

        mCurrentPlayPercent = 0f;

        mCurrentPlayPos = 0f;
        mLeftBorderPos = 0f;
        mRightBorderPos = mTrimWindowSpaceWidth;

        mTrimWindowRealMinGap = 2 * mTrimWindowWidth;
        mCurrentPosGap = mTrimWindowWidth;
    }

    /**
     * 更新当前播放的位置
     *
     * @param postPercent 当前位置在当前播放段的百分比
     */
    @Override
    public void updateCurrentPosition(float postPercent) {
        mCurrentPlayPercent = postPercent;
        mCurrentPlayPos = mViewWidth * mCurrentPlayPercent + mCurrentPosGap;
        if (mShowPlayPosition) {
            postInvalidate();
        }
    }

    @Override
    protected int getRightBorderLimit() {
        return mTrimWindowSpaceWidth;
    }

    @Override
    protected boolean isDrawTrimViewBackground() {
        return false;
    }

    @Override
    protected boolean isDrawMinMaxGapEffect() {
        return mShowMaxMinGapEffect;
    }

    @Override
    protected boolean isMonitorGesture() {
        return false;
    }

    @Override
    protected boolean checkCurrentTouchIndex() {
        return mCurrentTouchIndex < EMPTY_TOUCHED;
    }

    @Override
    protected boolean isHandleTouchEvent() {
        return mCurrentTouchIndex != DRAG_POSITION_TOUCHED;
    }

    @Override
    protected void evalTouchPosition(float mTouchDownX) {
        if ((mTouchDownX < mLeftBorderPos - 2 * mTrimWindowWidth) || (mTouchDownX > mRightBorderPos + 2 * mTrimWindowWidth)) {
            mCurrentTouchIndex = DRAG_POSITION_TOUCHED;
            return;
        }

        mCurrentTouchIndex = PLAY_POSITION_TOUCHED;

        if ((mTouchDownX >= mLeftBorderPos - 2 * mTrimWindowWidth) && (mTouchDownX <= mLeftBorderPos + mTrimWindowWidth)) {
            mCurrentTouchIndex = LEFT_BORDER_TOUCHED;
            mShowPlayPosition = false;
        }

        if ((mTouchDownX >= mRightBorderPos - mTrimWindowWidth) && (mTouchDownX <= mRightBorderPos + 2 * mTrimWindowWidth)) {
            mCurrentTouchIndex = RIGHT_BORDER_TOUCHED;
            mShowPlayPosition = false;
        }
    }

    @Override
    protected int getBorderOffset(boolean isLeft) {
        int offset = 0;
        if (!isLeft) {
            offset = 2 * mViewPadding;
        }
        return offset;
    }

    @Override
    protected float getMaxTrimContentWidth() {
        return mViewWidth;
    }

    /**
     * 获取trimIn的绝对时间
     *
     * @param originTime 当前视口的原点时间
     */
    public long getTrimInTime(long originTime) {
        return originTime + (long) Math.floor(mDuration * getLeftBorderPosPercent() + ROUND_OFFSET);
    }

    /**
     * 获取trimOut的绝对时间
     *
     * @param originTime 当前视口的原点时间
     */
    public long getTrimOutTime(long originTime) {
        return originTime + (long) Math.floor(mDuration * getRightBorderPosPercent() + ROUND_OFFSET);
    }

    /**
     * 获取当前播放的绝对时间
     *
     * @param originTime 当前视口的原点时间
     */
    public long getCurrentPlayTime(long originTime) {
        return originTime + (long) Math.floor(mDuration * mCurrentPlayPercent + ROUND_OFFSET);
    }
}
