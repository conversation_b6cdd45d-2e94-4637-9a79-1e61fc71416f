/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/8/14
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/8/14  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.autodownload;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.foundation.networkaccess.base.task.NetTaskManager;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListener;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.LocalSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;

import java.util.List;

public class ThemeAutoDownloadHelper extends Thread {
    private static final String TAG = "ThemeAutoDownloadHelper";

    public static final int SUCCESS = 1 << 0;
    public static final int FAILED_NOT_WIFI = 1 << 1;
    public static final int FAILED_CONTEXT_NULL = 1 << 2;
    public static final int FAILED_NETWORK = 1 << 3;
    public static final int FAILED_NO_TEMPLATE = 1 << 4;
    public static final int FAILED_NOT_OPEN_USE_NETWORK = 1 << 5;
    private static ThemeAutoDownloadHelper sAutoDownThread = null;
    private static String sAutoDownloadTaskTag = null;
    private boolean mStopDownload = false;
    private boolean mNeedDownloadTheme = false;
    private int mCurrentThemeIndex = 0;
    private int mCurrentThemeId = LocalSourceManager.ID_INVALID;
    private int mCurrentMusicId = LocalSourceManager.ID_INVALID;
    private Context mContext;
    private Callback mCallback;
    private List<ThemeItem> mWaitingDownloadTheme;
    private LocalReceiver mLocalReceiver;
    private NetworkMonitor.NetworkListener mNetworkListener;

    public static void autoDownloadInWifi(Callback callback) {
        if (callback == null) {
            GLog.e(TAG, "callback is null, do nothing");
            return;
        }

        Context context = ContextGetter.context;
        if (context == null) {
            GLog.d(TAG, "autoDownloadInWifi, context is null");
            callback.onResult(false, FAILED_CONTEXT_NULL);
            return;
        }

        if (!NetworkPermissionManager.isUseOpenNetwork()) {
            GLog.d(TAG, "autoDownloadInWifi, had not open use network");
            callback.onResult(false, FAILED_NOT_OPEN_USE_NETWORK);
            return;
        }
        if (!NetworkMonitor.isWifiValidated()) {
            GLog.d(TAG, "autoDownloadInWifi, is not wifi");
            callback.onResult(false, FAILED_NOT_WIFI);
            return;
        }
        updateNetworkSourceLists(context, callback);
    }

    private ThemeAutoDownloadHelper(Context context, Callback callback) {
        mContext = context.getApplicationContext();
        mCallback = callback;
        registerDownloadReceiver();
        registerNetStateListener();
    }

    @Override
    public void run() {
        mWaitingDownloadTheme = ThemeSourceManager.getInstance().queryNeedDownloadMemoriesTheme();
        startDownloadTheme();
    }

    private static void updateNetworkSourceLists(Context context, Callback callback) {
        updateMusicLists(context, callback);
    }

    private static void updateMusicLists(Context context, Callback callback) {
        sAutoDownloadTaskTag = MusicSourceManager.getInstance().requestNetworkResource(new OnLoadingListener<MusicItem>() {
            @Override
            public void onLoadingFinish(int code, List<MusicItem> allEntityList) {
                if ((code == RtnCode.Music.SUCCESS) || (code == RtnCode.Music.AT_INTERVALS)) {
                    updateThemeLists(context, callback);
                }
            }

            @Override
            public void onIconDownloadFinish(MusicItem musicItem) {

            }

            @Override
            public void onIconDownloadError(int errCode, MusicItem musicItem) {

            }

            @Override
            public void onLoadingError(int errCode) {

            }
        }, false, true);
    }

    private static void updateThemeLists(Context context, Callback callback) {
        sAutoDownloadTaskTag = ThemeSourceManager.getInstance().requestNetworkResource(new OnLoadingListener<ThemeItem>() {
            @Override
            public void onLoadingFinish(int code, List<ThemeItem> allEntityList) {
                GLog.d(TAG, "ThemeList ready code = " + code);
                synchronized (ThemeAutoDownloadHelper.class) {
                    if (sAutoDownThread != null) {
                        sAutoDownThread.cancelAutoDownloadTask();
                    }
                    sAutoDownThread = new ThemeAutoDownloadHelper(context, callback);
                    sAutoDownThread.start();
                }
            }

            @Override
            public void onIconDownloadFinish(ThemeItem item) {
                GLog.d(TAG, "onIconDownloadFinish item = " + item);
            }

            @Override
            public void onIconDownloadError(int errCode, ThemeItem item) {
                GLog.d(TAG, "onIconDownloadError errCode = " + errCode + "item = " + item);
            }

            @Override
            public void onLoadingError(int errCode) {
                GLog.d(TAG, "onLoadingError errCode = " + errCode);
            }
        }, false, true);
    }

    private void startDownloadTheme() {
        GLog.d(TAG, "startDownloadTheme");
        downloadSingleTheme();
    }

    private void downloadSingleTheme() {
        if (mWaitingDownloadTheme == null) {
            mCallback.onResult(false, FAILED_NO_TEMPLATE);
            return;
        }

        if ((mCurrentThemeIndex > (mWaitingDownloadTheme.size() - 1)) || mStopDownload) {
            onAllThemeDownloaded();
            return;
        }

        GLog.d(TAG, "downloadSingleTheme:" + mCurrentThemeIndex);
        ThemeItem item = mWaitingDownloadTheme.get(mCurrentThemeIndex);
        ++mCurrentThemeIndex;
        if (!item.isNeedDownloadFile()) {
            downloadSingleTheme();
            return;
        }
        mCurrentThemeId = item.getThemeId();
        mCurrentMusicId = item.getSongId();
        sAutoDownloadTaskTag = ThemeSourceManager.getInstance().downloadMemoriesTheme(item,
                LocalSourceManager.DOWNLOAD_AUTO);
    }

    private void onAllThemeDownloaded() {
        GLog.d(TAG, "onAllThemeDownloaded!");
        cancel();
    }

    private void cancel() {
        unRegisterDownloadReceiver();
        unRegisterNetStateListener();
    }

    private void onDownloadBroadcastReceive(Intent intent) {
        String action = intent.getAction();
        if (TextUtils.equals(action, ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE)
                || TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
            int state = intent.getIntExtra(LocalSourceManager.DOWNLOAD_STATE, LocalSourceManager.DOWNLOAD_STATE_INVALID);
            int resourceId = intent.getIntExtra(LocalSourceManager.DOWNLOAD_RESOURCE_ID,
                    LocalSourceManager.ID_INVALID);
            if ((state == LocalSourceManager.DOWNLOAD_STATE_INVALID)
                    || ((resourceId != mCurrentThemeId) && (resourceId != mCurrentMusicId))) {
                return;
            }
            boolean isAutoDownload = true;
            if (TextUtils.equals(action, ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE)) {
                isAutoDownload = ThemeSourceManager.getInstance().isAutoDownload(resourceId);
            } else if (TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
                isAutoDownload = MusicSourceManager.getInstance().isAutoDownload(resourceId);
            }
            if (!isAutoDownload) {
                return;
            }
            int code = intent.getIntExtra(LocalSourceManager.DOWNLOAD_CODE, ErrorCode.Theme.UNZIP_ERROR);
            if (TextUtils.equals(action, ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE)) {
                switch (state) {
                    case LocalSourceManager.DOWNLOAD_STATE_DOWNLOADING:
                        break;
                    case LocalSourceManager.DOWNLOAD_STATE_FINISH:
                        if ((code == RtnCode.Theme.SUCCESS) || (code == RtnCode.Theme.ALREADY_DOWNLOADED)) {
                            downloadSingleTheme();
                        }
                        break;
                    case LocalSourceManager.DOWNLOAD_STATE_ERROR:
                        cancelAutoDownloadTask();
                        break;
                    default:
                        break;
                }
            } else if (TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
                boolean needDownloadTheme = intent.getBooleanExtra(LocalSourceManager.DOWNLOAD_THEME, false);
                switch (state) {
                    case LocalSourceManager.DOWNLOAD_STATE_DOWNLOADING:
                        if (needDownloadTheme) {
                            mNeedDownloadTheme = true;
                        }

                        break;
                    case LocalSourceManager.DOWNLOAD_STATE_FINISH:
                        if (needDownloadTheme || mNeedDownloadTheme) {
                            ThemeSourceManager.getInstance().downloadThemeByMusicId(resourceId);
                            mNeedDownloadTheme = false;
                        }
                        break;
                    case LocalSourceManager.DOWNLOAD_STATE_ERROR:
                        cancelAutoDownloadTask();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void registerDownloadReceiver() {
        if (mLocalReceiver == null) {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE);
            intentFilter.addAction(MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE);
            mLocalReceiver = new LocalReceiver();
            LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalReceiver, intentFilter);

        }
    }

    private void registerNetStateListener() {
        if (mNetworkListener == null) {
            mNetworkListener = (connectType, isValidated) -> {
                if (!mStopDownload && ((connectType != NetworkMonitor.ConnectType.WIFI) || !isValidated)) {
                    cancelAutoDownloadTask();
                }
            };
            NetworkMonitor.addListener(mNetworkListener);
        }
    }

    private class LocalReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            onDownloadBroadcastReceive(intent);
        }
    }

    private void unRegisterDownloadReceiver() {
        if (mLocalReceiver != null) {
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLocalReceiver);
            mLocalReceiver = null;
        }
    }

    private void unRegisterNetStateListener() {
        if (mNetworkListener != null) {
            NetworkMonitor.removeListener(mNetworkListener);
            mNetworkListener = null;
        }
    }

    public static void release() {
        synchronized (ThemeAutoDownloadHelper.class) {
            if (sAutoDownThread != null) {
                GLog.d(TAG, "release");
                sAutoDownThread.cancelAutoDownloadTask();
                sAutoDownThread = null;
            }
        }
    }

    public void cancelAutoDownloadTask() {
        GLog.d(TAG, "cancelAutoDownloadTask");
        mStopDownload = true;
        resetDownloadProgress();
        NetTaskManager.getInstance().cancel(sAutoDownloadTaskTag, true);
        onAllThemeDownloaded();
    }

    private void resetDownloadProgress() {
        MusicSourceManager.getInstance().resetDownloadProgress(mCurrentMusicId);
        ThemeSourceManager.getInstance().resetDownloadProgress(mCurrentThemeId);
    }

    public interface Callback {
        void onResult(boolean success, int msg);
    }
}
