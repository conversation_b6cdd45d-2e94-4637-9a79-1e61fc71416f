/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ViewPagerAdapter.kt
 ** Description : tab页面切换的viePager适配器
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter

import android.content.Context
import android.graphics.PointF
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.MeicamCaption
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.video.business.caption.CaptionOperationListener
import com.oplus.gallery.videoeditorpage.video.business.caption.resource.ResourceManager
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.FontViewData
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewData
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecoration
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.TextDecorationItem
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.BackgroundSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.BaseSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.FontSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.ShadowSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.StrokeSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.StyleSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.TextEffectsSelector
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.TextEffectsType
import com.oplus.gallery.videoeditorpage.video.business.caption.selector.TextSelector
import java.lang.Math.toRadians
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * tab页面切换的viePager适配器，用于适配弹窗的5个tab页面
 */
internal class ViewPagerAdapter(
    context: Context,
    private val isInMultiWindow: Boolean,
    private val bottomNaviBarHeight: Int,
    private val pageTypes: MutableList<PageType>,
    captionScope: LifecycleCoroutineScope,
    private val operationListener: CaptionOperationListener
) : RecyclerView.Adapter<ViewPagerAdapter.PageViewHolder>(), CaptionEffectsChangedListener {

    private val mContext: Context = context

    /**
     * 当前编辑的字幕，当使用不同的样式导致字幕类型发生变化时该值也需要更新
     */
    var caption: MeicamCaption? = null

    /**
     * 字幕效果选择器map
     * PageType：选择器页面类型
     * BaseSelector：选择器页面实例
     */
    private val  textEffectsSelector: MutableMap<PageType, BaseSelector> = mutableMapOf()

    /**
     * 字幕样式字体资源包管理器
     */
    private val resourceManager: ResourceManager = ResourceManager(context, captionScope) { pageType, position ->
        // 更新样式字体下载进度
        when (pageType) {
            PageType.PAGE_TYPE_STYLE -> (textEffectsSelector[pageType] as? StyleSelector)?.notifyStyleItemChanged(position)
            PageType.PAGE_TYPE_FONT -> (textEffectsSelector[pageType] as? FontSelector)?.notifyFontItemChanged(position)
            else -> Unit
        }
    }

    inner class PageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        init {
            /**
             * 解决viewpage2配置报错
             * recyclerView.Adapter报java.lang.IllegalStateException: Pages must fill the whole ViewPager2 (use match_parent)
             */
            itemView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder  {
        val styleContainer = LayoutInflater.from(parent.context)
            .inflate(R.layout.videoeditor_caption_styles_fonts_layout, parent, false) as ViewGroup
        val textEffectsContainer = LayoutInflater.from(parent.context)
            .inflate(R.layout.videoeditor_caption_text_effects_layout, parent, false) as ViewGroup
        val textEffectsLinearLayout = textEffectsContainer.findViewById<LinearLayout>(R.id.text_effects_linearlayout_container)
        if ((viewType != PageType.PAGE_TYPE_STYLE.value) && (viewType != PageType.PAGE_TYPE_FONT.value)) {
            val textEffectsLayoutParams = textEffectsLinearLayout.layoutParams
            if (isInMultiWindow.not() && isMiddleAndLargeWindow(parent.context)) {
                // 中大屏：设置固定宽度
                val widthInPx = mContext.resources.getDimensionPixelSize(R.dimen.videoeditor_caption_input_edit_middle_and_large_effects_width)
                textEffectsLayoutParams.width = widthInPx
            } else {
                // 普通屏幕：match_parent
                textEffectsLayoutParams.width = LinearLayout.LayoutParams.MATCH_PARENT
            }
            textEffectsLinearLayout.layoutParams = textEffectsLayoutParams
        }
        // 设置 layout_marginBottom
        val layoutParams = textEffectsContainer.layoutParams as? ViewGroup.MarginLayoutParams
        layoutParams?.let {
            it.setMargins(it.leftMargin, it.topMargin, it.rightMargin, bottomNaviBarHeight)
            textEffectsContainer.layoutParams = it
        }
        val pageVH = when (viewType) {
            PageType.PAGE_TYPE_STYLE.value -> buildStylesViewHolder(styleContainer, viewType)
            PageType.PAGE_TYPE_FONT.value -> buildFontsViewHolder(styleContainer, viewType)
            PageType.PAGE_TYPE_TEXT.value -> buildTextPageViewHolder(textEffectsContainer, viewType)
            PageType.PAGE_TYPE_STROKE.value -> buildStrokePageViewHolder(textEffectsContainer, viewType)
            PageType.PAGE_TYPE_SHADOW.value -> buildShadowPageViewHolder(textEffectsContainer, viewType)
            PageType.PAGE_TYPE_BACKGROUND.value -> buildBackgroundPageViewHolder(textEffectsContainer, viewType)
            // 给个默认的VH进行兜底
            else -> buildEmptyPageViewHolder(parent)
        }
        return pageVH
    }

    /**
     * 初始化样式VH
     */
    private fun buildStylesViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        textEffectsSelector[pageTypes[viewType]]?.destroy()
        textEffectsSelector[pageTypes[viewType]] = StyleSelector(isInMultiWindow, bottomNaviBarHeight, resourceManager, pageTypes[viewType]).apply {
            container = parent
            captionEffectsChangedListener = this@ViewPagerAdapter
        }
        return PageViewHolder(parent)
    }

    /**
     * 初始化字体VH
     */
    private fun buildFontsViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        textEffectsSelector[pageTypes[viewType]]?.destroy()
        textEffectsSelector[pageTypes[viewType]] = FontSelector(isInMultiWindow, bottomNaviBarHeight, resourceManager, pageTypes[viewType]).apply {
            container = parent
            captionEffectsChangedListener = this@ViewPagerAdapter
        }
        return PageViewHolder(parent)
    }

    /**
     * 获取文本页面VH
     */
    private fun buildTextPageViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        textEffectsSelector[pageTypes[viewType]]?.destroy()
        textEffectsSelector[pageTypes[viewType]] = TextSelector(pageTypes[viewType]).apply {
            container = parent
            captionEffectsChangedListener = this@ViewPagerAdapter
        }
        return PageViewHolder(parent)
    }

    /**
     * 获取描边页面VH
     */
    private fun buildStrokePageViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        textEffectsSelector[pageTypes[viewType]]?.destroy()
        textEffectsSelector[pageTypes[viewType]] = StrokeSelector(pageTypes[viewType]).apply {
            container = parent
            captionEffectsChangedListener = this@ViewPagerAdapter
        }
        return PageViewHolder(parent)
    }

    /**
     * 获取阴影页面VH
     */
    private fun buildShadowPageViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        textEffectsSelector[pageTypes[viewType]]?.destroy()
        textEffectsSelector[pageTypes[viewType]] = ShadowSelector(pageTypes[viewType]).apply {
            container = parent
            captionEffectsChangedListener = this@ViewPagerAdapter
        }
        return PageViewHolder(parent)
    }

    /**
     * 获取背景页面VH
     */
    private fun buildBackgroundPageViewHolder(parent: ViewGroup, viewType: Int): PageViewHolder {
        textEffectsSelector[pageTypes[viewType]]?.destroy()
        textEffectsSelector[pageTypes[viewType]] = BackgroundSelector(pageTypes[viewType]).apply {
            container = parent
            captionEffectsChangedListener = this@ViewPagerAdapter
        }
        return PageViewHolder(parent)
    }

    /**
     * 默认空VH
     */
    private fun buildEmptyPageViewHolder(parent: ViewGroup): PageViewHolder {
        return PageViewHolder(View(parent.context))
    }

    override fun onBindViewHolder(holder: PageViewHolder, position: Int) {
        // 将选中编辑的字幕的编辑初始化参数更新到UI控件中显示
        showDefaultCaptionValues()
    }

    override fun getItemCount(): Int = pageTypes.size

    override fun getItemViewType(position: Int): Int = pageTypes[position].value

    /**
     * 更新字幕初始化值到UI控件
     */
    private fun showDefaultCaptionValues() {
        val curCaption = caption ?: return
        textEffectsSelector.forEach { (pageType, selector) ->
            when (pageType) {
                PageType.PAGE_TYPE_STYLE -> {
                    // 样式页
                    val sel = selector as StyleSelector
                    sel.resourceId = curCaption.styleId ?: TextUtil.EMPTY_STRING
                }
                PageType.PAGE_TYPE_FONT -> {
                    // 字体页
                    val sel = selector as FontSelector
                    sel.resourceId = curCaption.fontId ?: TextUtil.EMPTY_STRING
                }
                PageType.PAGE_TYPE_TEXT -> showDefaultTextValues(selector, curCaption)
                PageType.PAGE_TYPE_STROKE -> showDefaultStrokeValues(selector, curCaption)
                PageType.PAGE_TYPE_SHADOW -> showDefaultShadowValues(selector, curCaption)
                PageType.PAGE_TYPE_BACKGROUND -> showDefaultBackgroundValues(selector, curCaption)
            }
        }
    }

    /**
     * 显示文本默认值
     */
    private fun showDefaultTextValues(selector: BaseSelector, curCaption: BaseCaption) {
        val sel = (selector as TextSelector)
        sel.showColorValue(curCaption.textColor)
        sel.showProgressValue(TextEffectsType.TRANSPARENT, curCaption.textColor.extractAlphaPercentage())
        sel.showTextDecorationValue(
            curCaption.italic,
            curCaption.bold,
            curCaption.underline,
            curCaption.strikeOut
        )
    }

    /**
     * 显示默认描边值
     */
    private fun showDefaultStrokeValues(selector: BaseSelector, curCaption: BaseCaption) {
        val sel = selector as StrokeSelector
        sel.showColorValue(curCaption.outlineColor)
        mapOf(
            TextEffectsType.TRANSPARENT to curCaption.outlineColor.extractAlphaPercentage(),
            TextEffectsType.THICKNESS to curCaption.outlineWidth.toInt()
        ).forEach { (type, value) ->
            // 如果描边开关是关闭的则重置不显示描边值,否则显示描边值
            val result = if (curCaption.drawOutline) value else 0
            sel.showProgressValue(type, result)
        }
    }

    /**
     * 显示默认阴影值
     */
    private fun showDefaultShadowValues(selector: BaseSelector, curCaption: BaseCaption) {
        val sel = selector as ShadowSelector
        sel.showColorValue(curCaption.shadowColor)
        curCaption.shadowOffset ?: return
        // 阴影角度和距离的回显逻辑
        val (distance, angle) = calculateShadowDistanceAndAngle(curCaption.shadowOffset.x, curCaption.shadowOffset.y)
        mapOf(
            TextEffectsType.TRANSPARENT to curCaption.shadowColor.extractAlphaPercentage(),
            TextEffectsType.BLUR to curCaption.shadowFeather.toInt(),
            TextEffectsType.ANGLE to angle.toInt(),
            TextEffectsType.DISTANCE to distance.toInt()
        ).forEach { (type, value) ->
            // 如果阴影开关是关闭的则重置不显示阴影距离和角度，否则显示默认值
            val result = if (curCaption.drawShadow) value else 0
            sel.showProgressValue(type, result)
        }
    }

    /**
     * 显示默认背景值
     */
    private fun showDefaultBackgroundValues(selector: BaseSelector, curCaption: BaseCaption) {
        val sel = selector as BackgroundSelector
        sel.showColorValue(curCaption.backgroundColor)
        mapOf(
            TextEffectsType.TRANSPARENT to curCaption.backgroundColor.extractAlphaPercentage(),
            TextEffectsType.CORNERS to curCaption.backgroundRadius.toInt(),
            TextEffectsType.MARGIN to (curCaption.boundaryPaddingRatio * PROGRESS_MAX_VALUE).toInt()
        ).forEach { (type, value) ->
            sel.showProgressValue(type, value)
        }
    }


    /**
     * 字幕透明度、边距、模糊度等设置回调
     */
    override fun textEffectsChanged(
        pageType: PageType,
        effectType: TextEffectsType,
        value: Float,
        isIncreasing: Boolean
    ) {
        val curCaption = caption ?: return
        if ((pageType == PageType.PAGE_TYPE_STYLE) || (pageType == PageType.PAGE_TYPE_FONT)) return
        when (pageType) {
            // 文本设置
            PageType.PAGE_TYPE_TEXT -> {
                // 设置文字颜色透明度
                if (effectType == TextEffectsType.TRANSPARENT) curCaption.textColor = curCaption.textColor.applyAlpha(value)
            }
            // 描边设置
            PageType.PAGE_TYPE_STROKE -> {
                curCaption.drawOutline = true
                when (effectType) {
                    // 设置描边颜色透明度
                    TextEffectsType.TRANSPARENT -> curCaption.outlineColor = curCaption.outlineColor.applyAlpha(value)
                    // 设置描边边宽
                    TextEffectsType.THICKNESS -> curCaption.outlineWidth = value
                    else -> Unit
                }
            }
            // 阴影设置
            PageType.PAGE_TYPE_SHADOW -> {
                curCaption.drawShadow = true
                val (distance, angle) = calculateShadowDistanceAndAngle(curCaption.shadowOffset.x, curCaption.shadowOffset.y)
                when (effectType) {
                    // 设置阴影颜色透明度
                    TextEffectsType.TRANSPARENT -> curCaption.shadowColor = curCaption.shadowColor.applyAlpha(value)
                    // 设置阴影模糊度
                    TextEffectsType.BLUR -> curCaption.shadowFeather = value
                    // 设置阴影偏移距离
                    TextEffectsType.DISTANCE -> curCaption.shadowOffset = calculateShadowOffset(value, angle)
                    //设置阴影偏移角度
                    TextEffectsType.ANGLE -> curCaption.shadowOffset = calculateShadowOffset(distance, value)
                    else -> Unit
                }
            }
            // 背景设置
            PageType.PAGE_TYPE_BACKGROUND -> {
                when (effectType) {
                    // 设置背景透明度
                    TextEffectsType.TRANSPARENT -> curCaption.backgroundColor = curCaption.backgroundColor.applyAlpha(value)
                    // 设置背景圆角
                    TextEffectsType.CORNERS -> curCaption.backgroundRadius = value
                    // 设置背景边距
                    TextEffectsType.MARGIN -> curCaption.boundaryPaddingRatio = value / PROGRESS_MAX_VALUE
                    else -> Unit
                }
            }
            else -> Unit
        }
        // 设置完字幕属性后触发更新预览
        operationListener.onCaptionPropertyUpdated()
    }


    /**
     * 颜色设置回调
     */
    override fun colorChanged(pageType: PageType, colorHexString: String) {
        when (pageType) {
            PageType.PAGE_TYPE_TEXT -> caption?.let { it.textColor = colorHexString }
            PageType.PAGE_TYPE_STROKE -> {
                caption?.let {
                    it.outlineColor = colorHexString
                    it.drawOutline = true
                }
            }
            PageType.PAGE_TYPE_SHADOW -> {
                caption?.let {
                    it.drawShadow = true
                    it.shadowColor = colorHexString
                }
            }
            PageType.PAGE_TYPE_BACKGROUND -> caption?.let { it.backgroundColor = colorHexString }
            else -> Unit
        }
        // 重新选择颜色后需要将对应的透明度设置100
        val sel = textEffectsSelector[pageType] as? TextEffectsSelector
        sel?.showProgressValue(TextEffectsType.TRANSPARENT, MAX_ALPHA_PERCENT.toInt())
        // 设置完字幕属性后触发更新预览
        operationListener.onCaptionPropertyUpdated()
    }

    /**
     * 字体设置回调
     */
    override fun fontChanged(pageType: PageType, captionFont: FontViewData, position: Int) {
        if (pageType == PageType.PAGE_TYPE_FONT) {
            // 设置字体
            resourceManager.applyCaptionFont(captionFont, position) { _, item ->
                caption?.let {
                    it.setFontByFilePath(item.localPath)
                    it.fontId = item.resourceId
                }
                // 设置完字幕属性后触发更新预览
                operationListener.onCaptionPropertyUpdated()
            }
        }
    }

    /**
     * 样式设置回调
     */
    override fun styleChanged(pageType: PageType, captionStyle: StyleViewData, position: Int) {
        if (pageType == PageType.PAGE_TYPE_STYLE) {
            // 设置字幕样式
            resourceManager.applyCaptionStyle(captionStyle, position) { assetPackageId, item ->
                caption?.let {
                    // 设置样式
                    if (it.captionType == captionStyle.captionType) {
                        // 当前字幕与样式所需的字幕类型匹配，则直接更改字幕类型生效
                        it.captionStyleId = assetPackageId
                        it.styleId = item.resourceId
                    } else {
                        // 当前字幕与样式所需的字幕类型不匹配，则需要进行字幕替换
                        operationListener.onCaptionTypeChanged(captionStyle.captionType, assetPackageId)?.also { newCaption ->
                            // 替换成新的字幕后需要更新字幕当前选中的样式id
                            newCaption.styleId = item.resourceId
                        }
                    }
                    // 设置字体
                    val fontViewData = (item as? StyleViewData)?.fontViewData
                    fontViewData?.let { fontData ->
                        it.setFontByFilePath(fontData.localPath)
                        it.fontId = fontData.resourceId
                    } ?: run {
                        it.setFontByFilePath(TextUtil.EMPTY_STRING)
                        it.fontId = DEFAULT_BUILTIN_FONT_ID
                    }
                    // 设置字体页对应的样式绑定字体为选中状态
                    (textEffectsSelector[PageType.PAGE_TYPE_FONT] as? FontSelector)?.setSelectedForFont(fontViewData)
                    // 设置完字幕属性后触发更新预览
                    operationListener.onCaptionPropertyUpdated()
                } ?: run {
                    // 当前没有创建默认字幕时，则通知创建默认字幕
                    (item as? StyleViewData)?.let {
                        caption = operationListener.onDefaultCaptionCreate(it, assetPackageId)
                    }
                }
                // 设置样式完成后需要重新刷新回显属性设置值
                showDefaultCaptionValues()
                // 切换字幕样式后播放预览
                operationListener.onCaptionStartPlay()
            }
        }
    }

    /**
     * 加粗、斜体、下划线、中划线设置回调
     */
    override fun textDecorationChanged(pageType: PageType, item: TextDecorationItem) {
        if (pageType == PageType.PAGE_TYPE_TEXT) {
            when (item.textDecoration) {
                TextDecoration.BOLD -> caption?.bold = item.selected
                TextDecoration.ITALIC -> caption?.italic = item.selected
                TextDecoration.UNDERLINE -> caption?.underline = item.selected
                TextDecoration.LINE_THROUGH -> caption?.strikeOut = item.selected
            }
        }
        // 设置完字幕属性后触发更新预览
        operationListener.onCaptionPropertyUpdated()
    }

    /**
     * 回收资源
     */
    fun destroy() {
        pageTypes.clear()
        textEffectsSelector.values.forEach { selector ->
            selector.destroy()
        }
        textEffectsSelector.clear()
        resourceManager.destroy()
    }

    companion object {
        /**
         * 样式页标识
         */

        const val PAGE_TYPE_STYLE_INDEX = 0

        /**
         * 字体页标识
         */
       const val PAGE_TYPE_FONT_INDEX = 1

        /**
         * 文本页标识
         */
        const val PAGE_TYPE_TEXT_INDEX = 2

        /**
         * 描边页标识
         */
        const val PAGE_TYPE_STROKE_INDEX = 3

        /**
         * 阴影页标识
         */
       const val PAGE_TYPE_SHADOW_INDEX = 4

        /**
         * 背景页标识
         */
        const val PAGE_TYPE_BACKGROUND_INDEX = 5

        /**
         * 默认内置字体的id
         */
        const val DEFAULT_BUILTIN_FONT_ID = "videoeditor_caption_font_oppo_sans_builtin"
    }
}

/**
 * 字幕效果编辑变化监听器
 */
interface CaptionEffectsChangedListener {

    /**
     * 字幕透明度、边距、模糊度等设置回调
     */
    fun textEffectsChanged(pageType: PageType, effectType: TextEffectsType, value: Float, isIncreasing: Boolean)

    /**
     * 颜色设置回调
     */
    fun colorChanged(pageType: PageType, colorHexString: String)

    /**
     * 字体设置回调
     */
    fun fontChanged(pageType: PageType, captionFont: FontViewData, position: Int)

    /**
     * 样式设置回调
     */
    fun styleChanged(pageType: PageType, captionStyle: StyleViewData, position: Int)

    /**
     * 加粗、斜体、下划线、中划线设置回调
     */
    fun textDecorationChanged(pageType: PageType, item: TextDecorationItem)
}

/**
 * 字幕输入弹窗页面类型
 *
 * @param value 页面类型标识值
 * @param resId 页面名称显示的资源id
 */
enum class PageType(val value: Int, val resId: Int) {
    /**
     * 样式页类型
     */
    PAGE_TYPE_STYLE(ViewPagerAdapter.PAGE_TYPE_STYLE_INDEX, R.string.videoeditor_caption_text_style),

    /**
     * 字体页类型
     */
    PAGE_TYPE_FONT(ViewPagerAdapter.PAGE_TYPE_FONT_INDEX, R.string.videoeditor_caption_text_font),

    /**
     * 文本页类型
     */
    PAGE_TYPE_TEXT(ViewPagerAdapter.PAGE_TYPE_TEXT_INDEX, R.string.videoeditor_caption_text_text),

    /**
     * 描边页类型
     */
    PAGE_TYPE_STROKE(ViewPagerAdapter.PAGE_TYPE_STROKE_INDEX, R.string.videoeditor_caption_text_stroke),

    /**
     * 阴影页类型
     */
    PAGE_TYPE_SHADOW(ViewPagerAdapter.PAGE_TYPE_SHADOW_INDEX, R.string.videoeditor_caption_text_shadow),

    /**
     * 背景页类型
     */
    PAGE_TYPE_BACKGROUND(ViewPagerAdapter.PAGE_TYPE_BACKGROUND_INDEX, R.string.videoeditor_caption_text_background);
}

/**
 * 设置颜色透明度
 *
 * @param alpha 颜色的 alpha值 范围为0F - 100F
 * @return 返回#AARRGGBB
 */
fun String.applyAlpha(alpha: Float): String {
    require(alpha in 0f..MAX_ALPHA_PERCENT) { "Alpha 必须设置在 0 - 100" }
    val cleanHex = when {
        this.startsWith(HEX_PREFIX) -> this.substring(HEX_PREFIX_LENGTH)
        else -> this
    }
    require((cleanHex.length == HEX_COLOR_LENGTH_WITHOUT_ALPHA) || (cleanHex.length == HEX_COLOR_LENGTH_WITH_ALPHA)) {
        "hexColorString的长度为 6 或 8 个字符串 (RRGGBB or AARRGGBB)"
    }
    val alphaHex = ((alpha / MAX_ALPHA_PERCENT) * MAX_ALPHA_VALUE).toInt().coerceIn(0, MAX_ALPHA_VALUE)
        .toString(HEX_RADIX)
        .padStart(CHANNEL_LENGTH, '0')
        .takeLast(CHANNEL_LENGTH)
    val rgb = if (cleanHex.length == HEX_COLOR_LENGTH_WITH_ALPHA) cleanHex.substring(CHANNEL_LENGTH) else cleanHex
    return "$HEX_PREFIX$alphaHex$rgb".uppercase()
}

/**
 * 从 #AARRGGBB 格式的颜色值中提取透明度百分比
 * @param colorStr 颜色字符串，格式为 #AARRGGBB 或 #RRGGBB（默认为不透明）
 * @return 透明度百分比 (0-100)
 */
fun String.extractAlphaPercentage(): Int {
    // 确保字符串以#开头
    if (!this.startsWith(HEX_PREFIX)) {
        // 默认不透明
        return MAX_ALPHA_PERCENT.toInt()
    }
    // 处理不同长度的颜色字符串
    return when (this.length) {
        // #RRGGBB 格式 - 默认不透明(100%)
        RGB_LENGTH -> MAX_ALPHA_PERCENT.toInt()

        // #AARRGGBB 格式 - 提取AA部分
        ARGB_LENGTH -> {
            // 提取AA部分
            val alphaHex = this.substring(REPLACE_DEFAULT_POSITION, REPLACE_ALPHA_POSITION)
            // 转换为0-255
            val alpha = alphaHex.toInt(HEX_RADIX)
            // 转换为百分比
            (alpha.toFloat() / MAX_ALPHA_VALUE * MAX_ALPHA_PERCENT).roundToInt()
        }
        // 其他情况视为不透明
        else -> MAX_ALPHA_PERCENT.toInt()
    }
}

/**
 * 计算字体阴影的 X/Y 偏移坐标
 * @param distance 阴影距离（单位：像素）
 * @param angleDegrees 角度（范围：-180° ~ 180°，0° 向右，90° 向下）
 * @return `Pair<Float, Float>` 表示 (xOffset, yOffset)
 */
fun calculateShadowOffset(distance: Float, angleDegrees: Float): PointF {
    // 转换为弧度
    val angleRadians = toRadians(angleDegrees.toDouble())
    // 计算 X 和 Y 偏移
    val xOffset = (distance * cos(angleRadians)).toFloat()
    val yOffset = (distance * sin(angleRadians)).toFloat()
    return PointF(xOffset, yOffset)
}

/**
 * 通过阴影的 X/Y 偏移坐标计算距离和角度
 * @param xOffset X 偏移（像素）
 * @param yOffset Y 偏移（像素）
 * @return Pair<Float, Float> 表示 (distance, angleDegrees)，角度范围 [-180°, 180°]
 */
fun calculateShadowDistanceAndAngle(xOffset: Float, yOffset: Float): Pair<Float, Float> {
    // 计算距离
    val distance = sqrt(xOffset * xOffset + yOffset * yOffset)
    // 计算角度（弧度转角度，范围自动为 [-180°, 180°]）
    val angleDegrees = Math.toDegrees(atan2(yOffset.toDouble(), xOffset.toDouble())).toFloat()

    // 四舍五入取整
    val roundedDistance = Math.round(distance).toFloat()
    val roundedAngle = Math.round(angleDegrees).toFloat()
    return Pair(roundedDistance, roundedAngle)
}

/**
 * 颜色相关常量
 */

/**
 * 不带alpha的颜色字符串长度
 */
private const val HEX_COLOR_LENGTH_WITHOUT_ALPHA = 6

/**
 * 带alpha的颜色字符串长度
 */
private const val HEX_COLOR_LENGTH_WITH_ALPHA = 8

/**
 * alpha最大值比例值
 */
private const val MAX_ALPHA_PERCENT = 100f

/**
 * alpha 通道最大值
 */
private const val MAX_ALPHA_VALUE = 255

/**
 * 16进制数
 */
private const val HEX_RADIX = 16

/**
 * ARGB单通道的位数位数
 */
private  const val CHANNEL_LENGTH = 2

/**
 * 颜色值前缀
 */
private const val HEX_PREFIX = "#"

/**
 * 颜色值前缀长度
 */
private const val HEX_PREFIX_LENGTH = 1

/**
 * 进度条最大值
 */
private const val PROGRESS_MAX_VALUE = 100f

/**
 * 替换有alpha通道字符串结束位置
 */
private const val REPLACE_ALPHA_POSITION = 3

/**
 * 替换无alpha通道字符串结束位置
 */
private const val REPLACE_DEFAULT_POSITION = 1

/**
 * RGB颜色值长度
 */
private const val RGB_LENGTH = 7

/**
 * ARGB颜色长度
 */
private const val ARGB_LENGTH = 9
