/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorTransformVM
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/21
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/21      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.transform

import android.graphics.RectF
import android.opengl.Matrix
import android.util.Size
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.foundation.opengl.transform.AxisAngleTransform
import com.oplus.gallery.foundation.opengl.transform.IdentityMatrix
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.scale
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ISeekingListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.params.TransformParams
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video.VideoClipPlaybackMode
import com.oplus.gallery.videoeditorpage.utlis.MATRIX4x4_SIZE
import com.oplus.gallery.videoeditorpage.utlis.ModelMatrixConvertor
import com.oplus.gallery.videoeditorpage.utlis.NDC_FULL_RECT
import com.oplus.gallery.videoeditorpage.utlis.UNIT_MATRIX
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils
import com.oplus.gallery.videoeditorpage.utlis.approxEquals
import com.oplus.gallery.videoeditorpage.utlis.aspectRatio
import com.oplus.gallery.videoeditorpage.utlis.calculateCenterInsideRect
import com.oplus.gallery.videoeditorpage.utlis.calculateNDCScaleFactor
import com.oplus.gallery.videoeditorpage.utlis.ceilToMultiple
import com.oplus.gallery.videoeditorpage.utlis.center
import com.oplus.gallery.videoeditorpage.utlis.coerceAtLeast
import com.oplus.gallery.videoeditorpage.utlis.computeViewToTimelineMatrix
import com.oplus.gallery.videoeditorpage.utlis.formatMatrix
import com.oplus.gallery.videoeditorpage.utlis.fromNDCRelativeTo
import com.oplus.gallery.videoeditorpage.utlis.isMatrixApproximatelyEqual
import com.oplus.gallery.videoeditorpage.utlis.toNDCRelativeTo
import com.oplus.gallery.videoeditorpage.video.business.adjust.FxNameKey
import com.oplus.gallery.videoeditorpage.video.business.canvas.CANVAS_MIN_SIZE
import com.oplus.gallery.videoeditorpage.video.business.canvas.VideoResolution
import com.oplus.gallery.videoeditorpage.video.business.manager.EnginePlayingTimeManager
import com.oplus.gallery.videoeditorpage.video.business.output.OperationSaveHelper
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.preview.PreviewAnimController
import com.oplus.gallery.videoeditorpage.video.config.RATIO_ID_FILM
import com.oplus.gallery.videoeditorpage.video.config.RATIO_ID_FREE
import com.oplus.gallery.videoeditorpage.video.config.RATIO_ID_ORIGINAL
import com.oplus.gallery.videoeditorpage.video.config.createRatioOptions
import com.oplus.gallery.videoeditorpage.widget.ratioselector.RatioOption
import kotlin.math.max

/**
 * 视频裁剪、旋转、镜像相关的ViewModel
 */
class EditorTransformVM(
    private val engine: EditorEngine,
    private val videoClip: IVideoClip,
    private val previewAnimController: PreviewAnimController,
    private val operationSaveHelper: OperationSaveHelper,
) : ViewModel() {

    /**
     * 当前状态
     */
    private val _state: MutableLiveData<EditorTransformVMState> =
        MutableLiveData(EditorTransformVMState.STARTING)
    val state: LiveData<EditorTransformVMState> get() = _state

    val isRunning: Boolean
        get() = state.value == EditorTransformVMState.RUNNING

    /**
     * 旋转的角度
     */
    private val _rotationDegree: MutableLiveData<Double> = MutableLiveData(extractUIData(KEY_ROTATION_DEGREE, 0.0))
    val rotationDegree: LiveData<Double> get() = _rotationDegree

    /**
     * 旋转朝向
     */
    private val _rotationOrientation: MutableLiveData<RotationOrientation> = MutableLiveData(
        RotationOrientation.valueOf(extractUIData(KEY_ROTATION_ORIENTATION, RotationOrientation.TOP.name))
    )
    val rotationOrientation: LiveData<RotationOrientation> get() = _rotationOrientation

    /**
     * 是否进行了水平镜像
     */
    private val _mirrorHorizontal: MutableLiveData<Boolean> = MutableLiveData(
        extractUIData(KEY_MIRROR_HORIZONTAL, false)
    )
    val mirrorHorizontal: LiveData<Boolean> get() = _mirrorHorizontal

    private val transformTimelineSize = VideoResolution.P1080
        .calcSizeByMaxPixels(Size(videoClip.width, videoClip.height).aspectRatio())
        .coerceAtLeast(CANVAS_MIN_SIZE)
        .ceilToMultiple(VideoUtils.WIDHT_MULTIPLE, VideoUtils.HEIGHT_MULTIPLE)

    private val clipAspectRatio: Float = transformTimelineSize.aspectRatio()

    /**
     * 裁剪比例选项列表
     */
    val ratioOptions: List<RatioOption> = createRatioOptions(clipAspectRatio)

    /**
     * 裁剪比例选项
     */
    private val _ratioOption: MutableLiveData<RatioOption?> = MutableLiveData(
        ratioOptions.firstOrNull { it.ratioId == extractUIData(KEY_ASPECT_RATIO_ID, RATIO_ID_FREE) }
    )
    val ratioOption: LiveData<RatioOption?> get() = _ratioOption

    /**
     * 由于宽高比与当前选中的宽高比选项不是一一对应，原始宽高比选项对应两个宽高比，所以单独一个LiveData来表示
     */
    private val _ratioValue: MediatorLiveData<Float> = MediatorLiveData<Float>().apply {
        fun updateRatioValue() {
            val ratioOptionValue = ratioOption.value ?: return
            val rotationOrientationValue = rotationOrientation.value ?: return

            val newValue = when (ratioOptionValue.ratioId) {
                RATIO_ID_ORIGINAL, RATIO_ID_FILM -> {
                    // 需要根据当前的旋转朝向来决定宽高比
                    when (rotationOrientationValue) {
                        RotationOrientation.TOP, RotationOrientation.BOTTOM -> ratioOptionValue.ratioValue
                        RotationOrientation.LEFT, RotationOrientation.RIGHT -> {
                            if (ratioOptionValue.ratioValue == 0F) 0F else 1f / ratioOptionValue.ratioValue
                        }
                    }
                }

                else -> ratioOptionValue.ratioValue
            }
            if (newValue != value) value = newValue
        }
        addSource(ratioOption) { updateRatioValue() }
        addSource(rotationOrientation) { updateRatioValue() }
        value = ratioOption.value?.ratioValue
    }
    val ratioValue: LiveData<Float> get() = _ratioValue

    private val initialCropRectInDisplay: LiveData<RectF> = MediatorLiveData<RectF>().apply {
        addSource(previewAnimController.previewArea) { value = it.calculateCenterInsideRect(clipAspectRatio) }
        value = previewAnimController.previewArea.value?.calculateCenterInsideRect(clipAspectRatio)
    }

    /**
     * 裁剪框，相对于预览视窗的坐标
     */
    private val _clipRectInDisplay: MutableLiveData<RectF> = MutableLiveData(
        videoClip.cuttedTailorSize?.let {
            previewAnimController.previewArea.value?.calculateCenterInsideRect(it.aspectRatio())
        } ?: initialCropRectInDisplay.value
    )
    val clipRectInDisplay: LiveData<RectF> get() = _clipRectInDisplay

    private val clipTransformInDisplay: LiveData<FloatArray> get() = previewAnimController.finalContentTransformMatrixStartingIdentity

    /**
     * 重置按钮是否可用
     *
     * 只要裁剪框和变换矩阵与默认值不同，则可认为有过变更
     */
    private val _isResetEnabled = MediatorLiveData<Boolean>().apply {
        fun updateResetEnabled() {
            val clipRect = clipRectInDisplay.value ?: return
            val initialContentRect = initialCropRectInDisplay.value ?: return
            val isClipRectChanged = clipRect.approxEquals(initialContentRect, RECT_DIFF_EPSILON).not()

            val finalPose = previewAnimController.finalContentPose
            val defaultPose = previewAnimController.defaultContentPose
            val finalAxisAngleTransform = finalPose.compositeRotate
            val isPoseChange = (!finalPose.equalsRzTSTransform(defaultPose) // 判断平移、缩放、旋转
                    || !(finalAxisAngleTransform.allPastTransform.equalsTransform(IdentityMatrix) // 判断矩阵
                    && (finalAxisAngleTransform.formatAngle.compareTo(0f) == 0))) // 判断水平镜像
            value = isPoseChange || isClipRectChanged
        }
        addSource(clipTransformInDisplay) { updateResetEnabled() }
        addSource(clipRectInDisplay) { updateResetEnabled() }
        addSource(initialCropRectInDisplay) { updateResetEnabled() }
        value = false
    }
    val isResetEnabled: LiveData<Boolean> get() = _isResetEnabled

    val isContentAnimating: LiveData<Boolean> = previewAnimController.isContentAnimating

    /**
     * 旧时间线上的Clip到新时间线上的Clip的缩放值
     */
    val videoClipScale: Float
        get() {
            val previewArea = previewAnimController.previewArea.value ?: return 1.0F
            val clipCropSize = videoClip.cuttedTailorSize ?: Size(videoClip.width, videoClip.height)
            val preTimelineRatio = originalTimeline.width / 1.0F / originalTimeline.height
            val preClipSize = previewArea
                .calculateCenterInsideRect(preTimelineRatio)
                .calculateCenterInsideRect(clipCropSize.aspectRatio())
            val newClipSize = previewArea.calculateCenterInsideRect(clipCropSize.aspectRatio())

            return (newClipSize.width() / preClipSize.width()).also {
                GLog.d(TAG, LogFlag.DL) { "preClipSize: $preClipSize, newClipSize: $newClipSize, scale: $it" }
            }
        }

    val originalTimeline: ITimeline = engine.currentTimeline

    /**
     * 用于将View坐标系中的变换矩阵，转换为Clip的坐标系中的变换矩阵
     */
    private var displayMatrixConvertor: ModelMatrixConvertor? = null

    /**
     * 用于记录当旋转操作开始时的缩放值，约束旋转操作的使用
     */
    private var scaleWhenStartRotation: Float = previewAnimController.currentContentPose.scale

    /**
     * 对[videoClip]和裁剪框逆时针旋转90度
     */
    fun notifyRotateOrientation() {
        if (isRunning.not()) return
        GLog.d(TAG, LogFlag.DL) { "[notifyRotateOrientation]" }
        rotationOrientation.value?.let { orientation ->
            previewAnimController.anticlockwiseRotateOrientation(true)
            _rotationOrientation.value = orientation.anticlockwiseRotate()

            // 处理宽高比联动
            ratioOption.value?.let { currentRatio ->
                if (currentRatio.ratioId == RATIO_ID_ORIGINAL) return@let
                val newRatio = 1f / currentRatio.ratioValue
                ratioOptions.find { it.ratioValue == newRatio }
                    ?.let { _ratioOption.value = it }
            }
        }
    }

    fun notifyRotateStart() {
        if (isRunning.not()) return
        scaleWhenStartRotation = previewAnimController.currentContentPose.scale
    }

    /**
     * 对[videoClip]旋转到指定角度[toDegree]
     *
     * @param toDegree 旋转到的角度
     */
    fun notifyRotate(toDegree: Double) {
        if (isRunning.not()) return
        GLog.d(TAG, LogFlag.DL) { "[notifyRotate] degree=$toDegree" }
        if (rotationDegree.value == toDegree) return
        previewAnimController.rotateByWithConstraint(
            (toDegree - (rotationDegree.value ?: 0.0)).toFloat(),
            scaleWhenStartRotation,
            withAnimation = false
        )
        _rotationDegree.value = toDegree
    }

    /**
     * 对[videoClip]做水平镜像翻转
     */
    fun notifyMirrorHorizontal() {
        if (isRunning.not()) return
        GLog.d(TAG, LogFlag.DL) { "[notifyMirrorHorizontal] currentRotationDegree=${rotationDegree.value}" }
        mirrorHorizontal.value?.let { mirrored ->
            previewAnimController.mirrorHorizontal(true)
            _mirrorHorizontal.value = !mirrored
        }
        _rotationDegree.value = -(rotationDegree.value ?: 0.0)
    }

    /**
     * 修改裁剪框比例
     *
     * @param ratioOption 新的裁剪框比例
     */
    fun notifyRatioOptionChanged(ratioOption: RatioOption) {
        if (isRunning.not()) return
        GLog.d(TAG, LogFlag.DL) { "[notifyRatioOptionChanged] ratioOption=$ratioOption" }
        if (this.ratioOption.value == ratioOption) return
        _ratioOption.value = ratioOption
    }

    /**
     * 通知裁剪框更新
     */
    fun notifyClipFrameUpdated(clipRect: RectF, withAnimation: Boolean) {
        if (isRunning.not()) return
        GLog.d(TAG, LogFlag.DL) { "[notifyClipFrameUpdated] rect=$clipRect withAnimation=$withAnimation" }
        previewAnimController.notifyClipFrameChanged(clipRect, withAnimation)
        previewAnimController.snapBack(withAnimation)
    }

    /**
     * 通知裁剪框更新结束
     */
    fun notifyClipFrameUpdatedEnd() {
        if (isRunning.not()) return
        previewAnimController.doOnOperationEnd(::zoomToCenter)
    }

    /**
     * 为了防止重复添加，将该方法提取为一个单独的方法，而不是每次创建匿名函数
     */
    private fun zoomToCenter() = previewAnimController.zoomToCenter(withAnimation = true)

    private fun onClipRectChanged(rectF: RectF) {
        GLog.d(TAG, LogFlag.DL) { "[onClipRectChanged] rect=$rectF" }
        _clipRectInDisplay.value = rectF
    }

    /**
     * 取消裁剪旋状操作
     */
    fun notifyCancel() {
        if (isContentAnimating.value == true || isRunning.not()) return
        doTransformStopProcess(true)
    }

    /**
     * 应用裁剪旋转操作
     *
     * @return Boolean 是否进行了任何编辑操作
     */
    fun notifyConfirm() {
        if (isContentAnimating.value == true || isRunning.not()) return
        // markby <EMAIL> 2025/05/22 这里需要判断用户是否正在进行操作，如拖动画面，则不进行保存操作
        val (cropRect, contentTransform) = convertDisplayTransformToTimeline()
        val cropRectBefore = videoClip.cropRect ?: NDC_FULL_RECT
        val contentTransformBefore = videoClip.transform?.transMatrix ?: UNIT_MATRIX
        val hasEdits = cropRectBefore.approxEquals(cropRect).not()
                || isMatrixApproximatelyEqual(contentTransformBefore, contentTransform).not()

        if (hasEdits) {
            GLog.d(TAG, LogFlag.DL) {
                "[notifyConfirm] \ncontentTransform=\n${formatMatrix(contentTransform)}\ncropRect=$cropRect"
            }
            videoClip.transform = TransformParams(contentTransform, encodeTransformUIData())
            videoClip.cropRect = cropRect
            operationSaveHelper.saveTimeline(originalTimeline, OperationType.TRANSFORM)
        }

        doTransformStopProcess(false)
    }


    fun notifyReset() {
        if (isRunning.not()) return
        _rotationOrientation.value = RotationOrientation.TOP
        _rotationDegree.value = 0.0
        _mirrorHorizontal.value = false
        _ratioOption.value = ratioOptions.firstOrNull()
        previewAnimController.revert(true)
    }

    /**
     * 为了进入裁剪旋转页面时动画丝滑无跳变，需要按照如下顺序执行启动：
     * 等待预览区入场动画结束 -> 禁用矩阵重绘 -> 更新时间线 -> 恢复变换矩阵 -> seek刷新 -> 完成启动流程
     */
    fun doTransformStartProcess(enginePlayingTimeManager: EnginePlayingTimeManager, videoPlaybackMode: VideoClipPlaybackMode) {
        if (state.value != EditorTransformVMState.STARTING) {
            GLog.w(TAG, LogFlag.DL) { "[doTransformStartProcess] has been started, current state=${state.value}" }
            return
        }
        GLog.d(TAG, LogFlag.DL) { "[doTransformStartProcess] >>>>>>>>开始裁剪旋转页UI启动流程>>>>>>>>" }
        displayMatrixConvertor = ModelMatrixConvertor(
            computeViewToTimelineMatrix(
                checkNotNull(initialCropRectInDisplay.value),
                videoClip.width.toFloat(),
                videoClip.height.toFloat()
            )
        )
        previewAnimController.disableRepaintWhenMatrixChangeUntilNextSeek()
        changeToTransformTimelineIfNeed(enginePlayingTimeManager, videoPlaybackMode)
        videoClip.transform?.let {
            convertTimelineTransformToDisplay(videoClip.cropRect ?: NDC_FULL_RECT, it.transMatrix, false)
        }

        previewAnimController.addClipRectChangeListener(::onClipRectChanged)
        onClipRectChanged(previewAnimController.finalClipRect)

        engine.seekTo(engine.timelineCurrentPosition, 0)

        _state.value = EditorTransformVMState.RUNNING
        GLog.d(TAG, LogFlag.DL) { "[doTransformStartProcess] <<<<<<<<结束裁剪旋转页UI启动流程<<<<<<<" }
    }

    /**
     * 为了裁剪页面丝滑退出，需要按照如下顺序执行停止：
     * 新时间线复原动画 -> 恢复时间线 -> 设置新矩阵 -> seek刷新 -> revert动画
     */
    private fun doTransformStopProcess(cancelTransform: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[doTransformStopProcess] >>>>>>>>开始裁剪旋转页UI停止流程>>>>>>>>" }
        _state.value = EditorTransformVMState.STOPPING

        val recoverTimelineAndFinish = {
            previewAnimController.removeClipRectChangeListener(::onClipRectChanged)
            recoverTimeline {
                GLog.d(TAG, LogFlag.DL) { "[doTransformStopProcess] <<<<<<<<结束裁剪旋转页UI停止流程<<<<<<<" }
                _state.value = EditorTransformVMState.STOPPED
            }
        }

        if (cancelTransform.not()) return recoverTimelineAndFinish()

        videoClip.transform?.transMatrix?.also {
            convertTimelineTransformToDisplay(videoClip.cropRect ?: NDC_FULL_RECT, it, true)
        } ?: run {
            previewAnimController.revert(true)
        }
        previewAnimController.doOnOperationEnd(recoverTimelineAndFinish)
    }

    private fun recoverTimeline(onFinish: () -> Unit) {
        engine.setOrthoPlanes(DEFAULT_ORTHO_PLANE_DISTANCE, -DEFAULT_ORTHO_PLANE_DISTANCE)
        previewAnimController.run {
            disableRepaintWhenMatrixChangeUntilNextSeek()
            engine.restoreByTimeline(originalTimeline, true)
            revert(false)
            previewArea.value?.let {
                scale(videoClipScale, it.centerX(), it.centerY(), false)
            }
        }
        engine.seekTo(engine.timelineCurrentPosition, 0)
        engine.addSeekingListener(object : ISeekingListener {
            override fun onSeekingTimelinePosition(position: Long) {
                engine.removeSeekingListener(this)
                onFinish()
            }
        })
    }

    private fun <T : Any> extractUIData(key: String, defaultValue: T): T {
        @Suppress("UNCHECKED_CAST")
        return (videoClip.transform?.transUIData?.get(key)) as? T ?: defaultValue
    }

    private fun changeToTransformTimelineIfNeed(
        enginePlayingTimeManager: EnginePlayingTimeManager,
        videoPlaybackMode: VideoClipPlaybackMode
    ): Boolean {
        if (engine.currentTimeline != originalTimeline) {
            GLog.w(TAG, LogFlag.DL) {
                "[changeToTransformTimelineIfNeed] timeline has been changed, no need change again"
            }
            return false
        }
        val transformTimeline: ITimeline = engine.cloneCurrentTimeline() ?: run {
            GLog.w(TAG, LogFlag.DL) { "[changeToTransformTimelineIfNeed] cloneCurrentTimeline failed!" }
            return false
        }

        engine.restoreByTimeline(transformTimeline, false)

        val transformVideoClip = transformTimeline.getVideoTrack(MAIN_TRACK_INDEX)?.getClipByTimelinePostion(engine.timelineCurrentPosition) ?: run {
            GLog.w(TAG, LogFlag.DL) { "[changeToTransformTimelineIfNeed] getClipByTimelinePosition failed!" }
            return false
        }

        // 移除transformTimeline的文字效果
        engine.currentTimeline.captionList.forEach { engine.removeCaption(it) }

        transformVideoClip.run {
            // 移除transformTimeline的暗角效果
            removeRawEffectByName(FxNameKey.VIGNETTE_KYE)
            // 移除transformTimeline的裁剪旋转效果
            transform = null
            cropRect = null
            setScaleInPositioner(AppConstants.Number.NUMBER_1f)
        }

        // 修改transformTimeline大小
        val timelineSize = transformTimelineSize
        val changeSizeSuccess = transformTimeline.setVideoSize(transformTimelineSize.width, transformTimelineSize.height)
        if (changeSizeSuccess.not()) {
            GLog.w(TAG, LogFlag.DL) {
                "[changeToTransformTimelineIfNeed] change clipOnlyTimeline size failed, newSize=$transformTimeline"
            }
        }

        // 设置正交投影平面，以便在做镜像动画时能完整显示
        val orthoPlaneDistance = max(timelineSize.width, timelineSize.height).toFloat()
        engine.setOrthoPlanes(orthoPlaneDistance, -orthoPlaneDistance)

        enginePlayingTimeManager.switchVideoClipPlaybackMode(videoPlaybackMode, transformVideoClip)
        engine.notifyTimelineChanged(false)
        return true
    }

    private fun encodeTransformUIData(): Map<String, Any> {
        return mapOf(
            KEY_ROTATION_DEGREE to (rotationDegree.value ?: 0.0),
            KEY_ROTATION_ORIENTATION to (rotationOrientation.value ?: RotationOrientation.TOP).name,
            KEY_MIRROR_HORIZONTAL to (mirrorHorizontal.value == true),
            KEY_ASPECT_RATIO_ID to (ratioOption.value?.ratioId ?: RATIO_ID_ORIGINAL),
            KEY_MIRROR_ROTATION to (EditorEngineGlobalContext.getInstance().gson.toJson(previewAnimController.finalContentPose.compositeRotate))
        )
    }

    /**
     * 将当前视图中的裁剪区域和变换矩阵转换为时间线上的裁剪区域和变换矩阵
     *
     * @return Pair<RectF, FloatArray> 第一项为裁剪区域，第二项为变换矩阵
     */
    private fun convertDisplayTransformToTimeline(): Pair<RectF, FloatArray> {
        val cropRectInDisplay = checkNotNull(clipRectInDisplay.value)
        val initialCropRectInDisplay = checkNotNull(<EMAIL>)
        val displayMatrixConvertor = checkNotNull(displayMatrixConvertor)
        val contentTransformInDisplay = checkNotNull(clipTransformInDisplay.value)

        val cropRectInTimeline = cropRectInDisplay.toNDCRelativeTo(initialCropRectInDisplay)
        val contentTransformInTimeline = displayMatrixConvertor.convert(contentTransformInDisplay)

        val scale = cropRectInTimeline.calculateNDCScaleFactor()
        GLog.d(TAG, LogFlag.DL) { "[encodeTransformData] cropRectInTimeline=$cropRectInTimeline, scale=$scale" }
        if (scale == 1.0F) return Pair(cropRectInTimeline, contentTransformInTimeline)

        // 计算出来的裁剪区域超出NDC坐标系的坐标范围(-1, 1)，需要修正
        val adjustedCropRectInTimeline = cropRectInTimeline.scale(scale)
        val scaleMatrix = FloatArray(MATRIX4x4_SIZE).apply {
            Matrix.setIdentityM(this, 0)
            Matrix.scaleM(this, 0, scale, scale, scale)
        }
        val adjustedContentTransformInTimeline = contentTransformInTimeline.clone()
            .apply { Matrix.multiplyMM(this, 0, scaleMatrix, 0, this, 0) }
        return Pair(adjustedCropRectInTimeline, adjustedContentTransformInTimeline)
    }

    /**
     * 将时间上的裁剪区域和变换矩阵转换为当前视图中的裁剪区域和变换矩阵
     */
    private fun convertTimelineTransformToDisplay(cropRectInTimeline: RectF, transformMatrixInTimeline: FloatArray, withAnimation: Boolean) {
        val initialContentRectInDisplay = checkNotNull(initialCropRectInDisplay.value)
        val displayMatrixConvertor = checkNotNull(displayMatrixConvertor)
        GLog.d(TAG, LogFlag.DL) {
            "[convertTimelineTransformToDisplay] cropRectInTimeline=$cropRectInTimeline, " +
                    "transformMatrixInTimeline=${formatMatrix(transformMatrixInTimeline)}"
        }
        val cropRectInDisplay = cropRectInTimeline.fromNDCRelativeTo(initialContentRectInDisplay)
        val transformMatrixInDisplay = displayMatrixConvertor.convertInverse(transformMatrixInTimeline)

        previewAnimController.run {
            notifyClipFrameChanged(cropRectInDisplay, withAnimation)
            updateContentTransformWithStartingIdentity(
                transformMatrixInDisplay,
                decodeAxisAngleTransFromClip(),
                cropRectInDisplay.center(),
                withAnimation
            )
            zoomToCenter(withAnimation)  // 缩放以充满预览区域
        }
    }

    private fun decodeAxisAngleTransFromClip(): AxisAngleTransform =
        videoClip.transform?.transUIData?.get(KEY_MIRROR_ROTATION)?.toString()?.let { json ->
            val gson = EditorEngineGlobalContext.getInstance().gson
            runCatching { gson.fromJson(json, AxisAngleTransform::class.java) }
                .onFailure { GLog.e(TAG, LogFlag.DL, "Failed to parse mirror rotation: $json", it) }
                .getOrDefault(AxisAngleTransform.Empty)
        } ?: AxisAngleTransform.Empty

    companion object {
        private const val TAG = "EditorTransformVM"

        private const val KEY_ROTATION_DEGREE = "rotation_degree"
        private const val KEY_ROTATION_ORIENTATION = "rotation_orientation"
        private const val KEY_MIRROR_HORIZONTAL = "mirror_horizontal"
        private const val KEY_ASPECT_RATIO_ID = "aspect_ratio_id"
        private const val KEY_MIRROR_ROTATION = "mirror_rotation"

        /**
         * 默认正交投影平面距离
         */
        private const val DEFAULT_ORTHO_PLANE_DISTANCE = 1.0F

        /**
         * 裁剪区域判定是否相等的容差范围，用于解决浮点数精度问题
         */
        private const val RECT_DIFF_EPSILON = 0.5F

        private const val MAIN_TRACK_INDEX = 0
    }
}

class EditorTransformVMFactory(
    private val engine: EditorEngine,
    private val videoClip: IVideoClip,
    private val previewAnimController: PreviewAnimController,
    private val operationSaveHelper: OperationSaveHelper,
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(EditorTransformVM::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return EditorTransformVM(engine, videoClip, previewAnimController, operationSaveHelper) as T
        } else {
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}

/**
 * 裁剪旋转ViewModel的状态
 */
enum class EditorTransformVMState {
    /**
     * 正在启动，此过程中进行时间线更换、矩阵恢复等操作
     */
    STARTING,

    /**
     * 正在运行，此过程中用户可以进行裁剪旋转操作
     */
    RUNNING,

    /**
     * 正在停止，此过程中进行时间线恢复、矩阵复原等操作
     */
    STOPPING,

    /**
     * 已经停止，所有操作已经完成，此时需要退出页面
     */
    STOPPED,
}