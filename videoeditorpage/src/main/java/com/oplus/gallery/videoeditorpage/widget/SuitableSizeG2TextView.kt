/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - SuitableSizeG2TextView
 ** Description: 自适应尺寸文本视图
 ** Version: 1.0
 ** Date : 2025/05/26
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >      <desc>
 **  80320709      2025/05/26  1.0             created
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.TextView
import com.oplus.gallery.videoeditorpage.utlis.ColorSupportUtil

/**
 * 自适应尺寸文本视图
 */
@SuppressLint("AppCompatCustomView")
class SuitableSizeG2TextView : TextView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        setTextSize(textSize)
    }

    override fun setTextSize(size: Float) {
        val metrics = resources.displayMetrics
        val scaleDensity = metrics.scaledDensity
        var density = metrics.density
        if (java.lang.Float.compare(density, 0f) == 0) {
            density = 1f
        }
        val fontScale = scaleDensity / density
        val suitableTextSize = ColorSupportUtil.getSuitableFontSize(size, fontScale, ColorSupportUtil.G2).toInt().toFloat()
        super.setTextSize(TypedValue.COMPLEX_UNIT_PX, suitableTextSize)
    }
}