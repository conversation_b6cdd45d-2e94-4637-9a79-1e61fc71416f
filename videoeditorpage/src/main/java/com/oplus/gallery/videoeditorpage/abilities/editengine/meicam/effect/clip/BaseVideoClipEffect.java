/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseVideoClipEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamMaskRegionInfo;
import com.google.gson.annotations.SerializedName;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoEffect;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class BaseVideoClipEffect extends BaseVideoEffect implements Cloneable, IBaseClip {

    private static final String TAG = "BaseVideoClipEffect";

    public static final int PLAY_TYPE_IN = StreamingConstant.Cartoon.TYPE_IN;
    public static final int PLAY_TYPE_OUT = StreamingConstant.Cartoon.TYPE_OUT;
    public static final int PLAY_TYPE_COMPOSE = StreamingConstant.Cartoon.TYPE_COMPOUND;

    public static final int TYPE_PACKAGED_FX = 0;
    public static final int TYPE_BUILT_IN_FX = 1;
    public static final int TYPE_CUSTOMER_FX = 2;
    public static final int DEFAULT_FX_ID = 0;
    public static final float FX_CLIP_STRENGTH = 1F;


    protected List<BaseVideoClipEffect> mSubEffectList = new ArrayList<>();

    @SerializedName("class_type")
    protected String mClassType;
    protected int mEffectType = -1;
    protected Map<String, String> mSatisticsMaps = new HashMap<>();//本地用，用来存放额外信息，如埋点需要的
    private boolean mIsMainEffect = true;
    private String mZhName;
    private String mChName;
    private String mEnName;
    private int mSpecialEffectsPlayMode = 0;///迭代24新增特效播放类型，0表示循环播放 1表示单次播放
    private boolean mFillClipEdge = false;//是否需要撑满至一边为止，目前只在自由编辑裁剪时使用

    public BaseVideoClipEffect(String name) {
        super(name);
    }

    @NonNull
    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            GLog.e(TAG, LogFlag.DL, "clone, fail:" + e.getMessage());
        }
        return null;
    }

    public void setIsMainEffect(boolean isMainEffect) {
        mIsMainEffect = isMainEffect;
    }

    public boolean isMainEffect() {
        return mIsMainEffect;
    }

    public String getZhName() {
        return mZhName;
    }

    public void setZhName(String zhName) {
        mZhName = zhName;
    }

    public String getChName() {
        return mChName;
    }

    public void setChName(String chName) {
        mChName = chName;
    }

    public String getEnName() {
        return mEnName;
    }

    public void setEnName(String enName) {
        mEnName = enName;
    }

    public int getEffectType() {
        return mEffectType;
    }

    public void setEffectType(int mEffectType) {
        this.mEffectType = mEffectType;
    }

    /**
     * 获取整数型参数映射表
     *
     * @return 包含参数名和对应浮点值的HashMap
     */
    public abstract Map<String, Integer> getIntParams();

    /**
     * 获取浮点型参数映射表
     *
     * @return 包含参数名和对应浮点值的HashMap
     */
    public abstract HashMap<String, Float> getFloatParams();
    /**
     * 获取字符串型参数映射表
     *
     * @return 包含参数名和对应字符串值的HashMap
     */
    public abstract HashMap<String, String> getStringParams();
    /**
     * 获取布尔型参数映射表
     *
     * @return 包含参数名和对应布尔值的HashMap
     */
    public abstract HashMap<String, Boolean> getBooleanParams();

    public abstract HashMap<String, HashMap<Long, Float>> getTimeFloatParams();

    public abstract void setFloatValue(String paramName, float value);

    public abstract float getFloatValue(String paramName);

    public abstract void setFloatValAtTime(String paramName, float value, long time);

    public abstract float getFloatValAtTime(String paramName, long time);

    public abstract void setFloatOffset(String paramName, float offset);

    public abstract void removeAllKeyframe();

    public abstract boolean hasKeyframe();

    public abstract void removeKeyframeAtTime(long time);

    public abstract void setLocalFloatValue(String paramName, float value);

    public abstract float getLocalFloatValue(String paramName);

    public abstract void setStringValue(String paramName, String value);

    public abstract String getStringValue(String paramName);

    public abstract void setBooleanVal(String paramName, boolean value);

    public abstract Boolean getBooleanVal(String paramName);

    public abstract void setStrength(float strength);

    public abstract float getStrength();

    public abstract void setEffectId(int cartoonId);

    public abstract int getEffectId();

    public abstract void setEffectPlayType(int playType);

    public abstract void setEffectPlayDuration(long mCartoonActualDuration);

    public abstract long getEffectPlayDuration();

    public abstract int getEffectPlayType();

    public abstract void setAttachment(String key, Object value);

    public abstract Object getAttachment(String key);

    public abstract void setFilterMask(boolean filterMask);

    public abstract void setIgnoreBackground(boolean ignoreBackground);

    public abstract boolean getIgnoreBackground();

    public abstract void setRegion(float[] region);

    public abstract void setRegionAtTime(float[] region, long time);

    public abstract void setRegional(boolean regional);

    public abstract boolean isRegional();

    public abstract float[] getRegion();

    public abstract HashMap<Long, float[]> getTimeRegions();

    public abstract boolean isFilterMask();

    public abstract boolean setDuration(long duration, long clipDuration);

    public abstract void setInTime(long mInTime);

    public abstract long getInTime();

    public abstract void setOutTime(long mOutTime);

    public abstract long getOutTime();

    public abstract int getTrackIndex();

    public abstract void setTrackIndex(int mTrackIndex);

    public abstract int getType();

    //蒙版区域信息
    public abstract void setRegionInfo(MeicamMaskRegionInfo maskRegionInfo);

    public abstract MeicamMaskRegionInfo getRegionInfo();

    //蒙版羽化值
    public abstract void setRegionalFeatherWidth(float value);

    public abstract float getRegionalFeatherWidth();

    //蒙版是否反转
    public abstract void setInverseRegion(boolean isInverseRegion);

    public abstract boolean isInverseRegion();

    public abstract void setIntVal(String key, int value);

    public abstract int getIntVal(String key);

    //设置裁剪的区域，以中心为原点，类似于世界坐标系
    public abstract void setCutRectF(SLRectF rectF);
    public abstract SLRectF getCutRectF();

    public boolean isFillClipEdge() {
        return mFillClipEdge;
    }

    public void setFillClipEdge(boolean mFillClipEdge) {
        this.mFillClipEdge = mFillClipEdge;
    }

    public void setSpecialEffectsPlayMode(int specialEffectsPlayMode) {
        this.mSpecialEffectsPlayMode = specialEffectsPlayMode;
    }

    public int getSpecialEffectsPlayMode() {
        return mSpecialEffectsPlayMode;
    }

    public List<BaseVideoClipEffect> getSubEffectList() {
        return mSubEffectList;
    }

    public @NonNull
    Map<String, String> getSatisticsMaps() {
        if (mSatisticsMaps == null) {
            mSatisticsMaps = new HashMap<>();
        }
        return mSatisticsMaps;
    }

    public String getClassType() {
        return this.mClassType;
    }

    public interface AttachmentKeys {
        String ATTACHMENT_KEY_SUB_EFFECT = "sub_effect";
        String ATTACHMENT_KEY_EFFECT_NAME = "effect_name";
        String EXTENDS_DATA_KEY_BACKGROUND = "background_story";
        String EXTENDS_DATA_KEY_BACKGROUND_TRANS = "background_trans";
    }

    /**
     * 是否作用在原始输入的片段特效；
     * 纹理返回的宽高是素材的宽高而非timeline的宽高
     * @return true表示作用在原始输入的片段特效，false表示不作用在原始输入的片段特效
     */
    public abstract boolean isRawFx();

    /**
     * 设置是否作用在原始输入的片段特效
     * @param isRawFx true: 作用在原始输入的片段特效 false: 不作用在原始输入的片段特效
     */
    public abstract void setIsRawFx(boolean isRawFx);

    public boolean clearEffectRendererCache() {
        return false;
    }

    @Override
    public long getInPoint() {
        return getInTime();
    }

    @Override
    public long getOutPoint() {
        return getOutTime();
    }
}
