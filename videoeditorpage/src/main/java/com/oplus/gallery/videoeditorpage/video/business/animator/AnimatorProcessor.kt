/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AnimatorProcessor
 ** Description: 动画处理器
 ** Version: 1.0
 ** Date : 2025/5/9
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2025/05/09    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.animator

import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import android.view.View
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation.ViewProperty
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.OnTouchEventInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.util.concurrent.ConcurrentHashMap

object AnimatorProcessor {

    /**
     * 隐藏透明度常量
     */
    const val HIDE_ALPHA = 0f

    /**
     * 显示透明度常量
     */
    const val SHOW_ALPHA = 1f

    private const val TAG = "AnimatorProcessor"

    /**
     * 执行了70%之后停止动画.之所以有这个逻辑，是因为页面间的切换动画是串行执行，为了减少动画时长，所以把淡出动画时间缩短
     */
    private const val PROGRESS_STOP = 0.3f

    /**
     * 弹性值常量
     */
    private const val RESPONSE_THIRD = 0.3f

    /**
     * 弹性值常量
     */
    private const val RESPONSE_QUARTER = 0.25f

    /**
     * 阻力值
     */
    private const val BOUNCE_ZERO = 0f

    /**
     * 滚动弹性值常量
     */
    private const val RESPONSE_SCROLL = 0.4f

    /**
     * 速度为0的常量
     */
    private const val VELOCITY_ZERO = 0f

    /**
     * 无效的view ID常量，用来标记无效的ID值。因为动画的key是int类型，所以用一个不可能的值来表示无效的状态。
     */
    private const val INVALID_VIEW_ID = -1

    /**
     * 动画跟动画结束结束回调的映射表
     */
    private val animationEndMap: HashMap<COUISpringAnimation, COUIDynamicAnimation.OnAnimationEndListener> = hashMapOf()

    /**
     * 动画跟动画更新回调的映射表
     */
    private val animationUpdateMap: HashMap<COUISpringAnimation, COUIDynamicAnimation.OnAnimationUpdateListener> = hashMapOf()

    /**
     * 执行淡出动画的view ID跟动画的映射
     */
    private val exitAnimMap = hashMapOf<Int, COUISpringAnimation>()

    /**
     * 轨道的view ID
     */
    private var trackViewId = INVALID_VIEW_ID

    /**
     * 子菜单的view ID
     */
    private var subMenuViewId = INVALID_VIEW_ID

    /**
     * 是否移除轨道的动画，因为淡入和淡出动画是分开执行的，如果其中一个动画场景中没有执行轨道动画，那另一个场景也不需要执行轨道动画
     */
    private var removeTrackView = false

    /**
     * 是否移除子菜单的动画，因为淡入和淡出动画是分开执行的，如果其中一个动画场景中没有执行子菜单动画，那另一个场景也不需要执行子菜单动画
     */
    private var removeSubMenuView = false

    /**
     * 执行淡入动画的view ID跟动画的映射
     */
    private val enterAnimMap = hashMapOf<Int, COUISpringAnimation>()

    /**
     * 退出动画回调列表
     */
    private val exitCallbackList: ArrayList<(() -> Unit)> = arrayListOf()

    /**
     * 淡入动画回调列表
     */
    private val enterCallbackList: ArrayList<(() -> Unit)> = arrayListOf()

    /**
     * 标记要做淡出动画，无淡出动画/淡出动画执行完，则为false，有动画在执行中，则为true
     */
    private var isNeedExit = false

    /**
     * 标记要做淡入动画，无淡入动画/淡入动画执行完，则为false，有动画在执行中，则为true
     */
    private var isNeedEnter = false

    /**
     * 触摸事件拦截，如果在执行动画，则拦截触摸事件
     */
    private val onTouchEventInterceptor = object : OnTouchEventInterceptor {
        override fun shouldInterceptTouchEvent(ev: MotionEvent): Boolean {
            GLog.d(TAG, LogFlag.DL) { "isAnimRunning: ${isAnimRunning()}, flagExit:$isNeedExit, flagEnter:$isNeedEnter" }
            return isAnimRunning()
        }
    }

    /**
     * 存储待执行的动画任务列表
     */
    private val animationTaskMap: ConcurrentHashMap<Int, MutableList<AnimationTask>> = ConcurrentHashMap()

    /**
     * 用于在主线程中执行动画任务
     */
    private val handler = Handler(Looper.getMainLooper())

    /**
     * 启动alpha动画，并且在动画结束的时候执行逻辑
     * @param view 需要进行alpha动画的view
     * @param fadeOut 是否淡出，true的话 alpha由1~0，false的话  alpha由0~1
     * @param after 动画结束后的执行逻辑
     */
    private fun createAlphaAnimatorWithListener(view: View, fadeOut: Boolean, after: ((canceled: Boolean) -> Unit)): COUISpringAnimation {
        val animation = createAlphaAnimator(view, fadeOut)
        val endListener = COUIDynamicAnimation.OnAnimationEndListener { _, canceled, _, _ ->
            after.invoke(canceled)
            animation.removeEndListener(animationEndMap[animation])
            animationEndMap.remove(animation)
        }
        animationEndMap[animation] = endListener
        animation.addEndListener(endListener)
        return animation
    }

    /**
     * 启动滚动动画
     * 创建并启动一个弹簧动画，使得给定视图的属性从起始位置平滑过渡到结束位置。
     *
     * @param viewProperty 需要进行动画的视图属性
     * @param view 执行动画的视图对象
     * @param startPosition 动画的起始位置
     * @param endPosition 动画的结束位置
     * @return 返回创建的COUISpringAnimation对象
     */
    fun startScrollAnimation(viewProperty: ViewProperty, view: View, startPosition: Float, endPosition: Float): COUISpringAnimation {
        val springForce = COUISpringForce().setBounce(BOUNCE_ZERO)        //值越大弹性效果越明显
            .setResponse(RESPONSE_SCROLL)  //值越小动画越快
            .setFinalPosition(endPosition)
        val springScroll =
            COUISpringAnimation(view, viewProperty, endPosition).setStartVelocity(VELOCITY_ZERO).setSpring(springForce).setStartValue(startPosition)
        return springScroll
    }

    /**
     * 执行淡入动画（延迟150ms执行）
     */
    fun startEnterAnim() {
        GLog.d(TAG, LogFlag.DL) { "[startEnterAnim]: size: ${enterAnimMap.size}" }
        processTrackLayoutAnimWhenEnter()
        val size = enterAnimMap.size
        var index = 1
        isNeedEnter = true
        if (size != 0) {
            enterAnimMap.forEach { (_, animation) ->
                val endListener = COUIDynamicAnimation.OnAnimationEndListener { _, _, _, _ ->
                    animation.removeEndListener(animationEndMap[animation])
                    animationEndMap.remove(animation)
                    if (size == index) {
                        isNeedEnter = false
                        enterCallbackList.forEach { it.invoke() }
                        enterCallbackList.clear()
                        enterAnimMap.clear()
                    }
                    index += 1
                }
                animationEndMap[animation] = endListener
                animation.addEndListener(endListener)
                animation.start()
            }
        } else {
            isNeedEnter = false
            enterCallbackList.forEach { it.invoke() }
            enterCallbackList.clear()
        }
    }

    /**
     * 启动退出动画
     * @param showTrack 旧和新的页面是否显示轨道
     * @param showSubMenu 旧和新的页面是否显示子菜单
     * @param needEnterAnim 是否需要执行淡入动画，默认为true.
     */
    fun startExitAnim(
        showTrack: Pair<Boolean, Boolean>,
        showSubMenu: Pair<Boolean, Boolean>,
        needEnterAnim: Boolean
    ) {
        processTrackLayoutAnimWhenExit(showTrack, showSubMenu, needEnterAnim.not())
        val size = exitAnimMap.size
        GLog.d(TAG, LogFlag.DL) { "[startExitAnim]: size: $size" }
        var index = 1
        isNeedExit = true
        isNeedEnter = needEnterAnim
        if (size != 0) {
            exitAnimMap.forEach { (_, animation) ->
                val endListener = COUIDynamicAnimation.OnAnimationEndListener { _, _, _, _ ->
                    animation.removeEndListener(animationEndMap[animation])
                    animationEndMap.remove(animation)
                    if (size == index) {
                        isNeedExit = false
                        exitCallbackList.forEach { it.invoke() }
                        exitCallbackList.clear()
                        exitAnimMap.clear()
                    }
                    index += 1
                }
                animationEndMap[animation] = endListener
                animation.addEndListener(endListener)

                val updateListener = COUIDynamicAnimation.OnAnimationUpdateListener { _, currentValue, _ ->
                    if (currentValue <= PROGRESS_STOP) {
                        animation.skipToEnd()
                        animation.removeUpdateListener(animationUpdateMap[animation])
                        animationUpdateMap.remove(animation)
                    }
                }
                animationUpdateMap[animation] = updateListener
                animation.addUpdateListener(updateListener)

                animation.start()
            }
        } else {
            isNeedExit = false
            exitCallbackList.forEach { it.invoke() }
            exitCallbackList.clear()
        }
    }

    /**
     * 轨道的视图ID
     *
     * 将传入的ID注册为轨迹视图的ID。如果当前轨迹视图ID为无效值（INVALID_VIEW_ID），则将其更新为传入的ID。
     *
     * @param trackId 要注册的轨迹视图ID
     * @param subMenuId 要注册的子菜单视图ID
     */
    fun registerViewId(trackId: Int, subMenuId: Int) {
        if (trackViewId == INVALID_VIEW_ID) {
            trackViewId = trackId
        }
        if (subMenuViewId == INVALID_VIEW_ID) {
            subMenuViewId = subMenuId
        }
    }

    /**
     * 为指定的视图创建退出动画。
     *
     * @param view 需要创建退出动画的视图。
     * @param fadeOut 是否淡出，true的话 alpha由1~0，false的话  alpha由0~1
     * @param cover 如果视图已经存在退出动画，是否覆盖之前的动画。默认为false。
     */
    fun createExitAnim(view: View, fadeOut: Boolean, cover: Boolean = false) {
        if (exitAnimMap.containsKey(view.id).not()) {
            val animation = createAlphaAnimator(view, fadeOut)
            exitAnimMap[view.id] = animation
        } else {
            if (cover) {
                exitAnimMap.remove(view.id)
                val animation = createAlphaAnimator(view, fadeOut)
                exitAnimMap[view.id] = animation
            }
        }
    }

    /**
     * 创建进入动画
     *
     * @param view 要添加动画的视图
     * @param fadeOut 是否淡出，true的话 alpha由1~0，false的话  alpha由0~1
     * @param cover 如果为true，则会覆盖已存在的动画
     */
    fun createEnterAnim(view: View, fadeOut: Boolean, cover: Boolean = false) {
        if (enterAnimMap.containsKey(view.id).not()) {
            val animation = createAlphaAnimator(view, fadeOut)
            enterAnimMap[view.id] = animation
        } else {
            if (cover) {
                enterAnimMap.remove(view.id)
                val animation = createAlphaAnimator(view, fadeOut)
                enterAnimMap[view.id] = animation
            }
        }
    }

    /**
     * 从enterAnimMap中移除指定视图的进入动画 （目前只有调节/滤镜一级页用到）
     * 调节/滤镜一级页的淡入动画不是由mContentContainer来控制，而是由其几个子view来控制 （编辑，复制，删除按钮）
     * 前一个页面的淡出执行完之后，mContentContainer的透明度为0，所以需要重置为1
     *
     * @param view 需要移除进入动画的视图
     */
    fun removeEnterAnim(view: View) {
        view.alpha = SHOW_ALPHA
        enterAnimMap.remove(view.id)
    }

    /**
     * 移除所有进入动画
     */
    fun removeAllEnterAnim() {
        enterAnimMap.clear()
        enterCallbackList.clear()
    }

    /**
     * 移除所有退出动画
     */
    fun removeAllExitAnim() {
        exitAnimMap.clear()
        exitCallbackList.clear()
    }

    /**
     * 添加淡出动画结束之后的回调
     *
     * @param func 淡出动画结束之后执行的动画
     */
    fun addExitCallback(func: (() -> Unit)) {
        exitCallbackList.add(func)
    }

    /**
     * 添加淡入动画结束之后的回调
     *
     * @param func 淡入动画结束之后执行的动画
     */
    fun addEnterCallback(func: (() -> Unit)) {
        enterCallbackList.add(func)
    }

    /**
     * 注册触摸事件拦截器
     *
     * 在给定的 [BaseActivity] 中添加一个触摸事件拦截器，用于拦截触摸事件。
     *
     * @param activity 要注册触摸事件拦截器的 BaseActivity 实例
     */
    fun registerTouchInterceptor(activity: BaseActivity) {
        activity.addInterceptEventListener(onTouchEventInterceptor)
    }

    /**
     * 取消注册触摸拦截器
     *
     * @param activity 需要取消注册触摸拦截器的活动对象
     */
    fun unregisterTouchInterceptor(activity: BaseActivity) {
        activity.removeInterceptEventListener(onTouchEventInterceptor)
    }

    /**
     * 释放资源，清空所有动画映射和回调列表
     */
    fun release() {
        exitAnimMap.clear()
        enterAnimMap.clear()
        exitCallbackList.clear()
        enterCallbackList.clear()
        animationEndMap.clear()
        animationUpdateMap.clear()
        isNeedExit = false
        isNeedEnter = false
        removeTrackView = false
        cancelAllAnimationTasks()
    }

    /**
     * 异步执行一个动画效果，并在动画结束后执行指定的操作
     *
     * @param targetId 动画目标的ID，用于标识动画任务
     * @param view 需要应用动画效果的视图对象
     * @param isVisible 指示视图是否可见，用于决定动画的透明度变化方向
     * @param delay 动画任务延迟执行的时间，单位为毫秒
     * @param after 动画结束后执行的回调函数，接受一个布尔参数表示动画是否被取消
     */
    fun postAnimation(targetId: Int, view: View, isVisible: Boolean, delay: Long, after: ((canceled: Boolean) -> Unit)) {
        val animation = createAlphaAnimatorWithListener(view, !isVisible, after)
        val runnable = Runnable {
            try {
                animation.start()
            } catch (e: IllegalArgumentException) {
                GLog.e(TAG, LogFlag.DL, "[postAnimation] springAnimation.start(), illegalArgumentException: ${e.message}")
            }
        }

        val task = AnimationTask(animation, runnable) {
            after.invoke(true)
        }
        animationTaskMap.computeIfAbsent(targetId) { mutableListOf() }.add(task)
        handler.postDelayed(runnable, delay)
    }
    /**
     * 根据view找到其对应所有已发布的动画任务列表并且取消。
     */
    fun cancelAllAnimationTaskList(targetId: Int) {
        val taskList = animationTaskMap.remove(targetId) ?: return
        for (task in taskList) {
            handler.removeCallbacks(task.getRunnable())
            task.cancel()
        }
        taskList.clear()
    }

    /**
     * 在执行页面切换的时候需要针对是否有相同元素来做处理，仅针对差异元素做动画
     * 如果当前页面有轨道，并且要跳转的新页面也有轨道，那么就不执行轨道动画
     * 二级菜单栏的逻辑稍微特殊一点，在“文字”“调节”“滤镜”几个页面有特效时，相互之间切换不需要对二级菜单栏做动画
     */
    private fun processTrackLayoutAnimWhenExit(
        showTrack: Pair<Boolean, Boolean>,
        showSubMenu: Pair<Boolean, Boolean>,
        isSkipAnim: Boolean
    ) {
        if ((exitAnimMap[trackViewId] != null) && isSkipAnim.not() && showTrack.first && showTrack.second) {
            exitAnimMap.remove(trackViewId)
            removeTrackView = true
        }
        if ((exitAnimMap[subMenuViewId] != null) && isSkipAnim.not() && showSubMenu.first && showSubMenu.second) {
            exitAnimMap.remove(subMenuViewId)
            removeSubMenuView = true
        }
    }

    private fun processTrackLayoutAnimWhenEnter() {
        if (removeTrackView) {
            removeTrackView = false
            enterAnimMap.remove(trackViewId)
        }
        if (removeSubMenuView) {
            removeSubMenuView = false
            enterAnimMap.remove(subMenuViewId)
        }
    }

    /**
     * 检查动画是否正在运行
     *
     * @return 如果动画正在运行，则返回 true；否则返回 false
     */
    private fun isAnimRunning(): Boolean {
        return isNeedExit || isNeedEnter
    }

    /**
     * 一二级切换时的alpha动画
     * @param view 进行alpha动画的view，目前进行alpha动画的有actionbar，底部控件区
     * @param fadeOut 是否淡出，true的话 alpha由1~0，false的话  alpha由0~1
     */
    private fun createAlphaAnimator(view: View, fadeOut: Boolean): COUISpringAnimation {
        /**
         * 越大弹性效果越明显
         */
        val bounce = BOUNCE_ZERO

        /**
         * 越小动画越快
         */
        val response = if (fadeOut) {
            RESPONSE_QUARTER
        } else {
            RESPONSE_THIRD
        }

        /**
         * 0 或实际离手速度
         */
        val velocity = VELOCITY_ZERO
        val targetAlpha = if (fadeOut) {
            HIDE_ALPHA
        } else {
            SHOW_ALPHA
        }
        val startAlpha = if (fadeOut) {
            SHOW_ALPHA
        } else {
            HIDE_ALPHA
        }
        val springForce = COUISpringForce().setBounce(bounce).setResponse(response).setFinalPosition(targetAlpha)
        val springAlpha = COUISpringAnimation(view, COUIDynamicAnimation.ALPHA, targetAlpha).setStartVelocity(velocity).setSpring(springForce)
            .setStartValue(startAlpha)
        return springAlpha
    }

    /**
     * 清除指定的动画任务列表
     */
    private fun clearAnimationTasks(taskList: MutableList<AnimationTask>) {
        val iterator = taskList.iterator()
        while (iterator.hasNext()) {
            val task = iterator.next()
            handler.removeCallbacks(task.getRunnable())
            task.cancel()
            iterator.remove() // 安全移除
        }
    }

    /**
     * 取消所有已发布的动画任务列表。
     */
    private fun cancelAllAnimationTasks() {
        val viewIds = ArrayList(animationTaskMap.keys)
        for (viewId in viewIds) {
            animationTaskMap[viewId]?.let { taskList ->
                clearAnimationTasks(taskList)
            }
        }
        animationTaskMap.clear()
    }
}