/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorRingtoneUIScheme
 ** Description:视频铃声编辑 UIScheme
 ** Version: 1.0
 ** Date : 2025/06/10
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.ringtone

import android.view.ViewGroup
import android.widget.RelativeLayout
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.video.controler.BaseCustomTrimUIScheme

/**
 * 视频铃声业务UI适配
 */
class VideoEditorRingtoneUIScheme(
    activity: BaseActivity,
    rootView: ViewGroup,
    editorEngine: EditorEngine
) : BaseCustomTrimUIScheme(activity, rootView, editorEngine) {
    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        return when {
            isLandscape -> R.layout.videoeditor_frame_ringtone_trimvideo_layout_landscape
            else -> R.layout.videoeditor_frame_ringtone_trimvideo_layout
        }
    }

    /**
     * 适配系统导航栏和状态栏间距
     */
    override fun adaptSysBarMargin() {
        (previewContainer.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.topMargin = activity.resources.getDimensionPixelSize(R.dimen.dp_42)
            previewContainer.requestLayout()
        }
        (bottomSafeView.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.height = activity.bottomNaviBarHeight()
            bottomSafeView.requestLayout()
        }
    }

    override fun setScheme() {
        uiExecutor.setScheme(this@VideoEditorRingtoneUIScheme, null)
    }
}