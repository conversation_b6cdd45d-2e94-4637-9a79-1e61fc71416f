/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - BaseFileCacheServiceTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/27
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/12/27        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.foundation.cache

import android.content.Context
import com.oplus.gallery.foundation.cache.diskcache.BaseFileCacheService
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.mockk
import org.junit.AfterClass
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class BaseFileCacheServiceTest {
    companion object {
        private const val TEST_COUNT = 5
        private const val MAX_COUNT = 3
        private const val MIN_CLEAN_INTERVAL_AFTER_USE = 1000
        private val testDir = "build/${BaseFileCacheServiceTest::class.simpleName}"

        @AfterClass
        fun classTearDown() {
            deleteDir()
        }

        private fun deleteDir() {
            val dir = File(testDir)
            dir.listFiles()?.forEach {
                it.delete()
            }
            dir.delete()
        }
    }

    private object TestFileCacheService : BaseFileCacheService<Boolean, Boolean>() {
        override val maxFileCount: Int = MAX_COUNT
        override val minCleanIntervalAfterUse: Int = MIN_CLEAN_INTERVAL_AFTER_USE
        override fun getDir(context: Context): File {
            return File(testDir)
        }

        override fun onGetCache(context: Context, file: File): Boolean {
            return file.exists()
        }

        override fun onPutCache(context: Context, file: File, data: Boolean): Boolean {
            return file.createNewFile()
        }
    }

    private val context = mockk<Context>(relaxed = true)

    @Before
    fun setUp() {
        ContextGetter.context = context
        deleteDir()
        val dir = File(testDir)
        dir.mkdirs()
        for (i in 0 until TEST_COUNT) {
            Thread.sleep(1000)
            File(dir, i.toString()).createNewFile()
        }
    }

    @Test
    fun should_delete_files_when_cleanCache() {
        TestFileCacheService.cleanCache(context)
        checkFiles(arrayOf("2", "3", "4"))
    }

    @Test
    fun should_delete_files_except_getCache_when_cleanCache() {
        TestFileCacheService.getCache(context, "1")
        TestFileCacheService.cleanCache(context)
        checkFiles(arrayOf("1", "3", "4"))
    }

    @Test
    fun should_delete_files_long_after_getCache_when_cleanCache() {
        TestFileCacheService.getCache(context, "1")
        Thread.sleep(MIN_CLEAN_INTERVAL_AFTER_USE * 2.toLong())
        TestFileCacheService.cleanCache(context)
        checkFiles(arrayOf("2", "3", "4"))
    }

    private fun checkFiles(exceptedFiles: Array<String>) {
        val files = File(testDir).listFiles()
        Assert.assertEquals(exceptedFiles.size, File(testDir).listFiles().size)
        files.sortBy {
            it.lastModified()
        }
        files.forEachIndexed { index, file ->
            Assert.assertEquals(exceptedFiles[index], file.name)
        }
    }
}