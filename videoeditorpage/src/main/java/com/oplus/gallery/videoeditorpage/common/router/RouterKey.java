/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RouterKey.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.router;

public class RouterKey {

    public static final String EXP_ID = "expId";
    public static final String EXP_PARAM = "expParam";
    public static final String KEY_AD_FLOAT_ID = "key_ad_float_id";

    public static final String KEY_COVER_IS_DYNAMIC = "cover_is_dynamic";

    public static final String KEY_ENTER_SOURCE = "enter_source";//判断用户是哪个来源跳转模板
    public static final String KEY_FROM_LIMIT_TASK_ACTIVTY = "from_unlock_material";//来自解锁落地页
    public static final String KEY_UNLOCK_MATERIAL_ID = "unlock_material_id";//落地页Id
    public static final String KEY_IS_REPLACED = "is_replaced"; //是否在编辑页替换素材
    public static final String KEY_CLIP_LABLE = "key_clip_lable"; //片段的便签信息
    public static final String KEY_LABLE_DETAIL = "key_lable_detail"; //片段的标签详细信息（迭代32）

    public static final String KEY_IS_ORDER = "is_order"; //是否排序

    public static final String KEY_RE_MODIFIED_TYPE = "re_modified_type"; //二次编辑

    // 编辑页 resultCode
    public static final int EDITOR_SAVE_NOT_RESULT_CODE = 102;
    public static final int EDITOR_SAVE_NOT_RESULT_NOT_RESELECT_CODE = 103;
    public static final String EDITOR_IS_FROM_IMPORT_CLIP = "editor_is_from_import_clip";

    public static final String KEY_SAMPLE_ID = "sample_id";
    public static final String VALUE_BUUID = "0";

    public static final String IMPORT_ADD_IS_NORMAL_COMPILE = "import_add_is_normal_compile";
    // 导出
    public static final String KEY_COMPILE_SOURCE = "compile_source";


    public static class CompileSource {
        public static final int KEY_EDITOR_EXPORT = 1;
    }
}
