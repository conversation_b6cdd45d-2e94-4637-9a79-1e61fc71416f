/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 * * File:  - VideoTimeAnimFrameLayout.java
 * * Description: 视频编辑中播放按钮、时间的容器
 **
 ** Version: 1.0
 ** Date: 2022/08/04
 ** Author: zhong<PERSON><PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2022/08/04		1.0		OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig.getAnimAlpha
import com.oplus.gallery.foundation.ui.helper.FeedbackAnimatorHelper
import com.oplus.gallery.foundation.ui.helper.enablePressFeedback

/**
 * 播控容器 目前只有壁纸业务使用
 */
class VideoTimeAnimFrameLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private val feedbackHelper: FeedbackAnimatorHelper?
    private val srcAlpha: Int = Color.alpha(context.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask))
    private val view: View? by lazy {
        findViewById(R.id.editor_btn_play_and_time)
    }

    init {
        feedbackHelper = FeedbackAnimatorHelper(this, FeedbackAnimatorHelper.CARD_PRESS_FEEDBACK).apply {
            setAnimationUpdateListener(object : FeedbackAnimatorHelper.AnimationUpdateListener {
                override fun onAnimationUpdate(ratio: Float) {
                    setBackgroundAlpha(getAnimAlpha(ratio, srcAlpha))
                }
            })
            setAlphaAnimEnable(false)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        view?.setOnTouchListener { _, motionEvent ->
            feedbackHelper?.enablePressFeedback(motionEvent)
            return@setOnTouchListener false
        }
        setBackgroundAlpha(srcAlpha)
    }

    private fun setBackgroundAlpha(alpha: Int) {
        view?.background?.alpha = alpha
    }
}