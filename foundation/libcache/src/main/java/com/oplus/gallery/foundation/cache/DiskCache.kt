/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - DiskCache.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/7/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/7/5        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.foundation.cache

import android.content.Context
import com.oplus.gallery.foundation.cache.diskcache.DiskLruCache
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.thread.ThreadUtils
import com.oplus.gallery.standard_lib.file.File
import java.io.BufferedWriter
import java.io.ByteArrayOutputStream
import java.io.OutputStreamWriter

/**
 * 磁盘缓存，输入输出类型为ByteArray
 * @param context Context
 * @param cacheConfig CacheConfig
 */
class DiskCache(private val context: Context, private val cacheConfig: CacheConfig) : ICache<ByteArray> {
    private var diskLruCache: DiskLruCache? = null

    private fun openCache(context: Context) {
        ThreadUtils.assertInSubThread()
        runCatching {
            context.cacheDir?.let {
                val cacheDir = File(it)
                val directory = File(cacheDir.absolutePath + File.separator + cacheConfig.name)
                diskLruCache = DiskLruCache.open(
                    directory,
                    cacheConfig.version,
                    cacheConfig.valueCount,
                    cacheConfig.maxSize,
                    false
                )
            } ?: let {
                GLog.e(TAG, LogFlag.DL, "openCache, cacheDir is null")
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "openCache, fail, $it")
        }
    }

    private fun openCacheIfClose(context: Context) {
        if (diskLruCache?.isClosed != false) {
            openCache(context)
        }
    }

    override fun put(key: String, value: ByteArray) {
        openCacheIfClose(context)
        runCatching {
            diskLruCache?.edit(key)?.let { editor ->
                BufferedWriter(OutputStreamWriter(editor.newOutputStream(0))).use {
                    it.write(String(value))
                }
                editor.commit()
                diskLruCache?.flush()
            } ?: {
                GLog.e(TAG, LogFlag.DL, "put, diskLruCache is $diskLruCache, edit is null")
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "put, fail, $it")
        }
    }

    override fun get(key: String): ByteArray? {
        runCatching {
            openCacheIfClose(context)
            diskLruCache?.get(key)?.let { snapShot ->
                snapShot.getInputStream(0)?.use { inputStream ->
                    ByteArrayOutputStream().use { bos ->
                        val buffer = ByteArray(BUFFER_ARRAY_SIZE)
                        var len: Int
                        while (inputStream.read(buffer).also { len = it } != -1) {
                            bos.write(buffer, 0, len)
                        }
                        return bos.toByteArray()
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "get, fail, $it")
        }
        return null
    }

    override fun remove(key: String) {
        runCatching {
            openCacheIfClose(context)
            diskLruCache?.remove(key)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "remove, fail, $it")
        }
    }

    override fun clear() {
        runCatching {
            openCacheIfClose(context)
            diskLruCache?.delete()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "clear, fail: $it")
        }
    }

    override fun size(): Long {
        return diskLruCache?.size() ?: 0
    }

    /**
     * 缓存配置
     * @param name 缓存名称，用于创建缓存目录
     * @param version 缓存版本，版本变更时会清空缓存
     * @param maxSize 缓存最大大小，超过此大小时会按照LRU策略清除缓存
     * @param valueCount 缓存value数量，一般为1
     */
    data class CacheConfig(
        val name: String,
        val version: Int,
        val maxSize: Long,
        val valueCount: Int
    )

    companion object {
        private const val TAG = "ByteArrayDiskCache"
        private const val BUFFER_ARRAY_SIZE = 1024
    }
}
