/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RefreshRateUpdater.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video

import android.content.Context
import android.view.Window
import androidx.annotation.MainThread
import com.oplus.gallery.addon.view.getSurfaceControl
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.hardware.IScreen
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 刷新率更新器。
 * 使用此更新器设置窗口帧率，并维持住帧率。
 *
 * e.g.
 * ```kotlin
 * private val updater: IRefreshRateUpdater
 *
 * fun onVideoPlaybackStateChanged() {
 *      // 需要变更刷新率时，下发目标刷新率
 *      updater.setRefreshRate(window, 120)
 * }
 *
 * fun onDestroy() {
 *      // 注意在合适的时候释放资源，防止泄漏
 *      updater.release()
 * }
 * ```
 */
interface IRefreshRateUpdater {
    /**
     * 设置窗口刷新率。
     * @param targetRate 目标刷新率值
     * @return result。true成功，false表示失败（window无效、帧率不支持等原因）
     */
    @MainThread
    fun setRefreshRate(window: Window, targetRate: Int): Boolean

    /**
     * 释放资源。
     * 需要在合适的时候调用，防止泄漏
     */
    @MainThread
    fun release()
}

internal class VideoEditorRefreshRateUpdater(private val appContext: Context) : IRefreshRateUpdater {
    private val hardwareAbility: IHardwareAbility? by lazy {
        appContext.getAppAbility<IHardwareAbility>()
    }

    /**
     * 记录当前最新的窗口刷新率。防止相同的刷新率重复下发。
     */
    private var currentWindowRefreshRate = 0

    /**
     * 维持刷新率的子线程job。
     */
    private var maintainRefreshRateJob: Job? = null

    /**
     * 记录当前已下发的需要维持的刷新率。防止相同的刷新率重复下发。
     */
    private var maintainedRefreshRate = 0

    override fun setRefreshRate(window: Window, targetRate: Int): Boolean {
        val screenAbility = hardwareAbility?.screen ?: let {
            GLog.e(TAG, LogFlag.DL) { "[setRefreshRate] screenAbility is null! set failed. check what happened." }
            return false
        }

        // 1. 检查屏幕是否能支持到此刷新率
        if (screenAbility.isSupportedSpecificWindowRefreshRate(targetRate).not()) {
            GLog.w(TAG, LogFlag.DL) { "[setRefreshRate] current screen not support refreshRate [$targetRate], set failed." }
            return false
        }

        // 2. 下发窗口刷新率参数
        screenAbility.setWindowRefreshRate(window, targetRate).also {
            if (it.not()) return false
        }
        currentWindowRefreshRate = targetRate

        // 3. 维持帧率：防止无触摸降帧
        isKeepingRefreshRateEnabled(window, screenAbility)
        return true
    }

    /**
     * 维持帧率：设置无触摸降帧
     */
    private fun isKeepingRefreshRateEnabled(window: Window, screenAbility: IScreen) {
        // 重复的targetRate不再下发
        val curRefreshRate = currentWindowRefreshRate
        if (maintainedRefreshRate == curRefreshRate) {
            return
        }

        // 涉及binder调用，子线程中操作
        maintainRefreshRateJob?.cancel()
        maintainRefreshRateJob = AppScope.launch {
            val sf = window.decorView.getSurfaceControl() ?: return@launch
            screenAbility.isKeepingRefreshRateEnabled(sf, true)
            maintainedRefreshRate = curRefreshRate
        }
    }

    override fun release() {
        hardwareAbility?.close()
        maintainRefreshRateJob?.cancel()
        maintainRefreshRateJob = null
    }

    companion object {
        private const val TAG = "VideoEditorRefreshRateUpdater"
    }
}