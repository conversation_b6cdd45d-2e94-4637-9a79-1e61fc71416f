/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : IThumbnailLoader.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2019/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >        <desc>
 * *  <EMAIL>      2019/5/8   1.0  build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.imageloader;

import com.oplus.gallery.basebiz.widget.EditorMenuItemView;

public interface IThumbnailLoader<Data> {

    void startLoader(Data item, EditorMenuItemView target);

    void removeTask(Data item);

    int getWaitTask();

    void cancelAllWaitTask();

    void destroy();
}
