/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: CustomHorizontalScrollView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguang<PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.HorizontalScrollView;

public class CustomHorizontalScrollView extends HorizontalScrollView {

    public OnCustomScrollViewListener mOnCustomScrollViewListener;

    public CustomHorizontalScrollView(Context context) {
        super(context);
    }

    public CustomHorizontalScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (mOnCustomScrollViewListener != null) {
            mOnCustomScrollViewListener.onCustomScrollChanged(this, l, oldl);
        }
    }

    public interface OnCustomScrollViewListener {
        void onCustomScrollChanged(Object o, int x, int oldX);
    }

    public void setOnCustomScrollViewListener(OnCustomScrollViewListener onCustomScrollViewListener) {
        this.mOnCustomScrollViewListener = onCustomScrollViewListener;
    }
}
