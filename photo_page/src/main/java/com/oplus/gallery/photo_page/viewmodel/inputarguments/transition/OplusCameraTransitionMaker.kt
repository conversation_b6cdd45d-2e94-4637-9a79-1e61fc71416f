/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OplusCameraTransitionMaker.kt
 ** Description : 负责处理来自于Camera模块的呼起相册大图页的转场动画
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : Co<PERSON><EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.inputarguments.transition

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.graphics.Color
import android.graphics.PointF
import android.graphics.RectF
import android.os.Bundle
import android.view.Surface
import android.view.animation.PathInterpolator
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_CAMERA
import androidx.core.graphics.toRectF
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_CLOSE_CAMERA_ENTER_ANIM
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_ENTER_PHOTO_ANIMATE
import com.oplus.gallery.foundation.ui.animation.AnimationKeyParams
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.basebiz.transition.PageTransitionMaker.PageActivityTransition.Companion.emptySourceBounds
import com.oplus.gallery.basebiz.transition.InvokeFrom
import com.oplus.gallery.basebiz.transition.PageTransitionMaker
import com.oplus.gallery.basebiz.transition.PhotoTransitionMaker
import com.oplus.gallery.basebiz.transition.Transition
import com.oplus.gallery.framework.abilities.ipc.IIPCAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import kotlin.math.pow
import kotlin.math.sqrt
import android.graphics.Rect as Rect

/**
 * 负责处理来自于Camera模块的呼起相册大图页的转场动画
 */
internal class OplusCameraTransitionMaker : PhotoTransitionMaker {

    /**
     * 记录退出动画的外部相对位置及动画资源。
     * 此实现是为了兼容原始[com.oplus.gallery.picture_lib.picture.util.BackToCameraAnimHelper]的实现，
     * 后续对退出到Camera的动画实现优化重构后，可去除这些兼容的内部设计
     * **内部业务类，禁止开放**
     */
    private data class ExitAnimation(
        /**
         * 转场动画位于Camera页面上的终点位置（为屏幕相对百分比坐标）
         */
        val sourcePoint: PointF,

        /**
         * 转场动画资源
         */
        val animationResId: Int = Resources.ID_NULL
    ) {
        /**
         * 给定的点是否在此动画终点附近
         */
        fun isSourcePointNearby(point: PointF, error: Float): Boolean =
            sqrt((point.x - sourcePoint.x).pow(2) + (point.y - sourcePoint.y).pow(2)) < error
    }

    /**
     * 退出动画记录
     *
     * 之前相机和相册跳转时遗留的~
     */
    @Suppress("MagicNumber")
    private val exitAnimationRecords = arrayOf(
        ExitAnimation(PointF(0.14f, 0.915f), R.anim.picture3d_op_back_to_camera_normal),     // OPlus LTR
        ExitAnimation(PointF(0.86f, 0.915f), R.anim.picture3d_op_back_to_camera_rtl),        // OPlus RTL
        ExitAnimation(PointF(0.915f, 0.86f), R.anim.picture3d_op_back_to_camera_normal_90),  // OPlus LTR in 90°
        ExitAnimation(PointF(0.915f, 0.14f), R.anim.picture3d_op_back_to_camera_rtl_90),     // OPlus RTL in 90°
        ExitAnimation(PointF(0.085f, 0.14f), R.anim.picture3d_op_back_to_camera_normal_270), // OPlus LTR in 270°
        ExitAnimation(PointF(0.085f, 0.86f), R.anim.picture3d_op_back_to_camera_rtl_270),    // OPlus RTL in 270°

        ExitAnimation(PointF(0.105f, 0.112f), R.anim.picture3d_op_back_to_camera_movie_mode),     // OPlus LTR in movie mode
        ExitAnimation(PointF(0.112f, 0.895f), R.anim.picture3d_op_back_to_camera_movie_mode_90),  // OPlus LTR in 90° movie mode
        ExitAnimation(PointF(0.888f, 0.105f), R.anim.picture3d_op_back_to_camera_movie_mode_270), // OPlus LTR in 270° movie mode

        ExitAnimation(PointF(0.926f, 0.883f), R.anim.picture3d_ops_back_to_camera_normal), // OnePlus LTR
        ExitAnimation(PointF(0.883f, 0.074f), R.anim.picture3d_ops_back_to_camera_normal_90), // OnePlus LTR in 90°
        ExitAnimation(PointF(0.117f, 0.926f), R.anim.picture3d_ops_back_to_camera_normal_270), // OnePlus LTR in 270°

        ExitAnimation(PointF(0.074f, 0.883f), R.anim.picture3d_ops_back_to_camera_rtl), // OnePlus RTL
        ExitAnimation(PointF(0.883f, 0.926f), R.anim.picture3d_ops_back_to_camera_rtl_90), // OnePlus RTL in 90°
        ExitAnimation(PointF(0.117f, 0.074f), R.anim.picture3d_ops_back_to_camera_rtl_270), // OnePlus RTL in 270°
    )

    /**
     * 0度，90度，270度的相机缩略图坐标
     */
    private val exitAnimationRectRecords: HashMap<Int, Rect> by lazy { HashMap() }

    /**
     * 默认的出场动画
     */
    private val defaultExitAnimation = ExitAnimation(
        PointF(Float.NaN, Float.NaN), R.anim.oplus_rounded_corners_anim_picture3d_op_back_to_camera_normal
    )

    override fun chooseTransitionMaker(
        intent: Intent,
        bundle: Bundle,
        invokeFrom: InvokeFrom
    ): Boolean = (invokeFrom.parentPage == KEY_FROM_CAMERA)

    /**
     * - isEnterAnimationEnabled（FIXME 应重命名为isPageTransitionAnimationEnabled，但因需保持对外接口兼容性而搁置）
     *   包含的动画有：
     *   1. 进入页面时，页面背景透明度变化（ALPHA: 0f -> 1f, duration: 300, delay: 0）
     *   2. 下拉页面时，页面背景透明度变化（ALPHA: 1f -> 0f, duration: 300, delay: 0）
     * - isExitAnimationEnabled
     *   包含的动画有：
     *   1. 退出页面时，页面缩放、平移、透明度动画（大图页缩放到相机缩图处消失，详见动画定义）
     */
    @Suppress("LongMethod")
    override fun make(
        intent: Intent,
        bundle: Bundle
    ): Transition {
        val hasIntegrationUITransition = IntentUtils.getBinderExtra(intent, KEY_INTEGRATION_UI_GESTURE_TOKEN) != null
        val isForceFinishActivity = bundle.getBoolean(IntentConstant.ViewGalleryConstant.KEY_FORCE_FINISH_ACTIVITY, false)

        // 1. 是否启用入场动画
        val isSuggestingEnableEnterAnimation = bundle.getBoolean(KEY_ENTER_PHOTO_ANIMATE, false)
        val isEnterAnimationEnabled = hasIntegrationUITransition.not() && isSuggestingEnableEnterAnimation

        // 1.1 是否透明activity
        val isActivityTransparent = IntentUtils.getBooleanExtra(intent, KEY_IS_ACTIVITY_TRANSPARENT, false)

        // 1.2 是否启用出场动画
        val isSuggestingEnableExitAnimation = isActivityTransparent || bundle.getBoolean(KEY_CLOSE_CAMERA_ENTER_ANIM, false)
        val isExitAnimationEnabled = hasIntegrationUITransition.not() && isSuggestingEnableExitAnimation
        val isPagePositionTransitionEnabled = hasIntegrationUITransition.not()
        val isPageBackgroundTransitionEnabled = hasIntegrationUITransition.not() && isSuggestingEnableExitAnimation

        // 1.3 出场动画时长
        val thumbnailAnimationDuration = intent.getLongExtra(KEY_THUMBNAIL_EXIT_ANIMATION_DURATION, PHOTO_PAGE_TRANSITION_DURATION)

        // 1.4 相机缩略图圆角X/Y半径
        val thumbnailRadiusX = IntentUtils.getIntExtra(intent, KEY_THUMBNAIL_CORNER_X, 0).toFloat()
        val thumbnailRadiusY = IntentUtils.getIntExtra(intent, KEY_THUMBNAIL_CORNER_Y, 0).toFloat()

        // 1.5 动效中断距离
        val thumbnailInterruptAnimationDistance = IntentUtils.getIntExtra(intent, KEY_THUMBNAIL_INTERRUPT_ANIMATION_DISTANCE, 0)

        // 2. 相机指定的SourceBounds
        val cameraSourceBounds = figureCameraSourceBounds(intent)
        /*
         * 3. 根据启动动画源位置计算出enterAnimation序列
         * 3.1 如果使用来自于Camera的转场动画，则入场/出场动画为Activity动画，且入场动画已由Camera设置并执行完毕，计算出场的Activity动画即可
         */
        val enterTransition = OPlusCameraEnterTransitionParamsMaker(
            isEnterAnimationEnabled,
            if (isActivityTransparent) 0 else PHOTO_PAGE_TRANSITION_DURATION,
            actualAnimationResId = {
                makeEnterFromCameraAnimation()
            },
            actualSourceBounds = actualSourceBounds@{ surfaceRotation ->
                /**
                 * 如果 cameraSourceBounds 是 emptySourceBounds()，则直接返回 emptySourceBounds。
                 */
                if (cameraSourceBounds.rect === emptySourceBounds()) {
                    return@actualSourceBounds emptySourceBounds()
                }

                remapSourcePoint(
                    sourcePoint = PointF(cameraSourceBounds.rect.centerX(), cameraSourceBounds.rect.centerY()),
                    surfaceRotation = surfaceRotation
                ).let {
                    /**
                     * TODO 重构后赋值
                     *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                     */
                    /**
                     * TODO 重构后赋值
                     *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                     */
                    val sourceBoundsWidth = 0f

                    /**
                     * TODO 重构后赋值
                     *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                     */
                    /**
                     * TODO 重构后赋值
                     *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                     */
                    val sourceBoundsHeight = 0f

                    RectF(
                        it.x - sourceBoundsWidth / 2f,
                        it.y - sourceBoundsHeight / 2f,
                        it.x + sourceBoundsWidth / 2f,
                        it.y + sourceBoundsHeight / 2f
                    )
                }
            }
        )

        // 4. 根据启动动画源位置计算出exitAnimation序列
        val exitTransition = OPlusCameraExitTransitionParamsMaker(
            isPagePositionTransitionEnabled = isPagePositionTransitionEnabled,
            isPageBackgroundTransitionEnabled = isPageBackgroundTransitionEnabled,
            thumbnailAnimationDuration = thumbnailAnimationDuration,
            thumbnailRadiusX = thumbnailRadiusX,
            thumbnailRadiusY = thumbnailRadiusY,
            thumbnailInterruptAnimationDistance = thumbnailInterruptAnimationDistance,
            actualAnimationResId = { surfaceRotation ->
                /**
                 * 如果相机的缩略图带的是坐标,则无需使用动效
                 */
                if (cameraSourceBounds.type == CameraSourceBoundsType.BOUNDS) {
                    Resources.ID_NULL
                } else {
                    makeExitToCameraAnimation(
                        sourcePoint = PointF(cameraSourceBounds.rect.centerX(), cameraSourceBounds.rect.centerY()),
                        surfaceRotation = surfaceRotation
                    )
                }
            },
            actualSourceBounds = actualSourceBounds@{ surfaceRotation ->
                when (cameraSourceBounds.type) {
                    /**
                     * 未定义，则直接返回 emptySourceBounds。
                     */
                    CameraSourceBoundsType.UNDEFINED -> emptySourceBounds()

                    /**
                     * 依据目前surfaceRotation取得相机缩略图坐标
                     */
                    CameraSourceBoundsType.BOUNDS -> exitAnimationRectRecords[surfaceRotation]?.toRectF() ?: cameraSourceBounds.rect


                    CameraSourceBoundsType.PIVOT -> {
                        remapSourcePoint(
                            sourcePoint = PointF(cameraSourceBounds.rect.centerX(), cameraSourceBounds.rect.centerY()),
                            surfaceRotation = surfaceRotation
                        ).let { sourceBounds ->
                            /**
                             * TODO 重构后赋值
                             *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                             */
                            /**
                             * TODO 重构后赋值
                             *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                             */
                            val sourceBoundsWidth = 0f

                            /**
                             * TODO 重构后赋值
                             *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                             */
                            /**
                             * TODO 重构后赋值
                             *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                             */
                            val sourceBoundsHeight = 0f

                            RectF(
                                sourceBounds.x - sourceBoundsWidth / 2f,
                                sourceBounds.y - sourceBoundsHeight / 2f,
                                sourceBounds.x + sourceBoundsWidth / 2f,
                                sourceBounds.y + sourceBoundsHeight / 2f
                            )
                        }
                    }
                }
            }
        )

        return Transition(
            remoteSourceBounds = cameraSourceBounds.rect,
            isEnterTransitionAnimationEnabled = isEnterAnimationEnabled,
            isExitTransitionAnimationEnabled = isExitAnimationEnabled,
            positionControllerKey = bundle.getString(PicturePageConstant.KEY_POSITION_CONTROLLER),
            isForceFinishActivity = isForceFinishActivity,
            enterTransition = enterTransition,
            exitTransition = exitTransition
        )
    }

    /**
     * 获取相机指定的SourceBounds
     */
    private fun figureCameraSourceBounds(intent: Intent): CameraSourceBounds {
        val sourceBoundsCenterX = intent.getFloatExtra(KEY_ANIMATION_PIVOT_X, Float.NaN)
        val sourceBoundsCenterY = intent.getFloatExtra(KEY_ANIMATION_PIVOT_Y, Float.NaN)
        IntentUtils.getParcelableExtra<Rect>(intent, KEY_THUMBNAIL_RECT_ORIENTATION_90)?.let { rect ->
            exitAnimationRectRecords[Surface.ROTATION_270] = rect
        }
        IntentUtils.getParcelableExtra<Rect>(intent, KEY_THUMBNAIL_RECT_ORIENTATION_270)?.let { rect ->
            exitAnimationRectRecords[Surface.ROTATION_90] = rect
        }
        IntentUtils.getParcelableExtra<Rect>(intent, KEY_THUMBNAIL_RECT)?.let { rect ->
            exitAnimationRectRecords[Surface.ROTATION_0] = rect
            return CameraSourceBounds(RectF(rect), CameraSourceBoundsType.BOUNDS)
        }
        return if (sourceBoundsCenterX.isNaN() || sourceBoundsCenterY.isNaN()) {
            CameraSourceBounds(emptySourceBounds(), CameraSourceBoundsType.UNDEFINED)
        } else {
            /**
             * TODO 重构后赋值
             *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
             */
            val sourceBoundsWidth = 0f

            /**
             * TODO 重构后赋值
             *      详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
             */
            val sourceBoundsHeight = 0f

            CameraSourceBounds(RectF(
                sourceBoundsCenterX - sourceBoundsWidth / 2f,
                sourceBoundsCenterY - sourceBoundsHeight / 2f,
                sourceBoundsCenterX + sourceBoundsWidth / 2f,
                sourceBoundsCenterY + sourceBoundsHeight / 2f
            ), CameraSourceBoundsType.PIVOT)
        }
    }


    /**
     * 反回相机动效的SourceBounds及type
     */
    private data class CameraSourceBounds(val rect: RectF, val type: CameraSourceBoundsType)

    /**
     * 反回相机动效的type
     */
    private enum class CameraSourceBoundsType {
        /**
         * 未定义
         */
        UNDEFINED,

        /**
         * Pivot
         */
        PIVOT,

        /**
         * 坐标
         */
        BOUNDS
    }

    private fun remapSourcePoint(sourcePoint: PointF, surfaceRotation: Int): PointF = when (surfaceRotation) {
        Surface.ROTATION_90 -> PointF(sourcePoint.y, 1f - sourcePoint.x)
        Surface.ROTATION_270 -> PointF(1f - sourcePoint.y, sourcePoint.x)
        else -> sourcePoint
    }

    private fun makeEnterFromCameraAnimation(): Int = R.anim.picture3d_camera_fade_in

    private fun makeExitToCameraAnimation(sourcePoint: PointF, surfaceRotation: Int): Int {
        exitAnimationRecords.forEach { record ->
            record.isSourcePointNearby(
                point = remapSourcePoint(sourcePoint, surfaceRotation),
                error = ERROR_DISTANCE_OF_SOURCE_POINT
            ).let { isNearby ->
                if (isNearby) {
                    return record.animationResId
                }
            }
        }

        return defaultExitAnimation.animationResId
    }

    /**
     * 因返回相机的出场动画需要实时根据屏幕朝向来计算获取，所以使用此类代理
     * [PageTransitionMaker]的全部属性
     */
    private class OPlusCameraEnterTransitionParamsMaker(
        isPageBackgroundTransitionEnabled: Boolean,
        thumbnailAnimationDuration: Long,
        private val actualAnimationResId: (Int) -> Int,
        private val actualSourceBounds: (Int) -> RectF
    ) : PageTransitionMaker() {

        override val backgroundColors: AnimationKeyParams<Color> = if (isPageBackgroundTransitionEnabled) {
            AnimationKeyParams(
                duration = thumbnailAnimationDuration,
                animations = arrayOf(Color.valueOf(Color.TRANSPARENT), Color.valueOf(Color.BLACK)),
                interpolator = backgroundInterpolator
            )
        } else {
            AnimationKeyParams(
                duration = thumbnailAnimationDuration,
                animations = arrayOf(Color.valueOf(Color.BLACK), Color.valueOf(Color.BLACK))
            )
        }

        override val thumbnailPositions: AnimationKeyParams<Rect> =
            AnimationKeyParams(
                duration = thumbnailAnimationDuration,
                animations = arrayOf(Rect(), Rect()),
                interpolator = positionInterpolator
            )

        override fun makeActivityTransition(context: Context, surfaceRotation: Int, region: Rect?): PageActivityTransition = PageActivityTransition(
            surfaceRotation = surfaceRotation,
            transition = actualAnimationResId(surfaceRotation),
            sourceBounds = actualSourceBounds(surfaceRotation)
        )
    }

    /**
     * 因返回相机的出场动画需要实时根据屏幕朝向来计算获取，所以使用此类代理
     * [PageTransitionMaker]的全部属性
     */
    private class OPlusCameraExitTransitionParamsMaker(
        private val isPagePositionTransitionEnabled: Boolean,
        isPageBackgroundTransitionEnabled: Boolean,
        thumbnailAnimationDuration: Long,
        thumbnailRadiusX: Float,
        thumbnailRadiusY: Float,
        thumbnailInterruptAnimationDistance: Int,
        private val actualAnimationResId: (Int) -> Int,
        private val actualSourceBounds: (Int) -> RectF
    ) : PageTransitionMaker() {

        override val backgroundColors: AnimationKeyParams<Color> = if (isPageBackgroundTransitionEnabled) {
            AnimationKeyParams(
                duration = PHOTO_PAGE_TRANSITION_DURATION,
                animations = arrayOf(Color.valueOf(Color.BLACK), Color.valueOf(Color.TRANSPARENT)),
                interpolator = backgroundInterpolator
            )
        } else {
            AnimationKeyParams(
                duration = PHOTO_PAGE_TRANSITION_DURATION,
                animations = arrayOf(Color.valueOf(Color.BLACK), Color.valueOf(Color.BLACK)),
                interpolator = COUIEaseInterpolator()
            )
        }

        override val thumbnailPositions: AnimationKeyParams<Rect> = if (isPagePositionTransitionEnabled) {
            AnimationKeyParams(
                duration = thumbnailAnimationDuration,
                animations = emptyArray(),
                interpolator = positionInterpolator,
                thumbnailCornerRadiusX =  thumbnailRadiusX,
                thumbnailCornerRadiusY =  thumbnailRadiusY,
                interruptDistance = thumbnailInterruptAnimationDistance
            )
        } else {
            AnimationKeyParams(animations = emptyArray())
        }

        override fun makeActivityTransition(context: Context, surfaceRotation: Int, region: Rect?): PageActivityTransition {
            if (isPagePositionTransitionEnabled.not()) {
                return PageActivityTransition(surfaceRotation, Resources.ID_NULL, emptySourceBounds())
            }

            val rect = region ?: context.getAppAbility<IIPCAbility>()?.use {
                it.callCamera.loadTransitionThumbRegion(context)
            }
            return PageActivityTransition(
                surfaceRotation = surfaceRotation,
                transition = rect?.let { Resources.ID_NULL } ?: actualAnimationResId(surfaceRotation),
                sourceBounds = rect?.toRectF() ?: actualSourceBounds(surfaceRotation)
            )
        }
    }

    companion object {
        private const val ERROR_DISTANCE_OF_SOURCE_POINT = 0.01f

        /**
         * 指定大图反回相机动画中，缩略图的坐标。目前此参数为相机所用
         *
         * @see [Transition.remoteSourceBounds]
         */
        private const val KEY_THUMBNAIL_RECT = PhotoInputArgumentsViewModel.KEY_THUMBNAIL_RECT

        private const val KEY_IS_ACTIVITY_TRANSPARENT = PhotoInputArgumentsViewModel.KEY_IS_ACTIVITY_TRANSPARENT

        private const val KEY_INTEGRATION_UI_GESTURE_TOKEN = PhotoInputArgumentsViewModel.KEY_INTEGRATION_UI_GESTURE_TOKEN

        /**
         * 指定大图启动动画中缩略图的启动中心位置。
         *
         * 大图父页面在启动大图页面来呈现图片时，可能希望大图页面会有一个从原始图片位置展开
         * 到大图页全屏的启动动画。因此需要指定这个飞入位置的中心点的X位置。目前此参数为相机所用
         * @see [Transition.remoteSourceBounds]
         */
        private const val KEY_ANIMATION_PIVOT_X = "animation_pivotX"

        /**
         * 指定大图启动动画中缩略图的启动中心位置。
         *
         * 大图父页面在启动大图页面来呈现图片时，可能希望大图页面会有一个从原始图片位置展开
         * 到大图页全屏的启动动画。因此需要指定这个飞入位置的中心点的Y位置。目前此参数为相机所用
         * @see [Transition.remoteSourceBounds]
         */
        private const val KEY_ANIMATION_PIVOT_Y = "animation_pivotY"

        /**
         * 指定大图反回相机动画中，缩略图顺时针转90度的坐标。目前此参数为相机所用
         *
         * @see [Transition.remoteSourceBounds]
         */
        private const val KEY_THUMBNAIL_RECT_ORIENTATION_90 = "ThumbnailRectOrientation90"

        /**
         * 指定大图反回相机动画中，缩略图顺顺时针转270度的坐标。目前此参数为相机所用
         *
         * @see [Transition.remoteSourceBounds]
         */
        private const val KEY_THUMBNAIL_RECT_ORIENTATION_270 = "ThumbnailRectOrientation270"

        /**
         * 指定大图反回相机动画中，相机缩略图的X轴圆角半径。目前此参数为相机所用
         *
         */
        private const val KEY_THUMBNAIL_CORNER_X = "ThumbnailBarCornerX"

        /**
         * 指定大图反回相机动画中，相机缩略图的Y轴圆角半径。目前此参数为相机所用
         *
         */
        private const val KEY_THUMBNAIL_CORNER_Y = "ThumbnailBarCornerY"

        /**
         * 指定大图反回相机动画中，动效的总时长。目前此参数为相机所用
         *
         */
        private const val KEY_THUMBNAIL_EXIT_ANIMATION_DURATION = "ThumbnailBarExitAnimationDuration"

        /**
         * 指定大图反回相机动画，中断动效的距离。
         * 因为反回动效，会看见相机的画面，当动效很接近相机缩略图时，提早中断动效反回相机，
         * 可以让相机Activity尽早起来，看到相机预览，缩短相机预览高斯模糊显示的时间。
         * 目前此参数为相机所用
         *
         */
        private const val KEY_THUMBNAIL_INTERRUPT_ANIMATION_DISTANCE = "ThumbnailInterruptAnimationDistance"

        /**
         * 位置动画插值器使用的坐标点
         */
        private const val POSITION_PATH_INTERPOLATOR_CONTROL_X1 = 0.3f
        private const val POSITION_PATH_INTERPOLATOR_CONTROL_Y1 = 0.0f
        private const val POSITION_PATH_INTERPOLATOR_CONTROL_X2 = 0.1f
        private const val POSITION_PATH_INTERPOLATOR_CONTROL_Y2 = 1.0f

        /**
         * 相机进出大图转场动画耗时
         */
        private const val PHOTO_PAGE_TRANSITION_DURATION = 300L

        /**
         * 位置动画默认使用的插值器
         */
        private val positionInterpolator =
            PathInterpolator(
                POSITION_PATH_INTERPOLATOR_CONTROL_X1,
                POSITION_PATH_INTERPOLATOR_CONTROL_Y1,
                POSITION_PATH_INTERPOLATOR_CONTROL_X2,
                POSITION_PATH_INTERPOLATOR_CONTROL_Y2
            )

        /**
         * 背景颜色动画插值器使用的坐标点
         */
        private const val BACKGROUND_PATH_INTERPOLATOR_CONTROL_X1 = 0.3f
        private const val BACKGROUND_PATH_INTERPOLATOR_CONTROL_Y1 = 0.0f
        private const val BACKGROUND_PATH_INTERPOLATOR_CONTROL_X2 = 0.67f
        private const val BACKGROUND_PATH_INTERPOLATOR_CONTROL_Y2 = 1.0f

        /**
         * 背景颜色动画默认使用的插值器
         */
        private val backgroundInterpolator =
            PathInterpolator(
                BACKGROUND_PATH_INTERPOLATOR_CONTROL_X1,
                BACKGROUND_PATH_INTERPOLATOR_CONTROL_Y1,
                BACKGROUND_PATH_INTERPOLATOR_CONTROL_X2,
                BACKGROUND_PATH_INTERPOLATOR_CONTROL_Y2
            )
    }
}