/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  OperationEndActionsHandler
 ** Description: 处理操作结束回调的处理器，防止嵌套回调问题
 ** Version: 1.0
 ** Date : 2023/5/10
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2023/5/10      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.preview

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 操作结束回调处理器
 * 用于处理操作结束时的回调，防止嵌套回调问题
 */
class OperationEndActionsHandler {
    /**
     * 使用线程安全的列表存储操作结束回调
     * CopyOnWriteArrayList 在遍历时不会抛出 ConcurrentModificationException
     */
    private val operationEndActions = CopyOnWriteArrayList<() -> Unit>()

    /**
     * 标记是否正在执行回调
     * 使用 AtomicBoolean 确保线程安全
     */
    private val isExecuting = AtomicBoolean(false)

    /**
     * 添加操作结束回调
     * @param action 操作结束时要执行的回调
     * @return 返回 true 表示添加成功，false 表示该回调已存在
     */
    fun addAction(action: () -> Unit): Boolean {
        // 检查是否已存在相同的回调，防止重复添加
        if (operationEndActions.contains(action)) {
            GLog.d(TAG, LogFlag.DL) { "[addAction] action already exists, skip" }
            return false
        }
        operationEndActions.add(action)
        GLog.d(TAG, LogFlag.DL) { "[addAction] after adding, size=${operationEndActions.size}" }
        return true
    }

    /**
     * 执行所有操作结束回调
     * 该方法会确保回调只被执行一次，并且防止嵌套执行
     */
    fun executeActions() {
        // 如果已经在执行中，直接返回，防止嵌套执行
        if (!isExecuting.compareAndSet(false, true)) {
            GLog.d(TAG, LogFlag.DL) { "[executeActions] already executing, skip" }
            return
        }

        try {
            val actionCount = operationEndActions.size
            GLog.d(TAG, LogFlag.DL) { "[executeActions] start executing $actionCount actions" }

            // 复制当前的操作列表，然后清空原列表
            val actionsToExecute = ArrayList(operationEndActions)
            GLog.d(TAG, LogFlag.DL) { "[executeActions] copied $actionCount actions, now clearing original list" }
            operationEndActions.clear()

            // 执行所有操作
            actionsToExecute.forEachIndexed { index, action ->
                GLog.d(TAG, LogFlag.DL) { "[executeActions] executing action ${index + 1}/$actionCount" }
                try {
                    action.invoke()
                    GLog.d(TAG, LogFlag.DL) {
                        "[executeActions] action ${index + 1}/$actionCount executed successfully"
                    }
                } catch (e: Exception) {
                    GLog.e(TAG, LogFlag.DL, e) { "[executeActions] execute action ${index + 1}/$actionCount failed" }
                }
            }

            GLog.d(TAG, LogFlag.DL) { "[executeActions] all $actionCount actions executed" }
        } finally {
            // 确保执行标志被重置
            isExecuting.set(false)

            // 如果在执行过程中有新的操作被添加，继续执行
            val newActionCount = operationEndActions.size
            if (newActionCount > 0) {
                GLog.d(TAG, LogFlag.DL) { "[executeActions] found $newActionCount new actions, continue executing" }
                executeActions()
            } else {
                GLog.d(TAG, LogFlag.DL) { "[executeActions] no new actions, execution complete" }
            }
        }
    }

    /**
     * 清空所有操作结束回调
     */
    fun clear() {
        GLog.d(TAG, LogFlag.DL) { "[clear] clear ${operationEndActions.size} actions" }
        operationEndActions.clear()
    }

    /**
     * 获取当前操作结束回调的数量
     */
    fun size(): Int = operationEndActions.size

    /**
     * 判断是否有操作结束回调
     */
    fun isEmpty(): Boolean = operationEndActions.isEmpty()

    companion object {
        private const val TAG = "OperationEndActionsHandler"
    }
}
