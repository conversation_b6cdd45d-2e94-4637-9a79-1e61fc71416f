/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Renderer.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.custom;

import android.util.Size;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.filter.EffectRenderer;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data.FxConfigInfo;
import com.meicam.sdk.NvsCustomVideoFx;


public class Renderer implements NvsCustomVideoFx.Renderer {
    private static final String TAG = "Renderer";
    private static final int MILLISECOND_TO_SECOND = 1000;

    private String mPath;
    private EffectRenderer mEffectRenderer;
    private FxConfigInfo mConfigInfo;
    private IVideoClip mTargetClip;

    public Renderer(String path) {
        mPath = path;
    }

    public Renderer setConfigInfo(FxConfigInfo mConfigInfo) {
        this.mConfigInfo = mConfigInfo;
        return this;
    }

    public Renderer setTargetClip(IVideoClip mTargetClip) {
        this.mTargetClip = mTargetClip;
        return this;
    }

    @Override
    public void onInit() {
    }

    @Override
    public void onCleanup() {

    }

    @Override
    public void onPreloadResources() {

    }

    @Override
    public void onRender(NvsCustomVideoFx.RenderContext renderContext) {
        if ((mEffectRenderer == null) || !mEffectRenderer.isProgramReady()) {
            mEffectRenderer = new EffectRenderer(mPath);
            mEffectRenderer.createProgram();
            if ((mConfigInfo != null) && (mConfigInfo.isRepeat())) {
                mEffectRenderer.setDuration(mConfigInfo.getDuration());
            }
        }
        mEffectRenderer.setTextureSize(renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
        mEffectRenderer.setInTextureId(renderContext.inputVideoFrame.texId);
        mEffectRenderer.setOutTextureId(renderContext.outputVideoFrame.texId);
        float progress = 0f;
        if ((mConfigInfo != null) && (mConfigInfo.isRepeat())) {
            long duration = mConfigInfo.getDuration() * MILLISECOND_TO_SECOND;
            progress = (float) (Math.floorMod(renderContext.effectTime - renderContext.effectStartTime, duration)) / duration;
        } else {
            progress = (float) (renderContext.effectTime - renderContext.effectStartTime) / (renderContext.effectEndTime - renderContext.effectStartTime);
        }
        GLog.d(TAG, "onRender, progress : " + progress);
        mEffectRenderer.setProgress(progress);
        setContentArea(renderContext.effectTime, renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
        mEffectRenderer.draw();
    }

    private void setContentArea(long currentTime, int maxWidth, int maxHeight) {
        if (mTargetClip != null) {
            ClipAreaCache clipAreaCache = getClipContentArea(mTargetClip, maxWidth, maxHeight);
            if (clipAreaCache == null) {
                return;
            }
            mEffectRenderer.setContentSize(clipAreaCache.mContentAreaWidth, clipAreaCache.mContentAreaHeight);
        }
    }

    private ClipAreaCache getClipContentArea(IVideoClip videoClip, int maxWidth, int maxHeight) {
        ClipAreaCache clipAreaCache = new ClipAreaCache();
        clipAreaCache.mCurrentClip = videoClip;
        if ((videoClip == null) || (maxHeight == 0)) {
            GLog.d(TAG, "getClipContentArea failed,(videoClip == null):" + (videoClip == null));
            return clipAreaCache;
        }

        // 获取图片/视频的原始宽高
        Size size = videoClip.getFileVideoSize();
        if ((size == null) || (size.getHeight() == 0) || (size.getWidth() == 0)) {
            GLog.d(TAG, "getClipContentArea getSize is null:" + (size == null));
            return clipAreaCache;
        }

        // 按照规则计算素材应该在timeline内的宽高
        double targetRatio = ((double) maxWidth) / maxHeight;
        double videoOriginalRatio = ((double) size.getWidth()) / size.getHeight();
        boolean resizeByWidth = targetRatio - videoOriginalRatio < 0; //是根据宽还是高来重新计算素材的内容宽高
        if (resizeByWidth) {
            clipAreaCache.mContentAreaWidth = maxWidth;
            clipAreaCache.mContentAreaHeight = (int) (maxWidth / videoOriginalRatio);
        } else {
            clipAreaCache.mContentAreaHeight = maxHeight;
            clipAreaCache.mContentAreaWidth = (int) (maxHeight * videoOriginalRatio);
        }
        return clipAreaCache;
    }

    static class ClipAreaCache {
        IVideoClip mCurrentClip = null;
        int mContentAreaWidth;
        int mContentAreaHeight;
    }
}
