/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - RuleScrollerView.java
 * * Description: XXXXXXXXXXXXXXX
 * * Version: 1.0
 * * Date : 2020/04/22
 * * Author: zhangpeng5@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>  <data>    <version >        <desc>
 * *  zhangpeng5@Apps.Gallery3D       2020/03/06    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.widget;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Display;
import android.view.GestureDetector;
import android.view.GestureDetector.OnGestureListener;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.Interpolator;
import android.view.animation.PathInterpolator;
import android.view.animation.Transformation;
import android.widget.LinearLayout;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.coui.appcompat.animation.COUIPhysicalAnimationUtil;
import com.coui.appcompat.scroll.SpringOverScroller;
import com.oplus.gallery.addon.os.VibratorUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.math.MathUtil;
import com.oplus.gallery.foundation.util.typeface.FontSizeUtil;
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * markby <EMAIL> 2025/04/14 从相册photoeditor迁移过来，后续进行重构
 */
public class RuleScrollerView extends View {
    private static final String TAG = "RuleScrollerView";
    private static final float DISABLE_ALPHA_PERCENT = 0.5f;
    private static final float ENABLE_ALPHA_PERCENT = 1f;
    private static final float NORMAL_LINE_ALPHA_PERCENT = 0.5f;
    private static final float POINT_ALPHA_PERCENT = 0.3f;
    private static final float WHOLE_LINE_ALPHA_PERCENT = 0.8f;
    private static final float TEXT_PAIN_ALPHA_PERCENT = 0.55f;
    private static final float NORMAL_FRAME_ALPHA_PERCENT = 0.1f;
    private static final float NORMAL_WHOLE_FRAME_ALPHA_PERCENT = 0.2f;
    private static final float FLING_PARAMS = 2f / 3f;
    private static final int MAX_ALPHA = 255;
    private static final int MAX_TEXT_PAIN_ALPHA = (int) (MAX_ALPHA * TEXT_PAIN_ALPHA_PERCENT);
    private static final float HALF = 0.5f;
    private static final float DISMISS_VALUE = 0.5f;
    private static final int ANIMATION_TIME = 430;
    private static final int ANIMATION_DISMISS_TIME = 80;
    private static final int ANIMATION_APPEAR_TIME = 350;
    private static final float PAINT_SHADOW_SIZE = 12f;
    private static final float POINT_DISMISS_VALUE = 0.1f;
    private static final float ONE_HALF_FLOAT = 0.5f;
    private static final float SPRING_OVER_TENSION_HORIZONTAL = 3.2F;
    private static final float SPRING_OVER_TENSION_VERTICAL = 2.15F;
    private static final int DRAW_OFFSET = 0;
    private static final int VELOCITY_UNITS = 1000;
    private static final float MIN_VELOCITY_DISTANCE = 50f;
    private static final long SCROLL_TO_DEFAULT_DELAY = 50L;
    private static final float CENTER_LINE_BORDER_INTOUCH = 0.66F; // 中心线按压时描边宽度
    private static final float MIN_SCALE_DEFAULT = 1.0f;

    // 刻度线对齐方式
    private static final int SCALE_LINE_ALIGNMENT_CENTER = 0;
    private static final int SCALE_LINE_ALIGNMENT_START = 1;
    private static final int SCALE_LINE_ALIGNMENT_END = 2;

    @IntDef({SCALE_LINE_ALIGNMENT_CENTER, SCALE_LINE_ALIGNMENT_START, SCALE_LINE_ALIGNMENT_END})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ScaleLineAlignment {}

    private final RuleAnimation mRuleAnimation = new RuleAnimation();
    private final Interpolator mPointPathInterpolator = new PathInterpolator(0.33f, 0f, 0.67f, 1f);
    private final Interpolator mScrollToPositionPathInterpolator = new PathInterpolator(0.3f, 0f, 0f, 1f);
    private final ViewFlinger mViewFlinger = new ViewFlinger();
    private Paint mWholePaint;
    private Paint mIndicatorPaint;
    private Paint mPressedIndicatorPaint; // 按下状态的画笔
    private Paint mPaint;
    private Paint mPointPaint;
    private Paint mTextPaint;
    private Paint mWholeFramePaint;
    private Paint mFramePaint;

    private int mStartValue;
    private int mEndValue;
    private int mMinPrecise;
    private float mMinScaleValue;
    private int mMinPrecisePixel;
    private int mCurrentWidth;
    private int mCurrentHeight;
    private int mMinRuleTotalNumber;
    private float mLastMinRuleValue;
    private int mLineWidth;
    private int mLineHeight;
    private int mMaxLineHeight;
    private int mPointTop;
    private int mIndicatorHeight;
    private int mOrientation = LinearLayout.HORIZONTAL;
    private int mFloatingTextMargin;
    private int mStartPadding;
    private float mTextSize;
    private int mTextMarginTop;
    private int mWholeMinSpaceNumber;
    private int mRuleViewMarginBottom;
    private int mPointMarginBottom;
    private float mFrameWidth;
    private int mValue = Integer.MIN_VALUE;
    private boolean mNeedHighPrecision = false;
    private OnSelectValueChangeListener mOnSelectValueChangeListener;
    private boolean mIsParamsChange = true;
    private int mRuleTotalLength;
    private SpringOverScroller mOverScroller;
    private GestureDetector mGestureDetector;
    private boolean mIsFromUser = true;
    private int mWholeLinePaintColor;
    private int mPaintColor;
    private int mFramePaintColor;
    private int mWholeFramePaintColor;
    private int mShadowColor;
    private int mTextColor;
    private String mUnitString;
    private int mVerticalTextYOffset = 0;
    private boolean mIsUnevenScales = false;
    private int mThemeColor = ThemeHelper.getCouiColorContainerTheme(getContext());

    /* mInterval 用来设置不规则调节尺的大小间隔 */
    private int mInterval = 0;

    /* 刻度线对齐方式 */
    @ScaleLineAlignment
    private int mScaleLineAlignment = SCALE_LINE_ALIGNMENT_CENTER;

    private int mDefaultIndex = Integer.MIN_VALUE;
    private float mDefaultPointValue = 0f;
    private AnimatorSet mScrollAnimatorSet;
    private long mScrollToPositionAnimatorDuration = 300L;
    private long mPointAnimatorDuration = 180L;
    private float mPointRadius;
    private PointAnimation mPointAnimation;
    private int mOverscrollDistance;
    private boolean mShowCenterLineAndPoint = true;
    /**
     * 上次的吸附状态
     */
    private boolean mLastAdsorption = false;
    /**
     * 判断是否是从默认白点位置开始滑动
     */
    private boolean mIsFromDefaultStart = false;
    /**
     * 用于追踪手指滑动速度
     */
    private VelocityTracker mVelocityTracker = null;
    /**
     * 标尺实际滑动距离
     */
    private int mScrollDistance = -1;
    /**
     * 判断是否在惯性滑动
     */
    private boolean mIsInertialScroll = false;

    /**
     * 判断是否在触摸中
     */
    private boolean mIsInTouch = false;

    public boolean isInTouch() {
        return mIsInTouch;
    }

    public interface OnSelectValueChangeListener {

        boolean canSelectValueChange();

        void onStartChangeValue();

        void onSelectValueChanged(int value, boolean isFromUser);

        void onChangeValueComplete(int finalValue);
    }

    public RuleScrollerView(Context context) {
        this(context, null);
    }

    public RuleScrollerView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RuleScrollerView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initDefaultParams();
        initAttr(context, attrs);
        init();
    }

    public void setOrientation(@OrientationMode int orientation) {
        if (mOrientation == orientation) {
            return;
        }
        boolean fadingEdgeEnable = (isVertical() && isVerticalFadingEdgeEnabled()) || (isHorizontal() && isHorizontalFadingEdgeEnabled());
        mOrientation = orientation;
        if (fadingEdgeEnable) {
            if (isVertical()) {
                setVerticalFadingEdgeEnabled(true);
                setHorizontalFadingEdgeEnabled(false);
            } else {
                setVerticalFadingEdgeEnabled(false);
                setHorizontalFadingEdgeEnabled(true);
            }
        }
        initRuleParams();
        initOverscrollDistanceByOrientation(getContext());
        initOverScroller();
        requestLayout();
    }

    public void setStartValue(int value) {
        mIsParamsChange = (mStartValue != value);
        mStartValue = value;
        if (mIsParamsChange) {
            initRuleParams();
        }
        invalidate();
    }

    public int getStartValue() {
        return mStartValue;
    }

    public void setEndValue(int value) {
        mIsParamsChange = (mEndValue != value);
        mEndValue = value;
        if (mIsParamsChange) {
            initRuleParams();
        }
        invalidate();
    }

    public int getEndValue() {
        return mEndValue;
    }

    public void setMinPrecise(int value) {
        mIsParamsChange = (mMinPrecise != value);
        mMinPrecise = value;
        invalidate();
    }

    public int getMinPrecise() {
        return mMinPrecise;
    }

    public void setMinScaleValue(float value) {
        mIsParamsChange = (mMinScaleValue != value);
        mMinScaleValue = value;
    }

    public float getMinScaleValue() {
        return mMinScaleValue;
    }

    public void setMinPrecisePixel(int value) {
        mIsParamsChange = (mMinPrecisePixel != value);
        mMinPrecisePixel = value;
        invalidate();
    }

    public int getScrollerIndex() {
        return mScrollDistance / mMinPrecisePixel;
    }

    public void setWholeMinSpaceNumber(int number) {
        mIsParamsChange = (mWholeMinSpaceNumber != number);
        mWholeMinSpaceNumber = number;
        invalidate();
    }

    public int getWholeMinSpaceNumber() {
        return mWholeMinSpaceNumber;
    }

    public void setWholePaintColor(int color) {
        mWholeLinePaintColor = color;
        invalidate();
    }

    public int getWholePaintColor() {
        return mWholeLinePaintColor;
    }

    public void setTextColor(int color) {
        mTextColor = color;
        invalidate();
    }

    public int getTextColor() {
        return mTextColor;
    }

    public void setTextSize(float size) {
        mTextSize = size;
        mTextPaint.setTextSize(mTextSize);
        updateVerticalTextYOffset();
        invalidate();
    }

    public float getTextSize() {
        return mTextSize;
    }

    public void stopFling() {
        if ((mOverScroller != null)) {
            mViewFlinger.stop();
        }
    }

    /**
     * 设置默认选中刻度
     *
     * @param index 位置
     */
    public void setDefaultIndex(int index) {
        mIsParamsChange = (mDefaultIndex != index);
        mDefaultIndex = index;
        if (mIsUnevenScales) {
            mDefaultPointValue = index;
        } else {
            mDefaultPointValue = (mEndValue - mStartValue) * mDefaultIndex / (mMinRuleTotalNumber - 1) + mStartValue;
        }
    }

    /**
     * 是否需要高精度值
     *
     * @param needHighPrecision 默认false非高精度，true时为高精度
     */
    public void setNeedHighPrecision(boolean needHighPrecision) {
        this.mNeedHighPrecision = needHighPrecision;
    }

    /**
     * 设置刻度线对齐方式
     *
     * @param alignment 对齐方式，可选值为 SCALE_LINE_ALIGNMENT_CENTER、SCALE_LINE_ALIGNMENT_START 或 SCALE_LINE_ALIGNMENT_END
     */
    public void setScaleLineAlignment(@ScaleLineAlignment int alignment) {
        if (mScaleLineAlignment != alignment) {
            mScaleLineAlignment = alignment;
            invalidate();
        }
    }

    /**
     * 获取当前刻度线对齐方式
     *
     * @return 当前对齐方式
     */
    @ScaleLineAlignment
    public int getScaleLineAlignment() {
        return mScaleLineAlignment;
    }

    public void setCurrentValue(int value) {
        if ((mValue == value) && !mIsParamsChange) {
            return;
        }
        mValue = value;
        if (mValue > mEndValue) {
            mValue = mStartValue;
        }
        invalidate();
        initRuleParams();
        int sx = getScrollXByValue(mValue);
        int sy = getScrollYByValue(mValue);
        mIsFromUser = false;
        if ((mScrollAnimatorSet != null) && mScrollAnimatorSet.isRunning()) {
            mScrollAnimatorSet.cancel();
        }
        scrollTo(sx, sy);
    }

    /**
     * 此方法用于适配最小值为<0的场景下,需要主动刷新mValue相关的值， 让光标能scrollTo到正确的位置。
     *
     * 原来有问题的场景:
     * 如从startvalue=0的项，切到startvalue为-100项（或者从startvalue=-100的项切到startvalue=0项时），
     * 会调用原来本类的setCurrentValue()方法，而这个方法中会触发return,导致-100的项的ruleview的
     * 当前位置跟上一项的位置一样，保持在最左边不刷新(对于startvalue=0的项来说，最左边是0，
     * 而实际上对于-100的项来说，最左边的位置其实是-100），所以此时需要刷新位置到0刻度，即ruleview的中央，而不是显示在
     * 最左边
     *
     * 此方法在本类的setCurrentValue()基础上删除了开始的return逻辑，这个逻辑会影响上面的问题场景，因为这部分逻辑是历史
     * 共用，为了不影响其它的功能所以单独写一个方法，
     * 目前只给EditorAiIDPhotoBeautyUIController->updateCurrentValue(currProgress)使用，
     * 不会影响其它场景
     */
    public void updateCurrentValue(int value) {
        mValue = value;
        if (mValue > mEndValue) {
            mValue = mStartValue;
        }
        invalidate();
        initRuleParams();
        int sx = getScrollXByValue(mValue);
        int sy = getScrollYByValue(mValue);
        mIsFromUser = false;
        scrollTo(sx, sy);
    }

    public void scrollToPosition(int x, int y, boolean isFast) {

        ObjectAnimator xTranslator = ObjectAnimator.ofInt(this, "scrollX", x);
        ObjectAnimator yTranslator = ObjectAnimator.ofInt(this, "scrollY", y);

        mScrollAnimatorSet = new AnimatorSet();
        mScrollAnimatorSet.setDuration(isFast ? SCROLL_TO_DEFAULT_DELAY : mScrollToPositionAnimatorDuration);
        mScrollAnimatorSet.setInterpolator(mScrollToPositionPathInterpolator);
        mScrollAnimatorSet.playTogether(xTranslator, yTranslator);
        mScrollAnimatorSet.start();
    }

    public int getCurrentValue() {
        return mValue;
    }

    public float getHighPrecisionValue() {
        return getRealValueByScrollerValue(getScrollDistance());
    }

    public void setOnSelectValueChangeListener(OnSelectValueChangeListener listener) {
        mOnSelectValueChangeListener = listener;
    }

    public boolean isIdle() {
        return (mScrollAnimatorSet == null || !mScrollAnimatorSet.isRunning()) && !mIsInTouch;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isEnabled()) {
            return false;
        }
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
        mVelocityTracker.addMovement(event);
        mVelocityTracker.computeCurrentVelocity(VELOCITY_UNITS);
        int actionId = event.getAction();
        boolean handled = mGestureDetector.onTouchEvent(event);
        switch (actionId) {
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                handleTouchUpOrCancel(event, handled);
                break;
            case MotionEvent.ACTION_MOVE:
                handleTouchMove();
                break;
            case MotionEvent.ACTION_DOWN:
                handleTouchDown();
                break;
            default:
                break;
        }
        return true;
    }

    private void handleTouchUpOrCancel(MotionEvent event, boolean handled) {
        mIsInTouch = false;
        mIsInertialScroll = true;
        invalidate();
        if (!handled) {
            overScrollerSpringBack();
        }
        if (canSelectValueChange()) {
            if (mOverScroller.isCOUIFinished()) {
                int finalValue = getValueByScrollerValue(getScrollDistance());
                selectValueChangeComplete(finalValue);
                if (mIsUnevenScales) {
                    scrollToValue(finalValue, false);
                }
                mValue = finalValue;
            } else {
                int x = isVertical() ? mOverScroller.getCOUIFinalY() : mOverScroller.getCOUIFinalX();
                int finalValue = getValueByScrollerValue(x);
                selectValueChangeComplete(finalValue);
                mValue = finalValue;
            }
            // computeScrollOffset为false代表结束了惯性滑动，值不再变化
            if (!mOverScroller.computeScrollOffset() && !mIsUnevenScales) {
                scrollToValue(mValue, false);
                mIsInertialScroll = false;
            }
        }
        selectValueChanged(mValue, true);
        mVelocityTracker.recycle();
        mVelocityTracker = null;
        mLastAdsorption = false;
    }

    private void handleTouchMove() {
        int scrollDistance = isReversePrint() ? mRuleTotalLength - getScrollDistance() : getScrollDistance();
        int absDistance = Math.abs(scrollDistance - mDefaultIndex * mMinPrecisePixel);
        if (absDistance > mMinPrecisePixel) {
            mIsFromDefaultStart = false;
            mLastAdsorption = false;
        }
        if (!mLastAdsorption && (mVelocityTracker != null)
                && (mVelocityTracker.getXVelocity() < MIN_VELOCITY_DISTANCE)
                && (absDistance <= mMinPrecisePixel)
                && !mIsFromDefaultStart) {
            mLastAdsorption = true;
            scrollToDefaultValue();
        }
    }

    private void handleTouchDown() {
        mIsInTouch = true;
        mIsInertialScroll = false;
        invalidate();
        int startDistance = isReversePrint() ? mRuleTotalLength - getScrollDistance() : getScrollDistance();
        if (Math.abs(startDistance - Math.round(mDefaultIndex * mMinPrecisePixel)) <= mMinPrecisePixel) {
            mIsFromDefaultStart = true;
        }
    }

    private void scrollToDefaultValue() {
        scrollToValue(Math.round(mDefaultPointValue), true);
        selectValueChanged(mValue, true);
        postDelayed(() -> {
            VibratorUtils.vibrateInSubThread(ContextGetter.context, VibratorUtils.EffectType.VIBRATE_TYPE_WEAKEST, true);
            mValue = Math.round(mDefaultPointValue);
            selectValueChanged(mValue, true);
            invalidate();
        }, SCROLL_TO_DEFAULT_DELAY);
    }

    @Override
    public void computeScroll() {
        if (mOverScroller.computeScrollOffset()) {
            scrollTo(mOverScroller.getCOUICurrX(), mOverScroller.getCOUICurrY());
            invalidate();
        } else if (canSelectValueChange()) {
            int x = isVertical() ? mOverScroller.getCOUICurrY() : mOverScroller.getCOUICurrX();
            int value = getValueByScrollerValue(x);
            if (mValue == value) {
                selectValueChangeComplete(value);
            }
        }
        /*
        computeScrollOffset为false代表结束了惯性滑动，值不再变化；
        若正在惯性滑动的值mIsInertialScroll为true，此时若已结束惯性滑动则设置滑动到整点的值并将mIsInertialScroll的值设置成false。
         */
        if (mIsInertialScroll && !mOverScroller.computeScrollOffset() && !mIsUnevenScales) {
            scrollToValue(mValue, false);
            mIsInertialScroll = false;
        }
    }

    @Override
    public void setEnabled(boolean enabled) {
        clearAnimation();
        if (!enabled) {
            stopFling();
        }
        super.setEnabled(enabled);
    }

    /**
     * 设置画笔透明度
     *
     * @param percentage 透明度百分比
     */
    private void setPaintAlpha(float percentage) {
        mIndicatorPaint.setAlpha((int) (MAX_ALPHA * percentage));
        mWholePaint.setAlpha((int) (MAX_ALPHA * WHOLE_LINE_ALPHA_PERCENT * percentage));
        mTextPaint.setAlpha((int) (MAX_TEXT_PAIN_ALPHA * percentage));
        mPaint.setAlpha((int) (MAX_ALPHA * NORMAL_LINE_ALPHA_PERCENT * percentage));
    }

    /**
     * 仅改变调节尺的透明度，可滑动。
     *
     * @param enabled 是否已启用
     */
    public void setEnablePaintAlpha(boolean enabled) {
        clearAnimation();
        setPaintAlpha(enabled ? ENABLE_ALPHA_PERCENT : DISABLE_ALPHA_PERCENT);
        mShowCenterLineAndPoint = enabled;
        invalidate();
    }

    public void updateWithAnimation() {
        mRuleAnimation.setDuration(ANIMATION_TIME);
        this.startAnimation(mRuleAnimation);
        if (isReversePrint()) {
            mScrollDistance = Math.round((mEndValue - mValue) * mMinPrecisePixel / mMinScaleValue);
        } else {
            mScrollDistance = Math.round((mValue - mStartValue) * mMinPrecisePixel / mMinScaleValue);
        }
    }

    @Override
    protected float getLeftFadingEdgeStrength() {
        return (!isVertical() && isHorizontalFadingEdgeEnabled()) ? 1f : 0f;
    }

    @Override
    protected float getRightFadingEdgeStrength() {
        return getLeftFadingEdgeStrength();
    }

    @Override
    protected float getTopFadingEdgeStrength() {
        return (isVertical() && isVerticalFadingEdgeEnabled()) ? 1f : 0f;
    }

    @Override
    protected float getBottomFadingEdgeStrength() {
        return getTopFadingEdgeStrength();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if ((getWidth() != mCurrentWidth) || (getHeight() != mCurrentHeight)) {
            mCurrentWidth = getWidth();
            mCurrentHeight = getHeight();
            mIsParamsChange = true;
        }
        if (changed) {
            mIsFromUser = false;
            scrollTo(getScrollXByValue(mValue), getScrollYByValue(mValue));
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if ((getWidth() <= 0) || (getHeight() <= 0)) {
            return;
        }
        initRuleParams();

        int currentPosition = getScrollDistance();
        int sideDrawNumber = (isVertical() ? mCurrentHeight : mCurrentWidth) / 2 / mMinPrecisePixel + 1;

        int ruleBottom = Math.max(mLineHeight, mIndicatorHeight);
        int lineDrawPadding = mLineWidth / 2;

        drawNormalRule(canvas, currentPosition, sideDrawNumber, ruleBottom, lineDrawPadding);
        if (mIsUnevenScales) {
            updatePointWithAnimation(getCurrentValue() != mDefaultIndex);
            drawPoint(canvas);
        } else {
            updatePointWithAnimation(mScrollDistance == mDefaultIndex * mMinPrecisePixel);
            drawPoint(canvas);
        }
        drawCenterLine(canvas, lineDrawPadding);
    }

    @Override
    protected void onScrollChanged(int left, int top, int oldl, int oldt) {
        super.onScrollChanged(left, top, oldl, oldt);
        if ((mScrollAnimatorSet != null) && mScrollAnimatorSet.isRunning()) {
            return;
        }
        int value = getValueByScrollerValue((isVertical()) ? top : left);
        if (!mNeedHighPrecision && (value == mValue)) {
            return;
        }
        mValue = value;
        if (isReversePrint()) {
            mScrollDistance = Math.round((mEndValue - mValue) * mMinPrecisePixel / mMinScaleValue);
        } else {
            mScrollDistance = Math.round((mValue - mStartValue) * mMinPrecisePixel / mMinScaleValue);
        }
        selectValueChanged(mValue, mIsFromUser);
        boolean finalXY = (isVertical()) ? (mOverScroller.getCOUIFinalY() == top) : (mOverScroller.getCOUIFinalX() == left);
        if (finalXY) {
            stopFling();
            if (mIsUnevenScales) {
                scrollToValue(mValue, false);
            }
        }
    }

    private int getScrollDistance() {
        int scrollDistance = getScrollPositionByOrientation();
        mScrollDistance = isReversePrint() ? mRuleTotalLength - scrollDistance : scrollDistance;
        return MathUtil.clamp(scrollDistance, 0, mRuleTotalLength);
    }

    /**
     * 横屏的rtl模式及竖屏需要将起点终点交换显示
     *
     * @return
     */
    private boolean isReversePrint() {
        return isRtl() || isVertical();
    }

    private void drawCenterLine(Canvas canvas, int lineDrawPadding) {
        if (!mShowCenterLineAndPoint) {
            return;
        }

        Paint currentIndicatorPaint = mIsInTouch ? mPressedIndicatorPaint : mIndicatorPaint;

        if (isVertical()) {
            int top = getScrollY() + mStartPadding - lineDrawPadding - DRAW_OFFSET;
            int bottom = getScrollY() + mStartPadding + lineDrawPadding;
            int left = 0;
            int right = 0;

            // 根据对齐方式设置中心线的位置
            switch (mScaleLineAlignment) {
                case SCALE_LINE_ALIGNMENT_START:
                    left = 0;
                    right = mIndicatorHeight;
                    break;
                case SCALE_LINE_ALIGNMENT_END:
                    left = mCurrentWidth - mIndicatorHeight;
                    right = mCurrentWidth;
                    break;
                case SCALE_LINE_ALIGNMENT_CENTER:
                default:
                    left = (mCurrentWidth - mIndicatorHeight) / 2;
                    right = (mCurrentWidth + mIndicatorHeight) / 2;
                    break;
            }

            canvas.drawRect(left, top, right, bottom, mWholeFramePaint);
            canvas.drawRect(left, top, right, bottom, currentIndicatorPaint);
        } else {
            int left = getScrollX() + mStartPadding - lineDrawPadding - DRAW_OFFSET;
            int right = getScrollX() + mStartPadding + lineDrawPadding;
            int top = 0;
            int bottom = 0;

            // 根据对齐方式设置中心线的位置
            switch (mScaleLineAlignment) {
                case SCALE_LINE_ALIGNMENT_START:
                    top = 0;
                    bottom = mIndicatorHeight;
                    break;
                case SCALE_LINE_ALIGNMENT_END:
                    top = mCurrentHeight - mIndicatorHeight - mRuleViewMarginBottom;
                    bottom = mCurrentHeight - mRuleViewMarginBottom;
                    break;
                case SCALE_LINE_ALIGNMENT_CENTER:
                default:
                    top = (mCurrentHeight - mIndicatorHeight - mRuleViewMarginBottom) / 2;
                    bottom = (mCurrentHeight + mIndicatorHeight - mRuleViewMarginBottom) / 2;
                    break;
            }

            canvas.drawRect(left, top, right, bottom, mWholeFramePaint);
            canvas.drawRect(left, top, right, bottom, currentIndicatorPaint);
        }
    }

    private String getShowValueUnitString(int value) {
        if (TextUtils.isEmpty(mUnitString)) {
            return String.valueOf(value);
        }
        return " " + value + mUnitString;
    }

    private class RuleAnimation extends Animation {
        @Override
        protected void applyTransformation(float interpolatedTime, Transformation t) {
            int alpha = 0;
            if (interpolatedTime < (float) ANIMATION_DISMISS_TIME / ANIMATION_TIME) {
                alpha = (int) ((1f - interpolatedTime * ANIMATION_TIME / ANIMATION_DISMISS_TIME
                        * (1f - DISMISS_VALUE)) * MAX_ALPHA);
            } else {
                alpha = (int) ((DISMISS_VALUE + (1f - DISMISS_VALUE) * (interpolatedTime - (float) ANIMATION_DISMISS_TIME
                        / ANIMATION_TIME) / ((float) ANIMATION_APPEAR_TIME / ANIMATION_TIME)) * MAX_ALPHA);
            }
            mPaint.setAlpha((int) (alpha * NORMAL_LINE_ALPHA_PERCENT));
            mTextPaint.setAlpha(Math.min(alpha, MAX_TEXT_PAIN_ALPHA));
            mWholePaint.setAlpha((int) (alpha * WHOLE_LINE_ALPHA_PERCENT));
            invalidate();
        }
    }

    private class PointAnimation extends Animation {
        private boolean mFadeIn;

        PointAnimation(boolean fadeIn) {
            this.mFadeIn = fadeIn;
        }

        @Override
        protected void applyTransformation(float interpolatedTime, Transformation t) {
            mPointPaint.setAlpha((int) (((mFadeIn) ? interpolatedTime : (1 - interpolatedTime))
                    * POINT_ALPHA_PERCENT * MAX_ALPHA));
            invalidate();
        }
    }

    /**
     * 更新调节尺小白点显示动画
     *
     * @param fadeIn true 淡入；false 淡出
     */
    private void updatePointWithAnimation(boolean fadeIn) {
        boolean needStart = false;
        if (mPointAnimation == null) {
            needStart = true;
        } else if (mPointAnimation.mFadeIn != fadeIn) {
            mPointAnimation.cancel();
            needStart = true;
        }

        if (needStart) {
            mPointAnimation = new PointAnimation(fadeIn);
            mPointAnimation.setDuration(mPointAnimatorDuration);
            mPointAnimation.setInterpolator(mPointPathInterpolator);
            this.startAnimation(mPointAnimation);
        }
    }

    private void scrollToValue(int value, boolean isFast) {
        int sx = getScrollXByValue(value);
        int sy = getScrollYByValue(value);
        if ((mScrollAnimatorSet != null) && mScrollAnimatorSet.isRunning()) {
            mScrollAnimatorSet.end();
            mScrollAnimatorSet = null;
        }
        scrollToPosition(sx, sy, isFast);
    }

    private void selectValueChangeComplete(int finalValue) {
        if (mOnSelectValueChangeListener != null) {
            mOnSelectValueChangeListener.onChangeValueComplete(MathUtil.clamp(finalValue, mStartValue, mEndValue));
        }
    }

    private void selectValueChanged(int value, boolean isFromUser) {
        if (mOnSelectValueChangeListener != null) {
            mOnSelectValueChangeListener.onSelectValueChanged(MathUtil.clamp(value, mStartValue, mEndValue), isFromUser);
            mIsFromUser = true;
        }
    }

    private void selectValueStart() {
        if (mOnSelectValueChangeListener != null) {
            mOnSelectValueChangeListener.onStartChangeValue();
        }
    }

    private int getValueByScrollerValue(int x) {
        return Math.round(getRealValueByScrollerValue(x));
    }

    private float getRealValueByScrollerValue(int x) {
        float value = 0;
        int len = (mMinRuleTotalNumber - 1) * mMinPrecisePixel;
        int adjustedX = x;
        if (isReversePrint()) {
            adjustedX = len - x;
        }
        int startIndex = 0;
        int remain = 0;
        if (mMinPrecisePixel != 0) {
            remain = adjustedX % mMinPrecisePixel;
            startIndex = adjustedX / mMinPrecisePixel;
        }

        if (startIndex == (mMinRuleTotalNumber - 1)) {
            value = mEndValue;
        } else if (startIndex == (mMinRuleTotalNumber - 2) && mMinPrecisePixel != 0) {
            if (mIsUnevenScales) {
                value = mStartValue + startIndex * mMinPrecise + (float) remain * mLastMinRuleValue
                        / mMinPrecisePixel;
            } else {
                value = mStartValue + startIndex * mMinScaleValue + (float) remain * mLastMinRuleValue
                        / mMinPrecisePixel;
            }
        } else if (mMinPrecisePixel != 0) {
            if (mIsUnevenScales) {
                value = mStartValue + ((float) x * mMinPrecise / mMinPrecisePixel);
            } else {
                value = mStartValue + ((float) x * mMinScaleValue / mMinPrecisePixel);
            }
        }
        return value;
    }

    private int getScrollYByValue(int value) {
        return isVertical() ? getScrollByValue(value) : 0;
    }

    private int getScrollXByValue(int value) {
        return isVertical() ? 0 : getScrollByValue(value);
    }

    private int getScrollByValue(int value) {
        int startIndex = 0;
        float remain = 0;
        if (mMinScaleValue != 0) {
            startIndex = (int) ((value - mStartValue) / mMinScaleValue);
            remain = (value - mStartValue) % mMinScaleValue;
        }
        if (mIsUnevenScales && (mMinPrecise != 0)) {
            startIndex = (value - mStartValue) / mMinPrecise;
            remain = (value - mStartValue) % mMinPrecise;
        }
        int x = 0;
        int len = (mMinRuleTotalNumber - 1) * mMinPrecisePixel;
        if (startIndex == (mMinRuleTotalNumber - 1)) {
            x = len;
        } else if ((startIndex == (mMinRuleTotalNumber - 2)) && (mLastMinRuleValue != 0)) {
            x = Math.round(startIndex * mMinPrecisePixel + remain * mMinPrecisePixel / mLastMinRuleValue);
        } else if (mIsUnevenScales && (mMinPrecise != 0)) {
            x = Math.round(startIndex * mMinPrecisePixel + remain * mMinPrecisePixel / mMinPrecise);
        } else if (mMinScaleValue != 0) {
            x = Math.round(startIndex * mMinPrecisePixel + remain * mMinPrecisePixel / mMinScaleValue);
        }
        if (isReversePrint()) {
            x = len - x;
        }
        return x;
    }

    private void drawNormalRule(
            Canvas canvas,
            int currentPosition,
            int sideDrawNumber,
            int ruleBottom,
            int lineDrawPadding
    ) {
        int lineIndex = currentPosition / mMinPrecisePixel;
        int start = lineIndex - sideDrawNumber;
        start = Math.max(start, 0);
        int end = lineIndex + sideDrawNumber;
        end = (end < mMinRuleTotalNumber) ? end : (mMinRuleTotalNumber - 1);
        for (int i = start; i <= end; i++) {
            // 获取线条的坐标信息
            RectCoords coords = calculateLineCoordinates(i, lineDrawPadding);

            // 确定使用的画笔
            PaintInfo paintInfo = determinePaints(i);

            // 绘制线条和边框
            drawLineAndFrame(canvas, coords, paintInfo.mPaint, paintInfo.mFramePaint);
        }
    }

    private RectCoords calculateLineCoordinates(int index, int lineDrawPadding) {
        RectCoords coords = new RectCoords();

        if (isVertical()) {
            calculateVerticalLineCoordinates(coords, index, lineDrawPadding);
        } else {
            calculateHorizontalLineCoordinates(coords, index, lineDrawPadding);
        }

        return coords;
    }

    private void calculateVerticalLineCoordinates(RectCoords coords, int index, int lineDrawPadding) {
        if (mIsUnevenScales && (mInterval != 0)) {
            int lineLength = (index % mInterval == 0) ? mMaxLineHeight : mLineHeight;

            switch (mScaleLineAlignment) {
                case SCALE_LINE_ALIGNMENT_START:
                    coords.mLeft = 0;
                    coords.mRight = lineLength;
                    break;
                case SCALE_LINE_ALIGNMENT_END:
                    coords.mLeft = mCurrentWidth - lineLength;
                    coords.mRight = mCurrentWidth;
                    break;
                case SCALE_LINE_ALIGNMENT_CENTER:
                default:
                    coords.mLeft = (mCurrentWidth - lineLength) / 2;
                    coords.mRight = mCurrentWidth - (mCurrentWidth - lineLength) / 2;
                    break;
            }
        } else {
            switch (mScaleLineAlignment) {
                case SCALE_LINE_ALIGNMENT_START:
                    coords.mLeft = 0;
                    coords.mRight = mLineHeight;
                    break;
                case SCALE_LINE_ALIGNMENT_END:
                    coords.mLeft = mCurrentWidth - mLineHeight;
                    coords.mRight = mCurrentWidth;
                    break;
                case SCALE_LINE_ALIGNMENT_CENTER:
                default:
                    coords.mLeft = (mCurrentWidth - mLineHeight) / 2;
                    coords.mRight = mCurrentWidth - (mCurrentWidth - mLineHeight) / 2;
                    break;
            }
        }
        coords.mTop = mStartPadding + mMinPrecisePixel * index - lineDrawPadding - DRAW_OFFSET;
        coords.mBottom = mStartPadding + mMinPrecisePixel * index + lineDrawPadding;
    }

    private void calculateHorizontalLineCoordinates(RectCoords coords, int index, int lineDrawPadding) {
        if (mIsUnevenScales && (mInterval != 0)) {
            int lineLength = (index % mInterval == 0) ? mMaxLineHeight : mLineHeight;

            switch (mScaleLineAlignment) {
                case SCALE_LINE_ALIGNMENT_START:
                    coords.mTop = 0;
                    coords.mBottom = lineLength;
                    break;
                case SCALE_LINE_ALIGNMENT_END:
                    coords.mTop = mIndicatorHeight - lineLength;
                    coords.mBottom = mIndicatorHeight;
                    break;
                case SCALE_LINE_ALIGNMENT_CENTER:
                default:
                    coords.mTop = (mIndicatorHeight - lineLength) / 2;
                    coords.mBottom = mIndicatorHeight - (mIndicatorHeight - lineLength) / 2;
                    break;
            }
        } else {
            switch (mScaleLineAlignment) {
                case SCALE_LINE_ALIGNMENT_START:
                    coords.mTop = 0;
                    coords.mBottom = mLineHeight;
                    break;
                case SCALE_LINE_ALIGNMENT_END:
                    coords.mTop = mCurrentHeight - mLineHeight - mRuleViewMarginBottom;
                    coords.mBottom = mCurrentHeight - mRuleViewMarginBottom;
                    break;
                case SCALE_LINE_ALIGNMENT_CENTER:
                default:
                    coords.mTop = (mCurrentHeight - mLineHeight - mRuleViewMarginBottom) / 2;
                    coords.mBottom = mCurrentHeight - (mCurrentHeight - mLineHeight - mRuleViewMarginBottom) / 2 - mRuleViewMarginBottom;
                    break;
            }
        }

        coords.mLeft = mStartPadding + mMinPrecisePixel * index - lineDrawPadding - DRAW_OFFSET;
        coords.mRight = mStartPadding + mMinPrecisePixel * index + lineDrawPadding;
    }

    private PaintInfo determinePaints(int index) {
        int actualIndex = index;
        if (isReversePrint()) {
            actualIndex = mMinRuleTotalNumber - index - 1;
        }

        Paint paint = (!mIsUnevenScales
                && ((actualIndex % mWholeMinSpaceNumber == 0)
                || index == mDefaultIndex)) ? mWholePaint : mPaint;
        Paint framePaint = (!mIsUnevenScales
                && ((actualIndex % mWholeMinSpaceNumber == 0)
                        || index == mDefaultIndex)) ? mWholeFramePaint : mFramePaint;

        return new PaintInfo(paint, framePaint);
    }

    private void drawLineAndFrame(Canvas canvas, RectCoords coords, Paint paint, Paint framePaint) {
        if (isVertical()) {
            canvas.drawLines(new float[] {
                    coords.mLeft + mFrameWidth, coords.mTop, coords.mRight - mFrameWidth, coords.mTop,
                    coords.mRight, coords.mTop - mFrameWidth, coords.mRight, coords.mBottom + mFrameWidth,
                    coords.mRight - mFrameWidth, coords.mBottom, coords.mLeft + mFrameWidth, coords.mBottom,
                    coords.mLeft, coords.mBottom + mFrameWidth, coords.mLeft, coords.mTop - mFrameWidth
            }, framePaint);
        } else {
            canvas.drawLines(new float[] {
                    coords.mLeft - mFrameWidth, coords.mTop, coords.mRight + mFrameWidth, coords.mTop,
                    coords.mRight, coords.mTop + mFrameWidth, coords.mRight, coords.mBottom - mFrameWidth,
                    coords.mRight + mFrameWidth, coords.mBottom, coords.mLeft - mFrameWidth, coords.mBottom,
                    coords.mLeft, coords.mBottom - mFrameWidth, coords.mLeft, coords.mTop + mFrameWidth
            }, framePaint);
        }
        canvas.drawRect(coords.mLeft, coords.mTop, coords.mRight, coords.mBottom, paint);
    }

    private void drawPoint(Canvas canvas) {
        if (!shouldDrawPoint()) {
            return;
        }

        PointCoords coords = calculatePointCoordinates();

        if (!mIsUnevenScales) {
            mPointPaint.setColor(mWholeLinePaintColor);
        }

        canvas.drawCircle(coords.mX, coords.mY, mPointRadius, mWholeFramePaint);
        canvas.drawCircle(coords.mX, coords.mY, mPointRadius, mPointPaint);
    }

    private boolean shouldDrawPoint() {
        if (mDefaultIndex == Integer.MIN_VALUE) {
            GLog.d(TAG, "[drawPoint] mDefaultIndex is wrong");
            return false;
        }

        if (mScrollDistance == mDefaultIndex * mMinPrecisePixel) {
            return false;
        }

        if (!mShowCenterLineAndPoint) {
            return false;
        }

        return true;
    }

    private PointCoords calculatePointCoordinates() {
        PointCoords coords = new PointCoords();

        if (isVertical()) {
            calculateVerticalPointCoordinates(coords);
        } else {
            calculateHorizontalPointCoordinates(coords);
        }

        return coords;
    }

    private void calculateVerticalPointCoordinates(PointCoords coords) {
        // 根据对齐方式设置点的水平位置
        int lineLength = ((mInterval != 0) && (mDefaultIndex % mInterval == 0)) ? mMaxLineHeight : mLineHeight;

        switch (mScaleLineAlignment) {
            case SCALE_LINE_ALIGNMENT_START:
                // START对齐时，点应该绘制在右侧
                coords.mX = lineLength + mPointTop + (int) mPointRadius;
                break;
            case SCALE_LINE_ALIGNMENT_END:
                // END对齐时，点应该绘制在左侧
                coords.mX = mCurrentWidth - lineLength - mPointTop - (int) mPointRadius;
                break;
            case SCALE_LINE_ALIGNMENT_CENTER:
            default:
                // CENTER对齐时，点应该绘制在左侧
                coords.mX = (mCurrentWidth - lineLength) / 2 - mPointTop - (int) mPointRadius;
                break;
        }
        coords.mY = mStartPadding + mMinPrecisePixel * (mMinRuleTotalNumber - mDefaultIndex - 1) - DRAW_OFFSET;
    }

    private void calculateHorizontalPointCoordinates(PointCoords coords) {
        // 根据对齐方式设置点的垂直位置
        int lineLength = ((mInterval != 0) && (mDefaultIndex % mInterval == 0)) ? mMaxLineHeight : mLineHeight;

        switch (mScaleLineAlignment) {
            case SCALE_LINE_ALIGNMENT_START:
                // START对齐时，点应该绘制在下侧
                coords.mY = lineLength + mPointTop + (int) mPointRadius;
                break;
            case SCALE_LINE_ALIGNMENT_END:
                // END对齐时，点应该绘制在上侧
                coords.mY = mCurrentHeight - mRuleViewMarginBottom - lineLength - mPointTop - (int) mPointRadius;
                break;
            case SCALE_LINE_ALIGNMENT_CENTER:
            default:
                // CENTER对齐时，点应该绘制在上侧
                coords.mY = (mCurrentHeight - mRuleViewMarginBottom - lineLength) / 2 - mPointTop - (int) mPointRadius;
                break;
        }

        if (isRtl()) {
            coords.mX = mStartPadding + mMinPrecisePixel * (mMinRuleTotalNumber - mDefaultIndex - 1) - DRAW_OFFSET;
        } else {
            coords.mX = mStartPadding + mMinPrecisePixel * mDefaultIndex - DRAW_OFFSET;
        }
    }

    private void drawText(Canvas canvas, int currentPosition, int ruleBottom) {
        int x1 = 0;
        int y1 = 0;
        int x2 = 0;
        int y2 = 0;

        String startText = getShowValueUnitString(isReversePrint() ? mStartValue : mEndValue);
        String endText = getShowValueUnitString(isReversePrint() ? mEndValue : mStartValue);

        if (isVertical()) {
            y1 = mStartPadding + mRuleTotalLength + mVerticalTextYOffset;
            x1 = ruleBottom + mTextMarginTop + (int) mTextSize;
            y2 = mStartPadding + mVerticalTextYOffset;
            x2 = x1;

            if (mFloatingTextMargin >= 0) {
                y1 = Math.min(y1, currentPosition + mCurrentHeight - mFloatingTextMargin + mVerticalTextYOffset);
                y2 = Math.max(y2, currentPosition + mFloatingTextMargin + mVerticalTextYOffset);
            }
        } else {
            x1 = mStartPadding + mRuleTotalLength;
            y1 = ruleBottom + mTextMarginTop + (int) mTextSize;
            x2 = mStartPadding;
            y2 = y1;
            if (mFloatingTextMargin >= 0) {
                x1 = Math.min(x1, currentPosition + mCurrentWidth - mFloatingTextMargin
                        - (int) (mTextPaint.measureText(startText) / AppConstants.Number.NUMBER_2));
                x2 = Math.max(x2, currentPosition + mFloatingTextMargin
                        + (int) (mTextPaint.measureText(endText) / AppConstants.Number.NUMBER_2));
            }
        }
        canvas.drawText(startText, x1, y1, mTextPaint);
        canvas.drawText(endText, x2, y2, mTextPaint);
    }

    private void updateVerticalTextYOffset() {
        mVerticalTextYOffset = (int) (-mTextPaint.ascent() - (mTextPaint.descent()
                - mTextPaint.ascent()) / AppConstants.Number.NUMBER_2);
    }

    /**
     * 是否处于overScrolling状态.
     * <p>
     * {@link #isStartDirectionOverScrolling() } 返回 true 或者
     * {@link #isEndDirectionOverScrolling() } 返回 true
     *
     * @return true/false
     */
    private boolean isOverScrolling() {
        return isStartDirectionOverScrolling() || isEndDirectionOverScrolling();
    }

    /**
     * overScrolling 是否发生在view的起始方向.
     *
     * @return true/false
     */
    private boolean isStartDirectionOverScrolling() {
        int position = getScrollPositionByOrientation();
        return position < 0;
    }

    /**
     * overScrolling 是否发生在view的结束方向.
     *
     * @return true/false
     */
    private boolean isEndDirectionOverScrolling() {
        int position = getScrollPositionByOrientation();
        return (position > 0) && (position > mRuleTotalLength);
    }

    private int getScrollPositionByOrientation() {
        return isVertical() ? getScrollY() : getScrollX();
    }

    /**
     * overScroller 开启回弹
     *
     * @return
     */
    private boolean overScrollerSpringBack() {
        if (!isOverScrolling()) {
            return false;
        }
        int startX = getScrollX();
        int startY = 0;
        int maxX = mRuleTotalLength;
        int maxY = 0;

        if (isVertical()) {
            startX = 0;
            startY = getScrollY();
            maxX = 0;
            maxY = mRuleTotalLength;
        }
        boolean isSpringBack = mOverScroller.springBack(startX, startY, 0, maxX, 0, maxY);
        if (isSpringBack) {
            invalidate();
        }
        return isSpringBack;
    }

    /**
     * 一次完整的滑动 DOWN - > MOVE -> UP:
     * <p>
     * | overScroll区域（A） | view内容区域（B） | overScroll区域(C) |
     * <p>
     * 正方向 ( -> 代表View滚动方向) ： A->A, A->B, B->B, B->C, C->C
     * 反方向 ( <- 代表View滚动方向) ： C<-C, B<-C, B<-B, A<-B, A<-A
     * <p>
     * 当前极限阻尼效果可能会发生在以下阶段：
     * B->C, C->C
     * A<-B, A<-A
     * <p>
     * 界定是否发生阻尼的条件：
     * 以每次onScroll为时机节点，区分当前View是否处于OverScrolling状态 {@link RuleScrollerView#isOverScrolling()}
     *
     * @param distanceX
     * @param distanceY
     */
    private void scroll(int distanceX, int distanceY) {
        int dx = distanceX;
        int dy = distanceY;
        if (isOverScrolling()) {
            /* overScroll区域 部分 */
            int delta = dx;
            int scroll = getScrollX();
            if (isVertical()) {
                delta = dy;
                scroll = getScrollY();
            }

            if (scroll > 0) {
                /* overScroll C 区域 */
                scroll = scroll - mRuleTotalLength;
            }

            /* 目前 A<-A, A<-B ,B->C, C->C 需要阻尼 */
            if ((scroll < 0 && delta < 0) || (scroll > 0 && delta > 0)) {
                int dist = COUIPhysicalAnimationUtil.calcRealOverScrollDist(delta, scroll, mOverscrollDistance);
                if (isVertical()) {
                    dy = dist;
                } else {
                    dx = dist;
                }
            }
        }

        /* ContentScroll 部分 */
        if (isVertical()) {
            dx = 0;
        } else {
            dy = 0;
        }

        scrollBy(dx, dy);
    }

    private void initAttr(Context context, AttributeSet attrs) {
        if (attrs != null) {
            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.RuleScrollerView);
            mStartValue = typedArray.getInt(R.styleable.RuleScrollerView_startValue, mStartValue);
            mEndValue = typedArray.getInt(R.styleable.RuleScrollerView_endValue, mEndValue);
            mValue = typedArray.getInt(R.styleable.RuleScrollerView_currentValue, mValue);
            mMinPrecise = typedArray.getInt(R.styleable.RuleScrollerView_minPreciseValue, mMinPrecise);
            mMinScaleValue = typedArray.getFloat(R.styleable.RuleScrollerView_minScaleValue, mMinScaleValue);
            mWholeMinSpaceNumber = typedArray.getInt(R.styleable.RuleScrollerView_wholeMinSpaceNumber, mWholeMinSpaceNumber);
            mMinPrecisePixel = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_minPreciseLength, mMinPrecisePixel);
            mWholeLinePaintColor = typedArray.getInt(R.styleable.RuleScrollerView_wholeLineColor, mWholeLinePaintColor);
            mPointTop = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_pointTop, mPointTop);
            mPointRadius = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_pointRadius, 0);
            mPaintColor = typedArray.getInt(R.styleable.RuleScrollerView_lineColor, mPaintColor);
            mIsUnevenScales = typedArray.getBoolean(R.styleable.RuleScrollerView_isUnevenScales, mIsUnevenScales);
            mInterval = typedArray.getInt(R.styleable.RuleScrollerView_interval, mInterval);
            float textSize = typedArray.getDimension(R.styleable.RuleScrollerView_textSize, -1);
            if (textSize >= 0) {
                mTextSize = FontSizeUtil.getSuitableFontSize(
                        textSize,
                        context.getResources().getConfiguration().fontScale,
                        FontSizeUtil.TEXT_SIZE_LEVEL_2
                );
            }
            mTextColor = typedArray.getInt(R.styleable.RuleScrollerView_textColor, mTextColor);

            mLineHeight = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_lineHeight, 0);
            mMaxLineHeight = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_maxLineHeight, 0);
            mIndicatorHeight = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_indicatorHeight, 0);
            initOverscrollDistanceByOrientation(getContext());
            setOrientation(typedArray.getInt(R.styleable.RuleScrollerView_ruleOrientation, LinearLayout.HORIZONTAL));
            mFloatingTextMargin = typedArray.getDimensionPixelSize(R.styleable.RuleScrollerView_floatingTextMargin, 0);
            mUnitString = typedArray.getString(R.styleable.RuleScrollerView_unitText);
            mScaleLineAlignment = typedArray.getInt(R.styleable.RuleScrollerView_scaleLineAlignment, SCALE_LINE_ALIGNMENT_CENTER);
            typedArray.recycle();
        }
    }

    private void initOverscrollDistanceByOrientation(Context context) {
        if (context == null) {
            return;
        }
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        mOverscrollDistance = isVertical() ? metrics.heightPixels : metrics.widthPixels;
    }

    /**
     * 是否为竖向调节尺
     *
     * @return true 当前为竖向调节尺；false 当前为横向调节尺
     */
    private boolean isVertical() {
        return mOrientation == LinearLayout.VERTICAL;
    }

    /**
     * 是否为水平向调节尺
     *
     * @return true 当前为水平向调节尺；false 当前为竖向调节尺
     */
    private boolean isHorizontal() {
        return !isVertical();
    }

    private void initDefaultParams() {
        Resources res = getResources();
        mStartValue = res.getInteger(R.integer.rule_scroller_view_default_start_value);
        mTextMarginTop = res.getDimensionPixelSize(R.dimen.rule_scroller_view_text_margin_top);
        mEndValue = res.getInteger(R.integer.rule_scroller_view_default_end_value);
        mMinPrecisePixel = res.getDimensionPixelSize(R.dimen.rule_scroller_view_default_min_precise_width_value);
        mMinPrecise = res.getInteger(R.integer.rule_scroller_view_default_min_precise_value);
        TypedValue outValue = new TypedValue();
        res.getValue(R.dimen.rule_scroller_view_min_scale_value, outValue, true);
        mMinScaleValue = outValue.getFloat();
        if (mMinScaleValue == 0) {
            mMinScaleValue = MIN_SCALE_DEFAULT;
        }
        if (mIsUnevenScales) {
            mLineWidth = res.getDimensionPixelSize(R.dimen.rule_scroller_view_portrait_blur_line_width);
        } else {
            mLineWidth = res.getDimensionPixelSize(R.dimen.rule_scroller_view_default_line_width);
        }
        mTextSize = FontSizeUtil.getSuitableFontSize(
                res.getDimensionPixelSize(R.dimen.rule_scroller_view_default_text_size),
                res.getConfiguration().fontScale,
                FontSizeUtil.TEXT_SIZE_LEVEL_2
        );
        mWholeLinePaintColor = res.getColor(R.color.rule_scroller_view_whole_line_color, null);
        mPaintColor = res.getColor(R.color.rule_scroller_view_line_color, null);
        mTextColor = res.getColor(R.color.rule_scroller_view_text_color, null);
        mFramePaintColor = res.getColor(R.color.rule_scroller_view_frame_color, null);
        mWholeFramePaintColor = res.getColor(R.color.rule_scroller_view_whole_frame_color, null);
        mWholeMinSpaceNumber = res.getInteger(R.integer.rule_scroller_view_default_whole_space_number);
        mRuleViewMarginBottom = res.getDimensionPixelSize(R.dimen.video_editor_rule_margin_bottom);
        mFrameWidth = getResources().getDimensionPixelSize(R.dimen.video_editor_rule_frame_width);
        mPointMarginBottom = res.getDimensionPixelSize(R.dimen.video_editor_rule_point_margin_bottom);
        mShadowColor = getResources().getColor(R.color.rule_scroller_view_shadow_color, null);
    }

    private void init() {
        mWholePaint = new Paint();
        mWholePaint.setColor(mWholeLinePaintColor);
        mWholePaint.setAlpha((int) (MAX_ALPHA * WHOLE_LINE_ALPHA_PERCENT));
        mWholePaint.setShadowLayer(PAINT_SHADOW_SIZE, 0, 0, mShadowColor);

        mIndicatorPaint = new Paint();
        mIndicatorPaint.setColor(mWholeLinePaintColor);
        mIndicatorPaint.setAlpha(MAX_ALPHA);
        mIndicatorPaint.setShadowLayer(PAINT_SHADOW_SIZE, 0, 0, mShadowColor);

        mPressedIndicatorPaint = new Paint();
        mPressedIndicatorPaint.setAlpha(MAX_ALPHA);
        mPressedIndicatorPaint.setShadowLayer(PAINT_SHADOW_SIZE, 0, 0, mShadowColor);
        mPressedIndicatorPaint.setColor(mThemeColor);
        mPressedIndicatorPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPressedIndicatorPaint.setStrokeWidth(ResourceUtils.dp2px(getContext(), CENTER_LINE_BORDER_INTOUCH));

        mPaint = new Paint();
        mPaint.setColor(mPaintColor);
        mPaint.setAlpha((int) (MAX_ALPHA * NORMAL_LINE_ALPHA_PERCENT));
        mPaint.setShadowLayer(PAINT_SHADOW_SIZE, 0, 0, mShadowColor);

        mFramePaint = new Paint();
        mFramePaint.setColor(mFramePaintColor);
        mFramePaint.setAlpha((int) (MAX_ALPHA * NORMAL_FRAME_ALPHA_PERCENT));
        mFramePaint.setStyle(Paint.Style.STROKE);
        mFramePaint.setStrokeWidth(mFrameWidth);

        mWholeFramePaint = new Paint();
        mWholeFramePaint.setColor(mWholeFramePaintColor);
        mWholeFramePaint.setAlpha((int) (MAX_ALPHA * NORMAL_WHOLE_FRAME_ALPHA_PERCENT));
        mWholeFramePaint.setStyle(Paint.Style.STROKE);
        mWholeFramePaint.setStrokeWidth(mFrameWidth);

        mPointPaint = new Paint();
        mPointPaint.setColor(mPaintColor);
        mPointPaint.setAlpha((int) (MAX_ALPHA * POINT_ALPHA_PERCENT));
        mPointPaint.setShadowLayer(PAINT_SHADOW_SIZE, 0, 0, mShadowColor);

        mTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.setTypeface(TypefaceUtil.INSTANCE.getSansEnRegular());
        mTextPaint.setColor(mTextColor);
        mTextPaint.setStyle(Paint.Style.FILL);
        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setTextAlign(Paint.Align.CENTER);
        mTextPaint.setAlpha(MAX_TEXT_PAIN_ALPHA);
        mTextPaint.setShadowLayer(PAINT_SHADOW_SIZE, 0, 0, Color.BLACK);
        updateVerticalTextYOffset();
        initOverScroller();
        initGestureDetector();
        if (mValue == Integer.MIN_VALUE) {
            mValue = mStartValue;
        }
        setCurrentValue(mValue);
        initRuleParams();
    }

    private boolean canSelectValueChange() {
        return (mOnSelectValueChangeListener != null)
                && (mOnSelectValueChangeListener.canSelectValueChange());
    }

    private void initRuleParams() {
        if (!mIsParamsChange) {
            return;
        }
        int total = mEndValue - mStartValue;
        if (mIsUnevenScales && (mMinPrecise != 0)) {
            mLastMinRuleValue = total % mMinPrecise;
            mMinRuleTotalNumber = total / mMinPrecise + 1;
            mMinRuleTotalNumber = (mLastMinRuleValue == 0) ? mMinRuleTotalNumber : (mMinRuleTotalNumber + 1);
            mLastMinRuleValue = (mLastMinRuleValue == 0) ? (float) mMinPrecise : mLastMinRuleValue;
        } else if (mMinScaleValue != 0) {
            mLastMinRuleValue = total % mMinScaleValue;
            mMinRuleTotalNumber = Math.round(total / mMinScaleValue) + 1;
            mMinRuleTotalNumber = (mLastMinRuleValue == 0) ? mMinRuleTotalNumber : (mMinRuleTotalNumber + 1);
            mLastMinRuleValue = (mLastMinRuleValue == 0) ? mMinScaleValue : mLastMinRuleValue;
        }

        mStartPadding = (isVertical() ? mCurrentHeight : mCurrentWidth) / 2;
        mRuleTotalLength = (mMinRuleTotalNumber - 1) * mMinPrecisePixel;
        mIsParamsChange = false;
    }

    private void initOverScroller() {
        if (mOverScroller == null) {
            mOverScroller = new SpringOverScroller(getContext());
        }
        float springOverTension = SPRING_OVER_TENSION_HORIZONTAL;
        if (isVertical()) {
            springOverTension = SPRING_OVER_TENSION_VERTICAL;
        }
        mOverScroller.setSpringBackTensionMultiple(springOverTension);
    }

    private void initGestureDetector() {
        if (mGestureDetector == null) {
            OnGestureListener listener = new OnGestureListener() {
                @Override
                public boolean onDown(@NonNull MotionEvent e) {
                    if (getParent() != null) {
                        getParent().requestDisallowInterceptTouchEvent(true);
                    }

                    stopFling();

                    if (canSelectValueChange()) {
                        selectValueStart();
                    }
                    if ((mScrollAnimatorSet != null) && mScrollAnimatorSet.isRunning()) {
                        mScrollAnimatorSet.cancel();
                    }
                    return true;
                }

                @Override
                public void onShowPress(@NonNull MotionEvent e) {
                    // do nothing
                }

                @Override
                public boolean onSingleTapUp(@NonNull MotionEvent e) {
                    return false;
                }

                @Override
                public boolean onScroll(@NonNull MotionEvent e1, @NonNull MotionEvent e2, float distanceX, float distanceY) {
                    if (!canSelectValueChange()) {
                        return true;
                    }
                    scroll((int) (distanceX + ONE_HALF_FLOAT), (int) (distanceY + ONE_HALF_FLOAT));
                    return true;
                }

                @Override
                public void onLongPress(@NonNull MotionEvent e) {
                    // do nothing
                }

                @Override
                public boolean onFling(@NonNull MotionEvent e1, @NonNull MotionEvent e2, float velocityX, float velocityY) {
                    if (!canSelectValueChange()) {
                        return true;
                    }

                    return mViewFlinger.fling(-(int) (velocityX + ONE_HALF_FLOAT), -(int) (velocityY + ONE_HALF_FLOAT));
                }
            };
            mGestureDetector = new GestureDetector(getContext(), listener);
            mGestureDetector.setIsLongpressEnabled(false);
        }
    }

    private boolean isRtl() {
        return getLayoutDirection() == View.LAYOUT_DIRECTION_RTL;
    }

    @IntDef({LinearLayout.HORIZONTAL, LinearLayout.VERTICAL})
    @Retention(RetentionPolicy.SOURCE)
    public @interface OrientationMode {
    }

    private class ViewFlinger implements Runnable {
        /**
         * 用于避免run运行期间重复postAnimation
         */
        private volatile boolean mEatRunOnAnimationRequest = false;
        private volatile boolean mReSchedulePostAnimationCallback = false;

        private Interpolator mQuinticInterpolator = t -> {
            t -= 1.0f;
            return t * t * t * t * t + 1.0f;
        };

        @Override
        public void run() {
            mEatRunOnAnimationRequest = true;
            mReSchedulePostAnimationCallback = false;
            if (!mOverScroller.isCOUIFinished()) {
                /* 检测是否需要开启回弹效果 */
                if (!overScrollerSpringBack()) {
                    /* 每次检测都需要请求当前信号来时绘制一把，直到开启回弹 */
                    invalidate();
                    postOnAnimation();
                }
            }

            if (mReSchedulePostAnimationCallback) {
                internalPostOnAnimation();
            } else if (mOverScroller.isCOUIFinished()) {
                stop();
            }
            mEatRunOnAnimationRequest = false;
        }

        private void postOnAnimation() {
            if (mEatRunOnAnimationRequest) {
                mReSchedulePostAnimationCallback = true;
            } else {
                internalPostOnAnimation();
            }
        }

        private void internalPostOnAnimation() {
            removeCallbacks(this);
            RuleScrollerView.this.postOnAnimation(this);
        }

        public boolean fling(int velocityX, int velocityY) {
            int startX = getScrollX();
            int startY = getScrollY();
            int flingVelocityX = velocityX;
            int flingVelocityY = velocityY;
            if (isVertical()) {
                startX = 0;
                flingVelocityX = 0;
            } else {
                startY = 0;
                flingVelocityY = 0;
            }
            mOverScroller.setInterpolator(mQuinticInterpolator);
            Display display = getDisplay();
            if (display != null) {
                mOverScroller.setRefreshRate(display.getRefreshRate());
            } else {
                GLog.e(TAG, "[fling] can't setRefreshRate because view.getDisplay() return null");
            }
            mOverScroller.fling(startX, startY, flingVelocityX, flingVelocityY);
            boolean isScrollerFinished = mOverScroller.isCOUIFinished();
            if (!isScrollerFinished) {
                postOnAnimation();
            }
            return !isScrollerFinished;
        }

        void stop() {
            removeCallbacks(this);
            mOverScroller.abortAnimation();
        }
    }

    // 辅助类用于存储点坐标
    private static class PointCoords {
        int mX;
        int mY;
    }

    // 辅助类用于存储矩形坐标
    private static class RectCoords {
        int mLeft;
        int mTop;
        int mRight;
        int mBottom;
    }

    // 辅助类用于存储画笔信息
    private static class PaintInfo {
        final Paint mPaint;
        final Paint mFramePaint;

        PaintInfo(Paint paint, Paint framePaint) {
            this.mPaint = paint;
            this.mFramePaint = framePaint;
        }
    }
}
