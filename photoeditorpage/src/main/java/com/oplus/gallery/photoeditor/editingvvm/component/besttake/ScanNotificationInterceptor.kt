/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ScanNotificationInterceptor.kt
 ** Description: 相册后台扫描“使用通知”权限拦截器
 ** Version: 1.0
 ** Date : 2025/5/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>       2025/5/8        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.besttake

import android.app.Activity
import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConfirmInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.notification.EditorConfirmDialogAction

class ScanNotificationInterceptor(
    private val scanCallback: () -> Unit,
    private val context: Context,
    postNotificationAction: (NotificationAction) -> Unit,
) : ConfirmInterceptor(postNotificationAction) {
    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): EditorConfirmDialogAction {
        return EditorConfirmDialogAction(
            titleResId = com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_notification_title,
            messageResId = R.string.photoeditorpage_best_take_scan_progress_prompt,
            positiveButtonTextResId = R.string.photoeditorpage_best_take_scan_allow,
            negativeButtonTextResId = R.string.photoeditorpage_best_take_scan_refuse,
            confirmCallback = confirmCallback
        )
    }

    override fun onCheckCondition(param: Bundle): Boolean {
        val isNotificationEnable = PermissionHelper.isNotificationsEnabled(context)
        GLog.d(TAG, LogFlag.DL) { "onCheckCondition, isNotificationEnable: $isNotificationEnable" }
        return isNotificationEnable
    }

    override fun onConditionPassed(chain: IInterceptor.IChain<Bundle, Unit>) {
        super.onConditionPassed(chain)
        scanCallback.invoke()
        GLog.d(TAG, LogFlag.DL) { "onConditionPassed scanCallback invoke" }
    }

    override fun onAgreed(activity: Activity) {
        PermissionHelper.requestNotificationPermission(activity)
        scanCallback.invoke()
        GLog.d(TAG, LogFlag.DL) { "onAgreed scanCallback invoke" }
    }

    override fun onRefused() {
        scanCallback.invoke()
        GLog.d(TAG, LogFlag.DL) { "onRefused scanCallback invoke" }
    }

    override fun onRefusedProcessChain(chain: IInterceptor.IChain<Bundle, Unit>) {
        chain.proceed(chain.param)
    }

    companion object {
        private const val TAG = "ScanNotificationInterceptor"
    }
}