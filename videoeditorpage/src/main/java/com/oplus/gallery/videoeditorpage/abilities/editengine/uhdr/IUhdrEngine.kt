/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IUhdrEngine.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.uhdr

import android.graphics.Bitmap
import androidx.annotation.MainThread

/**
 * UHDR 引擎接口定义层
 */
interface IUhdrEngine {
    /**
     * engine 类型，与 UhdrEngineType 对应
     */
    val engineType: Int

    /**
     * 基于GPU的方式从原视频中抽 HDR 帧
     * 适用于视频抽帧
     * 由于美摄限制，需要在 MAIN 线程中调用（主线程 + 挂起函数）
     * @param timeUs 时间点，微妙
     * @return HDR bitmap，固定返回 HLG
     */
    @MainThread
    suspend fun getHdrFrameFromSourceAsyncByGpu(timeUs: Long): Bitmap?

    /**
     * 使用相册的下变换算法，将 HDR 图变换到 SDR + GAINMAP
     * @param hdrBitmap hdr 图
     * @return SDR + GAINMAP 合成的 UHDR
     */
    fun transformToUhdr(hdrBitmap: Bitmap): Bitmap?
}