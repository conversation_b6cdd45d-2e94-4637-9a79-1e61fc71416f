/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AdjustVM
 ** Description: 负责调节功能的业务实现
 ** Version: 1.0
 ** Date : 2025/4/4
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2025/04/04    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.adjust

import android.app.Application
import android.content.Context
import android.graphics.Bitmap
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.meishe.libait.AitInfo
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.MeicamEdit
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoFrameRetriever
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.timeline.MeicamClipMultiEffect
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.AdjustParams
import com.oplus.gallery.videoeditorpage.utlis.getTextByLocale
import com.oplus.gallery.videoeditorpage.video.business.adjust.AdjustConstant.POSITIVE_FULL_PROGRESS
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_AUTO_ENHANCE
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_BLACK_POINT
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_BRIGHTNESS
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_BRILLIANCE
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_CONTRAST
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_EXPOSURE
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_HIGHLIGHT
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_SATURATION
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_SHADOW
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_TEMPERATURE
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_TINT
import com.oplus.gallery.videoeditorpage.video.business.adjust.Position.POSITION_VIBRANCE
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseViewModel
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.system.measureTimeMillis
import com.oplus.gallery.basebiz.R as BaseR

/**
 * 调节功能的业务实现
 */
class AdjustVM(
    application: Application,
    val editorEngine: EditorEngine,
    val editorControlView: EditorControlView,
    val timelineViewModel: TimelineViewModel?
) : EditorBaseViewModel(application), LifecycleObserver {

    /**
     * "调节"中文名称
     */
    private val fxNameZh by lazy { getTextByLocale(getApplication(), ZH, R.string.videoeditor_adjust) }

    /**
     * 菜单栏列表的适配器数据
     */
    private val editorMenuItemViewDataList by lazy { queryMenuAdapterData() }

    /**
     * 当前编辑的clip对象
     */
    private val currentClip by lazy { getCurrentVideoClip(editorEngine) }

    /**
     * vm内部的ui实体bean
     */
    private val _adjustUIBean = MutableLiveData<AdjustUIBean>()

    /**
     * 调节的ui数据对象实体
     */
    private var adjustRecord = AdjustRecord()

    /**
     * 是否正在处理自动调节，由于自动调节需要获取当前帧，会有耗时，所以如果在自动调节还在处理的时候，就不响应这次点击
     */
    private var isProcessAutoEnhance = false

    /**
     * 对外暴露的特效对象
     */
    private var adjustVideoEffect: BaseVideoClipEffect? = null

    /**
     * 上次操作的调节参数效果
     */
    private var lastRecord: AdjustRecord? = null

    /**
     * 对外暴露的ui实体bean
     */
    val adjustUIBean: LiveData<AdjustUIBean> = _adjustUIBean

    /**
     * 提供从视频中取帧的能力
     */
    private val videoFrameRetriever: IVideoFrameRetriever? by lazy {
        currentClip?.takeUnless { it.isImage }?.let { clip ->
            editorEngine.createVideoFrameRetriever(clip.srcFilePath)
        }
    }

    /**
     * 下发给算法的调节参数对象
     */
    private var adjustParams: AdjustParams? = null

    fun initData() {
        viewModelScope.launch(Dispatchers.MeicamEdit) {
            getCurrentBitmap()
        }
        adjustRecord.currentIndex = POSITION_AUTO_ENHANCE
        val itemViewData = editorMenuItemViewDataList[POSITION_AUTO_ENHANCE]
        itemViewData.isDisableStyle = true

        val adjustUIBean = AdjustUIBean()
        adjustUIBean.apply {
            id = AdjustBeanId.INIT_DATA
            menuItemListData = editorMenuItemViewDataList
            adjustRecord = <EMAIL>
        }.also { _adjustUIBean.value = it }
        editorControlView.seekTimeline(editorEngine.timelineCurrentPosition, 0, true)
    }

    /**
     * 拖动seekbar时更新数据，通知刷新ui，下发数据给算法
     */
    fun seekBarValueChange(progress: Int, fromUser: Boolean) {
        //更新record数据
        adjustRecord.setValue(progress.toFloat())
        //更新ui的item数据
        updateItemViewDataWhenScroll(adjustRecord.currentIndex, progress)
        //通知ui进行更新
        val adjustUIBean = _adjustUIBean.value
        adjustUIBean?.apply {
            id = AdjustBeanId.UPDATE_ITEM_VIEW
            itemPosition = <EMAIL>
        }.also { _adjustUIBean.value = it }

        adjustUIBean?.apply {
            id = AdjustBeanId.RESTORE_AUTO_ADJUST_BUTTON
        }.also { _adjustUIBean.value = it }

        val autoEnhanceSwitch = isAutoEnhanceSwitchOn()
        enableAutoEnhanceSwitch(autoEnhanceSwitch)

        //下发数据给到engine
        emitAdjustFx(adjustRecord.currentIndex, autoEnhanceSwitch)
    }

    /**
     * 选中任意一个调节子算法时的处理
     */
    fun onItemClick(position: Int, item: EditorMenuItemViewData, itemClickedFrom: Int): Boolean {
        if (isProcessAutoEnhance) {
            GLog.d(TAG, LogFlag.DL) { "[onItemClick]: processing auto enhance" }
            return false
        }
        GLog.d(TAG, LogFlag.DL) { "[updateAdjustState]:$position " }
        adjustRecord.currentIndex = position
        val currentAttr = adjustRecord.getAttrByIndex()
        val currentSwitch = currentAttr.second
        val adjustUIBean = _adjustUIBean.value
        item.oldLoadProgress = adjustRecord.getFxDataByIndex(position)
        item.loadProgress = adjustRecord.getFxDataByIndex(position)
        if (adjustRecord.lastIndex == adjustRecord.currentIndex) {
            item.isDisableStyle = currentSwitch
            item.isIconColorAnimEnable = true
            if (currentSwitch) {
                item.centerText = TextUtil.EMPTY_STRING
            }
            adjustRecord.switchState(adjustRecord.currentIndex)
            item.oldLoadProgress = adjustRecord.getFxDataByIndex(position)
            item.loadProgress = adjustRecord.getFxDataByIndex(position)
            adjustUIBean?.apply {
                id = AdjustBeanId.RESTORE_AUTO_ADJUST_BUTTON
                itemPosition = position
                selectItemViewData = item
            }.also { _adjustUIBean.value = it }
            // 如果是相同的Entry才更新，不同的仅是item变更，不需要更新
            if (adjustRecord.currentIndex == POSITION_AUTO_ENHANCE) {
                enableAutoEnhanceSwitch(currentSwitch.not())
                startAutoEnhance(currentSwitch.not())
            } else {
                enableAutoEnhanceSwitch(isAutoEnhanceSwitchOn())
                emitAdjustFx(position, isAutoEnhanceSwitchOn())
            }
        }
        adjustUIBean?.apply {
            selectItemViewData = item
            id = AdjustBeanId.UPDATE_ITEM_VIEW
        }.also { _adjustUIBean.value = it }

        adjustRecord.currentIndex = position
        adjustUIBean?.apply {
            id = AdjustBeanId.UPDATE_SEEK_BAR
            itemPosition = position
            adjustRecord = <EMAIL>
        }.also { _adjustUIBean.value = it }

        adjustRecord.lastIndex = adjustRecord.currentIndex
        return true
    }

    /**
     * 将效果重置成初始化状态
     */
    fun resetInitState() {
        // 更新菜单项视图
        resetMenuItemView()

        // 触发 seekbar 更新，确保 seekbar 重置为 0
        val adjustUIBean = _adjustUIBean.value
        adjustUIBean?.apply {
            id = AdjustBeanId.UPDATE_SEEK_BAR
            itemPosition = POSITION_AUTO_ENHANCE
            adjustRecord = <EMAIL>
        }.also { _adjustUIBean.value = it }

        // 下发数据给算法
        adjustRecord.map.forEach { (index, _) ->
            if (index != POSITION_AUTO_ENHANCE) {
                emitAdjustFx(index, false)
            }
        }
    }

    /**
     * 根据特效信息去更新ui
     */
    fun recoverData(data: BaseVideoClipEffect?) {
        if (data is MeicamClipMultiEffect) {
            this.adjustVideoEffect = data
            val record = adjustRecord.recoverRecord(data.getEffectDataMap(), data.autoSwitch)
            this.lastRecord = record
            adjustRecord = record.copy()
            updateMenuItemView()
        } else {
            GLog.d(TAG, LogFlag.DL) { "[recoverData]: no need recover." }
        }
    }

    /**
     * 点击取消按钮，取消本次调节效果，并且把ui也还原到进来前的状态
     * 如果已经有调节效果再进行二次编辑，则取消本次的编辑效果。如果是第一次添加效果，则直接移除特效
     */
    fun clickCancel() {
        updateMenuItemView()
        lastRecord?.let {
            adjustRecord = it
        } ?: run {
            adjustVideoEffect?.let { effect ->
                currentClip?.removeEffect(effect)
                editorControlView.seekTimeline(editorEngine.timelineCurrentPosition, 0, true)
            }
            return
        }
        val autoEnhance = adjustRecord.getAttrByIndex(POSITION_AUTO_ENHANCE).second
        adjustRecord.map.forEach { (index, _) ->
            if (index != POSITION_AUTO_ENHANCE) {
                emitAdjustFx(index, autoEnhance)
            }
        }
    }

    /**
     * 点击完成按钮，如果没有任何调节效果，则移除特效，否则添加特效
     */
    fun clickDone(): Boolean {
        if (adjustRecord.isInitState()) {
            adjustVideoEffect?.let {
                currentClip?.removeEffect(it)
                editorControlView.seekTimeline(editorEngine.timelineCurrentPosition, 0, true)
            }
            return true
        }
        return false
    }

    /**
     * 判断当前是否是重置状态
     */
    fun isInitState(): Boolean {
        return adjustRecord.isInitState()
    }

    /**
     * 获取当前特效数据
     * @return 特效数据
     */
    fun getCurrentVideoFx(): BaseVideoClipEffect? {
        return adjustVideoEffect
    }

    /**
     * 资源释放
     */
    fun release() {
        videoFrameRetriever?.release()
    }

    /**
     * 判断position是否是当前选中项
     *
     * @param position 需要判断的索引位置
     * @return 如果当前项与调整记录中的当前项相同，则返回true；否则返回false
     */
    fun isTheSameItem(position: Int): Boolean {
        return position == adjustRecord.currentIndex
    }

    /**
     * 只有当自动调节开关按钮为开启状态，并且当前调节项不是自动调节的关联项时，自动调节的按钮开关才为开启状态，否则为关闭状态
     * @return true:开启状态，false：关闭状态
     */
    private fun isAutoEnhanceSwitchOn(): Boolean {
        return adjustRecord.isSubItemOfAutoAdjust().not() && (adjustRecord.map[POSITION_AUTO_ENHANCE]?.switch == true)
    }

    /**
     * 如果是修改自动调节关联项的item，需要将自动调节按钮开关置为false
     */
    private fun enableAutoEnhanceSwitch(switch: Boolean) {
        val currentAutoEnhanceSwitch = adjustRecord.map[POSITION_AUTO_ENHANCE]?.switch
        if (switch != currentAutoEnhanceSwitch) {
            adjustRecord.map[POSITION_AUTO_ENHANCE]?.switch = switch
        }
    }

    private fun resetMenuItemView() {
        editorMenuItemViewDataList.forEachIndexed { index, menuItemView ->
            if (index != POSITION_AUTO_ENHANCE) {
                menuItemView.isDisableStyle = false
                menuItemView.centerText = TextUtil.EMPTY_STRING
                menuItemView.loadProgress = 0f
                menuItemView.oldLoadProgress = 0f
            } else {
                menuItemView.isDisableStyle = true
            }
        }
        val bean = _adjustUIBean.value
        // 重置调节记录
        adjustRecord = AdjustRecord()
        adjustRecord.currentIndex = POSITION_AUTO_ENHANCE
        bean?.apply {
            id = AdjustBeanId.UPDATE_ALL_VIEW
            itemPosition = POSITION_AUTO_ENHANCE
            menuItemListData = editorMenuItemViewDataList
            adjustRecord = <EMAIL>
        }.also { _adjustUIBean.value = it }
    }

    private fun updateMenuItemView() {
        editorMenuItemViewDataList.forEachIndexed { index, menuItemView ->
            if (index != POSITION_AUTO_ENHANCE) {
                val progress = if ((adjustVideoEffect != null) && (lastRecord != null)) {
                    lastRecord?.getProgressByIndex(index)?.toInt() ?: 0f
                } else {
                    0f
                }
                if (adjustVideoEffect == null) {
                    lastRecord?.setValueByIndex(0f, index, AdjustState.ENABLE)
                } else {
                    lastRecord?.let {
                        val enable = it.getAttrByIndex(index).second
                        menuItemView.isDisableStyle = enable.not()
                    } ?: run { menuItemView.isDisableStyle = false }
                }
                menuItemView.loadProgress = division(progress.toFloat(), POSITIVE_FULL_PROGRESS.toFloat())
                menuItemView.oldLoadProgress = division(progress.toFloat(), POSITIVE_FULL_PROGRESS.toFloat())
            } else {
                menuItemView.isDisableStyle = lastRecord?.getAttrByIndex(POSITION_AUTO_ENHANCE)?.second?.not() ?: true
            }
        }
        val bean = _adjustUIBean.value
        bean?.apply {
            id = AdjustBeanId.UPDATE_ALL_VIEW
            adjustRecord = <EMAIL>
            menuItemListData = editorMenuItemViewDataList
        }.also { _adjustUIBean.value = it }
    }

    /**
     * @param switch 自动调节的开关状态 true 为开启，false为关闭
     * marked by youpeng:
     * 优化1.判断如果当前点击自动调节的时间跟上次点击自动调节的时间相同，则可以省略掉跟向美摄请求数据的逻辑
     */
    private fun startAutoEnhance(switch: Boolean) {
        isProcessAutoEnhance = true
        var bitmap: Bitmap? = null
        var aitInfo: AitInfo? = null
        if (switch) {
            measureTimeMillis {
                bitmap = getCurrentBitmap()
            }.also { GLog.d(TAG, LogFlag.DL) { "[startAutoEnhance]: getCurrentBitmap cost time: $it" } }
            measureTimeMillis {
                bitmap?.let {
                    aitInfo = editorEngine.currentTimeline?.getAutoEnhanceValueMap(bitmap)
                }
            }.also { GLog.d(TAG, LogFlag.DL) { "[startAutoEnhance]: getAutoEnhanceValueMap cost time: $it" } }
        } else {
            aitInfo = AitInfo()
        }
        bitmap?.let {
            BitmapUtils.recycleSilently(it)
        }
        aitInfo?.let {
            updateItemWhenAutoEnhance(it, switch)
        }
        isProcessAutoEnhance = false
    }

    /**
     * 获取当前clip播放的当前帧的bitmap
     * @return 返回当前clip播放的当前帧的bitmap
     */
    private fun getCurrentBitmap(): Bitmap? {
        val clip = currentClip ?: return null
        return if (clip.isImage.not()) {
            videoFrameRetriever?.getFrameAtTime(
                editorEngine.timelineCurrentPosition,
                StreamingConstant.VideoFrameRetriever.VIDEO_FRAME_HEIGHT_GRADE_360
            )
        } else {
            editorEngine.bitmapFromPreviewWindow
        }
    }

    /**
     * 自动调节时更新相关子项的刻度值，并且下发算法数据给到管线
     * @param aitInfo 封装算法参数的对象
     * @param enhance 自动调节的开关状态 true 为开启，false为关闭
     */
    private fun updateItemWhenAutoEnhance(aitInfo: AitInfo, enhance: Boolean) {
        adjustRecord.subItemsOfAutoAdjust.forEachIndexed { _, data ->
            val currentProgress = adjustRecord.getFxDataByIndex(data)
            setAutoEnhanceSubitemValues(aitInfo, data)
            val progress = adjustRecord.getFxDataByIndex(data)
            val adjustBean = _adjustUIBean.value ?: return@forEachIndexed
            val itemViewData = adjustBean.menuItemListData[data]
            var modified = false
            if (itemViewData.isDisableStyle) {
                itemViewData.isDisableStyle = false
                modified = true
            }
            //如果之前的currentProgress与设置的progress数据不一样就进行刷新
            if (currentProgress != progress) {
                itemViewData.oldLoadProgress = currentProgress
                itemViewData.loadProgress = progress
                modified = true
            }
            if (modified) {
                adjustBean.apply {
                    id = AdjustBeanId.UPDATE_ITEM_VIEW
                    itemPosition = data
                }.also { _adjustUIBean.value = it }
            }
        }
        emitAdjustFx(POSITION_AUTO_ENHANCE, enhance)
    }

    /**
     * 设置自动调节关联的子项的算法值
     * @param aitInfo 算法值的封装对象
     * @param index 子算法的索引值
     */
    private fun setAutoEnhanceSubitemValues(aitInfo: AitInfo, index: Int) {
        when (index) {
            POSITION_BRILLIANCE -> adjustRecord.setValueByIndex(aitInfo.brilliance.toFloat(), index, AdjustState.ENABLE)
            POSITION_EXPOSURE -> adjustRecord.setValueByIndex(aitInfo.exposure.toFloat(), index, AdjustState.ENABLE)
            POSITION_CONTRAST -> adjustRecord.setValueByIndex(aitInfo.contrast.toFloat(), index, AdjustState.ENABLE)
            POSITION_SHADOW -> adjustRecord.setValueByIndex(aitInfo.shadow.toFloat(), index, AdjustState.ENABLE)
            POSITION_HIGHLIGHT -> adjustRecord.setValueByIndex(aitInfo.highlight.toFloat(), index, AdjustState.ENABLE)
            POSITION_BRIGHTNESS -> adjustRecord.setValueByIndex(aitInfo.brightness.toFloat(), index, AdjustState.ENABLE)
            POSITION_BLACK_POINT -> adjustRecord.setValueByIndex(aitInfo.blackPoint.toFloat(), index, AdjustState.ENABLE)
            POSITION_SATURATION -> adjustRecord.setValueByIndex(aitInfo.saturation.toFloat(), index, AdjustState.ENABLE)
            POSITION_VIBRANCE -> adjustRecord.setValueByIndex(aitInfo.vibrance.toFloat(), index, AdjustState.ENABLE)
            POSITION_TEMPERATURE -> adjustRecord.setValueByIndex(aitInfo.warmth.toFloat(), index, AdjustState.ENABLE)
            POSITION_TINT -> adjustRecord.setValueByIndex(aitInfo.tint.toFloat(), index, AdjustState.ENABLE)
        }
    }

    /**
     * 初始化菜单栏列表适配器所需的数据，按钮id,icon,文本
     * @return 菜单栏列表ui数据
     */
    private fun queryMenuAdapterData(): ArrayList<EditorMenuItemViewData> {
        val idArray: IntArray = ResourceUtils.getResourceIdArrays(
            getApplication() as Context,
            BaseR.array.picture3d_editor_array_enhance_adjust_state_id_array
        )
        val iconArray: IntArray = ResourceUtils.getResourceIdArrays(
            getApplication() as Context,
            BaseR.array.picture3d_editor_array_enhance_adjust_state_icon_array
        )
        val textArray: IntArray = ResourceUtils.getResourceIdArrays(
            getApplication() as Context,
            BaseR.array.picture3d_editor_array_enhance_adjust_state_text_array
        )
        val bean = ItemViewBean(idArray, iconArray, textArray)
        val filterIdArray = arrayListOf<Int>()
        val filterIconArray = arrayListOf<Int>()
        val filterTextArray = arrayListOf<Int>()
        bean.idArray?.forEachIndexed { index, id ->
            filterIdArray.add(id)
            bean.iconArray?.get(index)?.also {
                filterIconArray.add(it)
            }
            bean.textArray?.get(index)?.also {
                filterTextArray.add(it)
            }
        }
        return EditorUIConfig.initEditorMenuAdapterData(
            filterIdArray,
            filterIconArray,
            filterTextArray
        )
    }

    /**
     * 更新指定索引位置itemview的进度数据
     * @param index item的位置索引
     * @param progress 进度值
     */
    private fun updateItemViewDataWhenScroll(index: Int, progress: Int) {
        val itemViewData = _adjustUIBean.value?.menuItemListData?.get(index)
        itemViewData?.apply {
            centerText = if (index == POSITION_AUTO_ENHANCE) {
                TextUtil.EMPTY_STRING
            } else {
                adjustRecord.getAttrByIndex(index).first.toInt().toString()
            }
            // 如果是不可用状态，则更新除“自动调节”外的按钮状态
            if ((index != POSITION_AUTO_ENHANCE) && isDisableStyle) {
                isDisableStyle = false
                adjustRecord.switchState(index, true)
                adjustRecord.lastIndex = adjustRecord.currentIndex
            }
            loadProgress = division(progress.toFloat(), POSITIVE_FULL_PROGRESS.toFloat())
            oldLoadProgress = division(progress.toFloat(), POSITIVE_FULL_PROGRESS.toFloat())
        }
    }

    /**
     * 计算两个浮点数相除返回结果
     */
    private fun division(x: Float?, y: Float?): Float {
        if ((x == null) || (y == null) || (y == 0f)) {
            return 0f
        }
        return x / y
    }

    /**
     * 将效果数据下发给到美摄
     * @param index 指定菜单项的索引值
     * @param autoEnhance 是否开启自动调节功能
     */
    private fun emitAdjustFx(index: Int, autoEnhance: Boolean) {
        val time = getClipStartEndTime(editorEngine) ?: run {
            GLog.d(TAG, LogFlag.DL) { "[emitAdjustFx] getClipStartEndTime returned null" }
            return
        }
        adjustParams?.apply {
            enhance = autoEnhance
            algoDataList = adjustRecord.createFxDataByIndex(index)
            nameList = adjustRecord.collectFxNameByIndex(index)
            effectDataList = adjustRecord.collectEffectMap(index)
            startTime = time.first
            endTime = time.second
            nameZh = fxNameZh
            nameCh = fxNameZh
            nameEn = fxNameEn
        } ?: run {
            adjustParams = AdjustParams(
                autoEnhance,
                adjustRecord.createFxDataByIndex(index),
                adjustRecord.collectFxNameByIndex(index),
                adjustRecord.collectEffectMap(index),
                time.first,
                time.second,
                fxNameZh,
                fxNameZh,
                fxNameEn
            )
        }
        adjustVideoEffect = currentClip?.setAdjust(adjustParams)
        editorControlView.seekTimeline(editorEngine.timelineCurrentPosition, 0, true)
    }

    data class ItemViewBean(val idArray: IntArray? = null, val iconArray: IntArray? = null, val textArray: IntArray? = null)

    class AdjustVMFactory(
        val application: Application,
        val engine: EditorEngine,
        val editorControlView: EditorControlView,
        val timelineViewModel: TimelineViewModel?
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(AdjustVM::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return AdjustVM(application, engine, editorControlView, timelineViewModel) as T
            } else {
                throw IllegalArgumentException("Unknown ViewModel class")
            }
        }
    }

    companion object {
        private const val TAG = "AdjustVM"
        private const val EN = "en"
        private const val ZH = "zh"

        /**
         * "调节"英文名称
         */
        val fxNameEn by lazy { getTextByLocale(ContextGetter.context, EN, R.string.videoeditor_adjust) }
    }
}