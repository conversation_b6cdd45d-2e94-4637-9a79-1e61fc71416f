/*******************************************************
 * Copyright 2010 - 2012 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description	:
 * History  	:
 * (ID, Date, Author, Description)
 *V1.0,2016-12-5,xiuhua.ke create
 *******************************************************/
package com.oplus.breakpad;


import com.oplus.gallery.standard_lib.util.loadLibrary.LoadLibraryTool;

import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * <p>
 * 2016-12-5
 */
public class BreakpadManager {

    static {
        LoadLibraryTool.loadLibrary("media_breakpad_jni");
    }

    public static native void registerBreakpad(String dumpCached);

    public static native void registerBreakpadWithSignals(String dumpCached, int[] signals);

    public static native void setCachedFile(String cachedFile);

    public static native void setRename(boolean isRename);

    public static native void setFilePath(String path);

    public static native void setCrashFolder(String folderPath);

    public static native synchronized void guardScene(@NotNull String scene, String carriedMsg);

    public static native synchronized void guardSceneWithTid(int tid, @NotNull String scene, String carriedMsg);

    public static native synchronized void unguardScene(@NotNull String scene, String carriedMsg);

    public static native synchronized void unguardSceneWithTid(int tid, @NotNull String scene, @NotNull String carriedMsg);

    public static native int getNativeTid();

    public static native void testCrash();

    public static native void fakeCrash(String scene,String carriedMsg);

}
