/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ClipIndexInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.Objects;

public class ClipIndexInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @SerializedName("trackIndex")
    public int mTrackIndex;
    @SerializedName("clipIndex")
    public int mClipIndex;


    public ClipIndexInfo() {
    }

    public ClipIndexInfo(int trackIndex, int clipIndex) {
        this.mTrackIndex = trackIndex;
        this.mClipIndex = clipIndex;
    }

    @NonNull
    @Override
    public String toString() {
        return "ClipIndexInfo{"
                + ", trackIndex='"
                + mTrackIndex
                + ", clipIndex='"
                + mClipIndex
                + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if ((o == null) || (getClass() != o.getClass())) {
            return false;
        }
        ClipIndexInfo that = (ClipIndexInfo) o;
        return (mTrackIndex == that.mTrackIndex)
                && (mClipIndex == that.mClipIndex);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mTrackIndex, mClipIndex);
    }
}
