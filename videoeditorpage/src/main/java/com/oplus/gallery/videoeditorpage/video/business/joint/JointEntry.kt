/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - JointEntry
 ** Description: 选择的素材实体
 ** Version: 1.0
 ** Date : 2025/05/21
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >      <desc>
 **  80320709      2025/05/21  1.0             created
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.joint

import androidx.core.net.toUri
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getMediaHdrType
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.utils.PhotoHdrTypeUtils
import com.oplus.gallery.business_lib.util.formatNvmFilePath
import com.oplus.gallery.business_lib.videoedit.VideoSpecHelper
import com.oplus.gallery.foundation.fileaccess.FileConstants.MediaType.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext

/**
 * 素材实体数据
 */
data class JointEntry(
    /** 视频片段类型 */
    val videoClipType: Int,
    /** 宽  */
    var width: Int = 0,
    /** 高 */
    var height: Int = 0,
    /** 内容路径 */
    val path: String = TextUtil.EMPTY_STRING,
    /** 文件路径 */
    val filePath: String = TextUtil.EMPTY_STRING,
    /** 系统文件路径 */
    val systemFilePath: String = TextUtil.EMPTY_STRING,
    /** 时长 */
    val duration: Long = 0,
    /** 是否需要编码 */
    val needConvert: Boolean = false,
    /**
     * 媒体Hdr类型
     */
    val mediaHdrType: Int = PhotoHdrTypeUtils.SDR_PHOTO,
    /**
     * 是否是实况图
     */
    val isOlivePhoto: Boolean
) {
    companion object {
        /**
         * 视频编辑选图需要的数据包
         * @param mediaItem 媒体对象
         * @return 数据包
         */
        fun fromMediaItem(mediaItem: MediaItem): JointEntry {
            val mediaType = if (mediaItem.mediaType == MEDIA_TYPE_VIDEO) VIDEO_CLIP_TYPE_AV else VIDEO_CLIP_TYPE_IMAGE
            val size = VideoSpecHelper.getRotated(mediaItem.rotation, mediaItem.width, mediaItem.height)
            var mediaDuration = EditorEngineGlobalContext.getInstance().getDuration(mediaItem.filePath)
            var mediaUri = mediaItem.contentUri.toString()
            val isOlivePhoto = mediaItem.isOlivePhoto()
            if (isOlivePhoto) {
                OLiveDecode.create(mediaItem.filePath).decode()?.microVideo?.let {
                    mediaUri = formatNvmFilePath(mediaUri.toUri(), it.offset, it.length)
                    mediaDuration = EditorEngineGlobalContext.getInstance().getDuration(mediaUri)
                }
                if (mediaDuration == AppConstants.Number.NUMBER_0.toLong()) {
                    mediaDuration = mediaItem.duration.toLong() * TimeUtils.TIME_1_MS_IN_US
                }
            }
            return JointEntry(
                mediaType,
                size.width,
                size.height,
                mediaUri,
                mediaUri,
                mediaItem.filePath,
                mediaDuration,
                false,
                getMediaHdrType(mediaItem),
                isOlivePhoto
            )
        }
    }
}
