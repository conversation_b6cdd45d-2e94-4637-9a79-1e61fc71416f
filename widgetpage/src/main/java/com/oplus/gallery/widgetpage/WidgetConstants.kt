/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetConstants.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/5        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage

import android.content.Context
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

class WidgetConstants {
    // 六种不同尺寸的卡片的type，对应widget_choice_2x2.xml等文件里的type属性值
    object Type {
        val WIDGET_TYPE_CHOICE_2x2 = ContextGetter.context.resources.getInteger(R.integer.type_widget_choice_2x2)
        val WIDGET_TYPE_CHOICE_4x2 = ContextGetter.context.resources.getInteger(R.integer.type_widget_choice_4x2)
        val WIDGET_TYPE_CHOICE_4x4 = ContextGetter.context.resources.getInteger(R.integer.type_widget_choice_4x4)
        val WIDGET_TYPE_CUSTOM_2X2 = ContextGetter.context.resources.getInteger(R.integer.type_widget_custom_2x2)
        val WIDGET_TYPE_CUSTOM_4X2 = ContextGetter.context.resources.getInteger(R.integer.type_widget_custom_4x2)
        val WIDGET_TYPE_CUSTOM_4X4 = ContextGetter.context.resources.getInteger(R.integer.type_widget_custom_4x4)
    }

    object Layout {
        const val NO_PERMISSION_2_2 = "widget_no_permission_2x2.json"
        const val NO_PERMISSION_4_2 = "widget_no_permission_4x2.json"
        const val NO_PERMISSION_4_4 = "widget_no_permission_4x4.json"
        const val CUSTOM_EMPTY = "widget_custom_empty.json"
        const val RECOMMENDED_EMPTY = "widget_choice_empty_2x2.json"
        const val RECOMMENDED_EMPTY_4_2 = "widget_choice_empty_4x2.json"
        const val RECOMMENDED_EMPTY_4_4 = "widget_choice_empty_4x4.json"
        const val NO_DATA_EMPTY = "widget_no_data_2x2.json"
        const val NO_DATA_EMPTY_4_2 = "widget_no_data_4x2.json"
        const val NO_DATA_EMPTY_4_4 = "widget_no_data_4x4.json"
        const val IMAGE = "widget_image.json"
    }

    object ParamKey {
        const val CARD_ID = "cardId"
        const val CARD_TYPE = "cardType"
        const val HOST_ID = "hostId"
    }

    object DisplayList {
        const val MAX_RECOMMENDED_LIST_SIZE = 20
        const val MAX_CUSTOM_LIST_SIZE = 50
        val SHARED_DISPLAY_LIST = arrayOf(GalleryStore.WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED)
    }

    object Display {
        const val DISPLAY_COUNT_INFO_MAX_SIZE = 2
    }

    object Action {
        /**
         * 负一屏调起相册权限
         */
        const val ACTION_GALLERY_AUTHORIZE_PERMISSIONS = "oplus.intent.action.GALLERY_AUTHORIZE_PERMISSIONS"

        /**
         * 负一屏调起相册自选图片管理
         */
        const val ACTION_WIDGET_CUSTOM_PICTURE_MANAGER = "oplus.intent.action.WIDGET_CUSTOM_PICTURE_MANAGER"

        /**
         * 无精选图片时提示用户
         */
        const val ACTION_WIDGET_CHOICE_PICTURE_MANAGER = "oplus.intent.action.WIDGET_CHOICE_PICTURE_MANAGER"

        /**
         * 监听卖场模式重启广播
         */
        const val ACTION_BROADCAST_OPPO_DAYDREAM_VIDEO_CAMERA = "com.oppo.daydreamvideo.REQUEST_RESTORE_DATA"
        const val ACTION_BROADCAST_OPLUS_DAYDREAM_VIDEO_CAMERA = "com.oplus.daydreamvideo.REQUEST_RESTORE_DATA"
    }

    object SPName {
        const val LAST_GENERATE_RECOMMENDED_LIST_TIME = "last_generate_recommended_list_time"
    }

    object LastTime {
        const val DELAY_GENERATE_RECOMMENDED_LIST = TimeUtils.TIME_5_SEC_IN_MS.toLong()
        const val DELAY_GENERATE_RECOMMENDED_LIST_WHEN_SELL_MODE_BR = TimeUtils.TIME_10_SEC_IN_MS.toLong()
    }

    object BundleKey {
        const val KEY_TIME = "time"
        const val KEY_LOCATION = "location"
        const val KEY_TIME_SIZE = "timeSize"
        const val KEY_MASKING_LAYER = "maskingLayer"
        const val KEY_LOCATION_SIZE = "locationSize"
        const val KEY_WIDGET_WIDTH = "widgetWidth"
    }

    object Dir {
        private const val DIR_WIDGET_IMAGE = "widget_image"

        fun getWidgetImageDir(context: Context): File = File(context.cacheDir, DIR_WIDGET_IMAGE)
    }
}