/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ImageDecoderWrapper.kt
 * Description:
 * Version: 1.0
 * Date: 2022/02/19
 * Author: Wangrunxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Wangrunxin@Apps.Gallery3D      2022/02/19      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.ColorSpace
import android.graphics.ImageDecoder
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PixelFormat
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.PostProcessor
import android.graphics.Rect
import android.graphics.drawable.Drawable
import java.io.IOException
import android.graphics.ImageDecoder.OnHeaderDecodedListener

internal class ImageDecoderWrapper(private val source: ImageDecoder.Source) {

    companion object {
        // 尺寸设置监听器Key值
        private const val KEY_SIZE = "size"

        // 清晰度监听器Key值
        private const val KEY_SAMPLE_SIZE = "sampleSize"

        // 内存分配设置监听器Key值
        private const val KEY_ALLOCATOR = "allocator"

        // 裁剪尺寸设置监听器Key值
        private const val KEY_CROP = "crop"

        // 色彩空间设置监听器角标
        private const val KEY_COLOR_SPACE = "colorSpace"

        // 仅解码Alpha通道设置监听器角标
        private const val KEY_ALPHA_MASK_ENABLED = "alphaMaskEnabled"

        // 内存大小策略设置监听器角标
        private const val KEY_MEMORY_SIZE_POLICY = "memorySizePolicy"

        // 是否可变设置监听器角标
        private const val KEY_IS_MUTABLE_REQUIRED = "isMutableRequired"

        // 位图是否未预乘的像素设置监听器角标
        private const val KEY_UNPREMULTIPLIED = "unpremultiplied"

        private const val KEY_HEADER_DECODED_LISTENER = "onHeaderDecodedListener"

        private fun removeListenerIfExist(key: String, listenerMap: MutableMap<String, ImageDecoder.OnHeaderDecodedListener>) {
            if (listenerMap.containsKey(key)) listenerMap.remove(key)
        }
    }

    // 监听器Map
    private val decodeListenerMap: MutableMap<String, ImageDecoder.OnHeaderDecodedListener> = mutableMapOf()

    // 不完全解码监听器。若触发并返回true，则展示不完整的图片，缺损区域将会是空白
    private var partialImageListener: ImageDecoder.OnPartialImageListener? = null

    // 图片后处理
    private var postProcessor: PostProcessor? = null

    // 真正的头加载监听器，在里面遍历调用定义的方法
    private val headerDecodedListener by lazy {
        // 图片预处理
        OnHeaderDecodedListener { decoder, info, source ->
            postProcessor?.let { decoder.postProcessor = postProcessor }

            partialImageListener?.let { decoder.onPartialImageListener = partialImageListener }

            decodeListenerMap.forEach { it.value.onHeaderDecoded(decoder, info, source) }
        }
    }

    /**
     * 设置圆角图片，会覆盖之前设置的postProcessor
     * @param roundX X轴
     * @param roundY Y轴
     */
    fun setRoundCorners(roundX: Float, roundY: Float): ImageDecoderWrapper {
        postProcessor = PostProcessor { canvas ->
            val path = Path().apply {
                fillType = Path.FillType.INVERSE_EVEN_ODD
                addRoundRect(
                    0F, 0F, canvas.width.toFloat(), canvas.height.toFloat(), roundX, roundY,
                    Path.Direction.CW
                )
            }
            Paint().apply {
                isAntiAlias = true
                color = Color.TRANSPARENT
                xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC)
            }.let {
                canvas.drawPath(path, it)
            }
            PixelFormat.TRANSLUCENT
        }
        return this
    }

    /**
     * 设置不完整加载监听器
     * @param listener
     * @return
     */
    fun setPartialImageListener(listener: ImageDecoder.OnPartialImageListener?): ImageDecoderWrapper {
        partialImageListener = listener
        return this
    }

    /**
     * 设置头解码监听器
     * @param listener
     * @return
     */
    fun setHeadDecodeListener(listener: ImageDecoder.OnHeaderDecodedListener): ImageDecoderWrapper {
        removeListenerIfExist(KEY_HEADER_DECODED_LISTENER, decodeListenerMap)
        decodeListenerMap[KEY_HEADER_DECODED_LISTENER] = listener
        return this
    }

    /**
     * 设置图片后处理方法
     * @param postProcessor
     * @return
     */
    fun setPostProcessor(postProcessor: PostProcessor?): ImageDecoderWrapper {
        this.postProcessor = postProcessor
        return this
    }

    /**
     * 设置图片压缩
     * @param sampleSize
     * @return
     */
    fun setSampleSize(sampleSize: Int): ImageDecoderWrapper {
        removeListenerIfExist(KEY_SAMPLE_SIZE, decodeListenerMap)
        decodeListenerMap[KEY_SAMPLE_SIZE] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.setTargetSampleSize(sampleSize)
        }
        return this
    }

    /**
     * 设置图片尺寸
     * @param width 宽
     * @param height 高
     */
    fun setSampleSize(width: Int, height: Int): ImageDecoderWrapper {
        removeListenerIfExist(KEY_SIZE, decodeListenerMap)
        decodeListenerMap[KEY_SIZE] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.setTargetSize(width, height)
        }
        return this
    }

    /**
     * 设置解码器内存分配方式
     * @param allocator 分配器
     */
    fun setAllocator(allocator: Int): ImageDecoderWrapper {
        removeListenerIfExist(KEY_ALLOCATOR, decodeListenerMap)
        decodeListenerMap[KEY_ALLOCATOR] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.allocator = allocator
        }
        return this
    }

    /**
     * 设置图片裁剪尺寸
     * @param subset 矩形数据
     * @return
     */
    fun setCrop(subset: Rect): ImageDecoderWrapper {
        removeListenerIfExist(KEY_CROP, decodeListenerMap)
        decodeListenerMap[KEY_CROP] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.crop = subset
        }
        return this
    }

    /**
     * 按照目标宽高比例设置图片裁剪尺寸（针对固定区域裁剪原图，用于固定页大小的PDF展示）
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @param imagesInfo 原图宽高数据
     * @return
     */
    fun setTargetSampleSize(targetWidth: Int, targetHeight: Int, imagesInfo: MutableList<String>): ImageDecoderWrapper {
        removeListenerIfExist(KEY_SIZE, decodeListenerMap)
        decodeListenerMap[KEY_SIZE] = OnHeaderDecodedListener { decoder, info, _ ->
            imagesInfo.add("${info.size.width}*${info.size.height}")
            val targetRatio = targetHeight.toFloat() / targetWidth.toFloat()
            val originRatio = info.size.height.toFloat() / info.size.width.toFloat()
            if (targetRatio > originRatio) {
                // 目标比例更窄，需要缩放原图的高度
                val scale = targetWidth.toFloat() / info.size.width
                decoder.setTargetSize(targetWidth, (info.size.height * scale).toInt())
            } else {
                // 目标比例更宽，需要缩放原图的宽度
                val scale = targetHeight.toFloat() / info.size.height
                decoder.setTargetSize((info.size.width * scale).toInt(), targetHeight)
            }
        }
        return this
    }

    /**
     * 设置图片色彩空间
     * @param colorSpace
     * @return
     */
    fun setColorSpace(colorSpace: ColorSpace): ImageDecoderWrapper {
        removeListenerIfExist(KEY_COLOR_SPACE, decodeListenerMap)
        decodeListenerMap[KEY_COLOR_SPACE] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.setTargetColorSpace(colorSpace)
        }
        return this
    }

    /**
     * 设置是否仅解码Alpha通道
     * @param enabled
     * @return
     */
    fun setDecodeAsAlphaMaskEnabled(enabled: Boolean): ImageDecoderWrapper {
        removeListenerIfExist(KEY_ALPHA_MASK_ENABLED, decodeListenerMap)
        decodeListenerMap[KEY_COLOR_SPACE] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.isDecodeAsAlphaMaskEnabled = enabled
        }
        return this
    }

    /**
     * 设置已解码位图的内存策略。
     * @param policy 内存策略值
     * @return
     */
    fun setMemorySizePolicy(policy: Int): ImageDecoderWrapper {
        removeListenerIfExist(KEY_MEMORY_SIZE_POLICY, decodeListenerMap)
        decodeListenerMap[KEY_MEMORY_SIZE_POLICY] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.memorySizePolicy = policy
        }
        return this
    }

    /**
     * 设置位图是否应可变
     * @param mutable 是否可变
     * @return
     */
    fun setMutableRequired(mutable: Boolean): ImageDecoderWrapper {
        removeListenerIfExist(KEY_IS_MUTABLE_REQUIRED, decodeListenerMap)
        decodeListenerMap[KEY_IS_MUTABLE_REQUIRED] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.isMutableRequired = mutable
        }
        return this
    }

    /**
     * 设置位图是否未预乘的像素
     * @param unpremultipliedRequired
     * @return
     */
    fun setUnpremultipliedRequired(unpremultipliedRequired: Boolean): ImageDecoderWrapper {
        removeListenerIfExist(KEY_UNPREMULTIPLIED, decodeListenerMap)
        decodeListenerMap[KEY_UNPREMULTIPLIED] = OnHeaderDecodedListener { decoder, _, _ ->
            decoder.isUnpremultipliedRequired = unpremultipliedRequired
        }
        return this
    }

    /**
     * 解码Drawable
     * @exception IOException
     * @return bitmap
     */
    @Throws(IOException::class)
    fun decodeDrawable(): Drawable {
        return ImageDecoder.decodeDrawable(source, headerDecodedListener)
    }

    /**
     * 解码Bitmap
     * @exception IOException
     * @return bitmap
     */
    @Throws(IOException::class)
    fun decodeBitmap(): Bitmap {
        return ImageDecoder.decodeBitmap(source, headerDecodedListener)
    }

    /**
     * 解码Bitmap
     * @exception IOException
     * @param listener 自定义监听回调
     * @return bitmap
     */
    @Throws(IOException::class)
    fun decodeBitmap(listener: OnHeaderDecodedListener): Bitmap {
        return ImageDecoder.decodeBitmap(source, listener)
    }
}