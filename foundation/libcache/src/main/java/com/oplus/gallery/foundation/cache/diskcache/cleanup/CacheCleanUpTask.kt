/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseCacheCleanUpTask.kt
 ** Description : 对磁盘缓存临时文件进行清理的任务基类
 ** Version     : 1.0
 ** Date        : 2023/07/09
 ** Author      : <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2023/07/09      1.0
 *********************************************************************************/
package com.oplus.gallery.foundation.cache.diskcache.cleanup

import androidx.annotation.WorkerThread
import androidx.core.net.toUri
import com.oplus.gallery.foundation.cache.diskcache.cleanup.strategy.ICacheCleanUpStrategy
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.file.File
import kotlin.system.measureTimeMillis

/**
 * 磁盘缓存清理任务基类，提供了默认的[retrieveCacheFiles]和[checkNeedCacheCleanUp]，其中[checkNeedCacheCleanUp]通过
 * 配置的清理策略来进行触发条件判断以及执行清理动作
 * @param cacheCleanUpStrategy 配置的缓存清理策略
 */
class CacheCleanUpTask(
    private val cacheCleanUpStrategy: ICacheCleanUpStrategy
) : ICacheCleanUpTask {
    private var totalCacheSize: Long = INVALID_TOTAL_CACHE_SIZE
    private var totalCount: Int = NO_FILE_COUNT

    @WorkerThread
    override fun run() {
        measureTimeMillis {
            val cacheFiles = retrieveCacheFiles()
            for (cacheFile in cacheFiles) {
                // 对给定的file检查是否符合清理策略，如果需要清理则执行清理策略的[ICacheCleanUpStrategy.cleanUpCacheFile]动作
                if (checkNeedCacheCleanUp(cacheFile)) {
                    performCacheCleanUp(cacheFile)
                }
            }
        }.let { costTime ->
            GLog.d(getTag()) { "[run] finish cache clean up, cost $costTime ms" }
        }
        return
    }

    /**
     * 对缓存目录[targetCacheDir]进行遍历，获取目录下的缓存文件。支持对多个路径下的文件进行遍历清理
     */
    @WorkerThread
    override fun retrieveCacheFiles(): List<File> {
        val fileList = ArrayList<File>()
        runCatching {
            // 获取具体策略类定义的缓存目录
            val targetCacheDir = cacheCleanUpStrategy.getTargetCleanUpDir()
            if (targetCacheDir.exists().not()) {
                GLog.e(getTag()) { "[traverseCacheFiles] targetCacheDir: $targetCacheDir, dir not exists" }
                return@runCatching
            }

            val walkCost = measureTimeMillis {
                targetCacheDir.file.walk().iterator().forEach {
                    if (it.isFile) fileList.add(File(it))
                }
            }
            GLog.d(getTag()) { "[traverseCacheFiles] walk finish for ${targetCacheDir.path}, cost $walkCost ms" }
        }.onFailure {
            GLog.e(getTag()) { "[traverseCacheFiles] walk cache dir failed, cause: $it" }
        }
        totalCacheSize = calculateFilesTotalSize(fileList)
        totalCount = fileList.size
        return fileList
    }

    private fun calculateFilesTotalSize(files: List<File>): Long {
        if (files.isEmpty()) {
            GLog.w(getTag()) { "[calculateFilesTotalSize] targetCacheDir: ${cacheCleanUpStrategy.getTargetCleanUpDir()}, no file found in dir" }
            return INVALID_TOTAL_CACHE_SIZE
        }
        var size: Long = 0
        files.forEach {
            size += it.length()
        }
        return size
    }

    @WorkerThread
    override fun checkNeedCacheCleanUp(cacheFileToCheck: File): Boolean {
        runCatching {
            return cacheCleanUpStrategy.shouldCleanUpCache(cacheFileToCheck, totalCacheSize, totalCount)
        }.onFailure {
            GLog.e(getTag()) { "[checkCacheCleanUp] check for file($cacheFileToCheck) failed!" }
        }
        return false
    }

    /**
     * 具体的清理行为代理给[cacheCleanUpStrategy]
     */
    @WorkerThread
    override fun performCacheCleanUp(cacheFileToClean: File) {
        runCatching {
            val cleanUpCost = measureTimeMillis {
                cacheCleanUpStrategy.cleanUpCacheFile(cacheFileToClean)
            }
            GLog.d(getTag()) { "[performCacheCleanUp] cost $cleanUpCost ms for cleaning up file cache: ${cacheFileToClean.path.toUri()}" }
        }.onFailure {
            GLog.e(getTag()) { "[performCacheCleanUp] clean up cache file($cacheFileToClean) failed!" }
        }
    }

    private fun getTag() = "${TAG}_${cacheCleanUpStrategy.getTag()}"

    companion object {
        private const val TAG = "CacheCleanUpTask"
        private const val INVALID_TOTAL_CACHE_SIZE = -1L
        private const val NO_FILE_COUNT = 0
    }
}