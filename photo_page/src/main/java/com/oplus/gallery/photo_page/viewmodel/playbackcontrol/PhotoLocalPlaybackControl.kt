/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoLocalPlaybackControl.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/5/18
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2022/5/18    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.photo_page.viewmodel.playbackcontrol

import android.view.SurfaceView
import com.oplus.gallery.basebiz.permission.helper.CTAHelper
import com.oplus.gallery.business_lib.helper.VideoAutoPlayHelper
import com.oplus.gallery.common.playbackcontrol.IPhotoPlaybackControl
import com.oplus.gallery.common.playbackcontrol.IPlaybackInfoChangedListener
import com.oplus.gallery.common.playbackcontrol.PlaybackRequest
import com.oplus.gallery.common.playbackcontrol.PlaybackState
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.hardware.audio.IAudioVolumeController
import com.oplus.gallery.standard_lib.codec.player.AVController

/**
 * 播放控制包装类，实现普通视频的播控控制
 * 注意：
 * - 视频播放默认的播控和拦截条件都在这个类实现
 * - 扩展的播放器可以继承此类或者直接实现IPhotoPlaybackable进行包装，例如：投屏、gif等
 */
internal open class PhotoLocalPlaybackControl(
    protected var playerController: AVController = AVController.EMPTY,
    private val playerEventListener: AVController.OnEventListener?,
    private val audioControllerGetter: () -> IAudioVolumeController?,
    private var playbackInfoChangedListener: IPlaybackInfoChangedListener,
    private var mediaSourceStateGetter: () -> MediaSourceState,
    private var isAllowPlay: (() -> Boolean)? = null,
    private var isInDetailsModeGetter: () -> Boolean = { false }
) : IPhotoPlaybackControl {

    open val tag: String = TAG

    /**
     * 当前播放器状态
     * ```
     * true: 此播放器正在播放，允许进行界面交互和控制
     * false: 此播放器停止播放，解除和界面的关联，不响应界面控制
     * ```
     */
    override var isActivated: Boolean = false
    override var shouldResetToStartWhenEnd: Boolean = false
    override val playerToken: Int get() = playerController.playerToken()
    protected var isSeeking: Boolean = false
    protected var seekingPosition: Long = INVALID_POSITION
    protected var isPlayingBeforeSeeking: Boolean = false
    protected var isPlayingBeforeWindowChanged: Boolean = false
    protected var recordedPosition: Long? = null

    /**
     * Audio controller
     */
    private val audioController by lazy { audioControllerGetter.invoke() }

    /**
     * 播放时是否静音, 默认静音, 即自动播放时静音
     * 如果音频焦点导致静音, 先反馈到player上
     * 如果player被控制静音, 由回调通知刷新标志位
     */
    private val shouldMutePlayer: Boolean
        get() = (audioController?.getAudioState() == IAudioVolumeController.AudioState.AUDIO_STATE_MUTE)

    /**
     * 由于焦点丢失导致的自动暂停，在焦点恢复后自动播放
     * 现在只有窗口焦点丢失或准备播放但窗口焦点丢失的时候记录
     */
    private var shouldPlayAfterGetFocus = false

    private var hasFocused = true

    private var playbackRequest = PlaybackRequest.INITIALIZE

    protected val internalPlayerEventListener = object : AVController.OnEventListener {

        override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
            if (playerController != avController) {
                GLog.d(tag, "[onError] received move out AVController")
                return
            }

            if (!isActivated) {
                GLog.d(tag, "[onError] avController not active")
                return
            }

            // 播放异常回调到外部
            playerEventListener?.onError(avController, what, extra, details)

            // do nothing
            GLog.d(tag, "[onError] what=$what, extra=$extra, details=$details")
        }

        override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            if (playerController != avController) {
                GLog.d(tag, "[onInfo] received move out AVController")
                return
            }

            if (!isActivated) {
                GLog.d(tag, "[onInfo] avController not active")
                return
            }

            // 播放信息回调到外部
            playerEventListener?.onInfo(avController, what, extra, details)

            getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()

            when (what) {
                AVController.INFO_PLAYING_INFO_READY -> {
                    val shouldAutoPlay = shouldAutoPlay()

                    GLog.d(
                        tag, "[onInfo] ready, recordedPosition=$recordedPosition autoPlay=${VideoAutoPlayHelper.isVideoAutoPlay} " +
                                "shouldAutoPlay=$shouldAutoPlay isInDetailsMode=${isInDetailsModeGetter()}"
                    )

                    // 当时长小于1s时，禁止循环播放
                    if (getDuration() < LOOP_PLAY_MIN_TIME) setLooping(false)

                    avController.setMute(shouldMutePlayer)

                    //  Marked: 张文明 临时方案，后续要解决，ready两次的问题
                    onReady()

                    // 自动播放且非控制式暂停
                    if (shouldAutoPlay
                        && (mediaSourceStateGetter.invoke().isReady)
                        && (playbackRequest != PlaybackRequest.PAUSE)
                    ) {
                        // 准备就绪后,自动播放流程
                        if (CTAHelper.isFirstShowCTA() || hasFocused.not()) {
                            shouldPlayAfterGetFocus = true
                        } else if (hasFocused && isInDetailsModeGetter().not()) { // 详情模式下，不直接播放，由详情页业务控制播放时机
                            changePlayerState(PlaybackState.PLAYING, null)
                        }
                    }
                }

                AVController.INFO_SOUND_CHANGED -> GLog.d(tag, "[onInfo] sound")

                else -> {
                    GLog.d(tag, "[onInfo] what=$what")
                    // ignore
                }
            }
        }

        override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
            if (playerController != avController) {
                GLog.d(tag, "[onPlaybackStateChanged] received moved out AVController")
                return
            }

            if (!isActivated) {
                GLog.d(tag, "[onPlaybackStateChanged] avController not active")
                return
            }

            // 状态改变回调到外部
            playerEventListener?.onPlaybackStateChanged(avController, state)

            GLog.d(tag, "[onPlaybackStateChanged] state=$state")
            getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
            when (state) {
                AVController.PlaybackState.STARTED -> {
                    if (isPlayerAtEndPosition()) {
                        resetPlayerToStartPosition()
                    } else {
                        getPlaybackInfoChangedListener()?.onScheduleRefreshPlaybackInfo()
                    }
                    audioController?.enableAudioFocusGrabIfUnmute()
                }

                AVController.PlaybackState.COMPLETED -> {
                    getPlaybackInfoChangedListener()?.onCancelScheduleRefreshPlaybackInfo()
                    audioController?.disableAudioFocusGrabIfUnmute()
                }

                AVController.PlaybackState.PAUSED,
                AVController.PlaybackState.STOPPED,
                AVController.PlaybackState.END,
                AVController.PlaybackState.ERROR -> {
                    getPlaybackInfoChangedListener()?.onCancelScheduleRefreshPlaybackInfo()
                    audioController?.disableAudioFocusGrabIfUnmute()
                }

                else -> {
                    /*
                    ignored
                    */
                }
            }
        }
    }

    private fun shouldAutoPlay(): Boolean {
        val shouldAutoPlay = VideoAutoPlayHelper.isVideoAutoPlay
        if (shouldAutoPlay.not()) {
            return false
        }
        if (isAllowPlay?.invoke() == false) {
            // 在线视频时，且当前是数据流量，则不能直接播放
            return false
        }
        return true
    }

    /////////////////// 以下为对外方法 //////////////////////

    override fun changePlayerState(playState: PlaybackState, requestState: PlaybackRequest?) {
        when (playState) {
            PlaybackState.PLAYING -> {
                if (isSeeking.not()) {
                    GLog.d(tag, LogFlag.DL) { "[play] shouldMutePlayer=$shouldMutePlayer playerController=$playerController" }
                    playerController.setMute(shouldMutePlayer)
                    playerController.play(playAfterPrepare = true, needPrepare = true)
                }
            }

            PlaybackState.PAUSED -> {
                GLog.d(tag, LogFlag.DL) { "[pause] playerController=$playerController" }
                playerController.pause()
            }

            PlaybackState.STOP -> {
                GLog.d(tag, LogFlag.DL) { "[stop]" }
                playerController.stop()
            }

            else -> GLog.d(TAG, LogFlag.DL) { "changePlayerState, ignore playbackState: $playState" }
        }
        requestState?.apply {
            playbackRequest = this
        }
    }

    protected open fun onReady() {
        //recordPosition()中已有position=null时seekTo(0)的判断，Ready时可recordedPosition有效才进行seekTo
        recordedPosition?.let {
            seekTo(it)
            recordedPosition = null
        }
    }

    override fun seekTo(position: Long, seekType: AVController.SeekType?) {
        val actualSeekType = seekType ?: if (isSeeking) AVController.SeekType.ENABLE_PREVIEW else AVController.SeekType.NORMAL
        GLog.d(tag) { "[seekTo] position=$position, pendingSeekType=$seekType, actualSeekType=$actualSeekType" }
        if (isSeeking) {
            // 播放器接口执行可能是异步的，seeking时记录进度，exitPreviewSeeking时使用此进度seek，不要使用当前播放器的进度。
            seekingPosition = position
        }
        playerController.seekTo(
            position = position,
            seekType = actualSeekType
        )
        getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
    }

    override fun getDuration(): Long = playerController.getDuration()

    override fun getCurrentPosition(): Long = recordedPosition ?: playerController.getCurrentPosition()

    override fun isPlaying(): Boolean = playerController.isPlaying()

    override fun isPlayerSeeking(): Boolean = isSeeking

    override fun setMute(isMute: Boolean) {
        GLog.d(tag, "[setMute] isMute: $isMute")
        playerController.setMute(isMute)
        getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
    }

    override fun isMute(): Boolean = playerController.isMute()

    override fun setLooping(looping: Boolean) = playerController.setLooping(looping)

    override fun setSpeedRate(speedRate: Float) = playerController.setSpeedRate(speedRate)

    override fun getCurrentPlayingInfo(): AVController.PlayingInfo = playerController.getCurrentPlayingInfo()

    override fun enableMiniView(enable: Boolean) = playerController.enableMiniView(enable)

    override fun setVideoScalingMode(mode: Int) = playerController.setVideoScalingMode(mode)

    override fun setSurfaceView(surfaceView: SurfaceView) = playerController.setSurfaceView(surfaceView)

    override fun enterPreviewSeeking() {
        if (!isSeeking) {
            GLog.d(tag, "[enterPreviewSeeking]")
            isSeeking = true
            isPlayingBeforeSeeking = isPlayerPlaying()
            seekingPosition = playerController.getCurrentPosition()

            if (isPlayingBeforeSeeking) {
                GLog.d(tag, "[enterPreviewSeeking] pause, playing before seeking")
                changePlayerState(PlaybackState.PAUSED, null)
            }
            playerController.seekTo(seekingPosition, AVController.SeekType.ENABLE_PREVIEW)
        }
    }

    override fun exitPreviewSeeking() {
        if (isSeeking) {
            GLog.d(tag, "[exitPreviewSeeking] isPlayingBeforeSeeking=$isPlayingBeforeSeeking")
            isSeeking = false
            playerController.seekTo(seekingPosition, AVController.SeekType.DISABLE_PREVIEW)
            seekingPosition = INVALID_POSITION
            if (isPlayingBeforeSeeking) {
                // 开启循环播放时，即使在 EndPosition 也需继续播放
                if (getCurrentPlayingInfo().isLooping.not() && isPlayerAtEndPosition()) {
                    if (shouldResetToStartWhenEnd) {
                        GLog.d(tag, "[exitPreviewSeeking] resetPlayerToStartPosition, because at end position")
                        resetPlayerToStartPosition()
                    }
                } else {
                    GLog.d(tag, "[exitPreviewSeeking] play, playing before seeking")
                    changePlayerState(PlaybackState.PLAYING, null)
                }
            } else {
                /**
                 * 存在这样一种情况：缩图轴开启&播放中，快速将缩图轴向左拖动两次，停止拖动后，视频继续播放，但是进度条没有跟随滚动,详细原因见commit msg.
                 * 此处再检查一遍播放状态，如果处于播放状态，手动触发一次 FrameCallback 的注册
                 */
                if (isPlayerPlaying()) {
                    GLog.i(TAG) { "[exitPreviewSeeking]Playing , schedule refresh PlaybackInfo" }
                    getPlaybackInfoChangedListener()?.onScheduleRefreshPlaybackInfo()
                }
            }
        }
    }

    /**
     * Marked by LinHui：焦点管理职责应放置在PhotoPlaybackControlManager中统一管理，后续需重做这一块逻辑
     * @param isFocused
     */
    override fun notifyWindowFocusChanged(isFocused: Boolean, isForceToContinuePlaying: Boolean) {
        GLog.d(tag, "[notifyWindowFocusChanged] isFocused=$isFocused " +
                "isForceToContinuePlaying=$isForceToContinuePlaying " +
                "shouldPlayAfterGetFocus=$shouldPlayAfterGetFocus isInDetailsMode=${isInDetailsModeGetter()}")
        hasFocused = isFocused
        if (isFocused) {
            if (shouldPlayAfterGetFocus) {
                shouldPlayAfterGetFocus = false
                if (isAllowPlay?.invoke() == false) {
                    GLog.d(tag, "[notifyWindowFocusChanged] mobileNetwork not allow to play")
                    return
                }
                if (!isInDetailsModeGetter.invoke()) { // 详情模式下，不直接播放，由详情页业务控制播放时机
                    changePlayerState(PlaybackState.PLAYING, null)
                }
            }
        } else {
            val isPlayingOrPreparingToPlay = (isPlayerPlaying()) || playerController.getCurrentPlayingInfo().shouldPlayAfterPrepared
            // 如果之前已经被丢失焦点导致暂停, 保持标志位
            shouldPlayAfterGetFocus = shouldPlayAfterGetFocus || isPlayingOrPreparingToPlay
            isPlayingBeforeWindowChanged = isPlayerPlaying()
            if (isPlayingBeforeWindowChanged && isForceToContinuePlaying.not()) {
                changePlayerState(PlaybackState.PAUSED, null)
            }
        }
    }

    override fun isInvalidPlayer(): Boolean = playerController == AVController.EMPTY

    override fun recordPosition(position: Long?) {
        GLog.d(tag) { "[recordPosition] current=$playerController, expect position=$position" }
        if (position == null) {
            playerController.seekTo(0L)
        }
        if (isInvalidPlayer()) {
            recordedPosition = null
            getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
            return
        }
        recordedPosition = position
    }

    override fun clearCurrentPlayer(avController: AVController?) {
        GLog.d(tag, LogFlag.DL) { "[clearCurrentPlayer] current=$playerController, expect=$avController" }

        if (isInvalidPlayer()) {
            getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
            return
        }
        if ((avController != null) && (avController != playerController)) {
            GLog.d(tag, "[clearCurrentPlayer] ignored, current player not equals expect player")
            return
        }

        changePlayerState(PlaybackState.PAUSED, null)
        isSeeking = false
        seekingPosition = 0
        isPlayingBeforeSeeking = false
        shouldPlayAfterGetFocus = false
        playerController.removeOnEventListener(internalPlayerEventListener)

        playerController = AVController.EMPTY
        getPlaybackInfoChangedListener()?.onCancelScheduleRefreshPlaybackInfo()
        getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
    }

    override fun changeCurrentPlayer(avController: AVController) {
        GLog.d(tag, LogFlag.DL) { "[changeCurrentPlayer] old=$playerController, new=$avController" }
        if (playerController == avController) {
            return
        }
        clearCurrentPlayer()
        playerController = avController
        changePlayerState(PlaybackState.INITIALIZED, null)
        playerController.addOnEventListener(internalPlayerEventListener)

        getPlaybackInfoChangedListener()?.onUpdatePlaybackInfo()
    }

    override fun shouldPlayAfterGetFocus(): Boolean {
        return shouldPlayAfterGetFocus
    }

    /////////////////// 以下为内部方法 //////////////////////

    protected fun isPlayerPlaying(): Boolean {
        return playerController.getCurrentPlayingInfo().isPlaying
    }

    protected fun isPlayerAtEndPosition(): Boolean {
        val playingInfo = playerController.getCurrentPlayingInfo()
        return playingInfo.currentPosition >= playingInfo.duration
    }

    protected fun resetPlayerToStartPosition() {
        playerController.seekTo(0, AVController.SeekType.NORMAL)
        getPlaybackInfoChangedListener()?.onCancelScheduleRefreshPlaybackInfo()
        changePlayerState(PlaybackState.PAUSED, null)
    }

    protected fun getPlaybackInfoChangedListener(): IPlaybackInfoChangedListener? = if (isActivated) playbackInfoChangedListener else null

    companion object {
        private const val TAG = "PhotoLocalPlaybackControl"
        private const val INVALID_POSITION = -1L
        /**
         * 循环播放最小时长
         */
        const val LOOP_PLAY_MIN_TIME: Long = 1000L
    }
}