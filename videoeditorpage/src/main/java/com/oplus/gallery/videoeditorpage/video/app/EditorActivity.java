/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorActivity
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.app;

import static com.oplus.gallery.addon.osense.CpuFrequencyManager.TIMEOUT_1S;
import static com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_IS_CANCEL_FROM_EDIT;
import static com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_PHOTO_POSITION_FOR_EDITOR;
import static com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_TRANSITION_THUMBNAIL;
import static com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_RECT;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.VIDEO_EDITOR_ACTIVITY;
import static com.oplus.gallery.business_lib.menuoperation.EditVideoAction.IS_MULTIPLE_EDIT_TYPE;
import static com.oplus.gallery.business_lib.menuoperation.EditVideoAction.KEY_IS_FROM_EDITOR_SDK;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.DIRECT_EXIT_FORMAT_NOT_SUPPORT;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.EXCEPTION_DIRECT_EXIT;
import static com.oplus.gallery.foundation.util.text.TextUtil.LEFT_SLASH;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.VideoEditor.VIDEO_EDITOR_OLIVE_SAVE_MAX_DURATION;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK;
import static com.oplus.gallery.framework.abilities.hardware.IScreen.DEFAULT_WINDOW_REFRESH_RATE;
import static com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_50_MS_IN_MS;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.FRAME_DURATION;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_AVFILE_INFO;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_AVFILE_KEYFRAME_INFO;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_CAPTION_FONT_INFO;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_ICON_ENGINE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_STREAMING_ENGINE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_WAVEFORM_ENGINE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.EngineState.STREAMING_ENGINE_STATE_PLAYBACK;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.EngineState.STREAMING_ENGINE_STATE_STOPPED;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper.getEnterFrom;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper.getSaveType;
import static com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.isMiddleAndLargeWindow;
import static com.oplus.gallery.videoeditorpage.utlis.ScreenUtils.getShowingNavigationBarHeight;
import static com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView.EDITOR_PREVIEW_STATUS_FINISH;
import static com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView.EDITOR_PREVIEW_STATUS_PLAY;
import static com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView.EDITOR_PREVIEW_STATUS_READY;
import static com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView.EDITOR_PREVIEW_STATUS_STOP;
import static com.oplus.gallery.videoeditorpage.video.business.preview.ui.PlayAndPauseButton.PlayAndPauseType.TYPE_PAUSE;
import static com.oplus.gallery.videoeditorpage.widget.EditTimelineView.STATE_PREVIEW;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.RectF;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Size;
import android.view.Gravity;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.lifecycle.Lifecycle;

import com.oplus.gallery.addon.osense.CpuFrequencyManager;
import com.oplus.gallery.basebiz.constants.IntentConstant;
import com.oplus.gallery.basebiz.constants.RouterConstants;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser;
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter;
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm;
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant;
import com.oplus.gallery.foundation.ui.transition.ITransitionBoundsProvider;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.multiprocess.TransBitmapBinder;
import com.oplus.gallery.foundation.util.systemcore.IntentUtils;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.router_lib.Starter;
import com.oplus.gallery.router_lib.annotations.RouterNormal;
import com.oplus.gallery.router_lib.meta.PostCard;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ISeekingListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IStreamingEngineListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.player.IVideoPlayerListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.waveform.WaveformDataCacheManager;
import com.oplus.gallery.videoeditorpage.base.EditorStateContext;
import com.oplus.gallery.videoeditorpage.common.broadcast.SDCardBroadcastReceiver;
import com.oplus.gallery.videoeditorpage.common.event.EventIndex;
import com.oplus.gallery.videoeditorpage.common.event.LiveDataBus;
import com.oplus.gallery.videoeditorpage.common.ui.ExitConfirmDialog;
import com.oplus.gallery.videoeditorpage.common.ui.ExitDialogListener;
import com.oplus.gallery.videoeditorpage.common.ui.SuitableSizeG2TextView;
import com.oplus.gallery.videoeditorpage.controler.IVideoEditorUIScheme;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper;
import com.oplus.gallery.videoeditorpage.resource.manager.filter.FilterManager;
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil;
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper;
import com.oplus.gallery.videoeditorpage.utlis.VideoClipUtils;
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils;
import com.oplus.gallery.videoeditorpage.video.IRefreshRateUpdater;
import com.oplus.gallery.videoeditorpage.video.VideoEditorRefreshRateUpdater;
import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorProcessor;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum;
import com.oplus.gallery.videoeditorpage.video.business.cover.EditorCoverHelper;
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser;
import com.oplus.gallery.videoeditorpage.video.business.joint.JointHelper;
import com.oplus.gallery.videoeditorpage.video.business.manager.EnginePlayingTimeManager;
import com.oplus.gallery.videoeditorpage.video.business.output.EditorSaveState;
import com.oplus.gallery.videoeditorpage.video.business.output.OperationSaveHelper;
import com.oplus.gallery.videoeditorpage.video.business.output.SaveInfo;
import com.oplus.gallery.videoeditorpage.video.business.output.SaveInstanceInfo;
import com.oplus.gallery.videoeditorpage.video.business.output.SaveInstanceInfoKt;
import com.oplus.gallery.videoeditorpage.video.business.output.TimelineSaveInfo;
import com.oplus.gallery.videoeditorpage.video.business.output.task.ResultSaveFileData;
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveType;
import com.oplus.gallery.videoeditorpage.video.business.picker.PickerHelper;
import com.oplus.gallery.videoeditorpage.video.business.picker.data.PickerItemInfo;
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView;
import com.oplus.gallery.videoeditorpage.video.business.preview.ui.PlayAndPauseButton;
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper;
import com.oplus.gallery.videoeditorpage.video.business.transform.EditorTransformState;
import com.oplus.gallery.videoeditorpage.video.business.trim.EditorTrimState;
import com.oplus.gallery.videoeditorpage.video.business.trim.EditorTrimUIController;
import com.oplus.gallery.videoeditorpage.video.cache.CacheManager;
import com.oplus.gallery.videoeditorpage.video.config.UiConfigManager;
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView;
import com.oplus.gallery.videoeditorpage.video.controler.EditorStateManager;
import com.oplus.gallery.videoeditorpage.video.controler.ResourceLoader;
import com.oplus.gallery.videoeditorpage.video.invoker.InvokerManager;
import com.oplus.gallery.videoeditorpage.video.section.SectionManager;
import com.oplus.gallery.videoeditorpage.video.section.TopActionbarSection;
import com.oplus.gallery.videoeditorpage.video.ui.brighten.EditorVideoBrightenManager;
import com.oplus.gallery.videoeditorpage.widget.HorizontalListView;
import com.oplus.gallery.videoeditorpage.widget.PlaySeekBar;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

/**
 * 视频编辑一级页
 */
@RouterNormal(path = VIDEO_EDITOR_ACTIVITY)
public class EditorActivity extends BaseActivity implements EditorControlView.OnPipListener, EditorControlView.OnUISeekingTimelineListener,
        ISeekingListener, IStreamingEngineListener, IVideoPlayerListener, EditorEngine.ITimelineChangedListener,
        OperationSaveHelper.OperationSaveListener, EditorStateContext, View.OnClickListener,
        EditorStateManager.OnMonitorStateChangeForShowMaterialSelectListener, EditorStateManager.OnStateResultListener,
        MeicamEngineLimiter.LimitAble {
    public static final String TAG = "EditorActivity_TAG";
    public static final float ONE_FLOAT_VALUE = 1.0f;
    public static final int INVALID_VIDEO_ID = -1;
    public static final int REQUEST_CODE_EXTRACT_REPLACE = 600;
    public static final int REQUEST_CODE_CLIP_CUTTING = 800;
    public static final String KEY_PHOTO_EDITOR_INVOKER = "invoker";

    /**
     * 实况支持的最大时长（单位：秒）
     */
    private static final int OLIVE_SAVE_MAX_DURATION = 10;
    private static final int VIDEO_FRAME_RATE_HIGH = 120;

    public boolean mIsTimelineChange = true;

    private EditorEngine mEditorEngine;
    private EnginePlayingTimeManager mEnginePlayingTimeManager;

    /**
     * 是否通過editor sdk 跳入
     * 场景：1，从相册编辑跳入
     */
    private boolean mIsFromEditorSDK = false;

    /**
     * 是否从多选进入视频编辑
     */
    private boolean mIsMultipleEdit = false;
    private OperationSaveHelper mOperationSaveHelper;
    private EditorControlView mControlBar;
    private FrameLayout mFbEditorBottom;
    private EditorPreviewView mLivewindow;

    private long mTimelineDuration = 0;
    private long mPlayStartTimeStamp = -1;
    private ViewGroup mRootView;
    private LinearLayout mOperationLayout;
    private ImageView mUndoIcon;
    private ImageView mRevertIcon;

    private PlayAndPauseButton mPlayAndPauseBtn;
    private SuitableSizeG2TextView mTimeCodeText;

    /**
     * 菜单面板上边缘的播放条
     */
    private PlaySeekBar mPlaySeekBar;
    private long mCurrentVideoId = INVALID_VIDEO_ID;
    private IVideoEditorUIScheme mVideoEditorUIScheme;

    /**
     * 大图进编辑位置规则封装
     */
    private ITransitionBoundsProvider mPhotoPositionForEditor = null;

    /**
     * 编辑回调源
     */
    private String mEditorInvoker;
    private String mActivityCreateTime;
    private SDCardBroadcastReceiver mStopPreviewReceiver;
    private boolean mIsPlayAfterWindowAnim = false; // decide the action after ratio change. disposable variable
    private AudioFocusRequest mAudioFocusRequest = null;
    private ExitConfirmDialog mExitConfirmDialog;
    private long mTabMontageSelectTime = 0L;
    private EditorActivityPanelWrapper mCartoonWrapper;
    private boolean mGoToSaveWaitEngineStopped;

    /**
     * 承载activity上 actionbar、preview、controlBar 三大块业务
     */
    private SectionManager mSectionManager;

    /**
     * 不同业务调用管理器
     */
    private InvokerManager mInvokerManager;

    /**
     * 播放显示的时间格式
     */
    private String[] mTimeTextFormat;

    /**
     * 视频提亮
     */
    private EditorVideoBrightenManager mVideoBrightenManager;
    private ResourceLoader mResourceLoader;

    /**
     * 屏幕刷新率更新器
     */
    private IRefreshRateUpdater mRefreshRateUpdater;

    /**
     * 首次进入视频编辑时，会切换到剪辑页，这个场景不需要动画
     */
    private boolean mIsChangedState = true;

    private BaseActivity.OnUserInteractionListener mOnUserInteractionListener = new BaseActivity.OnUserInteractionListener() {
        @Override
        public void onUserInteraction(@NonNull InteractionType type) {
            if (mVideoBrightenManager != null) {
                mVideoBrightenManager.handlerUserInteraction(type);
            }
        }
    };

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        TrackHelper.INSTANCE.reset();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        GLog.d(TAG, LogFlag.DL, "[onCreate]");
        MeicamEngineLimiter.getInstance().register(this);
        CpuFrequencyManager.setAction(CpuFrequencyManager.Action.ACTIVITY_START, TIMEOUT_1S);
        super.onCreate(savedInstanceState);
        setScreenOrientation();
        CacheManager.INSTANCE.release();
        initIntentData();
        initEngineAndTimeline(savedInstanceState);
        mRefreshRateUpdater = new VideoEditorRefreshRateUpdater(getApplicationContext());
        initMainUI();
        AnimatorProcessor.INSTANCE.registerTouchInterceptor(this);

        mResourceLoader = new ResourceLoader(this);
        mResourceLoader.load(mEditorEngine);
    }

    /**
     * 获取标题栏切片
     *
     * @return 返回标题栏切片
     */
    private TopActionbarSection getTopActionbarSection() {
        if (mSectionManager == null) {
            return null;
        }

        return (TopActionbarSection) mSectionManager.getSection(TopActionbarSection.class);
    }

    private void setReselectMaterialListener() {
        EditorStateManager editorStateManager = mControlBar.getEditorStateManager();
        editorStateManager.setOnStateChangeListener(this);
    }

    protected void controlBarLayoutChange() {
        HorizontalListView tabView = findViewById(R.id.video_editor_menu_list);
        if (tabView != null) {
            tabView.setVisibility(View.VISIBLE);
        }
    }

    public void changePlaySeekBarVisibily(boolean isShow) {
        if (mPlaySeekBar != null) {
            if (isShow) {
                mPlaySeekBar.setVisibility(View.VISIBLE);
            } else {
                mPlaySeekBar.setVisibility(View.GONE);
            }
        }
    }

    private void handleStateWhenFinish(boolean isBack) {
        EditorBaseState currentEditorState = getCurrentEditorState();
        if (currentEditorState != null) {
            currentEditorState.backAndForwardHandle(isBack);
        }
    }

    private void showSaveOrCancelDialog() {
        if (mExitConfirmDialog == null) {
            mExitConfirmDialog = new ExitConfirmDialog(this);
            mExitConfirmDialog.setExitDialogListener(new ExitDialogListener() {
                @Override
                public void onCancelClick() {
                    mExitConfirmDialog.dismiss();
                    if (!mIsFromEditorSDK) {
                        finish();
                        trackReportWhenExit(VideoEditorTrackConstant.Value.CANCEL_SAVE_VIDEO);
                    }
                }

                @Override
                public void onAbandonAmendClick() {
                    if (!mIsFromEditorSDK) {
                        handleStateWhenFinish(true);
                        mExitConfirmDialog.dismiss();
                    }
                    finish();
                    trackReportWhenExit(VideoEditorTrackConstant.Value.CANCEL_SAVE_VIDEO);
                }
            });
        }
        mExitConfirmDialog.show();
    }

    private void registerBroadcast() {
        mStopPreviewReceiver = new SDCardBroadcastReceiver();
        mStopPreviewReceiver.setEditorEngine(mEditorEngine);
        IntentFilter itf = new IntentFilter();
        itf.addAction(Intent.ACTION_MEDIA_EJECT);
        itf.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        itf.addAction(Intent.ACTION_MEDIA_MOUNTED);
        itf.addAction(Intent.ACTION_MEDIA_REMOVED);
        itf.addAction(Intent.ACTION_MEDIA_SHARED);
        itf.addAction(Intent.ACTION_MEDIA_BAD_REMOVAL);
        itf.addDataScheme("file");
        registerReceiver(mStopPreviewReceiver, itf, VideoUtils.COMPONENT_SAFE_PERMISSION, null, RECEIVER_NOT_EXPORTED);
    }

    private void initControlBar() {
        mControlBar.setEditorEngine(mEditorEngine);
        mControlBar.setOperationSaveHelper(mOperationSaveHelper);
        mControlBar.setEnginePlayingTimeManager(mEnginePlayingTimeManager);

        EditorBaseState<?> state = mInvokerManager.getCustomizedState();
        if (state != null) {
            mControlBar.getEditorStateManager().changeState(state);
            return;
        }

        state = mControlBar.getEditorStateManager().enterMenuEditor(mControlBar);
        if (state != null) {
            state.setFbBottom(mFbEditorBottom, mLivewindow);
            TopActionbarSection topActionbarSection = getTopActionbarSection();
            if (topActionbarSection != null) {
                state.setActionBar(topActionbarSection.getActionBar());
            }
        }

        mOperationLayout.setVisibility(View.VISIBLE);
        updateTimelineDuration(mEditorEngine.getCurrentTimeline());
        updateTimelinePosition(0);
    }

    /**
     * 初始化视频引擎
     */
    private void initEngineAndTimeline(Bundle savedInstanceState) {
        // 1. 初始化引擎
        mEditorEngine = VideoParser.Companion.getInstance().getEditorEngine(); // 在EditorIntroActivity中，将初始化好的engine设置到了VideoParser中
        if (mEditorEngine == null) { // 重启的场景，不会走 EditorIntroActivity 流程，需要在这里初始化
            mEditorEngine = EditorEngineGlobalContext.getInstance().createEditorEngine();
            mEditorEngine.setMaxCountText(getString(R.string.videoeditor_import_over_max_count));
            boolean isSuccess = mEditorEngine.init(getApplicationContext());
            if (!isSuccess) {
                ToastUtil.showShortToast(R.string.videoeditor_start_senior_editor_failed);
                VideoEditorTrackerHelper.appendExceptionData(EXCEPTION_DIRECT_EXIT, DIRECT_EXIT_FORMAT_NOT_SUPPORT);
                finish();
                return;
            }
            mEditorEngine.initVideoFilterManager();
            VideoParser.Companion.getInstance().setEditorEngine(mEditorEngine);
        }
        final List<PickerItemInfo> pickerList = PickerHelper.INSTANCE.getPickerList();
        final boolean noOliveMedia = pickerList.stream().anyMatch(info -> !info.isOlivePhoto());
        mEditorEngine.setSaveType(noOliveMedia ? SaveType.VIDEO : SaveType.OLIVE);
        pickerList.forEach(VideoEditorTrackerHelper::buildTrackVideoData);

        // 2. 恢复或者创建时间线
        mInvokerManager = new InvokerManager(this, mEditorEngine, mEditorInvoker);
        Intent intent = mInvokerManager.parseIntent(getIntent());
        ITimeline lastSavedTimeline = null;
        SaveInstanceInfo saveInstanceInfo = SaveInstanceInfoKt.getSaveInstanceInfo(savedInstanceState);
        if (saveInstanceInfo != null) {
            lastSavedTimeline = saveInstanceInfo.getCurrentTimeline();
            VideoParser.Companion.getInstance().recoverVideoInfoMap(saveInstanceInfo.getVideoInfoMap());
        }
        /* 默认在Importing在已经创建好时间线了，这里为了兼容之前对重启activity时恢复timeline的流程
        TimelineGenerator 会优先获取IntentDataHolder的timeline */
        ITimeline initTimeline = new TimelineGenerator(getApplicationContext())
                .generate(intent, lastSavedTimeline);
        mEditorEngine.trySetTimeline(initTimeline);

        // 3. 初始化操作保存帮助类
        mOperationSaveHelper = new OperationSaveHelper(this, mEditorEngine);
        mOperationSaveHelper.setOperationSaveListener(this);
        mOperationSaveHelper.setSaveInfo(mCurrentVideoId = System.currentTimeMillis());
        mOperationSaveHelper.recoveryTimelineOperateInfo(saveInstanceInfo);
        mEnginePlayingTimeManager = new EnginePlayingTimeManager(mEditorEngine);
    }

    /**
     * 初始化Intent中数据
     */
    private void initIntentData() {
        Intent intent = getIntent();
        if (intent == null) {
            GLog.e(TAG, LogFlag.DL, "[initIntentData] failed to get intent");
            return;
        }

        mActivityCreateTime = System.currentTimeMillis() + "";
        mPhotoPositionForEditor = IntentUtils.getParcelableExtra(intent, KEY_PHOTO_POSITION_FOR_EDITOR);
        mEditorInvoker = IntentUtils.getStringExtra(intent, KEY_PHOTO_EDITOR_INVOKER);
        mIsFromEditorSDK = intent.getBooleanExtra(KEY_IS_FROM_EDITOR_SDK, false);
        mIsMultipleEdit = intent.getBooleanExtra(IS_MULTIPLE_EDIT_TYPE, false);
    }

    /**
     * 初始化视图
     */
    private void initView() {
        initUIScheme();
        mControlBar = findViewById(R.id.main_control_view);
        mControlBar.setOnPipListener(this);
        mControlBar.setOnUISeekingTimelineListener(this);
        mFbEditorBottom = findViewById(R.id.fb_editor_opt_layout);
        mLivewindow = findViewById(R.id.engine_preview_layout);
        mLivewindow.setNeedStopPlayer(true);
        mLivewindow.setEditorEngine(mEditorEngine);
        mLivewindow.setControlBar(mControlBar);
        controlBarLayoutChange();
        mOperationLayout = findViewById(R.id.operation_layout);
        mUndoIcon = findViewById(R.id.undo_view);
        mUndoIcon.setOnClickListener(this);
        mRevertIcon = findViewById(R.id.revert_view);
        mRevertIcon.setOnClickListener(this);
        setRTLOperatorIcon();
        mPlayAndPauseBtn = findViewById(R.id.btn_play_and_pause);

        mTimeCodeText = findViewById(R.id.main_time_code_string);
        mPlaySeekBar = findViewById(R.id.play_seekbar);
        mTimeTextFormat = getResources().getStringArray(R.array.picture_thumbnail_seek_bar_time_text_format);
    }

    /**
     * 设置RTL undo redo 图标，布局已经适配了RTL，但是图标要对换一下
     */
    private void setRTLOperatorIcon() {
        if (ResourceUtils.isRTL(this)) {
            mUndoIcon.setImageResource(R.drawable.editor_sub_action_bar_revert_selector);
            mRevertIcon.setImageResource(R.drawable.editor_sub_action_bar_undo_selector);
        }
    }

    /**
     * 初始化UI框架
     */
    private void initUIScheme() {
        mRootView = findViewById(R.id.video_editor_root);
        mVideoEditorUIScheme = mInvokerManager.createEditorUIScheme(mRootView);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        GLog.d(TAG, LogFlag.DL, "onWindowFocusChanged hasFocus: " + hasFocus);
        if (hasFocus) {
            int uiOptions = View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION;
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(uiOptions);
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        setScreenOrientation();
        setTheme(ThemeHelper.getTheme());
        if (mVideoEditorUIScheme != null) {
            mVideoEditorUIScheme.onConfigurationChanged(newConfig);
        }
    }

    /**
     * 根据屏幕宽度设置旋转规则
     */
    private void setScreenOrientation() {
        if ((getCurrentAppUiConfig().getOrientation().getCurrent() == Configuration.ORIENTATION_LANDSCAPE)
                && (getCurrentAppUiConfig().getWindowHeightDp().getCurrent() <= WindowSizeForm.STANDARD_LANDSCAPE_WINDOW_HEIGHT)) {
            // 如果进视频编辑之前是直板机横屏，则设置成竖屏
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        } else {
            // 小屏不能旋转屏幕
            setRequestedOrientation(isMiddleAndLargeWindow(this) ? ActivityInfo.SCREEN_ORIENTATION_SENSOR : ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    public void showPipRect(IVideoClip videoClip) {
        if (mLivewindow != null) {
            mLivewindow.showPipRectByVideoClip(videoClip);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        ITimeline toSaveTimeline = (getCurrentEditorState() != null) && (getCurrentEditorState().getBackupTimeline() != null)
                ? getCurrentEditorState().getBackupTimeline() : mEditorEngine.getCurrentTimeline();
        if ((mOperationSaveHelper != null) && (toSaveTimeline != null)) {
            mOperationSaveHelper.saveTimelineOperateInfoToHolder(outState, toSaveTimeline, mActivityCreateTime);
        }
        super.onSaveInstanceState(outState);
    }

    @Override
    public void onStackCountChanged(int undoCount, int revertCount) {
        updateUndoStatus(undoCount > 0);
        updateRevertStatus(revertCount > 0);

        TopActionbarSection topActionbarSection = getTopActionbarSection();
        if (topActionbarSection != null) {
            topActionbarSection.onStackCountChanged(undoCount > 0);
        }
    }

    /**
     * 设置封面后，封面图发生变化的回调
     *
     * @param coverBitmap   视频封面图片
     * @param coverTimestamp 视频封面的时间戳
     */
    public void onCoverChanged(Bitmap coverBitmap, Long coverTimestamp) {
        BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getMain(), CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
            TopActionbarSection topActionbarSection = getTopActionbarSection();
            if (topActionbarSection != null) {
                topActionbarSection.onCoverChanged();
            }
            return null;
        });
    }

    @Override
    public void onOperationSuccess(SaveInfo saveInfo) {
        EditorBaseState state = getCurrentEditorState();
        if (saveInfo instanceof TimelineSaveInfo) {
            if (state != null) {
                state.updateUndo(saveInfo);
            }
        }
    }

    private EditorBaseState stopPlayAndFling() {
        EditorStateManager editorStateManager = mControlBar.getEditorStateManager();
        if (editorStateManager == null) {
            return null;
        }
        EditorBaseState currentEditorState = getCurrentEditorState();
        if (currentEditorState == null) {
            return null;
        }
        if (currentEditorState instanceof EditorTrimState) {
            currentEditorState.stopTimelineViewFling();
        }
        EditorEngine editorEngine = currentEditorState.getEditorEngine();
        if (editorEngine != null) {
            editorEngine.stopPlayer();
        }
        return currentEditorState;
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onBackPressed() {
        if ((mCartoonWrapper != null) && (mCartoonWrapper.isAnimating())) {
            return;
        }
        if ((mFbEditorBottom != null) && (mFbEditorBottom.getVisibility() == View.VISIBLE)) {
            return;
        }

        EditorBaseState currentEditorState = stopPlayAndFling();
        if ((currentEditorState != null) && (!currentEditorState.canDoAction(EditorBaseState.ActionType.TYPE_BACK_PRESSED))) {
            return;
        }

        if ((currentEditorState != null) && (!currentEditorState.isPreview())) {
            mControlBar.getEditorStateManager().onBackPressed();
        } else {
            onDoBack();
        }
    }


    private void setWindowRefreshRateIfNeed(boolean isPlaying) {
        // 不支持此功能feature，do nothing
        if (!ConfigAbilityWrapper.getBoolean(FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK, false, false)) {
            return;
        }

        // 非120帧视频，无视。
        float fps = mEditorEngine.getCurrentTimeline().getFps();
        if (fps < PlayerAdapter.Companion.FPS_120_MIN_AVERAGE_FPS_THRESHOLD) {
            GLog.d(TAG, LogFlag.DF, "[setWindowRefreshRateIfNeed] no need for refreshRate setting for fps: " + fps);
            return;
        }

        if (isDestroyed() || isFinishing() || isRestricted()) {
            GLog.w(TAG, LogFlag.DL, "[setWindowRefreshRateIfNeed] no need to set refreshRate, "
                    + "because current activity is destroyed or restricted. Screen touch idle refresh rate should recover to DEFAULT now. "
                    + "Otherwise, ask OPPO DisplayFramework Team for what happened.");
            return;
        }

        Window window = getWindow();
        if (window == null) {
            GLog.w(TAG, LogFlag.DL, "[setWindowRefreshRateIfNeed] no need to set refreshRate, "
                    + "because no window in activity. Screen touch idle refresh rate should recover to DEFAULT now. "
                    + "Otherwise, ask OPPO DisplayFramework Team for what happened.");
            return;
        }
        mRefreshRateUpdater.setRefreshRate(window, isPlaying ? VIDEO_FRAME_RATE_HIGH : DEFAULT_WINDOW_REFRESH_RATE);
    }

    /**
     * 进行预览控件和actionbar和播控区的动画切换
     *
     * @param isEnter                  是否是从一级页面切换到二级页面
     * @param isSecondLevel            新页面的页面等级
     * @param showOperaIcon            是否显示操作图标（如撤销和重做图标）
     * @param isSkipAnim               当前页面是否跳过动画（其他页面默认为true，只有没有文字/调节/滤镜时，其他页面跳转这几个页面，这个值为true）
     * @param previousSkipAnim         上一个页面是否跳过动画
     * @param isSwitchToTransformState 是否切换至裁剪旋转页面的state
     */
    private void switchAnimation(
            boolean isEnter,
            boolean isSecondLevel,
            boolean showOperaIcon,
            boolean isSkipAnim,
            boolean previousSkipAnim,
            boolean isSwitchToTransformState
    ) {
        if ((mUndoIcon != null) && (mRevertIcon != null)) {
            if (showOperaIcon) {
                // 需要显示撤销重做图标
                if ((mUndoIcon.getVisibility() != View.VISIBLE)) {
                    mUndoIcon.setVisibility(View.VISIBLE);
                    mRevertIcon.setVisibility(View.VISIBLE);
                    AnimatorProcessor.INSTANCE.createEnterAnim(mUndoIcon, false, false);
                    AnimatorProcessor.INSTANCE.createEnterAnim(mRevertIcon, false, false);
                }
            } else {
                if ((mUndoIcon.getVisibility() == View.VISIBLE)) {
                    AnimatorProcessor.INSTANCE.createExitAnim(mUndoIcon, true, false);
                    AnimatorProcessor.INSTANCE.createExitAnim(mRevertIcon, true, false);
                    if (previousSkipAnim) {
                        // 如果是跳过动画的话，也不会执行退出回调，所以需要直接调用
                        mUndoIcon.setVisibility(View.GONE);
                        mRevertIcon.setVisibility(View.GONE);
                    } else {
                        AnimatorProcessor.INSTANCE.addExitCallback(() -> {
                            mUndoIcon.setVisibility(View.GONE);
                            mRevertIcon.setVisibility(View.GONE);
                            return null;
                        });
                    }
                }
            }
        }
        TopActionbarSection topActionbarSection = getTopActionbarSection();
        if (topActionbarSection == null) {
            GLog.e(TAG, LogFlag.DL, "[switchAnimation] topActionbarSection is null");
            return;
        }
        final RectF previewMargins = UiConfigManager.INSTANCE.getPreviewAreaMargins(
                !isEnter,
                EditorUIConfig.isEditorLandscape(getCurrentAppUiConfig()),
                isSwitchToTransformState
        );
        if (isEnter) {
            if (mLivewindow.getHeight() != 0) {
                if (isSecondLevel) {
                    if (!isSkipAnim && !previousSkipAnim) {
                        //正常情况下，一级跳二级，需要对actionbar跟预览区做动画
                        topActionbarSection.switchPage(true);
                        AnimatorProcessor.INSTANCE.addExitCallback(() -> {
                            mLivewindow.getAnimController().setPreviewAreaMargins(previewMargins, true);
                            return null;
                        });
                    } else if (!isSkipAnim) {
                        //一级跳二级，并且一级页不可见的情况下，actionbar的动画已经做了，所以只需要做预览区动画
                        mLivewindow.getAnimController().setPreviewAreaMargins(previewMargins, true);
                    } else {
                        //一级A跳一级B，并且一级页B在这个过程不可见，只需要在A的淡出动画中执行actionbar的淡出
                        topActionbarSection.switchPage(true);
                    }
                }
            }
        } else {
            RelativeLayout actionBar = topActionbarSection.getActionBar();
            if ((actionBar != null) && (actionBar.getVisibility() != View.VISIBLE)) {
                if (mLivewindow.getHeight() != 0) {
                    mLivewindow.getAnimController().revert(true);
                    mLivewindow.getAnimController().setPreviewAreaMargins(previewMargins, true);
                    topActionbarSection.switchPage(false);
                }
            }
        }
    }

    @Override
    public void onStart() {
        GLog.d(TAG, LogFlag.DL, "[onStart]");
        super.onStart();
        mSectionManager.onStart();
    }

    @Override
    public void onResume() {
        GLog.d(TAG, LogFlag.DL, "[onResume]");
        super.onResume();
        mSectionManager.onResume();
        AudioManager audioManager = (AudioManager) getSystemService(AUDIO_SERVICE);
        if (audioManager != null) {
            mAudioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(new AudioAttributes.Builder().setUsage(AudioAttributes.USAGE_MEDIA)
                            .setContentType(AudioAttributes.CONTENT_TYPE_MOVIE).build()).build();
            audioManager.requestAudioFocus(mAudioFocusRequest);
        }

        if (mEditorEngine != null) {
            mEditorEngine.repaintPreviewWindow();
            mEditorEngine.restoreListener(this);
            if (mInvokerManager.isNeedSeekToCurrentPlayStation()) {
                mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
            }
        }

        if ((mFbEditorBottom != null) && (mFbEditorBottom.getVisibility() == View.VISIBLE)) {
            GLog.d(TAG, LogFlag.DL, "onResume mFbEditorBottom visible");
        } else {
            mControlBar.getEditorStateManager().onActivityResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        long time = System.currentTimeMillis();
        mSectionManager.onPause();
        AudioManager audioManager = (AudioManager) getSystemService(AUDIO_SERVICE);
        if ((mAudioFocusRequest != null) && (audioManager != null)) {
            audioManager.abandonAudioFocusRequest(mAudioFocusRequest);
        }
        mControlBar.getEditorStateManager().onActivityPause();
        stop();
        GLog.d(TAG, LogFlag.DL, "onPause has stop engine: " + GLog.getTime(time));
    }

    @Override
    public void onStop() {
        super.onStop();
        GLog.d(TAG, LogFlag.DL, "onStop");
        mSectionManager.onStop();
    }

    /**
     * 重置上次的主题模式
     * @param context
     */
    private void restLastUIMode(@NonNull Context context) {
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        configuration.uiMode = getLastUiMode();
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());
    }

    @Override
    public void finish() {
        restLastUIMode(this);
        super.finish();
    }

    @Override
    public void onDestroy() {
        GLog.d(TAG, LogFlag.DL, "onDestroy");
        CpuFrequencyManager.setAction(CpuFrequencyManager.Action.ACTIVITY_START, TIMEOUT_1S);
        super.onDestroy();
        CacheManager.INSTANCE.release();
        WaveformDataCacheManager.INSTANCE.release();
        EditorCoverHelper.INSTANCE.release();
        if (mEditorEngine != null) {
            mEditorEngine.removeCurrentTimeline();
            mEditorEngine.clearCacheResource(CLEAR_CACHE_FLAG_AVFILE_INFO | CLEAR_CACHE_FLAG_AVFILE_KEYFRAME_INFO | CLEAR_CACHE_FLAG_CAPTION_FONT_INFO | CLEAR_CACHE_FLAG_ICON_ENGINE | CLEAR_CACHE_FLAG_STREAMING_ENGINE | CLEAR_CACHE_FLAG_WAVEFORM_ENGINE);
            mEditorEngine.unInit(this);
        }
        unRegisterBroadcast();
        VideoParser.Companion.getInstance().release();
        if (mSectionManager != null) {
            mSectionManager.onDestroy();
        }
        if (mControlBar != null) {
            mControlBar.getEditorStateManager().setEditorStateResultListener(null);
            mControlBar.getEditorStateManager().destroy();
        }
        EditorEngineGlobalContext.releaseInstance();
        if (mVideoBrightenManager != null) {
            mVideoBrightenManager.close();
            mVideoBrightenManager = null;
        }
        removeUserInteractionListener(mOnUserInteractionListener);
        if (mRefreshRateUpdater != null) {
            mRefreshRateUpdater.release();
        }
        AnimatorProcessor.INSTANCE.release();
        AnimatorProcessor.INSTANCE.unregisterTouchInterceptor(this);
        FilterManager.INSTANCE.clear();
        if (mResourceLoader != null) {
            mResourceLoader.release();
        }
        new Handler(Looper.getMainLooper()).removeCallbacksAndMessages(JointHelper.BRIGHTNESS_DELAY_TOKEN);
        MeicamEngineLimiter.getInstance().unregister(this);
    }


    private void unRegisterBroadcast() {
        if (mStopPreviewReceiver == null) {
            return;
        }

        try {
            unregisterReceiver(mStopPreviewReceiver);
        } catch (Exception e) {
            GLog.d(TAG, LogFlag.DL, "unRegisterBroadcast, e = " + e);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        GLog.d(TAG, LogFlag.DL, "onActivityResult. requestCode = " + requestCode + ", resultCode = " + resultCode + ", data = " + data);

        if (mInvokerManager.handleActivityResult(requestCode, resultCode, data)) {
            return;
        }
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case REQUEST_CODE_EXTRACT_REPLACE:
                if (PickerHelper.INSTANCE.getPickerList().isEmpty()) {
                    GLog.e(TAG, LogFlag.DL, "onActivityResult,REQUEST_CODE_EXTRACT_REPLACE ,picker list is empty");
                    break;
                }

                EditorBaseState editState = getCurrentEditorState();
                if (editState instanceof EditorTrimState) {
                    editState.pickMaterialComplete(requestCode, resultCode, data, PickerHelper.INSTANCE.getPickerList());
                    mEditorEngine.notifyTimelineChanged(true);
                }
                break;
            default:
                EditorBaseState baseState = getCurrentEditorState();
                if (baseState != null) {
                    baseState.onActivityResult(requestCode, resultCode, data);
                }
                break;
        }

    }

    /**
     * 保存完成回执
     *
     * @param data 回执携带数据
     */
    public void onSaveResult(ResultSaveFileData data, Size bitmapSize) {
        if ((data != null) && (data.getUri() != null)) {
            if (mIsFromEditorSDK) {
                Intent intent = new Intent();
                if (mIsMultipleEdit) {
                    startPhotoActivity(intent, data.getUri());
                } else {
                    Bitmap thumbBitmap = data.getThumbBitmap();
                    if (thumbBitmap != null) {
                        // intent放置：过渡图
                        IntentUtils.putBinderExtra(intent, KEY_TRANSITION_THUMBNAIL, new TransBitmapBinder(thumbBitmap));
                    }
                    setResultPhotoActivity(intent, data.getUri(), bitmapSize);
                }
                return;
            }
        }
        finish();
    }

    /**
     * 启动大图页面
     *
     * @param uri 跳转的素材uri
     */
    private void startPhotoActivity(Intent intent, Uri uri) {
        setResult(RESULT_OK, intent);
        Bundle bundle = new Bundle();
        bundle.putBoolean(IntentConstant.ViewGalleryConstant.KEY_IS_ACTIVITY_TRANSPARENT, true);
        new Starter.ActivityStarter(
                this,
                bundle,
                new PostCard(RouterConstants.RouterName.VIEW_GALLERY_ACTIVITY),
                null,
                null,
                false,
                false,
                true,
                resultIntent -> {
                    resultIntent.setAction(Intent.ACTION_VIEW);
                    resultIntent.setData(uri);
                    return null;
                }
        ).start();
        finish();
    }

    /**
     * 返回大图页
     */
    private void setResultPhotoActivity(Intent intent, Uri uri, Size bitmapSize) {
        setTransitionInfoForResult(intent, uri, bitmapSize);
        setResult(RESULT_OK, intent);
        finish();
    }

    /**
     * 尝试在intent中放置过渡图信息。避免编辑回大图后，出现闪黑现象。
     *
     * @param resultIntent intent
     * @param uri          新保存视频的uri
     */
    private void setTransitionInfoForResult(Intent resultIntent, Uri uri, Size bitmapSize) {
        resultIntent.setData(uri);
        ITransitionBoundsProvider positionProvider = mPhotoPositionForEditor;
        if (positionProvider == null) {
            GLog.e(TAG, LogFlag.DL, "[trySetTransitionInfoForResult] positionProvider is null");
            return;
        }

        MediaItem mediaItem = LocalMediaDataHelper.getLocalMediaItem(uri);
        if (mediaItem == null) {
            GLog.e(TAG, LogFlag.DL, "[trySetTransitionInfoForResult] mediaItem is null");
            return;
        }

        // 结合大图提供的位置信息，计算过渡bitmap应该显示在屏幕的哪个区域
        AppUiResponder.AppUiConfig uiConfig = getCurrentAppUiConfig();
        Size windowSize = new Size(uiConfig.getWindowWidth().getCurrent(), uiConfig.getWindowHeight().getCurrent());
        Rect finalRect = positionProvider.getExitBounds(bitmapSize, windowSize);

        // intent放置：通知大图是编辑保存回大图
        resultIntent.putExtra(KEY_IS_CANCEL_FROM_EDIT, false);
        // intent放置：大图过渡图显示位置
        resultIntent.putExtra(KEY_MEDIA_ITEM_RECT, finalRect);
        // intent放置：大图新跳转的视频标识id
        resultIntent.putExtra(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH, mediaItem.getPath().toString());
    }

    @Override
    public void onCurrentTimelineChanged(ITimeline timeline, boolean updateTimelineView) {
        GLog.d(TAG, LogFlag.DL, "onCurrentTimelineChanged,");
        if (timeline == null) {
            GLog.w(TAG, "onCurrentTimelineChanged timeline is null, updateTimelineView = " + updateTimelineView);
            return;
        }

        mIsTimelineChange = true;
        timeline.trimAudioToVideo();

        updateTimelineDuration(timeline);
        if (updateTimelineView) {
            long curTimelinePosition = mEditorEngine.getTimelineCurrentPosition();
            updateTimelinePosition(curTimelinePosition);
        }
        //modify video aspect ratio
        mLivewindow.resetRatioByTimeline(timeline, false, false);
        VideoClipUtils.scaleToFitCenterAllVideoClips(timeline);
    }

    @Override
    public void onSeekingTimelinePosition(long position) {
        EditorBaseState state = getCurrentEditorState();

        if (state != null) {
            state.onSeekTimelinePosition(position, mTimelineDuration);
        }
    }

    private void setPlayStatus(int playStatus) {
        if (playStatus == EDITOR_PREVIEW_STATUS_STOP || playStatus == EDITOR_PREVIEW_STATUS_FINISH) {
            mPlayAndPauseBtn.setStopState();

            if (mLivewindow != null) {
                mLivewindow.updatePipRectForPreview(false);
            }
        } else if (playStatus == EDITOR_PREVIEW_STATUS_PLAY || playStatus == EDITOR_PREVIEW_STATUS_READY) {
            mPlayAndPauseBtn.setPlayState();

            if (mLivewindow != null) {
                mLivewindow.updatePipRectForPreview(true);
            }
        }
        if (mVideoBrightenManager != null) {
            mVideoBrightenManager.handlerPlayerStatus(playStatus);
        }
    }

    @Override
    public void onStreamingEngineStateChanged(int state) {
        switch (state) {
            case STREAMING_ENGINE_STATE_PLAYBACK:
                setPlayStatus(EDITOR_PREVIEW_STATUS_PLAY);
                break;
            case STREAMING_ENGINE_STATE_STOPPED:
                if (mGoToSaveWaitEngineStopped) {
                    mGoToSaveWaitEngineStopped = false;
                    runOnUiThread(() -> changeSaveState());
                }
                break;
            default:
                break;
        }

        changePlayStatus();
    }

    @Override
    public void onFirstVideoFramePresented() {

    }

    @Override
    public void onPositionChange(long absolutePosition) {
        updateTimelinePosition(absolutePosition);
    }

    @Override
    public void onPreloadingCompletion() {
        setPlayStatus(EDITOR_PREVIEW_STATUS_READY);
    }

    @Override
    public void onStopped() {
        if (!mEditorEngine.isPlaying()) {
            mControlBar.updateFilter();
            setPlayStatus(EDITOR_PREVIEW_STATUS_STOP);
            if (mPlayStartTimeStamp > 0) {
                mPlayStartTimeStamp = -1;
            }

            if (mCartoonWrapper != null) {
                mCartoonWrapper.onStopPlayer();
            }
            if (mPlayAndPauseBtn != null) {
                mPlayAndPauseBtn.setStopState();
            }
        }
    }

    @Override
    public void onStartPlay(boolean success) {
        if (mEditorEngine.isPlaying()) {
            if (mCartoonWrapper != null) {
                mCartoonWrapper.onStartPlayer(success);
            }
            if (mPlayAndPauseBtn != null) {
                mPlayAndPauseBtn.setPlayState();
            }
        }
    }

    @Override
    public void onEOF(long absolutePosition) {
        GLog.d(TAG, LogFlag.DL, "onEOF absolutePosition = " + absolutePosition);
        setPlayStatus(EDITOR_PREVIEW_STATUS_FINISH);
        // 确保播放完成码线与尾帧完全重叠
        updateTimelinePosition(absolutePosition);
    }

    @Override
    public void onException(int i, String s) {

    }

    @Override
    public void onHardwareError(int i, String s) {

    }

    @Override
    public void onPlaybackDelayed(long l, boolean b) {

    }

    /**
     * 点击playseekbar的在时间轴上位置变化回调
     *
     * @param position 在时间轴上的绝对位置
     */
    @Override
    public void onUISeekingTimelinePositionChange(long position) {
        if (mEditorEngine != null) {
            updateTextTimeAndPlayProgress(
                    mEnginePlayingTimeManager.getCurrentVideoPlaybackPosition(position),
                    mEnginePlayingTimeManager.getVideoPlaybackDuration())
            ;
        }
    }

    public void initUIStatus() {
        mLivewindow.connectPreviewToEditorEngine(mEditorEngine, false);
        final RectF previewMargins = mInvokerManager.getPreviewMargins();
        mLivewindow.getAnimController().setPreviewAreaMargins(previewMargins, false);
        mControlBar.seekTimeline(0, 0, true);
    }

    public void setListener() {
        mEditorEngine.addSeekingListener(this);
        mEditorEngine.addStreamingEngineListener(this);
        mEditorEngine.addTimelineChangeListener(this);
        mEditorEngine.addVideoPlayerListener(this);
        addUserInteractionListener(mOnUserInteractionListener);

        mPlaySeekBar.setPlayer(new PlaySeekBar.IPlayer() {
            @Override
            public int getCurrentProgress() {
                float position = (float) mEnginePlayingTimeManager.getCurrentVideoPlaybackPosition() / TimeUtils.TIME_1_MS_IN_US;
                return (int) position;
            }

            @Override
            public boolean isPlaying() {
                return mEditorEngine.isPlaying();
            }
        });
        mPlaySeekBar.setOnSeekBarChangeListener(new PlaySeekBar.OnSeekBarChangeListener() {

            @Override
            public void onStartTrackingTouch(@NonNull PlaySeekBar seekBar, boolean isPlayingBeforeDown) {
                if (isPlayingBeforeDown) {
                    stop();
                }
                mPlayAndPauseBtn.setClickable(false);
            }

            @Override
            public void onProgressChanged(@NotNull PlaySeekBar seekBar, int progress, boolean fromUser) {
                long timeMills = mEnginePlayingTimeManager.getPlayBackStartPosition() + ((long) progress * TimeUtils.TIME_1_MS_IN_US);
                mControlBar.seekTimeline(timeMills, 0, true);
            }

            @Override
            public void onStopTrackingTouch(@NonNull PlaySeekBar seekBar, boolean isPlayingBeforeDown) {
                if (isPlayingBeforeDown) {
                    playbackTimeline();
                    mPlayStartTimeStamp = System.currentTimeMillis();
                }
                mPlayAndPauseBtn.setClickable(true);
            }
        });
        mPlayAndPauseBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EditorBaseState state = getCurrentEditorState();
                if ((state != null) && ((state.isEditing()) || (!state.canDoAction(EditorBaseState.ActionType.TYPE_PLAY_VIDEO)))) {
                    return;
                }

                int playAndPauseType = mPlayAndPauseBtn.getPlayAndPauseType();
                if (state != null) {
                    state.playAndPause(playAndPauseType);
                }
                if (playAndPauseType == TYPE_PAUSE) {
                    playbackTimeline();
                    mPlayStartTimeStamp = System.currentTimeMillis();
                } else {
                    stop();
                }
            }
        });
        if (!mEditorEngine.isPlaying()) {
            mPlayAndPauseBtn.setStopState();
        } else {
            mPlayAndPauseBtn.setPlayState();
        }

        mLivewindow.setAnimationListener(new EditorPreviewView.OnAnimationListener() {
            @Override
            public void onAnimationUpdate() {

            }

            @Override
            public void onAnimationEnd() {
                GLog.d(TAG, LogFlag.DL, "livewindow onAnimationEnd");
                mEditorEngine.enableBlurToTimeLine(false);
                ITimeline timeline = mEditorEngine.getCurrentTimeline();
                if (timeline == null) {
                    mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
                    return;
                }

                List<BaseCaption> captionList = timeline.getCaptionList();
                if ((captionList == null) || (captionList.isEmpty())) {
                    if (mIsPlayAfterWindowAnim) {
                        mIsPlayAfterWindowAnim = false;
                        mEditorEngine.startPlayer(0, mEnginePlayingTimeManager.getVideoPlaybackDuration());
                    } else {
                        mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
                    }
                    return;
                }
                mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);

                if (mIsPlayAfterWindowAnim) {
                    mIsPlayAfterWindowAnim = false;
                    mEditorEngine.startPlayer(0, mEnginePlayingTimeManager.getVideoPlaybackDuration());
                }
            }

            @Override
            public void onAnimationCancel() {
                mEditorEngine.enableBlurToTimeLine(false);
            }
        });

        mControlBar.getEditorStateManager().setEditorStateResultListener(this);
    }

    private void onDoBack() {
        TopActionbarSection topActionbarSection = getTopActionbarSection();
        boolean isOptionChanged = false;
        if (topActionbarSection != null) {
            isOptionChanged = topActionbarSection.isOptionChanged();
        }
        if (mOperationSaveHelper.canUndo() || isOptionChanged) {
            stopPlayAndFling();
            if (mIsFromEditorSDK) {
                showSaveOrCancelDialog();
            } else {
                finish();
                trackReportWhenExit(VideoEditorTrackConstant.Value.CANCEL_SAVE_VIDEO);
            }
        } else {
            finish();
            trackReportWhenExit(VideoEditorTrackConstant.Value.CANCEL_SAVE_VIDEO);
        }
    }

    /**
     * 点击完成按钮
     */
    private void onDoDone() {
        TopActionbarSection topActionbarSection = getTopActionbarSection();
        if ((topActionbarSection != null) && !topActionbarSection.isDoneAvailable()) {
            GLog.w(TAG, LogFlag.DL, "onClick done. can not done, return");
            return;
        }
        int duration = ConfigAbilityWrapper.getInt(VIDEO_EDITOR_OLIVE_SAVE_MAX_DURATION, OLIVE_SAVE_MAX_DURATION);
        long maxDuration = (long) duration * TimeUtils.TIME_1_MS_IN_US * TimeUtils.TIME_1_MS_IN_US;
        if ((mEditorEngine != null) && (mEditorEngine.getSaveType() == SaveType.OLIVE) && (mEditorEngine.getTimelineDuration() > maxDuration)) {
            String unit = getResources().getString(R.string.videoeditor_time_unit_s);
            ToastUtil.showLongToast(getResources().getString(R.string.videoeditor_unsupport_done_tip, duration, unit));
            return;
        }

        if ((mCartoonWrapper != null) && mCartoonWrapper.isPanelShow()) {
            GLog.w(TAG, LogFlag.DL, "onClick back. is panel show, return");
            onBackPressed();
            mGoToSaveWaitEngineStopped = true;
            return;
        }

        changeSaveState();
    }

    /**
     * 点击保存，切换到保存视图页
     */
    private void changeSaveState() {
        handleStateWhenFinish(false);
        mControlBar.stopTimelineViewFling();
        EditorSaveState state = new EditorSaveState(this, mControlBar);
        mControlBar.getEditorStateManager().changeState(state);
        trackReportWhenExit(VideoEditorTrackConstant.Value.CONFIRM_SAVE_VIDEO);
    }

    private void trackReportWhenExit(int saveVideo) {
        VideoEditorTrackerHelper.trackVideoEditMenuClickUserAction(
                saveVideo,
                getSaveType(mEditorEngine.getSaveType()),
                getEnterFrom(mIsMultipleEdit)
        );
    }

    @Override
    public EditorBaseState getCurrentEditorState() {
        if (mControlBar == null) {
            GLog.d(TAG, LogFlag.DL, "getCurrentEditorState, mControlBar is null");
            return null;
        }
        return mControlBar.getEditorStateManager().getCurrentEditorState();
    }

    @Override
    public void onNavigationBarVisibilityChanged(boolean isShowNavigationBar) {
        EditorBaseState state = getCurrentEditorState();
        if (state != null) {
            state.onNavigationBarVisibilityChanged(isShowNavigationBar);
            if (((mFbEditorBottom != null) && (mFbEditorBottom.getVisibility() == View.VISIBLE))) {
                if (mCartoonWrapper != null) {
                    mCartoonWrapper.refreshLayoutparams(this, true);
                }
            } else {
                mControlBar.onLayoutChange(state, isShowNavigationBar, false);
            }
        }
    }

    @Override
    public void onStateResult(EditorBaseState editorState, Intent intent) {
        mInvokerManager.handleStateResult(this, intent);
    }

    private void playbackTimeline() {
        EditorBaseState currentEditorState = getCurrentEditorState();
        if (currentEditorState == null) {
            long position = mEditorEngine.getTimelineCurrentPosition();
            ITimeline timeline = mEditorEngine.getCurrentTimeline();
            if (position >= (timeline.getDuration() - FRAME_DURATION)) {
                position = 0;
            }
            mEditorEngine.startPlayer(position, mEnginePlayingTimeManager.getVideoPlaybackDuration());
        } else {
            currentEditorState.playbackTimeline();
        }
    }

    private void stop() {
        if (mEditorEngine != null) {
            mEditorEngine.stopPlayer();
        }
    }

    public void updateTimelinePosition(long currentPos) {
        if (mEditorEngine != null) {
            updateTextTimeAndPlayProgress(
                    mEnginePlayingTimeManager.getCurrentVideoPlaybackPosition(currentPos),
                    mEnginePlayingTimeManager.getVideoPlaybackDuration()
            );
        }
        if (mControlBar != null) {
            mControlBar.onPlayPositionChange(currentPos);
        }
    }

    private void updateTimelineDuration(ITimeline timeline) {
        if ((mEditorEngine == null) || (timeline == null)) {
            return;
        }

        mTimelineDuration = timeline.getDuration();

        updateTextTimeAndPlayProgress(
                mEnginePlayingTimeManager.getCurrentVideoPlaybackPosition(),
                mEnginePlayingTimeManager.getVideoPlaybackDuration()
        );
    }

    @Override
    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig uiConfig) {
        super.onAppUiStateChanged(uiConfig);
        // 控制栏
        if (mControlBar != null) {
            mControlBar.onAppUiConfigChange(uiConfig);
        }
        if (mSectionManager != null) {
            mSectionManager.onAppUiStateChanged(uiConfig);
        }
        // 整体的页面框架
        if (mVideoEditorUIScheme != null) {
            mVideoEditorUIScheme.notifyAppUiConfigChange(uiConfig);
        }
    }

    /**
     * 更新播放组件的，显示时间和播放进度
     *
     * @param position 当前播放的时间（单位：微秒）
     * @param duration 视频的总时长（单位：微秒）
     */
    private void updateTextTimeAndPlayProgress(long position, long duration) {
        long timeMills = position / TimeUtils.TIME_1_MS_IN_US;
        long durationMills = duration / TimeUtils.TIME_1_MS_IN_US;
        if (mTimeCodeText != null) {
            if (mTimeTextFormat == null) {
                mTimeTextFormat = getResources().getStringArray(R.array.picture_thumbnail_seek_bar_time_text_format);
            }
            String text = TimeUtils.formatPlayProgress(timeMills, durationMills, mTimeTextFormat, TIME_50_MS_IN_MS);
            if (!TextUtils.isEmpty(text)) {
                int slashIndex = text.indexOf(LEFT_SLASH);
                SpannableStringBuilder builder = new SpannableStringBuilder(text);
                builder.setSpan(new ForegroundColorSpan(Color.WHITE), 0, slashIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                mTimeCodeText.setText(builder);
            }
        }
        // 只在needPlaySeekBar的界面才去更新progress
        if ((getCurrentEditorState() != null)
                && (getCurrentEditorState().getUIController().needPlaySeekBar())
                && (mPlaySeekBar != null)) {
            if (mPlaySeekBar.getMaxProgress() != durationMills) {
                mPlaySeekBar.setMaxProgress((int) durationMills);
            }
            mPlaySeekBar.setProgress((int) timeMills);
        }
    }

    public void changePlayStatus() {
        BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getMain(), CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
            Window window = getWindow();
            if (mEditorEngine.isPlaying()) {
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                setWindowRefreshRateIfNeed(true);
            } else {
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                setWindowRefreshRateIfNeed(false);
            }
            return null;
        });
    }

    public void updateUndoStatus(boolean enable) {
        mUndoIcon.setEnabled(enable);
    }

    public void updateRevertStatus(boolean enable) {
        mRevertIcon.setEnabled(enable);
    }

    public String getLandPageId() {
        return "";
    }

    @Override
    public void onStateChange(EditorBaseState currentState, EditorBaseState previousState) {
        if (currentState != null) {
            if (!mIsChangedState) {
                switchAnimation(
                    true,
                    currentState.getPageLevel() == PageLevelEnum.PAGE_LEVEL_SECOND,
                    currentState.showOperaIcon(),
                    currentState.isSkipAnim(),
                    (previousState != null) && previousState.isSkipAnim(),
                    currentState instanceof EditorTransformState
                );
            }
            mIsChangedState = false;
            if (mResourceLoader != null) {
                mResourceLoader.onStateChange(currentState, previousState);
            }
        }
    }

    private void undo() {
        long currentProgress = mEditorEngine.getTimelineCurrentPosition();
        EditorBaseState state = getCurrentEditorState();
        if (state instanceof EditorTrimState) {
            EditorTrimUIController trimUIController = ((EditorTrimState) state).getUIController();
            if (trimUIController != null) {
                trimUIController.changeTimelineState(STATE_PREVIEW);
            }
        }
        showUndoRevertToast(mOperationSaveHelper.undo());
        mControlBar.seekTimeline(currentProgress, 0, true);
        mEditorEngine.stopPlayer();
        if (mLivewindow != null) {
            mLivewindow.resetMaskEffect();
        }
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.undo_view) {
            GLog.d(TAG, LogFlag.DL, "onClick undo_view. mOperationSaveHelper is null? " + (mOperationSaveHelper != null));
            if (mOperationSaveHelper != null) {
                if (!mOperationSaveHelper.canUndo()) {
                    return;
                }
                undo();
            }
        } else if (viewId == R.id.revert_view) {
            GLog.d(TAG, LogFlag.DL, "onClick revert_view. mOperationSaveHelper is null? " + (mOperationSaveHelper != null));
            if (mOperationSaveHelper != null) {
                if (!mOperationSaveHelper.canRedo()) {
                    return;
                }
                revert();
            }
        } else if (viewId == R.id.action_bar_back) {
            onDoBack();
        } else if (viewId == R.id.action_bar_done) {
            if (ClickUtil.isDoubleClickAtSameKey(view.getId())) {
                GLog.w(TAG, LogFlag.DL, "onClick done. is already save, return");
                return;
            }
            onDoDone();
            VideoEditorTrackerHelper.appendMenuClickData(VideoEditorTrackConstant.Value.VIDEO_SAVE,
                    VideoEditorTrackerHelper.MenuLevelType.FIRST_LEVEL);
        }
    }
    private void revert() {
        long currentProgress = mEditorEngine.getTimelineCurrentPosition();
        EditorBaseState state = getCurrentEditorState();
        if (state instanceof EditorTrimState) {
            EditorTrimUIController trimUIController = ((EditorTrimState) state).getUIController();
            if (trimUIController != null) {
                trimUIController.changeTimelineState(STATE_PREVIEW);
            }
        }
        showUndoRevertToast(mOperationSaveHelper.redo());
        mControlBar.seekTimeline(currentProgress, 0, true);
        mEditorEngine.stopPlayer();
        if (mLivewindow != null) {
            mLivewindow.resetMaskEffect();
        }
    }

    /**
     * 显示撤销恢复的Toast提示样式
     * Toast显示在图片上方
     *
     * @param msg 提示内容
     */
    private void showUndoRevertToast(String msg) {
        if (TextUtils.isEmpty(msg)) {
            GLog.w(TAG, LogFlag.DL, "showUndoRevertToast, message is empty, return");
            return;
        }
        ToastUtil.showShortCustomToast(
                R.layout.videoeditor_layout_custom_toast_tag,
                msg,
                Gravity.TOP | Gravity.CENTER_HORIZONTAL,
                0,
                getResources().getDimensionPixelOffset(R.dimen.tag_toast_top_margin)
        );
    }

    @Override
    public void onSetPipRectVisible(int visible) {
        if (mLivewindow != null) {
            mLivewindow.setPipDrawRectVisible(visible);
        }
    }

    @Override
    public void onStateDestroy() {
        exitAnimation();
    }

    /**
     * 子页面退出时撤销重做的动画
     */
    private void exitAnimation() {
        EditorBaseState currentState = getCurrentEditorState();
        boolean showOperaIcon = false;
        if (currentState != null) {
            showOperaIcon = currentState.showOperaIcon();
        }
        switchAnimation(false, false, showOperaIcon, false, false, false);
    }

    @Override
    public void onCreateStateForShowMaterialSelect(EditorBaseState state) {
        refreshTextTimeAndPlayProgress();
    }

    @Override
    public void onFinishStateForShowMaterialSelect(EditorBaseState state, EditorBaseState topState) {
        refreshTextTimeAndPlayProgress();
    }

    @Override
    public void limitNow() {
        finish();
    }

    @Override
    public boolean isActive() {
        return getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.RESUMED);
    }

    class HomeAndRecentKeyEventBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            // markded by yeguangjin 这里确认是否需要监听多任务回调
        }
    }

    public long getEditorVideoId() {
        return mCurrentVideoId;
    }

    /**
     * 刷新播放时间控件和进度条
     */
    private void refreshTextTimeAndPlayProgress() {
        if (mEditorEngine != null) {
            long currentPlaybackPosition = mEnginePlayingTimeManager.getCurrentVideoPlaybackPosition();
            updateTextTimeAndPlayProgress(currentPlaybackPosition, mEnginePlayingTimeManager.getVideoPlaybackDuration());
        }
    }

    /**
     * 初始化视图
     */
    @MainThread
    private void initMainUI() {
        GLog.d(TAG, LogFlag.DL, "[initMainUI]");
        getDelegate().setLocalNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        setContentView(R.layout.activity_video_editor_layout);
        // 必须置于setContentView后面，确保配置正确的主题，因为BaseActivity.setContentView有适配系统主题的处理
        setTheme(ThemeHelper.getTheme());
        initView();
        mSectionManager = new SectionManager(EditorActivity.this, mRootView, mEditorEngine, mEditorInvoker);
        TopActionbarSection topActionbarSection = getTopActionbarSection();
        if (topActionbarSection != null) {
            topActionbarSection.setActionBarClickListener(EditorActivity.this);
            topActionbarSection.onStackCountChanged(mOperationSaveHelper.canUndo());
        }
        mSectionManager.onCreate();
        initControlBar();
        initUIStatus();
        setListener();
        registerBroadcast();
        updateUndoStatus(mOperationSaveHelper.canUndo());
        updateRevertStatus(mOperationSaveHelper.canRedo());
        setReselectMaterialListener();
        if (mFbEditorBottom != null) {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mFbEditorBottom.getLayoutParams();
            lp.setMargins(0, 0, 0, getShowingNavigationBarHeight(this));
            if (mControlBar != null) {
                mCartoonWrapper = new EditorActivityPanelWrapper(getSupportFragmentManager(), mRootView, mFbEditorBottom, mControlBar);
                mCartoonWrapper.setTargetViews(mOperationLayout);
                mCartoonWrapper.setPreviewView(mLivewindow);
            }
        }
        LiveDataBus.get().with(EventIndex.NAVIGATIONBAR_VISIBILITY_CHANGED, Boolean.class).observe(this, navigationChanged -> {
            if (mCartoonWrapper != null) {
                mCartoonWrapper.refreshLayoutparams(this, navigationChanged);
            }
        });
        updateVideoSpecInfo();
        updateBrightness();
        mInvokerManager.onDataImportReady();
    }

    /**
     * 更新视频素材信息（帧率、分辨率等）
     */
    public void updateVideoSpecInfo() {
        // 设置 刷新率
        setWindowRefreshRateIfNeed(mEditorEngine.isPlaying());
        // 更改 时间线为sdr或hdr
        mEditorEngine.changeVideoBitDepth();
        // 更改 color gain的值
        mEditorEngine.changeColorGain();
    }

    /**
     * 更新提亮信息
     */
    public void updateBrightness() {
        // 设置提亮
        SurfaceView surfaceView = mEditorEngine.getSurfaceView();
        int brightnessVideoType = VideoParser.Companion.getInstance().getBrightnessVideoType();
        // 如果之前提亮过,则可以更新提亮参数,处理 hdr下降为sdr的场景
        boolean hasBrightened = mVideoBrightenManager != null;
        if (surfaceView != null && (!VideoTypeParser.isSdrType(brightnessVideoType) || hasBrightened)) {
            if (mVideoBrightenManager == null) {
                // 初始化并且提亮
                mVideoBrightenManager = new EditorVideoBrightenManager(surfaceView, brightnessVideoType);
            } else {
                // 更新提亮参数
                mVideoBrightenManager.updateBrighten(brightnessVideoType);
            }
        }
    }

    @Override
    public boolean isSupportTextSizeChange(@NotNull Context context) {
        return false;
    }
}