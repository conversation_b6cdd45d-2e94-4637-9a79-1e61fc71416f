/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  RatioAdapter
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/9
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/9      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ratioselector

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.R
import com.coui.appcompat.textview.COUITextView

/**
 * RatioAdapter 负责将 RatioOption 显示在列表中，并处理选中态、图标 tint 及样式配置
 */
class RatioAdapter(
    private var config: RatioSelectorConfig,
    private val onItemClick: (RatioOption) -> Unit
) : RecyclerView.Adapter<RatioAdapter.RatioViewHolder>() {

    private val items = mutableListOf<RatioOption>()

    // 记录当前选中比例的 ratioId（null 表示没有选中）
    private var selectedRatioId: Int? = null

    private var isEnabled = true

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RatioViewHolder {
        val itemView = LayoutInflater.from(parent.context)
            .inflate(R.layout.videoeditor_item_ratio_option, parent, false)
        return RatioViewHolder(itemView, onItemClick)
    }

    override fun onBindViewHolder(holder: RatioViewHolder, position: Int) {
        val option = items[position]
        holder.bind(option, config)
    }

    override fun getItemCount() = items.size

    fun submitList(newList: List<RatioOption>) {
        items.clear()
        items.addAll(newList)
        notifyDataSetChanged()
    }

    /**
     * 更新选中项：局部刷新上一次选中和当前选中的项
     */
    fun setSelected(ratioId: Int?) {
        if (ratioId == selectedRatioId) return
        val previousSelected = selectedRatioId
        selectedRatioId = ratioId

        previousSelected?.let { updateItemByRatioId(it) }
        updateItemByRatioId(ratioId)
    }

    private fun updateItemByRatioId(ratioId: Int?) {
        val index = items.indexOfFirst { option -> option.ratioId == ratioId }
        if (index >= 0) notifyItemChanged(index)
    }

    /**
     * 允许外部动态更新配置项
     */
    fun updateConfig(newConfig: RatioSelectorConfig) {
        config = newConfig
        notifyDataSetChanged()
    }

    /**
     * 设置适配器是否启用
     */
    fun setEnabled(enabled: Boolean) {
        isEnabled = enabled
        notifyDataSetChanged()
    }

    inner class RatioViewHolder(
        itemView: View,
        private val onItemClick: (RatioOption) -> Unit
    ) : RecyclerView.ViewHolder(itemView) {

        private val container: LinearLayout = itemView.findViewById(R.id.ratio_item_container)
        private val ratioNameText: COUITextView = itemView.findViewById(R.id.ratio_name)
        private val iconView: ImageView = itemView.findViewById(R.id.ratio_icon)

        fun bind(option: RatioOption, config: RatioSelectorConfig) {
            // 动态设置图标与文字的间距
            val layoutParam = ratioNameText.layoutParams as LinearLayout.LayoutParams
            layoutParam.topMargin = config.iconTextSpacing
            ratioNameText.apply {
                layoutParams = layoutParam
                setText(option.ratioNameRes)
                isEnabled = isEnabled
            }
            iconView.setImageResource(option.iconRes)
            val isSelected = (option.ratioId == selectedRatioId)
            updateSelected(isSelected)
            // 根据enabled状态设置视图可用性
            itemView.isEnabled = isEnabled
            container.isEnabled = isEnabled
            iconView.isEnabled = isEnabled

            // 点击事件：更新选中状态并回调
            itemView.setOnClickListener {
                if (isEnabled) {
                    updateSelected(true)
                    setSelected(option.ratioId)
                    onItemClick(option)
                }
            }
        }

        /**
         * 更新选中态：容器、图标及文字均需设置选中状态
         */
        private fun updateSelected(isSelected: Boolean) {
            container.isSelected = isSelected
            itemView.isSelected = isSelected
            iconView.isSelected = isSelected
        }
    }
}
