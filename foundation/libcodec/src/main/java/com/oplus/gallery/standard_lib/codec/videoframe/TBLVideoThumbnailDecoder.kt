/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : TBLVideoThumbnailDecoder.kt
 ** Description : VideoThumbnailDecoder Based on TBL scheme.
 ** Version     : 1.0
 ** Date        : 2020/08/31
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** JinP<PERSON>@Apps.Gallery3D             2020/08/31   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.standard_lib.codec.videoframe

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.tblplayer.retriever.IMediaMetadataRetriever
import com.oplus.tblplayer.retriever.MediaMetadataRetrieverWrapper
import com.oplus.tblplayer.retriever.TBLMediaMetadataRetriever
import java.io.FileDescriptor
import kotlin.math.max
import kotlin.math.roundToInt

class TBLVideoThumbnailDecoder : VideoThumbnailDecoder() {

    companion object {
        private const val TAG = "TBLVideoThumbnailDecoder"
    }

    private var retriever: IMediaMetadataRetriever? = TBLMediaMetadataRetriever().apply {
        // TBL设置缩略图目标image的格式，可解决视频封面帧出现横线等问题，需要在setDataSource前调用
        runWithTryCatching { setTargetImageFormat(IMediaMetadataRetriever.BMP_IMAGE_FORMAT_BGR24) }
    }
    private var height: Int = 0
    private var width: Int = 0

    override fun setDataSource(path: String) {
        runWithTryCatching { retriever?.setDataSource(path) }
    }

    override fun setDataSource(fd: FileDescriptor) {
        GLog.i(TAG, "setDataSource, fileDescriptor : $fd")
        runWithTryCatching { retriever?.setDataSource(fd) }
    }

    override fun setDataSource(context: Context, uri: Uri) {
        GLog.i(TAG, "setDataSource, Uri : $uri")
        runWithTryCatching { retriever?.setDataSource(context, uri) }
    }

    override fun setDataSource(fd: FileDescriptor, offset: Long, length: Long) {
        GLog.i(TAG, "setDataSource, offset : $offset, length : $length")
        runWithTryCatching { retriever?.setDataSource(fd, offset, length) }
    }

    private fun runWithTryCatching(setDataSourceFun: () -> Unit) {
        runCatching {
            setDataSourceFun()
        }.onFailure {
            GLog.e(TAG, it) { "[runWithTryCatching] set data source with TBL retriever failed" }
            close()
            retriever = MediaMetadataRetrieverWrapper()
            runCatching {
                setDataSourceFun()
            }.onFailure {
                GLog.e(TAG, it) { "[runWithTryCatching] try to set data source with system retriever failed" }
            }
        }
    }

    override fun setThumbnailSize(size: Int) {
        if (size <= 0) {
            GLog.w(TAG, "[setThumbnailSize], size=$size")
            return
        }
        runCatching {
            height = retriever?.extractMetadata(IMediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toInt() ?: 0
            width = retriever?.extractMetadata(IMediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toInt() ?: 0
            if ((height != 0) && (width != 0)) {
                val scale = max(size / width.toFloat(), size / height.toFloat())
                // 需要缩放的时候，调整宽高，否则不调整
                if (scale < NO_SCALE) {
                    width = (width * scale).roundToInt()
                    height = (height * scale).roundToInt()
                }
            }
        }.onFailure {
            GLog.e(TAG, it) { "[setThumbnailSize] extract video size from metadata failed" }
            width = 0
            height = 0
        }
    }

    override fun decodeFrameBitmapAtTime(timeUs: Long, size: Int): Bitmap? {
        return decodeFrameBitmapAtTime(timeUs, size, OptionFlag.OPTION_CLOSEST_SYNC)
    }

    override fun decodeFrameBitmapAtTime(timeUs: Long, size: Int, optionFlag: OptionFlag): Bitmap? {
        if (retriever == null) {
            GLog.w(TAG, "[decodeFrameBitmapAtTime] can't decode bitmap because retriever is null, it may be released")
            return null
        }
        val frameOptionFlag = getOptionFlag(optionFlag)
        val bitmap = try {
            if (size <= 0) {
                if ((width != 0) && (height != 0)) {
                    retriever?.getScaledFrameAtTime(timeUs, frameOptionFlag, width, height)
                } else {
                    retriever?.getFrameAtTime(timeUs, frameOptionFlag)
                }
            } else {
                retriever?.getScaledFrameAtTime(timeUs, frameOptionFlag, size, size)
            }
        } catch (e: Exception) {
            GLog.e(TAG, e) { "[decodeFrameBitmapAtTime] decode frame with specified time failed" }
            null
        }
        return DecodeUtils.ensureGLCompatibleBitmap(bitmap)
    }

    override fun decodeCoverBitmap(size: Int): Bitmap? {
        if (retriever == null) {
            GLog.w(TAG, "[decodeCoverBitmap] can't decode bitmap because retriever is null, it may be released")
            return null
        }
        val bitmap = try {
            if ((width != 0) && (height != 0)) {
                retriever?.getCoverPicture(IMediaMetadataRetriever.OPTION_CLOSEST_SYNC, width, height)
            } else {
                retriever?.getCoverPicture(IMediaMetadataRetriever.OPTION_CLOSEST_SYNC, -1, -1)
            }
        } catch (e: Exception) {
            GLog.e(TAG, e) { "[decodeCoverBitmap] decode cover picture with specified size($size, $size) failed" }
            null
        } ?: let {
            GLog.e(TAG) {
                "[decodeCoverBitmap] decode cover picture with specified size($size, $size) is null, try decodeFrameBitmapAtTime(0)"
            }
            decodeFrameBitmapAtTime(0)
        }
        return DecodeUtils.ensureGLCompatibleBitmap(bitmap)
    }

    override fun close() {
        runCatching {
            GLog.i(TAG) { "[close] retriever release" }
            retriever?.release()
            retriever = null
        }.onFailure {
            GLog.e(TAG, it) { "[close] retriever release failed" }
        }
    }

    override fun isValid(): Boolean {
        return retriever != null
    }

    private fun getOptionFlag(flag: OptionFlag): Int {
        val frameFlag = when (flag) {
            OptionFlag.OPTION_PREVIOUS_SYNC -> IMediaMetadataRetriever.OPTION_PREVIOUS_SYNC
            OptionFlag.OPTION_NEXT_SYNC -> IMediaMetadataRetriever.OPTION_NEXT_SYNC
            OptionFlag.OPTION_CLOSEST_SYNC -> IMediaMetadataRetriever.OPTION_CLOSEST_SYNC
            OptionFlag.OPTION_CLOSEST -> IMediaMetadataRetriever.OPTION_CLOSEST
        }
        return frameFlag
    }
}