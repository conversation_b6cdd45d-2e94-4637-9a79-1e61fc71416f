/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PortraitBlurDataStruct
 ** Description:
 ** Version: 1.0
 ** Date : 2025/6/4
 ** Author: sunwenli
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  sunwenli                      2025/6/4    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.extend.portraitblur

import com.oplus.gallery.foundation.codec.extend.ExtendStruct

abstract class PortraitBlurDepthStruct<T : PortraitBlurDepthData>(byteArray: ByteArray) : ExtendStruct<T>(byteArray)

/**
 *  @property fNumber 光圈值
 *  @property blurStrengthParam 虚化强度，取值范围 [0,100]
 *  @property blurApertures 光圈值数组，范围 [1.4f,16f]，与 [blurValue] 一一对应
 *  @property blurValue 景深虚化值数组，范围 [0,100]，与 [blurApertures] 一一对应
 */
abstract class PortraitBlurDepthData(
    val fNumber: Float? = 0f,
    val blurApertures: FloatArray? = null,
    val blurValue: FloatArray? = null
) {
    abstract val blurStrengthParam: Float?
}