/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbilityCallLocationDetector.kt
 ** Description: 能力调用的位置检查
 ** Version: 1.0
 ** Date: 2023/5/11
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/5/11      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.codecheck.detector

import com.android.tools.lint.detector.api.Category
import com.android.tools.lint.detector.api.Detector
import com.android.tools.lint.detector.api.Implementation
import com.android.tools.lint.detector.api.Issue
import com.android.tools.lint.detector.api.JavaContext
import com.android.tools.lint.detector.api.Scope
import com.android.tools.lint.detector.api.Severity
import com.intellij.psi.PsiMethod
import org.jetbrains.uast.UCallExpression
import org.jetbrains.uast.UFile
import org.jetbrains.uast.getParentOfType

/**
 * 能力调用的位置检查
 *
 * @GalleryFramework注解的能力只能由framework层模块调用
 * @GalleryApp注解的能力可以在整个app层模块调用
 */
class AbilityCallLocationDetector : Detector(), Detector.UastScanner {

    override fun getApplicableMethodNames(): List<String>? {
        return listOf("getAppAbility")
    }

    override fun visitMethodCall(context: JavaContext, node: UCallExpression, method: PsiMethod) {
        val classPkgName = node.getParentOfType<UFile>()?.packageName
        if (classPkgName?.contains("framework.") == true) {
            return
        }
        node.typeArguments.forEach { typeArg ->
            val typeArgName = typeArg.getPresentableText(true)
            if (DISABLE_CALL_ABILITIES.contains(typeArgName)) {
                reportError(context, node, typeArgName)
            }
        }
    }

    private fun reportError(context: JavaContext, node: UCallExpression, name: String) {
        context.report(
            ConfigApiCallDetector.ISSUE, node, context.getCallLocation(
                call = node,
                includeReceiver = false,
                includeArguments = false
            ),
            message = "${name}能力禁止在业务层调用, 只能在framework层使用"
        )
    }

    companion object {
        private val IMPLEMENTATION: Implementation =
            Implementation(AbilityCallLocationDetector::class.java, Scope.JAVA_FILE_SCOPE)

        /**
         * 定义、创建一个问题
         */
        internal val ISSUE: Issue = Issue.create(
            id = "AbilityCallLocationError", // 唯一的 id，简要表达当前问题。
            briefDescription = "领域能力调用位置限制", // 唯一的 id，简要表达当前问题。
            explanation = "标记@GalleryFramework的领域能力不可在业务层使用，只能在framework层使用", // 唯一的 id，简要表达当前问题。
            category = Category.CORRECTNESS, // 唯一的 id，简要表达当前问题。
            8, // 优先级，从 1 到 10，10 最重要。
            Severity.ERROR, // 严重程度，包括 FATAL、ERROR、WARNING、INFORMATIONAL和 IGNORE。
            IMPLEMENTATION // Issue 和哪个 Detector 绑定，以及声明检查的范围。
        ).setAndroidSpecific(true)

        /**
         * 禁止在业务层使用的能力列表
         */
        private val DISABLE_CALL_ABILITIES = listOf("IConfigSetterAbility")
    }
}