/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IEditorUISchemeExt.kt
 ** Description: 自定义IEditorUIScheme的扩展能力
 ** Version: 1.0
 ** Date : 2025/3/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2025/3/18    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.controler

import android.content.res.Configuration
import com.oplus.gallery.business_lib.template.editor.ui.IEditorUIScheme
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder

/**
 * 扩展IEditorUIScheme，提供更多能力
 */
interface IVideoEditorUIScheme : IEditorUIScheme {
    /**
     * 配置变化时，重新计算UI
     */
    fun notifyAppUiConfigChange(config: AppUiResponder.AppUiConfig)

    /**
     * 配置变化时，重新计算UI
     */
    fun onConfigurationChanged(newConfig: Configuration)
}