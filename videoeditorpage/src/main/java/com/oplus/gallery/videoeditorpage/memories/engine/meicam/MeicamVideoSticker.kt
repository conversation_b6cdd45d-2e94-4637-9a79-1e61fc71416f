/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoSticker.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.videoedit.data.StickerInfo
import com.oplus.gallery.framework.abilities.videoedit.strategy.TemplateStrategy
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoSticker

class MeicamVideoSticker : IGalleryVideoSticker {

    companion object {
        private const val TAG = "MeicamVideoSticker"
    }

    private lateinit var meicamTimeline: MeicamTimeline
    private var effectList: ArrayList<BaseVideoTimelineEffect> = ArrayList()

    fun setTimeline(timeline: MeicamTimeline) {
        this.meicamTimeline = timeline
    }

    override fun applyVideoSticker(stickerInfo: StickerInfo) {
        GLog.d(TAG, "applyVideoSticker")
        var inTime = stickerInfo.inTime
        var outTime = stickerInfo.outTime
        val videoTrack: NvsVideoTrack = meicamTimeline.nvsTimeline.getVideoTrackByIndex(0)

        // compute inTime and outTime
        if (videoTrack != null) {
            val withClip: Int = if (stickerInfo.withClip === TemplateStrategy.INDEX_LAST) videoTrack.clipCount - 1 else stickerInfo.withClip
            val duration: Long = videoTrack.duration

            if ((withClip >= 0) && (withClip < videoTrack.clipCount)) {
                val videoClip: NvsVideoClip = videoTrack.getClipByIndex(withClip)
                inTime = videoClip.trimIn
                outTime = videoClip.trimOut
            } else if ((stickerInfo.extraFlags and TemplateStrategy.FLAG_REPEAT_TO_END) > 0) {
                outTime = duration
            } else if (stickerInfo.withClip === TemplateStrategy.INDEX_NONE) {
                if (outTime >= duration) {
                    outTime = duration
                }
            }
        }

        if ((inTime < 0) || (outTime <= inTime)) {
            GLog.e(TAG, "applyVideoSticker, illegal in/out time: $inTime , $outTime  with index:$stickerInfo.withClip")
            return
        }

        var repeatType: Int = StickerInfo.REPEAT_TYPE_NONE
        if ((stickerInfo.extraFlags and TemplateStrategy.FLAG_REPEAT_TO_END) > 0) {
            repeatType = StickerInfo.REPEAT_TYPE_REPEAT
        } else if ((stickerInfo.extraFlags and TemplateStrategy.FLAG_REPEAT_LAST_FRAME) > 0) {
            repeatType = StickerInfo.REPEAT_TYPE_LAST_FRAME
        }

        when (stickerInfo.stickerType) {
            StickerInfo.TYPE_STATIC_STICKER -> {
                val stickerEffect = MeicamStickerEffect(stickerInfo.filePath)
                stickerEffect.setSize(stickerInfo.width, stickerInfo.height)
                stickerEffect.inTime = inTime
                stickerEffect.outTime = outTime
                stickerEffect.setRepeatType(repeatType)
                meicamTimeline.addEffectToTimeline(stickerEffect)
                effectList.add(stickerEffect)
            }
            StickerInfo.TYPE_DYNAMIC_STICKER -> {
                /*
                do nothing
                */
            }
            StickerInfo.TYPE_ANIMATED_STICKER -> {
                /*
                do nothing
                */
            }
        }
    }

    override fun reAlignVideoSticker(inTimeNs: Long) {
        for (timelineEffect in effectList) {
            if (timelineEffect is MeicamTimelineEffect) {
                timelineEffect.reAlignVideoSticker(inTimeNs)
            }
        }
    }

    override fun removeVideoSticker() {
        for (timelineEffect in effectList) {
            if (timelineEffect is MeicamTimelineEffect) {
                meicamTimeline.nvsTimeline.removeTimelineVideoFx(timelineEffect.nvsTimelineVideoFx)
            }
        }
        effectList.clear()
    }
}