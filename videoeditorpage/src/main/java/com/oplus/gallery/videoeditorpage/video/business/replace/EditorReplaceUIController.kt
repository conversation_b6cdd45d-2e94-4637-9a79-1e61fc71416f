/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - Editor<PERSON><PERSON>lace<PERSON>Controller
 ** Description: XXX.
 ** Version: 1.0
 ** Date : 2025/06/09
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >      <desc>
 **  80320709      2025/06/09  1.0             created
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.replace

import android.content.Context
import android.util.SparseArray
import android.view.View
import android.view.ViewGroup
import androidx.core.util.isNotEmpty
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_0
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.TRACK_INDEX_FIRST
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.replace.view.OnClipViewScrollListener
import com.oplus.gallery.videoeditorpage.video.business.replace.view.ReplaceVideoClipView
import com.oplus.gallery.videoeditorpage.video.business.replace.view.ReplaceVideoClipView.Companion.BORDER_VIEW_WIDTH_DP
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel

/**
 * 替换业务UI
 */
class EditorReplaceUIController(
    context: Context,
    rootView: ViewGroup,
    editorReplaceState: EditorReplaceState,
    /** 替换的新时间线 */
    private val replaceTimeline: ITimeline,
    /** 替换的媒体资源 */
    private val mediaItem: MediaItem,
    /** 编辑的视频片段时长 */
    private val editVideoClipDuration: Long,
    /** 替换业务选中监听 */
    private val onSelectionListener: EditorReplaceHelper.ReplaceSelectionListener,
) : EditorTrackBaseUIController<Any>(context, rootView, editorReplaceState) {

    /** 替换视频轨片段视图 */
    private val replaceVideoClipView: ReplaceVideoClipView by lazy { findContainerChild(R.id.replace_video_clip_view) }

    /** 视频轨道片段视图手指按下时水平方向滚动距离 */
    private var clipViewScrollXWhenTouchDown: Int = 0

    val vm: EditorReplaceVM by lazy {
        val factory = EditorReplaceVMFactory(
            mEditorState.editorEngine,
            replaceTimeline,
            mediaItem,
            editVideoClipDuration,
            onSelectionListener
        )
        ViewModelProvider(this, factory)[EditorReplaceVM::class.java]
    }

    override fun getContainerId(): Int = R.id.overlap_container

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return R.layout.videoeditor_replace_layout
    }

    override fun getTitleId(): Int {
        return R.string.videoeditor_ai_extract_replace
    }

    override fun needPlaySeekBar(): Boolean {
        return false
    }

    override val trackScene: EditorTrackScene = EditorTrackScene.REPLACE

    override fun createView() {
        super.createView()
        val clipSparseArray: SparseArray<ArrayList<ClipModel>> =
            (mEditorState as EditorTrackBaseState).getClipArrayFromTimeline(replaceTimeline)
        val replaceClipModel = clipSparseArray.takeIf { it.isNotEmpty() }
            ?.get(TRACK_INDEX_FIRST)?.get(NUMBER_0)?.getOrLog("replaceClipModel is null") ?: return
        replaceClipModel.let { replaceVideoClipView.setClipModel(it, editVideoClipDuration) }

        replaceVideoClipView.onClipViewScrollListener = object : OnClipViewScrollListener {
            override fun onDown(scrollX: Int) {
                clipViewScrollXWhenTouchDown = scrollX
            }

            override fun onMove(scrollX: Int) {
                vm.stopPlaybackIfPlaying()
                vm.setEngineStartPlayPosition(getPlayPositionByDistance(scrollX))
                if (vm.playProgress.value != MathUtils.ZERO_F) {
                    // 时码线当前的播放进度设置为0
                    vm.setPlayProgress(MathUtils.ZERO_F)
                }
            }

            override fun onStop(scrollX: Int) {
                if (scrollX != clipViewScrollXWhenTouchDown) {
                    // 视频轨道有滚动时，需要重新播放
                    vm.startEnginePlay(getPlayPositionByDistance(scrollX))
                }
            }
        }
        vm.playProgress.observe(this) { playProgress ->
            replaceVideoClipView.setTimelineSpanImageViewPlayProgress(playProgress)
        }
    }
    /**
     * 点击取消
     */
    fun clickCancel() {
        vm.destroy()
    }

    /**
     * 点击完成
     */
    fun clickDone() {
        vm.finishClipReplace()
    }

    override fun destroyView() {
        super.destroyView()
        replaceVideoClipView.onClipViewScrollListener = null
    }

    override fun onAppUiStateChanged(appUiConfig: AppUiConfig) {
        super.onAppUiStateChanged(appUiConfig)
        val windowWidth = appUiConfig.windowWidth.current
        replaceVideoClipView.requestLayout(vm.playProgress.value, windowWidth)
    }


    /**
     * 根据距离计算播放位置
     *
     * @param distance 距离值，用于计算播放位置
     * @return 计算得到的播放位置
     */
    private fun getPlayPositionByDistance(distance: Int): Long {
        return editVideoClipDuration * distance / ScreenUtils.dpToPixel(BORDER_VIEW_WIDTH_DP)
    }

    /**
     * 查找container下的子view控件
     */
    private fun <T : View> findContainerChild(id: Int): T = mContentContainer.findViewById(id)
}