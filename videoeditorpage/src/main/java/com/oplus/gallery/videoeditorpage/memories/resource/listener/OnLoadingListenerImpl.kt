/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OnLoadingListenerImpl.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/09/23
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** YeG<PERSON><EMAIL>		2021/09/23		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.listener

import com.oplus.gallery.foundation.util.debug.GLog

open class OnLoadingListenerImpl<T> : OnLoadingListener<T> {
    companion object {
        private const val TAG = "OnLoadingListenerImpl"
    }

    override fun onLoadingFinish(code: Int, allEntityList: MutableList<T>?) {
    }

    override fun onIconDownloadFinish(t: T) {
    }

    override fun onIconDownloadError(errCode: Int, t: T) {
    }

    override fun onLoadingError(errCode: Int) {
        GLog.e(TAG, "onLoadingError $errCode")
    }
}