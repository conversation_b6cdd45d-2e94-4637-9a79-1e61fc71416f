/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ShadowSelector.kt
 ** Description : 文字阴影效果面板组件
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.ProgressSeekbar

/**
 * 文字阴影效果面板组件
 */
internal class ShadowSelector(
    pageType: PageType
) : TextEffectsSelector(pageType) {

    override fun getTextEffectsTypeConfig(): List<TextEffectsType> {
        return listOf(
            TextEffectsType.TRANSPARENT,
            TextEffectsType.BLUR,
            TextEffectsType.ANGLE,
            TextEffectsType.DISTANCE
        )
    }

    override fun initData() = Unit

    /**
     * 滑动取值后进行更新字幕设置
     */
    override fun onChanged(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) {
        when (seekBar.progressId) {
            TextEffectsType.TRANSPARENT.progressId -> updateTransparent(progress, isIncreasing)
            TextEffectsType.BLUR.progressId -> updateBlur(progress, isIncreasing)
            TextEffectsType.ANGLE.progressId -> updateAngle(progress, isIncreasing)
            TextEffectsType.DISTANCE.progressId -> updateDistance(progress, isIncreasing)
        }
    }

    private fun updateTransparent(progress: Float, isIncreasing: Boolean) {
        // 更新透明度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.TRANSPARENT,
            progress,
            isIncreasing
        )
    }

    private fun updateBlur(progress: Float, isIncreasing: Boolean) {
        // 更新模糊度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.BLUR,
            progress,
            isIncreasing
        )
    }

    private fun updateAngle(progress: Float, isIncreasing: Boolean) {
        // 更新角度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.ANGLE,
            progress,
            isIncreasing
        )
    }

    private fun updateDistance(progress: Float, isIncreasing: Boolean) {
        // 更新距离
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.DISTANCE,
            progress,
            isIncreasing
        )
    }

    companion object {
        private const val TAG = "ShadowSelector"
    }
}
