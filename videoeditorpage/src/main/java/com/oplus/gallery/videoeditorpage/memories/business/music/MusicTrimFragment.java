/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MusicTrimFragment.java
 * Description:
 * Version: 1.0
 * Date: 2022/3/1
 * Author: Luya<PERSON>.Tan@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Luyao.Tan@Apps.Gallery3D 2022/3/1     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 **************************************************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.music;

import android.content.AsyncQueryHandler;
import android.content.BroadcastReceiver;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.CharArrayBuffer;
import android.database.Cursor;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Parcelable;
import android.os.PowerManager;
import android.provider.MediaStore;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.SectionIndexer;
import android.widget.SimpleCursorAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.coui.appcompat.button.COUIButton;
import com.coui.appcompat.list.COUIListView;
import com.coui.appcompat.theme.COUIThemeOverlay;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.google.android.material.appbar.AppBarLayout;
import com.oplus.gallery.addon.utils.OplusChangeTextUtilWrapper;
import com.oplus.gallery.foundation.database.util.MediaStoreUtils;
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.common.Constants;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Activity allowing the user to select a music track on the device, and
 * return it to its caller.  The music picker user interface is fairly
 * extensive, providing information about each track like the music
 * application (title, author, album, duration), as well as the ability to
 * previous tracks and sort them in different orders.
 * <p>
 * <p>This class also illustrates how you can load data from a content
 * provider asynchronously, providing a good UI while doing so, perform
 * indexing of the content for use inside of a {@link }, and
 * perform filtering of the data as the user presses keys.
 */
public class MusicTrimFragment extends Fragment implements MediaPlayer.OnCompletionListener, MediaPlayer.OnErrorListener {
    private static final String TAG = "MusicTrimFragment";
    /**
     * 裁剪后返回裁剪片段的信息的key
     */
    public static final String MUSIC_TRIM_DURATION = "music_trim_duration";
    public static final String MUSIC_TRIM_START = "music_trim_start";
    public static final String MUSIC_TRIM_END = "music_trim_end";

    /**
     * min trim music 3s
     */
    public static final int TRIM_MUSIC_MIN_DURATION = 3000;
    public static final int BUFFER1_SIZE = 100;
    public static final int BUFFER2_SIZE = 200;
    public static final int POST_DELAY = 100;
    public static final int TRIM_DURATION = 100;
    public static final int SMOOTH_SCROLL_TIME = 100;
    public static final String MUSIC_FILE_PATH = "music_file_path";
    static final boolean DBG = false;
    /**
     * Holds the previous state of the list, to restore after the async
     * query has completed.
     */
    static final String LIST_STATE_KEY = "liststate";
    /**
     * Remember whether the list last had focus for restoring its state.
     */
    static final String FOCUS_KEY = "focused";
    /**
     * Remember the last ordering mode for restoring state.
     */
    static final String SORT_MODE_KEY = "sortMode";

    /**
     * Arbitrary number, doesn't matter since we only do one query type.
     */
    static final int MY_QUERY_TOKEN = 42;

    /**
     * Menu item to sort the music list by track title.
     */
    static final int TRACK_MENU = Menu.FIRST;
    /**
     * Menu item to sort the music list by album title.
     */
    static final int ALBUM_MENU = Menu.FIRST + 1;
    /**
     * Menu item to sort the music list by artist name.
     */
    static final int ARTIST_MENU = Menu.FIRST + 2;

    /**
     * These are the columns in the music cursor that we are interested in.
     */
    static final String[] CURSOR_COLS = new String[]{
        MediaStore.Audio.Media._ID,
        MediaStore.Audio.Media.TITLE,
        MediaStore.Audio.Media.TITLE_KEY,
        MediaStoreUtils.DATA,
        MediaStore.Audio.Media.ALBUM,
        MediaStore.Audio.Media.ARTIST,
        MediaStore.Audio.Media.ARTIST_ID,
        MediaStore.Audio.Media.DURATION,
        MediaStore.Audio.Media.TRACK,
        MediaStore.Audio.Media.DISPLAY_NAME
    };
    private static final String UNKNOWN_TAG = "<unknown>";
    private static final int TRIM_END_START_PLAY_TIME_OFFSET = 3000; // 3s

    protected View mEmptyPageView;

    /**
     * list load prompt
     */
    protected View mLoadingPageProcess;
    protected View mLoadingPageTips;

    /**
     * list load manual
     */
    protected View mLoadingPageManual;
    private COUIToolbar mToolBar;
    private AppBarLayout mColorAppBarLayout;

    /**
     * Uri to the directory of all music being displayed.
     */
    private Uri mBaseUri;

    /**
     * This is the adapter used to display all of the tracks.
     */
    private TrackListAdapter mAdapter;
    /**
     * Our instance of QueryHandler used to perform async background queries.
     */
    private QueryHandler mQueryHandler;

    /**
     * Used to keep track of the last scroll state of the list.
     */
    private Parcelable mListState = null;
    /**
     * Used to keep track of whether the list last had focus.
     */
    private boolean mListHasFocus;

    /**
     * The current cursor on the music that is being displayed.
     */
    private Cursor mCursor;
    /**
     * The actual sort order the user has selected.
     */
    private int mSortMode = -1;
    /**
     * SQL order by string describing the currently selected sort order.
     */
    private String mSortOrder = MediaStore.Audio.Media.TITLE_KEY;

    /**
     * Container of the in-screen progress indicator, to be able to hide it
     * when done loading the initial cursor.
     */
    private View mProgressContainer;
    /**
     * Container of the list view hierarchy, to be able to show it when done
     * loading the initial cursor.
     */
    private View mListContainer;
    /**
     * Set to true when the list view has been shown for the first time.
     */
    private boolean mListShown;

    /**
     * Which track row ID the user has last selected.
     */
    private long mSelectedId = -1;
    /**
     * Completel Uri that the user has last selected.
     */
    private Uri mSelectedUri;

    private String mSelectedFilePath;

    /**
     * If >= 0, we are currently playing a track for preview, and this is its
     * row ID.
     */
    private long mPlayingId = -1;

    /**
     * This is used for playing previews of the music files.
     */
    private MediaPlayer mMediaPlayer;
    private COUIListView mListView = null;
    private boolean mIsGetAudioFocus = false;
    private int mListPaddingTop;
    private AudioFocusRequest mAudioFocusRequest = null;
    private AudioManager mAudioManager = null;
    private ResultCallback mResultCallback;

    private float mLeftTrim = 0f;
    private float mRightTrim = 1f;
    private long mSelectDuration;

    private int mSelectedIndex = 0;
    private ViewHolder mSelectedViewHolder = null;

    private Handler mRefreshHandler = new Handler();
    private UpdateSbRunnable mUpdateSbRunnable = new UpdateSbRunnable(this);

    private BroadcastReceiver mStopPreviewReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            GLog.v(TAG, "onReceive, action=" + action);
            if (null == action) {
                return;
            }
            if (action.equals(Intent.ACTION_MEDIA_MOUNTED)) {
                doQuery(false, null);
            } else if (action.equals(Intent.ACTION_MEDIA_UNMOUNTED)) {
                stopMediaPlayer();
                finish();
                return;
            } else {
                doQuery(false, null);
            }
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    };

    private GalleryMusicTrimView.TrimMusicChangeListener mTrimMusicChangeListener = new GalleryMusicTrimView.TrimMusicChangeListener() {
        @Override
        public void onTrimChange(float leftPosition, float rightPosition) {
            if (leftPosition >= 0) {
                mLeftTrim = leftPosition;
                if (mSelectedViewHolder != null) {
                    mSelectedViewHolder.mLeftTrim.setText(VideoEditorHelper.formatTimeWithMillis(
                        MusicTrimFragment.this.getContext(), (int) (mLeftTrim * mSelectDuration)));
                }
                pauseWhileTrim();
            }
            if (rightPosition >= 0) {
                mRightTrim = rightPosition;
                if (mSelectedViewHolder != null) {
                    mSelectedViewHolder.mRightTrim.setText(VideoEditorHelper.formatTimeWithMillis(
                        MusicTrimFragment.this.getContext(), (int) (mRightTrim * mSelectDuration)));
                }
                pauseWhileTrim();
            }
        }

        @Override
        public void onTrimFinish(boolean isTouchRight) {
            if (mMediaPlayer != null) {
                int leftTrimTime = (int) (mLeftTrim * mSelectDuration);
                int rightTrimTime = (int) (mRightTrim * mSelectDuration);
                if (isTouchRight && ((rightTrimTime - leftTrimTime) > TRIM_END_START_PLAY_TIME_OFFSET)) {
                    playerSeekTo(rightTrimTime - TRIM_END_START_PLAY_TIME_OFFSET);
                } else {
                    playerSeekTo(leftTrimTime);
                }
                if (!mMediaPlayer.isPlaying()) {
                    updatePlayPos(false);
                    startPlayer();
                    if (mAdapter != null) {
                        mAdapter.notifyDataSetChanged();
                    }
                    updatePlayButtonImage(true);
                }
            }
        }
    };

    private View.OnClickListener mOnClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int id = v.getId();
            if (id == R.id.image_play) {
                GLog.d(TAG, "onClick() image_play");
                if (mMediaPlayer != null) {
                    if (mIsPlayerError) {
                        GLog.e(TAG, "onClick() image_play, isPlayerError = " + mIsPlayerError);
                        return;
                    }
                    if (mMediaPlayer.isPlaying()) {
                        mRefreshHandler.removeCallbacks(mUpdateSbRunnable);
                        mRefreshHandler.postDelayed(mUpdateSbRunnable, POST_DELAY);
                        pausePlayer();
                        updatePlayButtonImage(false);
                    } else {
                        updatePlayPos(false);
                        startPlayer();
                        updatePlayButtonImage(true);
                    }
                } else if (mCursor != null) {
                    mCursor.moveToPosition(mSelectedIndex);
                    updateSelected();
                }
            } else if (id == R.id.use_music_btn) {
                long start = (long) (mLeftTrim * mSelectDuration);
                long end = (long) (mRightTrim * mSelectDuration);
                if (end - start < TRIM_MUSIC_MIN_DURATION) {
                    end = Math.min(mSelectDuration, start + TRIM_MUSIC_MIN_DURATION);
                    start = Math.max(0, end - TRIM_MUSIC_MIN_DURATION);
                }

                GLog.d(TAG, "onClick() use_music_btn, start:" + start
                    + ", end:" + end
                    + ", mSelectedUri:" + mSelectedUri);
                if (mSelectedId >= 0) {
                    Intent intent = new Intent();
                    intent.setData(mSelectedUri);
                    intent.putExtra(MUSIC_FILE_PATH, mSelectedFilePath);
                    intent.putExtra(MUSIC_TRIM_START, start);
                    intent.putExtra(MUSIC_TRIM_END, end);
                    intent.putExtra(MUSIC_TRIM_DURATION, mSelectDuration);
                    finish();
                    if (mResultCallback != null) {
                        mResultCallback.onResult(intent);
                    }
                }
            }
        }
    };

    /**
     * 是否存在播放器错误
     */
    private boolean mIsPlayerError = false;

    public void updateUnavailableView(int visible) {
        GLog.d(TAG, " updateUnavailableView visible=" + visible);
        if (mEmptyPageView != null) {
            mEmptyPageView.setVisibility(visible);
        }
    }

    @Override
    public void onCompletion(MediaPlayer mp) {
        GLog.d(TAG, "onCompletion isPlayerError = " + mIsPlayerError);
        if ((mMediaPlayer == mp) && !mIsPlayerError) {
            updatePlayPos(true);
        }
    }

    @Override
    public boolean onError(MediaPlayer mp, int what, int extra) {
        GLog.d(TAG, "onError what = " + what + ", extra = " + extra);
        mIsPlayerError = true;
        return false;
    }

    private void pausePlayer() {
        if (!mIsPlayerError) {
            mMediaPlayer.pause();
        } else {
            GLog.e(TAG, "pausePlayer, isPlayerError is true ");
        }
    }

    private void playerSeekTo(int ms) {
        if (!mIsPlayerError) {
            mMediaPlayer.seekTo(ms);
        }
    }

    private void startPlayer() {
        if (!mIsPlayerError) {
            mMediaPlayer.start();
        } else {
            GLog.e(TAG, "startPlayer, isPlayerError is true ");
        }
    }

    private void stopPlayer() {
        if (!mIsPlayerError) {
            mMediaPlayer.stop();
        } else {
            GLog.e(TAG, "stopPlayer, isPlayerError is true ");
        }
    }

    /**
     * A special implementation of SimpleCursorAdapter that knows how to bind
     * our cursor data to our list item structure, and takes care of other
     * advanced features such as indexing and filtering.
     */
    class TrackListAdapter extends SimpleCursorAdapter implements SectionIndexer {
        final ListView mListView;

        private final StringBuilder mBuilder = new StringBuilder();
        private final String mUnknownArtist;
        private final String mUnknownAlbum;

        private int mIdIdx;
        private int mTitleIdx;
        private int mArtistIdx;
        private int mAlbumIdx;
        private int mDurationIdx;

        private boolean mLoading = true;
        private int mIndexerSortMode;
        private boolean mIndexerOutOfDate;
        private AlphabetIndexer mIndexer;

        @SuppressWarnings("deprecation")
        TrackListAdapter(Context context, ListView listView, int layout,
                         String[] from, int[] to) {
            super(context, layout, null, from, to);
            mListView = listView;
            mUnknownArtist = context.getString(R.string.videoeditor_unknown_artist_name);
            mUnknownAlbum = context.getString(R.string.videoeditor_unknown_album_name);
        }

        /**
         * The mLoading flag is set while we are performing a background
         * query, to avoid displaying the "No music" empty view during
         * this time.
         */
        public void setLoading(boolean loading) {
            mLoading = loading;
        }

        @Override
        public boolean isEmpty() {
            if (mLoading) {
                // We don't want the empty state to show when loading.
                return false;
            } else {
                return super.isEmpty();
            }
        }

        private void getColumnIndices(Cursor cursor) {
            if (cursor != null) {
                // Retrieve indices of the various columns we are interested in.
                mIdIdx = cursor.getColumnIndex(MediaStore.Audio.Media._ID);
                mTitleIdx = cursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME);
                mArtistIdx = cursor.getColumnIndex(MediaStore.Audio.Media.ARTIST);
                mAlbumIdx = cursor.getColumnIndex(MediaStore.Audio.Media.ALBUM);
                mDurationIdx = cursor.getColumnIndex(MediaStore.Audio.Media.DURATION);

                /* If the sort mode has changed, or we haven't yet created an
                 * indexer one, then create a new one that is indexing the
                 * appropriate column based on the sort mode.
                 */
                if ((mIndexerSortMode != mSortMode) || (mIndexer == null)) {
                    mIndexerSortMode = mSortMode;
                    int idx = mTitleIdx;
                    switch (mIndexerSortMode) {
                        case ARTIST_MENU:
                            idx = mArtistIdx;
                            break;
                        case ALBUM_MENU:
                            idx = mAlbumIdx;
                            break;
                        default:
                            break;
                    }

                    mIndexer = new AlphabetIndexer(cursor, idx, getResources().getString(R.string.videoeditor_fast_scroll_alphabet));
                    /* If we have a valid indexer, but the cursor has changed since
                     * its last use, then point it to the current cursor.
                     */
                } else if (mIndexerOutOfDate) {
                    mIndexer.setCursor(cursor);
                }
            }
        }

        @Override
        public View newView(Context context, Cursor cursor, ViewGroup parent) {
            View view = super.newView(context, cursor, parent);
            view.setTag(new ViewHolder(view));
            return view;
        }

        @Override
        public void bindView(View view, Context context, Cursor cursor) {
            ViewHolder viewHolder = (ViewHolder) view.getTag();

            cursor.copyStringToBuffer(mTitleIdx, viewHolder.mBuffer);
            String title = new String(viewHolder.mBuffer.data, 0, viewHolder.mBuffer.sizeCopied);
            int extIndex = title.lastIndexOf('.');
            if (-1 == extIndex) {
                GLog.e(TAG, "can't find filename's extension! name =" + title);
                viewHolder.mMusicTitle.setText(title);
            } else {
                viewHolder.mMusicTitle.setText(title.substring(0, extIndex));
            }

            final StringBuilder builder = mBuilder;
            builder.delete(0, builder.length());

            String name = cursor.getString(mAlbumIdx);
            if ((name == null) || UNKNOWN_TAG.equals(name)) {
                builder.append(mUnknownAlbum);
            } else {
                builder.append(name);
            }
            builder.append("-");

            name = cursor.getString(mArtistIdx);
            if ((name == null) || UNKNOWN_TAG.equals(name)) {
                builder.append(mUnknownArtist);
            } else {
                builder.append(name);
            }
            int len = builder.length();
            if (viewHolder.mChars.length < len) {
                viewHolder.mChars = new char[len];
            }
            builder.getChars(0, len, viewHolder.mChars, 0);
            viewHolder.mMusicArtistAndAlbum.setText(viewHolder.mChars, 0, len);

            /* Update the checkbox of the item, based on which the user last
             * selected.  Note that doing it this way means we must have the
             * list view update all of its items when the selected item
             * changes.
             */
            final long id = cursor.getLong(mIdIdx);
            buildViewHolder(viewHolder, cursor, id);
            if (DBG) {
                GLog.v(TAG, "Binding id=" + id + " sel=" + mSelectedId
                    + " playing=" + mPlayingId + " cursor=" + cursor);
            }
        }

        private void buildViewHolder(ViewHolder viewHolder, Cursor cursor, long id) {
            final boolean isSelected = (id == mSelectedId);
            int visible = isSelected ? View.VISIBLE : View.GONE;
            viewHolder.mUseThisMusic.setVisibility(visible);
            viewHolder.mPlayControlGroup.setVisibility(visible);
            viewHolder.mMusicDuration.setText(" - " + VideoEditorHelper.formatTimeWithMillis(
                MusicTrimFragment.this.getContext(), cursor.getLong(mDurationIdx)));
            if (visible == View.VISIBLE) {
                GLog.d(TAG, "isPlaying->" + ((mMediaPlayer != null) && mMediaPlayer.isPlaying())
                    + ",mSelectedViewHolder == viewHolder->" + (mSelectedViewHolder == viewHolder));
                mSelectedViewHolder = viewHolder;
                viewHolder.mLeftTrim.setText(VideoEditorHelper.formatTimeWithMillis(
                    MusicTrimFragment.this.getContext(), (int) (mLeftTrim * mSelectDuration)));
                viewHolder.mRightTrim.setText(VideoEditorHelper.formatTimeWithMillis(
                    MusicTrimFragment.this.getContext(), (int) (mRightTrim * mSelectDuration)));
                int playPos = (mMediaPlayer != null) ? (mMediaPlayer.getCurrentPosition()) : 0;
                viewHolder.mTrimView.setTrimStatus(cursor.getLong(mDurationIdx), mLeftTrim, mRightTrim, playPos);
            }
            viewHolder.mPlayButton.setClickable(isSelected);
            if (isSelected && (mMediaPlayer != null) && mMediaPlayer.isPlaying()) {
                viewHolder.mPlayButton.setSelected(true);
                viewHolder.mPlayButton.setContentDescription(getString(R.string.videoeditor_editor_pause_description));
            } else {
                viewHolder.mPlayButton.setSelected(false);
                viewHolder.mPlayButton.setContentDescription(getString(R.string.videoeditor_editor_play_description));
            }
        }

        /**
         * This method is called whenever we receive a new cursor due to
         * an async query, and must take care of plugging the new one in
         * to the adapter.
         */
        @Override
        public void changeCursor(Cursor cursor) {
            super.changeCursor(cursor);
            if (DBG) {
                GLog.v(TAG, "changeCursor, Setting cursor to: " + cursor + " from: " + MusicTrimFragment.this.mCursor);
            }

            MusicTrimFragment.this.mCursor = cursor;

            getColumnIndices(cursor);

            /* The next time the indexer is needed, we will need to rebind it
             * to this cursor.
             */
            mIndexerOutOfDate = true;

            /* Ensure that the list is shown (and initial progress indicator
             * hidden) in case this is the first cursor we have gotten.
             */
            makeListShown();
        }

        /**
         * This method is called from a background thread by the list view
         * when the user has typed a letter that should result in a filtering
         * of the displayed items.  It returns a Cursor, when will then be
         * handed to changeCursor.
         */
        @Override
        public Cursor runQueryOnBackgroundThread(CharSequence constraint) {
            if (DBG) {
                GLog.v(TAG, "Getting new cursor...");
            }
            return doQuery(true, constraint.toString());
        }

        @Override
        public int getPositionForSection(int section) {
            GLog.v(TAG, "getPositionForSection mIndexer =" + mIndexer);
            Cursor cursor = getCursor();
            if (cursor == null) {
                // No cursor, the section doesn't exist so just return 0
                return 0;
            }
            getColumnIndices(cursor);
            mIndexerOutOfDate = false;

            return mIndexer.getPositionForSection(section);
        }

        @Override
        public int getSectionForPosition(int position) {
            return 0;
        }

        @Override
        public Object[] getSections() {
            if (mIndexer != null) {
                return mIndexer.getSections();
            } else {
                return null;
            }
        }
    }

    private class ViewHolder {
        TextView mMusicTitle;
        TextView mMusicArtistAndAlbum;
        TextView mMusicDuration;
        COUIButton mUseThisMusic;
        LinearLayout mPlayControlGroup;
        GalleryMusicTrimView mTrimView;
        TextView mLeftTrim;
        TextView mRightTrim;
        ImageView mPlayButton;
        CharArrayBuffer mBuffer;
        char[] mChars;

        ViewHolder(View parent) {
            mMusicTitle = parent.findViewById(R.id.tv_line1);
            mMusicArtistAndAlbum = parent.findViewById(R.id.tv_line2);
            mUseThisMusic = parent.findViewById(R.id.use_music_btn);
            mUseThisMusic.setTextSize(TypedValue.COMPLEX_UNIT_PX,
                (int) OplusChangeTextUtilWrapper.getSuitableFontSize(mUseThisMusic.getTextSize(),
                    getResources().getConfiguration().fontScale, OplusChangeTextUtilWrapper.G2));
            mUseThisMusic.setOnClickListener(mOnClickListener);
            mMusicDuration = parent.findViewById(R.id.music_duration);
            mPlayControlGroup = parent.findViewById(R.id.play_control);
            mPlayControlGroup.setOnClickListener(mOnClickListener);
            mPlayButton = parent.findViewById(R.id.image_play);
            mPlayButton.setOnClickListener(mOnClickListener);
            mTrimView = parent.findViewById(R.id.trim_progress);
            mTrimView.setSlowMotionChangeListener(mTrimMusicChangeListener);
            mLeftTrim = parent.findViewById(R.id.left_time);
            mRightTrim = parent.findViewById(R.id.right_time);
            mBuffer = new CharArrayBuffer(BUFFER1_SIZE);
            mChars = new char[BUFFER2_SIZE];
        }
    }

    /**
     * This is our specialization of AsyncQueryHandler applies new cursors
     * to our state as they become available.
     */
    private final static class QueryHandler extends AsyncQueryHandler {
        private WeakReference<MusicTrimFragment> mFragment;

        public QueryHandler(MusicTrimFragment fragment) {
            super(fragment.getActivity().getContentResolver());
            mFragment = new WeakReference<>(fragment);
        }

        @Override
        protected void onQueryComplete(int token, Object cookie, Cursor cursor) {
            MusicTrimFragment fragment = null;
            if (mFragment != null) {
                fragment = mFragment.get();
            }
            if (fragment != null) {
                if (!fragment.isDetached()) {
                    if ((cursor == null) || (cursor.getCount() == 0)) {
                        fragment.updateUnavailableView(View.VISIBLE);
                        fragment.mListView.setVisibility(View.GONE);
                    } else {
                        fragment.updateUnavailableView(View.GONE);
                        fragment.mListView.setVisibility(View.VISIBLE);
                    }
                    /* Update the adapter: we are no longer loading, and have
                     * a new cursor for it.
                     */
                    fragment.mAdapter.setLoading(false);
                    fragment.mAdapter.changeCursor(cursor);

                    // Now that the cursor is populated again, it's possible to restore the list state
                    if (fragment.mListState != null) {
                        fragment.mListView.onRestoreInstanceState(fragment.mListState);
                        if (fragment.mListHasFocus) {
                            fragment.mListView.requestFocus();
                        }
                        fragment.mListHasFocus = false;
                        fragment.mListState = null;
                    }
                } else if (null != cursor) {
                    cursor.close();
                }
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        COUIThemeOverlay.getInstance().applyThemeOverlays(this.getActivity());
        initData(savedInstanceState);
        View view = inflater.inflate(R.layout.videoeditor_video_editor_music_picker, null, false);
        initViews(view);
        registerBroadcast();
        mQueryHandler = new QueryHandler(this);
        mAudioManager = (AudioManager) getContext().getApplicationContext().getSystemService(Context.AUDIO_SERVICE);
        setSortMode((savedInstanceState == null) ? TRACK_MENU : savedInstanceState.getInt(SORT_MODE_KEY, TRACK_MENU));
        ((PanelFragment) getParentFragment()).setDialogOnKeyListener((dialog, keyCode, event) -> {
            if (event.getAction() == KeyEvent.ACTION_UP) {
                return onKeyUP(keyCode, event);
            }
            return false;
        });
        return view;
    }

    /**
     * Maybe finish activity
     */
    private void initData(Bundle icicle) {
        if (icicle == null) {
            if (getArguments() != null) {
                mSelectedUri = getArguments().getParcelable(RingtoneManager.EXTRA_RINGTONE_EXISTING_URI);
            }
        } else {
            mSelectedUri = icicle.getParcelable(RingtoneManager.EXTRA_RINGTONE_EXISTING_URI);
            mSelectedFilePath = icicle.getString(MUSIC_FILE_PATH);
            // Retrieve list state. This will be applied after the QueryHandler has run
            mListState = icicle.getParcelable(LIST_STATE_KEY);
            mListHasFocus = icicle.getBoolean(FOCUS_KEY);
        }
        mBaseUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
        // If there is a currently selected Uri, then try to determine who it is.
        if (mSelectedUri != null) {
            Uri.Builder builder = mSelectedUri.buildUpon();
            String path = mSelectedUri.getEncodedPath();
            int idx = path.lastIndexOf('/');
            if (idx >= 0) {
                path = path.substring(0, idx);
            }
            builder.encodedPath(path);
            Uri baseSelectedUri = builder.build();
            if (DBG) {
                GLog.v(TAG, "onCreate, Selected Uri: " + mSelectedUri + ", base Uri: " + baseSelectedUri + ", Base Uri: =" + mBaseUri);
            }
            if (baseSelectedUri.equals(mBaseUri)) {
                /* If the base Uri of the selected Uri is the same as our
                 * content's base Uri, then use the selection!
                 */
                mSelectedId = ContentUris.parseId(mSelectedUri);
            }
        }
    }

    private void initViews(View rootView) {
        mListPaddingTop = getResources().getDimensionPixelSize(R.dimen.video_editor_music_pick_list_padding_top);
        mListView = rootView.findViewById(android.R.id.list);
        mListView.setOnItemClickListener((adapterView, view, position, id) -> {
            mSelectedIndex = position;
            mSelectedViewHolder = (ViewHolder) view.getTag();
            mCursor.moveToPosition(position);
            if (DBG) {
                GLog.v(TAG, "onItemClick, Click on " + position + " (id=" + id + ") in cursor " + mCursor + ", adapter=" + adapterView.getAdapter());
            }
            if (mSelectedViewHolder.mPlayControlGroup.getVisibility() != View.VISIBLE) {
                mSelectedViewHolder.mTrimView.resetTrimStatus();
                mLeftTrim = 0;
                mRightTrim = 1f;
                updateSelected();
                final int pos = position;
                mListView.post(() -> {
                    if (mListView != null) {
                        View current = mListView.getChildAt(pos - mListView.getFirstVisiblePosition());
                        if (current != null) {
                            int offset = current.getBottom() - mListView.getBottom();
                            if (offset > 0) {
                                mListView.smoothScrollBy(offset, SMOOTH_SCROLL_TIME);
                            }
                        }
                    }
                });
            }
        });
        mListView.setItemsCanFocus(false);
        mListView.setTextFilterEnabled(true);
        // We manually save/restore the listview state
        mListView.setSaveEnabled(false);
        mListView.setVerticalFadingEdgeEnabled(false);
        mListView.setNestedScrollingEnabled(true);
        mAdapter = new TrackListAdapter(this.getContext(), mListView,
            R.layout.videoeditor_video_editor_music_picker_item, new String[]{}, new int[]{});
        mListView.setAdapter(mAdapter);
        mEmptyPageView = rootView.findViewById(R.id.empty_page_view);
        initLoadPage(rootView);
    }

    private void registerBroadcast() {
        IntentFilter itf = new IntentFilter();
        itf.addAction(Intent.ACTION_MEDIA_EJECT);
        itf.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        itf.addAction(Intent.ACTION_MEDIA_MOUNTED);
        itf.addAction(Intent.ACTION_MEDIA_REMOVED);
        itf.addAction(Intent.ACTION_MEDIA_SHARED);
        itf.addDataScheme("file");
        BroadcastDispatcher.INSTANCE.registerReceiver(getActivity(), mStopPreviewReceiver, itf,
            AppConstants.Permission.getPERMISSION_COMPONENT_SAFE(), null, 0);
    }

    private void unRegisterBroadcast() {
        try {
            BroadcastDispatcher.INSTANCE.unregisterReceiver(getActivity(), mStopPreviewReceiver);
        } catch (Exception e) {
            GLog.d(TAG, "unRegisterBroadcast, e=" + e);
        }
    }

    private void initLoadPage(View rootView) {
        mProgressContainer = rootView.findViewById(R.id.video_editor_loading_page);
        mLoadingPageProcess = rootView.findViewById(R.id.loading_process);
        mLoadingPageTips = rootView.findViewById(R.id.loading_progress_tip);
        mLoadingPageManual = rootView.findViewById(R.id.loading_manual);
        mLoadingPageManual.setVisibility(View.GONE);
    }

    protected void showLoadPage(boolean isLoading) {
        if (isLoading) {
            mProgressContainer.setVisibility(View.VISIBLE);
            mLoadingPageProcess.setVisibility(View.VISIBLE);
            mLoadingPageTips.setVisibility(View.VISIBLE);
        } else {
            mProgressContainer.setVisibility(View.GONE);
            mLoadingPageProcess.setVisibility(View.GONE);
            mLoadingPageTips.setVisibility(View.GONE);
        }
    }

    private void refreshListView(@NotNull AppUiResponder.AppUiConfig uiConfig) {
        final AppUiResponder.ScreenMode mode = uiConfig.getScreenMode().getCurrent();
        if ((mode == AppUiResponder.ScreenMode.LARGE) && uiConfig.getScreenMode().isChanged()) {
            if (mAdapter != null) {
                mAdapter.changeCursor(null);
            }
            refreshData();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        GLog.d(TAG, "onResume");
        refreshData();
    }

    private void refreshData() {
        mPlayingId = -1;
        mListView.invalidateViews();
        showLoadPage(true);
        doQuery(false, null);
        showLoadPage(false);
    }

    @Override
    public void onSaveInstanceState(Bundle icicle) {
        super.onSaveInstanceState(icicle);
        /* Save list state in the bundle so we can restore it after the
         * QueryHandler has run
         */
        icicle.putParcelable(LIST_STATE_KEY, mListView.onSaveInstanceState());
        icicle.putBoolean(FOCUS_KEY, mListView.hasFocus());
        icicle.putInt(SORT_MODE_KEY, mSortMode);
        icicle.putString(MUSIC_FILE_PATH, mSelectedFilePath);
    }

    @Override
    public void onPause() {
        super.onPause();
        GLog.v(TAG, "onPause");
        mListView.invalidateViews();
        if (mMediaPlayer != null) {
            pausePlayer();
        }
        if ((mAudioManager != null) && (mAudioFocusRequest != null)) {
            mAudioManager.abandonAudioFocusRequest(mAudioFocusRequest);
        }
    }

    @Override
    public void onDestroy() {
        GLog.v(TAG, "onDestroy");
        unRegisterBroadcast();
        mIsGetAudioFocus = false;
        super.onDestroy();
    }

    @Override
    public void onStop() {
        super.onStop();
        GLog.v(TAG, "onStop");
        stopMediaPlayer();
        /* We don't want the list to display the empty state, since when we
         * resume it will still be there and show up while the new query is
         * happening. After the async query finishes in response to onResume()
         * setLoading(false) will be called.
         */
        if (mAdapter != null) {
            mAdapter.setLoading(true);
            mAdapter.changeCursor(null);
        }
    }

    /**
     * Changes the current sort order, building the appropriate query string
     * for the selected order.
     */
    boolean setSortMode(int sortMode) {
        if (sortMode != mSortMode) {
            switch (sortMode) {
                case TRACK_MENU:
                    mSortMode = sortMode;
                    mSortOrder = MediaStore.Audio.Media.TITLE_KEY;
                    break;
                case ALBUM_MENU:
                    mSortMode = sortMode;
                    mSortOrder = MediaStore.Audio.Media.ALBUM_KEY + " ASC, "
                        + MediaStore.Audio.Media.TRACK + " ASC, "
                        + MediaStore.Audio.Media.TITLE_KEY + " ASC";
                    break;
                case ARTIST_MENU:
                    mSortMode = sortMode;
                    mSortOrder = MediaStore.Audio.Media.ARTIST_KEY + " ASC, "
                        + MediaStore.Audio.Media.ALBUM_KEY + " ASC, "
                        + MediaStore.Audio.Media.TRACK + " ASC, "
                        + MediaStore.Audio.Media.TITLE_KEY + " ASC";
                    break;
                default:
                    return false;
            }
        }
        return false;
    }

    /**
     * The first time this is called, we hide the large progress indicator
     * and show the list view, doing fade animations between them.
     */
    void makeListShown() {
        if (!mListShown) {
            mListShown = true;
            mProgressContainer.setVisibility(View.GONE);
        }
    }

    /**
     * Common method for performing a query of the music database, called for
     * both top-level queries and filtering.
     *
     * @param sync         If true, this query should be done synchronously and the
     *                     resulting cursor returned.  If false, it will be done asynchronously and
     *                     null returned.
     * @param filterstring If non-null, this is a filter to apply to the query.
     */
    Cursor doQuery(boolean sync, String filterstring) {
        GLog.d(TAG, "doQuery, sync=" + sync + ", filter=" + filterstring);
        // Cancel any pending queries
        mQueryHandler.cancelOperation(MY_QUERY_TOKEN);

        StringBuilder where = new StringBuilder();
        List<String> argsList = new ArrayList<>();

        where.append(MediaStore.Audio.Media.TITLE + " != ''");

        // Add in the filtering constraints
        if (filterstring != null) {
            String[] searchWords = filterstring.split(" ");
            for (int i = 0; i < searchWords.length; i++) {
                where.append(" AND ");
                where.append(MediaStore.Audio.Media.ARTIST_KEY + "||");
                where.append(MediaStore.Audio.Media.ALBUM_KEY + "||");
                where.append(MediaStore.Audio.Media.TITLE_KEY + " LIKE ?");
                argsList.add('%' + MediaStore.Audio.keyFor(searchWords[i]) + '%');
            }
        }

        // filter audio type
        where.append(" AND " + MediaStore.Audio.Media.MIME_TYPE);
        where.append(" IN (");
        for (String audioType : Constants.SUPPORT_AUDIO_TYPE) {
            where.append("?,");
            argsList.add(audioType);
        }
        where.delete(where.length() - 1, where.length());
        where.append(")");

        // filter audio duration must greater than or equal to 2 sec
        where.append(" AND ");
        where.append(MediaStore.Audio.Media.DURATION + " >= " + TRIM_MUSIC_MIN_DURATION);

        GLog.d(TAG, "doQuery, sync=" + sync + ", argsList=" + argsList + ", where=" + where);
        String[] selectionArgs = new String[argsList.size()];
        argsList.toArray(selectionArgs);
        if (sync) {
            try {
                return getContext().getContentResolver().query(mBaseUri, CURSOR_COLS,
                    where.toString(), selectionArgs, mSortOrder);
            } catch (UnsupportedOperationException ex) {
                GLog.d(TAG, "doQuery, ex=" + ex);
            }
        } else {
            mAdapter.setLoading(true);
            mQueryHandler.startQuery(MY_QUERY_TOKEN, null, mBaseUri, CURSOR_COLS,
                where.toString(), selectionArgs, mSortOrder);
        }
        return null;
    }

    void updateSelected() {
        Uri uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
        int index = mCursor.getColumnIndex(MediaStore.Audio.Media._ID);
        long newId = mCursor.getLong(index);
        mSelectedUri = ContentUris.withAppendedId(uri, newId);
        mSelectedId = newId;
        int filePathIndex = mCursor.getColumnIndex(MediaStore.Audio.Media.DATA);
        mSelectedFilePath = mCursor.getString(filePathIndex);
        if ((newId != mPlayingId) || (mMediaPlayer == null)) {
            stopMediaPlayer();
            mIsPlayerError = false;
            mMediaPlayer = new MediaPlayer();
            try {

                mMediaPlayer.setDataSource(this.getContext(), mSelectedUri);
                mMediaPlayer.setOnCompletionListener(this);
                mMediaPlayer.setOnErrorListener(this);
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setLegacyStreamType(AudioManager.STREAM_MUSIC)
                    .build();
                if (!mIsGetAudioFocus) {
                    mMediaPlayer.setAudioAttributes(audioAttributes);
                    mIsGetAudioFocus = true;
                }
                mMediaPlayer.prepare();
                mMediaPlayer.setWakeMode(this.getContext(), PowerManager.PARTIAL_WAKE_LOCK);
                GLog.d(TAG, "get Focus");
                createAudioFocusRequestIfNeed(audioAttributes);
                if (mAudioManager != null) {
                    mAudioManager.requestAudioFocus(mAudioFocusRequest);
                }
                startPlayer();
                mSelectDuration = mMediaPlayer.getDuration();
                updatePlayPos(false);
                updatePlayButtonImage(true);

                mPlayingId = newId;
                mListView.invalidateViews();
            } catch (Exception e) {
                //add by chentiequn 2011-3-21 begin  for:195568
                stopMediaPlayer();
                mListView.invalidateViews();
                ToastUtil.showShortToast(R.string.videoeditor_notif_not_supported);
                GLog.w(TAG, "Unable to play track", e);
            }
        } else {
            stopMediaPlayer();
            mListView.invalidateViews();
        }
    }

    private void createAudioFocusRequestIfNeed(AudioAttributes audioAttributes) {
        if (mAudioFocusRequest == null) {
            mAudioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                .setAudioAttributes(audioAttributes)
                .setOnAudioFocusChangeListener(focusChange -> {
                    // AudioFocus is a new feature: focus updates are made verbose on purpose
                    switch (focusChange) {
                        case AudioManager.AUDIOFOCUS_LOSS:
                        case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                            GLog.d(TAG, "AudioFocus: received AUDIOFOCUS_LOSS");
                            reset();
                            break;
                        case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                            GLog.d(TAG, "AudioFocus: received AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
                            if (!VideoEditorHelper.isTalkbackEnabled(getActivity().getContentResolver())) {
                                reset();
                            }
                            break;
                        case AudioManager.AUDIOFOCUS_GAIN:
                            GLog.d(TAG, "AudioFocus: received AUDIOFOCUS_GAIN ");
                            break;
                        case AudioManager.AUDIOFOCUS_GAIN_TRANSIENT:
                            break;
                        default:
                            GLog.e(TAG, "Unknown audio focus change code");
                            break;
                    }
                })
                .build();
        }
    }

    private void reset() {
        mSelectedUri = null;
        mSelectedFilePath = null;
        mSelectedId = -1;
        mPlayingId = -1;
        stopMediaPlayer();
        if (mListView != null) {
            mListView.post(() -> mListView.invalidateViews());
        }
    }

    void stopMediaPlayer() {
        if (mMediaPlayer != null) {
            mRefreshHandler.removeCallbacks(mUpdateSbRunnable);
            stopPlayer();
            mMediaPlayer.release();
            mMediaPlayer = null;
            mPlayingId = -1;
        }
    }

    public void finish() {
        ((PanelFragment) getParentFragment()).dismiss();
    }

    public static class AlphabetIndexer extends android.widget.AlphabetIndexer {

        public AlphabetIndexer(Cursor cursor, int sortedColumnIndex, CharSequence alphabet) {
            super(cursor, sortedColumnIndex, alphabet);
        }

        @Override
        protected int compare(String word, String letter) {
            String wordKey = MediaStore.Audio.keyFor(word);
            String letterKey = MediaStore.Audio.keyFor(letter);
            if (wordKey.startsWith(letter)) {
                return 0;
            } else {
                return wordKey.compareTo(letterKey);
            }
        }
    }

    public void setResultCallback(ResultCallback resultCallback) {
        this.mResultCallback = resultCallback;
    }

    private void pauseWhileTrim() {
        if (mMediaPlayer != null) {
            if (mMediaPlayer.isPlaying()) {
                pausePlayer();
            }
            playerSeekTo((int) (mLeftTrim * mSelectDuration));
            updatePlayButtonImage(false);
        }
    }

    private void updatePlayPos(boolean forceStop) {
        if ((mMediaPlayer != null) && (mSelectedViewHolder != null)) {
            int pos = mMediaPlayer.getCurrentPosition();
            if (forceStop || (pos > (int) (mRightTrim * mSelectDuration) - TRIM_DURATION)) {
                pos = (int) (mLeftTrim * mSelectDuration);
                GLog.d(TAG, "updatePlayPos = seekTo --> " + pos);
                playerSeekTo(pos);
                pausePlayer();

                mSelectedViewHolder.mTrimView.updateCurrentPos((float) pos / mSelectDuration);
                mSelectedViewHolder.mLeftTrim.setText(VideoEditorHelper.formatTimeWithMillis(this.getContext(), pos));
                updatePlayButtonImage(false);
                mRefreshHandler.removeCallbacks(mUpdateSbRunnable);
                return;
            }

            if (mSelectDuration != 0f) {
                mSelectedViewHolder.mTrimView.updateCurrentPos((float) pos / mSelectDuration);
            }
            mRefreshHandler.removeCallbacks(mUpdateSbRunnable);
            mRefreshHandler.postDelayed(mUpdateSbRunnable, POST_DELAY);
        }
    }

    private static final class UpdateSbRunnable implements Runnable {

        private final WeakReference<MusicTrimFragment> mSeekbarRef;

        protected UpdateSbRunnable(MusicTrimFragment activity) {
            mSeekbarRef = new WeakReference<>(activity);
        }

        @Override
        public void run() {
            final MusicTrimFragment activity = mSeekbarRef.get();
            if (activity != null) {
                activity.updatePlayPos(false);
            }
        }
    }

    private void updatePlayButtonImage(boolean isPlaying) {
        if (mSelectedViewHolder != null) {
            View playButton = mSelectedViewHolder.mPlayButton;
            if (isPlaying) {
                playButton.setSelected(true);
                playButton.setContentDescription(getString(R.string.videoeditor_editor_pause_description));
            } else {
                playButton.setSelected(false);
                playButton.setContentDescription(getString(R.string.videoeditor_editor_play_description));
            }
        }
    }

    public boolean onKeyUP(int keyCode, @NonNull KeyEvent event) {
        GLog.d(TAG, "onKeyUp() keyCode = " + keyCode);
        if (DoubleClickUtils.isFastDoubleClick()) {
            GLog.d(TAG, "onKeyUp() isFastDoubleClick  return");
            return false;
        }
        if ((mMediaPlayer == null) || (mPlayingId == -1)) {
            GLog.d(TAG, "onKeyUp() mMediaPlayer == null or mPlayingId == -1, return");
            return false;
        }
        switch (keyCode) {
            case KeyEvent.KEYCODE_HEADSETHOOK:
            case KeyEvent.KEYCODE_MEDIA_PLAY:
            case KeyEvent.KEYCODE_MEDIA_PAUSE:
            case KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE:
                if (mMediaPlayer.isPlaying()) {
                    pausePlayer();
                    updatePlayButtonImage(false);
                } else {
                    updatePlayPos(false);
                    startPlayer();
                    updatePlayButtonImage(true);
                }
                return true;
            case KeyEvent.KEYCODE_MEDIA_STOP:
                if (mMediaPlayer.isPlaying()) {
                    pausePlayer();
                    updatePlayButtonImage(false);
                }
                return true;
            default:
                break;
        }
        return false;
    }

    public interface ResultCallback {
        void onResult(Intent data);
    }
}