/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/6/13
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/6/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.helper;


import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.resource.room.dao.SimpleDao;

import java.util.List;

public abstract class AbsTableHelper<T> {
    private static final String TAG = "AbsTableHelper";

    protected SimpleDao mBaseDao;

    public long insert(T t) {
        try {
            return mBaseDao.insert(t);
        } catch (Exception e) {
            GLog.e(TAG, "insert e:" + e);
            return -1;
        }
    }

    public List<Long> insertAll(List<T> t) {

        try {
            return mBaseDao.insert(t);
        } catch (Exception e) {
            GLog.e(TAG, "insertAll e:" + e);
            return null;
        }
    }

    public int update(T t) {
        try {
            return mBaseDao.update(t);
        } catch (Exception e) {
            GLog.e(TAG, "update e:" + e);
            return -1;
        }
    }

    public abstract void clearBuiltin();

    public abstract List<T> getAllBuiltin();

    public abstract int getAllBuiltinSize();

    public abstract List<T> getAll();

    public abstract int getAllResourceSize();

    public abstract T getEntityByPosition(int position);

    public abstract T getEntityByResourceId(int resourceId);

    public abstract int getDownloadState(int resourceId);

    public abstract int deleteInvalidEntity(int maxPosition);

    public abstract List<T> getInvalidEntityList(int maxPosition);

    public abstract List<T> getNoIconEntityList();
}
