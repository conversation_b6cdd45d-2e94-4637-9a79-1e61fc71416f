/***********************************************************
 * * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * * All rights reserved.
 * *
 * * File:  - EditorStateManager.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2019/05/02
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                    <data>        <version>    <desc>
 * *  <EMAIL>  2019/05/02    1.0   build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.controler;

import static androidx.annotation.VisibleForTesting.PRIVATE;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.View;

import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;

import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.EditorStateContext;
import com.oplus.gallery.videoeditorpage.video.business.animator.IStateChangeLifecycle;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum;
import com.oplus.gallery.videoeditorpage.video.business.menu.EditorMenuState;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.widget.BottomActionBar;

import java.util.Stack;

/**
 * 页面切换控制类
 */
public class EditorStateManager implements BottomActionBar.OnActionItemClickListener {
    private static final String TAG = "EditorStateManager";

    @VisibleForTesting(otherwise = PRIVATE)
    public EditorMenuState mMenuState;

    @VisibleForTesting(otherwise = PRIVATE)
    public Stack<EditorBaseState> mEditorStateStack = new Stack<>();

    private Context mContext;
    private boolean mIsActivityResumed = false;
    private EditorBaseState mBeforeSate;
    private OnMonitorStateChangeForShowMaterialSelectListener mMonitorStateChangeListener;
    private OnStateResultListener mOnStateResultListener;
    private IStateChangeLifecycle mIStateChangeLifecycle;

    public EditorStateManager(Context context) {
        mContext = context;
    }

    public EditorBaseState enterMenuEditor(EditorControlView controlView) {
        if (controlView == null) {
            return null;
        }

        if (mMenuState == null) {
            mMenuState = new EditorMenuState(mContext, controlView);
        }
        mMenuState.create();
        mMenuState.resume(false);
        return mMenuState;
    }

    public EditorBaseState getCurrentEditorState() {
        return getTopEditorState();
    }

    public EditorBaseState getMenuState() {
        return mMenuState;
    }

    public void regisStateChangeLifecycle(IStateChangeLifecycle iStateChangeLifecycle) {
        mIStateChangeLifecycle = iStateChangeLifecycle;
    }

    public void removeStateChangeLifecycle() {
        mIStateChangeLifecycle = null;
    }

    public final EditorBaseState changeState(EditorBaseState state) {
        EditorBaseState topState = getTopEditorState();
        if (state == null) {
            GLog.w(TAG, LogFlag.DL, "[changeState] state is null, return topState");
            return topState;
        }
        if ((topState != null) && (topState.equals(state))) {
            GLog.w(TAG, LogFlag.DL, "[changeState] state is same topState, return topState");
            return topState;
        }
        GLog.d(TAG, LogFlag.DL, "[changeState] state: " + state + ", topState: " + topState);
        if (topState != null) {
            if (topState.isPreview() && state.isPreview()) {
                topState.destroy();
                mEditorStateStack.pop();
            } else {
                topState.pause(false);
            }

            /**
             * 底部菜单栏执行pause的场景：
             * 1.普通场景一级页A进二级页B
             * 2.特殊场景：一级页A跳一级页B再跳到二级页B1，这个过程B页面的isSkipAnim()值为true，底部菜单栏也需要跟随A页面一起执行淡出动画
             * 3 特殊场景：一级页A跳一级页B（音乐 文字），这个时候虽然这个过程B页面的isSkipAnim()值为true，但是实际空间并不足够添加特效，这个时候需要menu不需要pause消失
             */
            boolean hasEnoughTrackSpace = true;
            if (state instanceof EditorTrackBaseState) {
                hasEnoughTrackSpace = ((EditorTrackBaseState<?>) state).isCurrentTimeHasEnoughEmptyRange();
            }

            boolean enterSecondLevel = ((topState.isPreview() && !state.isPreview()) || state.isSkipAnim());
            if ((mMenuState != null) && enterSecondLevel && hasEnoughTrackSpace) {
                mMenuState.pause(false);
            }
        }

        if (mContext instanceof EditorStateContext) {
            ((EditorStateContext) mContext).onStateChange(state, topState);
        }

        mIStateChangeLifecycle.preChangeState(topState, state, () -> {
            startState(state);
            return null;
        });
        return topState;
    }

    private void startState(EditorBaseState state) {
        state.create();
        mEditorStateStack.push(state);
        if (mIsActivityResumed) {
            state.resume(false);
            mIStateChangeLifecycle.changeState();
        }
        EditorBaseUIController uiController = state.getUIController();
        if (uiController != null) {
            uiController.setActionItemClickListener(this);
        }
        switchEnginePlaybackVideoClipMode(state);
        if (mMonitorStateChangeListener != null) {
            mMonitorStateChangeListener.onCreateStateForShowMaterialSelect(state);
        }
    }

    /**
     * 切换engine 多视频播放模式或者单视频播放模式
     *
     * @param state
     */
    private void switchEnginePlaybackVideoClipMode(EditorBaseState state) {
        if ((mMenuState == null) || (mMenuState.getEnginePlayingTimeManager() == null) || (state == null)) {
            GLog.e(TAG, LogFlag.DL, "[switchEnginePlaybackVideoClipMode] mMenuState is null "
                    + "or state is null or getEnginePlayingTimeManager is null");
            return;
        }
        IClip processingClip = null;
        // 对二级页面需更新显示播放时间为对应处理的Clip的播放时间，所以这里获取页面正在处理的Clip（VideoClip or AudioClip）
        if (state.getPageLevel() == PageLevelEnum.PAGE_LEVEL_SECOND) {
            processingClip = state.getProcessingClip();
        }
        mMenuState.getEnginePlayingTimeManager().switchVideoClipPlaybackMode(state.getVideoPlaybackMode(), processingClip);
    }

    // do not add business logic into
    public final void onActivityResume() {
        if (mIsActivityResumed) {
            return;
        }
        mIsActivityResumed = true;

        EditorBaseState topState = getTopEditorState();
        if (topState != null) {
            topState.resume(true);
        } else if (mMenuState != null) {
            mMenuState.resume(true);
        }
    }

    // do not add business logic into
    public final void onActivityPause() {
        if (!mIsActivityResumed) {
            return;
        }
        mIsActivityResumed = false;

        EditorBaseState topState = getTopEditorState();
        if (topState != null) {
            topState.pause(true);
        } else if (mMenuState != null) {
            mMenuState.pause(true);
        }
    }

    public final void finish(EditorBaseState editorState) {
        GLog.d(TAG, "finish, state = " + editorState);
        if (editorState == null) {
            GLog.w(TAG, "finish, null editor state!");
            return;
        }

        boolean isNeedFinishActivity = ((mMenuState != null) && mEditorStateStack.empty());
        GLog.d(TAG, "finish, isNeedFinishActivity: " + isNeedFinishActivity);

        EditorBaseState topEditorState = null;
        if (isNeedFinishActivity) {
            // 直接结束Activity的场景
            editorState.pause(false);
            editorState.destroy();
            if (mMenuState != null) {
                mMenuState.destroy();
            }
            if (mContext instanceof Activity) {
                ((Activity) mContext).finish();
            }
        } else {
            // 正常状态切换的场景
            topEditorState = handleStateFinish(editorState);
        }

        // 后续处理
        switchEnginePlaybackVideoClipMode(topEditorState);
        if (mMonitorStateChangeListener != null) {
            mMonitorStateChangeListener.onFinishStateForShowMaterialSelect(editorState, topEditorState);
        }
    }

    private EditorBaseState handleStateFinish(EditorBaseState editorState) {
        EditorBaseState topState = getTopEditorState();
        GLog.d(TAG, "finish, topState null: " + (topState == null));
        if (topState == null) {
            editorState.destroy();
            return null;
        }

        if (editorState.isDestroy()) {
            return topState;
        }

        if (editorState != topState) {
            GLog.w(TAG, "The editor state to be finished"
                    + " is not at the top of the stack: " + editorState + ", topState: " + topState);
        }

        if (!mEditorStateStack.isEmpty()) {
            mEditorStateStack.pop();
        }

        GLog.d(TAG, "finish, editorState = " + editorState + ", stack size = "
                + mEditorStateStack.size() + ",mPreviewState is null: " + (mMenuState == null));
        mBeforeSate = editorState;
        editorState.destroy();

        if (!mEditorStateStack.isEmpty()) {
            EditorBaseState topEditorState = mEditorStateStack.peek();
            EditorBaseState tempState = topEditorState;
            /**
             * 先通知当前state即将推出，预览区先执行150ms动画，然后开始执行当前二级页的淡出动画。
             * 淡出动画执行结束之后再执行一级页的淡入动画。
             */
            if (mContext instanceof EditorStateContext) {
                ((EditorStateContext) mContext).onStateDestroy();
            }
            mIStateChangeLifecycle.resumeState(topEditorState, () -> {
                if ((mMenuState != null) && tempState.isPreview()) {
                    mMenuState.resume(false);
                }
                tempState.resume(false);
                return null;
            });
            return topEditorState;
        } else {
            if (mMenuState != null) {
                mMenuState.resume(false);
            }
            return null;
        }
    }

    public final void destroy() {

        if (mMenuState != null) {
            mMenuState.destroy();
            mMenuState = null;
        }
        while (!mEditorStateStack.isEmpty()) {
            EditorBaseState state = mEditorStateStack.pop();
            state.destroy();
        }
    }

    @Override
    public void onActionItemClick(View view) {
        if (DoubleClickUtils.isFastDoubleClick()) {
            GLog.d(TAG, LogFlag.DL, "[onActionItemClick]:isFastDoubleClick. ");
            return;
        }
        EditorBaseState state = getTopEditorState();
        GLog.d(TAG, "onActionItemClick, state = " + state + ", view = " + view);
        int id = view.getId();
        if (id == R.id.editor_id_action_cancel) {
            GLog.d(TAG, "onActionItemClick.editor_id_action_cancel, state = " + state);
            if (state != null) {
                state.clickCancel();
            }
        } else if (id == R.id.editor_id_action_done) {
            GLog.d(TAG, "onActionItemClick.editor_id_action_done, state = " + state);
            if (state != null) {
                state.clickDone();
            }
        }
    }

    private EditorBaseState getTopEditorState() {
        if (mEditorStateStack.isEmpty()) {
            return null;
        }
        return mEditorStateStack.peek();
    }

    public void setAllStateTabId(String tabId) {
        if (mEditorStateStack.isEmpty()) {
            return;
        }
        for (EditorBaseState baseState : mEditorStateStack) {
            baseState.setTabId(tabId);
        }
    }

    public boolean onBackPressed() {
        GLog.d(TAG, "onBackPressed" + ", mEditorStateStack = " + mEditorStateStack);
        if (!mEditorStateStack.isEmpty()) {
            EditorBaseState topState = getTopEditorState();
            if (topState == null) {
                GLog.w(TAG, "onBackPressed topState is null, mEditorStateStack = " + mEditorStateStack);
                return false;
            }
            if (!topState.onBackPressed()) {
                topState.destroy();
                mEditorStateStack.pop();
                if (mEditorStateStack.isEmpty()) {
                    return false;
                } else {
                    topState = mEditorStateStack.peek();
                    topState.resume(false);
                    return true;
                }
            } else {
                return !topState.isPreview();
            }
        }
        return false;
    }

    public void onAppUiConfigChange(AppUiResponder.AppUiConfig uiconfig) {
        GLog.d(TAG, LogFlag.DL, "[onAppUiConfigChange] config is changed");
        // 一级菜单是一直可见的，故一直要通知UI变化
        if (mMenuState != null) {
            mMenuState.onAppUiStateChanged(uiconfig);
        }
        EditorBaseState currentState = getCurrentEditorState();
        if (currentState != mMenuState && currentState != null) {
            currentState.onAppUiStateChanged(uiconfig);
        }
    }

    public EditorBaseState getBeforeSate() {
        return mBeforeSate;
    }

    public void setOnStateChangeListener(OnMonitorStateChangeForShowMaterialSelectListener listener) {
        mMonitorStateChangeListener = listener;
    }

    public void selectMenuItem(@IdRes int id, Object routeParams) {
        if (mMenuState != null) {
            mMenuState.selectItem(id, routeParams);
        }
    }

    /**
     * 直接finish 当前 state 及 activity，用于非 menuState 进入，而是直接加载定制 state 的需要销毁 activity 的场景
     *
     * @param editorState
     */
    public void finishActivity(EditorBaseState editorState) {
        GLog.d(TAG, "finish, state = " + editorState);
        if (editorState == null) {
            GLog.w(TAG, "finish, null editor state!");
            return;
        }
        if (mContext instanceof Activity) {
            ((Activity) mContext).finish();
        }
        editorState.pause(false);
        editorState.destroy();
    }

    public void setEditorStateResultListener(OnStateResultListener listener) {
        mOnStateResultListener = listener;
    }

    public void onStateResultChange(EditorBaseState editorState, Intent intent) {
        if (mOnStateResultListener != null) {
            mOnStateResultListener.onStateResult(editorState, intent);
        }
    }

    public interface OnMonitorStateChangeForShowMaterialSelectListener {
        void onFinishStateForShowMaterialSelect(EditorBaseState editorState, @Nullable EditorBaseState topState);

        void onCreateStateForShowMaterialSelect(EditorBaseState editorState);
    }

    /**
     * 将 state 结果回调给 editorActivity
     */
    public interface OnStateResultListener {
        void onStateResult(EditorBaseState editorState, Intent intent);
    }
}