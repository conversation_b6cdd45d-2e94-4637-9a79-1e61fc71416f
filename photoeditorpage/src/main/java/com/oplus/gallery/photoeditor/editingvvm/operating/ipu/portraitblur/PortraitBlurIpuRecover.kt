/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PortraitBlurIpuRecover
 ** Description: PortraitBlurIpuRecover ipu景深还原
 **
 ** Version: 1.0
 ** Date: 2025/05/27
 ** Author: 80348695
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80348695                          2025/05/27  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating.ipu.portraitblur

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.util.Size
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_FRONT_DEPTH_CONFIG
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_REAR_DEPTH_CONFIG
import com.oplus.gallery.foundation.codec.extend.portraitblur.PortraitBlurDepthData
import com.oplus.gallery.foundation.codec.extend.portraitblur.PortraitBlurDepthStruct
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAITBLUR
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.ipu.IpuRecover
import com.oplus.gallery.photoeditor.editingvvm.operating.ipu.IpuRecoverType
import com.oplus.gallery.photoeditor.editingvvm.portraitblur.PortraitBlurRecord

/**
 * ipu景深还原
 */
internal class PortraitBlurIpuRecover : IpuRecover {

    /**
     * 解析出的景深图片信息
     */
    private var portraitBlurDepthData: PortraitBlurDepthData? = null

    /**
     * 编辑vm
     */
    private var editingVM: EditingVM? = null

    /**
     * 是否是预览模式
     */
    private var isPreview: Boolean = false

    override val type: IpuRecoverType = IpuRecoverType.PORTRAIT_BLUR

    override fun initFileExtendInfo(context: Context, viewModel: EditingVM, dataSource: DataSource, mediaItem: MediaItem) {
        editingVM = viewModel
        val sessionProxy = editingVM?.sessionProxy ?: return
        // 人像图才解析人像信息，人像 + 滤镜 + heic 后置为非人像图，但是相机写入了后置人像脏数据信息，造成还原出错
        portraitBlurDepthData = sessionProxy.takeIf {
            it.matchesTagFlag(OplusExifTag.EXIF_TAG_PORTRAIT_BLUR)
        }?.let {
            it.getMetadataStruct<PortraitBlurDepthStruct<*>>(MetadataType.EXTENDED, EXTEND_KEY_FRONT_DEPTH_CONFIG)
                ?: it.getMetadataStruct<PortraitBlurDepthStruct<*>>(MetadataType.EXTENDED, EXTEND_KEY_REAR_DEPTH_CONFIG)
        }?.toData()
    }

    override fun getSrcBitmap(context: Context, size: Size, mediaItem: MediaItem?, uri: Uri?, originalBitmap: Bitmap?): Bitmap? {
        return originalBitmap
    }

    override fun createRecord(): OperatingRecord? {
        val photoInfo = portraitBlurDepthData?.getOrLog("portraitBlurPhotoInfo") ?: return null
        val blurStrength = photoInfo.blurStrengthParam?.getOrLog("blurStrength") ?: return null
        val aperture = photoInfo.fNumber?.getOrLog("aperture") ?: return null
        // 大图直接进入景深编辑需要设置为预览模式
        if (editingVM?.viewModelBus?.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID) == R.id.strategy_specific_portrait_blur) { isPreview = true }
        // 效果图片是否是ultraHDR
        val effectImageIsUltraHdr = editingVM?.sessionProxy?.isUltraHdrImage() ?: false
        return PortraitBlurRecord(
            isPreview = isPreview,
            shouldCachedForUser = true,
            aperture = aperture,
            blurStrength = blurStrength,
            effectImageIsUltraHdr = effectImageIsUltraHdr
        )
    }

    override fun isFeatureSupportIpu(): Boolean {
        // feature功能支持 且是相同机型的照片
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PORTRAITBLUR, false) && editingVM?.sessionProxy?.matchesModel() == true
    }

    override fun isPhotoSupportIpu(): Boolean { return portraitBlurDepthData != null }

    override fun isIpuHdrSupport(context: Context): Boolean { return true }

    override fun release() {}
}