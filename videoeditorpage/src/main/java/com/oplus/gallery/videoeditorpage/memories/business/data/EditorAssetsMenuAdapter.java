/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorAssetsMenuAdapter.java
 * Description:
 * Version: 1.0
 * Date: 2021/5/10
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>   2021/5/10          1.0         OPLUS_FEATURE_APP_LOG_MONITOR
 </desc></version></date></author> */
package com.oplus.gallery.videoeditorpage.memories.business.data;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuAdapter;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.imageloader.ImageIconLoader;
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item;

import java.util.List;

public class EditorAssetsMenuAdapter<T extends EditorMenuItemViewData> extends EditorMenuAdapter<T> {

    private int mLayoutId = 0;

    public EditorAssetsMenuAdapter(Context context, List<T> data, int layoutId) {
        super(context, data);
        mLayoutId = layoutId;
    }

    @Override
    public int getItemLayoutId(int viewType) {
        return (mLayoutId == 0) ? R.layout.videoeditor_editor_menu_assets_item_layout : mLayoutId;
    }


    private void bindNameText(TextView nameTextView, EditorMenuItemViewData item) {
        if ((item.getName() != null) && !item.getName().isEmpty()) {
            nameTextView.setText(item.getName());
        } else if (item.getTextId() != 0) {
            nameTextView.setText(item.getTextId());
        } else {
            nameTextView.setText(item.getText());
        }
        int heightId = R.dimen.videoeditor_menu_item_text_height;
        if (getOrientation() == LinearLayout.VERTICAL) {
            heightId = R.dimen.videoeditor_menu_item_text_height_landscape;
        }
        ViewGroup.LayoutParams layoutParams = nameTextView.getLayoutParams();
        layoutParams.height = mContext.getResources().getDimensionPixelOffset(heightId);
        nameTextView.setLayoutParams(layoutParams);
    }

    private void bindMenuItemView(EditorMenuItemView menuItemView, EditorMenuItemViewData item, int position) {
        menuItemView.setNormalDrawFlag(EditorMenuItemView.DRAW_BACKGROUND_COLOR
                | EditorMenuItemView.DRAW_IMAGE
                | EditorMenuItemView.DRAW_CENTER_ICON
                | EditorMenuItemView.DRAW_BORDER
                | EditorMenuItemView.DRAW_FOREGROUND_COLOR);
        menuItemView.setSelectedDrawFlag(EditorMenuItemView.DRAW_BACKGROUND_COLOR
                | EditorMenuItemView.DRAW_IMAGE
                | EditorMenuItemView.DRAW_CENTER_ICON
                | EditorMenuItemView.DRAW_CENTER_TEXT
                | EditorMenuItemView.DRAW_BORDER
                | EditorMenuItemView.DRAW_OUT_BORDER
                | EditorMenuItemView.DRAW_FOREGROUND_COLOR);

        Bitmap icon = item.getImage();
        if (icon != null) {
            menuItemView.setImageBitmap(icon, null);
        }
        if (item.getIconResId() != Resources.ID_NULL) {
            menuItemView.setIconResource(item.getIconResId());
        }
        menuItemView.setDrawBackgroundColor(true);
        menuItemView.setDrawForegroundColor(true);
        menuItemView.setCenterText(item.getCenterText());
        menuItemView.setTextColor(item.getTextColor());
    }

    @Override
    public void bindData(BaseRecycleViewHolder viewHolder, int position, EditorMenuItemViewData item) {
        ViewGroup viewGroup = (ViewGroup) viewHolder.itemView;
        if (item.getViewId() == -1) {
            viewGroup.setId(View.generateViewId());
        } else {
            viewGroup.setId(item.getViewId());
        }

        TextView nameTextView = getNameTextView(viewHolder);
        bindNameText(nameTextView, item);

        EditorMenuItemView menuItemView = getMenuItemView(viewHolder);
        bindMenuItemView(menuItemView, item, position);

        if (item instanceof Item) {
            ImageIconLoader.getInstance().startLoader(item, menuItemView);
        }

        menuItemView.setDrawForegroundColor(false);
    }

    public TextView getNameTextView(BaseRecycleViewHolder viewHolder) {
        return (TextView) viewHolder.findViewById(R.id.id_editorMenuItem_item_name);
    }

    public EditorMenuItemView getMenuItemView(BaseRecycleViewHolder viewHolder) {
        return (EditorMenuItemView) viewHolder.findViewById(R.id.id_editorMenuItem_item_icon);
    }

}
