<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="148dp"
    android:height="148dp"
    android:viewportWidth="148"
    android:viewportHeight="148">
  <group>
    <clip-path
        android:pathData="M0,0h148v148h-148z"/>
    <path
        android:pathData="M0,0h148v148h-148z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="27"
            android:startY="-17"
            android:endX="123"
            android:endY="159"
            android:type="linear">
          <item android:offset="0" android:color="#FF282929"/>
          <item android:offset="1" android:color="#FF252627"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M172.13,11.96L43.84,11.96L43.84,11.16L172.13,11.16L172.13,11.96Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="43.84"
            android:startY="12.46"
            android:endX="172.13"
            android:endY="12.46"
            android:type="linear">
          <item android:offset="0" android:color="#1936393B"/>
          <item android:offset="0.47" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M177.05,61.2H-20V60.4H177.05V61.2Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="15.72"
            android:startY="61.2"
            android:endX="140.89"
            android:endY="61.2"
            android:type="linear">
          <item android:offset="0" android:color="#1936393B"/>
          <item android:offset="0.44" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#1936393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M79.28,36.47H25.11V35.47H79.28V36.47Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="25.11"
            android:startY="36.47"
            android:endX="79.28"
            android:endY="36.47"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.55" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M152.39,35.29L128.71,35.29L128.71,36.29L152.39,36.29L152.39,35.29Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="128.72"
            android:startY="35.79"
            android:endX="152.39"
            android:endY="35.79"
            android:type="linear">
          <item android:offset="0.05" android:color="#FF36393B"/>
          <item android:offset="0.83" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M79.1,110.94L79.1,-11.63L79.9,-11.63L79.9,110.94L79.1,110.94Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="79.1"
            android:startY="0.12"
            android:endX="79.1"
            android:endY="89.42"
            android:type="linear">
          <item android:offset="0" android:color="#1936393B"/>
          <item android:offset="0.49" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#1936393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M103.72,110.94L103.72,60.96L104.33,60.96L104.33,110.94L103.72,110.94ZM103.72,11.53L103.72,-11.63L104.33,-11.63L104.33,11.53L103.72,11.53Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="104.03"
            android:startY="-0.23"
            android:endX="103.53"
            android:endY="110.94"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.16" android:color="#FF36393B"/>
          <item android:offset="0.65" android:color="#FF36393B"/>
          <item android:offset="0.72" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M128.71,-11.27L128.71,-11.63L129.32,-11.63L129.32,-11.27L128.71,-11.27ZM128.71,-9.81L128.71,-10.54L129.32,-10.54L129.32,-9.81L128.71,-9.81ZM128.71,-8.35L128.71,-9.08L129.32,-9.08L129.32,-8.35L128.71,-8.35ZM128.71,-6.89L128.71,-7.62L129.32,-7.62L129.32,-6.89L128.71,-6.89ZM128.71,-5.43L128.71,-6.16L129.32,-6.16L129.32,-5.43L128.71,-5.43ZM128.71,-3.97L128.71,-4.7L129.32,-4.7L129.32,-3.97L128.71,-3.97ZM128.71,-2.51L128.71,-3.24L129.32,-3.24L129.32,-2.51L128.71,-2.51ZM128.71,-1.05L128.71,-1.78L129.32,-1.78L129.32,-1.05L128.71,-1.05ZM128.71,0.41L128.71,-0.32L129.32,-0.32L129.32,0.41L128.71,0.41ZM128.71,1.86L128.71,1.13L129.32,1.13L129.32,1.86L128.71,1.86ZM128.71,3.32L128.71,2.59L129.32,2.59L129.32,3.32L128.71,3.32ZM128.71,4.78L128.71,4.05L129.32,4.05L129.32,4.78L128.71,4.78ZM128.71,6.24L128.71,5.51L129.32,5.51L129.32,6.24L128.71,6.24ZM128.71,7.7L128.71,6.97L129.32,6.97L129.32,7.7L128.71,7.7ZM128.71,9.16L128.71,8.43L129.32,8.43L129.32,9.16L128.71,9.16ZM128.71,10.62L128.71,9.89L129.32,9.89L129.32,10.62L128.71,10.62ZM128.71,12.08L128.71,11.35L129.32,11.35L129.32,12.08L128.71,12.08ZM128.71,13.54L128.71,12.81L129.32,12.81L129.32,13.54L128.71,13.54ZM128.71,15L128.71,14.27L129.32,14.27L129.32,15L128.71,15ZM128.71,16.46L128.71,15.73L129.32,15.73L129.32,16.46L128.71,16.46ZM128.71,17.92L128.71,17.19L129.32,17.19L129.32,17.92L128.71,17.92ZM128.71,19.37L128.71,18.65L129.32,18.65L129.32,19.37L128.71,19.37ZM128.71,20.83L128.71,20.1L129.32,20.1L129.32,20.83L128.71,20.83ZM128.71,22.29L128.71,21.56L129.32,21.56L129.32,22.29L128.71,22.29ZM128.71,23.75L128.71,23.02L129.32,23.02L129.32,23.75L128.71,23.75ZM128.71,25.21L128.71,24.48L129.32,24.48L129.32,25.21L128.71,25.21ZM128.71,26.67L128.71,25.94L129.32,25.94L129.32,26.67L128.71,26.67ZM128.71,28.13L128.71,27.4L129.32,27.4L129.32,28.13L128.71,28.13ZM128.71,29.59L128.71,28.86L129.32,28.86L129.32,29.59L128.71,29.59ZM128.71,31.05L128.71,30.32L129.32,30.32L129.32,31.05L128.71,31.05ZM128.71,32.51L128.71,31.78L129.32,31.78L129.32,32.51L128.71,32.51ZM128.71,33.97L128.71,33.24L129.32,33.24L129.32,33.97L128.71,33.97ZM128.71,35.43L128.71,34.7L129.32,34.7L129.32,35.43L128.71,35.43ZM128.71,36.88L128.71,36.16L129.32,36.16L129.32,36.88L128.71,36.88ZM128.71,38.34L128.71,37.61L129.32,37.61L129.32,38.34L128.71,38.34ZM128.71,39.8L128.71,39.07L129.32,39.07L129.32,39.8L128.71,39.8ZM128.71,41.26L128.71,40.53L129.32,40.53L129.32,41.26L128.71,41.26ZM128.71,42.72L128.71,41.99L129.32,41.99L129.32,42.72L128.71,42.72ZM128.71,44.18L128.71,43.45L129.32,43.45L129.32,44.18L128.71,44.18ZM128.71,45.64L128.71,44.91L129.32,44.91L129.32,45.64L128.71,45.64ZM128.71,47.1L128.71,46.37L129.32,46.37L129.32,47.1L128.71,47.1ZM128.71,48.56L128.71,47.83L129.32,47.83L129.32,48.56L128.71,48.56ZM128.71,50.02L128.71,49.29L129.32,49.29L129.32,50.02L128.71,50.02ZM128.71,51.48L128.71,50.75L129.32,50.75L129.32,51.48L128.71,51.48ZM128.71,52.94L128.71,52.21L129.32,52.21L129.32,52.94L128.71,52.94ZM128.71,54.39L128.71,53.67L129.32,53.67L129.32,54.39L128.71,54.39ZM128.71,55.85L128.71,55.12L129.32,55.12L129.32,55.85L128.71,55.85ZM128.71,57.31L128.71,56.58L129.32,56.58L129.32,57.31L128.71,57.31ZM128.71,58.77L128.71,58.04L129.32,58.04L129.32,58.77L128.71,58.77ZM128.71,60.23L128.71,59.5L129.32,59.5L129.32,60.23L128.71,60.23ZM128.71,61.69L128.71,60.96L129.32,60.96L129.32,61.69L128.71,61.69ZM128.71,63.15L128.71,62.42L129.32,62.42L129.32,63.15L128.71,63.15ZM128.71,64.61L128.71,63.88L129.32,63.88L129.32,64.61L128.71,64.61ZM128.71,66.07L128.71,65.34L129.32,65.34L129.32,66.07L128.71,66.07ZM128.71,67.53L128.71,66.8L129.32,66.8L129.32,67.53L128.71,67.53ZM128.71,68.99L128.71,68.26L129.32,68.26L129.32,68.99L128.71,68.99ZM128.71,70.45L128.71,69.72L129.32,69.72L129.32,70.45L128.71,70.45ZM128.71,71.91L128.71,71.18L129.32,71.18L129.32,71.91L128.71,71.91ZM128.71,73.36L128.71,72.63L129.32,72.63L129.32,73.36L128.71,73.36ZM128.71,74.82L128.71,74.09L129.32,74.09L129.32,74.82L128.71,74.82ZM128.71,76.28L128.71,75.55L129.32,75.55L129.32,76.28L128.71,76.28ZM128.71,77.74L128.71,77.01L129.32,77.01L129.32,77.74L128.71,77.74ZM128.71,79.2L128.71,78.47L129.32,78.47L129.32,79.2L128.71,79.2ZM128.71,80.66L128.71,79.93L129.32,79.93L129.32,80.66L128.71,80.66ZM128.71,82.12L128.71,81.39L129.32,81.39L129.32,82.12L128.71,82.12ZM128.71,83.58L128.71,82.85L129.32,82.85L129.32,83.58L128.71,83.58ZM128.71,85.04L128.71,84.31L129.32,84.31L129.32,85.04L128.71,85.04ZM128.71,86.5L128.71,85.77L129.32,85.77L129.32,86.5L128.71,86.5ZM128.71,87.96L128.71,87.23L129.32,87.23L129.32,87.96L128.71,87.96ZM128.71,89.42L128.71,88.69L129.32,88.69L129.32,89.42L128.71,89.42ZM128.71,90.87L128.71,90.14L129.32,90.14L129.32,90.87L128.71,90.87ZM128.71,92.33L128.71,91.6L129.32,91.6L129.32,92.33L128.71,92.33ZM128.71,93.79L128.71,93.06L129.32,93.06L129.32,93.79L128.71,93.79ZM128.71,95.25L128.71,94.52L129.32,94.52L129.32,95.25L128.71,95.25ZM128.71,96.71L128.71,95.98L129.32,95.98L129.32,96.71L128.71,96.71ZM128.71,98.17L128.71,97.44L129.32,97.44L129.32,98.17L128.71,98.17ZM128.71,99.63L128.71,98.9L129.32,98.9L129.32,99.63L128.71,99.63ZM128.71,101.09L128.71,100.36L129.32,100.36L129.32,101.09L128.71,101.09ZM128.71,102.55L128.71,101.82L129.32,101.82L129.32,102.55L128.71,102.55ZM128.71,104.01L128.71,103.28L129.32,103.28L129.32,104.01L128.71,104.01ZM128.71,105.47L128.71,104.74L129.32,104.74L129.32,105.47L128.71,105.47ZM128.71,106.93L128.71,106.2L129.32,106.2L129.32,106.93L128.71,106.93ZM128.71,108.39L128.71,107.65L129.32,107.65L129.32,108.39L128.71,108.39ZM128.71,109.84L128.71,109.11L129.32,109.11L129.32,109.84L128.71,109.84ZM128.71,110.94L128.71,110.57L129.32,110.57L129.32,110.94L128.71,110.94Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="128.22"
            android:startY="-11.63"
            android:endX="128.22"
            android:endY="110.94"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.19" android:color="#FF36393B"/>
          <item android:offset="0.63" android:color="#FF36393B"/>
          <item android:offset="0.8" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M29.86,16.58L29.86,16.21L30.46,16.21L30.46,16.58L29.86,16.58ZM29.86,18.05L29.86,17.32L30.46,17.32L30.46,18.05L29.86,18.05ZM29.86,19.53L29.86,18.79L30.46,18.79L30.46,19.53L29.86,19.53ZM29.86,21L29.86,20.27L30.46,20.27L30.46,21L29.86,21ZM29.86,22.48L29.86,21.74L30.46,21.74L30.46,22.48L29.86,22.48ZM29.86,23.95L29.86,23.21L30.46,23.21L30.46,23.95L29.86,23.95ZM29.86,25.43L29.86,24.69L30.46,24.69L30.46,25.43L29.86,25.43ZM29.86,26.9L29.86,26.16L30.46,26.16L30.46,26.9L29.86,26.9ZM29.86,28.38L29.86,27.64L30.46,27.64L30.46,28.38L29.86,28.38ZM29.86,29.85L29.86,29.11L30.46,29.11L30.46,29.85L29.86,29.85ZM29.86,31.33L29.86,30.59L30.46,30.59L30.46,31.33L29.86,31.33ZM29.86,32.8L29.86,32.06L30.46,32.06L30.46,32.8L29.86,32.8ZM29.86,34.28L29.86,33.54L30.46,33.54L30.46,34.28L29.86,34.28ZM29.86,35.75L29.86,35.01L30.46,35.01L30.46,35.75L29.86,35.75ZM29.86,37.22L29.86,36.49L30.46,36.49L30.46,37.22L29.86,37.22ZM29.86,38.7L29.86,37.96L30.46,37.96L30.46,38.7L29.86,38.7ZM29.86,40.17L29.86,39.44L30.46,39.44L30.46,40.17L29.86,40.17ZM29.86,41.65L29.86,40.91L30.46,40.91L30.46,41.65L29.86,41.65ZM29.86,43.12L29.86,42.39L30.46,42.39L30.46,43.12L29.86,43.12ZM29.86,44.6L29.86,43.86L30.46,43.86L30.46,44.6L29.86,44.6ZM29.86,46.07L29.86,45.34L30.46,45.34L30.46,46.07L29.86,46.07ZM29.86,47.55L29.86,46.81L30.46,46.81L30.46,47.55L29.86,47.55ZM29.86,49.02L29.86,48.28L30.46,48.28L30.46,49.02L29.86,49.02ZM29.86,50.5L29.86,49.76L30.46,49.76L30.46,50.5L29.86,50.5ZM29.86,51.97L29.86,51.23L30.46,51.23L30.46,51.97L29.86,51.97ZM29.86,53.45L29.86,52.71L30.46,52.71L30.46,53.45L29.86,53.45ZM29.86,54.92L29.86,54.18L30.46,54.18L30.46,54.92L29.86,54.92ZM29.86,56.4L29.86,55.66L30.46,55.66L30.46,56.4L29.86,56.4ZM29.86,57.87L29.86,57.13L30.46,57.13L30.46,57.87L29.86,57.87ZM29.86,59.35L29.86,58.61L30.46,58.61L30.46,59.35L29.86,59.35ZM29.86,60.82L29.86,60.08L30.46,60.08L30.46,60.82L29.86,60.82ZM29.86,62.29L29.86,61.56L30.46,61.56L30.46,62.29L29.86,62.29ZM29.86,63.77L29.86,63.03L30.46,63.03L30.46,63.77L29.86,63.77ZM29.86,65.24L29.86,64.51L30.46,64.51L30.46,65.24L29.86,65.24ZM29.86,66.72L29.86,65.98L30.46,65.98L30.46,66.72L29.86,66.72ZM29.86,68.19L29.86,67.46L30.46,67.46L30.46,68.19L29.86,68.19ZM29.86,69.67L29.86,68.93L30.46,68.93L30.46,69.67L29.86,69.67ZM29.86,71.14L29.86,70.41L30.46,70.41L30.46,71.14L29.86,71.14ZM29.86,72.62L29.86,71.88L30.46,71.88L30.46,72.62L29.86,72.62ZM29.86,74.09L29.86,73.35L30.46,73.35L30.46,74.09L29.86,74.09ZM29.86,75.57L29.86,74.83L30.46,74.83L30.46,75.57L29.86,75.57ZM29.86,77.04L29.86,76.3L30.46,76.3L30.46,77.04L29.86,77.04ZM29.86,78.52L29.86,77.78L30.46,77.78L30.46,78.52L29.86,78.52ZM29.86,79.99L29.86,79.25L30.46,79.25L30.46,79.99L29.86,79.99ZM29.86,81.47L29.86,80.73L30.46,80.73L30.46,81.47L29.86,81.47ZM29.86,82.94L29.86,82.2L30.46,82.2L30.46,82.94L29.86,82.94ZM29.86,84.41L29.86,83.68L30.46,83.68L30.46,84.41L29.86,84.41ZM29.86,85.52L29.86,85.15L30.46,85.15L30.46,85.52L29.86,85.52Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="29.36"
            android:startY="16.21"
            android:endX="29.36"
            android:endY="85.52"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.19" android:color="#FF36393B"/>
          <item android:offset="0.63" android:color="#FF36393B"/>
          <item android:offset="0.8" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M128.71,60.96L128.71,11.71L129.51,11.71L129.51,60.96L128.71,60.96Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M111.73,30.68L123.97,54.03H99.53L111.73,30.68Z"
        android:fillColor="#2A2C2D"/>
    <path
        android:pathData="M113.07,29.45L108.37,38.5L107.48,38.04L112.19,28.99L113.07,29.45Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="113.07"
            android:startY="29.45"
            android:endX="108.37"
            android:endY="38.5"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.16" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M111.13,28.45L125.43,55.67L124.55,56.13L110.25,28.91L111.13,28.45Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="111.13"
            android:startY="28.45"
            android:endX="125.43"
            android:endY="55.67"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.07" android:color="#FF36393B"/>
          <item android:offset="0.92" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M99.53,22.11L116.13,53.85H83L99.53,22.11Z"
        android:fillColor="#2E3031"/>
    <path
        android:pathData="M126.53,54.03L80.2,54.03L80.2,53.03L126.53,53.03L126.53,54.03Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="126.53"
            android:startY="54.53"
            android:endX="80.2"
            android:endY="54.53"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M98.9,19.51L117.64,55.62L116.75,56.08L98.02,19.97L98.9,19.51Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="98.9"
            android:startY="19.51"
            android:endX="117.64"
            android:endY="55.62"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M99.97,19.87L81.24,55.99L82.12,56.45L100.86,20.33L99.97,19.87Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="99.97"
            android:startY="19.87"
            android:endX="81.24"
            android:endY="55.99"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M116.68,27.49C118.94,27.49 120.78,25.65 120.78,23.39C120.78,21.12 118.94,19.29 116.68,19.29C114.41,19.29 112.58,21.12 112.58,23.39C112.58,25.65 114.41,27.49 116.68,27.49ZM116.68,28.13C119.3,28.13 121.42,26.01 121.42,23.39C121.42,20.77 119.3,18.65 116.68,18.65C114.06,18.65 111.94,20.77 111.94,23.39C111.94,26.01 114.06,28.13 116.68,28.13Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M80.53,11.5C80.53,12.13 80.02,12.64 79.39,12.64C78.76,12.64 78.25,12.13 78.25,11.5C78.25,10.87 78.76,10.36 79.39,10.36C80.02,10.36 80.53,10.87 80.53,11.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M79.39,12.04C79.69,12.04 79.93,11.8 79.93,11.5C79.93,11.2 79.69,10.96 79.39,10.96C79.09,10.96 78.85,11.2 78.85,11.5C78.85,11.8 79.09,12.04 79.39,12.04ZM79.39,12.64C80.02,12.64 80.53,12.13 80.53,11.5C80.53,10.87 80.02,10.36 79.39,10.36C78.76,10.36 78.25,10.87 78.25,11.5C78.25,12.13 78.76,12.64 79.39,12.64Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M80.53,36.2C80.53,36.83 80.02,37.34 79.39,37.34C78.76,37.34 78.25,36.83 78.25,36.2C78.25,35.57 78.76,35.06 79.39,35.06C80.02,35.06 80.53,35.57 80.53,36.2Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M79.39,36.74C79.69,36.74 79.93,36.5 79.93,36.2C79.93,35.9 79.69,35.66 79.39,35.66C79.09,35.66 78.85,35.9 78.85,36.2C78.85,36.5 79.09,36.74 79.39,36.74ZM79.39,37.34C80.02,37.34 80.53,36.83 80.53,36.2C80.53,35.57 80.02,35.06 79.39,35.06C78.76,35.06 78.25,35.57 78.25,36.2C78.25,36.83 78.76,37.34 79.39,37.34Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M80.65,60.9C80.65,61.53 80.14,62.04 79.51,62.04C78.88,62.04 78.37,61.53 78.37,60.9C78.37,60.27 78.88,59.76 79.51,59.76C80.14,59.76 80.65,60.27 80.65,60.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M79.51,61.44C79.81,61.44 80.05,61.2 80.05,60.9C80.05,60.6 79.81,60.36 79.51,60.36C79.21,60.36 78.97,60.6 78.97,60.9C78.97,61.2 79.21,61.44 79.51,61.44ZM79.51,62.04C80.14,62.04 80.65,61.53 80.65,60.9C80.65,60.27 80.14,59.76 79.51,59.76C78.88,59.76 78.37,60.27 78.37,60.9C78.37,61.53 78.88,62.04 79.51,62.04Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M105.23,60.9C105.23,61.53 104.72,62.04 104.09,62.04C103.46,62.04 102.95,61.53 102.95,60.9C102.95,60.27 103.46,59.76 104.09,59.76C104.72,59.76 105.23,60.27 105.23,60.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M104.09,61.44C104.39,61.44 104.63,61.2 104.63,60.9C104.63,60.6 104.39,60.36 104.09,60.36C103.79,60.36 103.55,60.6 103.55,60.9C103.55,61.2 103.79,61.44 104.09,61.44ZM104.09,62.04C104.72,62.04 105.23,61.53 105.23,60.9C105.23,60.27 104.72,59.76 104.09,59.76C103.46,59.76 102.95,60.27 102.95,60.9C102.95,61.53 103.46,62.04 104.09,62.04Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M105.23,11.5C105.23,12.13 104.72,12.64 104.09,12.64C103.46,12.64 102.95,12.13 102.95,11.5C102.95,10.87 103.46,10.36 104.09,10.36C104.72,10.36 105.23,10.87 105.23,11.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M104.09,12.04C104.39,12.04 104.63,11.8 104.63,11.5C104.63,11.2 104.39,10.96 104.09,10.96C103.79,10.96 103.55,11.2 103.55,11.5C103.55,11.8 103.79,12.04 104.09,12.04ZM104.09,12.64C104.72,12.64 105.23,12.13 105.23,11.5C105.23,10.87 104.72,10.36 104.09,10.36C103.46,10.36 102.95,10.87 102.95,11.5C102.95,12.13 103.46,12.64 104.09,12.64Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M130.31,11.5C130.31,12.13 129.8,12.64 129.17,12.64C128.54,12.64 128.03,12.13 128.03,11.5C128.03,10.87 128.54,10.36 129.17,10.36C129.8,10.36 130.31,10.87 130.31,11.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M129.17,12.04C129.47,12.04 129.71,11.8 129.71,11.5C129.71,11.2 129.47,10.96 129.17,10.96C128.88,10.96 128.63,11.2 128.63,11.5C128.63,11.8 128.88,12.04 129.17,12.04ZM129.17,12.64C129.8,12.64 130.31,12.13 130.31,11.5C130.31,10.87 129.8,10.36 129.17,10.36C128.54,10.36 128.03,10.87 128.03,11.5C128.03,12.13 128.54,12.64 129.17,12.64Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M130.27,36.2C130.27,36.83 129.76,37.34 129.13,37.34C128.5,37.34 127.99,36.83 127.99,36.2C127.99,35.57 128.5,35.06 129.13,35.06C129.76,35.06 130.27,35.57 130.27,36.2Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M129.13,36.74C129.43,36.74 129.67,36.5 129.67,36.2C129.67,35.9 129.43,35.66 129.13,35.66C128.83,35.66 128.59,35.9 128.59,36.2C128.59,36.5 128.83,36.74 129.13,36.74ZM129.13,37.34C129.76,37.34 130.27,36.83 130.27,36.2C130.27,35.57 129.76,35.06 129.13,35.06C128.5,35.06 127.99,35.57 127.99,36.2C127.99,36.83 128.5,37.34 129.13,37.34Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M130.27,60.9C130.27,61.53 129.76,62.04 129.13,62.04C128.5,62.04 127.99,61.53 127.99,60.9C127.99,60.27 128.5,59.76 129.13,59.76C129.76,59.76 130.27,60.27 130.27,60.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M129.13,61.44C129.43,61.44 129.67,61.2 129.67,60.9C129.67,60.6 129.43,60.36 129.13,60.36C128.83,60.36 128.59,60.6 128.59,60.9C128.59,61.2 128.83,61.44 129.13,61.44ZM129.13,62.04C129.76,62.04 130.27,61.53 130.27,60.9C130.27,60.27 129.76,59.76 129.13,59.76C128.5,59.76 127.99,60.27 127.99,60.9C127.99,61.53 128.5,62.04 129.13,62.04Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
  </group>
</vector>
