/**************************************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -   EditorMusicTrackBeatUIController
 ** Description: 音乐卡点/音乐节拍
 ** Version: 1.0
 ** Date :   2025/4/23/20/07
 ** Author: <EMAIL> 80412737
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>                      <version>         <desc>
 ** -----------------------------------------------------------------------------------------------
 **  <EMAIL>    2025/4/23/20/07    1.0             音乐卡点
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.music.beat

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.core.content.ContextCompat
import com.google.android.material.shape.MaterialShapeDrawable
import com.google.android.material.shape.ShapeAppearanceModel
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.isNullOrEmpty
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.databinding.VideoeditorMusicTrackMenuBeatLayoutBinding
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper
import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorProcessor
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IAudioClip
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper
import com.oplus.gallery.videoeditorpage.widget.CustomHorizontalScrollView.OnCustomScrollViewListener
import com.oplus.gallery.videoeditorpage.widget.EditMusicBeatView

/**
 * 音乐卡点
 * 从音乐tab 点击卡点调起
 */
class EditorMusicTrackBeatUIController(context: Context, rootView: ViewGroup, state: EditorTrackBaseState<*>) :
    EditorTrackBaseUIController<Any>(context, rootView, state), OnCustomScrollViewListener {

    /**
     *自动卡点title背景色
     */
    private val titleBackground: MaterialShapeDrawable by lazy {
        MaterialShapeDrawable().apply {
            mContext?.let {
                /**
                 * 配置形状外观模型，设置所有角的圆角半径。
                 * 圆角半径通过ViewUtils.dpToPx方法将16dp转换为像素值。
                 */
                val cornerSize = it.resources.getDimensionPixelSize(R.dimen.videoeditor_musicbeat_corner)
                shapeAppearanceModel = ShapeAppearanceModel.builder().setAllCornerSizes(cornerSize.toFloat()).build() // 16dp 转换为像素

                /**
                 * 设置背景颜色为指定的颜色值。
                 * 颜色值通过ContextCompat.getColor方法从资源中获取。
                 */
                fillColor = ColorStateList.valueOf(ThemeHelper.getCouiColorContainerThemeHalftone(context))
            }
        }
    }

    private val titleColor by lazy {
        val colors = intArrayOf(
            // 选中状态的颜色
            ThemeHelper.getCouiColorContainerTheme(context),
            // 未选中状态的颜色
            Color.WHITE
        )
        val states = arrayOf(
            intArrayOf(android.R.attr.state_selected), intArrayOf(-android.R.attr.state_selected)
        )
        ColorStateList(states, colors)
    }

    /**
     * 标题字体大小设置为11.43f，
     */
    private val titleFontSize = mContext.resources.getDimensionPixelSize(R.dimen.text_size_11_43)

    private var beatListener: OnBeatListener? = null

    /**
     * 中线布局变化监听器，当布局变化时候（如分屏、缩小、放大等），同步更新波形布局的内间距，以保证波形的左端与中线对齐
     */
    private val centerLineOnLayoutChangeListener by lazy {
        View.OnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            val horizontalPadding = (left + v.width / 2)
            binding?.run {
                editorBeatSubLinearLayout.setPadding(horizontalPadding, 0, horizontalPadding, 0)
                root.post {
                    setScrollViewScrollTo(editWaveformView.curPoint)
                }
            }
        }
    }

    private var waveFormEditListener: OnWaveFormEditListener? = null
    private var handScrollView = false
    private var isAddPointMode = true
    private var curIAudioClip: IAudioClip? = null
    private var binding: VideoeditorMusicTrackMenuBeatLayoutBinding? = null

    /**
     * 用于存储当前节拍类型的私有变量。
     * 该变量的初始值为 `BEAT_TYPE_CLOSE`，表示默认的节拍类型。
     */
    private var beatType1 = BEAT_TYPE_CLOSE

    init {
        needTranslateAnimate = true
    }

    fun setBeatListner(listner: OnBeatListener?) {
        beatListener = listner
    }

    fun setOnWaveFormEditListener(onWaveFormEditListener: OnWaveFormEditListener?) {
        this.waveFormEditListener = onWaveFormEditListener
    }

    fun setBeatType(beatType: Int, beatArray: ArrayList<Long?>?) {
        beatType1 = beatType
        doChangeTitleSelectState(beatType)
        GLog.d(TAG, LogFlag.DL, String.format("setBeatType,beatType=%d", beatType))
        binding?.editWaveformView?.updateBeatArray(beatArray)
        // 卡点变化时，切换重置按钮状态
        toggleResetButton()
    }

    /**
     * 根据卡点类型 选择底部title选中背景色
     */
    fun doChangeTitleSelectState(beatType: Int) {
        when (beatType) {
            BEAT_TYPE_CLOSE -> {
                resetAddState()
                mBottomActionBar?.let {
                    mBottomActionBar.titleView.isSelected = false
                    titleBackground.fillColor = ColorStateList.valueOf(ContextCompat.getColor(mContext, R.color.videoeditor_color_333333))
                }
            }

            BEAT_TYPE_MELODY, BEAT_TYPE_RHYTHM -> {
                mBottomActionBar?.let {
                    titleBackground.fillColor = ColorStateList.valueOf(ThemeHelper.getCouiColorContainerThemeHalftone(mContext))
                    mBottomActionBar.titleView.isSelected = true
                }
            }

            else -> {}
        }
    }

    /**
     * 初始化波形视图。
     * 该函数根据传入的音频剪辑对象和当前播放位置，初始化并显示波形视图。
     * 如果音频剪辑为空，函数将直接返回并记录错误日志。
     *
     * @param audioClip       音频剪辑对象，包含音频文件路径、入点、出点等信息。
     */
    fun initWaveform(audioClip: IAudioClip?) {
        // 检查音频剪辑是否为空，若为空则记录错误日志并返回
        if (audioClip == null) {
            GLog.e(TAG, LogFlag.DL, "initWaveform: audio clip is null")
            return
        }

        // 设置当前音频剪辑对象
        curIAudioClip = audioClip
        binding?.let {
            // 将波形视图添加到滚动视图的线性布局中，并设置布局的内边距
            // 计算波形视图的宽度，并设置其布局参数
            val len = TrackHelper.getLengthNoScaleByCurrentDuration(audioClip.trimOut - audioClip.trimIn, ContextGetter.context).toInt()
            val params = LinearLayout.LayoutParams(len, RelativeLayout.LayoutParams.MATCH_PARENT)
            it.editWaveformView.layoutParams = params
            it.editWaveformView.updateBeatArray(audioClip.curUsedBeatArray)
            it.editWaveformView.inPoint = audioClip.inPoint
            it.editWaveformView.outPoint = audioClip.outPoint
            it.editWaveformView.setAudioFilePath(audioClip.filePath, audioClip.trimOut)
            it.editWaveformView.trimIn = audioClip.trimIn
            it.editWaveformView.trimOut = audioClip.trimOut
            it.editWaveformView.singleChannelMode = true
            it.editorTimelineCenterLine.addOnLayoutChangeListener(centerLineOnLayoutChangeListener)
        }
        // 片段赋值后，需刷新下重置按钮状态
        toggleResetButton()
        // 将播放器定位到音频片段的时长范围内的位置，超过片段范围的要修正到片段首尾端位置
        val correctedPosition = mEditorState.editorEngine.timelineCurrentPosition.coerceIn(audioClip.inPoint, audioClip.outPoint)
        mEditorState.editorEngine.seekTo(correctedPosition, 0)
        // 设置初始滚动位置，与当前播放位置保持一致
        mRootView.post {
            setScrollViewScrollTo(correctedPosition)
        }
    }

    override fun createView() {
        super.createView()
        binding = VideoeditorMusicTrackMenuBeatLayoutBinding.bind(mContentView)
        binding?.editorMusicBeatWaveformView?.setOnCustomScrollViewListener(this)
        val context = mContext ?: return // 空值检查
        val resources = context.resources ?: return // 空值检查
        mBottomActionBar.titleView.apply {
            val layoutParams = layoutParams as RelativeLayout.LayoutParams
            layoutParams.height = resources.getDimensionPixelSize(R.dimen.dp_32)
            layoutParams.width = resources.getDimensionPixelSize(R.dimen.dp_66)
            background = titleBackground
            setTextColor(titleColor)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, titleFontSize.toFloat())
        }
        initListner()
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return R.layout.videoeditor_music_track_menu_beat_layout
    }

    override fun getContainerId(): Int {
        return R.id.overlap_container
    }

    override fun onCustomScrollChanged(o: Any, x: Int, oldX: Int) {
        GLog.e(TAG, LogFlag.DL, "[onCustomScrollChanged]: x= $x")
        // 由于刻度内容收尾端有选中点半径长度的偏移，所以这里进行修正，以限制滚动范围在刻度内容范围内
        val correctedX = binding?.run {
            val offset = Math.round(editWaveformView.selectBeatDiameter / MathUtils.TWO_F)
            x.coerceIn(offset, ((editWaveformView.width - offset).takeIf { it > 0 } ?: x)).let {
                // 若不等于x，说明滚动位置被修正了，需要滚动到收尾端修正后的位置
                if (it != x) {
                    editorMusicBeatWaveformView.scrollTo(it, 0)
                }
                // 修正位置减去首端的偏移量得到的滚动长度，才是正确的可用于下面转成时间线时长的长度
                if (ResourceUtils.isRTL(mContext)) {
                    (editWaveformView.width - it - offset)
                } else {
                    (it - offset)
                }
            }
        } ?: x

        curIAudioClip?.let {
            if (handScrollView) {
                val innerPostion = TrackHelper.getDurationNoScaleByCurrentLength(correctedX.toLong(), ContextGetter.context)
                val target = innerPostion + it.inPoint
                updateCurPoint(target)
                waveFormEditListener?.onWaveFormScrollViewScroll(target)
                GLog.d(TAG, LogFlag.DL, "[onCustomScrollChanged]: innerPostion= $innerPostion , target= $target")
            }
        } ?: run {
            GLog.e(TAG, LogFlag.DL, "[onCustomScrollChanged]: audio clip is null")
        }
    }

    private fun selectBeatType(type: Int, clearBeat: Boolean = false) {
        beatType1 = type
        beatListener?.onBeatTypeChanged(beatType1, clearBeat)
    }

    fun setIsHandScrollView(isHandScrollView: Boolean) {
        this.handScrollView = isHandScrollView
    }

    fun setScrollViewScrollTo(currentPosition: Long) {
        curIAudioClip?.let {
            GLog.d(TAG, LogFlag.DL, "[setScrollViewScrollTo]: currentPosition =$currentPosition")
            updateCurPoint(currentPosition)
            val len = TrackHelper.getLengthNoScaleByCurrentDuration(currentPosition - it.inPoint, ContextGetter.context).toInt()
            // 由于刻度内容起始点是选中点半径长度的位置，所以需要作为偏移量加在原位置上
            binding?.run {
                val offset = Math.round(editWaveformView.selectBeatDiameter / MathUtils.TWO_F)
                val x = if (ResourceUtils.isRTL(mContext)) {
                    (editWaveformView.width - len - offset)
                } else {
                    (len + offset)
                }
                editorMusicBeatWaveformView.scrollTo(x, 0)
            }
        } ?: run {
            GLog.e(TAG, LogFlag.DL, "setScrollViewScrollTo: audio clip is null")
        }
    }

    /**
     * 更新当前点对应ui状态，如显示添加还是删除
     *
     * @param innerPostion 当前时间线位置
     */
    private fun updateCurPoint(innerPostion: Long) {
        binding?.let {
            it.editWaveformView.setCurPoint(innerPostion)
            val curAudioClip = curIAudioClip ?: return
            GLog.d(TAG, LogFlag.DL, "initWaveform: beat=${curAudioClip.curUsedBeatArray} ")
            resetAddState()
            curAudioClip.curUsedBeatArray.getOrLog(TAG, "[updateCurPoint], curUsedBeatArray is null")?.let { beatArray ->
                // 遍历节拍数组，以确定当前操作的节拍位置
                for (pos in beatArray) {
                    // 如果节拍位置超出修剪终点，则结束循环
                    if (pos > curAudioClip.trimOut) {
                        return
                    }
                    // 定义节拍的上下界限，用于判断是否在范围内
                    val lowerBound = pos - EditMusicBeatView.HALF_MIN_BEAT_LEN
                    val upperBound = pos + EditMusicBeatView.HALF_MIN_BEAT_LEN
                    // 判断当前操作的位置是否在节拍的范围内，所有节拍点的相对起始点为0，所以这里需要用时间线点减去片段入口点得到当前时间线位置对应的节拍点位置，再做判断
                    val isWithinRange = (innerPostion - curAudioClip.inPoint + curAudioClip.trimIn) in lowerBound..upperBound
                    // 如果在范围内，则更新界面元素状态并退出循环
                    if (isWithinRange) {
                        it.editorAddPointTxt.text = mContext.getString(R.string.videoeditor_delete)
                        it.editorAddPoint.setImageResource(R.drawable.ic_beat_delete_point_svg)
                        isAddPointMode = false
                        return
                    }
                }
            }
        }
    }

    /**
     * 重置添加状态，将点按钮图标设置为添加图标，并将点文本设置为“添加”，同时将添加点模式设置为 true。
     */
    private fun resetAddState() {
        binding?.let {
            it.editorAddPoint.setImageResource(R.drawable.ic_beat_add_point_svg)
            it.editorAddPointTxt.text = mContext.getString(R.string.videoeditor_add)
        }
        isAddPointMode = true
    }


    private fun initListner() {

        binding?.ivBeatOperationBg?.setOnClickListener {
            beatListener?.let { listener ->
                curIAudioClip.getOrLog(TAG, "[initListner] audio clip is null")
                curIAudioClip?.let {
                    val curTimelinePosition = mEditorState.editorEngine.timelineCurrentPosition
                    val start = it.inPoint
                    val end = it.outPoint
                    if (curTimelinePosition !in start..end) {
                        GLog.e(TAG, LogFlag.DL, "[setScrollViewScrollTo]: not in range")
                        return@let
                    }
                    if (isAddPointMode) {
                        listener.onAddPoint(curTimelinePosition)
                    } else {
                        listener.onDelPoint(curTimelinePosition)
                    }
                    GLog.d(TAG, LogFlag.DL, "[setScrollViewScrollTo]: it= $it, $curTimelinePosition")
                    updateCurPoint(curTimelinePosition)
                }
            }
        }

        binding?.editorMusicBeatWaveformView?.let { waveformView ->
            waveformView.setOnTouchListener { _: View?, event: MotionEvent ->
                handScrollView = true
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> waveformView.fling(0)
                    else -> Unit
                }
                false
            }
        }

        mBottomActionBar.titleView.setOnClickListener {
            val beatTyp = if (mBottomActionBar.titleView.isSelected) {
                BEAT_TYPE_CLOSE
            } else {
                BEAT_TYPE_RHYTHM
            }
            selectBeatType(beatTyp)
        }
        mResetButton?.run {
            setOnClickListener {
                selectBeatType(BEAT_TYPE_CLOSE, true)
            }
            // 默认置灰重置按钮
            isEnabled = false
        }
    }

    /**
     * 根据卡点信息判断启用/禁用重置按钮，进卡点页面或添加/删除卡点等引起卡点信息变化的操作时调用
     */
    private fun toggleResetButton() {
        mResetButton?.isEnabled = curIAudioClip?.curUsedBeatArray.isNullOrEmpty().not()
    }


    override fun getTitleId(): Int {
        return R.string.videoeditor_music_auto_beat
    }

    override fun createEnterAnim(): Boolean {
        mResetButton?.let {
            AnimatorProcessor.createEnterAnim(it, false, true)
            return true
        } ?: return false
    }

    /**
     * 判断是否有重置功能
     *
     * 此函数用于指示当前对象或系统是否具有重置功能始终返回true，表示支持重置操作
     *
     * @return Boolean 表示是否具有重置功能，此处始终返回true
     */
    override fun hasReset() = true

    override fun destroyView() {
        super.destroyView()
        binding?.editorTimelineCenterLine?.removeOnLayoutChangeListener(centerLineOnLayoutChangeListener)
    }

    interface OnBeatListener {
        fun onAddPoint(curTimelinePosition: Long)

        fun onDelPoint(curTimelinePosition: Long)

        fun onBeatTypeChanged(beatType: Int, clearBeat: Boolean = false)
    }

    interface OnWaveFormEditListener {
        fun onWaveFormScrollViewScroll(targetTimestamp: Long)
    }

    companion object {
        private const val TAG = "EditorMusicTrackBeatUIController"

        //关闭自动节拍
        const val BEAT_TYPE_CLOSE: Int = 0

        //节奏
        const val BEAT_TYPE_RHYTHM: Int = 1

        //节拍
        const val BEAT_TYPE_MELODY: Int = 2
    }
}
