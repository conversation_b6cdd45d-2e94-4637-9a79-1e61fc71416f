/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditorSubCaptionUIController.kt
 ** Description : 字幕UI二级页面控制器
 ** Version     : 1.0
 ** Date        : 2025/6/4 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/4  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption

import android.content.Context
import android.content.res.Resources
import android.view.ViewGroup
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController

/**
 * 字幕UI二级页面控制器
 *
 * @param context           上下文
 * @param rootView          编辑控制视图
 * @param state             编辑视图状态
 */
class EditorSubCaptionUIController(
    private val context: Context,
    rootView: ViewGroup,
    state: EditorSubCaptionState,
) : EditorTrackBaseUIController<Any>(
    context,
    rootView,
    state,
) {

    override fun getBottomBarLayoutId(): Int {
        return Resources.ID_NULL
    }

    override fun getContainerId(): Int {
        return R.id.overlap_container
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return R.layout.videoeditor_caption_subpage_layout
    }
}