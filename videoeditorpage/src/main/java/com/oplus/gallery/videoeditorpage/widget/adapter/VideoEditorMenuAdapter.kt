/*********************************************************************************
 ** Copyright (C), 2019-2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorMenuAdapter.kt
 ** Description: 视频编辑的扩展EditorMenuAdapter
 **
 ** Version: 1.0.0
 ** Date: 2025/7/12
 ** Author: yeguangjin@OppoGallery3D
 ** TAG:[功能标签]
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yeguangjin@OppoGallery3D         2025/7/12      1.0.0        初始创建
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.adapter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.oplus.gallery.basebiz.widget.EditorMenuItemView
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuAdapter
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.videoeditorpage.R

open class VideoEditorMenuAdapter(
    context: Context,
    data: List<EditorMenuItemViewData>
) : EditorMenuAdapter<EditorMenuItemViewData>(context, data) {

    override fun getNameTextView(viewHolder: BaseRecycleViewHolder?): TextView? {
        val textView = viewHolder?.findViewById(R.id.id_editorMenuItem_item_name) as? TextView?
        textView?.isForceDarkAllowed = false
        return textView
    }

    override fun getMenuItemView(viewHolder: BaseRecycleViewHolder?): EditorMenuItemView? {
        return viewHolder?.findViewById(R.id.id_editorMenuItem_item_icon)
    }

    override fun getAnimItemView(itemView: View): View? {
        return if (itemView is ViewGroup) {
            itemView.findViewById(R.id.id_editorMenuItem_item_icon) ?: itemView
        } else itemView
    }
}