/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RotatableGifDrawable.kt
 ** Description:
 *    支持旋转的[GifDrawable]
 **
 ** Version: 1.0
 ** Date: 2021/09/28
 ** Author: yaoweihe
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** yaoweihe                     2022/08/12        1.0
 *********************************************************************************/
@file:Suppress("SpacingAroundParens")
package com.oplus.gallery.standard_lib.codec.glide.load.resource.gif

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.util.Size
import com.oplus.gallery.standard_lib.codec.glide.gifdecoder.GifDecoder
import com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.BitmapPool
import kotlin.math.max

/**
 * Marked by yaoweihe，后续替换封装方式，用Drawable包裹[GifDrawable]
 */
@Suppress("LongParameterList")
class RotatableGifDrawable(
    context: Context,
    gifDecoder: GifDecoder,
    bitmapPool: BitmapPool,
    targetFrameWidth: Int,
    targetFrameHeight: Int,
    rotation: Int,
    firstFrame: Bitmap
) : GifDrawable(
    context,
    gifDecoder,
    bitmapPool,
    targetFrameWidth,
    targetFrameHeight,
    firstFrame
) {
    /**
     * 设置旋转角度
     */
    var rotation: Int = rotation
        set(value) {
            if (field == value) return
            field = value
            invalidateSelf()
        }

    private val bitmapSize: Size by lazy {
        Size((constantState as GifState).frameLoader.width, (constantState as GifState).frameLoader.height)
    }

    private val paint: Paint = Paint(Paint.FILTER_BITMAP_FLAG).apply {
        isAntiAlias = true
    }

    private val drawingRectF: RectF by lazy { RectF(bounds) }

    private val isRotationVertical: Boolean
        get() = (rotation + ANGLE_90) % ANGLE_180 == 0

    override fun getIntrinsicWidth(): Int = if (isRotationVertical) bitmapSize.height else bitmapSize.width

    override fun getIntrinsicHeight(): Int = if (isRotationVertical) bitmapSize.width else bitmapSize.height

    override fun draw(canvas: Canvas) {
        if (isRecycled) {
            return
        }

        val currentFrame: Bitmap = (constantState as GifState).frameLoader.currentFrame

        setupDrawingPaintIfNeeded(currentFrame)

        canvas.drawRect(drawingRectF, paint)
    }

    override fun setBounds(left: Int, top: Int, right: Int, bottom: Int) {
        super.setBounds(left, top, right, bottom)
        drawingRectF.set(bounds)
        invalidateSelf()
    }

    override fun setAlpha(alpha: Int) {
        if (alpha == paint.alpha) {
            return
        }
        paint.alpha = alpha
        invalidateSelf()
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        if (paint.colorFilter == colorFilter) {
            return
        }
        paint.colorFilter = colorFilter
        invalidateSelf()
    }


    private fun setupDrawingPaintIfNeeded(bitmap: Bitmap) {
        val transformData = calculateTransformData(rotation, isRotationVertical, Size(bitmap.width, bitmap.height), bounds)
        val transformedMatrix = Matrix().applyTransform(transformData)
        val bitmapShader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        bitmapShader.setLocalMatrix(transformedMatrix)
        paint.shader = bitmapShader
    }

    private fun calculateTransformData(
        rotation: Int,
        isRotationVertical: Boolean,
        bitmapSize: Size,
        bounds: Rect
    ): TransformData {
        if ((bitmapSize.width <= 0) || (bitmapSize.height <= 0)) {
            return TransformData()
        }
        val boundsWidth = bounds.right
        val boundsHeight = bounds.bottom
        val contentWidth = if (isRotationVertical) bitmapSize.height else bitmapSize.width
        val contentHeight = if (isRotationVertical) bitmapSize.width else bitmapSize.height
        val scale = max(boundsWidth.toFloat() / contentWidth, boundsHeight.toFloat() / contentHeight)
        val translateX: Float
        val translateY: Float
        when (rotation) {
            ANGLE_90 -> {
                translateX = (boundsWidth + contentWidth * scale) / 2f
                translateY = (boundsHeight - contentHeight * scale) / 2f
            }
            ANGLE_180 -> {
                translateX = (boundsWidth + contentWidth * scale) / 2f
                translateY = (boundsHeight + contentHeight * scale) / 2f
            }
            ANGLE_270 -> {
                translateX = (boundsWidth - contentWidth * scale) / 2f
                translateY = (boundsHeight + contentHeight * scale) / 2f
            }
            else -> {
                translateX = (boundsWidth - contentWidth * scale) / 2f
                translateY = (boundsHeight - contentHeight * scale) / 2f
            }
        }
        return TransformData(rotation.toFloat(), scale, translateX, translateY)
    }

    private fun Matrix.applyTransform(transformData: TransformData): Matrix {
        reset()
        setScale(transformData.scale, transformData.scale)
        postRotate(transformData.rotation)
        postTranslate(transformData.translateX, transformData.translateY)
        return this
    }

    private data class TransformData(
        val rotation: Float = 0f,
        val scale: Float = 1f,
        val translateX: Float = 0f,
        val translateY: Float = 0f
    )

    private companion object {
        private const val TAG = "RotatableGifDrawable"
        private const val ANGLE_90 = 90
        private const val ANGLE_180 = 180
        private const val ANGLE_270 = 270
    }
}