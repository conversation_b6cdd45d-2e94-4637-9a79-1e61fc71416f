/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoThumbnailDecoderTest.kt
 ** Description:MeicamVideoThumbnailDecoder的测试类
 ** Version: 1.0
 ** Date : 2022/12/10
 ** Author: xiewu<PERSON><PERSON>@Apps.Gallery3D
 ** TAG: MeicamVideoThumbnailDecoderTest
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewu<PERSON><PERSON>@Apps.Gallery3D      2022/12/10    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.standard_lib.codec.videoframe

import android.graphics.Bitmap
import com.meicam.sdk.NvsVideoFrameRetriever
import com.meicam.sdk.NvsVideoStreamInfo
import io.mockk.InternalPlatformDsl
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MeicamVideoThumbnailDecoderTest {

    private val meicamVideoThumbnailDecoder = spyk(MeicamVideoThumbnailDecoder())
    private lateinit var nvsVideoFrameRetriever: NvsVideoFrameRetriever
    private lateinit var nvVideoStreamInfo: NvsVideoStreamInfo

    private lateinit var bitmap: Bitmap

    @Before
    fun setUp() {
        nvsVideoFrameRetriever = mockk()
        bitmap = mockk()
        nvVideoStreamInfo = NvsVideoStreamInfo().apply {
            imageHeight = 100
            imageWidth = 200
            displayRotation = 0
        }
        InternalPlatformDsl.dynamicSetField(meicamVideoThumbnailDecoder, "nvsRetriever", nvsVideoFrameRetriever)
        every { nvsVideoFrameRetriever.nvVideoStreamInfo } returns nvVideoStreamInfo
        every { nvsVideoFrameRetriever.getFrameAtTimeWithCustomVideoFrameHeight(any(), any()) } returns bitmap
    }

    @Test
    fun should_return_invalid_value_when_retriever_info_is_null() {
        // given
        InternalPlatformDsl.dynamicSetField(meicamVideoThumbnailDecoder, "nvsRetriever", null)
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(100)
        // then
        Assert.assertEquals(result, -1)
    }

    @Test
    fun should_return_invalid_value_when_nvVideoStreamInfo_is_null() {
        // given
        every { nvsVideoFrameRetriever.nvVideoStreamInfo } returns null
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(100)
        // then
        Assert.assertEquals(result, -1)
    }

    @Test
    fun should_return_invalid_value_when_imageHeight_less_than_0() {
        // given
        nvVideoStreamInfo.imageHeight = -1
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(100)
        // then
        Assert.assertEquals(result, -1)
    }

    @Test
    fun should_return_invalid_value_when_imageWidth_is_0() {
        // given
        nvVideoStreamInfo.imageWidth = 0
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(100)
        // then
        Assert.assertEquals(result, -1)
    }

    @Test
    fun should_return_invalid_value_when_imageWidth_is_less_than_0() {
        // given
        nvVideoStreamInfo.imageWidth = -1
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(100)
        // then
        Assert.assertEquals(result, -1)
    }

    @Test
    fun should_return_48_value_when_inputSize_is_50() {
        // given
        val inputSize = 50
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(inputSize)
        // then
        Assert.assertEquals(result, 50)
    }

    @Test
    fun should_return_48_value_when_inputSize_is_52() {
        // given
        val inputSize = 53
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(inputSize)
        // then
        Assert.assertEquals(result, 52)
    }

    @Test
    fun should_return_210_value_when_inputSize_is_105_and_rotation_is_90() {
        // given
        val inputSize = 105
        nvVideoStreamInfo.imageHeight = 100
        nvVideoStreamInfo.imageWidth = 200
        nvVideoStreamInfo.displayRotation = 1
        // when
        val result = meicamVideoThumbnailDecoder.calculateThumbnailHeight(inputSize)
        // then
        Assert.assertEquals(result, 210)
    }
}