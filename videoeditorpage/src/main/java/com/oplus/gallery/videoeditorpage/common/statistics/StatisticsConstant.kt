/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - StatisticsConstant.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.common.statistics

import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine

/**
 * 常量对象
 */
object StatisticsConstant {
    // do not change any value

    /** Null字符串常量 */
    const val NULL: String = "null"

    /** 或字符串常量 */
    const val UNIT_SEPARATOR: String = "||"

    /** 连接符 */
    const val INFO_LINK: String = "_"

    // Boolean

    /** true */
    const val TRUE: String = "true"

    /** false */
    const val FALSE: String = "false"

    /** 音量操作  */
    const val VOLUME: String = "0"

    /** 切割操作  */
    const val CUT: String = "1"

    /** 删除操作  */
    const val DELETE: String = "2"

    /** 节拍操作  */
    const val BEAT: String = "3"

    /** 复制操作  */
    const val COPY: String = "4"

    /** 调整操作  */
    const val ADJUST: String = "5"

    /** 拖拽操作  */
    const val DRAG: String = "6"

    const val KEY_FIRTST_TAB_ID: String = "first_tab_id"
    const val KEY_SECOND_TAB_ID: String = "second_tab_id"

    /** item最短时长 */
    const val MIN_TIME_OF_ITEM: Int = EditorEngine.MIN_CLIP_DURATION.toInt()

    /** 资源大小 */
    const val SOURCE_SIZE: String = "Source Size"

    /** 额外信息 */
    const val KEY_EXTRACT_INFO: String = "extractInfo"

    /** 额外信息捆绑包 */
    const val KEY_EXTRACT_INFO_BUNDLE: String = "extractInfo_bundle"

    /**
     * 排序常量
     */
    object SortClick {
        /** 点击 */
        const val CLICK: String = "click"

        /** 按压 */
        const val PRESS: String = "press"
    }

    /** 片段ID */
    object ClipItemId {
        /** 转场 */
        const val TRANSITION: String = "transition"
    }

    /** 片段操作结果 */
    object ClipOperResult {
        const val COMPLETE: String = "0"
        const val CANCEL: String = "1"
        const val FAILED: String = "2"
    }

    /**
     * 曝光常量
     */
    object Exposure {
        const val ITEM_TYPE: String = "item_type"
        const val ITEM_ID: String = "item_id"
        const val EDIT_TAB: String = "edit_tab"
    }

    /**
     * 片段功能id
     */
    object ClipFunctionId {
        const val SPEED: String = "speed"
        const val ROTATE: String = "rotate"
        const val REVERSE: String = "reverse"
        const val VOLUME: String = "volume"
        const val FILTER: String = "filter"
        const val EFFECT: String = "effect"
        const val MASK: String = "mask"
        const val CUT: String = "cut"
        const val DELETE: String = "delete"
        const val ANIMATION: String = "animation"
        const val KEYING: String = "keying"
        const val REPLACE: String = "replace"
        const val ORDER: String = "order"
        const val CARTOON: String = "cartoon"
        const val CLIP: String = "clip"
        const val ADD: String = "add"
        const val LEVEL: String = "level"
        const val MIX: String = "mix"
        const val TRICKS: String = "tricks"
    }

    /**
     * 操作页
     */
    object OperationTabId {

        const val SUBTITLE = "subtitle"

        const val STICKER = "sticker"
    }

    object MusicFirstTabId {
        const val MUSIC_CATEGORY: String = "music_lib"
        const val MUSIC_IMPORT: String = "import_music"
        const val MUSIC_CONSTANT: String = "pick_music"
    }
}