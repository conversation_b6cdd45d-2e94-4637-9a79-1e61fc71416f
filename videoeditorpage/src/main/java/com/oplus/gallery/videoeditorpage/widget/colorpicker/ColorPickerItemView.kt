/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * File: ColorPickerItemView.kt
 * Description: tang<PERSON>bin created
 * Version: 1.0
 * Date: 2025/4/30
 * Author: tangzhibin
 *
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * tangzhibin      2025/4/30        1.0         NEW
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.colorpicker

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import kotlin.math.min

/**
 * 颜色选择器单项视图
 *
 * markby <EMAIL> 2025/07/3 此类随后重构优化下
 */
class ColorPickerItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 数据
    var itemData: ColorPickerItemData? = null
        set(value) {
            field = value
            invalidate()
        }

    // 颜色画笔
    private val colorPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    // 边框画笔
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        color = borderColor // 使用静态边框颜色
        strokeWidth = borderWidth.toFloat() // 使用静态边框宽度
    }

    // 选中标记画笔
    private val selectedPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        color = selectedBorderColor // 使用静态选中边框颜色
        strokeWidth = selectedBorderWidth.toFloat() // 使用静态选中边框宽度
    }

    private var centerX: Float = 0f
    private var centerY: Float = 0f
    private var radius: Float = 0f

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        centerX = w / 2f
        centerY = h / 2f
        radius = (min(w, h) / 2f) - borderPaint.strokeWidth
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        drawBorder(canvas)
        drawContent(canvas)
    }

    private fun drawBorder(canvas: Canvas) {
        canvas.drawCircle(centerX, centerY, borderRadius - borderPaint.strokeWidth / 2F, borderPaint)
    }

    private fun drawContent(canvas: Canvas) {
        itemData?.let { data ->
            if (data.drawable != null) {
                drawDrawable(canvas, data.drawable)
            } else {
                drawColor(canvas, data.color)
            }

            if (isSelected) {
                drawSelectionMarker(canvas)
            }
        }
    }

    private fun drawDrawable(canvas: Canvas, drawable: Drawable) {
        val halfDrawableW = (drawable.intrinsicWidth / 2f).toInt()
        val halfDrawableH = (drawable.intrinsicHeight / 2f).toInt()
        drawable.setBounds(
            (centerX - halfDrawableW).toInt(),
            (centerY - halfDrawableH).toInt(),
            (centerX + halfDrawableW).toInt(),
            (centerY + halfDrawableH).toInt()
        )
        drawable.draw(canvas)
    }

    private fun drawColor(canvas: Canvas, color: Int) {
        colorPaint.color = color
        canvas.drawCircle(centerX, centerY, radius - borderPaint.strokeWidth, colorPaint)
    }

    private fun drawSelectionMarker(canvas: Canvas) {
        canvas.drawCircle(centerX, centerY, borderRadius - selectedPaint.strokeWidth / 2F, selectedPaint)
    }

    companion object {
        // 默认边框颜色
        private var borderColor: Int = Color.TRANSPARENT

        // 默认选中边框颜色
        private var selectedBorderColor: Int = Color.WHITE

        // 默认边框宽度
        private var borderWidth: Int = 2

        // 默认选中边框宽度
        private var selectedBorderWidth: Int = 2

        // 默认边框外圆半径
        private var borderRadius = 16

        /**
         * 设置边框属性
         */
        fun setBorderAttributes(
            borderColor: Int,
            selectedBorderColor: Int,
            borderWidth: Int,
            selectedBorderWidth: Int,
            borderRadius: Int
        ) {
            this.borderColor = borderColor
            this.selectedBorderColor = selectedBorderColor
            this.borderWidth = borderWidth
            this.selectedBorderWidth = selectedBorderWidth
            this.borderRadius = borderRadius
        }
    }
}
