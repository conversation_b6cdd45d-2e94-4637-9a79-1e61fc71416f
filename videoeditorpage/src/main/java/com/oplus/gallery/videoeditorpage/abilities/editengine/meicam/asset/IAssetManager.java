/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IAssetManager.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.asset;

public interface IAssetManager {

    /**
     * Install asset
     *
     * @param assetPackageFilePath The asset package file path
     * @param licenseFilePath      The license file path
     * @param type                 The asset package type
     */
    void installAsset(String assetPackageFilePath, String licenseFilePath, Integer type, IAssetProcessListener listener);

    /**
     * Uninstall asset
     *
     * @param assetPackageId The asset package Id
     * @param type           The asset package type
     * @return The uninstall result
     */
    int uninstallAsset(String assetPackageId, Integer type);

    String registerFontByFilePath(String fontPath);

    boolean needInstall(String filePath, int type);

    String getIdByPath(String filePath);

    /**
     * Release resource
     */
    void release();
}
