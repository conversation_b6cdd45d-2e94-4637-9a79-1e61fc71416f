/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IVideoFilterManager.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces

import com.oplus.gallery.videoeditorpage.memories.engine.meicam.BaseVideoClipEffect

interface IVideoFilterManager {
    fun getVideoClipFilter(filterName: String?): BaseVideoClipEffect?
}