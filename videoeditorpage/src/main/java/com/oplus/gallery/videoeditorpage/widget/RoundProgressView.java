/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: RoundProgressView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.View;

import com.oplus.gallery.videoeditorpage.R;

public class RoundProgressView extends View {
    private static final String TAG = "RoundProgressView";
    public static final int PROGRESS_TEXT_SIZE_DEF_VALUE = 15;
    public static final int PROGRESS_ROUND_WIDTH_DEF_VALUE = 5;
    public static final int PROGRESS_ROUND_MAX_DEF_VALUE = 100;
    public static final int PERCENT_BASE_VALUE = 100;
    public static final int CIRCLE_ANGLE_VALUE = 360;
    public static final int DRAW_ARC_START_ANGLE = -90;

    private Paint mTextPaint;
    private Paint mRoundPaint;
    private Paint mRoundProgressPaint;
    private int mRoundColor;
    private int mRoundProgressColor;
    private int mTextColor;
    private float mTextSize;
    private float mRoundWidth;
    private int mMax;
    private int mProgress;
    private boolean mTextHide;

    public RoundProgressView(Context context) {
        this(context, null);
    }

    public RoundProgressView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RoundProgressView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.RoundProgressView);
        mRoundColor = typedArray.getColor(R.styleable.RoundProgressView_roundColor, Color.RED);
        mRoundProgressColor = typedArray.getColor(R.styleable.RoundProgressView_roundProgressColor, Color.GREEN);
        mTextColor = typedArray.getColor(R.styleable.RoundProgressView_progressTextColor, Color.GREEN);
        mTextSize = typedArray.getDimension(R.styleable.RoundProgressView_progressTextSize, PROGRESS_TEXT_SIZE_DEF_VALUE);
        mRoundWidth = typedArray.getDimension(R.styleable.RoundProgressView_roundWidth, PROGRESS_ROUND_WIDTH_DEF_VALUE);
        mMax = typedArray.getInteger(R.styleable.RoundProgressView_roundMax, PROGRESS_ROUND_MAX_DEF_VALUE);
        mTextHide = typedArray.getBoolean(R.styleable.RoundProgressView_progressTextHidden, true);
        typedArray.recycle();
        initPaint();
    }

    private void initPaint() {
        mRoundPaint = new Paint();
        mRoundPaint.setColor(mRoundColor);
        mRoundPaint.setStyle(Paint.Style.STROKE);
        mRoundPaint.setStrokeWidth(mRoundWidth);
        mRoundPaint.setAntiAlias(true);

        mTextPaint = new Paint();
        mTextPaint.setStrokeWidth(0);
        mTextPaint.setColor(mTextColor);
        mTextPaint.setTextSize(mTextSize);
        mTextPaint.setAntiAlias(true);
        mTextPaint.setTypeface(Typeface.DEFAULT_BOLD);

        // progress
        mRoundProgressPaint = new Paint();
        mRoundProgressPaint.setStrokeWidth(mRoundWidth);
        mRoundProgressPaint.setColor(mRoundProgressColor);
        mRoundProgressPaint.setAntiAlias(true);
        mRoundProgressPaint.setStyle(Paint.Style.STROKE);
    }

    public void setColorS(int roundColor, int roundProgressColor, int textColor) {
        if (roundColor != -1) {
            mRoundColor = roundColor;
        }
        if (roundProgressColor != -1) {
            mRoundProgressColor = roundProgressColor;
        }
        if (textColor != -1) {
            mTextColor = textColor;
        }
        initPaint();
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // background
        int center = getWidth() / 2;
        int radus = (int) (center - mRoundWidth);
        canvas.drawCircle(center, center, radus, mRoundPaint);

        // progress text
        int percent = (int) (((float) mProgress / (float) mMax) * PERCENT_BASE_VALUE);
        float textWidth = mTextPaint.measureText(percent + "%");
        if (!mTextHide) {
            canvas.drawText(percent + "%", center - textWidth / 2, center + mTextSize / 2, mTextPaint);
        }

        RectF oval = new RectF(center - radus, center - radus, center + radus, center + radus);
        //categor
        canvas.drawArc(oval, DRAW_ARC_START_ANGLE, CIRCLE_ANGLE_VALUE * mProgress / mMax, false, mRoundProgressPaint);
    }

    public int getMax() {
        return mMax;
    }

    public void setMax(int max) {
        if (max < 0) {
            this.mMax = PROGRESS_ROUND_MAX_DEF_VALUE;
        } else {
            this.mMax = max;
        }
    }

    public int getProgress() {
        return mProgress;
    }

    public void setProgress(int progress) {
        if (progress < 0) {
            throw new IllegalArgumentException("progress value less than 0");
        }
        int adjustedProgress = progress;
        if (adjustedProgress > mMax) {
            adjustedProgress = mMax;
        }
        if (adjustedProgress <= mMax) {
            this.mProgress = adjustedProgress;
            postInvalidate();
        }
    }
}
