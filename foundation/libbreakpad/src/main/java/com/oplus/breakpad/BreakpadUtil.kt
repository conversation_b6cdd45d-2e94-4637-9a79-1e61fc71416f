/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BreakpadUtil.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import com.oplus.gallery.foundation.security.CrcUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING

object BreakpadUtil {

    /**
     * 生成breakpad场景key值，用以定位出现native异常的场景
     * @param filePath 文件路径
     * @param dateModifiedInSec 最近修改时间
     * @return breakpad场景保护key值
     */
    @JvmStatic
    fun generateKey(filePath: String?, dateModifiedInSec: Long?): String {
        if (filePath.isNullOrEmpty() || (dateModifiedInSec == null)) return EMPTY_STRING
        val keyInfo: StringBuilder = StringBuilder(filePath)
        keyInfo.append("+${filePath.hashCode()}")
        keyInfo.append("+$dateModifiedInSec")
        val key = CrcUtil.getBytes(keyInfo.toString())
        val finalKey: Long = CrcUtil.crc64Long(key)
        return finalKey.toString()
    }

    /**
     * 生成breakpad场景key值，用以定位出现native异常的场景
     * @param tombstone 遗言信息
     * @return breakpad场景保护key值
     */
    @JvmStatic
    fun generateKey(tombstone: BreakpadTombstone?): String {
        return generateKey(tombstone?.filePath, tombstone?.dateModifiedInSec)
    }
}

data class BreakpadTombstone(val filePath: String?, val dateModifiedInSec: Long? = 0)
