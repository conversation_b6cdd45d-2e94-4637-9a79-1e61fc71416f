/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorTransformController
 ** Description: 80413407 created
 ** Version: 1.0
 ** Date : 2025/4/19
 ** Author: 80413407
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80413407      2025/4/19     1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.content.Context
import android.graphics.Typeface
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.TRACK_INDEX_FIRST
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ScrollMode
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.params.SpeederMode
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.params.SpeederParams
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper.appendMenuClickData
import com.oplus.gallery.videoeditorpage.utlis.TextUtil
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper
import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorProcessor
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.output.OperationSaveHelper
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper.getDurationByCurrentLength
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewWrapper
import com.oplus.gallery.videoeditorpage.video.business.trim.IEffectClipResponder
import com.oplus.gallery.videoeditorpage.widget.BottomActionBar
import com.oplus.gallery.videoeditorpage.widget.ruleview.OnRuleChangedListener
import com.oplus.gallery.videoeditorpage.widget.ruleview.RuleView

/**
 * 负责实现变速页面的UI
 */
class EditorSpeederUIController(
    context: Context,
    rootView: ViewGroup,
    editorSpeederState: EditorSpeederState,
    operationSaveHelper: OperationSaveHelper,
    timelineViewModel: TimelineViewModel,
    private val effectClipResponderList: List<IEffectClipResponder?>? = null
) : EditorTrackBaseUIController<Any>(context, rootView, editorSpeederState, timelineViewModel) {

    private val vm: EditorSpeederVM by lazy {
        val factory = EditorSpeederVMFactory(
            context,
            operationSaveHelper,
            timelineViewModel,
            editorSpeederState.editorEngine,
            changeSpeedCurveTemplateList,
            currentPlayingClip,
            effectClipResponderList
        )
        ViewModelProvider(this, factory)[EditorSpeederVM::class.java]
    }

    /**
     * 变速刻度尺控件
     */
    private val speederRuleView: RuleView by lazy { findContainerChild(R.id.editor_speeder_rule_view) }

    /**
     * 变速结果文本控件
     */
    private val speederResultTextView: TextView by lazy { findContainerChild(R.id.speeder_value_tv) }

    /**
     * 线性速度选项卡的文本视图
     */
    private val speederLinearTabTv: TextView by lazy { findContainerChild(R.id.speeder_linear_tab_tv) }

    /**
     * 曲线速度选项卡的文本视图
     */
    private val speederCurveTabTv: TextView by lazy { findContainerChild(R.id.speeder_curve_tab_tv) }

    /**
     * 曲线速度的RecyclerView
     */
    private val curveSpeederRecyclerView: RecyclerView by lazy { findContainerChild(R.id.speeder_curve_rv) }

    /**
     * 线性速度的容器视图
     */
    private val linearSpeederContainer: View by lazy { findContainerChild(R.id.speeder_container_linear) }

    /**
     * 曲线速度视图的适配器
     */
    private val curveSpeedRecyclerViewAdapter by lazy {
        CurveSpeedRecyclerViewAdapter(mContext, changeSpeedCurveTemplateList)
    }

    /**
     * 曲线变速的容器视图
     */
    private val curveSpeederContainer: View by lazy { findContainerChild(R.id.editor_speeder_curve_container) }

    /**
     * 曲线变速的模板列表，通过CurveSpeedHelper获取
     */
    private val changeSpeedCurveTemplateList: List<SpeederCurveInfo> by lazy { CurveSpeedHelper.getCurveSpeedTemplate(mContext) }

    /**
     * 当前正在播放的视频剪辑对象。
     */
    private var currentPlayingClip: IVideoClip? = getPlayingVideoClip()

    override fun getTitleId(): Int {
        return R.string.videoeditor_change_speed
    }

    override fun getContentLayoutId(appUiConfig: AppUiConfig): Int {
        return R.layout.videoeditor_speeder_menu_layout
    }

    override fun getContainerId(): Int = R.id.menu_container

    /**
     * 设置 TextView 的字体粗细
     */
    private fun setTextViewFontWeight(textView: TextView, fontWeight: String) {
        textView.setTypeface(
            Typeface.create(fontWeight, Typeface.NORMAL)
        )
    }

    override fun createView() {
        super.createView()
        initRuleView()

        speederCurveTabTv.setOnClickListener {
            vm.notifyIsSpeedCurveModeChanged(true)
        }
        speederLinearTabTv.setOnClickListener {
            vm.notifyIsSpeedCurveModeChanged(false)
        }
        initCurveView()
        vm.videoSpeed.observe(this) { videoSpeed ->
            speederRuleView.setCurrentValue(videoSpeed.toFloat())
        }
        vm.currentDisplaySpeed.observe(this) { currentDisplaySpeed ->
            speederResultTextView.text = "${currentDisplaySpeed.toFloat()}${RuleView.GRADATION_UNIT}"
        }
        vm.isSpeedCurveMode.observe(this) { isSpeedCurveMode ->
            //当前为曲线变速模式
            if (isSpeedCurveMode) {
                speederLinearTabTv.setTextColor(mContext.getColor(R.color.videoeditor_speed_tv_unselect_color))
                speederCurveTabTv.setTextColor(ThemeHelper.getCouiColorContainerTheme(mContext))
                curveSpeederContainer.visibility = View.VISIBLE
                linearSpeederContainer.visibility = View.GONE
                setTextViewFontWeight(speederCurveTabTv, FONT_MEDIUM)
                setTextViewFontWeight(speederLinearTabTv, FONT_REGULAR)
                appendMenuClickData(VideoEditorTrackConstant.Value.VIDEO_SPEED_NORMAL, VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL)
            } else {
                //当前为线性变速模式
                speederCurveTabTv.setTextColor(mContext.getColor(R.color.videoeditor_speed_tv_unselect_color))
                speederLinearTabTv.setTextColor(ThemeHelper.getCouiColorContainerTheme(mContext))
                curveSpeederContainer.visibility = View.GONE
                linearSpeederContainer.visibility = View.VISIBLE
                setTextViewFontWeight(speederCurveTabTv, FONT_REGULAR)
                setTextViewFontWeight(speederLinearTabTv, FONT_MEDIUM)
                appendMenuClickData(VideoEditorTrackConstant.Value.VIDEO_SPEED_CURVE, VideoEditorTrackerHelper.MenuLevelType.SECONDARY_LEVEL)
            }
        }
        vm.speedCurveIndexSelected.observe(this) { newIndex ->
            curveSpeedRecyclerViewAdapter.let { adapter ->
                if (adapter.findFirstSelectedPosition() != newIndex) {
                    adapter.changeSelectedPosition(newIndex)
                    val scrollIndex = if (newIndex in 0 until adapter.itemCount) {
                        newIndex
                    } else {
                        0
                    }
                    curveSpeederRecyclerView.scrollToPosition(scrollIndex)
                }
            }
        }
        mResetButton?.apply {
            vm.isResetBtnEnabled.observe(this@EditorSpeederUIController) {
                isEnabled = it
            }
            setOnClickListener {
                vm.resetSpeed(currentPlayingClip)
            }
        }
        mResetButton?.visibility = View.VISIBLE
        bottomActionBar.applyAllListener = object : BottomActionBar.OnApplyAllListener {
            override fun applyAll(buttonView: View) {
                val videoClip = currentPlayingClip ?: return
                vm.changeSpeed(videoClip, videoClip.speederParams, true)
            }
        }
        mEditorState.editorEngine?.let { editorEngine ->
            timelineViewModel?.setSubSelectedMainTrack(editorEngine.timelineCurrentPosition)
        }
    }

    private fun initCurveView() {
        val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
        curveSpeederRecyclerView.setLayoutManager(layoutManager)
        curveSpeederRecyclerView.setAdapter(curveSpeedRecyclerViewAdapter)
        curveSpeederRecyclerView.overScrollMode = View.OVER_SCROLL_NEVER

        curveSpeedRecyclerViewAdapter.onItemClickListener = { view, pos ->
            val changeSpeedCurveInfo = changeSpeedCurveTemplateList[pos]
            val newSpeederParams = if (TextUtil.isEmpty(changeSpeedCurveInfo.speedCurveRawData)) {
                SpeederParams.modeLinear()
            } else {
                SpeederParams.modeCurve(changeSpeedCurveInfo.speedCurveRawData, changeSpeedCurveInfo.name)
            }
            vm.changeSpeed(currentPlayingClip, newSpeederParams, false)
        }
    }

    override fun createEnterAnim(): Boolean {
        mResetButton?.let {
            AnimatorProcessor.createEnterAnim(it, false, true)
            return true
        } ?: return false
    }

    /**
     * 查找container下的子view控件
     */
    private fun <T : View> findContainerChild(id: Int): T = mContentContainer.findViewById(id)

    /**
     * 初始化变速条控件
     */
    private fun initRuleView() {
        // 设置特殊长刻度
        speederRuleView.setSpecialGradations(getSpecialGradationRules())
        speederRuleView.ruleChangedListener = speederRuleChangedListener
        // 创建刻度间隔规则列表
        speederRuleView.setGradationGapRules(getGradationRules(mContext))
    }

    /**
     * 变速刻度尺值变化监听器
     */
    private val speederRuleChangedListener = object : OnRuleChangedListener {
        override fun onStartDragging() {
            vm.stopPlaybackIfPlaying()
            timelineViewModel.stopFling()
        }

        override fun onValueChanged(speedValue: Float) {
            vm.notifyDisplaySpeedChanged(speedValue.toDouble())
        }

        override fun onScrollStop(speedValue: Float, label: String) {
            vm.changeSpeed(currentPlayingClip, SpeederParams.modeLinear(speedValue.toDouble()), false)
        }
    }

    /**
     * 更新选中的视频片段，并通知视图模型相关的速度和曲线模式变化
     */
    private fun updateSelectedClip(videoClip: IVideoClip) {
        currentPlayingClip = videoClip
        vm.notifySpeedChanged(videoClip.speed)
        vm.notifyDisplaySpeedChanged(videoClip.speed)
        val newSpeederParams = videoClip.speederParams
        val isSpeederCurveMode = SpeederMode.SPEEDER_CURVE == newSpeederParams.mode
        val speedCurveTemplateIndex = vm.getSpeedCurveSelectedIndex(
            videoClip.speederParams.speedCurveRawData
        )
        vm.notifyCurveItemIndexChanged(speedCurveTemplateIndex)
        vm.notifyIsSpeedCurveModeChanged(isSpeederCurveMode)
    }

    override fun destroyView() {
        curveSpeedRecyclerViewAdapter.onItemClickListener = null
        speederRuleView.ruleChangedListener = null
        bottomActionBar.applyAllListener = null
        super.destroyView()
    }

    override fun needTitleCheckBox(): Boolean = vm.hasMultipleTimelineClips()

    /**
     * 点击取消
     */
    fun clickCancel() {
        vm.cancelClipSpeedChange()
    }

    /**
     * 点击完成
     */
    fun clickDone() {
        vm.finishClipSpeedChange()
    }

    /**
     * 视频轨道滑动时的回调监听（手指拖动轨道，播放时轨道自动同步场景）
     */
    override fun onScrolling(scrollX: Int, mode: ScrollMode, isRollback: Boolean) {
        super.onScrolling(scrollX, mode, isRollback)

        val timeScrolled = getDurationByCurrentLength(
            scrollX.toLong(),
            ContextGetter.context
        ).toInt().toLong()

        timelineViewModel?.let { viewModel ->
            currentPlayingClip?.let { clip ->
                if (clip.inPoint <= timeScrolled && timeScrolled < clip.inPoint + clip.duration) {
                    // 仍然在当前选中的视频片段内进行滑动，不需要更新当前选中的视频片段的值
                    return
                }
            }
            val videoClip = vm.getSelectedVideoClip(timeScrolled) ?: return
            if (videoClip == currentPlayingClip) return
            //更新当前选中的视频片段的值
            updateSelectedClip(videoClip)
        }
    }

    /**
     * 获取当前正在播放的视频片段
     */
    private fun getPlayingVideoClip(): IVideoClip? {
        val editorEngine = mEditorState.editorEngine ?: return null
        val timeline: ITimeline = editorEngine.getCurrentTimeline()
        val videoTrack = timeline.getVideoTrack(TRACK_INDEX_FIRST)
        if (videoTrack == null) {
            e(TAG, LogFlag.DL, "[getPlayingVideoClip] video track is null")
            return null
        }

        val currentTime: Long = editorEngine.getTimelineCurrentPosition()
        // 视频片段累计时间
        var accumulatedTime: Long = 0
        for (i in videoTrack.clipList.indices) {
            val videoClip = videoTrack.clipList[i]
            val duration = videoClip.duration
            if (currentTime < (accumulatedTime + duration)) {
                //当前播放时间小于遍历片段累计的时间时，说明当前片段是正在播放的片段
                return videoClip
            }
            //继续遍历查找下一个片段
            accumulatedTime += duration
        }
        return null
    }
    override fun onSelected(
        view: ClipViewWrapper?,
        selected: Boolean,
        clip: ClipModel?,
        withAnim: Boolean
    ) {
        // 检查剪辑是否被选中且不是当前播放的剪辑
        (clip?.takeIf { selected && it != currentPlayingClip }?.clip as? IVideoClip)?.let { updateSelectedClip(it) }
    }

    override fun hasReset() = true

    companion object {
        //中等粗细的字体
        private const val FONT_MEDIUM: String = "sans-serif-medium"
        //常规粗细的字体
        private const val FONT_REGULAR: String = "sans-serif-regular"
    }
}