/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - TBLVideoPlayEffect.kt
 * Description:
 * Version: 1.0
 * Date: 2024/12/5
 * Author: zhangweichao@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * zhangweichao@Apps.Gallery3D  2024/12/5          1.0           OPLUS_FEATURE_APP_LOG_MONITOR
 * <EMAIL>        2025/1/11          1.1           添加对Uri的支持
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec.player.effect

import android.graphics.Rect
import android.net.Uri
import android.util.Pair
import android.util.Size
import com.google.common.reflect.TypeToken
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.tbl.exoplayer2.Effect
import com.oplus.tbl.exoplayer2.util.UnstableApi
import com.oplus.tblplayer.mediainfo.Mp4Util
import com.oplus.tblplayer.processor.CropEffect
import com.oplus.tblplayer.processor.EdgeEffect
import com.oplus.tblplayer.processor.FrameInterpolationEffect
import com.oplus.tblplayer.processor.HomoMatrixTransformationEffect
import com.oplus.tblplayer.processor.MultiTextureMatrixProvider
import com.oplus.tblplayer.processor.ScaleTransformation
import com.oplus.tblplayer.processor.util.AlphaBlendEnum
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileInputStream
import java.lang.reflect.Type

/**
 * TBL视频特效解析和设置类
 */
@UnstableApi
open class TBLVideoPlayEffect(
    private val uri: Uri = Uri.EMPTY,
    private val filePath: String = TextUtil.EMPTY_STRING,
    private val offset: Long = 0L
) : IVideoPlayEffect {

    /**
     * 实况图片的原始eis数据
     */
    private var eisData: String? = null

    /**
     * 解析实况文件获取到的eis数据对应的bean对象
     */
    private var eisInfo: EisInfo? = null

    /**
     * 解码图片后生成的特效列表对象，key为特效对应的名称
     */
    private var effectMap = linkedMapOf<String, Effect>()

    /**
     * 外部动效参数
     */
    private var outerEffectParams: PlayEffectParams? = null

    /**
     * 默认视频播放特效，可以通过forceDefaultEffect接口修改修改默认效果
     */
    private var defaultEffectsKeys = listOf(
        /**
         * 此处去掉TBL_FOV_EFFECT特效，bugfix 9494688，原因如下
         * 在刚进入live图时的默认用的fov处理特效list，没有scaleEffect，首帧已经出来时视频画面还是原来的尺寸1.0倍，而重新长按/点击切换特效list，
         * 有scaleEffect，视频画面放大到了1.1倍，这样从封面图==>1.0倍视频首帧==>1.1倍视频后续帧，看着画面就有较大的跳变，跳变是因为不同scale的图
         * 片合成导致，这个合成会偶现图片不完整而出现类似马赛克
         */
        TBL_FRAME_FILL_EFFECT,
        TBL_CROP_EFFECT
    )

    /**
     * 被禁用的特效，对外提供特效时会将此列表内的特效移除。
     */
    private val _forceDisabledEffects = mutableSetOf<String>()
    private val disabledEffects: Set<String> get() = synchronized(_forceDisabledEffects) { _forceDisabledEffects.toSet() }

    /**
     * 初始化图片的特效和eis数据
     */
    fun init(): Boolean {
        return initEisInfo()
    }

    override fun setPlayEffectParams(effectParams: PlayEffectParams) {
        //外部特效参数生效需要在eis数据存在的情况下才能设置
        if (eisInfo?.cropRect == null) {
            GLog.e(TAG, LogFlag.DL) { "[setPlayEffectParams] do not have eis data, no need to set effect params" }
            return
        }

        outerEffectParams = effectParams
    }

    /**
     * 根据初始化的eis数据，生成对应的TBL特效列表
     */
    fun decode() {
        defaultEffectsKeys.forEach { effectKey ->
            getVideoEffectByKey(effectKey)?.let {
                this.effectMap[effectKey] = it
            } ?: GLog.e(TAG, LogFlag.DL) { "[decode] add effect failed key=$effectKey" }
        }
    }

    override fun getEffectListKeys(): List<String> {
        return effectMap.minus(disabledEffects).keys.toList()
    }

    override fun getEffectList(): List<Any> {
        return effectMap.minus(disabledEffects).values.toList()
    }

    override fun setVideoEffects(effectKeys: List<String>) {
        GLog.i(TAG, LogFlag.DL) { "[setVideoEffects] effectKeys=$effectKeys" }
        effectMap.clear()
        effectKeys.forEach { key ->
            getVideoEffectByKey(key)?.let {
                this.effectMap[key] = it
            } ?: GLog.e(TAG, LogFlag.DL) { "[setVideoEffects] add effect failed key=$key" }
        }
    }

    /**
     * 获取视频照片视频裁剪参数
     */
    fun getVideoCropFactor(): List<Float>? {
        return eisInfo?.eisCropFactor
    }

    /**
     * 获取视频eis原始数据
     */
    fun getVideoEisData(): String? {
        return eisData
    }

    /**
     * 禁用特效
     *
     * @param effectKey 需禁用的特效的名字
     * @return true 则成功加入到禁用列表中，false 则已存在再列表中
     */
    fun disableEffectByKey(effectKey: String): Boolean {
        synchronized(_forceDisabledEffects) {
            return _forceDisabledEffects.add(effectKey)
        }
    }

    /**
     * 解除特效的禁用
     *
     * @param effectKey 需解除禁用的特效的名字
     * @return true 则成功解除禁用，false 则表示特效未被禁用
     */
    fun recoverDisabledEffectByKey(effectKey: String): Boolean {
        synchronized(_forceDisabledEffects) {
            return _forceDisabledEffects.remove(effectKey)
        }
    }

    private fun initEisInfo(): Boolean = runCatching {
        eisData = if (filePath.isEmpty().not()) {
            if (File(filePath).exists().not()) {
                GLog.e(TAG, LogFlag.DL) { "[initEisInfo] video file no exist" }
                return false
            }

            if ((File(filePath).length() < offset) || (offset < 0)) {
                GLog.e(TAG, LogFlag.DL) { "[initEisInfo] offset error $offset " }
                return false
            }
            FileInputStream(filePath).use {
                it.skip(offset)
                it.readBytes()
            }.let(::ByteArrayInputStream)
        } else {
            if ((uri == Uri.EMPTY) || (offset < 0)) {
                GLog.e(TAG, LogFlag.DL) { "[initEisInfo] illegal input. offset = $offset, uri = $uri" }
                return false
            }
            FileAccessManager.getInstance().openFile(ContextGetter.context, uri)?.use { pfd ->
                FileInputStream(pfd.fileDescriptor).use {
                    it.skip(offset)
                    it.readBytes()
                }.let(::ByteArrayInputStream)
            }
        }?.use(Mp4Util::getLivePhotoExtension) ?: TextUtil.EMPTY_STRING

        val eisType: Type = object : TypeToken<EisInfo>() {}.type
        eisInfo = JsonUtil.fromJson<EisInfo>(eisData, eisType)
    }.onFailure {
        GLog.e(TAG, LogFlag.DL, it) { "[initEisInfo] error = " }
    }.isSuccess

    private fun getVideoEffectByKey(effectKey: String): Effect? {
        if (effectKey.isEmpty()) {
            return null
        }

        val videoEffect = when (effectKey) {
            TBL_CUSTOM_CROP_EFFECT -> generateCropEffect(outerEffectParams?.videoSize, outerEffectParams?.cropRect)
            TBL_CROP_EFFECT -> generateCropEffect(eisInfo?.videoSize, eisInfo?.cropRect)
            TBL_FRAME_FILL_EFFECT -> generateFrameFillEffect()
            TBL_SCALE_EFFECT -> generateScaleEffect()
            TBL_FOV_EFFECT -> generateFovEffect()
            TBL_PRESENTATION_EFFECT -> generatePresentationEffect(outerEffectParams?.videoSize, outerEffectParams?.cropRect)
            else -> generateCustomEffect(effectKey)
        }

        return videoEffect
    }

    /**
     * 生成自定义的Effect对象
     * 除了TBL自带的几个动效[TBL_CROP_EFFECT]、[TBL_FRAME_FILL_EFFECT]、[TBL_SCALE_EFFECT]、[TBL_FOV_EFFECT]，其它的都需要自己创建并实现效果。
     * 子类可自行实现。
     *
     * @param effectKey 自定义动效的key，用于区分不同效果
     * @return 依据[effectKey]实现得Effect效果，没有键或没有实现效果则返回null
     */
    open fun generateCustomEffect(effectKey: String): Effect? {
        return null
    }

    /**
     * 裁剪特效
     * @param videoSize 视频宽高
     * @param cropRect 裁剪区域
     */
    private fun generateCropEffect(videoSizeList: List<Int>?, cropList: List<Int>?): Effect? {
        val videoSize = videoSizeList?.let {
            if (it.size != AppConstants.Number.NUMBER_2) {
                GLog.e(TAG, LogFlag.DL) { "[generateCropEffect] videoSizeList size error size=${it.size}" }
                return null
            }
            Size(it[AppConstants.Number.NUMBER_0], it[AppConstants.Number.NUMBER_1])
        }

        val cropRect = cropList?.let {
            if (it.size != AppConstants.Number.NUMBER_4) {
                GLog.e(TAG, LogFlag.DL) { "[generateCropEffect] cropList error size=${it.size}" }
                return null
            }
            Rect().apply {
                left = it[AppConstants.Number.NUMBER_0]
                top = it[AppConstants.Number.NUMBER_1]
                right = it[AppConstants.Number.NUMBER_2]
                bottom = it[AppConstants.Number.NUMBER_3]
            }
        }

        return generateCropEffect(videoSize, cropRect)
    }

    /**
     * 裁剪特效
     * @param videoSize 视频宽高
     * @param cropRect 裁剪区域
     */
    private fun generateCropEffect(videoSize: Size?, cropRect: Rect?): Effect? {
        //获取视频大小
        videoSize ?: run {
            GLog.e(TAG, LogFlag.DL) { "[generateCropEffect] videoSize is null" }
            return null
        }

        //获取裁剪Rect
        cropRect ?: run {
            GLog.e(TAG, LogFlag.DL) { "[generateCropEffect] cropRect is null" }
            return null
        }

        GLog.d(TAG, LogFlag.DL) { "[generateCropEffect] effect=CropEffect, videoSize=$videoSize, cropRect=$cropRect" }
        return CropEffect.createCropEffectFromRect(videoSize, 0, cropRect)
    }

    /**
     * 裁剪特效
     * @param videoSize 视频宽高
     * @param cropRect 裁剪区域
     */
    private fun generatePresentationEffect(videoSize: Size?, cropRect: Rect?): Effect? {
        //获取视频大小
        videoSize ?: run {
            GLog.e(TAG, LogFlag.DL) { "[generatePresentationEffect] videoSize is null" }
            return null
        }


        cropRect ?: run {
            GLog.e(TAG, LogFlag.DL) { "[generatePresentationEffect] cropRect is null" }
            return null
        }

        //获取裁剪Rect
        val presentCropRect = Rect().apply {
            left = (cropRect.left - AppConstants.Number.NUMBER_4).coerceAtLeast(0)
            top = (cropRect.top - AppConstants.Number.NUMBER_4).coerceAtLeast(0)
            right = cropRect.right + AppConstants.Number.NUMBER_4
            bottom = cropRect.bottom + AppConstants.Number.NUMBER_4
        }

        GLog.d(TAG, LogFlag.DL) { "[generatePresentationEffect] effect=PresentationEffect, videoSize=$videoSize, cropRect=$presentCropRect" }
        return EdgeEffect.createForPresentationRect(videoSize, 0, presentCropRect)
    }

    /**
     * 缩放特效
     */
    private fun generateScaleEffect(): Effect? {
        val scale = eisInfo?.eisCropFactor?.let {
            if (it.size != AppConstants.Number.NUMBER_2) {
                Pair(DEFAULT_VIDEO_SCALE, DEFAULT_VIDEO_SCALE)
            } else {
                Pair(it[AppConstants.Number.NUMBER_0], it[AppConstants.Number.NUMBER_1])
            }
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "[generateScaleEffect] video crop factor is null" }
            Pair(DEFAULT_VIDEO_SCALE, DEFAULT_VIDEO_SCALE)
        }

        GLog.d(TAG, LogFlag.DL) { "[generateScaleEffect] effect=ScaleTransformation, scale=[${scale.first}, ${scale.second}]" }
        return ScaleTransformation.Builder().setScale(scale.first, scale.second).build()
    }

    /**
     * 补帧特效
     */
    private fun generateFrameFillEffect(): Effect? {
        return FrameInterpolationEffect.createBlendFrameInterpolationEffect(AlphaBlendEnum.BLEND_MODE_DEFAULT)
    }

    /**
     * Fov特效
     */
    private fun generateFovEffect(): Effect? {
        val matrices = eisInfo?.matrices ?: let {
            GLog.e(TAG, LogFlag.DL) { "[generateFovEffect] matrices is null" }
            return null
        }

        val matrixList = mutableListOf<Pair<Long, FloatArray>>().apply {
            matrices.forEach { (key, value) ->
                add(Pair<Long, FloatArray>(key.toLong(), value.toFloatArray()))
            }
        }

        return eisInfo?.videoOrientation?.let {
            HomoMatrixTransformationEffect.createHomoMatrixEffect(MultiTextureMatrixProvider(matrixList), false, it)
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "generateFovEffect: video orientation is null" }
            null
        }
    }

    /**
     * 外部传入的特效参数来自定义播放效果
     * @param videoSize 视频大小, 创建裁切特效时tbl需要知道视频大小
     * @param cropRect  裁剪特效参数：具体视频要进行裁切的区域
     */
    data class PlayEffectParams(val videoSize: Size?, val cropRect: Rect?)

    companion object {
        private const val TAG = "TBLVideoPlayEffect"

        /**
         * 缩放特效
         */
        const val TBL_SCALE_EFFECT = "TBL_SCALE_EFFECT"

        /**
         *  自定义裁剪特效，指定视频大小和裁切范围
         */
        const val TBL_CUSTOM_CROP_EFFECT = "TBL_CUSTOM_CROP_EFFECT"

        /**
         * eis数据中的裁切特效
         */
        const val TBL_CROP_EFFECT = "TBL_CROP_EFFECT"

        /**
         * 补帧特效
         */
        const val TBL_FRAME_FILL_EFFECT = "TBL_FRAME_FILL_EFFECT"

        /**
         * FOV特效
         */
        const val TBL_FOV_EFFECT = "TBL_MATRIX_EFFECT"

        /**
         * 显示特效,在不显示的内容位置填充黑色
         */
        const val TBL_PRESENTATION_EFFECT = "TBL_PRESENTATION_EFFECT"

        /**
         * 默认缩放比例
         */
        private const val DEFAULT_VIDEO_SCALE = 1.05F
    }
}

/**
 * 视频中插帧数据信息类
 */
private data class EisInfo(
    @SerializedName("desc")
    val desc: String? = null,

    @SerializedName("cropRect")
    val cropRect: List<Int>? = null,

    @SerializedName("eisCropFactor")
    val eisCropFactor: List<Float>? = null,

    @SerializedName("videoOrientation")
    val videoOrientation: Int? = null,

    @SerializedName("videoSize")
    val videoSize: List<Int>? = null,

    @SerializedName("matrices")
    val matrices: Map<String, List<Float>>? = null,

    @SerializedName("version")
    val version: Int? = null
)