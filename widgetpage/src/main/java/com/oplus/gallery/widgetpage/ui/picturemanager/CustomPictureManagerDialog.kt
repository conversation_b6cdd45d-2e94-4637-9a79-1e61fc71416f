/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CustomPictureManagerDialog.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/28        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.picturemanager

import android.content.DialogInterface
import android.os.Bundle
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment

@RouterNormal(path = RouterConstants.RouterName.WIDGET_MODE_SELECTION_DIALOG)
class CustomPictureManagerDialog : PanelDialog() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setPanelFragment(
            PanelFragment.Builder()
                .setContentFragment(CustomPictureManagerFragment().apply {
                    arguments = <EMAIL>
                })
                .setBackgroundColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard))
                .setNavColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard))
                .create()
        )
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        activity?.finish()
    }
}