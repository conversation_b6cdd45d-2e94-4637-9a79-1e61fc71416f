/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIEliminateEffectView.kt
 ** Description: 智能消除动效view
 ** Version: 1.0
 ** Date : 2023/11/1
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/11/01    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.eliminate.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PointF
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizer
import com.oplus.gallery.foundation.ui.gesture.OnComplexGestureListener
import com.oplus.gallery.foundation.ui.gesture.detector.ScaleRotateDetector
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.animationLevelV2
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminatePath
import com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminateType
import com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.LayerInfo
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizerView2
import com.oplus.vfxsdk.magicediteffect.BitmapInfo
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * 智能消除笔迹和动效的控件
 * @param context Context
 * @param attributeSet AttributeSet
 */
class AIEliminateEffectView @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null
) : AIEffectView(context, attributeSet), OnComplexGestureListener, IEliminatePathPainter {

    val detectEffect: ILayerDetectEffect by lazy { LayerDetectEffect(this) }

    val eliminateEffect: IEliminateEffect by lazy { EliminateEffect(this) }

    private var eliminatePath: EliminatePath? = null

    private val gestureRecognizer = GestureRecognizer(context, this)

    private var simpleGestureListener: GestureRecognizerView2.SimpleGestureListener? = null

    private var eliminateListener: EliminatePathListener? = null

    private var previewAnimationProperties: PreviewAnimationProperties? = null

    private val drawOutbound get() = previewAnimationProperties?.contentDrawingOutBound?.current

    private var strokeSize: EliminatePath.Size? = null

    private var downPointF = PointF()
    private val touchSlop by lazy { ViewConfiguration.get(context).scaledTouchSlop }

    private var isTouchableWhenDown = false

    private val safeDistance = resources.getDimensionPixelOffset(R.dimen.picture3d_eliminate_drop_down_safety_distance)

    override var touchable: Boolean = true

    private var touchState = TouchingState.UNDEFINE

    private var currentAnimationState = EliminateAnimationState.NULL

    var layerDetectEffectListener: LayerDetectEffectListener? = null
    var eliminateEffectListener: EliminateEffectListener? = null
    private var currentType = EliminateType.AI

    /** 双指相关操作后需要更新下图像内容的位置 */
    private var needUpdateRectAfterScaleRotate = false
    private var hasScaleRotateAnimation = false

    /**
     * 初始化
     * @param type 消除类型
     * @param imgWidth 图片宽度
     * @param imgHeight 图片高度
     */
    fun init(type: EliminateType, imgWidth: Int, imgHeight: Int) {
        startRenderThread()
        setType(type)
        // 告诉SDK显示在动效View上的图片宽高
        setOriginImageSize(imgWidth, imgHeight)
        updateImageRect()
        setEliminateListener(object : EffectEliminateListener {
            override fun onPreCompleteLayerShow() {
                GLog.d(TAG) { "onPreCompleteLayerShow" }
                // 此接口触发时动效指令才上传，相册直接刷新图片会比sdk快，造成闪烁，故相册需要晚一点点时间才能刷新图片
                postDelayed({
                    eliminateEffectListener?.onPreCompleteLayerShow()
                    currentAnimationState = EliminateAnimationState.MOSAIC
                }, SHOW_STAGE_RESULT_DELAY)
            }

            override fun onCompleteAnimationEnd() {
                GLog.d(TAG) { "onCompleteAnimationEnd" }
                eliminateEffectListener?.onCompleteAnimationEnd()
                currentAnimationState = EliminateAnimationState.NULL
            }

            override fun onBitmapInfoError(code: Int) {
                GLog.e(TAG, "onBitmapInfoError code = $code")
                eliminateEffectListener?.onBitmapInfoError(code)
            }
        })
        setDetectListener(object : EffectDetectListener {
            override fun onLayerDetectedAnimationEnd() {
                GLog.d(TAG) { "onLayerDetectedAnimationEnd" }
                layerDetectEffectListener?.onLayerDetectedAnimationEnd()
            }
        })
    }

    /**
     * 设置类型
     * @param type 消除类型
     */
    fun setType(type: EliminateType) {
        GLog.d(TAG, "setType $type")
        when (type) {
            EliminateType.AI -> setEliminateType(TYPE_AI)
            EliminateType.MANUAL -> setEliminateType(TYPE_MANUAL)
            EliminateType.PASSERBY -> setEliminateType(TYPE_PASSERS)
        }
        currentType = type
    }

    override fun setAnimationProperties(previewAnimationProperties: PreviewAnimationProperties) {
        this.previewAnimationProperties = previewAnimationProperties
    }

    /**
     * 设置全图遮罩
     */
    fun setAllLayerMaskBitmap(mask: Bitmap) {
        GLog.d(TAG) { " setAllLayerMaskBitmap" }
        setAllLayerMask(mask)
    }

    /**
     * 设置起始图层信息
     * @param layers 图层及图层的轮廓等信息
     */
    fun setLayerInfo(layers: List<LayerInfo>) {
        GLog.d(TAG) { "setLayerInfo ids:${layers.map { it.id }}" }
        if (layers.isEmpty()) return
        // 设置每个图层的信息
        setLnsInfo(
            layers.map {
                it.maskBitmap?.let { mask ->
                    if (mask.config != Bitmap.Config.ALPHA_8) {
                        GLog.w(TAG, "setLayerInfo mask config error:${mask.config}, layer:$it")
                    }
                }
                it.toLnsLayerInfo()
            }
        )
        currentAnimationState = EliminateAnimationState.CONTOUR
    }

    override fun onCheckNeedEnterLowMode(): Boolean {
        return animationLevelV2 == PerformanceLevelUtils.PerformanceLevel.LOW || animationLevelV2 == PerformanceLevelUtils.PerformanceLevel.LOWER
    }

    override fun setStrokeSize(strokeSize: EliminatePath.Size) {
        this.strokeSize = strokeSize
        setStrokeSize(strokeSize.strokeWidth.toFloat())
    }

    /**
     * 获取当前的动画状态
     * @return 动画状态
     */
    fun getAnimationState(): EliminateAnimationState {
        return currentAnimationState
    }

    /**
     * 通知SDK停止渲染，并去掉所有外部监听
     */
    fun destroy() {
        exitRenderThread()
        clearContent()
        eliminateEffectListener = null
        setEliminateListener(null)
        layerDetectEffectListener = null
        setDetectListener(null)
        setEliminatePathListener(null)
        simpleGestureListener = null
    }

    override fun invalidPath() = Unit

    /**
     * 预览图的位置发生变化，通知覆盖层的view刷新相关位置
     */
    fun updateLayout() {
        updateImageRect()
    }

    fun onContentAnimationEnd() {
        GLog.d(TAG) { "onAnimationStop animation:$animation, touchState:$touchState, needShowLayerOutline:$needUpdateRectAfterScaleRotate" }
        if ((touchState != TouchingState.ROTATESCALING) && needUpdateRectAfterScaleRotate) {
            updateRectAfterScaleRotate()
        }
    }

    private fun updateRectAfterScaleRotate() {
        needUpdateRectAfterScaleRotate = false
        updateImageRect()
        if (currentType == EliminateType.PASSERBY) {
            GLog.d(TAG) { "updateRectAfterScaleRotate showAllLayersOutline" }
            showAllLayersOutline()
        }
    }

    override fun setEliminatePathListener(listener: EliminatePathListener?) {
        eliminateListener = listener
    }

    override fun setSimpleGestureListener(listener: GestureRecognizerView2.SimpleGestureListener) {
        this.simpleGestureListener = listener
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // down 事件下来的时候是否可以触摸，以避免touchable修改前后导致gestureRecognize没消费down却接收了后续事件，导致事件异常
        if (event.action == MotionEvent.ACTION_DOWN) {
            isTouchableWhenDown = touchable
        }
        return if (touchable && isTouchableWhenDown) {
            gestureRecognizer.onTouchEvent(event)
        } else {
            true
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        post { updateImageRect() }
    }

    override fun onUpOrCancel(event: MotionEvent): Boolean {
        val intercept = simpleGestureListener?.onUp(event.x, event.y)
        if ((touchState == TouchingState.SLIDING) || (touchState == TouchingState.CLICKING)) {
            // 单击的up点是不需要记录的，只添加滑动的即可
            if (touchState == TouchingState.SLIDING) {
                addPointIfNeed(event)
            }
            if (event.isActionUp()) {
                eliminateListener?.onPathEnd(eliminatePath)
            } else {
                eliminateListener?.onPathCancel()
                clearContent()
            }
        }
        touchState = TouchingState.UNDEFINE
        eliminatePath = null
        return intercept == true
    }

    override fun clearContent() {
        super.clearContent()
        GLog.d(TAG) { "clearContent" }
        currentAnimationState = EliminateAnimationState.NULL
    }

    override fun onHover(x: Float, y: Float): Boolean {
        return false
    }

    override fun onHoverLeave(x: Float, y: Float): Boolean {
        return false
    }

    override fun onDown(e: MotionEvent): Boolean {
        val strokeSize = strokeSize ?: let {
            GLog.e(TAG, "[onDown] can't draw path because strokeSize is null")
            return false
        }
        val drawingBound = drawOutbound ?: let {
            GLog.e(TAG, "[onDown] can't draw path because drawOutbound is null")
            return false
        }
        if (e.y < safeDistance) {
            GLog.d(TAG) { "[onDown] Touch position ${e.y} in status bar region [0, $safeDistance), skip..." }
            //这里返回true拦截事件，防止事件透传到下层事件EliminateSheet导致的图片被拖拽下拉
            return true
        }
        touchState = TouchingState.CLICKING
        eliminatePath = EliminatePath(strokeSize, drawingBound)
        addPointIfNeed(e)
        downPointF.x = e.x
        downPointF.y = e.y
        simpleGestureListener?.onDown(e.x, e.y)
        return true
    }

    override fun onShowPress(e: MotionEvent) {
    }

    override fun onSingleTapUp(e: MotionEvent): Boolean {
        return false
    }

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        if (e2.isMoved() && touchState == TouchingState.CLICKING) {
            if (previewAnimationProperties?.animationState?.isAnimating == false) {
                touchState = TouchingState.SLIDING
                eliminateListener?.onPathStart()
            }
        }
        if (touchState == TouchingState.ROTATESCALING) {
            simpleGestureListener?.onScroll(distanceX, distanceY)
        } else if (touchState == TouchingState.SLIDING) {
            addPointIfNeed(e2)
        }
        return true
    }

    override fun onLongPress(e: MotionEvent) {
        simpleGestureListener?.onLongPress(e)
    }

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        return false
    }

    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        return false
    }

    override fun onDoubleTap(e: MotionEvent): Boolean {
        return simpleGestureListener?.onDoubleTap() ?: false
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        return false
    }

    override fun onScaleRotateBegin(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        detector: ScaleRotateDetector?
    ): Boolean {
        GLog.d(TAG, "onScaleRotateBegin")
        if (touchState == TouchingState.SLIDING) {
            eliminateListener?.onPathCancel()
        }
        touchState = TouchingState.ROTATESCALING
        eliminatePath = null
        if (currentType == EliminateType.PASSERBY) {
            GLog.d(TAG) { "onScaleRotateBegin hideAllLayersOutline" }
            updateImageRect()
            hideAllLayersOutline()
        } else {
            clearContent()
        }
        parent.requestDisallowInterceptTouchEvent(true)
        return simpleGestureListener?.onScaleAndRotateBegin(
            scalePivotX,
            scalePivotY,
            angle,
            scale
        ) == true
    }

    override fun onScaleRotate(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        detector: ScaleRotateDetector
    ): Boolean {
        if ((angle != 0F) || (scale != 1.0F)) {
            hasScaleRotateAnimation = true
            return simpleGestureListener?.onScaleAndRotate(scalePivotX, scalePivotY, angle, scale, detector) == true
        } else {
            //说明未执行缩放和旋转，则不处理事件。比如三指滑动截屏时，触摸滑动事件最终是被系统消费了，相册不需记住动画状态
            return false
        }
    }

    override fun onScaleRotateEnd(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        detector: ScaleRotateDetector?
    ): Boolean {
        GLog.d(TAG, "onScaleRotateEnd")
        val result = simpleGestureListener?.onScaleAndRotateEnd(
            scalePivotX,
            scalePivotY,
            angle,
            scale
        ) == true
        if (hasScaleRotateAnimation) {
            hasScaleRotateAnimation = false
            // 有动画需要执行，则在动画完成时（onAnimationStop）再跑updateRectAfterScaleRotate方法更新图像位置
            needUpdateRectAfterScaleRotate = true
        } else {
            updateRectAfterScaleRotate()
        }
        return result
    }

    private fun addPointIfNeed(event: MotionEvent) {
        val drawingBound = drawOutbound ?: return
        if (currentType != EliminateType.PASSERBY) {
            eliminatePath?.let {
                touchEvent(event)
            }
        }
        eliminatePath?.addPoint(event.mapToDrawingArea(drawingBound))
    }

    private fun MotionEvent.mapToDrawingArea(drawingBound: RectF): PointF {
        val mapPoint = PointF()
        val width = drawingBound.width()
        val height = drawingBound.height()
        if (width == 0f || height == 0f) {
            mapPoint[0f] = 0f
        } else {
            val scaleX = 1f / width
            val scaleY = 1f / height
            val translateX = drawingBound.left
            val translateY = drawingBound.top
            mapPoint.x = (x - translateX) * scaleX
            mapPoint.y = (y - translateY) * scaleY
        }
        return mapPoint
    }

    private fun MotionEvent.isMoved(): Boolean {
        val xDiff = abs(x - downPointF.x)
        val yDiff = abs(y - downPointF.y)
        return (xDiff > touchSlop) || (yDiff > touchSlop)
    }

    private fun updateImageRect() {
        previewAnimationProperties?.contentDrawingOutBound?.final?.takeIf { it.isValid() }?.let {
            GLog.d(TAG) { "updateImageRect rect:$it" }
            // 告诉SDK显示在动效View上的图片位置和宽高，后续手势的坐标位置（如涂抹路径）均以这个为准
            setLnsShowRect(it.left.roundToInt(), it.top.roundToInt(), it.width().roundToInt(), it.height().roundToInt())
        } ?: GLog.w(TAG, "updateImageRect invalid:${previewAnimationProperties?.contentDrawingOutBound?.final}")
    }

    private fun RectF.isValid(): Boolean {
        return left.isNaN().not() && top.isNaN().not() && width().isNaN().not() && height().isNaN().not()
    }

    private fun MotionEvent.isActionUp(): Boolean {
        return action == MotionEvent.ACTION_UP
    }

    private class LayerDetectEffect(private val effectView: AIEliminateEffectView) : ILayerDetectEffect {

        private val tag = "$TAG.LayerDetectEffect"
        override fun startLayerDetectingAnimation() {
            GLog.d(TAG) { "startLayerDetectingAnimation" }
            effectView.updateImageRect()
            // 开始光点动画
            effectView.startLayerDetectingAnimation()
        }

        override fun startLayerDetectedAnimation() {
            GLog.d(tag) { "startLayerDetectedAnimation" }
            // 结束光点动画，开始扫描完成的动画
            effectView.startLayerDetectedAnimation()
        }

        override fun showLayerOutline(layerIds: List<Int>, hasAnimation: Boolean) {
            GLog.d(tag) { "showLayerOutline layerIds:$layerIds, hasAnimation:$hasAnimation" }
            // 显示指定图层
            effectView.showLayerOutline(layerIds, hasAnimation)
        }

        override fun hideLayerOutline(layerIds: List<Int>, hasAnimation: Boolean) {
            GLog.d(tag) { "hideLayerOutline layerIds:$layerIds, hasAnimation:$hasAnimation" }
            effectView.hideLayerOutline(layerIds)
        }
    }

    private class EliminateEffect(private val effectView: AIEliminateEffectView) : IEliminateEffect {
        private val tag = "$TAG.LayerEliminateEffect"

        override fun startEliminateAnimation(layerIds: List<Int>, duration: Int) {
            GLog.d(tag) { "startEliminateAnimation layerIds:$layerIds" }
            if (layerIds.isEmpty()) return
            effectView.updateImageRect()
            // 开始点选消除的动画
            effectView.startEliminateAnimation(layerIds, duration.toFloat())
        }

        override fun startPreCompleteAnimation(layers: List<LayerInfo>) {
            GLog.d(tag) { "startPreCompleteAnimation count:${layers.size}" }
            if (layers.isEmpty()) return
            // 设置预补全阶段的图层信息，用于马赛克动画的显示
            effectView.startPreCompleteAnimation(layers.map { BitmapInfo(it.contentBitmap, it.id) })
        }

        override fun startCompleteAnimation() {
            GLog.d(tag) { "startCompleteAnimation" }
            effectView.startCompleteAnimation()
        }

        override fun cancelEliminateAnimation(layerIds: List<Int>) {
            GLog.d(tag) { "cancelEliminateAnimation layerIds:$layerIds" }
            if (layerIds.isEmpty()) return
            // 取消消除动画，并显示消除前的图层轮廓
            effectView.cancelEliminateAnimation(layerIds)
        }
    }

    /**
     * AIEliminateEffectView的事件状态，
     * 默认:UNDEFINE
     * 点击:CLICKING
     * 滑动:SLIDING
     * 缩放:ROTATESCALING
     */
    enum class TouchingState {
        UNDEFINE,
        CLICKING,
        SLIDING,
        ROTATESCALING
    }

    /**
     * 消除的动画状态
     */
    enum class EliminateAnimationState {
        /**
         * 无动画，包含以下场景：默认值、消除结束、取消消除
         */
        NULL,

        /**
         * 轮廓动画，从笔迹收缩开始到马赛克期间的动画
         */
        CONTOUR,

        /**
         * 马赛克动画，预补全结果来到时触发
         */
        MOSAIC
    }

    companion object {
        private const val TAG = "AIEliminateEffectView"
        private const val SHOW_STAGE_RESULT_DELAY = 100L

        /** 智能圈选 */
        private const val TYPE_AI = 0

        /** 手动涂抹 */
        private const val TYPE_MANUAL = 1

        /** 路人消除 */
        private const val TYPE_PASSERS = 2
    }
}