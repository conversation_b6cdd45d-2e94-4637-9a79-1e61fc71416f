package com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle;

import android.annotation.SuppressLint;
import android.content.ComponentCallbacks2;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.codec.glide.util.Synthetic;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

/**
 * An {@link  com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.BitmapPool} implementation that uses an
 * {@link  com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.LruPoolStrategy} to bucket {@link Bitmap}s
 * and then uses an LRU eviction policy to evict {@link android.graphics.Bitmap}s from the least
 * recently used bucket in order to keep the pool below a given maximum size limit.
 */
public class LruBitmapPool implements BitmapPool {
    private static final String TAG = "LruBitmapPool";
    private static final Bitmap.Config DEFAULT_CONFIG = Bitmap.Config.ARGB_8888;

    private static final int DEFAULT_MAX_REUSE_MULTIPLE = 8;

    private static final boolean DEBUG = GallerySystemProperties.getBoolean("debug.gallery.lruBitmapPool", false);

    private static final int IDLE_DURATION_THRESHOLD = 20 * TimeUtils.MILLISECOND_IN_SECOND;

    private static final int MSG_IDLE_CHECK = 1;

    private final LruPoolStrategy strategy;
    private final Set<Bitmap.Config> allowedConfigs;
    private final long initialMaxSize;
    private final BitmapTracker tracker;

    private final long maxIdleSize;

    private final String tag;

    private long maxSize;

    private long currentSize;
    private int hits;
    private int misses;
    private int puts;
    private int evictions;
    /** 缓存池超过闲置大小时记录的系统时间，当缓存池降到低于该值 */
    private long lastOverIdleSizeTime = 0;
    private Handler idleCheckHandler;

    public LruBitmapPool(long maxSize) {
        this(null, maxSize, Long.MAX_VALUE, DEFAULT_MAX_REUSE_MULTIPLE);
    }

    /**
     * 构建缓存池
     * @param tag              日志标记信息
     * @param maxSize          初始缓存池最大大小，单位字节
     * @param maxIdleSize      缓存池最大闲置大小，单位字节
     * @param maxReuseMultiple 复用bitmap时，允许被复用的bitmap大于要求大小的倍数
     */
    public LruBitmapPool(String tag, long maxSize, long maxIdleSize, float maxReuseMultiple) {
        this(tag, maxSize, maxIdleSize, new SizeConfigStrategy(maxReuseMultiple), getDefaultAllowedConfigs());
    }

    /**
     * 构建缓存池
     * @param tag            日志标记信息
     * @param maxSize        初始缓存池最大大小，单位字节
     * @param maxIdleSize    缓存池最大限制大小，单位字节。如果发现缓冲池持续一段时间{@link IDLE_DURATION_THRESHOLD)}超过该值，则会清理超过该值的多余缓存
     * @param strategy       缓存池策略
     * @param allowedConfigs 允许抛进缓存池的config名单{@link android.graphics.Bitmap.Config}
     */
    public LruBitmapPool(String tag, long maxSize, long maxIdleSize, LruPoolStrategy strategy, Set<Bitmap.Config> allowedConfigs) {
        this.tag = tag;
        this.initialMaxSize = maxSize;
        this.maxSize = maxSize;
        this.maxIdleSize = maxIdleSize;
        this.strategy = strategy;
        this.allowedConfigs = allowedConfigs;
        this.tracker = DEBUG ? new ThrowingBitmapTracker(tag) : new NullBitmapTracker();
    }

    @Override
    public long getMaxSize() {
        return maxSize;
    }

    @Override
    public synchronized void setSizeMultiplier(float sizeMultiplier) {
        maxSize = Math.round(initialMaxSize * sizeMultiplier);
        evict();
    }

    @Override
    public synchronized void put(Bitmap bitmap) {
        if (bitmap == null) {
            return;
        }
        if (bitmap.isRecycled()) {
            if (DEBUG) {
                GLog.w(TAG, markLog("put recycled bitmap!"));
            }
            return;
        }
        final int size = strategy.getSize(bitmap);
        if (!bitmap.isMutable()
                || (size > maxSize)
                || !allowedConfigs.contains(bitmap.getConfig())) {
            if (DEBUG) {
                GLog.w(TAG, markLog("put bitmap: " + strategy.logBitmap(bitmap)
                        + ", is mutable: " + bitmap.isMutable()
                        + ", is allowed config: " + allowedConfigs.contains(bitmap.getConfig())));
            }
            bitmap.recycle();
            return;
        }

        // pool中已包含该bitmap，则跳过，避免出现重复bitmap引发异常
        if (!strategy.put(bitmap)) {
            GLog.e(TAG, markLog("put repeat bitmap! skip..."));
            return;
        }
        tracker.add(bitmap);
        puts++;
        currentSize += size;

        if (DEBUG) {
            GLog.d(TAG, markLog("put bitmap in pool=" + strategy.logBitmap(bitmap)));
        }
        dump();

        evict();

        idleCheck();
    }

    /**
     * 闲置内存检查，超过阈值持续达到一定时间就会清理掉
     */
    private void idleCheck() {
        if ((lastOverIdleSizeTime <= 0) && (currentSize > maxIdleSize)) {
            lastOverIdleSizeTime = SystemClock.elapsedRealtime();
            if (idleCheckHandler == null) {
                idleCheckHandler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(@NonNull Message msg) {
                        if (currentSize <= maxIdleSize) {
                            return;
                        }
                        GLog.w(TAG, markLog("idleCheck, clear currentSize:" + currentSize + " to maxIdleSize:" + maxIdleSize));
                        BuildersKt.launch(AppScope.INSTANCE, (CoroutineContext) Dispatchers.getIO(), CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
                            trimToSize(maxIdleSize);
                            return null;
                        });
                    }
                };
            }
            idleCheckHandler.sendEmptyMessageDelayed(MSG_IDLE_CHECK, IDLE_DURATION_THRESHOLD);
        }
    }

    private void evict() {
        trimToSize(maxSize);
    }

    @Override
    @NonNull
    public Bitmap get(int width, int height, Bitmap.Config config) {
        Bitmap result = getOrNull(width, height, config);
        if (result == null) {
            result = createBitmap(width, height, config);
        }

        return result;
    }

    @Nullable
    public Bitmap getOrNull(int width, int height, @Nullable Bitmap.Config config) {
        Bitmap result = getDirtyOrNull(width, height, config);
        if (result != null) {
            // Bitmaps in the pool contain random data that in some cases must be cleared for an image
            // to be rendered correctly. we shouldn't force all consumers to independently erase the
            // contents individually, so we do so here. See issue #131.
            result.eraseColor(Color.TRANSPARENT);
        }
        return result;
    }

    @NonNull
    @Override
    public Bitmap getDirty(int width, int height, Bitmap.Config config) {
        Bitmap result = getDirtyOrNull(width, height, config);
        if (result == null) {
            result = createBitmap(width, height, config);
        }
        return result;
    }

    @NonNull
    private static Bitmap createBitmap(int width, int height, @Nullable Bitmap.Config config) {
        return Bitmap.createBitmap(width, height, config != null ? config : DEFAULT_CONFIG);
    }

    private void assertNotHardwareConfig(Bitmap.Config config) {
        if (config == Bitmap.Config.HARDWARE) {
            throw new IllegalArgumentException(
                    markLog("Cannot create a mutable Bitmap with config: "
                            + config
                            + ". Consider setting Downsampler#ALLOW_HARDWARE_CONFIG to false in your"
                            + " RequestOptions and/or in GlideBuilder.setDefaultRequestOptions"));
        }
    }

    @Nullable
    public synchronized Bitmap getDirtyOrNull(
            int width, int height, @Nullable Bitmap.Config config) {
        assertNotHardwareConfig(config);
        // Config will be null for non public config types, which can lead to transformations naively
        // passing in null as the requested config here. See issue #194.
        final Bitmap result = strategy.get(width, height, config != null ? config : DEFAULT_CONFIG);
        if (result == null) {
            if (DEBUG && (currentSize > 0)) {
                GLog.w(TAG, markLog("getDirtyOrNull missing, currentSize:" + currentSize
                        + ", w:" + width
                        + ", h:" + height
                        + ", size:" + strategy.logBitmap(width, height, config)));
            }
            misses++;
        } else {
            hits++;
            onBitmapPop(result);
            normalize(result);
            if (DEBUG) {
                GLog.d(TAG, markLog("getDirtyOrNull success, currentSize:" + currentSize
                        + ", w:" + result.getWidth()
                        + ", h:" + result.getHeight()
                        + ", size:" + strategy.logBitmap(width, height, config))
                        + ", realSize:" + strategy.getSize(result));
            }
        }

        dump();

        return result;
    }

    // Setting these two values provides Bitmaps that are essentially equivalent to those returned
    // from Bitmap.createBitmap.
    private static void normalize(Bitmap bitmap) {
        bitmap.setHasAlpha(true);
        bitmap.setPremultiplied(true);
    }

    @Override
    public void clearMemory() {
        GLog.d(TAG, markLog("clearMemory"));
        trimToSize(0);
    }

    @SuppressLint("InlinedApi")
    @Override
    public void trimMemory(int level) {
        if (level > ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            clearMemory();
        } else if ((level == ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL)
                || (level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN)) {
            trimToSize(getMaxSize() / 2);
        }
    }

    public synchronized List<Bitmap> removeAll() {
        List<Bitmap> bitmaps = new ArrayList<>();
        Bitmap removed = null;
        while ((removed = strategy.removeLast()) != null) {
            bitmaps.add(removed);
            tracker.remove(removed);
        }
        currentSize = 0;
        return bitmaps;
    }

    private synchronized void trimToSize(long size) {
        while (currentSize > size) {
            final Bitmap removed = strategy.removeLast();
            if (removed == null) {
                GLog.e(TAG, markLog("trimToSize mismatch, resetting"));
                dumpUnchecked();
                currentSize = 0;
                return;
            }
            onBitmapPop(removed);
            if (DEBUG) {
                GLog.d(TAG, markLog("trimToSize bitmap:" + strategy.logBitmap(removed) + ", currentSize:" + currentSize));
            }
            evictions++;
            dump();
            if (!removed.isRecycled()) {
                removed.recycle();
            }
        }
    }

    private void onBitmapPop(Bitmap bitmap) {
        tracker.remove(bitmap);
        currentSize -= strategy.getSize(bitmap);
        if ((lastOverIdleSizeTime > 0) && (currentSize <= maxIdleSize)) {
            lastOverIdleSizeTime = 0;
            idleCheckHandler.removeMessages(MSG_IDLE_CHECK);
        }
    }

    private void dump() {
        if (DEBUG) {
            dumpUnchecked();
        }
    }

    private void dumpUnchecked() {
        GLog.d(
                TAG,
                markLog("Hits="
                        + hits
                        + ", misses="
                        + misses
                        + ", puts="
                        + puts
                        + ", evictions="
                        + evictions
                        + ", currentSize="
                        + currentSize
                        + ", maxSize="
                        + maxSize
                        + "\nStrategy="
                        + strategy));
    }

    private static Set<Bitmap.Config> getDefaultAllowedConfigs() {
        Set<Bitmap.Config> configs = new HashSet<>(Arrays.asList(Bitmap.Config.values()));
        // GIFs, among other types, end up with a native Bitmap config that doesn't map to a java
        // config and is treated as null in java code. On KitKat+ these Bitmaps can be reconfigured
        // and are suitable for re-use.
        configs.add(null);
        configs.remove(Bitmap.Config.HARDWARE);
        return Collections.unmodifiableSet(configs);
    }

    private interface BitmapTracker {
        void add(Bitmap bitmap);

        void remove(Bitmap bitmap);
    }

    private static class ThrowingBitmapTracker implements BitmapTracker {

        private final String tag;

        ThrowingBitmapTracker(String tag) {
            this.tag = tag;
        }

        private final Set<Bitmap> bitmaps = Collections.synchronizedSet(new HashSet<>());

        @Override
        public void add(Bitmap bitmap) {
            if (bitmaps.contains(bitmap)) {
                throw new IllegalStateException(
                        "<" + tag + ">Can't add already added bitmap: "
                                + bitmap
                                + " ["
                                + bitmap.getWidth()
                                + "x"
                                + bitmap.getHeight()
                                + "]");
            }
            bitmaps.add(bitmap);
        }

        @Override
        public void remove(Bitmap bitmap) {
            if (!bitmaps.contains(bitmap)) {
                throw new IllegalStateException("<" + tag + ">Cannot remove bitmap not in tracker");
            }
            bitmaps.remove(bitmap);
        }
    }

    private String markLog(String msg) {
        return "<" + tag + ">" + msg;
    }

    private static final class NullBitmapTracker implements BitmapTracker {

        @Synthetic
        NullBitmapTracker() {
        }

        @Override
        public void add(Bitmap bitmap) {
            // Do nothing.
        }

        @Override
        public void remove(Bitmap bitmap) {
            // Do nothing.
        }
    }
}
