/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ProgressSeekbar.kt
 ** Description : 字幕滑动取值控件
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import com.coui.appcompat.seekbar.COUISeekBar
import com.oplus.gallery.videoeditorpage.common.ui.SuitableSizeG2TextView
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.widget.GallerySeekbar

/**
 * 字幕滑动取值控件
 */
class ProgressSeekbar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr), COUISeekBar.OnSeekBarChangeListener {

    /**
     * 设置进度条的的值范围
     */
    var progressValueRange: Pair<Int, Int> = Pair(0, DEFAULT_MAX_VALUE)
        set(value) {
            field = value
            seekBar.setMinData(value.first)
            seekBar.setMaxData(value.second)
        }

    /**
     * 滑动取值控件
     */
    private val seekBar: GallerySeekbar

    /**
     * 控件名称
     */
    private val progressNameTextView: SuitableSizeG2TextView

    /**
     * 控件值
     */
    private val progressValue: SuitableSizeG2TextView

    /**
     * 当前进度值
     */
    private var curProgress: Int = 0

    /**
     * 标记当前进度条是调增还是调减状态
     */
    private var isIncreasing: Boolean = false

    /**
     * 设置进度条显示名称
     */
    var progressName: String? = TextUtil.EMPTY_STRING
        set(value) {
            if (field != value) {
                field = value
                progressNameTextView.text = value
            }
        }

    /**
     * 进度条id标识
     */
    var progressId: Int = 0

    /**
     * 只展示进度值结果不回调变化，用于回显属性值使用
     */
    private var onlyShow: Boolean = true

    /**
     * 回调监听器
     */
    private var progressSeekbarChangedListener: ProgressSeekbarChangedListener? = null


    init {
        // 初始化UI
        val rootView = LayoutInflater.from(context).inflate(R.layout.videoeditor_caption_seekbar_item, this, true)
        if (isMiddleAndLargeWindow(context).not()) {
            val padding = resources.getDimensionPixelSize(R.dimen.videoeditor_caption_seekbar_padding_horizontal)
            rootView.setPadding(padding, 0, padding, 0)
        }
        seekBar = findViewById(R.id.caption_seekbar_progress)
        progressNameTextView = findViewById(R.id.caption_seekbar_name)
        progressValue = findViewById(R.id.caption_seekbar_value)
        // 初始化属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.VideoEditorProgressSeekBar)
        progressNameTextView.text = typedArray.getString(R.styleable.VideoEditorProgressSeekBar_videoeditor_progress_seekbar_name)
        val progress = typedArray.getInteger(R.styleable.VideoEditorProgressSeekBar_videoeditor_progress_seekbar_progress, 0)
        seekBar.setCurrentValue(progress)
        progressValue.text = "$progress"
        seekBar.setMinData(progressValueRange.first)
        seekBar.setMaxData(progressValueRange.second)
        // 设置默认监听器
        seekBar.setOnExtendSeekBarChangeListener(this)
        // 回收typedArray
        typedArray.recycle()
    }

    /**
     * 设置进度变化监听器
     */
    fun setOnCaptionSeekBarChangeListener(seekbarListener: ProgressSeekbarChangedListener?) {
        progressSeekbarChangedListener = seekbarListener
    }

    /**
     * 设置进度条从中间开始
     */
    fun startFromMiddle(isFromMiddle: Boolean) {
        seekBar.setStartFromMiddle(isFromMiddle)
    }

    override fun onProgressChanged(seekBar: COUISeekBar, progress: Int, fromUser: Boolean) {
        updateProgress(progress)
    }

    override fun onStartTrackingTouch(seekBar: COUISeekBar) {
        // 如果是用户操作控件改变设置值，则设置为true进行回调结果
        onlyShow = false
        progressSeekbarChangedListener?.onStartTouch(this, curProgress.toFloat(), isIncreasing)
    }

    override fun onStopTrackingTouch(seekBar: COUISeekBar) {
        progressSeekbarChangedListener?.onStopTouch(this, curProgress.toFloat(), isIncreasing)
    }

    /**
     * 更新进度并显示
     * @param progress 进度
     */
    private fun updateProgress(progress: Int) {
        isIncreasing = progress > curProgress
        curProgress = progress
        progressValue.text = "$progress"
        if (onlyShow.not()) {
            progressSeekbarChangedListener?.onChanged(this, progress.toFloat(), isIncreasing)
        }
    }

    /**
     * 设置进度
     */
    fun setProgress(progress: Int) {
        onlyShow = true
        seekBar.setCurrentValue(progress)
        updateProgress(progress)
    }

    /**
     * 销毁资源
     */
    fun onDestroy() {
        seekBar.setOnSeekBarChangeListener(null)
        progressSeekbarChangedListener = null
    }

    /**
     * seekbar进度值回调监听器
     */
    interface ProgressSeekbarChangedListener {
        /**
         * seekbar进度值回调
         *
         * @param seekBar 控件实例
         * @param progress 进度值
         */
        fun onChanged(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean)

        /**
         * 开始滑动时的回调
         *
         * @param seekBar 控件实例
         * @param progress 进度值
         */
        fun onStartTouch(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean)

        /**
         * 结束滑动时的回调
         *
         * @param seekBar 控件实例
         * @param progress 进度值
         */
        fun onStopTouch(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean)
    }

    companion object {
        private const val TAG = "ProgressSeekbar"

        /**
         * 进度条默认最大值
         */
        private const val DEFAULT_MAX_VALUE = 100
    }
}