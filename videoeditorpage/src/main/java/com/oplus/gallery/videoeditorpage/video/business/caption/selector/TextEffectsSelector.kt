/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextEffectsSelector.kt
 ** Description : 文字效果面板组件基类
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import android.view.View
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.ColorsAdapter
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.ColorItem
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.LinearSpacingItemDecoration
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.ProgressSeekbar

/**
 * 文字效果面板组件基类
 */
internal abstract class TextEffectsSelector(
    pageType: PageType
) : BaseSelector(pageType), ProgressSeekbar.ProgressSeekbarChangedListener {

    /**
     * 存放文字效果调节进度条控件
     */
    protected val  textEffectsSeekbar: MutableMap<TextEffectsType, ProgressSeekbar> = mutableMapOf()

    /**
     * 显示颜色的recyclerView
     */
    private var colorRecyclerView: RecyclerView? = null

    /**
     * 颜色适配器
     */
    private var colorAdapter: ColorsAdapter? = null


    override fun initViews() {
        initColorSelector()
        createProgressSeekbar()
    }

    /**
     * 更新进度值到UI控件
     */
    fun showProgressValue(effectsType: TextEffectsType, value: Int) {
        textEffectsSeekbar[effectsType]?.setProgress(value)
    }

    /**
     * 更新回显颜色至颜色选择器
     */
    fun showColorValue(colorHexString: String) {
        colorAdapter?.showColorValue(colorHexString)
    }

    /**
     * 添加子view到容器中
     */
    protected fun addView(subview: View) {
        container?.findViewById<LinearLayout>(R.id.text_effects_linearlayout_container)?.addView(subview)
    }

    /**
     * 获取文字调节效果类型配置
     */
    abstract fun getTextEffectsTypeConfig(): List<TextEffectsType>

    /**
     * 生成进度条控件实例
     */
    private fun createProgressSeekbar() {
        container?.let {
            getTextEffectsTypeConfig().forEach { textEffectsType ->
                val seekbar = ProgressSeekbar(it.context).apply {
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT
                    )
                    progressName = it.resources.getString(textEffectsType.nameResId)
                    progressId = textEffectsType.progressId
                    if (textEffectsType == TextEffectsType.ANGLE) {
                        // 单独设置阴影圆角的值和从中间开始往左右滑动
                        progressValueRange = Pair(ANGLE_MIN_VALUE, ANGLE_MAX_VALUE)
                        startFromMiddle(true)
                    } else if (textEffectsType == TextEffectsType.THICKNESS) {
                        // 单独设置描边粗细最大值
                        progressValueRange = Pair(THICKNESS_MIN_VALUE, THICKNESS_MAX_VALUE)
                    } else if (textEffectsType == TextEffectsType.BLUR) {
                        // 单独设置模糊值最大值最小值
                        progressValueRange = Pair(BLUR_MIN_VALUE, BLUR_MAX_VALUE)
                    }
                    setOnCaptionSeekBarChangeListener(this@TextEffectsSelector)
                }
                textEffectsSeekbar[textEffectsType] = seekbar
                addView(seekbar)
            }
        }
    }

    private fun initColorSelector() {
        container?.let {
            colorRecyclerView = it.findViewById<RecyclerView>(R.id.caption_color_recycler_view)?.apply {
                if (isMiddleAndLargeWindow(context).not()) {
                    // 设置padding
                    val padding = resources.getDimensionPixelSize(R.dimen.videoeditor_item_caption_color_horizontal_padding)
                    var paddingLeft = 0
                    var paddingRight = 0
                    if (ResourceUtils.isRTL(context)) {
                        paddingRight = padding
                    } else {
                        paddingLeft = padding
                    }
                    setPadding(paddingLeft, 0, paddingRight, 0)
                }
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                val itemHorizontalSpacing = resources.getDimensionPixelSize(R.dimen.videoeditor_item_caption_color_spacing)
                addItemDecoration(LinearSpacingItemDecoration(itemHorizontalSpacing, false))
                colorAdapter = ColorsAdapter(this).apply {
                    val colorItems = mutableListOf<ColorItem>()
                    resources.getStringArray(R.array.fontColor).forEach { hexColor ->
                        colorItems.add(ColorItem(hexColor))
                    }
                    setDataSource(colorItems)
                    onItemClickCallback = { v: View?, position: Int, data: ColorItem? ->
                        // 点击item回调
                        data?.let { colorItem ->
                            captionEffectsChangedListener?.colorChanged(pageType, colorItem.color)
                        }
                    }
                }
                adapter = colorAdapter
            }
        }
    }

    override fun onChanged(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) = Unit

    override fun onStartTouch(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) = Unit

    override fun onStopTouch(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) = Unit

    /**
     * 移除监听和回调
     */
    private fun removeListenerAndCallback() {
        textEffectsSeekbar.values.forEach { it.setOnCaptionSeekBarChangeListener(null) }
    }

    /**
     * 资源销毁
     */
    override fun destroy() {
        removeListenerAndCallback()
        textEffectsSeekbar.clear()
        super.destroy()
    }

    companion object {
        private const val TAG = "TextEffectsSelector"

        /**
         * 透明度id
         */
        const val TRANSPARENT_ID = 0

        /**
         * 粗细度id
         */
        const val THICKNESS_ID = 1

        /**
         * 模糊度id
         */
        const val BLUR_ID = 2

        /**
         * 角度id
         */
        const val ANGLE_ID = 3

        /**
         * 距离id
         */
        const val DISTANCE_ID = 4

        /**
         * 圆角角度id
         */
        const val CORNERS_ID = 5

        /**
         * 边距id
         */
        const val MARGIN_ID = 6

        /**
         * 最小角度值，用于设置阴影角度
         */
        private const val ANGLE_MIN_VALUE = -180

        /**
         * 最大角度值。用于设置阴影角度
         */
        private const val ANGLE_MAX_VALUE = 180

        /**
         * 最小描边粗细
         */
        private const val THICKNESS_MIN_VALUE = 0

        /**
         * 最大描边粗细
         */
        private const val THICKNESS_MAX_VALUE = 50

        /**
         * 最小阴影模糊值
         */
        private const val BLUR_MIN_VALUE = 0

        /**
         * 最大阴影模糊值
         */
        private const val BLUR_MAX_VALUE = 50
    }
}

/**
 * 进度条类型
 *
 * @param progressId 文字调节效果进度值id
 * @param nameResId 调节效果名称资源id
 */
enum class TextEffectsType(
    val progressId: Int,
    val nameResId: Int
) {
    /**
     * 透明度
     */
    TRANSPARENT(TextEffectsSelector.TRANSPARENT_ID, R.string.videoeditor_caption_text_transparent),

    /**
     * 粗细度
     */
    THICKNESS(TextEffectsSelector.THICKNESS_ID, R.string.videoeditor_caption_text_thickness),

    /**
     * 模糊度
     */
    BLUR(TextEffectsSelector.BLUR_ID, R.string.videoeditor_caption_text_blur),

    /**
     * 角度
     */
    ANGLE(TextEffectsSelector.ANGLE_ID, R.string.videoeditor_caption_text_angle),

    /**
     * 距离
     */
    DISTANCE(TextEffectsSelector.DISTANCE_ID, R.string.videoeditor_caption_text_distance),

    /**
     * 圆角角度
     */
    CORNERS(TextEffectsSelector.CORNERS_ID, R.string.videoeditor_caption_text_corners),

    /**
     * 边距
     */
    MARGIN(TextEffectsSelector.MARGIN_ID, R.string.videoeditor_caption_text_margin)
}
