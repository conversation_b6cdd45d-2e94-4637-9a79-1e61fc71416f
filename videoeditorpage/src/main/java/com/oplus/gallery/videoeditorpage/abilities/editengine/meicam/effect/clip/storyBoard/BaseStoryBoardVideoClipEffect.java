/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseStoryBoardVideoClipEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard;

import androidx.annotation.IntDef;

import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.MeicamVideoClipEffect;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.HashMap;
import java.util.Map;

public abstract class BaseStoryBoardVideoClipEffect extends MeicamVideoClipEffect {

    public final static int INVALID_INT = -1;

    @IntDef({StoryboardType.TYPE_CARTOON, StoryboardType.TYPE_BACKGROUND})
    @Retention(RetentionPolicy.SOURCE)
    public @interface StoryboardType {
        int TYPE_CARTOON = StreamingConstant.ClipEffectType.TYPE_CARTOON;
        int TYPE_BACKGROUND = StreamingConstant.ClipEffectType.TYPE_BACKGROUND;
    }

    @IntDef({BackgroundStoryType.TYPE_COLOR, BackgroundStoryType.TYPE_PIC, BackgroundStoryType.TYPE_BLUR})
    @Retention(RetentionPolicy.SOURCE)
    public @interface BackgroundStoryType {
        int TYPE_NUll = -1;
        int TYPE_COLOR = 1;
        int TYPE_PIC = 2;
        int TYPE_BLUR = 3;
    }

    protected int mPlayType;
    protected String mDirPath;
    protected String mSourceFileName;
    protected String mXmlFilePath;
    protected float mTimelineRatio;
    protected long mDuration;
    protected float mEffectStrength = 1.0F;
    protected HashMap<String, Float> mLocalFloatParams;
    protected Map<String, Object> mExtendsData;

    public BaseStoryBoardVideoClipEffect(String dirPath, int playType, String xmlPath, float ratio, long duration, Map<String, Object> extendsData) {
        super(StreamingConstant.StoryBoard.STORYBOARD_NAME, BaseVideoClipEffect.TYPE_BUILT_IN_FX);
        mPlayType = playType;
        mDirPath = dirPath;
        mXmlFilePath = xmlPath;
        mTimelineRatio = ratio;
        mDuration = duration;
        mLocalFloatParams = new HashMap<>();
        mExtendsData = extendsData;
    }

    public abstract BaseVideoClipEffect buildEffectParam(String sourceDir, String sourcePath, float strength,
                                                         int width, int height, int imageView,
                                                         int imageHeight, long clipDuration, int clipRotation,
                                                         int type, boolean hasKeyframe);

    public abstract String getBlurDescription(String keyframeDescription, int width, int height, int imageWidth, int imageHeight, int clipRotation);

    public long getDuration() {
        return mDuration;
    }

    public boolean setDuration(long duration, long clipDuration) {
        mDuration = duration;
        return true;
    }

    public String getDirPath() {
        return mDirPath;
    }

    public void setDirPath(String dirPath) {
        mDirPath = dirPath;
    }

    public String getXmlFilePath() {
        return mXmlFilePath;
    }

    public void setXmlFilePath(String xmlFilePath) {
        mXmlFilePath = xmlFilePath;
    }

    public int getPlayType() {
        return mPlayType;
    }

    public float getTimelineRatio() {
        return mTimelineRatio;
    }

    public void setLocalFloatValue(String paramName, float value) {
        if (mLocalFloatParams.containsKey(paramName)) {
            mLocalFloatParams.remove(paramName);
        }
        mLocalFloatParams.put(paramName, value);
    }

    public float getLocalFloatValue(String paramName) {
        if (mLocalFloatParams.containsKey(paramName)) {
            return mLocalFloatParams.get(paramName);
        }
        return 0;
    }
    public HashMap<String, Float> getLocalFloatParams() {
        return (HashMap<String, Float>) mLocalFloatParams.clone();
    }
    public String getSourceFileName() {
        return mSourceFileName;
    }

    public void setSourceFileName(String sourceFileName) {
        this.mSourceFileName = sourceFileName;
    }

    public float getEffectStrength() {
        return mEffectStrength;
    }

    public void setEffectStrength(float effectStrength) {
        this.mEffectStrength = effectStrength;
    }

    public void setSubType(int subType) {

    }

    public int getSubType() {
        return INVALID_INT;
    }

    public void setIndex(int index) {

    }

    public int getIndex() {
        return INVALID_INT;
    }
}
