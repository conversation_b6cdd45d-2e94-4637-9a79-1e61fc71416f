/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SceneCrashInfo.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import android.os.Parcel
import android.os.Parcelable
import java.io.Serializable

data class SceneCrashInfo(
    var scene /* 场景名 */: String? = null,
    var carriedMsg /* 携带的信息 */: String? = null,
    var crashTime /* 崩溃时间 */: Long = 0,
    var backtrace /* 堆栈，预留字段，不一定能获得 */: String? = null,
    var strerror /* 错误代码转换成对应的文本信息 */: String? = null,
    var si_signo /* Signal number */: Int = 0,
    var si_errno /* An errno value */: Int = 0,
    var si_code /* Signal code */: Int = 0,
    var si_tid: Int = 0,
    var si_overrun: Int = 0,
    var si_pid /* Sending process ID */: Int = 0,
    var si_uid /* Real user ID of sending process */: Int = 0,
    var si_status /* Exit value or signal */: Int = 0,
    var si_band /* Band event */: Long = 0,
    var si_fd: Int = 0
) : Parcelable, Serializable {

    constructor(parcel: Parcel) : this() {
        scene = parcel.readString()
        carriedMsg = parcel.readString()
        crashTime = parcel.readLong()
        backtrace = parcel.readString()
        strerror = parcel.readString()
        si_signo = parcel.readInt()
        si_errno = parcel.readInt()
        si_code = parcel.readInt()
        si_tid = parcel.readInt()
        si_overrun = parcel.readInt()
        si_pid = parcel.readInt()
        si_uid = parcel.readInt()
        si_status = parcel.readInt()
        si_band = parcel.readLong()
        si_fd = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(scene)
        parcel.writeString(carriedMsg)
        parcel.writeLong(crashTime)
        parcel.writeString(backtrace)
        parcel.writeString(strerror)
        parcel.writeInt(si_signo)
        parcel.writeInt(si_errno)
        parcel.writeInt(si_code)
        parcel.writeInt(si_tid)
        parcel.writeInt(si_overrun)
        parcel.writeInt(si_pid)
        parcel.writeInt(si_uid)
        parcel.writeInt(si_status)
        parcel.writeLong(si_band)
        parcel.writeInt(si_fd)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SceneCrashInfo> {

        private const val serialVersionUID = 0L

        override fun createFromParcel(parcel: Parcel): SceneCrashInfo {
            return SceneCrashInfo(parcel)
        }

        override fun newArray(size: Int): Array<SceneCrashInfo?> {
            return arrayOfNulls(size)
        }
    }
}