/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  ReplaceVideoClipView
 ** Description: 替换视频控件
 ** Version: 1.0
 ** Date : 2025/7/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/7/8      1.0     NEW
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.replace.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1f
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewFactory
import com.oplus.gallery.videoeditorpage.video.business.track.view.VideoClipExtractView
import com.oplus.gallery.videoeditorpage.widget.ScrollDetectorHScrollView

/**
 * 替换视频控件
 */
class ReplaceVideoClipView(context: Context, attrs: AttributeSet?) : RelativeLayout(context, attrs) {
    /** 视频片段滚动监听器 */
    var onClipViewScrollListener: OnClipViewScrollListener? = null

    /** 滚动控件 */
    private var scrollView: ScrollDetectorHScrollView = ScrollDetectorHScrollView(context)

    /** 轨道的蒙层 */
    private val maskView = ReplaceVideoMaskView(context)
    /**
     * 时间线定位图标
     */
    private val timeLineSpanImageView: ImageView by lazy { ImageView(context) }
    /**
     * 视频片段缩略图控件
     */
    private val clipExtractView = VideoClipExtractView(context)
    /**
     * 添加内容区域控件
     */
    private val contentLayout = FrameLayout(context)

    /**  是否从右向左绘制 */
    private val isRTL = ResourceUtils.isRTL(ContextGetter.context)
    /**   轨道顶部的间距 */
    private val trackPaddingTop = context.resources.getDimensionPixelSize(R.dimen.videoeditor_track_timeline_axis_offset)

    init {
        scrollView = ScrollDetectorHScrollView(context).apply {
            isHorizontalScrollBarEnabled = true
            overScrollMode = View.OVER_SCROLL_NEVER // 禁用弹性滚动
            onScrollListener = object : ScrollDetectorHScrollView.OnScrollListener {
                override fun onDown(scrollX: Int) {
                    onClipViewScrollListener?.onDown(scrollX)
                }

                override fun onStop(scrollX: Int) {
                    onClipViewScrollListener?.onStop(scrollX)
                }

                override fun onMove(scrollX: Int) {
                    val rightMaskWidth: Float =
                        (maskView.width - getBorderViewWidth() - scrollX).toFloat()
                    maskView.setMaskWidth(scrollX.toFloat(), rightMaskWidth)
                    clipExtractView.invalidate()
                    onClipViewScrollListener?.onMove(scrollX)
                }
            }
        }

        val scrollViewLayoutParams = LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT
        )
        addView(scrollView, scrollViewLayoutParams)
    }

    /**
     * 设置尺码线的播放进度
     *
     * @param progress 播放进度，范围为0.0到1.0，表示播放位置的百分比
     */
    fun setTimelineSpanImageViewPlayProgress(progress: Float) {
        val layoutParams = timeLineSpanImageView.layoutParams as? LayoutParams ?: return
        val screenWidth = context.resources.displayMetrics.widthPixels
        val borderViewWidth = getBorderViewWidth()
        val spanWidthPx = ScreenUtils.dpToPixel(TIMELINE_SPAN_WIDTH_DP)
        layoutParams.marginStart = (progress * borderViewWidth).toInt() + (screenWidth - borderViewWidth) / NUMBER_2 - spanWidthPx
        timeLineSpanImageView.requestLayout()
    }

    /**
     * 设置视频片段模型信息
     */
    fun setClipModel(clipModel: ClipModel, editVideoClipDuration: Long) {
        val clipExtractViewHeight = ClipViewFactory.getClipHeight(clipModel.isInMainTrack)
        val contentHeight = clipExtractViewHeight + (trackPaddingTop * NUMBER_2)

        // 设置内容布局内边距
        setContentLayoutPadding(context.resources.displayMetrics.widthPixels)

        val containerParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT, contentHeight
        )
        contentLayout.layoutParams = containerParams
        scrollView.addView(contentLayout)

        //添加视频提取视图
        val borderWidth = getBorderViewWidth()

        // 视频提取视图相对中间选择框的比例
        val clipToBorderWidthRatio = NUMBER_1f * clipModel.adjustedDuration / editVideoClipDuration
        val clipExtractViewWidth: Int = (clipToBorderWidthRatio * borderWidth).toInt()
        val clipExtractViewLayoutParams = LayoutParams(clipExtractViewWidth, clipExtractViewHeight).apply {
            marginEnd = calculateBorderHorizontalPadding(context.resources.displayMetrics.widthPixels)
        }
        clipExtractView.setClipInfo(clipModel.filePath, clipToBorderWidthRatio)
        contentLayout.addView(clipExtractView, clipExtractViewLayoutParams)

        //添加轨道的蒙层
        val maskViewLayoutParams = LinearLayout.LayoutParams(
            clipExtractViewWidth, clipExtractViewHeight
        )
        contentLayout.addView(maskView, maskViewLayoutParams)
        val rightMaskWidth = (clipExtractViewWidth - getBorderViewWidth()).toFloat()
        maskView.setMaskWidth(0f, rightMaskWidth)

        //添加中心的白色选择边框控件
        val selectBorderView = View(context)
        selectBorderView.setBackgroundResource(R.drawable.videoeditor_replace_clip_select_border)
        val borderViewParams = LayoutParams(
            borderWidth, clipExtractViewHeight
        ).apply {
            addRule(CENTER_HORIZONTAL)
            topMargin = trackPaddingTop
        }
        selectBorderView.layoutParams = borderViewParams
        addView(selectBorderView)

        //设置尺码线图标
        addTimelineSpanImageView(contentHeight)
    }

    /**
     * 请求视图位置更新
     */
    fun requestLayout(playProgress: Float?, windowWidth: Int) {
        val clipExtractViewLayoutParams = (clipExtractView.layoutParams as? FrameLayout.LayoutParams)
            .getOrLog("the layoutParams of clipExtractView is null or not FrameLayout.LayoutParams") ?: return
        val borderHorizontalPadding = calculateBorderHorizontalPadding(windowWidth)

        clipExtractViewLayoutParams.marginEnd = borderHorizontalPadding

        setContentLayoutPadding(windowWidth)
        if (maskView.width > 0) {
            val borderViewWidth = getBorderViewWidth()
            val rightMaskWidth: Float = (maskView.width - borderViewWidth - scrollX).toFloat()
            maskView.setMaskWidth(scrollX.toFloat(), rightMaskWidth)
        }

        playProgress?.let {
            setTimelineSpanImageViewPlayProgress(it)
        }
        val contentLayoutParams = (contentLayout.layoutParams as? FrameLayout.LayoutParams)
            .getOrLog("the layoutParams of contentLayout is null or not FrameLayout.LayoutParams") ?: return
        contentLayoutParams.width = clipExtractView.layoutParams.width + borderHorizontalPadding * NUMBER_2
        contentLayout.layoutParams = contentLayoutParams
    }

    /**
     * 计算白框居中时的水平间距
     */
    private fun calculateBorderHorizontalPadding(windowWidth: Int): Int {
        return (windowWidth - getBorderViewWidth()) / NUMBER_2
    }

    /**
     * 设置内容布局的内边距
     */
    private fun setContentLayoutPadding(windowWidth: Int) {
        val horizontalPadding = calculateBorderHorizontalPadding(windowWidth)
        val containerLeftPadding = if (isRTL) 0 else horizontalPadding
        val containerRightPadding = if (isRTL) horizontalPadding else 0
        contentLayout.setPadding(containerLeftPadding, trackPaddingTop, containerRightPadding, 0)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        onClipViewScrollListener = null
    }

    /**
     * 添加尺码线图标
     */
    private fun addTimelineSpanImageView(contentHeight:  Int) {
        timeLineSpanImageView.setImageResource(R.drawable.videoeditor_timeline_axis)
        timeLineSpanImageView.isForceDarkAllowed = false
        val layoutParams = LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            contentHeight
        )
        addView(timeLineSpanImageView, layoutParams)
        setTimelineSpanImageViewPlayProgress(0f)
    }

    /**
     * 获取边框视图的宽度
     */
    private fun getBorderViewWidth(): Int {
        return ScreenUtils.dpToPixel(BORDER_VIEW_WIDTH_DP)
    }

    companion object {
        //选择边框的宽度
        const val BORDER_VIEW_WIDTH_DP = 160
        // 时码线的宽度
        private const val TIMELINE_SPAN_WIDTH_DP = 2
    }
}

/**
 * 视频片段视图滚动监听器
 */
interface OnClipViewScrollListener {
    /**
     * 按下回调
     * @param scrollX 当前滚动的水平位置
     */
    fun onDown(scrollX: Int)

    /**
     * 当正在滚动时调用
     *
     * @param scrollX 当前滚动的水平位置
     */
    fun onMove(scrollX: Int)

    /**
     *  滚动停止回调
     * @param scrollX 当前滚动位置
     */
    fun onStop(scrollX: Int)
}
