/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/7/13
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/7/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.autodownload;

public class RtnCode {
    public static final int NETWORK_SUCCESS = 0;
    public static final int BUILTIN_SUCCESS = 1;

    /**
     * 网络能力不存在
     */
    public static final int DOWNLOAD_ABILITY_NOT_EXIST = -1;

    public interface Base {
        int CODE_MUSIC = 0;
        int CODE_TEMPLATE = 1 << 5;
        int CODE_FILTER = CODE_TEMPLATE * 2;
        int CODE_TRANSITION = CODE_TEMPLATE * 3;
    }

    public interface Music {
        int SUCCESS = Base.CODE_MUSIC + 1; //1
        int AT_INTERVALS = Base.CODE_MUSIC + 2; //2
        int DOWNLOADING = Base.CODE_MUSIC + 3; //3
        int ALREADY_DOWNLOADED = Base.CODE_TEMPLATE + 4; //4
    }

    public interface Theme {
        int SUCCESS = Base.CODE_TEMPLATE + 1; //33
        int AT_INTERVALS = Base.CODE_TEMPLATE + 2; //34
        int DOWNLOADING = Base.CODE_TEMPLATE + 3; //35
        int ALREADY_DOWNLOADED = Base.CODE_TEMPLATE + 4; //36
    }
}