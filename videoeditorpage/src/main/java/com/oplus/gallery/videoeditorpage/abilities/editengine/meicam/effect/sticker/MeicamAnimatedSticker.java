/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamAnimatedSticker.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.sticker;

import android.graphics.PointF;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsTimelineAnimatedSticker;

import java.util.ArrayList;
import java.util.List;

public class MeicamAnimatedSticker extends BaseSticker {

    private static final String TAG = "MeicamAnimatedSticker";
    public static final String JSON_TYPE_NAME = "animated sticker";
    public static final float DEFAULT_SCALE = 1;

    protected transient NvsTimelineAnimatedSticker mNvsAnimatedSticker;

    @SerializedName("class_type")
    private String mEffectNameForSave = JSON_TYPE_NAME;// use to mark the type in saved json file
    @SerializedName("trans_x")
    private float mTranslationX;
    @SerializedName("trans_y")
    private float mTranslationY;
    @SerializedName("scale")
    private float mScale = DEFAULT_SCALE;
    @SerializedName("rotation")
    private float mRotationAngle = 0;
    @SerializedName("clip_affinity_enable")
    private boolean mClipAffinityEnabled = true;

    public MeicamAnimatedSticker(String name) {
        super(name);
    }

    public NvsTimelineAnimatedSticker getNvsAnimatedSticker() {
        return mNvsAnimatedSticker;
    }

    public void setNvsAnimatedSticker(NvsTimelineAnimatedSticker nvsAnimatedSticker) {
        mNvsAnimatedSticker = nvsAnimatedSticker;
    }

    @Override
    protected MeicamAnimatedSticker clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamAnimatedSticker result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamAnimatedSticker(getName());
        }
        return result;
    }

    public String getInstalledName() {
        return mName;
    }

    @Override
    public void setOutTime(long outTime) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.changeOutPoint(outTime);
        }
        super.setOutTime(outTime);
    }

    @Override
    public void setInTime(long inTime) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.changeInPoint(inTime);
        }
        super.setInTime(inTime);
    }

    @Override
    public void translateStcker(PointF translation) {
        if (translation == null) {
            return;
        }

        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.translateAnimatedSticker(translation);
            PointF pointF = mNvsAnimatedSticker.getTranslation();
            if (pointF != null) {
                mTranslationX = pointF.x;
                mTranslationY = pointF.y;
            }
        }
        GLog.d(TAG, "translateCaption: (" + mTranslationX + "," + mTranslationY + ")");
    }

    @Override
    public void setTranslation(PointF translation) {
        if (translation == null) {
            return;
        }
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.setTranslation(translation);
        }
        mTranslationX = translation.x;
        mTranslationY = translation.y;
        GLog.d(TAG, "setTranslation: (" + mTranslationX + "," + mTranslationY + ")");
    }

    @Override
    public PointF getTranslation() {
        return new PointF(mTranslationX, mTranslationY);
    }

    @Override
    public void scaleSticker(float scaleFactor, PointF anchor) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.scaleAnimatedSticker(scaleFactor, anchor);
        }
        resetPositionVariablesByNvs();
        setAnchor(anchor);
    }

    @Override
    public void rotateSticker(float angle, PointF anchor) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.rotateAnimatedSticker(angle, anchor);
        }
        mRotationAngle += angle;
        setAnchor(anchor);
    }

    @Override
    public void setScale(float scale) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.setScale(scale);
        }
        mScale = scale;
    }

    @Override
    public float getScale() {
        return mScale;
    }

    @Override
    public float getRotation() {
        return mRotationAngle;
    }

    @Override
    public void setRotation(float rotation) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.setRotationZ(rotation);
        }
        mRotationAngle = rotation;
    }

    @Override
    public List<PointF> getBoundingRectangleVertices() {
        if (mNvsAnimatedSticker != null) {
            return mNvsAnimatedSticker.getBoundingRectangleVertices();
        }
        return new ArrayList<>();
    }

    @Override
    public void setZValue(float zValue) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.setZValue(zValue);
        }
    }

    @Override
    public float getZValue() {
        if (mNvsAnimatedSticker != null) {
            return mNvsAnimatedSticker.getZValue();
        }
        return 0;
    }

    public void setClipAffinityEnabled(boolean enabled) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.setClipAffinityEnabled(enabled);
        }
        mClipAffinityEnabled = enabled;
    }

    public boolean getClipAffinityEnabled() {
        return mClipAffinityEnabled;
    }

    public Object getAttachment(String key) {
        if (mNvsAnimatedSticker != null) {
            return mNvsAnimatedSticker.getAttachment(key);
        }
        return null;
    }

    public void setAttachment(String key, Object value) {
        if (mNvsAnimatedSticker != null) {
            mNvsAnimatedSticker.setAttachment(key, value);
        }
    }

    /**
     * changes is stepping in Meicam SDK, need reset variables after change
     */
    private void resetPositionVariablesByNvs() {
        if (mNvsAnimatedSticker == null) {
            return;
        }
        mScale = mNvsAnimatedSticker.getScale();
        PointF translation = mNvsAnimatedSticker.getTranslation();
        if (translation != null) {
            mTranslationX = translation.x;
            mTranslationY = translation.y;
        }
        mRotationAngle = mNvsAnimatedSticker.getRotationZ();
        GLog.d(TAG, "resetPositionVariablesByNvs: scale(" + mScale + "),translation("
                + mTranslationX + "," + mTranslationY + "),angle(" + mRotationAngle + ")");
    }

    @Override
    public void setTrackIndex(int mTrackIndex) {
        super.setTrackIndex(mTrackIndex);
        setZValue(mTrackIndex);
    }
}
