/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LnSActionMenu.kt
 ** Description : 抠图菜单封装实现
 ** Version     : 1.0
 ** Date        : 2023/07/20
 ** Author      : <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2023/07/20      1.0
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.liftandshift

import android.graphics.Rect
import android.view.ActionMode
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.annotation.IdRes
import androidx.annotation.MainThread
import androidx.core.content.ContextCompat
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.photo_page.R
import kotlin.math.abs

/**
 * 抠图菜单的监听回调：
 * 1. 点击事件回调
 * 2. 菜单创建回调
 * 3. 菜单销毁回调
 */
interface LnSActionMenuListener {
    fun onMenuItemClick(@IdRes itemId: Int): Boolean
    fun onActionMenuCreate(lnSActionMenu: LnSActionMenu, menu: Menu)
    fun onActionMenuDestroy(mode: ActionMode)
}

/**
 * 抠图菜单实现，封装了ActionMode，实际底层实现是LocalFloatWindowToolbar
 * @param parentView
 * @param lnsActionMenuListener
 */
class LnSActionMenu(
    private val parentView: View,
    private var lnsActionMenuListener: LnSActionMenuListener
) {
    /**
     * 当前抠图view的容器位置, 相对于屏幕坐标系
     */
    private val pageContentRect: Rect = Rect()

    /**
     * 框起选中主题的矩形位置，相对于屏幕坐标系
     */
    private val selectedSubsContentRect: Rect = Rect()

    private var actionMode: ActionMode? = null

    /**
     * 菜单显示模式
     */
    private var menuType: LnSMenuType = LnSMenuType.DEFAULT_TYPE

    private var copyMenuItem: MenuItem? = null
    private var shareMenuItem: MenuItem? = null

    private val actionModeCallback = object : ActionMode.Callback2() {
        /**
         * 顶部的最小安全区域，当主体内容区域到大图页的顶部区域距离大于此安全距离时，才能保证菜单安全显示在主体内容区域上方
         */
        private val minTopSecureSpace by lazy {
            parentView.context.resources.getDimension(R.dimen.photopage_lns_subject_top_secure_space)
        }

        /**
         * 底部的最小安全区域，当主体内容区域到大图页的底部区域距离大于此安全距离时，才能保证菜单安全显示在主体内容区域下方
         */
        private val minBottomSecureSpace by lazy {
            parentView.context.resources.getDimension(R.dimen.photopage_lns_subject_bottom_secure_space)
        }

        override fun onCreateActionMode(mode: ActionMode, menu: Menu): Boolean {
            val inflater: MenuInflater = mode.menuInflater
            inflater.inflate(R.menu.photo_page_lns_menu, menu)
            lnsActionMenuListener.onActionMenuCreate(this@LnSActionMenu, menu)
            copyMenuItem = menu.findItem(R.id.lns_menu_action_copy)
            shareMenuItem = menu.findItem(R.id.lns_menu_action_share)
            setMenuDisplay(menuType)
            return true
        }

        override fun onPrepareActionMode(mode: ActionMode, menu: Menu): Boolean {
            return false
        }

        override fun onActionItemClicked(mode: ActionMode, item: MenuItem): Boolean {
            return lnsActionMenuListener.onMenuItemClick(item.itemId)
        }

        override fun onDestroyActionMode(mode: ActionMode) {
            lnsActionMenuListener.onActionMenuDestroy(mode)
        }

        /**
         * 当ActionMode调用[ActionMode.invalidateContentRect]时，会回掉到这里，获取主体的内容区域[outRect]
         * 视觉给定了菜单位置的规则：
         * 1.水平方向上左右空间不足，沿用系统规则控制菜单贴着屏幕边缘显示
         * 2.竖直方向上需要进行手动调整，如果上方下方的空间都不够，需要居中显示；如果只有上方/下方安全空间不够，则显示菜单在下方/上方
         * 3.针对多主体的情况，将多个选中主体合成的矩形区域视为一个主体，沿用上面的1、2的规则
         */
        override fun onGetContentRect(mode: ActionMode, view: View, outRect: Rect) {
            val curSubsTopSpace = abs(pageContentRect.top - selectedSubsContentRect.top)
            val curSubsBottomSpace = abs(pageContentRect.bottom - selectedSubsContentRect.bottom)

            val topSafe = (curSubsTopSpace >= minTopSecureSpace)
            val bottomSafe = (curSubsBottomSpace >= minBottomSecureSpace)

            if (topSafe.not() && bottomSafe.not()) {
                outRect.set(
                    selectedSubsContentRect.centerX(),
                    selectedSubsContentRect.centerY(),
                    selectedSubsContentRect.centerX(),
                    selectedSubsContentRect.centerY(),
                )
            } else if (topSafe.not() && bottomSafe) {
                outRect.set(
                    selectedSubsContentRect.left,
                    pageContentRect.top,
                    selectedSubsContentRect.right,
                    selectedSubsContentRect.bottom
                )
            } else if (topSafe && bottomSafe.not()) {
                outRect.set(
                    selectedSubsContentRect.left,
                    selectedSubsContentRect.top,
                    selectedSubsContentRect.right,
                    pageContentRect.bottom
                )
            } else {
                outRect.set(selectedSubsContentRect)
            }
            if (GProperty.DEBUG_LNS) {
                GLog.d(TAG) {
                    "[onGetContentRect] print menu position arg:\n" +
                            "outRect=$outRect, selectedSubsContentRect=$selectedSubsContentRect, pageContentRect=$pageContentRect"
                }
            }
        }
    }

    /**
     * 拉起菜单进行展示
     * @param currentPageContentRect 当前抠图view的容器位置
     * @param curSelectedSubsContentRect 选中主体合成后包围矩形的位置rect
     * @param selectAllMenuVisible "全选" 菜单是否可见
     */
    @MainThread
    fun show(currentPageContentRect: Rect, curSelectedSubsContentRect: Rect, selectAllMenuVisible: Boolean) {
        if (curSelectedSubsContentRect.isEmpty) {
            GLog.e(TAG) { "[show] curSelectedSubsContentRect=$curSelectedSubsContentRect, cancel show menu" }
            return
        }
        menuType = if (selectAllMenuVisible) {
            LnSMenuType.CUSTOM_STICKER_MULTI_LNS_RESULT_TYPE
        } else {
            LnSMenuType.DEFAULT_TYPE
        }
        pageContentRect.set(currentPageContentRect)
        selectedSubsContentRect.set(curSelectedSubsContentRect)
        show()
    }

    /**
     * 直接使用之前设置的位置坐标信息展示菜单
     */
    @MainThread
    fun show() {
        actionMode?.finish()
        // 通过触发[onGetContentRect]回调，进行菜单的定位
        actionMode = parentView.startActionMode(actionModeCallback, ActionMode.TYPE_FLOATING)
    }

    /**
     * 是否正在展示菜单
     */
    fun isShowing(): Boolean {
        return actionMode != null
    }

    /**
     * 更新已经展示菜单的位置
     * @param currentPageContentRect 当前抠图view的容器位置
     * @param curSelectedSubsContentRect 选中主体合成后包围矩形的位置rect
     * @param hideDuration 菜单隐藏动画时长
     */
    @MainThread
    fun updateShowLocation(currentPageContentRect: Rect, curSelectedSubsContentRect: Rect, hideDuration: Long = 0L) {
        if (curSelectedSubsContentRect.isEmpty) {
            GLog.e(TAG) { "[updateShowLocation] curSelectedSubsContentRect=$curSelectedSubsContentRect, cancel update menu" }
            return
        }

        pageContentRect.set(currentPageContentRect)
        selectedSubsContentRect.set(curSelectedSubsContentRect)

        actionMode?.also {
            it.hide(hideDuration)
            // 将会触发[onGetContentRect]回调，进行菜单的重新定位
            it.invalidateContentRect()
        } ?: run {
            GLog.e(TAG) { "[updateShowLocation] update location fail, cause action mode is null" }
        }
    }

    /**
     * 更新菜单某项的可见性
     * @param selectAllMenuVisible "全选" 菜单是否可见
     * @param menu “全选”所依附的菜单
     */
    fun updateMenu(selectAllMenuVisible: Boolean, menu: Menu? = actionMode?.menu) {
        if (menu == null) {
            GLog.w(TAG) { "[updateMenu] menu is null" }
            return
        }
        menu.findItem(R.id.lns_menu_action_select_all)?.isVisible = selectAllMenuVisible
        menuType = if (selectAllMenuVisible) {
            LnSMenuType.CUSTOM_STICKER_MULTI_LNS_RESULT_TYPE
        } else {
            LnSMenuType.DEFAULT_TYPE
        }
        setMenuDisplay(menuType)
    }

    /**
     * 隐藏菜单
     * @param hideDuration 菜单隐藏时长
     */
    fun dismiss(hideDuration: Long = 0L) {
        actionMode?.hide(hideDuration)
        actionMode?.finish()
        actionMode = null
    }

    /**
     * 根据类型设置菜单显示
     * @param lnSMenuType 菜单显示类型
     */
    private fun setMenuDisplay(lnSMenuType: LnSMenuType) {
        when (lnSMenuType) {
            LnSMenuType.DEFAULT_TYPE -> {
                //15.0会显示MenuItem.SHOW_AS_ACTION_IF_ROOM的icon，所以将icon设为null。
                if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)) {
                    copyMenuItem?.icon = null
                    shareMenuItem?.icon = null
                }
                if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_16_0_0)) {
                    shareMenuItem?.setIcon(ContextCompat.getDrawable(parentView.context, R.drawable.photopage_ic_menu_share))
                }
                copyMenuItem?.setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM)
                shareMenuItem?.setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM)
            }

            LnSMenuType.CUSTOM_STICKER_MULTI_LNS_RESULT_TYPE -> {
                if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)) {
                    copyMenuItem?.icon = ContextCompat.getDrawable(parentView.context, R.drawable.ic_photo_menu_copy)
                    shareMenuItem?.icon = ContextCompat.getDrawable(parentView.context, R.drawable.photopage_ic_menu_share)
                }
                copyMenuItem?.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
                shareMenuItem?.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    companion object {
        private const val TAG = "LnSActionMenu"
    }
}

/**
 * 抠图菜单显示类型
 */
enum class LnSMenuType {
    /**
     * 默认显示类型，所有menu选项平铺
     */
    DEFAULT_TYPE,

    /**
     * 自定义贴纸且有多主体类型
     */
    CUSTOM_STICKER_MULTI_LNS_RESULT_TYPE
}