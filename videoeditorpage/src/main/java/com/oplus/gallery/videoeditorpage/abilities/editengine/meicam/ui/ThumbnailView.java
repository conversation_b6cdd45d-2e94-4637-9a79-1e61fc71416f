/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ThumbnailView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import com.meicam.sdk.NvsThumbnailView;

public class ThumbnailView extends RelativeLayout {
    private final String TAG = "ThumbnailView";

    private NvsThumbnailView mThumbnailView;

    public ThumbnailView(Context context) {
        super(context);
        init(context);
    }

    public ThumbnailView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public ThumbnailView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    public ThumbnailView(Context context, AttributeSet attrs, int defStyle, int defStyleRes) {
        super(context, attrs, defStyle, defStyleRes);
        init(context);
    }

    private void init(Context context) {
        mThumbnailView = new NvsThumbnailView(context);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        addView(mThumbnailView, layoutParams);
    }

    public void setMediaFilePath(String mediaFilePath) {
        mThumbnailView.setMediaFilePath(mediaFilePath);
    }

    public void setMediaFilePath(String mediaFilePath, long timeStamp) {
        mThumbnailView.setMediaFilePath(mediaFilePath, timeStamp);
    }


    public String getMediaFilePath() {
        return mThumbnailView.getMediaFilePath();
    }
}