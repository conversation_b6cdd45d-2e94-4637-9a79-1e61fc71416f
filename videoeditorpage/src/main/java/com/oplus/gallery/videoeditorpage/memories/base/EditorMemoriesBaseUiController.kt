/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorMemoriesBaseUiController.kt
 * Description:
 * Version: 1.0
 * Date: 2022/5/15
 * Author: Luya<PERSON>.Tan@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Luyao.Tan@Apps.Gallery3D 2022/5/15     1.0              create
 **************************************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.base

import android.animation.AnimatorSet
import android.content.Context
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig.getListOrientation
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils.dpToPixel
import com.oplus.gallery.foundation.util.display.ScreenUtils.pixelToDp
import com.oplus.gallery.foundation.util.display.ScreenUtils.toDp
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.FORM_COMMON_PAGE
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.getToolBarContentSize
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.getViewScaleByWindowSize
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.getViewSizeByScale
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.isToolBarGridLevelT0

open class EditorMemoriesBaseUiController @JvmOverloads constructor(
    context: Context,
    rootView: ViewGroup,
    state: EditorBaseState,
    permitMonitor: Int = MONITOR_DEFAULT
) : EditorBaseUIController(context, rootView, state, permitMonitor) {

    // 这里的landscape 与 portrait 代表的并不是安卓系统意义上的横屏或者竖屏，这里单纯的指横向与竖向的意思
    private val landscapeLayoutId: Int = R.layout.videoeditor_memories_frame_layout_landscape
    private val portraitLayoutId: Int = R.layout.videoeditor_memories_frame_layout
    private val reconfigureViewIds = listOf(
        R.id.preview_area,
        R.id.time_seek_bar_area,
        R.id.start_safe_area,
        R.id.title_bar_container,
        R.id.toolbar_container
    )

    private val timeSetPickerHeight by lazy {
        context.resources.getDimensionPixelOffset(R.dimen.memories_editor_timeset_picker_height)
    }

    protected val timeSeekBarStartPadding by lazy {
       context.resources.getDimensionPixelSize(R.dimen.memories_editor_time_seek_bar_padding_start)
    }
    protected val timeSeekBarEndPadding by lazy {
        context.resources.getDimensionPixelSize(R.dimen.memories_editor_time_seek_bar_padding_end)
    }

    protected val timeSeekBarMarginTop by lazy {
        context.resources.getDimensionPixelOffset(R.dimen.memories_editor_time_seek_bar_margin_top)
    }

    protected val timeSeekBarHeight by lazy {
       context.resources.getDimensionPixelOffset(R.dimen.memories_editor_time_seek_bar_layout_height)
    }
    protected val menuBarHeight by lazy {
        context.resources.getDimensionPixelOffset(R.dimen.memories_editor_toolbar_height)
    }

    protected val menuBarWidthLandscape by lazy {
        context.resources.getDimensionPixelOffset(R.dimen.video_editor_toolbar_width_landscape)
    }

    protected val previewAreaMarginTop by lazy {
        context.resources.getDimensionPixelSize(R.dimen.video_editor_preview_safe_area_size)
    }

    protected val previewAreaMarginTopLandscape by lazy {
        context.resources.getDimensionPixelSize(R.dimen.video_editor_preview_safe_area_size_landscape)
    }

    protected val titleBarHeight by lazy {
        context.resources.getDimensionPixelOffset(R.dimen.video_editor_titlebar_height)
    }
    protected open var leftEdgeView: View? = null
    protected open var rightEdgeView: View? = null
    protected open var menuListView: EditorLinearListView? = null
    protected open var previewArea: RelativeLayout? = null
    protected open var menuContentView: RelativeLayout? = null

    /**
     * 播放进度条view，用于做动画
     */
    protected open var timeSeekBarView: View? = null

    override fun getBottomTitleLayoutId(): Int = 0

    override fun getMenuLayoutId(): Int = 0

    override fun getTitleId(): Int = 0

    override fun getContentLayoutId(config: AppUiConfig): Int = if (isLandscapeLayout(config)) {
        landscapeLayoutId
    } else {
        portraitLayoutId
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        if ((mToolBarMenuContainer == null) || (mContainer.parent == null)) {
            return
        }
        adaptSafeArea(baseActivity, config.appUiConfig)
        adaptToolBarArea(layoutId, mRootView, config.appUiConfig, baseActivity)
        adaptPreviewArea(layoutId, mRootView, config.appUiConfig, baseActivity)
    }

    private fun adaptSafeArea(baseActivity: BaseActivity, config: AppUiConfig) {
        if (leftEdgeView == null) {
            leftEdgeView = mContainer.findViewById(R.id.start_safe_area)
        }
        if (rightEdgeView == null) {
            rightEdgeView = mContainer.findViewById(R.id.end_safe_area)
        }
        rightEdgeView?.let { rightEdgeViewIt ->
            leftEdgeView?.let { leftEdgeViewIt ->
                VideoEditorUIConfig.adaptSafeAreaInMemories(baseActivity, leftEdgeViewIt, rightEdgeViewIt, config)
            }
        }
    }

    private fun adaptToolBarArea(layoutId: Int, view: View, config: AppUiConfig, context: BaseActivity) {
        adaptTimeSeekBarContainer(layoutId, view, config, context)
        adaptToolBarContainer(layoutId, view, config, context)
    }

    /**
     * 注意：如果通过post的方式进行view的布局适配，必须判断当前activity是否已经结束。因为activity结束后controller回调将
     * 对引用的控件view字段进行置空，post的任务可能在activity destroy 后执行访问控件view字段方法，导致空异常
     * @param layoutId
     * @param view
     * @param config
     * @param context
     */
    open fun adaptTimeSeekBarContainer(layoutId: Int, view: View?, config: AppUiConfig, context: BaseActivity) {
        (mRootView as? ControlBarView)?.timeSeekBar?.apply {
            post {
                if (context.isFinishing || context.isDestroyed) {
                    GLog.d(TAG, "adaptTimeSeekBarContainer, UI controller has been destroyed, cancel perform post runnable")
                    return@post
                }
                checkSafeArea(context, config)
                val timeSeekBarWidth = dpToPixel(VideoEditorUIConfig.getTimeSeekBarWidth(config.windowWidth.current.toDp)) -
                        context.horizontalNaviBarHeight(false)
                var isSeekBarWidthChange = false
                if (timeSeekBarView == null) {
                    timeSeekBarView = this.findViewById(R.id.time_seek_bar_content)
                }
                timeSeekBarView?.let {
                    isSeekBarWidthChange = (it.layoutParams?.width ?: 0) != timeSeekBarWidth
                    it.layoutParams?.width = timeSeekBarWidth
                    it.setPadding(timeSeekBarStartPadding, 0, timeSeekBarEndPadding, 0)
                    /**
                     * 满足条件时，播放进度条view需要做Alpha动画，需要先设置不可见
                     */
                    if (mIsNeedAnimator) {
                        it.visibility = View.INVISIBLE
                    }
                }
                val viewPaddingLeft = VideoEditorUIConfig.getLeftSafeWidth()
                var viewPaddingRight = VideoEditorUIConfig.getRightSafeWidth()
                if (isLandscapeLayout(config)) {
                    viewPaddingRight = VideoEditorUIConfig.getRightSafeWidth() + menuBarWidthLandscape
                }
                val isPaddingChange = viewPaddingLeft != paddingLeft || viewPaddingRight != paddingRight
                if (isPaddingChange) {
                    setPadding(viewPaddingLeft, 0, viewPaddingRight, 0)
                }
                var isLayoutChange = false
                (layoutParams as? FrameLayout.LayoutParams)?.apply {
                    val viewBottomMargin = if (isLandscapeLayout(config)) titleBarHeight else menuBarHeight + titleBarHeight
                    val timeSeekBarHeight = timeSeekBarHeight
                    isLayoutChange = viewBottomMargin != bottomMargin || timeSeekBarHeight != height
                    if (isLayoutChange) {
                        width = FrameLayout.LayoutParams.MATCH_PARENT
                        height = timeSeekBarHeight
                        bottomMargin = viewBottomMargin
                        gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                    }
                }
                if (isLayoutChange || isSeekBarWidthChange) {
                    requestLayout()
                }
            }
        }
    }

    /**
     * 适配工具栏区域，可以在这里修改通用的播放按钮区域，Menu列表，静音旋转等view的位置
     * 注意：如果通过post的方式进行view的布局适配，必须判断当前activity是否已经结束。因为activity结束后controller回调将
     * 对引用的控件view字段进行置空，post的任务可能在activity destroy 后执行访问控件view字段方法，导致空异常
     * @param layoutId
     * @param view
     * @param config
     * @param context
     */
    open fun adaptToolBarContainer(layoutId: Int, view: View, config: AppUiConfig, context: BaseActivity) {
        val isLandscape = isLandscapeLayout(config)
        val toolBarContainer = mToolBarMenuContainer as RelativeLayout
        toolBarContainer.gravity =
            if (isLandscape) Gravity.RIGHT or Gravity.CENTER_VERTICAL else Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
        val paddingEndLandscape = context.resources.getDimensionPixelOffset(R.dimen.video_editor_toolbar_container_padding_end_landscape)
        val paddingBottom = context.resources.getDimensionPixelOffset(R.dimen.memories_editor_toolbar_container_padding_bottom)
        toolBarContainer.setPadding(0, 0, if (isLandscape) paddingEndLandscape else 0, if (isLandscape) 0 else paddingBottom)
        adaptMenuView(config, context)
        zoomToolBar(toolBarContainer, config)
    }

    /**
     * 适配底部工具栏区域，Menu列表
     *
     * @param layoutId
     * @param view
     * @param config
     * @param context
     */
    private fun adaptMenuView(config: AppUiConfig, context: BaseActivity) {
        if (menuContentView == null) {
            menuContentView = mContainer.findViewById(R.id.menu_layout)
        }

        menuContentView?.let { menuContentViewIt ->
            /**
             * 当屏幕旋转时(onConfigChanged)。
             * 此处ui的更新需要依赖 BaseActivity#bottomNaviBarHeight方法获取正确的导航栏高度，
             * 但是BaseActivity#bottomNaviBarHeight方法需要依赖decorView layout后才会有正确的返回，
             * 故这里需要post一下
             */
            menuContentViewIt.post {
                if (context.isFinishing || context.isDestroyed) {
                    GLog.d(TAG, "adaptMenuView, UI controller has been destroyed, cancel perform post runnable")
                    return@post
                }
                menuListView?.let { menuListViewIt ->
                    val isLandscape = isLandscapeLayout(config)
                    val listOritation = getListOrientation(config)
                    menuListViewIt.setOrientation(listOritation)
                    val layoutManager = menuListViewIt.layoutManager as LinearLayoutManager
                    if (layoutManager.orientation != listOritation) {
                        layoutManager.orientation = listOritation
                    }
                    var viewSize = config.windowWidth.current
                    val titleBarHeight = titleBarHeight
                    if (isLandscape) {
                        viewSize = config.windowHeight.current - titleBarHeight - context.bottomNaviBarHeight(false)
                    }
                    viewSize = pixelToDp(viewSize)
                    val toolBarContentSize = dpToPixel(getToolBarContentSize(viewSize, isLandscape))
                    val layoutParams = menuContentViewIt.layoutParams as RelativeLayout.LayoutParams
                    layoutParams.width = toolBarContentSize
                    layoutParams.height = RelativeLayout.LayoutParams.WRAP_CONTENT
                    menuContentViewIt.gravity = Gravity.CENTER_HORIZONTAL
                    val isT0: Boolean = isToolBarGridLevelT0(viewSize)
                    val paddingHorizontalDef: Int =
                        context.resources.getDimensionPixelOffset(R.dimen.videoeditor_lib_list_padding_horizontal)
                    var paddingHorizontal = if (isT0) paddingHorizontalDef else 0
                    var paddingVertical = 0
                    if (isLandscape) {
                        paddingHorizontal = 0
                        val paddingVerticalDef: Int =
                            context.resources.getDimensionPixelOffset(R.dimen.videoeditor_lib_list_padding_vertical_landscape)
                        paddingVertical = if (isT0) paddingVerticalDef else 0
                        layoutParams.height = toolBarContentSize
                        layoutParams.width = RelativeLayout.LayoutParams.WRAP_CONTENT
                        menuContentViewIt.gravity = Gravity.CENTER_VERTICAL
                    }
                    menuListViewIt.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
                    menuListViewIt.clipToPadding = false
                    menuContentViewIt.layoutParams = layoutParams
                }
            }
        }
    }

    private fun zoomToolBar(toolbarContainer: ViewGroup, config: AppUiConfig) {
        val scale = getViewScaleByWindowSize(config, FORM_COMMON_PAGE)
        val isLandscape = isLandscapeLayout(config)
        if (!isLandscape) {
            val toolBarHeight = getViewSizeByScale(menuBarHeight, config, FORM_COMMON_PAGE)
            val layoutParams = toolbarContainer.layoutParams
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
            layoutParams.height = toolBarHeight
            toolbarContainer.layoutParams = layoutParams
        }
        toolbarContainer.scaleX = scale
        toolbarContainer.scaleY = scale
    }

    /**
     * 动态修改视频预览区域
     * 注意：如果通过post的方式进行view的布局适配，必须判断当前activity是否已经结束。因为activity结束后controller回调将
     * 对引用的控件view字段进行置空，post的任务可能在activity destroy 后执行访问控件view字段方法，导致空异常
     * @param layoutId
     * @param view
     * @param config
     * @param context
     */
    open fun adaptPreviewArea(layoutId: Int, view: View, config: AppUiConfig, context: BaseActivity) {
        adaptPreviewArea(layoutId, view, config, context, true)
    }

    open fun checkSafeArea(context: BaseActivity, config: AppUiConfig) {
        rightEdgeView?.let { rightEdgeViewIt ->
            leftEdgeView?.let { leftEdgeViewIt ->
                VideoEditorUIConfig.adaptSafeAreaInMemories(context, leftEdgeViewIt, rightEdgeViewIt, config)
            }
        }
    }

    override fun getReconfigureViewIds(): List<Int> {
        return reconfigureViewIds
    }

    override fun hide(animate: Boolean, removeContainer: Boolean) {
        leftEdgeView = null
        rightEdgeView = null
        menuContentView = null
        previewArea = null
        super.hide(animate, removeContainer)
    }

    /**
     * 调整视频预览view位置 <p>
     * @param isFromCommonPage 是否使用 回忆-编辑 一级页面的位置计算逻辑. 时长页面有单独的位置计算逻辑, 使用时应该为false.<p>
     */
    protected fun adaptPreviewArea(layoutId: Int, view: View, config: AppUiConfig, context: BaseActivity, isFromCommonPage: Boolean) {
        if (previewArea == null) {
            previewArea = view.findViewById(R.id.preview_area)
        }

        previewArea?.post {
            if (context.isFinishing || context.isDestroyed) {
                GLog.d(TAG, "adaptPreviewArea, UI controller has been destroyed, cancel perform post runnable")
                return@post
            }
            checkSafeArea(context, config)

            val videoEditorView = (mRootView as ControlBarView).editorVideoView
            if (videoEditorView == null) {
                GLog.d(TAG, "adaptPreviewArea, videoEditorView is null")
                return@post
            }

            /**
             * 获取进入视频预览View的应该显示位置的padding(结束padding)
             */
            val paddingList = getVideoEditorPadding(config, isFromCommonPage, context)
            val paddingLeft = paddingList[PADDING_LIST_LEFT]
            val paddingTop = paddingList[PADDING_LIST_TOP]
            val paddingRight = paddingList[PADDING_LIST_RIGHT]
            val paddingBottom = paddingList[PADDING_LIST_BOTTOM]
            /**
             * 当前位置的padding等于应该显示位置的padding时，说明位置已正确，直接返回
             * 取paddingBottom做判断
             */
            if (videoEditorView.paddingBottom == paddingBottom) {
                return@post
            }

            /**
             * 满足条件时，视频预览相关view和时间显示相关view做动画, 显示到正确的位置
             */
            if (mIsNeedAnimator) {
                /**
                 * 获取视频预览view动画的起始padding, 等于上一个页面的结束padding值
                 * 例如：从 回忆-编辑 进入 时长 时，动画的起始padding为之前进入 回忆-编辑 时，编辑页面中视频view最终显示位置的padding
                 * 例如：从 时长 返回 回忆-编辑 ，动画的起始padding为之前进入 时长 时，时长页面中视频view最终显示位置的padding
                 */
                val startPaddingList = getVideoEditorPadding(config, !isFromCommonPage, context)
                startAnimator(videoEditorView, startPaddingList, paddingList)
            } else {
                videoEditorView.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom
                )
            }
        }
    }

    /**
     * 获取视频预览view应该显示的区域的padding
     * @param config 当前UI的config
     * @param isFromCommonPage 是否使用 回忆-编辑 一级页面的位置计算逻辑
     * @return 包含左上右下4个padding值的列表
     */
    private fun getVideoEditorPadding(config: AppUiConfig, isFromCommonPage: Boolean, context: BaseActivity): List<Int> {
        /**
         * 判断是否是横向模式
         * 在回忆-编辑一级页面, 判断标准：窗口宽度 >= 600dp, 与一级页面中isLandscapeLayout(AppUiConfig)一致
         * 在时长界面, 判断标准：窗口高度 <= 540dp, 与时长页面中isLandscapeLayout(AppUiConfig)一致
         */
        val isLandscape = if (isFromCommonPage) EditorUIConfig.isEditorLandscape(config) else isLandscapeLayoutByHeight(config)
        val paddingLeft = leftEdgeView?.layoutParams?.width ?: 0
        var paddingTop = previewAreaMarginTop
        var paddingRight = rightEdgeView?.layoutParams?.width ?: 0
        var paddingBottom: Int = context.bottomNaviBarHeight(false) +
                titleBarHeight +
                if (isFromCommonPage) {
                    getViewSizeByScale(menuBarHeight, config, FORM_COMMON_PAGE)
                } else {
                    menuBarHeight
                } +
                timeSeekBarHeight +
                timeSeekBarMarginTop
        if (isLandscape) {
            paddingTop = previewAreaMarginTopLandscape
            paddingBottom = if (isFromCommonPage) {
                context.bottomNaviBarHeight(false) + titleBarHeight
            } else {
                context.bottomNaviBarHeight(false) + titleBarHeight + timeSetPickerHeight
            }
            if (isFromCommonPage) {
                paddingRight = (rightEdgeView?.layoutParams?.width ?: paddingRight) + menuBarWidthLandscape
            }
        }
        return ArrayList(listOf(paddingLeft, paddingTop, paddingRight, paddingBottom))
    }

    /**
     * 播放动画, 包括视频预览view动画，满足条件时还包括进度条播放view动画
     * @param videoEditorView 视频预览view
     * @param startPaddingList 起始padding
     * @param endPaddingList 结束padding
     */
    private fun startAnimator(videoEditorView: ViewGroup, startPaddingList: List<Int>, endPaddingList: List<Int>) {
        val videoEditorViewAnimator = getVideoEditorViewAnimator(videoEditorView,
            startPaddingList,
            endPaddingList)

        val timeSeekBarViewAnimator = getTimeViewAnimator(timeSeekBarView)
        val animatorSet = AnimatorSet()
        val builder = animatorSet.play(videoEditorViewAnimator)
        if (timeSeekBarViewAnimator != null) {
            builder.with(timeSeekBarViewAnimator)
        }
        animatorSet.start()
    }

    protected companion object {
        const val TAG = "EditorMemoriesBaseUiController"
    }
}