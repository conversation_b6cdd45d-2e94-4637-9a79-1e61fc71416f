<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="316dp"
    android:height="336dp"
    android:viewportWidth="316"
    android:viewportHeight="336">
  <group>
    <clip-path
        android:pathData="M0,0h316v336h-316z"/>
    <path
        android:pathData="M0,0h316v336h-316z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="57.65"
            android:startY="-38.59"
            android:endX="282.65"
            android:endY="349.36"
            android:type="linear">
          <item android:offset="0" android:color="#FF282929"/>
          <item android:offset="1" android:color="#FF252627"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M0,0h316v336h-316z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="57.65"
            android:startY="-38.59"
            android:endX="282.65"
            android:endY="349.36"
            android:type="linear">
          <item android:offset="0" android:color="#FF282929"/>
          <item android:offset="1" android:color="#FF252627"/>
        </gradient>
      </aapt:attr>
    </path>
    <group>
      <clip-path
          android:pathData="M0,0h316v336h-316z"/>
      <path
          android:pathData="M0,0h316v336h-316z">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="57.65"
              android:startY="-38.59"
              android:endX="282.65"
              android:endY="349.36"
              android:type="linear">
            <item android:offset="0" android:color="#FF282929"/>
            <item android:offset="1" android:color="#FF252627"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M0,0h316v336h-316z">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="57.65"
              android:startY="-38.59"
              android:endX="282.65"
              android:endY="349.36"
              android:type="linear">
            <item android:offset="0" android:color="#FF282929"/>
            <item android:offset="1" android:color="#FF252627"/>
          </gradient>
        </aapt:attr>
      </path>
      <group>
        <clip-path
            android:pathData="M0,0h316v336h-316z"/>
        <path
            android:pathData="M43,90H110V158H43V90Z">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="43"
                android:startY="90"
                android:endX="110"
                android:endY="158"
                android:type="linear">
              <item android:offset="0" android:color="#0A36393B"/>
              <item android:offset="1" android:color="#FF292B2D"/>
            </gradient>
          </aapt:attr>
        </path>
        <group>
          <clip-path
              android:pathData="M43,90H110V158H43V90Z"/>
          <path
              android:pathData="M100.39,109.58C98.65,102.41 91.42,98 84.25,99.74C77.08,101.48 72.68,108.7 74.41,115.86H74.4L71.79,120.67C71.56,121.09 71.66,121.61 72.02,121.92L74.35,123.89L75.12,128.89C75.4,130.73 77.18,131.95 79,131.55L88.19,129.51C87.24,129.74 89.12,129.48 88.19,129.51C95.36,127.77 102.13,116.75 100.39,109.58Z"
              android:fillColor="#464A4D"/>
          <path
              android:pathData="M93.52,148.77C88.33,149.46 83.57,145.81 82.89,140.63L80.38,121.6C79.89,117.89 82.52,114.51 86.23,114.06L91.72,113.4C95.36,112.97 98.68,115.54 99.16,119.18L101.66,138.15C102.35,143.33 98.7,148.09 93.52,148.77Z"
              android:fillColor="#464A4D"/>
          <path
              android:pathData="M100.22,108.91C98.62,103.25 93.36,99.23 87.47,99.17C82,99.12 77.45,102.2 75.23,106.57L84.95,105.28L84.27,110.94C84.18,111.69 84.54,112.43 85.19,112.81C85.71,113.12 86.05,113.65 86.11,114.25L86.46,117.53L89.23,117.17L93.46,116.61L98.33,120.35C100.53,117.18 101.41,113.08 100.22,108.91Z"
              android:fillColor="#36393B"/>
          <path
              android:pathData="M74.5,218.37L61.57,142.79L122.34,134.77L126.73,211.48L74.5,218.37Z"
              android:fillColor="#464A4D"/>
          <path
              android:pathData="M113.33,213.45L89.38,216.61L79.33,140.4L103.37,137.27L113.33,213.45Z"
              android:fillColor="#3D4043"/>
          <path
              android:pathData="M92.81,119.14C95.01,119.14 96.8,117.36 96.8,115.15C96.8,112.95 95.01,111.16 92.81,111.16C90.6,111.16 88.82,112.95 88.82,115.15C88.82,117.36 90.6,119.14 92.81,119.14Z"
              android:fillColor="#464A4D"/>
          <path
              android:pathData="M62.9,165.6C69.22,165.6 74.34,160.48 74.34,154.15C74.34,147.83 69.22,142.71 62.9,142.71C56.58,142.71 51.45,147.83 51.45,154.15C51.45,160.48 56.58,165.6 62.9,165.6Z"
              android:fillColor="#464A4D"/>
          <path
              android:pathData="M52.88,148.62L72.96,159.61L47.79,208.21L29.69,200.03L52.88,148.62Z"
              android:fillColor="#464A4D"/>
        </group>
        <path
            android:pathData="M179,158H316V226H179V158Z">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="179"
                android:startY="185.5"
                android:endX="316"
                android:endY="185.5"
                android:type="linear">
              <item android:offset="0" android:color="#7F36393B"/>
              <item android:offset="0.64" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <group>
          <clip-path
              android:pathData="M179,158h137v68h-137z"/>
          <path
              android:pathData="M242.51,186.23C242.51,179.07 236.7,173.26 229.54,173.26C222.37,173.26 216.56,179.07 216.56,186.24C216.56,189.6 218.08,193.37 220.44,196.43C220.31,196.98 220.25,197.55 220.25,198.14L220.25,211.13C220.25,215.47 223.76,218.99 228.11,218.99C232.45,218.99 235.98,215.47 235.98,211.13L235.98,202.92L240.01,203.28C241.81,203.44 243.37,202.03 243.4,200.23L243.5,195.31L245.49,193.13C245.81,192.78 245.84,192.26 245.57,191.88L242.44,187.59C242.48,187.15 242.51,186.69 242.51,186.23Z"
              android:fillColor="#464A4D"
              android:fillType="evenOdd"/>
          <path
              android:pathData="M210.96,183.12C213.07,183.12 215.03,182.5 216.68,181.43C216.18,182.84 215.9,184.35 215.9,185.93C215.9,189.43 217.5,193.37 219.98,196.54C221.29,195.64 222.47,194.57 223.49,193.36C223.65,193.35 223.81,193.34 223.97,193.32C223.25,192.59 222.8,191.58 222.8,190.47C222.8,188.23 224.62,186.41 226.87,186.41C229.11,186.41 230.93,188.23 230.93,190.47C230.93,190.71 230.91,190.95 230.87,191.18C235.69,188.52 239.16,183.7 239.98,178.03C237.55,174.74 233.65,172.6 229.24,172.6C226.18,172.6 223.36,173.63 221.12,175.36C221.35,174.48 221.48,173.55 221.48,172.6C221.48,166.79 216.77,162.08 210.96,162.08C205.14,162.08 200.43,166.79 200.43,172.6C200.43,178.41 205.14,183.12 210.96,183.12Z"
              android:fillColor="#393C3F"
              android:fillType="evenOdd"/>
          <path
              android:pathData="M224.32,196.65C225.93,196.65 227.24,195.35 227.24,193.74C227.24,192.13 225.93,190.82 224.32,190.82C222.71,190.82 221.41,192.13 221.41,193.74C221.41,195.35 222.71,196.65 224.32,196.65Z"
              android:fillColor="#5C6267"/>
          <path
              android:pathData="M279.94,225.58L287.52,214.23C287.68,213.97 287.68,213.63 287.49,213.38C287.2,212.97 286.58,212.97 286.28,213.37L282.14,218.88L286.02,209.39C286.63,207.9 285.93,206.2 284.46,205.55L283.83,205.28C282.48,204.68 280.89,205.19 280.12,206.45L273.33,217.52C273.1,217.9 272.69,218.16 272.24,218.15C271.54,218.14 271.03,217.6 271,216.96L270.84,213.73C270.79,212.61 270.03,211.64 268.95,211.31C268.85,211.28 268.74,211.35 268.72,211.46L267.52,219.1C267.35,220.17 267.38,221.26 267.6,222.33L269.06,229.21L268.86,231.34C272.49,231.27 275.53,233.66 279.14,233.51L279.94,225.58Z"
              android:fillColor="#3E4143"/>
          <path
              android:pathData="M247.62,209.43C252,209.64 255.61,212.76 256.56,216.9L263.18,235.39C259.45,237.43 255.89,239.74 252.38,242.12L247.95,265.16H208.32L204.11,240.45C199.89,238.98 195.68,237.48 191.54,235.93L198.38,216.83C199.39,212.58 203.21,209.42 207.77,209.42C207.99,209.42 208.21,209.43 208.42,209.45L208.43,209.43H246.85C246.95,209.42 247.05,209.42 247.15,209.42C247.26,209.42 247.36,209.42 247.46,209.43H247.62L247.62,209.43Z"
              android:fillColor="#373A3C"
              android:fillType="evenOdd"/>
        </group>
        <path
            android:pathData="M316,90H0V89H316V90Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="0"
                android:startY="90.5"
                android:endX="316"
                android:endY="90.5"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.37" android:color="#FF36393B"/>
              <item android:offset="0.62" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M1,22H0V21H1V22ZM5,22H3V21H5V22ZM9,22H7V21H9V22ZM13,22H11V21H13V22ZM17,22H15V21H17V22ZM21,22H19V21H21V22ZM25,22H23V21H25V22ZM29,22H27V21H29V22ZM33,22H31V21H33V22ZM37,22H35V21H37V22ZM41,22H39V21H41V22ZM45,22H43V21H45V22ZM49,22H47V21H49V22ZM53,22H51V21H53V22ZM57,22H55V21H57V22ZM61,22H59V21H61V22ZM65,22H63V21H65V22ZM69,22H67V21H69V22ZM73,22H71V21H73V22ZM77,22H75V21H77V22ZM81,22H79V21H81V22ZM85,22H83V21H85V22ZM89,22H87V21H89V22ZM93,22H91V21H93V22ZM97,22H95V21H97V22ZM101,22H99V21H101V22ZM105,22H103V21H105V22ZM109,22H107V21H109V22ZM113,22H111V21H113V22ZM117,22H115V21H117V22ZM121,22H119V21H121V22ZM125,22H123V21H125V22ZM129,22H127V21H129V22ZM133,22H131V21H133V22ZM137,22H135V21H137V22ZM141,22H139V21H141V22ZM145,22H143V21H145V22ZM149,22H147V21H149V22ZM153,22H151V21H153V22ZM157,22H155V21H157V22ZM161,22H159V21H161V22ZM165,22H163V21H165V22ZM169,22H167V21H169V22ZM173,22H171V21H173V22ZM177,22H175V21H177V22ZM181,22H179V21H181V22ZM185,22H183V21H185V22ZM189,22H187V21H189V22ZM193,22H191V21H193V22ZM197,22H195V21H197V22ZM201,22H199V21H201V22ZM205,22H203V21H205V22ZM209,22H207V21H209V22ZM213,22H211V21H213V22ZM217,22H215V21H217V22ZM221,22H219V21H221V22ZM225,22H223V21H225V22ZM229,22H227V21H229V22ZM233,22H231V21H233V22ZM237,22H235V21H237V22ZM241,22H239V21H241V22ZM245,22H243V21H245V22ZM249,22H247V21H249V22ZM253,22H251V21H253V22ZM257,22H255V21H257V22ZM261,22H259V21H261V22ZM265,22H263V21H265V22ZM269,22H267V21H269V22ZM273,22H271V21H273V22ZM277,22H275V21H277V22ZM281,22H279V21H281V22ZM285,22H283V21H285V22ZM289,22H287V21H289V22ZM293,22H291V21H293V22ZM297,22H295V21H297V22ZM301,22H299V21H301V22ZM305,22H303V21H305V22ZM309,22H307V21H309V22ZM313,22H311V21H313V22ZM316,22H315V21H316V22Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="0"
                android:startY="22.5"
                android:endX="316"
                android:endY="22.5"
                android:type="linear">
              <item android:offset="0.4" android:color="#0036393B"/>
              <item android:offset="0.6" android:color="#FF36393B"/>
              <item android:offset="0.82" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M43,226.5L1,226.5L1,225.5L43,225.5L43,226.5Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="1"
                android:startY="226.5"
                android:endX="43"
                android:endY="226.5"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.53" android:color="#FF36393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M316,226.5L178,226.5L178,225.5L316,225.5L316,226.5Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="178"
                android:startY="226.5"
                android:endX="316"
                android:endY="226.5"
                android:type="linear">
              <item android:offset="0" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M316,294H0L0,293H316V294Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="0"
                android:startY="294.5"
                android:endX="316"
                android:endY="294.5"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.22" android:color="#FF36393B"/>
              <item android:offset="0.56" android:color="#FF36393B"/>
              <item android:offset="0.85" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M316,158.5H0V157.5H316V158.5Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="0"
                android:startY="159"
                android:endX="316"
                android:endY="159"
                android:type="linear">
              <item android:offset="0" android:color="#0044484B"/>
              <item android:offset="0.14" android:color="#FF36393B"/>
              <item android:offset="0.79" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0044484B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M178,336L178,0L179,0L179,336L178,336Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="177.5"
                android:startY="0"
                android:endX="177.5"
                android:endY="336"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.17" android:color="#FF36393B"/>
              <item android:offset="0.77" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M246,336L246,226.5L247,226.5L247,336L246,336ZM246,158L246,0L247,0L247,158L246,158Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="245.5"
                android:startY="0"
                android:endX="245.5"
                android:endY="336"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.4" android:color="#FF36393B"/>
              <item android:offset="0.62" android:color="#FF36393B"/>
              <item android:offset="0.91" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M110,158L110,0L111,0L111,158L110,158Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="109.5"
                android:startY="0"
                android:endX="109.5"
                android:endY="158"
                android:type="linear">
              <item android:offset="0.21" android:color="#0036393B"/>
              <item android:offset="1" android:color="#FF36393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M110,336L110,294L111,294L111,336L110,336Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="109.5"
                android:startY="294"
                android:endX="109.5"
                android:endY="336"
                android:type="linear">
              <item android:offset="0" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M42,336L42,0L43,0L43,336L42,336Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="41.5"
                android:startY="0"
                android:endX="41.5"
                android:endY="336"
                android:type="linear">
              <item android:offset="0.15" android:color="#0036393B"/>
              <item android:offset="0.23" android:color="#FF36393B"/>
              <item android:offset="0.89" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M129.09,213.1L159.3,270.7H99L129.09,213.1Z"
            android:fillColor="#2A2C2D"/>
        <path
            android:pathData="M131.75,209.73L120.14,232.05L119.26,231.59L130.86,209.27L131.75,209.73Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="131.75"
                android:startY="209.73"
                android:endX="120.14"
                android:endY="232.06"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.16" android:color="#FF36393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M126.97,207.92L162.24,275.07L161.36,275.54L126.09,208.38L126.97,207.92Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="126.97"
                android:startY="207.92"
                android:endX="162.25"
                android:endY="275.07"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.07" android:color="#FF36393B"/>
              <item android:offset="0.92" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M99,191.95L139.95,270.25H58.21L99,191.95Z"
            android:fillColor="#2E3031"/>
        <path
            android:pathData="M165.6,270.7L51.3,270.7L51.3,269.7L165.6,269.7L165.6,270.7Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="51.3"
                android:startY="271.2"
                android:endX="165.6"
                android:endY="271.2"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.06" android:color="#FF36393B"/>
              <item android:offset="0.94" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M96.8,185.87L143.02,274.97L142.13,275.43L95.91,186.33L96.8,185.87Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="96.8"
                android:startY="185.87"
                android:endX="143.02"
                android:endY="274.97"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.1" android:color="#FF36393B"/>
              <item android:offset="0.94" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M100.73,186.77L54.51,275.87L55.4,276.33L101.62,187.23L100.73,186.77Z"
            android:fillType="evenOdd">
          <aapt:attr name="android:fillColor">
            <gradient
                android:startX="100.73"
                android:startY="186.77"
                android:endX="54.51"
                android:endY="275.87"
                android:type="linear">
              <item android:offset="0" android:color="#0036393B"/>
              <item android:offset="0.1" android:color="#FF36393B"/>
              <item android:offset="0.94" android:color="#FF36393B"/>
              <item android:offset="1" android:color="#0036393B"/>
            </gradient>
          </aapt:attr>
        </path>
        <path
            android:pathData="M141.3,205.8C147.21,205.8 152,201.01 152,195.1C152,189.19 147.21,184.4 141.3,184.4C135.39,184.4 130.6,189.19 130.6,195.1C130.6,201.01 135.39,205.8 141.3,205.8ZM141.3,206.8C147.76,206.8 153,201.56 153,195.1C153,188.64 147.76,183.4 141.3,183.4C134.84,183.4 129.6,188.64 129.6,195.1C129.6,201.56 134.84,206.8 141.3,206.8Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M44.5,158.25C44.5,159.49 43.49,160.5 42.25,160.5C41.01,160.5 40,159.49 40,158.25C40,157.01 41.01,156 42.25,156C43.49,156 44.5,157.01 44.5,158.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M42.25,159.5C42.94,159.5 43.5,158.94 43.5,158.25C43.5,157.56 42.94,157 42.25,157C41.56,157 41,157.56 41,158.25C41,158.94 41.56,159.5 42.25,159.5ZM42.25,160.5C43.49,160.5 44.5,159.49 44.5,158.25C44.5,157.01 43.49,156 42.25,156C41.01,156 40,157.01 40,158.25C40,159.49 41.01,160.5 42.25,160.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M44.5,226.25C44.5,227.49 43.49,228.5 42.25,228.5C41.01,228.5 40,227.49 40,226.25C40,225.01 41.01,224 42.25,224C43.49,224 44.5,225.01 44.5,226.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M42.25,227.5C42.94,227.5 43.5,226.94 43.5,226.25C43.5,225.56 42.94,225 42.25,225C41.56,225 41,225.56 41,226.25C41,226.94 41.56,227.5 42.25,227.5ZM42.25,228.5C43.49,228.5 44.5,227.49 44.5,226.25C44.5,225.01 43.49,224 42.25,224C41.01,224 40,225.01 40,226.25C40,227.49 41.01,228.5 42.25,228.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M44.5,293.25C44.5,294.49 43.49,295.5 42.25,295.5C41.01,295.5 40,294.49 40,293.25C40,292.01 41.01,291 42.25,291C43.49,291 44.5,292.01 44.5,293.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M42.25,294.5C42.94,294.5 43.5,293.94 43.5,293.25C43.5,292.56 42.94,292 42.25,292C41.56,292 41,292.56 41,293.25C41,293.94 41.56,294.5 42.25,294.5ZM42.25,295.5C43.49,295.5 44.5,294.49 44.5,293.25C44.5,292.01 43.49,291 42.25,291C41.01,291 40,292.01 40,293.25C40,294.49 41.01,295.5 42.25,295.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M112.5,293.25C112.5,294.49 111.49,295.5 110.25,295.5C109.01,295.5 108,294.49 108,293.25C108,292.01 109.01,291 110.25,291C111.49,291 112.5,292.01 112.5,293.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M110.25,294.5C110.94,294.5 111.5,293.94 111.5,293.25C111.5,292.56 110.94,292 110.25,292C109.56,292 109,292.56 109,293.25C109,293.94 109.56,294.5 110.25,294.5ZM110.25,295.5C111.49,295.5 112.5,294.49 112.5,293.25C112.5,292.01 111.49,291 110.25,291C109.01,291 108,292.01 108,293.25C108,294.49 109.01,295.5 110.25,295.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M112.5,158.25C112.5,159.49 111.49,160.5 110.25,160.5C109.01,160.5 108,159.49 108,158.25C108,157.01 109.01,156 110.25,156C111.49,156 112.5,157.01 112.5,158.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M110.25,159.5C110.94,159.5 111.5,158.94 111.5,158.25C111.5,157.56 110.94,157 110.25,157C109.56,157 109,157.56 109,158.25C109,158.94 109.56,159.5 110.25,159.5ZM110.25,160.5C111.49,160.5 112.5,159.49 112.5,158.25C112.5,157.01 111.49,156 110.25,156C109.01,156 108,157.01 108,158.25C108,159.49 109.01,160.5 110.25,160.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M180.5,158.25C180.5,159.49 179.49,160.5 178.25,160.5C177.01,160.5 176,159.49 176,158.25C176,157.01 177.01,156 178.25,156C179.49,156 180.5,157.01 180.5,158.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M178.25,159.5C178.94,159.5 179.5,158.94 179.5,158.25C179.5,157.56 178.94,157 178.25,157C177.56,157 177,157.56 177,158.25C177,158.94 177.56,159.5 178.25,159.5ZM178.25,160.5C179.49,160.5 180.5,159.49 180.5,158.25C180.5,157.01 179.49,156 178.25,156C177.01,156 176,157.01 176,158.25C176,159.49 177.01,160.5 178.25,160.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M180.5,226.25C180.5,227.49 179.49,228.5 178.25,228.5C177.01,228.5 176,227.49 176,226.25C176,225.01 177.01,224 178.25,224C179.49,224 180.5,225.01 180.5,226.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M178.25,227.5C178.94,227.5 179.5,226.94 179.5,226.25C179.5,225.56 178.94,225 178.25,225C177.56,225 177,225.56 177,226.25C177,226.94 177.56,227.5 178.25,227.5ZM178.25,228.5C179.49,228.5 180.5,227.49 180.5,226.25C180.5,225.01 179.49,224 178.25,224C177.01,224 176,225.01 176,226.25C176,227.49 177.01,228.5 178.25,228.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M180.5,294.25C180.5,295.49 179.49,296.5 178.25,296.5C177.01,296.5 176,295.49 176,294.25C176,293.01 177.01,292 178.25,292C179.49,292 180.5,293.01 180.5,294.25Z"
            android:fillColor="#272728"/>
        <path
            android:pathData="M178.25,295.5C178.94,295.5 179.5,294.94 179.5,294.25C179.5,293.56 178.94,293 178.25,293C177.56,293 177,293.56 177,294.25C177,294.94 177.56,295.5 178.25,295.5ZM178.25,296.5C179.49,296.5 180.5,295.49 180.5,294.25C180.5,293.01 179.49,292 178.25,292C177.01,292 176,293.01 176,294.25C176,295.49 177.01,296.5 178.25,296.5Z"
            android:fillColor="#36393B"
            android:fillType="evenOdd"/>
      </group>
    </group>
  </group>
</vector>
