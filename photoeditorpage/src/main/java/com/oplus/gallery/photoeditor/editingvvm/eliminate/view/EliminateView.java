/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EliminateView.java
 * * Description: draw a eliminate path
 * * Version: 1.0
 * * Date : 2017/12/10
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/12/10    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.eliminate.view;

import static com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminatePath.ELIMINATE_LIMIT_SIZE;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.view.MotionEvent;
import android.view.ViewConfiguration;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.ui.gesture.detector.ScaleRotateDetector;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.math.MathUtils;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.thread.ThreadUtils;
import com.oplus.gallery.photoeditor.R;
import com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminatePath;
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties;
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizerView2;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

public class EliminateView extends GestureRecognizerView2 implements IEliminatePathPainter {
    private static final String TAG = "EliminateView";

    private static final int MULTI_TOUCH_DETECTED_TIME_OUT = 100;
    private static final int SCROLL_TIME_OUT_FOR_LOW_PERFORMANCE_PLATFORM = TimeUtils.TIME_5_SEC_IN_MS;
    private static final float INVALID_NUMBER = -0.1f;

    private int mTouchSlop;
    private float mDownX = INVALID_NUMBER;
    private float mDownY = INVALID_NUMBER;
    private boolean mIsRotateScaleEvent = false;
    private boolean mIsClickEvent = true;
    private boolean mIsLowPerformPlatform = FeatureUtils.isLowPerformancePlatform();
    private Matrix mImageTransformMatrix = new Matrix();
    private RectF mCurringDrawingImageOutBound = new RectF();

    private EliminatePath mEliminatePath;
    private EliminatePath.Size mSize;
    private Path mPath = new Path();
    private Paint mPaint = new Paint();
    private PointF mPrePointF = new PointF();
    private EliminatePathListener mPathListener;
    private boolean mIsTouchable = true;

    public EliminateView(Context context) {
        super(context);
        mTouchSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setColor(context.getColor(R.color.picture3d_eliminate_paint_color));
        mPaint.setStrokeJoin(Paint.Join.ROUND);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        postInvalidateOnAnimation();
    }

    @Override
    protected void onAnimationPropertySet(@NonNull PreviewAnimationProperties animationProperties) {
        super.onAnimationPropertySet(animationProperties);
        updateCurrentMatrix();
    }

    @Override
    public boolean onDown(MotionEvent e) {
        updateCurrentMatrix();
        final float touchX = e.getX();
        final float touchY = e.getY();
        if (!mCurringDrawingImageOutBound.contains(touchX, touchY)) {
            GLog.d(TAG, "onDown mDrawingOutBound is out mCurringDrawingImageOutBound: " + mCurringDrawingImageOutBound.toString()
                    + " touchX:" + touchX + " touchY:" + touchY);
            return true;
        }
        mDownX = touchX;
        mDownY = touchY;
        mIsClickEvent = true;
        mIsRotateScaleEvent = false;

        PointF unProjectionPoint = unprojection(touchX, touchY);
        mPrePointF.set(unProjectionPoint);
        mPath.reset();
        mPath.moveTo(mPrePointF.x, mPrePointF.y);
        setStrokeSize(mSize);

        mEliminatePath = new EliminatePath(mSize, mCurringDrawingImageOutBound);
        mEliminatePath.addPoint((mapPointTopDrawingBound(touchX, touchY, mCurringDrawingImageOutBound)));
        return true;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (getTouchable()) {
            return super.onTouchEvent(event);
        } else {
            return false;
        }
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        long elapsedTime = e2.getEventTime() - e2.getDownTime();
        boolean scrollDetected = elapsedTime > MULTI_TOUCH_DETECTED_TIME_OUT;
        if (GProperty.getPHOTOEDIT_DEBUG()) {
            GLog.d(TAG, "onScroll, scrollDetected = " + scrollDetected + ", mIsRotateScaleEvent = "
                    + mIsRotateScaleEvent + ", mIsClickEvent = " + mIsClickEvent);
        }

        if (scrollDetected && mIsRotateScaleEvent) {
            super.onScroll(e1, e2, distanceX, distanceY);
        } else if (mIsClickEvent) {
            if (scrollDetected && checkMoved(mDownX, mDownY, e2.getX(), e2.getY())) {
                mIsClickEvent = false;
            }
        } else if (mIsLowPerformPlatform && (elapsedTime > SCROLL_TIME_OUT_FOR_LOW_PERFORMANCE_PLATFORM)) {
            GLog.d(TAG, "onScroll, mIsLowPerformPlatform and scroll time out, abort");

            if (mEliminatePath != null) {
                mEliminatePath.addPoint(mapPointTopDrawingBound(e2.getX(), e2.getY(), mCurringDrawingImageOutBound));
                if (mPathListener != null) {
                    mPathListener.onPathEnd(mEliminatePath);
                }
                mPath.reset();
                invalidate();
                mEliminatePath = null;
            }
            return true;
        } else {
            if (GProperty.getPHOTOEDIT_DEBUG()) {
                GLog.d(TAG, "onScroll, mEntry.addPoint normal mEntry:" + mEliminatePath);
            }

            if (mEliminatePath != null) {
                float x = e2.getX();
                float y = e2.getY();

                PointF currPointF = unprojection(x, y);
                mPath.quadTo(mPrePointF.x, mPrePointF.y, (currPointF.x + mPrePointF.x) / 2f, (currPointF.y + mPrePointF.y) / 2f);
                mEliminatePath.addPoint(mapPointTopDrawingBound(x, y, mCurringDrawingImageOutBound));

//                mPrePointF.set(e2.getX(), e2.getY());
                mPrePointF.set(currPointF);
                invalidate();
            }
        }

        return true;
    }

    @Override
    public boolean onScaleRotateBegin(float scalePivotX, float scalePivotY, float angle, float rotatePivotX, float rotatePivotY,
                                                   float scale, ScaleRotateDetector detector) {
        GLog.d(TAG, "onScaleRotateBegin, angle: " + angle + ", scale: " + scale
            + ", mIsClickEvent: " + mIsClickEvent + ", mEntry: " + mEliminatePath);

        mIsRotateScaleEvent = true;
        if (!mIsClickEvent) {
            if (mEliminatePath != null) {
                if (mPathListener != null) {
                    mPathListener.onPathEnd(mEliminatePath);
                }
                mPath.reset();
                invalidate();
                mEliminatePath = null;
            }
        } else {
            if (!mPath.isEmpty()) {
                mPath.reset();
                invalidate();
                mEliminatePath = null;
            }
        }
        return super.onScaleRotateBegin(scalePivotX, scalePivotY, angle, rotatePivotX, rotatePivotY, scale, detector);
    }

    @Override
    public boolean onScaleRotate(float scalePivotX, float scalePivotY, float angle,
                                 float rotatePivotX, float rotatePivotY, float scale, ScaleRotateDetector detector) {
        if (GProperty.getPHOTOEDIT_DEBUG()) {
            GLog.d(TAG, "onScaleRotate, angle = " + angle + ", scale = " + scale);
        }
        return super.onScaleRotate(scalePivotX, scalePivotY, angle, rotatePivotX, rotatePivotY, scale, detector);
    }

    @Override
    public boolean onScaleRotateEnd(float scalePivotX, float scalePivotY, float angle,
                                    float rotatePivotX, float rotatePivotY, float scale, ScaleRotateDetector detector) {
        GLog.d(TAG, "onScaleRotateEnd, angle : " + angle + ", scale : " + scale);

        mIsRotateScaleEvent = false;
        return super.onScaleRotateEnd(scalePivotX, scalePivotY, angle, rotatePivotX, rotatePivotY, scale, detector);
    }

    @Override
    public boolean onUpOrCancel(MotionEvent event) {
        GLog.d(TAG, "onUp, mIsClickEvent = " + mIsClickEvent + ", mEntry = " + mEliminatePath
                + " mCurringDrawingImageOutBound: " + mCurringDrawingImageOutBound.toString());
        if (!mIsClickEvent && (mEliminatePath != null) && (mPathListener != null)) {
            mEliminatePath.addPoint(mapPointTopDrawingBound(event.getX(), event.getY(), mCurringDrawingImageOutBound));

            mPathListener.onPathEnd(mEliminatePath);
        }
        mPath.reset();
        mEliminatePath = null;
        mIsRotateScaleEvent = false;
        mIsClickEvent = false;
        invalidate();
        super.onUpOrCancel(event);
        return true;
    }

    private boolean checkMoved(float downX, float downY, float currX, float currY) {
        float xDiff = Math.abs(currX - downX);
        float yDiff = Math.abs(currY - downY);
        if ((xDiff > mTouchSlop) || (yDiff > mTouchSlop)) {
            return true;
        }
        return false;
    }

    @Override
    public void setStrokeSize(EliminatePath.Size size) {
        mSize = size;
        GLog.d(TAG, "[setStrokeSize] size:" + size.getStrokeWidth());
        float unprojectionWidth = MathUtils.unprojectionRadius(mImageTransformMatrix, size.getStrokeWidth());
        unprojectionWidth = Math.max(unprojectionWidth, ELIMINATE_LIMIT_SIZE);
        GLog.d(TAG, "setStrokeSize :" + unprojectionWidth);
        mPaint.setStrokeWidth(unprojectionWidth);
    }

    @Override
    public void clearContent() {
        // do nothing
    }

    @Override
    public boolean getTouchable() {
        return mIsTouchable;
    }

    @Override
    public void setTouchable(boolean enable) {
        mIsTouchable = enable;
    }

    private void updateCurrentMatrix() {
        RectF currentOutBound = getCurrentDrawingOutBound();
        if (mCurringDrawingImageOutBound.equals(currentOutBound)) {
            return;
        }
        mCurringDrawingImageOutBound.set(currentOutBound);
        mImageTransformMatrix = getAnimationProperties().getFullPose().getCurrent().affine();
    }

    private PointF unprojection(float x, float y) {
        return MathUtils.unprojectionPoint(mImageTransformMatrix, x, y);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        updateCurrentMatrix();
        canvas.clipRect(mCurringDrawingImageOutBound);
        canvas.save();
        canvas.concat(mImageTransformMatrix);
        if (!mPath.isEmpty()) {
            canvas.drawPath(mPath, mPaint);
        }
        canvas.restore();
    }

    @Override
    public void setEliminatePathListener(EliminatePathListener listener) {
        mPathListener = listener;
    }

    @Override
    public void invalidPath() {
        if (ThreadUtils.isMainThread()) {
            invalidate();
        } else {
            postInvalidateOnAnimation();
        }
    }
}