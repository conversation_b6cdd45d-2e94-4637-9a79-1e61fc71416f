/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetDatabaseHelper.kt
 ** Description:桌面卡片相关数据库查询和修改，以及精选列表生成等修改数据库相关逻辑
 ** Version: 1.0
 ** Date : 2021/11/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2021/11/23        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.helper

import android.content.ContentValues
import android.database.Cursor
import android.provider.MediaStore
import android.util.Pair
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketIdsCacheHelper
import com.oplus.gallery.business_lib.model.data.base.utils.MediaSetUtils
import com.oplus.gallery.business_lib.seniorpicked.PickedDayDBFilterCondition
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.SeniorMedia
import com.oplus.gallery.foundation.database.store.GalleryStore.SeniorMediaColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetDisplayList
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetDisplayListColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSet
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns
import com.oplus.gallery.foundation.database.util.ConstantUtils.AND
import com.oplus.gallery.foundation.database.util.ConstantUtils.FROM
import com.oplus.gallery.foundation.database.util.ConstantUtils.GROUP_BY
import com.oplus.gallery.foundation.database.util.ConstantUtils.INNER_JOIN
import com.oplus.gallery.foundation.database.util.ConstantUtils.NOT_IN
import com.oplus.gallery.foundation.database.util.ConstantUtils.ON
import com.oplus.gallery.foundation.database.util.ConstantUtils.OR
import com.oplus.gallery.foundation.database.util.ConstantUtils.SELECT
import com.oplus.gallery.foundation.database.util.ConstantUtils.WHERE
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ALL
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert
import com.oplus.gallery.foundation.dbaccess.convert.CountAllConvert
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.convert.FilePathsConvert
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.dao.MediaStoreDbDao
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SELL_MODE
import com.oplus.gallery.widgetpage.WidgetConstants.Display.DISPLAY_COUNT_INFO_MAX_SIZE
import com.oplus.gallery.widgetpage.WidgetConstants.DisplayList.MAX_RECOMMENDED_LIST_SIZE
import com.oplus.gallery.widgetpage.WidgetConstants.DisplayList.SHARED_DISPLAY_LIST
import com.oplus.gallery.widgetpage.ext.getCardMode
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import kotlin.collections.set
import kotlin.random.Random

class WidgetDatabaseHelper {

    /**
     * 桌面 素材路径限制
     * relative_path 为 /DCIM/Camera
     */
    private val validPathRecommended = arrayOf("/DCIM/Camera")

    /**
     * 插入卡片数据
     * @param widgetCode 卡片标识
     */
    fun addWidget(widgetCode: String): Boolean {
        val resultUri = InsertReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setConvert {
                ContentValues().apply {
                    put(WidgetSetColumns.WIDGET_CODE, widgetCode)
                    put(WidgetSetColumns.MODE, widgetCode.getCardMode())
                }
            }
            .build().exec()
        return (resultUri != null)
    }

    /**
     * 删除卡片数据
     * @param widgetCode 卡片标识
     */
    fun removeWidget(widgetCode: String) {
        if (queryWidgetCountById(widgetCode) > 0) {
            DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.WIDGET_SET)
                .setWhere("${WidgetSetColumns.WIDGET_CODE} = ?")
                .setWhereArgs(arrayOf(widgetCode))
                .build().exec()
            // 只删除独立轮播列表
            DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
                .setWhere("${WidgetDisplayListColumns.DISPLAY_LIST_ID} = ?")
                .setWhereArgs(arrayOf(widgetCode))
                .build().exec()
        }
    }

    /**
     * 查询所有卡片数量
     */
    fun queryAllWidgetCount(): Int {
        return queryWidgetCount(whereClause = null)
    }

    private fun queryWidgetCountById(widgetCode: String): Int {
        return queryWidgetCount(whereClause = "${WidgetSetColumns.WIDGET_CODE} = '$widgetCode'")
    }

    private fun queryWidgetCount(whereClause: String?): Int {
        return QueryReq.Builder<Int>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setProjection(DatabaseUtils.getCountProjection())
            .setWhere(whereClause)
            .setConvert(CountAllConvert())
            .build().exec()
    }

    /**
     * 把所有卡片设为dirty，也就是触发刷新所有卡片
     */
    fun setAllWidgetDirty() {
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setConvert(ContentValuesConvert(ContentValues().apply {
                put(WidgetSetColumns.DIRTY, 1)
            }))
            .build().exec()
    }

    private fun addToRecommendedDisplayList(count: Int): Int {
        val availableList = getAvailableItemsForRecommendedMode().toMutableList()
        if (availableList.isEmpty() && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SELL_MODE)) {
            getAvailableItemsForRecommendedModeInSellMode()?.let {
                availableList.addAll(it)
            }
        }
        GLog.d(TAG, "addToRecommendedDisplayList: availableList.size=${availableList.size}")
        if (availableList.isEmpty()) {
            return 0
        }
        val selectedList = mutableListOf<String>()
        repeat(count) {
            if (availableList.isEmpty()) {
                return@repeat
            }
            availableList.removeAt(Random.nextInt(availableList.size)).run {
                selectedList.add(this)
            }
        }
        GLog.d(TAG, "addToRecommendedDisplayList: selectedList.size=${selectedList.size}")
        return addToDisplayList(selectedList, WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED)
    }

    /**
     *  卖场模式下从 DCIM/Camera 路径下获取图集
     */
    private fun getAvailableItemsForRecommendedModeInSellMode(): List<String>? {
       runCatching {
            // DCIM/Camera
            val buckIdList = BucketIdsCacheHelper.getCameraBucketIds()
            val where = DatabaseUtils.getWhereQueryIn(MediaStore.Images.Media.BUCKET_ID, buckIdList.size)
            val whereArgs = DatabaseUtils.getWhereArgs(buckIdList)
            return QueryReq.Builder<ArrayList<String>>()
                .setDaoType(IDao.DaoType.MEDIA_STORE)
                .setTableType(MediaStoreDbDao.TableType.IMAGE)
                .setProjection(arrayOf(MediaStore.Images.Media.DATA))
                .setWhere(where)
                .setWhereArgs(whereArgs)
                .setConvert(FilePathsConvert())
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, "getAvailableItemsForRecommendedModeInSellMode", it)
        }
        return null
    }

    /**
     * 生成精选轮播列表
     */
    fun generateRecommendedDisplayListIfNeeded() {
        if (getRecommendedModeWidgetCount() == 0) {
            GLog.d(TAG, "generateRecommendedDisplayListIfNeeded: no recommended widget")
            return
        }
        val deleteCount = deleteOldRecommendedList()
        val insertCount = addToRecommendedDisplayList(MAX_RECOMMENDED_LIST_SIZE)
        setAllRecommendedWidgetDirty()
        GLog.d(TAG, "generateRecommendedDisplayListIfNeeded: " +
                "deleteCount=$deleteCount insertCount=$insertCount")
    }

    private fun getRecommendedModeWidgetCount(): Int {
        return QueryReq.Builder<Int>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setProjection(DatabaseUtils.getCountProjection())
            .setWhere("${WidgetSetColumns.MODE} = ${WidgetSetColumns.MODE_RECOMMENDED}")
            .setConvert(CountAllConvert())
            .build().exec()
    }

    /**
     * SELECT local_media._data
     * FROM senior_media INNER JOIN local_media ON local_media._data = senior_media._data
     * WHERE width <= 15000 AND width >= 800 AND height <= 15000 AND height >= 800
     * AND _size <= 52428800 AND _size >= 102400
     * AND (relative_path =  'DCIM/Camera/'  OR cshot_id != 0 )
     * AND (local_media._data IN ( SELECT _data FROM senior_media WHERE senior_score >= 4.0
     *     GROUP BY similar_group_id HAVING senior_score =  MAX (senior_score)))
     * AND height / width <= 2
     * AND local_media._data NOT IN ( SELECT _data FROM widget_display_list WHERE display_list_id = -1)
     * GROUP BY media_id
     */
    private fun getAvailableItemsForRecommendedMode(): List<String> {
        val recommendedDataList = "$SELECT${WidgetDisplayListColumns.DATA}" +
                "$FROM${WidgetDisplayList.TAB}" +
                "$WHERE${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '${WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED}'"
        val where = "${PickedDayDBFilterCondition.getBasicFilterCondition(true, validPathRecommended)}" +
                "$AND${SeniorMediaCondition.buildValidSeniorScoreForCardWidget(MIN_QUALITY_SCORE)}" +
                "$AND${LocalColumns.HEIGHT} / ${LocalColumns.WIDTH} <= $MAX_HEIGHT_WIDTH_RADIO" +
                "$AND${GalleryStore.GalleryMedia.TAB}.${LocalColumns.DATA}" + NOT_IN + "($recommendedDataList)"
        val rawQuery = MediaSetUtils.getInnerJoinLocalMediaRawWhereClause(
            arrayOf(LocalColumns.DATA),
            SeniorMedia.TAB,
            SeniorMediaColumns.DATA,
            where,
            ORDER_NO_LIMIT,
            START_NO_LIMIT,
            COUNT_NO_LIMIT
        )
        return RawQueryReq.Builder<List<String>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setQuerySql(rawQuery)
            .setConvert {
                val result = mutableListOf<String>()
                val dataIndex = it.getColumnIndex(LocalColumns.DATA)
                while (it.moveToNext()) {
                    result.add(it.getString(dataIndex))
                }
                result
            }
            .build().exec()
    }

    private fun deleteOldRecommendedList(): Int {
        val where = StringBuilder()
            .append("${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '${WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED}'")
            .append(AND)
            .append("${WidgetDisplayListColumns.INVALID} != ${WidgetDisplayListColumns.INVALID_EXCLUDED}")
            .toString()
        return DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setWhere(where)
            .build().exec()
    }

    private fun setAllRecommendedWidgetDirty() {
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setWhere("${WidgetSetColumns.MODE} = ${WidgetSetColumns.MODE_RECOMMENDED}")
            .setConvert(ContentValuesConvert(ContentValues().apply {
                put(WidgetSetColumns.DIRTY, 1)
            }))
            .build().exec()
    }

    /**
     * 添加图片到指定卡片的轮播列表
     * @param galleryIds 相册数据库id列表
     * @param displayListId 轮播列表
     * @return 成功插入数据库的数量
     */
    fun addToDisplayListByGalleryId(galleryIds: List<Int>, displayListId: String): Int {
        // 轮播顺序是按用户选择的顺序，所以需要查出_id来排序，保证查询出来的绝对路径集合顺序也是用户选择的顺序
        val map = QueryReq.Builder<Map<Int, String>>().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(arrayOf(
                LocalColumns._ID,
                LocalColumns.DATA
            ))
            .setWhere(DatabaseUtils.getWhereIn(LocalColumns._ID, galleryIds))
            .setConvert { original ->
                val map = HashMap<Int, String>()
                original?.use {
                    while (it.moveToNext()) {
                        map[it.getInt(0)] = it.getString(1)
                    }
                }
                map
            }
            .build().exec()
        val filePathList = ArrayList<String>()
        galleryIds.forEach { index ->
            map[index]?.let { filePathList.add(it) }
        }
        return addToDisplayList(filePathList, displayListId)
    }

    /**
     * 添加图片到指定卡片的轮播列表
     * @param filePathList 图片绝对路径
     * @param displayListId 卡片标识{widgetCode}，推荐模式统一为[WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED]
     * @return 成功添加的数量
     */
    private fun addToDisplayList(filePathList: List<String>, displayListId: String): Int {
        val values = mutableListOf<ContentValues>().apply {
            filePathList.forEach { filePath ->
                add(ContentValues().apply {
                    put(WidgetDisplayListColumns.DATA, filePath)
                    put(WidgetDisplayListColumns.DISPLAY_LIST_ID, displayListId)
                })
            }
        }
        val count = BulkInsertReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setConvert {
                values.toTypedArray()
            }
            .build().exec()
        GLog.d(TAG, "addToDisplayList: displayListId=$displayListId count=$count")
        return count
    }

    /**
     * 从轮播列表移出
     * @param filePathList 文件路径列表
     * @param displayListId 轮播列表
     * @return 是否移出成功，部分成功也视为成功
     */
    fun removeFromDisplayList(filePathList: List<String>, displayListId: String): Boolean {
        val itemCount = updateOrDeleteDisplayList(filePathList, displayListId)
        // 精选模式移出轮播列表后从候选数据抽取补充进队尾
        if ((itemCount > 0) && (displayListId == WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED)) {
            addToRecommendedDisplayList(itemCount)
        }
        val dirtyWidgetCount = setWidgetDirty(displayListId)
        GLog.d(TAG, "removeFromDisplayList: itemCount=$itemCount dirtyWidgetCount=$dirtyWidgetCount")
        return itemCount > 0
    }

    private fun updateOrDeleteDisplayList(filePathList: List<String>, displayListId: String): Int {
        val displayListWhere = StringBuilder()
            .append("${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '$displayListId'")
            .append(AND)
            .append(DatabaseUtils.getWhereQueryIn(WidgetDisplayListColumns.DATA, filePathList.size))
            .toString()
        return if (SHARED_DISPLAY_LIST.contains(displayListId)) { // 共用轮播列表，不删除，只标记为移出
            UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
                .setWhere(displayListWhere)
                .setWhareArgs(filePathList.toTypedArray())
                .setConvert(ContentValuesConvert(ContentValues().apply {
                    put(WidgetDisplayListColumns.INVALID, WidgetDisplayListColumns.INVALID_EXCLUDED)
                }))
                .build().exec()
        } else { // 独立轮播列表，直接删除
            DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
                .setWhere(displayListWhere)
                .setWhereArgs(filePathList.toTypedArray())
                .build().exec()
        }
    }

    private fun setWidgetDirty(displayListId: String): Int {
        // 对共用轮播列表，把轮播列表id转换成卡片模式；对独立轮播列表，轮播列表id就是widget code
        val widgetSetWhere = if (SHARED_DISPLAY_LIST.contains(displayListId)) {
            "${WidgetSetColumns.MODE} = '${getWidgetModeFromDisplayListId(displayListId)}'"
        } else {
            "${WidgetSetColumns.WIDGET_CODE} = '$displayListId'"
        }
        return UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setWhere(widgetSetWhere)
            .setConvert(ContentValuesConvert(ContentValues().apply {
                put(WidgetSetColumns.DIRTY, 1)
            }))
            .build().exec()
    }

    /**
     * 轮播列表id转换为卡片模式，共用轮播列表才能这样转换
     */
    private fun getWidgetModeFromDisplayListId(displayListId: String): Int {
        return when (displayListId) {
            WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED -> WidgetSetColumns.MODE_RECOMMENDED
            else -> 0
        }
    }

    /**
     * 更新卡片已展示信息
     * @param widgetCode 卡片标识
     * @param currentFilePath 展示的照片路径，null表示默认样式等不展示照片的情况
     */
    fun saveWidgetDisplayedContent(widgetCode: String, currentFilePath: String?) {
        val values = ContentValues().apply {
            put(WidgetSetColumns.DIRTY, 0)
            put(WidgetSetColumns.LAST_DISPLAY_DATA, currentFilePath)
        }
        QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setProjection(arrayOf(WidgetSetColumns.DISPLAY_COUNT_INFO))
            .setWhere("${WidgetSetColumns.WIDGET_CODE} = ?")
            .setWhereArgs(arrayOf(widgetCode))
            .setConvert(CursorConvert())
            .build().exec()?.use {
                if (it.moveToNext()) {
                    convertToJson(it.getString(0)).run {
                        increaseTodayCount(this)
                        // 最多两天（确保能覆盖到昨天）
                        limitJsonSize(this)
                        values.put(WidgetSetColumns.DISPLAY_COUNT_INFO, toString())
                    }
                }
            }
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setWhere("${WidgetSetColumns.WIDGET_CODE} = ?")
            .setWhareArgs(arrayOf(widgetCode))
            .setConvert(ContentValuesConvert(values))
            .build().exec()
    }

    private fun increaseTodayCount(jsonObject: JSONObject) {
        val today = SimpleDateFormat(DATE_FORMAT).format(Date())
        jsonObject.run {
            optInt(today).run {
                put(today, this + 1)
            }
        }
    }

    private fun limitJsonSize(jsonObject: JSONObject) {
        jsonObject.apply {
            while (length() > DISPLAY_COUNT_INFO_MAX_SIZE) {
                remove(keys().next())
            }
        }
    }

    /**
     * 删除invalid轮播列表项
     * @param displayListId 轮播列表id
     */
    fun deleteInvalidDisplayList(displayListId: String, extraWhere: String? = null) {
        val localMediaFilePaths = RawQueryReq.Builder<ArrayList<String>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(FilePathsConvert())
            .setQuerySql(
                MediaSetUtils.getInnerJoinLocalMediaRawWhereClause(
                    arrayOf(LocalColumns.DATA),
                    WidgetDisplayList.TAB,
                    WidgetDisplayListColumns.DATA,
                    "${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '$displayListId'",
                    null,
                    -1,
                    -1
                ))
            .build().exec()
        val notInLocalMediaFilePaths = DatabaseUtils.getWhereQueryNotIn(WidgetDisplayListColumns.DATA, localMediaFilePaths.size)
        var where = "${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '$displayListId'" +
                "$AND((${WidgetDisplayListColumns.INVALID} = ${WidgetDisplayListColumns.INVALID_DELETE})$OR($notInLocalMediaFilePaths))"
        extraWhere?.run {
            where += "$AND$this"
        }
        val deleteCount = DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setWhere(where)
            .setWhereArgs(localMediaFilePaths.toTypedArray())
            .build().exec()
        GLog.d(TAG, "deleteInvalidDisplayList: $deleteCount")
    }

    /**
     * 设置卡片模式
     * @param widgetCode 设置模式的卡片
     * @param mode 设置成该模式，取值如下：
     *   [com.oplus.gallery.standard_lib.database.store.GalleryStore.WidgetSetColumns.MODE_DEFAULT]
     *   [com.oplus.gallery.standard_lib.database.store.GalleryStore.WidgetSetColumns.MODE_RECOMMENDED]
     *   [com.oplus.gallery.standard_lib.database.store.GalleryStore.WidgetSetColumns.MODE_CUSTOM]
     * @return 是否更新数据库成功
     */
    fun updateWidgetMode(widgetCode: String, mode: Int): Boolean {
        return UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setWhere("${WidgetSetColumns.WIDGET_CODE} = '$widgetCode'")
            .setConvert(ContentValuesConvert(ContentValues().apply {
                put(WidgetSetColumns.MODE, mode)
                put(WidgetSetColumns.DIRTY, 1)
            }))
            .build().exec() > 0
    }

    /**
     * 查询推荐卡片的轮播照片数
     */
    fun queryRecommendWidgetItemCount(): Int {
        return queryWidgetItemCount(WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED)
    }

    /**
     * 查询指定卡片的轮播照片数
     * @param displayListId 卡片标识{widgetCode}，推荐模式统一为[WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED]
     */
    fun queryWidgetItemCount(displayListId: String): Int {
        return QueryReq.Builder<Int>().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setProjection(DatabaseUtils.getCountProjection())
            .setWhere("${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '$displayListId' AND " +
                    "${WidgetDisplayList.TAB}.${WidgetDisplayListColumns.INVALID} = ${WidgetDisplayListColumns.INVALID_DEFAULT}")
            .setConvert(CountAllConvert())
            .build().exec()
    }

    /**
     * 在删除、设为私密等移除文件操作（列表数据标为invalid）后调用，查询在精选轮播列表里的invalid == deleted数量，补充对应数量进列表
     * @param filePathList 文件路径列表
     */
    fun notifyFileRemoved() {
        //先查有没有精选卡片，没有就跳过
        if (getRecommendedModeWidgetCount() == 0) {
            GLog.d(TAG, "notifyFileRemoved: no recommended widget")
            return
        }
        val widgetDisplayCount = getWidgetDisplayValidItemCount()
        val addWidgetDisplayCount = MAX_RECOMMENDED_LIST_SIZE - widgetDisplayCount
        if (addWidgetDisplayCount > 0) {
            addToRecommendedDisplayList(addWidgetDisplayCount)
        }
    }

    private fun getWidgetDisplayValidItemCount(): Int {
        val where = "${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '${WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED}'" +
                "$AND(${WidgetDisplayList.TAB}$DOT${WidgetDisplayListColumns.INVALID} = ${WidgetDisplayListColumns.INVALID_DEFAULT})"
        val validItemCount = RawQueryReq.Builder<Int>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(CountAllConvert())
            .setQuerySql(
                DatabaseUtils.getInnerJoinRawWhereClause(
                    COUNT_ALL,
                    Pair(GalleryStore.GalleryMedia.TAB, WidgetDisplayList.TAB),
                    Pair(LocalColumns.DATA, WidgetDisplayListColumns.DATA),
                    where,
                    null,
                    null,
                    -1,
                    -1
                )
            )
            .build().exec()

        GLog.d(TAG, LogFlag.DL) { "getWidgetDisplayValidItemCount: $validItemCount" }
        return validItemCount
    }

    /**
     * 获取某个模式的卡片数量
     * @param mode 模式
     * @return 数量
     */
    fun getWidgetCount(mode: Int): Int {
        return QueryReq.Builder<Int>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setProjection(DatabaseUtils.getCountProjection())
            .setWhere("${WidgetSetColumns.MODE} = $mode")
            .setConvert(CountAllConvert())
            .build().exec()
    }

    /**
     * 某天每个卡片的展示次数
     * 目前保存最多两天，即可以确保最早有昨天的数据
     * @param dayOffset 日期偏移量，0表示今天，正数表示晚于今天，负数表示早于今天
     * @return 次数的list
     */
    fun getEachWidgetDisplayCountOfDate(dayOffset: Int): List<Int> {
        return QueryReq.Builder<List<Int>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_SET)
            .setProjection(arrayOf(WidgetSetColumns.DISPLAY_COUNT_INFO))
            .setConvert(WidgetDisplayCountConvert(dayOffset))
            .build().exec()
    }

    private class WidgetDisplayCountConvert(private val dayOffset: Int) : IConvert<Cursor, List<Int>> {
        override fun convert(cursor: Cursor): List<Int> {
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DAY_OF_YEAR, dayOffset)
            val date = SimpleDateFormat(DATE_FORMAT).format(calendar.time)
            val result = mutableListOf<Int>()
            while (cursor.moveToNext()) {
                convertToJson(cursor.getString(0)).run {
                    result.add(optInt(date))
                }
            }
            return result
        }
    }

    /**
     * 每个自选卡片的照片数量
     * @return 数量的list
     */
    fun getEachCustomDisplayListSize(): List<Int> {
        val widgetSetTable = WidgetSet.TAB
        val widgetDisplayListTable = WidgetDisplayList.TAB
        val widgetCodeColumn = "$widgetSetTable.${WidgetSetColumns.WIDGET_CODE}"
        val widgetModeColumn = "$widgetSetTable.${WidgetSetColumns.MODE}"
        val displayListIdColumn = "$widgetDisplayListTable.${WidgetDisplayListColumns.DISPLAY_LIST_ID}"
        val rawQuery = "$SELECT COUNT($widgetCodeColumn) " +
                "$FROM$widgetSetTable$INNER_JOIN$widgetDisplayListTable$ON$widgetCodeColumn = $displayListIdColumn" +
                "$WHERE$widgetModeColumn = ${WidgetSetColumns.MODE_CUSTOM}" +
                "$GROUP_BY$widgetCodeColumn"
        return RawQueryReq.Builder<List<Int>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setQuerySql(rawQuery)
            .setConvert {
                val result = mutableListOf<Int>()
                while (it.moveToNext()) {
                    result.add(it.getInt(0))
                }
                result
            }
            .build().exec()
    }

    /**
     * 移出精选列表的数量
     * @return 数量
     */
    fun getExcludedRecommendedDisplayListSize(): Int {
        return QueryReq.Builder<Int>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.WIDGET_DISPLAY_LIST)
            .setProjection(DatabaseUtils.getCountProjection())
            .setWhere("${WidgetDisplayListColumns.DISPLAY_LIST_ID} = '${WidgetDisplayListColumns.DISPLAY_LIST_ID_RECOMMENDED}' " +
                    "$AND${WidgetDisplayListColumns.INVALID} = ${WidgetDisplayListColumns.INVALID_EXCLUDED}")
            .setConvert(CountAllConvert())
            .build().exec()
    }

    companion object {
        private const val TAG = "WidgetDatabaseHelper"
        private const val MIN_QUALITY_SCORE = 3.7f
        private const val MAX_HEIGHT_WIDTH_RADIO = 2
        private const val MIN_QUERY_WIDGET_COUNT = 10
        private const val START_NO_LIMIT = -1
        private const val COUNT_NO_LIMIT = -1
        private const val DATE_FORMAT = "yyyyMMdd"
        private val ORDER_NO_LIMIT = null

        private fun convertToJson(jsonString: String?): JSONObject {
            return jsonString?.let {
                JSONObject(it)
            } ?: JSONObject()
        }
    }
}