/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - UnitBezier.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.common.anim;

public class UnitBezier {
    private static final double DOUBLE_THREE = 3.0D;
    private static final double DOUBLE_TWO = 2.0D;
    private static final double DOUBLE_ONE = 1.0D;
    private static final double DOUBLE_HALF = 0.5D;
    private static final double ABOVE_ZERO = 1.0E-6D;
    private static final int COUNT = 8;
    private double mAx;
    private double mBx;
    private double mCx;
    private double mAy;
    private double mBy;
    private double mCy;

    public UnitBezier(double p1x, double p1y, double p2x, double p2y) {
        this.mCx = DOUBLE_THREE * p1x;
        this.mBx = DOUBLE_THREE * (p2x - p1x) - this.mCx;
        this.mAx = DOUBLE_ONE - this.mCx - this.mBx;
        this.mCy = DOUBLE_THREE * p1y;
        this.mBy = DOUBLE_THREE * (p2y - p1y) - this.mCy;
        this.mAy = DOUBLE_ONE - this.mCy - this.mBy;
    }

    public double sampleCurveX(double t) {
        return ((this.mAx * t + this.mBx) * t + this.mCx) * t;
    }

    public double sampleCurveY(double t) {
        return ((this.mAy * t + this.mBy) * t + this.mCy) * t;
    }

    public double sampleCurveDerivativeX(double t) {
        return (DOUBLE_THREE * this.mAx * t + DOUBLE_TWO * this.mBx) * t + this.mCx;
    }

    public double solveCurveX(double x, double epsilon) {
        double t2 = x;

        double x2 = 0.0D;
        for (int i = 0; i < COUNT; ++i) {
            x2 = this.sampleCurveX(t2) - x;
            if (Math.abs(x2) < epsilon) {
                return t2;
            }

            double d2 = this.sampleCurveDerivativeX(t2);
            if (Math.abs(d2) < ABOVE_ZERO) {
                break;
            }

            t2 -= x2 / d2;
        }

        double t0 = 0.0D;
        double t1 = DOUBLE_ONE;
        t2 = x;
        if (x < t0) {
            return t0;
        } else if (x > t1) {
            return t1;
        } else {
            for (; t0 < t1; t2 = (t1 - t0) * DOUBLE_HALF + t0) {
                x2 = this.sampleCurveX(t2);
                if (Math.abs(x2 - x) < epsilon) {
                    return t2;
                }

                if (x > x2) {
                    t0 = t2;
                } else {
                    t1 = t2;
                }
            }

            return t2;
        }
    }

    public double solve(double x, double epsilon) {
        return sampleCurveY(this.solveCurveX(x, epsilon));
    }
}
