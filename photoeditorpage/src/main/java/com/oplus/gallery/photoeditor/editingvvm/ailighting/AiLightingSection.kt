/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AiLightingSection
 ** Description : AI 补光功能section
 ** Version     : 1.0
 ** Date        : 2025/3/4
 ** Author      : gary@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80411420        2025/3/4    1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.ailighting

import android.app.Activity
import android.graphics.Bitmap
import android.graphics.RectF
import androidx.annotation.StringRes
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import android.view.View
import android.widget.RelativeLayout
import com.coui.component.responsiveui.ResponsiveUIModel
import com.coui.component.responsiveui.layoutgrid.MarginType
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseEditingSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_CURRENT_TEXTURE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_DISPLAY_RECT_WRAPPER
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_HDR_SDR_RATIO
import com.oplus.gallery.photoeditor.editingvvm.ailighting.bean.AiLightingProcessingResult
import com.oplus.gallery.photoeditor.editingvvm.ailighting.bean.LightType
import com.oplus.gallery.photoeditor.editingvvm.ailighting.statemachine.State
import com.oplus.gallery.photoeditor.editingvvm.ailighting.statemachine.Reason
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.AiLightingSectionViewBinding
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.AiLightingCOEManager
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.COEViewWrapper
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.animations.AnalyzingAnimation
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.animations.PhotoLoadFinishAnimation
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notification.EditorConfirmDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notification.NetworkErrorDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notification.NoNetworkDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingAction
import com.oplus.gallery.photoeditor.editingvvm.preview.DisplayRectWrapper
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.widget.EditorCompareButton

/**
 * 补光section
 * @property viewBinding 视图绑定:清晰化 AiLightingSection 结构
 * @property coeManager 补光coe业务类
 * @property isDomestic 是否内销
 */
internal class AiLightingSection(sectionBus: ISectionBus<EditingFragment, EditingVM>) : BaseEditingSection(sectionBus) {

    private var viewBinding: AiLightingSectionViewBinding? = null

    private var coeManager: AiLightingCOEManager? = null

    private var currentTexture: ITexture? = null

    /**
     * 响应示布局 栅格适配
     */
    val responsiveUIModel by lazy {
        ResponsiveUIModel(activity, activity.resources.displayMetrics.widthPixels, 0).chooseMargin(MarginType.MARGIN_LARGE)
    }

    /**
     * 当前的UI状态
     */
    private var currentUIState: State = State.STATE_IDLE

    private val isDomestic by lazy {
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN)
    }

    private val stateProcessingResultObserver: TObserver<AiLightingProcessingResult> = { result ->
        currentUIState = result.state
        updateUi(result)
    }

    /**
     * 监听当前Texture变化
     */
    private val currentTextureObserver: TObserver<ITexture> = {
        currentTexture = it
        viewBinding?.updateCapsuleViewPosition(activity, vBus, isLandscape, it)
    }

    private val onProgressChanged: (progress: Float, fromUser: Boolean) -> Unit = { percent, fromUser ->
        if (fromUser) {
            vBus.notifyOnce(TopicID.AiLighting.TOPIC_ADJUST_STRENGTH, percent)
        }
    }

    private var isLandscape: Boolean = false

    private val hdrSdrRatio: TObserver<Float> = {
        //更新hdrSdrRatio
        coeManager?.updateDeviceHdrSdrRatio(it)
    }

    override fun onCreate() {
        super.onCreate()
        initView()
        registerTopics()
    }

    private fun initView() {
        isLandscape = EditorUIConfig.isEditorLandscape(sectionBus.hostInstance.getCurrentAppUiConfig())
        sectionContentView?.also {
            viewBinding = AiLightingSectionViewBinding(it, sectionBus.rootView)
        }
        context?.let {
            viewBinding?.initCapsuleView(context, this, addBaseCallback = {
                addExtraViewToBaseUIView(it)
            }, exitCallback = {
                exitAiLighting()
            }) {
                //收到胶囊完全消失的通知，去展示补光成功布局
                coeManager?.hide()
                updateOperatingViewState(titleBarOperable = true, compareButtonVisible = true, cancelable = true)
                updateContentViewVisible(isLandscape)
            }
        }
        updateContentViewVisible(isLandscape)
    }

    override fun onDestroy() {
        super.onDestroy()
        sectionContainerView?.visibility = View.VISIBLE
        viewBinding?.onDestroy()
        unsubscribeTopics()
        coeManager?.destroy()
    }

    private fun registerTopics() {
        vBus.apply {
            subscribeT(TopicID.AiLighting.TOPIC_STATE_PROCESSING_RESULT, stateProcessingResultObserver)
            subscribeT(TOPIC_PREVIEW_HDR_SDR_RATIO, hdrSdrRatio)
            subscribeT(TOPIC_PREVIEW_CURRENT_TEXTURE, currentTextureObserver)
        }
    }

    private fun unsubscribeTopics() {
        vBus.apply {
            unsubscribe(TopicID.AiLighting.TOPIC_STATE_PROCESSING_RESULT, stateProcessingResultObserver)
            unsubscribe(TOPIC_PREVIEW_HDR_SDR_RATIO, hdrSdrRatio)
            unsubscribe(TOPIC_PREVIEW_CURRENT_TEXTURE, currentTextureObserver)
        }
    }

    override fun getContainerViewID(): Int {
        return R.id.photo_editor_ui_framework
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return R.layout.photoeditorpage_ai_lighting
    }

    private fun updateUi(result: AiLightingProcessingResult) {
        when (result.state) {
            //隐藏所有元素
            State.STATE_IDLE -> viewBinding?.idleUi()
            //分析中,显示胶囊动效
            State.STATE_ANALYZE -> {
                analyzing(result)
                updateContentViewVisible(isLandscape)
            }
            //匹配中,显示胶囊动效
            State.STATE_MATCH -> viewBinding?.matchUi(result)
            /**
             * 分析成功
             * 1.从vm对应消息里面拿图
             * 2.什么灯光,对应灯光参数
             */
            State.STATE_SUCCESS -> success(result)

            //分析 未完成,分析未完成原因,然后再具体处理
            State.STATE_ERROR -> {
                errorState(result)
                updateContentViewVisible(isLandscape)
            }
        }
    }

    /**
     * 处理结束,非算法成功状态
     */
    private fun errorState(result: AiLightingProcessingResult) {
        viewBinding?.errorUi()
        updateUiError(result.reason)
        coeManager?.hide()
    }

    /**
     * 初始化ai补光
     * @param previewBitmap 图片 由于初始化需要默认图片的色彩空间 所以得延迟初始化到 预览图拿到后
     */
    private fun initAiLightingCOE(previewBitmap: Bitmap) {
        if (GProperty.DEBUG_AI_LIGHTING_CLOSE_COE) return
        coeManager ?: context?.let { context ->
            coeManager = AiLightingCOEManager(COEViewWrapper(context, previewBitmap.colorSpace), ::displayRectWrapper).also {
                addExtraViewToBaseUIView(it.maskLayout, sectionBus.rootView.findViewById(R.id.compare_button))
            }
        }
    }

    /**
     * 更新 ui 错误逻辑显示:
     * 使用人数过多,使用次数,风险拒识 等
     */
    private fun updateUiError(reason: Reason?) {
        when (reason) {
            //被用户取消
            Reason.CANCELLED_BY_USER -> updateOperatingViewState(true, false, true)
            //存在风险 拒识
            Reason.PHOTOS_POSE_RISKS -> photoRefuseBottomDialog()
            //该功能使用人数过多，请稍后重试
            Reason.SERVER_MORE_USAGE -> bottomDialogExitOrRetry(R.string.picture3d_editor_text_function_is_too_many_people_using)
            //用户使用次数达到上限 EXCESSIVE_USAGE
            Reason.EXCESSIVE_USAGE -> bottomDialogExitOrRetry(R.string.picture3d_editor_text_function_use_frequently_tips)
            //断网
            Reason.NETWORK_DISCONNECT -> netWorkDisconnectDialog()
            //弱网
            Reason.WEAK_NETWORK -> showWeakNetWorkToast()
            //其他网络错误
            Reason.NETWORK_ERROR -> bottomDialogExitOrRetry(R.string.picture3d_editor_text_network_error)
            Reason.NO_PERSON -> noPersonErrorTipAndExit()

            else -> bottomDialogExitOrRetry(R.string.picture3d_editor_text_ai_lighting_analysis_fail)
        }
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        coeManager?.onUiConfigChanged()
        isLandscape = EditorUIConfig.isEditorLandscape(config.appUiConfig)
        currentTexture?.let {
            viewBinding?.updateCapsuleViewPosition(activity, vBus, isLandscape, it)
        }
        updateContentViewVisible(isLandscape)
    }

    /**
     * 根据横竖屏状态更新Section布局的显示和隐藏
     *
     * @param isLandscape
     */
    private fun updateContentViewVisible(isLandscape: Boolean) {
        updateCompareButtonPosition(isLandscape)
        viewBinding?.updateUiPosition(currentUIState, sectionBus.hostInstance.getCurrentAppUiConfig(), responsiveUIModel)
    }

    /**
     * 处理无主体错误的
     * 弹出Toast提示并且退出界面
     */
    private fun noPersonErrorTipAndExit() {
        updateOperatingViewState(true, false, true)
        NotificationAction.ToastAction(R.string.picture3d_editor_text_ai_lighting_photo_not_person_try_again).showNotification()
        exitAiLighting()
    }

    /**
     * 显示弱网提示
     */
    private fun showWeakNetWorkToast() {
        NetworkErrorDialogAction(object : NotificationAction.IConfirmCallback {
            override fun onPositiveButtonClicked(activity: Activity) {
                updateOperatingViewState(true, false, true)
                //关闭页面
                exitAiLighting()
            }
        }).showNotification()
    }

    /**
     * 底部弹窗  只有文字不一样,按钮是一样的
     * @param titleStringRes 标题字符串id
     */
    private fun bottomDialogExitOrRetry(
        @StringRes titleStringRes: Int
    ) {
        EditorConfirmDialogAction(
            titleString = activity.getString(titleStringRes),
            positiveButtonTextResId = com.oplus.gallery.basebiz.R.string.common_retry,
            negativeButtonTextResId = com.oplus.gallery.basebiz.R.string.common_runtime_permission_runtime_cancel,
            confirmCallback = object : NotificationAction.IConfirmCallback {
                override fun onNegativeButtonClicked() {
                    updateOperatingViewState(true, false, true)
                    //退出
                    exitAiLighting()
                }

                override fun onPositiveButtonClicked(activity: Activity) {
                    //重试
                    retryAlgo()
                }
            },
        ).showNotification()
    }

    /**
     * 拒识底部弹窗
     */
    private fun photoRefuseBottomDialog() {
        val title = if (isDomestic) {
            R.string.picture3d_editor_text_ai_lighting_photo_refuse
        } else {
            R.string.picture3d_editor_text_unified_show_refuse_copywriting_oversea
        }
        EditorConfirmDialogAction(
            titleString = activity.getString(title),
            positiveButtonTextResId = com.oplus.gallery.basebiz.R.string.common_ok,
            confirmCallback = object : NotificationAction.IConfirmCallback {
                override fun onPositiveButtonClicked(activity: Activity) {
                    updateOperatingViewState(true, false, true)
                    //关闭页面
                    exitAiLighting()
                }
            }
        ).showNotification()
    }

    /**
     * 断网弹窗
     *
     */
    private fun netWorkDisconnectDialog() {
        NoNetworkDialogAction(confirmCallback = object : NotificationAction.IConfirmCallback {
            override fun onPositiveButtonClicked(activity: Activity) {
                updateOperatingViewState(true, false, true)
                //关闭页面
                exitAiLighting()
            }
        }).showNotification()
    }

    private fun displayRectWrapper(): RectF {
        return vBus.get<DisplayRectWrapper>(TOPIC_PREVIEW_DISPLAY_RECT_WRAPPER)?.displayRect ?: RectF()
    }

    /**
     * 退出补光页面
     */
    private fun exitAiLighting() {
        vBus.notifyOnce(TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.editor_id_text_action_cancel))
    }

    /**
     * 重试算法
     */
    private fun retryAlgo() {
        vBus.notifyOnce(TopicID.AiLighting.TOPIC_ALGO_RETRY)
    }

    /**
     * 显示对应通知
     */
    private fun NotificationAction.showNotification() {
        vBus.notifyOnce(TopicID.Notification.TOPIC_NOTIFICATION_ACTION, this)
    }

    /**
     * 分析中:
     * 从 状态中获取数据来处理: bitmap,hdr信息 用于给动效用
     * 1.获取bitmap
     * 2.初始化coe
     * 3.展示对应的ui
     * 4.启动动效
     * 5.收集hdr信息传给动效
     *
     * @param result 处理中的结果
     */
    private fun analyzing(result: AiLightingProcessingResult) {
        result.extras?.also { extras ->
            val bitmap = extras[AiLightingProcessingResult.RESULT_PROCESSING_BITMAP] as? Bitmap
            bitmap?.also { previewBitmap ->
                initAiLightingCOE(previewBitmap)
                viewBinding?.analyzeUi(this)
                coeManager?.triggerAnimation(AnalyzingAnimation(previewBitmap))
                coeManager?.show()
            }
        }
    }

    /**
     * 成功:状态中获取数据来处理
     * 1.获取bitmap
     * 2.切换动效 动效的动画结束回掉
     *
     * @param result 处理结果 bitmap,hdr信息 用于给动效用
     */
    private fun success(result: AiLightingProcessingResult) {
        result.extras?.also { extras ->
            val bitmap = extras[AiLightingProcessingResult.RESULT_BITMAP] as? Bitmap
            val lightType = extras[AiLightingProcessingResult.RESULT_PROCESSING_LIGHT_TYPE] as? LightType
            viewBinding?.updateProgressColor(lightType)
            bitmap?.also { previewBitmap ->
                coeManager?.triggerAnimation(PhotoLoadFinishAnimation(previewBitmap, null, onAnimateEnd = {
                    //成功ui后续展示会等待胶囊完全隐藏后展示
                    viewBinding?.successUi(lightType, onProgressChanged)
                    previewBitmap.recycle()
                })) ?: run {
                    //成功ui后续展示会等待胶囊完全隐藏后展示
                    viewBinding?.successUi(lightType, onProgressChanged)
                    previewBitmap.recycle()
                }
            }
        }
    }

    private fun updateCompareButtonPosition(isLandscape: Boolean) {
        val compareButton: EditorCompareButton = sectionBus.rootView.findViewById(R.id.compare_button)
        val isLargeScreen = ScreenUtils.isMiddleAndLargeScreen(sectionBus.hostInstance.requireContext())
        compareButton.layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            val resource = sectionBus.rootView.resources
            if (isLargeScreen) {
                addRule(RelativeLayout.ALIGN_BOTTOM, R.id.default_preview_area)
                if (isLandscape) {
                    addRule(RelativeLayout.ALIGN_END, R.id.default_preview_area)
                } else {
                    addRule(RelativeLayout.ALIGN_PARENT_END)
                    marginEnd = resource.getDimensionPixelSize(R.dimen.photo_editor_ai_lighting_compare_button_end_margin)
                }
            } else if (isLandscape) {
                addRule(RelativeLayout.ALIGN_END, R.id.default_preview_area)
                addRule(RelativeLayout.ALIGN_BOTTOM, R.id.default_preview_area)
            } else {
                addRule(RelativeLayout.ALIGN_END, R.id.default_preview_area)
                addRule(RelativeLayout.ALIGN_BOTTOM, R.id.default_preview_area)
                bottomMargin = -resource.getDimensionPixelSize(R.dimen.photo_editor_ai_lighting_compare_button_end_margin_bottom)
            }
        }
    }

    /**
     * 更新标题栏和对比按钮的状态
     * @see com.oplus.gallery.photoeditor.editingvvm.ailighting.AiLightingVM.updateOperatingViewStateObserver
     * @see com.oplus.gallery.photoeditor.editingvvm.TopicID.AiLighting.TOPIC_UPDATE_OPERATING_VIEW_STATE
     * @param titleBarOperable 标题栏是否可操作
     * @param compareButtonVisible 对比按钮的可见性
     * @param cancelable 是否可以取消，即取消按钮是否禁用
     */
    private fun updateOperatingViewState(titleBarOperable: Boolean, compareButtonVisible: Boolean, cancelable: Boolean = true) {
        vBus.notifyOnce(TopicID.AiLighting.TOPIC_UPDATE_OPERATING_VIEW_STATE, titleBarOperable, compareButtonVisible, cancelable)
    }

    companion object {
        val SPEC = SectionSpec(AiLightingSection::class.java)
        const val TAG = "AiLightingSection"
    }
}