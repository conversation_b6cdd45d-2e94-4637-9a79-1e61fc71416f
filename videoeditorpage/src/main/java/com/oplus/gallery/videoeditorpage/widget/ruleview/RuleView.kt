/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  RuleView
 ** Description: 80413407 created
 ** Version: 1.0
 ** Date : 2025/4/19
 ** Author: 80413407
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80413407      2025/4/19     1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ruleview

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Typeface
import android.os.VibrationEffect
import android.os.Vibrator
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import android.view.VelocityTracker
import android.view.View
import android.widget.OverScroller
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.widget.ruleview.SlideDirection.Companion.NONE
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * 刻度卷尺控件
 * 实现原理：
 * 1. 数值处理：将刻度float类型数据乘以10转为int类型，避免浮点精度问题，简化计算（刻度1.5x转换成15来计算）
 * 2. 绘制刻度：
 * - 根据中间指针位置的数值，计算最小值位置与中间指针的距离
 * - 仅绘制控件宽度范围内的刻度，并在两侧各扩展一定数量刻度，保证滑动过程中的视觉连续性
 * 3. 区间规则：
 * - 使用GradationGapRule定义不同值区间的刻度间距
 * - 自动根据控件宽度计算合适的刻度间距，确保每个规则内刻度均匀分布
 * 4. 滑动处理：
 * - 移动时更新当前位置与中心指针的距离，逆向计算当前刻度值
 * - 滑动停止后，自动对齐到最近的刻度
 * 5. 惯性滑动：使用VelocityTracker跟踪手指速度，实现松手后的惯性滑动
 * 6.点击某一刻度，自动滚动到点击的刻度上
 * 7.吸附功能：
 * - 到达长刻度或者特殊刻度，需要停留吸附，滑动一段距离才能脱离吸附功能
 * - 按下瞬间，当前在长刻度上，不需要吸附
 * 8.吸附功能：
 *  - 到达长刻度或者特殊刻度，需要震动
 *
 * 设置多种不同间隔刻度的逻辑规则：
 *   创建刻度间隔规则列表
 *   val rules: MutableList<RuleView.GradationGapRule> = ArrayList()
 *     // 规则1: 0.1x 到 1.0x 之间刻度间隔为 GRADATION_WIDE_GAP
 *     rules.add(
 *         RuleView.GradationGapRule(
 *         //起始的刻度值
 *         FIRST_RULE_START_VALUE,
 *         //起始的刻度值
 *         FIRST_RULE_END_VALUE,
 *         //每一个刻度的间距（宽的）
 *         VideoUtils.dp2px(mContext, GRADATION_WIDE_GAP)
 *         )
 *     )
 *     ....（增加其他规则）
 *    speederRuleView?.setGradationGapRules(rules, DEFAULT_CENTER_GRADATION_VALUE)
 *    DEFAULT_CENTER_GRADATION_VALUE 表示指针指向的默认起始值
 *
 */
class RuleView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    View(context, attrs, defStyleAttr) {
    /** 值变化监听器  */
    var ruleChangedListener: OnRuleChangedListener? = null
    /** 检查布局方向是否为从右到左 **/
    val isRtl: Boolean
        get() = layoutDirection == LAYOUT_DIRECTION_RTL
    /** 刻度尺属性，用于存储和管理与视图相关的规则属性，这些属性定义了视图的行为和外观 */
    private val ruleViewAttributes: RuleViewAttributes
    /** 特殊长刻度规则列表  */
    private val specialGradations: ArrayList<SpecialGradationRule> = ArrayList()
    /** 不同区间的刻度间隔规则  */
    private val gradationGapRules: ArrayList<GradationGapRule> = ArrayList()
    /** 长刻度的位置信息list  */
    private val longGradationPositionInfoList: ArrayList<GradationDistanceInfo> = ArrayList()
    /** 交互相关管理器 （处理吸附，滑动，触摸等） */
    private val interactionManager: InteractionManager
    /** 绘制刻度的的画笔 */
    private val gradationPaint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG)
    }
    /** 滑动器，用于实现平滑滚动和惯性滑动效果  */
    private val scroller: OverScroller by lazy { OverScroller(context) }
    /** 上次检查震动时的距离  */
    private var lastDistanceWhenCheckVibrate = DISTANCE_UNCHECKED_VIBRATE
    /** 震动管理器，用于实现触觉反馈  */
    private var vibrator: Vibrator? = null
    /** 速度跟踪器，用于测量手指滑动速度  */
    private var velocityTracker: VelocityTracker? = null
    /** 是否已释放资源标志，防止内存泄漏  */
    private var isReleased: Boolean = false
    /**
     * 构造函数，初始化参数
     */
    init {
        ruleViewAttributes = RuleViewAttributes.fromAttributes(context, attrs)
        interactionManager = InteractionManager(
            this,
            gradationGapRules,
            specialGradations,
            ruleViewAttributes,
            longGradationPositionInfoList
        )
        interactionManager.convertValue2Number()
        init(context)
    }

    /** 文字画笔，用于绘制刻度值  */
    private val textPaint: Paint by lazy {
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            this.typeface = Typeface.create(OPPO_SANS_FC_FONT_FAMILY_NAME, Typeface.NORMAL)
            this.textSize = ruleViewAttributes.textSize
            this.color = ruleViewAttributes.textColor
        }
    }

    /**
     * 处理点击事件
     * 计算点击位置对应的值并滚动到该位置
     *
     * @param x 点击的x坐标
     * @return true表示事件已处理
     */
    fun handleClickEvent(x: Int): Boolean {
        // 计算点击位置对应的距离，需要减去 paddingStart 才是相对于内容区域的位置
        val clickX = x - paddingStart
        val clickDistance = (clickX - getHalfContentWidth()) + interactionManager.currentDistance
        // 限定点击范围在有效区间内
        val validDistance =
            min(max(clickDistance, DISTANCE_VALUE_DEFAULT), interactionManager.numberRangeDistance)
        // 计算目标值并滚动
        val targetNumber = interactionManager.calculateNumberFromDistance(validDistance, true)
        setCurrentValue(targetNumber / VALUE_TO_NUMBER_SCALE, true)
        return true
    }

    /**
     * 触发设备震动
     */
    fun checkVibrate() {
        // 检查是否需要执行震动反馈
        if ((vibrator == null) || isReleased) return

        // 初始化或更新上次检查震动的距离，仅当未检查时
        if (lastDistanceWhenCheckVibrate == DISTANCE_UNCHECKED_VIBRATE) {
            lastDistanceWhenCheckVibrate = interactionManager.currentDistance
            return
        }
        // 计算滑动方向
        val slideDirection =
            SlideDirection.fromDelta((lastDistanceWhenCheckVibrate - interactionManager.currentDistance).toInt())
        // 如果滑动方向不明显，则不执行后续操作
        if (slideDirection == NONE) {
            return
        }
        // 检查是否已经过一个吸附点
        if (!interactionManager.isPassedOneSnapPoint(
                interactionManager.currentDistance,
                lastDistanceWhenCheckVibrate,
                slideDirection
            )
        ) {
            lastDistanceWhenCheckVibrate = interactionManager.currentDistance
            return
        }
        // 更新上次检查的距离为当前距离
        lastDistanceWhenCheckVibrate = interactionManager.currentDistance
        vibrator?.vibrate(VibrationEffect.createOneShot(VIBRATE_TIME, VIBRATE_EFFECT_STRENGTH))
    }

    /**
     * 处理快速滑动
     */
    fun handleScrollFling() {
        // 计算滑动速度，用于惯性滑动
        velocityTracker?.computeCurrentVelocity(
            FLING_VELOCITY_UNIT,
            ruleViewAttributes.flingVelocityMax.toFloat()
        )
        val xVelocity = velocityTracker?.xVelocity?.toInt() ?: 0
        // 如果速度超过最小阈值，启动惯性滑动
        if (abs(xVelocity.toDouble()) >= ruleViewAttributes.flingVelocityMin) {
            // 注意xVelocity取反，与滑动方向一致
            scroller.fling(
                interactionManager.currentDistance.toInt(), // 起始 X 坐标
                START_Y_POSITION_DEFAULT,         // 起始 Y 坐标
                xVelocity * (if (isRtl) RTL_MULTIPLIER else LTR_MULTIPLIER), // 初始水平速度
                INITIAL_VERTICAL_VELOCITY, // 初始垂直速度
                HORIZONTAL_SCROLL_RANGE_MIN, // 水平方向最小滚动范围
                interactionManager.numberRangeDistance.toInt(), // 水平方向最大滚动范围
                VERTICAL_SCROLL_RANGE_MIN, // 垂直方向最小滚动范围
                VERTICAL_SCROLL_RANGE_MAX  // 垂直方向最大滚动范围
            )
            invalidate()
        } else {
            ruleChangedListener?.onScrollStop(
                interactionManager.currentValue,
                formatValueToLabel(interactionManager.currentValue)
            )
        }
    }

    /**
     * 停止快速滑动
     */
    fun stopFling() {
        scroller.forceFinished(true)
    }

    /**
     * 除去padding获取view的宽度
     */
    fun getContentWidth(): Int = width - paddingStart - paddingRight

    /**
     * 初始化震动器和滚动器
     * @param context 上下文
     */
    private fun init(context: Context) {
        // 初始化震动管理器
        vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        // 计算吸附相关的距离，8dp和5dp转为像素
    }

    /**
     * 除去padding获取view的宽度一半
     */
    private fun getHalfContentWidth(): Int = getContentWidth() / 2

    /**
     * 测量控件尺寸
     * 处理wrap_content和padding情况
     * 计算可用宽度和高度
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 计算实际宽高
        val width = calculateSize(true, widthMeasureSpec)
        val height = calculateSize(false, heightMeasureSpec)
        setMeasuredDimension(width, height)
    }

    /**
     * 计算宽度或高度的真实大小
     * 处理不同测量模式下的尺寸计算
     * @param isWidth 是不是宽度
     * @param spec 测量规则
     * @return 真实的大小（像素）
     */
    private fun calculateSize(isWidth: Boolean, spec: Int): Int {
        // 获取测量模式和测量大小
        val mode = MeasureSpec.getMode(spec)
        val size = MeasureSpec.getSize(spec)

        var realSize = size
        when (mode) {
            MeasureSpec.EXACTLY -> {}
            MeasureSpec.AT_MOST -> {
                // 仅处理高度的wrap_content情况，宽度保持原样
                if (!isWidth) {
                    val defaultContentSize = dp2px(VIEW_HEIGHT_DEFAULT)
                    realSize = min(realSize.toDouble(), defaultContentSize.toDouble()).toInt()
                }
            }

            MeasureSpec.UNSPECIFIED -> {}
            else -> {}
        }
        return realSize
    }

    /**
     * 处理触摸事件
     * 实现滑动、点击、惯性滑动等交互功能
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 如果已释放资源，不处理触摸事件
        if (isReleased) {
            return false
        }
        // 初始化或获取速度跟踪器
        if (velocityTracker == null) {
            velocityTracker = VelocityTracker.obtain()
        }
        // 添加移动事件，用于计算速度
        velocityTracker?.addMovement(event)
        return interactionManager.onTouchEvent(event)
    }

    /**
     * 计算滚动
     * 当View重绘时由系统调用，用于实现平滑滚动效果
     * 处理惯性滑动和平滑过渡到刻度的逻辑
     */
    override fun computeScroll() {
        if (scroller.computeScrollOffset()) {
            interactionManager.currentDistance = scroller.currX.toFloat()
            interactionManager.calculateCurrentIndicatorValue()
            invalidate()
            checkVibrate()
            if (scroller.currX == scroller.finalX) {
                ruleChangedListener?.onScrollStop(
                    interactionManager.currentValue,
                    formatValueToLabel(interactionManager.currentValue)
                )
            }
        }
    }

    /**
     * 绘制视图
     * 按顺序绘制：背景、刻度和数字、指针
     *
     * @param canvas 画布
     */
    override fun onDraw(canvas: Canvas) {
        // 1 绘制刻度、数字
        drawGradation(canvas)
        // 2 绘制指针
        drawIndicator(canvas)
    }

    /**
     * 绘制刻度和刻度值
     * 计算可见范围内的刻度，并根据不同规则绘制长/短刻度和刻度值
     *
     * @param canvas 画布
     */
    private fun drawGradation(canvas: Canvas) {
        // 保存画布状态
        canvas.save()
        // 平移画布，处理padding
        canvas.translate(paddingStart.toFloat(), paddingTop.toFloat())

        // 使用规则绘制刻度
        if (gradationGapRules.isNotEmpty()) {
            drawGradationWithRules(canvas, ruleViewAttributes.textSize)
            canvas.restore()
        }
    }

    /**
     * 判断当前数字是否是某个单位数量的整数倍。
     *
     * @param number 当前数字
     * @param perUnitCount 单位数量
     */
    private fun isMultipleOf(number: Int, perUnitCount: Int): Boolean {
        return number % perUnitCount == MODULO_RESULT_ZERO
    }

    /**
     * 使用不同区间规则绘制刻度
     *
     * @param canvas 画布
     * @param textBaseline 文本基线位置
     */
    private fun drawGradationWithRules(canvas: Canvas, textBaseline: Float) {
        // 计算可见区域的值范围
        val (startNum, endNum) = calculateVisibleRange()

        // 依次绘制每个可见的刻度
        var currentNum = startNum
        while (currentNum <= endNum) {
            // 计算当前刻度对应的x坐标
            var distance = calculateDistance(currentNum)
            if (isRtl) {
                distance = width - distance
            }

            // 判断是否是长刻度（整数倍刻度）或特殊刻度
            val isLongGradation = isLongGradation(currentNum)

            // 绘制刻度
            drawGradation(canvas, distance, textBaseline, isLongGradation)

            // 如果是长刻度，绘制刻度值文本
            if (isLongGradation) {
                drawGradationText(canvas, distance, textBaseline, currentNum)
            }

            // 移动到下一个刻度
            currentNum += ruleViewAttributes.numberUnit
        }

        // 恢复文本画笔的透明度
        textPaint.alpha = FULL_ALPHA
    }

    /**
     * 计算可见区域的值范围
     */
    private fun calculateVisibleRange(): Pair<Int, Int> {
        // 计算左右可见区域对应的值范围
        val leftValue = interactionManager.calculateNumberFromDistance(
            max(
                DISTANCE_VALUE_DEFAULT,
                interactionManager.currentDistance - getHalfContentWidth()
            ),
            false
        )

        val rightValue = interactionManager.calculateNumberFromDistance(
            min(
                interactionManager.numberRangeDistance,
                interactionManager.currentDistance + getHalfContentWidth()
            ),
            false
        )

        // 扩展绘制范围，确保滑动时两侧有足够的刻度
        val expendUnit = ruleViewAttributes.numberUnit shl EXTEND_UNIT_SHIFT
        val startNum = max(ruleViewAttributes.minNumber, leftValue - expendUnit)
        val endNum = min(ruleViewAttributes.maxNumber, rightValue + expendUnit)

        return Pair(startNum, endNum)
    }

    /**
     * 计算当前刻度对应的x坐标
     */
    private fun calculateDistance(currentNum: Int): Float {
        return getHalfContentWidth() + (interactionManager.calculateDistanceFromNumber(currentNum) - interactionManager.currentDistance)
    }

    /**
     * 判断当前刻度是否是长刻度
     */
    private fun isLongGradation(currentNum: Int): Boolean {
        val currentValue = currentNum / VALUE_TO_NUMBER_SCALE
        val isSpecialGradation = specialGradations.any { it.value == currentValue }
        val perUnitCount = ruleViewAttributes.numberUnit * ruleViewAttributes.numberPerCount
        return isMultipleOf(currentNum, perUnitCount) || isSpecialGradation
    }

    /**
     * 绘制刻度
     */
    private fun drawGradation(canvas: Canvas, distance: Float, textBaseline: Float, isLongGradation: Boolean) {
        val gradationLen: Float?
        if (isLongGradation) {
            // 长刻度：使用长刻度颜色和宽度
            gradationPaint.color = ruleViewAttributes.longLineColor
            gradationPaint.strokeWidth = ruleViewAttributes.longLineWidth
            gradationLen = ruleViewAttributes.longGradationLen
        } else {
            // 短刻度
            gradationPaint.color = ruleViewAttributes.shortLineColor
            gradationPaint.strokeWidth = ruleViewAttributes.shortLineWidth
            gradationLen = ruleViewAttributes.shortGradationLen
        }
        // 从文字下方开始绘制刻度
        gradationPaint.let {
            canvas.drawLine(
                distance,
                textBaseline + ruleViewAttributes.textGradationGap,
                distance,
                textBaseline + ruleViewAttributes.textGradationGap + gradationLen,
                it
            )
        }
    }

    /**
     * 绘制刻度值文本
     */
    private fun drawGradationText(canvas: Canvas, distance: Float, textBaseline: Float, currentNum: Int) {
        val currentValue = currentNum / VALUE_TO_NUMBER_SCALE
        val text = currentValue.toString() + GRADATION_UNIT
        val textWidth = textPaint.measureText(text) ?: 0f

        // 计算当前刻度与中心线的距离，用于文本透明度渐变
        val distanceToCenter = abs((distance - getHalfContentWidth()).toDouble()).toFloat()
        var alpha = MAX_ALPHA
        val fadeDistance = textWidth * TEXT_FADE_DISTANCE_FACTOR
        if (distanceToCenter < fadeDistance) {
            val ratio = distanceToCenter / fadeDistance
            alpha = (MAX_ALPHA * (ratio * ratio)).toInt()
        }
        textPaint.alpha = alpha

        // 在刻度上方居中绘制文字
        textPaint.let {
            canvas.drawText(
                text,
                distance - (textWidth * HALF_SCALE_FACTOR),
                textBaseline,
                it
            )
        }
    }

    /**
     * 绘制指针
     * 绘制中间的垂直指示线，显示当前刻度位置
     */
    private fun drawIndicator(canvas: Canvas) {
        // 保存画布状态
        canvas.save()
        // 平移画布，处理padding
        canvas.translate(paddingStart.toFloat(), paddingTop.toFloat())
        val actualIndicatorLineWidth = if (interactionManager.isPressed) {
            ruleViewAttributes.indicatorLinePressedWidth
        } else {
            ruleViewAttributes.indicatorLineWidth
        }

        val actualIndicatorLineColor = if (interactionManager.isPressed) {
            ruleViewAttributes.indicatorLinePressedColor
        } else {
            ruleViewAttributes.indicatorLineColor
        }

        /**
         * 计算指示器线的起始Y坐标。起始Y坐标是基于文本大小（textSize）、文本与刻度之间的间距（textGradationGap）、
         * 长刻度的长度（longGradationLen）以及指示器线的长度（indicatorLineLen）来计算的。
         */
        val startY = ruleViewAttributes.textSize +
                ruleViewAttributes.textGradationGap +
                ruleViewAttributes.longGradationLen -
                ruleViewAttributes.indicatorLineLen

        // 设置指示器线的颜色和宽度
        gradationPaint.color = actualIndicatorLineColor
        gradationPaint.strokeWidth = actualIndicatorLineWidth
        // 在中心位置绘制指示器，长度为actualIndicatorLineLen
        gradationPaint.let {
            canvas.drawLine(
                getHalfContentWidth().toFloat(),
                startY,
                getHalfContentWidth().toFloat(),
                startY + ruleViewAttributes.indicatorLineLen,
                it
            )
        }
        // 恢复画布状态
        canvas.restore()
    }

    /**
     * 将dp值转换为像素值
     *
     * @param dp 密度无关像素值
     * @return 实际像素值
     */
    private fun dp2px(dp: Float): Int {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.displayMetrics)
            .toInt()
    }

    /**
     * 设置新的当前值
     * @param currentIndicatorValue 当前指标值，应位于规则视图的最小值和最大值范围内
     * @param useAnimation 是否使用动画来展示值的变化，默认为false
     */
    fun setCurrentValue(currentIndicatorValue: Float, useAnimation: Boolean = false) {
        if ((currentIndicatorValue < ruleViewAttributes.minValue) || (currentIndicatorValue > ruleViewAttributes.maxValue)) {
            GLog.e(
                TAG,
                LogFlag.DL,
                "[setCurrentValue] $currentIndicatorValue out of the valid range: [$ruleViewAttributes.minValue, $ruleViewAttributes.maxValue]"
            )
            return
        }
        if (currentIndicatorValue == interactionManager.currentValue) {
            GLog.e(TAG, LogFlag.DL, "[setCurrentValue] currentIndicatorValue equals to currentValue")
            return
        }

        // 强制结束当前滚动动画
        if (scroller.isFinished == false) {
            scroller.forceFinished(true)
        }

        interactionManager.currentValue = currentIndicatorValue

        interactionManager.currentNumber =
            (interactionManager.currentValue * VALUE_TO_NUMBER_SCALE).toInt()

        if (useAnimation) {
            // 使用区间规则计算新位置
            val newDistance =
                interactionManager.calculateDistanceFromNumber(interactionManager.currentNumber)
            val dx = (newDistance - interactionManager.currentDistance).toInt()

            // 根据距离计算动画时长，最大2000ms
            val duration = min(
                (abs(dx.toDouble()) * MAX_VALUE_CHANGE_DURATION / interactionManager.numberRangeDistance),
                MAX_VALUE_CHANGE_DURATION.toDouble()
            ).toInt()

            // 启动滚动动画
            scroller.startScroll(
                interactionManager.currentDistance.toInt(),
                START_Y_POSITION_DEFAULT,
                dx,
                Y_POSITION_OFFSET_DEFAULT,
                duration
            )
        } else {
            interactionManager.currentDistance =
                interactionManager.calculateDistanceFromNumber(interactionManager.currentNumber)
        }
        postInvalidate()
    }

    /**
     * 设置不同区间的刻度间隔
     * 允许在不同的值范围内使用不同的像素间距
     *
     * @param gapRules 刻度间隔规则列表，每个规则定义一个区间和对应的像素间距
     * @param initialValue 可选参数，设置初始刻度值。如果不指定，保持当前值
     */
    fun setGradationGapRules(gapRules: List<GradationGapRule>) {
        // 确保规则列表不为空
        if (gapRules.isEmpty()) {
            return
        }

        // 验证规则列表覆盖了整个值范围
        val sortedRules = gapRules.sortedBy { it.startValue }

        // 更新视图的最小值和最大值
        ruleViewAttributes.minValue = sortedRules.first().startValue
        ruleViewAttributes.maxValue = sortedRules.last().endValue
        ruleViewAttributes.minNumber = (ruleViewAttributes.minValue * VALUE_TO_NUMBER_SCALE).toInt()
        ruleViewAttributes.maxNumber = (ruleViewAttributes.maxValue * VALUE_TO_NUMBER_SCALE).toInt()

        // 保存规则列表
        gradationGapRules.clear()
        gradationGapRules.addAll(sortedRules.toList())

        // 转换值并重新计算内部参数
        interactionManager.convertValue2Number()
        postInvalidate()
    }

    /**
     * 视图可见性变化时的回调
     * 在视图不可见时停止动画，减少资源消耗
     */
    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility != VISIBLE) {
            // 视图不可见时停止动画
            scroller.abortAnimation()
        }
    }

    /**
     * 视图从窗口分离时的回调
     * 释放资源，避免内存泄漏
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 释放资源
        release()
    }

    /**
     * 释放资源方法，防止内存泄漏
     * 可在Activity/Fragment的onDestroy中主动调用
     */

    fun release() {
        // 避免重复释放
        if (isReleased) {
            return
        }
        // 停止所有动画
        scroller.abortAnimation()
        // 释放速度跟踪器
        velocityTracker?.recycle()
        // 移除所有回调
        ruleChangedListener = null
        vibrator = null
    }

    /**
     * 格式化值为标签文本
     * @param value 需要格式化的值
     * @return 格式化后的标签文本
     */
    private fun formatValueToLabel(value: Float): String {
        // 格式化浮点数，确保小数部分符合预期
        return String.format(FLOAT_FORMAT_PATTERN, value) + GRADATION_UNIT
    }

    /**
     * 设置特殊长刻度
     * 这些刻度会像主刻度一样显示为长刻度
     */
    fun setSpecialGradations(specialValues: List<SpecialGradationRule>) {
        specialGradations.clear()
        specialGradations.addAll(specialValues)
        invalidate()
    }

    companion object {
        // 刻度单位，用于表示倍数（例如 "x" 表示倍数）
        const val GRADATION_UNIT = "x"
        // OPPO Sans 4.0 SC字体名字
        private const val OPPO_SANS_FC_FONT_FAMILY_NAME = "OPPO Sans 4.0 SC"
        //表示当前未检查震动的距离
        private const val DISTANCE_UNCHECKED_VIBRATE = -1f
        //float 精度的转化格式
        private const val FLOAT_FORMAT_PATTERN = "%.1f"
        //日志
        private const val TAG = "RuleView"
        // 缩放因子的一半，用于计算某些比例或偏移量（例如 0.5 表示一半的比例）
        private const val HALF_SCALE_FACTOR = 0.5f
        // 数字到值的缩放因子，用于将数字转换为实际值
        private const val VALUE_TO_NUMBER_SCALE = 10f
        // 默认距离值，滚动距离或偏移量
        private const val DISTANCE_VALUE_DEFAULT = 0f
        // 默认视图高度
        private const val VIEW_HEIGHT_DEFAULT = 60f
        // 起始 Y 坐标
        private const val START_Y_POSITION_DEFAULT = 0
        // Y 坐标默认偏移量
        private const val Y_POSITION_OFFSET_DEFAULT = 0
        // 初始垂直速度（垂直方向没有滚动，速度为 0）
        private const val INITIAL_VERTICAL_VELOCITY = 0
        // 水平方向最小滚动范围（X 方向的最小值，通常为 0）
        private const val HORIZONTAL_SCROLL_RANGE_MIN = 0
        // 垂直方向最小滚动范围（Y 方向的最小值，通常为 0）
        private const val VERTICAL_SCROLL_RANGE_MIN = 0
        // 垂直方向最大滚动范围（垂直方向没有滚动，最大值也为 0）
        private const val VERTICAL_SCROLL_RANGE_MAX = 0
        //定义速度的单位时间（单位为毫秒）
        private const val FLING_VELOCITY_UNIT = 1000
        // 模运算结果为零的常量
        private const val MODULO_RESULT_ZERO = 0
        // 文本画笔的完全不透明透明度值
        private const val FULL_ALPHA = 255
        /** 值变化最大动画时间，单位毫秒  */
        private const val MAX_VALUE_CHANGE_DURATION = 1000
        /** 文本渐变距离系数，控制文本在接近中心线时的渐变效果  */
        private var TEXT_FADE_DISTANCE_FACTOR = 1.9f
        /** 最大透明度值  */
        private const val MAX_ALPHA = 255
        /** 扩展单位位移量，用于计算左右两侧额外绘制的刻度数量  */
        private const val EXTEND_UNIT_SHIFT = 1
        // 控制振动效果的强度
        private const val VIBRATE_EFFECT_STRENGTH = 50
        // 震动时间
        private const val VIBRATE_TIME = 15L
        /**
         * 从右到左（RTL）布局的乘数为1
         */
        private const val RTL_MULTIPLIER = 1
        /**
         * 定义了从左到右（LTR）布局的乘数为-1
         */
        private const val LTR_MULTIPLIER = -1
    }
}

/**
 * 值变化监听器接口
 * 当控件的值发生变化时，会回调此接口方法
 */
interface OnRuleChangedListener {
    /**
     * 当 RuleView 被按下时回调
     *
     */
    fun onStartDragging()
    /**
     * 值变化的回调方法
     *
     * @param value 当前值
     */
    fun onValueChanged(value: Float)
    /**
     * 滑动停止的回调方法
     *
     * @param value 当前值
     * @param label 当前刻度标签文本
     */
    fun onScrollStop(value: Float, label: String)
}