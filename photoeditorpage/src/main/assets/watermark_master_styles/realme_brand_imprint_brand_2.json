{"styleId": "realme_brand_imprint_brand_2", "baseImageSize": 360, "size": {"leftMargin": 10, "topMargin": 10, "rightMargin": 162, "bottomMargin": 10}, "imageOffset": {"startX": 10, "startY": 10}, "background": {"backgroundType": 0, "color": "#FFFFFF"}, "bitmaps": [{"direction": 2, "orientation": 0, "position": {"borderX": 10, "borderY": 10, "gravity": "center"}, "width": 162, "height": -1, "elements": [{"id": 1, "visible": true, "content": {"type": "elements", "orientation": 1, "width": 122}, "elements": [{"id": 2, "visible": true, "editable": true, "content": {"type": "image", "bitmapSourceType": 0, "bitmap": "realme_brand_logo_2", "bitmapResName": "realme_brand_logo_2", "width": 52, "height": 52, "scaleType": 5}, "position": {"layoutGravity": "horCenter", "layoutGravityEnable": true, "topMargin": 0}}, {"id": 3, "visible": true, "editable": false, "content": {"type": "text", "textSource": 0}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "textSize": 12, "letterSpacing": 0.02, "lineHeight": 14.06, "fontWeight": 600, "alpha": 0.9, "colors": ["#000000"], "gradientType": -1}, "position": {"layoutGravity": "horCenter", "layoutGravityEnable": true, "topMargin": 14}}, {"id": 4, "visible": true, "editable": true, "content": {"type": "text", "textSource": 1}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "textSize": 8, "fontWeight": 400, "letterSpacing": 0.06, "lineHeight": 9.38, "alpha": 0.54, "colors": ["#000000"], "gradientType": -1}, "position": {"layoutGravity": "horCenter", "layoutGravityEnable": true, "topMargin": 8}}, {"id": 5, "visible": true, "editable": true, "content": {"type": "text", "textSource": 3}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "textSize": 8, "fontWeight": 400, "letterSpacing": 0.06, "lineHeight": 9.38, "alpha": 0.54, "colors": ["#000000"], "gradientType": -1}, "position": {"layoutGravity": "horCenter", "layoutGravityEnable": true, "topMargin": 5}}]}]}]}