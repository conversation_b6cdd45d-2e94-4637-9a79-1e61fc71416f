/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - GalleryVideoTrimView.java
 ** Description: for video thumbnail view.
 ** Version: 1.0
 ** Date : 2017/11/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/14    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryThumbController;

public class GalleryVideoThumbnailView extends LinearLayout implements IGalleryThumbController {

    private IGalleryThumbController mThumbController;


    public GalleryVideoThumbnailView(Context context) {
        super(context);
    }

    public GalleryVideoThumbnailView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public GalleryVideoThumbnailView(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
    }

    public GalleryVideoThumbnailView(Context context, AttributeSet attributeSet, int i, int i1) {
        super(context, attributeSet, i, i1);
    }

    public void setThumbController(IGalleryThumbController controller) {
        mThumbController = controller;
    }

    @Override
    public long mapTimelinePosFromX(int x) {
        if (mThumbController != null) {
            return mThumbController.mapTimelinePosFromX(x);
        }
        return 0;
    }

    @Override
    public int mapXFromTimelinePos(long time) {
        if (mThumbController != null) {
            return mThumbController.mapXFromTimelinePos(time);
        }
        return 0;
    }

    @Override
    public double getPixelPerMicrosecond() {
        if (mThumbController != null) {
            return mThumbController.getPixelPerMicrosecond();
        }
        return 0;
    }

    @Override
    public int getScrollState() {
        if (mThumbController != null) {
            return mThumbController.getScrollState();
        }
        return GalleryHorizontalScrollView.SCROLL_STATE_IDLE;
    }

    @Override
    public void setScrollListener(ThumbScrollerListener scrollerListener) {
        if (mThumbController != null) {
            mThumbController.setScrollListener(scrollerListener);
        }
    }

    @Override
    public void setTouchListener(OnTouchListener touchListener) {
        if (mThumbController != null) {
            mThumbController.setTouchListener(touchListener);
        }
    }

    @Override
    public void smoothScrollTo(int x, int y) {
        if (mThumbController != null) {
            mThumbController.smoothScrollTo(x, y);
        }
    }

    @Override
    public void scrollTo(int x, int y) {
        if (mThumbController != null) {
            mThumbController.scrollTo(x, y);
        }
    }

    @Override
    public void fullScroll(int focusPos) {
        if (mThumbController != null) {
            mThumbController.fullScroll(focusPos);
        }
    }

    @Override
    public void stopScroll() {
        if (mThumbController != null) {
            mThumbController.stopScroll();
        }
    }
}
