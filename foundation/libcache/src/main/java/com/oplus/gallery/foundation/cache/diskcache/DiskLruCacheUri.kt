/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DiskLruCacheUri.kt
 ** Description: 定义DiskLruCache的uri规范，实现Uri和DiskLruCacheUri的互相转换
 ** Version: 1.0
 ** Date: 2022/5/5
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2022/5/5    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.foundation.cache.diskcache

import android.net.Uri
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING

/**
 * 构建一个uri来打包访问DiskLruCache需要的各项参数。
 * ```
 * - scheme: 按照目前规则定义，必须是：DISK_LRU_CACHE_SCHEME。如果有需要，可以扩展规则。
 * - authority: 此参数不影响功能，可以业务自行定义，用于业务来源识别，可以是业务的包名。
 * - path: 缓存所在目录
 * - appVersion: app版本号，对应versionCode
 * - valueCount: 每个缓存条目的值的数量
 * - maxSize: 此缓存应用于存储的最大字节数
 * - cacheKey: 缓存中某条内容的key
 * ```
 */
class DiskLruCacheUri {
    var scheme: String = DISK_LRU_CACHE_SCHEME
        private set
    var authority: String = EMPTY_STRING
        private set
    var path: String = EMPTY_STRING
        private set
    var appVersion: Int = 0
        private set
    var valueCount: Int = 0
        private set
    var maxSize: Long = 0
        private set
    var cacheKey: String = EMPTY_STRING
        private set

    @Suppress("LongParameterList")
    constructor(
        scheme: String = DISK_LRU_CACHE_SCHEME,
        authority: String = EMPTY_STRING,
        path: String = EMPTY_STRING,
        appVersion: Int = 0,
        valueCount: Int = 0,
        maxSize: Long = 0,
        cacheKey: String = EMPTY_STRING
    ) {
        this.scheme = scheme
        this.authority = authority
        this.path = path
        this.appVersion = appVersion
        this.valueCount = valueCount
        this.maxSize = maxSize
        this.cacheKey = cacheKey
    }

    constructor(uri: Uri) {
        scheme = uri.scheme ?: EMPTY_STRING
        if (isInvalidScheme()) {
            GLog.w(TAG, LogFlag.DL, "constructor, invalid scheme.")
            return
        }

        authority = uri.authority ?: EMPTY_STRING
        if (isInvalidAuthority()) {
            GLog.w(TAG, LogFlag.DL, "constructor, invalid authority.")
            return
        }

        path = uri.path ?: EMPTY_STRING
        if (isInvalidPath()) {
            GLog.w(TAG, LogFlag.DL, "constructor, invalid path.")
            return
        }

        appVersion = uri.getQueryParameter(PARAM_APP_VERSION)?.takeIf { it.isNotEmpty() }?.toInt() ?: 0
        valueCount = uri.getQueryParameter(PARAM_VALUE_COUNT)?.takeIf { it.isNotEmpty() }?.toInt() ?: 0
        maxSize = uri.getQueryParameter(PARAM_MAX_SIZE)?.takeIf { it.isNotEmpty() }?.toLong() ?: 0
        cacheKey = uri.getQueryParameter(PARAM_CACHE_KEY) ?: EMPTY_STRING
        if (isInvalidQueryParams()) {
            GLog.w(TAG, LogFlag.DL, "constructor, invalid query param.")
            return
        }
    }

    /**
     * @return Uri 参数合法，返回使用DiskLruCache相关参数组装成的uri；参数非法，则返回 [Uri.EMPTY]。
     */
    fun getUri(): Uri =
        if (isInvalidUri()) Uri.EMPTY
        else Uri.Builder()
            .scheme(scheme)
            .authority(authority)
            .path(path)
            .appendQueryParameter(PARAM_APP_VERSION, appVersion.toString())
            .appendQueryParameter(PARAM_VALUE_COUNT, valueCount.toString())
            .appendQueryParameter(PARAM_MAX_SIZE, maxSize.toString())
            .appendQueryParameter(PARAM_CACHE_KEY, cacheKey)
            .build()

    /**
     * 当前Uri是否是一个合法的DiskLruCacheUri。判断标准是其scheme是否是 [DISK_LRU_CACHE_SCHEME]
     */
    fun isInvalidUri(): Boolean = isInvalidScheme() || isInvalidAuthority() || isInvalidPath() || isInvalidQueryParams()

    private fun isInvalidScheme(): Boolean = !DISK_LRU_CACHE_SCHEME.equals(scheme, true)

    private fun isInvalidAuthority(): Boolean = authority.isEmpty()

    private fun isInvalidPath(): Boolean = path.isEmpty()

    private fun isInvalidQueryParams(): Boolean = (appVersion <= 0) || (valueCount <= 0) || (maxSize <= 0) || cacheKey.isEmpty()


    companion object {
        private const val TAG = "DiskLruCacheUri"
        private const val DISK_LRU_CACHE_SCHEME = "disklrucache"

        // query param key
        private const val PARAM_APP_VERSION = "app_version"
        private const val PARAM_VALUE_COUNT = "value_count"
        private const val PARAM_MAX_SIZE = "max_size"
        private const val PARAM_CACHE_KEY = "cache_key"
    }
}