/**************************************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -   EditorSongVM.kt
 ** Description:
 ** Version: 1.0
 ** Date :  2025/05/22
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 **  kuang<PERSON><PERSON><PERSON>@yeahzee.com  2025/05/22  1.0
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.music.viewmodel

import android.app.Application
import android.content.ContentUris
import android.provider.MediaStore
import androidx.annotation.WorkerThread
import androidx.collection.arrayMapOf
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.addon.utils.VersionHelper.isAtLeastAndroidR
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.common.Constants
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener
import com.oplus.gallery.videoeditorpage.memories.resource.listener.SimpleLoadFileListener
import com.oplus.gallery.videoeditorpage.resource.data.SongByTagResponseBean
import com.oplus.gallery.videoeditorpage.resource.listener.SimpleLoadDataListener
import com.oplus.gallery.videoeditorpage.resource.manager.SongResourceManager
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item
import com.oplus.gallery.videoeditorpage.resource.room.bean.SongItem
import com.oplus.gallery.videoeditorpage.resource.room.entity.BaseMusicItem
import com.oplus.gallery.videoeditorpage.resource.room.entity.SongEntity
import com.oplus.gallery.videoeditorpage.resource.storage.VideoAudioStorage
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.video.business.base.ApplyResourceData
import com.oplus.gallery.videoeditorpage.video.business.base.BaseResourceViewModel
import com.oplus.gallery.videoeditorpage.video.business.music.source.cloud.util.MusicConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlin.collections.set

/**
 * 歌曲编辑视图模型，用于管理视频编辑页面中的音乐资源
 * 继承自BaseResourceViewModel，处理歌曲资源的加载和管理
 *
 * @param application 应用程序上下文
 */
class EditorSongVM(application: Application) : BaseResourceViewModel<SongItem, SongByTagResponseBean.SongListBean>(application) {
    /**
     * 可变状态流，用于保存当前加载的音乐项列表
     * 初始值为空的可变列表
     */
    private val _data = MutableStateFlow<MutableList<BaseMusicItem>>(mutableListOf())

    /**
     * 下载完成的item集合，用于列表刷新时使用 key是songid ,value是item位置索引
     */
    val downloadedMap by lazy { arrayMapOf<Int, Int>() }

    /**
     * 下载中 的item集合，用于列表刷新时使用 key是songid ,value是item位置索引
     */
    val downloadingMap by lazy { arrayMapOf<Int, Int>() }

    /**
     * 将 MutableStateFlow 类型的 _data 转换为共享的 StateFlow，供外部观察数据变化。
     *
     * 使用 stateIn 操作符将数据流挂接到 ViewModel 的生命周期，并在没有订阅者时合理释放资源，
     * 避免不必要的资源消耗。
     *
     * @param scope 设置协程作用域为 viewModelScope，确保数据流与 ViewModel 生命周期绑定
     * @param started 设置共享策略为 WhileSubscribed(STOP_FLOW_DELAY_MS)，表示：
     *        - 当有活跃的订阅者（如 UI）时开始发射数据
     *        - 在最后一个订阅者取消订阅后 5 秒内若无新订阅者接入，则停止数据流
     * @param initialValue 设置初始值为空的可变列表，保证在未加载完成前 UI 可以安全访问数据
     */
    val listData: StateFlow<MutableList<BaseMusicItem>> = _data.stateIn(
        scope = viewModelScope, started = SharingStarted.WhileSubscribed(STOP_FLOW_DELAY_MS), initialValue = mutableListOf()
    )

    val videoAudioStorage by lazy { VideoAudioStorage() }
    override val currentResourceManager = SongResourceManager(application)
    private val loadFileListener by lazy {
        object : OnLoadFileListener<SongItem> {
            override fun onProgress(progress: Int, item: SongItem) {
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOADING_FILE))
            }

            override fun onFinish(item: SongItem) {
                // 音乐图标下载完成也是回调这里，这里把下载完图标的SongItem传递出去更新列表UI
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_FINISH, data = item))
            }

            override fun onError(errCode: ErrorCode, item: SongItem?) {
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_ERROR, errCode))
            }

            override fun onCancel(item: SongItem) {
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_CANCEL))
            }
        }
    }
    private val onLoadDataListener by lazy {
        object : SimpleLoadDataListener<SongItem>() {
            override fun onLoadRefresh(items: List<SongItem>) {
                GLog.d(TAG, LogFlag.DL, "[onLoadRefresh], size=${items.size}")
                _data.value = items.map {
                    currentResourceManager.loadIcon(it.itemUniqueId, loadFileListener)
                    it.convertToSongEntity()
                }.toMutableList()
            }

            override fun onError(errCode: ErrorCode) {
                GLog.e(TAG, LogFlag.DL, "onError, errCode=$errCode")
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_ERROR, errCode))
            }
        }
    }

    @JvmOverloads
    fun loadData(categoryId: Int = MusicConstants.CategoryId.ID_INVALID) {
        viewModelScope.launch(Dispatchers.IO) {

            when (categoryId) {
                /**
                 * 请求云音乐
                 */
                MusicConstants.CategoryId.CLOUD_CATEGORY -> loadCloudData()
                /**
                 * sd卡公共目录本地音乐
                 */
                MusicConstants.CategoryId.LOCAL_CATEGORY -> _data.value = loadAllLocalMusicSync()
                /**
                 * 视频提取音乐
                 */
                MusicConstants.CategoryId.LOCAL_VIDEO_AUDIO_CATEGORY -> _data.value = loadAllLocalVideoMusicSync()
                /**
                 * 加载asset中的资源
                 */
                MusicConstants.CategoryId.BUILDIN_CATEGORY -> currentResourceManager.loadList(false, onLoadDataListener)

                else -> Unit
            }
        }
    }

    /**
     * 同步加载本地所有音乐文件，并将其转换为 BaseMusicEntity 列表
     * @return 包含所有本地音乐文件信息的 BaseMusicEntity 列表
     */
    private fun loadAllLocalMusicSync(): MutableList<BaseMusicItem> {
        val entityList: MutableList<BaseMusicItem> = ArrayList()
        val projection = arrayOf(
            MediaStore.Audio.Media._ID,
            MediaStore.Audio.Media.TITLE,
            MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.DURATION,
            MediaStore.Audio.Media.ARTIST,
            MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.SIZE
        )
        val sortOrder = "${MediaStore.Audio.Media.DATE_ADDED} DESC"
        // 增加查询条件，限制音频文件类型
        val selection = "${MediaStore.Audio.Media.MIME_TYPE} IN (${Constants.SUPPORT_AUDIO_TYPE.joinToString(",") { "'$it'" }})"
        val cursor = ContextGetter.context.contentResolver.query(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, projection, selection, null, sortOrder
        )

        cursor.use {
            if ((it == null) || (it.count <= 0)) {
                GLog.e(TAG, LogFlag.DL, "cursor is null or count is 0.")
                return entityList
            }

            val titleIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)
            val displayNameIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME)
            val durationIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
            val artistIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)
            val dataIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
            val sizeIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)
            val idIndex = it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
            while (it.moveToNext()) {
                val entity = SongEntity()
                entity.songId = idIndex

                entity.zhName = it.getString(titleIndex) ?: ""
                entity.chName = entity.zhName
                entity.enName = entity.zhName

                val duration = it.getInt(durationIndex)
                entity.timeSecondLength = if (duration >= 0) duration / TimeUtils.MILLISECOND_IN_SECOND else 0
                entity.fileSize = it.getLong(sizeIndex)
                entity.singer = it.getString(artistIndex) ?: ""

                if (isAtLeastAndroidR()) {
                    val id = it.getInt(idIndex)
                    entity.filePath = ContentUris.withAppendedId(
                        MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id.toLong()
                    ).toString()
                    entity.systemFilePath = it.getString(displayNameIndex) ?: ""
                } else {
                    entity.filePath = it.getString(dataIndex) ?: ""
                }
                entity.downloadState = Item.TYPE_DOWNLOAD_FILE
                entityList.add(entity)
            }
        }
        return entityList
    }

    @WorkerThread
    private fun loadAllLocalVideoMusicSync(): MutableList<BaseMusicItem> {
        return videoAudioStorage.getBuiltin().toMutableList()
    }

    /**
     * 加载网络云音乐
     */
    private fun loadCloudData() {
        currentResourceManager.loadList(true, onLoadDataListener)
    }

    override fun download(id: String, listener: OnLoadFileListener<SongItem>?) {
        super.download(id, object : SimpleLoadFileListener<SongItem>(listener) {
            override fun onProgress(progress: Int, item: SongItem) {
                updateProgress(item)
                listener?.onProgress(progress, item)
            }

            override fun onFinish(item: SongItem) {
                updateSongItem(item)
                listener?.onFinish(item)
            }
        })
    }

    /**
     * 更新指定歌曲项的进度。
     * @param songItem 要更新进度的歌曲项。
     * @return 更新后的进度索引，如果未找到对应歌曲项则返回 INVALID_INDEX。
     */
    fun updateProgress(songItem: SongItem): Int {
        val musicEntities = listData.value
        return musicEntities.indexOfFirst { songItem.songId == it.songId.toString() }.getOrLog(TAG, "[updateProgress],musicEntities IS NULL")?.let {
            val songEntity = musicEntities[it] as SongEntity
            downloadingMap[songEntity.songId] = it
            songEntity.progress = songItem.progress
            GLog.d(TAG, LogFlag.DL, "[updateProgress], ${songItem.getName()}, progress = ${songItem.progress},$it")
            it
        } ?: INVALID_INDEX
    }

    /**
     * 更新指定歌曲项的信息。
     * @param songItem 要更新的歌曲项。
     * @return 更新后的索引，如果未找到对应歌曲项则返回 INVALID_INDEX。
     */
    fun updateSongItem(songItem: SongItem): Int {
        val musicEntities = listData.value
        return musicEntities.indexOfFirst { songItem.songId == it.songId.toString() }.getOrLog(TAG, "[updateSongItem],musicEntities IS NULL")?.let {
            musicEntities[it].let { v ->
                downloadedMap[v.songId] = it
                (v as SongEntity).progress = songItem.progress
                v.downloadState = songItem.downloadState
                v.filePath = songItem.resourcePath
                if (v.timeSecondLength <= 0) {
                    v.timeSecondLength = parseDuration(v.filePath)
                }
            }
            GLog.d(TAG, LogFlag.DL, "[updateSongItem], ${songItem.getName()}, progress = ${songItem.progress},$it")
            it
        } ?: INVALID_INDEX
    }

    /**
     * 获取文件的持续时间（以秒为单位）
     * @return 文件的持续时间（秒）
     */
    @WorkerThread
    fun parseDuration(filePath: String?): Int {
        if (filePath.isNullOrEmpty()) return 0
        val avFileInfo = EditorEngineGlobalContext.getInstance().getNvsAVFileInfo(filePath)
        if (avFileInfo == null) {
            GLog.e(TAG, LogFlag.DL, "[parseDuration] file info is null")
            return 0
        }
        GLog.e(TAG, LogFlag.DL, "[parseDuration] $filePath, ${avFileInfo.duration}")
        return (avFileInfo.duration / TimeUtils.MILLISECOND_IN_SECOND / TimeUtils.MILLISECOND_IN_SECOND).toInt()
    }

    override fun clearDownloadStatus() {
        super.clearDownloadStatus()
        downloadedMap.clear()
        downloadingMap.clear()
    }

    override fun onCleared() {
        super.onCleared()
        currentResourceManager.release()
    }

    companion object {
        private const val TAG = "EditorSongVM"
        private const val STOP_FLOW_DELAY_MS = 5000L
    }
}
