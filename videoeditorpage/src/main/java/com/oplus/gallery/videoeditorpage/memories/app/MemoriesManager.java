/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MemoriesManager.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/27
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/27    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.app;

import static com.oplus.breakpad.BreakpadUtil.generateKey;
import static com.oplus.breakpad.NativeCrashGuardKt.runNativeGuarding;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG;
import static com.oplus.gallery.business_lib.selectionpage.SelectInputDataKt.KEY_IS_SUPPORT_PREVIEW_PHOTO;
import static com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper.SCAN_FILE_TIME_OUT;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.INVALID_MEMORIES_SET_ID;
import static com.oplus.gallery.framework.abilities.data.DataRepository.KEY_PATH_STR;
import static com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.TYPE_ALL_PICTURE;
import static com.oplus.gallery.framework.abilities.data.DataRepository.MemoriesModelGetter.TYPE_MEMORIES_ALBUM;
import static com.oplus.gallery.framework.abilities.data.DataRepository.SpecifiedDataAlbumModelGetter.KEY_ID_LIST;
import static com.oplus.gallery.framework.abilities.data.DataRepository.SpecifiedDataAlbumModelGetter.KEY_MEDIA_TYPE_SUPPORT;
import static com.oplus.gallery.framework.abilities.data.DataRepository.SpecifiedDataAlbumModelGetter.KEY_ORDER_TYPE;
import static com.oplus.gallery.framework.abilities.data.DataRepository.SpecifiedDataAlbumModelGetter.TYPE_ID_LIST_ALBUM;
import static com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_ALBUM_ANY_ID_LIST;
import static com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_ALBUM_MEMORIES_ANY;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL;
import static com.oplus.gallery.business_lib.model.data.internal.IdListAlbum.ORDER_TYPE_DATE_TAKEN;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.KEY_MEMORIES_EDITOR;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.KEY_MEMORIES_SET_ID;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.KEY_MEMORIES_SUB_TITLE;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.KEY_MEMORIES_TITLE;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MAX;
import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MIN;
import static com.oplus.gallery.business_lib.selectionpage.SelectInputDataKt.KEY_REQUEST_KEY;
import static com.oplus.gallery.business_lib.selectionpage.SelectInputDataKt.KEY_RESULT_CODE;
import static com.oplus.gallery.business_lib.selectionpage.SelectInputDataKt.KEY_RESULT_DATA;
import static com.oplus.gallery.business_lib.selectionpage.SelectInputDataKt.KEY_RESULT_DATA_LIST;
import static com.oplus.gallery.business_lib.selectionpage.SelectMode.SELECT_ALBUM_ONLY;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.SECONDS_1000;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentKt;

import com.coui.appcompat.theme.COUIThemeOverlay;
import com.oplus.gallery.addon.osense.CpuFrequencyManager;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants;
import com.oplus.gallery.business_lib.model.selection.PathSelectionManager;
import com.oplus.gallery.business_lib.model.selection.SelectionData;
import com.oplus.gallery.business_lib.selectionpage.SelectInputData;
import com.oplus.gallery.business_lib.selectionpage.SelectInputDataKt;
import com.oplus.gallery.business_lib.selectionpage.SelectType;
import com.oplus.gallery.business_lib.selectionpage.SelectionFrom;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider;
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper;
import com.oplus.gallery.foundation.tracing.helper.LaunchExitPopupTrackHelper;
import com.oplus.gallery.foundation.ui.dialog.ProgressDialog;
import com.oplus.gallery.foundation.ui.systembar.IStatusBarController;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.ext.BitmapFactoryKt;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.systemcore.IntentUtils;
import com.oplus.gallery.framework.abilities.codec.CodecHelper;
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.router_lib.Starter;
import com.oplus.gallery.router_lib.meta.PostCard;
import com.oplus.gallery.standard_lib.codec.OriginalRegionDecoder;
import com.oplus.gallery.standard_lib.file.Dir;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog;
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.util.UserEventMap;
import com.oplus.gallery.videoeditorpage.memories.MemoriesProviderHelper;
import com.oplus.gallery.videoeditorpage.memories.UpdateMemoriesDataCallBack;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.memories.util.VideoStorageHelper;
import com.oplus.gallery.videoeditorpage.memories.engine.GalleryVideoEngineManager;
import com.oplus.gallery.videoeditorpage.memories.business.photo.EditorPhotoState;
import com.oplus.gallery.videoeditorpage.memories.business.preview.EditorPreviewState;
import com.oplus.gallery.videoeditorpage.memories.data.MemoriesInfo;
import com.oplus.gallery.videoeditorpage.memories.data.MemoriesStore;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;

import org.jetbrains.annotations.NotNull;

import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import kotlin.Unit;

public class MemoriesManager {

    public static final boolean DUMP_DEBUG = true;
    public static final CpuFrequencyManager.Action SAVE_VIDEO_ACTION
        = CpuFrequencyManager.Action.STORAGE_BOOST;
    public static final int SAVE_VIDEO_DURATION = TimeUtils.TIME_2_MIN_IN_MS;
    public static final int MAX_PROGRESS = 100;
    public static final long ENSURE_SPACE_MB = 100 * 1024 * 1024;

    private static final String KEY_MEMORIES_MANAGER_TO_COVER = "memoriesManagerToCover.requestKey";
    private static final String KEY_MEMORIES_MANAGER_TO_CREATE = "memoriesManagerToCreate.requestKey";
    private static final String MEMORIES_VIDEO_PREFIX = "Story_";
    private static final String MEMORIES_VIDEO_SUFFIX = ".mp4";
    private static final String MEMORIES_VIDEO_SUFFIX_TMP = ".memories";
    private static final String TAG = "MemoriesManager";
    private static final ArrayList<MediaInfo> CURRENT_VIDEO_FILE_LIST = new ArrayList<>();
    private static final Map<String, Bitmap> THUMBNAIL_CACHE = new ConcurrentHashMap<>();
    private static final Comparator<MediaInfo> MEDIA_TIME_COMPARATOR = new Comparator<MediaInfo>() {
        public final int compare(MediaInfo info1, MediaInfo info2) {
            long compare = info1.mFileTime - info2.mFileTime;
            if (compare < 0) {
                return -1;
            } else if (compare > 0) {
                return 1;
            } else {
                return 0;
            }
        }
    };
    private static MemoriesInfo sBaseMemoriesInfo = new MemoriesInfo();
    private static MemoriesInfo sCurMemoriesInfo = new MemoriesInfo();
    private static int sSetId = -1;
    private static boolean sGOInTakeCover = false;
    private final GalleryVideoEngineManager mOplusEngineManager;
    private final Context mContext;
    private final Handler mHandler;
    private final HandlerThread mWorkerThread;

    private long mExportTime = 0;
    private File mCurSaveDir = null;
    private String mVideoFileName = null;
    private Uri mMediaUri = null;

    /**
     * saving video dialog
     */
    private ProgressDialog mProgressDialog;
    private Handler mWorker;

    public MemoriesManager(Context context, Handler handler, GalleryVideoEngineManager engineManager) {
        mContext = context;
        mHandler = handler;
        mOplusEngineManager = engineManager;
        mWorkerThread = new HandlerThread("MemoriesManagerTask");
        mWorkerThread.start();
        mWorker = new Handler(mWorkerThread.getLooper());
    }

    public void destroy(boolean clearAll) {
        if (mWorkerThread != null) {
            mWorkerThread.quitSafely();
            mWorker = null;
        }
        if (clearAll) {
            sBaseMemoriesInfo.mMediaInfos.clear();
            sCurMemoriesInfo.mMediaInfos.clear();
            CURRENT_VIDEO_FILE_LIST.clear();
            clearThumbnailCache();
        }
        VideoStorageHelper.deleteOldVideoFile(mContext, VideoStorageHelper.VideoType.MEMORIES);
    }

    private void clearThumbnailCache() {
        for (Bitmap b : THUMBNAIL_CACHE.values()) {
            if ((b != null) && (!b.isRecycled())) {
                b.recycle();
                b = null;
            }
        }
        THUMBNAIL_CACHE.clear();
    }

    public void enqueueWorkTask(Runnable r) {
        if (mWorker != null) {
            mWorker.post(r);
        }
    }

    public void loadOptimalThumbnail() {
        if (mWorker == null) {
            GLog.w(TAG, "loadOptimalThumbnail sWorker = null");
            return;
        }
        mWorker.post(() -> {
            try {
                ArrayList<MediaInfo> mediaList = new ArrayList<>(sCurMemoriesInfo.mMediaInfos);
                Collections.sort(mediaList, MEDIA_TIME_COMPARATOR);
                // load other not in video media thumbnail
                for (MediaInfo info : mediaList) {
                    if (info.mOptimal) {
                        loadThumbnailTask(info, null);
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, "loadOptimalThumbnail error = ", e);
            }
        });
    }

    public ThemeItem getCurrentTheme() {
        List<ThemeItem> themeList = ThemeSourceManager.getInstance().queryEnableMemoriesTheme();
        if (themeList == null) {
            GLog.d(TAG, "getCurrentTheme theme list is null");
            return null;
        }

        GLog.d(TAG, "getCurrentTheme theme = " + sCurMemoriesInfo.mTheme + ", themeList = " + themeList);
        for (ThemeItem item : themeList) {
            if (TextUtils.equals(item.getSourcePath(), sCurMemoriesInfo.mTheme)) {
                return item;
            }
        }
        ThemeItem themeItem = null;
        if (themeList.size() > 0) {
            final int index = random(themeList.size() - 1);
            themeItem = themeList.get(index);
            sCurMemoriesInfo.mTheme = themeItem.getSourcePath();
            if (TextUtils.isEmpty(sCurMemoriesInfo.mTheme)) {
                themeItem = themeList.get(0);
                sCurMemoriesInfo.mTheme = themeItem.getSourcePath();
            }
            sBaseMemoriesInfo.mTheme = sCurMemoriesInfo.mTheme;
            if (mWorker != null) {
                mWorker.post(() -> MemoriesProviderHelper.setTheme(sBaseMemoriesInfo.mSetId, sBaseMemoriesInfo.mTheme));
            }
        }
        return themeItem;
    }

    private List<MediaInfo> findDifferentMedias() {
        ArrayList<MediaInfo> list = new ArrayList<>();
        for (MediaInfo baseInfo : sBaseMemoriesInfo.mMediaInfos) {
            MediaInfo curInfo = getMediaInfoFromAll(baseInfo.mPath);
            if ((curInfo != null) && !curInfo.equals(baseInfo)) {
                GLog.d(TAG, LogFlag.DL, () -> "findDifferentMedias baseInfo = " + baseInfo + ", curInfo = " + curInfo);
                list.add(curInfo);
            }
        }
        return list;
    }

    public void updateMemoriesData() {
        if (mWorker == null) {
            GLog.w(TAG, "updateMemoriesData sWorker = null");
            return;
        }
        mWorker.post(this::updateMemoriesDataInfo);
    }

    public void updateMemoriesData(UpdateMemoriesDataCallBack updateCallBack) {
        if (mWorker == null) {
            GLog.w(TAG, LogFlag.DL, "updateMemoriesData sWorker = null");
            return;
        }
        updateCallBack.updateStart();
        mWorker.post(() -> {
            updateMemoriesDataInfo();
            updateCallBack.updateSuccess();
        });
    }

    private void updateMemoriesDataInfo() {
        GLog.d(TAG, LogFlag.DL, () -> "updateMemoriesDataInfo sCurMemoriesInfo = " + sCurMemoriesInfo);
        sCurMemoriesInfo.mDuration = mOplusEngineManager.getTotalTime();
        MemoriesProviderHelper.updateMemoriesInfo(sCurMemoriesInfo, sBaseMemoriesInfo);
        final List<MediaInfo> list = findDifferentMedias();
        GLog.i(TAG, LogFlag.DL, () -> "updateMemoriesDataInfo list.size = " + list.size());
        if (!list.isEmpty()) {
            MemoriesProviderHelper.updateMediaList(list);
        }
        getCurrentVideoFileList();
        // copy current to base
        sBaseMemoriesInfo = sCurMemoriesInfo.obtain();
    }


    public void checkPhoto() {
        if (mWorker == null) {
            GLog.w(TAG, "checkPhoto sWorker = null");
            return;
        }
        mWorker.post(() -> {
            MemoriesInfo memoriesInfo = MemoriesProviderHelper.getMemoriesInfo(mContext, sSetId);
            memoriesInfo.mName = sBaseMemoriesInfo.mName;
            memoriesInfo.mSubName = sBaseMemoriesInfo.mSubName;
            if (sBaseMemoriesInfo.equals(memoriesInfo)) {
                GLog.d(TAG, "checkPhoto MemoriesInfo is same.");
                return;
            }
            sBaseMemoriesInfo = memoriesInfo.obtain();
            ArrayList<MediaInfo> removeList = new ArrayList<>();
            List<MediaInfo> allList = sCurMemoriesInfo.mMediaInfos;
            GLog.d(TAG, "checkPhoto allList.size = " + allList.size());
            if (!allList.isEmpty()) {
                boolean needNewCover = false;
                for (MediaInfo info : allList) {
                    if ((info != null) && (getMediaInfoFromAll(sBaseMemoriesInfo.mMediaInfos, info.mPath) == null)) {
                        removeList.add(info);
                        if (info.mIsCover) {
                            needNewCover = true;
                        }
                        GLog.d(TAG, "checkPhoto should remove info = " + info);
                    }
                }
                if (!removeList.isEmpty()) {
                    if (needNewCover) {
                        if (sBaseMemoriesInfo.mCover != null) {
                            MediaInfo cover = getMediaInfoFromAll(sBaseMemoriesInfo.mCover.mPath);
                            if ((cover != null) && cover.mInVideo) {
                                cover.mIsCover = true;
                                sCurMemoriesInfo.mCover = cover;
                            } else {
                                getNextCover();
                            }
                        } else {
                            getNextCover();
                        }
                    }

                    Message msg = mHandler.obtainMessage(MemoriesActivity.MSG_REFRESH_VIDEO);
                    msg.obj = removeList;
                    mHandler.sendMessage(msg);
                }
            }
            GLog.d(TAG, "checkPhoto end");
        });
    }

    // static fun
    public static boolean isSetIdValid() {
        return (sSetId != INVALID_MEMORIES_SET_ID);
    }

    public static void setSetId(int setId) {
        sSetId = setId;
    }

    public static int getSetId() {
        return sSetId;
    }

    public static int getMemoriesType() {
        return sCurMemoriesInfo.mType;
    }

    public static MemoriesInfo getBaseMemoriesInfo() {
        return sBaseMemoriesInfo;
    }

    public static MemoriesInfo getCurMemoriesInfo() {
        return sCurMemoriesInfo;
    }

    public static void setCurMemoriesInfo(MemoriesInfo info) {
        sCurMemoriesInfo = info.obtain();
    }

    public static void setThumbnailCache(String path, Bitmap bmp) {
        THUMBNAIL_CACHE.put(path, bmp);
    }

    public static Bitmap getThumbnailCache(String path) {
        return THUMBNAIL_CACHE.get(path);
    }

    public static boolean isMediaInSDCard() {
        String internalDir = OplusEnvironment.getInternalPath();
        for (MediaInfo info : sCurMemoriesInfo.mMediaInfos) {
            if (!TextUtils.isEmpty(info.mPath) && !info.mPath.startsWith(internalDir)) {
                GLog.d(TAG, "isMediaInSDCard, internalDir = " + internalDir + ", uri = " + info.mUri);
                return true;
            }
        }

        return false;
    }

    public static List<MediaInfo> getAllMediaInfoList() {
        return sCurMemoriesInfo.mMediaInfos;
    }

    public static List<MediaInfo> getCurrentVideoFileList() {
        CURRENT_VIDEO_FILE_LIST.clear();
        if (!sCurMemoriesInfo.mMediaInfos.isEmpty()) {
            for (MediaInfo info : sCurMemoriesInfo.mMediaInfos) {
                if ((info != null) && info.mInVideo) {
                    CURRENT_VIDEO_FILE_LIST.add(info);
                }
            }
        }
        GLog.d(TAG, LogFlag.DL, () -> "getCurrentVideoFileList size = " + CURRENT_VIDEO_FILE_LIST.size());
        return CURRENT_VIDEO_FILE_LIST;
    }

    private static ArrayList<MediaInfo> getCurrentSingleFaceMediaList() {
        ArrayList<MediaInfo> mediaList = new ArrayList<>();
        for (MediaInfo info : CURRENT_VIDEO_FILE_LIST) {
            if ((info != null) && info.mSingleFace) {
                mediaList.add(info);
            }
        }
        return mediaList;
    }

    public static MediaInfo getNextCover() {
        getCurrentVideoFileList();
        if (!CURRENT_VIDEO_FILE_LIST.isEmpty()) {
            ArrayList<MediaInfo> mediaList = new ArrayList<>();
            if (sCurMemoriesInfo.mType == MemoriesStore.FACE_SINGLE_PERSON_OPTIMAL) {
                mediaList = getCurrentSingleFaceMediaList();
            }
            if (mediaList.isEmpty()) {
                mediaList = CURRENT_VIDEO_FILE_LIST;
            }
            MediaInfo mediaInfo = mediaList.get(0);
            if (mediaInfo != null) {
                mediaInfo.mIsCover = true;
                GLog.d(TAG, "getNextCover oldCover = " + sCurMemoriesInfo.mCover + ", newCover = " + mediaInfo);
                sCurMemoriesInfo.mCover = mediaInfo;
            }
        }
        return sCurMemoriesInfo.mCover;
    }

    public static MediaInfo getMediaInfoFromAll(List<MediaInfo> mediaList, String path) {
        MediaInfo info = null;
        for (MediaInfo mediaInfo : mediaList) {
            if ((mediaInfo != null) && (TextUtils.equals(mediaInfo.mPath, path) || TextUtils.equals(mediaInfo.mUri, path))) {
                info = mediaInfo;
                break;
            }
        }
        return info;
    }

    public static MediaInfo getMediaInfoFromAll(String path) {
        MediaInfo info = null;
        for (MediaInfo mediaInfo : sCurMemoriesInfo.mMediaInfos) {
            if ((mediaInfo != null) && (TextUtils.equals(mediaInfo.mPath, path) || TextUtils.equals(mediaInfo.mUri, path))) {
                info = mediaInfo;
                break;
            }
        }
        return info;
    }

    public static MediaInfo getMediaInfoFromCurrent(String path) {
        getCurrentVideoFileList();
        MediaInfo info = null;
        if (!CURRENT_VIDEO_FILE_LIST.isEmpty()) {
            for (MediaInfo mediaInfo : CURRENT_VIDEO_FILE_LIST) {
                if ((mediaInfo != null) && (TextUtils.equals(mediaInfo.mPath, path) || TextUtils.equals(mediaInfo.mUri, path))) {
                    info = mediaInfo;
                    break;
                }
            }
        }
        GLog.d(TAG, LogFlag.DL, "getMediaInfoFromCurrent path = " + path + " info = " + info);
        return info;
    }

    public static MediaInfo getCurrentCoverInfo() {
        MediaInfo info = sCurMemoriesInfo.mCover;
        if (info != null) {
            for (MediaInfo mediaInfo : sCurMemoriesInfo.mMediaInfos) {
                if ((mediaInfo != null)
                    && (TextUtils.equals(mediaInfo.mPath, info.mPath)
                    || (TextUtils.equals(mediaInfo.mUri, info.mPath)))) {
                    info = mediaInfo;
                    break;
                }
            }
        }
        GLog.d(TAG, "getCurrentCoverInfo info = " + info);
        return info;
    }

    public static void resetCurrentMemoriesInfo() {
        sCurMemoriesInfo = sBaseMemoriesInfo.obtain();
    }

    public static boolean isMemoriesSame() {
        return sBaseMemoriesInfo.equals(sCurMemoriesInfo);
    }

    public static MediaInfo getCurrentCoverId() {
        return sCurMemoriesInfo.mCover;
    }

    public static String getCurrentThemeId() {
        return sCurMemoriesInfo.mTheme;
    }

    public static String getCurrentMusicId() {
        return sCurMemoriesInfo.mMusic;
    }

    public static void setTitleText(String name) {
        sCurMemoriesInfo.mName = name;
    }

    public static String getTitleText() {
        return sCurMemoriesInfo.mName;
    }

    public static String getTimeText() {
        return sCurMemoriesInfo.getTimeText();
    }

    public static void setDuration(long duration) {
        sBaseMemoriesInfo.mDuration = duration;
        sCurMemoriesInfo.mDuration = duration;
    }

    public boolean createVideoFileForSend() {
        if ((mProgressDialog != null) && mProgressDialog.isShowing()) {
            GLog.w(TAG, "createVideoFileForSend ProgressDialog.isShowing = true.");
            return false;
        }
        mHandler.post(() -> {
            String dateTime = TimeUtils.getFormatDateTime();
            if (TextUtils.isEmpty(dateTime)) {
                GLog.w(TAG, "createVideoFileForSend dateTime is null");
                return;
            }
            int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
            mCurSaveDir = VideoStorageHelper.checkStorageEnough(mContext, VideoStorageHelper.VideoType.MEMORIES, ENSURE_SPACE_MB,
                Dir.getCAMERA().getRelativePath(), tipRes);
            if (mCurSaveDir == null) {
                GLog.w(TAG, "createVideoFileForSend saveDir is null");
                return;
            }
            VideoStorageHelper.deleteOldVideoFile(mContext, VideoStorageHelper.VideoType.MEMORIES);
            mVideoFileName = MEMORIES_VIDEO_PREFIX + dateTime + MEMORIES_VIDEO_SUFFIX;
            VideoEditorHelper.setStringPref(mContext, VideoEditorHelper.KEY_MEMORIES_VIDEO_PREF, mVideoFileName);

            File file = new File(mCurSaveDir, mVideoFileName);
            GLog.d(TAG, "createVideoFileForSend, mVideoFileName = " + mVideoFileName + ", file = " + file);
            CpuFrequencyManager.setAction(SAVE_VIDEO_ACTION, SAVE_VIDEO_DURATION);
            mMediaUri = VideoEditorHelper.insertVideoSandbox(mContext.getApplicationContext(), file, mVideoFileName, System.currentTimeMillis());
            VideoEditorHelper.setStringPref(mContext, VideoEditorHelper.KEY_MEMORIES_VIDEO_URI_PREF, mMediaUri.toString());
            mOplusEngineManager.saveVideo(file.getAbsolutePath());
            showProgressDialog(R.string.videoeditor_memories_dialog_video_creating);
            //statistic export mp4 start
            mExportTime = System.currentTimeMillis();
        });
        return true;
    }

    public boolean isSavingInSDCard() {
        if ((mProgressDialog != null) && mProgressDialog.isShowing()) {
            File saveMemoriesDir = VideoStorageHelper.getSaveMemoriesDir();
            GLog.d(TAG, "isSavingInSDCard, mCurSaveDir = " + mCurSaveDir
                + ", sSaveMemoriesDir = " + saveMemoriesDir);
            return (mCurSaveDir != null) && !mCurSaveDir.equals(saveMemoriesDir);
        }
        return false;
    }

    public boolean isVideoSaving() {
        return (mProgressDialog != null) && mProgressDialog.isShowing();
    }

    /**
     * 取消导出视频或者导出失败会回调该函数，
     * 删除文件，toast提示失败，隐藏dialog，重置变量
     *
     * @param needToast 是否需要toast提示导出失败
     */
    public void onCanceExportVideo(boolean needToast) {
        if (mWorker == null) {
            GLog.w(TAG, "onCanceExportVideo sWorker = null");
            return;
        }
        mWorker.post(() -> {
            try {
                if (!TextUtils.isEmpty(mVideoFileName)) {
                    final File mediaFile = new File(mCurSaveDir, mVideoFileName);
                    GLog.d(TAG, "onCanceExportVideo, delete file.");
                    DeleteFileRequest deleteFileRequest = new DeleteFileRequest.Builder()
                        .setFile(mediaFile)
                        .setUri(mMediaUri)
                        .setImage(false)
                        .builder();
                    boolean result = FileAccessManager.getInstance().delete(mContext.getApplicationContext(), deleteFileRequest);
                    if (!result) {
                        GLog.w(TAG, "onCanceExportVideo, delete file failed. mediaFile = " + mediaFile);
                    }
                    if (needToast) {
                        mHandler.post(() -> {
                            ToastUtil.showShortToast(R.string.videoeditor_memories_toast_export_fail);
                        });
                    }
                }
                mHandler.post(() -> {
                    destroyDialog();
                });
                mCurSaveDir = null;
                mVideoFileName = null;
            } catch (Exception e) {
                GLog.w(TAG, "onCanceExportVideo, Exception:", e);
            }
        });
    }

    public boolean saveVideoFinish() {
        if (TextUtils.isEmpty(mVideoFileName)) {
            GLog.w(TAG, "saveVideoFinish mVideoFileName is null");
            return false;
        }
        File mediaFile = new File(mCurSaveDir, mVideoFileName);
        if (!mediaFile.exists()) {
            GLog.e(TAG, "saveVideoFinish failed. the file does not exists, mediaFile = " + mediaFile);
            return false;
        }
        String newName = mediaFile.getName();
        if (!TextUtils.isEmpty(newName) && newName.endsWith(MEMORIES_VIDEO_SUFFIX_TMP)) {
            newName = newName.replace(MEMORIES_VIDEO_SUFFIX_TMP, MEMORIES_VIDEO_SUFFIX);
        }

        final File newFileName = new File(mCurSaveDir, newName);
        boolean result = mediaFile.renameTo(newFileName);
        GLog.d(TAG, "saveVideoFinish mediaFile = " + mediaFile + ", newName = " + newName + ", result = " + result);
        if (!result) {
            GLog.w(TAG, "saveVideoFinish renameTo failed. mediaFile = " + mediaFile + ", newName = " + newName);
            return false;
        }
        mVideoFileName = newName;
        VideoEditorHelper.setStringPref(mContext, VideoEditorHelper.KEY_MEMORIES_VIDEO_PREF, null);
        VideoEditorHelper.setStringPref(mContext, VideoEditorHelper.KEY_MEMORIES_VIDEO_URI_PREF, null);

        MediaScannerConnection.scanFile(mContext, new String[]{newFileName.getAbsolutePath()}, null, null);

        //statistic save video file done
        mHandler.post(() -> {
            long duration = mOplusEngineManager.getTotalTime();
            float size = (float) VideoEditorHelper.getFileSize(newFileName) / VideoEditorHelper.SIZE_NUM;
            long exportTime = (System.currentTimeMillis() - mExportTime) / SECONDS_1000;
            Map<String, String> eventMap = new HashMap<>();
            eventMap.put(UserEventMap.KEY_VIDEO_EXPORT_TIME, exportTime + "S");
            eventMap.put(UserEventMap.KEY_VIDEO_SIZE, String.format("%.1fMB", size));
            eventMap.put(UserEventMap.KEY_MEMORIES_TYPE, String.valueOf(getMemoriesType()));
            eventMap.put(UserEventMap.KEY_PHOTO_COUNT, String.valueOf(CURRENT_VIDEO_FILE_LIST.size()));
            eventMap.put(UserEventMap.KEY_THEME_NAME, getCurrentThemeId());
            eventMap.put(UserEventMap.KEY_MUSIC_NAME, getCurrentMusicId());
            eventMap.put(UserEventMap.KEY_VIDEO_DURATION, duration / SECONDS_1000 + "S");
        });

        return true;
    }

    public boolean saveVideoFinishSandbox() {
        if (TextUtils.isEmpty(mVideoFileName)) {
            GLog.w(TAG, "saveVideoFinishSandbox mVideoFileName is null");
            return false;
        }

        File mediaFile = new File(mCurSaveDir, mVideoFileName);
        if (!mediaFile.exists()) {
            GLog.e(TAG, "saveVideoFinishSandbox failed. the file does not exists, mediaFile = " + mediaFile);
            return false;
        }

        // 美摄sdk写文件是通过fuse，先插入媒体库记录，再通过fuse写文件时会产生另外的新记录，之后如果再更新媒体库记录就会因为冲突把记录删除，需要媒体库扫描
        mMediaUri = MediaStoreScannerHelper.scanFileByMediaStoreSingle(
            mContext,
            mediaFile.getAbsolutePath(),
            SCAN_FILE_TIME_OUT);
        if (mMediaUri == null) {
            GLog.e(TAG, "saveVideoFinishSandbox mMediaUri is null. ");
            return false;
        }

        VideoEditorHelper.updateLocalMedia(mContext, mMediaUri, mediaFile);
        VideoEditorHelper.setStringPref(mContext, VideoEditorHelper.KEY_MEMORIES_VIDEO_PREF, null);
        VideoEditorHelper.setStringPref(mContext, VideoEditorHelper.KEY_MEMORIES_VIDEO_URI_PREF, null);
//        GLog.d(TAG, "saveVideoFinishSandbox newFileName = " + mediaFile.getAbsolutePath() + ", getName = " + mediaFile.getName());

        //statistic save video file done
        mHandler.post(() -> {
            long duration = mOplusEngineManager.getTotalTime();
            float size = (float) VideoEditorHelper.getFileSize(mediaFile) / VideoEditorHelper.SIZE_NUM;
            long exportTime = (System.currentTimeMillis() - mExportTime) / SECONDS_1000;
            Map<String, String> eventMap = new HashMap<>();
            eventMap.put(UserEventMap.KEY_VIDEO_EXPORT_TIME, exportTime + "S");
            eventMap.put(UserEventMap.KEY_VIDEO_SIZE, String.format("%.1fMB", size));
            eventMap.put(UserEventMap.KEY_MEMORIES_TYPE, String.valueOf(getMemoriesType()));
            eventMap.put(UserEventMap.KEY_PHOTO_COUNT, String.valueOf(CURRENT_VIDEO_FILE_LIST.size()));
            eventMap.put(UserEventMap.KEY_THEME_NAME, getCurrentThemeId());
            eventMap.put(UserEventMap.KEY_MUSIC_NAME, getCurrentMusicId());
            eventMap.put(UserEventMap.KEY_VIDEO_DURATION, duration / SECONDS_1000 + "S");
        });

        return true;
    }

    public void startShareActivity() {
        if (TextUtils.isEmpty(mVideoFileName)) {
            GLog.w(TAG, "startShareActivity mVideoFileName is null");
            return;
        }
        File mediaFile = new File(mCurSaveDir, mVideoFileName);
        if (!mediaFile.exists()) {
            GLog.e(TAG, "startShareActivity failed. the file does not exists, mediaFile = " + mediaFile);
            return;
        }

        Uri fileUri = MediaStoreUriHelper.getUriFromFile(mContext, mediaFile);
        if (fileUri == null) {
            fileUri = GalleryFileProvider.fromFile(mContext, mediaFile);
        }
        if (fileUri == null) {
            GLog.e(TAG, "startShareActivity failed. fileUri is null, mediaFile = " + mediaFile);
            return;
        }
        VideoEditorHelper.startShareVideoActivity(mContext, fileUri);
        mCurSaveDir = null;
        mVideoFileName = null;
    }

    private void showProgressDialog(int resId) {
        ProgressDialog.Builder builder = new ProgressDialog.Builder(mContext, true);
        COUIThemeOverlay.getInstance().applyThemeOverlays(builder.getContext());
        builder.setPositiveButton(
            mContext.getResources().getString(android.R.string.cancel),
            (dialog, whichButton) -> {
                if (DoubleClickUtils.isFastDoubleClick()) {
                    GLog.w(TAG, "createProgressDialog.onClick() isFastDoubleClick");
                    return;
                }
                if ((dialog instanceof Dialog) && (((Dialog) dialog).isShowing())) {
                    GLog.d(TAG, "createProgressDialog onClick cancel"
                        + ", mVideoFileName = " + mVideoFileName);
                    onCanceExportVideo(false);
                    if (mOplusEngineManager != null) {
                        mOplusEngineManager.stop(true);
                    }
                }
            });
        builder.setTitle(resId);
        builder.setCancelable(false);
        mProgressDialog = builder.build().show();
        //ignore home and menu key when saving memories video,bug id :1365975.
        ScreenUtils.ignoreHomeMenuKey(mProgressDialog.getWindow());
    }

    public void destroyDialog() {
        GLog.d(TAG, "destroyDialog mProgressDialog = " + mProgressDialog);
        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
            mProgressDialog = null;
        }
    }

    public void setDialogProgress(int progress) {
        if (null != mProgressDialog) {
            if (progress >= MAX_PROGRESS) {
                mProgressDialog.setProgress(MAX_PROGRESS);
            } else {
                if (mProgressDialog.getProgress() < progress) {
                    mProgressDialog.setProgress(progress);
                }
            }
        }
    }

    public static void takeCover(FragmentActivity activity, EditorPreviewState editorPreviewState) {
        sGOInTakeCover = true;
        //生成五宫格封面的图片列表
        List<MediaInfo> currentCovers = sCurMemoriesInfo.getCovers();
        if (currentCovers.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "takeCover: allCovers is empty.");
            return;
        }
        // 回忆编辑中选择回忆封面
        ArrayList<String> idList = getCoverIdList();
        if (idList.isEmpty()) {
            GLog.w(TAG, "startSelectionActivityByMemories, idList is empty");
            return;
        }

        SelectionData<Path> selectionData = PathSelectionManager.INSTANCE.createSelectionData();
        Set<Path> coverPaths = new HashSet<>();
        for (MediaInfo mediaInfo : currentCovers) {
            Path path = SourceConstants.Local.PATH_ITEM_IMAGE.getChild(mediaInfo.mGalleryId);
            if (mediaInfo.mType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                path = SourceConstants.Local.PATH_ITEM_VIDEO.getChild(mediaInfo.mGalleryId);
            }
            coverPaths.add(path);
        }
        selectionData.selectItems(coverPaths);
        Bundle selectionBundle = buildTakeCoverBundle(activity, idList, selectionData);

        LaunchExitPopupTrackHelper.trackSendEnterGalleryMemoryAlbum(new WeakReference<>(activity));
        PanelDialog panelDialog = new Starter.DialogFragmentStarter<PanelDialog>(
            activity.getSupportFragmentManager(),
            selectionBundle,
            new PostCard(SELECTION_PANEL_DIALOG),
            null
        ).start();
        if (panelDialog != null) {
            FragmentKt.setFragmentResultListener(panelDialog, KEY_MEMORIES_MANAGER_TO_COVER, (s, resultBundle) -> {
                dealTakeCoverResult(editorPreviewState, resultBundle);
                return Unit.INSTANCE;
            });
            panelDialog.setOnDismissListener(dialog -> {
                if (activity instanceof IStatusBarController) {
                    ((IStatusBarController) activity).hideStatusBar();
                }
            });
            panelDialog.setFinalNavColorAfterDismiss(PanelFragment.getPanelFinalNavColor(panelDialog.getActivity(), false));
        }
        GLog.d(TAG, "takeCover, currentCovers = " + currentCovers + ", sCurMemoriesInfo.mCover = " + sCurMemoriesInfo.mCover);
    }

    @NonNull
    private static Bundle buildTakeCoverBundle(FragmentActivity activity, ArrayList<String> idList, SelectionData<Path> selectionData) {
        Bundle bundle = new Bundle();
        bundle.putString(KEY_PATH_STR, PATH_ALBUM_ANY_ID_LIST.getChild(idList.toString().hashCode()).toString());
        bundle.putStringArrayList(KEY_ID_LIST, idList);
        bundle.putInt(KEY_MEDIA_TYPE_SUPPORT, MEDIA_TYPE_SUPPORT_ALL);
        bundle.putInt(KEY_ORDER_TYPE, ORDER_TYPE_DATE_TAKEN);
        bundle.putBoolean(KEY_IS_SUPPORT_PREVIEW_PHOTO, true);

        Bundle selectionBundle = new SelectInputData(
            SELECT_ALBUM_ONLY,
            false,
            true,
            SelectType.ALL,
            SelectInputDataKt.COUNT_AT_LEAST,
            SelectInputDataKt.COUNT_AT_MOST,
            Resources.ID_NULL,
            Resources.ID_NULL,
            Resources.ID_NULL,
            SelectInputDataKt.MAX_SIZE_LIMIT,
            SelectInputDataKt.IMAGE_MAX_SIZE_LIMIT,
            SelectInputDataKt.VIDEO_MAX_SIZE_LIMIT,
            activity.getResources().getString(com.oplus.gallery.basebiz.R.string.main_select_memories_cover),
            selectionData.getSelectionDataId(),
            null,
            TYPE_ALL_PICTURE,
            new Bundle(),
            TYPE_ID_LIST_ALBUM,
            bundle,
            SelectionFrom.GALLERY,
            null,
            false,
            0
        ).createBundle();
        selectionBundle.putString(KEY_REQUEST_KEY, KEY_MEMORIES_MANAGER_TO_COVER);
        return selectionBundle;
    }

    @NonNull
    private static ArrayList<String> getCoverIdList() {
        return (ArrayList<String>) sCurMemoriesInfo.mMediaInfos
            .stream()
            .filter(mediaInfo -> (mediaInfo != null) && mediaInfo.mInVideo)
            .map(mediaInfo -> String.valueOf(mediaInfo.mGalleryId))
            .collect(Collectors.toList());
    }

    /**
     * 获取封面信息，如果当前封面获取不到，会尝试去获取下一个封面
     *
     * @return
     */
    private static MediaInfo getCoverInfo() {
        MediaInfo cover = getCurrentCoverInfo();
        GLog.d(TAG, "getCoverInfo cover = " + cover + ", sCurMemoriesInfo.mCover = " + sCurMemoriesInfo.mCover);
        if (cover == null) {
            cover = getNextCover();
            GLog.d(TAG, "getCoverInfo getNextCover cover = " + cover + ", sCurMemoriesInfo.mCover = " + sCurMemoriesInfo.mCover);
        }
        return cover;
    }

    private static void dealTakeCoverResult(EditorPreviewState editorPreviewState, Bundle resultBundle) {
        if (resultBundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
            GLog.w(TAG, "onSelectionFinished: cancel select items.");
            return;
        }

        String pathStr = resultBundle.getString(KEY_RESULT_DATA);
        if (pathStr == null) {
            GLog.w(TAG, LogFlag.DL, "takeCover: return filePaths is null.");
            return;
        }
        Uri uri = DataManager.getContentUri(Path.fromString(pathStr));

        if (uri != null) {
            List<MediaInfo> curCovers = sCurMemoriesInfo.getCovers();
            MediaInfo curCover = curCovers.get(0);
            MediaInfo newCover = MemoriesManager.getMediaInfoFromCurrent(uri.toString());
            GLog.d(TAG, LogFlag.DL, () -> "takeCoverResult coverUri=" + uri
                + ", newCover=" + newCover + ", curCovers=" + curCovers);

            if (newCover != null) {
                if (curCover != null) {
                    MediaInfo oldCover = MemoriesManager.getMediaInfoFromCurrent(curCover.mPath);
                    if (newCover.equals(oldCover) || curCovers.contains(newCover)) {
                        GLog.d(TAG, LogFlag.DL, () -> "takeCoverResult cover not change. oldCover = " + oldCover);
                        return;
                    }
                    if (oldCover != null) {
                        GLog.d(TAG, LogFlag.DL, "takeCoverResult The new cover is not in the list of covers.");
                        oldCover.mIsCover = false;
                    }
                    GLog.d(TAG, LogFlag.DL, () -> "takeCoverResult oldCover = " + oldCover);
                }
                if (editorPreviewState.getEngineManager().addThemeVideoCover(newCover)) {
                    newCover.mInVideo = true;
                    newCover.mIsCover = true;
                    GLog.d(TAG, LogFlag.DL, () -> "takeCoverResult newCover = " + newCover);
                    MemoriesManager.getCurMemoriesInfo().mCover = newCover;
                    editorPreviewState.setActionDoneEnable(!MemoriesManager.isMemoriesSame());
                    return;
                }
            }
        }
        ToastUtil.showShortToast(R.string.videoeditor_memories_toast_change_cover_failed);
    }

    public static boolean hasEditCover() {
        return sGOInTakeCover;
    }

    public static void takePhoto(FragmentActivity activity, EditorPhotoState editorPhotoState, List<MediaInfo> mediaInfoList) {
        Bundle startBundle = buildTakePhotoBundle(mediaInfoList);
        PanelDialog panelDialog = new Starter.DialogFragmentStarter<PanelDialog>(
            activity.getSupportFragmentManager(),
            startBundle,
            new PostCard(SELECTION_PANEL_DIALOG),
            null
        ).start();
        if (panelDialog != null) {
            FragmentKt.setFragmentResultListener(panelDialog, KEY_MEMORIES_MANAGER_TO_CREATE, (s, resultBundle) -> {
                onSelectionFinished(activity, editorPhotoState, resultBundle);
                return Unit.INSTANCE;
            });
            panelDialog.setOnDismissListener(dialog -> {
                if (activity instanceof IStatusBarController) {
                    ((IStatusBarController) activity).hideStatusBar();
                }
            });
            panelDialog.setFinalNavColorAfterDismiss(PanelFragment.getPanelFinalNavColor(panelDialog.getActivity(), false));
        }
    }

    @NotNull
    private static Bundle buildTakePhotoBundle(List<MediaInfo> mediaInfoList) {
        Bundle modelBundle = new Bundle();
        modelBundle.putInt(KEY_MEMORIES_SET_ID, sSetId);
        modelBundle.putBoolean(KEY_MEMORIES_EDITOR, true);
        modelBundle.putString(KEY_PATH_STR, PATH_ALBUM_MEMORIES_ANY.getChild(sSetId).toString());

        SelectionData<Path> session = PathSelectionManager.INSTANCE.createSelectionData();
        List<MediaInfo> targetList = mediaInfoList;
        if ((targetList == null) || targetList.isEmpty()) {
            targetList = sCurMemoriesInfo.mMediaInfos;
        }

        session.selectItems(targetList.parallelStream()
            .map(mediaInfo -> {
                if ((mediaInfo != null) && mediaInfo.mInVideo) {
                    Path path = SourceConstants.Local.PATH_ITEM_IMAGE.getChild(mediaInfo.mGalleryId);
                    if (mediaInfo.mType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                        path = SourceConstants.Local.PATH_ITEM_VIDEO.getChild(mediaInfo.mGalleryId);
                    }
                    return path;
                }
                return null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toSet()));

        Bundle bundle = new SelectInputData(
            SELECT_ALBUM_ONLY,
            true,
            true,
            SelectType.ALL,
            MEMORIES_VIDEO_PHOTOS_MIN,
            MEMORIES_VIDEO_PHOTOS_MAX,
            com.oplus.gallery.basebiz.R.string.base_toast_memories_cannot_less_than_photo,
            com.oplus.gallery.basebiz.R.string.base_toast_memories_cannot_more_than_photo,
            Resources.ID_NULL,
            SelectInputDataKt.MAX_SIZE_LIMIT,
            SelectInputDataKt.IMAGE_MAX_SIZE_LIMIT,
            SelectInputDataKt.VIDEO_MAX_SIZE_LIMIT,
            null,
            session.getSelectionDataId(),
            null,
            TYPE_ALL_PICTURE,
            new Bundle(),
            TYPE_MEMORIES_ALBUM,
            modelBundle,
            SelectionFrom.GALLERY,
            null,
            false,
            0
        ).createBundle();
        bundle.putString(KEY_REQUEST_KEY, KEY_MEMORIES_MANAGER_TO_CREATE);
        return bundle;
    }

    private static void onSelectionFinished(Context context, EditorPhotoState editorPhotoState, Bundle resultBundle) {
        if (resultBundle == null) {
            GLog.w(TAG, "onSelectionFinished: resultBundle is null");
            return;
        }
        if (resultBundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
            GLog.w(TAG, "onSelectionFinished: cancel select items.");
            return;
        }

        String[] resultImageList = resultBundle.getStringArray(KEY_RESULT_DATA_LIST);
        if ((resultImageList == null) || (resultImageList.length <= 0)) {
            GLog.w(TAG, "takePhotoResult resultImageList is null or empty.");
            return;
        }
        List<String> list = Arrays.stream(resultImageList)
            .parallel()
            .map(pathStr -> {
                Uri uri = DataManager.getContentUri(Path.fromString(pathStr));
                if (uri != null) {
                    return uri.toString();
                } else {
                    return null;
                }
            })
            .filter(uriStr -> !TextUtils.isEmpty(uriStr))
            .collect(Collectors.toList());
        GLog.d(TAG, "takePhotoResult resultImageList.size = " + list.size());
        if (list.size() > MEMORIES_VIDEO_PHOTOS_MAX) {
            String toast = context.getResources().getString(R.string.videoeditor_memories_toast_cannot_more_than_photo, MEMORIES_VIDEO_PHOTOS_MAX);
            ToastUtil.showShortToast(toast);
            GLog.w(TAG, "takePhotoResult toast = " + toast);
            return;
        }
        editorPhotoState.updateTakePhoto(list);
    }

    public void initMemoriesSetData(Intent intent) {
        if ((mWorker == null) || (intent == null)) {
            GLog.w(TAG, "initMemoriesSetData sWorker = null MSG_EXIT");
            // error, finish activity
            mHandler.sendEmptyMessage(MemoriesActivity.MSG_EXIT);
            return;
        }
        setSetId(IntentUtils.getIntExtra(intent, KEY_MEMORIES_SET_ID, INVALID_MEMORIES_SET_ID));
        final String title = IntentUtils.getStringExtra(intent, KEY_MEMORIES_TITLE);
        final String subTitle = IntentUtils.getStringExtra(intent, KEY_MEMORIES_SUB_TITLE);
        sGOInTakeCover = false;
        GLog.d(TAG, "initMemoriesSetData sSetId = " + sSetId + ", subTitle = " + subTitle);
        mWorker.post(() -> {
            try {
                long time = System.currentTimeMillis();
                sBaseMemoriesInfo = MemoriesProviderHelper.getMemoriesInfo(mContext, sSetId);
                if (!TextUtils.isEmpty(title)) {
                    sBaseMemoriesInfo.mName = title;
                }
                sBaseMemoriesInfo.mSubName = subTitle;
                sCurMemoriesInfo = sBaseMemoriesInfo.obtain();
                getCurrentVideoFileList();
                // check and update video file
                if (!CURRENT_VIDEO_FILE_LIST.isEmpty()) {
                    mHandler.sendEmptyMessage(MemoriesActivity.MSG_DATA_READY);
                    GLog.d(TAG, "initMemoriesSetData getMemoriesInfo cost time = " + (System.currentTimeMillis() - time));
                    time = System.currentTimeMillis();
                    getCurrentVideoFileList();
                    ArrayList<MediaInfo> mediaList = new ArrayList<>(CURRENT_VIDEO_FILE_LIST);
                    Collections.sort(mediaList, MEDIA_TIME_COMPARATOR);
                    int count = 0;
                    // load in video media thumbnail
                    for (MediaInfo info : mediaList) {
                        if (count < MEMORIES_VIDEO_PHOTOS_MIN) {
                            loadThumbnail(info);
                            count++;
                        } else {
                            loadThumbnailTask(info, null);
                        }
                    }
                    GLog.d(TAG, "initMemoriesSetData.loadThumbnail cost time = " + (System.currentTimeMillis() - time));
                    return;
                }
            } catch (Exception e) {
                GLog.e(TAG, "initMemoriesSetData error = ", e);
            }
            // error, finish activity
            GLog.w(TAG, "initMemoriesSetData failed MSG_EXIT");
            mHandler.sendEmptyMessage(MemoriesActivity.MSG_EXIT);
        });
    }

    public static void dumpThemeAndMusicList(PrintWriter writer) {
        List<ThemeItem> themeList = ThemeSourceManager.getInstance().queryEnableMemoriesTheme();
        if (DUMP_DEBUG) {
            writer.println("----------------------- Dump dumpThemeAndMusicList start -----------------------");
            if ((themeList != null) && (themeList.size() > 0)) {
                writer.println("-------- dump sThemeList --------");
                for (int i = 0; i < themeList.size(); i++) {
                    writer.println("dump sThemeList i " + i + ", info = " + themeList.get(i));
                }
            }
            if ((themeList != null) && (themeList.size() > 0)) {
                writer.println("-------- dump sMusicList --------");
                for (int i = 0; i < themeList.size(); i++) {
                    writer.println("dump sMusicList i " + i + ", info = " + themeList.get(i));
                }
            }
            if ((CURRENT_VIDEO_FILE_LIST != null) && (CURRENT_VIDEO_FILE_LIST.size() > 0)) {
                writer.println("-------- dump sCurrentVideoFileList --------");
                for (int i = 0; i < CURRENT_VIDEO_FILE_LIST.size(); i++) {
                    writer.println("dump sCurrentVideoFileList i " + i + ", mediaInfo = " + CURRENT_VIDEO_FILE_LIST.get(i));
                }
            }
            writer.println("----------------------- Dump dumpThemeAndMusicList end -----------------------");
            writer.println("");
        }
    }

    private int random(int bound) {
        if (bound <= 0) {
            GLog.e(TAG, "random, bound is zero ");
            return 0;
        }
        return ThreadLocalRandom.current().nextInt(bound);
    }

    /**
     * load bmp
     *
     * @param info
     */
    private static void loadThumbnail(MediaInfo info) {
        if ((info != null) && (MemoriesManager.getThumbnailCache(info.mUri) == null)) {
            ParcelFileDescriptor parcelFileDescriptor = null;
            try {
                long time = System.currentTimeMillis();
                BitmapFactory.Options options = new BitmapFactory.Options();
                parcelFileDescriptor = FileAccessManager.getInstance()
                    .openFile(ContextGetter.context, Uri.parse(info.mUri));
                if (parcelFileDescriptor == null) {
                    GLog.w(TAG, "loadThumbnail failed to open file.");
                    return;
                }
                FileDescriptor fd = parcelFileDescriptor.getFileDescriptor();
                options.inJustDecodeBounds = true;
                BitmapFactory.decodeFileDescriptor(fd, null, options);
                if ((options.outWidth <= 0) || (options.outHeight <= 0)) {
                    return;
                }
                options.inSampleSize = VideoEditorHelper.computeSampleSizeSmaller(options.outWidth,
                    options.outHeight, EditorUIConfig.INSTANCE.getVideoEditorDefaultImageItemWidth());
                options.inJustDecodeBounds = false;

                Bitmap bmp = runNativeGuarding(OriginalRegionDecoder.TAG, generateKey(info.mPath, info.mFileTime), () ->
                    CodecHelper.decodeBitmapPro(fd, options))
                    .onNativeFailure(() -> {
                            GLog.w(TAG, "loadThumbnail: crash file = " + PathMask.INSTANCE.mask(info.mPath)
                                + " , options = " + BitmapFactoryKt.asString(options));
                        }
                    )
                    .getOrNull();
                try (IColorManagementAbility ability = ((GalleryApplication) ContextGetter.context).getAppAbility(IColorManagementAbility.class)) {
                    if (ability != null) {
                        ability.adjustBitmapColorSpace(bmp);
                    }
                }
                int degree = VideoEditorHelper.getImageDegree(info);
                bmp = VideoEditorHelper.getRotatedImage(bmp, degree);
                if (bmp != null) {
                    MemoriesManager.setThumbnailCache(info.mUri, bmp);
                    GLog.d(TAG, "doInBackground ok"
                        + " time = " + (System.currentTimeMillis() - time)
                        + " degree = " + degree
                        + " w = " + bmp.getWidth()
                        + " h = " + bmp.getHeight()
                        + " info = " + info);
                } else {
                    GLog.w(TAG, "doInBackground bmp is null, info = " + info);
                }
            } catch (Exception ex) {
                GLog.w(TAG, ex);
            } finally {
                IOUtils.closeQuietly(parcelFileDescriptor);
            }
        }
    }

    /**
     * load bmp background
     *
     * @param info
     * @param view
     */
    public static void loadThumbnailTask(MediaInfo info, ImageView view) {
        if ((info != null) && (MemoriesManager.getThumbnailCache(info.mUri) == null)) {
            new LoadImageTask(view).execute(new MediaInfo[]{info});
        }
    }

    private static class LoadImageTask extends AsyncTask<MediaInfo, Void, MediaInfo> {
        private final WeakReference<ImageView> mImageView;

        public LoadImageTask(ImageView view) {
            super();
            mImageView = (view != null) ? new WeakReference(view) : null;
        }

        @Override
        protected MediaInfo doInBackground(MediaInfo... params) {
            try {
                MediaInfo info = params[0];
                if (info != null) {
                    loadThumbnail(info);
                    return info;
                }
            } catch (Exception e) {
                GLog.e(TAG, "doInBackground Exception: " + e);
            }
            return null;
        }

        @Override
        protected void onPostExecute(MediaInfo info) {
            if ((mImageView != null) && (info != null)) {
                Bitmap bmp = MemoriesManager.getThumbnailCache(info.mUri);
                if (bmp != null) {
                    ImageView view = mImageView.get();
                    GLog.d(TAG, "onPostExecute------>success width = " + bmp.getWidth()
                        + ", height = " + bmp.getHeight() + ", view = " + view);
                    if (view != null) {
                        view.setImageBitmap(bmp);
                    }
                }
            }
        }
    }
}