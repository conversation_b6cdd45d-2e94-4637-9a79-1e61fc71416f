/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DefaultPageTransitionMaker.kt
 ** Description : 大图页的默认转场动画
 ** Version     : 1.0
 ** Date        : 2023/05/31
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2023/05/31  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.inputarguments.transition

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.annotation.ColorInt
import androidx.core.graphics.drawable.toDrawable
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FORCE_FINISH_ACTIVITY
import com.oplus.gallery.foundation.ui.animation.AnimationKeyParams
import com.oplus.gallery.basebiz.transition.InvokeFrom
import com.oplus.gallery.basebiz.transition.PageTransitionMaker
import com.oplus.gallery.basebiz.transition.PhotoTransitionMaker
import com.oplus.gallery.basebiz.transition.Transition
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.multiprocess.ITransBitmap

/**
 * 大图页的默认转场动画，当其他 [PhotoTransitionMaker] 不处理时，由本类进行兜底处理
 *
 * @property context
 * @property startColor 动画开始颜色
 * @property endColor 动画结束颜色
 * @constructor Create empty Default page transition maker
 */
internal class DefaultPageTransitionMaker(
    private val context: Context,
    @ColorInt private val startColor: Int,
    @ColorInt private val endColor: Int
) : PhotoTransitionMaker {

    override fun chooseTransitionMaker(intent: Intent, bundle: Bundle, invokeFrom: InvokeFrom): Boolean = true

    override fun make(intent: Intent, bundle: Bundle): Transition {
        val enterThumbnail = runCatching {
            bundle.getBinder(PicturePageConstant.KEY_TRANSITION_THUMBNAIL)
                ?.let(ITransBitmap.Stub::asInterface)
                ?.bitmap
                ?.toDrawable(context.resources)
        }.onFailure {
            GLog.w(TAG) { "[make] get enterThumbnail onFailure=$it" }
        }.getOrDefault(null)

        val isForceFinishActivity = bundle.getBoolean(KEY_FORCE_FINISH_ACTIVITY, false)
        GLog.d(TAG) { "[make] enterThumbnail=$enterThumbnail" }
        return Transition(
            remoteSourceBounds = RectF(),
            isEnterTransitionAnimationEnabled = false,
            isExitTransitionAnimationEnabled = false,
            positionControllerKey = null,
            isForceFinishActivity = isForceFinishActivity,
            enterTransition = DefaultEnterPageTransitionMaker(enterThumbnail, startColor, endColor),
            exitTransition = DefaultExitPageTransitionMaker()
        )
    }

    /**
     * 默认页面入场动画参数（无动画）
     */
    private class DefaultEnterPageTransitionMaker(
        thumbnail: Drawable?,
        @ColorInt startColor: Int,
        @ColorInt endColor: Int
    ) : PageTransitionMaker() {
        override val backgroundColors: AnimationKeyParams<Color> = AnimationKeyParams(
            duration = PHOTO_PAGE_ENTER_DURATION,
            animations = arrayOf(Color.valueOf(startColor), Color.valueOf(endColor))
        )
        override val thumbnailPositions: AnimationKeyParams<Rect> = AnimationKeyParams(
            duration = PHOTO_PAGE_ENTER_DURATION,
            animations = arrayOf(Rect(), Rect()),
            thumbnail = thumbnail
        )
    }

    /**
     * 默认页面出场动画参数（无动画）
     */
    private class DefaultExitPageTransitionMaker : PageTransitionMaker() {
        override val backgroundColors: AnimationKeyParams<Color> = AnimationKeyParams(
            animations = arrayOf(Color.valueOf(Color.TRANSPARENT), Color.valueOf(Color.TRANSPARENT))
        )
    }

    private companion object {
        private const val TAG = "DefaultPageTransitionMaker"
        private const val PHOTO_PAGE_ENTER_DURATION = 300L
    }
}