/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * File: ColorPickerView.kt
 * Description: tangzhibin created
 * Version: 1.0
 * Date: 2025/4/30
 * Author: tangzhibin
 *
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * tangzhibin      2025/4/30        1.0         NEW
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.colorpicker

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.widget.SpacingItemDecoration

/**
 * 颜色选择器视图
 */
class ColorPickerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 颜色选择器适配器
    private val adapter: ColorPickerAdapter

    // 颜色选择器RecyclerView
    private val recyclerView: COUIRecyclerView

    // 颜色选择监听器
    var onColorSelectedListener: ((color: Int, tag: Any?) -> Unit)? = null

    // 项目间距
    private var itemSpacing: Int = 0

    // 内容水平内边距
    private var contentPaddingHorizontal: Int = 0

    // 内容垂直内边距
    private var contentPaddingVertical: Int = 0

    init {
        // 加载布局
        LayoutInflater.from(context).inflate(R.layout.videoeditor_color_picker_view, this, true)

        // 获取自定义属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ColorPickerView)
        initAttributes(typedArray)
        typedArray.recycle()

        // 初始化RecyclerView
        recyclerView = findViewById(R.id.color_picker_recycler_view)
        recyclerView.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        recyclerView.addItemDecoration(
            SpacingItemDecoration(
                itemSpacing = itemSpacing,
                isHorizontal = true,
                paddingStart = contentPaddingHorizontal,
                paddingEnd = contentPaddingHorizontal,
                paddingTop = contentPaddingVertical,
                paddingBottom = contentPaddingVertical
            )
        )

        // 初始化适配器
        adapter = ColorPickerAdapter(context)
        adapter.onItemClickListener = { position, item ->
            onColorSelectedListener?.invoke(item.color, item.tag)
        }
        recyclerView.adapter = adapter
    }

    /**
     * 初始化自定义属性
     */
    private fun initAttributes(typedArray: TypedArray) {
        // 项目间距
        itemSpacing = typedArray.getDimensionPixelSize(
            R.styleable.ColorPickerView_itemSpacing,
            resources.getDimensionPixelSize(R.dimen.color_picker_item_spacing)
        )

        // 内容水平内边距
        contentPaddingHorizontal =
            typedArray.getDimensionPixelSize(R.styleable.ColorPickerView_contentPaddingHorizontal, 0)

        // 内容垂直内边距
        contentPaddingVertical = typedArray.getDimensionPixelSize(R.styleable.ColorPickerView_contentPaddingVertical, 0)

        // 边框颜色
        val borderColor = typedArray.getColor(
            R.styleable.ColorPickerView_borderColor,
            ContextCompat.getColor(context, R.color.color_picker_border)
        )

        // 选中边框颜色
        val selectedBorderColor = typedArray.getColor(
            R.styleable.ColorPickerView_selectedBorderColor,
            ContextCompat.getColor(context, R.color.color_picker_selected_border)
        )

        // 边框宽度
        val borderWidth = typedArray.getDimensionPixelSize(
            R.styleable.ColorPickerView_borderWidth,
            resources.getDimensionPixelSize(R.dimen.color_picker_border_width)
        )

        // 选中边框宽度
        val selectedBorderWidth = typedArray.getDimensionPixelSize(
            R.styleable.ColorPickerView_selectedBorderWidth,
            resources.getDimensionPixelSize(R.dimen.color_picker_selected_border_width)
        )

        // 边框外圆半径
        val selectedBorderRadius = typedArray.getDimensionPixelSize(
            R.styleable.ColorPickerView_borderRadius,
            resources.getDimensionPixelSize(R.dimen.color_picker_border_radius)
        )

        // 初始选中位置
        val initialSelection = typedArray.getInt(
            R.styleable.ColorPickerView_initialSelection,
            0
        )

        // 将属性传递给ColorPickerItemView
        ColorPickerItemView.setBorderAttributes(borderColor, selectedBorderColor, borderWidth, selectedBorderWidth, selectedBorderRadius)

        // 保存初始选中位置，用于后续设置默认颜色
        this.initialSelection = initialSelection
    }

    // 初始选中位置
    private var initialSelection: Int = 0

    /**
     * 设置颜色选项
     */
    fun setColors(colors: List<Int>, initialSelection: Int = 0) {
        val items = colors.mapIndexed { index, color ->
            ColorPickerItemData(color = color)
        }.toMutableList()
        adapter.setData(items, initialSelection)
    }

    /**
     * 设置颜色选项（带标签）
     */
    fun setColorsWithTags(colorTags: List<Pair<Int, Any?>>, initialSelection: Int = 0) {
        val items = colorTags.mapIndexed { index, (color, tag) ->
            ColorPickerItemData(color = color, tag = tag)
        }.toMutableList()

        adapter.setData(items, initialSelection)
    }

    /**
     * 设置自定义项
     * @param items 自定义项
     */
    fun setCustomItems(items: List<ColorPickerItemData>, initialSelection: Int = 0) =
        adapter.setData(items, initialSelection)

    /**
     * 获取当前选中的颜色
     */
    fun getSelectedColor(): Int? = adapter.getSelectedItem()?.color

    /**
     * 获取当前选中的标签
     */
    fun getSelectedTag(): Any? = adapter.getSelectedItem()?.tag
}
