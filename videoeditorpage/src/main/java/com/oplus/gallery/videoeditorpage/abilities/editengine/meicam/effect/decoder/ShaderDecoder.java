/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ShaderDecoder.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.decoder;

import android.content.Context;

import com.oplus.decoder.CommonCodec;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.utlis.Utils;
import com.oplus.gallery.videoeditorpage.utlis.FileUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class ShaderDecoder {

    private static final String TAG = "ShaderDecoder";

    public static String decode(String filePath) {
        FileInputStream fileInputStream = null;
        File file = new File(filePath);
        try {
            fileInputStream = new FileInputStream(file);
            byte[] bytes = readBytes(fileInputStream);
            bytes = CommonCodec.decodeByteArray(bytes);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (FileNotFoundException e) {
            GLog.e(TAG, LogFlag.DL, "decode, fail:" + e.getMessage());
        }  finally {
            Utils.closeSilently(fileInputStream);
        }
        GLog.e(TAG, "decode,failed");
        return null;
    }

    private static byte[] readBytes(InputStream fis) {
        ByteArrayOutputStream baos = null;
        try {
            baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int readSize = 0;
            while ((readSize = fis.read(buffer)) > 0) {
                baos.write(buffer, 0, readSize);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            GLog.e(TAG, LogFlag.DL, "readBytes, fail:" + e.getMessage());
        } finally {
            Utils.closeSilently(fis);
            Utils.closeSilently(baos);
        }
        return new byte[0];
    }

    public static String decode(Context context, String assetsPath) {
        InputStream inputStream = null;
        try {
            inputStream = context.getAssets().open(assetsPath.substring(FileUtils.ASSETS_HEAD_LENGTH));
            byte[] bytes = readBytes(inputStream);
            bytes = CommonCodec.decodeByteArray(bytes);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            GLog.e(TAG, LogFlag.DL, "decode, fail:" + e.getMessage());
        } finally {
            Utils.closeSilently(inputStream);
        }
        return null;
    }

    /**
     * @param source,  encode string
     * @param filePath file's absolute path ,include file name, such as : filePath = FileUtils.DCIM_DIR + "mergeFilter.fs";
     * @return
     */
    public static boolean encode(String source, String filePath) {
        byte[] bytes = source.getBytes(StandardCharsets.UTF_8);
        byte[] bytesEncode = CommonCodec.encodeByteArray(bytes);
        if (FileUtils.saveByteFile(filePath, bytesEncode)) {
            GLog.d(TAG, " encode success");
            return true;
        }
        GLog.e(TAG, "encode, failed");
        return false;
    }

}
