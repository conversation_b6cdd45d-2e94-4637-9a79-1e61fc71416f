/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ICache.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/7/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/7/5        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.foundation.cache

/**
 * 缓存接口，可实现内存缓存和磁盘缓存等
 */
interface ICache<T> {
    /**
     * 存入缓存
     */
    fun put(key: String, value: T)

    /**
     * 获取指定key的缓存
     */
    fun get(key: String): T?

    /**
     * 移除指定key的缓存
     */
    fun remove(key: String)

    /**
     * 清空缓存
     */
    fun clear()

    /**
     * 缓存大小
     */
    fun size(): Long
}