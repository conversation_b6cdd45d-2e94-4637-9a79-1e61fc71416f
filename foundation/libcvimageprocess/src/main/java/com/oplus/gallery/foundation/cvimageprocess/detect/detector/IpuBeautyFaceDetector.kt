/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IpuBeautyFaceDetector
 ** Description: ipu美颜人脸检测器
 **
 ** Version: 1.0
 ** Date: 2024/10/22
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2024/10/22  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cvimageprocess.detect.detector

import android.content.Context
import android.graphics.Bitmap
import com.oplus.gallery.foundation.cvimageprocess.detect.data.FaceDetectInputData
import com.oplus.gallery.foundation.cvimageprocess.detect.data.FaceDetectResult
import com.oplus.gallery.foundation.cvimageprocess.detect.data.IpuBeautyExtendData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_128
import com.oplus.ocs.camera.ipuapi.IPUClient
import com.oplus.ocs.camera.ipuapi.process.BaseProcessResult.RESULT_SUCCESSFUL
import com.oplus.ocs.camera.ipuapi.process.ProcessConfig
import com.oplus.ocs.camera.ipuapi.process.face.FaceConstant
import com.oplus.ocs.camera.ipuapi.process.face.FaceProcessUnit
import com.oplus.ocs.camera.ipuapi.process.face.entity.FaceInitParameter
import com.oplus.ocs.camera.ipuapi.process.face.entity.FaceProcessParameter
import com.oplus.ocs.camera.ipuapi.process.face.result.FaceProcessResult

/**
 * ipu美颜人脸检测器
 */
internal class IpuBeautyFaceDetector(private val context: Context) : IFaceDetector {

    /**
     * 美颜算法引擎
     */
    private val faceProcessUnit: FaceProcessUnit by lazy {
        IPUClient.createFeature(
            context,
            FaceProcessUnit::class.java
        )
    }

    override fun initialize() = Unit

    private fun initFaceProcess(bitmap: Bitmap, data: IpuBeautyExtendData) {
        val initBuilder = FaceInitParameter.ParameterBuilder()
        val initParam = initBuilder.apply {
            setAlgoType(FaceConstant.INIT_FACE_DETECT)

            setFaceBeautyVersion(data.fbLibVersion)
            setFaceInfoVersion(data.fiLibVersion)
            setCaptureMode(data.capMode)
            setCameraId(data.cameraId)
            setLogicCameraId(data.logicCameraId)
            setIso(data.iso)
            setColorTemperature(data.colorTemperature)
            setFaceLuma(data.faceLuma)
            setZoomRatio(data.zoomRatio)
            setFaceLumaRatio(data.faceLumaRatio)
            setOrientation(data.orientation)
            setFaceBeautyStrategy(data.faceBeautyStrategy)
            setSensorName(data.sensorName)
        }.build()
        GLog.d(TAG, "initFaceProcess. call init.  bitmap: ${bitmap.width}-${bitmap.height},")
        faceProcessUnit.init(initParam, bitmap)
    }

    override fun detectFace(bitmap: Bitmap, inputData: FaceDetectInputData): FaceDetectResult {
        val ipuBeautyExtendData = inputData as? IpuBeautyExtendData ?: IpuBeautyExtendData()
        val time = System.currentTimeMillis()
        val passedBitmap = BitmapUtils.addTransparentPadding(bitmap, NUMBER_128)
        runCatching {
            initFaceProcess(passedBitmap, ipuBeautyExtendData)
        }.onFailure {
            GLog.e(TAG, "detectFace. call initFaceProcess.  throwable : $it ")
        }
        val faceDetectBuilder = FaceProcessParameter.ParameterBuilder()
        val faceDetectParam =
            faceDetectBuilder.setAlgoType(FaceConstant.PROCESS_FACE_DETECT).build()
        val config = ProcessConfig().also {
            it.format = ProcessConfig.Format.BITMAP
            it.renderMode = ProcessConfig.RenderMode.PREVIEW
            it.scaleSize = DEFAULT_SCALE
        }

        return runCatching {
            faceProcessUnit.processImage(faceDetectParam, config)
        }.onFailure {
            GLog.e(TAG, "detectFace. call processImage.  throwable : $it ")
        }.getOrNull().toFaceDetectResult().also {
            GLog.d(
                TAG,
                "detectFace. bitmap size: ${passedBitmap.width}-${passedBitmap.height}," +
                    " result:$it, cost time = ${System.currentTimeMillis() - time}"
            )
        }
    }

    override fun release() {
        GLog.d(TAG, "release. call unInit. ")
        faceProcessUnit.uninit()
    }

    companion object {
        private const val TAG = "IpuBeautyFaceDetector"
        private const val DEFAULT_SCALE = 1.0f
    }
}

private fun FaceProcessResult?.toFaceDetectResult(): FaceDetectResult {
    return FaceDetectResult(hasFace = (this?.resultCode == RESULT_SUCCESSFUL) && (hasFace() > 0))
}