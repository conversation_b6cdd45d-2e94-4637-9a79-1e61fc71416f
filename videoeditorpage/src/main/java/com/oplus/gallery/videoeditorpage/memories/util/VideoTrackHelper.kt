/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoTrackHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/01/07
 ** Author: luy<PERSON>.<EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** luy<PERSON>.<PERSON>@Apps.Gallery		2021/01/13		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.memories.util

import android.content.Context
import android.media.MediaFormat
import android.media.MediaFormat.MIMETYPE_VIDEO_DOLBY_VISION
import android.media.MediaMetadataRetriever
import android.text.TextUtils
import androidx.annotation.WorkerThread
import com.meicam.sdk.NvsVideoClip
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_HORIZONTAL_LINE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_STAR
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_VERTICAL_LINE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.EventId
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.standard_lib.codec.retriever.MediaMetadataRetrieverUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.EXPORT_OLIVE_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.GET_FRAME_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.MEICAM_FORCE_STOP_SAVE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_NO_SPACE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_SUCCESS
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.TASK_STOP_FORCE_STOP_SAVE
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveType

object VideoTrackHelper {

    var videoTitle: String? = null
    var videoMediaId: String? = null
    var videoDuration: String? = null
    var videoDataTaken: String? = null
    var videoPath: String? = null
    var videoCodecType: String? = null
    var videoSize: String? = null
    var videoMimeType: String? = null
    var videoResolution: String? = null
    var videoMark: String? = null
    var fps: String? = null

    var speed = Value.SPEED_ORIGIN
    var isTrim = Value.RESULT_CLOSE
    var rotation = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_0
    var dimenFix = Value.VIDEO_CLIP_ORIGIN
    var isCarryTemplateResource = false
    var isCarryFilterResource = false
    var isCarryMusicResource = false
    var isCarryFxResource = false
    var subText: String? = null

    /**
     * 视频资源路径
     */
    var videoUri: String? = null
    /**
     * 原视频的MediaItem
     */
    var mVideoMediaItem: MediaItem? = null

    private val OPERATION_MAP: HashMap<String, String> by lazy {
        HashMap()
    }

    @JvmStatic
    fun saveThemeOperation(templateId: String?) {
        templateId?.let {
            OPERATION_MAP.put(Key.EDIT_THEME, it)
        }
        isCarryTemplateResource = !Value.NO_CHOOSE_ACTION.equals(templateId, ignoreCase = true)
    }

    @JvmStatic
    fun saveFilterOperation(filterItemId: String?) {
        filterItemId?.let {
            OPERATION_MAP.put(Key.EDIT_FILTER, it)
        }
        isCarryFilterResource = !Value.NO_CHOOSE_ACTION.equals(filterItemId, ignoreCase = true)
    }

    @JvmStatic
    fun saveTrimOperation(isTrim: String?, totalTime: String?) {
        isTrim?.let {
            OPERATION_MAP[Key.EDIT_IS_TRIM] = it
        }
    }

    @JvmStatic
    fun saveSpeederOperation(speed: String?) {
        speed?.let {
            OPERATION_MAP[Key.EDIT_SPEED] = it
        }
    }

    @JvmStatic
    fun saveBgmOperation(itemId: String?, isMute: String?) {
        StringBuilder().apply {
            append(itemId)
            isMute?.let {
                append(SYMBOL_HORIZONTAL_LINE)
                append(it)
            }
        }.let {
            OPERATION_MAP.put(Key.EDIT_BGM, it.toString())
        }
        isCarryMusicResource = !Value.NO_CHOOSE_ACTION.equals(itemId, ignoreCase = true)
    }

    @JvmStatic
    fun saveFxOperation(fxId: String?, fxTime: String?) {
        StringBuilder().apply {
            append(fxId)
            fxTime?.let {
                append(SYMBOL_HORIZONTAL_LINE)
                append(it)
            }
        }.let {
            OPERATION_MAP.put(Key.EDIT_FX, it.toString())
        }
        isCarryFxResource = !Value.NO_CHOOSE_ACTION.equals(fxId, ignoreCase = true)
    }

    @JvmStatic
    fun saveRotateClipOperation(rotation: Int, clipSize: String?) {
        StringBuilder().apply {
            append(rotation.toString())
            clipSize?.let {
                append(SYMBOL_HORIZONTAL_LINE)
                append(it)
            }
        }.let {
            OPERATION_MAP.put(Key.EDIT_ROTATION_CLIP, it.toString())
        }
        VideoTrackHelper.rotation = rotation
    }

    @JvmStatic
    fun saveTextOperaton(addSize: Int, deleteSize: Int, subText: String?) {
        StringBuilder().apply {
            append(addSize.toString())
            append(SYMBOL_HORIZONTAL_LINE)
            append(deleteSize)
        }.let {
            OPERATION_MAP.put(Key.EDIT_TEXT, it.toString())
        }
        VideoTrackHelper.subText = subText
    }

    @JvmStatic
    fun saveFinishOperation(
        isMemory: Boolean,
        newFilePath: String?,
        isSuccess: Boolean = true,
        costTime: Long = -1
    ) {
        val result = if (isSuccess) Value.SAVE_RESULT_SUCCESS else Value.SAVE_RESULT_FAILURE
        OPERATION_MAP.put(Key.SAVE_RESULT, result)
        videoMark?.let {
            OPERATION_MAP.put(Key.EDIT_WATER_MARK, Value.WATER_MARK_ADDED)
        }
        if (isMemory) {
            // 目前规划2d未规划回忆相关埋点
        } else {
            trackVideoEditMenuActionCommon(OPERATION_MAP)
        }
    }

    @JvmStatic
    fun cleanOperation() {
        OPERATION_MAP.clear()
    }

    @JvmStatic
    fun setVideoInfo(
        from: String?,
        currentTitle: String?,
        mediaId: String?,
        path: String?,
        codecType: String?,
        duration: Long,
        dataTaken: Long,
        size: Long,
        mimeType: String?,
        width: Int,
        height: Int,
        fps: Float,
        uri: String?,
        mediaItem: MediaItem?
    ) {
        videoTitle = currentTitle
        videoDuration = duration.toString()
        videoMediaId = mediaId
        videoDataTaken = dataTaken.toString()
        videoPath = path
        videoCodecType = codecType
        videoSize = size.toString()
        videoMimeType = mimeType
        videoResolution = "$width$SYMBOL_STAR$height"
        videoUri = uri
        mVideoMediaItem = mediaItem
        videoMark = null
        VideoTrackHelper.fps = fps.toString()
        cleanOperation()
        clearCarryResourceInfo()
    }

    private fun getVideoBasicInfo(map: HashMap<String, String>?): HashMap<String, String> = (map ?: HashMap()).apply {
        videoDataTaken?.let {
            put(Key.VIDEO_DATA_TAKEN, it)
        }
        videoPath?.let {
            put(Key.VIDEO_PATH, it)
        }
        videoResolution?.let {
            put(Key.VIDEO_RESOLUTION, it)
        }
        videoDuration?.let {
            put(Key.VIDEO_DURATION, it)
        }
    }

    private fun getVideoInfo(map: HashMap<String, String>?): Map<String, String> = getVideoBasicInfo(map).apply {
        videoSize?.let {
            put(Key.VIDEO_SIZE, it)
        }
        videoMimeType?.let {
            put(Key.VIDEO_MIMETYPE, it)
        }
        put(Key.VIDEO_HDR_TYPE, getHdrType())
        remove(Key.VIDEO_DATA_TAKEN)
    }

    private fun getHdrType(): String {
        val videoUri = MediaStoreUriHelper.getMediaUri(ContextGetter.context, videoPath, videoMediaId, false)
        val retrieveResult = MediaMetadataRetrieverUtils.extractMetadata(
            ContextGetter.context,
            videoUri,
            MediaMetadataRetriever.METADATA_KEY_COLOR_TRANSFER
        )
        if (TextUtils.equals(MIMETYPE_VIDEO_DOLBY_VISION, videoCodecType)) {
            return Value.VIDEO_DOLBY
        }
        return when (retrieveResult?.toInt()) {
            MediaFormat.COLOR_TRANSFER_LINEAR -> Value.VIDEO_NO_HDR
            MediaFormat.COLOR_TRANSFER_SDR_VIDEO -> Value.VIDEO_STATIC_HDR
            MediaFormat.COLOR_TRANSFER_ST2084 -> Value.VIDEO_DYNAMIC_HDR
            MediaFormat.COLOR_TRANSFER_HLG -> Value.VIDEO_HLG
            else -> ""
        }
    }

    private fun clearCarryResourceInfo() {
        speed = Value.SPEED_ORIGIN
        isTrim = Value.RESULT_CLOSE
        rotation = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_0
        dimenFix = Value.VIDEO_CLIP_ORIGIN
        isCarryTemplateResource = false
        isCarryFilterResource = false
        isCarryMusicResource = false
        isCarryFxResource = false
        subText = null
    }

    /**
     * 2006011001 视频编辑功能一级界面点击事件
     * @param itemId 点击过的一级菜单id（拼接的形式 如：1,2,2,3）
     * @param ifSaveVideo 是否保存视频
     * @param editFrom 视频编辑入口
     */
    @JvmStatic
    fun trackVideoEditMenuClickUserAction(
        itemId: String,
        ifSaveVideo: Int,
        editFrom: String
    ) {
        track(EventId.VIDEO_EDIT_FIRST_PAGE_CLICK) {
            it.putProperty(Key.ITEM_ID, itemId)
            it.putProperties(getVideoInfo(HashMap()))
            it.putProperty(Key.SAVE_VIDEO, ifSaveVideo)
            it.putProperty(Key.EDIT_FROM, editFrom)
            it.save()
        }
    }

    /**
     * 2006011004
     */
    @JvmStatic
    fun trackClickVideoCancelMenuUserAction(menuType: String?, OPResult: String?) {
        trackVideoEditMenuActionCommon(HashMap<String, String>().apply {
            OPResult?.let {
                put(Key.OPER_RESULT, it)
            }
        })
    }

    /**
     * 2006011004 视频编辑底部菜单操作
     */
    private fun trackVideoEditMenuActionCommon(map: HashMap<String, String>?) {
        track(EventId.VIDEO_EDIT_MENU) {
            it.putProperties(getVideoBasicInfo(map))
            it.save()
        }
    }

    /**
     * 2006011006
     */
    private fun getSeniorEditorCarryResource() = StringBuilder().apply {
        if (speed.compareTo(Value.SPEED_ORIGIN) != 0) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_SPEED)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (Value.RESULT_OPEN.equals(isTrim, ignoreCase = true)) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_DURATION_TRIM)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (rotation != NvsVideoClip.ClIP_EXTRAVIDEOROTATION_0) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_ROTATION)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (!Value.VIDEO_CLIP_ORIGIN.equals(dimenFix, ignoreCase = true)) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_FIX_EDIT)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (isCarryTemplateResource) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_THEME)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (isCarryFilterResource) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_FILTER)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (isCarryMusicResource) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_MUSIC)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (isCarryFxResource) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_FX)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (!TextUtils.isEmpty(subText)) {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_TEXT)
            append(SYMBOL_VERTICAL_LINE)
        }
        videoMark?.let {
            append(Value.ENTER_SENIOR_EDITOR_CARRY_RESOURCE_WATER_MARK)
            append(SYMBOL_VERTICAL_LINE)
        }
        if (length > 1) {
            deleteCharAt(length - 1)
        }
    }.toString()

    /**
     * 2006011014 主题点击
     */
    @JvmStatic
    fun trackVideoEditorClickTemplateItemCommon(templateItemId: String?, downloadState: Boolean) {
        track(EventId.VIDEO_EDIT_TEMPLATE_ITEM_CLICK) {
            it.putProperties(HashMap<String, String>().apply {
                templateItemId?.let {
                    put(Key.CLICK_TEMPLATE_ITEM_ID_KEY, resetItemIdIfNoAction(it))
                }
                put(Key.RESOURCES_DOWNLOAD_STATE, if (downloadState) Value.RESOURCES_STATE_DOWNLOADED else Value.RESOURCES_STATE_UNDOWNLOAD)
            })
            it.save()
        }
    }

    /**
     * 2006011015 音乐点击
     */
    @JvmStatic
    fun trackVideoEditorClickSongItemCommon(songItemId: String?, downloadState: Boolean) {
        track(EventId.VIDEO_EDIT_MUSIC_ITEM_CLICK) {
            it.putProperties(HashMap<String, String>().apply {
                songItemId?.let {
                    put(Key.CLICK_SONG_ITEM_ID_KEY, resetItemIdIfNoAction(it))
                }
                put(Key.RESOURCES_DOWNLOAD_STATE, if (downloadState) Value.RESOURCES_STATE_DOWNLOADED else Value.RESOURCES_STATE_UNDOWNLOAD)
            })
            it.save()
        }
    }

    /**
     * 2006011016 滤镜点击
     */
    @JvmStatic
    fun trackVideoEditorClickFilterItemCommon(filterItemId: String?) {
        track(EventId.VIDEO_EDIT_FILTER_ITEM_CLICK) {
            it.putProperties(HashMap<String, String>().apply {
                filterItemId?.let {
                    put(Key.CLICK_FILTER_ITEM_ID_KEY, resetItemIdIfNoAction(it))
                }
            })
            it.save()
        }
    }

    /**
     * 2006011017 特效点击
     */
    @JvmStatic
    fun trackVideoEditorClickFxItemCommon(fxItemId: String?) {
        track(EventId.VIDEO_EDIT_FX_ITEM_CLICK) {
            it.putProperties(HashMap<String, String>().apply {
                fxItemId?.let {
                    put(Key.CLICK_FX_ITEM_ID_KEY, resetItemIdIfNoAction(it))
                }
            })
            it.save()
        }
    }


    /**
     * 2006011018 视频编辑-导出实况 或者导出照片
     * @param context Context 上下文
     * @param oliveUri String 导出LivePhoto的 uri
     * @param costTime Long 导出执行用时（ms）
     * @param savaType: SaveType 导出类型，olive、photo
     * @param resultType Int 导出的结果code码 0:成功 1:其他类型失败 2:存储空间不足失败 3:抽帧失败 4:算法执行失败
     */
    @JvmStatic
    fun trackVideoEditorExportOLiveOrPhoto(
        context: Context,
        oliveUri: String?,
        costTime: Long,
        savaType: SaveType,
        resultType: Int
    ) {
        val eventId = if (savaType == SaveType.NORMAL) {
            EventId.VIDEO_EDIT_EXPORT_PHOTO_ITEM_CLICK
        } else {
            EventId.VIDEO_EDIT_EXPORT_OLIVE_ITEM_CLICK
        }
        track(eventId) {
            // 获取原视频的hdrType
            val originVideoHdrType = getHdrType(VideoTypeUtils.getOriginVideoHdrType(context, videoUri, mVideoMediaItem))
            it.putProperty(Key.VIDEO_EDIT_VIDEO_TYPE, originVideoHdrType)
            it.putProperty(Key.VIDEO_EDIT_EXPORT_PHOTO_IMAGE_TYPE, "image/jpeg")
            videoResolution?.let { videoWidthHeight ->
                it.putProperty(Key.VIDEO_EDIT_VIDEO_WIDTH_HEIGHT, videoWidthHeight)
            }
            oliveUri?.let { uri ->
                val oliveHdrType = getHdrType(VideoTypeUtils.getOliveHdrType(context, uri))
                it.putProperty(Key.VIDEO_EDIT_EXPORT_OLIVE_TYPE, oliveHdrType)
            }
            it.putProperty(Key.VIDEO_EDIT_EXPORT_COST_TIME, costTime)
            it.putProperty(Key.VIDEO_EDIT_EXPORT_RESULT_TYPE, getResultType(resultType))
            it.save()
        }
    }

    /**
     * 获取埋点需要的hdrType
     * @param hdrType 视频的类型
     * @return String 返回hdr的类型
     */
    private fun getHdrType(hdrType: Int): String {
        return when (hdrType) {
            VideoTypeUtils.HDR_TYPE_DOLBY -> Value.VIDEO_HDR_TYPE_DOLBYVISION
            VideoTypeUtils.HDR_TYPE_HLG -> Value.VIDEO_HDR_TYPE_HLG
            VideoTypeUtils.HDR_TYPE_HDR10 -> Value.VIDEO_HDR_TYPE_HDR10
            VideoTypeUtils.HDR_TYPE_HDR10_PLUS -> Value.VIDEO_HDR_TYPE_HDR10PLUS
            VideoTypeUtils.HDR_TYPE_NO_HDR -> Value.VIDEO_HDR_TYPE_SDR
            else -> Value.VIDEO_HDR_TYPE_UNKNOWN
        }
    }

    /**
     * 导出资源（图片或者olive）的code码映射
     * @param resultType Int 导出资源（图片或者olive）成功失败的code码
     * @return Int 埋点所需要的值
     */
    private fun getResultType(resultType: Int): Int {
        return when (resultType) {
            SAVE_SUCCESS -> Value.EXPORT_RESOURCE_RESULT_TYPE_SUCCESS
            SAVE_FAILED_NO_SPACE -> Value.EXPORT_RESOURCE_RESULT_TYPE_NO_SPACE_FAILED
            GET_FRAME_TASK_ERROR -> Value.EXPORT_RESOURCE_RESULT_TYPE_FRAME_FAILED
            MEICAM_FORCE_STOP_SAVE -> Value.EXPORT_RESOURCE_RESULT_TYPE_MEICAM_FORCE_STOP_SAVE
            TASK_STOP_FORCE_STOP_SAVE -> Value.EXPORT_RESOURCE_RESULT_TYPE_TASK_STOP_FORCE_STOP_SAVE
            EXPORT_OLIVE_TASK_ERROR -> Value.EXPORT_RESOURCE_RESULT_TYPE_OLIVE_TASK_ERROR
            SAVE_FAILED_ERROR -> Value.EXPORT_RESOURCE_RESULT_TYPE_SAVE_FAILED_ERROR
            else -> Value.EXPORT_RESOURCE_RESULT_TYPE_OTHER_FAILED
        }
    }

    /**
     * 主题、滤镜、特效、音乐点击空的item时，上传itemId为0
     * @param itemId
     *@return string
     */
    private fun resetItemIdIfNoAction(itemId: String): String =
        if (Value.NO_CHOOSE_ACTION.equals(itemId, ignoreCase = true)) "0" else itemId

    private fun track(eventId: String, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = eventId,
            type = VideoEditorTrackConstant.TYPE_VIDEO_EDITOR,
            func = func
        )
    }
}