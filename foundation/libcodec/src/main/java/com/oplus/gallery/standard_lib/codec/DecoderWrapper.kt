/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - ImageDecoderWrapper.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/19
 ** Author      : YongQ<PERSON>.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec

import com.oplus.gallery.standard_lib.codec.bitmap.BitmapImageDecoder
import com.oplus.gallery.standard_lib.codec.bitmap.BitmapRegionDecoder
import java.io.FileDescriptor

@Deprecated("TODO yaoweihe 后续重构去掉DecoderWrapper")
object DecoderWrapper {
    private const val TAG = "DecoderWrapper"

    @JvmStatic
    fun getImageDecoder(): IImageDecoder {
        return BitmapImageDecoder()
    }

    @JvmStatic
    fun getIRegionDecoder(fileDescriptor: FileDescriptor): IRegionDecoder? {
        val bitmapRegionDecoder = BitmapRegionDecoder(fileDescriptor)
        return if (bitmapRegionDecoder.isValid()) {
            bitmapRegionDecoder
        } else {
            null
        }
    }


    @JvmStatic
    fun getIRegionDecoder(data: ByteArray, offset: Int, length: Int): IRegionDecoder {
        return BitmapRegionDecoder(data, offset, length)
    }

    @JvmStatic
    @JvmOverloads
    fun getIRegionDecoder(filePath: String, isUseFd: Boolean = false): IRegionDecoder? {
        val bitmapRegionDecoder = BitmapRegionDecoder(filePath, isUseFd)
        return if (bitmapRegionDecoder.isValid()) {
            bitmapRegionDecoder
        } else {
            null
        }
    }
}