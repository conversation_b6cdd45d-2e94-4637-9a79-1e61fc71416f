/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamAudioTrack.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.audio;

import android.text.TextUtils;

import com.meicam.sdk.NvsAudioClip;
import com.meicam.sdk.NvsAudioTrack;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.AudioClipParams;
import com.oplus.gallery.videoeditorpage.video.business.music.volume.entity.Volume;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IAudioClip;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IAudioTrack;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;

import java.util.ArrayList;
import java.util.List;

public class MeicamAudioTrack implements IAudioTrack {
    private static final String TAG = "MeicamAudioTrack";
    private ArrayList<MeicamAudioClip> mAudioClipList;
    private transient NvsAudioTrack mNvsAudioTrack;
    private Volume mVolume;
    private boolean mIsFromAiTemplate = false;
    private boolean mMuted = false; //标志当前是否为静音，默认为false

    public void setIsFromAiTemplate(boolean mIsFromAiTemplate) {
        this.mIsFromAiTemplate = mIsFromAiTemplate;
    }

    public boolean isFromAiTemplate() {
        return mIsFromAiTemplate;
    }

    public MeicamAudioTrack() {
        mAudioClipList = new ArrayList<>();
        mVolume = new Volume();
    }

    @Override
    public void bindNvsObject(NvsAudioTrack nvsVideoTrack) {
        mNvsAudioTrack = nvsVideoTrack;
        setMuted(mMuted);
    }

    /**
     * 将所有音频片段转为IRange
     *
     * @return IRange列表
     */
    @Override
    public List<IBaseClip> convertToRangeList() {
        return new ArrayList<>(mAudioClipList);
    }

    public void clearNvsObjects() {
        for (MeicamAudioClip clip : mAudioClipList) {
            clip.clearNvsObjects();
        }
    }

    @Override
    public IAudioClip appendAudioClip(AudioClipParams params) {
        if (params == null) {
            return null;
        }
        if (TextUtils.isEmpty(params.getPath()) || (params.getTrimInTime() < 0) || (params.getTrimOutTime() <= params.getTrimInTime())) {
            GLog.e(TAG, "appendAudioClip exception" + ", in:" + params.getTrimInTime()
                    + ", out:" + params.getTrimOutTime() + ", path:" + params.getPath());
            return null;
        }

        MeicamAudioClip meicamAudioClip = new MeicamAudioClip(params);
        mAudioClipList.add(meicamAudioClip);
        if (hasNvsObject()) {
            NvsAudioClip nvsAudioClip = mNvsAudioTrack.appendClip(params.getPath()
                    , params.getTrimInTime()
                    , params.getTrimOutTime());
            if (nvsAudioClip == null) {
                GLog.e(TAG, "nvsAudioClip == null:" + params.getPath()
                        + ",in"
                        + params.getTrimInTime()
                        + ",out"
                        + params.getTrimOutTime());
                return null;
            }
            meicamAudioClip.bindNvsObject(nvsAudioClip);
            meicamAudioClip.setInPoint(nvsAudioClip.getInPoint());
        } else {
            if (mAudioClipList.size() >= 2) {
                int lastIndex = mAudioClipList.size() - 2;
                MeicamAudioClip audioClip = mAudioClipList.get(lastIndex);
                if (audioClip != null) {
                    long inPoint = audioClip.getInPoint() + audioClip.getDuration();
                    meicamAudioClip.setInPoint(inPoint);
                }
            }
        }
        meicamAudioClip.setMuted(isMuted());
        GLog.d(TAG, "appendAudioClip: meicamAudioClip is muted : " + meicamAudioClip.isMuted());
        return meicamAudioClip;
    }

    @Override
    public IAudioClip addAudioClip(AudioClipParams params) {
        if (params == null) {
            return null;
        }
        if (TextUtils.isEmpty(params.getPath()) || (params.getTrimInTime() < 0) || (params.getTrimOutTime() <= params.getTrimInTime())
                || (params.getInPoint() < 0)) {
            GLog.e(TAG, "addAudioClip exception" + ", in:" + params.getTrimInTime()
                    + ", out:" + params.getTrimOutTime() + ", path:" + params.getPath() + " inPoint: " + params.getInPoint());
            return null;
        }

        MeicamAudioClip meicamAudioClip = new MeicamAudioClip(params);

        int index = mAudioClipList.size();
        for (int i = 0; i < mAudioClipList.size(); i++) {
            MeicamAudioClip clip = mAudioClipList.get(i);
            if (clip.getInPoint() >= params.getInPoint()) {
                index = i;
                break;
            }
        }
        if (index < mAudioClipList.size()) {
            mAudioClipList.add(index, meicamAudioClip);
        } else {
            mAudioClipList.add(meicamAudioClip);
        }

        if (hasNvsObject()) {
            NvsAudioClip nvsAudioClip = mNvsAudioTrack.addClip(params.getPath(), params.getInPoint(), params.getTrimInTime(), params.getTrimOutTime());
            if (nvsAudioClip == null) {
                mAudioClipList.remove(meicamAudioClip);
                GLog.e(TAG, "nvsAudioClip == null:" + params.getPath()
                        + ",inPoint="
                        + params.getInPoint()
                        + ",in="
                        + params.getTrimInTime()
                        + ",out="
                        + params.getTrimOutTime());
                return null;
            }
            meicamAudioClip.bindNvsObject(nvsAudioClip);
        }
        meicamAudioClip.setMuted(isMuted());
        GLog.d(TAG, "addAudioClip: meicamAudioClip is muted : " + meicamAudioClip.isMuted());
        return meicamAudioClip;
    }

    public boolean hasNvsObject() {
        return mNvsAudioTrack != null;
    }

    @Override
    public List<IAudioClip> getClipList() {
        return (List<IAudioClip>) mAudioClipList.clone();
    }

    @Override
    public IAudioClip getClipByTimelinePosition(long timelinePosition) {
        if (!hasNvsObject()) {
            return null;
        }

        if (mAudioClipList.isEmpty() || (timelinePosition < 0)) {
            return null;
        }

        for (MeicamAudioClip clip : mAudioClipList) {
            if (clip == null) {
                continue;
            }
            if ((clip.getInPoint() <= timelinePosition) && (clip.getOutPoint() > timelinePosition)) {
                return clip;
            }
        }

        return null;
    }

    @Override
    public IAudioClip getNextClipByTimelinePosition(long timelinePosition) {
        if (!hasNvsObject()) {
            return null;
        }

        if (mAudioClipList.isEmpty() || (timelinePosition < 0)) {
            return null;
        }

        for (MeicamAudioClip clip : mAudioClipList) {
            if (clip == null) {
                continue;
            }
            if (clip.getInPoint() >= timelinePosition) {
                return clip;
            }
        }

        return null;
    }

    @Override
    public int getClipCount() {
        return mAudioClipList.size();
    }

    @Override
    public long getDuration() {
        long result = 0;
        for (MeicamAudioClip audioClip : mAudioClipList) {
            result += audioClip.getDuration();
        }
        return result;
    }

    @Override
    public int getType() {
        return StreamingConstant.TrackType.TRACK_TYPE_AUDIO;
    }

    @Override
    public long setInPoint(int clipIndex, long newInPoint) {
        return -1;
    }

    @Override
    public long setOutPoint(int clipIndex, long newOutPoint) {
        return -1;
    }

    @Override
    public boolean splitClip(int clipIndex, long splitPoint, boolean needPicMove) {
        return false;
    }

    @Override
    public boolean removeClip(int clipIndex, boolean keepSpace) {
        if ((clipIndex < 0) || (clipIndex >= mAudioClipList.size())) {
            GLog.e(TAG, "removeClip error: current index is invalid!");
            return false;
        }

        if (hasNvsObject()) {
            boolean result = mNvsAudioTrack.removeClip(clipIndex, keepSpace);
            if (!result) {
                GLog.e(TAG, "Call removeClip, error:" + "clipIndex: "
                        + clipIndex + ", keepSpace: " + keepSpace);
                return false;
            }
        }

        mAudioClipList.remove(clipIndex);
        return true;
    }

    @Override
    public boolean moveClip(int clipIndex, int destClipIndex) {
        return false;
    }

    @Override
    public boolean removeAllClips() {
        if (hasNvsObject()) {
            boolean result = mNvsAudioTrack.removeAllClips();
            if (!result) {
                GLog.e(TAG, "Call removeAllClips error: mNvsAudioTrack == null");
                return false;
            }
        }

        mAudioClipList.clear();
        return true;
    }

    @Override
    public IClip getClip(int clipIndex) {
        if ((clipIndex < 0) || (clipIndex >= mAudioClipList.size())) {
            GLog.e(TAG, "get audio clip error: index is invalid!");
            return null;
        }

        return mAudioClipList.get(clipIndex);
    }

    @Override
    public void setVolumeGain(float leftVolumeGain, float rightVolumeGain) {
        this.mVolume = new Volume(leftVolumeGain, rightVolumeGain);
        this.mMuted = (leftVolumeGain == Volume.VOLUME_MUTE_VALUE);
        if (mNvsAudioTrack == null) {
            GLog.e(TAG, "Call setVolumeGain error: mNvsAudioTrack is null.");
            return;
        }
        mNvsAudioTrack.setVolumeGain(mVolume.getLeftVolume(), mVolume.getRightVolume());
    }

    @Override
    public Volume getVolumeGain() {
        return this.mVolume;
    }

    @Override
    public boolean isMuted() {
        return mMuted;
    }

    @Override
    public void setMuted(boolean muted) {
        mMuted = muted;
        mVolume.setMute(muted);
        if (mNvsAudioTrack == null) {
            GLog.e(TAG, "Call setMuted error: mNvsAudioTrack is null.");
            return;
        }
        mNvsAudioTrack.setVolumeGain(mVolume.getLeftVolume(), mVolume.getRightVolume());
    }

    /**
     * 获取音频剪辑列表的副本。
     *
     * @return 包含音频剪辑的列表副本。
     */
    @Override
    public List<IClip> getIClipList() {
        return (List<IClip>) mAudioClipList.clone();
    }

    /**
     * 移除指定时间范围内的音频片段
     *
     * @param startTimelinePos 开始时间位置
     * @param endTimelinePos   结束时间位置
     * @param keepSpace        移除区间内的片段后，是否保留该区间所占轨道上的空间。true 表示保留，false 表示不保留。
     * @return 是否成功移除
     */
    @Override
    public boolean removeRange(long startTimelinePos, long endTimelinePos, boolean keepSpace) {
        if (hasNvsObject()) {
            boolean result = mNvsAudioTrack.removeRange(startTimelinePos, endTimelinePos, true);
            if (result) {
                mAudioClipList.removeIf(audioClip -> audioClip.getInPoint() >= startTimelinePos && audioClip.getOutPoint() <= endTimelinePos);
            } else {
                GLog.e(TAG, LogFlag.DL, "[removeRange] startTimelinePos=" + startTimelinePos + " ,endTimelinePos= " + endTimelinePos);
            }
            return result;
        }
        return false;
    }
}
