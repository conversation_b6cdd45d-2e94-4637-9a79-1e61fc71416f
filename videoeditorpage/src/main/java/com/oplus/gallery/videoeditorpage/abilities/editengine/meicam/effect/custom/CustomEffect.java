/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CustomEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.custom;

import android.util.Size;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.utlis.FileUtil;
import com.oplus.gallery.videoeditorpage.utlis.JsonUtil;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.filter.EffectRenderer;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data.FxConfigInfo;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.meicam.sdk.NvsCustomVideoFx;
import com.meicam.sdk.NvsTimelineVideoFx;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class CustomEffect extends BaseVideoFx implements IMeicamCustomerEffect {
    private static final String TAG = "CustomEffect";

    public static final String JSON_TYPE_NAME = "CustomEffect";
    private static final int MILLISECOND_TO_SECOND = 1000;
    @SerializedName("class_type")
    protected String mClassType = JSON_TYPE_NAME;// use to mark the type in saved json file
    protected transient List<NvsCustomVideoFx.Renderer> mRendererList;
    protected FxConfigInfo mConfigInfo;
    protected transient List<NvsTimelineVideoFx> mNvsVideoFxList;
    private transient IVideoTrack mTargetTrack;


    public CustomEffect(String name, int type) {
        super(name, type);
    }

    @Override
    public void setOutTime(long outTime) {
        if (mNvsVideoFxList != null) {
            for (NvsTimelineVideoFx nvsTimelineVideoFx : mNvsVideoFxList) {
                nvsTimelineVideoFx.changeOutPoint(outTime);
            }
        }
        super.setOutTime(outTime);
    }

    @Override
    public void setInTime(long inTime) {
        if (mNvsVideoFxList != null) {
            for (NvsTimelineVideoFx nvsTimelineVideoFx : mNvsVideoFxList) {
                nvsTimelineVideoFx.changeInPoint(inTime);
            }
        }
        super.setInTime(inTime);
    }

    @Override
    protected CustomEffect clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        CustomEffect result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new CustomEffect(getName(), TYPE_CUSTOMER_FX);
        }
        return result;
    }

    @Override
    public List<NvsCustomVideoFx.Renderer> getRendererList() {
        if (mRendererList == null) {
            mRendererList = new ArrayList<>();
            File config = new File(mName + File.separator + CONFIG);
            mConfigInfo = JsonUtil.parseJson(FileUtil.readFromFile(config), new TypeToken<FxConfigInfo>() {
            });

            if (mConfigInfo != null) {
                for (int i = 0; i < mConfigInfo.getPassCount(); i++) {
                    Renderer renderer = new Renderer(mName + File.separator + SHADER_PASS_DIR + i);
                    mRendererList.add(renderer);
                }
            }
        }
        return mRendererList;
    }

    public List<NvsTimelineVideoFx> getNvsVideoFxList() {
        return mNvsVideoFxList;
    }

    public void addNvsVideoFx(NvsTimelineVideoFx nvsVideoFx) {
        if (mNvsVideoFxList == null) {
            mNvsVideoFxList = new ArrayList<>();
        }
        mNvsVideoFxList.add(nvsVideoFx);
    }

    public void setTargetVideoTrack(IVideoTrack videoTrack) {
        mTargetTrack = videoTrack;
    }

    private class Renderer implements NvsCustomVideoFx.Renderer {
        private String mPath;
        private EffectRenderer mEffectRenderer;

        public Renderer(String path) {
            mPath = path;
        }

        @Override
        public void onInit() {

        }

        @Override
        public void onCleanup() {

        }

        @Override
        public void onPreloadResources() {
        }

        @Override
        public void onRender(NvsCustomVideoFx.RenderContext renderContext) {
            if ((mEffectRenderer == null) || !mEffectRenderer.isProgramReady()) {
                mEffectRenderer = new EffectRenderer(mPath);
                mEffectRenderer.createProgram();
                if ((mConfigInfo != null) && (mConfigInfo.isRepeat())) {
                    mEffectRenderer.setDuration(mConfigInfo.getDuration());
                }
            }
            mEffectRenderer.setTextureSize(renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
            mEffectRenderer.setInTextureId(renderContext.inputVideoFrame.texId);
            mEffectRenderer.setOutTextureId(renderContext.outputVideoFrame.texId);
            float progress = 0f;
            if ((mConfigInfo != null) && (mConfigInfo.isRepeat())) {
                long duration = mConfigInfo.getDuration() * MILLISECOND_TO_SECOND;
                progress = (float) (Math.floorMod(renderContext.effectTime - renderContext.effectStartTime, duration)) / duration;
            } else {
                progress = (float) (renderContext.effectTime - renderContext.effectStartTime) / (renderContext.effectEndTime - renderContext.effectStartTime);
            }
            GLog.d(TAG, "onRender, progress : " + progress);
            mEffectRenderer.setProgress(progress);
            setContentArea(renderContext.effectTime, renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
            mEffectRenderer.draw();
        }

        private void setContentArea(long currentTime, int maxWidth, int maxHeight) {
            if ((mTargetTrack != null) && (mTrackIndex == 0)) {
                IVideoClip nowClip = mTargetTrack.getClipByTimelinePostion(currentTime);
                if (nowClip != null) {
                    ClipAreaCache clipAreaCache = getClipContentArea(nowClip, maxWidth, maxHeight);
                    if (clipAreaCache == null) {
                        return;
                    }
                    mEffectRenderer.setContentSize(clipAreaCache.mContentAreaWidth, clipAreaCache.mContentAreaHeight);
                }
            }
        }
    }

    private ClipAreaCache getClipContentArea(IVideoClip videoClip, int maxWidth, int maxHeight) {
        ClipAreaCache clipAreaCache = new ClipAreaCache();
        clipAreaCache.mCurrentClip = videoClip;
        if ((videoClip == null) || (maxHeight == 0)) {
            GLog.d(TAG, "getClipContentArea failed,(videoClip == null):" + (videoClip == null));
            return clipAreaCache;
        }

        // 获取图片/视频的原始宽高
        Size size = videoClip.getFileVideoSize();
        if ((size == null) || (size.getHeight() == 0) || (size.getWidth() == 0)) {
            GLog.d(TAG, "getClipContentArea getSize is null:" + (size == null));
            return clipAreaCache;
        }

        // 按照规则计算素材应该在timeline内的宽高
        double targetRatio = ((double) maxWidth) / maxHeight;
        double videoOriginalRatio = ((double) size.getWidth()) / size.getHeight();
        boolean resizeByWidth = targetRatio - videoOriginalRatio < 0; //是根据宽还是高来重新计算素材的内容宽高
        if (resizeByWidth) {
            clipAreaCache.mContentAreaWidth = maxWidth;
            clipAreaCache.mContentAreaHeight = (int) (maxWidth / videoOriginalRatio);
        } else {
            clipAreaCache.mContentAreaHeight = maxHeight;
            clipAreaCache.mContentAreaWidth = (int) (maxHeight * videoOriginalRatio);
        }
        return clipAreaCache;
    }

    private static class ClipAreaCache {
        private IVideoClip mCurrentClip = null;
        private int mContentAreaWidth;
        private int mContentAreaHeight;
    }
}
