/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseWaterMark.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.watermark;

import androidx.annotation.NonNull;

import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoTimelineEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;

public abstract class BaseWaterMark extends BaseVideoTimelineEffect {
    public static final int WATERMARK_IN_POINT = 0;

    public BaseWaterMark(String name) {
        super(name);
    }

    @NonNull
    @Override
    public abstract Object clone();

    public abstract BaseVideoTimelineEffect addSticker(String filePath, int displayWidth, int displayHeight,
                                                       float opacity, int transitionX, int transitionY);

    public abstract BaseCaption addCaption(@NonNull CaptionParams captionParam);

    public abstract BaseVideoTimelineEffect getSticker();

    public abstract BaseCaption getCaption();

}
