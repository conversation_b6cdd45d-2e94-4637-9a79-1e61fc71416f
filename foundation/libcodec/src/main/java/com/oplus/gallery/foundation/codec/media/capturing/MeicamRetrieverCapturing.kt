/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamRetrieverCapturing.kt
 ** Description:使用美摄的方式实现视频画面截帧
 ** Version: 1.0
 ** Date : 2022/12/10
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG: MeicamRetrieverCapturing
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewujie@Apps.Gallery3D      2022/12/10    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.codec.media.capturing

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Handler
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.codec.videoframe.DecoderType
import com.oplus.gallery.standard_lib.codec.videoframe.MeicamVideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoder

/**
 * 美摄的视频画面取帧
 * @param context Context
 * @param uri 视频的uri
 * @param callbackHandler 回调的Handler
 * @param decoderType 解码器 解码类型
 */
class MeicamRetrieverCapturing(
    context: Context,
    uri: Uri,
    private val callbackHandler: Handler?,
    private val decoderType: DecoderType,
) : ICapturing {

    private val meicamThumbnailDecoder = MeicamVideoThumbnailDecoder(decoderType).apply {
        setDataSource(context, uri)
    }

    override fun capture(frameAtTimeUs: Long, onCaptured: (Bitmap?) -> Unit) {
        runCatching {
            val bitmap = meicamThumbnailDecoder.decodeFrameBitmapAtTime(frameAtTimeUs, VideoThumbnailDecoder.INVALID_SIZE)
            callbackHandler?.post { onCaptured.invoke(bitmap) } ?: onCaptured.invoke(bitmap)
        }.onFailure {
            GLog.e(TAG, "[capture], decode frame bitmap at time error", it)
        }
    }

    override fun close() {
        meicamThumbnailDecoder.close()
    }

    companion object {
        private const val TAG = "MeicamRetrieverCapturing"
    }
}