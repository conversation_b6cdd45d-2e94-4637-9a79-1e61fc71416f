/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: IconTextMenuData
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.preview.data;

import androidx.annotation.Nullable;

import com.oplus.gallery.standard_lib.app.AppConstants;

public class IconTextMenuData extends IconMenuData {
    private String mFileText; // file name or path to titleText
    private CharSequence mText;
    private int mTextResId;
    private String mIconUrl;
    private String mAppName;

    public IconTextMenuData(int viewId, int textId, String iconUrl, String text) {
        this(viewId);
        this.mIconUrl = iconUrl;
        this.mText = text;
        this.mTextResId = textId;
    }

    public IconTextMenuData(int viewId, int textId, String text) {
        this(viewId);
        this.mText = text;
        this.mTextResId = textId;
    }


    public IconTextMenuData(int viewId) {
        super(viewId);
    }

    public IconTextMenuData(int viewId, int iconResId) {
        super(viewId, iconResId);
    }

    public IconTextMenuData(int viewId, int iconResId, CharSequence text) {
        super(viewId, iconResId);
        mText = text;
    }

    public IconTextMenuData(int viewId, int iconResId, int textResId) {
        super(viewId, iconResId);
        mTextResId = textResId;
    }

    public IconTextMenuData(int viewId, int iconResId, int highlightIconResId, int textResId) {
        super(viewId, iconResId, highlightIconResId);
        mTextResId = textResId;
    }

    public IconTextMenuData(int viewId, int iconResId, int textResId, String fileText) {
        super(viewId, iconResId);
        mTextResId = textResId;
        mFileText = fileText;
    }


    public void setFileText(String fileText) {
        this.mFileText = fileText;
    }

    public void setIconUrl(String iconUrl) {
        this.mIconUrl = iconUrl;
    }

    public String getIconUrl() {
        return mIconUrl;
    }


    public CharSequence getText() {
        return mText;
    }

    public int getTextId() {
        return mTextResId;
    }

    public String getFileText() {
        return mFileText;
    }


    public void setText(CharSequence text) {
        this.mText = text;
    }

    public String getAppName() {
        return mAppName;
    }

    public void setAppName(String appName) {
        this.mAppName = appName;
    }

    @Override
    public String toString() {
        return "IconTextMenuData{"
                + "mFileText='" + mFileText + '\''
                + ", mText=" + mText
                + ", mTextResId=" + mTextResId
                + ", mIconUrl='" + mIconUrl + '\''
                + '}';
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        IconTextMenuData that = (IconTextMenuData) obj;
        return mFileText.equals(that.mFileText)
            && mText.equals(that.mText)
            && mTextResId == that.mTextResId
            && mIconUrl.equals(that.mIconUrl)
            && mAppName.equals(that.mAppName);
    }

    @Override
    public int hashCode() {
        int result = mFileText.hashCode();
        result = AppConstants.MagicCode.HASH_CODE_MAGIC * result + mText.hashCode();
        result = AppConstants.MagicCode.HASH_CODE_MAGIC * result + mTextResId;
        result = AppConstants.MagicCode.HASH_CODE_MAGIC * result + mIconUrl.hashCode();
        result = AppConstants.MagicCode.HASH_CODE_MAGIC * result + mAppName.hashCode();
        return result;
    }
}
