/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - ImageHandler.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/5/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/5/6  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.videoeditorpage.memories.imageloader;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.oplus.gallery.foundation.util.debug.GLog;

import java.lang.ref.WeakReference;

public class ImageHandler extends Handler {
    private static final String TAG = "ImageHandler";
    private WeakReference<BaseThumbnailLoader> mLoaderManagerWr;

    public ImageHandler(BaseThumbnailLoader loaderManager) {
        super(Looper.getMainLooper());
        this.mLoaderManagerWr = new WeakReference<>(loaderManager);
    }

    @Override
    public void handleMessage(Message msg) {
        BaseThumbnailLoader loaderManager = mLoaderManagerWr.get();
        if (loaderManager != null) {
            switch (msg.what) {
                case BaseThumbnailLoader.MSG_UPDATE_THUMBNAIL:
                    loaderManager.afterLoadComplete();

                    BaseThumbnailLoader.BaseThumbnailTask task = (BaseThumbnailLoader.BaseThumbnailTask) msg.obj;

                    GLog.d(TAG, "msg_update_thumbnail, task:" + task.toString());

                    task.getRequestData().getThumbnailListener().updateThumbnail(
                            new ThumbnailRespond<>(task.getRequestData().getSource(), task.getBitmap()));
                    break;
                case BaseThumbnailLoader.MSG_CANCEL:
                    loaderManager.afterLoadComplete();
                    break;
                default:
                    break;
            }
        }
    }
}
