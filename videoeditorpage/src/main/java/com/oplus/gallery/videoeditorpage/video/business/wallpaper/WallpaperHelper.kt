/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - WallpaperHelper.kt
 ** Description: for Wallpaper. 壁纸视频剪辑辅助类
 ** Version: 1.0
 ** Date : 2025/4/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2025/4/22    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.content.Context
import android.os.Bundle
import android.webkit.MimeTypeMap
import androidx.core.os.bundleOf
import com.oplus.gallery.videoeditorpage.R
import com.oplus.appcache.OplusAppCacheManager
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.standard_lib.app.AppConstants.Path.PATH_NO_MEDIA
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.SaveVideoInfo
import com.oplus.wrapper.os.UserHandle

object WallpaperHelper {

    /**
     * 壁纸剪辑请求action
     */
    const val ACTION_SET_VIDEO_WALLPAPER: String =
        "android.service.wallpaper.CROP_AND_SET_WALLPAPER"

    /**
     * 当前传递给壁纸的uri是否为视频
     */
    const val KEY_IS_VIDEO_WALLPAPER = "key_video_wallpaper"

    /**
     * 壁纸剪辑请求requestCode
     */
    const val SET_VIDEO_WALLPAPER_REQUEST_CODE: Int = 0xFF

    private const val TAG = "WallpaperHelper"

    /**
     * 壁纸应用包名
     */
    private const val WALLPAPER_PACKAGE_NAME = "com.oplus.wallpapers"

    /**
     * 请求壁纸数据的provider uri
     */
    private const val WALLPAPER_PROVIDER_URI =
        "content://com.oplus.wallpapers.InspirationThemeProvider"

    /**
     * 客户端传入的调用版本号 固定一个值
     */
    private const val WALLPAPER_PROVIDER_DATA_VERSION = "1"

    /**
     * 请求壁纸参数的provider method
     */
    private const val METHOD_IS_SUPPORT_VIDEO_WALLPAPER = "support_video_wallpaper"

    /**
     * 请求壁纸参数的provider key
     */
    private const val KEY_VIDEO_WALLPAPER_PARAM = "video_wallpaper_param"

    /**
     * 请求系统三级缓存类型
     */
    private const val TYPE_QUERY_PROVIDER_CACHE = "call"

    /**
     * 查询的客户端包名 string
     */
    private const val KEY_CLIENT_PKG = "client_pkg"

    /**
     * 查询的provider uri string
     */
    private const val KEY_URI = "uri"

    /**
     * 关注的provider uri string
     */
    private const val KEY_OB_URI = "ob_uri"

    /**
     * 请求provider的method string
     */
    private const val KEY_CALL_METHOD = "call_method"

    /**
     * client端传入的调用版本号 string
     */
    private const val KEY_CLIENT_VERSION = "client_version"

    /**
     * server端包名 string
     */
    private const val KEY_SERVER_PKG = "server_pkg"

    /**
     * 是否需要关注语言变化 boolean
     */
    private const val KEY_LOCAL_CHANGED = "local_changed"

    private const val DEFAULT_VIDEO_SUFFIX: String = ".mp4"

    /**
     * 保存壁纸视频的固定名称
     */
    private const val WALLPAPER_VIDEO_FILE_NAME = "Wallpaper_"
    private const val FILE_DOT: String = "."

    private const val NAME_FLAG_MASK = 1

    private const val ENSURE_SPACE_MB: Long = 100L * 1024 * 1024

    /**
     * 保存视频壁纸的文件路径名
     */
    private val pathOfSaveWallpaperVideo =
        "${ContextGetter.context.getExternalFilesDir(null)}/.wallpaper"

    /**
     * 视频壁纸名称序号
     * 通过相应的逻辑，保证产生的临时文件只有两个，是为了解决频繁点击"继续"-"取消"导致的传给壁纸uri对应的文件异常
     */
    private var nameFlag = 0
        get() {
            field = (++field) and NAME_FLAG_MASK
            return field
        }

    /**
     * 保存壁纸视频的文件
     */
    var curFile: File? = null

    /**
     * 分享给壁纸应用的视频uri
     */
    private var uri: Uri? = null

    /**
     * 保存剪辑后的壁纸视频
     */
    fun saveVideo(context: Context, engine: EditorEngine, showLoading: () -> Unit) {
        WallPaperParam.curInstance?.apply {
            val file = File(getSaveDir(), getFileName(videoTrimTargetContainerFormat))
            FileOperationUtils.deleteExistFile(file)
            FilePathUtils.getRelativePath(file.absolutePath)
            if (!checkStorageEnough(context)) {
                GLog.w(TAG, LogFlag.DL, "[saveVideo] storage not enough")
                return
            }
            curFile = file
            showLoading()
            WallPaperParam.curInstance?.let { param ->
                engine.saveVideo(
                    SaveVideoInfo(
                        file.absolutePath,
                        param.videoTrimTargetMaxWidth,
                        param.videoTrimTargetMaxHeight,
                        param.videoTrimTargetMaxLength,
                        System.currentTimeMillis(),
                        param.videoTrimTargetMaxFrameRate,
                        param.videoTrimTargetMaxBitRate,
                        param.videoTrimTargetCodecFormat,
                        param.videoTrimKeepHdr
                    )
                )
            }
        }
    }

    /**
     * 获取保存壁纸视频的文件路径
     */
    private fun getSaveDir(): File {
        return File(pathOfSaveWallpaperVideo).apply {
            if (!exists()) {
                mkdirs()
                File(this, PATH_NO_MEDIA).createNewFile()
            } else {
                File(this, PATH_NO_MEDIA).let {
                    if (!it.exists()) {
                        it.createNewFile()
                    }
                }
            }
        }
    }

    /**
     * 获取保存生成视频壁纸的文件名
     */
    private fun getFileName(mimeType: String?): String {
        if (mimeType.isNullOrEmpty()) {
            return "$WALLPAPER_VIDEO_FILE_NAME$nameFlag$DEFAULT_VIDEO_SUFFIX"
        }
        val videoSuffix = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType)
        if (videoSuffix.isNullOrEmpty()) {
            return "$WALLPAPER_VIDEO_FILE_NAME$nameFlag$DEFAULT_VIDEO_SUFFIX"
        }
        return WALLPAPER_VIDEO_FILE_NAME + nameFlag + FILE_DOT + videoSuffix
    }

    /**
     * 从壁纸的provider查询生成视频的参数
     */
    fun queryParamFromWallpaper(): Bundle {
        return if (UserHandle.myUserId() == UserHandle.USER_SYSTEM) {
            val startTime = System.currentTimeMillis()
            val result = OplusAppCacheManager.getInstance().queryProviderCache(
                TYPE_QUERY_PROVIDER_CACHE, bundleOf(
                    KEY_CLIENT_PKG to ContextGetter.context.packageName,
                    KEY_URI to WALLPAPER_PROVIDER_URI,
                    KEY_OB_URI to WALLPAPER_PROVIDER_URI,
                    KEY_CALL_METHOD to METHOD_IS_SUPPORT_VIDEO_WALLPAPER,
                    KEY_CLIENT_VERSION to WALLPAPER_PROVIDER_DATA_VERSION,
                    KEY_SERVER_PKG to WALLPAPER_PACKAGE_NAME,
                    KEY_LOCAL_CHANGED to false
                )
            )?.getBundle(KEY_VIDEO_WALLPAPER_PARAM) ?: Bundle()
            result.also {
                GLog.d(TAG, LogFlag.DL) {
                    "queryParamFromWallpaper: isSupportVideoWallpaper cost: ${System.currentTimeMillis() - startTime}ms"
                }
            }
        } else {
            Bundle()
        }
    }

    /**
     * 将视频壁纸的uri授予壁纸应用READ权限
     * @param activity 活动页
     * @param uri 分享的文件uri
     */
    fun grantUriPermission(activity: Activity, uri: Uri) {
        this.uri = uri
        activity.grantUriPermission(WALLPAPER_PACKAGE_NAME, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
    }

    /**
     * 手动回收壁纸应用的uri权限
     * @param activity 活动页
     */
    fun revokeUriPermission(activity: Activity) {
        uri?.let {
            activity.revokeUriPermission(it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
    }

    /**
     * 检查存储空间是否足够
     */
    fun checkStorageEnough(context: Context): Boolean {
        // check internal storage enough or not
        var isFull = !StorageLimitHelper.hasEnoughStorageSpace(
            OplusEnvironment.StorageType.PHONE_STORAGE,
            ENSURE_SPACE_MB
        )
        if (isFull) {
            val hasExternal = OplusEnvironment.isExternalMounted()
            if (!hasExternal) {
                StorageTipsHelper.show(
                    context,
                    OplusEnvironment.StorageType.PHONE_STORAGE,
                    StorageLimitHelper.State.NO_SPACE,
                    R.string.videoeditor_video_editor_disk_space_not_enough
                )
                return false
            }

            // check external storage enough or not
            isFull = !StorageLimitHelper.hasEnoughStorageSpace(
                OplusEnvironment.StorageType.SDCARD_STORAGE,
                ENSURE_SPACE_MB
            )
            if (isFull) {
                StorageTipsHelper.show(
                    context,
                    OplusEnvironment.StorageType.SDCARD_STORAGE,
                    StorageLimitHelper.State.NO_SPACE,
                    R.string.videoeditor_video_editor_disk_space_not_enough
                )
                return false
            }
        }

        return true
    }
}