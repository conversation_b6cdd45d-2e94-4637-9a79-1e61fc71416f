/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ClassAllMethodFinder.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/08/14		1.0		OPLUS_ARCH_BUS
 *********************************************************************************/

package com.oplus.gallery.bus_lib.finders

import android.os.Build
import androidx.annotation.RequiresApi
import com.oplus.gallery.bus_lib.beans.MethodInfo

class ClassAllMethodFinder : IMethodFinder {
    @RequiresApi(Build.VERSION_CODES.O)
    override fun findMethods(subscriber: Any): MutableSet<MethodInfo>? {
        var clazz: Class<*>? = subscriber.javaClass

        val methodInfos = HashSet<MethodInfo>()
        while (clazz != null) {
            methodInfos.addAll(ClassMethodCache.findMethodByClass(clazz))
            clazz.interfaces.forEach {
                methodInfos.addAll(ClassMethodCache.findMethodByClass(it))
            }
            clazz = clazz.superclass
        }
        return methodInfos
    }
}