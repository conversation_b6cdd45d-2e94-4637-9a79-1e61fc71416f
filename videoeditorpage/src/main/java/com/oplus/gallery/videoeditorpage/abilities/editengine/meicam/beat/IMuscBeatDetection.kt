/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IMuscBeatDetection.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.beat

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.beat.MeicamMusicBeatDetection.MusicBeatDetectionCallback

interface IMuscBeatDetection : AutoCloseable {
    /**
     * 必须调 否则容易内存泄露
     * 	销毁音乐节奏检测类实例。注意: 销毁之后可以再次创建及获取
     */
    fun release()


    /**
     * 开始检测
     *
     * 该函数用于检测输入音乐文件中的节奏点，并根据给定的敏感度参数判断相邻节奏点的时间间隔。
     *
     * @param inputFilePath 输入的音乐文件路径，用于指定需要检测的音乐文件。
     * @param sensitivity 相邻的两个节奏点的最小时间间隔，用于控制检测的敏感度。
     * @return 如果成功检测到节奏点并完成处理，则返回true；否则返回false。
     */
    fun startDetect(inputFilePath: String, sensitivity: Int): Boolean

    /**
     * 设置音乐节奏检测回调接口
     */
    fun setBeatDetectionCallback(callback: MusicBeatDetectionCallback)
}