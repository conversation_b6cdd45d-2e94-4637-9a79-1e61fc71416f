<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="316dp"
    android:height="148dp"
    android:viewportWidth="316"
    android:viewportHeight="148">
  <group>
    <clip-path
        android:pathData="M0,0h316v148h-316z"/>
    <path
        android:pathData="M0,0h316v148h-316z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="57.65"
            android:startY="-17"
            android:endX="112.41"
            android:endY="197.37"
            android:type="linear">
          <item android:offset="0" android:color="#FF282929"/>
          <item android:offset="1" android:color="#FF252627"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M316,27L105,27L105,26L316,26L316,27Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="105"
            android:startY="27.5"
            android:endX="316"
            android:endY="27.5"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.37" android:color="#FF36393B"/>
          <item android:offset="0.62" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M316,108L-0,108L0,107L316,107L316,108Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="-0"
            android:startY="108.5"
            android:endX="316"
            android:endY="108.5"
            android:type="linear">
          <item android:offset="0.08" android:color="#0036393B"/>
          <item android:offset="0.22" android:color="#FF36393B"/>
          <item android:offset="0.56" android:color="#FF36393B"/>
          <item android:offset="0.85" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M163,67.5L50,67.5L50,66.5L163,66.5L163,67.5Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="50"
            android:startY="67.5"
            android:endX="163"
            android:endY="67.5"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.22" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M317.2,66.7L244.6,66.7L244.6,65.7L317.2,65.7L317.2,66.7Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="244.6"
            android:startY="66.2"
            android:endX="317.2"
            android:endY="66.2"
            android:type="linear">
          <item android:offset="0" android:color="#0044484B"/>
          <item android:offset="0.14" android:color="#FF36393B"/>
          <item android:offset="0.6" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0044484B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M122.2,189.8L122.2,-11.8L123.2,-11.8L123.2,189.8L122.2,189.8Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="121.7"
            android:startY="-11.8"
            android:endX="122.2"
            android:endY="130.39"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.17" android:color="#FF36393B"/>
          <item android:offset="0.58" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M163,189.8L163,-11.8L164,-11.8L164,189.8L163,189.8Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="162.5"
            android:startY="-11.8"
            android:endX="163"
            android:endY="140.01"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.17" android:color="#FF36393B"/>
          <item android:offset="0.61" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M203.5,26.3L203.5,-11.8L204.5,-11.8L204.5,26.3L203.5,26.3ZM203.5,189.8L203.5,107.6L204.5,107.6L204.5,189.8L203.5,189.8Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="203.5"
            android:startY="-11.8"
            android:endX="203.5"
            android:endY="189.8"
            android:type="linear">
          <item android:offset="0" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M244.6,-11.2L244.6,-11.8L245.6,-11.8L245.6,-11.2L244.6,-11.2ZM244.6,-8.8L244.6,-10L245.6,-10L245.6,-8.8L244.6,-8.8ZM244.6,-6.4L244.6,-7.6L245.6,-7.6L245.6,-6.4L244.6,-6.4ZM244.6,-4L244.6,-5.2L245.6,-5.2L245.6,-4L244.6,-4ZM244.6,-1.6L244.6,-2.8L245.6,-2.8L245.6,-1.6L244.6,-1.6ZM244.6,0.8L244.6,-0.4L245.6,-0.4L245.6,0.8L244.6,0.8ZM244.6,3.2L244.6,2L245.6,2L245.6,3.2L244.6,3.2ZM244.6,5.6L244.6,4.4L245.6,4.4L245.6,5.6L244.6,5.6ZM244.6,8L244.6,6.8L245.6,6.8L245.6,8L244.6,8ZM244.6,10.4L244.6,9.2L245.6,9.2L245.6,10.4L244.6,10.4ZM244.6,12.8L244.6,11.6L245.6,11.6L245.6,12.8L244.6,12.8ZM244.6,15.2L244.6,14L245.6,14L245.6,15.2L244.6,15.2ZM244.6,17.6L244.6,16.4L245.6,16.4L245.6,17.6L244.6,17.6ZM244.6,20L244.6,18.8L245.6,18.8L245.6,20L244.6,20ZM244.6,22.4L244.6,21.2L245.6,21.2L245.6,22.4L244.6,22.4ZM244.6,24.8L244.6,23.6L245.6,23.6L245.6,24.8L244.6,24.8ZM244.6,27.2L244.6,26L245.6,26L245.6,27.2L244.6,27.2ZM244.6,29.6L244.6,28.4L245.6,28.4L245.6,29.6L244.6,29.6ZM244.6,32L244.6,30.8L245.6,30.8L245.6,32L244.6,32ZM244.6,34.4L244.6,33.2L245.6,33.2L245.6,34.4L244.6,34.4ZM244.6,36.8L244.6,35.6L245.6,35.6L245.6,36.8L244.6,36.8ZM244.6,39.2L244.6,38L245.6,38L245.6,39.2L244.6,39.2ZM244.6,41.6L244.6,40.4L245.6,40.4L245.6,41.6L244.6,41.6ZM244.6,44L244.6,42.8L245.6,42.8L245.6,44L244.6,44ZM244.6,46.4L244.6,45.2L245.6,45.2L245.6,46.4L244.6,46.4ZM244.6,48.8L244.6,47.6L245.6,47.6L245.6,48.8L244.6,48.8ZM244.6,51.2L244.6,50L245.6,50L245.6,51.2L244.6,51.2ZM244.6,53.6L244.6,52.4L245.6,52.4L245.6,53.6L244.6,53.6ZM244.6,56L244.6,54.8L245.6,54.8L245.6,56L244.6,56ZM244.6,58.4L244.6,57.2L245.6,57.2L245.6,58.4L244.6,58.4ZM244.6,60.8L244.6,59.6L245.6,59.6L245.6,60.8L244.6,60.8ZM244.6,63.2L244.6,62L245.6,62L245.6,63.2L244.6,63.2ZM244.6,65.6L244.6,64.4L245.6,64.4L245.6,65.6L244.6,65.6ZM244.6,68L244.6,66.8L245.6,66.8L245.6,68L244.6,68ZM244.6,70.4L244.6,69.2L245.6,69.2L245.6,70.4L244.6,70.4ZM244.6,72.8L244.6,71.6L245.6,71.6L245.6,72.8L244.6,72.8ZM244.6,75.2L244.6,74L245.6,74L245.6,75.2L244.6,75.2ZM244.6,77.6L244.6,76.4L245.6,76.4L245.6,77.6L244.6,77.6ZM244.6,80L244.6,78.8L245.6,78.8L245.6,80L244.6,80ZM244.6,82.4L244.6,81.2L245.6,81.2L245.6,82.4L244.6,82.4ZM244.6,84.8L244.6,83.6L245.6,83.6L245.6,84.8L244.6,84.8ZM244.6,87.2L244.6,86L245.6,86L245.6,87.2L244.6,87.2ZM244.6,89.6L244.6,88.4L245.6,88.4L245.6,89.6L244.6,89.6ZM244.6,92L244.6,90.8L245.6,90.8L245.6,92L244.6,92ZM244.6,94.4L244.6,93.2L245.6,93.2L245.6,94.4L244.6,94.4ZM244.6,96.8L244.6,95.6L245.6,95.6L245.6,96.8L244.6,96.8ZM244.6,99.2L244.6,98L245.6,98L245.6,99.2L244.6,99.2ZM244.6,101.6L244.6,100.4L245.6,100.4L245.6,101.6L244.6,101.6ZM244.6,104L244.6,102.8L245.6,102.8L245.6,104L244.6,104ZM244.6,106.4L244.6,105.2L245.6,105.2L245.6,106.4L244.6,106.4ZM244.6,108.8L244.6,107.6L245.6,107.6L245.6,108.8L244.6,108.8ZM244.6,111.2L244.6,110L245.6,110L245.6,111.2L244.6,111.2ZM244.6,113.6L244.6,112.4L245.6,112.4L245.6,113.6L244.6,113.6ZM244.6,116L244.6,114.8L245.6,114.8L245.6,116L244.6,116ZM244.6,118.4L244.6,117.2L245.6,117.2L245.6,118.4L244.6,118.4ZM244.6,120.8L244.6,119.6L245.6,119.6L245.6,120.8L244.6,120.8ZM244.6,123.2L244.6,122L245.6,122L245.6,123.2L244.6,123.2ZM244.6,125.6L244.6,124.4L245.6,124.4L245.6,125.6L244.6,125.6ZM244.6,128L244.6,126.8L245.6,126.8L245.6,128L244.6,128ZM244.6,130.4L244.6,129.2L245.6,129.2L245.6,130.4L244.6,130.4ZM244.6,132.8L244.6,131.6L245.6,131.6L245.6,132.8L244.6,132.8ZM244.6,135.2L244.6,134L245.6,134L245.6,135.2L244.6,135.2ZM244.6,137.6L244.6,136.4L245.6,136.4L245.6,137.6L244.6,137.6ZM244.6,140L244.6,138.8L245.6,138.8L245.6,140L244.6,140ZM244.6,142.4L244.6,141.2L245.6,141.2L245.6,142.4L244.6,142.4ZM244.6,144.8L244.6,143.6L245.6,143.6L245.6,144.8L244.6,144.8ZM244.6,147.2L244.6,146L245.6,146L245.6,147.2L244.6,147.2ZM244.6,149.6L244.6,148.4L245.6,148.4L245.6,149.6L244.6,149.6ZM244.6,152L244.6,150.8L245.6,150.8L245.6,152L244.6,152ZM244.6,154.4L244.6,153.2L245.6,153.2L245.6,154.4L244.6,154.4ZM244.6,156.8L244.6,155.6L245.6,155.6L245.6,156.8L244.6,156.8ZM244.6,159.2L244.6,158L245.6,158L245.6,159.2L244.6,159.2ZM244.6,161.6L244.6,160.4L245.6,160.4L245.6,161.6L244.6,161.6ZM244.6,164L244.6,162.8L245.6,162.8L245.6,164L244.6,164ZM244.6,166.4L244.6,165.2L245.6,165.2L245.6,166.4L244.6,166.4ZM244.6,168.8L244.6,167.6L245.6,167.6L245.6,168.8L244.6,168.8ZM244.6,171.2L244.6,170L245.6,170L245.6,171.2L244.6,171.2ZM244.6,173.6L244.6,172.4L245.6,172.4L245.6,173.6L244.6,173.6ZM244.6,176L244.6,174.8L245.6,174.8L245.6,176L244.6,176ZM244.6,178.4L244.6,177.2L245.6,177.2L245.6,178.4L244.6,178.4ZM244.6,180.8L244.6,179.6L245.6,179.6L245.6,180.8L244.6,180.8ZM244.6,183.2L244.6,182L245.6,182L245.6,183.2L244.6,183.2ZM244.6,185.6L244.6,184.4L245.6,184.4L245.6,185.6L244.6,185.6ZM244.6,188L244.6,186.8L245.6,186.8L245.6,188L244.6,188ZM244.6,189.8L244.6,189.2L245.6,189.2L245.6,189.8L244.6,189.8Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="244.1"
            android:startY="-11.8"
            android:endX="244.9"
            android:endY="149.93"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.13" android:color="#FF36393B"/>
          <item android:offset="0.59" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M82,34.98L82,34L83,34L83,34.98L82,34.98ZM82,38.91L82,36.95L83,36.95L83,38.91L82,38.91ZM82,42.84L82,40.88L83,40.88L83,42.84L82,42.84ZM82,46.78L82,44.81L83,44.81L83,46.78L82,46.78ZM82,50.71L82,48.74L83,48.74L83,50.71L82,50.71ZM82,54.64L82,52.67L83,52.67L83,54.64L82,54.64ZM82,58.57L82,56.6L83,56.6L83,58.57L82,58.57ZM82,62.5L82,60.53L83,60.53L83,62.5L82,62.5ZM82,66.43L82,64.47L83,64.47L83,66.43L82,66.43ZM82,70.36L82,68.4L83,68.4L83,70.36L82,70.36ZM82,74.29L82,72.33L83,72.33L83,74.29L82,74.29ZM82,78.22L82,76.26L83,76.26L83,78.22L82,78.22ZM82,82.16L82,80.19L83,80.19L83,82.16L82,82.16ZM82,86.09L82,84.12L83,84.12L83,86.09L82,86.09ZM82,90.02L82,88.05L83,88.05L83,90.02L82,90.02ZM82,93.95L82,91.98L83,91.98L83,93.95L82,93.95ZM82,97.88L82,95.91L83,95.91L83,97.88L82,97.88ZM82,101.81L82,99.84L83,99.84L83,101.81L82,101.81ZM82,105.74L82,103.78L83,103.78L83,105.74L82,105.74ZM82,109.67L82,107.71L83,107.71L83,109.67L82,109.67ZM82,113.6L82,111.64L83,111.64L83,113.6L82,113.6ZM82,117.53L82,115.57L83,115.57L83,117.53L82,117.53ZM82,121.47L82,119.5L83,119.5L83,121.47L82,121.47ZM82,125.4L82,123.43L83,123.43L83,125.4L82,125.4ZM82,129.33L82,127.36L83,127.36L83,129.33L82,129.33ZM82,133.26L82,131.29L83,131.29L83,133.26L82,133.26ZM82,137.19L82,135.22L83,135.22L83,137.19L82,137.19ZM82,141.12L82,139.15L83,139.15L83,141.12L82,141.12ZM82,145.05L82,143.09L83,143.09L83,145.05L82,145.05ZM82,148L82,147.02L83,147.02L83,148L82,148Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="81.5"
            android:startY="34"
            android:endX="81.64"
            android:endY="159.34"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.27" android:color="#FF36393B"/>
          <item android:offset="0.59" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M244.6,107.6L244.6,26.6L245.4,26.6L245.4,107.6L244.6,107.6Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M216.66,57.8L236.8,96.2H196.6L216.66,57.8Z"
        android:fillColor="#2A2C2D"/>
    <path
        android:pathData="M218.58,55.63L210.84,70.51L209.96,70.05L217.7,55.17L218.58,55.63Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="218.58"
            android:startY="55.63"
            android:endX="210.84"
            android:endY="70.51"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.16" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M215.39,54.27L238.91,99.04L238.02,99.5L214.51,54.73L215.39,54.27Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="215.39"
            android:startY="54.27"
            android:endX="238.91"
            android:endY="99.04"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.07" android:color="#FF36393B"/>
          <item android:offset="0.92" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M196.6,43.7L223.9,95.9H169.41L196.6,43.7Z"
        android:fillColor="#2E3031"/>
    <path
        android:pathData="M241,96.2L164.8,96.2L164.8,95.2L241,95.2L241,96.2Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="164.8"
            android:startY="96.7"
            android:endX="241"
            android:endY="96.7"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.06" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M195.44,39.64L226.25,99.04L225.36,99.5L194.55,40.1L195.44,39.64Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="195.44"
            android:startY="39.64"
            android:endX="226.25"
            android:endY="99.04"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M197.6,40.17L166.79,99.57L167.68,100.03L198.49,40.63L197.6,40.17Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="197.6"
            android:startY="40.17"
            android:endX="166.79"
            android:endY="99.57"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M224.8,52.8C228.67,52.8 231.8,49.67 231.8,45.8C231.8,41.94 228.67,38.8 224.8,38.8C220.93,38.8 217.8,41.94 217.8,45.8C217.8,49.67 220.93,52.8 224.8,52.8ZM224.8,53.6C229.11,53.6 232.6,50.11 232.6,45.8C232.6,41.49 229.11,38 224.8,38C220.49,38 217,41.49 217,45.8C217,50.11 220.49,53.6 224.8,53.6Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M164.8,26.9C164.8,27.73 164.13,28.4 163.3,28.4C162.47,28.4 161.8,27.73 161.8,26.9C161.8,26.07 162.47,25.4 163.3,25.4C164.13,25.4 164.8,26.07 164.8,26.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M163.3,27.8C163.8,27.8 164.2,27.4 164.2,26.9C164.2,26.4 163.8,26 163.3,26C162.8,26 162.4,26.4 162.4,26.9C162.4,27.4 162.8,27.8 163.3,27.8ZM163.3,28.4C164.13,28.4 164.8,27.73 164.8,26.9C164.8,26.07 164.13,25.4 163.3,25.4C162.47,25.4 161.8,26.07 161.8,26.9C161.8,27.73 162.47,28.4 163.3,28.4Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M164.8,66.5C164.8,67.33 164.13,68 163.3,68C162.47,68 161.8,67.33 161.8,66.5C161.8,65.67 162.47,65 163.3,65C164.13,65 164.8,65.67 164.8,66.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M163.3,67.4C163.8,67.4 164.2,67 164.2,66.5C164.2,66 163.8,65.6 163.3,65.6C162.8,65.6 162.4,66 162.4,66.5C162.4,67 162.8,67.4 163.3,67.4ZM163.3,68C164.13,68 164.8,67.33 164.8,66.5C164.8,65.67 164.13,65 163.3,65C162.47,65 161.8,65.67 161.8,66.5C161.8,67.33 162.47,68 163.3,68Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M164.8,107.9C164.8,108.73 164.13,109.4 163.3,109.4C162.47,109.4 161.8,108.73 161.8,107.9C161.8,107.07 162.47,106.4 163.3,106.4C164.13,106.4 164.8,107.07 164.8,107.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M163.3,108.8C163.8,108.8 164.2,108.4 164.2,107.9C164.2,107.4 163.8,107 163.3,107C162.8,107 162.4,107.4 162.4,107.9C162.4,108.4 162.8,108.8 163.3,108.8ZM163.3,109.4C164.13,109.4 164.8,108.73 164.8,107.9C164.8,107.07 164.13,106.4 163.3,106.4C162.47,106.4 161.8,107.07 161.8,107.9C161.8,108.73 162.47,109.4 163.3,109.4Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M205.6,107.9C205.6,108.73 204.93,109.4 204.1,109.4C203.27,109.4 202.6,108.73 202.6,107.9C202.6,107.07 203.27,106.4 204.1,106.4C204.93,106.4 205.6,107.07 205.6,107.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M204.1,108.8C204.6,108.8 205,108.4 205,107.9C205,107.4 204.6,107 204.1,107C203.6,107 203.2,107.4 203.2,107.9C203.2,108.4 203.6,108.8 204.1,108.8ZM204.1,109.4C204.93,109.4 205.6,108.73 205.6,107.9C205.6,107.07 204.93,106.4 204.1,106.4C203.27,106.4 202.6,107.07 202.6,107.9C202.6,108.73 203.27,109.4 204.1,109.4Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M205.6,26.9C205.6,27.73 204.93,28.4 204.1,28.4C203.27,28.4 202.6,27.73 202.6,26.9C202.6,26.07 203.27,25.4 204.1,25.4C204.93,25.4 205.6,26.07 205.6,26.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M204.1,27.8C204.6,27.8 205,27.4 205,26.9C205,26.4 204.6,26 204.1,26C203.6,26 203.2,26.4 203.2,26.9C203.2,27.4 203.6,27.8 204.1,27.8ZM204.1,28.4C204.93,28.4 205.6,27.73 205.6,26.9C205.6,26.07 204.93,25.4 204.1,25.4C203.27,25.4 202.6,26.07 202.6,26.9C202.6,27.73 203.27,28.4 204.1,28.4Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M246.4,26.9C246.4,27.73 245.73,28.4 244.9,28.4C244.07,28.4 243.4,27.73 243.4,26.9C243.4,26.07 244.07,25.4 244.9,25.4C245.73,25.4 246.4,26.07 246.4,26.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M244.9,27.8C245.4,27.8 245.8,27.4 245.8,26.9C245.8,26.4 245.4,26 244.9,26C244.4,26 244,26.4 244,26.9C244,27.4 244.4,27.8 244.9,27.8ZM244.9,28.4C245.73,28.4 246.4,27.73 246.4,26.9C246.4,26.07 245.73,25.4 244.9,25.4C244.07,25.4 243.4,26.07 243.4,26.9C243.4,27.73 244.07,28.4 244.9,28.4Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M246.4,66.5C246.4,67.33 245.73,68 244.9,68C244.07,68 243.4,67.33 243.4,66.5C243.4,65.67 244.07,65 244.9,65C245.73,65 246.4,65.67 246.4,66.5Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M244.9,67.4C245.4,67.4 245.8,67 245.8,66.5C245.8,66 245.4,65.6 244.9,65.6C244.4,65.6 244,66 244,66.5C244,67 244.4,67.4 244.9,67.4ZM244.9,68C245.73,68 246.4,67.33 246.4,66.5C246.4,65.67 245.73,65 244.9,65C244.07,65 243.4,65.67 243.4,66.5C243.4,67.33 244.07,68 244.9,68Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M246.4,107.9C246.4,108.73 245.73,109.4 244.9,109.4C244.07,109.4 243.4,108.73 243.4,107.9C243.4,107.07 244.07,106.4 244.9,106.4C245.73,106.4 246.4,107.07 246.4,107.9Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M244.9,108.8C245.4,108.8 245.8,108.4 245.8,107.9C245.8,107.4 245.4,107 244.9,107C244.4,107 244,107.4 244,107.9C244,108.4 244.4,108.8 244.9,108.8ZM244.9,109.4C245.73,109.4 246.4,108.73 246.4,107.9C246.4,107.07 245.73,106.4 244.9,106.4C244.07,106.4 243.4,107.07 243.4,107.9C243.4,108.73 244.07,109.4 244.9,109.4Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
  </group>
</vector>
