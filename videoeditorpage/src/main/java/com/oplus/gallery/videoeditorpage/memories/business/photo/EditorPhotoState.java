/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorPhotoState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.photo;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesManager;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.videoeditorpage.memories.data.MemoriesInfo;
import com.oplus.gallery.videoeditorpage.resource.room.bean.PhotoItem;

import java.util.ArrayList;
import java.util.List;

public class EditorPhotoState extends EditorBaseState implements EditorBaseUIController.OnIconClickListener {
    private static final String TAG = "EditorPhotoState";
    private static final String TYPE_NAME = "Photo";
    private final long mEnterBeforeTotalTime;

    private MediaInfo mVideoCover;
    private MemoriesInfo mCurMemInfo = null;

    public EditorPhotoState(Context context, ControlBarView controlBarView) {
        super(TYPE_NAME, context, controlBarView);
        mCurMemInfo = MemoriesManager.getCurMemoriesInfo().obtain();
        mEnterBeforeTotalTime = mEngineManager.getTotalTime();
        changeState(true, false);
    }

    private void changeState(boolean enter, boolean isCancel) {
        mEngineManager.stop();
        if (enter) {
            mVideoCover = mEngineManager.getThemeVideoCover();
            mEngineManager.removeThemeVideoCover();
            mEngineManager.removeThemeCaption();
            mEngineManager.cleanBuiltinTransition();
        } else {
            mEngineManager.addThemeVideoCover(mVideoCover);
            mEngineManager.reAddThemeCaption();
            if (isCancel) {
                mEngineManager.changeThemeDuration(mEnterBeforeTotalTime);
            }
        }
        mEngineManager.reset();
    }

    @Override
    protected EditorBaseUIController createUIController() {
        EditorPhotoUIController photoUIController = new EditorPhotoUIController(mContext, mControlBarView, this);
        photoUIController.setOnIconClickListener(this);
        return photoUIController;
    }

    @Override
    public boolean done() {
        List<MediaInfo> list = MemoriesManager.getCurrentVideoFileList();
        GLog.d(TAG, "done list = " + list);
        if (mCurMemInfo != null) {
            MemoriesManager.setCurMemoriesInfo(mCurMemInfo);
            List<MediaInfo> curList = MemoriesManager.getCurrentVideoFileList();
            GLog.d(TAG, "done mCurMemInfo = " + mCurMemInfo
                    + ", curList.size = " + curList.size()
                    + ", curList = " + curList);
            mVideoCover = mCurMemInfo.mCover;
        }
        changeState(false, false);
        return super.done();
    }

    @Override
    public void cancel() {
        super.cancel();
        ArrayList<String> curVideoList = mEngineManager.getThemeVideoClipList();
        List<MediaInfo> curMediaList = MemoriesManager.getCurrentVideoFileList();
        // check removed list, reAdd it
        ArrayList<MediaInfo> reAddList = new ArrayList<>();
        if ((curVideoList != null) && !curVideoList.isEmpty()
                && (curMediaList != null) && !curMediaList.isEmpty()) {
            GLog.d(TAG, "cancel reAdd curVideoList.size = " + curVideoList.size() + ", curMediaList.size = " + curMediaList.size());
            for (MediaInfo info : curMediaList) {
                if (!curVideoList.contains(info.mUri)) {
                    GLog.d(TAG, "cancel insertThemeVideoClip info = " + info);
                    reAddList.add(info);
                }
            }
        }
        if (!reAddList.isEmpty()) {
            mEngineManager.insertThemeVideoClip(reAddList);
            GLog.d(TAG, "cancel insertThemeVideoClip reAddList.size = " + reAddList.size());
        }
        // check added list, remove it
        if ((curVideoList != null) && !curVideoList.isEmpty()
                && (curMediaList != null) && !curMediaList.isEmpty()) {
            GLog.d(TAG, "cancel remove curVideoList.size = " + curVideoList.size() + ", curMediaList.size = " + curMediaList.size());
            for (String stringUri : curVideoList) {
                MediaInfo info = MemoriesManager.getMediaInfoFromCurrent(stringUri);
                if (info == null) {
                    MediaInfo mediaInfo = getMediaInfoFromList(MemoriesManager.getCurMemoriesInfo(), stringUri);
                    GLog.d(TAG, "cancel deleteThemeVideoClip mediaInfo = " + mediaInfo);
                    mEngineManager.deleteThemeVideoClip(mediaInfo);
                }
            }
        }
        // reset cover or not
        MediaInfo oldCover = MemoriesManager.getCurrentCoverInfo();
        GLog.d(TAG, "cancel mVideoCover = " + mVideoCover + ", oldCover = " + oldCover);
        mVideoCover = oldCover;
        MemoriesManager.getCurrentVideoFileList();
        getUIController().onDataSetChanged();
        changeState(false, true);
    }

    @Override
    public boolean onBackPressed() {
        cancel();
        return super.onBackPressed();
    }

    @Override
    public void click(View view) {
        GLog.d(TAG, "click id = " + view.getId());
        int position = getCurrentSelection();
        ArrayList<String> curVideoList = mEngineManager.getThemeVideoClipList();
        if ((curVideoList == null) || curVideoList.isEmpty()) {
            GLog.d(TAG, "click: curVideoList is isEmpty");
            return;
        }
        if ((position < 0) || (position >= curVideoList.size())) {
            GLog.d(TAG, "click: position = " + position + " is invalid");
            return;
        }
        // add photo
        int id = view.getId();
        if (id == R.id.editor_img_action_left) {
            GLog.d(TAG, "click add pos = " + position);
            if (mContext instanceof FragmentActivity) {
                List<MediaInfo> curMediaInfoList = convertVideoPathToMediaInfo(mCurMemInfo, curVideoList);
                MemoriesManager.takePhoto((FragmentActivity) mContext, this, curMediaInfoList);
            }
            // delete photo
        } else if (id == R.id.editor_img_action_right) {
            GLog.d(TAG, "click delete pos = " + position + ", videoClipCount = " + mEngineManager.getThemeVideoClipCount());
            if (mEngineManager.getThemeVideoClipCount() <= MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MIN) {
                String toast = mContext.getResources().getString(R.string.videoeditor_memories_toast_cannot_less_than_photo,
                        MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MIN);
                ToastUtil.showShortToast(toast);
                GLog.d(TAG, "click delete toast = " + toast);
                return;
            }

            if ((curVideoList != null) && (curVideoList.size() > position)) {
                MediaInfo info = getMediaInfoFromList(mCurMemInfo, curVideoList.get(position));
                boolean success = mEngineManager.deleteThemeVideoClip(info);
                if (success) {
                    mEngineManager.cleanBuiltinTransition();
                    info.mInVideo = false;
                    GLog.d(TAG, "click delete, info = " + info);
                    if (info.mIsCover) {
                        GLog.d(TAG, "click delete, remove cover info, info = " + info);
                        info.mIsCover = false;
                        curVideoList.remove(info.mUri);
                        // get mediaInfo list match current video list
                        List<MediaInfo> curMediaList = getCurrentMediaList(mCurMemInfo, curVideoList);
                        // get next cover
                        MediaInfo newCover = MemoriesInfo.getNextCover(curMediaList);
                        if (newCover != null) {
                            GLog.d(TAG, "click oldCover = " + info + ", newCover = " + newCover);
                            mCurMemInfo.mCover = newCover;
                        }
                    }
                    BaseRecyclerAdapter adapter = getUIController().getAdapter();
                    if (adapter != null) {
                        adapter.setData(getThemeVideoClipItemList());
                        int nextPos = Math.max(0, position - 1);
                        GLog.d(TAG, "click delete, position = " + position + ", nextPos = " + nextPos);
                        adapter.select(nextPos);
                        mEngineManager.seekToThemePosition(nextPos);
                    }
                }
            }
        }
    }

    @Override
    public void onIconClick(View view, final int position, Object item) {
        GLog.d(TAG, "onIconClick pos = " + position + ", item = " + item);
        if (mEngineManager != null) {
            mEngineManager.seekToThemePosition(position);
        }
    }

    public void updateTakePhoto(List<String> uriList) {
        ArrayList<String> curVideoUriList = mEngineManager.getThemeVideoClipList();
        if ((curVideoUriList == null) || curVideoUriList.isEmpty()) {
            GLog.w(TAG, "updateTakePhoto curVideoList is null or empty. curVideoList = " + curVideoUriList);
            return;
        }
        GLog.d(TAG, "updateTakePhoto curVideoList.size = " + curVideoUriList.size());
        // remove clip from video
        removeClipFromVideo(uriList, curVideoUriList);
        curVideoUriList = mEngineManager.getThemeVideoClipList();
        if ((curVideoUriList == null) || curVideoUriList.isEmpty()) {
            GLog.w(TAG, "updateTakePhoto curVideoList is null or empty. curVideoList = " + curVideoUriList);
            return;
        }
        // add clip to video
        ArrayList<MediaInfo> addMediaList = addClipToVideo(uriList, curVideoUriList);
        GLog.d(TAG, "updateTakePhoto addMediaList.size = " + addMediaList.size());
        if (!addMediaList.isEmpty()) {
            boolean result = mEngineManager.insertThemeVideoClip(addMediaList);
            GLog.d(TAG, "updateTakePhoto insertThemeVideoClip result = " + result);
        }
        mEngineManager.cleanBuiltinTransition();

        curVideoUriList = mEngineManager.getThemeVideoClipList();
        // get mediaInfo list match current video list
        List<MediaInfo> curMediaList = getCurrentMediaList(mCurMemInfo, curVideoUriList);
        // reselect another cover or not
        GLog.d(TAG, "updateTakePhoto mVideoCover = " + mVideoCover);
        if ((mCurMemInfo != null) && (mCurMemInfo.mCover != null)
                && (curVideoUriList != null) && !curVideoUriList.contains(mCurMemInfo.mCover.mUri)) {
            // get next cover
            MediaInfo newCover = MemoriesInfo.getNextCover(curMediaList);
            if (newCover != null) {
                GLog.d(TAG, "updateTakePhoto oldCover = " + mCurMemInfo.mCover + ", newCover = " + newCover);
                mCurMemInfo.mCover = newCover;
            }
        }
        BaseRecyclerAdapter adapter = getUIController().getAdapter();
        if (adapter != null) {
            adapter.setData(getThemeVideoClipItemList());
            int nextPos = Math.min((adapter.getItemCount() - 1), adapter.getSelectedPosition());
            GLog.d(TAG, "updateTakePhoto, nextPos = " + nextPos);
            adapter.select(nextPos);
            mEngineManager.seekToThemePosition(nextPos);
        }
    }

    @NonNull
    private ArrayList<MediaInfo> addClipToVideo(List<String> uriList, List<String> curVideoUriList) {
        ArrayList<MediaInfo> addMediaList = new ArrayList<>();
        for (int i = 0; i < uriList.size(); i++) {
            String uri = uriList.get(i);
            if (!curVideoUriList.contains(uri)) {
                MediaInfo info = getMediaInfoFromList(mCurMemInfo, uri);
                if (info != null) {
                    info.mInVideo = true;
                    addMediaList.add(info);
                    GLog.d(TAG, "addClipToVideo insertThemeVideoClip info = " + info);
                }
            }
        }
        return addMediaList;
    }

    private void removeClipFromVideo(List<String> uriList, List<String> curVideoUriList) {
        for (String uri : curVideoUriList) {
            if (!uriList.contains(uri)) {
                MediaInfo info = getMediaInfoFromList(mCurMemInfo, uri);
                if (info != null) {
                    boolean result = mEngineManager.deleteThemeVideoClip(info);
                    if (result) {
                        info.mInVideo = false;
                        GLog.d(TAG, "removeClipFromVideo deleteThemeVideoClip info = " + info);
                        if (info.mIsCover) {
                            GLog.d(TAG, "removeClipFromVideo remove cover info, info = " + info);
                            info.mIsCover = false;
                        }
                    }
                }
            }
        }
    }

    private MediaInfo getMediaInfoFromList(MemoriesInfo memoriesInfo, String filePath) {
        if ((memoriesInfo != null) && !memoriesInfo.mMediaInfos.isEmpty()) {
            for (MediaInfo info : memoriesInfo.mMediaInfos) {
                if (TextUtils.equals(filePath, info.mPath) || TextUtils.equals(filePath, info.mUri)) {
                    return info;
                }
            }
        }
        return null;
    }

    private List<MediaInfo> convertVideoPathToMediaInfo(MemoriesInfo memoriesInfo, List<String> videoPathList) {
        if ((memoriesInfo == null)
                || memoriesInfo.mMediaInfos.isEmpty()
                || (videoPathList == null)
                || videoPathList.isEmpty()) {
            return null;
        }
        ArrayList<MediaInfo> curMediaList = new ArrayList<>();
        for (String filePath : videoPathList) {
            MediaInfo info = getMediaInfoFromList(memoriesInfo, filePath);
            if (info != null) {
                curMediaList.add(info);
            }
        }
        return curMediaList;
    }

    private List<MediaInfo> getCurrentMediaList(MemoriesInfo memoriesInfo, ArrayList<String> curFileList) {
        ArrayList<MediaInfo> curMediaList = new ArrayList<>();
        if ((curFileList != null) && !curFileList.isEmpty()
                && (memoriesInfo != null) && !memoriesInfo.mMediaInfos.isEmpty()) {
            List<MediaInfo> allMediaList = mCurMemInfo.mMediaInfos;
            GLog.d(TAG, "getCurrentMediaList curList.size = " + curFileList.size() + ", allList.size = " + allMediaList.size());
            for (MediaInfo info : allMediaList) {
                if (curFileList.contains(info.mUri)) {
                    info.mInVideo = true;
                    curMediaList.add(info);
                } else {
                    info.mInVideo = false;
                    info.mIsCover = false;
                }
            }

        }
        GLog.d(TAG, "getCurrentMediaList size = " + curMediaList.size());
        return curMediaList;
    }

    public void updateAdapterPhoto() {
        ArrayList<String> curVideoList = mEngineManager.getThemeVideoClipList();
        if ((curVideoList == null) || curVideoList.isEmpty()) {
            GLog.w(TAG, "updateAdapterPhoto curVideoList is null or empty. curVideoList = " + curVideoList);
            return;
        }
        GLog.d(TAG, "updateAdapterPhoto curVideoList.size = " + curVideoList.size());
        mEngineManager.cleanBuiltinTransition();

        BaseRecyclerAdapter adapter = getUIController().getAdapter();
        if (adapter != null) {
            adapter.setData(getThemeVideoClipItemList());
            int nextPos = Math.max(0, adapter.getSelectedPosition());
            GLog.d(TAG, "updateAdapterPhoto, nextPos = " + nextPos);
            adapter.select(nextPos);
            mEngineManager.seekToThemePosition(nextPos);
        }
    }

    private ArrayList<PhotoItem> getThemeVideoClipItemList() {
        ArrayList<PhotoItem> data = new ArrayList<>();
        ArrayList<String> curVideoList = mEngineManager.getThemeVideoClipList();
        if ((curVideoList != null) && !curVideoList.isEmpty()) {
            int size = curVideoList.size();
            String filePath = TextUtil.EMPTY_STRING;
            for (int i = 0; i < size; i++) {
                filePath = curVideoList.get(i);
                if (!TextUtils.isEmpty(filePath)) {
                    data.add(new PhotoItem(-1, true, true, false, filePath));
                }
            }
        }
        return data;
    }
}
