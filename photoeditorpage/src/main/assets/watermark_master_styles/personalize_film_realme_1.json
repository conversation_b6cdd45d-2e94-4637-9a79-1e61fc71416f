{"styleId": "personalize_film_realme_1", "baseImageSize": 360, "size": {"leftMargin": 0, "topMargin": 0, "rightMargin": 0, "bottomMargin": 60}, "imageOffset": {"startX": 0, "startY": 0}, "background": {"backgroundType": 0, "color": "#FFFFFF"}, "bitmaps": [{"direction": 3, "orientation": 0, "position": {"borderX": 0, "borderY": 0}, "width": -1, "height": 60, "elements": [{"id": 1, "visible": true, "content": {"type": "elements", "orientation": 1, "width": 230}, "position": {"layoutGravity": "leftVerticalCenter", "layoutGravityEnable": true, "leftMargin": 20}, "elements": [{"id": 2, "visible": true, "editable": false, "content": {"type": "text", "textSource": 0}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "textSize": 12, "fontWeight": 600, "letterSpacing": 0.04, "lineHeight": 14.06, "alpha": 0.9, "lightAlpha": 0.9, "colors": ["#000000"], "gradientType": -1}, "position": {"layoutGravity": "leftTop", "layoutGravityEnable": true, "leftMargin": 0, "topMargin": 0}}, {"id": 3, "visible": true, "editable": true, "content": {"type": "text", "textSource": 1}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "textSize": 7, "fontWeight": 400, "alpha": 0.54, "lightAlpha": 0.54, "letterSpacing": 0.08, "lineHeight": 8.2, "colors": ["#000000"], "gradientType": -1}, "position": {"layoutGravity": "leftTop", "layoutGravityEnable": true, "leftMargin": 0, "topMargin": 2.81}}]}, {"id": 4, "visible": true, "editable": false, "spaceUse": "occupy", "content": {"type": "space"}, "layoutWeight": 1}, {"id": 5, "visible": true, "editable": true, "content": {"type": "image", "bitmapSourceType": 0, "bitmap": "realme_film_street_sweeping", "bitmapResName": "realme_film_street_sweeping", "width": 74, "height": 32, "scaleType": 5}, "position": {"layoutGravity": "rightVerticalCenter", "layoutGravityEnable": true, "rightMargin": 16}}]}]}