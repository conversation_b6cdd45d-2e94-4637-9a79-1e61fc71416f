/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : BaseAdapter.kt
 ** Description : 文字输入弹窗
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.annotation.IdRes
import androidx.annotation.LayoutRes
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.BaseAdapter.BaseVH
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.BaseItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * 字幕使用的recyclerview的基类adapter
 */
abstract class BaseAdapter<DataBean, VH : BaseVH<DataBean>>(
    private val recyclerView: RecyclerView,
    private var createVHCallback: CreateVHCallback<BaseVH<DataBean>>
) : RecyclerView.Adapter<BaseVH<DataBean>>(), View.OnClickListener {

    val data: List<DataBean>
        get() = _dataList

    /**
     * 被选中item的位置
     */
    var selectedPosition: Int
        get() = itemSelectedPosition
        set(selectedPosition) {
            if (multipleSelectable) {
                // 多选控件逻辑
                multipleSelectedNotifyItemChanged(selectedPosition)
            } else {
                // 单选控件逻辑
                this.notifyItemChangedToPosition(selectedPosition)
            }
        }

    /**
     * item数据列表
     */
    private val _dataList: MutableList<DataBean> =  mutableListOf()

    /**
     * 数据变化差异计算回调
     */
    var diffCallback: DiffCallback<DataBean>? = null

    /**
     * item被点击的回调
     */
    var onItemClickCallback: OnItemClickCallback<DataBean>? = null

    /**
     * 设置是否支持多选，单选：false， 多选为true
     */
    abstract val multipleSelectable: Boolean

    /**
     * 当前选中的item位置
     */
    private var itemSelectedPosition = -1

    /**
     * 设置显示item的数据源
     */
    fun setDataSource(dataSource: List<DataBean>) {
        diffCallback?.let {
            it.oldList.clear()
            it.oldList.addAll(_dataList)
            it.newList.clear()
            it.newList.addAll(dataSource)
            DiffUtil.calculateDiff(it).dispatchUpdatesTo(this)
            _dataList.clear()
            _dataList.addAll(dataSource)
        } ?: {
            _dataList.clear()
            _dataList.addAll(dataSource)
            notifyDataSetChanged()
        }
    }

    /**
     * RecyclerView.Adapter 抽象方法实现，创建viewHolder
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH<DataBean> {
        return createVHCallback.invoke(parent, viewType)
    }

    /**
     * RecyclerView.Adapter 抽象方法实现，数据绑定viewHolder
     */
    override fun onBindViewHolder(holder: BaseVH<DataBean>, position: Int) {
        holder.bind(_dataList[position])
        (_dataList[position] as? BaseItem)?.let {
            if (it.selected) itemSelectedPosition = holder.bindingAdapterPosition
        }
        onItemClickCallback?.let {
            holder.itemView.setOnClickListener(this)
        }
    }

    /**
     * RecyclerView.Adapter 抽象方法实现，获取item的数量
     */
    override fun getItemCount(): Int {
        return _dataList.size
    }

    /**
     * View.OnClickListener接口函数实现
     *
     * @param v 响应点击事件的view
     */
    override fun onClick(v: View) {
        val position = recyclerView.getChildAdapterPosition(v)
        selectedPosition = position
        val data: DataBean? = getDataByPosition(selectedPosition)
        onItemClickCallback?.invoke(v, selectedPosition, data)
    }

    /**
     * 设置view回收后清楚动画防止回收后动画还在执行导致闪退
     */
    override fun onViewRecycled(holder: BaseVH<DataBean>) {
        holder.itemView.clearAnimation()
        super.onViewRecycled(holder)
    }

    /**
     * 获取某个位置item的元数据
     *
     * @param position item的位置
     */
    private fun getDataByPosition(position: Int): DataBean? {
        if (position in 0 until _dataList.size) {
            return _dataList[position]
        }
        return null
    }

    /**
     * 通知改变选中的item位置
     *
     * @param checkedPosition 被选中的item位置
     */
    private fun notifyItemChangedToPosition(checkedPosition: Int) {
        // 将旧位置的item置为未选中
        if (itemSelectedPosition in 0 until _dataList.size) {
            val dataBean = _dataList[itemSelectedPosition]
            (dataBean as? BaseItem)?.selected = false
            notifyItemChanged(itemSelectedPosition)
        }
        // 将新位置的item置为选中
        if (checkedPosition in 0 until _dataList.size) {
            val dataBean = _dataList[checkedPosition]
            (dataBean as? BaseItem)?.selected = true
            notifyItemChanged(checkedPosition)
        }
        itemSelectedPosition = checkedPosition
        // 修改位置变化后自动将滑动到选中为止，主要用于初始化编辑框回显场景（非人为点击选中）
        recyclerView.post {
            recyclerView.smoothScrollToPosition(itemSelectedPosition)
        }
    }

    /**
     * 设置item多选
     */
    private fun multipleSelectedNotifyItemChanged(checkedPosition: Int) {
        if (checkedPosition in 0 until _dataList.size) {
            val dataBean = _dataList[checkedPosition]
            val baseItem = (dataBean as? BaseItem) ?: return
            baseItem.selected = !baseItem.selected
            notifyItemChanged(checkedPosition)
        }
        itemSelectedPosition = checkedPosition
    }

    /**
     * 移除所有的监听和回调
     */
    private fun removeListenersAndCallbacks() {
        diffCallback = null
        onItemClickCallback = null
    }

    /**
     * 销毁回收资源
     */
    fun destroy() {
        removeListenersAndCallbacks()
        _dataList.clear()
    }
    /**
     * viewHolder基类
     */
    abstract class BaseVH<DataBean>(parent: ViewGroup, @LayoutRes layoutId: Int) :
        RecyclerView.ViewHolder(LayoutInflater.from(parent.context).inflate(layoutId, parent, false)) {
        fun <T : View?> findViewById(@IdRes id: Int): T {
            return itemView.findViewById(id)
        }

        /**
         * data和view进行数据绑定，在子类中实现
         */
        abstract fun bind(t: DataBean)

        /**
         * imageView加载显示图片资源
         */
        protected fun loadImage(path: String, imageView: ImageView, placeHolderResId: Int) {
            runCatching {
                AppScope.launch(Dispatchers.IO) {
                    val builder = if (!path.startsWith(HTTP_PREFIX, true)) {
                        Glide.with(imageView.context).load(File(path))
                    } else {
                        Glide.with(imageView.context).load(path).diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    }
                    AppScope.launch(Dispatchers.Main) {
                        when (placeHolderResId) {
                            NO_PLACE_HOLDER -> builder.placeholder(placeHolderResId).into(imageView)
                            else -> builder.into(imageView)
                        }
                    }
                }
            }.onFailure { error ->
                GLog.e(TAG, LogFlag.DL) { "load image failed: $error" }
            }
        }
        companion object {

            private const val TAG = "BaseVH"
            /**
             * 不需要占位图显示标识
             */
            private const val NO_PLACE_HOLDER = -1

            /**
             * 本地assets资源路径前缀
             */
            private const val HTTP_PREFIX = "http"
        }
    }

    /**
     * 增量比对接口
     */
    abstract class DiffCallback<DataBean> : DiffUtil.Callback() {
        val oldList: MutableList<DataBean> = mutableListOf()
        val newList: MutableList<DataBean> = mutableListOf()

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }
    }
}

/**
 * 点击单个item的回调函数
 */
typealias OnItemClickCallback<DataBean> = (v: View?, position: Int, data: DataBean?) -> Unit

/**
 * 创建view holder的回调函数
 */
typealias CreateVHCallback<VH> = (parent: ViewGroup, viewType: Int) -> VH


