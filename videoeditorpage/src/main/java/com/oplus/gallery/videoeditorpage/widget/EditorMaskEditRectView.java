/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditorMaskEditRectView.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/8/1 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/8/1  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;

import androidx.annotation.Nullable;

import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.widget.base.IntercepableBaseView;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.helper.MaskHelper;

import java.util.List;

public class EditorMaskEditRectView extends IntercepableBaseView {
    private static final String TAG = "EditorMaskEditRectView";

    private final static int EVENT_NONE = 0;//无对应事件
    private final static int EVENT_DRAG = 1;//单指拖动
    private final static int EVENT_SCALE_HORIZONTAL = 2;//横向缩放
    private final static int EVENT_SCALE_VERTICAL = 3;//纵向缩放
    private final static int EVENT_ZOOM_CORNER = 4;//调节圆角
    private final static int EVENT_ZOOM_FEATHER = 5;//调节羽化值

    private final static float DIMENS_DP_FRAME_WIDTH = 1.5F;//边框宽度
    private final static float DIMENS_DP_RADIUS_CENTER_CIRCLE = 4F;//中心圆半径
    private final static float DIMENS_DP_SIZE_IMAGE = 18F;//按钮尺寸
    private final static float DIMENS_DP_MARGIN_IMAGE = 6.5F;//按钮边距

    private final static int ANGLE_180 = 180;//180度
    private final static int ANGLE_45 = 45;//45度

    private final static float FLOAT_270 = 270f;
    private final static float FLOAT_271 = 271f;
    private final static float FLOAT_282 = 282f;
    private final static float FLOAT_296 = 296f;

    private final static int INT_0 = 0;
    private final static int INT_1 = 1;
    private final static int INT_2 = 2;
    private final static int INT_3 = 3;

    private final static float FLOAT_0_DOT_5 = 0.5f;

    private final static float SQUARE_ROOT_2 = 1.414F;//2的平方根

    private final static float COEFFICIENT_2F = 2F;//系数

    private final int mTouchSlop;
    private final float mDensityDP2PX;
    private OnTouchEventListener mOnTouchEventListener;
    private Path mMaskRectPath = new Path();
    private Path mCenterRectPath = new Path();
    private Paint mRectPaint = new Paint();

    private MaskHelper.RectInfo mMaskRectInfo;
    private PointF mMaskCenter = new PointF();
    private PointF mSingleTouchedPoint = new PointF();
    private PointF[] mDoubleTouchedPoint = new PointF[2];
    private boolean mIsSingleFingerTouched;
    private boolean mIsTouchMoved;
    private int mTouchEventType;
    private Drawable mCornerZoomImage;
    private Drawable mFeatherZoomImage;
    private Drawable mVerticalScaleImage;
    private Drawable mHorizontalScaleImage;
    private int mMaskType;
    private float mMaskFeatherWidth;

    public EditorMaskEditRectView(Context context) {
        this(context, null);
    }

    public EditorMaskEditRectView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        //判断滑动事件的最小距离
        this.mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
        //dp转px的系数
        this.mDensityDP2PX = context.getResources().getDisplayMetrics().density;

        mRectPaint = new Paint();
        mRectPaint.setColor(getResources().getColor(R.color.color_F9BE01));
        mRectPaint.setAntiAlias(true);
        mRectPaint.setStrokeWidth(DIMENS_DP_FRAME_WIDTH * mDensityDP2PX);
        mRectPaint.setStyle(Paint.Style.STROKE);

        mCornerZoomImage = getResources().getDrawable(R.drawable.editor_mask_edit_zoom_corner);
        mFeatherZoomImage = getResources().getDrawable(R.drawable.editor_mask_edit_zoom_feather);
        mVerticalScaleImage = getResources().getDrawable(R.drawable.editor_mask_edit_scale_vertical);
        mHorizontalScaleImage = getResources().getDrawable(R.drawable.editor_mask_edit_scale_horizontal);
    }

    public void setOnEditTouchListener(OnTouchEventListener listener) {
        mOnTouchEventListener = listener;
    }

    public void setMaskType(int maskType) {
        this.mMaskType = maskType;
    }

    public void setMaskFeatherWidth(float featherWidth) {
        this.mMaskFeatherWidth = featherWidth;
    }

    public void buildLinearMask(MaskHelper.RectInfo rectInfo, List<PointF> pointList, int[] liveWindowLocation) {
        this.mMaskRectInfo = rectInfo;

        int[] maskRectViewLocation = new int[INT_2];
        getLocationOnScreen(maskRectViewLocation);

        int offsetX = liveWindowLocation[INT_0] - maskRectViewLocation[INT_0] - INT_1; // - 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失
        int offsetY = liveWindowLocation[INT_1] - maskRectViewLocation[INT_1] + INT_1; // + 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失

        mMaskCenter = new PointF(rectInfo.maskCenter.x + offsetX, rectInfo.maskCenter.y + offsetY);

        //中心圆
        mCenterRectPath.reset();
        mCenterRectPath.addCircle(mMaskCenter.x, mMaskCenter.y, DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, Path.Direction.CCW);

        //蒙版边框
        mMaskRectPath.reset();
        PointF point = null;

        point = pointList.get(INT_1); //左
        point.offset(offsetX, offsetY);
        mMaskRectPath.moveTo(point.x, point.y);

        point = pointList.get(INT_2); //右
        point.offset(offsetX, offsetY);
        mMaskRectPath.lineTo(point.x, point.y);

        mMaskRectPath.close();

        float imageSize = DIMENS_DP_SIZE_IMAGE * mDensityDP2PX;
        float margin = DIMENS_DP_MARGIN_IMAGE * mDensityDP2PX;
        float vOffset = DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX;
        float featherOffset = mMaskFeatherWidth / Math.max(mMaskRectInfo.videoWidth, mMaskRectInfo.videoHeight) * imageSize * COEFFICIENT_2F;
        mFeatherZoomImage.setBounds((int) -(imageSize / COEFFICIENT_2F),
                (int) (vOffset + margin + featherOffset), (int) (imageSize / COEFFICIENT_2F),
                (int) (imageSize + vOffset + margin + featherOffset));
    }

    public void buildMirrorMask(MaskHelper.RectInfo rectInfo, List<PointF> pointList, int[] liveWindowLocation) {
        this.mMaskRectInfo = rectInfo;

        int[] maskRectViewLocation = new int[INT_2];
        getLocationOnScreen(maskRectViewLocation);

        int offsetX = liveWindowLocation[INT_0] - maskRectViewLocation[INT_0] - INT_1; // - 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失
        int offsetY = liveWindowLocation[INT_1] - maskRectViewLocation[INT_1] + INT_1; // + 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失

        mMaskCenter = new PointF(rectInfo.maskCenter.x + offsetX, rectInfo.maskCenter.y + offsetY);

        //中心圆
        mCenterRectPath.reset();
        mCenterRectPath.addCircle(mMaskCenter.x, mMaskCenter.y, DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, Path.Direction.CCW);

        //蒙版边框
        mMaskRectPath.reset();
        PointF point = null;

        point = pointList.get(INT_0);
        point.offset(offsetX, offsetY);
        mMaskRectPath.moveTo(point.x, point.y);

        point = pointList.get(INT_1);
        point.offset(offsetX, offsetY);
        mMaskRectPath.lineTo(point.x, point.y);

        point = pointList.get(INT_2);
        point.offset(offsetX, offsetY);
        mMaskRectPath.lineTo(point.x, point.y);

        point = pointList.get(INT_3);
        point.offset(offsetX, offsetY);
        mMaskRectPath.lineTo(point.x, point.y);

        mMaskRectPath.close();

        float imageSize = DIMENS_DP_SIZE_IMAGE * mDensityDP2PX;
        float margin = DIMENS_DP_MARGIN_IMAGE * mDensityDP2PX;
        float vOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, rectInfo.maskHeight / COEFFICIENT_2F);
        float featherOffset = mMaskFeatherWidth / Math.max(mMaskRectInfo.videoWidth, mMaskRectInfo.videoHeight) * imageSize * COEFFICIENT_2F;
        mFeatherZoomImage.setBounds((int) -(imageSize / COEFFICIENT_2F), (int) (vOffset + margin + featherOffset), (int) (imageSize / COEFFICIENT_2F), (int) (imageSize + vOffset + margin + featherOffset));
    }

    public void buildCircleMask(MaskHelper.RectInfo rectInfo, int[] liveWindowLocation) {
        this.mMaskRectInfo = rectInfo;

        int[] maskRectViewLocation = new int[INT_2];
        getLocationOnScreen(maskRectViewLocation);

        int offsetX = liveWindowLocation[INT_0] - maskRectViewLocation[INT_0] - INT_1; // - 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失
        int offsetY = liveWindowLocation[INT_1] - maskRectViewLocation[INT_1] + INT_1; // + 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失

        mMaskCenter = new PointF(rectInfo.maskCenter.x + offsetX, rectInfo.maskCenter.y + offsetY);

        //中心圆
        mCenterRectPath.reset();
        mCenterRectPath.addCircle(mMaskCenter.x, mMaskCenter.y, DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, Path.Direction.CCW);

        //蒙版边框
        mMaskRectPath.reset();
        mMaskRectPath.lineTo(0, 0);
        RectF rect = new RectF(mMaskCenter.x - rectInfo.maskWidth / COEFFICIENT_2F,
                mMaskCenter.y - rectInfo.maskHeight / COEFFICIENT_2F,
                mMaskCenter.x + rectInfo.maskWidth / COEFFICIENT_2F,
                mMaskCenter.y + rectInfo.maskHeight / COEFFICIENT_2F);
        mMaskRectPath.addOval(rect, Path.Direction.CCW);

        Matrix matrix = new Matrix();
        matrix.setRotate(rectInfo.rotation, mMaskCenter.x, mMaskCenter.y);
        mMaskRectPath.transform(matrix);

        float imageSize = DIMENS_DP_SIZE_IMAGE * mDensityDP2PX;
        float margin = DIMENS_DP_MARGIN_IMAGE * mDensityDP2PX;

        float hOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, rectInfo.maskWidth / COEFFICIENT_2F);
        float vOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, rectInfo.maskHeight / COEFFICIENT_2F);

        mVerticalScaleImage.setBounds((int) -(imageSize / COEFFICIENT_2F),
                (int) -(imageSize + vOffset + margin),
                (int) (imageSize / COEFFICIENT_2F),
                (int) -(vOffset + margin));
        mHorizontalScaleImage.setBounds((int) (hOffset + margin),
                (int) -(imageSize / COEFFICIENT_2F),
                (int) (imageSize + hOffset + margin),
                (int) (imageSize / COEFFICIENT_2F));

        float featherOffset = mMaskFeatherWidth / Math.max(mMaskRectInfo.videoWidth, mMaskRectInfo.videoHeight) * imageSize * COEFFICIENT_2F;
        mFeatherZoomImage.setBounds((int) -(imageSize / COEFFICIENT_2F),
                (int) (vOffset + margin + featherOffset),
                (int) (imageSize / COEFFICIENT_2F),
                (int) (imageSize + vOffset + margin + featherOffset));
    }

    /**
     * @param rectInfo           区域信息
     * @param pointList          三界贝塞尔曲线点集合，顺序为：顶点  -  前控制点  -  后控制点（也即下一个顶点的前前控制点）
     * @param liveWindowLocation liveWindow的位置坐标
     */
    public void buildCubicBezierMask(MaskHelper.RectInfo rectInfo, List<PointF> pointList, int[] liveWindowLocation) {
        this.mMaskRectInfo = rectInfo;

        int[] maskRectViewLocation = new int[INT_2];
        getLocationOnScreen(maskRectViewLocation);

        int offsetX = liveWindowLocation[INT_0] - maskRectViewLocation[INT_0] - INT_1; // - 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失
        int offsetY = liveWindowLocation[INT_1] - maskRectViewLocation[INT_1] + INT_1; // + 1是因为美摄的蒙版效果实际存在1个像素的偏差，后续也有可能因SDK升级偏差消失

        mMaskCenter = new PointF(rectInfo.maskCenter.x, rectInfo.maskCenter.y);
        mMaskCenter.offset(offsetX, offsetY);

        //中心圆
        mCenterRectPath.reset();
        mCenterRectPath.addCircle(mMaskCenter.x, mMaskCenter.y, DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, Path.Direction.CCW);

        //蒙版边框
        buildBezierMaskPath(pointList, offsetX, offsetY);

        //设置控制按钮
        setupControlButtons(rectInfo, offsetX, offsetY);
    }

    private void buildBezierMaskPath(List<PointF> pointList, int offsetX, int offsetY) {
        mMaskRectPath.reset();
        //顶点
        PointF vertexPoint = null;
        //前控制点坐标
        PointF perControlPoint = null;
        //前前控制点坐标
        PointF farControlPoint = null;

        //最后一个顶点
        PointF lastVertexPoint = pointList.get(pointList.size() - INT_3);
        //先移动到最后一个顶点处
        mMaskRectPath.moveTo(lastVertexPoint.x, lastVertexPoint.y);

        //第一个顶点的前前控制点
        farControlPoint = pointList.get(pointList.size() - INT_1);

        for (PointF point : pointList) {
            if (farControlPoint == null) {
                farControlPoint = point; //前前控制点
            } else if (vertexPoint == null) {
                vertexPoint = point; //顶点
            } else { //前控制点，三个点集齐可以来条三阶贝塞尔曲线了
                perControlPoint = point;
                //canvas绘制贝塞尔曲线的顺序是：前前控制点，前控制点，顶点
                mMaskRectPath.cubicTo(farControlPoint.x, farControlPoint.y, point.x, perControlPoint.y, vertexPoint.x, vertexPoint.y);
                vertexPoint = null;
                farControlPoint = null;
            }
        }
        mMaskRectPath.close();
        mMaskRectPath.offset(offsetX, offsetY);
    }

    private void setupControlButtons(MaskHelper.RectInfo rectInfo, int offsetX, int offsetY) {
        float imageSize = DIMENS_DP_SIZE_IMAGE * mDensityDP2PX;
        float margin = DIMENS_DP_MARGIN_IMAGE * mDensityDP2PX;

        float hOffset = 0;
        float vOffset = 0;
        if (mMaskType == StreamingConstant.MaskFx.SHAPE_LOVEHEART) {
            float ratio = Math.min((rectInfo.maskWidth / FLOAT_296), (rectInfo.maskHeight / FLOAT_271));
            hOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_296 / COEFFICIENT_2F);
            vOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_271 / COEFFICIENT_2F);
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_STARTLIKE) {
            float ratio = Math.min((rectInfo.maskWidth / FLOAT_282), (rectInfo.maskHeight / FLOAT_270));
            hOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_282 / COEFFICIENT_2F);
            vOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_270 / COEFFICIENT_2F);
        } else {
            hOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, rectInfo.maskWidth / COEFFICIENT_2F);
            vOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, rectInfo.maskHeight / COEFFICIENT_2F);
        }

        mVerticalScaleImage.setBounds((int) -(imageSize / COEFFICIENT_2F),
                (int) -(imageSize + vOffset + margin), (int) (imageSize / COEFFICIENT_2F),
                (int) -(vOffset + margin));
        mHorizontalScaleImage.setBounds((int) (hOffset + margin),
                (int) -(imageSize / COEFFICIENT_2F),
                (int) (imageSize + hOffset + margin),
                (int) (imageSize / COEFFICIENT_2F));

        float featherOffset = mMaskFeatherWidth / Math.max(mMaskRectInfo.videoWidth, mMaskRectInfo.videoHeight) * imageSize * COEFFICIENT_2F;
        mFeatherZoomImage.setBounds((int) -(imageSize / COEFFICIENT_2F),
                (int) (vOffset + margin + featherOffset),
                (int) (imageSize / COEFFICIENT_2F),
                (int) (imageSize + vOffset + margin + featherOffset));

        hOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX + margin, hOffset) + rectInfo.cornerRatio * imageSize;
        vOffset = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX + margin, vOffset) + rectInfo.cornerRatio * imageSize;

        mCornerZoomImage.setBounds((int) -(hOffset + imageSize), (int) -(vOffset + imageSize), (int) (-hOffset), (int) (-vOffset));
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mMaskType == StreamingConstant.MaskFx.SHAPE_NONE) {
            return;
        }

        //保存画布状态
        canvas.save();

        //设置抗锯齿
        canvas.setDrawFilter(new PaintFlagsDrawFilter(INT_0, Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG));

        canvas.drawPath(mCenterRectPath, mRectPaint);

        //直线蒙版的边线在中心点位置不绘制
        if (mMaskType == StreamingConstant.MaskFx.SHAPE_LINEAR) {
            canvas.clipOutPath(mCenterRectPath);
        }

        canvas.drawPath(mMaskRectPath, mRectPaint);

        //平移画布
        canvas.translate(mMaskCenter.x, mMaskCenter.y);
        //旋转画布
        canvas.rotate(mMaskRectInfo.rotation);

        if (mMaskType == StreamingConstant.MaskFx.SHAPE_LINEAR) {
            mFeatherZoomImage.draw(canvas);
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_MIRROR) {
            mFeatherZoomImage.draw(canvas);
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_CIRCLE) {
            mFeatherZoomImage.draw(canvas);
            mVerticalScaleImage.draw(canvas);
            mHorizontalScaleImage.draw(canvas);
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_RECTANGLE) {
            mCornerZoomImage.draw(canvas);
            mFeatherZoomImage.draw(canvas);
            mVerticalScaleImage.draw(canvas);
            mHorizontalScaleImage.draw(canvas);
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_LOVEHEART) {
            mFeatherZoomImage.draw(canvas);
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_STARTLIKE) {
            mFeatherZoomImage.draw(canvas);
        }

        //恢复画布状态
        canvas.restore();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (onTouchEventImpl(event)) {
            return false;
        }
        if (!isEnabled() || (mOnTouchEventListener == null) || (mMaskType == StreamingConstant.MaskFx.SHAPE_NONE)) {
            return false;
        }
        //touch事件的index
        switch (event.getActionMasked()) {
            //首次按下事件
            case MotionEvent.ACTION_DOWN: {
                mIsTouchMoved = false;
                mIsSingleFingerTouched = true;
                mSingleTouchedPoint = new PointF(event.getX(), event.getY());
                // 判断落指区域，后续执行不同的操作
                mTouchEventType = resetTouchEventType();
                break;
            }
            //多指按下
            case MotionEvent.ACTION_POINTER_DOWN: {
                mIsTouchMoved = true;
                mIsSingleFingerTouched = false;
                int pointerCount = event.getPointerCount();
                if (pointerCount == INT_2) {
                    mDoubleTouchedPoint[INT_0] = new PointF(event.getX(INT_0), event.getY(INT_0));
                    mDoubleTouchedPoint[INT_1] = new PointF(event.getX(INT_1), event.getY(INT_1));
                }
                break;
            }
            //触摸移动事件
            case MotionEvent.ACTION_MOVE: {
                int pointerCount = event.getPointerCount();
                if (pointerCount == INT_1) {
                    onOneFingerTouchMoved(new PointF(event.getX(), event.getY()));
                } else if (pointerCount == INT_2) {
                    onTwoFingerTouchMoved(new PointF(event.getX(INT_0), event.getY(INT_0)), new PointF(event.getX(INT_1), event.getY(INT_1)));
                }
                break;
            }
            case MotionEvent.ACTION_POINTER_UP: //多指触摸时的手指抬起事件
            case MotionEvent.ACTION_UP: //手指抬起事件
            case MotionEvent.ACTION_CANCEL: //取消事件
            default: {
                break;
            }
        }
        return true;
    }

    private int resetTouchEventType() {
        float radian = (float) ((mMaskRectInfo.rotation * Math.PI) / ANGLE_180);
        float cosv = (float) Math.cos(radian);
        float sinv = (float) Math.sin(radian);

        float hOffset = (mSingleTouchedPoint.x - mMaskCenter.x) * cosv - (mMaskCenter.y - mSingleTouchedPoint.y) * sinv;
        float vOffset = (mSingleTouchedPoint.x - mMaskCenter.x) * sinv + (mMaskCenter.y - mSingleTouchedPoint.y) * cosv;

        float imageMargin = DIMENS_DP_MARGIN_IMAGE * mDensityDP2PX;

        float verticalLimit = 0;
        float horizontalLimit = 0;

        if (mMaskType == StreamingConstant.MaskFx.SHAPE_LINEAR) {
            verticalLimit = DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX + imageMargin / COEFFICIENT_2F;
            horizontalLimit = Integer.MAX_VALUE;
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_MIRROR) {
            verticalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, mMaskRectInfo.maskHeight / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
            horizontalLimit = Integer.MAX_VALUE;
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_LOVEHEART) {
            float ratio = Math.min((mMaskRectInfo.maskWidth / FLOAT_296), (mMaskRectInfo.maskHeight / FLOAT_271));
            verticalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_271 / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
            horizontalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_296 / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
        } else if (mMaskType == StreamingConstant.MaskFx.SHAPE_STARTLIKE) {
            float ratio = Math.min((mMaskRectInfo.maskWidth / FLOAT_282), (mMaskRectInfo.maskHeight / FLOAT_270));
            verticalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_270 / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
            horizontalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, ratio * FLOAT_282 / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
        } else {
            verticalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, mMaskRectInfo.maskHeight / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
            horizontalLimit = Math.max(DIMENS_DP_RADIUS_CENTER_CIRCLE * mDensityDP2PX, mMaskRectInfo.maskWidth / COEFFICIENT_2F) + imageMargin / COEFFICIENT_2F;
        }

        if (vOffset > verticalLimit) {
            if (hOffset < -horizontalLimit) {
                return EVENT_ZOOM_CORNER;
            } else if (hOffset <= horizontalLimit) {
                return EVENT_SCALE_VERTICAL;
            }
        } else if (vOffset < -verticalLimit) {
            if (Math.abs(hOffset) <= horizontalLimit) {
                return EVENT_ZOOM_FEATHER;
            }
        } else if (hOffset > horizontalLimit) {
            if (Math.abs(vOffset) <= verticalLimit) {
                return EVENT_SCALE_HORIZONTAL;
            }
        } else if (hOffset >= -horizontalLimit) {
            return EVENT_DRAG;
        }
        return EVENT_NONE;
    }

    private void onOneFingerTouchMoved(PointF point) {
        if (!mIsSingleFingerTouched) {
            return;
        }
        if (mIsTouchMoved) {
            switch (mTouchEventType) {
                case EVENT_DRAG: {
                    float radian = (float) ((mMaskRectInfo.videoRotation * Math.PI) / ANGLE_180);
                    float cosv = (float) Math.cos(radian);
                    float sinv = (float) Math.sin(radian);

                    float hOffset = (point.x - mSingleTouchedPoint.x) * cosv + (point.y - mSingleTouchedPoint.y) * sinv;
                    float vOffset = (point.y - mSingleTouchedPoint.y) * cosv - (point.x - mSingleTouchedPoint.x) * sinv;

                    mOnTouchEventListener.onTranslation(hOffset / mMaskRectInfo.videoWidth, vOffset / mMaskRectInfo.videoHeight);
                    break;
                }
                case EVENT_SCALE_HORIZONTAL: {
                    if ((mMaskType == StreamingConstant.MaskFx.SHAPE_CIRCLE) || (mMaskType == StreamingConstant.MaskFx.SHAPE_RECTANGLE)) {
                        float radian = (float) ((mMaskRectInfo.rotation * Math.PI) / ANGLE_180);
                        float cosv = (float) Math.cos(radian);
                        float sinv = (float) Math.sin(radian);

                        float hOffset = (point.x - mSingleTouchedPoint.x) * cosv - (mSingleTouchedPoint.y - point.y) * sinv;

                        float size = Math.min(mMaskRectInfo.videoWidth / COEFFICIENT_2F, mMaskRectInfo.videoHeight / COEFFICIENT_2F);
                        if (size <= 0) {
                            break;
                        }
                        float maskHorizontalScaleRatio = mMaskRectInfo.maskWidth / size;
                        float maskVerticalScaleRatio = mMaskRectInfo.maskHeight / size;

                        float scaleChange = 0;
                        if (size > 0) {
                            scaleChange = INT_2 * hOffset / size;
                        }

                        mOnTouchEventListener.onScaleAndRotate(Math.max(INT_0, maskHorizontalScaleRatio + scaleChange),
                                maskVerticalScaleRatio, 0,
                                OnTouchEventListener.OPTTYPE_HORIZONTAL);
                    }
                    break;
                }
                case EVENT_SCALE_VERTICAL: {
                    if ((mMaskType == StreamingConstant.MaskFx.SHAPE_CIRCLE) || (mMaskType == StreamingConstant.MaskFx.SHAPE_RECTANGLE)) {
                        float radian = (float) ((mMaskRectInfo.rotation * Math.PI) / ANGLE_180);
                        float cosv = (float) Math.cos(radian);
                        float sinv = (float) Math.sin(radian);

                        float vOffset = (point.x - mSingleTouchedPoint.x) * sinv + (mSingleTouchedPoint.y - point.y) * cosv;

                        float size = Math.min(mMaskRectInfo.videoWidth / COEFFICIENT_2F, mMaskRectInfo.videoHeight / COEFFICIENT_2F);
                        if (size <= 0) {
                            break;
                        }
                        float maskHorizontalScaleRatio = mMaskRectInfo.maskWidth / size;
                        float maskVerticalScaleRatio = mMaskRectInfo.maskHeight / size;
                        float scaleChange = 0;
                        if (size > 0) {
                            scaleChange = 2 * vOffset / size;
                        }
                        mOnTouchEventListener.onScaleAndRotate(maskHorizontalScaleRatio,
                                Math.max(INT_0, maskVerticalScaleRatio + scaleChange),
                                0,
                                OnTouchEventListener.OPTTYPE_VERTICAL);
                    }
                    break;
                }
                case EVENT_ZOOM_CORNER: {
                    if (mMaskType == StreamingConstant.MaskFx.SHAPE_RECTANGLE) {
                        float radian = (float) (((mMaskRectInfo.rotation - ANGLE_45) * Math.PI) / ANGLE_180);
                        float cosv = (float) Math.cos(radian);
                        float sinv = (float) Math.sin(radian);

                        float offset = (point.x - mSingleTouchedPoint.x) * sinv + (mSingleTouchedPoint.y - point.y) * cosv;

                        float imageDiagonal = DIMENS_DP_SIZE_IMAGE * mDensityDP2PX * SQUARE_ROOT_2;
                        if (imageDiagonal <= 0) {
                            break;
                        }
                        mOnTouchEventListener.onCornerRatioVary(Math.max(INT_0, Math.min(INT_1, mMaskRectInfo.cornerRatio + offset / imageDiagonal)));
                    }
                    break;
                }
                case EVENT_ZOOM_FEATHER: {
                    float radian = (float) ((mMaskRectInfo.rotation * Math.PI) / ANGLE_180);
                    float cosv = (float) Math.cos(radian);
                    float sinv = (float) Math.sin(radian);

                    float offset = (point.x - mSingleTouchedPoint.x) * sinv + (mSingleTouchedPoint.y - point.y) * cosv;
                    float divisor = DIMENS_DP_SIZE_IMAGE * mDensityDP2PX * COEFFICIENT_2F;
                    if (divisor <= 0) {
                        break;
                    }

                    float ratio = offset / divisor;
                    float max = Math.max(mMaskRectInfo.videoWidth, mMaskRectInfo.videoHeight);
                    float featherWidth = Math.max(0, Math.min(max, mMaskFeatherWidth - ratio * max));

                    mOnTouchEventListener.onFeatherWidthVary(featherWidth);
                    break;
                }
                default: {
                    break;
                }
            }
            mSingleTouchedPoint = point;
        } else if ((Math.abs(mSingleTouchedPoint.x - point.x) > mTouchSlop) || (Math.abs(mSingleTouchedPoint.y - point.y) > mTouchSlop)) { //距离足够触发滑动
            mIsTouchMoved = true;
            mSingleTouchedPoint = point;
        }
    }

    private void onTwoFingerTouchMoved(PointF firstPoint, PointF secondPoint) {
        float oldDegree = (float) Math.toDegrees(Math.atan2(mDoubleTouchedPoint[INT_1].y - mDoubleTouchedPoint[INT_0].y, mDoubleTouchedPoint[INT_0].x - mDoubleTouchedPoint[INT_1].x));
        float newDegree = (float) Math.toDegrees(Math.atan2(secondPoint.y - firstPoint.y, firstPoint.x - secondPoint.x));

        float ratio = 1F;
        //直线不进行双指缩放
        if (mMaskType != StreamingConstant.MaskFx.SHAPE_LINEAR) {
            float xLen = mDoubleTouchedPoint[INT_1].x - mDoubleTouchedPoint[INT_0].x;
            float yLen = mDoubleTouchedPoint[INT_1].y - mDoubleTouchedPoint[INT_0].y;
            float oldDistance = (float) Math.sqrt(xLen * xLen + yLen * yLen);

            xLen = secondPoint.x - firstPoint.x;
            yLen = secondPoint.y - firstPoint.y;
            float newDistance = (float) Math.sqrt(xLen * xLen + yLen * yLen);
            ratio = newDistance / oldDistance;
        }

        float size = Math.min(mMaskRectInfo.videoWidth / COEFFICIENT_2F, mMaskRectInfo.videoHeight / COEFFICIENT_2F);
        if (size <= 0) {
            return;
        }
        float maskHorizontalScaleRatio = mMaskRectInfo.maskWidth / size;
        float maskVerticalScaleRatio = mMaskRectInfo.maskHeight / size;

        mOnTouchEventListener.onScaleAndRotate(maskHorizontalScaleRatio * ratio,
                maskVerticalScaleRatio * ratio, oldDegree - newDegree, OnTouchEventListener.OPTTYPE_ANGLE);

        mDoubleTouchedPoint[INT_0] = firstPoint;
        mDoubleTouchedPoint[INT_1] = secondPoint;
    }

    public interface OnTouchEventListener {

        int OPTTYPE_HORIZONTAL = 1;
        int OPTTYPE_VERTICAL = 2;
        int OPTTYPE_ANGLE = 3;

        /**
         * scale and rotate when touch on button or gesture
         *
         * @param horizontalScaleRatio horizontal scale value
         * @param verticalScaleRatio   vertical scale value
         * @param angle                rotate angle
         * @param optType 操作类型, 1：horizontalScaleRatio，2：verticalScaleRatio， 3：angle
         */
        void onScaleAndRotate(float horizontalScaleRatio, float verticalScaleRatio, float angle, int optType);

        /**
         * touch in sticker and drag
         */
        void onTranslation(float horizontalTranslationRatio, float verticalTranslationRatio);

        void onCornerRatioVary(float cornerRatio);

        void onFeatherWidthVary(float featherWidth);

    }

}
