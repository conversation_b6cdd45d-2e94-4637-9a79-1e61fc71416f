/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ImportVideoDialog
 ** Description: 导入视频弹框
 ** Version: 1.0
 ** Date : 2025/05/28
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/05/28    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.oplus.gallery.foundation.ui.dialog.ProgressDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 导入视频弹框
 */
class ImportVideoDialog(
    val context: Context
) {

    /**
     * 导入弹框监听器
     */
    var importDialogListener: ImportDialogListener? = null

    /**
     * 导入弹框结束时，提示不支持导入的素材量
     */
    var unSupportCount: Int = 0

    /**
     * 导入素材的流程，是否还在进行中
     */
    @Volatile
    private var isWorking: Boolean = false

    /**
     * 进度弹框
     */
    private var importDialog: ProgressDialog? = null

    /**
     * 剩余显示时长
     */
    private var remainingDuration: Float = MathUtils.ZERO_F

    /**
     * 是否需要结束流程
     */
    private var needFinished: Boolean = false

    /**
     * 主线程handler，用于刷新进度
     */
    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_SHOW_DIALOG -> show((msg.obj as? Boolean) ?: false)

                MSG_UPDATE_IMPORT_VIDEO_PROGRESS -> {
                    val dialog = importDialog ?: return
                    var progress = msg.arg1.coerceIn(MathUtils.ZERO, MAX_PROGRESS)
                    if (progress == MAX_PROGRESS) {
                        GLog.d(TAG, LogFlag.DL) { "[handleMessage] progress is max, needFinished: $needFinished" }
                        if (needFinished) {
                            importDialog?.dismiss()
                            importDialogListener?.onFinished()
                            showUnSupportToast()
                            release()
                        }
                        return
                    }

                    dialog.setProgress(progress)
                    val delayMillis = (remainingDuration / (MAX_PROGRESS - progress)).toLong()
                    progress += MathUtils.ONE
                    remainingDuration -= delayMillis
                    startProgressWithMessage(progress, delayMillis)
                }
            }
        }
    }

    /**
     * 延时1秒，如果流程未结束，再显示弹框
     *
     * @param isNormalTheme true-白色正常主题色，false-黑色主题
     * @param startTimeMillis 点击开始导入时的时间戳
     */
    fun showIfNeed(isNormalTheme: Boolean, startTimeMillis: Long? = null) {
        // 已经消耗了的时长
        val delayedMillis: Long = startTimeMillis?.takeIf { it > 0 }?.let {
            System.currentTimeMillis() - startTimeMillis
        } ?: MathUtils.ZERO.toLong()
        // 剩余需要延时等待的时长
        val delayMillis = (SHOW_DELAY_MILLIS - delayedMillis).takeIf { it > 0 } ?: MathUtils.ZERO.toLong()

        isWorking = true
        if (handler.hasMessages(MSG_SHOW_DIALOG)) {
            GLog.w(TAG, LogFlag.DL) { "[showIfNeed] has messages" }
            handler.removeMessages(MSG_SHOW_DIALOG)
        }
        handler.sendMessageDelayed(Message().apply {
            what = MSG_SHOW_DIALOG
            obj = isNormalTheme
        }, delayMillis)
    }

    /**
     * 结束导入流程
     */
    @Synchronized
    fun finishIfNeed() {
        if (isWorking.not()) {
            GLog.i(TAG, LogFlag.DL) { "[finishIfNeed] is not working, return" }
            return
        }

        isWorking = false
        GLog.d(TAG, LogFlag.DL) { "[finishIfNeed] dialog is showing? ${importDialog?.isShowing()}" }
        if (importDialog?.isShowing() == true) {
            start(ImportStatus.FINISH)
        } else {
            importDialogListener?.onFinished()
            showUnSupportToast()
            release()
        }
    }

    /**
     * 强制中断，销毁
     */
    @Synchronized
    fun destroy() {
        if (isWorking.not()) {
            GLog.i(TAG, LogFlag.DL) { "[destroy] is not working, return" }
            return
        }

        isWorking = false
        GLog.d(TAG, LogFlag.DL) { "[destroy] dialog is showing? ${importDialog?.isShowing()}" }
        importDialog?.dismiss()
        release()
    }

    /**
     * 显示进度弹框
     *
     * @param isNormalTheme true-白色正常主题色，false-黑色主题
     */
    @Synchronized
    private fun show(isNormalTheme: Boolean) {
        if (isWorking.not()) {
            GLog.d(TAG, LogFlag.DL) { "[show] is finished, no need show dialog" }
            return
        }

        if (importDialog == null) {
            GLog.d(TAG, LogFlag.DL) { "[show] build and show import dialog" }
            val themeResId = if (isNormalTheme) {
                com.oplus.gallery.basebiz.R.style.picture3d_Theme_Gallery
            } else {
                com.support.appcompat.R.style.Theme_COUI_Dark_Yellow
            }
            val builder = ProgressDialog.Builder(context, R.style.ImportDialogStyle, themeResId).apply {
                setTitle(R.string.videoeditor_picker_loading)
                setMaxProgress(MAX_PROGRESS)
                setCancelable(false)
                setCanceledOnTouchOutside(false)
                setPositiveButton(context.resources.getString(R.string.videoeditor_cancel)) { _, _ ->
                    isWorking = false
                    importDialogListener?.onCancelClick()
                    release()
                }
            }
            importDialog = builder.build().show()
        } else if (importDialog?.isShowing()?.not() == true) {
            GLog.d(TAG, LogFlag.DL) { "[show] show import dialog" }
            importDialog?.show()
        }
        start(ImportStatus.START)
    }

    /**
     * 显示不支持的素材Toast提示
     */
    private fun showUnSupportToast() {
        val count = unSupportCount
        handler.post {
            if (count > 0) {
                // 部分素材不支持时，提示用户
                ToastUtil.showLongToast(context.resources.getQuantityString(BasebizR.plurals.common_trim_unsupported_some, count, count))
            }
        }
    }

    /**
     * 开始对应流程
     *
     * @param status 导入流程状态
     */
    private fun start(status: ImportStatus) {
        GLog.d(TAG, LogFlag.DL) { "[start] status: $status, remainingDuration: $remainingDuration" }
        when (status) {
            ImportStatus.START -> {
                remainingDuration = TOTAL_DURATION
                startProgressWithMessage()
            }

            ImportStatus.FINISH -> {
                needFinished = true
                if (remainingDuration <= TRANSITION_DURATION) {
                    // 当状态结束时，进度已经执行到尾声，则直接结束
                    startProgressWithMessage(MAX_PROGRESS)
                } else {
                    val usedDuration = TOTAL_DURATION - remainingDuration
                    remainingDuration = if (usedDuration > MIN_DURATION) {
                        // 当状态结束时，已显示时长，已经不低于最小时长。则直接快速过渡到结束
                        TRANSITION_DURATION
                    } else {
                        // 当状态结束时，已显示时长，还未达到最小时长。则按最小时长作为总量
                        (MIN_DURATION - usedDuration).coerceAtLeast(TRANSITION_DURATION)
                    }
                }
            }
        }
    }

    /**
     * 根据当前类型，开始启动加载进度条到对应值
     *
     * @param progress 加载进度
     * @param delayMillis 消息延时发送时长
     */
    private fun startProgressWithMessage(progress: Int = 0, delayMillis: Long = 0) {
        if (handler.hasMessages(MSG_UPDATE_IMPORT_VIDEO_PROGRESS)) {
            GLog.w(TAG, LogFlag.DL) { "[startProgressByType] has messages" }
            handler.removeMessages(MSG_UPDATE_IMPORT_VIDEO_PROGRESS)
        }
        handler.sendMessageDelayed(Message().apply {
            what = MSG_UPDATE_IMPORT_VIDEO_PROGRESS
            arg1 = progress
        }, delayMillis)
    }

    /**
     * 释放资源
     */
    @Synchronized
    private fun release() {
        GLog.d(TAG, LogFlag.DL) { "[release]" }
        if (handler.hasMessages(MSG_SHOW_DIALOG)) {
            handler.removeMessages(MSG_SHOW_DIALOG)
        }
        if (handler.hasMessages(MSG_UPDATE_IMPORT_VIDEO_PROGRESS)) {
            handler.removeMessages(MSG_UPDATE_IMPORT_VIDEO_PROGRESS)
        }
        importDialog = null
        importDialogListener = null
        remainingDuration = MathUtils.ZERO_F
        needFinished = false
        unSupportCount = 0
    }

    internal companion object {
        private const val TAG = "ImportVideoDialog"

        /**
         * 显示弹框
         */
        private const val MSG_SHOW_DIALOG: Int = 1

        /**
         * 更新导入视频进度的消息
         */
        private const val MSG_UPDATE_IMPORT_VIDEO_PROGRESS: Int = 2

        /**
         * 延时显示时长
         */
        private const val SHOW_DELAY_MILLIS = 150L

        /**
         * 最大进度
         */
        const val MAX_PROGRESS: Int = 100

        /**
         * 按照该时长，计算进度加载速度
         */
        const val TOTAL_DURATION: Float = 10000f

        /**
         * 如果流程提前结束，显示时长，最少也能不低于此时长
         */
        const val MIN_DURATION: Float = 1000f

        /**
         * 用来过渡的时间，防止提前中断时，直接跳变到100结束的情况
         */
        const val TRANSITION_DURATION: Float = 100f
    }
}

/**
 * 导入流程状态
 */
enum class ImportStatus {
    /**
     * 开始状态
     */
    START,

    /**
     * 结束状态
     */
    FINISH
}

/**
 * 导入弹框监听器
 */
interface ImportDialogListener {
    /**
     * 点击取消监听
     */
    fun onCancelClick()

    /**
     * 进度加载完成
     */
    fun onFinished()
}