/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorRingtoneState
 ** Description:视频铃声编辑 state
 ** Version: 1.0
 ** Date : 2025/06/10
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.ringtone

import android.content.Context
import android.content.Intent
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.business.ringtone.RingtoneParam.Companion.DEFAULT_VIDEO_TRIM_MAX_DURATION
import com.oplus.gallery.videoeditorpage.video.business.ringtone.RingtoneParam.Companion.DEFAULT_VIDEO_TRIM_MIN_DURATION
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 用于定制视频铃声专用编辑页
 */
class EditorRingtoneState(
    context: Context,
    editorControlView: EditorControlView,
    private val videoTrimMinDuration: Long?,
    private val videoTrimMaxDuration: Long?
) : EditorBaseState<EditorRingtoneUIController>(TAG, context, editorControlView) {

    override fun createUIController(): EditorRingtoneUIController {
        return EditorRingtoneUIController(
            mContext,
            mEditorControlView,
            this,
            videoTrimMinDuration ?: DEFAULT_VIDEO_TRIM_MIN_DURATION,
            videoTrimMaxDuration ?: DEFAULT_VIDEO_TRIM_MAX_DURATION
        )
    }

    override fun showOperaIcon() = false

    /**
     * 视频铃声编辑是直接进入该state，没有state间的过度，无需动画
     */
    override fun isSkipAnim(): Boolean {
        return true
    }

    override fun onBackPressed(): Boolean {
        editorControlView.editorStateManager.finishActivity(this)
        return true
    }

    /**
     * 开始进行编辑导出操作
     * 1.显示导出进度 dialog
     * 2.启动导出任务
     */
    override fun clickDone() {
        uiController.startCompileOperation()
    }

    /**
     * 将定制 state 的结果返回给 activity
     * @param intent activity 最终作为 result 的 intent
     */
    fun setStateResult(intent: Intent) {
        editorControlView.editorStateManager.onStateResultChange(this, intent)
    }

    override fun clickCancel() {
        editorControlView.editorStateManager.finishActivity(this)
    }

    companion object {
        private const val TAG = "EditorRingtoneState"
    }
}