/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  PreviewAnimControllerImpl
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/21
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/21      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.preview

import android.content.Context
import android.graphics.PointF
import android.graphics.RectF
import android.util.Size
import android.view.MotionEvent
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.foundation.opengl.transform.AxisAngleTransform
import com.oplus.gallery.foundation.opengl.transform.CompoundMatrixProxy
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.foundation.opengl.transform.Pose
import com.oplus.gallery.foundation.ui.animationcontrol.AnimateOperation
import com.oplus.gallery.foundation.ui.animationcontrol.AnimationControl
import com.oplus.gallery.foundation.ui.animationcontrol.ClipRectChangeListener
import com.oplus.gallery.foundation.ui.animationcontrol.ContentAnimationListener
import com.oplus.gallery.foundation.ui.animationcontrol.GestureCollector
import com.oplus.gallery.foundation.ui.animationcontrol.OperationChangeListener
import com.oplus.gallery.foundation.ui.animationcontrol.TransformChangeListener
import com.oplus.gallery.foundation.ui.preview.AnimationState
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties
import com.oplus.gallery.foundation.ui.preview.createProperties
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ISeekingListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.utlis.MATRIX4x4_SIZE
import com.oplus.gallery.videoeditorpage.utlis.ModelMatrixConvertor
import com.oplus.gallery.videoeditorpage.utlis.SIZE_ZERO
import com.oplus.gallery.videoeditorpage.utlis.ViewCoordinateConverter
import com.oplus.gallery.videoeditorpage.utlis.aspectRatio
import com.oplus.gallery.videoeditorpage.utlis.computeViewToTimelineMatrix
import com.oplus.gallery.videoeditorpage.utlis.fitToAspectRatio
import com.oplus.gallery.videoeditorpage.utlis.formatMatrix
import com.oplus.gallery.videoeditorpage.utlis.onSizeChanged
import com.oplus.gallery.videoeditorpage.utlis.removePaddings
import com.oplus.gallery.videoeditorpage.utlis.sizeRound
import com.oplus.gallery.videoeditorpage.video.config.UiConfigManager

/**
 * 视频预览页面动画控制器
 */
internal class PreviewAnimControllerImpl(
    private val context: Context,
    /**
     * 预览内容所在的View，一般为美摄提供的LiveWindow
     */
    private val contentView: View,
) : PreviewAnimController {

    private val _gestureEnabled: MutableLiveData<Boolean> = MutableLiveData(false)
    val gestureEnabled: LiveData<Boolean> get() = _gestureEnabled

    private val rawAnimCtrl: AnimationControl = AnimationControl(UiConfigManager.minContentClipSize)

    /**
     * 带动画的显示变换的矩阵参数
     * 第一个参数为4*4矩阵，第二个参数为是否需要重绘
     */
    private val _displayMatrix: MutableLiveData<Pair<FloatArray, Boolean>?> = MutableLiveData(null)
    val displayMatrix: LiveData<Pair<FloatArray, Boolean>?> get() = _displayMatrix

    /**
     * 当显示矩阵发生变化时，是否需要重绘，默认为true
     */
    private var repaintWhenDisplayMatrixChanged: Boolean = true

    override val properties: PreviewAnimationProperties by lazy { rawAnimCtrl.createProperties() }

    /**
     * 手势事件监听器，使用者需要配置接受触摸事件的View为该监听器
     */
    val touchListener: View.OnTouchListener = object : View.OnTouchListener {
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            previewGestureListener.touchView = v
            gestureDetector.onTouch(v, event)
            return gestureEnabled.value == true
        }
    }

    /**
     * 由于[AnimationControl]在配置过程中，会有很多中间状态矩阵通知，导致预览闪烁
     * 这里通过一个标志位来控制是否发布变换矩阵
     */
    private var enableContentTransformPublish: Boolean = true

    private val currentContentTransformListener = TransformChangeListener { contentTransform ->
        if (enableContentTransformPublish.not()) return@TransformChangeListener
        displayMatrixConvertor?.let {
            GLog.d(TAG, LogFlag.DL) {
                "[contentTransformListener] contentTransform=${contentTransform}"
            }
            // 由于 AnimationControl 会按照CenterInside的策略计算布局偏移，
            // 但美摄的 LiveWindow 也按照CenterInside的策略进行默认布局，这样相当于叠加了两次偏移，
            // 所以这里手动将 AnimationControl 的偏移量去掉
            contentTransform.translate(-centerInsideOffsetInDisplay.x, -centerInsideOffsetInDisplay.y)
            _displayMatrix.value = it.convert(contentTransform.matrix) to repaintWhenDisplayMatrixChanged
        }
    }

    private val finalContentTransformListener = TransformChangeListener { contentTransform ->
        finalContentTransformMatrixStartingIdentity.value = calcFinalContentTransformMatrixStartingIdentity()
    }

    private val displayArea: MutableLiveData<RectF> =
        MutableLiveData(RectF(0F, 0F, contentView.width.toFloat(), contentView.height.toFloat()))

    /**
     * 预览区域相对于显示区域的偏移量，用于计算预览区域
     */
    private var previewAreaMargins: RectF = RectF()

    override var previewArea: MutableLiveData<RectF> = MutableLiveData(
        displayArea.value?.removePaddings(previewAreaMargins)?.takeIf { it.isEmpty.not() } ?: RectF()
    )

    override val defaultContentPose: Pose get() = rawAnimCtrl.defaultContentPose

    override val currentContentPose: Pose get() = rawAnimCtrl.currentContentPose

    override val finalContentPose: Pose get() = rawAnimCtrl.finalContentPose

    private var initialContentTransformMatrix: Matrix = Matrix()

    private var invertedInitialContentTransformMatrix: Matrix = Matrix()

    override val finalContentTransformMatrixStartingIdentity: MutableLiveData<FloatArray> =
        MutableLiveData(calcFinalContentTransformMatrixStartingIdentity())

    override val finalClipRect: RectF get() = rawAnimCtrl.finalClipRect

    val timelineChangeListener: EditorEngine.ITimelineChangedListener = object : EditorEngine.ITimelineChangedListener {
        override fun onCurrentTimelineChanged(timeline: ITimeline?, updateTimelineView: Boolean) {
            GLog.d(TAG, LogFlag.DL) {
                "[onCurrentTimelineChange] timelineSize=${timeline?.width}*${timeline?.height}"
            }
            timeline?.let {
                // 时间线大小变更后，对应的矩阵是在下一帧才生效，所以这里不立即更新，
                // 需要等待下一帧刷新，避免矩阵与对应的帧不匹配，导致预览闪烁
                if (contentSize.width != it.width || contentSize.height != it.height) {
                    disableRepaintWhenMatrixChangeUntilNextSeek()
                    setContentSize(it.width, it.height, false)
                }
            }
        }
    }

    val onSeekListener = object : ISeekingListener {
        override fun onSeekingTimelinePosition(position: Long) {
            GLog.d(TAG, LogFlag.DL) { "[onSeekingTimelinePosition] position=$position" }
            // 当帧刷新后，将立即重绘的标志位设置为true
            repaintWhenDisplayMatrixChanged = true
        }
    }


    private var contentSize: Size = SIZE_ZERO

    /**
     * 预览的模型变换矩阵转换器，用于将屏幕坐标系中的模型矩阵转换为美摄预览坐标系中的模型矩阵
     */
    private var displayMatrixConvertor: ModelMatrixConvertor? = null

    /**
     * 手势识别器，用于将触摸事件转为为手势事件
     */
    private val gestureDetector: GestureCollector by lazy {
        GestureCollector(context).apply { gestureListener = previewGestureListener }
    }
    private val previewGestureListener by lazy {
        PreviewGestureListener(rawAnimCtrl, contentView)
    }

    override val isContentAnimating: MutableLiveData<Boolean> = MutableLiveData(false)

    private val contentAnimationListener = object : ContentAnimationListener {
        override fun onContentAnimationStart() {
            GLog.d(TAG, LogFlag.DL) { "[onContentAnimationStart]" }
            isContentAnimating.setOrPostValue(true)
        }

        override fun onContentAnimationUpdate(matrix: Matrix) {
            GLog.d(TAG, LogFlag.DL) { "[onContentAnimationUpdate] matrix=$matrix" }
        }

        override fun onContentAnimationEnd() {
            GLog.d(TAG, LogFlag.DL) { "[onContentAnimationEnd]" }
            isContentAnimating.setOrPostValue(false)
        }
    }

    /**
     * 内容在视图容器中，如果按照centerInside方式布局的话，需要的偏移量
     */
    private var centerInsideOffsetInDisplay: PointF = PointF(0F, 0F)

    /**
     * 操作结束回调处理器，用于处理操作结束时的回调，防止嵌套回调问题
     */
    private val operationEndActionsHandler = OperationEndActionsHandler()

    private val operationChangeListener: OperationChangeListener = object : OperationChangeListener {
        override fun onChange(operation: AnimateOperation) {
            GLog.d(TAG, LogFlag.DL) { "[operationChange] operation=$operation" }
            properties.animationState = AnimationState(operation)
            if (operation is AnimateOperation.NoneOperation) {
                operationEndActionsHandler.executeActions()
            }
        }
    }

    init {
        contentView.onSizeChanged(::onDisplaySizeChanged)
        rawAnimCtrl.run {
            addCurrentContentTransformChangeListener(currentContentTransformListener)
            addFinalContentTransformChangeListener(finalContentTransformListener)
            addContentAnimationListener(contentAnimationListener)
            addOperationChangeListener(operationChangeListener)
            setInterpolatorStrategy(PreviewInterpolatorStrategy())
        }
    }

    override fun disableRepaintWhenMatrixChangeUntilNextSeek() {
        GLog.d(TAG, LogFlag.DL) { "[disableRepaintWhenMatrixChangeUntilNextSeek]" }
        repaintWhenDisplayMatrixChanged = false
    }

    override fun setGestureEnabled(enabled: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[setGestureEnabled] enabled=$enabled" }
        _gestureEnabled.value = enabled
    }

    override fun setContentSize(width: Int, height: Int, withAnimation: Boolean): Boolean {
        GLog.d(TAG, LogFlag.DL) {
            "[setContentSize] width=$width height=$height withAnimation=$withAnimation"
        }
        if ((width == contentSize.width) && (height == contentSize.height)) return false
        contentSize = Size(width, height)
        displayArea.value?.sizeRound()?.let { displaySize ->
            updateDisplayMatrixConvertor(displaySize, contentSize)
        }
        runAnimationWithPublishControl(withAnimation.not()) {
            setImageSize(width, height)
            doRevert()
            if (withAnimation.not()) forceFinishAnimation()
        }
        return true
    }

    override fun setPreviewAreaMargins(margins: RectF, withAnimation: Boolean) {
        if (previewAreaMargins == margins) return
        GLog.d(TAG, LogFlag.DL) {
            "[setPreviewAreaMargins] margins=$margins withAnimation=$withAnimation"
        }
        previewAreaMargins = margins
        val newPreviewArea = displayArea.value?.removePaddings(margins)
            ?.takeIf { it.isEmpty.not() } ?: displayArea.value ?: return
        runAnimationWithPublishControl(withAnimation.not()) {
            setPreviewArea(newPreviewArea)
            doRevert()
            if (withAnimation.not()) forceFinishAnimation()
        }
        previewArea.value = rawAnimCtrl.previewArea
    }


    override fun notifyClipFrameChanged(clipRect: RectF, withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) {
            "[notifyClipFrameChanged] clipRect=$clipRect withAnimation=$withAnimation"
        }
        rawAnimCtrl.updateClipRect(clipRect)
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun addClipRectChangeListener(listener: ClipRectChangeListener) {
        GLog.d(TAG, LogFlag.DL) { "[addClipRectChangeListener]" }
        rawAnimCtrl.addClipRectChangeListener(listener)
    }

    override fun removeClipRectChangeListener(listener: ClipRectChangeListener) {
        GLog.d(TAG, LogFlag.DL) { "[removeClipRectChangeListener]" }
        rawAnimCtrl.removeClipRectListener(listener)
    }

    override fun rotateBy(degree: Float, pivotX: Float, pivotY: Float, withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) {
            "[rotateBy] degree=$degree pivotX=$pivotX pivotY=$pivotY withAnimation=$withAnimation"
        }
        rawAnimCtrl.rotateContent(degree, pivotX, pivotY)
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun rotateByWithConstraint(degree: Float, constraintScale: Float, withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) {
            "[rotateByWithConstraint] degree=$degree constraintScale=$constraintScale withAnimation=$withAnimation"
        }
        rawAnimCtrl.rotateWithConstraint(degree, constraintScale)
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun zoomToCenter(withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[zoomToCenter] withAnimation=$withAnimation" }
        rawAnimCtrl.zoomToCenter()
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun mirrorHorizontal(withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[mirrorHorizontal] withAnimation=$withAnimation" }
        rawAnimCtrl.flipHorizontal()
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun anticlockwiseRotateOrientation(withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[rotateOrientation] withAnimation=$withAnimation" }
        rawAnimCtrl.changeOrientation()
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun scale(scale: Float, pivotX: Float, pivotY: Float, withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) {
            "[scale] scale=$scale pivotX=$pivotX pivotY=$pivotY withAnimation=$withAnimation"
        }
        rawAnimCtrl.scaleContent(scale, pivotX, pivotY)
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun revert(withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[revert] withAnimation=$withAnimation" }
        doRevert()
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    private fun doRevert() {
        rawAnimCtrl.revert()
        if (initialContentTransformMatrix != rawAnimCtrl.finalContentTransform) {
            initialContentTransformMatrix.set(rawAnimCtrl.finalContentTransform)
            initialContentTransformMatrix.invert(invertedInitialContentTransformMatrix)
            finalContentTransformMatrixStartingIdentity.value = calcFinalContentTransformMatrixStartingIdentity()
            GLog.d(TAG, LogFlag.DL) {
                "[doRevert] initialContentTransformMatrix=$initialContentTransformMatrix\n" +
                        "invertedInitialContentTransformMatrix=$invertedInitialContentTransformMatrix"
            }
        }
    }

    override fun updateContentTransformWithStartingIdentity(
        matrix: FloatArray,
        axisAngleTransform: AxisAngleTransform,
        cropCenter: PointF,
        withAnimation: Boolean
    ) {
        GLog.d(TAG, LogFlag.DL) {
            "[updateContentTransformWithStartingIdentity] " +
                    "withAnimation=$withAnimation " +
                    "mirrorAxis=$axisAngleTransform " +
                    "cropCenter=$cropCenter" +
                    "\nmatrix=\n${formatMatrix(matrix)}"
        }
        val extendedMatrix = FloatArray(32) { if (it < matrix.size) matrix[it] else 0F }
        rawAnimCtrl.updateContentTransform(
            CompoundMatrixProxy().apply {
                set(Matrix(extendedMatrix).apply { preConcat(initialContentTransformMatrix) })
                this.axisAngleTransform = axisAngleTransform
                makeFinalMatrix(cropCenter.x, cropCenter.y)
                set(finalMatrix)
            }
        )
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    override fun snapBack(withAnimation: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[snapBack] withAnimation=$withAnimation" }
        rawAnimCtrl.snapBack()
        if (withAnimation.not()) {
            rawAnimCtrl.forceFinishAnimation()
        }
    }

    private fun onDisplaySizeChanged(newWidth: Int, newHeight: Int) {
        if ((newWidth == 0) || (newHeight == 0)) return
        val displayRect = RectF(0F, 0F, newWidth.toFloat(), newHeight.toFloat())
        val previewRect = displayRect.removePaddings(previewAreaMargins).takeIf { it.isEmpty.not() } ?: displayRect

        GLog.d(TAG, LogFlag.DL) {
            "[onDisplaySizeChanged] displayArea=$displayRect previewArea=$previewRect contentSize=${rawAnimCtrl.imageSize}"
        }

        if (this.displayArea.value != displayRect) {
            updateDisplayMatrixConvertor(displayRect.sizeRound(), contentSize)
        }

        // 重置 AnimationControl
        runAnimationWithPublishControl {
            setFullVisibleArea(displayRect)
            setPreviewArea(previewRect)
            if (displayArea.value?.isEmpty == false) {
                // 非首次更改，需要保持裁剪区域不变
                zoomToCenter()
            } else { // 首次初始化预览区域，需要Revert
                doRevert()
            }
            forceFinishAnimation()
        }

        // 通知显示区域发生变化
        this.previewArea.value = previewRect
        this.displayArea.value = displayRect
    }

    private fun updateDisplayMatrixConvertor(displaySize: Size, timelineSize: Size) {
        val timelineAspectRatio = timelineSize.aspectRatio()
        val timelineSizeInDisplay = if (timelineAspectRatio == 0F) displaySize else displaySize.fitToAspectRatio(timelineAspectRatio)
        centerInsideOffsetInDisplay = PointF(
            (displaySize.width - timelineSizeInDisplay.width) / 2F,
            (displaySize.height - timelineSizeInDisplay.height) / 2F
        )
        displayMatrixConvertor = ModelMatrixConvertor(computeViewToTimelineMatrix(displaySize, displaySize))
    }

    private fun runAnimationWithPublishControl(
        enablePublishOnce: Boolean = true,
        block: AnimationControl.() -> Unit,
    ) {
        enableContentTransformPublish = false
        rawAnimCtrl.run(block)
        enableContentTransformPublish = true
        if (enablePublishOnce) {
            currentContentTransformListener.onTransformChanged(rawAnimCtrl.currentContentTransform)
        }
    }

    override fun doOnOperationEnd(action: () -> Unit) {
        GLog.d(TAG, LogFlag.DL) { "[doOnOperationEnd] current operation: ${rawAnimCtrl.currentOperation}" }
        if (rawAnimCtrl.currentOperation is AnimateOperation.NoneOperation) {
            action.invoke()
        } else {
            operationEndActionsHandler.addAction(action)
        }
    }

    override fun addContentAnimationListener(listener: ContentAnimationListener) =
        rawAnimCtrl.addContentAnimationListener(listener)

    override fun removeContentAnimationListener(listener: ContentAnimationListener) =
        rawAnimCtrl.removeContentAnimationListener(listener)

    private fun calcFinalContentTransformMatrixStartingIdentity(): FloatArray =
        FloatArray(MATRIX4x4_SIZE).apply {
            System.arraycopy(rawAnimCtrl.finalContentTransform.matrix, 0, this, 0, MATRIX4x4_SIZE)
            android.opengl.Matrix.multiplyMM(this, 0, this, 0, invertedInitialContentTransformMatrix.matrix, 0)
        }

    companion object {
        private const val TAG: String = "PreviewAnimControllerImpl"
    }
}

/**
 * 预览手势监听器
 */
private class PreviewGestureListener(
    private val rawAnimCtrl: AnimationControl,
    private val contentView: View
) : SimpleGestureListener {
    /**
     * 缓存触摸的View，用于计算与contentView的坐标差
     * （因为接受触摸事件的View和contentView不一定是一个View）
     */
    var touchView: View? = null

    private val scalePivot: PointF = PointF()

    override fun onUp(event: MotionEvent): Boolean {
        rawAnimCtrl.snapBack()
        return true
    }

    override fun onDoubleTap(): Boolean = true

    override fun onScroll(distanceX: Float, distanceY: Float): Boolean {
        rawAnimCtrl.scrollContent(distanceX, distanceY)
        rawAnimCtrl.forceFinishAnimation()
        return true
    }

    override fun onScaleRotate(
        scalePivotX: Float,
        scalePivotY: Float,
        angle: Float,
        deltaAngle: Float,
        rotatePivotX: Float,
        rotatePivotY: Float,
        scale: Float,
        deltaScale: Float
    ): Boolean {
        touchView?.let {
            ViewCoordinateConverter.convertPoint(it, scalePivotX, scalePivotY, contentView, scalePivot)
            rawAnimCtrl.scaleContent(deltaScale, scalePivot.x, scalePivot.y)
            rawAnimCtrl.forceFinishAnimation()
        }
        return true
    }
}
