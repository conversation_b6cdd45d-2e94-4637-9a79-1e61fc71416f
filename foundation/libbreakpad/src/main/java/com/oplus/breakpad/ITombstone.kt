/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IDecoderTombstone.kt
 ** Description: native异常遗言信息接口
 **
 ** Version: 1.0
 ** Date: 2022/6/17
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/6/17     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.breakpad

interface ITombstone {

    /**
     * 设置遗言信息
     */
    fun setTombstone(tombstone: BreakpadTombstone)

    /**
     * 获取遗言信息
     */
    fun getTombstone(): BreakpadTombstone?
}