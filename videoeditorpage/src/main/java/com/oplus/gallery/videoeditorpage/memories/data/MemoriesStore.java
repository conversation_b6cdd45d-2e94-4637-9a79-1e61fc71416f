/*********************************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  - MemoriesStore.java
 * * Description : MemoriesStore
 * * Version     : 1.0
 * * Date        : 2017/11/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <EMAIL>  2017/11/21  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.data;

import android.provider.BaseColumns;

public class MemoriesStore {
    // User defined name
    public static final int NAME_USER_DEFINED = 1;
    public static final int FACE_SINGLE_PERSON_OPTIMAL = 3002;

    protected interface MediaMetaStateColumns {
        String INVALID = "invalid";
        String IS_RECYCLED = "is_recycled";
    }

    protected interface MediaMetaColumns {
        String DATA = "_data";
        String DATE_TAKEN = "date_taken";
        String MEDIA_TYPE = "media_type";
        String SCORE = "score";
        String IS_SINGLE_FACE = "is_single_face";
    }

    protected interface SetColumns {
        String MEMORIES_ID = "_id";
        String MEMORIES_NAME = "m_name";
        String MEMORIES_TYPE = "m_type";
        String MEMORIES_TAKEN = "m_taken";
        String MEMORIES_IS_DELETED = "m_is_deleted";
        String MEMORIES_FACE_GROUP_ID = "m_group_id";
        String MEMORIES_COUNTRY = "m_country";
        String MEMORIES_CITY = "m_city";
        String MEMORIES_IS_FOREIGN = "m_is_foreign";
        String MEMORIES_START_TIME = "m_start_time";
        String MEMORIES_END_TIME = "m_end_time";
        String MEMORIES_COVER = "m_cover_path";
        String MEMORIES_THEME = "m_theme";
        String MEMORIES_MUSIC = "m_music";
        String MEMORIES_LATITUDE = "m_latitude";
        String MEMORIES_LONGITUDE = "m_longitude";
        String MEMORIES_NAME_TYPE = "m_name_type";

        int MEMORIES_NOT_DELETED = 0;
        int MEMORIES_HAS_DELETED = 1;
    }

    protected interface SetMapColumns {
        String MEDIA_ID = "media_id";
        String SET_ID = "set_id";
        String IS_COVER = "is_cover";
        String IN_VIDEO = "in_video";
        String TAG_ID = "tag_id";
    }

    public static final class MediaMeta implements BaseColumns, MediaMetaColumns, MediaMetaStateColumns {
        public static final String TAB = "mediameta";

    }

    public static final class Set implements BaseColumns, SetColumns {
        public static final String TAB = "metaset";

    }

    public static final class SetMap implements BaseColumns, SetMapColumns {
        public static final String TAB = "setmap";

    }

    public static final class SetMapView implements BaseColumns, SetMapColumns, MediaMetaColumns {
        public static final String TAB = "setmapview";

    }

    public static final class SetView implements BaseColumns, SetColumns {
        public static final String TAB = "setview";
        public static final String NUMBER_OF_META = "number_of_meta";

    }
}
