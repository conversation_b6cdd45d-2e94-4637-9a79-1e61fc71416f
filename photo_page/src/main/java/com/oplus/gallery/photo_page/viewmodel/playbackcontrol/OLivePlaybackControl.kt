/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OLivePlaybackControl.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/03/08
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** tanluya<PERSON>@Apps.Gallery          2024/03/08      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.playbackcontrol

import android.view.SurfaceView
import com.oplus.gallery.common.playbackcontrol.IPhotoPlaybackControl
import com.oplus.gallery.common.playbackcontrol.PlaybackRequest
import com.oplus.gallery.common.playbackcontrol.PlaybackState
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.codec.player.AVController

/**
 * 播放控制包装类，实现OlivePhoto视频的播控控制
 * @param playerController AVController 一般为播放器
 * @param playerEventListener 播放相关信息的回调Listener
 * @param audioController 音频控制器
 */
internal open class OLivePlaybackControl(
    protected var playerController: AVController = AVController.EMPTY,
    private val playerEventListener: AVController.OnEventListener?,
) : IPhotoPlaybackControl {

    open val tag: String = TAG

    /**
     * 当前播放器状态
     * ```
     * true: 此播放器正在播放，允许进行界面交互和控制
     * false: 此播放器停止播放，解除和界面的关联，不响应界面控制
     * ```
     */
    override var isActivated: Boolean = false
    override var shouldResetToStartWhenEnd: Boolean = false
    override val playerToken: Int get() = playerController.playerToken()

    private var playbackRequest = PlaybackRequest.INITIALIZE

    private val internalPlayerEventListener = object : AVController.OnEventListener {

        override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
            if (playerController != avController) {
                GLog.d(tag, "[onError] received move out AVController")
                return
            }

            if (!isActivated) {
                GLog.d(tag, "[onError] avController not active")
                return
            }

            // 播放异常回调到外部
            playerEventListener?.onError(avController, what, extra, details)

            // do nothing
            GLog.d(tag, "[onError] what=$what, extra=$extra, details=$details")
        }

        override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            if (playerController != avController) {
                GLog.d(tag, "[onInfo] received move out AVController")
                return
            }

            if (!isActivated) {
                GLog.d(tag, "[onInfo] avController not active")
                return
            }
            // 播放信息回调到外部
            playerEventListener?.onInfo(avController, what, extra, details)
        }

        override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
            if (playerController != avController) {
                GLog.d(tag, "[onPlaybackStateChanged] received moved out AVController")
                return
            }

            if (!isActivated) {
                GLog.d(tag, "[onPlaybackStateChanged] avController not active")
                return
            }

            GLog.d(tag, "[onPlaybackStateChanged] state=$state, avController = $avController")

            // 状态改变回调到外部
            playerEventListener?.onPlaybackStateChanged(avController, state)
        }
    }

    /////////////////// 以下为对外方法 //////////////////////

    override fun changePlayerState(playState: PlaybackState, requestState: PlaybackRequest?) {
        when (playState) {
            PlaybackState.PLAYING -> {
                GLog.d(tag, "[play] playerController=$playerController")
                playerController.play(playAfterPrepare = true, needPrepare = true)
            }

            PlaybackState.PAUSED -> {
                GLog.d(tag, "[pause] playerController=$playerController")
                playerController.pause()
            }

            PlaybackState.STOP -> {
                GLog.d(tag, "[stop]")
                playerController.stop()
            }

            else -> GLog.d(TAG, "changePlayerState, ignore playbackState: $playState")
        }
        requestState?.apply {
            playbackRequest = this
        }
    }


    override fun seekTo(position: Long, seekType: AVController.SeekType?) {
        val actualSeekType = AVController.SeekType.NORMAL
        GLog.d(tag) { "[seekTo] position=$position, pendingSeekType=$seekType, actualSeekType=$actualSeekType" }
        playerController.seekTo(position = position, seekType = actualSeekType)
    }

    override fun getDuration(): Long = playerController.getDuration()

    override fun getCurrentPosition(): Long = playerController.getCurrentPosition()

    override fun isPlaying(): Boolean = playerController.isPlaying()

    override fun isPlayerSeeking(): Boolean = false

    override fun setMute(isMute: Boolean) {
        GLog.d(tag, "[setMute] isMute: $isMute")
        playerController.setMute(isMute)
    }

    override fun isMute(): Boolean = playerController.isMute()

    override fun setLooping(looping: Boolean) = playerController.setLooping(looping)

    override fun setSpeedRate(speedRate: Float) = playerController.setSpeedRate(speedRate)

    override fun getCurrentPlayingInfo(): AVController.PlayingInfo = playerController.getCurrentPlayingInfo()

    override fun enableMiniView(enable: Boolean) = playerController.enableMiniView(enable)

    override fun setVideoScalingMode(mode: Int) = playerController.setVideoScalingMode(mode)

    override fun setSurfaceView(surfaceView: SurfaceView) = playerController.setSurfaceView(surfaceView)

    override fun enterPreviewSeeking() = Unit

    override fun exitPreviewSeeking() = Unit

    override fun notifyWindowFocusChanged(isFocused: Boolean, isForceToContinuePlaying: Boolean) {
        GLog.d(tag, "[notifyWindowFocusChanged] isFocused=$isFocused")
        //对于Olive来说 只要窗口焦点变化了，直接暂停
        changePlayerState(PlaybackState.PAUSED, null)
    }

    override fun isInvalidPlayer(): Boolean = playerController == AVController.EMPTY

    override fun recordPosition(position: Long?) = Unit

    override fun clearCurrentPlayer(avController: AVController?) {
        GLog.d(tag, "[clearCurrentPlayer] current=$playerController, expect=$avController")

        if (isInvalidPlayer()) {
            return
        }
        if ((avController != null) && (avController != playerController)) {
            GLog.d(tag, "[clearCurrentPlayer] ignored, current player not equals expect player")
            return
        }
        /*
         * 这里调整为先删除回调再暂停吧：changePlayerState在播放会抛到子线程处理，接着再删除internalPlayerEventListener话回调存在不稳定性：
         * 1.主线程卡住，子线程很欢 internalPlayerEventListener就回调了
         * 2.主线程和子线程都正常执行时，主线程一般情况下就跑的快，internalPlayerEventListener就没回调
         * 故清理播放器统一不回调
         */
        playerController.removeOnEventListener(internalPlayerEventListener)
        changePlayerState(PlaybackState.PAUSED, null)

        playerController = AVController.EMPTY
    }

    override fun changeCurrentPlayer(avController: AVController) {
        GLog.d(tag, "[changeCurrentPlayer] old=$playerController, new=$avController")
        if (playerController == avController) {
            return
        }
        clearCurrentPlayer()
        playerController = avController
        playbackRequest = PlaybackRequest.INITIALIZE
        playerController.addOnEventListener(internalPlayerEventListener, isSticky = false)
    }

    override fun shouldPlayAfterGetFocus(): Boolean = false

    companion object {
        private const val TAG = "OLivePlaybackControl"
    }
}