/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BasicToneInfoStruct.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/2/10
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/2/10      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.extend

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 青品冷暖&暗角 效果需要的参数
 * 【超光影-青品冷暖&暗角】需求新增青品冷暖&暗角效果，该效果需要在滤镜之后立刻做效果，且需要基于P3-HLG色域哦~
 *
 * 对应的key为：[EXTEND_KEY_FILTER_BASIC_TONE_INFO]
 *
 * 相机提供方案，方案见：访问密码 phhyxw
 * https://odocs.myoas.com/docs/m4kMLxpWXlTXmvqD?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
 */
class BasicToneInfoStruct(byteArray: ByteArray) : ExtendStruct<BasicToneInfo>(byteArray) {
    var version: Float32
    var vigEnable: Signed32
    var ditherTable: Array<Float32>? = null
    var str: Float32
    var dvig: Float32
    var maxValueIn: Signed32
    var tuneLutEnable: Signed32

    init {
        version = Float32()
        vigEnable = Signed32()
        ditherTable = array(arrayOfNulls<Float32>(DITHER_TABLE_SIZE))
        str = Float32()
        dvig = Float32()
        maxValueIn = Signed32()
        tuneLutEnable = Signed32()
    }

    override fun toData(): BasicToneInfo? {
        return runCatching {
            BasicToneInfo(
                version.get(),
                vigEnable.get() != BOOL_VALUE_FALSE,
                ditherTable?.get(),
                str.get(),
                dvig.get(),
                maxValueIn.get(),
                tuneLutEnable.get() != BOOL_VALUE_FALSE
            )
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "toData: is error." }
        }.getOrNull()
    }

    private companion object {
        private const val TAG = "BasicToneInfoStruct"

        /**
         * 抖动表是一个32*64的单通道的Lut表，具体见类注释中的方案
         */
        private const val DITHER_TABLE_SIZE = 32 * 64
        private const val BOOL_VALUE_FALSE = 0
    }
}

/**
 * 青品冷暖&暗角 色调 固定信息
 *
 * 对应的key为：[EXTEND_KEY_FILTER_BASIC_TONE_INFO]
 *
 * @param version 兼容性版本号
 * @param vigEnable 暗角开关
 * @param ditherTable 暗角抖动表，这里之所以用var是因为相机用的是竖图，但是写到扩展信息确实横图，所以这里需要后处理旋转数据之后再重新写入
 * @param str fStr参数，暗角计算的参数
 * @param dvig dvig参数，暗角计算的参数
 * @param maxValueIn 默认1023，暗角计算的参数
 * @param tuneLutEnable 饱和度对比度冷暖青品Lut并表开关
 */
data class BasicToneInfo(
    val version: Float,
    val vigEnable: Boolean,
    var ditherTable: FloatArray?,
    val str: Float,
    val dvig: Float,
    val maxValueIn: Int,
    val tuneLutEnable: Boolean
)

/**
 * 青品冷暖&暗角 色调 全部信息
 * @param basicToneInfo 青品冷暖&暗角 色调 固定信息
 * @param vigTable 一个13*17的单通道的lut表，内部值会依据相机拍照时暗角值不同而不固定；
 *                  这里之所以用var是因为相机用的是竖图，但是写到扩展信息确实横图，所以这里需要后处理旋转数据之后再重新写入
 *                  对应的key为：[EXTEND_KEY_FILTER_BASIC_TONE_VIG_TABLE]
 * @param lmtLutTable 青品冷暖需要的lut并表，内部值会依据相机拍照时青品/冷暖值不同而不固定；
 *                    对应的key为：[EXTEND_KEY_FILTER_BASIC_TONE_LMTLUT_TABLE]
 * @param ditherTableWidth 抖动表的宽，会因为原图的orientation不同而变化，所以需要业务层决策好传入算法插件
 * @param ditherTableHeight 抖动表的高，会因为原图的orientation不同而变化，所以需要业务层决策好传入算法插件
 * @param vigTableWidth 暗角表的宽，会因为原图的orientation不同而变化，所以需要业务层决策好传入算法插件
 * @param vigTableHeight 暗角表的高，会因为原图的orientation不同而变化，所以需要业务层决策好传入算法插件
 */
data class BasicToneSource(
    val basicToneInfo: BasicToneInfo,
    var vigTable: FloatArray?,
    val lmtLutTable: ByteArray?,
    val ditherTableWidth: Int,
    val ditherTableHeight: Int,
    val vigTableWidth: Int,
    val vigTableHeight: Int
)