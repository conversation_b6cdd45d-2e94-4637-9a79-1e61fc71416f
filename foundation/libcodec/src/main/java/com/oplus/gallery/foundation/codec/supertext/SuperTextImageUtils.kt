/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SuperTextImageUtils
 ** Description: SuperTextImageUtils
 ** Version: 1.0
 ** Date : 2022/5/19
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/05/19    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.codec.supertext

import android.text.TextUtils
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainerExt
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.io.File
import java.io.RandomAccessFile

internal object SuperTextImageUtils {

    private const val TAG = "SuperTextImageUtils"
    private const val READ_MODEL = "r"

    /**
     * 获取超级文本幻数
     *
     * @param filePath        文件路径
     * @param mediaId         媒体库_id，用来获取fd，如果不传入，需要用filePath从媒体库查找，有额外耗时
     * @param parseLengthByExtender 是否由FileExtender来解析文件的原始长度
     * @return 读取文件获取到的幻数（如果使用FileExtender解析，会先获取扩展信息的长度，文件长度减掉这个长度再去读文件的超级文本幻数）
     */
    @JvmStatic
    fun getMagicNumber(filePath: String, mediaId: Int = -1, parseLengthByExtender: Boolean = true): ByteArray {
        if (TextUtils.isEmpty(filePath)) {
            GLog.e(TAG, "hasMagicNumber filePath is null")
            return ByteArray(0)
        }
        runCatching {
            /**
             *  超级文本模式下拍的图片，可能同时写入扩展数据（水印可编辑后基本都会写入扩展数据）
             *  这种情况下，为保证正常读取超级文本，文件长度需要使用原始文件长度，不能使用文件的总长度
             */
            val originDataLength = if (parseLengthByExtender) {
                FileExtendedContainerExt.getOriginDataLength(ContextGetter.context, filePath, mediaId)
            } else {
                File(filePath).length().toInt()
            }
            RandomAccessFile(filePath, READ_MODEL).use {
                return getMagicNumber(it, originDataLength)
            }
        }.onFailure {
            GLog.e(TAG, "hasMagicNumber", it)
        }
        return ByteArray(0)
    }

    /**
     * 获取超级文本幻数
     *
     * @param randomAccessFile RandomAccessFile
     * @param originDataLength 原始文件长度（除去扩展文件部分）
     * @return 读取文件获取到的幻数，没有读到则返回空的byte数组
     */
    @JvmStatic
    fun getMagicNumber(randomAccessFile: RandomAccessFile, originDataLength: Int): ByteArray {
        runCatching {
            val magicNumber = ByteArray(SuperTextProperties.MAGIC_NUMBER_BYTE_LENGTH)
            // Magic number & SOT mOffset
            val contentEndPos: Int =
                (originDataLength - SuperTextProperties.SOT_OFFSET_BYTE_LENGTH - SuperTextProperties.MAGIC_NUMBER_BYTE_LENGTH)
            randomAccessFile.seek(contentEndPos.toLong())
            val readLength = randomAccessFile.read(magicNumber, 0, magicNumber.size)
            if (readLength < magicNumber.size) {
                GLog.w(TAG, "getMagicNumber,there may be a problem with the number of bytes read magicNumber.")
            }
            // Check super text validity
            return magicNumber
        }.onFailure {
            GLog.e(TAG, "getMagicNumber RandomAccessFile", it)
        }
        return ByteArray(0)
    }
}

