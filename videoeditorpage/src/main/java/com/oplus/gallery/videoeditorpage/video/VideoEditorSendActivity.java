/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - VideoEditorSendActivity.java
 ** Description: for share slow motion video.
 ** Version: 1.0
 ** Date : 2018/02/05
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                  <data>        <version>     <desc>
 **  <EMAIL>    2018/02/05    1.0           build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video;

import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.VIDEO_EDITOR_SEND_ACTIVITY;
import static com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.SDR_VIDEO_TYPE;
import static com.oplus.gallery.standard_lib.util.chrono.TimeUtils.getFormatDateTime;

import android.app.Activity;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.FileObserver;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.widget.Toast;

import com.coui.appcompat.theme.COUIThemeOverlay;
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter;
import com.oplus.gallery.foundation.database.util.MediaStoreUtils;
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.ui.dialog.ProgressDialog;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper;
import com.oplus.gallery.router_lib.annotations.RouterNormal;
import com.oplus.gallery.standard_lib.codec.SlowMotionVideoUtils;
import com.oplus.gallery.standard_lib.file.Dir;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.videoeditor.data.VideoSpecification;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.VideoBaseActivity;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.memories.util.VideoStorageHelper;
import com.oplus.gallery.videoeditorpage.memories.engine.GalleryVideoEngineListener;
import com.oplus.gallery.videoeditorpage.memories.engine.GalleryVideoEngineManager;

import java.lang.ref.WeakReference;


@RouterNormal(path = VIDEO_EDITOR_SEND_ACTIVITY)
public class VideoEditorSendActivity extends VideoBaseActivity implements MeicamEngineLimiter.LimitAble {

    /**
     * 慢动作默认FPS
     */
    public static final int SLOW_MOTION_DEFAULT_FPS = 30;
    private static final String TAG = "VideoEditorSendActivity";
    private static final String DEFAULT_VIDEO_FILE_NAME_HEAD = "SLOW_";
    private static final String DEFAULT_VIDEO_SUFFIX = ".mp4";
    private static final String DEFAULT_VIDEO_SUFFIX_TMP = ".videoedit";
    private static final String PATH_SLASHES_SEPARATION = "/";

    private static final int COMPILE_VIDEO_MAX = 100;
    private static final int NUM_0 = 0;
    private static final int NUM_1 = 1;
    private static final int NUM_2 = 2;
    private static final int NUM_3 = 3;
    private static final int NUM_4 = 4;
    private static final int NUM_5 = 5;
    /**
     * slow motion title example: 0slow_motion_hsr_120:12,220,300,900 or op**_0slow_motion_hsr_120:12,220,300,900
     * 对标题使用":"分割，对分割后的前一个字符串使用"_"分割，有”op**_“前缀时得到5个字符串，没有”op**_“前缀时得到4个
     */
    private static final int SLOW_MOTION_FPS_FOUR_PARTS = 4;
    private static final int SLOW_MOTION_FPS_FIVE_PARTS = 5;
    private static final int SUPPORT_SLOW_MOTION_FPS_120 = 120;
    private static final int SUPPORT_SLOW_MOTION_FPS_240 = 240;
    private static final int SUPPORT_SLOW_MOTION_FPS_480 = 480;
    private static final long ENSURE_SPACE_MB = 100 * 1024 * 1024;
    private static final int SLOW_MOTION_VIDEO_PREVIEW_MAX_HEIGHT = 720;

    private GalleryVideoEngineManager mGalleryVideoEngine;
    private ProgressDialog mSaveProgressDialog;
    private SlowMotionFileObserver mVideoFileObserver;
    private File mCurSaveDir;
    private Toast mToast;
    private ExportHandler mExportHandler;
    private String mVideoPath;
    private String mVideoUri;
    private String mVideoTitle;
    private long mDateTaken;
    private String mOldFileName;
    private String mVideoFileName;
    private long mVideoDuration;
    private long mSlowAEnter = 0;
    private long mSlowAOut = 0;
    private long mSlowBEnter = 0;
    private long mSlowBOut = 0;
    private int mVideoWidth = 0;
    private int mVideoHeight = 0;
    private int mFPS = SLOW_MOTION_DEFAULT_FPS;
    private boolean mIsFullSlow = false;
    private boolean mIsHfrSlowMotion = false;
    private boolean mIsHsrSlowMotion = false;
    private Uri mMediaUri = null;
    private GalleryVideoEngineListener mGalleryVideoEngineListener = new GalleryVideoEngineListener() {

        @Override
        public void onPlayStatusChange(int status) {
            // Do nothing
        }

        @Override
        public void onPlayPositionChange(long position) {
            // Do nothing
        }

        @Override
        public void onExportStatusChange(int state) {
            switch (state) {
                case GalleryVideoEngineManager.EXPORT_STATUS_ERROR:
                    if (mSaveProgressDialog.isShowing()) {
                        mSaveProgressDialog.dismiss();
                    }
                    int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
                    if (null != VideoStorageHelper.checkStorageEnough(VideoEditorSendActivity.this,
                            VideoStorageHelper.VideoType.VIDEO_EDITOR, ENSURE_SPACE_MB, Dir.getCAMERA().getRelativePath(), tipRes)) {
                        showToast(getString(R.string.videoeditor_editor_cancel_compile_space_not_enough));
                    } else {
                        showToast(getString(R.string.videoeditor_editor_cancel_compile_error));
                    }
                    break;
                case GalleryVideoEngineManager.EXPORT_STATUS_COMPLETE:
                    if (updateSavedFile()) {
                        MediaStoreScannerHelper.scanFileByMediaStoreSingle(getApplicationContext(),
                                mCurSaveDir + PATH_SLASHES_SEPARATION + mVideoFileName, new SingleVideoScanCompleteListener());
                    }
                    break;
                case GalleryVideoEngineManager.EXPORT_STATUS_CANCEL:
                    showToast(getString(R.string.videoeditor_editor_cancel_compile_cancel));
                    break;
                default:
                    break;
            }
        }

        class SingleVideoScanCompleteListener implements MediaStoreScannerHelper.SingleFileScanCompleteListener {
            @Override
            public void onScanCompleted() {
                mExportHandler.removeMessages(ExportHandler.MSG_SEND);
                mExportHandler.sendEmptyMessage(ExportHandler.MSG_SEND);
            }
        }

        private boolean updateSavedFile() {
            if (TextUtils.isEmpty(mVideoFileName)) {
                GLog.w(TAG, "updateSavedFile mVideoFileName is empty!");
                return false;
            }

            File mediaFile = new File(mCurSaveDir, mVideoFileName);
            if (!mediaFile.exists()) {
                GLog.e(TAG, "updateSavedFile failed. the file does not exists, mediaFile = " + mediaFile);
                return false;
            }
            // 美摄sdk写文件是通过fuse，先插入媒体库记录，再通过fuse写文件时会产生另外的新记录，之后如果再更新媒体库记录就会因为冲突把记录删除，需要媒体库扫描
            mMediaUri = MediaStoreScannerHelper.scanFileByMediaStoreSingle(getApplicationContext(), mediaFile.getAbsolutePath());
            if (mMediaUri != null) {
                VideoEditorHelper.updateLocalMedia(getApplicationContext(), mMediaUri, mediaFile);
                return true;
            }
            GLog.e(TAG, "updateSavedFile failed");
            return false;
        }

        @Override
        public void onExportProgressChange(int progress) {
            setDialogProgress(progress);
        }

        @Override
        public void onEngineStateChanged(int state) {
            // Do nothing
        }

        @Override
        public void onFirstVideoFrameReady() {
            // Do nothing
        }

        @Override
        public void onEngineException(int errCode, String msg) {
            GLog.e(TAG, "onEngineException errCode = " + errCode + ", msg = " + msg);
        }
    };

    @Override
    public void loadMain() {
        MeicamEngineLimiter.getInstance().register(this);
        mGalleryVideoEngine = new GalleryVideoEngineManager(VideoEditorSendActivity.this, mGalleryVideoEngineListener);
    }

    @Override
    public void onCreateCheck() {
        super.onCreateCheck();
        if (updateVideo()) {
            if (mIsHfrSlowMotion) {
                GLog.d(TAG, "onCreateCheck() mIsHfrSlowMotion, start share");
                mVideoFileName = mVideoPath;
                startShareActivity();
                return;
            } else if (initEngine() && initSlowMotion()) {
                initFileObserver();
            } else {
                GLog.e(TAG, "onCreateCheck() initEngine or initSlowMotion error!");
                finish();
                return;
            }
        } else {
            finish();
            GLog.e(TAG, "onCreateCheck() uri is null");
            return;
        }
        mExportHandler = new ExportHandler();
        createVideoFileForSend();
    }

    private boolean initEngine() {
        if (!mGalleryVideoEngine.initVideoFileInfo(mVideoUri, mVideoPath, mIsHfrSlowMotion, SDR_VIDEO_TYPE)) {
            return false;
        } else {
            mVideoWidth = mGalleryVideoEngine.getVideoFileWidth();
            mVideoHeight = mGalleryVideoEngine.getVideoFileHeight();
        }
        boolean succeed = mGalleryVideoEngine.initEngine(null,
                new VideoSpecification(
                        mVideoWidth,
                        mVideoHeight,
                        SLOW_MOTION_DEFAULT_FPS
                ), false
        );
        if (succeed) {
            mGalleryVideoEngine.setPreviewMaxHeight(SLOW_MOTION_VIDEO_PREVIEW_MAX_HEIGHT);
        }
        return succeed;
    }

    private boolean initSlowMotion() {
        if (mIsHfrSlowMotion) {
            return mGalleryVideoEngine.addHfrFullSlowMotionVideoClip(mVideoUri, mVideoPath, mDateTaken, SLOW_MOTION_DEFAULT_FPS, SLOW_MOTION_DEFAULT_FPS);
        } else if (mIsHsrSlowMotion) {
            if (mSlowBEnter > 0) {
                return mGalleryVideoEngine.addSlowMotionVideoClip(mVideoUri, mVideoPath, mDateTaken, mFPS,
                        new long[]{mSlowAEnter, mSlowAOut, mSlowBEnter, mSlowBOut}, mIsFullSlow);
            } else {
                return mGalleryVideoEngine.addSlowMotionVideoClip(mVideoUri, mVideoPath, mDateTaken, mFPS,
                        new long[]{mSlowAEnter, mSlowAOut}, mIsFullSlow);
            }
        }
        return false;
    }


    private void createVideoFileForSend() {
        if (checkAlreadyExported()) {
            GLog.d(TAG, "createVideoFileForSend() checkAlreadyExported true");
            mExportHandler.removeMessages(ExportHandler.MSG_SEND);
            mExportHandler.sendEmptyMessage(ExportHandler.MSG_SEND);
            return;
        }
        if ((mSaveProgressDialog != null) && (mSaveProgressDialog.isShowing())) {
            return;
        }

        String dateTime = getFormatDateTime();
        if (TextUtils.isEmpty(dateTime)) {
            GLog.w(TAG, "createVideoFileForSend dateTime is null");
            return;
        }
        String tempName = DEFAULT_VIDEO_FILE_NAME_HEAD + mOldFileName;
        mVideoFileName = tempName;

        VideoStorageHelper.deleteOldVideoFile(VideoEditorSendActivity.this, VideoStorageHelper.VideoType.VIDEO_EDITOR, mVideoFileName);
        int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
        mCurSaveDir = VideoStorageHelper.checkStorageEnough(VideoEditorSendActivity.this,
                VideoStorageHelper.VideoType.VIDEO_EDITOR, ENSURE_SPACE_MB, Dir.getCAMERA().getRelativePath(), tipRes);
        if (mCurSaveDir == null) {
            GLog.w(TAG, "createVideoFileForSend storage not enough");
            return;
        }
        File file = new File(mCurSaveDir, mVideoFileName);
        file = FilePathUtils.switchToPublicDir(file, false);
        long start = mGalleryVideoEngine.getTrimInTime();
        long end = mGalleryVideoEngine.getTotalTime();
        float[] slowList = mGalleryVideoEngine.getSlowMotionList();
        long total = mGalleryVideoEngine.getTotalTime();
        GLog.d(TAG, "createVideoFileForSend, mSupportSlowMotion old time start: " + start + " end:" + end);
        if (mGalleryVideoEngine.isHfrSlowMotion()) {
            start = SlowMotionVideoUtils.getHFRSlowMotionTime(start, slowList, total, SLOW_MOTION_DEFAULT_FPS) / mFPS;
            end = SlowMotionVideoUtils.getHFRSlowMotionTime(end, slowList, total, SLOW_MOTION_DEFAULT_FPS / mFPS);
        } else {
            start = SlowMotionVideoUtils.getHSRSlowMotionTime(start, slowList, total, mFPS / SLOW_MOTION_DEFAULT_FPS);
            end = SlowMotionVideoUtils.getHSRSlowMotionTime(end, slowList, total, mFPS / SLOW_MOTION_DEFAULT_FPS);
        }
        GLog.d(TAG, "createVideoFileForSend, mVideoFileName = " + mVideoFileName
                + ", file = " + file
                + " start time:" + start
                + " end time:" + end);
        mMediaUri = VideoEditorHelper.insertVideoSandbox(getApplicationContext(), file, mVideoFileName, System.currentTimeMillis());
        if (mMediaUri != null) {
            mGalleryVideoEngine.saveShareVideo(mMediaUri.toString(), start, end, mVideoHeight);
        }
        showProgressDialog();
        Window window = mSaveProgressDialog.getWindow();
        if (window != null) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            window.getDecorView().setAccessibilityDelegate(new View.AccessibilityDelegate() {
                @Override
                public void sendAccessibilityEvent(View view, int i) {
                    if (i != AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
                        super.sendAccessibilityEvent(view, i);
                    }
                }
            });
        }
    }

    private boolean checkAlreadyExported() {
        String fileName = DEFAULT_VIDEO_FILE_NAME_HEAD + mOldFileName;
        int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
        VideoStorageHelper.checkStorageEnough(VideoEditorSendActivity.this, VideoStorageHelper.VideoType.VIDEO_EDITOR, ENSURE_SPACE_MB, Dir.getCAMERA().getRelativePath(), tipRes);
        File checkFile = FileOperationUtils.checkFileAlreadyExit(fileName, Dir.getCAMERA().getRelativePath());
        if (checkFile != null) {
            mVideoFileName = fileName;
            mCurSaveDir = checkFile.getParentFile();
            GLog.d(TAG, "checkAlreadyExported() mVideoFileName:" + mVideoFileName + " mCurSaveDir:" + mCurSaveDir);
            return true;
        }
        return false;
    }

    private void initFileObserver() {
        if (!TextUtils.isEmpty(mVideoPath)) {
            File file = new File(mVideoPath);
            String parentPath = file.getParent();
            String fileName = file.getName();
            if (!TextUtils.isEmpty(parentPath) && !TextUtils.isEmpty(fileName)) {
                mOldFileName = fileName;
                mVideoFileObserver = new SlowMotionFileObserver(this, parentPath, fileName, FileObserver.DELETE);
                mVideoFileObserver.startWatching();
            }
        }
    }

    private void showProgressDialog() {
        if (mSaveProgressDialog == null) {
            ProgressDialog.Builder builder = new ProgressDialog.Builder(this, true);
            COUIThemeOverlay.getInstance().applyThemeOverlays(builder.getContext());
            builder.setTitle(R.string.videoeditor_editor_cancel_exporting);
            builder.setCancelable(false);
            builder.setPositiveButton(
                    getResources().getString(android.R.string.cancel),
                    (dialog, whichButton) -> {
                        cancelSaveVideo();
                        if (mGalleryVideoEngine != null) {
                            mGalleryVideoEngine.stop(true);
                        }
                        mExportHandler.removeMessages(ExportHandler.MSG_FINISH);
                        mExportHandler.sendEmptyMessageDelayed(ExportHandler.MSG_FINISH, ExportHandler.FINISH_DELAY);
                    });
            mSaveProgressDialog = builder.build().show();
            mSaveProgressDialog.setMaxProgress(COMPILE_VIDEO_MAX);
        } else if (!mSaveProgressDialog.isShowing()) {
            mSaveProgressDialog.show();
        }
    }

    private void setDialogProgress(int progress) {
        if (null != mSaveProgressDialog) {
            mSaveProgressDialog.setProgress(progress);
        }
    }

    public void cancelSaveVideo() {
        try {
            File mediaFile = null;
            if (!TextUtils.isEmpty(mVideoFileName)) {
                mediaFile = new File(mCurSaveDir, mVideoFileName);
            }
            GLog.d(TAG, "cancelSaveVideo, mediaFile = " + mediaFile);
            if ((mediaFile != null) && mediaFile.exists()) {
                GLog.d(TAG, "cancelSaveVideo, delete file.");
                if (mMediaUri != null) {
                    int result = getContentResolver().delete(mMediaUri, null, null);
                    if (result <= 0) {
                        GLog.w(TAG, "cancelSaveVideo, delete file failed. mMediaUri = " + mMediaUri);
                    }
                }
            }
            mCurSaveDir = null;
            mVideoFileName = null;
        } catch (Exception e) {
            GLog.w(TAG, "cancelSaveVideo, Exception:", e);
        }
    }

    @Override
    public void onStopCheck() {
        super.onStopCheck();
        if ((mSaveProgressDialog != null) && mSaveProgressDialog.isShowing()) {
            cancelSaveVideo();
        }
        finish();
    }

    public void startShareActivity() {
        GLog.d(TAG, "startShareActivity()");
        if (TextUtils.isEmpty(mVideoFileName)) {
            GLog.w(TAG, "startShareActivity mVideoFileName is null");
            return;
        }
        File mediaFile = new File(mCurSaveDir, mVideoFileName);
        if (!mediaFile.exists()) {
            GLog.e(TAG, "startShareActivity failed. the file does not exists, mediaFile = " + mediaFile);
            return;
        }

        Uri fileUri = MediaStoreUriHelper.getUriFromFile(getApplicationContext(), mediaFile);
        if (fileUri == null) {
            fileUri = GalleryFileProvider.fromFile(getApplicationContext(), mediaFile);
        }
        if (fileUri == null) {
            GLog.e(TAG, "startShareActivity failed. fileUri is null, mediaFile = " + mediaFile);
            return;
        }
        VideoEditorHelper.startShareVideoActivity(VideoEditorSendActivity.this, fileUri);
        mCurSaveDir = null;
        mVideoFileName = null;
    }


    @Override
    public void onResumeCheck() {
        super.onResumeCheck();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (!TextUtils.isEmpty(mVideoFileName)) {
            String tempName = (DEFAULT_VIDEO_FILE_NAME_HEAD + mOldFileName).replace(DEFAULT_VIDEO_SUFFIX, DEFAULT_VIDEO_SUFFIX_TMP);
            GLog.w(TAG, "onDestroy() mVideoFileName not empty, delete temp file:" + tempName);
            VideoStorageHelper.deleteOldVideoFile(VideoEditorSendActivity.this, VideoStorageHelper.VideoType.VIDEO_EDITOR, tempName);
        }

        if (mGalleryVideoEngine != null) {
            mGalleryVideoEngine.destroy(!mIsLimited);
        }
        if (mSaveProgressDialog != null) {
            Window window = mSaveProgressDialog.getWindow();
            if (window != null) {
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            }
            if (mSaveProgressDialog.isShowing()) {
                mSaveProgressDialog.dismiss();
            }
        }
        if (mExportHandler != null) {
            mExportHandler.removeCallbacksAndMessages(null);
        }
        MeicamEngineLimiter.getInstance().unregister(this);
    }

    private boolean updateVideo() {
        Intent data = getIntent();
        Uri uri = data.getData();
        if (uri == null) {
            GLog.e(TAG, "updateVideo() uri is null");
            return false;
        }
        mVideoUri = uri.toString();
        if ("content".equalsIgnoreCase(uri.getScheme())) {
            Cursor c = null;
            try {
                c = this.getContentResolver().query(uri,
                        new String[]{
                                MediaStoreUtils.DATA,
                                MediaStore.Video.Media.DURATION,
                                MediaStore.Video.Media.TITLE,
                                MediaStore.Video.Media.DATE_TAKEN
                        }, null, null, null);
                if (VideoEditorHelper.checkCursorValid(c)) {
                    c.moveToFirst();
                    mVideoPath = c.getString(NUM_0);
                    mVideoDuration = c.getInt(NUM_1);
                    mVideoTitle = c.getString(NUM_2);
                    mDateTaken = c.getLong(NUM_3);
                }
            } catch (Exception e) {
                GLog.e(TAG, "updateVideo(), failed!" + e);
                return false;
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        }

        if (TextUtils.isEmpty(mVideoPath)) {
            return false;
        }
        boolean isSlowMotion = false;
        if (!TextUtils.isEmpty(mVideoTitle)) {
            // slow motion title example: _0slow_motion_hsr_120:12,220,300,900
            GLog.d(TAG, "updateVideo(), slow motion mTitle:" + mVideoTitle);
            if (mVideoTitle.toLowerCase().contains(SlowMotionVideoUtils.HSR_SLOW_MOTION_SUFFIX)) {
                mIsHsrSlowMotion = true;
            } else if (mVideoTitle.toLowerCase().contains(SlowMotionVideoUtils.HFR_SLOW_MOTION_SUFFIX)) {
                mIsHfrSlowMotion = true;
            }
            if (mIsHsrSlowMotion || mIsHfrSlowMotion) {
                try {
                    String[] motion = mVideoTitle.split(":");
                    if (motion.length == NUM_2) {
                        String[] time = motion[NUM_1].split(",");
                        String[] fps = motion[NUM_0].split("_");
                        if ((time.length == NUM_4) && ((fps.length == SLOW_MOTION_FPS_FIVE_PARTS) || (fps.length == SLOW_MOTION_FPS_FOUR_PARTS))) {
                            mSlowAEnter = Long.parseLong(time[NUM_0]);
                            mSlowAOut = Long.parseLong(time[NUM_1]);
                            mSlowBEnter = Long.parseLong(time[NUM_2]);
                            mSlowBOut = Long.parseLong(time[NUM_3]);
                            mFPS = Integer.parseInt(fps[fps.length - 1]);
                            GLog.d(TAG, "updateVideo()"
                                    + " mFPS:" + mFPS
                                    + " mSlowAEnter:" + mSlowAEnter
                                    + " mSlowAOut:" + mSlowAOut
                                    + " mSlowBEnter:" + mSlowBEnter
                                    + " mSlowBOut:" + mSlowBOut);
                            if ((mFPS != SUPPORT_SLOW_MOTION_FPS_120)
                                    && (mFPS != SUPPORT_SLOW_MOTION_FPS_240)
                                    && (mFPS != SUPPORT_SLOW_MOTION_FPS_480)) {
                                isSlowMotion = false;
                                mIsHfrSlowMotion = false;
                                mIsHsrSlowMotion = false;
                                mFPS = SLOW_MOTION_DEFAULT_FPS;
                            } else {
                                isSlowMotion = true;
                                /*
                                because the slow time  writeln by camera into database may be difference with
                                the time writeln by media coder into the file
                                we need adjust incorrect slow motion time
                                */
                                if (mSlowAEnter >= mVideoDuration) {
                                    GLog.w(TAG, "updateVideo() mSlowAEnter >= mVideoDuration");
                                    mSlowAEnter = 0;
                                    mSlowAOut = 0;
                                    mSlowBEnter = 0;
                                    mSlowBOut = 0;
                                } else if (mSlowAOut >= mVideoDuration) {
                                    GLog.w(TAG, "updateVideo() mSlowAOut >= mVideoDuration");
                                    mSlowAOut = mVideoDuration;
                                    mSlowBEnter = 0;
                                    mSlowBOut = 0;
                                } else if (mSlowBEnter >= mVideoDuration) {
                                    GLog.w(TAG, "updateVideo() mSlowBEnter >= mVideoDuration");
                                    mSlowBEnter = 0;
                                    mSlowBOut = 0;
                                } else if (mSlowBOut >= mVideoDuration) {
                                    GLog.w(TAG, "updateVideo() mSlowBOut >= mVideoDuration");
                                    mSlowBOut = mVideoDuration;
                                }

                                mIsFullSlow = (mSlowAEnter == 0L) && (mSlowAOut == 0L) && (mSlowBEnter == 0L) && (mSlowBOut == 0L);
                            }
                        }
                    }
                } catch (Exception e) {
                    GLog.e(TAG, "updateVideo() check slow motion error:" + e);
                    isSlowMotion = false;
                    mIsHfrSlowMotion = false;
                    mIsHsrSlowMotion = false;
                    mFPS = SLOW_MOTION_DEFAULT_FPS;
                }
            }
        }
        GLog.d(TAG, "updateVideo() uri:" + uri
                + " mVideoDuration:" + mVideoDuration
                + " mVideoTitle:" + mVideoTitle
                + " isSlowMotion:" + isSlowMotion
                + " mIsFullSlow:" + mIsFullSlow);
        return isSlowMotion;
    }

    private void showToast(String msg) {
        if (mToast == null) {
            mToast = Toast.makeText(getApplicationContext(), "", Toast.LENGTH_SHORT);
        }
        mToast.setText(msg);
        mToast.show();
    }

    private static final class SlowMotionFileObserver extends FileObserver {
        private WeakReference<Activity> mActivity;
        private String mFileName;

        private SlowMotionFileObserver(Activity activity, String dir, String fileName, int event) {
            super((new File(dir)).getFile(), event);
            mActivity = new WeakReference<>(activity);
            mFileName = fileName;
        }

        @Override
        public void onEvent(int event, String path) {
            switch (event) {
                case FileObserver.DELETE:
                    Activity activity = mActivity.get();
                    if (activity != null) {
                        if (TextUtils.equals(mFileName, path)) {
//                            GLog.d(TAG, "SlowMotionFileObserver  delete editing file : " + path);
                            activity.finish();
                        }
                    } else {
                        GLog.w(TAG, "SlowMotionFileObserver  activity destroyed, stop watching");
                        stopWatching();
                    }
                    break;

                default:
                    break;
            }
        }
    }

    private class ExportHandler extends Handler {

        static final int MSG_SEND = 1;
        static final int MSG_FINISH = 2;
        static final int EXPORT_SEND_DELAY = 800;
        static final int FINISH_DELAY = 100;

        public ExportHandler() {
            super(Looper.myLooper());
        }

        @Override
        public void handleMessage(Message message) {
            switch (message.what) {
                case MSG_SEND:
                    if ((mSaveProgressDialog != null) && mSaveProgressDialog.isShowing()) {
                        mSaveProgressDialog.dismiss();
                    }
                    startShareActivity();
                    finish();
                    break;
                case MSG_FINISH:
                    finish();
                    break;
                default:
                    break;
            }
        }
    }
}
