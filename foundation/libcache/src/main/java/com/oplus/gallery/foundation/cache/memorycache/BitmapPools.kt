/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BitmapPools.kt
 ** Description : Bitmap复用池
 ** Version     : 1.0
 ** Date        : 2023/03/07
 ** Author      : zhongxuechang@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D             2023/03/07   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cache.memorycache

import android.content.ComponentCallbacks2
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.standard_lib.app.AppConstants.Capacity.MEM_10M
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.LruBitmapPool
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.concurrent.CopyOnWriteArrayList

object BitmapPools {

    private const val TAG = "BitmapPools"
    /**
     * 缓存池分级配置，由外部设置进来保存在这里
     */
    private val configList = CopyOnWriteArrayList<PoolConfig>()

    /**
     * 全局缓存池，每个级别保存一个全局实例，存储该级别的Bitmap
     */
    private val globalPools = mutableListOf<IBitmapPool>()

    init {
        ContextGetter.context.registerComponentCallbacks(object : ComponentCallbacks2 {
            override fun onConfigurationChanged(newConfig: Configuration) {
                // Do nothing.
            }

            override fun onLowMemory() {
                GLog.d(TAG, DL) { "onLowMemory" }
                clear()
            }

            override fun onTrimMemory(level: Int) {
                GLog.d(TAG, DL) { "onTrimMemory" }
                trimMemory(level)
            }
        })
    }

    /**
     * 初始化全局缓存池
     * @param configs 缓存池信息
     */
    fun setPoolConfig(configs: List<PoolConfig>) {
        if (configs.isEmpty()) {
            GLog.w(TAG, DL) { "setPoolConfig, configs is empty" }
            return
        }

        configList.addAll(configs)
    }

    /**
     * 创建缓存池
     * @param config 缓存池信息
     */
    @JvmStatic
    fun createPool(config: PoolConfig): IBitmapPool {
        return createPoolInternal((if (config.checkConfigValid()) config else IBitmapPool.DEFAULT_POOL_CONFIG))
    }

    private fun createPoolInternal(config: PoolConfig): IBitmapPool {
        return LruBitmapPool(config.tag, config.totalMaxSize, config.singleMaxSize, config.idleMaxSize, config.maxReuseMultiple)
    }

    /**
     * copy一个Bitmap
     * 1.优先从Bitmap缓存池中找到一个与[src]同宽高的可复用的Bitmap.
     *     找不到则使用Bitmap原生的copy方式复制Bitmap
     * 2.使用[BitmapUtils]提供的从一个Bitmap复制到另一个Bitmap
     * 3.如果还是不成功则使用Bitmap原生的copy方式复制Bitmap
     */
    @JvmStatic
    fun copyBitmap(src: Bitmap?): Bitmap? {
        src ?: return null
        if (src.isRecycled) return null
        getBitmap(src.width, src.height, src.config)?.let { reusableBitmap ->
            if (BitmapUtils.copyBitmapTo(src, reusableBitmap)) {
                return reusableBitmap
            } else {
                recycle(reusableBitmap)
            }
        }

        return BitmapUtils.copyBitmap(src)
    }

    /**
     * 根据宽高，按ARGB_8888编码算出图片会占用多少内存，单位字节
     * @param width 图片宽
     * @param height 图片高
     * @return 占用内存大小
     */
    @JvmStatic
    fun getBitmapSize(width: Int, height: Int): Long = (width * height * BitmapUtils.PER_PIXEL_SIZE_ARGB).toLong()

    /**
     * 用图像数据生成bitmap
     * @param buffer 图像数据
     * @param options bitmap读取配置，只作复用，里面设置的值不会被使用
     * @param width 图像宽，不传则自行解析图像数据，建议传
     * @param height 图像高，不传则自行解析图像数据，建议传
     */
    @JvmStatic
    fun decode(buffer: BytesBuffer, options: BitmapFactory.Options? = null, width: Int = 0, height: Int = width): Bitmap? {
        return decode(buffer.data, buffer.offset, buffer.length, options, width, height)
    }

    /**
     * 用图像数据生成bitmap
     * @param data 图像数据
     * @param offset data偏移值
     * @param length data有效数据长度
     * @param options bitmap读取配置，只作复用，里面设置的值不会被使用
     * @param width 图像宽，不传则自行解析图像数据，建议传
     * @param height 图像高，不传则自行解析图像数据，建议传
     */
    @JvmStatic
    fun decode(
        data: ByteArray,
        offset: Int,
        length: Int,
        options: BitmapFactory.Options? = null,
        width: Int = 0,
        height: Int = width
    ): Bitmap? {
        val tmpOptions = IBitmapPool.adjustOptions(data, offset, length, options, width, height)
        val pool = selectPool(getBitmapSize(tmpOptions.outWidth, tmpOptions.outHeight)) ?: return null
        return pool.decodeInternal(data, offset, length, tmpOptions)
    }

    /**
     * 根据宽高要求，从缓存池取出一个可复用的bitmap
     * @param width 指定bitmap的宽
     * @param height 指定bitmap的高
     * @param config 指定bitmap的config格式
     */
    @JvmStatic
    fun getBitmap(width: Int, height: Int, config: Bitmap.Config?): Bitmap? =
        selectPool(getBitmapSize(width, height))?.getBitmap(width, height, config)

    /**
     * bitmap放入缓存池
     *
     * 如果开启 reconfigure debug，所有图片会被重新设置尺寸为 1x1。
     * 当执行下面命令后，非 1x1 尺寸的 [Bitmap] 便是暂未回收复用的，减少干扰
     *
     * 命令：
     *
     * `adb shell dumpsys meminfo com.coloros.gallery3d`
     *
     * 输出：
     *
     * ```shell
     * BITMAPS
     *     TotalSize: 130226 KB
     *     TotalCount: 130
     *     Detail Info(TOP10):
     *       width  height  type  count  total_size
     *        1024    1024     4     12       49152 KB
     *           1       1     4     12       26004 KB
     *         256     256     4     64       16384 KB
     *         720     960     4      4       10800 KB
     *         960     639     4      3        7196 KB
     *         768     960     4      1        2880 KB
     *         960     720     4      1        2700 KB
     *         560     960     4      1        2400 KB
     *         960     608     4      1        2280 KB
     * ```
     */
    @JvmStatic
    fun recycle(bitmap: Bitmap?) {
        bitmap ?: return

        // 是否已经回收，此处做健壮性处理，打印异常日志
        if (bitmap.isRecycled) {
            GLog.e(TAG, DL, IllegalStateException("Bitmap is recycled")) { "[recycle] ignore, bitmap is recycled" }
            return
        }

        // 如果找不到合适的 pool，则直接回收处理
        val pool = selectPool(bitmap.allocationByteCount.toLong()) ?: let {
            if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                GLog.e(TAG, LogFlag.DL) {
                    "[recycle] real recycle bitmap, hashCode=${bitmap.hashCode()}"
                }
            }
            bitmap.recycle()
            return
        }

        // Debug 时处理，如果开启 reconfigure debug，所有图片会被重新设置尺寸为 1x1，便于 debug
        if (GProperty.DEBUG_BITMAP_POOL_RECONFIGURE && bitmap.isMutable) {
            bitmap.reconfigure(1, 1, bitmap.getConfigSafely())
        }

        // 回收时，需要将bitmap对应的gainmap做清除处理。
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            bitmap.gainmap = null
        }
        pool.recycle(bitmap).also {
            if ((GProperty.PROPERTY_OTEST_RUNNING) || (GProperty.PROPERTY_MONKEY_RUNNING)) {
                GLog.e(TAG, LogFlag.DL) {
                    "[recycle] put bitmap to pool, hashCode=${bitmap.hashCode()}"
                }
            }
        }
    }

    /**
     * globalPools已按单张bitmap最大值做升序排序，优先使用小bitmap的缓存池
     */
    private fun selectPool(singleSize: Long): IBitmapPool? {
        if (globalPools.isEmpty() && configList.isNotEmpty()) {
            synchronized(globalPools) {
                GTrace.trace("initGlobalPools<BitmapPools>") {
                    initGlobalPools()
                }
            }
        }
        if (globalPools.isEmpty()) return null
        synchronized(globalPools) {
            return globalPools.firstOrNull { singleSize <= it.singleMaxSize } ?: globalPools.last()
        }
    }

    /**
     * 初始化缓存池
     */
    private fun initGlobalPools() {
        val logBuilder = StringBuilder("globalPools init, ")
        synchronized(globalPools) {
            configList.forEach {
                globalPools.add(createPool(it))
                if (GProperty.DEBUG_GLOBAL_POOL) {
                    val totalSize = FilePathUtils.getUnitValue(ContextGetter.context, it.totalMaxSize)
                    val singleSize = FilePathUtils.getUnitValue(ContextGetter.context, it.singleMaxSize)
                    val idleSize = FilePathUtils.getUnitValue(ContextGetter.context, it.idleMaxSize)
                    logBuilder.append("[${it.tag}: total=$totalSize, single=$singleSize, idle=$idleSize, reuse=${it.maxReuseMultiple}]")
                }
            }
            globalPools.sortBy { it.singleMaxSize }
        }
        if (GProperty.DEBUG_GLOBAL_POOL) {
            GLog.d(TAG, DL) { logBuilder.toString() }
        }
    }

    /**
     * 清空缓存池，释放所有bitmap占用的内存
     */
    private fun clear() {
        synchronized(globalPools) {
            globalPools.forEach { it.clear() }
        }
    }

    /**
     * 裁剪缓存
     * @param level 剪裁的上下文，提示应用程序可能要执行的剪裁量
     */
    private fun trimMemory(level: Int) {
        synchronized(globalPools) {
            globalPools.forEach { it.onTrimMemory(level) }
        }
    }

    abstract class IBitmapPool(val singleMaxSize: Long) {
        /**
         * 根据宽高要求，从缓存池取出一个可复用的bitmap
         * @param width 指定bitmap的宽
         * @param height 指定bitmap的高
         * @param config 指定bitmap的config格式
         */
        abstract fun getBitmap(width: Int, height: Int, config: Bitmap.Config? = Bitmap.Config.ARGB_8888): Bitmap?

        /**
         * bitmap放入缓存池
         */
        abstract fun recycle(bitmap: Bitmap?)

        /**
         * 用图像数据生成bitmap
         * @param buffer 图像数据
         * @param options bitmap读取配置，只作复用，里面设置的值不会被使用
         * @param width 图像宽，不传则自行解析图像数据，建议传
         * @param height 图像高，不传则自行解析图像数据，建议传
         */
        fun decode(buffer: BytesBuffer, options: BitmapFactory.Options? = null, width: Int = 0, height: Int = width): Bitmap? {
            return BitmapPools.decode(buffer.data, buffer.offset, buffer.length, options, width, height)
        }

        /**
         * 用图像数据生成bitmap
         * @param data 图像数据
         * @param offset data偏移值
         * @param length data有效数据长度
         * @param options bitmap读取配置，只作复用，里面设置的值不会被使用
         * @param width 图像宽，不传则自行解析图像数据，建议传
         * @param height 图像高，不传则自行解析图像数据，建议传
         */
        fun decode(data: ByteArray, offset: Int, length: Int, options: BitmapFactory.Options? = null, width: Int = 0, height: Int = width): Bitmap? {
            return decodeInternal(data, offset, length, adjustOptions(data, offset, length, options, width, height))
        }

        internal fun decodeInternal(data: ByteArray, offset: Int, length: Int, options: BitmapFactory.Options): Bitmap? {
            options.inPreferredConfig = Bitmap.Config.ARGB_8888
            options.inBitmap = getBitmap(options.outWidth, options.outHeight, options.outConfig)
            return runCatching {
                val bitmap = DecodeUtils.decode(data, offset, length, options)
                if ((options.inBitmap != null) && (options.inBitmap != bitmap)) {
                    recycle(options.inBitmap)
                    options.inBitmap = null
                }
                bitmap
            }.onFailure {
                GLog.w(TAG, DL) { "[decodeInternal] fail with a given bitmap, try decode to a new bitmap" }
                recycle(options.inBitmap)
                options.inBitmap = null
                DecodeUtils.decode(data, offset, length, options)
            }.getOrNull()
        }

        /**
         * 清空缓存池，释放所有bitmap占用的内存
         */
        abstract fun clear()

        /**
         * 取出所有的bitmap
         */
        internal abstract fun getAllBitmap(): List<Bitmap>?

        /**
         * 监听系统关于应用内存消减的通知，根据level场景做不同程度的内存释放
         * @param level 消减紧急程度，越大代表内存越紧张
         */
        internal abstract fun onTrimMemory(level: Int)

        companion object {

            internal val DEFAULT_POOL_CONFIG = PoolConfig(
                tag = "default",
                totalMaxSize = MEM_10M
            )

            @JvmStatic
            internal fun adjustOptions(
                data: ByteArray,
                offset: Int,
                length: Int,
                options: BitmapFactory.Options?,
                width: Int,
                height: Int
            ): BitmapFactory.Options {
                val result = options ?: BitmapFactory.Options()
                if (result.inSampleSize < 1) {
                    result.inSampleSize = 1
                }

                if ((width <= 0) || (height <= 0)) {
                    DecodeUtils.decodeBounds(data, offset, length, result)
                } else {
                    result.outWidth = width
                    result.outHeight = height
                }
                return result
            }
        }
    }

    private class LruBitmapPool(tag: String, totalMaxSize: Long, singleMaxSize: Long, maxIdleSize: Long, reuseMultiple: Float) :
        IBitmapPool(singleMaxSize) {
        private val pool = LruBitmapPool(tag, totalMaxSize, maxIdleSize, reuseMultiple)

        override fun getBitmap(width: Int, height: Int, config: Bitmap.Config?): Bitmap? = pool.getOrNull(width, height, config)

        override fun recycle(bitmap: Bitmap?) {
            pool.put(bitmap)
        }

        override fun getAllBitmap(): List<Bitmap>? {
            return pool.removeAll()
        }

        override fun onTrimMemory(level: Int) {
            pool.trimMemory(level)
        }

        override fun clear() {
            pool.clearMemory()
        }
    }
}