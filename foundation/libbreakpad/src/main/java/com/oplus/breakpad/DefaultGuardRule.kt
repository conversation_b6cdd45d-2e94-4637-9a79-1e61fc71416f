/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DefaultGuardRule.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import com.oplus.gallery.standard_lib.util.chrono.TimeUtils

class DefaultGuardRule : IGuardRule {

    override fun checkIfNormal(crashes: List<SceneCrashInfo>?): Boolean {
        return crashes.isNullOrEmpty()
    }

    override fun cacheDuration(): Long {
        return TimeUtils.TIME_2_MONTH_IN_MS
    }
}