/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseCacheCleanUpStrategy.kt
 ** Description : 对于磁盘缓存文件进行校验和清理的策略基类
 ** Version     : 1.0
 ** Date        : 2023/07/09
 ** Author      : <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2023/07/09      1.0
 *********************************************************************************/
package com.oplus.gallery.foundation.cache.diskcache.cleanup.strategy

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.file.File
import kotlin.math.abs

/**
 * 对于磁盘缓存文件进行校验和清理的策略基类，提供基本的触发条件实现和缓存清理动作实现。
 * 策略类用于配置相关清理任务的参数和策略行为，调用方为[CacheCleanUpTask]
 */
abstract class BaseCacheCleanUpStrategy : ICacheCleanUpStrategy {
    /**
     * 获取待清理的目标缓存文件目录
     */
    abstract val targetCacheDir: File

    /**
     * 目标缓存文件最短保存时间，避免还未使用就已被错误清理。单位为 ms
     */
    protected abstract val cacheFileMinProtectTime: Long

    /**
     * 缓存文件目录中所有文件总共占用的最大阈值。单位byte
     */
    protected abstract val cacheFileMaxTotalSize: Long

    /**
     * 缓存文件目录中所有文件最大数量
     */
    protected abstract val cacheMaxTotalFileCount: Int

    /**
     * 缓存文件允许的最大留存时间。单位为 ms
     */
    protected abstract val cacheFileMaxPreservationTime: Long

    /**
     * 是否要清理[AppConstants.Path.PATH_NO_MEDIA]后缀结尾的缓存文件
     */
    protected abstract val shouldCleanNoMedia: Boolean

    override fun getTargetCleanUpDir(): File = targetCacheDir

    /**
     * 触发条件的判断流程：
     * 1. [isCacheCleanUpForbidden]判断是否在保护状态，处于保护状态的文件不应该进行清理，返回false
     * 2. [isOverTotalLimit]检查所有目标缓存文件大小、文件数量是否超过了限制，如果缓存文件的总大小或者总数量超过限制，
     * 断定需要清理当前文件，返回true
     * 3. [shouldTriggerCacheCleanUp]检查当前缓存文件[fileToCheck]是否满足清理策略触发条件，会根据策略条件针对性的判断
     * @param fileToCheck 当前待检查的缓存文件
     * @param totalCacheSize 所有缓存文件总共占用的大小
     * @param totalCount 所有缓存文件总共占用的数目
     */
    override fun shouldCleanUpCache(fileToCheck: File, totalCacheSize: Long, totalCount: Int): Boolean {
        if (isCacheCleanUpForbidden(fileToCheck)) {
            GLog.w(getTag()) { "[checkNeedCacheCleanUp] cancel cleaning up for ${fileToCheck.toUri()}, cause file is forbidden to delete" }
            return false
        }
        if (isOverTotalLimit(totalCacheSize, totalCount)) {
            return true
        }
        if (shouldTriggerCacheCleanUp(fileToCheck)) {
            return true
        }
        return false
    }

    /**
     * 判断目标缓存文件当前的状态是否在禁止删除，处于保护中的文件不应该被删除，判断优先级应该是最高的
     * @param fileToCheck 待判断的缓存文件
     * @return 是否处于保护状态
     */
    private fun isCacheCleanUpForbidden(fileToCheck: File): Boolean {
        if (shouldCleanNoMedia.not() && (fileToCheck.absolutePath.endsWith(AppConstants.Path.PATH_NO_MEDIA))) {
            return true
        }
        // 缓存文件是否在保护时间内，避免误删除
        if (isCacheFileInProtectTime(fileToCheck)) {
            return true
        }
        return false
    }

    /**
     * 保护时间条件判断，当文件留存时间小于[cacheFileMinProtectTime]时，不应该触发清理
     * @param fileToCheck 待检查的缓存文件
     * @return 返回true表明目标缓存文件符合保护条件，不进行清理
     */
    private fun isCacheFileInProtectTime(fileToCheck: File): Boolean {
        val elapsedTime = abs(System.currentTimeMillis() - fileToCheck.lastModified())
        return (elapsedTime <= cacheFileMinProtectTime)
    }

    /**
     * 针对一批缓存文件，检查所有目标缓存文件在文件占用、文件数量方面是否超过了限制
     * @param totalCacheSize 当前找到的所有目标缓存文件空间占用
     * @param totalCount 当前找到的所有目标缓存文件数
     * @return 是否需要触发清理
     */
    private fun isOverTotalLimit(totalCacheSize: Long, totalCount: Int): Boolean {
        return (totalCacheSize > cacheFileMaxTotalSize) || (totalCount > cacheMaxTotalFileCount)
    }

    /**
     * 针对单个缓存文件，判断目标缓存文件是否需要进行清理
     * @param fileToCheck 待判断的缓存文件
     * @return 是否需要触发清理
     */
    private fun shouldTriggerCacheCleanUp(fileToCheck: File): Boolean {
        if (fileToCheck.exists().not()) {
            GLog.d(getTag()) { "[shouldTriggerCacheCleanUp] file: $fileToCheck not exist!" }
            return false
        }
        if (isCachePreservationTimeInvalid(fileToCheck)) {
            return true
        }
        return false
    }

    /**
     * 留存时间条件判断，当文件留存时间大于[cacheFileMaxPreservationTime]时，触发清理
     * @param fileToCheck 待检查的缓存文件
     * @return 返回true表明目标缓存文件符合清理条件，需要进行清理
     */
    private fun isCachePreservationTimeInvalid(fileToCheck: File): Boolean {
        val elapsedTime = abs(System.currentTimeMillis() - fileToCheck.lastModified())
        return (elapsedTime > cacheFileMaxPreservationTime)
    }

    override fun cleanUpCacheFile(fileToCleanUp: File): Boolean {
        return if (fileToCleanUp.exists()) {
            fileToCleanUp.delete()
        } else {
            false
        }
    }
}