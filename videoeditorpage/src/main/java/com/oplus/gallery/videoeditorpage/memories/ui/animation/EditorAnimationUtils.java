/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorAnimationUtil.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/10
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/10    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.ui.animation;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;

import java.util.ArrayList;

public class EditorAnimationUtils {
    private static final float ALPHA_0 = 0f;
    private static final float ALPHA_1 = 1f;

    public static void startAlphaAnimation(View view, boolean fadeIn, int startDelay,
                                           int duration, final OnEditorAnimationListener listener) {
        Animator animator = createAlphaAnimator(view, fadeIn, duration);
        if (animator != null) {
            animator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    if (listener != null) {
                        listener.onAnimationEnd();
                    }
                }
            });
            animator.setStartDelay(startDelay);
            animator.start();
        }
    }

    public static ObjectAnimator createTranslationAnimator(View view, boolean vertical,
                                                           int from, int to, int duration) {
        if (view == null) {
            return null;
        }
        String propertyName = "";
        if (vertical) {
            propertyName = "translationY";
        } else {
            propertyName = "translationX";
        }
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, propertyName, from, to);
        animator.setDuration(duration);
        return animator;
    }

    public static ObjectAnimator createAlphaAnimator(View view, boolean fadeIn, int duration) {
        if (view == null) {
            return null;
        }
        if (fadeIn) {
            view.setAlpha(ALPHA_0);
        }
        ObjectAnimator animator = null;
        if (fadeIn) {
            animator = ObjectAnimator.ofFloat(view, "alpha", ALPHA_0, ALPHA_1);
        } else {
            animator = ObjectAnimator.ofFloat(view, "alpha", ALPHA_1, ALPHA_0);
        }
        animator.setDuration(duration);
        return animator;
    }

    public static void startAnimationSet(ArrayList<Animator> animators, int duration, int startDelay,
                                         final OnEditorAnimationListener listener) {
        if ((animators != null) && !animators.isEmpty()) {
            AnimatorSet set = new AnimatorSet();
            set.playTogether(animators);
            set.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    if (listener != null) {
                        listener.onAnimationEnd();
                    }
                }
            });
            set.setDuration(duration);
            set.setStartDelay(startDelay);
            set.start();
        }
    }

    public static void startAnimationSet(ArrayList<AutoRemovedViewAnimator> animatorList) {
        if ((animatorList != null) && !animatorList.isEmpty()) {
            AnimatorSet.Builder builder = null;
            AnimatorSet animatorSet = new AnimatorSet();
            for (AutoRemovedViewAnimator autoRemovedViewAnimator : animatorList) {
                if (builder == null) {
                    builder = animatorSet.play(autoRemovedViewAnimator.getAnimator());
                } else {
                    builder.with(autoRemovedViewAnimator.getAnimator());
                }
            }
            animatorSet.start();
        }
    }

    public interface OnEditorAnimationListener {
        void onAnimationEnd();
    }
}
