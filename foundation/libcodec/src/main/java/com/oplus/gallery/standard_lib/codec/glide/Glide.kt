@file:Suppress("HeadKDocRule")
package com.oplus.gallery.standard_lib.codec.glide

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.Log
import com.oplus.gallery.standard_lib.codec.glide.gifdecoder.GifHeader
import com.oplus.gallery.standard_lib.codec.glide.gifdecoder.GifHeaderParser
import com.oplus.gallery.standard_lib.codec.glide.gifdecoder.StandardGifDecoder
import com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.BitmapPool
import com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.LruBitmapPool
import com.oplus.gallery.standard_lib.codec.glide.load.resource.gif.GifBitmapProvider
import com.oplus.gallery.foundation.util.math.ByteArrUtils
import com.oplus.gallery.standard_lib.codec.glide.load.resource.gif.RotatableGifDrawable
import java.io.InputStream
import java.nio.ByteBuffer

object Glide {
    private const val TAG = "GlideGifDecoder"
    private const val MAX_SIZE = 10 * 1024 * 1024L
    private val bitmapPool: BitmapPool = LruBitmapPool(MAX_SIZE)

    fun decode(context: Context, stream: InputStream, targetWidth: Int, targetHeight: Int, rotation: Int): Drawable? {
        return ByteArrUtils.streamToByteArray(stream)?.let { bytes ->

            val headerParser = GifHeaderParser()
            headerParser.setData(bytes)
            val header = headerParser.parseHeader()
            if (header.numFrames <= 0) {
                Log.e(TAG, "decoder, frames num is 0.")
                return null
            }

            if (Log.isLoggable(TAG, Log.DEBUG)) {
                Log.d(TAG, "decoder, frameCount=${header.numFrames}, w*h=${header.width} * ${header.height}")
            }

            val decoder =
                StandardGifDecoder(
                    GifBitmapProvider(bitmapPool),
                    header,
                    ByteBuffer.wrap(bytes),
                    getSampleSize(header, targetWidth, targetHeight)
                )
            decoder.advance()
            decoder.nextFrame?.let {
                if (Log.isLoggable(TAG, Log.DEBUG)) {
                    Log.d(TAG, "decoder, firstFrame=$it")
                }
                RotatableGifDrawable(context, decoder, bitmapPool, targetWidth, targetHeight, rotation, it)
            }
        }
    }

    fun clearMemory() {
        bitmapPool.clearMemory()
    }

    private fun getSampleSize(gifHeader: GifHeader, targetWidth: Int, targetHeight: Int): Int {
        val exactSampleSize = Math.min(gifHeader.height / targetHeight, gifHeader.width / targetWidth)
        val powerOfTwoSampleSize = if (exactSampleSize == 0) 0 else Integer.highestOneBit(exactSampleSize)
        /*
         * Although functionally equivalent to 0 for BitmapFactory, 1 is a safer default for our code
         * than 0.
         */
        return Math.max(1, powerOfTwoSampleSize)
    }
}