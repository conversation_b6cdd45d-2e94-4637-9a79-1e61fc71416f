/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextDecorationItem.kt
 ** Description : 文字装饰item
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

/**
 * 文字装饰item
 */
data class TextDecorationItem(
    /**
     * 文字装饰（加粗、下划线）
     */
    val textDecoration: TextDecoration,

    /**
     * 背景资源id
     */
    val backgroundResId: Int
) : BaseItem()

/**
 * 文字装饰
 */
enum class TextDecoration {
    /**
     * 加粗
     */
    BOLD,

    /**
     * 斜体
     */
    ITALIC,

    /**
     * 下划线
     */
    UNDERLINE,

    /**
     * 中划线
     */
    LINE_THROUGH
}