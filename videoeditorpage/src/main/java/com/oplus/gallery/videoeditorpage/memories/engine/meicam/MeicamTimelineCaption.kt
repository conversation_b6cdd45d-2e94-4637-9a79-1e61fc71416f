/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamTimelineCaption.kt
 * Description: 对字幕类做包装,增加调整显示位置,是否显示的功能
 * Version: 1.0
 * Date: 2021/4/7
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>   2021/4/7          1.0         OPLUS_FEATURE_APP_LOG_MONITOR
</desc></version></date></author> */
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.meicam.sdk.NvsTimelineCaption

class MeicamTimelineCaption(nvsCaption: NvsTimelineCaption) {
    companion object {
        const val TAG = "MeicamTimelineCaption"
        const val EMPTY = " "
    }

    var duration = nvsCaption.outPoint - nvsCaption.inPoint

    var oriText: String = nvsCaption.text

    var caption = nvsCaption
        private set

    var visibility = true
        private set

    var inTime = nvsCaption.inPoint
        private set
    private var outTime = nvsCaption.outPoint

    fun movePosition(offsetNs: Long) {
        inTime += offsetNs
        outTime += offsetNs

        if (outTime <= 0) {
            visibility = false
            caption.run {
                if (caption.text != EMPTY) {
                    oriText = text
                }
                changeInPoint(0)
                changeOutPoint(0)
                text = EMPTY
            }
        } else {
            // 如果之前被隐藏,则显示出来
            visibility = true
            caption.run {
                if (text == EMPTY) {
                    text = oriText
                }
                /*
                 * 移动in out时间时,左移先动inTime,右移先移outTime
                 * 否则outTime超过inTime会移动到inTime的时间
                 */
                if (offsetNs <= 0) {
                    changeInPoint(inTime)
                    changeOutPoint(outTime)
                } else {
                    changeOutPoint(outTime)
                    changeInPoint(inTime)
                }
            }
        }
    }

    fun movePosition(speed: Float) {
        if (speed.compareTo(0f) <= 0) {
            return
        }
        inTime = (inTime.toFloat() / speed).toLong()
        outTime = (outTime.toFloat() / speed).toLong()
        caption.run {
            /*
             * 移动in out时间时,左移先动inTime,右移先移outTime
             * 否则outTime超过inTime会移动到inTime的时间
             */
            if (speed < 1f) {
                changeOutPoint(outTime)
                changeInPoint(inTime)
            } else {
                changeInPoint(inTime)
                changeOutPoint(outTime)
            }
        }
    }
}