/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - StatisticsHelper.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.common.statistics

import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip

/**
 * 统计辅助类
 */
object StatisticsHelper {
    /**
     * 获取视频片段的路径
     */
    fun getClipPathInfo(clips: List<IVideoClip>?): String {
        if ((clips == null) || (clips.isEmpty())) {
            return StatisticsConstant.NULL
        }

        val info = StringBuilder()

        for (i in clips.indices) {
            info.append(clips[i].filePath)
            if (i != (clips.size - AppConstants.Number.NUMBER_1)) {
                info.append(StatisticsConstant.UNIT_SEPARATOR)
            }
        }

        return info.toString()
    }
}