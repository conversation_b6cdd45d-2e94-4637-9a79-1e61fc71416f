/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SuitableTextView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.common.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.utlis.ColorSupportUtil;

public class SuitableTextView extends androidx.appcompat.widget.AppCompatTextView {

    private static final int TEXT_SIZE_DEFAULT = 0;
    private static final float DEFAULT_WIDTH_DP = 360f;

    private boolean mSupportChangeDensity;
    private int mMaxSizeLevel;

    public SuitableTextView(Context context) {
        this(context, null);
    }

    public SuitableTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SuitableTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        if (attrs == null) {
            mMaxSizeLevel = TEXT_SIZE_DEFAULT;
            mSupportChangeDensity = false;
        } else {
            TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.SuitableTextView, defStyleAttr, 0);
            mMaxSizeLevel = array.getInt(R.styleable.SuitableTextView_sizeMaxLevel, TEXT_SIZE_DEFAULT);
            mSupportChangeDensity = array.getBoolean(R.styleable.SuitableTextView_supportChangeDensity, false);
            array.recycle();
        }
        if (mMaxSizeLevel != TEXT_SIZE_DEFAULT) {
            setTextSize(getTextSize());
        }
    }

    @Override
    public void setTextSize(float size) {
        if (mMaxSizeLevel == TEXT_SIZE_DEFAULT) {
            super.setTextSize(size);
        } else {
            DisplayMetrics metrics = getResources().getDisplayMetrics();
            final float scaleDensity = metrics.scaledDensity;
            float density = metrics.density;
            if (density <= 0f) {
                density = 1f;
            }
            float fontScale = scaleDensity / density;
            float suitableTextSize = size / fontScale;
            if (mMaxSizeLevel > ColorSupportUtil.G1) {
                suitableTextSize = (int) ColorSupportUtil.getSuitableFontSize(size, fontScale, mMaxSizeLevel);
            }
            super.setTextSize(TypedValue.COMPLEX_UNIT_PX, suitableTextSize);
        }
    }

    @Override
    public void setMaxWidth(int maxPixels) {
        if (!mSupportChangeDensity) {
            DisplayMetrics metrics = getResources().getDisplayMetrics();
            float density = metrics.density;
            if (density <= 0f) {
                density = 1f;
            }
            float defaultDensity = metrics.widthPixels / DEFAULT_WIDTH_DP;
            int realMaxPixel = (int) (maxPixels * defaultDensity / density);
            super.setMaxWidth(realMaxPixel);
        } else {
            super.setMaxWidth(maxPixels);
        }
    }

    public void setSupportChangeDensity(boolean supportChangeDensity) {
        this.mSupportChangeDensity = supportChangeDensity;
    }

    public void setMaxSizeLevel(int maxSizeLevel) {
        this.mMaxSizeLevel = maxSizeLevel;
    }
}
