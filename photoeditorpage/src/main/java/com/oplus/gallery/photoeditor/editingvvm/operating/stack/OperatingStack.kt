/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OperatingStack
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating.stack

import android.text.TextUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveSupportCheckStrategy.Companion.isSupportOliveEdit
import com.oplus.gallery.photoeditor.editingvvm.operating.ICombineStrategy
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingExporter
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_MAX
import com.oplus.gallery.photoeditor.editingvvm.operating.PlusAssignToFirst
import com.oplus.gallery.photoeditor.editingvvm.operating.priority
import com.oplus.gallery.photoeditor.editingvvm.output.ExportData
import com.oplus.gallery.photoeditor.editingvvm.output.SaveType
import kotlin.collections.reversed as ktReversed

/**
 * 操作栈，当前操作栈只支持两级操作栈
 * @param stack 操作栈的内部实现
 */
class OperatingStack private constructor(
    private val stack: IMultiStack<OperatingRecord>
) : IMultiStack<OperatingRecord> by stack {

    /**
     * commandStr 操作命令字符串，保存在文件扩展数据中，可用来恢复操作栈
     */
    constructor() : this(MultiStack())

    /**
     * 操作栈导出器，根据操作栈实例，导出操作栈信息字符串
     */
    private val exporter = OperatingExporter()

    /**
     * 导入的操作记录列表，这部分记录不可撤销，所以不加入内部的栈
     */
    private val importRecordList = mutableListOf<OperatingRecord>()

    init {
        // 一级栈入栈
        addStack()
    }

    /**
     * 外部导入操作记录
     */
    fun importRecords(records: List<OperatingRecord>) {
        importRecordList.addAll(records)
    }

    private fun getActiveElementsAtStack(index: Int, includeImportRecord: Boolean?): List<OperatingRecord> {
        return if ((index == 0) && (includeImportRecord == true)) {
            stack.getActiveElementsAtStack(index) + importRecordList.ktReversed()
        } else {
            stack.getActiveElementsAtStack(index)
        }
    }

    /**
     * 获取全部多级编辑栈中的活跃（非已撤销）元素，顺序为栈顶到栈底. 即整体是新的在前面, 陈旧的在后面
     *
     * @param includeImportRecord 是否包含导入录列表
     */
    private fun getActiveElementsAllStack(includeImportRecord: Boolean?): List<OperatingRecord> {
        return if (true == includeImportRecord) {
            stack.getActiveElements() + importRecordList.ktReversed()
        } else {
            stack.getActiveElements()
        }
    }

    override fun push(element: OperatingRecord) {
        GLog.d(TAG, LogFlag.DL, "push: element = $element")
        stack.push(element)
    }

    override fun undo(): OperatingRecord? {
        return stack.undo().apply {
            GLog.d(TAG, LogFlag.DL, "undo: record = $this")
        }
    }

    override fun pop(): OperatingRecord? {
        return stack.pop().apply {
            GLog.d(TAG, LogFlag.DL, "pop: record = $this")
        }
    }

    override fun redo(onRedoItem: ((OperatingRecord?) -> Unit)?) {
        return stack.redo {
            GLog.d(TAG, LogFlag.DL, "redo: record = $it")
            onRedoItem?.invoke(it)
        }
    }

    /**
     * 进入次级页面栈，与[stackPop]配套使用
     *
     * 内部实现是添加一个新的栈作为当前栈，用于记录次级页面的操作
     */
    fun stack() {
        addStack()
    }

    /**
     * 多个次级栈操作合并成一个操作
     * @return [OperatingRecord] 合并后的参数
     */
    private fun combine(records: List<OperatingRecord>, combineStrategy: ICombineStrategy): OperatingRecord {
        var record = records.first()
        for (index in (1 until records.size)) {
            record = combineStrategy.combine(record, records[index])
        }
        return record
    }

    /**
     * 退出次级页面栈，与[stack]配套使用
     *
     * 内部实现是移除当前栈，根据参数决定是否合并次级栈为一步
     * @param isKeepAsOneStep 是否保留成一步：是的话合并次级栈为一步，回调新一步和次级栈；否则删掉次级栈，只回调次级栈
     * @param func 回调stackPop结果
     */
    fun stackPop(
        isKeepAsOneStep: Boolean,
        combineStrategy: ICombineStrategy = PlusAssignToFirst(),
        func: (OperatingRecord?, List<OperatingRecord>) -> Unit
    ) {
        if (getStackCount() <= FIRST_STACK_DEPTH) {
            GLog.w(TAG, LogFlag.DL, "stackPop: Doesn't exist any sub stack, pop failed.")
            return
        }
        // 移除次级栈
        val recordList = removeStack()
        if (recordList.isEmpty()) {
            func.invoke(null, recordList)
            return
        }
        if (isKeepAsOneStep) {
            // 合成一步
            val record: OperatingRecord = combine(recordList.asReversed(), combineStrategy)
            // 压入上一级栈
            push(record)
            GLog.d(TAG, LogFlag.DL, "stackPop: record = $record")
            // 回调新record和旧的recordList
            func.invoke(record, recordList)
        } else {
            // 只回调旧的recordList
            func.invoke(null, recordList)
        }
    }

    /**
     * 在次级页面栈操作 restore() -- 撤销当前次级编辑页的所有操作。
     *
     * 内部实现是清空多级栈的顶栈， 返回被清的items
     * @param func 回调被清的item列表
     */
    fun clearTopStack(func: (List<OperatingRecord>) -> Unit) {
        val clearTopStack = stack.clearTopStack()
        func.invoke(clearTopStack)
    }

    /**
     * 从操作栈中整合导出参数化数据和保存的任务类型。需要验证的场景：
     * 场景1：编辑前该图片无参数化数据，此次编辑产生参数化信息，则保存的时候，则只需要关心和存储当次的参数化数据
     * 场景2：编辑前该图片有参数化数据，此次编辑不产生参数化数据（此次编辑最后一步为内容编辑），则抹除所有参数化数据
     * 场景3：编辑前该图片有参数化数据，此次也仅有参数化数据
     *       3.1.原先参数化信息：a,b,c,d，此次更新b1,c1，则更新为a,b1,c1,d
     *       3.2.原先参数化信息：a,b,c，此次更新c1,d，则更新为a,b,c1,d
     *       3.3.原先参数化信息：a,b，此次更新c,d，则更新为a,b,c,d
     * 场景4：编辑前该图片有参数化数据，此次既产生了内容编辑，也有参数化编辑，且最后一步为参数化编辑，则只需要关心和存储当次的参数化数据
     * @return 参数化数据以及保存任务类型
     */
    internal fun export(format: CommandFormat, isSupportParametric: Boolean, isOliveFile: Boolean): ExportData {
        // 处理复原，只看用户操作部分是否为空
        var firstStackRecordList = getActiveElementsAtStack(0, false)
            .map { if (it.isJustPackingBox) it.split() else listOf(it) }.flatten()
        if (firstStackRecordList.isEmpty()) {
            // 栈的数据为空，意味着此次是点击"复原"之后的保存,此时走正常的保存逻辑,参数化的数据为空
            return ExportData(extendData = EMPTY_STRING, saveType = SaveType.RECOVERY).also {
                GLog.d(TAG, LogFlag.DL) { "[export] saveType = recovery" }
            }
        }

        // 处理完整的栈，包含导入和用户操作部分
        firstStackRecordList = getActiveElementsAtStack(0, true)
            .map { if (it.isJustPackingBox) it.split().asReversed() else listOf(it) }.flatten()

        // marked by zhang lei 同时包含人像和隐私水印，则不需要导出， 隐私水印参数化后移除
        if (exporter.notNeedExport(firstStackRecordList)) { return exporter.defaultExportData() }

        val recordList = arrayListOf<OperatingRecord>()
        var hasContentEdit = false
        // 从新到旧，找到非参数化的就停止，当前record的操作是叠加的，所以只需要保留最后一个record
        for (currentRecord in firstStackRecordList) {
            // 非参数化，即内容编辑，清除前面的编辑项记录
            if (currentRecord.isParametric.not()) {
                hasContentEdit = true
                break
            } else if (exporter.filterList.contains(currentRecord.effectName)) {
                // 过滤不需要导出的编辑项记录
                continue
            } else {
                // 导出编辑项记录
                recordList.find { it.effectName == currentRecord.effectName } ?: recordList.add(currentRecord)
            }
        }
        val exportData = exporter.export(
            ArrayList(recordList.asReversed()),
            format,
            isSupportParametric,
            (isOliveFile && isOliveAfterSave(firstStackRecordList))
        )
        exportData.hasContentEdit = hasContentEdit
        return exportData
    }

    private fun isOliveAfterSave(stacks: List<OperatingRecord>): Boolean {
        return isSupportOliveEdit(stacks)
    }

    /**
     * 0级(最底层)操作栈中,是否存在满足条件的数据
     */
    internal fun anyInFirstStack(predicate: (OperatingRecord) -> Boolean): Boolean {
        return internalFindRecords(
            stackLevel = 0,
            predicate = predicate, isFindUntilNonParametric = false
        ).isEmpty().not()
    }

    /**
     * 查找最近一次非参数化操作后的所有指定的参数化操作。
     * 例：先做裁剪A，再做标记，再做裁剪B，裁剪C，则返回 B,C
     * @param effectName 指定的参数化操作的名称，如果传入空字符串，则会获取所有的参数化操作
     */
    fun findRecordsAfterNonparametric(effectName: String): List<OperatingRecord>? {
        return internalFindRecords(
            predicate = { if (TextUtils.isEmpty(effectName)) true else effectName == it.effectName },
            isFindUntilNonParametric = true
        )
    }

    /**
     * 获取导入的操作记录列表
     */
    fun findImportRecord(): List<OperatingRecord> {
        return importRecordList
    }

    /**
     * 找到最后一个符合筛选条件的操作记录
     * @param isFindUntilNonParametric true:一直找，找到非参数化或者相同效果的参数化；false:找一轮，找到就找到，找不到返回空
     * @param predicate 查找条件
     * @return [OperatingRecord]
     */
    fun findLast(
        isFindUntilNonParametric: Boolean = false,
        predicate: (OperatingRecord) -> Boolean
    ): OperatingRecord? {
        return internalFindRecords(predicate = predicate, isFindUntilNonParametric = isFindUntilNonParametric, returnOnFirstFound = true).firstOrNull()
    }

    /**
     * 按照指定条件, 遍历整个编辑多级栈, 查找单个,或者收集多个record
     *
     * @param stackLevel -1: 遍历所有栈; 0: 只遍历第一级栈; 1: 只遍历第二级栈; 2: 只遍历第三级栈; ...
     * @param includeImportRecords 是否包含import类型的record.
     *                             (所谓import类型的record 是指用户以前编辑后保存成文件的编辑记录, 当前重入编辑时反序列化而来)
     * @param travelOrder 遍历顺序.
     *                    FIFO: 表示遍历的顺序是从旧到新 (即先被录入的record先被遍历到);
     *                    FILO: 表示遍历的顺序是从新到旧 (即后被录入的record先被遍历到)
     * @param predicate 查找条件
     * @param isFindUntilNonParametric true: 遇到非参数化record就停止查找； false:查找所有类型record
     * @param returnOnFirstFound true: 遇到第一个满足条件的 就直接返回结果; false: 一直收集所有满足条件的record
     *
     * 注意: 默认遍历顺序是TravelOrder.FILO, 其遍历的顺序是:
     *   1. 多级栈遍历次序: 从高级栈 往 低级栈 遍历;
     *   2. 在每个栈内部, 是从'较新的record' 开始往 '较旧的record' 遍历.
     *  -- 这是由 getActiveElementsAllStack() 的内部的实现决定的.
     *  -- 简单说, 越新的编辑record越先被遍历到;
     *  而另外一种遍历 TravelOrder.FIFO 就是先遍历到更旧的record
     *
     */
    private fun internalFindRecords(
        stackLevel: Int = -1,
        includeImportRecords: Boolean = true,
        travelOrder: TravelOrder = TravelOrder.FILO,
        predicate: (OperatingRecord) -> Boolean,
        isFindUntilNonParametric: Boolean = false,
        returnOnFirstFound: Boolean = false
    ): List<OperatingRecord> {
        val activeRecords = if (stackLevel < 0) {
            getActiveElementsAllStack(includeImportRecords)
        } else {
            getActiveElementsAtStack(stackLevel, includeImportRecords)
        }.let {
            if (travelOrder == TravelOrder.FIFO) {
                // 底层函数 getActiveElementsAtStack() 是按照FILO返回records的, 所以这里要反转一下
                it.ktReversed()
            } else {
                it
            }
        }

        if (activeRecords.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "[internalFindRecords] all stack activeElements is empty" }
        }

        // 遍历查找
        return findInSpecifiedRecords(activeRecords, predicate, isFindUntilNonParametric, returnOnFirstFound)
    }

    /**
     * 在给定的records集中,按照条件查找一个或者多个records
     *
     * @param activeRecords 所有待查records集合
     * @param predicate 查找条件
     * @param isFindUntilNonParametric true: 遇到非参数化record就停止查找； false:查找所有类型record
     * @param returnOnFirstFound true: 遇到第一个满足条件的 就直接返回结果; false: 一直收集所有满足条件的record
     */
    private fun findInSpecifiedRecords(
        activeRecords: List<OperatingRecord>,
        predicate: (OperatingRecord) -> Boolean,
        isFindUntilNonParametric: Boolean,
        returnOnFirstFound: Boolean
    ): ArrayList<OperatingRecord> {
        val resultList = arrayListOf<OperatingRecord>()
        for (operatingRecord in activeRecords) {
            // step 1: 有个空指针crash定位到这里，但按理这里不可空，先改一下看看
            @Suppress("SENSELESS_COMPARISON")
            if (operatingRecord == null) {
                // 打个log，方便后续定位异常
                GLog.w(TAG, LogFlag.DL) { "[internalFindRecords] operatingRecord is null" }
                continue
            }

            // step2: 对于'包装类record', 先看自己是否match后, 然后还要拆开检查内部的子record
            if (operatingRecord.isJustPackingBox) {
                if (predicate(operatingRecord)) {
                    resultList.add(operatingRecord)
                    if (returnOnFirstFound && resultList.isEmpty().not()) {
                        return resultList
                    }
                }
            }
            val operatingRecordList =
                if (operatingRecord.isJustPackingBox) {
                    operatingRecord.split().ktReversed().filter { it.isJustPackingBox.not() }
                } else {
                    listOf(operatingRecord)
                }

            // step3: 正常按照条件查找
            operatingRecordList.forEach {
                // 如果外部标记遍历到非参数化操作就结束 && 当前遍历到非参数化操作
                if (isFindUntilNonParametric && it.isParametric.not()) {
                    return resultList
                }
                if (predicate(it)) {
                    resultList.add(it)
                    if (returnOnFirstFound && resultList.isEmpty().not()) {
                        return resultList
                    }
                }
            }
        }

        return resultList
    }

    /**
     * 找到最后一个符合筛选条件的操作记录
     * @param index 指定的栈级
     * @param isFindUntilNonParametric true:一直找，找到非参数化或者相同效果的参数化；false:找一轮，找到就找到，找不到返回空
     * @param predicate 查找条件
     * @return [OperatingRecord]
     */
    fun findLast(
        index: Int,
        isFindUntilNonParametric: Boolean = false,
        predicate: (OperatingRecord) -> Boolean
    ): OperatingRecord? {
        return internalFindRecords(
            stackLevel = index,
            includeImportRecords = true,
            predicate = predicate,
            isFindUntilNonParametric = isFindUntilNonParametric,
            returnOnFirstFound = true
        ).firstOrNull()
    }

    /**
     * 在栈中找到符合条件的record数据,直至找到不符合条件的record就break
     */
    fun peekInStack(predicate: (OperatingRecord) -> Boolean): List<OperatingRecord>? {
        val recordsInFirstStack = getActiveElementsAtStack(0, true)
        if (recordsInFirstStack.isEmpty()) {
            return null
        }
        val recordList = arrayListOf<OperatingRecord>()
        for (record in recordsInFirstStack) {
            if (predicate(record)) {
                recordList.add(record)
            } else {
                break
            }
        }
        return recordList
    }

    /**
     * 往导入的操作记录列表中添加元素
     * @param record OperatingRecord
     */
    fun addRecordToImport(record: OperatingRecord) {
        importRecordList.add(record)
    }

    /**
     * 获取某一级栈的所有的操作记录
     * @param includeImportRecord true:同时也在参数化导入的栈数据中查找记录，false:只在当次编辑产生的操作记录中查找
     * @param index 几级栈
     * @param predicate 查找条件
     */
    fun findAllRecord(
        includeImportRecord: Boolean = true,
        index: Int = 0,
        predicate: (OperatingRecord) -> Boolean
    ): List<OperatingRecord> {
        return internalFindRecords(
            stackLevel = index,
            includeImportRecords = includeImportRecord,
            predicate = predicate
        )
    }

    /**
     * 在操作栈找优先级低于传入的记录，优先级diff最小
     * @param record
     * @param index 几级栈.  -1: 遍历所有栈; 0: 只遍历第一级栈; 1: 只遍历第二级栈; 2: 只遍历第三级栈; ...
     * @return 参数化记录，如果没有，返回空
     */
    fun findNextPriorityRecord(record: OperatingRecord, index: Int = -1, isSupportParametric: Boolean = true): OperatingRecord? {
        // step1: 按照FILO(即从新record到旧record的次序), 找到所有优先级低于指定record的记录 (且排除非参数化的干扰 -- 即遇到参数化就停止查找)
        val findRecords = internalFindRecords(
            stackLevel = index,
            includeImportRecords = true,
            predicate = { it.priority(isSupportParametric) < record.priority(isSupportParametric) },
            isFindUntilNonParametric = true
        )

        // step2: 从上述集合中, 寻找优先级与给定record diff最小的, 且小于给定record的record
        var priorityDiff = PRIORITY_MAX
        var targetRecord: OperatingRecord? = null
        for (operatingRecord in findRecords) {
            val diff = record.priority(isSupportParametric) - operatingRecord.priority(isSupportParametric)
            if (diff in 1 until priorityDiff) {
                priorityDiff = diff
                targetRecord = operatingRecord
            }
        }
        return targetRecord
    }

    /**
     * 清空操作栈（点击"复原"之后需要清空操作栈，这个状态不能撤销），保留空的一级栈
     */
    override fun clear(): List<OperatingRecord> {
        val clearedItems = stack.clear()
        importRecordList.clear()
        return clearedItems
    }

    /**
     * 指针是否处理初始状态。有三种情况返回true：1.刚进编辑时。2.点击复原时
     * 3.产生了编辑动作之后，又撤销到了第一步
     */
    fun isInitialState(): Boolean {
        return stack.getActiveElements().isEmpty()
    }

    companion object {
        const val TAG = "OperatingStack"

        private const val FIRST_STACK_DEPTH = 1
    }
}

/**
 * 操作栈导出、导入格式
 */
enum class CommandFormat {
    JSON,
    PROTOBUF,
    XML
}



/**
 * 栈遍历顺序.
 */
enum class TravelOrder {
    FIFO, // 先入栈的record先被遍历
    FILO, // 先入栈的record后被遍历(默认)
}