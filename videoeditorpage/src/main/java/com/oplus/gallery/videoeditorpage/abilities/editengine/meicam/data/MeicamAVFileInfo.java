/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamAVFileInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.DEFAULT_VIDEO_RESOLUTION_FPS;

import android.util.Rational;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.engine.base.interfaces.IAVFileInfo;


public class MeicamAVFileInfo implements IAVFileInfo {
    private static final String TAG = "MeicamAVFileInfo";

    private int mImageWidth;
    private int mImageHeight;
    private int mImageRotation;
    private long mVideoDuration;
    private int mAVFileType;
    private long mBitRate;

    /**
     * 帧率
     */
    private float mFps;

    public MeicamAVFileInfo(String filePath) {
        mImageWidth = 0;
        mImageHeight = 0;
        mImageRotation = 0;
        mVideoDuration = 0;
        mAVFileType = StreamingConstant.VideoStreamInfo.AV_FILE_TYPE_UNKNOWN;
        mFps = DEFAULT_VIDEO_RESOLUTION_FPS;

        NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
        if (streamingContext == null) {
            GLog.e(TAG, "streaming context is null");
            return;
        }
        NvsAVFileInfo fileInfo = streamingContext.getAVFileInfo(filePath);
        if (fileInfo == null) {
            GLog.e(TAG, "file info is null");
            return;
        }

        if (fileInfo.getVideoStreamCount() > 0) {
            mImageWidth = fileInfo.getVideoStreamDimension(0).width;
            mImageHeight = fileInfo.getVideoStreamDimension(0).height;
            mImageRotation = fileInfo.getVideoStreamRotation(0);
            NvsRational nvsRational = fileInfo.getVideoStreamFrameRate(0);
            if ((nvsRational != null) && (nvsRational.den > 0)) {
                mFps = new Rational(nvsRational.num, nvsRational.den).floatValue();
            }
        }
        mAVFileType = fileInfo.getAVFileType();
        mVideoDuration = fileInfo.getDuration();
        mBitRate = fileInfo.getDataRate();
    }

    @Override
    public int getVideoWidth() {
        return mImageWidth;
    }

    @Override
    public int getVideoHeidht() {
        return mImageHeight;
    }

    @Override
    public int getVideoRotation() {
        return mImageRotation;
    }

    @Override
    public long getDuration() {
        return mVideoDuration;
    }

    @Override
    public int getAVFileType() {
        return mAVFileType;
    }

    @Override
    public long getBitRate() {
        return mBitRate;
    }

    @Override
    public float getFps() {
        return mFps;
    }

    @Override
    public String toString() {
        return "MeicamAVFileInfo{"
                + "mImageWidth=" + mImageWidth
                + ", mImageHeight=" + mImageHeight
                + ", mImageRotation=" + mImageRotation
                + ", mVideoDuration=" + mVideoDuration
                + ", mAVFileType=" + mAVFileType
                + ", mBitRate=" + mBitRate
                + ", mFps=" + mFps
                + '}';
    }
}
