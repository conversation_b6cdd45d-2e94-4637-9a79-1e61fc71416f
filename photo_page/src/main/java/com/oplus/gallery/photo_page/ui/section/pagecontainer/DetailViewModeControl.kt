/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DetailViewExpandControl.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-05-30
 ** Author: huang<PERSON>ow<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/05/30  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.pagecontainer

import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.appendWithComma
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.PanelContractEffectState
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.ProgressEffectController

/**
 * 控制详情页展开和收缩，展开时，隐藏其他组件，收缩时，显示其他组件
 * 涉及的组件：状态栏，顶部菜单栏，底部控件和底部菜单栏，导航栏
 */
internal class DetailViewModeControl {

    /**
     * 是否显示由其自行处理（条件：是否沉浸式，是否详情页，其他条件）
     * 播放条:PhotoPlaybackSection
     * xdr:BrightenCompareComponent
     * ocr:TextOcrComponent
     * 缩图轴：PhotoThumbLineSection
     * 半沉浸视频进度缩图：HalfImmersivePlaybackProgressController
     * 导航栏占位：PhotoPlaybackSection
     * google lens:PhotoGoogleLensSection
     */
    private val excludeViewList by lazy {
        listOf(
            R.id.fl_playback_container, R.id.iv_proxdr, R.id.iv_text_ocr, R.id.vs_thumb_line, R.id.rv_thumb_line,
            R.id.hsv_playback_progress_container, R.id.bottom_blur_bg, R.id.top_blur_bg, R.id.navibar_placeholder, R.id.fl_lens
        )
    }

    /**
     * 通过订阅detailViewStateFlow，确认控件显示和隐藏
     */
    private val _detailsViewStateList = MutableLiveData<List<DetailsViewState>?>()
    val detailsViewStateList: LiveData<List<DetailsViewState>?> get() = _detailsViewStateList

    /**
     * 详情页Decoration部分的 HashSet
     * 在详情页进入的时候，将Decoration部分可见的View加到这个HashSet，然后一起做动画变为消失状态
     * 在详情页退出的时候，将这些View做动画到可见状态
     */
    val detailsVisibleDecorationViews: HashSet<View> = HashSet()

    /**
     * 当详情状态改变时调用
     * @param info 详情页状态信息
     */
    fun onDetailsModeChange(info: DetailsModeInfo) {
        val isUnderImmersionInteractive = info.isUnderImmersionInteractive
        val isInDetailsMode = ProgressEffectController.isDetailsMode(info.panelContractEffectState)
        // 1.竖屏
        val verticalStructure = info.verticalStructure
        // 2.平板
        val isTabletLargeScreen = info.isTabletLargeScreen
        // 3.折叠屏
        val isFoldLargeScreen = info.isFoldLargeScreen
        // 4.小屏
        val smallScreen = isTabletLargeScreen.not() && isFoldLargeScreen.not()

        //是否隐藏顶部菜单栏，true为展示，false不展示
        val isShowTopDecorationView = smallScreen.not()
        //是否展现底部菜单栏，true为展示，false不展示
        val isShowBottomMenu = verticalStructure && smallScreen
        //展开时：是否展现状态栏，true为展示，false不展示
        val isShowStatusBarExpand = isTabletLargeScreen || isFoldLargeScreen
        //收起时：是否展现状态栏，true为展示，false不展示
        val isShowStatusBarCollapse = (verticalStructure && smallScreen) || isShowStatusBarExpand
        //正常模式下收起详情页，是否展示导航栏
        val isShowSystemBarCollapse = (smallScreen && verticalStructure.not()).not() || info.hasVirtualKey

        val isNightMode = info.isNightMode
        //设置状态栏亮暗色状态，暗色状态-字体白色显示，true-亮色状态-字体黑色显示 false-暗色状态-字体白色显示
        val isLightStatusBarAppearance = isNightMode.not()

        when {
            //沉浸式模式展开详情页
            isUnderImmersionInteractive && isInDetailsMode -> {
                immersionExpand(
                    isShowStatusBarExpand,
                    isShowTopDecorationView,
                    isShowBottomMenu,
                    isLightStatusBarAppearance
                )
            }
            //沉浸式模式收起详情页
            isUnderImmersionInteractive && !isInDetailsMode -> immersionCollapse(isShowBottomMenu, isLightStatusBarAppearance)
            //正常预览模式展开详情页
            !isUnderImmersionInteractive && isInDetailsMode -> {
                normalExpand(
                    isShowStatusBarExpand,
                    isShowTopDecorationView,
                    isShowBottomMenu,
                    isLightStatusBarAppearance
                )
            }
            //正常预览模式收起详情页
            else -> normalCollapse(isShowStatusBarCollapse, isShowBottomMenu, isShowSystemBarCollapse, isLightStatusBarAppearance)
        }
    }

    /**
     * 正常预览模式收起详情页
     */
    private fun normalCollapse(
        isShowStatusBarCollapse: Boolean,
        isShowBottomMenu: Boolean,
        isShowSystemBarCollapse: Boolean,
        isLightStatusBarAppearance: Boolean
    ) {
        _detailsViewStateList.setOrPostValue(
            listOf(
                DetailsViewState.StatusBar(isShowStatusBarCollapse, isLightStatusBarAppearance),
                DetailsViewState.TopDecorationView(true),
                DetailsViewState.BottomDecorationView(true),
                DetailsViewState.BottomDecorationChildView(
                    isShowBottomMenu,
                    true,
                    excludeViewList
                ),
                DetailsViewState.NaviBar(isShowSystemBarCollapse)
            )
        )
        GLog.d(TAG, LogFlag.DL) {
            val logContent = StringBuilder()
                .appendWithComma("isShowStatusBarCollapse:$isShowStatusBarCollapse")
                .append("isShowBottomMenu:$isShowBottomMenu")
                .toString()
            "normalCollapse $logContent"
        }
    }

    /**
     * 正常预览模式展开详情页
     */
    private fun normalExpand(
        isShowStatusBarExpand: Boolean,
        isShowTopDecorationView: Boolean,
        isShowBottomMenu: Boolean,
        isLightStatusBarAppearance: Boolean
    ) {
        _detailsViewStateList.setOrPostValue(
            listOf(
                DetailsViewState.StatusBar(isShowStatusBarExpand, isLightStatusBarAppearance),
                DetailsViewState.TopDecorationView(
                    isShowTopDecorationView
                ),
                DetailsViewState.BottomDecorationView(isShowBottomMenu),
                DetailsViewState.BottomDecorationChildView(
                    isShowBottomMenu,
                    false,
                    excludeViewList
                ),
                DetailsViewState.NaviBar(true)
            )
        )
        GLog.d(TAG, LogFlag.DL) {
            val logContent = StringBuilder()
                .appendWithComma("isShowStatusBarExpand:$isShowStatusBarExpand")
                .appendWithComma("isShowTopDecorationView:$isShowTopDecorationView")
                .append("isShowBottomMenu:$isShowBottomMenu")
                .toString()
            "normalExpand $logContent"
        }
    }

    /**
     * 沉浸式模式收起详情页
     */
    private fun immersionCollapse(isShowBottomMenu: Boolean, isLightStatusBarAppearance: Boolean) {
        _detailsViewStateList.setOrPostValue(
            listOf(
                DetailsViewState.StatusBar(false, isLightStatusBarAppearance),
                DetailsViewState.TopDecorationView(false),
                DetailsViewState.BottomDecorationView(false),
                DetailsViewState.BottomDecorationChildView(
                    isShowBottomMenu,
                    false,
                    excludeViewList
                ),
                DetailsViewState.NaviBar(false)
            )
        )
        GLog.d(TAG, LogFlag.DL) {
            "immersionCollapse isShowBottomMenu:$isShowBottomMenu"
        }
    }

    /**
     * 沉浸式模式展开详情页
     */
    private fun immersionExpand(
        isShowStatusBarExpand: Boolean,
        isShowTopDecorationView: Boolean,
        isShowBottomMenu: Boolean,
        isLightStatusBarAppearance: Boolean
    ) {
        _detailsViewStateList.setOrPostValue(
            listOf(
                DetailsViewState.StatusBar(isShowStatusBarExpand, isLightStatusBarAppearance),
                DetailsViewState.TopDecorationView(
                    isShowTopDecorationView
                ),
                DetailsViewState.BottomDecorationView(isShowBottomMenu),
                DetailsViewState.BottomDecorationChildView(
                    isShowBottomMenu,
                    false,
                    excludeViewList
                ),
                DetailsViewState.NaviBar(true)
            )
        )
        GLog.d(TAG, LogFlag.DL) {
            val logContent = StringBuilder()
                .appendWithComma("isShowStatusBarExpand:$isShowStatusBarExpand")
                .appendWithComma("isShowTopDecorationView:$isShowTopDecorationView")
                .append("isShowBottomMenu:$isShowBottomMenu")
                .toString()
            "immersionExpand $logContent"
        }
    }

    companion object {
        private const val TAG = "DetailViewModeControl"
    }
}

internal sealed class DetailsViewState(shouldShow: Boolean) : ViewComponentState(shouldShow) {
    /**
     * 状态栏
     * @param shouldShow 是否显示
     * @param isLightStatusBarAppearance 状态栏字体颜色,true-亮色状态-字体黑色显示 false-暗色状态-字体白色显示
     */
    data class StatusBar(override val shouldShow: Boolean, val isLightStatusBarAppearance: Boolean) : DetailsViewState(shouldShow)

    /**
     * 顶部装饰视图
     * @param shouldShow 是否显示
     */
    data class TopDecorationView(override val shouldShow: Boolean) : DetailsViewState(shouldShow)

    /**
     * 底部装饰视图
     * @param shouldShow 是否显示
     */
    data class BottomDecorationView(override val shouldShow: Boolean) : DetailsViewState(shouldShow)

    /**
     * 底部装饰子视图
     * @param shouldShow 菜单栏是否显示
     * @param childShouldShow 子视图是否显示
     * @param excludeViewList 排除的视图
     */
    data class BottomDecorationChildView(override val shouldShow: Boolean, val childShouldShow: Boolean, val excludeViewList: List<Int>) :
        DetailsViewState(shouldShow)

    /**
     * 导航栏
     * @param shouldShow 是否显示
     */
    data class NaviBar(override val shouldShow: Boolean) : DetailsViewState(shouldShow)

    /**
     * 其他视图
     */
    data class OtherView(override val shouldShow: Boolean = false) : DetailsViewState(shouldShow)
}

/**
 * 视图组件状态
 */
internal sealed class ViewComponentState(open val shouldShow: Boolean)

/**
 * 详情模式信息
 */
internal data class DetailsModeInfo(
    /**
     * 是否沉浸式,不关心是否为沉浸式或半沉浸式，true为沉浸式,false为非沉浸式
     */
    val isUnderImmersionInteractive: Boolean,
    /**
     * 是否为竖直结构，true为竖直结构,false为非竖直结构
     */
    val verticalStructure: Boolean,
    /**
     * 是否平板中大屏，true为平板,false为非平板
     */
    val isTabletLargeScreen: Boolean,
    /**
     * 是否折叠中大屏屏，true为折叠,false为非折叠
     */
    val isFoldLargeScreen: Boolean,
    /**
     * 是否夜间(深色)模式，true为夜间模式,false为非夜间模式
     */
    val isNightMode: Boolean,
    /**
     * 是否有虚拟导航栏，true为有,false为没有
     */
    val hasVirtualKey: Boolean,
    /**
     * 当前面板滑动状态
     */
    val panelContractEffectState: PanelContractEffectState
)