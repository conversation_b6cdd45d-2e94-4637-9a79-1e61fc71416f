/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorTransitionState
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.transition;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.FRAME_DURATION;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.transition.BaseTransition;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener;
import com.oplus.gallery.videoeditorpage.resource.room.bean.TransitionItem;
import com.oplus.gallery.videoeditorpage.resource.room.entity.TransitionEntity;
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode;
import com.oplus.gallery.videoeditorpage.utlis.NetworkPermissionHelper;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum;
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType;
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * 转场业务
 */
public class EditorTransitionState extends EditorTrackBaseState<EditorTransitionUIController>
        implements EditorTransitionUIController.OnIconClickListener {
    private static final String TAG = "EditorTransitionState";
    private final EditorTransitionVM mViewModel = new ViewModelProvider(requireEditorActivity()).get(EditorTransitionVM.class);

    /**
     * 当前索引（前一个片段和后一个片段的场景过渡，索引为前一个片段的索引），与当前片段索引不完成重合
     * eg: 点击的片段1右侧转场按钮和点击片段2的左侧转场按钮，此时mCurrentIndex均为1
     * 直观一点：第一个片段（index = 0）和第二个片段（index = 1）的场景过渡，此时mCurrentIndex为0
     */
    private int mCurrentIndex = 0;
    private boolean mOperationHasDone = false;
    private TransitionListener mTransitionListener;

    /**
     * 构造方法
     * @param context 上下文
     * @param editorControlView 控制视图
     * @param index 进入转场的片段索引
     * @param weakTimelineViewModel 时间线视图模型
     */
    public EditorTransitionState(
        Context context,
        EditorControlView editorControlView,
        int index,
        WeakReference<TimelineViewModel> weakTimelineViewModel
    ) {
        super(TAG, context, editorControlView, weakTimelineViewModel);
        mCurrentIndex = index;
        GLog.i(TAG, LogFlag.DL, "[constructor] mTransitionIndex = " + mCurrentIndex);
    }

    @Override
    protected EditorTransitionUIController createUIController() {
        EditorTransitionUIController controller = new EditorTransitionUIController(
            mContext,
            mEditorControlView,
            this,
            "",
            getClipCount(),
            (getWeakTimelineViewModel() == null) ? null : getWeakTimelineViewModel().get()
        );
        controller.setOnIconClickListener(this);
        return controller;
    }

    @Override
    public boolean showOperaIcon() {
        return false;
    }

    @Override
    public void clickCancel() {
        super.clickCancel();
        restoreCurrentTimeline(false);
        mEditorControlView.seekTimeline(mEditorEngine.getTimelineCurrentPosition(), 0, true);
        mOperationHasDone = false;
        GLog.d(TAG, LogFlag.DL, "clickCancel() ");
    }

    @Override
    public void clickDone() {
        super.clickDone();
        if (mEditorEngine.isPlaying()) {
            mEditorEngine.stopPlayer();
        }
        if (mOperationHasDone) {
            saveCurrentTimeline(OperationType.TRANSITION_SETTING);
            mEditorEngine.notifyTimelineChanged(true);
        }
        GLog.d(TAG, LogFlag.DL, "clickDone() ");
    }

    @Override
    public void onCurrentTimelineChanged(ITimeline timeline, boolean updateTimelineView) {
        super.onCurrentTimelineChanged(timeline, updateTimelineView);
        String transName = getTransitionName();
        mUIController.setCurrentTransitionIndex(transName);
    }

     @Override
    public PageLevelEnum getPageLevel() {
        return PageLevelEnum.PAGE_LEVEL_SECOND;
    }

    @Override
    public void create() {
        super.create();
        GLog.d(TAG, LogFlag.DL, "show()");
        mViewModel.init(mEditorEngine);
        mViewModel.getItemList().observe(requireEditorActivity(), this::onItemListChanged);
        mViewModel.getChangedItem().observe(requireEditorActivity(), this::onItemChanged);
        getEditorEngine().stopPlayer();
        mOperationHasDone = false;

        selectTransition();
        loadData();
    }

    private void loadData() {
        if (NetworkPermissionManager.isUseOpenNetwork()) {
            mViewModel.loadData(true);
        } else {
            NetworkPermissionHelper.INSTANCE.showNetworkPermissionDialog(
                    mContext,
                    com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_title,
                    com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_transition,
                    NetworkPermissionHelper.Scene.TRANSITION_LIST,
                    () -> {
                        if (NetworkMonitor.isNetworkValidated()) {
                            mViewModel.loadData(true);
                        } else {
                            ToastUtil.showLongToast(R.string.videoeditor_editor_no_network);
                            mViewModel.loadData(false);
                        }
                        return null;
                    },
                    () -> {
                        mViewModel.loadData(false);
                        return null;
                    },
                    () -> {
                        mViewModel.loadData(false);
                        return null;
                    }
            );
        }
    }

    private void onItemListChanged(List<TransitionEntity> entities) {
        mUIController.notifyDataSetChanged(entities);
    }

    private void onItemChanged(TransitionEntity entity) {
        mUIController.notifyItemChanged(entity);
    }

    @Override
    public void resume(boolean isActivityResume) {
        super.resume(isActivityResume);
    }

    /**
     * 设置转场监听器
     * @param transitionListener 转场监听器
     */
    public void setTransitionListener(TransitionListener transitionListener) {
        this.mTransitionListener = transitionListener;
    }

    /**
     * 转场点击回调
     * @param index 新的索引
     * @param isTail 点击的是否为片段右侧的转场按钮
     */
    public void onTransitionClick(int index, boolean isTail) {
        if (index != mCurrentIndex) {
            mCurrentIndex = index;
            selectTransition();
            WeakReference<TimelineViewModel> ref = getWeakTimelineViewModel();
            if (ref != null) {
                TimelineViewModel timelineViewModel = ref.get();
                if (timelineViewModel != null) {
                    // 缩图轴滚到转场位置并且暂停播放
                    ClipModel clipModel = timelineViewModel.getClipModel(AppConstants.Number.NUMBER_0, index);
                    if (clipModel != null) {
                        timelineViewModel.scrollToTransition(clipModel);
                    }
                    mEditorEngine.stopPlayer();
                } else {
                    GLog.w(TAG, LogFlag.DL, "[updateCurrentIndex] timelineViewModel is null");
                }
            } else {
                GLog.e(TAG, LogFlag.DL, "[updateCurrentIndex] ref is null");
            }
        }
        GLog.i(TAG, LogFlag.DL, "[updateCurrentIndex] mCurrentClipIndex = " + mCurrentIndex);
    }

    /**
     * 更新场景过渡
     * @param entity 场景过渡实体
     * @param videoTrack 视频轨道
     * @param isApplyToAll 是否应用至全部
     */
    private void updateAndPlayTransition(@NonNull TransitionEntity entity, @NonNull IVideoTrack videoTrack, boolean isApplyToAll) {
        boolean isUpdated = false;
        if (isApplyToAll) {
            int transitionCount = videoTrack.getTransitionCount();
            for (int i = 0; i < transitionCount; i++) {
                if (i == mCurrentIndex) {
                    isUpdated = updateTransition(entity, i, videoTrack);
                } else {
                    updateTransition(entity, i, videoTrack);
                }
            }
        } else {
            isUpdated = updateTransition(entity, mCurrentIndex, videoTrack);
        }

        // 播放转场
        if (isUpdated && !entity.getResourceId().isEmpty()) {
            IClip clip = videoTrack.getClip(mCurrentIndex);
            IClip nextClip = null;
            int nextClipIndex = mCurrentIndex + AppConstants.Number.NUMBER_1;
            if (nextClipIndex < videoTrack.getClipCount()) {
                nextClip = videoTrack.getClip(nextClipIndex);
                if (!isApplyToAll) {
                    ClipModel clipModel = updateVideoClipModelEffect(nextClipIndex, (IVideoClip) nextClip);
                    if ((getWeakTimelineViewModel() != null) && (getWeakTimelineViewModel().get() != null)) {
                        getWeakTimelineViewModel().get().notifyUpdateClip(clipModel);
                    }
                }
            }

            playTransitionBetween(clip, nextClip);
        }
        mOperationHasDone = true;
    }

    /**
     * 更新场景
     * @param entity 场景实体
     * @param clipIndex 片段索引
     * @param videoTrack 视频轨道
     * @return 更新是否成功
     */
    private boolean updateTransition(@NonNull TransitionEntity entity, int clipIndex, @NonNull IVideoTrack videoTrack) {
        String resourceId = entity.getResourceId();
        if (resourceId.isEmpty()) {
            videoTrack.setTransition(clipIndex, null);
        } else {
            BaseTransition transition = mEditorEngine.getVideoFilterManager().getTransition(resourceId);
            if (transition == null) {
                GLog.e(TAG, LogFlag.DL, "[updateTransition] transition " + resourceId + " is not exist");
                return false;
            }

            IClip clip = videoTrack.getClip(clipIndex);
            if (clip == null) {
                GLog.e(TAG, LogFlag.DL, "[updateTransition] clip is null");
                return false;
            }

            videoTrack.setTransition(clipIndex, transition);

            ClipModel clipModel = updateVideoClipModelEffect(clipIndex, (IVideoClip) clip);
            if ((getWeakTimelineViewModel() != null) && (getWeakTimelineViewModel().get() != null)) {
                getWeakTimelineViewModel().get().notifyUpdateClip(clipModel);
            }
        }
        return true;
    }

    /**
     * 播放场景过渡
     * @param clip 前一个片段
     * @param nextClip 后一个片段
     */
    private void playTransitionBetween(@NonNull IClip clip, @Nullable IClip nextClip) {
        if (getTimeline() == null) {
            GLog.e(TAG, LogFlag.DL, "[playTransitionBetween] timeline is null");
            return;
        }

        long clipOutPoint = clip.getOutPoint();
        long clipInPoint = clip.getInPoint();
        long startPlayTime = clipOutPoint - EditorEngine.TIME_BASE / 2;
        long endPlayTime = clipOutPoint + EditorEngine.TIME_BASE / 2 + FRAME_DURATION;
        if (startPlayTime < 0) {
            startPlayTime = 0;
        }
        if (startPlayTime < clipInPoint) {
            startPlayTime = clipInPoint;
        }
        if (nextClip != null) {
            long nextOutPoint = nextClip.getOutPoint();
            if (endPlayTime > nextOutPoint) {
                endPlayTime = nextOutPoint;
            }
        }
        if (endPlayTime > getTimeline().getDuration()) {
            endPlayTime = getTimeline().getDuration();
        }
        getEditorEngine().startPlayer(startPlayTime, endPlayTime);
    }

    @Override
    public void onPlayPositionChange(long currentPosition) {
        if (mTransitionListener != null) {
            mTransitionListener.onPlayPositionChange(currentPosition);
        }
    }

    public void selectTransition() {
        String transName = getTransitionName();
        mUIController.setCurrentTransitionIndex(transName);
    }

    private String getTransitionName() {
        EditorEngine engine = getEditorEngine();
        if (engine == null) {
            GLog.e(TAG, LogFlag.DL, "engine is null");
            return TextUtil.EMPTY_STRING;
        }
        ITimeline timeline = engine.getCurrentTimeline();
        if (timeline == null) {
            GLog.e(TAG, LogFlag.DL, "timeline is null");
            return TextUtil.EMPTY_STRING;
        }
        IVideoTrack videoTrack = timeline.getVideoTrack(0);

        if (videoTrack == null) {
            GLog.e(TAG, LogFlag.DL, "video track is null");
            return TextUtil.EMPTY_STRING;
        }

        BaseTransition transition = videoTrack.getTransition(mCurrentIndex);
        if (transition != null) {
            return transition.getName();
        } else {
            return TextUtil.EMPTY_STRING;
        }
    }

    /**
     * EditorTransitionUIController.OnIconClickListener的监听方法
     */
    @Override
    public void onSelectTransitionEntity(TransitionEntity entity, int position, boolean isApplyToAll) {
        if (getVideoTrack() == null) {
            GLog.e(TAG, LogFlag.DL, "[onIconClick] Can not handle transition");
            return;
        }

        if (entity.isNeedDownloadFile()) {
            if (!NetworkPermissionManager.isUseOpenNetwork()) {
                NetworkPermissionHelper.INSTANCE.showNetworkPermissionDialog(
                        mContext,
                        com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_title,
                        com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_transition,
                        NetworkPermissionHelper.Scene.TRANSITION_ITEM_DOWNLOAD,
                        () -> {
                            downloadTransition(position, entity, isApplyToAll);
                            return null;
                        },
                        null,
                        null
                );
            } else {
                downloadTransition(position, entity, isApplyToAll);
            }
        } else {
            updateAndPlayTransition(entity, getVideoTrack(), isApplyToAll);
        }
    }

    private void downloadTransition(int position, TransitionEntity entity, boolean isApplyToAll) {
        mViewModel.download(entity.getId(), new OnLoadFileListener<TransitionItem>() {
            @Override
            public void onProgress(int progress, TransitionItem item) {
                entity.setProgress(item.getProgress());
                requireEditorActivity().runOnUiThread(() -> mUIController.notifyItemChanged(position));
            }

            @Override
            public void onFinish(TransitionItem item) {
                entity.mergeFrom(item);
                requireEditorActivity().runOnUiThread(() -> {
                    mUIController.notifyItemChanged(position);
                    updateAndPlayTransition(entity, getVideoTrack(), isApplyToAll);
                });
            }

            @Override
            public void onError(@NonNull ErrorCode errCode, TransitionItem item) {
                entity.setProgress(0);
                requireEditorActivity().runOnUiThread(() -> {
                    switch (errCode) {
                        case NO_NETWORK:
                            ToastUtil.showLongToast(R.string.videoeditor_editor_no_network);
                            break;
                        case NO_NETWORK_WHEN_DOWNLOADING:
                            ToastUtil.showLongToast(R.string.videoeditor_download_network_disconnect);
                            break;
                        default:
                            ToastUtil.showShortToast(R.string.videoeditor_download_fail);
                            break;
                    }
                    mUIController.notifyItemChanged(position);
                });
            }

            @Override
            public void onCancel(TransitionItem item) {
                entity.setProgress(0);
                requireEditorActivity().runOnUiThread(() -> mUIController.notifyItemChanged(position));
            }
        });
    }

    @Override
    protected EditorTrackScene getEditorTrackScene() {
        return EditorTrackScene.TRANSITION;
    }

    @Override
    public void destroy() {
        super.destroy();
        mViewModel.getItemList().removeObserver(this::onItemListChanged);
        mViewModel.getChangedItem().removeObserver(this::onItemChanged);
    }

    /**
     * 转场业务接口监听器
     */
    public interface TransitionListener {
        /**
         * 播放位置变化监听
         * @param position 当前播放位置
         */
        default void onPlayPositionChange(long position) { }
    }
}
