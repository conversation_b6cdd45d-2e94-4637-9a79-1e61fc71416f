/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ResourceLoader
 ** Description:资源预加载器
 ** Version: 1.0
 ** Date: 2025-07-24
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2025-07-24     1.0
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.controler

import android.content.Context
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener
import com.oplus.gallery.videoeditorpage.memories.resource.listener.SimpleLoadFileListener
import com.oplus.gallery.videoeditorpage.resource.base.BaseResourceManager
import com.oplus.gallery.videoeditorpage.resource.listener.SimpleLoadDataListener
import com.oplus.gallery.videoeditorpage.resource.manager.CaptionFontResourceManager
import com.oplus.gallery.videoeditorpage.resource.manager.CaptionStyleResourceManager
import com.oplus.gallery.videoeditorpage.resource.manager.SongResourceManager
import com.oplus.gallery.videoeditorpage.resource.manager.TransitionResourceManager
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.business.caption.EditorCaptionState
import com.oplus.gallery.videoeditorpage.video.business.music.EditorSongState
import com.oplus.gallery.videoeditorpage.video.business.transition.EditorTransitionState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume

class ResourceLoader(context: Context) {

    private val transitionResourceManager = TransitionResourceManager(context)
    private val songResourceManager = SongResourceManager(context)
    private val captionStyleResourceManager = CaptionStyleResourceManager(context)
    private val captionFontResourceManager = CaptionFontResourceManager(context)

    /**
     * 页面和对应的资源管理器，新增预加载资源，理论上只要这里加配置就可以，默认只加载清单、缩图、文本，资源包需要另外加在[allowResourcePackageManagers]
     */
    private val stateManagerMap = mapOf<Class<out EditorBaseState<*>>, List<BaseResourceManager<out Item>>>(
        EditorTransitionState::class.java to listOf(transitionResourceManager),
        EditorSongState::class.java to listOf(songResourceManager),
        EditorCaptionState::class.java to listOf(
            captionStyleResourceManager,
            captionFontResourceManager
        ),
    )

    /**
     * 允许下载资源包的管理类名单
     */
    private val allowResourcePackageManagers = listOf(transitionResourceManager)

    fun load(editorEngine: EditorEngine) {
        AppScope.launch(Dispatchers.IO) {
            val requestNetwork = NetworkMonitor.isNetworkValidated() && NetworkPermissionManager.isUseOpenNetwork
            transitionResourceManager.init(editorEngine)
            loadResourcesWithPriority(requestNetwork, stateManagerMap.values.toList().flatten())
        }
    }

    private suspend fun loadResourcesWithPriority(
        requestNetwork: Boolean,
        resourceManagers: List<BaseResourceManager<out Item>>
    ) = withContext(Dispatchers.IO) {
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL, "loadResourcesWithPriority, start")
        }
        // 第一阶段：并发加载所有资源列表
        val resourceLists = resourceManagers.map { manager ->
            async {
                suspendCancellableCoroutine<List<Item>> { continuation ->
                    manager.loadList(requestNetwork, object : SimpleLoadDataListener<Item>() {
                        override fun onLoadFinish(items: List<Item>) {
                            continuation.resume(items)
                        }
                        override fun onError(errCode: ErrorCode) {
                            continuation.resume(emptyList())
                        }
                    })
                }
            }
        }.awaitAll()

        // 第二阶段：并发下载所有缩图和文本
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL, "loadResourcesWithPriority, load icon and text")
        }
        resourceLists.flatMapIndexed { managerIndex, items ->
            val manager = resourceManagers[managerIndex]
            if (manager.isReleased) return@flatMapIndexed emptyList()
            items.flatMap { item ->
                listOf(
                    async { loadIconSuspend(manager, item) },
                    async { loadTextTranslateSuspend(manager, item) }
                )
            }
        }.awaitAll()

        // 第三阶段：下载资源包文件
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL, "loadResourcesWithPriority, load file")
        }
        resourceLists.forEachIndexed { managerIndex, items ->
            val manager = resourceManagers[managerIndex]
            if (manager.isReleased) return@forEachIndexed
            if (allowResourcePackageManagers.contains(manager)) {
                items.map { item ->
                    async {
                        loadFileSuspend(manager, item)
                    }
                }
            }
        }
    }

    private suspend fun loadIconSuspend(manager: BaseResourceManager<out Item>, item: Item) {
        suspendCancellableCoroutine {
            if (DEBUG) {
                GLog.d(TAG, LogFlag.DL, "loadIconSuspend: ${item.itemUniqueId}")
            }
            manager.loadIcon(item.itemUniqueId, createFileListener("icon", it))
        }
    }

    private suspend fun loadTextTranslateSuspend(manager: BaseResourceManager<out Item>, item: Item) {
        suspendCancellableCoroutine {
            if (DEBUG) {
                GLog.d(TAG, LogFlag.DL, "loadTextTranslateSuspend: ${item.itemUniqueId}")
            }
            manager.loadTextTranslate(item.itemUniqueId, createFileListener("text", it))
        }
    }

    private suspend fun loadFileSuspend(manager: BaseResourceManager<out Item>, item: Item) {
        suspendCancellableCoroutine {
            if (DEBUG) {
                GLog.d(TAG, LogFlag.DL, "loadFileSuspend: ${item.itemUniqueId}")
            }
            manager.loadFile(item.itemUniqueId, createFileListener("file", it))
        }
    }

    private fun createFileListener(tag: String, continuation: Continuation<Unit>): OnLoadFileListener<Item> {
        return object : SimpleLoadFileListener<Item>() {
            override fun onFinish(item: Item) {
                if (DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "onFinish: $tag, ${item.itemUniqueId}")
                }
                continuation.resume(Unit)
            }

            override fun onCancel(item: Item) {
                if (DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "onCancel: $tag, ${item.itemUniqueId}")
                }
                continuation.resume(Unit)
            }

            override fun onError(errCode: ErrorCode, item: Item?) {
                if (DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "onError: $tag, ${item?.itemUniqueId}")
                }
                continuation.resume(Unit)
            }
        }
    }

    /**
     * manager不是单例，为了健壮性，切页面时，中止目标页面对应资源的加载，进去也会重新发起的
     */
    fun onStateChange(currentState: EditorBaseState<*>, previousState: EditorBaseState<*>?) {
        stateManagerMap[currentState::class.java]?.let { managers ->
            if (DEBUG) {
                GLog.d(TAG, LogFlag.DL, "onStateChange: ${currentState::class.java.simpleName}, ${managers::class.java.simpleName}")
            }
            managers.forEach { it.release() }
        }
    }

    fun release() {
        stateManagerMap.values.flatten().forEach { it.release() }
    }

    companion object {
        private const val TAG = "ResourceLoader"
        private const val DEBUG = false
    }
}