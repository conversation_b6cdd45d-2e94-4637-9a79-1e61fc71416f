/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudSongLibFragment.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/6/6
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/6/6        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.music.source.cloud

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle.isRunningForeground
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.waveform.ui.AudioWaveView
import com.oplus.gallery.videoeditorpage.databinding.VideoeditorFragmentChildMusicListBinding
import com.oplus.gallery.videoeditorpage.resource.room.bean.SongItem
import com.oplus.gallery.videoeditorpage.resource.room.entity.BaseMusicItem
import com.oplus.gallery.videoeditorpage.resource.room.entity.SongEntity
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.utlis.NetworkPermissionHelper
import com.oplus.gallery.videoeditorpage.video.business.base.ApplyResourceData
import com.oplus.gallery.videoeditorpage.video.business.base.BaseResourceViewModel.ApplyResourceState
import com.oplus.gallery.videoeditorpage.video.business.base.BaseResourceViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.videoeditorpage.video.business.music.BaseMusicFragment
import com.oplus.gallery.videoeditorpage.video.business.music.player.ISongPlayer
import com.oplus.gallery.videoeditorpage.video.business.music.player.SongPlayer
import com.oplus.gallery.videoeditorpage.video.business.music.player.SongPlayer.Companion.IDLE_PLAY_PERCENT
import com.oplus.gallery.videoeditorpage.video.business.music.source.SongLibraryDialog
import com.oplus.gallery.videoeditorpage.video.business.music.source.adapter.SongLibraryAdapter
import com.oplus.gallery.videoeditorpage.video.business.music.source.cloud.util.MusicConstants
import com.oplus.gallery.videoeditorpage.video.business.music.source.cloud.util.SpaceItemDecoration
import com.oplus.gallery.videoeditorpage.video.business.music.viewmodel.EditorSongVM
import kotlinx.coroutines.launch
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingR

/**
 * [CloudSongLibFragment] 是一个用于展示云端歌曲库的片段类。
 * 它继承自 [BaseFragment]，并实现了 [SongLibraryDialog.OnPageChangeListener] 和 [AudioWaveView.OnWaveScrollListener] 接口，
 * 以便响应页面变化和波形滚动事件。
 */
class CloudSongLibFragment : BaseMusicFragment(), SongLibraryDialog.OnPageChangeListener, AudioWaveView.OnWaveScrollListener {
    /**
     * 此vm是根据EditorActivity生命周期销毁的
     */
    private val editorSongVM: EditorSongVM by lazy {
        ViewModelProvider(requireActivity())[EditorSongVM::class.java]
    }
    private val onEventListener by lazy {
        object : AVController.OnEventListener {
            /**
             * 当 AV 播放器发生错误时调用此方法。
             *
             * @param avController AV 播放器控制器。
             * @param what 错误类型标识符。
             * @param extra 附加错误信息。
             * @param details 错误的详细描述。
             */
            override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
                updatePlayState(AVController.PlaybackState.ERROR)
            }

            /**
             * 当 AV 播放器发出信息时调用此方法。
             *
             * @param avController AV 播放器控制器。
             * @param what 信息类型标识符。
             * @param extra 附加信息。
             * @param details 信息的详细描述。
             */
            override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
            }

            /**
             * 当 AV 播放器的播放状态发生变化时调用此方法。
             *
             * @param avController AV 播放器控制器。
             * @param state 播放状态。
             */
            override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
                updatePlayState(state)
            }
        }
    }


    private var musicPlayer: ISongPlayer? = null
    private var musicLibraryAdapter: SongLibraryAdapter? = null
    private var categoryId: Int = MusicConstants.CategoryId.CLOUD_CATEGORY
    private var binding: VideoeditorFragmentChildMusicListBinding? = null
    private var lastClickSong: BaseMusicItem? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = VideoeditorFragmentChildMusicListBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        categoryId = arguments?.getInt(KEY_CATEGORY_ID, MusicConstants.CategoryId.CLOUD_CATEGORY) ?: MusicConstants.CategoryId.CLOUD_CATEGORY
        initRecyclerView()
        addObserve()
        loadData()
    }

    private fun useMusicData(data: BaseMusicItem?) {
        if (data != null) {
            GLog.d(TAG, LogFlag.DL, "useMusicData,mOnMusicSelectListener is null : " + (null == mOnMusicSelectListener))
            mOnMusicSelectListener?.onSelected(data, categoryId, mFirstTabId) //埋点需要上报音频的一级tab和二级tab，所以要一并带入
        }
    }

    private fun initRecyclerView() {
        binding?.musicList?.let {
            it.layoutManager = LinearLayoutManager(activity, RecyclerView.VERTICAL, false)
            it.itemAnimator = null
            // 设置 item 间距为 8dp
            val space = resources.getDimensionPixelSize(R.dimen.videoeditor_music_list_item_space)
            it.addItemDecoration(SpaceItemDecoration(space))

            musicLibraryAdapter = SongLibraryAdapter(it).apply {
                <EMAIL> = <EMAIL>
                viewLifecycleOwner.lifecycle.addObserver(this@apply)
                <EMAIL> = this@CloudSongLibFragment
                <EMAIL> = object : SongLibraryAdapter.ItemClickListener<BaseMusicItem> {
                    override fun onBtnClick(entity: BaseMusicItem, pos: Int) {
                        useMusicData(entity)
                    }

                    override fun onItemViewClick(view: View?, position: Int, entity: BaseMusicItem, isCoverClicked: Boolean) {

                        /*更新 lastClickSong 在所有播放决策完成后*/
                        (entity as? SongEntity?)?.let { musicEntity ->
                            val isSameSong = lastClickSong?.songId == musicEntity.songId
                            lastClickSong = entity
                            /**
                             * 下载逻辑 + 播放逻辑整合
                             */
                            if (musicEntity.isDownloaded().not() && (categoryId == MusicConstants.CategoryId.CLOUD_CATEGORY)) {
                                // 检查是否有联网权限，无权限弹框请求用户授权，否则直接走下载
                                if (NetworkPermissionManager.isUseOpenNetwork.not()) {
                                    showNetPermissionDialog(NetworkPermissionHelper.Scene.SONG_ITEM_DOWNLOAD) {
                                        GLog.d(TAG, LogFlag.DL, "[onItemViewClick] SONG_ITEM_DOWNLOAD")
                                        downloadMusic(musicEntity)
                                    }
                                } else {
                                    downloadMusic(musicEntity)
                                }
                                return
                            }


                            <EMAIL>(position)
                            /*播放/暂停判断*/
                            if (isSameSong && (musicPlayer != null)) {
                                musicPlayer?.let { av ->
                                    if (av.isPlaying()) {
                                        av.pause()
                                    } else {
                                        av.play()
                                    }
                                }
                            } else {
                                playSong(entity, true)
                            }
                        }
                    }
                }
            }
            it.adapter = musicLibraryAdapter
            (it.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        }
    }

    private fun downloadMusic(musicEntity: SongEntity) {
        val toSongItem = musicEntity.convertToSongItem()
        editorSongVM.download(toSongItem.itemUniqueId)
    }

    private fun addObserve() {
        lifecycleScope.launch {
            // 在生命周期处于 STARTED 状态时重复收集数据流
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                editorSongVM.listData.collect {
                    processData(it)
                }
            }
        }
        editorSongVM.applyResourceState.observe(viewLifecycleOwner) { processUiState(it) }
    }

    /**
     * 根据传入的 UI 状态处理相应的操作。
     * 如果状态不是 LOADING_FILE，则打印日志。
     * 根据不同的状态，执行不同的操作，如通知音乐库适配器数据已更改、显示下载失败的提示等。
     *
     * @param it 传入的 EditorSongVM 的 ApplyResourceState 状态
     */
    private fun processUiState(it: ApplyResourceData) {
        GLog.d(TAG, LogFlag.DL, "[processUiState],state:${it.state}, errorCode:${it.errorCode}")
        when (it.state) {
            ApplyResourceState.LOAD_FILE_CANCEL, ApplyResourceState.LOADING_FILE -> refreshDownloadingItem()

            ApplyResourceState.LOAD_FILE_FINISH -> {
                refreshDownloadedItem()
                /* 音乐列表图标下载也是回调到这里，原有设计，列表音乐图标是取本地路径进行加载，首次拉去音乐列表后，图标还没下载到本地，等下载完回调到这里，
                若不在这里处理下音乐数据项更新UI，列表上的音乐图标路径就不会更新，用户就一直看不到图标展示 */
                it.data?.let { data -> (data as? SongItem) }?.let { songItem ->
                    musicLibraryAdapter?.run {
                        data.indexOfFirst { item -> (item?.songId == songItem.songId.toInt()) }.takeIf { index -> (index != -1) }?.let { index ->
                            data[index]?.run {
                                iconUrl = songItem.thumbnailPath
                                notifyItemChanged(index)
                            }
                        }
                    }
                }
            }

            ApplyResourceState.LOAD_FILE_ERROR -> {

                if (isRunningForeground()) {
                    when (it.errorCode) {
                        ErrorCode.NO_NETWORK_WHEN_DOWNLOADING -> ToastUtil.showShortToast(R.string.videoeditor_download_network_disconnect)

                        ErrorCode.NO_NETWORK -> ToastUtil.showShortToast(R.string.videoeditor_editor_no_network)
                        // 检查错误码是否为无网络访问权限
                        ErrorCode.NO_ALLOW_OPEN_NETWORK -> {
                            showNetPermissionDialog(NetworkPermissionHelper.Scene.SONG_LIST) {
                                // 用户授权后加载云端音乐数据
                                GLog.d(TAG, LogFlag.DL, "[processUiState] SONG_LIST")
                                editorSongVM.loadData(MusicConstants.CategoryId.CLOUD_CATEGORY)
                            }
                        }

                        ErrorCode.GENERATE_KEY_ERROR -> GLog.e(TAG, LogFlag.DL, "[processUiState], generate_key_error")
                        else -> ToastUtil.showShortToast(R.string.videoeditor_download_fail)
                    }
                }
            }

            else -> Unit
        }
    }

    /**
     * 显示网络权限请求对话框。
     *
     * @param scene 当前网络权限请求的场景
     * @param invoke 用户允许打开网络时的回调函数
     */
    private fun showNetPermissionDialog(scene: NetworkPermissionHelper.Scene, invoke: () -> Unit) {
        activity?.let {
            // 显示网络权限请求对话框
            NetworkPermissionHelper.showNetworkPermissionDialog(it,
                AuthorizingR.string.authorizing_request_network_title,
                AuthorizingR.string.authorizing_request_network_music,
                scene,
                onAllowOpenNetwork = {
                    invoke()
                })
        }
    }

    /**
     * 刷新已下载的项目
     */
    private fun refreshDownloadedItem() {
        // 获取当前点击歌曲的位置，如果不存在则使用 INVALID_INDEX
        val currentPostion = editorSongVM.downloadedMap[lastClickSong?.songId] ?: INVALID_INDEX
        // 如果 musicLibraryAdapter 不为空，则进行相关操作
        musicLibraryAdapter?.let {
            // 如果当前点击歌曲的位置有效，则播放该歌曲
            if (currentPostion >= 0) {
                (it.data.getOrNull(currentPostion) as? SongEntity)?.run {
                    it.toggle(currentPostion)
                    playSong(this, false)
                }
            }
            // 遍历已下载的歌曲映射，刷新每个已下载歌曲的 UI 项
            editorSongVM.downloadedMap.forEach { (songId, position) ->
                musicLibraryAdapter?.notifyItemChanged(position)
                // 从下载中映射中移除已下载的歌曲
                editorSongVM.downloadingMap.remove(songId)
            }
            // 清空已下载的歌曲映射
            editorSongVM.downloadedMap.clear()
        }
    }


    /**
     * 刷新下载中 项 进度更新
     */
    private fun refreshDownloadingItem() {
        editorSongVM.downloadingMap.forEach { (_, position) ->
            musicLibraryAdapter?.notifyItemChanged(position)
        }
    }

    private fun processData(resource: List<BaseMusicItem>?) {
        GLog.d(TAG, LogFlag.DL, "[processData],size= ${resource?.size}")
        if (resource != null) {
            musicLibraryAdapter?.updateListData(resource)
        } else {
            musicLibraryAdapter?.clearListAllData()
        }
    }

    /**
     * 加载数据。首先检查网络权限，如果未被使用，则显示网络权限对话框；
     * 否则根据 categoryId 加载数据。
     * 如果 categoryId 为 CLOUD_CATEGORY，则加载云端音乐；
     * 如果 categoryId 为 BUILDIN_CATEGORY，则加载内置资源。
     */
    private fun loadData() {
        editorSongVM.loadData(MusicConstants.CategoryId.BUILDIN_CATEGORY)
        editorSongVM.loadData(MusicConstants.CategoryId.CLOUD_CATEGORY)
    }

    /**
     * tab页面切换,恢复未展开状态
     */
    override fun onChange() {
        GLog.e(TAG, LogFlag.DL, "onPageChange")
        releaseMusicPlayer()
        musicLibraryAdapter?.clearSelectionPosition()
    }

    fun playSong(songItem: BaseMusicItem?, isUserAction: Boolean) {
        releaseMusicPlayer()
        songItem?.getOrLog(TAG, "[playSong],NULL")?.let { s ->
            val path = s.filePath
            GLog.d(TAG, LogFlag.DL, "[applySong], path= $path, isUserAction= $isUserAction")
            SongPlayer(requireActivity()).run {
                musicPlayer = this
                this.setListener(onEventListener)
                path?.let {
                    setDataSource(it)
                    prepare()
                    if (isRunningForeground()) {
                        play()
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        musicPlayer?.pause()
    }

    override fun onDestroy() {
        super.onDestroy()
        editorSongVM.clearDownloadStatus()
        releaseMusicPlayer()
    }

    /**
     * 更新播放状态并通知适配器刷新视图。
     *
     * @param state 当前的播放状态。
     */
    private fun updatePlayState(state: AVController.PlaybackState) {
        GLog.d(TAG, LogFlag.DL, "[updatePlayState] playState=${state}")
        musicLibraryAdapter?.let { adapter ->
            adapter.data.getOrNull(adapter.getSelectionIndex())?.let {
                it.playState = state
                binding?.musicList?.let { musicList ->
                    musicList.post {
                        if (musicList.isComputingLayout.not()) {
                            GLog.d(TAG, LogFlag.DL, "[updatePlayState],${adapter.getSelectionIndex()}")
                            adapter.notifyItemChanged(adapter.getSelectionIndex())
                        }
                    }
                }
            }
        }
    }

    /**
     * 当用户结束拖动波纹 音频/视频进度条时调用的方法
     * @param percent 用户拖动结束时的播放进度百分比，表示播放位置与总时长的比例
     * @param duration 音频/视频的总时长，单位为毫秒
     */
    override fun onSeekEnd(percent: Float, duration: Long) {
        musicPlayer?.run {
            val progress = (percent * duration * TimeUtils.MILLISECOND_IN_SECOND).toLong()
            GLog.d(TAG, LogFlag.DL, "[onSeekEnd], percent= $percent, $duration,$progress")
            playTo(progress)
        }
    }

    override fun getPlayPercent(): Float {
        val progress = musicPlayer?.getPlayCurrentPercent() ?: IDLE_PLAY_PERCENT
        GLog.d(TAG, LogFlag.DL, "[getPlayPercent], progress= $progress")
        return progress
    }

    /**
     * 释放音乐播放器资源，并将相关引用置为 null
     */
    private fun releaseMusicPlayer() {
        musicPlayer?.release()
        musicPlayer = null
    }

    companion object {
        private const val TAG = "CloudSongLibFragment"
        private const val KEY_CATEGORY_ID = "category"
        const val ASSETS: String = "assets:/"
    }
}
