/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorThemeUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.theme;

import static com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolderKt.setViewSelectedState;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;

import com.coui.appcompat.progressbar.COUICircularProgressBar;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuItemBorderAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable;
import com.oplus.gallery.business_lib.template.editor.anim.EditorMenuItemBorderAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.base.EditorMemoriesBaseUiController;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListenerImpl;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.LocalSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;
import com.oplus.gallery.videoeditorpage.memories.autodownload.RtnCode;
import com.oplus.gallery.videoeditorpage.memories.business.data.EditorAssetsMenuAdapter;

import java.util.ArrayList;
import java.util.List;

public class EditorThemeUIController extends EditorMemoriesBaseUiController {
    private static final String TAG = "EditorThemeUIController";
    private static final int PROGRESS_RATIO = 2;
    private List<ThemeItem> mData;
    private ArrayMap<Integer, Integer> mThemePositionInfoMap = new ArrayMap<>();
    private ArrayMap<Integer, Integer> mMusicPositionInfoMap = new ArrayMap<>();
    private int mCurrentSelectedPosition = 0;
    private int mLastApplyItemPosition = 0;

    public EditorThemeUIController(Context context, ViewGroup rootView, EditorBaseState state) {
        super(context, rootView, state, MONITOR_DOWNLOAD);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_memories_editor_sub_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_video_editor_menu_list_layout;
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_text_preview_editor_theme;
    }

    @Override
    public void onShow() {
        hidePlayButtonTimeContainer();
        setMenuListView(mContainer.findViewById(R.id.horizontal_list));
        getMenuListView().keepLastFocusItem(true);
        initData();
        initAdapter();
        updateNetworkSourceLists();
        super.onShow();
    }

    public void updateNetworkSourceLists() {
        updateMusicLists();
    }

    private void initAdapter() {
        mAdapter = new EditorAssetsMenuAdapter<ThemeItem>(mContext, mData, 0) {
            private float mUnselectedAlpha = 0f;
            private float mSelectedAlpha = Color.alpha(
                mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_out_border_stroke_color)
            ) * 1f / EditorUIConfig.MAX_ALPHA_VALUE;

            @Override
            public void bindData(BaseRecycleViewHolder viewHolder, int position, EditorMenuItemViewData data) {
                super.bindData(viewHolder, position, data);
                ThemeItem item = (ThemeItem) data;
                EditorMenuItemView preview = getMenuItemView(viewHolder);
                COUICircularProgressBar progressbar = viewHolder.itemView.findViewById(R.id.download_progress_bar);
                boolean isNeedDownloadFile = item.isNeedDownloadFile();
                int musicProgress = MusicSourceManager.getInstance().getDownloadProgress(item.getSongId());
                int themeProgress = ThemeSourceManager.getInstance().getDownloadProgress(item.getThemeId());
                int progress = LocalSourceManager.DEFAULT_PROGRESS;
                if (themeProgress == LocalSourceManager.DEFAULT_PROGRESS) {
                    if (musicProgress != LocalSourceManager.DEFAULT_PROGRESS) {
                        progress = musicProgress / PROGRESS_RATIO;
                    }
                } else {
                    progress = LocalSourceManager.MAX_PROGRESS / PROGRESS_RATIO
                            + themeProgress / PROGRESS_RATIO;
                }
                boolean isAutoDownload =
                        ThemeSourceManager.getInstance().isAutoDownload(item.getThemeId());
                boolean isDrawForegroundColor = false;
                if (!isAutoDownload && isNeedDownloadFile && (LocalSourceManager.MIN_PROGRESS <= progress)
                        && (progress < LocalSourceManager.MAX_PROGRESS)) {
                    isDrawForegroundColor = true;
                    progressbar.setVisibility(View.VISIBLE);
                    progressbar.setProgress(progress);
                } else {
                    isDrawForegroundColor = false;
                    progressbar.setVisibility(View.GONE);
                }

                preview.setIconResource(R.drawable.videoeditor_ic_downloadable);

                if (isAutoDownload
                        || (isNeedDownloadFile && (progress < LocalSourceManager.MIN_PROGRESS))) {
                    isDrawForegroundColor = true;
                    preview.setDrawCenterIcon(true);
                } else {
                    preview.setDrawCenterIcon(false);
                }
                preview.setDrawForegroundColor(isDrawForegroundColor);

                boolean isSelected = item.isSelected() && !isNeedDownloadFile;
                preview.setSelected(isSelected);
                setViewSelectedState(viewHolder, isSelected ? Selectable.SELECTED : Selectable.UNSELECTED);
            }

            @Override
            public BaseRecycleViewHolder createViewHolder(View itemView, int viewType) {
                EditorMenuItemBorderAnimViewHolder holder = new EditorMenuItemBorderAnimViewHolder(
                    itemView,
                    new EditorMenuItemBorderAnimation(),
                    new EditorPressAnimation());
                setSupportPressAnim(holder);
                holder.setSelectedAnimEnable(true);
                holder.setSelectedAnimView(holder);
                holder.setDisableAlpha(mUnselectedAlpha);
                holder.setUnselectedAlpha(mUnselectedAlpha);
                holder.setSelectedAlpha(mSelectedAlpha);
                return holder;
            }
        };
        closeDefaultMoveAnimator(getMenuListView());
        mAdapter.setHasStableIds(true);
        mAdapter.setCanUnselectCurrentPosition(false);
        mAdapter.setItemClickListener(this);
        int currentPos = mEditorState.getEngineManager().getThemeCurrentThemePos();
        mAdapter.select(currentPos);
        mCurrentSelectedPosition = currentPos;
        mLastApplyItemPosition = currentPos;
        getMenuListView().setAdapter(mAdapter);
        getMenuListView().scrollToPosition(currentPos);
        getMenuListView().setKeepFocusItemPosition(currentPos);
    }

    private void initData() {
        mData = ThemeSourceManager.getInstance().queryIconExistedMemoriesTheme();
        if ((mData == null) || (mData.size() == 0)) {
            ThemeSourceManager.getInstance().checkBuiltinItem(true);
            mData = ThemeSourceManager.getInstance().queryIconExistedMemoriesTheme();
        }
        if (mData == null) {
            GLog.e(TAG, "getResourceLists mData is null!");
            return;
        }
        updatePositionInfo();
        updateDownloadSource();
    }

    private void updatePositionInfo() {
        if (mData != null) {
            for (int i = 0; i < mData.size(); i++) {
                mThemePositionInfoMap.put(mData.get(i).getThemeId(), i);
                mMusicPositionInfoMap.put(mData.get(i).getSongId(), i);
            }
        }
    }

    private void updateDownloadSource() {
        if (mData != null) {
            for (int i = 0; i < mData.size(); i++) {
                ThemeItem item = mData.get(i);
                if (!item.isBuiltin() && !item.isNeedDownloadFile()) {
                    ThemeSourceManager.getInstance().resetSource(item.getThemeId());
                }
            }
        }
    }

    private List<ThemeItem> getMemoriesThemeList(List<ThemeItem> list) {
        if (list == null) {
            return null;
        }
        List<ThemeItem> result = new ArrayList<>();
        for (ThemeItem item : list) {
            if (item.isMemoryTheme()
                    && (!item.isNeedDownloadIcon() || item.isDefaultIcon())) {
                result.add(item);
            }
        }
        return result;
    }

    private void updateMusicLists() {
        OnLoadingListenerImpl musicLoadingListener = new OnLoadingListenerImpl<MusicItem>() {
            @Override
            public void onLoadingFinish(int code, @Nullable List<MusicItem> allEntityList) {
                if ((code == RtnCode.Music.SUCCESS) || (code == RtnCode.Music.AT_INTERVALS)) {
                    updateThemeLists();
                }
            }
        };
        MusicSourceManager.getInstance().requestNetworkResource(musicLoadingListener, false, false);
    }

    public void refresh(List<ThemeItem> entryList) {
        List<ThemeItem> result = getMemoriesThemeList(entryList);
        if (result == null) {
            return;
        }
        mData.clear();
        mData.addAll(result);
        mAdapter.select(mCurrentSelectedPosition);
        updatePositionInfo();
        mAdapter.notifyDataSetChanged();
        mEditorState.setHasUpdateList(true);
    }

    private void updateThemeLists() {
        OnLoadingListenerImpl themeOnLoadingListener = new OnLoadingListenerImpl<ThemeItem>() {
            @Override
            public void onLoadingFinish(int code, @Nullable List<ThemeItem> allEntityList) {
                refresh(allEntityList);
                GLog.d(TAG, "updateNetworkSourceLists, loading finish, code = " + code);
            }
        };
        ThemeSourceManager.getInstance().requestNetworkResource(themeOnLoadingListener);
    }

    @Override
    public void onDownloadBroadcastReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (TextUtils.equals(action, ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE)
                || TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
            int state = intent.getIntExtra(LocalSourceManager.DOWNLOAD_STATE, LocalSourceManager.DOWNLOAD_STATE_INVALID);
            int resourceId = intent.getIntExtra(LocalSourceManager.DOWNLOAD_RESOURCE_ID, LocalSourceManager.ID_INVALID);
            if (state == LocalSourceManager.DOWNLOAD_STATE_INVALID) {
                return;
            }
            boolean isAutoDownload = true;
            Integer position = LocalSourceManager.ID_INVALID;
            if (TextUtils.equals(action, ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE)) {
                position = mThemePositionInfoMap.get(resourceId);
                isAutoDownload = ThemeSourceManager.getInstance().isAutoDownload(resourceId);
            } else if (TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
                position = mMusicPositionInfoMap.get(resourceId);
                isAutoDownload = MusicSourceManager.getInstance().isAutoDownload(resourceId);
            }
            GLog.d(TAG, "position = " + position + ", resourceId = " + resourceId
                    + ", state = " + state + ", isAutoDownload = " + isAutoDownload);
            if (isAutoDownload) {
                return;
            }
            switch (state) {
                case LocalSourceManager.DOWNLOAD_STATE_DOWNLOADING:
                    if (position != null) {
                        notifyDataSetChanged();
                    }
                    break;
                case LocalSourceManager.DOWNLOAD_STATE_FINISH:
                    if (TextUtils.equals(action, ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE)) {
                        if ((position != null) && (position < mData.size())) {
                            mData.set(position, ThemeSourceManager.getInstance().getThemeEntity(resourceId));
                            if (position == mCurrentSelectedPosition) {
                                mAdapter.select(position);
                                mOnIconClickListener.onIconClick(null, position, mData.get(position));
                            }
                            notifyDataSetChanged();
                        }
                    } else if (TextUtils.equals(action, MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE)) {
                        boolean needDownloadTheme = intent.getBooleanExtra(LocalSourceManager.DOWNLOAD_THEME, false);
                        if (needDownloadTheme) {
                            ThemeSourceManager.getInstance().downloadThemeByMusicId(resourceId);
                        }
                    }
                    break;
                case LocalSourceManager.DOWNLOAD_STATE_ERROR:
                    mEditorState.checkDownloadFailType();
                    selectedLastApplyItem();
                    resetDownloadProgress(resourceId);
                    notifyDataSetChanged();
                    break;
                default:
                    break;
            }
        }
    }

    private void resetDownloadProgress(int resourceId) {
        ThemeSourceManager.getInstance().resetDownloadProgress(resourceId);
        int musicId = ThemeSourceManager.getInstance().getMusicIdByThemeId(resourceId);
        MusicSourceManager.getInstance().resetDownloadProgress(musicId);
    }

    @Override
    public IntentFilter getDownloadIntentFilter() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ThemeSourceManager.ACTION_THEME_DOWNLOAD_STATE);
        intentFilter.addAction(MusicSourceManager.ACTION_MUSIC_DOWNLOAD_STATE);
        return intentFilter;
    }

    public void setCurrentSelectedPosition(int mCurrentSelectedPosition) {
        this.mCurrentSelectedPosition = mCurrentSelectedPosition;
    }

    public void setLastApplyItem(ThemeItem item) {
        if (item == null) {
            return;
        }
        int position = mData.indexOf(item);
        if ((position >= 0) && (position < mData.size())) {
            mLastApplyItemPosition = position;
        }
    }

    private ThemeItem getLastApplyItem() {
        ThemeItem lastApplyItem = null;
        if ((mData != null) && (mData.size() > mLastApplyItemPosition)) {
            lastApplyItem = mData.get(mLastApplyItemPosition);
            if ((lastApplyItem == null) && (mData.size() > 0)) {
                lastApplyItem = mData.get(0);
                mLastApplyItemPosition = 0;
            }
        }
        return lastApplyItem;
    }

    public void selectedLastApplyItem() {
        ThemeItem lastApplyItem = getLastApplyItem();
        if (lastApplyItem != null) {
            mAdapter.select(mLastApplyItemPosition);
        }
    }
}
