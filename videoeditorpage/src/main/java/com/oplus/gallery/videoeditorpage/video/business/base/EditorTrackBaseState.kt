/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorTrackBaseState
 ** Description:需要在特效轨道加片段的业务基类，根据配置控制特效轨道的行为
 ** 1、选择特效后，自适应对齐到当前片段（若当前片段空间不够，只加到有限空间）
 ** 2、选中会出把手，能调长短
 ** 3、关掉多轨，当前已经有特效的不给添加滤镜
 ** 4、轨道上有图标跟文字
 ** Version: 1.0
 ** Date : 2025/4/2
 ** Author: 80320709
 ** TAG: EditorTrackBaseState
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80320709                       2025/4/2   1.0          first created
 **  kangshuwen                     2025/4/9   1.1          update
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.base

import android.content.Context
import android.util.SparseArray
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.math.MathUtils.THREE
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoTimelineEffect
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.video.business.base.data.EffectRange
import com.oplus.gallery.videoeditorpage.video.business.base.interfaces.IFxChangeListener
import com.oplus.gallery.videoeditorpage.video.business.music.ui.MusicTimelineEditor
import com.oplus.gallery.videoeditorpage.video.business.track.config.AddClipStrategy
import com.oplus.gallery.videoeditorpage.video.business.track.config.AlignmentStrategy
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorEffectTrackConfig
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IAudioClip
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IAudioTrack
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel
import com.oplus.gallery.videoeditorpage.video.business.track.util.EffectTrackRangeHelper
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import java.lang.ref.WeakReference

/**
 * 编辑的轨道能力基类
 * 需要拥有轨道能力的UI，都继承这个轨道能力的基类
 */
abstract class EditorTrackBaseState<T : EditorTrackBaseUIController<Any>> : EditorBaseState<T> {
    /**
     * 当前clip的指定特效（由各个业务来指定，比如当前是调节页面，那么这个对象就是当前clip挂的调节特效）
     */
    open var currentVideoFx: IBaseClip? = null

    @JvmField
    protected val modelHashMap = HashMap<IClip, ClipModel>()

    private val baseVideoMap = hashMapOf<IBaseClip, ClipModel>()

    /**
     * 拷贝的非当前视频的特效列表
     */
    private val copyVideoFxList = mutableListOf<IBaseClip>()

    /**
     * 轨道能力配置
     * 子类如有定制化需求，请重写该属性
     */
    open val trackConfig: EditorEffectTrackConfig = EditorEffectTrackConfig.getConfig(EditorTrackScene.TRIM)

    /**
     * 将时间线视图模型提到轨道基类
     */
    protected val weakTimelineViewModel: WeakReference<TimelineViewModel>?

    /**
     * 特效变化时的监听回调
     */
    protected var iFxChangeListener: IFxChangeListener? = null

    /**
     * 是否应用至全部
     */
    protected var isApplyAll = false

    /**
     * 构建方法
     * @param editorType 编辑类型
     * @param context 上下问
     * @param editorControlView 编辑控制视图
     */
    constructor(
        editorType: String,
        context: Context,
        editorControlView: EditorControlView,
    ) : this(editorType, context, editorControlView, null)

    /**
     * 构建方法
     * @param editorType 编辑类型
     * @param context 上下问
     * @param editorControlView 编辑控制视图
     * @param weakTimelineViewModel 时间线视图模型
     */
    constructor(
        editorType: String,
        context: Context,
        editorControlView: EditorControlView,
        weakTimelineViewModel: WeakReference<TimelineViewModel>?
    ) : super(editorType, context, editorControlView) {
        this.weakTimelineViewModel = weakTimelineViewModel
    }

    /**
     * 美摄时间线
     */
    protected val timeline: ITimeline?
        get() {
            val engine = editorEngine.getOrLog(TAG, "editor engine is null") ?: return null
            return engine.currentTimeline.getOrLog(TAG, "timeline is null")
        }

    /**
     * 视频轨道
     */
    protected val videoTrack: IVideoTrack?
        get() {
            val engine = editorEngine.getOrLog(TAG, "editor engine is null") ?: return null
            val timeline = engine.currentTimeline.getOrLog(TAG, "timeline is null") ?: return null
            return timeline.getVideoTrack(0).getOrLog(TAG, "video track is null")
        }

    /**
     * 视频轨道上的所有片段特效
     */
    protected val videoTrackEffects: MutableList<BaseVideoClipEffect> = mutableListOf()
        get() {
            field.clear()
            videoTrack?.clipList?.forEach {
                field.addAll(it.effectList)
                field.addAll(it.rawEffectList)
            }
            return field
        }

    /**
     * 当前主轨道选中的片段
     */
    val currentVideoClip: IVideoClip?
        get() {
            val engine = editorEngine.getOrLog(TAG, "editor engine is null") ?: return null
            return videoTrack?.getClipByTimelinePostion(engine.timelineCurrentPosition).getOrLog(TAG, "video clip is null")
        }

    /**
     * 轨道片段数量
     */
    protected val clipCount by lazy { videoTrack?.clipCount ?: 0 }

    override fun pause(isActivityPause: Boolean) {
        super.pause(isActivityPause)
        GLog.d(TAG, LogFlag.DL) { "pause()" }
        /**
         * 应用切换到后台中间阶段，视频依然会播放一段距离，这里需要更新时间线位置
         */
        updateTimelinePosition()
    }

    /**
     * 更新时间线位置并滚动到当前播放位置
     */
    fun updateTimelinePosition() {
        val timelineViewModelCopy = uiController.timelineViewModel
        // 其他情况使用post延迟执行
        uiController.mTrackView.post {
            timelineViewModelCopy?.apply {
                val currentPosition = mEditorEngine.timelineCurrentPosition
                layoutScrollToXPositionByPlay(currentPosition)
            }
        }
    }

    /**
     * 设置特效变化监听器
     */
    fun setFxChangeListener(fxChangeListener: IFxChangeListener) {
        this.iFxChangeListener = fxChangeListener
    }

    /**
     * 根据时间查找视频片段
     */
    protected fun findVideoClipByTime(inTime: Long, outTime: Long): IVideoClip? {
        return videoTrack?.clipList?.firstOrNull { it.inPoint == inTime && it.outPoint == outTime }
    }

    /**
     * 更新clipModel
     */
    open fun updateClipModels(
        timeline: ITimeline,
        clipModelArray: SparseArray<ArrayList<ClipModel>>
    ) {
        val videoList = videoFxs
        var maxTrackIndex = 0
        videoList.filter { (it.getInPoint() + MusicTimelineEditor.MIN_TIME_OF_ITEM) <= timeline.duration }
            .forEachIndexed { _, baseVideoFx ->
                val trackIndex = baseVideoFx.getTrackIndex() + 1
                var clipModels = clipModelArray.get(trackIndex)
                if (clipModels == null) {
                    clipModels = arrayListOf()
                }
                if (trackIndex > maxTrackIndex) {
                    maxTrackIndex = trackIndex
                }
                val videoClipModel = initClipModelFromBaseVideoFx(baseVideoFx) ?: return
                var clipInTrackIndex = 0
                var findIndex = false
                clipModels.forEachIndexed { oldIndex, clipModel ->
                    if ((videoClipModel.inPoint < clipModel.inPoint) && findIndex.not()) {
                        clipInTrackIndex = oldIndex
                        clipModel.clipInTrackIndex = clipInTrackIndex
                        findIndex = true
                    }
                    if (findIndex) {
                        clipModels[oldIndex].clipInTrackIndex = (oldIndex + 1)
                    }
                }
                if (findIndex.not() && (clipInTrackIndex == 0)) {
                    videoClipModel.clipInTrackIndex = clipModels.size
                    clipModels.add(videoClipModel)
                } else {
                    clipModels.add(clipInTrackIndex, videoClipModel)
                }
                clipModelArray[trackIndex] = clipModels
            }
    }

    /**
     * 判断当前特效轨道是否还可以加特效
     * @param effectLists 时间线上的特效列表
     * @return true 可以加特效，false 不可以加特效
     */
    protected fun isMultiEffectTrack(effectLists: List<BaseVideoTimelineEffect>): Boolean? {
        return if (trackConfig.isMultiEffectTrack) {
            true
        } else {
            val currentEffectsInTimeline = getEffectRangeInTargetClip(
                currentVideoClip?.inPoint, currentVideoClip?.outPoint, effectLists
            )
            currentEffectsInTimeline?.isValid
        }
    }

    /**
     * marked by youpeng:将特效轨和视频轨的逻辑解耦
     * 根据对齐方式返回要添加的特效的开始时间和结束时间
     * @param timeline 时间线
     * @param effectLists 时间线上的特效列表
     * @return 特效的开始时间和结束时间
     * @param <E> BaseVideoTimelineEffect的子类
     */
    protected fun getEffectStartEndTime(
        timeline: ITimeline,
        effectLists: List<BaseVideoTimelineEffect>
    ): EffectRange? {
        return when (trackConfig.alignmentStrategy) {
            AlignmentStrategy.ALIGN_CLIP -> {
                getEffectRangeInTargetClip(
                    currentVideoClip?.inPoint, currentVideoClip?.outPoint, effectLists
                )
            }

            AlignmentStrategy.ALIGN_FIXED_LENGTH -> {
                getEffectRangeWithFixedLength(
                    editorEngine.timelineCurrentPosition, EFFECT_DEFAULT_DURATION, effectLists
                )
            }
        }
    }

    /**
     * 获取固定长度的特效片段区间
     * @param startTime 特效开始时间
     * @param effectFixDuration 特效固定时长
     * @param effectLists 现有的特效列表
     * @return 新增的特效可用的区间，找不到返回空
     */
    private fun getEffectRangeWithFixedLength(
        startTime: Long,
        effectFixDuration: Long,
        effectLists: List<IBaseClip>
    ): EffectRange? {
        if (effectLists.isEmpty()) {
            // 没有特效，直接返回固定时长的区间
            return EffectRange(startTime, startTime + effectFixDuration)
        }
        // 找到特效轨道上所有的有效空白区间
        val effectRanges = EffectTrackRangeHelper.getAllEmptyRange(mEditorEngine.timelineDuration, effectLists)
        // 找到符合条件的，离中线最近的区间
        return getNearestEffectRange(effectRanges)?.let {
            if ((it.outPoint - it.inPoint) > effectFixDuration) {
                EffectRange(it.inPoint, it.inPoint + effectFixDuration)
            } else {
                it
            }
        }
    }

    /**
     * copy effect
     * 返回要复制的特效的开始时间和结束时间
     * @param originalEffect 复制源特效
     * @param effectLists 时间线上的特效列表
     * @return 特效的开始时间和结束时间
     */
    protected fun getCopyEffectStartEndTime(
        originalEffect: IBaseClip?,
        effectLists: List<IBaseClip>
    ): EffectRange? {
        return when (trackConfig.alignmentStrategy) {
            AlignmentStrategy.ALIGN_CLIP -> getCopyRangeByClip(effectLists)
            AlignmentStrategy.ALIGN_FIXED_LENGTH -> {
                originalEffect.getOrLog("[getCopyEffectStartEndTime] originalEffect is null")?.let {
                    getEffectRangeWithFixedLength(it.getInPoint(), it.getOutPoint() - it.getInPoint(), effectLists)
                }
            }
        }
    }

    private fun getCopyRangeByClip(effectLists: List<IBaseClip>): EffectRange? {
        // 按片段复制，优先从当前片段找空白区间
        val effectRange = getEffectRangeInTargetClip(currentVideoClip?.inPoint, currentVideoClip?.outPoint, effectLists)
        effectRange?.let {
            if (it.isValid) {
                return effectRange
            }
        }
        // 找不到再从所有片段中找
        val clipList = currentTimelineClips ?: return null
        val results = mutableListOf<EffectRange>()
        clipList.forEach { iVideoClip ->
            val effectRangeInClip = getEffectRangeInTargetClip(iVideoClip.inPoint, iVideoClip.outPoint, effectLists)
            effectRangeInClip?.let {
                if (it.isValid) {
                    results.add(effectRangeInClip)
                }
            }
        }
        // 找到离当前时间戳最近的区间
        return getNearestEffectRange(results)
    }

    /**
     * 移除所有clip的指定特效
     * @param effectType 特效类型，@see {com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel.ClipType}
     * @param keepVideoFx 需要保留clip的特效
     */
    protected open fun removeAllVideoFx(effectType: Int, keepVideoFx: IBaseClip? = null) {
        GLog.d(TAG, LogFlag.DL, "removeAllVideoFx")
        if (effectType == ClipModel.ClipType.CLIP_TEXT) {
            timeline.getOrLog(TAG, "[removeAllVideoFx] timeline is null")?.removeVideoFx(keepVideoFx as BaseVideoFx)
        } else {
            videoTrack?.clipList
                ?.filterNot { (it.inPoint == keepVideoFx?.getInPoint()) && (it.outPoint == keepVideoFx.getOutPoint()) }
                ?.onEach { it.removeEffectByType(effectType) }
        }
    }

    /**
     * 应用至全部，子类需要设置 effectType，覆写 addVideoFxToTimeline
     * 注意 isApplyAll = false 的时机要业务自己确定，一般是切换了不同的特效或者修改了特效参数后就要设置为false
     * @param isDelete 是否删除特效, 如果是删除特效的话，就不需要重新再应用特效了
     */
    fun applyToAll(isDelete: Boolean? = false) {
        //只有第一次点击应用至全部的时候才需要拷贝其他clip上的已有特效，拷贝出来的特效用于在点击取消的时候重新对其他clip恢复特效
        if (isApplyAll.not()) {
            copyOtherVideoFxList()?.let { copyVideoFxList.addAll(it) }
        }
        isApplyAll = true
        // 应用至全部，先删除全部特效
        removeAllVideoFx(getClipModelType(), currentVideoFx)
        // 再将当前特效应用至全部片段
        if (isDelete != true) {
            applyVideoFxToAllClips(currentVideoFx)
        }
    }

    /**
     * 点击了应用全部，再点击取消的时候，需要将其他视频的特效恢复到之前的状态；
     * 先将已经被应用了"应用全部"的效果删除，然后再重新下发之前备份的效果
     */
    fun reapplyEffect() {
        // 应用至全部，先删除全部特效
        removeAllVideoFx(getClipModelType(), currentVideoFx)
        // 重新将各个视频的初始效果应用回去
        copyVideoFxList.forEach {
            addVideoFxToTimeline(it, EffectRange(it.getInPoint(), it.getOutPoint()))
        }
    }

    /**
     * 拷贝其他视频片段的特效
     * @return 拷贝的特效列表
     */
    protected open fun copyOtherVideoFxList(): List<IBaseClip>? {
        return null
    }

    /**
     * 添加特效到时间线
     * @param videoFx 特效
     * @param range 特效的开始时间和结束时间
     */
    open fun addVideoFxToTimeline(videoFx: IBaseClip, range: EffectRange): IBaseClip? {
        return null
    }

    public override fun getVideoFxs(): List<IBaseClip> {
        return arrayListOf()
    }

    open fun getClipModelType(): Int {
        return 0
    }

    /**
     * 应用特效到全部片段
     * @param videoFx 特效
     */
    private fun applyVideoFxToAllClips(videoFx: IBaseClip?) {
        if (videoFx == null) {
            GLog.e(TAG, LogFlag.DL, "[applyVideoFxToAllClips] videoFx is null!")
            return
        }

        getAlignEffectRanges()
            .filter { it.isValid }
            .filterNot { (it.inPoint == videoFx.getInPoint()) && (it.outPoint == videoFx.getOutPoint()) }
            .forEach {
                addVideoFxToTimeline(videoFx, it)
            }
    }

    /**
     * 找出目标区间没有覆盖特效且离当前时间戳最近的空白区间
     * @param inTime 目标区间开始时间
     * @param outTime 目标区间结束时间
     * @param effectLists 时间线上的特效列表
     * @return 特效的开始和结束时间
     */
    fun getEffectRangeInTargetClip(
        inTime: Long?,
        outTime: Long?,
        effectLists: List<IBaseClip>
    ): EffectRange? {
        if (inTime == null || outTime == null) {
            GLog.e(TAG, LogFlag.DL, "[getEffectRangeInTargetClip]: inTime or outTime is null")
            return null
        }
        if (effectLists.isEmpty()) {
            // 没有特效，直接返回当前视频的区间
            return EffectRange(inTime, outTime)
        }

        // 找到特效轨道上所有的有效空白区间
        val results = EffectTrackRangeHelper.getValidEmptyRange(mEditorEngine.timelineDuration, effectLists)
        // 找到离当前时间戳最近的区间
        val effectRange = getNearestEffectRange(results)

        if (trackConfig.alignmentStrategy == AlignmentStrategy.ALIGN_CLIP) {
            // 如果需要跟视频片段对齐，处理特效跨片段时的边界情况
            effectRange?.coerceIn(inTime, outTime)
        }
        return effectRange
    }

    /**
     * 找出列表中距离当前时间戳最近的区间
     * @param rangeLists 特效区间列表，已经按inPoint排序
     */
    private fun getNearestEffectRange(rangeLists: List<EffectRange>): EffectRange? {
        val validRanges = rangeLists.filter { it.isValid }
        if (validRanges.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "[getNearestEffectRange]: no valid effect range")
            return null
        }
        var resultIndex = -1
        val currentPosition = mEditorEngine.timelineCurrentPosition
        for ((index, item) in validRanges.withIndex()) {
            // 如果当前时间戳在区间内，则直接返回 || 找到开始时间大于中线的第一个区间，直接返回
            if ((currentPosition in item.inPoint until item.outPoint) || (item.inPoint >= currentPosition)) {
                resultIndex = index
                break
            }
        }
        resultIndex = findRangeByStrategy(resultIndex, validRanges, currentPosition)
        if (resultIndex >= 0) {
            return if (trackConfig.isStartWithMiddleLine) {
                // 以中线开始加特效，注意如果中线位置大于空白区间的inPoint才使用中线的位置作为起始点，否则可能造成重叠
                val inPoint = if (validRanges[resultIndex].inPoint >= currentPosition) validRanges[resultIndex].inPoint else currentPosition
                EffectRange(inPoint, validRanges[resultIndex].outPoint)
            } else {
                // 以片段开始加特效
                validRanges[resultIndex]
            }
        }
        return null
    }

    /**
     * 根据策略查找可用区间的下标
     * @param resultIndex 查找过一轮的下标，可能找到了，可能还是 -1
     * @param validRanges 可用区间列表
     * @param currentPosition 当前时间戳
     * @return 可用区间下标
     */
    private fun findRangeByStrategy(resultIndex: Int, validRanges: List<EffectRange>, currentPosition: Long): Int {
        return when (trackConfig.addClipStrategy) {
            AddClipStrategy.ADD_CLIP_AT_END_THEN_START -> {
                // 在中线后面没有找到空白区间，且支持往前找
                if (resultIndex == -1) {
                    validRanges.lastOrNull { it.outPoint <= currentPosition }?.let { validRanges.indexOf(it) } ?: resultIndex
                } else {
                    resultIndex
                }
            }

            AddClipStrategy.ADD_CLIP_AT_END_ONLY -> resultIndex
        }
    }

    /**
     * 获取所有视频片段的空白区间
     * 与视频片段对齐
     * 用于“应用至全部”
     * return: 特效区间列表
     */
    private fun getAlignEffectRanges(): MutableList<EffectRange> {
        // 根据视频片段的边界，返回对应区间
        val alignRanges: MutableList<EffectRange> = mutableListOf()
        currentTimelineClips?.forEach { clip ->
            alignRanges.add(EffectRange(clip.inPoint, clip.outPoint))
        }
        return alignRanges
    }

    /**
     * 获取当前片段的transition类型
     * @param videoTrack 视频轨道
     * @param clipIndex 当前片段的索引
     */
    private fun getTransitionType(videoTrack: IVideoTrack, clipIndex: Int): Int? {
        var transitionType: Int? = null
        if (videoTrack.getTransition(clipIndex - 1) != null) {
            transitionType = ClipModel.TransitionType.TRANSITION_LEFT
        }
        if (videoTrack.getTransition(clipIndex) != null) {
            transitionType = if (transitionType == null) {
                ClipModel.TransitionType.TRANSITION_RIGHT
            } else {
                transitionType or ClipModel.TransitionType.TRANSITION_RIGHT
            }
        }
        return transitionType
    }

    /**
     * 设置clipModel的类型
     * @param clip 素材片段
     * @param clipModel 素材片段对应的ClipModel
     */
    private fun setClipModelType(
        clip: IClip,
        clipModel: ClipModel
    ) {
        if (clip.type == StreamingConstant.ClipType.CLIP_TYPE_VIDEO) {
            if ((clip as IVideoClip).videoType == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE) {
                clipModel.apply {
                    type = ClipModel.ClipType.CLIP_PICTURE
                    if (!clip.isOlivePhoto) {
                        realDuration = (clip.getDuration() * clip.getSpeed()).toLong()
                        trimIn = ClipModel.TRIM_INFINITE
                        trimOut = (ClipModel.TRIM_INFINITE + clip.getDuration() * clip.getSpeed()).toLong()
                    }
                }
            } else {
                clipModel.type = ClipModel.ClipType.CLIP_VIDEO
            }
        }
    }

    /**
     * 根据特效信息初始化clipModel
     */
    private fun initClipModelFromBaseVideoFx(baseVideoFx: IBaseClip): ClipModel? {
        var clipModel = baseVideoMap[baseVideoFx]
        clipModel ?: run {
            clipModel = if (baseVideoFx is IClip) {
                ClipModel(baseVideoFx)
            } else {
                ClipModel()
            }
            clipModel?.let { baseVideoMap[baseVideoFx] = it }
        }
        val localModel = clipModel?.getOrLog() ?: return null
        localModel.inPoint = baseVideoFx.getInPoint()
        localModel.trimIn = ClipModel.TRIM_INFINITE
        val end = if (baseVideoFx.getOutPoint() > editorEngine.currentTimeline.duration) {
            editorEngine.currentTimeline.duration
        } else {
            baseVideoFx.getOutPoint()
        }
        localModel.trimOut = (localModel.trimIn + (end - baseVideoFx.getInPoint()))
        localModel.realDuration = (localModel.trimOut - localModel.trimIn)
        localModel.trackIndex = (baseVideoFx.getTrackIndex() + 1)
        localModel.type = getClipModelType()
        setClipModel(localModel, baseVideoFx)
        return localModel
    }

    /**
     * 设置clipModel的属性，各个特效设置的值不一样，让子类去设置
     * @param clipModel clipModel
     * @param baseVideoFx 特效
     */
    protected open fun setClipModel(clipModel: ClipModel, baseVideoFx: IBaseClip) = Unit

    override fun destroy() {
        baseVideoMap.clear()
        modelHashMap.clear()
        copyVideoFxList.clear()
        super.destroy()
    }

    /**
     * 从timeline中获取主轨道的片段列表
     * 这是从滤镜、调节、音乐、文字抽出来的公共方法，它们的 getClipArrayFromTimeline 方法中都有这段逻辑
     * @return key为0，value为主轨道的clipModel列表
     */
    protected fun getMainTrackClipModels(timeline: ITimeline): SparseArray<ArrayList<ClipModel>> {
        val arrayListSparseArray = SparseArray<ArrayList<ClipModel>>()
        // add video clip in track 0
        val videoTrack = timeline.getVideoTrack(0).getOrLog(TAG, "mainTrack is null!") ?: return arrayListSparseArray
        val clipList = videoTrack.clipList
        val clipModels = ArrayList<ClipModel>()
        clipList.forEachIndexed { index, clip ->
            var clipModel = modelHashMap[clip]
            if (clipModel == null) {
                clipModel = ClipModel(clip)
                modelHashMap[clip] = clipModel
            }
            clipModel.apply {
                setClip(clip)
                clipExtra = getTransitionType(videoTrack, index)
                setClipModelType(clip, clipModel)
                trackIndex = 0
                clipInTrackIndex = index
            }
            clipModels.add(clipModel)
        }
        arrayListSparseArray.put(0, clipModels)
        return arrayListSparseArray
    }

    /**
     * 更新视频片段特效
     * @param clipIndex 片段索引
     * @param videoClip 视频片段
     */
    protected fun updateVideoClipModelEffect(
        clipIndex: Int,
        videoClip: IVideoClip
    ): ClipModel? {
        return videoTrack?.let {
            val clipModel = weakTimelineViewModel?.get()?.getClipModel(AppConstants.Number.NUMBER_0, clipIndex)
            clipModel?.apply {
                setClip(videoClip)
                clipExtra = getTransitionType(it, clipIndex)
                setClipModelType(videoClip, clipModel)
                trackIndex = 0
                clipInTrackIndex = clipIndex
            }
        }
    }

    /**
     * 从时间线对象中提取剪辑模型数组，并将其组织为稀疏数组形式。
     *
     *
     * 该函数接收一个时间线对象，遍历其中的剪辑数据，并将其按照特定的键值对形式存储在稀疏数组中。
     * 稀疏数组的键通常表示时间线中的某个时间点或位置，而值则是对应时间点的剪辑模型列表。
     *
     * @param timeline 时间线对象，包含需要提取的剪辑数据。
     * @return 返回一个稀疏数组，其中键为时间线中的某个标识（如时间点），值为对应标识的剪辑模型列表。(视频轨道数据 和 音乐轨道数据 -》将来其他的也可以这里加)
     */
    open fun getClipArrayFromTimeline(timeline: ITimeline?): SparseArray<ArrayList<ClipModel>> {
        if (timeline == null) {
            GLog.e(TAG, LogFlag.DL, "getClipArrayFromTimeline: timeline is null")
            return SparseArray<ArrayList<ClipModel>>()
        }
        // 处理视频轨道
        val clips = getMainTrackClipModels(timeline)
        // 处理音频轨道
        val audioTrackCount = timeline.audioTrackCount
        if (audioTrackCount > 0) {
            for (i in 0 until audioTrackCount) {
                val audioTrack = timeline.getAudioTrack(i)
                if (audioTrack != null) {
                    val audioClipModels = processAudioTrack(audioTrack, i + 1)
                    clips.put(i + 1, audioClipModels)
                }
            }
        }
        // 将文字轨道添加到列表中供在剪辑页显示
        timeline.captionList.mapNotNull {
            initClipModelFromBaseVideoFx(it)?.apply {
                // 轨道index +1 显示在音乐的下方
                trackIndex += 1
                caption = it
                type = ClipModel.ClipType.CLIP_TEXT
                // 设置字幕轨道显示内容
                clipDescription = caption.text
            }
        }.also { clips.put((audioTrackCount + 1), it as? ArrayList<ClipModel>) }
        return clips
    }


    /**
     * 处理音频轨道，将其中的音频片段转换为ClipModel对象列表。
     *
     * @param audioTrack 音频轨道对象，包含多个音频片段
     * @param trackIndex 音频轨道的索引
     * @return 返回处理后的ClipModel对象列表
     */
    protected fun processAudioTrack(
        audioTrack: IAudioTrack,
        trackIndex: Int
    ): ArrayList<ClipModel> {
        val clipModels = ArrayList<ClipModel>()
        val audioClips = audioTrack.clipList

        // 遍历音频轨道中的所有片段，过滤掉时长过短的片段，并将其转换为ClipModel对象
        for (j in audioClips.indices) {
            val audioClip = audioClips[j]
            if (audioClip != null && (audioClip.outPoint - audioClip.inPoint) >= MusicTimelineEditor.MIN_TIME_OF_ITEM) {
                val clipModel = getOrCreateClipModel(audioClip)
                setAudioClipProperties(clipModel, audioClip, trackIndex, j)
                clipModels.add(clipModel)
            }
        }
        return clipModels
    }

    /**
     * 根据片段对象获取或创建对应的ClipModel对象。
     *
     * @param clip 片段对象，可以是视频片段或音频片段
     * @return 返回对应的ClipModel对象
     */
    private fun getOrCreateClipModel(clip: IClip): ClipModel {
        var clipModel = modelHashMap[clip]
        if (clipModel == null) {
            clipModel = ClipModel(clip)
            modelHashMap[clip] = clipModel
        }
        return clipModel
    }

    /**
     * 当前时码线所在位置是否有足够的轨道空间做特效
     *
     * @return
     */
    open fun isCurrentTimeHasEnoughEmptyRange(): Boolean {
        return true
    }

    /**
     * 设置音频片段的属性到ClipModel对象中。
     *
     * @param clipModel 目标ClipModel对象
     * @param audioClip 音频片段对象
     * @param trackIndex 音频轨道的索引
     * @param clipIndex 音频片段在轨道中的索引
     */
    private fun setAudioClipProperties(
        clipModel: ClipModel,
        audioClip: IAudioClip,
        trackIndex: Int,
        clipIndex: Int
    ) {
        clipModel.apply {
            this.clip = audioClip
            val attachment = audioClip.getAttachment(IAudioClip.AttachmentKeys.ATTACHMENT_KEY_CATEGORY_ID)
            this.type = if ((attachment == null)) ClipModel.ClipType.CLIP_MUSIC else ClipModel.ClipType.CLIP_MUSIC_CAPTION
            this.clipDescription = audioClip.displayName
            this.trackIndex = trackIndex
            this.clipInTrackIndex = clipIndex
        }
    }

    companion object {
        private val TAG = EditorTrackBaseState::class.java.simpleName
        private const val EFFECT_DEFAULT_DURATION = EditorEngine.TIME_BASE * THREE // 特效默认时长为3秒
    }
}
