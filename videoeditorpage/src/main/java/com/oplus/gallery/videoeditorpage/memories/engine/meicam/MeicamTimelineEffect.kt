/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamTimelineEffect.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.meicam.sdk.NvsTimelineVideoFx

open class MeicamTimelineEffect : BaseVideoTimelineEffect {

    @Transient
    var nvsTimelineVideoFx: NvsTimelineVideoFx? = null

    constructor() : super("", TYPE_PACKAGED_FX)

    constructor(name: String?, type: Int) : super(name, type)

    override fun changeOutPoint(outTime: Long) {
        nvsTimelineVideoFx?.changeOutPoint(outTime)
    }

    override fun changeInPoint(inTime: Long) {
        nvsTimelineVideoFx?.changeInPoint(inTime)
    }

    override fun reAlignVideoSticker(inTimeNs: Long) {
        val fx = nvsTimelineVideoFx ?: return
        nvsTimelineVideoFx?.movePosition(inTimeNs - fx.inPoint)
    }
}