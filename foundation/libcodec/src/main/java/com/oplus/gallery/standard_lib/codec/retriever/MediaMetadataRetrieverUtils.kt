/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaMetadataRetrieverUtils
 ** Description: MediaMetadataRetrieverUtils
 ** Version: 1.0
 ** Date : 2022/4/23
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2022/04/23    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.standard_lib.codec.retriever

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.text.TextUtils
import com.oplus.gallery.foundation.fileaccess.MediaStoreAccessImp
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter

object MediaMetadataRetrieverUtils {

    private const val TAG = "MediaMetadataRetrieverUtils"

    /**
     * 从MediaMetadataRetriever中获取信息
     * @param uri 文件的uri
     * @return RetrieverInfoData 宽，高，角度
     */
    @JvmStatic
    fun getRetrieverInfoData(uri: Uri, offset: Long? = null, length: Long? = null): RetrieverInfoData? {
        val time = System.currentTimeMillis()
        var retriever: MediaMetadataRetriever? = null
        try {
            retriever = MediaMetadataRetriever()
            MediaStoreAccessImp.getInstance().openFile(
                ContextGetter.context,
                OpenFileRequest.Builder()
                    .setUri(uri)
                    .builder()
            )?.use { pfd ->
                if ((pfd.fileDescriptor == null) || pfd.fileDescriptor.valid().not()) {
                    GLog.e(TAG) {
                        "<getRetrieverInfoData> fileDescriptor=${pfd.fileDescriptor} isValid=${pfd.fileDescriptor?.valid()} " +
                                "source is invalid, skip retrieve."
                    }
                    return null
                }

                if ((offset != null) && (length != null)) {
                    retriever.setDataSource(pfd.fileDescriptor, offset, length)
                } else {
                    retriever.setDataSource(pfd.fileDescriptor)
                }

                //角度
                val rotationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
                if (TextUtils.isEmpty(rotationStr)) {
                    GLog.d(TAG, "getRetrieverInfoData rotation is null ")
                    return null
                }
                val retrieverInfoData = RetrieverInfoData(0, 0, 0)
                rotationStr?.let {
                    retrieverInfoData.rotation = it.toInt()
                }
                // 宽和高
                retriever.frameAtTime?.let {
                    retrieverInfoData.width = it.width
                    retrieverInfoData.height = it.height
                }
                GLog.d(
                    TAG, "getRetrieverInfoData rotation = " + retrieverInfoData.toString()
                            + ", cost time = " + (System.currentTimeMillis() - time) + "ms"
                )
                return retrieverInfoData
            }
        } catch (e: NumberFormatException) {
            GLog.e(TAG, "getRetrieverInfoData NumberFormatException uri = $uri", e)
        } catch (e: IllegalArgumentException) {
            GLog.e(TAG, "getRetrieverInfoData IllegalArgumentException uri = $uri", e)
        } finally {
            retriever?.release()
        }
        return null
    }

    /**
     * 获取视频的方向
     * @param uri uri
     * @return 方向 0 ， 90 ，180 ， 270
     */
    @JvmStatic
    fun getVideoRotation(uri: Uri): Int {
        val time = System.currentTimeMillis()
        var retriever: MediaMetadataRetriever? = null
        try {
            retriever = MediaMetadataRetriever()
            retriever.setDataSource(ContextGetter.context, uri)
            val rotationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
            if (TextUtils.isEmpty(rotationStr)) {
                GLog.d(TAG, "getVideoRotation rotation is null ")
                return 0
            }
            val rotation = rotationStr?.toInt() ?: 0
            GLog.d(
                TAG, "getVideoRotation rotation = " + rotation
                        + ", cost time = " + (System.currentTimeMillis() - time) + "ms"
            )
            return rotation
        } catch (e: NumberFormatException) {
            GLog.e(TAG, "getVideoRotation NumberFormatException uri = $uri", e)
        } catch (e: IllegalArgumentException) {
            GLog.e(TAG, "getVideoRotation IllegalArgumentException uri = $uri", e)
        } finally {
            retriever?.release()
        }
        return 0
    }

    /**
     * 从Retriever提前数据
     * @param context 上下文
     * @param contentUri 媒体文件uri
     * @param filePath   媒体文件filePath，优先用uri
     * @param key  要提取的数据的key值
     */
    @JvmStatic
    fun extractMetadata(context: Context, contentUri: Uri?, key: Int): String? {
        if (contentUri == null) {
            GLog.e(TAG, "extractMetadata contentUri is null")
            return null
        }
        MediaMetadataRetriever().use {
            runCatching {
                MediaStoreAccessImp.getInstance().openFile(
                    context,
                    OpenFileRequest.Builder()
                        .setUri(contentUri)
                        .builder()
                )?.use { fileDescriptor ->
                    it.setDataSource(fileDescriptor.fileDescriptor)
                    return it.extractMetadata(key)
                }
            }.onFailure { e ->
                GLog.e(TAG, "getMediaMetadataRetriever", e)
            }
        }
        return null
    }
}