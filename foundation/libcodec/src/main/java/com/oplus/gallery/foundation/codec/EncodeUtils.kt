/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EncodeUtils.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/22    1.0                create
 ******************************************************************************/

package com.oplus.gallery.foundation.codec

import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import com.oplus.gallery.foundation.util.ext.inRange
import com.oplus.gallery.standard_lib.util.io.ExposedByteArrayOutputStream
import java.io.ByteArrayOutputStream

object EncodeUtils {
    const val HIGH_JPEG_QUALITY = 100
    const val MIDDLE_JPEG_QUALITY = 95
    const val LOW_JPEG_QUALITY = 90
    const val DEFAULT_JPEG_QUALITY = HIGH_JPEG_QUALITY
    private const val MAX_JPEG_QUALITY = 100
    private const val MIN_JPEG_QUALITY = 0
    private const val IO_BUFFER_LENGTH = 65536

    /**
     * [Bitmap]压缩为[ByteArray]
     * @param bitmap 待压缩的[Bitmap]
     * @param cf 压缩格式，取值见：[CompressFormat]
     * @param quality 压缩质量，取值见：[EncodeUtils.DEFAULT_JPEG_QUALITY]
     * @return 压缩后的ByteArray
     */
    @JvmStatic
    fun compressToBytes(
        bitmap: Bitmap,
        cf: CompressFormat,
        quality: Int
    ): ByteArray {
        if (isQualityValid(quality).not()) return ByteArray(0)
        val baoStream = ByteArrayOutputStream(IO_BUFFER_LENGTH)
        bitmap.compress(cf, quality, baoStream)
        return baoStream.toByteArray()
    }

    /**
     * [Bitmap]压缩为[ByteArray]
     * @param bitmap 待压缩的[Bitmap]
     * @param cf 压缩格式，取值见：[CompressFormat]
     * @param quality 压缩质量，取值见：[EncodeUtils.DEFAULT_JPEG_QUALITY]
     * @param getBuffer 压缩时获取可复用的[ByteArray]接口，由业务方实现，lib中无法引用到buffer的缓存
     * @param recycleBuffer 压缩过程中的冗余[ByteArray]回收接口，由业务方实现，lib中无法引用到buffer的缓存
     * @return 压缩后的ByteArray
     */
    @JvmStatic
    fun compressByReuseBuffer(
        bitmap: Bitmap,
        cf: CompressFormat,
        quality: Int,
        getBuffer: (length: Int) -> ByteArray,
        recycleBuffer: (ByteArray) -> Unit
    ): ByteArray {
        if (isQualityValid(quality).not()) return ByteArray(0)
        val baoStream = ExposedByteArrayOutputStream(IO_BUFFER_LENGTH, getBuffer, recycleBuffer)
        bitmap.compress(cf, quality, baoStream)
        return baoStream.toByteArray()
    }

    /**
     * 压缩质量是否合理，取值范围为：[[MIN_JPEG_QUALITY], [MAX_JPEG_QUALITY]]
     */
    @JvmStatic
    private fun isQualityValid(quality: Int): Boolean {
        return quality.inRange(MIN_JPEG_QUALITY, MAX_JPEG_QUALITY)
    }
}

/**
 * CompressQuality的扩展方法，获取CompressFormat
 */
private fun CompressQuality.getFormat(): CompressFormat {
    return if (this == CompressQuality.LOSSLESS) CompressFormat.PNG else CompressFormat.JPEG
}

/**
 * CompressQuality的扩展方法，获取[Bitmap]压缩的quality
 */
private fun CompressQuality.getQuality(): Int {
    return when (this) {
        CompressQuality.LOSSLESS -> EncodeUtils.DEFAULT_JPEG_QUALITY
        CompressQuality.HIGH_QUALITY -> EncodeUtils.DEFAULT_JPEG_QUALITY
        CompressQuality.MIDDLE_QUALITY -> EncodeUtils.MIDDLE_JPEG_QUALITY
        CompressQuality.LOW_QUALITY -> EncodeUtils.LOW_JPEG_QUALITY
    }
}

/**
 * 将Bitmap按照指定的压缩比例，编码成Byte数组
 * @receiver Bitmap
 * @param quality CompressQuality
 */
fun Bitmap.compressToBytes(quality: CompressQuality): ByteArray {
    return EncodeUtils.compressToBytes(this, quality.getFormat(), quality.getQuality())
}

/**
 * 将Bitmap按照指定的压缩比例，编码成Byte数组，压缩过程中可以复用业务提供的buf对象
 * @receiver Bitmap
 * @param quality CompressQuality
 * @param getBuffer 获取buf对象复用
 * @param recycleBuffer 回收冗余buf对象
 */
fun Bitmap.compressByReuseBuffer(
    quality: CompressQuality,
    getBuffer: (length: Int) -> ByteArray,
    recycleBuffer: (ByteArray) -> Unit
): ByteArray {
    return EncodeUtils.compressByReuseBuffer(this, quality.getFormat(), quality.getQuality(), getBuffer, recycleBuffer)
}