/********************************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File: - ShareInnerViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2025-01-08
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>    <date>    <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Fonan.<PERSON><PERSON>@Android.Apps.Gallery    2025/01/08    1.0   Create
 ********************************************************************************/
package com.oplus.gallery.sharepage.viewmodel

import android.app.Activity
import android.app.Application
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.ResolveInfo
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.addon.content.pm.ResolveInfoWrapper
import com.oplus.gallery.addon.zoomwindow.ZoomWindowManagerWrapper
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_SELECTION_DATA_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_URI_ARRAY
import com.oplus.gallery.basebiz.event.ShareExitEvent
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.bus_lib.Bus
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.helper.ShareHelper.KEY_FOCUS
import com.oplus.gallery.business_lib.helper.ShareHelper.KEY_ITEM_PATH
import com.oplus.gallery.business_lib.helper.ShareHelper.STATE_DISMISS_AND_EXIT_EDIT_MODE
import com.oplus.gallery.business_lib.helper.ShareHelper.STATE_DISMISS_AND_UPDATE_SELECTED_SHOW
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.ShareAction
import com.oplus.gallery.business_lib.menuoperation.ShareAction.Companion.KEY_MODEL_TYPE
import com.oplus.gallery.business_lib.menuoperation.ShareAction.Companion.KEY_VIEWDATA_ID
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.MediaObject
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.local.set.ClassifiedAlbum.Companion.getAllBucketId
import com.oplus.gallery.business_lib.model.data.local.set.ClassifiedCShotAlbum
import com.oplus.gallery.business_lib.model.data.person.set.FaceItem
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.business_lib.model.selection.PathSelectionManager
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.securityshare.SecurityShareHelper
import com.oplus.gallery.business_lib.securityshare.data.CompatibleFormatStatus
import com.oplus.gallery.business_lib.securityshare.track.ShareTrackHelper
import com.oplus.gallery.business_lib.sharetransform.TransformManager
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.util.ShareUtils
import com.oplus.gallery.business_lib.viewmodel.AlbumReloadTask
import com.oplus.gallery.business_lib.viewmodel.AlbumViewModel
import com.oplus.gallery.business_lib.viewmodel.base.BaseReloadInfo
import com.oplus.gallery.business_lib.viewmodel.base.BaseReloadTask
import com.oplus.gallery.business_lib.viewmodel.base.ItemInfo
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.fileaccess.FileConstants.MediaType.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.SHARE_DELETE_CLOSE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.SHARE_DELETE_OPEN
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_ALLPANORAMA
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_AVIF
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_BMP
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_CSHOT
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_DNG
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_FAST_VIDEO
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_GIF
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_HEIC
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_ICON
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_JPEG
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_LOG_VIDEO
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_PNG
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_PORTRAIT_BLUR
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_SLO_MO
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_TIFF
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.MEDIA_TYPE_WEBP
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_GIF
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_OLIVE
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_PIC
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_VIDEO
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.SharePage
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.COUNT_UNINITIALIZED
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.sharepage.GalleryShareDialog
import com.oplus.gallery.sharepage.GalleryShareDialog.Companion.isScreenSmallAndLandscape
import com.oplus.gallery.sharepage.R
import com.oplus.gallery.sharepage.SendMediaFragment.Companion.INVALID_INDEX
import com.oplus.gallery.sharepage.data.SecurityStatus
import com.oplus.gallery.sharepage.data.SelectionCountData
import com.oplus.gallery.sharepage.data.ShowShareDeleteData
import com.oplus.gallery.sharepage.model.DirectShareInfoData
import com.oplus.gallery.sharepage.model.ResolveInfoData
import com.oplus.gallery.sharepage.moreshare.data.EditItemData
import com.oplus.gallery.sharepage.sendbyapp.AIWidgetsHelper
import com.oplus.gallery.sharepage.sendbyapp.ShareAppSortHelper
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.SHARE_PAGE_PRIORITY_FOREGROUND
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.baselist.viewmodel.Range
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.shortcuts.chooser.SelectableTargetInfo
import com.oplus.shortcuts.chooser.TargetInfo
import com.oplus.wrapper.os.UserHandle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 分享页数据加载VM，给内部分享页使用
 */
class ShareInnerViewModel(application: Application) : AlbumViewModel(application), MeicamEngineLimiter.LimitAble {
    override val id: String = TaskOwnerConst.SHARE

    override var reloadInfo = BaseReloadInfo<MediaItem, ItemViewData>(
        reloadVersion = BaseModel.INVALID_DATA_VERSION,
        cacheDataArray = arrayOfNulls(DATA_CACHE_SIZE),
        activeViewDataArray = arrayOfNulls(DATA_ACTIVE_SIZE),
        visibleRange = Range(),
        activeRange = Range(),
        cacheRange = Range(),
        reloadStartPosition = COUNT_UNINITIALIZED,
        reloadCount = COUNT_UNINITIALIZED,
        minLoadCount = MIN_LOAD_COUNT,
        maxLoadCount = MAX_LOAD_COUNT,
        visibleExpendSize = DATA_VISIBLE_SIZE,
        totalCount = COUNT_UNINITIALIZED
    )

    // 执行Activity finish事件
    private val internalFinishActivityEvent = MutableLiveData<Boolean>()
    val finishActivityEvent: LiveData<Boolean>
        get() = internalFinishActivityEvent

    // 分享行为正常完成，不包含用户手动取消或者返回
    private val internalShareCompleteEvent = MutableLiveData<Boolean>()
    val shareCompleteEvent: LiveData<Boolean>
        get() = internalShareCompleteEvent

    // 画廊列表显示顺序
    private val internalIsOrderRevert = MutableLiveData<Boolean>()
    val isOrderRevert: LiveData<Boolean>
        get() = internalIsOrderRevert

    // app列表数据
    val resolveInfoVM: ResolveInfoViewModel by lazy { ResolveInfoViewModel(this) }
    val resolveInfoData: LiveData<ResolveInfoData>
        get() = resolveInfoVM.internalResolveInfoData
    val resolvePinOrder: LiveData<Boolean>
        get() = resolveInfoVM.internalResolvePinOrder

    // DirectShare列表数据
    val directShareVM: DirectShareViewModel by lazy { DirectShareViewModel(this) }
    val shortCutResultInfoData: LiveData<DirectShareInfoData>
        get() = directShareVM.internalShortCutResultInfoData

    // 选中数据数量，用于刷新标题
    private val internalSelectionData = MutableLiveData<SelectionCountData>()
    val selectionData: LiveData<SelectionCountData>
        get() = internalSelectionData

    // 选中路径，用于给OShare初始化选择数据
    private val internalSelectionDataPath = MutableLiveData<SelectionData<Path>?>()
    val selectionDataPath: LiveData<SelectionData<Path>?>
        get() = internalSelectionDataPath

    // 居中索引
    private val internalFocusIndex = MutableLiveData<Int>()
    val focusIndex: LiveData<Int>
        get() = internalFocusIndex
    private var downloadSuccessCallback: ((Set<Path>?) -> Unit)? = null

    // 外部如的ItemPath，会优先居中显示
    private var itemPath: Path? = null

    /**
     * 外部传入的焦点位置
     */
    private var focusPositionFromExternal: Int = -1
    private var isFromPhotoPage: Boolean = false

    // 分享的intent 安全分享时需要修改其中的uri
    var shareIntent: Intent? = null
    private var trackCallerEntry: TrackCallerEntry? = null
    private var currentResolveInfo: ResolveInfo? = null

    //当前正在转换的directShare对应的SelectableTargetInfo对象
    private var currentTargetInfo: TargetInfo? = null

    // 安全分享业务相册子VM
    val securityViewModel: SecurityViewModel = SecurityViewModel(this, session)

    // 隐私状态数据
    val securityStatus: LiveData<SecurityStatus>
        get() = securityViewModel.internalSecurityStatus

    // 隐私和格式化选项
    val hasSendOptions: LiveData<Boolean>
        get() = securityViewModel.internalHasSendOptions

    // 第一次抹除隐私和格式转换提示
    val showSecurityTips: LiveData<Boolean>
        get() = securityViewModel.internalSecurityTips

    // 用于隐私和格式选项弹窗中刷新位置信息、拍摄信息的选中状态
    val securityStatusCheck: LiveData<SecurityStatus>
        get() = securityViewModel.internalSecurityStatusCheck

    // 发送选项的对话框里图片兼容格式选中状态
    val imageFormatStatus: LiveData<CompatibleFormatStatus>
        get() = securityViewModel.internalImageFormatStatus

    // 发送选项的对话框里视频兼容格式选中状态
    val videoFormatStatus: LiveData<CompatibleFormatStatus>
        get() = securityViewModel.internalVideoFormatStatus

    // 一碰分享业务子VM
    val oneTouchShareViewModel: OneTouchShareViewModel = OneTouchShareViewModel(this, session)

    // 一碰分享引导弹窗显示状态
    val showOneTouchGuideDialog: LiveData<Boolean>
        get() = oneTouchShareViewModel.internalShowOneTouchGuideDialog

    // 分享后是否删除图片展示
    val showShareDelete: LiveData<ShowShareDeleteData>
        get() = internalShowShareDelete
    private val internalShowShareDelete = MutableLiveData<ShowShareDeleteData>()
    private var isFromExternal = false

    // 分享启动三方应用
    private val internalStartAppLiveData: MutableLiveData<Pair<Intent, Bundle?>> = MutableLiveData()
    val startAppLiveData: LiveData<Pair<Intent, Bundle?>> = internalStartAppLiveData

    // 是否支持分享后删除（截图）
    private var supportDeleteAfterShare: Boolean = false

    //获取到的所有分享的数据包括AI
    val allShareList: MutableList<ResolveInfo> = mutableListOf()

    //解析出来的按照顺序排列完成的所有数据
    val allShareEditList = LinkedHashMap<String, EditItemData?>()

    //已置顶与可置顶的原始数据
    val pinnedList: MutableList<EditItemData> = mutableListOf()
    val pinnedAbleList: MutableList<EditItemData> = mutableListOf()

    //修改后的已置顶与可置顶的数据
    val editPinnedList: MutableList<EditItemData> = mutableListOf()
    val editPinnedAbleList: MutableList<EditItemData> = mutableListOf()
    override fun createViewData(mediaItem: MediaItem, index: Int): ItemViewData {
        return super.createViewData(mediaItem, index).apply {
            // 分享页统一使用localMediaPath,不要使用personPath,避免SelectionMode和分享数据不统一
            (mediaItem as? FaceItem)?.refItem?.path?.let { localMediaPath ->
                id = localMediaPath.toString()
                supportedAbilities[SlotOverlayHelper.SUPPORT_SELECTED] = selectionModel.isItemSelected(localMediaPath)
            }
        }
    }

    override fun onSetDataModel() {
        // 置空，不执行父类的加载xqipCache的逻辑
    }

    /**
     * 根据传入的选择数据参数加载图片数据、app列表数据
     * @param activity 所在Activity
     */
    @MainThread
    fun initData(activity: BaseActivity) {
        activity.intent.extras?.let {
            val modelType = it.getString(KEY_MODEL_TYPE)
            if (modelType.isNullOrEmpty()) {
                GLog.w(TAG, LogFlag.DL) { "[initData] albumModel is null" }
                internalFinishActivityEvent.value = true
                return
            }

            val sessionId = it.getInt(KEY_SELECTION_DATA_ID, SelectionData.INVALID_ID)
            val selectionData = PathSelectionManager.getSelectionData(sessionId)
            if (selectionData == null) {
                GLog.w(TAG, LogFlag.DL) { "[initData] selectionData is null" }
                internalFinishActivityEvent.value = true
                return
            }
            val viewDataId = it.getString(KEY_VIEWDATA_ID)
            itemPath = it.getString(KEY_ITEM_PATH).takeIf { path -> path.isNullOrEmpty().not() }?.let(Path::fromString)
            focusPositionFromExternal = if (it.containsKey(KEY_FOCUS)) {
                it.getInt(KEY_FOCUS)
            } else {
                -1
            }
            val modelUriSet = it.getStringArray(KEY_URI_ARRAY)
            val modelBundle = Bundle().apply {
                putString(DataRepository.KEY_PATH_STR, viewDataId)
                modelUriSet?.let { putStringArray(KEY_URI_ARRAY, modelUriSet) }
                if (DataRepository.LocalAlbumModelGetter.TYPE_CSHOT_ALBUM == modelType) {
                    val allBucketId = getAllBucketId(ClassifiedCShotAlbum.CSHOT_BUCKET_PATH)
                    val bucketList = arrayListOf(allBucketId)
                    putIntegerArrayList(DataRepository.LocalAlbumModelGetter.KEY_BUCKET_ID_LIST, bucketList)
                    putString(DataRepository.LocalAlbumModelGetter.KEY_BUCKET_NAME, TextUtil.EMPTY_STRING)
                }
            }
            internalIsOrderRevert.value = it.getBoolean(ShareAction.KEY_ORDER_REVERT, false)
            trackCallerEntry = it.getParcelable(MenuAction.KEY_TRACK_CALLER_ENTRY)
            supportDeleteAfterShare = it.getBoolean(ShareAction.KEY_IS_SHOW_SHARE_DELETE, false)
            isFromPhotoPage = it.getBoolean(ShareAction.KEY_FROM_PHOTO_PAGE, false)
            isFromExternal = it.getBoolean(ShareAction.KEY_IS_FROM_EXTERNAL, false)
            val albumModel = DataRepository.getAlbumModel(modelType, modelBundle)
            if (isFromExternal) {
                // 外部分享的场景，虽然新数据已插入数据库，但是通知dirty可能有延迟，缓存的脏数据未刷新，为了避免导致选择被清除等异常，强制dirty
                albumModel.forceDirty()
            }
            selectionModel.setRealModel(albumModel)
            selectionData.selectionDataId.let { dataId ->
                enterSelectionMode(dataId)
                internalSelectionDataPath.value = selectionData
            }
            setDataModel(albumModel)
            // 更新选中项目是否需要隐私和格式转换选项
            securityViewModel.initSecurityHelper(activity)
            // 预加载app的ResolveInfo数据
            resolveInfoVM.updateResolveInfo()
            resolveInfoVM.initAIResolveInfo()
            //预加载app的directShare数据
            directShareVM.updateDirectShareInfo()
            // 加载选中数据
            loadSelectData()
            showDeleteViewIfNeed()
        }
    }

    private fun loadSelectData() {
        viewModelScope.launch(Dispatchers.CPU) {
            val selectedItems = getSelectedItems()
            val selectedCount = selectedItems.size
            val countArray = getSelectCount(selectedItems)
            internalSelectionData.postValue(SelectionCountData(selectedCount, countArray[0], countArray[1]))
            oneTouchShareViewModel.updateOneTouchShareData()
        }
    }

    /**
     * 获取选中的图片/视频的数量
     */
    private fun getSelectCount(selectedItems: Set<Path>): IntArray {
        var videoCount = 0
        var photoCount = 0
        selectedItems.forEach {
            val mediaType = DataManager.getMediaType(it)
            if (mediaType == MEDIA_TYPE_VIDEO) {
                videoCount++
            }
            if (mediaType == MEDIA_TYPE_IMAGE) {
                photoCount++
            }
        }
        return intArrayOf(videoCount, photoCount)
    }

    override fun onSelectItemChange(position: Int, selected: Boolean) {
        GLog.d(TAG, LogFlag.DL, "onSelectItemChange start shareIntent extras: ${shareIntent?.extras}")
        super.onSelectItemChange(position, selected)
        loadSelectData()
        securityViewModel.updateSendOptionInfo()
        oneTouchShareViewModel.updateOneTouchShareData()
    }

    override fun getTag() = TAG

    /**
     * 调用它会触发原图的下载（如果有瘦身图的话），并且如果有下载的话会下载弹框提示用户。
     * @param localActivity UI所在Activity
     * @param successCallback 下载和准备成功的回调
     */
    fun downloadOriginalPhoto(localActivity: Activity?, successCallback: ((Set<Path>?) -> Unit)?) {
        if (localActivity !is BaseActivity) return
        val selectedItems = getSelectedItems()
        if (selectedItems.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "[downloadOriginalPhoto] SelectedItems is no data , return!" }
            return
        }
        val hasInvalidSharedMedia = hasInvalidSharedMedia(selectedItems)
        // 不管是移动网络还是wifi网络，如果有瘦身图，弹框提示用户是下载原图后发送还是直接发送
        FileProcessTaskBuilder(
            localActivity,
            ArrayList(selectedItems),
            object : FinishListener {
                override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                    GLog.d(TAG, LogFlag.DL) { "[onFinished] success: $success errCode: $errCode errMsg: $errMsg" }
                    if (!success) return
                    // 下载完之前可能已经退出，如果当前activity正在销毁或者已经被销毁，我们不应该继续去执行后续逻辑
                    if (localActivity.isFinishing || localActivity.isDestroyed) {
                        GLog.w(TAG, LogFlag.DL) { "[onFinished] activity is invalid , return!" }
                        return
                    }
                    cleanUpCache(localActivity)
                    /*
                     图片已下载完成，或者本地图片已存在，但是仍存在无效的分享数据（如mediaId仍为0），
                     则需要重新刷新一下mediaObject，否则继续分享会导致crash
                     */
                    val dataChanged = errCode == FinishListener.DATA_CHANGED_ERROR_CODE
                    val needReloadMediaObject = (errCode == FinishListener.NOT_MODIFIED_ERROR_CODE) && hasInvalidSharedMedia
                    if (dataChanged || needReloadMediaObject) {
                        downloadSuccessCallback = successCallback
                        resolveInfoVM.updateResolveInfo(needReloadMediaObject)
                    } else {
                        runOnUiThread(viewModelScope) {
                            successCallback?.invoke(selectedItems)
                        }
                    }
                }
            },
            FileProcessScene.DOWNLOAD_ORIGIN_SCENE_SHARE
        ).addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
            .setCancelListener {
                GLog.w(TAG, LogFlag.DL) { "[downloadOriginalPhoto] canceled : lifecycle.currentState = ${localActivity.lifecycle.currentState}" }
            }
            .build()?.let {
                it.execute()
                it.autoCancelWhenOnDestroy(localActivity.lifecycle)
                GLog.d(TAG, LogFlag.DL) { "[downloadOriginalPhoto] execute : lifecycle.currentState = ${localActivity.lifecycle.currentState}" }
            }
    }

    private fun hasInvalidSharedMedia(selectedItems: Set<Path>): Boolean {
        return selectedItems.find {
            val mediaItem = it.`object` as? LocalMediaItem
            mediaItem?.mediaId == 0
        } != null
    }

    private fun cleanUpCache(localActivity: Activity) {
        TransformManager(
            localActivity,
            session,
            TransformStorage.APP_PRIV_CACHE
        ).apply {
            submitCacheCleanUpTask(TransformType.HEIF)
            submitCacheCleanUpTask(TransformType.HDR_VIDEO)
            submitCacheCleanUpTask(TransformType.OLIVE)
        }
        SecurityShareHelper.submitCacheCleanUpTask()
    }

    /**
     * 执行下载完成的后续操作
     */
    fun executeDownCallback() {
        downloadSuccessCallback?.invoke(getSelectedItems())
        downloadSuccessCallback = null
    }

    /**
     * 获取居中图片的索引
     * 目前是查询数据 遗留：是否可以优化成直接读取index?
     */
    fun setUpFocusPosition() {
        if (focusPositionFromExternal != -1) {
            internalFocusIndex.value = focusPositionFromExternal
        }
        viewModelScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            itemPath?.let {
                // Marked: lichengli  从时间轴、图集页进来，考虑给一个指定的Path。
                internalFocusIndex.postValue(model?.getIndexOfItem(it, 0))
            } ?: let {
                //2. 如果没有，从图集中查选中图片中，在当前图集中最前的图片。
                val allPaths = model?.getAllPath() ?: emptyList()
                val index = getSelectedItems() // 获取选中图片
                    .map { path -> allPaths.indexOf(path) } // 获取在图集中的位置
                    .filter { index -> index != INVALID_INDEX } // 过滤无效index
                    .sorted() // 排序
                    .let {
                        if (isPositiveOrderFromSetting()) {
                            // 如果是正序，获取最后一个
                            it.lastOrNull() ?: (totalSize - 1)
                        } else {
                            // 否则获取第一个
                            it.firstOrNull() ?: 0
                        }
                    }
                internalFocusIndex.postValue(index)
            }
        }
    }

    /**
     * 大图小屏横屏需要沉浸式，隐藏状态栏
     * @param uiConfig
     * @return boolean
     */
    fun needImmersion(uiConfig: AppUiResponder.AppUiConfig): Boolean {
        return isFromPhotoPage && isScreenSmallAndLandscape(uiConfig)
    }

    /**
     * 下载完原图后处理后续分享业务，安全隐私转换
     */
    fun onHandleShare(activity: BaseActivity, position: Int, resolveInfo: ResolveInfo?, selectedItems: Set<Path>?) {
        if (position < 0) {
            GLog.w(TAG, LogFlag.DL) { "[onHandleShare]: position= $position is out index" }
            return
        }
        GLog.w(TAG, LogFlag.DL) { "[onHandleShare]: selectedItems ${selectedItems?.size}, intentextras :${shareIntent?.extras}" }
        val packageName = resolveInfo?.activityInfo?.name
        GLog.w(TAG, LogFlag.DL) { "[onHandleShare]: selectedItems ${selectedItems?.size},resolvePackageName:$packageName" }
        selectedItems?.size?.let { size ->
            packageName?.let {
                if (showShareErrorToast(activity, size, it)) {
                    return
                }
            }
        }
        //如果点击的是共享图集图标,开子线程
        if (isClickShareAlbum(resolveInfo)) {
            viewModelScope.launch(Dispatchers.IO) {
                /**
                 * 1.菜单中分享到共享图集不仅仅依赖共享图集开关，还依赖云服务应用配置项
                 * 2.菜单中共享图集显示与shouldInterceptShare里边实际的代码判定没有关系
                 * 3.分享到相册共享图集并且不符合条件的提前拦截并提示
                 * */
                if (shouldInterceptShare(resolveInfo)) {
                    GLog.w(TAG, LogFlag.DL) { "[onHandleShare] handle intercept" }
                    return@launch
                }
                //切换到主线程处理后续分享业务
                withContext(Dispatchers.Main.immediate) {
                    onHandleShareOther(activity, position, resolveInfo, selectedItems)
                }
            }
        } else {
            GLog.d(TAG, LogFlag.DL) { "onHandleShare:no share album:" }
            /**
             * 不是点击的共享图集，不需要判断分享到共享图集逻辑
             * 处理后续分享业务
             * */
            onHandleShareOther(activity, position, resolveInfo, selectedItems)
        }
    }

    /**
     * directShare的点击事件处理
     * @param activity 当前的GalleryShareInnerActivity
     * @param position 点击的第几个directshare的iTem
     * @param targetInfo 点击的SelectedTargetInfo 的对象
     * @param selectedItems 需要分享的数据path列表
     */
    fun onHandleDirectShare(activity: BaseActivity, position: Int, targetInfo: TargetInfo?, selectedItems: Set<Path>?) {
        if (position < 0) {
            GLog.w(TAG, LogFlag.DL) { "[onHandleDirectShare]: position= $position is out index" }
            return
        }
        ShareUtils.updateShareAddScore(selectedItems)
        val packagesName = targetInfo?.chooserTargetComponentName?.packageName
        currentTargetInfo = targetInfo
        if (targetInfo is SelectableTargetInfo) {
            val targetIntent = targetInfo.targetIntent ?: return
            val choosertTargetIntentExtras = targetInfo.chooserTargetIntentExtras
            activity.lifecycleScope.launch(Dispatchers.IO) {
                //这里选择的图片数据更新到targetIntent中,
                ShareUtils.updateShareIntentContents(targetIntent, selectedItems, true)
                runOnUiThread {
                    securityViewModel.securityShareHelper?.apply {
                        // 安全分享
                        if (isEraseAnyPrivacyInfo()) {
                            this.handleShareAction = {
                                handleShareActionDirectShare(activity, targetInfo)
                            }
                            GLog.d(TAG, LogFlag.DL) {
                                "onHandleDirectShare:isEraseAnyPrivacyInfo:$targetIntent, intentExtra ${targetIntent.extras}," +
                                        " choosertTargetIntentExtras: $choosertTargetIntentExtras"
                            }
                            startSecurityShare(targetIntent, position, packagesName)
                            return@runOnUiThread
                        }
                    }
                    if (securityViewModel.isConvertAnyFormat()) {
                        GLog.d(TAG, LogFlag.DL) { "onHandleDirectShare:isConvertAnyFormat" }
                        // 转码以及分享
                        securityViewModel.checkConvertAndShare(position, packagesName, true)
                    } else {
                        GLog.d(TAG, LogFlag.DL) { "onHandleDirectShare:other::${targetInfo.targetIntent}" }
                        handleShareActionDirectShare(activity, targetInfo)
                    }
                }
            }
        } else {
            GLog.e(TAG, LogFlag.DL, "onHandleDirectShare error, input targetInfo not SelectableTargetInfo $targetInfo")
        }
    }

    /**
     * 处理后续分享业务
     * */
    private fun onHandleShareOther(activity: BaseActivity, position: Int, resolveInfo: ResolveInfo?, selectedItems: Set<Path>?) {
        // 给图片加分
        ShareUtils.updateShareAddScore(selectedItems)
        val packagesName = resolveInfo?.activityInfo?.packageName
        currentResolveInfo = resolveInfo
        securityViewModel.securityShareHelper?.apply {
            // 安全分享
            if (isEraseAnyPrivacyInfo()) {
                /*
                因为这里进行隐私信息擦除的时候，它会对传递的Intent进行修改它里面的extra参数，
                所以这里为了保证在擦除成功之后修改的intent对外部其他分享不影响，
                就在传递给安全发送的时候由直接传递过来引用变为new出来一个Intent进行包装和外部隔离
                */
                this.handleShareAction = {
                    handleShareAction(activity, resolveInfo)
                }
                GLog.d(TAG, LogFlag.DL) { "onHandleShareOther:isEraseAnyPrivacyInfo:$shareIntent" }
                startSecurityShare(shareIntent, position, packagesName)
                return
            }
        }
        if (securityViewModel.isConvertAnyFormat() && ShareUtils.isSupportShareTranscode(packagesName)) {
            GLog.d(TAG, LogFlag.DL) { "onHandleShareOther:isConvertAnyFormat" }
            // 转码以及分享
            securityViewModel.checkConvertAndShare(position, packagesName)
        } else {
            GLog.d(TAG, LogFlag.DL) { "onHandleShareOther:other::$shareIntent" }
            /**
             * 分享原图：不抹除隐私信息、不转码
             * 6906042:视频分享到微信（储存权限关闭）,微信收不到视频
             */
            updateVideoUriIfNeed(packagesName, shareIntent, selectedItems)
            handleShareAction(activity, resolveInfo)
        }

        trackAlbumShareDetailOperation(packagesName)
    }

    private fun trackAlbumShareDetailOperation(packagesName: String?) {
        TrackScope.launch {
            val selectedItems = getSelectedItems()
            if (selectedItems.isEmpty()) return@launch
            val totalCount = selectedItems.size
            val videoCount = TypeFilterUtils.getSelectedVideoCount(selectedItems)
            val imageCount = totalCount - videoCount
            val resMap = mutableMapOf<String, String>()
            val mediaMap = mutableMapOf<String, String>()

            TypeFilterUtils.apply {
                resMap[RES_TYPE_PIC] = imageCount.toString()
                resMap[RES_TYPE_VIDEO] = videoCount.toString()
                resMap[RES_TYPE_OLIVE] = getSelectedOliveImageCount(selectedItems).toString()
                val selectedGifImageCount = getSelectedGifImageCount(selectedItems).toString()
                resMap[RES_TYPE_GIF] = selectedGifImageCount

                mediaMap[MEDIA_TYPE_JPEG] = getSelectedJpegImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_PNG] = getSelectedPngImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_WEBP] = getSelectedWebpImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_GIF] = selectedGifImageCount
                mediaMap[MEDIA_TYPE_HEIC] = getSelectedHeicImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_DNG] = getSelectedDngImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_ICON] = getSelectedIconImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_BMP] = getSelectedBmpImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_TIFF] = getSelectedTiffImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_AVIF] = getSelectedAvifImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_SLO_MO] = getSelectedSloMoVideoCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_FAST_VIDEO] = getSelectedFastVideoCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_LOG_VIDEO] = getSelectedLogVideoCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_PORTRAIT_BLUR] = getSelectedPortraitImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_CSHOT] = getSelectedCShotImageCount(selectedItems).toString()
                mediaMap[MEDIA_TYPE_ALLPANORAMA] = getSelectedPanoramaImageCount(selectedItems).toString()
            }
            ShareTrackHelper.trackAlbumShareDetail(JsonUtil.toJson(resMap), JsonUtil.toJson(mediaMap), packagesName)
        }
    }

    private fun isClickShareAlbum(resolveInfo: ResolveInfo?): Boolean {
        return resolveInfo?.let {
            resolveInfo.activityInfo.name.equals(SHARE_ALBUM_NAME)
        } ?: false
    }

    private fun shouldInterceptShare(resolveInfo: ResolveInfo?): Boolean {
        return resolveInfo?.let {
            val filePaths: List<String> = getSelectedFilePaths()
            ApiDmManager.getSharedSyncDM().onInterceptShared(it, filePaths)
        } ?: false
    }

    private fun getSelectedFilePaths(): ArrayList<String> {
        val filePaths = ArrayList<String>()
        getSelectedItems().forEach { path ->
            val obj: MediaObject? = DataManager.getMediaObject(path)
            if (obj is MediaItem) {
                val filePath = obj.filePath
                if (!TextUtils.isEmpty(filePath)) {
                    filePaths.add(filePath)
                }
            }
        }
        return filePaths
    }

    /**
     * 6906042:单个视频分享到微信（微信的储存权限已关闭）,微信收不到视频，而图片会弹窗提示开启权限
     * 解决方案：更新分享视频的uri为providerUri,不要使用mediaStoreUri,
     * 因为mediaStoreUri只是授予直接组件（activity）权限，当此activity销毁时则权限会被回收,导致分享后的视频无法打开
     * 7.0（sdk24）及以上fileUri无法访问app私有目录或外部存储目录
     */
    private fun updateVideoUriIfNeed(packagesName: String?, shareIntent: Intent?, sharePaths: Set<Path>?) {
        // 当前只对分享单个视频到微信（图片无问题）做处理：微信的储存权限关掉之后,需使用fileProvider分享，使用mediaUri分享会失败
        runCatching {
            sharePaths?.takeIf {
                (it.size == 1)
                        && (PACKAGE_WECHAT == packagesName)
                        && GalleryFileProvider.isGalleryUri(shareIntent?.getParcelableExtra(Intent.EXTRA_STREAM)).not()
            }?.let {
                val mediaItem = it.first().`object` as? MediaItem
                if ((MEDIA_TYPE_VIDEO == mediaItem?.mediaType) && (mediaItem.filePath?.isNotEmpty() == true)) {
                    GLog.d(TAG, LogFlag.DL) { "updateVideoUriIfNeed: before=${shareIntent?.extras}" }
                    shareIntent?.putExtra(Intent.EXTRA_STREAM, GalleryFileProvider.getUriFromFile(context, File(mediaItem.filePath)))
                }
            }
        }.onFailure { err ->
            GLog.e(TAG, LogFlag.DL, err) { "updateVideoUriIfNeed: to provider uri fail " }
        }
    }

    /**
     * 具体分享实现，具体小窗分享还是全屏分享
     */
    @MainThread
    fun handleShareActionDirectShare(activity: BaseActivity, targetInfo: TargetInfo?) {
        if (targetInfo == null) return
        recordShareItems(getSelectedItems())
        val packageName = targetInfo.chooserTargetComponentName?.packageName ?: return
        val targetIntent = targetInfo.targetIntent
        val canShareToZoomWindow = ShareUtils.canShareToZoomWindow(activity, packageName)
        GLog.d(TAG, LogFlag.DL) {
            "[handleShareActionDirectShare]: packageName = $packageName, canShareToZoomWindow = $canShareToZoomWindow, intent = $targetIntent, " +
                    "intent.extras = ${targetIntent?.extras}"
        }
        startAppDirectShare(activity, targetInfo, canShareToZoomWindow)
        // mark:截屏退出需要发送广播 后续删除GalleryShareDialog类时在迁移此静态方法
        if (isFromExternal) {
            GalleryShareDialog.exitGalleryShare(context, false)
        }
        //分享的埋点
        operationClickTrackSendShare(packageName)
    }

    @MainThread
    fun handleShareAction(activity: BaseActivity, resolveInfo: ResolveInfo?) {
        if (resolveInfo == null) return
        recordShareItems(getSelectedItems())
        val activityInfo: ActivityInfo = resolveInfo.activityInfo ?: return
        val canShareToZoomWindow = ShareUtils.canShareToZoomWindow(activity, activityInfo.packageName)
        GLog.d(TAG, LogFlag.DL) {
            "[handleShareAction]: activityInfo = $activityInfo, canShareToZoomWindow = $canShareToZoomWindow, intent = $shareIntent, " +
                    "intent.extras = ${shareIntent?.extras}"
        }
        if (canShareToZoomWindow.not()) {
            if (AIWidgetsHelper.isAiWidgetsClicked(resolveInfo)) {
                AIWidgetsHelper.onItemClick(resolveInfo)
            } else {
                startApp(resolveInfo)
            }
        } else {
            if (AIWidgetsHelper.isAiWidgetsClicked(resolveInfo)) {
                val bundle = Bundle().apply {
                    putInt(ShareUtils.KEY_WINDOWING_MODE, ZoomWindowManagerWrapper.windowingModeZoom())
                }
                AIWidgetsHelper.onItemClick(resolveInfo, bundle)
                internalShareCompleteEvent.postValue(true)
            } else {
                shareIntoZoomWindow(activityInfo.packageName, activityInfo.name)
            }
        }
        // mark:截屏退出需要发送广播 后续删除GalleryShareDialog类时在迁移此静态方法
        if (isFromExternal) {
            GalleryShareDialog.exitGalleryShare(context, false)
        }
        operationClickTrackSendShare(activityInfo.packageName)
    }


    /**
     * 记录分享的项，用于后续删除等操作
     */
    fun recordShareItems(shareItems: Set<Path>) {
        if (supportDeleteAfterShare && ConfigAbilityWrapper.getBoolean(SharePage.ALLOW_SHARE_DELETE, false)) {
            ShareUtils.setShareDeleteItems(shareItems)
        }
    }

    /**
     * 全屏还是小窗分享的具体实现
     */
    private fun startAppDirectShare(activity: BaseActivity, targetInfo: TargetInfo?, inZoomWindow: Boolean) {
        if (targetInfo is SelectableTargetInfo) {
            val intent = targetInfo.targetIntent
            if (intent == null) {
                GLog.w(TAG, "startAppDirectShare targetInfo targetIntent is null, return false")
                return
            }
            intent.component = targetInfo.chooserTargetComponentName
            intent.putExtras(targetInfo.chooserTargetIntentExtras)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            TargetInfo.prepareIntentForCrossProfileLaunch(activity, intent, UserHandle.myUserId())
            GLog.d(
                TAG, "startAppDirectShare targetInfo: $targetInfo, inZoomWindow: $inZoomWindow, " +
                        "chooserTargetIntentExtras ${targetInfo.chooserTargetIntentExtras}, intent extras ${intent.extras}"
            )
            if (inZoomWindow) {
                val bundle = Bundle().apply {
                    putInt(ShareUtils.KEY_WINDOWING_MODE, ZoomWindowManagerWrapper.windowingModeZoom())
                }
                internalStartAppLiveData.postValue(Pair(intent, bundle))
            } else {
                internalStartAppLiveData.postValue(Pair(intent, null))
            }
        }
    }


    private fun startApp(resolveInfo: ResolveInfo) {
        val intent = shareIntent?.let { shareIntent -> getReplacementIntent(resolveInfo, Intent(shareIntent)) } ?: return
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

        val activityInfo: ActivityInfo = resolveInfo.activityInfo
        intent.component = ComponentName(activityInfo.applicationInfo.packageName, activityInfo.name)
        internalStartAppLiveData.postValue(Pair(intent, null))
    }

    private fun operationClickTrackSendShare(packageName: String) {
        TrackScope.launch {
            val selectedItems = getSelectedItems()
            if (selectedItems.isEmpty()) return@launch
            val totalCount = selectedItems.size
            val videoCount = TypeFilterUtils.getSelectedVideoCount(selectedItems)
            val imageCount = totalCount - videoCount

            AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                sendTo = packageName,
                currentPage = trackCallerEntry?.trackAlbumPageInfo,
                albumName = trackCallerEntry?.currentAlbumName,
                path = trackCallerEntry?.path,
                imageCount = imageCount.toString(),
                videoCount = videoCount.toString(),
                itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_SEND_VALUE
            )
        }
    }

    /**
     * 为解决bug3178670 工作模式停用时分享图片，在Tab页下方无模式开关按钮，同步com/oplus/widget/OplusResolverPagerAdapter.java
     * 的onItemClick的Intent Replace逻辑
     */
    private fun getReplacementIntent(resolveInfo: ResolveInfo, defIntent: Intent): Intent {
        return runCatching {
            val targetUserId = ResolveInfoWrapper.getTargetUserId(resolveInfo)
            val aInfo = resolveInfo.activityInfo
            if ((targetUserId == USER_CURRENT) || (!TextUtils.equals(aInfo.name, FORWARD_INTENT_TO_PARENT)
                        && !TextUtils.equals(aInfo.name, FORWARD_INTENT_TO_MANAGED_PROFILE))
            ) {
                defIntent
            } else {
                Intent.createChooser(defIntent, null).also {
                    /*
                     * Don't auto-launch single intents if the intent is being forwarded. This is done
                     * because automatically launching a resolving application as a response to the user
                     * action of switching accounts is pretty unexpected.
                     */
                    it.putExtra(Intent.EXTRA_AUTO_LAUNCH_SINGLE_CHOICE, false)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "getReplacementIntent, call fail:$it")
        }.getOrDefault(defIntent)
    }

    private fun shareIntoZoomWindow(packageName: String, name: String) {
        shareIntent?.let { intent ->
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.component = ComponentName(packageName, name)

            val bundle = Bundle().apply {
                putInt(ShareUtils.KEY_WINDOWING_MODE, ZoomWindowManagerWrapper.windowingModeZoom())
            }
            internalStartAppLiveData.postValue(Pair(intent, bundle))
        } ?: GLog.e(TAG, LogFlag.DL, "shareIntoZoomWindow error:originIntent = null")
    }

    /**
     * 处理文件转换后的分享操作, 在IO线程中更新分享意图，然后在UI线程中执行分享操作
     * @param activity 当前活动上下文
     * @param convertedMap 文件路径到其转换后URI的映射
     */
    fun handleShareAfterConvert(activity: BaseActivity, convertedMap: Map<Path, Uri>?, isForDirectShare: Boolean) {
        viewModelScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            GLog.d(TAG, "handleShareAfterConvert directShare $isForDirectShare, map $convertedMap")
            if (isForDirectShare) {
                val intent = currentTargetInfo?.targetIntent
                if (intent != null) {
                    ShareUtils.updateShareIntent(intent, getSelectedItems(), convertedMap)
                    withContext(Dispatchers.UI) {
                        handleShareActionDirectShare(activity, currentTargetInfo)
                    }
                }
            } else {
                ShareUtils.updateShareIntent(shareIntent, getSelectedItems(), convertedMap)
                withContext(Dispatchers.UI) {
                    handleShareAction(activity, currentResolveInfo)
                }
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        resolveInfoVM.onCreate()
        directShareVM.onCreate()
        MeicamEngineLimiter.getInstance().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        MeicamEngineLimiter.getInstance().unregister(this)
    }

    override fun onCleared() {
        super.onCleared()
        resolveInfoVM.onCleared()
        directShareVM.onCleared()
        securityViewModel.onCleared()
        clearShareData()
        ShareAppSortHelper.getUniqueMap().clear()
    }

    private fun showDeleteViewIfNeed() {
        if (supportDeleteAfterShare) {
            viewModelScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
                val isShareDeleteSelected = ConfigAbilityWrapper.getBoolean(SharePage.ALLOW_SHARE_DELETE, false)
                val isShareDeleteTipsAsFirstShow =
                    ConfigAbilityWrapper.getBoolean(SharePage.SHARE_DELETE_TIPS_AS_FIRST_SHOW, false)
                if (isShareDeleteTipsAsFirstShow.not()) {
                    context.getAppAbility<ISettingsAbility>()?.use {
                        it.setShareDeleteTipsAsFirstShow(true)
                    }
                }
                internalShowShareDelete.postValue(ShowShareDeleteData(isShareDeleteSelected, isShareDeleteTipsAsFirstShow))
            }
        }
    }

    /**
     * 设置勾选分享后删除按钮状态
     * @param enable 开或者关
     */
    fun setShareDeleteSelectedEnable(enable: Boolean) {
        viewModelScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            if (enable) {
                context.getAppAbility<ISettingsAbility>()?.use {
                    it.setShareDeleteSelectedEnable(true)
                }
                ShareTrackHelper.trackAndSendShareDeleteStatus(SHARE_DELETE_OPEN)
            } else {
                context.getAppAbility<ISettingsAbility>()?.use {
                    it.setShareDeleteSelectedEnable(false)
                }
                ShareTrackHelper.trackAndSendShareDeleteStatus(SHARE_DELETE_CLOSE)
            }
        }
    }

    override fun limitNow() {
        internalFinishActivityEvent.postValue(true)
    }

    override fun isActive(): Boolean {
        return isActive
    }

    /**
     * 清空二级菜单更多分享页面编辑后的数据
     */
    fun clearEditShareData() {
        editPinnedList.clear()
        editPinnedAbleList.clear()
    }

    /**
     * 更新分享置顶应用列表
     */
    fun updateSharePinnedData() {
        GLog.d(TAG, LogFlag.DL, "updateSharePinnedData: ")
        viewModelScope.launch(Dispatchers.IO) {
            val newList: MutableList<ResolveInfo> = ArrayList()
            editPinnedList.forEach {
                newList.add(it.resolveInfo)
            }
            ShareUtils.updateSharePinnedData(context, newList)
        }
    }

    /**
     * 清空二级菜单更多分享页面的数据
     */
    fun clearShareData() {
        clearEditShareData()
        pinnedList.clear()
        pinnedAbleList.clear()
        allShareEditList.clear()
        allShareList.clear()
    }

    /**
     * 通过bus发送事件给到菜单层继续分享后的操作
     * @param isCloseManual 是否用户主动关闭面板
     */
    fun postShareExitEvent(isCloseManual: Boolean) {
        val resultCode = if (isCloseManual) {
            STATE_DISMISS_AND_UPDATE_SELECTED_SHOW
        } else {
            STATE_DISMISS_AND_EXIT_EDIT_MODE
        }
        Bus.post(ShareExitEvent(resultCode))
    }

    /**
     * 显示分享图片/视频条件限制的Toast
     */
    private fun showShareErrorToast(context: Context, selectSize: Int, packageName: String): Boolean {
        return when (packageName) {
            SHARE_XHS -> showErrorMessage(context, selectSize, XHS_MAX_SHARE_SIZE)
            SHARE_WEI_BO -> showErrorMessage(context, selectSize, WB_MAX_SHARE_SIZE)
            else -> false
        }
    }

    /**
     * 根据条件与分享的应用显示不同Toast内容
     */
    private fun showErrorMessage(context: Context, selectSize: Int, maxSize: Int): Boolean {
        if (selectSize > maxSize) {
            ToastUtil.showShortToast(context.resources.getQuantityString(R.plurals.share_count_limit_message, maxSize, maxSize))
            return true
        } else {
            val selectionData = selectionData.value
            selectionData?.let {
                if ((it.videoCount > 0) && (it.photoCount > 0)) {
                    ToastUtil.showShortToast(context.getString(R.string.share_format_limit_message))
                    return true
                }
            }
        }
        return false
    }

    override fun createReloadTask(
        model: BaseModel<MediaItem>,
        reloadInfo: BaseReloadInfo<MediaItem, ItemViewData>,
        needDiff: Boolean,
        entrance: String?
    ): BaseReloadTask<MediaItem, ItemViewData> {
        return object : ShareReloadTask(model, reloadInfo, needDiff, this@ShareInnerViewModel, spanCount) {
            override val name: String = taskConfigStrategy.getTaskName(model.getModelType())
            override val taskOwner: ILoadTaskOwner = this@ShareInnerViewModel
            override val entrance: String? = entrance
            override fun createViewData(mediaItem: MediaItem, index: Int): ItemViewData =
                <EMAIL>(mediaItem, index)
        }.apply {
            priority = taskConfigStrategy.getPriority<Any>(this@ShareInnerViewModel)
        }
    }

    private abstract class ShareReloadTask(
        model: BaseModel<MediaItem>,
        reloadInfo: BaseReloadInfo<MediaItem, ItemViewData>,
        needDiff: Boolean,
        reloadCallback: ReloadCallback<MediaItem, ItemViewData>,
        spanCount: Int,
    ) : AlbumReloadTask(model, reloadInfo, needDiff, reloadCallback, spanCount) {

        /**
         * 使用空对象填充未加载的区域，避免计算diff出现多余的insert
         */
        override fun onCreateEmptyItemInfo(index: Int): ItemInfo<MediaItem?> = EMPTY_ITEM_INFO

        companion object {
            private val EMPTY_ITEM_INFO: ItemInfo<MediaItem?> = ItemInfo()
        }
    }

    companion object {
        private const val TAG = "ShareInnerViewModel"
        private const val DATA_CACHE_SIZE = 32
        private const val DATA_ACTIVE_SIZE = 16
        private const val DATA_VISIBLE_SIZE = 2
        private const val MIN_LOAD_COUNT = 4
        private const val MAX_LOAD_COUNT = 8
        private const val PACKAGE_WECHAT = "com.tencent.mm"
        private const val USER_CURRENT = -2
        private const val FORWARD_INTENT_TO_PARENT = "com.android.internal.app.ForwardIntentToParent"
        private const val FORWARD_INTENT_TO_MANAGED_PROFILE = "com.android.internal.app.ForwardIntentToManagedProfile"
        private const val SHARE_ALBUM_NAME = "com.oplus.gallery.framework.abilities.ocloudsync.ui.ShareToCloudAlbumActivity"
        private const val SHARE_WEI_BO = "com.sina.weibo.composerinde.ComposerDispatchActivity"
        private const val SHARE_XHS = "com.xingin.xhs.routers.RouterPageActivity"
        private const val XHS_MAX_SHARE_SIZE = 18
        private const val WB_MAX_SHARE_SIZE = 9
    }
}