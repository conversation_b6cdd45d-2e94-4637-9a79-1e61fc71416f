/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorEngine.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.base;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.PointF;
import android.util.Size;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAVFileInfo;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IHardwareErrorListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ISeekingListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IStreamingEngineListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoFrameRetriever;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data.SaveVideoInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.filter.IVideoFilterManager;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.player.IVideoPlayerListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.IImageGrabListener;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams;
import com.oplus.gallery.videoeditorpage.abilities.engine.base.interfaces.IAVFileInfo;
import com.oplus.gallery.videoeditorpage.abilities.interfaces.ITimelineCompileListener;
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveType;
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveVideoData;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频编辑总引擎
 */
public abstract class EditorEngine {

    private static final String TAG = "EditorEngine";
    public static final long TIME_MILLIS_TO_MICROSECOND = 1000;

    public static final long TIME_BASE = 1000000L;
    public static final long TIME_BASE_MS = 1000L;
    public static final long FRAME_DURATION = 40000L;
    /**
     * 最小剪辑持续时间，单位与TIME_BASE一致。
     * 用于限制视频编辑中片段的最短时长。
     */
    public static final long MIN_CLIP_DURATION = (long) (0.1 * TIME_BASE);

    public static final String MUSIC_ASSETS_PATH = "assets:/music";
    public static final String TRANSITION_ASSETS_PATH = "assets:/transition";
    public static final int DEFAULT_VIDEO_RESOLUTION_FPS = 30;
    public static final int DEFAULT_VIDEO_RESOLUTION_WIDTH = 1920;
    public static final int DEFAULT_VIDEO_RESOLUTION_HEIGHT = 1080;

    public static final int STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER = 2;
    public static final int STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER = 4;
    /**
     * 视频播放起始时间
     */
    public static final long VIDEO_TIME_START = 0L;
    /**
     * 视频空时长
     */
    public static final long VIDEO_DURATION_EMPTY = 0L;
    /**
     * 首个轨道位置
     */
    public static final int TRACK_INDEX_FIRST = 0;
    /**
     * 视频播放起始位置
     */
    public static final int VIDEO_POSITION_START = 0;
    protected static final int DEFAULT_AUDIO_SAMPLE_RATE = 44100;
    protected static final int DEFAULT_AUDIO_CHANNEL_COUNT = 2;

    protected List<IVideoPlayerListener> mPlayerListenerList;
    protected List<ITimelineCompileListener> mCompileListenerList;
    protected List<IStreamingEngineListener> mEngineListenersList;
    protected List<ISeekingListener> mSeekingListenerList;
    protected List<ITimelineChangedListener> mTimelineListenerList;
    protected List<IHardwareErrorListener> mHardwareErrorListenerList;
    protected List<IImageGrabListener> mImageGrabListenerList;
    protected int mFps;
    protected int mTimelineWidth;
    protected int mTimelineHeight;
    protected String mMaxCountText;
    protected boolean mAiIsOperation = false;//判断在快速成片是否进行过操作

    /**
     * 保存的类型，默认保存为视频类型
     */
    private SaveType mSaveType = SaveType.VIDEO;

    public void setmAiIsOperation(boolean mAiIsOperation) {
        this.mAiIsOperation = mAiIsOperation;
    }

    public String getMaxCountText() {
        return mMaxCountText;
    }

    public void setMaxCountText(String maxCountText) {
        mMaxCountText = maxCountText;
    }

    public EditorEngine() {
        mPlayerListenerList = new ArrayList<>();
        mCompileListenerList = new ArrayList<>();
        mEngineListenersList = new ArrayList<>();
        mSeekingListenerList = new ArrayList<>();
        mTimelineListenerList = new ArrayList<>();
        mHardwareErrorListenerList = new ArrayList<>();
        mImageGrabListenerList = new ArrayList<>();

        mFps = DEFAULT_VIDEO_RESOLUTION_FPS;
        mTimelineWidth = DEFAULT_VIDEO_RESOLUTION_WIDTH;
        mTimelineHeight = DEFAULT_VIDEO_RESOLUTION_HEIGHT;
    }

    public void addVideoPlayerListener(IVideoPlayerListener listener) {
        if (!mPlayerListenerList.contains(listener)) {
            mPlayerListenerList.add(listener);
        }
    }

    public void removeVideoPlayerListener(IVideoPlayerListener listener) {
        GLog.d(TAG, "removeVideoPlayerListener");
        mPlayerListenerList.remove(listener);
    }

    public void addTimelineCompileListener(ITimelineCompileListener listener) {
        if (!mCompileListenerList.contains(listener)) {
            mCompileListenerList.add(listener);
        }
    }

    public void removeTimelineCompileListener(ITimelineCompileListener listener) {
        mCompileListenerList.remove(listener);
    }

    public void addStreamingEngineListener(IStreamingEngineListener listener) {
        if (!mEngineListenersList.contains(listener)) {
            mEngineListenersList.add(listener);
        }
    }

    public void removeStreamingEngineListener(IStreamingEngineListener listener) {
        mEngineListenersList.remove(listener);
    }

    public void addSeekingListener(ISeekingListener listener) {
        if (!mSeekingListenerList.contains(listener)) {
            mSeekingListenerList.add(listener);
        }
    }

    /**
     * 移除seek监听器
     *
     * @param listener seek监听器
     */
    public void removeSeekingListener(ISeekingListener listener) {
        mSeekingListenerList.remove(listener);
    }

    public void addTimelineChangeListener(ITimelineChangedListener listener) {
        mTimelineListenerList.add(listener);
    }

    public boolean containTimelineChangeListener(ITimelineChangedListener listener) {
        return mTimelineListenerList.contains(listener);
    }

    public void removeTimelineListener(ITimelineChangedListener listener) {
        mTimelineListenerList.remove(listener);
    }

    public void notifyTimelineChanged(boolean updateTimelineView) {
        for (ITimelineChangedListener listener : mTimelineListenerList) {
            listener.onCurrentTimelineChanged(getCurrentTimeline(), updateTimelineView);
        }
    }

    public void removeImageGrabListener(IImageGrabListener listener) {
        mImageGrabListenerList.remove(listener);
    }

    public void setVideoResolution(int fps, int width, int height) {
        mTimelineWidth = width;
        mTimelineHeight = height;
        mFps = fps;
    }

    public int getFps() {
        return mFps;
    }

    public int getWidth() {
        return mTimelineWidth;
    }

    public int getHeight() {
        return mTimelineHeight;
    }

    /**
     * set timeline with try-catch
     *
     * @param timeline
     * @return
     */
    public boolean trySetTimeline(ITimeline timeline) {
        try {
            ITimeline resultTimeline = setTimeline(timeline);
            if (resultTimeline == null) {
                return false;
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "trySetTimeline failed, exception = " + e.getMessage());
            return false;
        }
        return true;
    }

    public Size getOriginalSizeFromTimeline() {
        ITimeline timeline = getCurrentTimeline();
        if (timeline != null) {
            Size originalSize = timeline.getOriginalSize();
            if ((originalSize != null) && (originalSize.getWidth() > 0) && (originalSize.getHeight() > 0)) {
                return originalSize;
            }

            if (timeline.getVideoTrackCount() > 0) {
                IVideoTrack videoTrack = timeline.getVideoTrack(0);
                if (videoTrack.getClipCount() > 0) {
                    IVideoClip videoClip = (IVideoClip) videoTrack.getClip(0);
                    if (videoClip != null) {
                        Size size = videoClip.getFileVideoSize();
                        timeline.setOriginalSize(size);
                        return size;
                    }
                }
            }
        }
        return new Size(0, 0);
    }

    /**
     * warning: when you operate current timeline, the preview will change too
     *
     * @return
     */
    public abstract ITimeline getCurrentTimeline();

    public abstract ITimeline cloneCurrentTimeline();

    public abstract ITimeline setTimeline(String jsonString);

    public abstract ITimeline setTimeline(ITimeline timeline);

    public abstract ITimeline setTimeline(ITimeline timeline, boolean isAdaptationResolution);

    public abstract void removeCurrentTimeline();

    public abstract void clearCacheResource(int flags);

    public abstract void setPreviewWindow(View view);

    /**
     * 设置预览的变换矩阵
     *
     * @param matrix     必须是4*4的矩阵
     * @param repaintNow 是否立即重绘: true 表示立即重绘; false 表示不立即重绘，矩阵会在下一次重绘时生效;
     */
    public abstract void setDisplayMatrix(float[] matrix, boolean repaintNow);

    /**
     * 获取预览区域的SurfaceView
     *
     * @return SurfaceView
     */
    public abstract SurfaceView getSurfaceView();


    /**
     * 初始化VideoFileInfo
     *
     * @param videoPath 路径
     * @return NvsAVFileInfo 文件信息
     */
    public abstract NvsAVFileInfo getVideoFileInfo(String videoPath);

    /**
     * 更改预览时 视频bitDepth
     */
    public abstract void changeVideoBitDepth();

    /**
     * 更改预览时 sdr上变换到 hdr 视频效果的 color gain值
     */
    public abstract void changeColorGain();

    /**
     * 设置正交投影的近平面和远平面
     *
     * @param nearPlane 近平面
     * @param farPlane  远平面
     */
    public abstract void setOrthoPlanes(float nearPlane, float farPlane);

    public abstract void setFillMode(int fillMode);

    public abstract void repaintPreviewWindow();

    public abstract void enableBlurToTimeLine(boolean enable);

    public abstract Bitmap getBitmapFromPreviewWindow();

    public abstract IVideoFrameRetriever createVideoFrameRetriever(String filepath);

    public abstract ITimeline createTimeline(boolean bindToEngine);

    /**
     * 初始化引擎
     *
     * @param context 上下文
     * @return true-初始化成功，false-初始化失败
     */
    public abstract boolean init(Context context);

    /**
     * 初始化上下文，不添加额外设置，用于给滤镜列表图标加滤镜
     *
     * @param context 上下文
     * @return true-初始化成功，false-初始化失败
     */
    public abstract boolean initContext(Context context);

    public abstract void unInit(Context context);

    public abstract void restoreListener(Context context);

    public abstract void startPlayer(long startTime, long endTime);

    public abstract void stopPlayer();

    /**
     * 强制停止播放器
     */
    public abstract void forceStopPlayer();

    /**
     * 是否播放中
     * @return true-播放中，false-未播放
     */
    public abstract boolean isPlaying();

    /**
     * 是否保存中
     * @return true-保存中，false-未保存
     */
    public abstract boolean isSaving();

    /**
     * 根据输入时间 seek timeline
     * @param time 时间点，单位为 ms
     */
    public abstract void seekTo(long time);

    public abstract void seekTo(long progress, int flag);

    public abstract void seekTo(long progress, int previewSizeMode, int flag);

    /**
     * 保存视频
     *
     * @param saveVideoData  保存视频参数信息
     * @param outputFilePath 保存视频输出路径
     * @return true-保存成功，false-保存失败
     */
    public abstract boolean saveVideo(SaveVideoData saveVideoData, String outputFilePath);

    public abstract void restoreByTimeline(ITimeline timeline, boolean updateTimelineView);

    public abstract void restoreByTimeline(ITimeline timeline, boolean updateTimelineView, boolean needChangeCaptionSize);

    public abstract IAVFileInfo getAVFileInfo(String filePath);

    public abstract boolean changeTimelineSize(int newW, int newH);

    /**
     * 获取时间线总时长
     * @return 时间线总时长，单位为 ms
     */
    public abstract long getTotalTime();

    /**
     * 获取时间线当前时间，单位为 ms
     * @return 时间线当前时间，单位为 ms
     */
    public abstract long getCurrentTime();

    /**
     * 获取时间线当前位置，单位为 us
     * @return 获取时间线当前位置，单位为 us
     */
    public abstract long getTimelineCurrentPosition();

    public abstract long getTimelineDuration();

    public abstract void initVideoFilterManager();

    public abstract IVideoFilterManager getVideoFilterManager();

    public abstract PointF mapCanonicalToLiveWindowView(PointF pointF);

    public abstract PointF mapLiveWindowViewToCanonical(PointF pointF);

    public abstract View getLiveWindow();

    public abstract int getLiveWindowWidth();

    public abstract int getLiveWindowHeight();

    public abstract void getLiveWindowLocation(int[] location);

    public abstract PointF mapLiveWindowViewToNormalized(PointF pointF);

    /**
     * 添加字幕
     *
     * @param captionParam 字幕初始化参数
     * @return 字幕实例
     */
    public abstract BaseCaption appendCaption(@NonNull CaptionParams captionParam);

    public abstract void removeCaption(BaseCaption caption);

    public abstract List<BaseCaption> getCaptionsByTimelinePosition(long position);

    public abstract int getCurrentEngineState();

    public abstract boolean isOutOfbounding(BaseCaption caption);

    public abstract void resetMaskEffect(boolean isTemplate, int flag);

    /**
     * 检查算法是否支持软解视频
     *
     * @param context  context
     * @param filePath 文件路径
     * @return true：支持，false：不支持
     */
    public abstract boolean checkVideoSoftSupported(Context context, String filePath);

    /**
     * 获取视频帧间距，等于0，则表示获取失败
     *
     * @param filePath 文件路径
     * @return 帧间距
     */
    public abstract int getVideoFrameInterval(String filePath);

    public SaveType getSaveType() {
        return mSaveType;
    }

    /**
     * 设置保存视频类型
     */
    public void setSaveType(SaveType saveType) {
        this.mSaveType = saveType;
    }

    /**
     * 显示自定义的裁剪缩略图加载器
     *
     * @param parent  父容器
     * @param time    当前界面宽度对应的时间
     * @param padding 缩图轴左右padding
     */
    public abstract void showCustomTrimThumbLoader(ViewGroup parent, long time, int padding);

    /**
     * 加载视频导出olive缩图轴组件
     * @param parent 父容器
     */
    public abstract void showVideoOliveThumbLoader(ViewGroup parent);

    /**
     * 定制化生成视频
     *
     * @param info 视频信息
     */
    public abstract void saveVideo(SaveVideoInfo info);

    /**
     * 同步音频轨道到视频轨道
     *
     */
    public abstract void trimAudioToVideo();
    public interface ITimelineChangedListener {
        void onCurrentTimelineChanged(ITimeline timeline, boolean updateTimelineView);
    }
}
