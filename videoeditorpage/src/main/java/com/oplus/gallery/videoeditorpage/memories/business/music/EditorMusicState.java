/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorMusicState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.music;

import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils;
import com.oplus.gallery.foundation.util.systemcore.IntentUtils;
import com.oplus.gallery.foundation.util.version.ApiLevelUtil;
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog;
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesActivity;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesManager;
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadingListenerImpl;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EditorMusicState extends EditorBaseState implements EditorBaseUIController.OnIconClickListener {
    private static final String TAG = "EditorMusicState";

    private static final String TYPE_NAME = "MemoriesMusic";
    private static final int RESULT_CODE_READ_MEDIA_AUDIO_LOCAL_MUSIC = 100;
    private String mPrevMusic;
    private int mPrevPosition = 0;
    private HashMap<Integer, MusicItem> mWaitingDownloadMap = new HashMap<>();

    public EditorMusicState(Context context, ControlBarView controlBarView) {
        super(TYPE_NAME, context, controlBarView, MONITOR_NET_STATE);
        MusicSourceManager.getInstance().checkBuiltinItem();
        if (mEngineManager != null) {
            mPrevMusic = mEngineManager.getCurrentThemeMusic();
            GLog.d(TAG, "EditorMusicState mPrevMusic = " + mPrevMusic);
            mEngineManager.play();
            mPrevPosition = mEngineManager.getThemeCurrentMusicPos();
        }
    }

    @Override
    protected EditorBaseUIController createUIController() {
        EditorMusicUIController musicUIController = new EditorMusicUIController(mContext, mControlBarView, this);
        musicUIController.setOnIconClickListener(this);
        return musicUIController;
    }

    @Override
    public boolean done() {
        if (mEngineManager != null) {
            String curMusic = mEngineManager.getCurrentThemeMusic();
            GLog.d(TAG, "done mPrevMusic = " + mPrevMusic + ", curMusic = " + curMusic
                    + ", mMusic = " + MemoriesManager.getCurMemoriesInfo().mMusic);
            if (!TextUtils.equals(mPrevMusic, curMusic)) {
                MemoriesManager.getCurMemoriesInfo().mMusic = curMusic;
            }
        }
        return super.done();
    }

    @Override
    public void cancel() {
        super.cancel();
        GLog.d(TAG, "cancel");
        if (mEngineManager != null) {
            String curMusic = mEngineManager.getCurrentThemeMusic();
            GLog.d(TAG, "cancel mPrevMusic = " + mPrevMusic + ", curMusic = " + curMusic);
            boolean isPlaying = mEngineManager.isPlaying();
            if (!TextUtils.isEmpty(mPrevMusic)) {
                if (!TextUtils.equals(mPrevMusic, curMusic)) {
                    if (TextUtils.equals(MusicSourceManager.MUSIC_NONE, mPrevMusic)) {
                        mEngineManager.setThemeMusicMute(true);
                    } else {
                        mEngineManager.addThemeMusic(mPrevMusic);
                    }
                    mEngineManager.reset();
                } else {
                    if (isPlaying && !mEngineManager.isPlaying()) {
                        mEngineManager.resume();
                    }
                }
            } else if (!TextUtils.isEmpty(curMusic)) {
                mEngineManager.removeThemeMusic(false);
                mEngineManager.play();
            }
        }
        if (hasUserDownloadTask()) {
            ToastUtil.showShortToast(R.string.videoeditor_source_cancel_download);
        }
        MusicSourceManager.getInstance().resetDownloadQueueStatus();

    }

    @Override
    public boolean onBackPressed() {
        if (!hideContinueDownloadOnMobileDialog() && !hideRequestUseNetworkDialog()
                && !hideRequestDownloadOnMobileDialog()) {
            cancel();
        }
        return super.onBackPressed();
    }

    @Override
    public void onIconClick(View view, int position, Object item) {
        GLog.d(TAG, "onIconClick pos = " + position + ", item = " + item);
        if ((mEngineManager != null) && (item instanceof MusicItem)) {
            final MusicItem data = (MusicItem) item;
            EditorMusicUIController uiController = (EditorMusicUIController) getUIController();
            uiController.setCurrentSelectedPosition(position);
            // jump to select local music
            if (TextUtils.equals(MusicSourceManager.MUSIC_LOCAL, data.getSourcePath())) {
                uiController.setLastApplyItem(data);
                // 外销用system gallery role替代宽泛权限，但是T及以上如果没有对应权限查询媒体库就会有额外限制，所以需要申请读取音频权限
                Activity activity = (FragmentActivity) mContext;
                String permission = Manifest.permission.READ_MEDIA_AUDIO;
                if ((activity != null) && ApiLevelUtil.isAtLeastAndroidT() && !ConfigAbilityWrapper.getBoolean(IS_REGION_CN)
                        && !RuntimePermissionUtils.isPermissionGranted(activity, permission)) {
                    activity.requestPermissions(new String[]{permission}, RESULT_CODE_READ_MEDIA_AUDIO_LOCAL_MUSIC);
                } else {
                    showLocalMusicPanelDialog();
                }
                mPrevPosition = position;
            } else if (position != mPrevPosition) {
                if (data.isNeedDownloadFile()) {
                    boolean requestNetwork = !NetworkPermissionManager.isUseOpenNetwork();
                    if (checkIfNeedShowPrivacyDialog()) {
                        showPrivacyDialog(mContext);
                        mWaitingDownloadMap.put(position, data);
                    } else if (requestNetwork) {
                        showNetworkPermissionDialog();
                        mWaitingDownloadMap.put(position, data);
                    } else if (!NetworkMonitor.isNetworkValidated()) {
                        handleNoNetworkConnection(view, uiController);
                    } else if (!NetworkMonitor.isWifiValidated()
                            && NetworkMonitor.isMobileValidated()
                            && !NetworkPermissionManager.isAllowDownloadOnMobile()) {
                        showRequestDownloadOnMobileDialog();
                        mWaitingDownloadMap.put(position, data);
                    } else {
                        MusicSourceManager.getInstance().manualDownloadMusic(data);
                    }
                } else {
                    GLog.d(TAG, "onIconClick");
                    MusicSourceManager.getInstance().resetSource(data.getMusicId());
                    if (TextUtils.equals(MusicSourceManager.MUSIC_NONE, data.getSourcePath())) {
                        uiController.setLastApplyItem(data);
                        mEngineManager.setThemeMusicMute(true);
                        mEngineManager.play();
                        mPrevPosition = position;
                    } else {
                        mEngineManager.stop();
                        if (mContext instanceof Activity) {
                            GLog.d(TAG, "onIconClick pos = " + position + ", item = " + item);
                            ((Activity) mContext).runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    uiController.setLastApplyItem(data);
                                    String musicId = MusicSourceManager.getMemoriesMusicPath(data);
                                    mEngineManager.addThemeMusic(musicId);
                                    if (ActivityLifecycle.isRunningForeground()) {
                                        mEngineManager.play();
                                    }
                                }
                            });
                        } else {
                            GLog.w(TAG, "onIconClick change music failed, pos = " + position + ", item = " + item);
                        }
                        mPrevPosition = position;
                    }
                }
            }
        }
    }

    private  void applyLocalSong(Intent data) {
        if ((data != null) && (data.getData() != null)) {
            String filePath = VideoEditorHelper.getPath(mContext, data.getData());
            long trimStart = IntentUtils.getLongExtra(data, MusicTrimFragment.MUSIC_TRIM_START, 0L);
            long trimEnd = IntentUtils.getLongExtra(data, MusicTrimFragment.MUSIC_TRIM_END, 0L);
            GLog.d(TAG, "applyLocalSong trimStart = " + trimStart + ", trimEnd = " + trimEnd);
            mEngineManager.addThemeTrimMusic(filePath, trimStart, trimEnd);
            mEngineManager.play();
        }
    }

    private void showLocalMusicPanelDialog() {
        FragmentActivity activity = (FragmentActivity) mContext;
        if (activity.isDestroyed() || activity.isFinishing()) {
            return;
        }
        MusicTrimFragment musicTrimFragment = new MusicTrimFragment();
        musicTrimFragment.setResultCallback(data -> applyLocalSong(data));
        PanelFragment panelFragment = new PanelFragment.Builder()
                .setContentFragment(musicTrimFragment)
                .setPreventDismissEnable(false)
                .setTitle(mContext.getResources().getString(R.string.videoeditor_music_picker_title))
                .setMenuLayoutId(R.menu.videoeditor_menu_panel_cancel)
                .setToolbarEnable(true)
                .setDragViewVisible(View.INVISIBLE)
                .create();
        panelFragment.setMenuLeftBtnListener(item -> {
            panelFragment.dismiss();
            return true;
        });
        PanelDialog panelDialog = new PanelDialog.Builder()
                .setSkipCollapsed(true)
                .setPanelFragment(panelFragment)
                .setFinalNavColorAfterDismiss(PanelFragment.getPanelFinalNavColor((activity), false))
                .setLifecycleCallback(new PanelDialog.LifecycleCallback() {
                    @Override
                    public void onStart() {
                        if ((mContext != null) && (mContext instanceof MemoriesActivity)) {
                            ((MemoriesActivity) mContext).onPauseCheck();
                        }
                    }

                    @Override
                    public void onStop() {
                        if ((mContext != null) && (mContext instanceof MemoriesActivity)) {
                            ((MemoriesActivity) mContext).onResumeCheck();
                        }
                    }
                }).create();
        panelDialog.show(activity.getSupportFragmentManager());
    }

    private void handleNoNetworkConnection(View view, EditorMusicUIController uiController) {
        ToastUtil.showShortToast(R.string.videoeditor_editor_no_network);
        if (view != null) {
            view.post(() -> {
                uiController.selectedLastApplyItem();
                uiController.getAdapter().notifyDataSetChanged();
            });
        }
    }

    @Override
    public void onAllowOpenNetwork() {
        if (!NetworkMonitor.isNetworkValidated()) {
            EditorMusicUIController uiController = (EditorMusicUIController) getUIController();
            handleNoNetworkConnection(mControlBarView, uiController);
            return;
        }
        OnLoadingListenerImpl listener = new OnLoadingListenerImpl<MusicItem>() {
            @Override
            public void onLoadingFinish(int code, @Nullable List allEntityList) {
                if (mUIController instanceof EditorMusicUIController) {
                    ((EditorMusicUIController) mUIController).refresh(allEntityList);
                }
                MusicSourceManager.getInstance().retryDownload();
                startDownloadRemain();
            }
        };
        MusicSourceManager.getInstance().requestNetworkResource(listener);
    }

    @Override
    public void onAllowUseMobileData() {
        MusicSourceManager.getInstance().retryDownload();
        startDownloadRemain();
    }

    @Override
    public void onNetworkError() {
        MusicSourceManager.getInstance().resetDownloadQueueStatus();
        ((EditorMusicUIController) getUIController()).selectedLastApplyItem();
    }

    @Override
    public void onDisallowOpenNetwork() {
        ((EditorMusicUIController) getUIController()).selectedLastApplyItem();
        mWaitingDownloadMap.clear();
        MusicSourceManager.getInstance().clearDownloadInfo();
        getUIController().notifyDataSetChanged();
    }

    @Override
    public void onDisallowUseMobileData() {
        ((EditorMusicUIController) getUIController()).selectedLastApplyItem();
        mWaitingDownloadMap.clear();
        MusicSourceManager.getInstance().clearDownloadInfo();
        getUIController().notifyDataSetChanged();
    }

    @Override
    public void retryRequestSource() {
        GLog.d(TAG, "retryDownload");
        MusicSourceManager.getInstance().retryDownload();
    }

    @Override
    public int requestDownloadOnMobileDialogTitle() {
        return R.string.videoeditor_video_editor_confirm_download_music;
    }

    @Override
    public int requestDownloadOnMobileDialogMsg() {
        return R.string.videoeditor_video_editor_network_change_tip;
    }

    @Override
    public int requestUseNetworkDialogTitle() {
        return com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_title;
    }

    @Override
    public int requestUseNetworkDialogMessage() {
        return com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_music;
    }

    @Override
    public int networkErrorToastMsg() {
        return R.string.videoeditor_editor_music_download_network_disconnect;
    }

    @Override
    public int downloadFailureToastMsg() {
        return R.string.videoeditor_editor_music_download_fail;
    }

    @Override
    public boolean hasUserDownloadTask() {
        return MusicSourceManager.getInstance().getDownloadTaskSize() > 0;
    }

    @Override
    public void onChangeToNetwork() {
        super.onChangeToNetwork();
        EditorMusicUIController controller = (EditorMusicUIController) getUIController();
        if ((controller != null) && !isHasUpdateList()) {
            controller.updateNetworkSourceLists();
        }
    }

    private void startDownloadRemain() {
        for (Map.Entry<Integer, MusicItem> entry : mWaitingDownloadMap.entrySet()) {
            int id = entry.getValue().getMusicId();
            MusicItem item = MusicSourceManager.getInstance().getMusic(id);
            onIconClick(null, entry.getKey(), item);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == RESULT_CODE_READ_MEDIA_AUDIO_LOCAL_MUSIC) {
            showLocalMusicPanelDialog();
        }
    }
}
