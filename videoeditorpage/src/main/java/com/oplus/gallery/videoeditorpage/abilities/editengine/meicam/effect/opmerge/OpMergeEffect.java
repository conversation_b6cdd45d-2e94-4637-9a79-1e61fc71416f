/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - OpMergeEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.opmerge;

import android.opengl.ETC1Util;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.filter.MergeFilter;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.filter.NoFilter;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.custom.IMeicamCustomerEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.timeline.MeicamTimelineEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsCustomVideoFx;
import com.meicam.sdk.NvsStreamingContext;

import java.util.ArrayList;
import java.util.List;

public class OpMergeEffect extends MeicamTimelineEffect implements IMeicamCustomerEffect {

    private static final String TAG = "OpMergeEffect";
    public static final String JSON_TYPE_NAME = "opmerge";
    public static final String EFFECT_NAME = "op";
    private transient List<NvsCustomVideoFx.Renderer> mRendererList;

    @SerializedName("class_type")
    private String mEffectNameForSave = JSON_TYPE_NAME;// use to mark the type in saved json file
    @SerializedName("filePath")
    private String mFilePath;
    @SerializedName("width")
    private int mWidth;
    @SerializedName("height")
    private int mHeight;
    @SerializedName("trans_x")
    private float mTransX;
    @SerializedName("trans_y")
    private float mTransY;
    @SerializedName("repeat")
    private int mRepeatType = StreamingConstant.Sticker.REPEAT_TYPE_NONE;

    public OpMergeEffect(String pastePath) {
        super(EFFECT_NAME, TYPE_CUSTOMER_FX);
        mFilePath = pastePath;
    }

    public void setRepeatType(int type) {
        mRepeatType = type;
    }

    public void setFilePath(String fileDir) {
        mFilePath = fileDir;
    }

    @Override
    public List<NvsCustomVideoFx.Renderer> getRendererList() {
        if (mRendererList == null) {
            mRendererList = new ArrayList<>();
        }

        Renderer renderer = new Renderer();
        mRendererList.add(renderer);

        return mRendererList;
    }

    public void setPasteRect(int x, int y, int width, int height) {
        mWidth = width;
        mHeight = height;
        mTransX = x;
        mTransY = y;
    }

    private class Renderer implements NvsCustomVideoFx.Renderer {

        private MergeFilter mMergeFilter;
        private NoFilter mShowFilter;
        private DirPkmReader mDirPkmReader;

        public Renderer() {
            mMergeFilter = new MergeFilter(NvsStreamingContext.getContext());
            mDirPkmReader = new DirPkmReader(NvsStreamingContext.getContext(), mFilePath + "/", true, 1);
        }

        @Override
        public void onInit() {

        }

        @Override
        public void onCleanup() {

        }

        @Override
        public void onPreloadResources() {

        }

        @Override
        public void onRender(NvsCustomVideoFx.RenderContext renderContext) {
            initProgram();
            ETC1Util.ETC1Texture texture = mDirPkmReader.getETC1Texture(renderContext.effectTime - mInTime, mRepeatType);
            if (texture != null) {
                mMergeFilter.setTextureSize(renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
                mMergeFilter.setInTextureId(renderContext.inputVideoFrame.texId);
                mMergeFilter.setOutTextureId(renderContext.outputVideoFrame.texId);
                mMergeFilter.setPasteTexture(texture);
                mMergeFilter.draw();
            } else {
                mShowFilter.setTextureSize(renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
                mShowFilter.setInTextureId(renderContext.inputVideoFrame.texId);
                mMergeFilter.setOutTextureId(renderContext.outputVideoFrame.texId);
                mShowFilter.draw();
            }

        }

        public void initProgram() {
            if (!mMergeFilter.isProgramReady()) {
                mMergeFilter.createProgram();
                mShowFilter = new NoFilter();
            }
        }

    }


}
