/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CustomClipEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.custom;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.utlis.FileUtil;
import com.oplus.gallery.videoeditorpage.utlis.JsonUtil;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.SLRectF;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamMaskRegionInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data.FxConfigInfo;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.meicam.sdk.NvsCustomVideoFx;
import com.meicam.sdk.NvsVideoFx;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomClipEffect extends BaseVideoClipEffect implements IMeicamCustomerEffect {
    public static final String JSON_TYPE_NAME = "CustomClipEffect";
    private static final String TAG = "CustomClipEffect";
    protected transient List<NvsVideoFx> mNvsVideoFxList;
    @SerializedName("mInTime")
    protected long mInTime;
    @SerializedName("mOutTime")
    protected long mOutTime;

    // inTime 基于clip inPoint 的偏差值百分比
    @SerializedName("mClipOffset")
    protected double mClipOffset;

    //时长占clip的百分比
    @SerializedName("mDurationPercentage")
    protected double mDurationPercentage = 1;

    protected transient IVideoClip mTargetClip;
    @SerializedName("mTrackIndex")
    protected int mTrackIndex;
    @SerializedName("mEffectId")
    protected int mEffectId = DEFAULT_FX_ID;
    @SerializedName("mStickerPath")
    protected String mStickerPath;
    @SerializedName("mPreviewUrl")
    protected String mPreviewUrl;

    /**
     * 是否是原始特效
     */
    @SerializedName("mIsRawFx")
    protected boolean mIsRawFx;

    private transient List<NvsCustomVideoFx.Renderer> mRendererList;
    private FxConfigInfo mConfigInfo;
    @SerializedName("mVideoClipType")
    private int mVideoClipType = -1;
    private SLRectF mCutRectF;
    @SerializedName("mFloatParams")
    private HashMap<String, Float> mFloatParams;
    @SerializedName("mEffectPlayType")
    private int mEffectPlayType = -1;
    @SerializedName("mIsInverseRegion")
    private boolean mIsInverseRegion = false;

    public CustomClipEffect(String name) {
        super(name);
        mClassType = JSON_TYPE_NAME;
        mFloatParams = new HashMap<>();
    }

    @NonNull
    @Override
    public Object clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        CustomClipEffect result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new CustomClipEffect(getName());
        }
        return result;
    }

    @Override
    public Map<String, Integer> getIntParams() {
        return null;
    }

    @Override
    public HashMap<String, Float> getFloatParams() {
        if (mFloatParams != null) {
            return (HashMap<String, Float>) mFloatParams.clone();
        }
        return null;
    }

    @Override
    public HashMap<String, String> getStringParams() {
        return null;
    }

    @Override
    public HashMap<String, Boolean> getBooleanParams() {
        return null;
    }

    @Override
    public HashMap<String, HashMap<Long, Float>> getTimeFloatParams() {
        return null;
    }

    @Override
    public void setFloatValue(String paramName, float value) {
        if ((mFloatParams != null) && (mFloatParams.containsKey(paramName))) {
            mFloatParams.remove(paramName);
        }
        if (mFloatParams != null) {
            mFloatParams.put(paramName, value);
        }
    }

    @Override
    public float getFloatValue(String paramName) {
        if ((mFloatParams != null) && (mFloatParams.containsKey(paramName))) {
            return mFloatParams.get(paramName);
        }

        return 0;
    }

    public void setPreviewUrl(String previewUrl) {
        this.mPreviewUrl = previewUrl;
    }

    public String getPreviewUrl() {
        return mPreviewUrl;
    }

    @Override
    public void setFloatValAtTime(String paramName, float value, long time) {

    }

    @Override
    public float getFloatValAtTime(String paramName, long time) {
        return 0;
    }

    @Override
    public void setFloatOffset(String paramName, float offset) {

    }

    public void setStickerPath(String stickerPath) {
        this.mStickerPath = stickerPath;
    }

    public String getStickerPath() {
        return mStickerPath;
    }

    @Override
    public void removeAllKeyframe() {

    }

    @Override
    public boolean hasKeyframe() {
        return false;
    }

    @Override
    public void removeKeyframeAtTime(long time) {

    }

    @Override
    public void setLocalFloatValue(String paramName, float value) {

    }

    @Override
    public float getLocalFloatValue(String paramName) {
        return 0;
    }

    @Override
    public void setStringValue(String paramName, String value) {

    }

    @Override
    public String getStringValue(String paramName) {
        return null;
    }

    @Override
    public void setBooleanVal(String paramName, boolean value) {

    }

    @Override
    public Boolean getBooleanVal(String paramName) {
        return null;
    }

    @Override
    public void setStrength(float strength) {

    }

    @Override
    public float getStrength() {
        return 0;
    }

    public int getEffectId() {
        return mEffectId;
    }

    public void setEffectId(int mEffectId) {
        this.mEffectId = mEffectId;
    }

    @Override
    public void setEffectPlayType(int playType) {
        this.mEffectPlayType = playType;
    }

    @Override
    public void setEffectPlayDuration(long mCartoonActualDuration) {

    }

    @Override
    public long getEffectPlayDuration() {
        return 0;
    }

    @Override
    public int getEffectPlayType() {
        return mEffectPlayType;
    }

    @Override
    public void setAttachment(String key, Object value) {

    }

    @Override
    public Object getAttachment(String key) {
        return null;
    }

    @Override
    public void setFilterMask(boolean filterMask) {

    }

    @Override
    public void setIgnoreBackground(boolean ignoreBackground) {

    }

    @Override
    public boolean getIgnoreBackground() {
        return false;
    }

    @Override
    public void setRegion(float[] region) {

    }

    @Override
    public void setRegionAtTime(float[] region, long time) {

    }

    @Override
    public void setRegional(boolean regional) {

    }

    @Override
    public boolean isRegional() {
        return false;
    }

    @Override
    public float[] getRegion() {
        return new float[0];
    }

    @Override
    public HashMap<Long, float[]> getTimeRegions() {
        return null;
    }

    @Override
    public boolean isFilterMask() {
        return false;
    }

    @Override
    public boolean setDuration(long duration, long clipDuration) {
        return false;
    }

    @Override
    public void setInTime(long inTime) {
        mInTime = inTime;
        if (mTargetClip != null) {
            long clipDuration = mTargetClip.getOutPoint() - mTargetClip.getInPoint();
            mClipOffset = (double) (mInTime - mTargetClip.getInPoint()) / clipDuration;
            double percentage = (double) (mOutTime - mInTime) / clipDuration;
            if (percentage > 0) {
                mDurationPercentage = percentage;
            }
        }
    }

    public double getClipOffset() {
        return mClipOffset;
    }

    public double getDurationPercentage() {
        return mDurationPercentage;
    }

    @Override
    public long getInTime() {
        return mInTime;
    }

    @Override
    public void setOutTime(long outTime) {
        mOutTime = outTime;
        if (mTargetClip != null) {
            long clipDuration = mTargetClip.getOutPoint() - mTargetClip.getInPoint();
            double percentage = (double) (mOutTime - mInTime) / clipDuration;
            if (percentage > 0) {
                mDurationPercentage = percentage;
            }
        }
    }

    @Override
    public long getOutTime() {
        return mOutTime;
    }

    @Override
    public int getTrackIndex() {
        return mTrackIndex;
    }

    @Override
    public void setTrackIndex(int trackIndex) {
        mTrackIndex = trackIndex;
    }

    @Override
    public int getType() {
        return TYPE_CUSTOMER_FX;
    }

    @Override
    public void setRegionInfo(MeicamMaskRegionInfo maskRegionInfo) {
    }

    @Override
    public MeicamMaskRegionInfo getRegionInfo() {
        return null;
    }

    @Override
    public void setRegionalFeatherWidth(float value) {
    }

    @Override
    public float getRegionalFeatherWidth() {
        return 0;
    }

    @Override
    public void setInverseRegion(boolean isInverseRegion) {
        mIsInverseRegion = isInverseRegion;
        if (mRendererList != null) {
            for (NvsCustomVideoFx.Renderer renderer : mRendererList) {
                if (renderer instanceof CustomRenderer) {
                    ((CustomRenderer) renderer).setFloatParams(getFloatParams(), isInverseRegion());
                }
            }
        }
    }

    @Override
    public void setIntVal(String key, int value) {
    }

    @Override
    public int getIntVal(String key) {
        return 0;
    }

    @Override
    public void setCutRectF(SLRectF rectF) {
        mCutRectF = rectF;
        if (mRendererList != null) {
            for (NvsCustomVideoFx.Renderer renderer : mRendererList) {
                if (renderer instanceof CustomRenderer) {
                    ((CustomRenderer) renderer).setTargetClip(mTargetClip, getEffectType());
                    ((CustomRenderer) renderer).setCutRectF(mCutRectF, isFillClipEdge());
                    ((CustomRenderer) renderer).setFloatParams(getFloatParams(), isInverseRegion());
                }
            }
        }
    }

    @Override
    public SLRectF getCutRectF() {
        return mCutRectF;
    }

    @Override
    public boolean isInverseRegion() {
        return mIsInverseRegion;
    }

    @Override
    public List<NvsCustomVideoFx.Renderer> getRendererList() {
        GLog.e(TAG, "getRendererList mRendererList = " + mRendererList);
        if (mRendererList == null) {
            mRendererList = new ArrayList<>();
            switch (getEffectType()) {
                case StreamingConstant.ClipEffectType.TYPE_CLIP_MASK_CUSTOM:
                case StreamingConstant.ClipEffectType.TYPE_TRANSFORM_2D_INTERNAL:
                    CustomRenderer customRenderer = new CustomRenderer(mName);
                    customRenderer.setTargetClip(mTargetClip, getEffectType());
                    customRenderer.setCutRectF(mCutRectF, isFillClipEdge());
                    customRenderer.setFloatParams(getFloatParams(), isInverseRegion());
                    mRendererList.add(customRenderer);
                    break;
                default:
                    File config = new File(mName + File.separator + BaseVideoFx.CONFIG);
                    mConfigInfo = JsonUtil.parseJson(FileUtil.readFromFile(config), new TypeToken<FxConfigInfo>() {
                    });

                    if (mConfigInfo != null) {
                        IVideoClip videoClip = mTargetClip;
                        if (mTargetClip != null) {
                            videoClip = mTargetClip.clone();
                        }
                        for (int i = 0; i < mConfigInfo.getPassCount(); i++) {
                            Renderer renderer = new Renderer(mName + File.separator + BaseVideoFx.SHADER_PASS_DIR + i);
                            renderer.setConfigInfo(mConfigInfo);
                            renderer.setTargetClip(videoClip);
                            mRendererList.add(renderer);
                        }
                    }
            }
        } else {
            for (NvsCustomVideoFx.Renderer renderer : mRendererList) {
                if (renderer instanceof CustomRenderer) {
                    ((CustomRenderer) renderer).setTargetClip(mTargetClip, getEffectType());
                    ((CustomRenderer) renderer).setCutRectF(mCutRectF, isFillClipEdge());
                    ((CustomRenderer) renderer).setFloatParams(getFloatParams(), isInverseRegion());
                }
            }
        }
        return mRendererList;
    }

    @Override
    public boolean clearEffectRendererCache() {
        return false;
    }

    public List<NvsVideoFx> getNvsVideoFxList() {
        return mNvsVideoFxList;
    }

    public void addNvsVideoFx(NvsVideoFx nvsVideoFx) {
        if (mNvsVideoFxList == null) {
            mNvsVideoFxList = new ArrayList<>();
        }

        mNvsVideoFxList.add(nvsVideoFx);
    }

    public void setTargetVideoTrack(IVideoClip videoClip) {
        mTargetClip = videoClip;
    }

    public void setVideoClipType(int videoType) {
        mVideoClipType = videoType;
    }

    public IVideoClip getTargetClip() {
        return mTargetClip;
    }

    @Override
    public boolean isRawFx() {
        return mIsRawFx;
    }

    @Override
    public void setIsRawFx(boolean isRawFx) {
        mIsRawFx = isRawFx;
    }
}
