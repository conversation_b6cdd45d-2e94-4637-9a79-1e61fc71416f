/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/6/13
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/6/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.data;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class MusicResponseBean {

    @SerializedName("songList")
    private List<SongListBean> mSongList;

    public List<SongListBean> getSongList() {
        return mSongList;
    }

    public void setSongList(List<SongListBean> songList) {
        this.mSongList = songList;
    }

    public static class SongListBean extends BaseResourceBean {
        @SerializedName("zhName")
        private String mZhName;
        @SerializedName("chName")
        private String mChName;
        @SerializedName("enName")
        private String mEnName;
        @SerializedName("iconPath")
        private String mIconPath;
        @SerializedName("songFilePath")
        private String mSongFilePath;
        @SerializedName("songId")
        private int mSongId;
        @SerializedName("updateTime")
        private String mUpdateTime;

        public String getZhName() {
            return mZhName;
        }

        public void setZhName(String zhName) {
            this.mZhName = zhName;
        }

        public String getChName() {
            return mChName;
        }

        public void setChName(String chName) {
            this.mChName = chName;
        }

        public String getEnName() {
            return mEnName;
        }

        public void setEnName(String enName) {
            this.mEnName = enName;
        }

        public String getIconPath() {
            return mIconPath;
        }

        public void setIconPath(String iconPath) {
            this.mIconPath = iconPath;
        }

        public String getSongFilePath() {
            return mSongFilePath;
        }

        public void setSongFilePath(String songFilePath) {
            this.mSongFilePath = songFilePath;
        }

        public int getSongId() {
            return mSongId;
        }

        public void setSongId(int songId) {
            this.mSongId = songId;
        }

        public String getUpdateTime() {
            return mUpdateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.mUpdateTime = updateTime;
        }
    }
}
