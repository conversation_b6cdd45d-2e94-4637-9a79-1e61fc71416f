/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : LinearSpacingItemDecoration.kt
 ** Description : 颜色item的间距配置类
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils

/**
 * 字幕recyclerview的线性布局item间距
 */
class LinearSpacingItemDecoration(
    /**
     * 间距值（像素）
     */
    private val spacing: Int,
    /**
     * 是否包括首尾间距
     */
    private val includeEdge: Boolean = true
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val itemCount = state.itemCount

        if (includeEdge) {
            // 设置上下左右的间距
            when ((parent.layoutManager as? LinearLayoutManager)?.orientation) {
                RecyclerView.VERTICAL -> {
                    outRect.top = if (position == 0) spacing else 0
                    outRect.bottom = spacing
                }
                RecyclerView.HORIZONTAL -> {
                    outRect.left = if (position == 0) spacing else 0
                    outRect.right = spacing
                }
            }
        } else {
            // 只在 item 之间添加间距
            val spacingForPos = (if (position < itemCount - 1) spacing else 0)
            when ((parent.layoutManager as? LinearLayoutManager)?.orientation) {
                RecyclerView.VERTICAL -> outRect.bottom = spacingForPos
                RecyclerView.HORIZONTAL -> {
                    if (ResourceUtils.isRTL(view.context)) {
                        outRect.left = spacingForPos
                    } else {
                        outRect.right = spacingForPos
                    }
                }
            }
        }
    }
}