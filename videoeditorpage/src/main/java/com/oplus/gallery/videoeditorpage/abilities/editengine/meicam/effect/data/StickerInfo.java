/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - StickerInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data;

import com.google.gson.annotations.SerializedName;

public class StickerInfo {
    @SerializedName("x_ratio")
    private float mXRatio = 0;
    @SerializedName("y_ratio")
    private float mYRatio = 1;
    @SerializedName("name")
    private String mFileName = null;
    @SerializedName("duration")
    private long mDuration = 0;
    @SerializedName("frame_count")
    private int mFrameCount = 0;
    @SerializedName("suffix")
    private String mSuffix;

    public float getXRatio() {
        return mXRatio;
    }

    public void setXRatio(float mXRatio) {
        this.mXRatio = mXRatio;
    }

    public float getYRatio() {
        return mYRatio;
    }

    public void setYRatio(float mYRatio) {
        this.mYRatio = mYRatio;
    }

    public String getFileName() {
        return mFileName;
    }

    public void setFileName(String mFileName) {
        this.mFileName = mFileName;
    }

    public boolean isAnim() {
        return mFrameCount > 0;
    }

    public int getFrameCount() {
        return mFrameCount;
    }

    public String getSuffix() {
        return mSuffix;
    }
}
