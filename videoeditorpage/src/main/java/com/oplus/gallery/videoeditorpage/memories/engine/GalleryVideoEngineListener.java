/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - GalleryVideoEngineListener.java
 ** Description: video engine status callback.
 ** Version: 1.0
 ** Date : 2017/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/13    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine;

public interface GalleryVideoEngineListener {
    // play callback
    void onPlayStatusChange(int status);

    void onPlayPositionChange(long position);

    // save mp4 callback
    void onExportStatusChange(int state);

    void onExportProgressChange(int progress);

    // stream ready callback
    void onEngineStateChanged(int state);

    void onFirstVideoFrameReady();

    // play exception callback
    void onEngineException(int errCode, String msg);
}
