/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorChangeSpeedState
 ** Description: 80413407 created
 ** Version: 1.0
 ** Date : 2025/4/19
 ** Author: 80413407
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80413407      2025/4/19     1.0     NEW
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.content.Context
import android.util.SparseArray
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene
import com.oplus.gallery.videoeditorpage.video.business.trim.IEffectClipResponder
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 负责实现变速页面的业务逻辑
 */
class EditorSpeederState(
    context: Context,
    editorControlView: EditorControlView,
    private val timelineViewModel: TimelineViewModel,
    private val effectClipResponderList: List<IEffectClipResponder?>? = null
) : EditorTrackBaseState<EditorSpeederUIController>(TAG, context, editorControlView) {

    /**
     * 视频播放监听器
     */
    var videoPlaybackListener: VideoPlaybackListener? = null

    @Override
    override fun getPageLevel(): PageLevelEnum = PageLevelEnum.PAGE_LEVEL_SECOND


    override fun create() {
        super.create()
        stopEngine()
    }

    override fun createUIController(): EditorSpeederUIController {
        return EditorSpeederUIController(
            mContext,
            mEditorControlView,
            this,
            operationSaveHelper,
            timelineViewModel,
            effectClipResponderList
        )
    }

    override fun showOperaIcon(): Boolean = false

    override fun clickCancel() {
        uiController.clickCancel()
        editorEngine?.trimAudioToVideo()
        super.clickCancel()
    }

    override fun clickDone() {
        uiController.clickDone()
        editorEngine?.trimAudioToVideo()
        super.clickDone()
    }

    override fun onPlayPositionChange(currentPosition: Long) {
        videoPlaybackListener?.onPlayBackPositionChange(currentPosition)
    }

    override fun onCurrentTimelineChanged(timeline: ITimeline?, updateTimelineView: Boolean) {
        super.onCurrentTimelineChanged(timeline, updateTimelineView)
        if (timeline == null) {
            return
        }
        updateZoomInOutFrameLayout()
    }

    override fun getEditorTrackScene(): EditorTrackScene {
        return EditorTrackScene.SPEED
    }

    /**
     * 更新缩放布局，根据当前时间线更新视频视图
     */
    private fun updateZoomInOutFrameLayout() {
        val timeline = mEditorEngine.currentTimeline
        val clipSparseArray: SparseArray<ArrayList<com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel>> = getClipArrayFromTimeline(timeline)
        uiController.addVideoView(clipSparseArray)
    }

    override fun destroy() {
        videoPlaybackListener = null
        super.destroy()
    }

    companion object {
        private const val TAG = "EditorChangeSpeedState"
    }
}

/**
 * 视频播放接口监听器
 */
interface VideoPlaybackListener {
    /**
     * 播放位置变化监听
     * @param position 当前播放位置
     */
    fun onPlayBackPositionChange(position: Long)
}