/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : BaseThumbnailLoader
 * * Description : manage thumbnailloader
 * * Version     : 1.0
 * * Date        : 2019/4/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >        <desc>
 * *  <EMAIL>      2019/4/22   1.0
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.imageloader;

import android.graphics.Bitmap;
import android.util.LruCache;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.standard_lib.scheduler.FutureListener;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

public abstract class BaseThumbnailLoader<Data> implements IThumbnailLoader<Data> {
    private static final String TAG = "BaseThumbnailLoader";

    public static final int MSG_UPDATE_THUMBNAIL = 1;
    public static final int MSG_CANCEL = 2;
    public static final int EXECUTOR_OREDER_SEQUENCE = 0;
    public static final int EXECUTOR_OREDER_REVER = 1;
    public static final int ONE_KB = 1024;

    private static final int TASK_LIMIT = 12;
    private static final int LIMIT_MEMORY_BLOCK = 6;
    private static final ConcurrentHashMap<String, ILoaderCallback> sLoaderCallbackCache = new ConcurrentHashMap<>();
    private static ThumbNailLruCache sThumbNailLruCache;
    private final LinkedList<BaseThumbnailTask> mJobs = new LinkedList<>();
    private final ConcurrentHashMap<ImageView, BaseThumbnailTask> mTaskMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Data, BaseThumbnailTask> mSourceMap = new ConcurrentHashMap<>();
    private final Object mLock = new Object();

    protected WorkerSession mWorkerSession = null;
    private boolean mPause = false;
    private int mLimit = TASK_LIMIT;
    private int mTaskLimit = TASK_LIMIT;
    private boolean mNoCache = false;
    private int mExecutorOrder = EXECUTOR_OREDER_SEQUENCE;
    private ImageHandler mHandler;

    public void release() {
        clearTaskMap(mTaskMap);
        clearTaskMap(mSourceMap);
        mJobs.clear();
        mHandler.removeCallbacksAndMessages(null);
    }

    private <T> void clearTaskMap(ConcurrentHashMap<T, BaseThumbnailTask> map) {
        if (!map.isEmpty()) {
            for (Map.Entry<T, BaseThumbnailTask> entry : map.entrySet()) {
                BaseThumbnailTask thumbnailTask = entry.getValue();
                thumbnailTask.cancelLoad();
            }
            map.clear();
        }
    }

    static {
        int maxMemorySize = (int) (Runtime.getRuntime().maxMemory() / ONE_KB / LIMIT_MEMORY_BLOCK);
        sThumbNailLruCache = new ThumbNailLruCache(maxMemorySize);
    }

    public void setExecutorOrder(int executorOrder) {
        mExecutorOrder = executorOrder;
    }

    public BaseThumbnailLoader() {
        mHandler = new ImageHandler(this);
    }

    @Override
    public final void startLoader(final Data item, final EditorMenuItemView target) {
        if ((null == item) || (null == target)) {
            GLog.e(TAG, "startLoader error");
            return;
        }
        target.setTag(item);
        start(item, new EditorMenuItemThumbnailListener(target));
    }

    @Override
    public synchronized void removeTask(Data item) {
        if (item == null) {
            return;
        }

        BaseThumbnailTask task = mSourceMap.remove(item);

        boolean isRemoveFormJobs = mJobs.remove(task);
        if (null != task) {
            task.cancelLoad();
        }

        GLog.d(TAG, "removeThumbNailLoadTask,isRemoveFormJobs: " + isRemoveFormJobs);
    }

    private synchronized void start(final Data item, @NonNull final ThumbnailListener thumbnailListener) {
        if (null != item.toString()) {
            GLog.d(TAG, "start item: " + item.toString());
        }

        ILoaderCallback<Data> loaderCallback = buildThumbnailCallback();

        final Bitmap bitmap = getBitmapFromMemoryCache(ThumbnailRequest.formRequestKey(loaderCallback.getDataUniqueTag(item)));
        if (!mNoCache && (null != bitmap)) {
            GLog.d(TAG, "start getBitmapFromMemoryCache,bitmap:  " + bitmap);
            mHandler.post(() -> thumbnailListener.updateThumbnail(new ThumbnailRespond<>(item, bitmap)));
        } else {
            mNoCache = false;
            ThumbnailRequest<Data> requestData = new ThumbnailRequest<>();
            requestData.setSource(item).setThumbnailListener(thumbnailListener)
                    .setTargetTag(loaderCallback.getDataUniqueTag(item));

            BaseThumbnailTask thumbnailTask = generateThumbnailTask(requestData, loaderCallback);

            if (!mJobs.contains(thumbnailTask)) {
                if (mExecutorOrder == EXECUTOR_OREDER_SEQUENCE) {
                    mJobs.addLast(thumbnailTask);
                } else {
                    mJobs.addFirst(thumbnailTask);
                }

                mSourceMap.put(item, thumbnailTask);

            } else {
                thumbnailTask.resetLoadedState();
            }

            if (thumbnailListener instanceof DefaultThumbnailListener) {
                ImageView imageView = ((DefaultThumbnailListener) thumbnailListener).getTarget();
                removeSameTargetTask(imageView);
                mTaskMap.put(imageView, thumbnailTask);
            }

            submitLoaderIfAllowed();
        }
    }

    private synchronized void removeSameTargetTask(ImageView imageView) {
        BaseThumbnailTask task = mTaskMap.get(imageView);
        boolean isRemove = mJobs.remove(task);
        GLog.d(TAG, "removeSameTargetTask,after mJob.size: " + mJobs.size() + ", isRemove :" + isRemove);

        if (null != task) {
            task.cancelLoad();
        }
    }

    public synchronized void setNoCache(boolean noCache) {
        mNoCache = noCache;
    }

    private BaseThumbnailTask generateThumbnailTask(ThumbnailRequest<Data> requestData,
                                                    ILoaderCallback<Data> loaderCallback) {

        return new BaseThumbnailTask<>(mHandler, mWorkerSession,
                requestData,
                loaderCallback);

    }

    protected abstract ILoaderCallback<Data> buildThumbnailCallback();

    @Override
    public int getWaitTask() {
        return mJobs.size();
    }

    @Override
    public void cancelAllWaitTask() {
        GLog.d(TAG, "cancelAllWaitTask, mJob.size: " + mJobs.size());

        while (!mJobs.isEmpty()) {
            BaseThumbnailTask bitmapTask = mJobs.removeFirst();
            if (null != bitmapTask) {
                bitmapTask.cancelLoad();
            }
        }

        mWorkerSession.removeTasks(BaseThumbnailTask.class);
        mHandler.removeMessages(BaseThumbnailLoader.MSG_UPDATE_THUMBNAIL);
        mLimit = mTaskLimit;
    }

    private void submitLoaderIfAllowed() {
        while (!mPause && (mLimit > 0) && !mJobs.isEmpty()) {
            BaseThumbnailTask bitmapTask = mJobs.removeFirst();
            if (bitmapTask != null) {
                --mLimit;
                sLoaderCallbackCache.put(bitmapTask.mRequestData.getRequestKey(), bitmapTask.mLoaderCallback);
                bitmapTask.startLoad();
            }
        }
    }

    public void afterLoadComplete() {
        ++mLimit;
        submitLoaderIfAllowed();
    }

    public synchronized static void putBitmapToMemoryCache(String key, Bitmap thumbnail) {
        if ((key != null) && (thumbnail != null) && !thumbnail.isRecycled()) {
            sThumbNailLruCache.put(key, thumbnail);
        }
    }

    private synchronized Bitmap getBitmapFromMemoryCache(String key) {
        return sThumbNailLruCache.get(key);
    }

    public void setQueneTaskLimit(int i) {
        GLog.d(TAG, "setQueneTaskLimit, i:" + i);
        if (i >= 0) {
            mTaskLimit = i;
        }
    }

    private static class ThumbNailLruCache extends LruCache<String, Bitmap> {

        /**
         * @param maxSize for caches that do not override {@link #sizeOf}, this is
         *                the maximum number of entries in the cache. For all other caches,
         *                this is the maximum sum of the sizes of the entries in this cache.
         */
        public ThumbNailLruCache(int maxSize) {
            super(maxSize);
        }

        @Override
        protected void entryRemoved(boolean evicted, String key, Bitmap oldValue, Bitmap newValue) {
            GLog.d(TAG, "entryRemoved, evicted: " + evicted + ", key :" + key + ","
                    + " oldValue: " + oldValue);

            if (evicted) {
                ILoaderCallback loaderCallback = sLoaderCallbackCache.get(key);

                if (null != loaderCallback) {
                    loaderCallback.recycleBitmap(oldValue);
                }

                sLoaderCallbackCache.remove(key);
            }
        }

        @Override
        protected int sizeOf(String key, Bitmap value) {
            return value.getByteCount() / ONE_KB;
        }
    }

    @Override
    public void destroy() {
        if (mWorkerSession != null) {
            mWorkerSession = null;
        }
    }

    public static class BaseThumbnailTask<Data> extends BitmapTask implements Job<Bitmap> {
        private final static String TAG = "BaseThumbnailTask";
        private ImageHandler mImageHandler;
        private ILoaderCallback<Data> mLoaderCallback;
        private ThumbnailRequest<Data> mRequestData;
        private WorkerSession mWorkerSession;

        public BaseThumbnailTask(ImageHandler imageHandler, WorkerSession session, ThumbnailRequest<Data> request,
                                 ILoaderCallback<Data> loaderCallback) {
            mImageHandler = imageHandler;
            mRequestData = request;
            mWorkerSession = session;
            mLoaderCallback = loaderCallback;
        }

        @Override
        public Future<Bitmap> submitTask(FutureListener<Bitmap> l) {
            GLog.d(TAG, "submitTask");

            return mWorkerSession.submit(this, l);
        }

        @Override
        public Bitmap call(JobContext jc) {
            return mLoaderCallback.onThumbNailLoad(mRequestData.getSource(), jc);
        }


        public ThumbnailRequest<Data> getRequestData() {
            return mRequestData;
        }

        @Override
        protected void onLoadComplete(Bitmap bitmap) {
            GLog.d(TAG, "onLoadComplete, bitmap :" + bitmap);
            BaseThumbnailLoader.putBitmapToMemoryCache(this.getRequestData().getRequestKey(), this.getBitmap());

            mImageHandler.obtainMessage(BaseThumbnailLoader.MSG_UPDATE_THUMBNAIL, this).sendToTarget();
        }

        @Override
        protected void onCancel() {
            GLog.d(TAG, "onCancel");

            mImageHandler.obtainMessage(BaseThumbnailLoader.MSG_CANCEL, null).sendToTarget();

        }
    }

    public interface ILoaderCallback<Data> {

        @WorkerThread
        Bitmap onThumbNailLoad(Data item, JobContext jc);

        void recycleBitmap(Bitmap bitmap);

        String getDataUniqueTag(Data item);
    }
}
