/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorTimeSetState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/29    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.time;

import android.content.Context;
import android.view.View;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesActivity;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesManager;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.videoeditorpage.memories.data.MemoriesInfo;

import java.util.ArrayList;
import java.util.List;

import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.SECONDS_1000;

public class EditorTimeSetState extends EditorBaseState implements EditorBaseUIController.OnIconClickListener {
    private static final String TAG = "EditorTimeSetState";

    private static final String TYPE_NAME = "TimeSet";
    private long mMaxTotalTime = 0;
    private long mBaseTotalTime = 0;
    private long mCurrentTotalTime = 0;
    private MemoriesInfo mCurMemInfo = null;
    private boolean mDurationChanged = false;

    public EditorTimeSetState(Context context, ControlBarView controlBarView) {
        super(TYPE_NAME, context, controlBarView);
        // cut millisecond
        mMaxTotalTime = mEngineManager.getThemeMaxTotalTime() / SECONDS_1000 * SECONDS_1000;
        mBaseTotalTime = mEngineManager.getTotalTime() / SECONDS_1000 * SECONDS_1000;
        mCurrentTotalTime = mBaseTotalTime;
        mCurMemInfo = MemoriesManager.getCurMemoriesInfo().obtain();
        GLog.d(TAG, "EditorTimeSetState videoCount = " + mEngineManager.getThemeVideoClipCount()
                + ", mMaxTotalTime = " + mMaxTotalTime
                + ", mBaseTotalTime = " + mBaseTotalTime);
    }

    @Override
    protected EditorBaseUIController createUIController() {
        EditorTimeSetUIController timeSetUIController = new EditorTimeSetUIController(mContext, mControlBarView, this);
        timeSetUIController.setOnIconClickListener(this);
        return timeSetUIController;
    }

    @Override
    public boolean done() {
        GLog.d(TAG, "done mCurrentTotalTime = " + mCurrentTotalTime + ", mBaseTotalTime = " + mBaseTotalTime);
        MemoriesManager.setCurMemoriesInfo(mCurMemInfo);
        List<MediaInfo> curList = MemoriesManager.getCurrentVideoFileList();
        GLog.d(TAG, "done mCurMemInfo = " + mCurMemInfo
                + ", curList.size = " + curList.size()
                + ", curList = " + curList);
        mDurationChanged = false;
        return super.done();
    }

    @Override
    public void cancel() {
        super.cancel();
        GLog.d(TAG, "cancel mCurrentTotalTime = " + mCurrentTotalTime
                + ", mBaseTotalTime = " + mBaseTotalTime
                + ", mDurationChanged = " + mDurationChanged);
        if (mDurationChanged && (mContext instanceof MemoriesActivity)) {
            ((MemoriesActivity) mContext).initEngine(false);
            mDurationChanged = false;
        }
    }

    @Override
    public boolean onBackPressed() {
        GLog.d(TAG, "onBackPressed mCurrentTotalTime = " + mCurrentTotalTime + ", mBaseTotalTime = " + mBaseTotalTime);
        cancel();
        return super.onBackPressed();
    }

    @Override
    public void click(View view) {
        if ((view != null) && (view.getTag() != null)) {
            long nextTime = (long) view.getTag();
            GLog.d(TAG, "click nextTime = " + nextTime + ", mCurrentTotalTime = " + mCurrentTotalTime
                    + ", mBaseTotalTime = " + mBaseTotalTime);
            if ((nextTime != mBaseTotalTime) || (nextTime != mCurrentTotalTime)) {
                mDurationChanged = true;
                mEngineManager.changeThemeDuration(nextTime);
                ArrayList<String> curVideoList = mEngineManager.getThemeVideoClipList();
                if ((curVideoList == null) || curVideoList.isEmpty()) {
                    GLog.d(TAG, "click curVideoList is null or empty. nextTime = " + nextTime);
                    return;
                }
                // get mediaInfo list match current video list
                ArrayList<MediaInfo> curMediaList = new ArrayList<>();
                if ((mCurMemInfo != null) && !mCurMemInfo.mMediaInfos.isEmpty()) {
                    List<MediaInfo> allMediaList = mCurMemInfo.mMediaInfos;
                    GLog.d(TAG, "click curList.size = " + curVideoList.size() + ", allList.size = " + allMediaList.size());
                    for (MediaInfo info : allMediaList) {
                        if (curVideoList.contains(info.mUri)) {
                            info.mInVideo = true;
                            curMediaList.add(info);
                        } else {
                            info.mInVideo = false;
                            info.mIsCover = false;
                        }
                    }

                }
                // reselect another cover or not
                MediaInfo cover = mEngineManager.getThemeVideoCover();
                GLog.d(TAG, "click oldCover = " + cover + ", curVideoList = " + curVideoList);
                if ((cover != null) && !curVideoList.contains(cover.mUri)) {
                    // get next cover
                    if (!curMediaList.isEmpty()) {
                        MediaInfo newCover = MemoriesInfo.getNextCover(curMediaList);
                        if (newCover != null) {
                            GLog.d(TAG, "click newCover = " + newCover);
                            mEngineManager.addThemeVideoCover(newCover);
                            if (mCurMemInfo != null) {
                                mCurMemInfo.mCover = newCover;
                            }
                        }
                    }
                }
                mEngineManager.reset();
            }
            mCurrentTotalTime = nextTime;
        }
    }

    @Override
    public void onIconClick(View view, int position, Object item) {
        GLog.d(TAG, "onIconClick pos = " + position + ", item = " + item);
    }

    /**
     * 进出 回忆-编辑-时长 需要动画，返回true
     */
    @Override
    public boolean isNeedAnimator() {
        return true;
    }
}
