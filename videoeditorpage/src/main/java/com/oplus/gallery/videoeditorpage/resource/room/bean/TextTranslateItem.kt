/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextTranslateItem.kt
 ** Description : 文本翻译数据库实体类
 ** Version     : 1.0
 ** Date        : 2025/6/17 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/17  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.bean

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.oplus.gallery.foundation.util.text.TextUtil

/**
 * 文本翻译数据库实体类
 */
@Entity(tableName = "text_translate", primaryKeys = ["text_id", "language_code"])
class TextTranslateItem : Item() {

    /**
     * 词条id
     */
    @ColumnInfo(name = "text_id")
    var id: String = TextUtil.EMPTY_STRING

    /**
     * 语言编码
     */
    @ColumnInfo(name = "language_code")
    var languageCode: String = TextUtil.EMPTY_STRING

    /**
     * 词条翻译内容
     */
    @ColumnInfo(name = "language_text")
    var languageText: String = TextUtil.EMPTY_STRING

    override fun toString(): String {
        return ("CaptionFontNameItem{" +
                "text_id='" + id + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", languageText='" + languageText + '\''
                + '}'
                )
    }

    override fun getItemUniqueId(): String = id

    override fun getItemDownloadState(): Int = TYPE_ALL_DOWNLOADED

    override fun getItemVersion(): String = TextUtil.EMPTY_STRING
}
