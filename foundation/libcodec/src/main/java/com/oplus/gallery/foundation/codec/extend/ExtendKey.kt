/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ExtendKey.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/31
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * hucanh<PERSON>@Apps.Gallery		2024/10/31		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.codec.extend

import com.oplus.gallery.foundation.codec.extend.portraitblur.CropRegionStruct
import com.oplus.gallery.foundation.codec.extend.portraitblur.FaceCoordStruct
import com.oplus.gallery.foundation.codec.extend.portraitblur.FrontDepthStruct
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct

/**
 * 原始的灰度图，跟[HDR_LINEAR_MASK]的区别是：
 * 前者是不加任何效果，后者是可能添加了虚化等效果
 */
const val EXTEND_KEY_ORIGIN_HDR_LINEAR_MASK = "src.local.hdr.linear.mask"

/**
 * 原图，不带任何效果，jpg
 *
 * zhufeng机器拍摄的景深图会在此key对应的byteArray携带gainmap(oppo三通道uhdr)
 */
const val EXTEND_KEY_SRC_IMAGE = "src.image"

const val EXTEND_KEY_SRC_IMAGE_BLOCK = "src.image.block"

/**
 * 相机水印图（图上水印），jpg
 */
const val EXTEND_KEY_WATERMARK = "watermark"

/**
 * 相机水印信息
 */
const val EXTEND_KEY_WATERMARK_CONFIG = "watermark.config"

/**
 * 相机后置深度图
 */
const val EXTEND_KEY_REAR_DEPTH = "rear.depth"

/**
 * 相机后置深度信息
 */
const val EXTEND_KEY_REAR_DEPTH_CONFIG = "rear.depth.config"

/**
 * 相机后置光斑图
 */
const val EXTEND_KEY_REAR_SPOTLIGHT = "rear.spotlight"

/**
 * 相机前置深度图
 */
const val EXTEND_KEY_FRONT_DEPTH = "front.depth"

/**
 * 相机前置深度信息
 */
const val EXTEND_KEY_FRONT_DEPTH_CONFIG = "front.depth.config"

/**
 * 相机前置分割图
 */
const val EXTEND_KEY_FRONT_SEGMENT = "front.segment"

/**
 * 相机前置发型图
 */
const val EXTEND_KEY_FRONT_HAIR_MASK = "front.hair.mask"

/**
 * 相机前置matte信息（matter是相机写错了）
 */
const val EXTEND_KEY_FRONT_MATTER_INFO = "front.matter.info"

/**
 * 相机前置负像图
 */
const val EXTEND_KEY_FRONT_NEGEVIMG = "front.negevimg"

/**
 * 相机滤镜
 */
const val EXTEND_KEY_FILTER = "filter"

/**
 * 滤镜信息
 */
const val EXTEND_KEY_FILTER_INFO = "filter.info"

const val EXTEND_KEY_DEPTH_RULE_CONFIG = "depth.rule.config"

/**
 * 角点配置，主要是人脸数，角点总数为人脸数x单位角点数
 */
const val EXTEND_KEY_MESH_COORD_CONFIG = "mesh.coord.config"

/**
 * 角点数组
 */
const val EXTEND_KEY_MESH_COORD = "mesh.coord"

/**
 * 裁剪区域
 */
const val EXTEND_KEY_CROP_REGION = "crop.region"

/**
 * 图片画幅尺寸
 */
const val EXTEND_KEY_IMAGE_FRAME_INFO = "final.out.image.info"

/**
 * 图片是否做过画质增强
 */
const val EXTEND_KEY_IMAGE_QUALITY_ENHANCED = "image.quality.enhanced"

/**
 * Local HDR 图片中存放灰度图的扩展数据名称。其对应的内容为原图1/4大小的灰图mask信息。
 * 相册需要转成Bitmap，未来会对灰图做处理，并将该Bitmap给到显示模块。
 */
const val EXTEND_KEY_LOCAL_HDR_LINEAR_MASK = "local.hdr.linear.mask"

/**
 * Local HDR 图片中存放的meta信息，内容对相册透明，需要透传给显示模块。
 */
const val EXTEND_KEY_LOCAL_HDR_META_DATA = "local.hdr.meta.data"

/**
 * uhdr的增益图获取key，当前仅heif图需要使用
 */
const val EXTEND_KEY_UHDR_GAINMAP_IMAGE = "local.uhdr.gainmap.data"

/**
 * uhdr的增益信息获取key，当前仅heif图需要使用
 */
const val EXTEND_KEY_UHDR_GAINMAP_INFO = "local.uhdr.gainmap.info"

/**
 * 图片编辑输入下变换的参数
 *
 * 对应数据结构：[HdrTransformDataStruct]
 */
const val EXTEND_KEY_HDR_TRANSFORM_DATA = "hdr.transform.data"

/**
 * UHDR图片中存放的灰度图和metadata信息。
 * 非扩展格式的标准字段，是个自定义的兼容字段。
 * - UHDR图片中存放灰度图的扩展数据名称。其对应的内容为原图1/4大小的灰图mask信息。
 * 相册需要转成Bitmap，未来会对灰图做处理，并将该Bitmap给到显示模块。
 * - UHDR图片中存放的meta信息，内容对相册透明，需要透传给显示模块。
 *
 * 数据类型：Pair<Bitmap, UltraHdrInfo>
 */
const val EXTEND_KEY_ULTRA_HDR_INFO: String = "ultra.hdr.info"

/**
 * 视频输入的下变换gamma
 */
const val EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA = "video.hdr.transform.data"

/**
 * 【超光影-青品冷暖&暗角】 色调固定信息
 * 对应数据结构：[BasicToneInfoStruct]
 */
const val EXTEND_KEY_FILTER_BASIC_TONE_INFO = "basictone.info"

/**
 * 【超光影-青品冷暖&暗角】 色调 暗角动态信息，一个13*17的单通道的lut表
 * 字节数组， 需要转成float数组放在[BasicToneSource]，后续传给滤镜算法插件
 */
const val EXTEND_KEY_FILTER_BASIC_TONE_VIG_TABLE = "basictone.vig.table"

/**
 * 【超光影-青品冷暖&暗角】 色调 青品冷暖动态信息，青品和冷暖的3d并表
 * 字节数组，放在[BasicToneSource]，后续传给滤镜算法插件
 */
const val EXTEND_KEY_FILTER_BASIC_TONE_LMTLUT_TABLE = "basictone.lmtlut.table"

/**
 * 相册产物的保存信息，
 *
 * 对应数据结构：[GalleryArtifactInfo]
 */
const val EXTEND_KEY_GALLERY_ARTIFACT_SAVE_INFO = "gallery.artifact.info"

/**
 * 实况照片子视频
 */
const val EXTEND_KEY_OLIVE_SUB_VIDEO = "live.subVideo"

/**
 * 实况照片子视频大小
 */
const val EXTEND_KEY_OLIVE_SUB_VIDEO_SIZE = "live.subVideoSize"

/**
 * 图片显示的水印参数
 */
const val EXTENSION_KEY_WATERMARK_PARAMS = "watermark.params"

/**
 * 图片水印的设备信息
 */
const val EXTENSION_KEY_WATERMARK_DEVICE = "watermark.device"

/**
 * 水印的自定义信息
 */
const val EXTENSION_KEY_WATERMARK_CUSTOM_INFO = "watermark.custominfo"

/**
 * 水印上的滤镜信息
 */
const val EXTENSION_KEY_WATERMARK_FILTER = "watermark.filter"

/**
 * 色彩水印导演滤镜信息
 */
const val EXTENSION_KEY_WATERMARK_DIRECTOR = "watermark.director"

/**
 * 色彩水印色彩提取信息
 */
const val EXTENSION_KEY_WATERMARK_COLOR = "watermark.color"

/**
 * 普通水印的截图
 */
const val EXTENSION_KEY_WATERMARK_CAPTURE = "watermark.capture"

/**
 * 新春水印信息
 */
const val EXTENSION_KEY_WATERMARK_SPRING_FESTIVAL_PARAMS = "watermark.spring.festival.params"

/**
 * 图片色域，返回值为[ColorSpace]
 */
const val COLOR_SPACE: String = "color.space"

/**
 * AI 超清长焦倍数
 */
const val EXTENSION_KEY_AIHD_TELEPHOTO = "aihd.telephoto"

/**
 * AI 超清下采样倍数
 */
const val EXTENSION_KEY_AIHD_DOWN_SCALE = "aihd.downscale"

/**
 * Ai水印大师文件扩展信息
 */
const val EXTENSION_KEY_WATERMARK_MASTER_PARAMS = "watermark.master.params"

/**
 * AI 风光信息扩展数据
 */
const val EXTENSION_KEY_SCENERY_INFO = "scenery.info"

/**
 * 矫正前原图，AI 风光专用
 */
const val EXTENSION_KEY_SCENERY_IMAGE = "scenery.image"

/**
 * 解析文件的fileExtend 数据时，文件协议中 键值和协议结构的配置表
 * 每一个键对应的struct 都根据相册 扩展文件格式表中标准的协议结构而来，一一对应
 * 如果配置了，找编辑能力可以使用 getMetadataStruct 拿到对应的 struct 可以toData 成对象使用
 * 如果没有配置，找编辑能力只能使用 getMetadata 拿到的就是元信息数据流 byteArray
 */
val EXTEND_KEY_OBJ: Map<String, Class<out ExtendStruct<out Any>>> = mapOf(
    EXTEND_KEY_FRONT_DEPTH_CONFIG to FrontDepthStruct::class.java,
    EXTEND_KEY_REAR_DEPTH_CONFIG to RearDepthStruct::class.java,
    EXTEND_KEY_CROP_REGION to CropRegionStruct::class.java,
    EXTEND_KEY_MESH_COORD_CONFIG to FaceCoordStruct::class.java,
    EXTEND_KEY_SRC_IMAGE to BitmapStruct::class.java,
    EXTEND_KEY_REAR_DEPTH to BitmapStruct::class.java,
    EXTEND_KEY_REAR_SPOTLIGHT to BitmapStruct::class.java,
    EXTEND_KEY_FRONT_DEPTH to BitmapStruct::class.java,
    EXTEND_KEY_FRONT_SEGMENT to BitmapStruct::class.java,
    EXTEND_KEY_FRONT_HAIR_MASK to BitmapStruct::class.java,
    EXTEND_KEY_FRONT_MATTER_INFO to BitmapStruct::class.java,
    EXTEND_KEY_FRONT_NEGEVIMG to BitmapStruct::class.java,
    EXTEND_KEY_ORIGIN_HDR_LINEAR_MASK to BitmapStruct::class.java,
    EXTEND_KEY_LOCAL_HDR_META_DATA to LocalHdrInfoStruct::class.java
)