/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseVideoTrackEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.track;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;

public class BaseVideoTrackEffect extends BaseVideoFx {
    /**
     * 这个特效作用的轨道，需要判空
     */
    protected transient IVideoTrack mTargetTrack;
    /**
     * 应用轨道Index
     */
    private int mVideoTrackIndex = 0;
    /**
     * 应用目标类型
     */
    private int mVideoTrackTargetType = StreamingConstant.FxTarget.FX_TARGET_TYPE_MAIN;
    /**
     * 应用作用于track上clip的index
     */
    private int mVideoClipTrackIndex = 0;

    public BaseVideoTrackEffect(String name, int type) {
        super(name, type);
    }


    public int getVideoTrackIndex() {
        return mVideoTrackIndex;
    }

    public void setVideoTrackIndex(int mVideoTrackIndex) {
        this.mVideoTrackIndex = mVideoTrackIndex;
    }

    public int getVideoClipTrackIndex() {
        return mVideoClipTrackIndex;
    }

    public void setVideoClipTrackIndex(int mVideoClipTrackIndex) {
        this.mVideoClipTrackIndex = mVideoClipTrackIndex;
    }

    public String getInstalledName() {
        return mName;
    }

    public int getVideoTrackTargetType() {
        return mVideoTrackTargetType;
    }

    public void setVideoTrackTargetType(int mVideoTrackTargetType) {
        this.mVideoTrackTargetType = mVideoTrackTargetType;
    }

    public void setTargetVideoTrack(IVideoTrack videoTrack) {
        mTargetTrack = videoTrack;
    }
}
