/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * File: ColorPickerAdapter.kt
 * Description: tangzhibin created
 * Version: 1.0
 * Date: 2025/4/30
 * Author: tangzhibin
 *
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * tangzhibin      2025/4/30        1.0         NEW
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.colorpicker

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.R

/**
 * 颜色选择器适配器
 */
class ColorPickerAdapter(
    private val context: Context,
    private var items: MutableList<ColorPickerItemData> = mutableListOf()
) : RecyclerView.Adapter<ColorPickerAdapter.ViewHolder>() {

    // 当前选中的位置
    private var selectedPosition = SELECTED_POSITION_NONE

    // 点击监听器
    var onItemClickListener: ((position: Int, item: ColorPickerItemData) -> Unit)? = null

    /**
     * 设置数据
     */
    fun setData(newItems: List<ColorPickerItemData>, initialSelection: Int = 0) {
        items.clear()
        items.addAll(newItems)

        selectedPosition = if (isValidPosition(initialSelection)) initialSelection else SELECTED_POSITION_NONE

        notifyDataSetChanged()
    }

    /**
     * 获取当前选中的项
     */
    fun getSelectedItem(): ColorPickerItemData? =
        if (isValidPosition(selectedPosition)) items[selectedPosition] else null

    /**
     * 设置选中项
     */
    fun setSelectedPosition(position: Int) {
        if (isValidPosition(position) && (position != selectedPosition)) {
            val oldPosition = selectedPosition
            selectedPosition = position

            if (isValidPosition(oldPosition)) notifyItemChanged(oldPosition)
            notifyItemChanged(selectedPosition)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.videoeditor_color_picker_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]

        // 设置数据和选中状态
        holder.colorPickerItemView.apply {
            itemData = item
            isSelected = position == selectedPosition
        }

        holder.itemView.setOnClickListener {
            setSelectedPosition(position)
            onItemClickListener?.invoke(position, item)
        }
    }

    override fun getItemCount(): Int = items.size

    private fun isValidPosition(position: Int) = position in 0 until items.size

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val colorPickerItemView: ColorPickerItemView = itemView.findViewById(R.id.color_picker_item_view)
    }

    companion object {
        private const val SELECTED_POSITION_NONE = -1
    }
}
