/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IHdrMetadataPack.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2023/10/16
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery       2023/10/16        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.standard_lib.codec.drawable

import android.os.Parcel
import android.os.Parcelable
import com.oplus.gallery.addon.graphics.EMPTY_ULTRA_HDR_INFO
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag

interface IHdrMetadataPack : Parcelable {
    val metadata: Any

    fun copy(): IHdrMetadataPack

    fun size(): Int

    fun getHdrType(): HdrImageType
}

class LHdrMetadataPack(override val metadata: ByteArray) : IHdrMetadataPack {
    constructor(parcel: Parcel) : this(parcel.createByteArray() ?: byteArrayOf())

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByteArray(metadata)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun copy(): IHdrMetadataPack = LHdrMetadataPack(metadata.copyOf())
    override fun size(): Int {
        return metadata.size
    }

    override fun getHdrType(): HdrImageType {
        return HdrImageType.LHDR
    }

    override fun toString(): String {
        return "LHdrMetadataPack(metadata=$metadata)"
    }

    companion object CREATOR : Parcelable.Creator<LHdrMetadataPack> {
        override fun createFromParcel(parcel: Parcel): LHdrMetadataPack {
            return LHdrMetadataPack(parcel)
        }

        override fun newArray(size: Int): Array<LHdrMetadataPack?> {
            return arrayOfNulls(size)
        }
    }
}

class UHdrMetadataPack(override val metadata: UltraHdrInfo) : IHdrMetadataPack {

    constructor(parcel: Parcel) : this(parcel.readParcelable(UltraHdrInfo::class.java.classLoader) ?: EMPTY_ULTRA_HDR_INFO)

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeParcelable(metadata, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun getHdrType(): HdrImageType {
        return HdrImageType.UHDR
    }

    override fun copy(): IHdrMetadataPack {
        return UHdrMetadataPack(metadata.run {
            copy(
                ratioMin = ratioMin.copyOf(),
                ratioMax = ratioMax.copyOf(),
                gamma = gamma.copyOf(),
                epsilonSdr = epsilonSdr.copyOf(),
                epsilonHdr = epsilonHdr.copyOf()
            )
        })
    }

    override fun size(): Int {
        return metadata.size()
    }

    override fun toString(): String {
        return "UHdrMetadataPack(metadata=${metadata.toDetailString()})"
    }

    companion object CREATOR : Parcelable.Creator<UHdrMetadataPack> {
        override fun createFromParcel(parcel: Parcel): UHdrMetadataPack {
            return UHdrMetadataPack(parcel)
        }

        override fun newArray(size: Int): Array<UHdrMetadataPack?> {
            return arrayOfNulls(size)
        }
    }
}

/**
 * 保存图片时，需要重新insert tag，需要根据图片的情况使用tagFlag。
 * 当前只支持保存为jpg，所以uhdr只需要有EXIF_TAG_ULTRA_HDR就可以了。
 */
enum class HdrImageType(val tagFlag: Long) {
    LHDR(OplusExifTag.EXIF_TAG_LOCAL_HDR),
    UHDR(OplusExifTag.EXIF_TAG_ULTRA_HDR)
}