/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BottomActionBar
 ** Description: 面板底部标题和按钮栏
 ** Version: 1.0
 ** Date : 2025/04/10
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/04/10    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.CompoundButton
import android.widget.RelativeLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R

/**
 * 面板底部标题和按钮栏
 */
class BottomActionBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr, defStyleRes), View.OnClickListener,
    CompoundButton.OnCheckedChangeListener {

    /**
     * 按钮是否可点击，默认可点击
     */
    var buttonsClickable: Boolean = true

    /**
     * 标题显示的文本
     */
    var title: String? = null
        set(value) {
            if ((value == null) || (value == field)) return
            titleView.text = value
            field = value
        }

    /**
     * 点击事件监听器
     */
    var itemClickListener: OnActionItemClickListener? = null

    /**
     * 选中状态发生变化的监听
     */
    var checkedChangeListener: OnCheckedChangeListener? = null

    /**
     * 应用至全部的监听
     */
    var applyAllListener: OnApplyAllListener? = null

    /**
     * 取消按钮
     */
    private val cancelButton: COUITextView

    /**
     * 完成按钮
     */
    private val doneButton: COUITextView

    /**
     * 标题
     */
    val titleView: COUITextView

    /**
     * 带选择框的标题组件
     */
    private val titleCheckBox: COUIChip

    init {
        val rootView = LayoutInflater.from(context).inflate(R.layout.videoeditor_base_bottom_action_bar_layout, this, true)
        cancelButton = rootView.findViewById(R.id.editor_id_action_cancel)
        cancelButton.setOnClickListener(this)
        doneButton = rootView.findViewById(R.id.editor_id_action_done)
        doneButton.setOnClickListener(this)
        titleView = rootView.findViewById(R.id.editor_id_title)
        titleCheckBox = rootView.findViewById(R.id.editor_id_title_checkbox)
        titleCheckBox.setOnCheckedChangeListener(this)
        titleCheckBox.setOnClickListener(this)
        titleCheckBox.isChecked = true

        val array = context.obtainStyledAttributes(attrs, R.styleable.BottomActionBar, defStyleAttr, defStyleRes)
        try {
            val cancelText = array.getString(R.styleable.BottomActionBar_actionCancelText)
            if (cancelText?.isNotEmpty() == true) cancelButton.text = cancelText

            val doneText = array.getString(R.styleable.BottomActionBar_actionDoneText)
            if (doneText?.isNotEmpty() == true) doneButton.text = doneText

            val titleText = array.getString(R.styleable.BottomActionBar_titleText)
            if (titleText?.isNotEmpty() == true) titleView.text = titleText
            array.getColorStateList(R.styleable.BottomActionBar_titleTintColor)?.also { titleTintColor ->
                titleView.setTextColor(titleTintColor)
                titleView.backgroundTintList = titleTintColor
            }
        } finally {
            array.recycle()
        }
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        // 两按钮宽度需保持一致
        if (cancelButton.width < doneButton.width) {
            cancelButton.width = doneButton.width
        } else if (cancelButton.width > doneButton.width) {
            doneButton.width = cancelButton.width
        }
    }

    /**
     * 切换 标题 跟"应用至全部"的显示隐藏逻辑
     * @param showApplyAll true: 显示"应用至全部"，false: 显示标题
     */
    fun switchTitleView(showApplyAll: Boolean) {
        titleView.visibility = if (showApplyAll) {
            View.GONE
        } else {
            View.VISIBLE
        }
        titleCheckBox.visibility = if (showApplyAll) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    override fun onClick(v: View) {
        GLog.d(TAG, LogFlag.DL) { "[onClick] itemClickListener: $itemClickListener" }
        when (v.id) {
            R.id.editor_id_title_checkbox -> {
                ToastUtil.showShortToast(R.string.videoeditor_bottom_title_chip_toast)
                applyAllListener?.applyAll(v)
            }
            else -> itemClickListener?.onActionItemClick(v)
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        return buttonsClickable.not() || super.onInterceptTouchEvent(ev)
    }

    override fun onCheckedChanged(buttonView: CompoundButton, isChecked: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[onCheckedChanged] isChecked: $isChecked" }
        checkedChangeListener?.onCheckedChanged(buttonView, isChecked)
    }

    internal companion object {
        private const val TAG = "BottomActionBar"
    }

    /**
     * 点击事件监听器
     */
    interface OnActionItemClickListener {

        /**
         * 取消和完成按钮点击事件回调
         */
        fun onActionItemClick(view: View)
    }

    /**
     * 中间纸片组件，选中状态发生变化的监听
     */
    interface OnCheckedChangeListener {

        /**
         * 选中状态发生变化时回调
         *
         * @param buttonView 发生变化的按钮
         * @param isChecked 是否选中状态
         */
        fun onCheckedChanged(buttonView: CompoundButton, isChecked: Boolean)
    }

    /**
     * 应用至全部按钮回调
     */
    interface OnApplyAllListener {

        /**
         * 应用至全部按钮回调
         *
         * @param buttonView 应用至全部按钮
         */
        fun applyAll(buttonView: View)
    }
}