/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - ThumbnailRequest.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/5/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  <EMAIL>  2019/5/6  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.videoeditorpage.memories.imageloader;

import android.text.TextUtils;

import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.UUID;

public class ThumbnailRequest<Data> {
    public static final String TAG = "ThumbnailRequest";
    private Data mItem;
    private ThumbnailListener mThumbnailListener;
    private String mTargetTag;

    public ThumbnailRequest setSource(Data item) {
        mItem = item;
        return this;
    }

    public Data getSource() {
        return mItem;
    }

    public ThumbnailRequest setThumbnailListener(ThumbnailListener thumbnailListener) {
        mThumbnailListener = thumbnailListener;
        return this;
    }

    public ThumbnailListener getThumbnailListener() {
        return mThumbnailListener;
    }

    public String getRequestKey() {
        GLog.d(TAG, "getRequestKey, mTargetTag:" + mTargetTag);

        return formRequestKey(mTargetTag);
    }

    public static String formRequestKey(String targetTag) {
        StringBuilder builder = new StringBuilder();
        builder.append(targetTag);

        return builder.toString();
    }

    public ThumbnailRequest setTargetTag(String targetTag) {
        if (TextUtils.isEmpty(targetTag)) {
            mTargetTag = UUID.randomUUID().toString();
        } else {
            mTargetTag = targetTag;
        }

        return this;
    }
}
