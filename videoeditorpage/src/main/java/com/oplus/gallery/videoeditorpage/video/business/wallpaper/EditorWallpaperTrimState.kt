/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - EditorWallpaperTrimState.kt
 ** Description: 壁纸业务state
 ** Version: 1.0
 ** Date : 2025/3/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2025/3/18    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.app.Activity.RESULT_CANCELED
import android.content.Context
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 壁纸业务state
 */
class EditorWallpaperTrimState(
    val context: Context,
    editorControlView: EditorControlView,
    private val videoTrimMinDuration: Long,
    private val videoTrimMaxDuration: Long
) : EditorBaseState<EditorWallpaperTrimUIController>(TAG, context, editorControlView) {

    override fun createUIController(): EditorWallpaperTrimUIController {
        return EditorWallpaperTrimUIController(context, editorEngine, this).apply {
            setTrimMinMaxTime(videoTrimMinDuration, videoTrimMaxDuration)
        }
    }

    override fun showOperaIcon() = true

    override fun onBackPressed(): Boolean {
        (context as BaseActivity).run {
            setResult(RESULT_CANCELED)
            finish()
        }
        return false
    }

    override fun isSkipAnim(): Boolean {
        return true
    }

    companion object {
        const val TAG = "EditorWallpaperTrimState"
    }
}