<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <path
      android:pathData="M92.5,88.38C91.88,84.22 92.54,79.96 94.38,76.17C96.21,72.41 100.23,68.72 103.87,66.67C104.56,67.67 102.09,72.47 100.87,72.79C101.43,73.08 101.86,73.58 102.07,74.17C102.28,74.77 102.26,75.42 102.01,76C101.44,77.15 100.57,78.12 99.49,78.82C99,79.28 98.39,79.6 97.73,79.73C97.41,79.78 97.08,79.71 96.81,79.52C96.54,79.34 96.35,79.05 96.29,78.73C96.3,78.58 96.34,78.43 96.42,78.29C96.49,78.15 96.59,78.03 96.72,77.94C96.84,77.84 96.99,77.78 97.14,77.74C97.29,77.7 97.45,77.7 97.6,77.73C97.91,77.81 98.19,77.98 98.41,78.22C98.62,78.46 98.76,78.76 98.8,79.08C98.87,79.71 98.75,80.34 98.45,80.9C98.07,81.66 97.53,82.33 96.87,82.87C96.2,83.4 95.43,83.79 94.61,84C94.9,84.86 94.85,85.81 94.45,86.62C94.06,87.44 93.36,88.08 92.5,88.38Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.37,83.47C90.84,79.11 89.83,74.83 88.37,70.69C86.91,66.53 84.14,62.95 80.47,60.5C80.47,64.07 81.43,67.58 83.23,70.67C83.45,71.06 83.74,71.4 84.09,71.67C84.27,71.81 84.49,71.9 84.72,71.92C84.94,71.94 85.17,71.9 85.38,71.8C85.48,71.74 85.56,71.67 85.62,71.57C85.69,71.48 85.73,71.38 85.75,71.27C85.77,71.16 85.76,71.04 85.73,70.93C85.7,70.83 85.65,70.73 85.58,70.64C85.48,70.57 85.37,70.53 85.25,70.51C85.13,70.49 85.01,70.5 84.89,70.53C84.78,70.56 84.67,70.62 84.58,70.69C84.49,70.77 84.41,70.86 84.36,70.97C84.18,71.43 84.18,71.94 84.36,72.4C84.85,74.25 86.01,75.85 87.63,76.87C87.3,76.94 87,77.11 86.78,77.36C86.56,77.61 86.43,77.93 86.41,78.26C86.38,78.93 86.58,79.59 86.97,80.14C87.91,81.72 89.8,82.51 91.37,83.47Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M92.33,77.46C92.62,70.16 95.09,63.12 99.42,57.23"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.8,72.41C91.5,69.54 90.69,66.74 89.42,64.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.37,65.53C90.4,65.53 91.24,64.69 91.24,63.66C91.24,62.63 90.4,61.79 89.37,61.79C88.34,61.79 87.5,62.63 87.5,63.66C87.5,64.69 88.34,65.53 89.37,65.53Z"
      android:fillColor="#8F96BB"/>
  <path
      android:pathData="M98.73,61C99.91,61 100.87,60.04 100.87,58.86C100.87,57.68 99.91,56.72 98.73,56.72C97.55,56.72 96.59,57.68 96.59,58.86C96.59,60.04 97.55,61 98.73,61Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M97.34,67.45C98.1,67.45 98.71,66.84 98.71,66.08C98.71,65.32 98.1,64.71 97.34,64.71C96.58,64.71 95.97,65.32 95.97,66.08C95.97,66.84 96.58,67.45 97.34,67.45Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M95.58,125.28H91.66V173.23H95.58V125.28Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.31,114.15V125.15C112.31,126.53 103.94,127.65 93.63,127.65C83.32,127.65 74.94,126.53 74.94,125.15V114.15C74.94,112.77 83.31,111.65 93.63,111.65C103.95,111.65 112.31,112.77 112.31,114.15Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.24,112V114.7C117.24,116.44 106.66,117.85 93.62,117.85C80.58,117.85 70,116.4 70,114.66V112H81.37C84.94,111.72 89.13,111.55 93.62,111.55C98.11,111.55 102.3,111.72 105.87,112H117.24Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M93.62,115.11C106.67,115.11 117.24,113.7 117.24,111.96C117.24,110.22 106.67,108.81 93.62,108.81C80.57,108.81 70,110.22 70,111.96C70,113.7 80.57,115.11 93.62,115.11Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.43,170.68C86.78,172.75 82.02,174.57 77.18,176.14C76.04,176.47 76.43,177.21 77.82,177.31C88.47,178.1 99.17,178.1 109.82,177.31C111.22,177.21 111.6,176.46 110.42,176.13C105.54,174.75 100.52,172.65 95.5,170.66C94.15,170.66 92.8,170.67 91.43,170.68Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.31,122.85C112.31,124.23 103.94,125.35 93.63,125.35C83.32,125.35 74.94,124.23 74.94,122.85"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.48,111.69C93.64,112.15 90.73,112.15 87.89,111.69C87.6,111.64 87.34,111.48 87.17,111.24C86.99,111 86.92,110.71 86.96,110.42C87.74,104.3 88.54,98.04 89.34,91.71C88.5,88.9 87.66,90.07 86.82,87.33H97.56C96.62,90.07 95.67,88.91 94.73,91.72C95.62,98 96.52,104.3 97.4,110.4C97.44,110.69 97.37,110.99 97.2,111.23C97.03,111.47 96.77,111.63 96.48,111.69Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M128.39,151.18L128.79,151.26L121.56,180.05C121.45,180.48 121.18,180.85 120.82,181.1C120.45,181.35 120.01,181.46 119.57,181.4C119.13,181.35 118.72,181.14 118.43,180.81C118.13,180.48 117.97,180.05 117.96,179.61C117.96,179.52 117.96,179.44 117.96,179.35L122.06,150.05L128.39,151.18Z"
      android:fillColor="#C9C9C9"/>
  <path
      android:pathData="M207.5,102.21C206.81,111.46 205.27,141.71 193.57,153.01L196.05,170.7C196.1,171.16 195.98,171.62 195.71,171.99C195.43,172.36 195.03,172.61 194.58,172.7C194.13,172.79 193.66,172.71 193.27,172.46C192.88,172.22 192.6,171.84 192.47,171.4L188.53,156.35C187.95,156.59 187.34,156.78 186.73,156.93C185.84,157.15 184.92,157.27 184,157.31C182.34,157.38 180.11,157.31 177.46,157.23L177.59,157.35L181.66,186.35C181.73,186.82 181.61,187.29 181.34,187.67C181.06,188.05 180.65,188.32 180.19,188.4C180.08,188.41 179.96,188.41 179.85,188.4C179.44,188.4 179.05,188.26 178.73,188.01C178.41,187.76 178.18,187.42 178.08,187.02L170.14,156.69C163.14,156.13 154.88,155.23 146.85,154.13L143.35,167.52C143.27,167.93 143.05,168.31 142.73,168.58C142.41,168.85 142,169 141.58,169C141.47,169.01 141.35,169.01 141.24,169C140.79,168.92 140.38,168.66 140.11,168.29C139.84,167.92 139.72,167.46 139.77,167L141.65,153.56C137.04,152.87 132.65,152.13 128.79,151.37L128.39,151.3C125.76,150.76 123.39,150.22 121.39,149.69C119.39,149.15 117.6,148.03 116.23,146.47C114.87,144.91 114,142.98 113.73,140.92L108.64,101.92C108.58,101.47 108.62,101 108.75,100.56C108.88,100.12 109.1,99.71 109.39,99.36C109.69,99.01 110.06,98.72 110.47,98.52C110.88,98.32 111.33,98.2 111.79,98.18L142.73,96.92C141.73,91.25 140.95,85.56 140.73,81.86C139.96,70.56 142.81,61.26 154.92,63.86C159.63,64.86 163.66,66.92 174.41,67.1C180.99,67.21 197.62,59.85 205.16,66.93C211.31,72.71 209.24,86.93 205.65,99.64C206.22,99.77 206.72,100.11 207.06,100.58C207.4,101.05 207.55,101.63 207.5,102.21Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M111.87,98.14C112.78,98.14 113.66,98.49 114.32,99.12C114.98,99.75 115.38,100.61 115.42,101.52L117.42,129.1"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M205.62,99.66C209.22,86.93 211.28,72.73 205.13,66.96C197.59,59.88 180.96,67.24 174.39,67.13C163.64,66.95 159.61,64.92 154.89,63.89C142.79,61.25 139.89,70.56 140.71,81.89C141.39,91.89 146.24,116.11 146.24,116.11C138.89,117.45 131.62,119.23 124.48,121.44C121.24,122.44 113.56,126.94 119.73,132.21C125.04,136.74 168.46,139.65 182.1,140.67C183.62,140.8 185.14,140.8 186.66,140.67"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.57,153L196.05,170.69C196.1,171.15 195.98,171.61 195.71,171.98C195.44,172.35 195.03,172.6 194.58,172.69C194.12,172.77 193.65,172.68 193.26,172.43C192.87,172.18 192.59,171.79 192.47,171.34L188.53,156.34"
      android:fillColor="#C9C9C9"/>
  <path
      android:pathData="M193.57,153L196.05,170.69C196.1,171.15 195.98,171.61 195.71,171.98C195.44,172.35 195.03,172.6 194.58,172.69C194.12,172.77 193.65,172.68 193.26,172.43C192.87,172.18 192.59,171.79 192.47,171.34L188.53,156.34"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.29,128.15C117.29,128.15 115.92,137.25 119.78,142.37C124,148 165.66,151.16 181,151.88"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M147.71,115.57C155.16,114.6 162.68,114.41 170.17,115C179.69,115.56 184.93,116.53 189.63,118.55"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M147.71,115.57C155.16,114.6 162.68,114.41 170.17,115C179.69,115.56 184.93,116.53 189.63,118.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.73,96.92L111.79,98.18C111.33,98.2 110.88,98.31 110.47,98.52C110.06,98.72 109.69,99.01 109.39,99.36C109.1,99.71 108.88,100.12 108.75,100.56C108.62,101 108.59,101.47 108.65,101.92L113.73,140.8C114,142.86 114.87,144.79 116.24,146.35C117.6,147.91 119.4,149.03 121.4,149.57C136.53,153.64 172.98,157.77 184.01,157.3C204.53,156.43 206.65,113.55 207.5,102.21C207.53,101.87 207.48,101.53 207.37,101.21C207.25,100.89 207.07,100.6 206.83,100.35C206.6,100.1 206.31,99.91 206,99.78C205.68,99.65 205.34,99.59 205,99.6L194.81,99.92C194.04,99.96 193.31,100.26 192.74,100.79C192.18,101.31 191.81,102.01 191.71,102.77C191.71,102.77 187.47,135.17 185.9,146.18C185.75,147.61 185.33,148.99 184.64,150.25C183.49,152.18 180.22,151.83 180.22,151.83"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M195.38,99.83C199.2,99.83 200.14,102.31 199.61,104.98C199.61,104.98 192.97,144.77 190.98,151.37C190.34,153.71 188.82,155.7 186.73,156.92"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M122.09,150.05L118,179.35C117.94,179.82 118.05,180.29 118.32,180.67C118.6,181.05 119.01,181.32 119.47,181.4C119.93,181.49 120.4,181.39 120.79,181.14C121.19,180.89 121.47,180.5 121.58,180.05L128.81,151.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.6,157.34L181.67,186.34C181.73,186.8 181.62,187.27 181.34,187.66C181.07,188.04 180.66,188.3 180.2,188.39C179.74,188.48 179.27,188.38 178.88,188.13C178.49,187.88 178.2,187.49 178.09,187.04L170.09,156.7"
      android:fillColor="#C9C9C9"/>
  <path
      android:pathData="M177.6,157.34L181.67,186.34C181.73,186.8 181.62,187.27 181.34,187.66C181.07,188.04 180.66,188.3 180.2,188.39C179.74,188.48 179.27,188.38 178.88,188.13C178.49,187.88 178.2,187.49 178.09,187.04L170.09,156.7"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M141.65,153.45L139.77,166.88C139.7,167.34 139.82,167.82 140.09,168.2C140.37,168.58 140.78,168.84 141.24,168.93C141.7,169.02 142.17,168.93 142.56,168.68C142.95,168.42 143.24,168.03 143.35,167.58L146.86,154.19"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M133.25,164.72C132.44,165.17 131.75,165.81 131.23,166.57C130.71,167.34 130.38,168.21 130.25,169.13C129.64,172.41 147.91,176.18 155.79,173.6C160.15,172.17 159.72,169.6 159.72,169.6C159.72,169.6 157.57,163.71 149.35,163.3C143.95,163.13 138.54,163.6 133.25,164.72Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.99,63.89C171.41,63.89 175,60.3 175,55.88C175,51.46 171.41,47.87 166.99,47.87C162.57,47.87 158.98,51.46 158.98,55.88C158.98,60.3 162.57,63.89 166.99,63.89Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M161.29,184.75L156.27,189.7L152.98,185.1L153.76,172.33L146.24,154.61L155.99,154.96L159.82,172.15L161.29,184.75Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.78,181.59C152.79,181.54 152.81,181.5 152.84,181.47C152.88,181.44 152.92,181.41 152.97,181.41C153.02,181.4 153.07,181.41 153.11,181.44C153.15,181.46 153.18,181.5 153.2,181.54C153.38,182.23 153.77,182.84 154.33,183.28C154.88,183.73 155.56,183.98 156.27,184C158.71,184.13 160.27,181.51 160.82,180.25C160.84,180.21 160.87,180.18 160.91,180.16C160.95,180.14 161,180.13 161.04,180.13C161.09,180.14 161.13,180.16 161.16,180.19C161.2,180.22 161.22,180.26 161.23,180.3C161.56,182.02 161.77,183.77 161.84,185.52C161.84,187.83 158.46,193.06 156.66,192.89C154.86,192.72 152.8,187.83 152.55,185.8C152.52,184.4 152.6,182.99 152.78,181.59Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M164.49,160.33L162.24,167.01L157.23,164.39L152.17,152.64L137.46,140.22L146.32,136.13L157.5,149.75L164.49,160.33Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.47,161.34C155.46,161.29 155.46,161.25 155.48,161.2C155.49,161.16 155.52,161.12 155.56,161.1C155.6,161.07 155.65,161.06 155.69,161.06C155.74,161.06 155.78,161.08 155.82,161.11C156.29,161.65 156.91,162.02 157.61,162.17C158.3,162.32 159.03,162.24 159.68,161.95C161.92,160.95 162.1,157.95 162.06,156.55C162.06,156.5 162.07,156.46 162.1,156.42C162.13,156.38 162.16,156.36 162.21,156.34C162.25,156.33 162.29,156.32 162.34,156.34C162.38,156.35 162.42,156.37 162.45,156.41C163.52,157.8 164.48,159.26 165.34,160.79C166.34,162.88 165.73,169.04 164.05,169.7C162.37,170.36 158.32,166.93 157.18,165.23C156.51,163.98 155.94,162.68 155.47,161.34Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M175.72,55.29C178.79,55.29 181.28,52.8 181.28,49.73C181.28,46.66 178.79,44.17 175.72,44.17C172.65,44.17 170.16,46.66 170.16,49.73C170.16,52.8 172.65,55.29 175.72,55.29Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M172.3,58.76C175.18,58.76 177.51,56.43 177.51,53.55C177.51,50.67 175.18,48.34 172.3,48.34C169.42,48.34 167.09,50.67 167.09,53.55C167.09,56.43 169.42,58.76 172.3,58.76Z"
      android:fillColor="#928EBA"/>
  <path
      android:pathData="M160.45,78.52C160.75,80.82 160.8,83.15 160.61,85.46C160.48,87.75 160.1,89.99 159.82,92.25L159.29,95.63L158.68,99C158.34,100.65 157.91,102.29 157.4,103.89C157.15,104.82 156.56,105.62 155.76,106.14C154.95,106.67 153.98,106.87 153.03,106.72C152.08,106.57 151.22,106.08 150.61,105.34C150.01,104.59 149.69,103.65 149.74,102.69C149.77,101 149.88,99.32 150.08,97.64L150.54,94.26L151.08,90.89C151.5,88.65 151.83,86.4 152.41,84.18C152.94,81.92 153.7,79.73 154.69,77.63C154.95,77.01 155.4,76.49 155.99,76.16C156.58,75.84 157.26,75.71 157.92,75.82C158.59,75.92 159.2,76.24 159.65,76.74C160.11,77.23 160.39,77.86 160.45,78.53V78.52Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M188.09,85.77C188.83,89.43 187.68,105.56 187.27,108.01C187.1,109.03 186.53,109.94 185.68,110.54C184.84,111.14 183.8,111.38 182.78,111.21C180.93,110.9 179.91,109.02 179.78,107.21C179.66,104.73 179.68,102.21 179.78,99.78C179.78,99.78 179.78,87.41 180.19,84.95C180.67,80.6 183.86,77.81 185.53,77.95C188,78.31 188,83.29 188.09,85.77Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M187.49,83.06C188.3,88.3 150.83,88.37 151.65,83.14C152.47,77.91 155.1,73.37 160.86,73.46C161.26,73.46 168.1,72.02 171.17,72.4C171.88,72.4 177.39,73.68 178.01,73.75C184.71,74.5 186.6,77.25 187.49,83.06Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M181.74,83.2C181.28,86.58 180.58,89.92 179.64,93.2C178.35,98.36 177.34,103.52 179.3,108.68H158.3C159.75,104.23 159.19,99.3 158.3,94.3C157.63,90.57 156.76,86.8 156.41,83.16C156.24,80.99 156.91,78.84 158.3,77.16C160.9,78 163.58,78.56 166.3,78.82C170.77,79.26 175.83,77.76 179.2,76.45C180.08,77.33 180.77,78.38 181.21,79.55C181.65,80.71 181.83,81.96 181.74,83.2Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.16,65.22C169.52,65.03 168.85,64.99 168.2,65.08C167.54,65.18 166.92,65.41 166.36,65.77C166.38,65.96 166.44,66.15 166.54,66.32C166.64,66.48 166.78,66.62 166.95,66.72C167.29,66.92 167.7,67 168.09,66.94C168.48,66.87 168.84,66.72 169.16,66.49C169.59,66.15 169.93,65.72 170.16,65.22Z"
      android:fillColor="#DD998A"/>
  <path
      android:pathData="M178.66,129.45C173.44,135.79 155.66,142.38 155.66,142.38C155.66,142.38 157.66,150.11 158.36,154.81C159.36,161.81 159.83,169.68 159.83,169.68C159.83,169.68 156.93,172.37 150.61,171.05C146.54,170.19 145.24,169.05 137.5,167.38C133.06,166.38 131.15,168.01 130.32,169.57C130.3,169.6 130.27,169.62 130.23,169.64C130.2,169.65 130.16,169.65 130.12,169.63C130.09,169.62 130.06,169.6 130.04,169.57C130.02,169.54 130.01,169.5 130.01,169.46L134.13,144.31C134.13,144.31 128.06,127.09 127.75,121.77C127.62,119.42 127.75,116.35 130.81,115.23C138.12,112.56 155.13,112.64 157.3,112.76L159.3,103.91L178.51,104.67C178.51,104.67 179.96,113.46 180.41,116.96C181,121.41 181.48,126 178.66,129.45Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M167.61,64.07C168.02,64.03 168.41,63.86 168.71,63.58"
      android:strokeLineJoin="round"
      android:strokeWidth="0.22"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166.11,65.79C166.85,66.08 167.66,66.15 168.44,66C169.22,65.84 169.93,65.47 170.51,64.92"
      android:strokeLineJoin="round"
      android:strokeWidth="0.22"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M163.65,60.28C163.65,60.68 163.88,61 164.18,61C164.48,61 164.71,60.69 164.72,60.29C164.74,60.13 164.7,59.96 164.6,59.83C164.5,59.69 164.36,59.6 164.19,59.57C164.03,59.6 163.88,59.68 163.78,59.82C163.68,59.95 163.63,60.12 163.65,60.28Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M170.69,60.28C170.69,60.68 170.92,61 171.22,61C171.52,61 171.75,60.69 171.76,60.29C171.78,60.13 171.74,59.96 171.64,59.83C171.54,59.69 171.4,59.6 171.23,59.57C171.07,59.6 170.92,59.68 170.82,59.82C170.72,59.95 170.67,60.12 170.69,60.28Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M162.73,59.4C162.9,59.25 163.11,59.14 163.32,59.07C163.54,59 163.77,58.98 164,59C164.4,59.08 164.76,59.31 165,59.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.22"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M172.61,59.4C172.43,59.25 172.22,59.13 172,59.06C171.77,58.99 171.54,58.97 171.3,59C170.9,59.08 170.54,59.31 170.3,59.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.22"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.12,60.54C167.15,58.78 165.79,57.33 164.1,57.31C162.4,57.29 161.01,58.69 160.98,60.45C160.96,62.22 162.31,63.66 164.01,63.69C165.7,63.71 167.1,62.31 167.12,60.54Z"
      android:strokeAlpha="0.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.23"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M174.5,60.64C174.53,58.88 173.17,57.43 171.48,57.4C169.78,57.38 168.39,58.79 168.36,60.55C168.34,62.31 169.69,63.76 171.39,63.78C173.08,63.81 174.48,62.4 174.5,60.64Z"
      android:strokeAlpha="0.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.23"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.13,60.27L168.37,60.28"
      android:strokeAlpha="0.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.23"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.07,51.28C165.27,51.88 165.52,52.45 165.83,53C167.83,56.35 173.83,59.33 175.26,58.94C175.44,57.92 175.44,56.87 175.26,55.85C174.97,53.93 174.26,52.07 172.08,51.08C170.97,50.63 169.8,50.33 168.61,50.18C167.98,50.09 167.34,50.14 166.72,50.32C166.11,50.51 165.55,50.82 165.07,51.24V51.28Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M166.11,50.61C165.56,53.23 164.31,55.65 162.49,57.61C162.26,57.94 161.97,58.22 161.62,58.43C161.28,58.64 160.89,58.76 160.49,58.8C160.11,57.29 160.21,55.7 160.78,54.25C161.38,52.83 162.39,51.62 163.69,50.78C163.97,50.59 164.27,50.44 164.59,50.34C165.11,50.31 165.63,50.4 166.11,50.61Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M161.85,57C162.19,56.7 162.58,56.48 163.01,56.36C163.45,56.23 163.9,56.21 164.34,56.28C164.93,56.4 165.45,56.72 165.84,57.17"
      android:strokeLineJoin="round"
      android:strokeWidth="0.22"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.44,56.76C173.1,56.46 172.7,56.24 172.27,56.12C171.84,55.99 171.38,55.97 170.94,56.04C170.36,56.17 169.84,56.48 169.45,56.93"
      android:strokeLineJoin="round"
      android:strokeWidth="0.22"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.7,57.54C177.87,61.39 174.28,65.06 170.01,65.06H167.53C163.26,65.06 159.79,61.93 159.79,58.06V55.84C159.79,51.98 163.26,48.84 167.53,48.84C167.53,48.85 177.28,47.57 177.7,57.54Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M174,62.56C173.93,63.86 173.55,65.12 172.89,66.24C172.23,67.36 171.31,68.31 170.21,69C169.21,69.6 168.07,69.92 166.91,69.94C166.19,69.87 165.49,69.66 164.85,69.32C164.21,68.97 163.65,68.51 163.19,67.94C161.96,66.45 161.19,64.63 161,62.71C160.79,61.25 160.66,59.78 160.62,58.3C160.62,54.22 162.42,51.78 165.98,51.87H167.98C171.54,51.78 173.89,54.23 173.88,58.32C173.82,59.79 174,61.09 174,62.56Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M168.84,65.27C168.2,65.08 167.53,65.04 166.88,65.14C166.22,65.23 165.59,65.47 165.04,65.83C165.05,66.02 165.12,66.21 165.22,66.37C165.32,66.54 165.46,66.67 165.63,66.77C165.97,66.98 166.37,67.06 166.76,66.99C167.15,66.93 167.52,66.78 167.84,66.54C168.27,66.2 168.61,65.77 168.84,65.27Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M173.75,59.18C174.1,58.98 174.5,58.9 174.89,58.95C175.29,59.01 175.65,59.19 175.93,59.47C176.64,60.34 175.93,61.99 175.18,62.83C175.07,62.95 174.08,64.05 173.44,63.74C172.54,63.3 172.29,60.06 173.75,59.18Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M175.72,60.54C175.62,60.44 175.49,60.37 175.35,60.32C175.22,60.28 175.07,60.27 174.93,60.3C174.64,60.36 174.38,60.51 174.19,60.73C174,60.95 173.83,61.19 173.7,61.44"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M166,64.14C166.41,64.1 166.8,63.92 167.1,63.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.79,65.84C165.52,66.13 166.33,66.21 167.11,66.06C167.88,65.91 168.6,65.54 169.18,65"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.16,60.69C163.97,60.83 163.75,60.93 163.52,60.98C163.3,61.03 163.06,61.03 162.83,60.99C162.42,60.9 162.06,60.64 161.83,60.29"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.68,60.37C168.86,60.56 169.08,60.71 169.32,60.8C169.57,60.89 169.83,60.92 170.09,60.89C170.35,60.87 170.6,60.78 170.82,60.64C171.04,60.5 171.23,60.31 171.36,60.08"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M162.66,51.5C162.85,52.11 163.1,52.7 163.4,53.27C165.32,56.62 172.48,59.52 173.91,59.13C173.91,59.13 176.72,51.82 171.72,50.43C170.63,50.13 163.61,50.64 162.66,51.5Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M164.59,50.72C164.19,53.25 163.16,55.64 161.59,57.66C160.87,58.66 160.25,58.85 159.91,58.82C159.55,57.32 159.61,55.76 160.08,54.29C160.53,52.93 161.39,51.74 162.53,50.87C162.76,50.68 163.02,50.54 163.3,50.45C163.74,50.43 164.19,50.52 164.59,50.72Z"
      android:fillColor="#525559"/>
  <path
      android:pathData="M159.82,107.29C164.07,106.79 177.33,104.04 177.33,104.04C179.46,103.82 181,103.1 183.15,103.2C185.3,103.3 187.36,105.87 187.27,108.01C187.29,108.79 187,109.56 186.48,110.14C185.95,110.72 185.22,111.08 184.44,111.14C182.33,111.51 180.2,111.73 178.06,111.78C178.06,111.78 164.58,112.42 160.3,112.53C158.69,112.53 157.89,111.31 157.84,109.71C157.79,108.11 158.34,107.47 159.82,107.29Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M173.9,73.46L165.56,73.91L166.25,67.91C167.66,68.02 169.06,67.71 170.29,67.02C171.52,66.33 172.52,65.29 173.15,64.03L173.9,73.46Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M163.4,122.18C152.25,125.06 143.55,129.46 138.33,133.3C137.4,133.98 136.61,134.85 136.01,135.84C135.41,136.83 135.02,137.93 134.85,139.07L134.17,143.71"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#525559"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139,117.68C143.31,121.08 144.33,122.33 147.45,127.78"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#525559"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M188.7,89.48C188.7,89.48 184.7,91.3 176.81,91.32C157.41,91.37 151,84.74 151,84.74C150.9,82.9 151.28,81.07 152.1,79.42C153.46,76.42 154.94,75.42 154.94,75.42C158.53,77.12 162.41,78.14 166.37,78.42C171.91,78.78 177.42,77.38 182.11,74.42C182.11,74.42 185.83,74.71 187.53,79.6C188.7,83.08 188.7,89.48 188.7,89.48Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176,65.23C176,65.63 175.84,66.01 175.56,66.29C175.28,66.57 174.9,66.73 174.5,66.73C174.1,66.73 173.72,66.57 173.44,66.29C173.16,66.01 173,65.63 173,65.23C173,64.4 174.5,62.65 174.5,62.65C174.5,62.65 176,64.4 176,65.23Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.29,77.59C152.29,81.76 153.54,85.84 154.71,89.97L155.55,93.08L156.32,96.2C156.83,98.28 157.18,100.4 157.38,102.53C157.45,103.48 157.16,104.41 156.58,105.17C156.01,105.92 155.17,106.43 154.24,106.61C153.31,106.8 152.34,106.63 151.53,106.15C150.71,105.67 150.1,104.9 149.8,104C149.18,101.95 148.71,99.86 148.38,97.74L147.9,94.56L147.5,91.37C147.01,87.1 146.88,82.74 146.24,78.51C145.99,76.92 146.61,75.51 148.24,75.31C148.94,75.23 149.65,75.43 150.21,75.85C150.78,76.27 151.17,76.89 151.29,77.59Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M170.11,68.86C169.95,69.68 169.49,70.42 168.82,70.93C168.15,71.44 167.32,71.68 166.48,71.62C166.25,71.62 166.02,71.6 165.79,71.57L166,69.7C166.24,69.77 166.5,69.8 166.75,69.8C167.93,69.78 169.09,69.46 170.11,68.86Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M156,142.1C152.1,146.26 150.1,148.95 146.66,155.68"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#525559"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.58,113.13L154.86,114.49C154.86,114.49 151.03,110.35 152.2,109.64C155.11,107.86 161.85,106.96 161.85,106.96L162.12,109.21C162.25,110.06 162.06,110.93 161.6,111.65C161.13,112.38 160.41,112.9 159.58,113.13Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M156.26,113L152.34,113.82L152.71,113.58L149.58,117C149.49,117.11 149.36,117.18 149.22,117.19C149.08,117.2 148.94,117.16 148.83,117.07C148.72,116.98 148.65,116.85 148.64,116.71C148.63,116.57 148.67,116.43 148.76,116.32L151.56,112.62C151.63,112.52 151.73,112.44 151.85,112.4H151.93L155.71,111.07C155.96,110.98 156.24,111 156.49,111.11C156.73,111.23 156.92,111.44 157.01,111.69C157.1,111.94 157.09,112.22 156.97,112.47C156.85,112.71 156.64,112.9 156.39,112.99L156.26,113Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M156.69,113.79L153.69,115.08L153.91,114.91L151.73,117.24C151.68,117.29 151.63,117.33 151.56,117.36C151.5,117.38 151.43,117.4 151.37,117.4C151.3,117.4 151.23,117.38 151.17,117.36C151.1,117.33 151.05,117.29 151,117.24C150.91,117.15 150.86,117.02 150.86,116.89C150.86,116.76 150.91,116.63 151,116.53L153,114.06C153.05,114 153.11,113.95 153.18,113.91L156.12,112.47C156.21,112.43 156.3,112.41 156.39,112.4C156.49,112.39 156.58,112.41 156.67,112.44C156.76,112.47 156.85,112.52 156.92,112.58C156.99,112.64 157.05,112.72 157.09,112.8C157.13,112.89 157.16,112.98 157.16,113.08C157.17,113.17 157.16,113.27 157.13,113.36C157.1,113.45 157.05,113.53 156.98,113.6C156.92,113.67 156.85,113.73 156.76,113.77L156.69,113.79Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M160.62,58.61C160.65,59.98 160.77,61.35 160.98,62.71C161.22,64.63 161.96,66.45 163.12,68C163.84,68.97 164.86,69.68 166.03,70L165.7,72.78C164.11,73.17 162.49,73.43 160.86,73.55C159.77,73.58 158.69,73.79 157.66,74.16C155.51,74.9 155.11,75.64 155.11,75.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M173.45,66.49L173.7,72.8C175.57,73.34 182.1,74.5 182.1,74.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160.7,57.64C161.3,57.39 161.96,57.3 162.61,57.38C163.26,57.45 163.88,57.68 164.41,58.06"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.66,57.7C168.37,57.32 169.14,57.1 169.94,57.03C170.74,56.97 171.54,57.06 172.3,57.32"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.7,72.69L165.56,73.91"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M142.5,18.99C142.12,17.98 141.49,17.08 140.68,16.36C139.54,15.52 138.27,14.88 136.91,14.49L136.64,14.38L127.57,11.01L127.43,10.94C126.52,10.57 125.57,10.28 124.61,10.08C123.85,9.97 123.09,9.97 122.33,10.08C121.36,10.28 120.42,10.57 119.51,10.94L119.36,11.01L110.28,14.38L110.02,14.49C108.66,14.89 107.39,15.52 106.25,16.36C105.44,17.08 104.82,17.98 104.43,18.99C104.06,20.34 103.93,21.74 104.04,23.14V28.9C104.04,40.86 111.18,49.15 121.14,53.45H121.21C121.7,53.68 122.21,53.85 122.74,53.95C123.25,54.02 123.76,54.02 124.27,53.95C124.8,53.85 125.31,53.68 125.79,53.45H125.88C135.83,49.15 142.97,40.86 142.97,28.9V23.14C143.07,21.74 142.91,20.33 142.5,18.99ZM123.48,19.77C124.72,19.77 125.93,20.13 126.97,20.82C127.32,21.05 127.65,21.33 127.95,21.63C128,21.67 128.04,21.71 128.09,21.75C129.31,22.87 129.99,24.39 130,25.98C130,27.17 129.63,28.33 128.91,29.32C128.69,29.63 128.43,29.92 128.15,30.19C128.08,30.27 128,30.35 127.92,30.43C127.04,31.31 125.93,31.9 124.71,32.14C123.49,32.38 122.23,32.26 121.08,31.79C120.15,31.4 119.33,30.8 118.69,30.04C117.9,29.23 117.36,28.25 117.13,27.18C116.87,26.01 117,24.81 117.49,23.71C117.82,22.98 118.29,22.33 118.88,21.78C118.94,21.72 119,21.66 119.06,21.6C120.23,20.43 121.82,19.77 123.48,19.77ZM136,41.88C133.09,45.8 129.13,48.84 124.58,50.68C124.32,50.82 124.04,50.92 123.75,50.99C123.59,51 123.43,51 123.27,50.99C122.98,50.92 122.7,50.82 122.43,50.68C117.88,48.84 113.92,45.8 111,41.88C111.71,40.12 112.94,38.62 114.53,37.57C116.13,36.51 118,35.97 119.92,36H127.08C129,35.97 130.88,36.51 132.47,37.56C134.06,38.61 135.29,40.12 136,41.88Z"
      android:fillColor="#8D65AC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M156.12,71.59C155.49,71.61 154.87,71.43 154.36,71.07C153.84,70.71 153.46,70.19 153.26,69.59C153.06,68.69 152.85,67.78 152.65,66.88C154.93,66.37 157.3,66.37 159.58,66.88C159.38,67.78 159.18,68.69 158.97,69.59C158.77,70.19 158.39,70.7 157.87,71.06C157.36,71.42 156.74,71.61 156.12,71.59Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.09,67.23C158.07,67.23 159.68,66.99 159.68,66.68C159.68,66.38 158.07,66.13 156.09,66.13C154.11,66.13 152.5,66.38 152.5,66.68C152.5,66.99 154.11,67.23 156.09,67.23Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M155.61,111.66L151.14,113.16L151.49,112.87L148.62,116.87C148.53,116.99 148.41,117.06 148.27,117.08C148.13,117.11 147.98,117.07 147.87,116.99C147.76,116.91 147.68,116.8 147.65,116.67C147.62,116.53 147.65,116.4 147.72,116.28L150.23,112.04C150.29,111.93 150.4,111.84 150.52,111.79H150.58L154.85,109.79C155.09,109.68 155.37,109.66 155.62,109.76C155.88,109.85 156.08,110.04 156.2,110.28C156.31,110.53 156.32,110.8 156.23,111.06C156.14,111.31 155.95,111.52 155.71,111.63L155.61,111.66Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M154.56,110.92L150,112L150.41,111.7L147.67,115.44C147.58,115.56 147.46,115.63 147.31,115.65C147.17,115.68 147.03,115.64 146.91,115.56C146.8,115.48 146.73,115.36 146.71,115.23C146.68,115.1 146.7,114.96 146.77,114.85L149.16,110.85C149.23,110.72 149.35,110.62 149.49,110.57H149.57L154,109C154.12,108.95 154.26,108.93 154.39,108.94C154.52,108.94 154.66,108.98 154.78,109.03C154.9,109.09 155.01,109.17 155.1,109.27C155.19,109.36 155.26,109.48 155.3,109.61C155.35,109.73 155.37,109.86 155.37,110C155.36,110.13 155.33,110.26 155.27,110.38C155.22,110.5 155.14,110.61 155.04,110.7C154.94,110.79 154.82,110.86 154.7,110.91L154.56,110.92Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M160,107.27L159.22,107.41C159.42,107.36 159.62,107.32 159.82,107.29L160,107.27Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.85,107L160,107.27C161.1,107.07 161.85,107 161.85,107Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M188.38,89.61C188.45,92.1 187.68,105.61 187.27,108.01C187.29,108.79 187.01,109.55 186.49,110.13C185.96,110.71 185.24,111.07 184.46,111.13H184.36L183.91,111.22H183.79C181.89,111.52 179.98,111.7 178.06,111.75C178.06,111.75 165.57,112.34 160.82,112.5C160.46,112.8 160.03,113 159.58,113.11L155.82,114.26L153.82,115.04L151.66,117.29C151.61,117.34 151.55,117.38 151.49,117.4C151.43,117.43 151.36,117.44 151.29,117.44C151.22,117.44 151.15,117.43 151.09,117.4C151.02,117.38 150.97,117.34 150.92,117.29C150.83,117.18 150.78,117.04 150.78,116.89C150.78,116.75 150.83,116.61 150.92,116.5L152.8,114.04C152.8,113.98 153.8,113.5 153.8,113.5L152.62,113.9L149.75,117.22C149.63,117.31 149.48,117.36 149.32,117.35C149.17,117.33 149.03,117.27 148.92,117.16C148.81,117.04 148.73,116.91 148.68,116.76V116.83C148.6,116.94 148.47,117.01 148.34,117.03C148.21,117.05 148.07,117.02 147.96,116.95C147.84,116.87 147.76,116.76 147.73,116.63C147.7,116.5 147.71,116.36 147.78,116.24L150.3,112C150.33,111.95 150.36,111.9 150.41,111.86H150.31L147.76,115.35C147.67,115.46 147.55,115.54 147.41,115.56C147.26,115.58 147.12,115.54 147.01,115.46C146.9,115.38 146.82,115.27 146.79,115.13C146.76,115 146.79,114.86 146.86,114.75L149.25,110.75C149.33,110.62 149.45,110.52 149.59,110.47H149.66L152.38,109.47C154.56,108.42 156.89,107.69 159.28,107.31L160.06,107.17L161.91,106.9C167.31,106.03 177.39,103.9 177.39,103.9C178.2,103.8 179.01,103.66 179.81,103.47C179.81,102.2 179.81,100.93 179.81,99.66C179.81,99.66 179.81,93.51 180.18,91.05"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.25,111.94L153.08,110.73"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.67,116.81L151.23,113.04L153.85,111.91"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.79,113.55L154.64,113.18"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.79,103.63C181.2,103.48 182.62,103.69 183.93,104.24"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.63,109.06C164.75,109.86 164.54,110.68 164.05,111.33C163.56,111.98 162.84,112.41 162.03,112.53C161.8,112.55 161.56,112.55 161.32,112.53C161.78,112.21 162.14,111.77 162.36,111.26C162.58,110.75 162.65,110.18 162.57,109.63C162.43,108.96 162.06,108.36 161.52,107.92C160.99,107.49 160.32,107.26 159.63,107.26C160.03,106.97 160.49,106.78 160.97,106.69C161.37,106.6 161.78,106.59 162.18,106.67C162.58,106.74 162.96,106.89 163.3,107.11C163.64,107.33 163.93,107.61 164.16,107.95C164.39,108.29 164.55,108.66 164.63,109.06Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.1,72.33L156.21,71.23H156.93L153.38,73.75C153.17,73.84 152.94,73.85 152.72,73.79C152.5,73.73 152.31,73.59 152.18,73.41C152.05,73.22 151.99,73 152,72.77C152.01,72.55 152.1,72.33 152.25,72.16L152.34,72.11L156.13,69.96H156.21C156.31,69.91 156.42,69.88 156.53,69.88C156.64,69.88 156.74,69.91 156.84,69.96L159.54,71.46C159.6,71.5 159.66,71.54 159.7,71.59C159.74,71.65 159.78,71.71 159.79,71.78C159.82,71.85 159.82,71.92 159.81,71.98C159.81,72.05 159.79,72.12 159.75,72.18C159.68,72.28 159.57,72.34 159.45,72.37C159.33,72.4 159.21,72.38 159.1,72.33Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M152.61,73.05L156.53,71.21H156.61C156.76,71.14 156.93,71.13 157.09,71.17C157.25,71.21 157.39,71.31 157.49,71.45L159.42,74.19C159.49,74.31 159.52,74.45 159.49,74.58C159.47,74.72 159.4,74.84 159.29,74.93C159.18,75.02 159.05,75.06 158.91,75.06C158.77,75.05 158.64,75 158.54,74.9L156.29,72.41L157.29,72.61L153.55,74.81C153.44,74.91 153.31,74.99 153.17,75.03C153.02,75.08 152.87,75.09 152.73,75.07C152.58,75.05 152.44,74.99 152.31,74.91C152.19,74.83 152.08,74.72 152.01,74.59C151.93,74.46 151.88,74.32 151.87,74.17C151.86,74.02 151.88,73.87 151.93,73.73C151.98,73.59 152.06,73.46 152.17,73.36C152.28,73.26 152.41,73.18 152.55,73.13L152.61,73.05Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M152.83,74.64L156.53,72.8H156.62C156.75,72.74 156.9,72.72 157.04,72.75C157.18,72.78 157.31,72.86 157.4,72.97L159.05,74.97C159.14,75.07 159.2,75.2 159.2,75.34C159.2,75.47 159.14,75.6 159.04,75.7C158.95,75.79 158.82,75.85 158.68,75.84C158.54,75.84 158.41,75.79 158.32,75.69L156.38,74.01L157.25,74.13L153.8,76.32C153.58,76.46 153.32,76.5 153.07,76.45C152.83,76.4 152.61,76.26 152.46,76.05C152.32,75.84 152.27,75.57 152.32,75.32C152.37,75.07 152.52,74.85 152.73,74.7L152.83,74.64Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M153.29,76.32L156.07,74.78C156.19,74.71 156.33,74.69 156.47,74.72C156.6,74.75 156.72,74.82 156.81,74.93L158.2,76.75C158.28,76.86 158.32,76.99 158.3,77.12C158.28,77.25 158.21,77.37 158.11,77.45C158.01,77.52 157.88,77.55 157.76,77.54C157.64,77.53 157.52,77.48 157.44,77.39L155.92,75.7L156.69,75.83L154,77.51C153.92,77.57 153.83,77.61 153.74,77.62C153.64,77.64 153.54,77.64 153.45,77.62C153.35,77.6 153.27,77.56 153.19,77.5C153.11,77.45 153.05,77.37 153,77.29C152.95,77.21 152.91,77.12 152.89,77.03C152.87,76.93 152.88,76.84 152.9,76.74C152.92,76.65 152.96,76.56 153.01,76.48C153.07,76.41 153.14,76.34 153.22,76.29L153.29,76.32Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M153.2,71.77C151.14,72.83 149.16,74.05 147.29,75.41C146.6,75.93 145.78,76.41 146.22,78.41C146.66,80.41 148.28,82.06 151.76,79.61C152.13,79.35 154.48,77.11 154.36,76.71L153.2,71.77Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M158.84,98.12C158.56,101.94 156.14,106.33 154.2,106.67C154.11,106.68 154.03,106.68 153.94,106.67H153.82H153.5C152.68,106.67 151.88,106.42 151.22,105.94C150.56,105.46 150.07,104.78 149.81,104C149.18,101.95 148.7,99.86 148.38,97.74L147.9,94.56L147.5,91.37C147.01,87.1 146.64,82.81 146.34,78.53C146.2,76.61 146.7,75.83 147.34,75.39C148.83,74.29 150.4,73.29 152.02,72.39C152.08,72.27 156.18,69.88 156.18,69.88H156.26C156.36,69.83 156.46,69.8 156.57,69.8C156.69,69.8 156.79,69.83 156.89,69.88L159.6,71.37C159.73,71.43 159.84,71.54 159.89,71.68C159.94,71.81 159.93,71.97 159.88,72.1C159.82,72.23 159.71,72.33 159.57,72.39C159.43,72.44 159.28,72.43 159.15,72.37L157.9,71.9L159.47,74.19C159.51,74.25 159.54,74.32 159.56,74.4C159.58,74.47 159.58,74.54 159.57,74.62C159.55,74.69 159.53,74.76 159.49,74.82C159.45,74.89 159.39,74.94 159.33,74.98L159.18,75.06C159.23,75.17 159.23,75.29 159.2,75.4C159.17,75.51 159.1,75.61 159.01,75.68C158.91,75.75 158.8,75.78 158.68,75.77C158.57,75.77 158.46,75.72 158.37,75.64L156.89,74.35L156.3,74.75C156.41,74.72 156.52,74.73 156.63,74.76C156.74,74.8 156.83,74.86 156.9,74.95L157.66,75.95L158.29,76.77C158.38,76.87 158.42,77 158.41,77.13C158.4,77.26 158.34,77.38 158.24,77.47C158.14,77.55 158.01,77.59 157.88,77.58C157.75,77.57 157.63,77.51 157.54,77.41L156.33,76.12C156.33,76.12 155.84,76.42 155.77,76.48C155.7,76.54 152.22,79.23 151.77,79.63C152,80.71 154,87.58 154.71,90C154.71,90 156.62,95.36 156.62,97.57"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.06,73.08C153.06,73.08 156.23,71.15 156.86,71.15C157.19,71.15 157.86,71.86 157.86,71.86"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.8,74.34C153.8,74.34 156.48,72.49 156.8,72.64C157.65,73.35 158.43,74.14 159.13,75"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M154.71,75.48L156.25,74.71"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
</vector>
