/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RoundLineView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.engine.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import com.oplus.gallery.videoeditorpage.R;

public class RoundLineView extends View {
    private static final float RECT_SIZE = 2F;
    private float mLineHeight;
    private Paint mPaint = new Paint();
    private RectF mLeftRectF = new RectF();
    private RectF mRightRectF = new RectF();
    private int mColorId = R.color.editor_caption_select_line_color;
    private boolean mIsDrawLeftRoundRect = true;

    public RoundLineView(Context context, float lineHeight) {
        super(context);
        mLineHeight = lineHeight;
    }

    public RoundLineView(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.RoundLineView);
        try {
            mLineHeight = a.getDimensionPixelSize(R.styleable.RoundLineView_line_height, 0);
        } finally {
            a.recycle();
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mPaint.setAntiAlias(true);
        mPaint.setColor(getContext().getColor(mColorId));

        mLeftRectF.left = 0;
        mLeftRectF.top = 0;
        mLeftRectF.right = mLineHeight;
        mLeftRectF.bottom = getHeight();

        mRightRectF.left = mLineHeight;
        mRightRectF.top = (getHeight() / RECT_SIZE) - mLineHeight / 2;
        mRightRectF.right = getWidth();
        mRightRectF.bottom = (getHeight() / RECT_SIZE) + mLineHeight / 2;

        if (mIsDrawLeftRoundRect) {
            canvas.drawRoundRect(mLeftRectF, mLineHeight / 2, mLineHeight / 2, mPaint);
        }
        canvas.drawRoundRect(mRightRectF, mLineHeight / 2, mLineHeight / 2, mPaint);
    }

    public void setBoundLineColor(int colorId) {
        this.mColorId = colorId;
        postInvalidate();
    }

    public void setIsDrawLeftRoundRect(boolean mIsDrawLeftRoundRect) {
        this.mIsDrawLeftRoundRect = mIsDrawLeftRoundRect;
        postInvalidate();
    }
}
