/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamVideoTrack.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video;

import android.text.TextUtils;
import android.util.Size;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.meicam.sdk.NvsPanAndScan;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.data.BaseKeyFrameInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.params.SpeederParams;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.transition.BaseTransition;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.transition.MeicamVideoTransition;
import com.oplus.gallery.videoeditorpage.video.business.music.volume.entity.Volume;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 视频轨道类，拥有对视频片段的编辑能力，添加、删除、移动片段，获取片段列表等。
 */
public class MeicamVideoTrack implements IVideoTrack {
    private static final String TAG = "MeicamVideoTrack";
    private static final String TAIL_CLIP_PATH = "assets:/tail.png";
    private static final int TAIL_CLIP_TYPE = 1;
    private static final int FIRST_CLIP_INDEX = 0;
    private static final int SECOND_CLIP_INDEX = 1;
    private transient NvsVideoTrack mNvsVideoTrack;
    private ArrayList<MeicamVideoClip> mVideoClipList;
    private HashMap<Integer, MeicamVideoTransition> mTransitionMap;
    private Volume mVolume;
    private boolean mIsContainImage;
    private float mCurMakeRatio;
    private boolean mIsApplyBackgroundToAll = true;
    private boolean mMuted = false; //标志当前是否为静音，默认为false

    public MeicamVideoTrack() {
        mVideoClipList = new ArrayList<>();
        mTransitionMap = new HashMap<>();
        mVolume = new Volume();
    }

    public NvsVideoTrack getNvsVideoTrack() {
        return mNvsVideoTrack;
    }

    public void setCurMakeRatio(float curMakeRatio) {
        this.mCurMakeRatio = curMakeRatio;
    }

    @Override
    public void bindNvsObject(NvsVideoTrack nvsVideoTrack) {
        mNvsVideoTrack = nvsVideoTrack;
        setMuted(mMuted);
    }

    public void clearNvsObjects() {
        for (MeicamVideoClip clip : mVideoClipList) {
            clip.clearNvsObjects();
        }
    }

    public void changeInPoint(int index, long inPoint) {
        if (mNvsVideoTrack != null) {
            mNvsVideoTrack.changeInPoint(index, inPoint);
        }
    }

    @Override
    @SuppressWarnings("ParameterNumber")
    public IVideoClip appendVideoClip(String path, String srcFilePath, int mediaType, boolean needConvert,
                                      long trimInTime, long trimOutTime, String fileSystemPath) {
        return appendVideoClip(path, srcFilePath, mediaType, needConvert, trimInTime, trimOutTime, 0, 0, fileSystemPath);
    }

    @Override
    @SuppressWarnings("ParameterNumber")
    public IVideoClip appendVideoClip(String path, String srcFilePath, int mediaType, boolean needConvert,
                                      long trimInTime, long trimOutTime, int width, int height, String fileSystemPath) {
        if (TextUtils.isEmpty(path) || (trimInTime < 0) || (trimOutTime <= trimInTime)) {
            GLog.e(TAG, "appendVideoClip exception" + ", in:" + trimInTime
                    + ", out:" + trimOutTime + ", path:" + path);
            return null;
        }
        IVideoClip meicamVideoClip = insertVideoClip(mVideoClipList.size(), path, srcFilePath, mediaType,
            needConvert, trimInTime, trimOutTime, width, height, fileSystemPath);
        return meicamVideoClip;
    }

    @Override
    public IVideoClip appendVideoClip(@NonNull IVideoClip videoClip) {
        IVideoClip newVideoClip = addVideoClip(videoClip.getFilePath(), videoClip.getSrcFilePath(),
            videoClip.getVideoType(), videoClip.needConvert(), videoClip.getTrimIn(),
            videoClip.getTrimOut(), videoClip.getWidth(), videoClip.getHeight(), videoClip.getFileSystemPath(), videoClip.getInPoint());
        if (newVideoClip == null) {
            GLog.e(TAG, "appendVideoClip: newVideoClip is null");
            return null;
        }
        newVideoClip.setTag(videoClip.getTag());
        newVideoClip.setAICaptionId(videoClip.getAICaptionId());
        newVideoClip.setExtraVideoRotation(videoClip.getExtraVideoRotation());
        boolean isMainTrack = (videoClip.getTrackIndex() == 0);
        newVideoClip.setPanAndScan(videoClip.getPan(), videoClip.getScan(), isMainTrack);
        newVideoClip.setPhotoAutoRect(videoClip.getPhotoAutoRect());
        newVideoClip.setRequestTrimInTime(videoClip.getRequestTrimInTime());
        newVideoClip.setRequestTrimOutTime(videoClip.getRequestTrimOutTime());
        newVideoClip.setReversePlay(videoClip.isReversePlay());
        newVideoClip.setImageMotionMode(videoClip.getImageMotionMode());
        newVideoClip.setImageMotionAnimationEnabled(videoClip.getImageMotionAnimationEnabled());
        newVideoClip.setClipType(videoClip.getClipType());
        newVideoClip.setImageMotionROI(videoClip.getStartROI(), videoClip.getEndROI());
        newVideoClip.setSpeed(videoClip.getSpeed());
        newVideoClip.setBlendingMode(videoClip.getBlendingMode());
        newVideoClip.setOpacity(videoClip.getOpacity());
        newVideoClip.setTrackIndex(videoClip.getTrackIndex());
        newVideoClip.setInAuxiliaryStreamingContext(videoClip.isInAuxiliaryStreamingContext());
        Size size = videoClip.getCuttedTailorSize();
        if (size != null) {
            newVideoClip.setCuttedTailorSize(size.getWidth(), size.getHeight());
        }
        Volume volumeGain = videoClip.getVolumeGain();
        if (volumeGain != null) {
            newVideoClip.setVolumeGain(volumeGain.getLeftVolume(), volumeGain.getRightVolume());
        }
        newVideoClip.setMuted(videoClip.isMuted());
        GLog.d(TAG, "appendVideoClip: newVideoClip is muted : " + newVideoClip.isMuted());

        copyEffectList(videoClip, newVideoClip);
        return newVideoClip;
    }

    private void copyEffectList(@NonNull IVideoClip videoClip, @NonNull IVideoClip newVideoClip) {
        List<BaseVideoClipEffect> effectList = videoClip.getEffectList();
        for (BaseVideoClipEffect effect : effectList) {
            newVideoClip.appendEffect(effect);
            if (effect.getName().equals(StreamingConstant.VideoTransform.TRANSFORM_2D)) {
                if (effect.hasKeyframe()) {
                    BaseVideoClipEffect newEffect = newVideoClip.getEffect(StreamingConstant.VideoTransform.TRANSFORM_2D);
                    if (newEffect == null) {
                        continue;
                    }
                    newEffect.removeAllKeyframe();
                    HashMap<String, HashMap<Long, Float>> timeFloatParams = effect.getTimeFloatParams();
                    for (Map.Entry<String, HashMap<Long, Float>> entry : timeFloatParams.entrySet()) {
                        HashMap<Long, Float> timeMap = entry.getValue();
                        for (Map.Entry<Long, Float> timeEntry : timeMap.entrySet()) {
                            newEffect.setFloatValAtTime(entry.getKey(), timeEntry.getValue(), timeEntry.getKey());
                        }
                    }
                } else {
                    float scale = effect.getFloatValue(StreamingConstant.VideoTransform.PARAM_SCALE_X);
                    float degree = effect.getFloatValue(StreamingConstant.VideoTransform.PARAM_ROTATION);
                    float tranX = effect.getFloatValue(StreamingConstant.VideoTransform.PARAM_TRANS_X);
                    float tranY = effect.getFloatValue(StreamingConstant.VideoTransform.PARAM_TRANS_Y);
                    newVideoClip.setTransformEffectValue(scale, degree, tranX, tranY);
                }
            } else if (TextUtils.equals(StreamingConstant.MaskFx.MASKFX_NAME, effect.getName())) {
                effect.setFilterMask(true);
                effect.setRegional(effect.isRegional());
                effect.setIgnoreBackground(effect.getIgnoreBackground());
                effect.setInverseRegion(effect.isInverseRegion());
                effect.setRegionInfo(effect.getRegionInfo());
                effect.setRegionalFeatherWidth(effect.getRegionalFeatherWidth());
            }
        }

        // 复制 Raw 特效
        List<BaseVideoClipEffect> rawEffectList = videoClip.getRawEffectList();
        for (BaseVideoClipEffect effect : rawEffectList) {
            newVideoClip.appendRawEffect(effect);
        }

        // 复制关键帧
        List<BaseKeyFrameInfo> keyFrameList = videoClip.getKeyFrameList();
        for (BaseKeyFrameInfo keyFrame : keyFrameList) {
            newVideoClip.addKeyFrame(keyFrame);
        }
    }

    @Override
    public int getUserClipCount() {
        int num = 0;
        for (int i = 0; i < mVideoClipList.size(); i++) {
            if (mVideoClipList.get(i).getClipType() == IVideoClip.ClipType.TYPE_USER_CLIP) {
                num++;
            }
        }
        return num;
    }

    @Override
    @SuppressWarnings("ParameterNumber")
    public IVideoClip insertVideoClip(int index, String path, String srcFilePath, int mediaType, boolean needConvert, long trimInTime,
                                      long trimOutTime, int width, int height, String fileSystemPath) {
        if (TextUtils.isEmpty(path) || (trimInTime < 0) || (trimOutTime <= trimInTime)) {
            GLog.e(TAG, "insertVideoClip exception" + ", in:" + trimInTime
                    + ", out:" + trimOutTime + ", path:" + path);
            return null;
        }
        MeicamVideoClip meicamVideoClip = new MeicamVideoClip(path, srcFilePath,
            mediaType, needConvert, trimInTime, trimOutTime, width, height, fileSystemPath);
        int index1 = index;
        if (index > mVideoClipList.size()) {
            GLog.e(TAG, "insert  error index that is large to timeline's size, has change to insert the timeline's end");
            index1 = mVideoClipList.size();
        }
        if (index < 0) {
            GLog.e(TAG, "insert  error index that is smaller to 0, has change to insert the timeline's head");
            index1 = 0;
        }
        if (hasNvsObject()) {
            NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
            if (streamingContext != null) {
                streamingContext.stop(NvsStreamingContext.STREAMING_ENGINE_INTERRUPT_STOP);
            }
            NvsVideoClip nvsVideoClip = mNvsVideoTrack.insertClip(path, trimInTime, trimOutTime, index1);
            if (nvsVideoClip == null) {
                GLog.e(TAG, "nvsVideoClip == null:" + path
                        + ",in"
                        + trimInTime
                        + ",out"
                        + trimOutTime);
                return null;
            }
            if (nvsVideoClip.getVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) {
                setContainImage(true);
            }
            GLog.d(TAG, "insertVideoClip mNvsVideoTrack clipCount: " + mNvsVideoTrack.getClipCount());
            meicamVideoClip.bindNvsObject(nvsVideoClip);

            // Do not add transition by default
            if (nvsVideoClip.getIndex() > 0) {
                mNvsVideoTrack.setBuiltinTransition(nvsVideoClip.getIndex() - 1, "");
            }
        }

        mVideoClipList.add(index1, meicamVideoClip);
        long duration = meicamVideoClip.getDuration();
        for (int i = index1 + 1; i < mVideoClipList.size(); i++) {
            MeicamVideoClip videoClip = mVideoClipList.get(i);
            if (videoClip == null) {
                continue;
            }
            videoClip.setInPoint(videoClip.getInPoint() + duration);
        }
        meicamVideoClip.setMuted(isMuted());
        GLog.d(TAG, "insertVideoClip: meicamVideoClip is muted : " + meicamVideoClip.isMuted());
        // Apply same background as existing clips if needed
        applyBackgroundToNewClip(meicamVideoClip, index1);
        return meicamVideoClip;
    }

    @Override
    @SuppressWarnings("ParameterNumber")
    public IVideoClip addVideoClip(String path, String srcFilePath, int mediaType, boolean needConvert, long trimInTime, long trimOutTime,
                                   int width, int height, String fileSystemPath, long inPoint) {
        if (TextUtils.isEmpty(path) || (trimInTime < 0) || (trimOutTime <= trimInTime)
                || (inPoint < 0)) {
            GLog.e(TAG, "addVideoClip exception" + ", in:" + trimInTime
                    + ", out:" + trimOutTime + ", path:" + path + " inPoint: " + inPoint);
            return null;
        }

        MeicamVideoClip meicamVideoClip = new MeicamVideoClip(path, srcFilePath, mediaType, needConvert, trimInTime, trimOutTime,
            width, height, fileSystemPath, inPoint);

        int index = mVideoClipList.size();
        for (int i = 0; i < mVideoClipList.size(); i++) {
            MeicamVideoClip clip = mVideoClipList.get(i);
            if (clip.getInPoint() >= inPoint) {
                index = i;
                break;
            }
        }
        if (index < mVideoClipList.size()) {
            mVideoClipList.add(index, meicamVideoClip);
        } else {
            mVideoClipList.add(meicamVideoClip);
        }

        if (hasNvsObject()) {
            NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
            if (streamingContext != null) {
                streamingContext.stop(NvsStreamingContext.STREAMING_ENGINE_INTERRUPT_STOP);
            }
            NvsVideoClip nvsVideoClip = mNvsVideoTrack.addClip(path, inPoint, trimInTime, trimOutTime);
            if (nvsVideoClip == null) {
                GLog.e(TAG, "nvsAudioClip == null:" + path
                        + ",inPoint="
                        + inPoint
                        + ",in="
                        + trimInTime
                        + ",out="
                        + trimOutTime);
                return null;
            }
            if (nvsVideoClip.getVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) {
                setContainImage(true);
            }
            GLog.d(TAG, "addVideoClip mNvsVideoTrack clipCount: " + mNvsVideoTrack.getClipCount());
            meicamVideoClip.bindNvsObject(nvsVideoClip);

            // Do not add transition by default
            if (nvsVideoClip.getIndex() > 0) {
                mNvsVideoTrack.setBuiltinTransition(nvsVideoClip.getIndex() - 1, "");
            }
        }
        meicamVideoClip.setMuted(isMuted());
        GLog.d(TAG, "addVideoClip: meicamVideoClip is muted : " + meicamVideoClip.isMuted());
        // Apply same background as existing clips if needed
        applyBackgroundToNewClip(meicamVideoClip, index);
        return meicamVideoClip;
    }

    @Override
    public long getDuration() {
        long result = 0;
        for (MeicamVideoClip videoClip : mVideoClipList) {
            result += videoClip.getDuration();
        }
        return result;
    }

    public boolean isContainImage() {
        if (!mIsContainImage) {
            checkContainImage();
        }
        return mIsContainImage;
    }

    public void setContainImage(boolean containImage) {
        this.mIsContainImage = containImage;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<IVideoClip> getClipList() {
        return (List<IVideoClip>) mVideoClipList.clone();
    }

    @Override
    public int getClipCount() {
        return mVideoClipList.size();
    }

    @Override
    public int getType() {
        return StreamingConstant.TrackType.TRACK_TYPE_VIDEO;
    }

    @Override
    public long setInPoint(int clipIndex, long newInPoint) {
        return -1;
    }

    @Override
    public long setOutPoint(int clipIndex, long newOutPoint) {
        return -1;
    }

    @Override
    public boolean splitClip(int clipIndex, long splitPoint, boolean needPicMove) {
        if ((clipIndex < 0) || (clipIndex >= mVideoClipList.size())) {
            GLog.e(TAG, "splitClip error: current index is invalid!");
            return false;
        }

        MeicamVideoClip videoClip = mVideoClipList.get(clipIndex);
        if (videoClip == null) {
            GLog.e(TAG, "splitClip error: current clip is null!");
            return false;
        }
        NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
        if (streamingContext != null) {
            streamingContext.stop(NvsStreamingContext.STREAMING_ENGINE_INTERRUPT_STOP);
        }
        //now the clip index is same with NvsVideoTrack, if it is not same, we must be modify it
        boolean bSucceeded = mNvsVideoTrack.splitClip(clipIndex, splitPoint);
        if (!bSucceeded) {
            GLog.e(TAG, "splitClip failed!");
            return false;
        }

        NvsVideoClip nvsVideoClip = mNvsVideoTrack.getClipByIndex(clipIndex);
        if (nvsVideoClip == null) {
            GLog.e(TAG, "splitClip error: nvsclip is null!");
            return false;
        }
        videoClip.setSpeederParams(getSpeederParams(videoClip.getSpeederParams().getSpeedDesc(), nvsVideoClip), false);

        NvsVideoClip newNvsVideoClip = mNvsVideoTrack.getClipByIndex(clipIndex + 1);
        if (newNvsVideoClip == null) {
            GLog.e(TAG, "splitClip error: new nvsclip is null!");
            return false;
        }

        MeicamVideoTransition transition = null;
        if (mTransitionMap.containsKey(clipIndex)) {
            transition = mTransitionMap.get(clipIndex);
            mTransitionMap.remove(clipIndex);
        }

        //modify clip trim out
        videoClip.setTrimOutPoint(nvsVideoClip.getTrimOut(), true);
        videoClip.bindNvsObject(nvsVideoClip);

        // To the end
        int endClipIndex = -1;
        // Transition index increase 1
        int moveOffset = 1;
        displaceTransition(clipIndex, endClipIndex, moveOffset);
        if (transition != null) {
            mTransitionMap.put(clipIndex + 1, transition);
        }

        MeicamVideoClip newClip = setSplitedNewClip(videoClip, nvsVideoClip, newNvsVideoClip, needPicMove);
        mVideoClipList.add(clipIndex + 1, newClip);

        newClip.setSpeederParams(getSpeederParams(videoClip.getSpeederParams().getSpeedDesc(), newNvsVideoClip), false);
        return true;
    }

    /**
     *  获取视频片段的变速参数
     * @param speedDesc 变速模板描述
     * @param nvsVideoClip 美摄的视频片段对象
     * @return
     */
    private SpeederParams getSpeederParams(String speedDesc, NvsVideoClip nvsVideoClip) {
        return TextUtils.isEmpty(nvsVideoClip.getClipVariableSpeedCurvesString())
                ? SpeederParams.modeLinear(nvsVideoClip.getSpeed(), nvsVideoClip.isKeepAudioPitch())
                : SpeederParams.modeCurve(nvsVideoClip.getClipVariableSpeedCurvesString(), speedDesc, nvsVideoClip.isKeepAudioPitch());
    }

    private MeicamVideoClip setSplitedNewClip(
        @NonNull MeicamVideoClip videoClip,
        @NonNull NvsVideoClip nvsVideoClip,
        @NonNull NvsVideoClip newNvsVideoClip,
        boolean needPicMove
    ) {
        NvsPanAndScan panAndScan = nvsVideoClip.getPanAndScan();
        Volume vol = videoClip.getVolumeGain();

        //set new nvs video clip info
        newNvsVideoClip.setExtraVideoRotation(videoClip.getExtraVideoRotation());
        if (panAndScan != null) {
            GLog.d(TAG, "splitClip,setPanAndScan");
            newNvsVideoClip.setPanAndScan(panAndScan.pan, panAndScan.scan);
        }
        if (vol != null) {
            newNvsVideoClip.setVolumeGain(vol.getLeftVolume(), vol.getRightVolume());
        }

        //create new clip
        MeicamVideoClip newClip = (MeicamVideoClip) videoClip.clone();
        newClip.setMuted(videoClip.isMuted());
        GLog.d(TAG, "splitClip: newClip is muted : " + newClip.isMuted());
        // Apply same background as source clip
        newClip.setBackground(videoClip.getBackground());
        newClip.setTrimInPoint(newNvsVideoClip.getTrimIn(), true);
        newClip.setTrimOutPoint(newNvsVideoClip.getTrimOut(), true);

        newClip.bindNvsObject(newNvsVideoClip);
        if (videoClip.getVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) {
            videoClip.addImageMove(mCurMakeRatio, videoClip.getDuration(), needPicMove);
            if ((newClip.getTrimOut() - videoClip.getTrimOut()) <= 0) {
                newClip.addNewImageMove(mCurMakeRatio, 0, needPicMove);
            } else {
                newClip.addNewImageMove(mCurMakeRatio, newClip.getDuration(), needPicMove);
            }
        }

        List<BaseVideoClipEffect> effectlist = newClip.getEffectList();
        for (BaseVideoClipEffect effect : effectlist) {
            newClip.removeEffect(effect);
        }
        for (BaseVideoClipEffect effect : effectlist) {
            newClip.appendEffect(effect);
        }

        List<BaseVideoClipEffect> effectlistRaw = newClip.getRawEffectList();
        for (BaseVideoClipEffect effect : effectlistRaw) {
            newClip.removeRawEffect(effect);
        }
        for (BaseVideoClipEffect effect : effectlistRaw) {
            newClip.appendRawEffect(effect);
        }

        return newClip;
    }

    @Override
    public boolean removeClip(int clipIndex, boolean keepSpace) {
        if ((clipIndex < 0) || (clipIndex >= mVideoClipList.size())) {
            GLog.e(TAG, "removeClip error: current index is invalid!" + clipIndex + "  " + mVideoClipList.size());
            return false;
        }

        if (!keepSpace) {
            MeicamVideoClip meicamVideoClip = mVideoClipList.get(clipIndex);
            long duration = meicamVideoClip.getDuration();
            for (int i = clipIndex + 1; i < mVideoClipList.size(); i++) {
                MeicamVideoClip videoClip = mVideoClipList.get(i);
                if (videoClip == null) {
                    continue;
                }
                videoClip.setInPoint(videoClip.getInPoint() - duration);
            }
        }

        mVideoClipList.remove(clipIndex);
        mTransitionMap.remove(clipIndex - 1);
        mTransitionMap.remove(clipIndex);
        int startClipIndex = clipIndex + 1;
        // To the end
        int endClipIndex = -1;
        // Transition index decrease 1
        int moveOffset = -1;
        displaceTransition(startClipIndex, endClipIndex, moveOffset);
        if (hasNvsObject()) {
            NvsStreamingContext streamingContext = EditorEngineGlobalContext.getInstance().getContext();
            if (streamingContext != null) {
                streamingContext.stop(NvsStreamingContext.STREAMING_ENGINE_INTERRUPT_STOP);
            }
            if (!mNvsVideoTrack.removeClip(clipIndex, keepSpace)) {
                GLog.e(TAG, "removeClip error: clipIndex is " + clipIndex);
                return false;
            }

            // Do not add transition by default
            if (clipIndex > 0) {
                mNvsVideoTrack.setBuiltinTransition(clipIndex - 1, "");
            }
        }

        checkContainImage();
        return true;
    }

    private boolean checkContainImage() {
        boolean containImage = false;
        for (MeicamVideoClip meicamVideoClip : mVideoClipList) {
            if (meicamVideoClip.getVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) {
                containImage = true;
                break;
            }
        }
        setContainImage(containImage);
        return containImage;
    }

    @Override
    public boolean moveClip(int clipIndex, int destClipIndex) {
        if ((clipIndex < 0) || (clipIndex >= mVideoClipList.size())
                || (destClipIndex < 0) || (destClipIndex >= mVideoClipList.size())) {
            GLog.e(TAG, "index is invalid");
            return false;
        }

        MeicamVideoClip clip = mVideoClipList.remove(clipIndex);
        mVideoClipList.add(destClipIndex, clip);

        return true;
    }

    @Override
    public boolean removeAllClips() {
        mVideoClipList.clear();
        mTransitionMap.clear();
        setContainImage(false);
        if (hasNvsObject()) {
            return mNvsVideoTrack.removeAllClips();
        }
        return true;
    }

    @Override
    public IClip getClip(int clipIndex) {
        if ((clipIndex < 0) || (clipIndex >= mVideoClipList.size())) {
            GLog.e(TAG, "get video clip error: index is invalid!  clipIndex: " + clipIndex + "  mVideoClipList.size: " + mVideoClipList.size());
            return null;
        }

        return mVideoClipList.get(clipIndex);
    }

    @Override
    public void setTransition(int srcClipIndex, BaseTransition transition) {
        if (transition == null) {
            removeTransition(srcClipIndex);
            return;
        }

        if (!(transition instanceof MeicamVideoTransition)) {
            GLog.e(TAG, "addTransition not instance of MeicamVideoTransition : " + transition);
            return;
        }
        MeicamVideoTransition meicamVideoTransition = (MeicamVideoTransition) transition;
        mTransitionMap.put(srcClipIndex, meicamVideoTransition);

        if (hasNvsObject()) {
            switch (meicamVideoTransition.getType()) {
                case MeicamVideoTransition.TYPE_PACKAGED_FX:
                    mNvsVideoTrack.setPackagedTransition(srcClipIndex, meicamVideoTransition.getName());
                    break;
                case MeicamVideoTransition.TYPE_BUILT_IN_FX:
                    mNvsVideoTrack.setBuiltinTransition(srcClipIndex, meicamVideoTransition.getName());
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public Map<Integer, MeicamVideoTransition> getTransitionMap() {
        return mTransitionMap;
    }

    @Override
    public BaseTransition getTransition(int srcClipIndex) {
        return mTransitionMap.get(srcClipIndex);
    }

    @Override
    public void removeTransition(int srcClipIndex) {
        mTransitionMap.remove(srcClipIndex);
        if (hasNvsObject()) {
            mNvsVideoTrack.setPackagedTransition(srcClipIndex, null);
        }
    }

    @Override
    public void removeAllTransitions() {
        mTransitionMap.clear();
        int clipCount = getClipCount();
        if (mNvsVideoTrack != null) {
            for (int i = 0; i < clipCount; i++) {
                mNvsVideoTrack.setPackagedTransition(i, null);
            }
        }
    }

    @Override
    public void setVolumeGain(float leftVolumeGain, float rightVolumeGain) {
        this.mVolume = new Volume(leftVolumeGain, rightVolumeGain);
        this.mMuted = (leftVolumeGain == Volume.VOLUME_MUTE_VALUE);
        if (mNvsVideoTrack == null) {
            GLog.e(TAG, "Call setVolumeGain error: mNvsVideoTrack is null.");
            return;
        }
        mNvsVideoTrack.setVolumeGain(mVolume.getLeftVolume(), mVolume.getRightVolume());
    }

    @Override
    public Volume getVolumeGain() {
        return this.mVolume;
    }

    @Override
    public boolean isMuted() {
        return mMuted;
    }

    @Override
    public void setMuted(boolean muted) {
        mMuted = muted;
        mVolume.setMute(muted);
        if (mNvsVideoTrack == null) {
            GLog.e(TAG, "Call setMuted error: mNvsVideoTrack is null.");
            return;
        }
        mNvsVideoTrack.setVolumeGain(mVolume.getLeftVolume(), mVolume.getRightVolume());
    }

    @Override
    public IVideoTrack clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamVideoTrack result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamVideoTrack();
        }

        return result;
    }

    @Override
    public IVideoClip getClipByTimelinePostion(long timePosition) {
        if (!hasNvsObject()) {
            return null;
        }

        if (mVideoClipList.isEmpty() || (timePosition < 0)) {
            return null;
        }

        long duration = 0;
        for (MeicamVideoClip clip : mVideoClipList) {
            if (clip == null) {
                continue;
            }
            duration += clip.getDuration();
            if (duration > timePosition) {
                return clip;
            }
        }

        MeicamVideoClip clip = mVideoClipList.get(mVideoClipList.size() - 1);
        return clip;
    }

    @Override
    public int getClipIndex(IVideoClip videoClip) {
        return mVideoClipList.indexOf(videoClip);
    }

    @Override
    public long getClipInPoint(int index) {
        if ((index < 0) || (index > mVideoClipList.size())) {
            GLog.e(TAG, "getClipInPoint err, index = " + index + "count = " + getClipCount());
            return 0;
        }
        long result = 0;
        for (int i = 0; i < index; i++) {
            result += mVideoClipList.get(i).getDuration();
        }
        return result;
    }

    protected boolean hasNvsObject() {
        return mNvsVideoTrack != null;
    }

    private boolean displaceTransition(int startClipIndex, int endClipIndex, int delta) {
        int endIndex = endClipIndex;
        if (endClipIndex < 0) {
            endIndex = Integer.MAX_VALUE;
        }

        HashMap<Integer, MeicamVideoTransition> tempMap = new HashMap<>();

        Integer key = null;
        MeicamVideoTransition value = null;
        Iterator iter = mTransitionMap.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry entry = (Map.Entry) iter.next();
            key = (Integer) entry.getKey();
            value = (MeicamVideoTransition) entry.getValue();

            if ((key.intValue() >= startClipIndex) && (key.intValue() < endIndex)) {
                tempMap.put(key + delta, value);
                iter.remove();
            }
        }
        mTransitionMap.putAll(tempMap);
        return true;
    }

    private void applyBackgroundToNewClip(MeicamVideoClip newClip, int insertIndex) {
        if (mIsApplyBackgroundToAll && !mVideoClipList.isEmpty()) {
            int refIndex = (insertIndex == FIRST_CLIP_INDEX && mVideoClipList.size() > 1) ? SECOND_CLIP_INDEX : FIRST_CLIP_INDEX;
            newClip.setBackground(mVideoClipList.get(refIndex).getBackground());
        }
    }

    @Override
    public int getTransitionCount() {
        return Math.max(0, getClipCount() - 1);
    }

    @Override
    public List<IClip> getIClipList() {
        return (List<IClip>) mVideoClipList.clone();
    }
}
