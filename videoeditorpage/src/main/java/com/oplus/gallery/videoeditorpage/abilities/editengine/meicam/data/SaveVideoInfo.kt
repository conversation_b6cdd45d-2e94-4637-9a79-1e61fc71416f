/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SaveVideoInfo.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data

/**
 * 客制化视频信息类 目前只有壁纸使用
 */
data class SaveVideoInfo(
    /**
     * 视频uri
     */
    val stringUri: String,
    /**
     * 视频保存期望宽度
     */
    val width: Int,
    /**
     * 视频保存期望高度
     */
    val height: Int,
    /**
     * 视频保存期望边长
     * 宽 > 高 时，期望边长为宽
     * 宽 < 高 时，期望边长为高
     */
    val length: Int,
    /**
     * 视频创建时间
     */
    val creationTime: Long,
    /**
     * 视频帧率
     */
    val frameRate: Int,
    /**
     * 视频码率
     */
    val bitRate: Int,
    /**
     * 视频编码格式
     * 美摄目前支持
     * "hevc"(h.265)
     * "vp8"(audio格式必须是vorbis，文件格式必须是Webm)
     * default(h.264)
     */
    val codecFormat: String?,
    /**
     * 保存视频时是否保持HDR
     */
    val isKeepHdr: Boolean,
)
