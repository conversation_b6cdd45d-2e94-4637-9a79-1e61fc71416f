/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : Block.kt
 ** Description : Block which contains fixed amount items for block cache.
 ** Version     : 1.0
 ** Date        : 2020/09/20
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery3D             2020/09/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cache.diskcache

import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import java.io.Serializable
import java.util.*

class Block(
    val dateKey: Int,
    val indexInNode: Int, // block index in node
    val maxSize: Int,
    val isSupportLossLess: Boolean = true
) : Serializable {

    companion object {
        const val PIXEL_COL_MAX = 2
        const val PIXEL_ROW_MAX = 2
        const val INVALID_VALUE = -1
    }

    // map of item key and it's Item
    val itemList = mutableMapOf<Long, Item>()

    private fun containItem(itemIndexInBlock: Int): Boolean {
        itemList.values.forEach { item ->
            if (item.indexInBlock == itemIndexInBlock) return true
        }
        return false
    }

    /**
     * find empty item index from min to max
     */
    fun findEmptyItemIndexInBlock(): Int {
        var nextItemIndex = itemList.size.coerceAtMost(maxSize - 1)
        if (containItem(nextItemIndex)) {
            for (index in 0 until maxSize) {
                if (!containItem(index)) {
                    nextItemIndex = index
                    break
                }
            }
        }
        return nextItemIndex
    }

    fun getItemCount(): Int = itemList.size

    fun addItem(itemKey: Long, item: Item) {
        itemList[itemKey] = item
    }

    fun removeItem(itemKey: Long): Item? = itemList.remove(itemKey)

    fun getItemIndexInBlock(itemKey: Long): Int = itemList[itemKey]?.indexInBlock ?: INVALID_VALUE

    override fun toString(): String {
        return "Block{$dateKey, idx:$indexInNode, mapSize:${itemList.size}}"
    }
}

class Item(
    var indexInBlock: Int = 0,
    val indexInNode: Int,
    val itemKey: Long
) : Serializable {
    var pixels: IntArray = IntArray(Block.PIXEL_COL_MAX * Block.PIXEL_ROW_MAX)

    override fun equals(other: Any?): Boolean {
        return if (other is Item) {
            (this === other) or (itemKey == other.itemKey)
        } else false
    }

    override fun hashCode(): Int = Objects.hash(itemKey)

    override fun toString(): String {
        return "Item{$itemKey, mediaId:${itemKey.toInt()}, idxInBlock:$indexInBlock, idxInNode:$indexInNode}"
    }
}

interface IBlockCallback {
    fun createBlockBitmap(bitmapConfig: Config): Bitmap?
    fun recycleBlockBitmap(block: Block? = null, blockBitmap: Bitmap?)
}
