/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - StoryBoardUtils.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.utlis.Utils;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

public class StoryBoardUtils {

    public static final int ASPECT_RATIO_16_V_9 = 1;
    public static final int ASPECT_RATIO_1_V_1 = 2;
    public static final int ASPECT_RATIO_9_V_16 = 4;
    public static final int ASPECT_RATIO_4_V_3 = 8;
    public static final int ASPECT_RATIO_3_V_4 = 16;
    public static final int ASPECT_RATIO_21_V_9 = 32;
    public static final int ASPECT_RATIO_9_V_21 = 64;

    public static final String STRING_ASPECTRATIO_16V9 = "fx.xml";
    public static final String STRING_ASPECTRATIO_1V1 = "fx1v1.xml";
    public static final String STRING_ASPECTRATIO_9V16 = "fx9v16.xml";
    public static final String STRING_ASPECTRATIO_4V3 = "fx4v3.xml";
    public static final String STRING_ASPECTRATIO_3V4 = "fx3v4.xml";
    public static final String STRING_ASPECTRATIO_21V9 = "fx21v9.xml";
    public static final String STRING_ASPECTRATIO_9V21 = "fx9v21.xml";
    public final static String ASSETS_PATH_HEAD = "assets:/";

    public static final int FX_CARTOON_IN = 0;
    public static final int FX_CARTOON_OUT = 1;
    public static final int FX_CARTOON_COMPOUND = 2;

    //effect node param
    public static final String VALUE_TRANS_X = "transX";
    public static final String VALUE_RADIUS = "radius";
    public static final String VALUE_TRANS_Y = "transY";
    public static final String VALUE_SCALE_X = "scaleX";
    public static final String VALUE_SCALE_Y = "scaleY";
    public static final String VALUE_ROTATION_Z = "rotationZ";
    public static final String VALUE_TRACK_TAG = "<track";

    private static final float ONE_VALUE = 1.0F;
    private static final float SIZE_DELTA = 0.1F;

    private static final AspectRatio ASPECT_RATIO_ARRAY[] = {
        new AspectRatio(ASPECT_RATIO_16_V_9, STRING_ASPECTRATIO_16V9, 16.0f / 9),
        new AspectRatio(ASPECT_RATIO_1_V_1, STRING_ASPECTRATIO_1V1, 1),
        new AspectRatio(ASPECT_RATIO_9_V_16, STRING_ASPECTRATIO_9V16, 9.0f / 16),
        new AspectRatio(ASPECT_RATIO_4_V_3, STRING_ASPECTRATIO_4V3, 4.0f / 3),
        new AspectRatio(ASPECT_RATIO_3_V_4, STRING_ASPECTRATIO_3V4, 3.0f / 4),
        new AspectRatio(ASPECT_RATIO_21_V_9, STRING_ASPECTRATIO_21V9, 21.0f / 9),
        new AspectRatio(ASPECT_RATIO_9_V_21, STRING_ASPECTRATIO_9V21, 9.0f / 21),
    };

    private static final String STRING_ENCODING = "encoding";
    private static final String STRING_ENCODING_FORMAT = "UTF-8";
    private static final String NODE_NAME_VALUE = "value";
    private static final String NODE_NAME_TIME = "time";
    private static final String NODE_NAME_TYPE = "type";
    private static final String NODE_NAME_CHANGEVALUE = "changeValue";
    private static final String NODE_NAME_ANIMATION = "animation";
    private static final String NODE_NAME_EFFECT = "effect";
    private static final String NODE_NAME_CLIPDURATION = "clipDuration";
    private static final String NODE_NAME_NONE = "none";
    private static final String NODE_NAME_TRACK = "track";
    private static final String FLOAT_LIMIT_NUM = "%.4f";
    private static final String NODE_NAME_LAYER = "layer";
    private static final String NODE_NAME_PARAMSET = "paramSet";
    private static final String NODE_NAME_DURATION = "duration";
    private static final String NODE_NAME_YES = "yes";
    private static final String NODE_NAME_CLIPSTART = "clipStart";

    private static final int NUMBER_ONE = 1;
    private static final int NUMBER_TOW = 2;
    private static final int NUMBER_THREE = 3;
    private static final int NUMBER_TEN = 10;
    private static final int NUMBER_HUNDRED = 100;
    private static final float ONE_SECOND = 1000F;
    private static final float TIME_SECOND = 1000000F;
    private static final long CLIP_DURATION = 5 * 60 * 60 * 1000;
    private static final float FX_SIZE = 2.25F;
    private static final String SAVE_STORYBOARD_3D_NAME = "result.xml";
    private static final String FX_STORYBOARD_3D = "Storyboard 3D";

    private static final String FX_FILTER_DIR = "filter";

    private static String TAG = "StoryBoardUtils";

    public static String splitXmlFile(String path, long clipDuration, int fxType, float size) {
        String xmlResult = "";
        if (TextUtils.isEmpty(path)) {
            return xmlResult;
        }

        float scale = NUMBER_ONE;
        if (size > 0) {
            scale = size * NUMBER_TEN;
        }
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document document = null;
        InputStream is = null;
        try {
            DocumentBuilder db = dbf.newDocumentBuilder();
            is = new FileInputStream(new File(path));
            document = db.parse(is);

            NodeList trackList = document.getElementsByTagName(NODE_NAME_TRACK);
            for (int i = 0; i < trackList.getLength(); i++) {
                Node effectNode = trackList.item(i);
                if (effectNode instanceof Element) {
                    if ((fxType == FX_CARTOON_COMPOUND)
                            && (NODE_NAME_TYPE.equals(effectNode.getAttributes().item(NUMBER_ONE).getNodeName()))
                            && (NODE_NAME_NONE.equals(effectNode.getAttributes().item(NUMBER_ONE).getNodeValue()))) {
                        effectNode.getAttributes().item(NUMBER_TOW).setNodeValue(String.valueOf((int) (NUMBER_HUNDRED * scale)));
                    } else {
                        if (NODE_NAME_CLIPDURATION.equals(effectNode.getAttributes().item(NUMBER_TOW).getNodeName())) {
                            effectNode.getAttributes().item(NUMBER_TOW).setNodeValue(CLIP_DURATION + "");
                        }
                    }
                }
            }
            NodeList effectList = document.getElementsByTagName(NODE_NAME_EFFECT);
            for (int i = 0; i < effectList.getLength(); i++) {
                double maxValue = 0;
                Node effectNode = effectList.item(i);
                if ((effectNode.getAttributes() == null) || (effectNode.getAttributes().item(0) == null)) {
                    continue;
                }
                NodeList childNodes = effectNode.getChildNodes();
                for (int j = 0; j < childNodes.getLength(); j++) {
                    Node animationNode = childNodes.item(j);
                    if ((animationNode instanceof Element) && (NODE_NAME_ANIMATION.equals(animationNode.getNodeName()))) {
                        boolean isChangeValue = false;
                        for (int t = 0; t < animationNode.getAttributes().getLength(); t++) {
                            Node attribute = animationNode.getAttributes().item(t);
                            if (NODE_NAME_TYPE.equals(attribute.getNodeName()) && NODE_NAME_CHANGEVALUE.equals(attribute.getNodeValue())) {
                                isChangeValue = true;
                            }
                        }
                        NodeList keyNodes = animationNode.getChildNodes();
                        int k = 0;
                        for (; k < keyNodes.getLength(); k++) {
                            Node keyNode = keyNodes.item(k);
                            if (keyNode instanceof Element) {
                                NamedNodeMap keyMap = keyNode.getAttributes();
                                for (int l = 0; l < keyMap.getLength(); l++) {
                                    Node lastNode = keyMap.item(l);
                                    if (NODE_NAME_TIME.equals(lastNode.getNodeName())) {
                                        String timeStr = lastNode.getNodeValue();
                                        if ((fxType == FX_CARTOON_IN) || (fxType == FX_CARTOON_COMPOUND)) {
                                            timeStr = String.valueOf((int) (Integer.parseInt(timeStr) * scale));
                                        } else if (fxType == FX_CARTOON_OUT) {
                                            timeStr = (int) ((clipDuration - (scale * NUMBER_HUNDRED)) + Integer.parseInt(timeStr) * scale) + "";
                                        }
                                        lastNode.setNodeValue(timeStr);
                                    }
                                    if (isChangeValue && (NODE_NAME_VALUE.equals(lastNode.getNodeName()))) {
                                        String valueStr = lastNode.getNodeValue();
                                        double value = Double.valueOf(valueStr);
                                        if (value > maxValue) {
                                            maxValue = value;
                                        }
                                    }
                                }
                            }
                        }
                        if ((isChangeValue) && (maxValue > 0) && (Math.abs(size - SIZE_DELTA) != 0)) {
                            double systemSize = 1;
                            if (size > 0) {
                                double newSystem = (ONE_VALUE * maxValue) / ((double) size / FX_SIZE);
                                systemSize = newSystem / maxValue;
                            }
                            for (k = 0; k < keyNodes.getLength(); k++) {
                                Node keyNode = keyNodes.item(k);
                                if (keyNode instanceof Element) {
                                    NamedNodeMap keyMap = keyNode.getAttributes();
                                    for (int l = 0; l < keyMap.getLength(); l++) {
                                        Node lastNode = keyMap.item(l);
                                        if (NODE_NAME_VALUE.equals(lastNode.getNodeName())) {
                                            String valueStr = lastNode.getNodeValue();
                                            double oldValue = Double.valueOf(valueStr);
                                            double newValue = oldValue * systemSize;
                                            String newValueStr = String.format(FLOAT_LIMIT_NUM, Math.max(newValue, 0));
                                            lastNode.setNodeValue(newValueStr);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "splitXmlFile, fail:" + e.getMessage());
        } finally {
            Utils.closeSilently(is);
        }

        if (document == null) {
            return xmlResult;
        }

        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = null;
        try {
            transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(STRING_ENCODING, STRING_ENCODING_FORMAT);
            StringWriter stringWriter = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(stringWriter));
            xmlResult = stringWriter.toString();
        } catch (TransformerException e) {
            GLog.e(TAG, LogFlag.DL, "splitXmlFile, fail:" + e.getMessage());
        }
        return xmlResult;
    }

    public static String split3DXmlFile(String path, long clipDuration, int fxType, float size) {
        String xmlResultPath = "";
        if (TextUtils.isEmpty(path)) {
            return xmlResultPath;
        }

        float scale = ONE_VALUE;
        if (size > 0) {
            scale = size * NUMBER_TEN;
        }
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document document = null;
        InputStream is = null;
        try {
            DocumentBuilder db = dbf.newDocumentBuilder();
            is = new FileInputStream(new File(path));
            document = db.parse(is);

            Element root = document.getDocumentElement();
            NamedNodeMap nodeMap = root.getAttributes();
            for (int a = 0; a < nodeMap.getLength(); a++) {
                Node node = nodeMap.item(a);
                if (NODE_NAME_DURATION.equals(node.getNodeName())) {
                    node.setNodeValue((int) (NUMBER_HUNDRED * scale) + "");
                    break;
                }
            }

            NodeList layerList = document.getElementsByTagName(NODE_NAME_LAYER);
            for (int i = 0; i < layerList.getLength(); i++) {
                Node layerNode = layerList.item(i);
                if (layerNode instanceof Element) {
                    if ((fxType == FX_CARTOON_COMPOUND)
                            && (NODE_NAME_TYPE.equals(layerNode.getAttributes().item(NUMBER_ONE).getNodeName()))
                            && (NODE_NAME_NONE.equals(layerNode.getAttributes().item(NUMBER_ONE).getNodeValue()))) {

                        NodeList childLayerList = layerNode.getChildNodes();
                        for (int z = 0; z < childLayerList.getLength(); z++) {
                            Node childLayerNode = childLayerList.item(z);
                            if (NODE_NAME_PARAMSET.equals(childLayerNode.getNodeName())) {
                                NodeList paramNodes = childLayerNode.getChildNodes();
                                for (int y = 0; y < paramNodes.getLength(); y++) {
                                    Node paramNode = paramNodes.item(y);
                                    if (NODE_NAME_ANIMATION.equals(paramNode.getNodeName())) {
                                        NodeList childAnimationNodes = paramNode.getChildNodes();
                                        int tag = 0;
                                        for (int x = childAnimationNodes.getLength() - NUMBER_ONE; x > 0; x--) {
                                            Node anmiationNode = childAnimationNodes.item(x);
                                            if (anmiationNode instanceof Element) {
                                                NamedNodeMap aNode = anmiationNode.getAttributes();
                                                if (NODE_NAME_TIME.equals(aNode.item(0).getNodeName())) {
                                                    aNode.item(0).setNodeValue(String.valueOf((int) ((NUMBER_HUNDRED * scale)) - tag));
                                                }
                                                tag++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }


            NodeList animationList = document.getElementsByTagName(NODE_NAME_ANIMATION);
            for (int an = 0; an < animationList.getLength(); an++) {
                Node animationNode = animationList.item(an);
                if (animationNode instanceof Element) {
                    if (setNoneTransformOpacity(animationNode, animationNode.getChildNodes(), fxType, scale)) {
                        continue;
                    }
                    if (setNoneLayerOpacity(animationNode, animationNode.getChildNodes(), fxType, scale)) {
                        continue;
                    }
                    double maxValue = 0;
                    boolean isChangeValue = false;
                    for (int t = 0; t < animationNode.getAttributes().getLength(); t++) {
                        Node attribute = animationNode.getAttributes().item(t);
                        if (NODE_NAME_TYPE.equals(attribute.getNodeName()) && NODE_NAME_CHANGEVALUE.equals(attribute.getNodeValue())) {
                            isChangeValue = true;
                        }
                        NodeList keyNodes = animationNode.getChildNodes();
                        int k = 0;
                        for (; k < keyNodes.getLength(); k++) {
                            Node keyNode = keyNodes.item(k);
                            if (keyNode instanceof Element) {
                                NamedNodeMap keyMap = keyNode.getAttributes();
                                for (int l = 0; l < keyMap.getLength(); l++) {
                                    Node lastNode = keyMap.item(l);
                                    if (NODE_NAME_TIME.equals(lastNode.getNodeName())) {
                                        String timeStr = lastNode.getNodeValue();
                                        if ((fxType == FX_CARTOON_IN) || (fxType == FX_CARTOON_COMPOUND)) {
                                            timeStr = String.valueOf((int) (Integer.parseInt(timeStr) * scale));
                                        } else if (fxType == FX_CARTOON_OUT) {
                                            timeStr = (int) ((clipDuration - (scale * NUMBER_HUNDRED)) + Integer.parseInt(timeStr) * scale) + "";
                                        }
                                        lastNode.setNodeValue(timeStr);
                                    }
                                    if (isChangeValue && (NODE_NAME_VALUE.equals(lastNode.getNodeName()))) {
                                        String valueStr = lastNode.getNodeValue();
                                        double value = Double.valueOf(valueStr);
                                        if (value > maxValue) {
                                            maxValue = value;
                                        }
                                    }
                                }
                            }
                        }

                        if ((isChangeValue) && (maxValue > 0) && (Math.abs(size - SIZE_DELTA) != 0)) {
                            double systemSize = 1;
                            if (size > 0) {
                                double newSystem = (ONE_VALUE * maxValue) / ((double) size / FX_SIZE);
                                systemSize = newSystem / maxValue;
                            }
                            for (k = 0; k < keyNodes.getLength(); k++) {
                                Node keyNode = keyNodes.item(k);
                                if (keyNode instanceof Element) {
                                    NamedNodeMap keyMap = keyNode.getAttributes();
                                    for (int l = 0; l < keyMap.getLength(); l++) {
                                        Node lastNode = keyMap.item(l);
                                        if (NODE_NAME_VALUE.equals(lastNode.getNodeName())) {
                                            String valueStr = lastNode.getNodeValue();
                                            double oldValue = Double.valueOf(valueStr);
                                            double newValue = oldValue * systemSize;
                                            @SuppressLint("DefaultLocale")
                                            String newValueStr = String.format(FLOAT_LIMIT_NUM, Math.max(newValue, 0));
                                            lastNode.setNodeValue(newValueStr);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "split3DXmlFile, fail:" + e.getMessage());
        } finally {
            Utils.closeSilently(is);
        }

        if (document == null) {
            return xmlResultPath;
        }

        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer tformer = null;
        try {
            tformer = tf.newTransformer();
            File rootPath = new File(path);
            xmlResultPath = rootPath.getParent() + File.separator + SAVE_STORYBOARD_3D_NAME;
            File outFile = new File(xmlResultPath);
            if (outFile.exists()) {
                outFile.delete();
            }
            tformer.setOutputProperty(OutputKeys.INDENT, NODE_NAME_YES);
            document.setXmlStandalone(true);
            tformer.transform(new DOMSource(document), new StreamResult(outFile));
        } catch (TransformerException e) {
            GLog.e(TAG, LogFlag.DL, "hashTemplate, fail:" + e.getMessage());
        }
        return xmlResultPath;
    }

    public static boolean isStoryboard3d(String xmlPath) {
        if (!TextUtils.isEmpty(xmlPath)) {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            Document document = null;
            InputStream is = null;
            try {
                DocumentBuilder db = dbf.newDocumentBuilder();
                is = new FileInputStream(new File(xmlPath));
                document = db.parse(is);
                Element root = document.getDocumentElement();
                if ((!TextUtils.isEmpty(root.getTagName()))
                        && (TextUtils.equals(FX_STORYBOARD_3D.toLowerCase().replaceAll("\\s*", ""), root.getTagName()))) {
                    return true;
                }
            } catch (Exception e) {
                GLog.e(TAG, "isStoryboard3d, error : " + e.getMessage());
            } finally {
                Utils.closeSilently(is);
            }
        }
        return false;
    }

    public static String getTimelineAspectRatio(float aspectRatio) {
        List<AspectRatio> aspectRatios = Arrays.asList(ASPECT_RATIO_ARRAY);
        Collections.sort(aspectRatios, new Comparator<AspectRatio>() {
            @Override
            public int compare(AspectRatio o1, AspectRatio o2) {
                if (Math.abs(aspectRatio - o1.getAspectRatioSize()) - Math.abs(aspectRatio - o2.getAspectRatioSize()) > 0) {
                    return 1;
                } else if (Math.abs(aspectRatio - o1.getAspectRatioSize()) - Math.abs(aspectRatio - o2.getAspectRatioSize()) < 0) {
                    return -1;
                }
                return 0;
            }
        });
        return aspectRatios.get(0).getAspectRatioXml();
    }

    public static String getStoryboardData(boolean isStoryboard3d, String xmlFilePath, long duration, int fxType, float size) {
        String storyBoardData = null;
        if (isStoryboard3d) {
            storyBoardData = StoryBoardUtils.split3DXmlFile(xmlFilePath, duration, fxType, size);
        } else {
            storyBoardData = StoryBoardUtils.splitXmlFile(xmlFilePath, duration, fxType, size);
        }
        return storyBoardData;
    }

    public static String getFilterRatioFilePath(String parentDir, float timelineRatio) {
        String path = parentDir + File.separator + FX_FILTER_DIR;
        return getRatioFilePath(path, timelineRatio);
    }

    public static String getRatioFilePath(String fileDir, float timelineRatio) {
        if (TextUtils.isEmpty(fileDir)) {
            GLog.e(TAG, "getFilterFilePath, fileDir is null ");
            return null;
        }
        if (fileDir.startsWith(ASSETS_PATH_HEAD)) {
            String aspectRatio = getTimelineAspectRatio(timelineRatio);
            GLog.d(TAG,"aspectRatio");
            return fileDir + File.separator + aspectRatio;
        }
        File file = new File(fileDir);
        if ((file == null) || (!file.exists()) || (!file.isDirectory())) {
            GLog.e(TAG, "getRatioFilePath file e");
            return null;
        }

        String[] list = file.list();
        if ((list == null) || (list.length <= 0)) {
            GLog.e(TAG, "getXmlFilePath, dirFile has no child file");
            return null;
        }
        String aspectRatio = getTimelineAspectRatio(timelineRatio);
        if (TextUtils.isEmpty(aspectRatio)) {
            GLog.e(TAG, "getFilterFilePath, no cartoon fx file for this ratio ");
            return null;
        }

        for (int index = 0; index < list.length; index++) {
            String childName = list[index];
            if (childName.contains(aspectRatio)) {
                return fileDir + File.separator + childName;
            }
        }
        return null;
    }

    public static String getBlurBackground(float strength, Map<String, String> blurTransData, int width, int height) {
        String trackInfo = "<track clipDuration=\"1\" clipStart=\"0\" width = \"" + width + "\" height = \"" + height + "\" repeat=\"true\" source=\":1\">\n"
                + "<effect name=\"fastBlur\">\n"
                + "<param name=\"radius\" value=\"" + strength + "\" />\n"
                + "</effect>\n"
                + "<effect name=\"transform\">\n"
                + "<param name=\"scaleX\" value=\"" + blurTransData.get(VALUE_SCALE_X) + "\" />\n"
                + "<param name=\"scaleY\" value=\"" + blurTransData.get(VALUE_SCALE_Y) + "\" />\n"
                + "<param name=\"transX\" value=\"0\" />\n"
                + "<param name=\"transY\" value=\"0\" />\n"
                + "<param name=\"rotationZ\" value=\"" + blurTransData.get(VALUE_ROTATION_Z) + "\" />\n"
                + "</effect>\n"
                + "</track>\n";
        return trackInfo;
    }

    public static String getBlurBackgroundNoTrans(float strength, Map<String, String> blurTransData, int width, int height) {
        String trackInfo = "<track clipDuration=\"1\" clipStart=\"0\"  width = \"" + width + "\" height = \"" + height + "\"  repeat=\"true\" source=\":1\">\n"
                + "<effect name=\"fastBlur\">\n"
                + "<param name=\"radius\" value=\"" + strength + "\" />\n"
                + "</effect>\n"
                + "<effect name=\"transform\">\n"
                + "<param name=\"scaleX\" value=\"" + blurTransData.get(VALUE_SCALE_X) + "\" />\n"
                + "<param name=\"scaleY\" value=\"" + blurTransData.get(VALUE_SCALE_Y) + "\" />\n"
                + "<param name=\"transX\" value=\"" + blurTransData.get(VALUE_TRANS_X) + "\" />\n"
                + "<param name=\"transY\" value=\"" + blurTransData.get(VALUE_TRANS_Y) + "\" />\n"
                + "<param name=\"rotationZ\" value=\"" + blurTransData.get(VALUE_ROTATION_Z) + "\" />\n"
                + "</effect>\n"
                + "</track>\n";
        return trackInfo;
    }

    public static String getClipTransStory(Map<String, String> clipTransData, int width, int height) {
        String story = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<storyboard sceneHeight=\"" + height + "\" sceneWidth=\"" + width + "\">\n"
                + "<track clipDuration=\"1\" clipStart=\"0\" repeat=\"true\" source=\":1\">\n"
                + "<effect name=\"transform\">\n"
                + "<param name=\"scaleX\" value=\"" + clipTransData.get(VALUE_SCALE_X) + "\" />\n"
                + "<param name=\"scaleY\" value=\"" + clipTransData.get(VALUE_SCALE_Y) + "\" />\n"
                + "<param name=\"rotationZ\" value=\"" + clipTransData.get(VALUE_ROTATION_Z) + "\" />\n"
                + "<param name=\"transX\" value=\"" + clipTransData.get(VALUE_TRANS_X) + "\" />\n"
                + "<param name=\"transY\" value=\"" + clipTransData.get(VALUE_TRANS_Y) + "\" />\n"
                + "</effect>\n"
                + "</track>\n"
                + "</storyboard>";
        return story;
    }

    public static String getBlurBackgroundStory(float strength, Map<String, String> clipTransData, Map<String, String> blurTransData, int width, int height) {
        String story = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<storyboard sceneHeight=\"" + height + "\" sceneWidth=\"" + width + "\">\n"
                + "<trackGroup>\n"
                + "<track clipDuration=\"1\" clipStart=\"0\" repeat=\"true\" source=\":1\">\n"
                + "<effect name=\"fastBlur\">\n"
                + "<param name=\"radius\" value=\"" + strength + "\" />\n"
                + "</effect>\n"
                + "<effect name=\"transform\">\n"
                + "<param name=\"scaleX\" value=\"" + blurTransData.get(VALUE_SCALE_X) + "\" />\n"
                + "<param name=\"scaleY\" value=\"" + blurTransData.get(VALUE_SCALE_Y) + "\" />\n"
                + "<param name=\"transX\" value=\"0\" />\n"
                + "<param name=\"transY\" value=\"0\" />\n"
                + "<param name=\"rotationZ\" value=\"" + blurTransData.get(VALUE_ROTATION_Z) + "\" />\n"
                + "</effect>\n"
                + "</track>\n"
                + "\n<track clipDuration=\"1\" clipStart=\"0\" repeat=\"true\" source=\":1\">\n"
                + "<effect name=\"transform\">\n"
                + "<param name=\"scaleX\" value=\"" + clipTransData.get(VALUE_SCALE_X) + "\" />\n"
                + "<param name=\"scaleY\" value=\"" + clipTransData.get(VALUE_SCALE_Y) + "\" />\n"
                + "<param name=\"transX\" value=\"" + clipTransData.get(VALUE_TRANS_X) + "\" />\n"
                + "<param name=\"transY\" value=\"" + clipTransData.get(VALUE_TRANS_Y) + "\" />\n"
                + "<param name=\"rotationZ\" value=\"" + clipTransData.get(VALUE_ROTATION_Z) + "\" />\n"
                + "</effect>\n"
                + "</track>\n"
                + "</trackGroup>\n"
                + "</storyboard>";
        return story;
    }

    public static String getClipTransData(Map<String, String> clipTransData, int width, int height) {
        String story = "<track clipDuration=\"1\" clipStart=\"0\" width = \" " + width + "\" height = \"" + height + "\"  repeat=\"true\" source=\":1\">\n"
                + "<effect name=\"transform\">\n"
                + "<param name=\"scaleX\" value=\"" + clipTransData.get(VALUE_SCALE_X) + "\" />\n"
                + "<param name=\"scaleY\" value=\"" + clipTransData.get(VALUE_SCALE_Y) + "\" />\n"
                + "<param name=\"rotationZ\" value=\"" + clipTransData.get(VALUE_ROTATION_Z) + "\" />\n"
                + "<param name=\"transX\" value=\"" + clipTransData.get(VALUE_TRANS_X) + "\" />\n"
                + "<param name=\"transY\" value=\"" + clipTransData.get(VALUE_TRANS_Y) + "\" />\n"
                + "</effect>\n"
                + "</track>\n";
        return story;
    }

    public static String composeStory(String insertStory, String originalStory) {
        if (TextUtils.isEmpty(originalStory)) {
            return null;
        }
        int index = originalStory.indexOf(VALUE_TRACK_TAG);
        StringBuilder stringBuilder = new StringBuilder(originalStory);
        stringBuilder.insert(index, insertStory);
        return stringBuilder.toString();
    }


    public static String wrapClip(int width, int height, long clipDuration) {
        String storyboard = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<storyboard sceneWidth=\"" + width + "\" sceneHeight=\"" + height + "\">\n"
                + "\t<track source=\":1\" clipStart=\"0\" repeat=\"true\" clipDuration=\"" + clipDuration + "\"/>\n"
                + "</storyboard>";
        return storyboard;
    }

    public static String wrapClipTrans(int width, int height, Map<String, String> transData, long clipDuration) {
        String storyboard = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<storyboard sceneWidth=\"" + width + "\" sceneHeight=\"" + height + "\">\n"
                + "\t<track source=\":1\" clipStart=\"0\"  clipDuration=\"" + clipDuration + "\">\n"
                + "\t\t<effect name=\"transform\">\n"
                + "\t\t\t<param name=\"anchorX\" value=\"0\"/>\n"
                + "\t\t\t<param name=\"anchorY\" value=\"0\"/>\n"
                + "\t\t\t<param name=\"scaleX\" value=\"" + transData.get(VALUE_SCALE_X) + "\"/>\n"
                + "\t\t\t<param name=\"scaleY\" value=\"" + transData.get(VALUE_SCALE_Y) + "\"/>\n"
                + "\t\t\t<param name=\"rotationZ\" value=\"" + transData.get(VALUE_ROTATION_Z) + "\"/>\n"
                + "\t\t\t<param name=\"transX\" value=\"" + transData.get(VALUE_TRANS_X) + "\"/>\n"
                + "\t\t\t<param name=\"transY\" value=\"" + transData.get(VALUE_TRANS_Y) + "\"/>\n"
                + "\t\t</effect>\n"
                + "\t</track>\n"
                + "</storyboard>";
        return storyboard;
    }

    public static String wrapCommonBackgroundTrans(int width, int height, String sourcePath, long clipDuration) {
        int imgeSize = width;
        if (imgeSize < height) {
            imgeSize = height;
        }
        String storyboard = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<storyboard sceneWidth=\"" + width + "\" sceneHeight=\"" + height + "\">\n"
                + "\t<track source=\"" + sourcePath + "\" width=\"" + imgeSize + "\" height=\"" + imgeSize + "\" clipStart=\"0\" repeat=\"true\" clipDuration=\"1\"/>\n"
                + "\t<track source=\":1\" clipStart=\"0\" clipDuration=\"" + clipDuration + "\"/>\n"
                + "</storyboard>";
        return storyboard;
    }

    /**
     * 这里处理应用动画，并且用户有缩放画面时，动画到最后一帧还存在的情况，通过添加transform,opacity来避免
     *
     * @return
     */
    private static Boolean setNoneTransformOpacity(Node animationNode, NodeList keyNodes, int fxType, float scale) {
        NamedNodeMap transformParents = animationNode.getParentNode().getAttributes();
        if (transformParents.getLength() >= 2) {
            Node transform = transformParents.item(0);
            Node transformNone = transformParents.item(1);
            if ((transform != null) && (transformNone != null)
                    && (TextUtils.equals(transform.getNodeValue(), "transform"))
                    && (TextUtils.equals(transformNone.getNodeName(), "type"))
                    && (TextUtils.equals(transformNone.getNodeValue(), "none"))) {
                setOpacity(keyNodes, fxType, scale);
                return true;
            }
        }
        return false;
    }

    private static Boolean setNoneLayerOpacity(Node animationNode, NodeList keyNodes, int fxType, float scale) {
        NamedNodeMap layerParents = animationNode.getParentNode().getParentNode().getAttributes();
        if ((layerParents.getLength() > 0)
                && (layerParents.getNamedItem("type") != null)
                && (TextUtils.equals(layerParents.getNamedItem("type").getNodeValue(), "none"))) {
            setOpacity(keyNodes, fxType, scale);
            return true;
        }
        return false;
    }

    private static void setOpacity(NodeList keyNodes, int fxType, float scale) {
        double maxTvalue = 0;
        for (int t = 0; t < keyNodes.getLength(); t++) {
            Node tKeyNode = keyNodes.item(t);
            if (tKeyNode instanceof Element) {
                NamedNodeMap tKeyMap = tKeyNode.getAttributes();
                for (int l = 0; l < tKeyMap.getLength(); l++) {
                    Node tNode = tKeyMap.item(l);
                    if ("time".equals(tNode.getNodeName())) {
                        int currentValue = Integer.parseInt(tNode.getNodeValue());
                        if (maxTvalue < currentValue) {
                            maxTvalue = currentValue;
                        }
                    }
                }
            }
        }
        for (int t = 0; t < keyNodes.getLength(); t++) {
            Node tKeyNode = keyNodes.item(t);
            if (tKeyNode instanceof Element) {
                NamedNodeMap tKeyMap = tKeyNode.getAttributes();
                for (int l = 0; l < tKeyMap.getLength(); l++) {
                    Node tNode = tKeyMap.item(l);
                    if ("time".equals(tNode.getNodeName())) {
                        if (fxType == FX_CARTOON_OUT) {
                            continue;
                        }
                        String timeStr = tNode.getNodeValue();
                        if ((fxType == FX_CARTOON_IN) || (fxType == FX_CARTOON_COMPOUND)) {
                            timeStr = String.valueOf((int) ((t == 1) ? ((maxTvalue * scale) - 1) : (maxTvalue * scale)));
                        }
                        tNode.setNodeValue(timeStr);
                    }
                }
            }
        }
    }
}
