{"styleId": "realme_brand_imprint_exclusiveMemory_2", "baseImageSize": 360, "size": {"leftMargin": 10, "topMargin": 79, "rightMargin": 10, "bottomMargin": 79}, "imageOffset": {"startX": 10, "startY": 79}, "background": {"backgroundType": 0, "color": "#FFFFFF"}, "bitmaps": [{"direction": 1, "orientation": 0, "position": {"borderX": 10, "borderY": 79, "gravity": "center"}, "width": -1, "height": 79, "elements": [{"id": 1, "visible": true, "editable": false, "content": {"type": "text", "textSource": 7}, "paint": {"fontType": 2, "fontName": "FZYaSongDB1GBK.zip", "fontFileType": 1, "font": "https://videoclipfs.coloros.com/dirfile/icon/2024/11/06/c2e1f256-4d18-48fa-ba4a-ad474a257a2a.zip", "md5": "e70a912839b253cf406efcb41cdd5739", "textSize": 14, "withLogoTextSize": 10.5, "noLogoTextSize": 12, "fontWeight": 700, "letterSpacing": 0.04, "lineHeight": 12.6, "alpha": 0.9, "colors": ["#000000"], "gradientType": -1}, "position": {"layoutGravity": "center", "layoutGravityEnable": true, "leftMargin": 0, "rightMargin": 0, "topMargin": 0, "bottomMargin": 0}}]}, {"direction": 3, "orientation": 0, "position": {"borderX": 10, "borderY": 0, "gravity": "center"}, "width": -1, "height": 79, "elements": [{"id": 2, "content": {"type": "elements", "orientation": 1, "width": 340}, "position": {"layoutGravity": "center", "layoutGravityEnable": true, "leftMargin": 0, "rightMargin": 0, "topMargin": 0, "bottomMargin": 0}, "elements": [{"id": 3, "visible": true, "editable": false, "content": {"type": "text", "textSource": 0}, "position": {"layoutGravity": "topHorCenter", "layoutGravityEnable": true, "topMargin": 0}, "paint": {"fontType": 2, "fontName": "FZYaSongDB1GBK.zip", "fontFileType": 1, "font": "https://videoclipfs.coloros.com/dirfile/icon/2024/11/06/c2e1f256-4d18-48fa-ba4a-ad474a257a2a.zip", "md5": "e70a912839b253cf406efcb41cdd5739", "fontWeight": 600, "textSize": 12, "withLogoTextSize": 10.5, "noLogoTextSize": 12, "letterSpacing": 0.04, "lineHeight": 14.06, "alpha": 1, "gradientType": -1, "colors": ["#000000"]}}, {"id": 4, "visible": true, "editable": true, "content": {"type": "text", "textSource": 1}, "paint": {"fontType": 0, "fontName": "OplusSans", "font": "/system/fonts/SysSans-En-Regular.ttf", "fontFileType": 1, "fontWeight": 400, "gradientType": -1, "textSize": 8, "letterSpacing": 0.06, "lineHeight": 9.38, "alpha": 0.54, "colors": ["#000000"]}, "position": {"layoutGravity": "horCenter", "layoutGravityEnable": true, "leftMargin": 0, "topMargin": 2, "rightMargin": 0, "bottomMargin": 0}}]}]}]}