/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - TimeLineModule.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: Jin<PERSON><PERSON>@Rom.Apps.Gallery
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  Jin<PERSON><PERSON>@Rom.Apps.Gallery       2022/2/17       1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.timelinepage

import android.app.Activity
import android.app.Application
import android.content.Intent.ACTION_MAIN
import android.content.res.Configuration
import android.os.Bundle
import com.oplus.gallery.addon.content.res.ConfigurationWrapper
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.constants.StartupConstant
import com.oplus.gallery.business_lib.model.selection.SelectionModelCache
import com.oplus.gallery.business_lib.seniorpicked.layout.PickedDayTypesettingSelector
import com.oplus.gallery.business_lib.timeline.viewmodel.TimelinePreLoader
import com.oplus.gallery.foundation.gstartup.GStartupLifecycle
import com.oplus.gallery.foundation.gstartup.task.TaskCollector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SENIOR_PICKED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_ALL_PICTURE
import com.oplus.gallery.router_lib.annotations.AppInit
import com.oplus.gallery.router_lib.center.BaseAppInit
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.oplus.gallery.addon.osense.UxThreadScene
import com.oplus.gallery.addon.osense.UxThreadUtils
import com.oplus.gallery.business_lib.model.data.config.PhotoPresentType
import com.oplus.gallery.business_lib.timeline.draw.BaseDecorationDrawer
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.PHOTO_VIEW_DEFAULT_PRESENT_TYPE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.PHOTO_VIEW_PRESENT_TYPE
import com.oplus.gallery.standard_lib.app.SINGLE_FOREGROUND

@AppInit(process = ["com.coloros.gallery3d", "com.oneplus.gallery"])
class TimelineModule(app: Application) : BaseAppInit(app), GStartupLifecycle {

    /**
     * 系统字体缩放倍数
     */
    private var fontScale = app.resources.configuration.fontScale

    /**
     * 系统字体样式标识
     */
    private var flipFont = ConfigurationWrapper.getFlipFont(app.resources.configuration)

    override fun onAppCreate(taskCollector: TaskCollector) {
        super.onAppCreate(taskCollector)
        // !!!!!!注意：onAppCreate会阻塞后续阶段，无强依赖任务放在该阶段触发时，不能在GStartup任务池执行，避免阻塞主线程

        /**
         * 加快冷启动速度：
         * 将时间轴的首屏数据预加载提前到第一阶段执行，
         * 但是不能将任务放到TaskCollector，会阻塞主线程，因此放到协程中执行。
         */
        taskCollector.add("TimelinePreLoader.preload") {
            AppScope.launch(Dispatchers.SINGLE_FOREGROUND) {
                UxThreadUtils.runInUxState(UxThreadScene.GalleryLaunchDataPreloadScene) {
                    GTrace.trace("onAppCreate.preload") {
                        TimelinePreLoader.preload(app)
                        //时间轴预加载完后,再做数据同步,加快时间轴时间查询数据。
                        enqueueIncrementSync("onAppCreate")
                    }
                }
            }
        }
    }

    override fun onAppCreated(taskCollector: TaskCollector) {
        taskCollector.add("$TAG.supportFeature") {
            // 提前加载，避免后面主线程操作被阻塞。
            ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH)
            val isSupportSeniorPicked = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SENIOR_PICKED)
            // 提前初始化沉浸式开关项 否则后面响应会看到刷新的过程认为是闪
            val defaultPhotoViewPresentType = ConfigAbilityWrapper.getInt(PHOTO_VIEW_DEFAULT_PRESENT_TYPE, PhotoPresentType.DATE.value())
            val isImmersivePresentation =
                ConfigAbilityWrapper.getInt(PHOTO_VIEW_PRESENT_TYPE, defaultPhotoViewPresentType) == PhotoPresentType.IMMERSE.value()
            GLog.d(TAG, LogFlag.DL) { "isImmersivePresentation=$isImmersivePresentation" }
            TimelinePreLoader.isImmersivePresentation.set(isImmersivePresentation)
            // 如果支持精选年月日，需要提前坐下预加载，避免在后续主线程中耗时,导致丢帧
            if (isSupportSeniorPicked) {
                DataRepository.getAlbumModel(DataRepository.PickedModelGetter.TYPE_PICKED_DAY_ALBUM, Bundle())
            }
        }
    }

    override fun onLaunchActivityCreated(activity: Activity, taskCollector: TaskCollector, entrance: Int) {
        if ((entrance == StartupConstant.MAIN) && (activity.intent.action == ACTION_MAIN)) {
            taskCollector.add("preloadBlockSlidingWindow") {
                preloadSelectionAlbumCache()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        val oldFontScale = fontScale
        val oldFlipFont = flipFont
        fontScale = newConfig.fontScale
        flipFont = ConfigurationWrapper.getFlipFont(newConfig)

        clearTextSizeCacheIfNeed(oldFontScale, fontScale, oldFlipFont, flipFont)
    }

    private fun clearTextSizeCacheIfNeed(oldFontScale: Float, newFontScale: Float, oldFlipFont: Int, newFlipFont: Int) {
        if ((oldFontScale != newFontScale) || (oldFlipFont != newFlipFont)) {
            GLog.d(TAG, LogFlag.DL) {
                "TextSizeCache cleared, fontScale new: $newFontScale old: $oldFontScale, flipFont change new: $newFlipFont old: $oldFlipFont"
            }
            BaseDecorationDrawer.clearTextSizeCache()
        }
    }

    /**
     * 执行数据同步
     * @param from 从哪里调用的
     */
    private fun enqueueIncrementSync(from: String) {
        GLog.d(TAG, LogFlag.DL) { "[enqueueIncrementSync] from:$from" }
        AppScope.launch(Dispatchers.IO) {
            ApiDmManager.getMediaDBSyncDM().enqueueIncrementSync()
        }
    }

    override fun onCPUIdle(taskCollector: TaskCollector, entrance: Int) {
        taskCollector.add("$TAG.PickedDayTypesettingSelector") {
            // ConfigAbilityWrapper.getBoolean 内部是IO操作，当IO繁忙的时候会阻塞线程，因此放到子线程
            val isSeniorPickedSupported = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SENIOR_PICKED)
            if (isSeniorPickedSupported) {
                PickedDayTypesettingSelector.instance.load()
            }
        }
        AppScope.launch {
            delay(CLEAR_TIMELINE_PRELOAD_DATA_DELAY)
            TimelinePreLoader.clear()
        }
    }

    /**
     * BlockSlidingWindow 初始化有一些耗时，在这里提前初始化，
     * 减少TimelineTabFragment.onCreateSlidingWindows在主线程的耗时
     */
    private fun preloadSelectionAlbumCache() {
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH)) {
            SelectionModelCache.getAlbumCache(TYPE_ALL_PICTURE)
        }
    }

    private companion object {
        private const val CLEAR_TIMELINE_PRELOAD_DATA_DELAY = 1000L
        private const val TAG = "TimelineModule"
    }
}
