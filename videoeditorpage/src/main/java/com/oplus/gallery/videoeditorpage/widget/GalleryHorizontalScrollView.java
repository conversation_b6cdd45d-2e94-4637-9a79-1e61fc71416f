/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** OPLUS Coding Static Checking Skip
 ** File: - COUIHorizontalScrollView.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2023/07/03
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>     2025/8/1  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.FocusFinder;
import android.view.InputDevice;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.AnimationUtils;
import android.widget.HorizontalScrollView;

import androidx.annotation.NonNull;
import androidx.core.view.ViewConfigurationCompat;

import com.coui.appcompat.animation.COUIPhysicalAnimationUtil;
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants;
import com.coui.appcompat.scroll.COUIIOverScroller;
import com.coui.appcompat.scroll.SpringOverScroller;
import com.coui.appcompat.view.ViewNative;

import java.util.List;

/**
 * 此类作用：
 * 1.复制COUI 13.2 COUIHorizontalScrollView 源码
 * 2.添加实现滚动监听OnScrollStateChangeListener代码（带有 'COUIHorizontalScrollView 的基础上添加的代码' 注释字样的 ）
 *
 * 另：
 * 1.当前需要监听COUIHorizontalScrollView的滚动状态，但其没有对应回调，后期可以推动COUI添加后，直接删除此类即可
 * 2.如需要同步对应的coui的新效果，复制其源码，保留滚动状态相关代码即可
 */
public class GalleryHorizontalScrollView extends HorizontalScrollView {

    /**
     * GalleryHorizontalScrollView 当前未滚动
     */
    public  static final int SCROLL_STATE_IDLE = 0;

    private static final String TAG = "GalleryHorizontalScrollView";

    static final int ANIMATED_SCROLL_GAP = 250;

    /*---------- 以下为在 COUIHorizontalScrollView 的基础上添加的代码 start ---------- */

    /**
     * 单位：毫秒（每秒计算速度）
     */
    private static final int VELOCITY_UNITS = 1000;

    /**
     * GalleryHorizontalScrollView 当前正在被外部输入（例如用户触摸输入）拖动
     */
    private static final int SCROLL_STATE_DRAGGING = 1;

    /**
     * GalleryHorizontalScrollView 当前正在动画到最终位置（fling ， smoothTo），而不是在外部控制
     */
    private static final int SCROLL_STATE_SETTLING = 2;

    /*---------- 在 COUIHorizontalScrollView 的基础上添加的代码 end ---------- */

    private static final float HORIZONTAL_SPRING_BACK_TENSION_MULTIPLE = 3.20f;


    private static final int INVALID_POINTER = -1;

    //#ifndef COUI_EDIT
    //<EMAIL>, 2022-04-14 add for Feature-2803286
    private static final int SLOW_SCROLL_THRESHOLD = 250;
    private static final int FLING_SCROLL_THRESHOLD = 1500;
    private static final int OVER_SCROLL_TOUCH_DURATION_THRESHOLD = 100;
    private static final int OVER_SCROLL_TOUCH_OFFSET_THRESHOLD = 10;

    // Called by executeKeyEvent(), pageScroll(), fullScroll(), arrowScroll(), isWithinDeltaOfScreen(),
    // scrollToDescendant(), onSizeChanged()
    private final Rect mTempRect = new Rect();
    private int mScreenWidth = 0;
    //#endif // COUI_EDIT

    // Called by smoothScrollBy()
    private long mLastScroll;

    // Called by ?
    private COUIIOverScroller mOverScroller = null;
    private SpringOverScroller mSpringOverScroller = null;

    private int mLastMotionX;

    private boolean mIsLayoutDirty = true;

    // Called by scrollToDescendant(), requestChildFocus(), onLayout()
    private View mChildToScrollTo = null;

    // Called by onInterceptTouchEvent(), onTouchEvent(), endDrag()
    private boolean mIsBeingDragged = false;

    private VelocityTracker mVelocityTracker;

    // Called by isFillViewport(), setFillViewport(), onMeasure(), encodeProperties()
    private boolean mFillViewport;

    // Called by isSmoothScrollingEnabled(), setSmoothScrollingEnabled(), doScrollX()
    private boolean mSmoothScrollingEnabled = true;

    // Called by onInterceptTouchEvent(), onTouchEvent()
    private int mTouchSlop;
    // Called by onTouchEvent()
    private int mMinimumVelocity;
    // Called by onTouchEvent()
    private int mMaximumVelocity;
    // Called by onTouchEvent()
    private int mOverscrollDistance;
    // Called by computeScroll()
    private int mOverflingDistance;

    // Called by initCOUIHorizontalScrollView(), onGenericMotionEvent()
    private float mHorizontalScrollFactor;


    // Called by onInterceptTouchEvent(), onTouchEvent(), onSecondaryPointerUp()
    private int mActivePointerId = INVALID_POINTER;
    private long mTouchTime;
    private int mInitialTouchX;
    private boolean mIsTouchDownWhileSlowScrolling;
    private boolean mIsTouchDownWhileOverScrolling;
    private boolean mItemClickableWhileSlowScrolling = true;
    private boolean mItemClickableWhileOverScrolling = true;
    private float mFlingVelocityX;

    // Called by onInterceptTouchEvent(), onDetachedFromWindow(), endDrag()
    private boolean mScrollStrictSpan = false;
    // Called by onTouchEvent(), smoothScrollBy(), computeScroll(), onDetachedFromWindow(), fling()
    private boolean mFlingStrictSpan = false;

    private boolean mEnableOptimizedScroll = true;
    private boolean mEnableVibrator = true;

    /*---------- 以下为在 COUIHorizontalScrollView 的基础上添加的代码 start ------ */

    private OnScrollStateChangeListener mScrollStateChangeListener;
    private int mScrollState = SCROLL_STATE_IDLE;

    /*---------- 在 COUIHorizontalScrollView 的基础上添加的代码 end ---------- */


    public GalleryHorizontalScrollView(Context context) {
        this(context, null);
    }

    public GalleryHorizontalScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GalleryHorizontalScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initCOUIHorizontalScrollView(context);
    }

    public GalleryHorizontalScrollView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initCOUIHorizontalScrollView(context);
    }

    private void initCOUIHorizontalScrollView(Context context) {
        // init overScroller
        if (mOverScroller == null) {
            mSpringOverScroller = new SpringOverScroller(context);
            mSpringOverScroller.setSpringBackTensionMultiple(HORIZONTAL_SPRING_BACK_TENSION_MULTIPLE);
            mSpringOverScroller.setIsScrollView(true);
            mOverScroller = mSpringOverScroller;
            setEnableFlingSpeedIncrease(true);
        }
        final DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        final ViewConfiguration configuration = ViewConfiguration.get(context);
        mTouchSlop = configuration.getScaledTouchSlop();
        mMinimumVelocity = configuration.getScaledMinimumFlingVelocity();
        mMaximumVelocity = configuration.getScaledMaximumFlingVelocity();
        int width = metrics.widthPixels;
        mOverscrollDistance = width;
        mOverflingDistance = width;
        mScreenWidth = metrics.widthPixels;
        mHorizontalScrollFactor = ViewConfigurationCompat.getScaledHorizontalScrollFactor(configuration, context);
        setOverScrollMode(OVER_SCROLL_ALWAYS);
    }

    public void setItemClickableWhileSlowScrolling(boolean itemClickableWhileSlowScrolling) {
        mItemClickableWhileSlowScrolling = itemClickableWhileSlowScrolling;
    }

    public void setItemClickableWhileOverScrolling(boolean itemClickableWhileOverScrolling) {
        mItemClickableWhileOverScrolling = itemClickableWhileOverScrolling;
    }

    public boolean isEnableFlingSpeedIncrease() {
        if (mSpringOverScroller != null) {
            return mSpringOverScroller.isEnableFlingSpeedIncrease();
        }
        return false;
    }

    public void setEnableFlingSpeedIncrease(boolean enableFlingSpeedIncrease) {
        if (mSpringOverScroller != null) {
            mSpringOverScroller.setEnableFlingSpeedIncrease(enableFlingSpeedIncrease);
        }
    }

    public void setIsUseOptimizedScroll(boolean enable) {
        mEnableOptimizedScroll = enable;
    }

    public void setSpringOverScrollerDebug(boolean enable) {
        mSpringOverScroller.setDebug(enable);
    }

    private boolean isOverScrolling() {
        return getScrollX() < 0 || getScrollX() > getScrollRange();
    }

    // Called by executeKeyEvent()
    private boolean canScroll() {
        View child = getChildAt(0);
        if (child != null) {
            int childWidth = child.getWidth();
            return getWidth() < childWidth + getPaddingLeft() + getPaddingRight();
        }
        return false;
    }

    private void performHapticFeedback() {
        if (mEnableVibrator) {
            performHapticFeedback(COUIHapticFeedbackConstants.EDGE_LIST_VIBRATE);
        }
    }

    public boolean isFillViewport() {
        return mFillViewport;
    }

    // Called by constructor(),
    public void setFillViewport(boolean fillViewport) {
        if (fillViewport != mFillViewport) {
            mFillViewport = fillViewport;
            requestLayout();
        }
    }

    public boolean isSmoothScrollingEnabled() {
        return mSmoothScrollingEnabled;
    }

    public void setSmoothScrollingEnabled(boolean smoothScrollingEnabled) {
        mSmoothScrollingEnabled = smoothScrollingEnabled;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        if (!mFillViewport) {
            return;
        }

        final int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        if (widthMode == MeasureSpec.UNSPECIFIED) {
            return;
        }

        if (getChildCount() > 0) {
            final View child = getChildAt(0);
            final int widthPadding;
            final int heightPadding;
            final LayoutParams lp = (LayoutParams) child.getLayoutParams();
            final int targetSdkVersion = getContext().getApplicationInfo().targetSdkVersion;
            if (targetSdkVersion >= Build.VERSION_CODES.M) {
                widthPadding = getPaddingLeft() + getPaddingRight() + lp.leftMargin + lp.rightMargin;
                heightPadding = getPaddingTop() + getPaddingBottom() + lp.topMargin + lp.bottomMargin;
            } else {
                widthPadding = getPaddingLeft() + getPaddingRight();
                heightPadding = getPaddingTop() + getPaddingBottom();
            }

            int desiredWidth = getMeasuredWidth() - widthPadding;
            if (child.getMeasuredWidth() < desiredWidth) {
                final int childWidthMeasureSpec = MeasureSpec.makeMeasureSpec(
                        desiredWidth, MeasureSpec.EXACTLY);
                final int childHeightMeasureSpec = getChildMeasureSpec(
                        heightMeasureSpec, heightPadding, lp.height);
                child.measure(childWidthMeasureSpec, childHeightMeasureSpec);
            }
        }
    }

    // Called by onOverScrolled()
    protected void invalidateParentIfNeeded() {
        if (isHardwareAccelerated() && getParent() instanceof View) {
            ((View) getParent()).invalidate();
        }
    }

    // Called by itself?
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        // Let the focused view and/or our descendants get the key first
        return super.dispatchKeyEvent(event) || executeKeyEvent(event);
    }

    // Called by dispatchKeyEvent()
    public boolean executeKeyEvent(KeyEvent event) {
        mTempRect.setEmpty();

        if (!canScroll()) {
            if (isFocused()) {
                View currentFocused = findFocus();
                if (currentFocused == this) {
                    currentFocused = null;
                }
                View nextFocused = FocusFinder.getInstance().findNextFocus(this,
                        currentFocused, View.FOCUS_RIGHT);
                return nextFocused != null && nextFocused != this
                        && nextFocused.requestFocus(View.FOCUS_RIGHT);
            }
            return false;
        }

        boolean handled = false;
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            switch (event.getKeyCode()) {
                case KeyEvent.KEYCODE_DPAD_LEFT:
                    if (!event.isAltPressed()) {
                        handled = arrowScroll(View.FOCUS_LEFT);
                    } else {
                        handled = fullScroll(View.FOCUS_LEFT);
                    }
                    break;
                case KeyEvent.KEYCODE_DPAD_RIGHT:
                    if (!event.isAltPressed()) {
                        handled = arrowScroll(View.FOCUS_RIGHT);
                    } else {
                        handled = fullScroll(View.FOCUS_RIGHT);
                    }
                    break;
                case KeyEvent.KEYCODE_SPACE:
                    pageScroll(event.isShiftPressed() ? View.FOCUS_LEFT : View.FOCUS_RIGHT);
                    break;
                default:
                    break;
            }
        }

        return handled;
    }

    // Called by onInterceptTouchEvent()
    private boolean inChild(int x, int y) {
        if (getChildCount() > 0) {
            final int scrollX = getScrollX();
            final View child = getChildAt(0);
            return !(y < child.getTop()
                    || y >= child.getBottom()
                    || x < child.getLeft() - scrollX
                    || x >= child.getRight() - scrollX);
        }
        return false;
    }

    // Called by onInterceptTouchEvent()
    private void initOrResetVelocityTracker() {
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        } else {
            mVelocityTracker.clear();
        }
    }

    // Called by onInterceptTouchEvent(), onTouchEvent()
    private void initVelocityTrackerIfNotExists() {
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
    }

    // Called by requestDisallowInterceptTouchEvent()
    private void recycleVelocityTracker() {
        if (mVelocityTracker != null) {
            mVelocityTracker.recycle();
            mVelocityTracker = null;
        }
    }

    // Called by onInterceptTouchEvent(), onTouchEvent()
    @Override
    public void requestDisallowInterceptTouchEvent(boolean disallowIntercept) {
        if (disallowIntercept) {
            recycleVelocityTracker();
        }
        super.requestDisallowInterceptTouchEvent(disallowIntercept);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        final int action = ev.getAction();
        if ((action == MotionEvent.ACTION_MOVE) && (mIsBeingDragged)) {
            return true;
        }
        if (super.onInterceptTouchEvent(ev)) {
            return true;
        }

        switch (action & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_MOVE:
                return handleMoveIntercept(ev);
            case MotionEvent.ACTION_DOWN:
                return handleDownIntercept(ev);
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                return handleUpCancelIntercept();
            case MotionEvent.ACTION_POINTER_DOWN:
                return handlePointerDownIntercept(ev);
            case MotionEvent.ACTION_POINTER_UP:
                return handlePointerUpIntercept(ev);
            default:
                break;
        }

        return mIsBeingDragged;
    }

    private boolean handleMoveIntercept(MotionEvent ev) {
        final int activePointerId = mActivePointerId;
        if (activePointerId == INVALID_POINTER) {
            return false;
        }

        final int pointerIndex = ev.findPointerIndex(activePointerId);
        if (pointerIndex == -1) {
            Log.e(TAG, "Invalid pointerId=" + activePointerId
                    + " in onInterceptTouchEvent");
            return false;
        }
        final int x = (int) ev.getX(pointerIndex);
        final int xDiff = (int) Math.abs(x - mLastMotionX);
        if (xDiff > mTouchSlop) {
            mIsBeingDragged = true;
            setScrollStateToDragging();
            mLastMotionX = x;
            initVelocityTrackerIfNotExists();
            mVelocityTracker.addMovement(ev);
            final ViewParent parent = getParent();
            if (parent != null) {
                parent.requestDisallowInterceptTouchEvent(true);
            }
        }
        return false;
    }

    private boolean handleDownIntercept(MotionEvent ev) {
        float scrollVelocityX = (mOverScroller != null) ? mOverScroller.getCurrVelocityX() : 0;
        boolean isFastFlingX = Math.abs(mFlingVelocityX) > FLING_SCROLL_THRESHOLD;
        mIsTouchDownWhileSlowScrolling = (Math.abs(scrollVelocityX) > 0 && Math.abs(scrollVelocityX) < SLOW_SCROLL_THRESHOLD && isFastFlingX);
        mIsTouchDownWhileOverScrolling = isOverScrolling();
        mTouchTime = System.currentTimeMillis();
        final int x = (int) ev.getX();
        if (!inChild((int) x, (int) ev.getY())) {
            mIsBeingDragged = false;
            recycleVelocityTracker();
            return false;
        }

        mInitialTouchX = x;
        mLastMotionX = x;
        mActivePointerId = ev.getPointerId(0);

        initOrResetVelocityTracker();
        mVelocityTracker.addMovement(ev);
        if (mOverScroller == null) {
            return false;
        }
        mIsBeingDragged = !mOverScroller.isCOUIFinished();
        if (mIsBeingDragged) {
            getParent().requestDisallowInterceptTouchEvent(true);
            setScrollStateToDragging();
        }
        return false;
    }

    private boolean handleUpCancelIntercept() {
        mIsBeingDragged = false;
        mActivePointerId = INVALID_POINTER;
        if (mOverScroller.springBack(getScrollX(), getScrollY(), 0, getScrollRange(), 0, 0)) {
            postInvalidateOnAnimation();
        }
        return false;
    }

    private boolean handlePointerDownIntercept(MotionEvent ev) {
        final int index = ev.getActionIndex();
        mInitialTouchX = (int) ev.getX(index);
        mLastMotionX = (int) ev.getX(index);
        mActivePointerId = ev.getPointerId(index);
        return false;
    }

    private boolean handlePointerUpIntercept(MotionEvent ev) {
        onSecondaryPointerUp(ev);
        mInitialTouchX = (int) ev.getX(ev.findPointerIndex(mActivePointerId));
        mLastMotionX = (int) ev.getX(ev.findPointerIndex(mActivePointerId));
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        initVelocityTrackerIfNotExists();
        mVelocityTracker.addMovement(ev);

        final int action = ev.getAction();

        switch (action & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                return handleDownTouchEvent(ev);
            case MotionEvent.ACTION_MOVE:
                return handleMoveTouchEvent(ev);
            case MotionEvent.ACTION_UP:
                return handleUpTouchEvent(ev);
            case MotionEvent.ACTION_CANCEL:
                return handleCancelTouchEvent();
            case MotionEvent.ACTION_POINTER_UP:
                onSecondaryPointerUp(ev);
                break;
            default:
                break;
        }
        return true;
    }

    private boolean handleDownTouchEvent(MotionEvent ev) {
        if (getChildCount() == 0) {
            return false;
        }

        if (!mOverScroller.isCOUIFinished()) {
            final ViewParent parent = getParent();
            if (parent != null) {
                parent.requestDisallowInterceptTouchEvent(true);
            }
        }

        /*
         * If being flinged and user touches, stop the fling. isFinished
         * will be false if being flinged.
         */
        if (!mOverScroller.isCOUIFinished()) {
            mOverScroller.abortAnimation();
            if (mFlingStrictSpan) {
                mFlingStrictSpan = false;
            }
        }

        // Remember where the motion event started
        mInitialTouchX = (int) ev.getX();
        mLastMotionX = (int) ev.getX();
        mActivePointerId = ev.getPointerId(0);
        return true;
    }

    private boolean handleMoveTouchEvent(MotionEvent ev) {
        if (mOverScroller instanceof SpringOverScroller && mEnableOptimizedScroll) {
            ((SpringOverScroller) mOverScroller).triggerCallback();
        }

        final int activePointerIndex = ev.findPointerIndex(mActivePointerId);
        if (activePointerIndex == -1) {
            Log.e(TAG, "Invalid pointerId=" + mActivePointerId + " in onTouchEvent");
            return true;
        }

        final int x = (int) ev.getX(activePointerIndex);
        int deltaX = mLastMotionX - x;

        handleDragStartIfNeeded(deltaX);

        if (!mIsBeingDragged) {
            return true;
        }

        if (mScrollState != SCROLL_STATE_DRAGGING) {
            setScrollStateToDragging();
        }

        // Scroll to follow the motion event
        mLastMotionX = x;
        processScrollMovement(deltaX);

        return true;
    }

    private void handleDragStartIfNeeded(int deltaX) {
        if (mIsBeingDragged || Math.abs(deltaX) <= mTouchSlop) {
            return;
        }

        final ViewParent parent = getParent();
        if (parent != null) {
            parent.requestDisallowInterceptTouchEvent(true);
        }
        mIsBeingDragged = true;
    }

    private void processScrollMovement(int deltaX) {
        final int range = getScrollRange();
        int overX = calculateOverScrollDistance(deltaX);
        if (overScrollBy(overX, 0, getScrollX(), 0, range, 0, mOverscrollDistance, 0, true)
                && !hasNestedScrollingParent()) {
            mVelocityTracker.clear();
        }
    }

    private int calculateOverScrollDistance(int deltaX) {
        if (getScrollX() < 0) {
            return COUIPhysicalAnimationUtil.calcRealOverScrollDist(deltaX, getScrollX(), mOverscrollDistance);
        } else if (getScrollX() > getScrollRange()) {
            return COUIPhysicalAnimationUtil.calcRealOverScrollDist(deltaX, getScrollX() - getScrollRange(), mOverscrollDistance);
        }
        return deltaX;
    }

    private boolean handleUpTouchEvent(MotionEvent ev) {
        handleItemClickIfNeeded(ev);

        if (mIsBeingDragged) {
            handleDragEndWithFling();
        } else {
            handleDragEndWithoutFling();
        }

        return true;
    }

    private void handleItemClickIfNeeded(MotionEvent ev) {
        boolean isOverScrolling = isOverScrolling();
        boolean handleWhileSlowScrolling = mItemClickableWhileSlowScrolling
                && mIsTouchDownWhileSlowScrolling;
        boolean handleWhileOverScrolling = mItemClickableWhileOverScrolling
                && mIsTouchDownWhileOverScrolling
                && isOverScrolling;

        if (!handleWhileSlowScrolling && !handleWhileOverScrolling) {
            return;
        }

        findViewToDispatchClickEvent(ev);
    }

    private void handleDragEndWithFling() {
        initVelocityTrackerIfNotExists();
        final VelocityTracker velocityTracker = mVelocityTracker;
        velocityTracker.computeCurrentVelocity(VELOCITY_UNITS, mMaximumVelocity);
        int initialVelocity = (int) velocityTracker.getXVelocity(mActivePointerId);

        if (Math.abs(initialVelocity) > mMinimumVelocity) {
            performFlingAction(initialVelocity);
        } else {
            performSpringBackAction();
        }

        if (getScrollX() < 0 || getScrollX() > getScrollRange()) {
            performHapticFeedback();
        }

        cleanupAfterDrag();
    }

    private void performFlingAction(int initialVelocity) {
        mOverScroller.setCurrVelocityX(-initialVelocity);

        if (getScrollX() < 0) {
            // fling right
            if (initialVelocity > -FLING_SCROLL_THRESHOLD) {
                performSpringBackIfNeeded();
            } else {
                fling(-initialVelocity);
            }
        } else if (getScrollX() > getScrollRange()) {
            // fling left
            if (initialVelocity < FLING_SCROLL_THRESHOLD) {
                performSpringBackIfNeeded();
            } else {
                fling(-initialVelocity);
            }
        } else {
            fling(-initialVelocity);
        }
    }

    private void performSpringBackAction() {
        if (mOverScroller.springBack(getScrollX(), getScrollY(), 0, getScrollRange(), 0, 0)) {
            postInvalidateOnAnimation();
        } else {
            setScrollStateToIdle();
        }
    }

    private void performSpringBackIfNeeded() {
        if (mOverScroller.springBack(getScrollX(), getScrollY(), 0, getScrollRange(), 0, 0)) {
            postInvalidateOnAnimation();
        }
    }

    private void handleDragEndWithoutFling() {
        if (mOverScroller.springBack(getScrollX(), getScrollY(), 0, getScrollRange(), 0, 0)) {
            postInvalidateOnAnimation();
        } else {
            setScrollStateToIdle();
        }

        cleanupAfterDrag();
    }

    private void cleanupAfterDrag() {
        mActivePointerId = INVALID_POINTER;
        mIsBeingDragged = false;
        recycleVelocityTracker();
    }

    private boolean handleCancelTouchEvent() {
        if (!mIsBeingDragged || getChildCount() <= 0) {
            return true;
        }

        if (mOverScroller.springBack(getScrollX(), getScrollY(), 0, getScrollRange(), 0, 0)) {
            postInvalidateOnAnimation();
        } else {
            setScrollStateToIdle();
        }

        mActivePointerId = INVALID_POINTER;
        mIsBeingDragged = false;
        recycleVelocityTracker();

        return true;
    }

    private void onSecondaryPointerUp(MotionEvent ev) {
        final int pointerIndex = (ev.getAction() & MotionEvent.ACTION_POINTER_INDEX_MASK)
                >> MotionEvent.ACTION_POINTER_INDEX_SHIFT;
        final int pointerId = ev.getPointerId(pointerIndex);
        if (pointerId == mActivePointerId) {
            final int newPointerIndex = pointerIndex == 0 ? 1 : 0;
            mInitialTouchX = mLastMotionX = (int) ev.getX(newPointerIndex);
            mActivePointerId = ev.getPointerId(newPointerIndex);
            if (mVelocityTracker != null) {
                mVelocityTracker.clear();
            }
        }
    }

    @Override
    public boolean onGenericMotionEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_SCROLL: {
                if (!mIsBeingDragged) {
                    final float axisValue;
                    if (event.isFromSource(InputDevice.SOURCE_CLASS_POINTER)) {
                        if ((event.getMetaState() & KeyEvent.META_SHIFT_ON) != 0) {
                            axisValue = -event.getAxisValue(MotionEvent.AXIS_VSCROLL);
                        } else {
                            axisValue = event.getAxisValue(MotionEvent.AXIS_HSCROLL);
                        }
                    } else if (event.isFromSource(InputDevice.SOURCE_ROTARY_ENCODER)) {
                        axisValue = event.getAxisValue(MotionEvent.AXIS_SCROLL);
                    } else {
                        axisValue = 0;
                    }

                    final int delta = Math.round(axisValue * mHorizontalScrollFactor);
                    if (delta != 0) {
                        final int range = getScrollRange();
                        int oldScrollX = getScrollX();
                        int newScrollX = oldScrollX + delta;
                        if (newScrollX < 0) {
                            newScrollX = 0;
                        } else if (newScrollX > range) {
                            newScrollX = range;
                        }
                        if (newScrollX != oldScrollX) {
                            super.scrollTo(newScrollX, getScrollY());
                            return true;
                        }
                    }
                }
            }
        }
        return super.onGenericMotionEvent(event);
    }

//    @Override
//    public void scrollTo(int x, int y) {
//        if (getChildCount() > 0) {
//            if (getScrollX() != x || getScrollY() != y) {
//                int oldX = getScrollX();
//                int oldY = getScrollY();
//                ViewNative.setScrollX(this, x);
//                ViewNative.setScrollY(this, y);
//                onScrollChanged(x, y, oldX, oldY);
//                if (!awakenScrollBars()) {
//                    postInvalidateOnAnimation();
//                }
//            }
//        }
//    }

    public int getScrollableRange() {
        return getWidth() - getPaddingLeft() - getPaddingRight();
    }

    // Called by View.overScrollBy()
    @Override
    protected void onOverScrolled(int scrollX, int scrollY,
                                  boolean clampedX, boolean clampedY) {
        if (getScrollY() != scrollY || getScrollX() != scrollX) {
            /* #ifndef COUI_EDIT
             <EMAIL>, 2022-08-29 add for Feature-3586366
             MODIFIED HERE
             */
            int adjustedScrollX = scrollX;
            if (isOverScrolling() && mFlingStrictSpan) {
                int boundary = 0;
                if (scrollX >= getScrollRange()) {
                    boundary = getScrollRange();
                }
                int deltaX = scrollX - boundary;
                adjustedScrollX = COUIPhysicalAnimationUtil.calcOverFlingDecelerateDist(boundary, deltaX, mScreenWidth);
            }
            /* MODIFIED HERE
            #endif // COUI_EDIT
             */
            if (getOverScrollMode() == OVER_SCROLL_NEVER
                    || (getOverScrollMode() == OVER_SCROLL_IF_CONTENT_SCROLLS
                    && getChildAt(0).getWidth()
                    <= getScrollableRange())) {
                adjustedScrollX = Math.min(Math.max(adjustedScrollX, 0), getScrollRange());
            }
            if (getScrollX() >= 0 && adjustedScrollX < 0 && mFlingStrictSpan) {
                performHapticFeedback();
                mSpringOverScroller.notifyHorizontalEdgeReached(adjustedScrollX, 0, mOverflingDistance);
            }
            if (getScrollX() <= getScrollRange() && adjustedScrollX > getScrollRange() && mFlingStrictSpan) {
                performHapticFeedback();
                mSpringOverScroller.notifyHorizontalEdgeReached(adjustedScrollX, getScrollRange(), mOverflingDistance);
            }
            ViewNative.setScrollX(this, adjustedScrollX);
            ViewNative.setScrollY(this, scrollY);
            invalidateParentIfNeeded();
            awakenScrollBars();
        }
    }

    /* Called by onInterceptTouchEvent(), onTouchEvent(), onGenericMotionEvent(), onOverScrolled(),
     * performAccessibilityActionInternal(), onInitializeAccessibilityNodeInfoInternal(), onInitializeAccessibilityEventInternal(),
     * computeScroll(), flingWithNestedDispatch(), draw()
     */
    private int getScrollRange() {
        int scrollRange = 0;
        if (getChildCount() > 0) {
            View child = getChildAt(0);
            scrollRange = Math.max(0,
                    child.getWidth() - (getWidth() - getPaddingLeft() - getPaddingRight()));
        }
        return scrollRange;
    }

    // Called by fling()
    private View findFocusableViewInMyBounds(final boolean leftFocus,
                                             final int left, View preferredFocusable) {
        /*
         * The fading edge's transparent side should be considered for focus
         * since it's mostly visible, so we divide the actual fading edge length
         * by 2.
         */
        final int fadingEdgeLength = getHorizontalFadingEdgeLength() / 2;
        final int leftWithoutFadingEdge = left + fadingEdgeLength;
        final int rightWithoutFadingEdge = left + getWidth() - fadingEdgeLength;

        if ((preferredFocusable != null)
                && (preferredFocusable.getLeft() < rightWithoutFadingEdge)
                && (preferredFocusable.getRight() > leftWithoutFadingEdge)) {
            return preferredFocusable;
        }

        return findFocusableViewInBounds(leftFocus, leftWithoutFadingEdge,
                rightWithoutFadingEdge);
    }

    private View findFocusableViewInBounds(boolean leftFocus, int left, int right) {
        List<View> focusables = getFocusables(View.FOCUS_FORWARD);
        View focusCandidate = null;
        boolean foundFullyContainedFocusable = false;

        int count = focusables.size();
        for (int i = 0; i < count; i++) {
            View view = focusables.get(i);
            int viewLeft = view.getLeft();
            int viewRight = view.getRight();

            // 检查视图是否在目标区域内
            if (left >= viewRight || viewLeft >= right) {
                continue;
            }
            final boolean viewIsFullyContained = isViewFullyContained(left, right, viewLeft, viewRight);

            if (focusCandidate == null) {
                // 没有候选视图，采用当前视图
                focusCandidate = view;
                foundFullyContainedFocusable = viewIsFullyContained;
            } else {
                // 已有候选视图，比较哪个更合适
                boolean shouldUpdate = shouldUpdateFocusCandidate(
                        view, focusCandidate, viewIsFullyContained,
                        foundFullyContainedFocusable, leftFocus);

                if (shouldUpdate) {
                    focusCandidate = view;
                    foundFullyContainedFocusable = viewIsFullyContained;
                }
            }
        }
        return focusCandidate;
    }

    private boolean isViewFullyContained(int left, int right, int viewLeft, int viewRight) {
        return (left < viewLeft) && (viewRight < right);
    }

    private boolean shouldUpdateFocusCandidate(View newView, View currentCandidate,
                                               boolean newViewIsFullyContained,
                                               boolean foundFullyContainedFocusable,
                                               boolean leftFocus) {
        if (foundFullyContainedFocusable) {
            return newViewIsFullyContained
                    && isViewCloserToBoundary(newView, currentCandidate, leftFocus);
        } else {
            if (newViewIsFullyContained) {
                return true;
            } else {
                return isViewCloserToBoundary(newView, currentCandidate, leftFocus);
            }
        }
    }

    private boolean isViewCloserToBoundary(View newView, View currentCandidate, boolean leftFocus) {
        return (leftFocus && newView.getLeft() < currentCandidate.getLeft())
                || (!leftFocus && newView.getRight() > currentCandidate.getRight());
    }

    // Called by executeKeyEvent()
    public boolean pageScroll(int direction) {
        boolean right = direction == View.FOCUS_RIGHT;
        int width = getWidth();

        if (right) {
            mTempRect.left = getScrollX() + width;
            int count = getChildCount();
            if (count > 0) {
                View view = getChildAt(0);
                if (mTempRect.left + width > view.getRight()) {
                    mTempRect.left = view.getRight() - width;
                }
            }
        } else {
            mTempRect.left = getScrollX() - width;
            if (mTempRect.left < 0) {
                mTempRect.left = 0;
            }
        }
        mTempRect.right = mTempRect.left + width;

        return scrollAndFocus(direction, mTempRect.left, mTempRect.right);
    }

    // Called by executeKeyEvent()
    public boolean fullScroll(int direction) {
        boolean right = direction == View.FOCUS_RIGHT;
        int width = getWidth();

        mTempRect.left = 0;
        mTempRect.right = width;

        if (right) {
            int count = getChildCount();
            if (count > 0) {
                View view = getChildAt(0);
                mTempRect.right = view.getRight();
                mTempRect.left = mTempRect.right - width;
            }
        }

        return scrollAndFocus(direction, mTempRect.left, mTempRect.right);
    }

    // Called by fullScroll(), pageScroll()
    private boolean scrollAndFocus(int direction, int left, int right) {
        boolean handled = true;

        int width = getWidth();
        int containerLeft = getScrollX();
        int containerRight = containerLeft + width;
        boolean goLeft = direction == View.FOCUS_LEFT;

        View newFocused = findFocusableViewInBounds(goLeft, left, right);
        if (newFocused == null) {
            newFocused = this;
        }

        if (left >= containerLeft && right <= containerRight) {
            handled = false;
        } else {
            int delta = goLeft ? (left - containerLeft) : (right - containerRight);
            doScrollX(delta);
        }

        if (newFocused != findFocus()) {
            newFocused.requestFocus(direction);
        }

        return handled;
    }

    // Called by executeKeyEvent()
    public boolean arrowScroll(int direction) {

        View currentFocused = findFocus();
        if (currentFocused == this) {
            currentFocused = null;
        }

        View nextFocused = FocusFinder.getInstance().findNextFocus(this, currentFocused, direction);

        final int maxJump = getMaxScrollAmount();

        if (nextFocused != null && isWithinDeltaOfScreen(nextFocused, maxJump)) {
            nextFocused.getDrawingRect(mTempRect);
            offsetDescendantRectToMyCoords(nextFocused, mTempRect);
            int scrollDelta = computeScrollDeltaToGetChildRectOnScreen(mTempRect);
            doScrollX(scrollDelta);
            nextFocused.requestFocus(direction);
        } else {
            // no new focus
            int scrollDelta = maxJump;

            if (direction == View.FOCUS_LEFT && getScrollX() < scrollDelta) {
                scrollDelta = getScrollX();
            } else if (direction == View.FOCUS_RIGHT && getChildCount() > 0) {

                int daRight = getChildAt(0).getRight();

                int screenRight = getScrollX() + getWidth();

                if (daRight - screenRight < maxJump) {
                    scrollDelta = daRight - screenRight;
                }
            }
            if (scrollDelta == 0) {
                return false;
            }
            doScrollX(direction == View.FOCUS_RIGHT ? scrollDelta : -scrollDelta);
        }

        if (currentFocused != null && currentFocused.isFocused()
                && isOffScreen(currentFocused)) {
            /* previously focused item still has focus and is off screen, give
             * it up (take it back to ourselves)
             * (also, need to temporarily force FOCUS_BEFORE_DESCENDANTS so we are
             * sure to
             * get it)
             */
            final int descendantFocusability = getDescendantFocusability();  // save
            setDescendantFocusability(ViewGroup.FOCUS_BEFORE_DESCENDANTS);
            requestFocus();
            setDescendantFocusability(descendantFocusability);  // restore
        }
        return true;
    }

    // Called by arrowScroll(), onRequestFocusInDescendants()
    private boolean isOffScreen(View descendant) {
        return !isWithinDeltaOfScreen(descendant, 0);
    }

    // Called by isOffScreen(), arrowScroll(), onSizedChanged()
    private boolean isWithinDeltaOfScreen(View descendant, int delta) {
        descendant.getDrawingRect(mTempRect);
        offsetDescendantRectToMyCoords(descendant, mTempRect);

        return (mTempRect.right + delta) >= getScrollX()
                && (mTempRect.left - delta) <= (getScrollX() + getWidth());
    }

    // Called by scrollAndFocus(), arrowScroll(), onSizeChanged()
    private void doScrollX(int delta) {
        if (delta != 0) {
            if (mSmoothScrollingEnabled) {
                smoothCOUIScrollBy(delta, 0);
            } else {
                scrollBy(delta, 0);
            }
        }
    }

    /**
     * #ifndef COUI_EDIT
     * <EMAIL>, 2022-08-29 add for Feature-3586366
     * Called by doScrollX(), smoothScrollTo(), scrollToChildRect()
     */
    public final void smoothCOUIScrollBy(int dx, int dy) {
        if (getChildCount() == 0) {
            // Nothing to do.
            return;
        }
        long duration = AnimationUtils.currentAnimationTimeMillis() - mLastScroll;
        if (duration > ANIMATED_SCROLL_GAP) {
            final int width = getWidth() - getPaddingRight() - getPaddingLeft();
            final int right = getChildAt(0).getWidth();
            final int maxX = Math.max(0, right - width);
            final int scrollX = getScrollX();
            int adjustedDx = Math.max(0, Math.min(scrollX + dx, maxX)) - scrollX;
            setScrollStateToSettling();
            mOverScroller.startScroll(scrollX, getScrollY(), adjustedDx, 0);
            postInvalidateOnAnimation();
        } else {
            if (!mOverScroller.isCOUIFinished()) {
                mOverScroller.abortAnimation();
                setScrollStateToIdle();
                if (mFlingStrictSpan) {
                    mFlingStrictSpan = false;
                }
            }
            scrollBy(dx, dy);
        }
        mLastScroll = AnimationUtils.currentAnimationTimeMillis();
    }
    //#endif // COUI_EDIT

    // Called by performAccessibilityActionInternal()
    public final void smoothCOUIScrollTo(int x, int y) {
        smoothCOUIScrollBy(x - getScrollX(), y - getScrollY());
    }

    protected boolean overScrollBy(int deltaX, int deltaY,
                                   int scrollX, int scrollY,
                                   int scrollRangeX, int scrollRangeY,
                                   int maxOverScrollX, int maxOverScrollY,
                                   boolean isTouchEvent) {

        int newScrollX = scrollX + deltaX;
        int newScrollY = scrollY + deltaY;
        onOverScrolled(newScrollX, newScrollY, false, false);
        return false;
    }

    // Called by View.draw(), View.createSnapshot(), View.buildDrawingCacheImpl(), View.updateDisplayListIfDirty()
    @Override
    public void computeScroll() {
        if (mOverScroller.computeScrollOffset()) {
            int oldX = getScrollX();
            int oldY = getScrollY();
            int x = mOverScroller.getCOUICurrX();
            int y = mOverScroller.getCOUICurrY();
            if (oldX != x || oldY != y) {
                final int range = getScrollRange();

                overScrollBy(x - oldX, y - oldY, oldX, oldY, range, 0,
                        mOverflingDistance, 0, false);
                onScrollChanged(getScrollX(), getScrollY(), oldX, oldY);
            }

            if (mOverScroller.isCOUIFinished()) {
                setScrollStateToIdle();
            } else {
                setScrollStateToSettling();
            }

            if (!awakenScrollBars()) {
                // Keep on drawing until the animation has finished.
                postInvalidateOnAnimation();
            }
        } else {
            if (mFlingStrictSpan) {
                setScrollStateToIdle();
                mFlingStrictSpan = false;
            }
        }
    }

    // Called by requestChildFocus(), onLayout()
    private void scrollToChild(View child) {
        child.getDrawingRect(mTempRect);

        /* Offset from child's local coordinates to ScrollView coordinates */
        offsetDescendantRectToMyCoords(child, mTempRect);

        int scrollDelta = computeScrollDeltaToGetChildRectOnScreen(mTempRect);

        if (scrollDelta != 0) {
            scrollBy(scrollDelta, 0);
        }
    }

    // Called by requestChildRectangleOnScreen()
    private boolean scrollToChildRect(Rect rect, boolean immediate) {
        final int delta = computeScrollDeltaToGetChildRectOnScreen(rect);
        final boolean scroll = delta != 0;
        if (scroll) {
            if (immediate) {
                scrollBy(delta, 0);
            } else {
                smoothCOUIScrollBy(delta, 0);
            }
        }
        return scroll;
    }

    //
    @Override
    public void requestChildFocus(View child, View focused) {
        if (focused != null && focused.getRevealOnFocusHint()) {
            if (!mIsLayoutDirty) {
                scrollToChild(focused);
            } else {
                // The child may not be laid out yet, we can't compute the scroll yet
                mChildToScrollTo = focused;
            }
        }
        if (focused != null) {
            super.requestChildFocus(child, focused);
        }
    }

    // Called by ?
    @Override
    protected boolean onRequestFocusInDescendants(int direction,
                                                  Rect previouslyFocusedRect) {
        int adjustedDirection = direction;
        if (direction == View.FOCUS_FORWARD) {
            adjustedDirection = View.FOCUS_RIGHT;
        } else if (direction == View.FOCUS_BACKWARD) {
            adjustedDirection = View.FOCUS_LEFT;
        }

        final View nextFocus = previouslyFocusedRect == null
                ? FocusFinder.getInstance().findNextFocus(this,
                null, adjustedDirection)
                : FocusFinder.getInstance().findNextFocusFromRect(this,
                        previouslyFocusedRect, adjustedDirection);

        if (nextFocus == null) {
            return false;
        }

        if (isOffScreen(nextFocus)) {
            return false;
        }

        return nextFocus.requestFocus(adjustedDirection, previouslyFocusedRect);
    }

    // Called by View
    @Override
    public boolean requestChildRectangleOnScreen(View child, Rect rectangle,
                                                 boolean immediate) {
        // offset into coordinate space of this scroll view
        rectangle.offset(child.getLeft() - child.getScrollX(),
                child.getTop() - child.getScrollY());

        return scrollToChildRect(rectangle, immediate);
    }

    // Called by setFillViewport(), onRestoreInstanceState()
    @Override
    public void requestLayout() {
        mIsLayoutDirty = true;
        super.requestLayout();
    }

    // Called by View?
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        if (mScrollStrictSpan) {
            mScrollStrictSpan = false;
        }
        if (mFlingStrictSpan) {
            mFlingStrictSpan = false;
        }

        mSpringOverScroller.cancelCallback();
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        if (visibility != VISIBLE) {
            mSpringOverScroller.abortAnimation();
            mSpringOverScroller.cancelCallback();
        }
    }

    // Called by View
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        final DisplayMetrics metrics = getContext().getResources().getDisplayMetrics();
        mOverflingDistance = mOverscrollDistance = metrics.widthPixels;
        mScreenWidth = metrics.widthPixels;
        View currentFocused = findFocus();
        if (null == currentFocused || this == currentFocused) {
            return;
        }

        final int maxJump = getRight() - getLeft();

        if (isWithinDeltaOfScreen(currentFocused, maxJump)) {
            currentFocused.getDrawingRect(mTempRect);
            offsetDescendantRectToMyCoords(currentFocused, mTempRect);
            int scrollDelta = computeScrollDeltaToGetChildRectOnScreen(mTempRect);
            doScrollX(scrollDelta);
        }
    }

    // Called by onTouchEvent()
    public void fling(int velocityX) {
        mFlingVelocityX = velocityX;

        if (getChildCount() > 0) {
            int width = getWidth() - getPaddingRight() - getPaddingLeft();
            int right = getChildAt(0).getRight() - getPaddingLeft();

            int maxScroll = Math.max(0, right - width);
            setScrollStateToSettling();
            mOverScroller.fling(getScrollX(), getScrollY(), velocityX, 0, 0,
                    maxScroll, 0, 0, width / 2, 0);

            if (!mFlingStrictSpan) {
                mFlingStrictSpan = true;
            }

            final boolean movingRight = velocityX > 0;

            View currentFocused = findFocus();
            View newFocused = findFocusableViewInMyBounds(movingRight,
                    mOverScroller.getCOUIFinalX(), currentFocused);

            if (newFocused == null) {
                newFocused = this;
            }

            if (newFocused != currentFocused) {
                newFocused.requestFocus(movingRight ? View.FOCUS_RIGHT : View.FOCUS_LEFT);
            }

            postInvalidateOnAnimation();
        }
    }

    /**
     * Find view to dispatch click event.
     *
     * @param event
     * @return
     */
    private View findViewToDispatchClickEvent(MotionEvent event) {
        if (!isClickEvent(event)) {
            return null;
        }
        View target = null;
        Rect frame = new Rect();
        for (int i = getChildCount() - 1; i >= 0; i--) {
            View child = getChildAt(i);
            if (child.getVisibility() == View.VISIBLE || child.getAnimation() != null) {
                child.getHitRect(frame);
                boolean isContains = frame.contains((int) event.getX() + getScrollX(), (int) event.getY() + getScrollY());
                MotionEvent newEvent = MotionEvent.obtain(event);
                newEvent.offsetLocation(getScrollX() - child.getLeft(), getScrollY() - child.getTop());
                if (isContains && dispatchClickEvent(child, newEvent)) {
                    target = child;
                }
            }
        }
        return target;
    }

    /**
     * When the following two conditions are met, the motion event can be regarded as a click event:
     * 1、duration is less than 100
     * 2、offset less than 10
     *
     * @param event
     * @return
     */
    private boolean isClickEvent(MotionEvent event) {
        int offsetX = (int) (event.getX() - mInitialTouchX);
        int offset = (int) Math.sqrt(offsetX * offsetX);
        long touchDuration = System.currentTimeMillis() - mTouchTime;
        return touchDuration < OVER_SCROLL_TOUCH_DURATION_THRESHOLD && offset < OVER_SCROLL_TOUCH_OFFSET_THRESHOLD;
    }

    /**
     * Manually trigger a click events.
     *
     * @param view
     * @param event
     * @return
     */
    private boolean dispatchClickEvent(@NonNull View view, @NonNull MotionEvent event) {
        int[] actions = new int[]{MotionEvent.ACTION_DOWN, MotionEvent.ACTION_UP};
        boolean isHandled = true;
        for (int action : actions) {
            event.setAction(action);
            isHandled &= view.dispatchTouchEvent(event);
        }
        return isHandled;
    }

    static class COUISavedState extends BaseSavedState {
        public int scrollOffsetFromStart;

        COUISavedState(Parcelable superState) {
            super(superState);
        }

        public COUISavedState(Parcel source) {
            super(source);
            scrollOffsetFromStart = source.readInt();
        }

        public COUISavedState(Parcel source, ClassLoader loader) {
            super(source, loader);
            scrollOffsetFromStart = source.readInt();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeInt(scrollOffsetFromStart);
        }

        @Override
        public String toString() {
            return "HorizontalScrollView.SavedState{"
                    + Integer.toHexString(System.identityHashCode(this))
                    + " scrollPosition=" + scrollOffsetFromStart
                    + "}";
        }

        public static final Creator<COUISavedState> CREATOR
                = new Creator<COUISavedState>() {
            public COUISavedState createFromParcel(Parcel in) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    return new COUISavedState(in, COUISavedState.class.getClassLoader());
                } else {
                    return new COUISavedState(in);
                }
            }

            public COUISavedState[] newArray(int size) {
                return new COUISavedState[size];
            }
        };
    }

    /*---------- 以下为在 COUIHorizontalScrollView 的基础上添加的代码 start ---------- */

    /**
     * 返回 COUIHorizontalScrollView 当前的滚动状态。
     *
     * @return {@link #SCROLL_STATE_IDLE}、{@link #SCROLL_STATE_DRAGGING} 或
     * {@link #SCROLL_STATE_SETTLING}
     */
    public int getScrollState() {
        return mScrollState;
    }

    /**
     * 终止当前view的运动状态
     */
    public void stopScroll() {
        stopScrollersInternal();
        setScrollStateToIdle();
    }

    /**
     * 当滚动状态变更时，回调此方法
     *
     * @param state
     */
    public void onScrollStateChanged(int state) {
        // Do nothing
    }

    /**
     * 设置滚动状态监听
     *
     * @param listener
     */
    public void setOnScrollStateChangeListener(OnScrollStateChangeListener listener) {
        this.mScrollStateChangeListener = listener;
    }

    /**
     * 滚动状态变更Listener
     */
    public abstract static class OnScrollStateChangeListener {
        /**
         * 当 COUIHorizontalScrollView 的滚动状态发生变化时的回调方法。
         *
         * @param horizontalScrollView 滚动状态已更改的 COUIHorizontalScrollView。
         * @param newState             更新后的滚动状态。 {@link #SCROLL_STATE_IDLE}，
         *                             {@link #SCROLL_STATE_DRAGGING} 或 {@link #SCROLL_STATE_SETTLING}。
         */
        public void onScrollStateChanged(@NonNull GalleryHorizontalScrollView horizontalScrollView, int newState) {
        }
    }

    private void dispatchOnScrollStateChanged(int state) {
        onScrollStateChanged(state);
        if (mScrollStateChangeListener != null) {
            mScrollStateChangeListener.onScrollStateChanged(this, state);
        }
    }

    private void setScrollStateToSettling() {
        setScrollState(SCROLL_STATE_SETTLING);
    }

    private void setScrollStateToDragging() {
        setScrollState(SCROLL_STATE_DRAGGING);
    }

    private void setScrollStateToIdle() {
        setScrollState(SCROLL_STATE_IDLE);
    }

    private void setScrollState(int state) {
        if (state == mScrollState) {
            return;
        }
        if (state != SCROLL_STATE_SETTLING) {
            mFlingStrictSpan = false;
            stopScrollersInternal();
        }
        mScrollState = state;
        dispatchOnScrollStateChanged(state);
    }

    private void stopScrollersInternal() {
        if (!mOverScroller.isCOUIFinished()) {
            mOverScroller.abortAnimation();
        }
    }
    /*---------- 在 COUIHorizontalScrollView 的基础上添加的代码 end ---------- */
}