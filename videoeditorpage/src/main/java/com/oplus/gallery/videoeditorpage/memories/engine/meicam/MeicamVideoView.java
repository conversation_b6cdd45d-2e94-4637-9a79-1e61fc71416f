/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MeicamVideoView.java
 * * Description: video view extends NvsLiveWindow.
 * * Version: 1.0
 * * Date : 2017/12/24
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/24    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import android.content.Context;
import android.util.AttributeSet;
import com.meicam.sdk.NvsLiveWindow;

public class MeicamVideoView extends NvsLiveWindow {
    public MeicamVideoView(Context context) {
        super(context);
    }

    public MeicamVideoView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MeicamVideoView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public MeicamVideoView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (!changed) {
            return;
        }
        onSizeChanged(left, top, right, bottom);
    }
}
