/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MemoriesView.java
 * * Description: EngineView extends Nvs Live Window
 * * Version: 1.0
 * * Date : 2017/11/10
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/10    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.ui;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Interpolator;
import android.view.animation.PathInterpolator;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig;

public class MemoriesView extends RelativeLayout {

    private static final String TAG = "MemoriesView";
    private static final double FLOAT_0 = 1e-6;
    /**
     * TimeSeekBar动画时间及interpolator
     */
    private static final int SEEK_BAR_DURATION = 420;
    private static final int SEEK_BAR_IN_DELAY = 10;
    private static final int SEEK_BAR_OUT_DELAY = 0;
    private static final Interpolator INTERPOLATOR_TIME_SEEK_BAR =
            new PathInterpolator(0.17f, 0.17f, 0.1f, 1.0f);
    /**
     * 操作选项和TitleBar动画时间及interpolator
     */
    private static final int EDITOR_MENU_IN_DURATION = 300;
    private static final int EDITOR_MENU_OUT_DURATION = 150;
    private static final PathInterpolator INTERPOLATOR_EDITORMENU =
            new PathInterpolator(0.17f, 0.17f, 0.1f, 1.0f);
    // VideoView动画时间及interpolator
    private static final int VIDEO_VIEW_DURATION = 420;
    private static final PathInterpolator INTERPOLATOR_VIDEO_VIEW =
            new PathInterpolator(0.17f, 0.17f, 0.1f, 1.0f);
    // ActionBar动画时间及interpolator
    private static final int ACTIONBAR_DURATION_IN = 150;
    private static final int ACTIONBAR_DURATION_OUT = 280;
    private static final int BOTTOM_AREA_ALPHA_DURATION = 150;
    private static final PathInterpolator INTERPOLATOR_ACTIONBAR =
            new PathInterpolator(0.33f, 0f, 0.67f, 1f);

    public enum State {
        NORMAL, EDIT
    }

    private ViewGroup mGalleryEditorVideoView;
    private FrameLayout mTimeSeekBar;
    private View mTimeSeekBarContent;
    private ControlBarView mControlBarView;
    private LinearLayout mActionBar;
    private View mEditorTitleBar;
    private View mEditorMenuBar;
    private View mBottomAreaView;
    private AnimatorSet mAnimator;
    private State mState = State.NORMAL;

    private int mActionBarDefaultHorizontalPadding = 0;
    private int mActionBarDefaultHeight = 0;
    private int mSeekBarDefaultHeight = 0;
    private int mEditorMenuBarHeight = 0;
    private int mEditorMenuBarWidthLandscape = 0;
    private int mEditorTitleBarHeight = 0;

    private int mVideoViewEditModeMarginTop = 0;
    private int mVideoViewEditModeMarginTopLandscape = 0;
    private int mVideoViewEditModeMarginBottom = 0;

    private int mBottomNavigationBarHeight = 0;

    private BaseActivity mBaseActivity;

    private boolean mIsFirstEnterMemories = true;

    public MemoriesView(Context context) {
        this(context, null);
    }

    public MemoriesView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MemoriesView(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public MemoriesView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mBaseActivity = (BaseActivity) context;
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        mGalleryEditorVideoView = findViewById(R.id.engine_view);
        mTimeSeekBar = findViewById(R.id.time_seek_bar);
        mTimeSeekBarContent = findViewById(R.id.time_seek_bar_content);
        mActionBar = findViewById(R.id.action_bar);
        mBottomAreaView = findViewById(R.id.bottom_area_view);
        mEditorTitleBar = findViewById(R.id.title_bar_container);
        mEditorMenuBar = findViewById(R.id.toolbar_container);
        mControlBarView = findViewById(R.id.control_bar);
        mControlBarView.setEditorVideoView(mGalleryEditorVideoView);
        mControlBarView.setTimeSeekBar(mTimeSeekBar);
        mActionBarDefaultHorizontalPadding = getResources().getDimensionPixelSize(R.dimen.memories_editor_action_bar_padding_start);
        mActionBarDefaultHeight = getResources().getDimensionPixelSize(R.dimen.memories_editor_action_bar_with_status_bar_height);
        mSeekBarDefaultHeight = getResources().getDimensionPixelSize(R.dimen.memories_editor_time_seek_bar_layout_height);
        mEditorMenuBarHeight = getResources().getDimensionPixelSize(R.dimen.memories_editor_toolbar_height);
        mEditorMenuBarWidthLandscape = getResources().getDimensionPixelSize(R.dimen.video_editor_toolbar_width_landscape);
        mEditorTitleBarHeight = getResources().getDimensionPixelSize(R.dimen.video_editor_titlebar_height);
        mVideoViewEditModeMarginTopLandscape = getResources().getDimensionPixelSize(R.dimen.video_editor_preview_safe_area_size_landscape);
        mVideoViewEditModeMarginTop = getResources().getDimensionPixelSize(R.dimen.video_editor_preview_safe_area_size);
        mVideoViewEditModeMarginBottom = getResources().getDimensionPixelSize(R.dimen.video_editor_bottom_container_bar_height);
    }

    private void updateControlBarView() {
        if (mControlBarView.getPaddingBottom() != mBottomNavigationBarHeight) {
            mControlBarView.setPadding(
                    0,
                    0,
                    0,
                    mBottomNavigationBarHeight
            );
        }
        int bottomAreaViewHeight = mSeekBarDefaultHeight + mBottomNavigationBarHeight;
        if (mBottomAreaView.getLayoutParams().height != bottomAreaViewHeight) {
            mBottomAreaView.getLayoutParams().height = bottomAreaViewHeight;
        }
    }

    public void updateActionBarView(boolean isLandscape) {
        int horizontalPadding = mActionBarDefaultHorizontalPadding + mBaseActivity.horizontalNaviBarHeight(true);
        if ((mActionBar.getPaddingStart() != horizontalPadding) || (mActionBar.getPaddingEnd() != horizontalPadding)) {
            mActionBar.setPadding(horizontalPadding, 0, horizontalPadding, 0);
        }
        int statusBarHeight = isLandscape ? 0 : mBaseActivity.statusBarHeight(true);
        int actionBarHeight = mActionBarDefaultHeight + statusBarHeight;
        if (mActionBar.getLayoutParams().height != actionBarHeight) {
            mActionBar.getLayoutParams().height = actionBarHeight;
            mActionBar.requestLayout();
        }
    }

    public void dispatchNaviHeightChanged(int naviHeight) {
        if (mBottomNavigationBarHeight != naviHeight || mIsFirstEnterMemories) {
            mIsFirstEnterMemories = false;
            mBottomNavigationBarHeight = naviHeight;
            updateControlBarView();
            updateVideoViewPosition(false, 0);
        }
    }

    public boolean isNormalState() {
        return mState == State.NORMAL;
    }

    public boolean isSwitchingState() {
        return ((mAnimator != null) && mAnimator.isRunning());
    }

    private void updateTimeSeekBarPosition(boolean isAnim, float progress) {
        if (mTimeSeekBar == null) {
            GLog.e(TAG, "updateTimeSeekBar, mTimeSeekBar = " + mTimeSeekBar);
            return;
        }
        ViewGroup.LayoutParams layoutParams = mTimeSeekBar.getLayoutParams();
        if (!(layoutParams instanceof FrameLayout.LayoutParams)) {
            GLog.e(TAG, "updateTimeSeekBar, layoutParams class error " + layoutParams);
            return;
        }
        if (!isAnim) {
            progress = isNormalState() ? 0f : 1f;
        }
        AppUiResponder.AppUiConfig appUiConfig = mBaseActivity.getCurrentAppUiConfig();
        int defSafeWidth = VideoEditorUIConfig.getDefSafeWidth(appUiConfig);
        int paddingRight = mBaseActivity.rightNaviBarHeight(false) + defSafeWidth;
        int paddingLeft = mBaseActivity.leftNaviBarHeight(false) + defSafeWidth;
        int bottomMargin = mEditorTitleBarHeight + mEditorMenuBarHeight;
        boolean isLandscapeLayout = EditorUIConfig.isEditorLandscape(appUiConfig);
        if (isLandscapeLayout) {
            bottomMargin = mEditorTitleBarHeight;
            paddingRight = mBaseActivity.rightNaviBarHeight(false) + defSafeWidth + mEditorMenuBarWidthLandscape;
        }
        layoutParams.width = LayoutParams.MATCH_PARENT;
        bottomMargin = (int) (bottomMargin * progress);
        ((FrameLayout.LayoutParams)layoutParams).bottomMargin = bottomMargin;
        paddingLeft = (int) (paddingLeft * progress);
        paddingRight = (int) (paddingRight * progress);
        mTimeSeekBar.setPadding(paddingLeft,0,paddingRight,0);
        mTimeSeekBar.requestLayout();
    }

    private void updateVideoViewPosition(boolean isAnim, float progress) {
        if (mGalleryEditorVideoView == null) {
            GLog.e(TAG, "updateVideoViewPosition, mGalleryVideoView is null.");
            return;
        }
        ViewGroup.LayoutParams lp = mGalleryEditorVideoView.getLayoutParams();
        if (!(lp instanceof RelativeLayout.LayoutParams)) {
            GLog.e(TAG, "updateVideoViewPosition, layoutParams class error " + lp);
            return;
        }
        if (!isAnim) {
            progress = isNormalState() ? 0f : 1f;
        }
        boolean isLandscape = EditorUIConfig.isEditorLandscape(mBaseActivity.getCurrentAppUiConfig());
        int paddingLeft = VideoEditorUIConfig.getLeftSafeWidth();
        int paddingRight = VideoEditorUIConfig.getRightSafeWidth();
        int paddingTop = mVideoViewEditModeMarginTop;
        int paddingBottom = mBottomNavigationBarHeight + mVideoViewEditModeMarginBottom;
        if (isLandscape) {
            paddingRight = paddingRight + mEditorMenuBarWidthLandscape;
            paddingTop = mVideoViewEditModeMarginTopLandscape;
            paddingBottom = mBottomNavigationBarHeight + mEditorTitleBarHeight;
        }
        if (progress <= FLOAT_0) {
            progress = 0;
        }
        paddingLeft = (int) (paddingLeft * progress);
        paddingTop = (int) (paddingTop * progress);
        paddingRight = (int) (paddingRight * progress);
        paddingBottom = (int) (paddingBottom * progress);
        boolean isPaddingChange = (mGalleryEditorVideoView.getPaddingLeft() != paddingLeft)
                || (mGalleryEditorVideoView.getPaddingTop() != paddingTop)
                || (mGalleryEditorVideoView.getPaddingRight() != paddingRight)
                || (mGalleryEditorVideoView.getPaddingBottom() != paddingBottom);
        if (isPaddingChange) {
            mGalleryEditorVideoView.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom);
        }
    }

    public void changeState(final State state, final boolean animated, final int delay) {
        GLog.d(TAG, "changeState -- mState = " + mState + ", state = " + state);
        if (mState == state) {
            return;
        }

        mState = state;
        // cancel any running transition animations
        if (mAnimator != null) {
            GLog.w(TAG, "changeState --- isAnimating = " + mAnimator.isRunning());
            mAnimator.cancel();
            mAnimator = null;
        }

        // Initialize animation arrays for the first time if necessary
        mAnimator = new AnimatorSet();

        final boolean isToEditMode = (state == State.EDIT);
        if (animated) {
            // video view
            ValueAnimator videoViewAnim = getVideoViewAnimator(isToEditMode);

            // time seek bar
            ValueAnimator timeSeekBarAnim = getTimeSeekBarAnimator(isToEditMode);

            // action bar
            ValueAnimator actionBarAnim = getActionBarAnimator(isToEditMode);

            // editor done/cancel btn bar
            ValueAnimator editorBtnBarAnim = getEditorButtonBarAnimator(isToEditMode);

            // editor preview menu bar
            ValueAnimator editorMenuBarAnim = getEditorMenuBarAnimator(isToEditMode);

            // bottom area view
            ValueAnimator bottomAreaViewAnim = getBottomViewAnimator(isToEditMode);

            // play all anim
            mAnimator.playTogether(videoViewAnim, timeSeekBarAnim, actionBarAnim, editorBtnBarAnim, editorMenuBarAnim, bottomAreaViewAnim);
            mAnimator.setStartDelay(delay);
            mAnimator.start();
        } else {
            // else 分支没有业务逻辑走？
            float fromValue = isToEditMode ? 0f : 1f;
            if (mActionBar != null) {
                mActionBar.setAlpha(fromValue);
            }

            float toValue = isToEditMode ? 1f : 0f;
            updateVideoViewPosition(false, toValue);
            updateTimeSeekBarPosition(false, toValue);
            if (mEditorTitleBar != null) {
                mEditorTitleBar.setAlpha(toValue);
            }
            if (mEditorMenuBar != null) {
                mEditorMenuBar.setAlpha(toValue);
            }
            if (!isToEditMode) {
                ControlBarView barView = findViewById(R.id.control_bar);
                barView.hidePreviewState();
            }
        }
    }

    // create video view anim
    private ValueAnimator getVideoViewAnimator(final boolean isToEditMode) {
        ValueAnimator videoViewAnim = ValueAnimator.ofFloat(isToEditMode ? 0f : 1f, isToEditMode ? 1f : 0f);
        videoViewAnim.setDuration(VIDEO_VIEW_DURATION);
        videoViewAnim.setInterpolator(INTERPOLATOR_VIDEO_VIEW);
        videoViewAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                GLog.d(TAG, "getVideoViewAnimator.onAnimationEnd");
                updateVideoViewPosition(true, isToEditMode ? 1f : 0f);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                GLog.d(TAG, "getVideoViewAnimator.onAnimationStart");
                updateVideoViewPosition(true, isToEditMode ? 0f : 1f);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                GLog.d(TAG, "getVideoViewAnimator.onAnimationCancel");
            }
        });
        videoViewAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getVideoViewAnimator.onAnimationUpdate value is null");
                return;
            }
            updateVideoViewPosition(true, (float) value);
        });

        return videoViewAnim;
    }

    // create time seek bar anim
    private ValueAnimator getTimeSeekBarAnimator(final boolean isToEditMode) {
        ValueAnimator timeSeekBarAnim = ValueAnimator.ofFloat(isToEditMode ? 0f : 1f, isToEditMode ? 1f : 0f);
        timeSeekBarAnim.setDuration(SEEK_BAR_DURATION);
        timeSeekBarAnim.setInterpolator(INTERPOLATOR_TIME_SEEK_BAR);
        timeSeekBarAnim.setStartDelay(isToEditMode ? SEEK_BAR_IN_DELAY : SEEK_BAR_OUT_DELAY);
        timeSeekBarAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                GLog.d(TAG, "getTimeSeekBarAnimator.onAnimationEnd");
                updateTimeSeekBarPosition(true, isToEditMode ? 1f : 0f);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                GLog.d(TAG, "getTimeSeekBarAnimator.onAnimationStart");
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                GLog.d(TAG, "getTimeSeekBarAnimator.onAnimationCancel");
            }
        });
        timeSeekBarAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getTimeSeekBarAnimator.onAnimationUpdate value is null");
                return;
            }
            updateTimeSeekBarPosition(true, (float) value);
        });

        return timeSeekBarAnim;
    }

    // create action bar anim
    private ValueAnimator getActionBarAnimator(final boolean isToEditMode) {
        ValueAnimator actionBarAnim = ValueAnimator.ofFloat(isToEditMode ? 1f : 0f, isToEditMode ? 0f : 1f);
        actionBarAnim.setDuration(ACTIONBAR_DURATION_IN);
        actionBarAnim.setInterpolator(INTERPOLATOR_ACTIONBAR);
        actionBarAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                float value = isToEditMode ? 0f : 1f;
                GLog.d(TAG, "getActionBarAnimator.onAnimationEnd value = " + value);
                if (mActionBar != null) {
                    mActionBar.setAlpha(value);
                    mActionBar.setVisibility(isToEditMode ? GONE : VISIBLE);
                }
            }

            @Override
            public void onAnimationStart(Animator animation) {
                GLog.d(TAG, "getActionBarAnimator.onAnimationStart");
                mActionBar.setVisibility(VISIBLE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                GLog.d(TAG, "getActionBarAnimator.onAnimationCancel");
            }
        });
        actionBarAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getActionBarAnimator.onAnimationUpdate value is null");
                return;
            }
            if (mActionBar != null) {
                mActionBar.setAlpha((float) value);
            }
        });
        return actionBarAnim;
    }

    /**
     * 作用在回忆播放主页底部代替进度条以及导航栏位置 进出编辑使用Alpha动画
     */
    private ValueAnimator getBottomViewAnimator(final boolean isToEditMode) {
        ValueAnimator bottomAreaViewAnim = ValueAnimator.ofFloat(isToEditMode ? 1f : 0f, isToEditMode ? 0f : 1f);
        bottomAreaViewAnim.setDuration(BOTTOM_AREA_ALPHA_DURATION);
        bottomAreaViewAnim.setInterpolator(INTERPOLATOR_ACTIONBAR);
        bottomAreaViewAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                float value = isToEditMode ? 0f : 1f;
                GLog.d(TAG, "getBottomAreaViewAnimator.onAnimationEnd value = " + value);
                if (mBottomAreaView != null) {
                    mBottomAreaView.setAlpha(value);
                    mBottomAreaView.setVisibility(isToEditMode ? GONE : VISIBLE);
                }
            }

            @Override
            public void onAnimationStart(Animator animation) {
                GLog.d(TAG, "getBottomAreaViewAnimator.onAnimationStart");
                mBottomAreaView.setVisibility(VISIBLE);
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                GLog.d(TAG, "getBottomAreaViewAnimator.onAnimationCancel");
            }
        });
        bottomAreaViewAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getBottomAreaViewAnimator.onAnimationUpdate value is null");
                return;
            }
            if (mBottomAreaView != null) {
                mBottomAreaView.setAlpha((float) value);
            }
        });
        return bottomAreaViewAnim;
    }

    // create editor done/cancel button bar anim
    private ValueAnimator getEditorButtonBarAnimator(final boolean isToEditMode) {
        mEditorTitleBar = findViewById(R.id.title_bar_container);
        ValueAnimator editorBtnBarAnim = ValueAnimator.ofFloat(isToEditMode ? 0f : 1f, isToEditMode ? 1f : 0f);
        editorBtnBarAnim.setDuration(isToEditMode ? EDITOR_MENU_IN_DURATION : EDITOR_MENU_OUT_DURATION);
        editorBtnBarAnim.setInterpolator(INTERPOLATOR_EDITORMENU);
        editorBtnBarAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                float value = isToEditMode ? 1f : 0f;
                GLog.d(TAG, "getEditorButtonBarAnimator.onAnimationEnd value = " + value);
                if (mEditorTitleBar != null) {
                    mEditorTitleBar.setAlpha(value);
                }
            }

            @Override
            public void onAnimationStart(Animator animation) {
                GLog.d(TAG, "getEditorButtonBarAnimator.onAnimationStart");
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                GLog.d(TAG, "getEditorButtonBarAnimator.onAnimationCancel");
            }
        });
        editorBtnBarAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getEditorButtonBarAnimator.onAnimationUpdate value is null");
                return;
            }
            if (mEditorTitleBar != null) {
                mEditorTitleBar.setAlpha((float) value);
            }
        });
        return editorBtnBarAnim;
    }

    // create editor preview menu bar anim
    private ValueAnimator getEditorMenuBarAnimator(final boolean isToEditMode) {
        mEditorMenuBar = findViewById(R.id.toolbar_container);

        ValueAnimator editorMenuBarAnim = ValueAnimator.ofFloat(isToEditMode ? 0f : 1f, isToEditMode ? 1f : 0f);
        editorMenuBarAnim.setDuration(isToEditMode ? EDITOR_MENU_IN_DURATION : EDITOR_MENU_OUT_DURATION);
        editorMenuBarAnim.setInterpolator(INTERPOLATOR_EDITORMENU);
        editorMenuBarAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                float value = isToEditMode ? 1f : 0f;
                GLog.d(TAG, "getEditorMenuBarAnimator.onAnimationEnd value = " + value);
                if (mEditorMenuBar != null) {
                    mEditorMenuBar.setAlpha(value);
                }
                if (!isToEditMode) {
                    ControlBarView barView = findViewById(R.id.control_bar);
                    barView.hidePreviewState();
                }
            }

            @Override
            public void onAnimationStart(Animator animation) {
                GLog.d(TAG, "getEditorMenuBarAnimator.onAnimationStart");
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                GLog.d(TAG, "getEditorMenuBarAnimator.onAnimationCancel");
            }
        });
        editorMenuBarAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getEditorMenuBarAnimator.onAnimationUpdate value is null");
                return;
            }
            if (mEditorMenuBar != null) {
                mEditorMenuBar.setAlpha((float) value);
            }
        });

        return editorMenuBarAnim;
    }

    public void updateAppUiConfig(AppUiResponder.AppUiConfig uiConfig) {
        if (mControlBarView != null) {
            mControlBarView.updateAppUiConfig(uiConfig);
        }
        updateTimeSeekBarPaddingWhenAppUiConfigChanged();
    }

    public void updateTimeSeekBarPaddingWhenAppUiConfigChanged() {
        int windowWidthDp = ScreenUtils.pixelToDp(mBaseActivity.getCurrentAppUiConfig().getWindowWidth().getCurrent());
        int timeSeekBarWidth = ScreenUtils.dpToPixel(VideoEditorUIConfig.getTimeSeekBarWidth(windowWidthDp));
        mTimeSeekBarContent.getLayoutParams().width = timeSeekBarWidth
                - mBaseActivity.horizontalNaviBarHeight(false);
        mTimeSeekBarContent.requestLayout();
    }
}
