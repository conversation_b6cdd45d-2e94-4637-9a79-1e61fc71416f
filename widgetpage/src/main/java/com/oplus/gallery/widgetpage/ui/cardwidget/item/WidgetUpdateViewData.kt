/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetUpdateViewData.kt
 ** Description:用于某个卡片的刷新
 ** Version: 1.0
 ** Date : 2021/11/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/25        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item

abstract class WidgetUpdateViewData(
    val layoutName: String
) {
    fun getViewType(): String = javaClass.name
}