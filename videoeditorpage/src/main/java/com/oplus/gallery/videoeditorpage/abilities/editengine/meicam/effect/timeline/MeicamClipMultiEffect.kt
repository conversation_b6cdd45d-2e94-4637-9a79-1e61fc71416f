/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamClipMultiEffect.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.timeline

import com.google.gson.JsonSyntaxException
import com.meicam.sdk.NvsVideoFx
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.custom.CustomClipEffect
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel.ClipType.CLIP_ADJUST

/**
 * 多特效美摄时间线特效对象
 * 适用于一个功能对应多个特效（目前只有调节功能用到）
 */
class MeicamClipMultiEffect(name: String, private val fxType: Int) : CustomClipEffect(name) {

    init {
        mClassType = JSON_TYPE_NAME
    }

    /**
     * 自动调节算法是通过关联的算法的数值来单独下发的，所以这里需要额外单独记录自动调节按钮的开关状态
     */
    var autoSwitch = false

    /**
     * 特效数据映射，key为特效的名称 @see {com.oplus.gallery.videoeditorpage.video.business.adjust.FxNameKey}
     * value为美摄对象
     */
    private val nvsVideoFxMap = hashMapOf<String, NvsVideoFx>()

    /**
     * 特效数据映射，用于记录UI数据用于还原，key为特效的位置索引
     * value的key为用于显示的数值，value的value为开关
     */
    private val effectDataMap = hashMapOf<Int, Pair<Number, Boolean>>()

    /**
     * 特效数据映射，key为特效的名称 @see {com.oplus.gallery.videoeditorpage.video.business.adjust.FxNameKey}
     * 一个特效名称可能对应多个算法，所以value用于存储这些子算法的名称和数值
     * value中的key 为子算法的名称  BasicImageAdjustPropertyKey/TintPropertyKey/SharpenPropertyKey/DefinitionPropertyKey/VignettePropertyKey/DenoisePropertyKey
     * value中的value 为子算法的数值
     */
    private val algoDataMap = hashMapOf<String, HashMap<String, Number>>()

    override fun getType(): Int {
        return fxType
    }

    /**
     * 根据特效map拷贝一个业务端的特效对象
     */
    fun copy(map: Map<String, NvsVideoFx>): MeicamClipMultiEffect {
        val effect = MeicamClipMultiEffect(name, fxType)
        effect.nvsVideoFxMap.putAll(map)
        effectDataMap.forEach { (key, value) ->
            effect.setEffectValue(Triple(key, value.first, value.second))
        }
        algoDataMap.forEach { (name, value) ->
            val copyMap = HashMap<String, Number>(value)
            effect.applyAlgoDataMap(name, copyMap)
        }
        effectType = CLIP_ADJUST
        effectPlayDuration = outTime - inTime
        effect.inTime = inTime
        effect.outTime = outTime
        effect.chName = chName
        effect.zhName = zhName
        effect.enName = enName
        setTrackIndex(0)
        return effect
    }

    /**
     * 将给定的映射添加到算法数据映射中。
     *
     * @param name 映射的名称
     * @param map 包含算法相关数据的映射
     */
    private fun applyAlgoDataMap(name: String, map: HashMap<String, Number>) {
        algoDataMap[name] = map
    }

    /**
     * 设置特效数据
     * @param effectData 特效数据
     */
    fun setEffectValue(effectData: Triple<Int, Number, Boolean>) {
        effectDataMap[effectData.first] = Pair(effectData.second, effectData.third)
    }

    /**
     * 获取特效数据
     * @return 特效数据
     */
    fun getEffectDataMap(): Map<Int, Pair<Number, Boolean>> {
        return effectDataMap
    }

    /**
     * 设置特效
     * @param name 特效名称
     * @param nvsVideoFx 特效对象
     */
    fun setVideoFx(name: String, nvsVideoFx: NvsVideoFx) {
        nvsVideoFxMap[name] = nvsVideoFx
    }

    /**
     * 应用特效数据
     * @param name 特效名称
     */
    fun applyData(name: String) {
        algoDataMap[name]?.let {
            setAlgoData(name, it, false)
        }
    }

    /**
     * 设置特效算法数据
     * @param name 特效名称
     * @param algoData 算法数据
     */
    fun setAlgoData(name: String, algoData: HashMap<String, Number>, keepData: Boolean? = true) {
        val nvsVideoFx = nvsVideoFxMap[name]
        nvsVideoFx?.let {
            algoData.entries.forEach { entry ->
                if (keepData == true) {
                    val map = algoDataMap[name]
                    if (map != null) {
                        map[entry.key] = entry.value
                    } else {
                        algoDataMap[name] = HashMap(algoData)
                    }
                }
                val value = entry.value
                if (value is Float) {
                    nvsVideoFx.setFloatVal(entry.key, value.toDouble())
                    GLog.d(TAG, LogFlag.DL) { "[setAlgoData]: key:${entry.key}, value:${value.toDouble()}" }
                } else if (value is Int) {
                    nvsVideoFx.setIntVal(entry.key, value.toInt())
                    GLog.d(TAG, LogFlag.DL) { "[setAlgoData]: key:${entry.key}, value:${value.toInt()}" }
                }
            }
        }
    }

    /**
     * 判断特效是否包含算法数据
     * @param name 特效名称
     */
    fun isContainAlgo(name: String): Boolean {
        return nvsVideoFxMap.keys.contains(name)
    }

    /**
     * 获取特效列表
     * @return List<NvsVideoFx> 特效列表
     */
    override fun getNvsVideoFxList(): List<NvsVideoFx> {
        return nvsVideoFxMap.values.toList()
    }

    /**
     * 获取特效列表map (由于一个功能有多个特效，因此这里存储的是一个map)
     * @return Map<String, NvsVideoFx> 特效列表
     */
    fun getNvsVideoFxMap(): Map<String, NvsVideoFx> {
        return nvsVideoFxMap
    }

    /**
     * 获取特效名称列表
     * @return List<String> 特效名称列表
     */
    fun getNameList(): List<String> {
        return nvsVideoFxMap.keys.toList()
    }

    override fun clone(): MeicamClipMultiEffect {
        val gson = EditorEngineGlobalContext.getInstance().gson
        var result: MeicamClipMultiEffect? = null
        var jsonString: String? = null
        try {
            jsonString = gson.toJson(this)
            result = gson.fromJson(jsonString, javaClass)
        } catch (e: JsonSyntaxException) {
            GLog.e(TAG, "clone, json = $jsonString, failed:", e)
        }
        if (result == null) {
            return MeicamClipMultiEffect(name, fxType)
        }
        return result
    }

    companion object {
        private const val TAG = "MeicamClipMultiEffect"
        const val JSON_TYPE_NAME = "clip_multi_effect"
    }
}