/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.foundation.cache;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.LruCache;

import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.security.CrcUtil;
import com.oplus.gallery.standard_lib.file.Dir;
import com.oplus.gallery.foundation.cache.DownloadEntry.Columns;
import com.oplus.gallery.standard_lib.scheduler.FutureListener;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.concurrent.Future;

/**
 * Marked by zhangwenming。该类需要被放到CachingAbility中。需要在重构缓存能力实现的时候，将该类及相关依赖迁走。
 */
public class DownloadCache {
    private static final String TAG = "DownloadCache";
    private static final int MAX_DELETE_COUNT = 16;
    private static final int LRU_CAPACITY = 4;

    private static final String TABLE_NAME = DownloadEntry.SCHEMA.getTableName();

    private static final String[] QUERY_PROJECTION = {Columns.ID, Columns.DATA};
    private static final String WHERE_HASH_AND_URL = String.format(Locale.ENGLISH, "%s = ? AND %s = ?",
            Columns.HASH_CODE, Columns.CONTENT_URL);
    private static final int QUERY_INDEX_ID = 0;
    private static final int QUERY_INDEX_DATA = 1;

    private static final String[] FREESPACE_PROJECTION = {Columns.ID, Columns.DATA,
            Columns.CONTENT_URL, Columns.CONTENT_SIZE};
    private static final String FREESPACE_ORDER_BY = String.format(Locale.ENGLISH, "%s ASC", Columns.LAST_ACCESS);
    private static final int FREESPACE_IDNEX_ID = 0;
    private static final int FREESPACE_IDNEX_DATA = 1;
    private static final int FREESPACE_INDEX_CONTENT_URL = 2;
    private static final int FREESPACE_INDEX_CONTENT_SIZE = 3;

    private static final String ID_WHERE = Columns.ID + " = ?";

    private static final String[] SUM_PROJECTION = {String.format(Locale.ENGLISH, "sum(%s)", Columns.CONTENT_SIZE)};
    private static final int SUM_INDEX_SUM = 0;

    private final LruCache<String, Entry> mEntryMap = new LruCache<>(LRU_CAPACITY);
    private final HashMap<String, DownloadTask> mTaskMap = new HashMap<>();
    private final File mRoot;
    private final DatabaseHelper mDatabaseHelper;
    private final SQLiteDatabase mDatabase;
    private final long mCapacity;

    private long mTotalBytes = 0;
    private boolean mInitialized = false;

    private static final long DOWNLOAD_CAPACITY = 64 * 1024 * 1024; // 64M
    private static DownloadCache mDownloadCache;

    public static synchronized DownloadCache getDownloadCache() {
        if (mDownloadCache == null) {
            File cacheDir = new File(ContextGetter.context.getExternalCacheDir(), Dir.getDOWNLOAD().getDirPath());
            if (!FileOperationUtils.ensureMakeDirectory(cacheDir)) {
                throw new RuntimeException("fail to create: " + cacheDir.getAbsolutePath());
            }
            mDownloadCache = new DownloadCache(ContextGetter.context, cacheDir, DOWNLOAD_CAPACITY);
        }
        return mDownloadCache;
    }

    public DownloadCache(Context context, File root, long capacity) {
        mRoot = root;
        mCapacity = capacity;

        mDatabaseHelper = new DatabaseHelper(context);
        mDatabase = mDatabaseHelper.getWritableDatabase();
    }

    public Entry download(JobContext jc, WorkerSession session, URL url) {
        if (!mInitialized) {
            initialize();
        }

        String stringUrl = url.toString();

        // First find in the entry-pool
        synchronized (mEntryMap) {
            Entry entry = mEntryMap.get(stringUrl);
            if (entry != null) {
                updateLastAccess(entry.mId);
                return entry;
            }
        }

        // Then, find it in database
        TaskProxy proxy = new TaskProxy();
        synchronized (mTaskMap) {
            Entry entry = findEntryInDatabase(stringUrl);
            if (entry != null) {
                updateLastAccess(entry.mId);
                return entry;
            }

            // Finally, we need to download the file ....
            // First check if we are downloading it now ...
            DownloadTask task = mTaskMap.get(stringUrl);
            if (task == null) { // if not, start the download task now
                task = new DownloadTask(stringUrl);
                mTaskMap.put(stringUrl, task);
                task.mFuture = session.submit(task, task);
            }
            task.addProxy(proxy);
        }

        return proxy.get(jc);
    }

    private Entry findEntryInDatabase(String stringUrl) {
        long hash = CrcUtil.crc64Long(stringUrl);
        String[] whereArgs = {String.valueOf(hash), stringUrl};

        try (Cursor cursor = mDatabase.query(TABLE_NAME, QUERY_PROJECTION, WHERE_HASH_AND_URL,
                whereArgs, null, null, null)) {
            if (cursor == null) {
                return null;
            }

            if (cursor.moveToNext()) {
                File file = new File(cursor.getString(QUERY_INDEX_DATA));
                long id = cursor.getInt(QUERY_INDEX_ID);
                Entry entry = null;
                synchronized (mEntryMap) {
                    entry = mEntryMap.get(stringUrl);
                    if (entry == null) {
                        entry = new Entry(id, file);
                        mEntryMap.put(stringUrl, entry);
                    }
                }
                return entry;
            }
        }
        return null;
    }

    private void updateLastAccess(long id) {
        ContentValues values = new ContentValues();
        values.put(Columns.LAST_ACCESS, System.currentTimeMillis());
        if (mDatabase.update(TABLE_NAME, values, ID_WHERE, new String[]{String.valueOf(id)}) <= 0) {
            GLog.w(TAG, "updateLastAccess update fail!");
        }
    }

    private synchronized void freeSomeSpaceIfNeed(int maxDeleteFileCount) {
        if (mTotalBytes <= mCapacity) {
            return;
        }

        try (Cursor cursor = mDatabase.query(TABLE_NAME, FREESPACE_PROJECTION, null, null, null, null,
                FREESPACE_ORDER_BY)) {
            if (cursor == null) {
                return;
            }

            while (maxDeleteFileCount > 0 && mTotalBytes > mCapacity && cursor.moveToNext()) {
                long id = cursor.getLong(FREESPACE_IDNEX_ID);
                String url = cursor.getString(FREESPACE_INDEX_CONTENT_URL);
                long size = cursor.getLong(FREESPACE_INDEX_CONTENT_SIZE);
                String path = cursor.getString(FREESPACE_IDNEX_DATA);
                boolean containsKey = false;
                synchronized (mEntryMap) {
                    containsKey = mEntryMap.get(url) != null;
                }
                if (!containsKey) {
                    --maxDeleteFileCount;
                    mTotalBytes -= size;
                    if (!(new File(path).delete())) {
                        GLog.e(TAG, "freeSomeSpaceIfNeed, delete file failed.");
                    }
                    mDatabase.delete(TABLE_NAME, ID_WHERE, new String[]{String.valueOf(id)});
                }
            }
        }
    }

    private synchronized long insertEntry(String url, File file) {
        long size = file.length();
        mTotalBytes += size;

        ContentValues values = new ContentValues();
        String hashCode = String.valueOf(CrcUtil.crc64Long(url));
        values.put(Columns.DATA, file.getAbsolutePath());
        values.put(Columns.HASH_CODE, hashCode);
        values.put(Columns.CONTENT_URL, url);
        values.put(Columns.CONTENT_SIZE, size);
        values.put(Columns.LAST_UPDATED, System.currentTimeMillis());
        return mDatabase.insert(TABLE_NAME, "", values);
    }

    private synchronized void initialize() {
        if (mInitialized) {
            return;
        }
        mInitialized = true;
        if (!FileOperationUtils.ensureMakeDirectory(mRoot)) {
            throw new RuntimeException("cannot create " + mRoot.getAbsolutePath());
        }

        mTotalBytes = 0;
        try (Cursor cursor = mDatabase.query(TABLE_NAME, SUM_PROJECTION, null, null, null, null, null)) {
            if ((cursor != null) && cursor.moveToNext()) {
                mTotalBytes = cursor.getLong(SUM_INDEX_SUM);
            }
        }
        if (mTotalBytes > mCapacity) {
            freeSomeSpaceIfNeed(MAX_DELETE_COUNT);
        }
    }

    public void close() {
        mInitialized = false;
        IOUtils.closeQuietly(mDatabase);
        mDatabaseHelper.close();
    }

    private final class DatabaseHelper extends SQLiteOpenHelper {
        public static final String DATABASE_NAME = "download.db";
        public static final int DATABASE_VERSION = 2;

        public DatabaseHelper(Context context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            DownloadEntry.SCHEMA.createTables(db);
            // Delete old files
            File[] files = mRoot.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!file.delete()) {
                        GLog.e(TAG, "onCreate, fail to remove");
                    }
                }
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            // reset everything
            DownloadEntry.SCHEMA.dropTables(db);
            onCreate(db);
        }
    }

    public static class Entry {
        public File mCacheFile;
        protected long mId;

        Entry(long id, File cacheFile) {
            mId = id;
            this.mCacheFile = cacheFile;
        }
    }

    private class DownloadTask implements Job<File>, FutureListener<File> {
        private final String mUrl;
        private HashSet<TaskProxy> mProxySet = new HashSet<>();
        private Future<File> mFuture;

        public DownloadTask(String url) {
            mUrl = url;
        }

        public void removeProxy(TaskProxy proxy) {
            synchronized (mTaskMap) {
                mProxySet.remove(proxy);
                if (mProxySet.isEmpty()) {
                    mFuture.cancel(false);
                    mTaskMap.remove(mUrl);
                }
            }
        }

        // should be used in synchronized block of mDatabase
        public void addProxy(TaskProxy proxy) {
            proxy.mTask = this;
            mProxySet.add(proxy);
        }

        public void onFutureDone(Future<File> future) {
            File file = null;
            try {
                file = future.get();
            } catch (Exception e) {
                GLog.e(TAG, "onFutureDone: ", e);
            }
            long id = 0;
            if (file != null) { // insert to database
                id = insertEntry(mUrl, file);
            }

            if (future.isCancelled()) {
                return;
            }

            synchronized (mTaskMap) {
                Entry entry = null;
                synchronized (mEntryMap) {
                    if (file != null) {
                        entry = new Entry(id, file);
                        mEntryMap.put(mUrl, entry);
                    }
                }
                for (TaskProxy proxy : mProxySet) {
                    proxy.setResult(entry);
                }
                mTaskMap.remove(mUrl);
                freeSomeSpaceIfNeed(MAX_DELETE_COUNT);
            }
        }

        public File call(JobContext jc) {
            // TODO: utilize etag
            File tempFile = null;
            try {
                URL url = new URL(mUrl);
                tempFile = File.createTempFile("cache", ".tmp", mRoot);
                // download from url to tempFile
                boolean downloaded = requestDownload(url, tempFile);
                if (downloaded) {
                    return tempFile;
                }
            } catch (Exception e) {
                GLog.e(TAG, "fail to download", e);
            }
            if (tempFile != null) {
                if (!tempFile.delete()) {
                    GLog.e(TAG, "run, temp file  failed.");
                }
            }
            return null;
        }
    }

    private boolean requestDownload(URL url, File file) {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file.getFile());
            return download(url, fos);
        } catch (FileNotFoundException e) {
            GLog.e(TAG, "requestDownload, e:", e);
            return false;
        } finally {
            IOUtils.closeQuietly(fos);
        }
    }

    private void write(InputStream is, OutputStream os) throws IOException {
        byte[] buffer = new byte[4096];
        int rc = is.read(buffer, 0, buffer.length);
        while (rc > 0) {
            os.write(buffer, 0, rc);
            rc = is.read(buffer, 0, buffer.length);
        }
        Thread.interrupted(); // consume the interrupt signal
    }

    private boolean download(URL url, OutputStream output) {
        InputStream input = null;
        try {
            input = url.openStream();
            write(input, output);
            return true;
        } catch (IOException e) {
            GLog.e(TAG, "fail to download, e:", e);
            return false;
        } finally {
            IOUtils.closeQuietly(input);
        }
    }

    public static class TaskProxy {
        private DownloadTask mTask;
        private boolean mIsCancelled = false;
        private Entry mEntry;

        synchronized void setResult(Entry entry) {
            if (mIsCancelled) {
                return;
            }
            mEntry = entry;
            notifyAll();
        }

        public synchronized Entry get(JobContext jc) {
            jc.setCancelListener(()-> {
                    mTask.removeProxy(TaskProxy.this);
                    synchronized (TaskProxy.this) {
                        mIsCancelled = true;
                        TaskProxy.this.notifyAll();
                    }
                return null;
            });
            while (!mIsCancelled && mEntry == null) {
                try {
                    wait();
                } catch (InterruptedException e) {
                    GLog.w(TAG, "ignore interrupt", e);
                }
            }
            jc.setCancelListener(null);
            return mEntry;
        }
    }
}
