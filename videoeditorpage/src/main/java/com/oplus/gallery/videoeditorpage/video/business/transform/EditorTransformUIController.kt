/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorTransformController
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/2
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/2      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.transform

import android.content.Context
import android.graphics.PointF
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.videoeditorpage.R
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.foundation.ui.animationcontrol.ContentAnimationListener
import com.oplus.gallery.foundation.ui.rotateclip2.RotateClipConfig
import com.oplus.gallery.foundation.ui.rotateclip2.RotateClipFrameView
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorProcessor
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView
import com.oplus.gallery.videoeditorpage.video.config.UiConfigManager
import com.oplus.gallery.videoeditorpage.widget.AnimatableStateImageView
import com.oplus.gallery.videoeditorpage.widget.AnimatableStateImageView.Companion.STATE_FIRST
import com.oplus.gallery.videoeditorpage.widget.AnimatableStateImageView.Companion.STATE_SECOND
import com.oplus.gallery.videoeditorpage.widget.RuleScrollerView
import com.oplus.gallery.videoeditorpage.widget.ratioselector.RatioSelectorView
import kotlin.math.abs
import kotlin.math.roundToInt
import kotlin.math.sign

/**
 * 负责实现裁剪旋转页面的UI
 */
class EditorTransformUIController(
    context: Context,
    rootView: ViewGroup,
    state: EditorTransformState,
    engine: EditorEngine,
    private val previewView: EditorPreviewView,
    private val videoClip: IVideoClip
) : EditorBaseUIController<Unit>(context, rootView, state) {

    private val vm: EditorTransformVM by lazy {
        val factory =
            EditorTransformVMFactory(engine, videoClip, previewView.animController, mEditorState.operationSaveHelper)
        ViewModelProvider(this, factory)[EditorTransformVM::class.java]
    }

    /**
     * 用于添加裁剪框的容器
     * 由于 rotateClipView 需要和预览SurfaceView一样大小，所有使用liveWindow作为容器
     */
    private val clipFrameContainer: ViewGroup? get() = previewView.liveWindow as? ViewGroup

    /**
     * 裁剪框视图，用于显示裁剪区域和裁剪手柄等控件
     */
    private val clipFrameView: RotateClipFrameView by lazy {
        val horizontalMargin = 0
        val clipConfig = RotateClipConfig(
            UiConfigManager.minContentClipSize.height,
            UiConfigManager.minContentClipSize.width,
            horizontalMargin,
            ::isEditorLandscape,
        )
        RotateClipFrameView(mContext, clipConfig).apply {
            animationProperties = previewView.animController.properties
            isForceDarkAllowed = false
            onZoomToCenter = vm::notifyClipFrameUpdatedEnd
            onClip = vm::notifyClipFrameUpdated
        }
    }

    /**
     * 朝向旋转按钮
     */
    private val rotateOrientationBtn: AnimatableStateImageView by lazy {
        findContainerChild(R.id.transform_rotate_orientation)
    }

    /**
     * 水平镜像按钮
     */
    private val mirrorBtn: AnimatableStateImageView by lazy {
        findContainerChild(R.id.transform_mirror)
    }

    /**
     * 旋转角度显示文本
     */
    private val rotationAngleTxt: COUITextView by lazy {
        findContainerChild(R.id.transform_rotation_angle)
    }

    /**
     * 旋转刻度尺
     */
    private val rotateRulerView: RuleScrollerView by lazy {
        findContainerChild(R.id.transform_rotate_ruler_view)
    }

    private val rotateRulerChangeListener = object : RuleScrollerView.OnSelectValueChangeListener {
        private var lastScrollerIndex = MathUtils.ZERO

        override fun canSelectValueChange(): Boolean = vm.isContentAnimating.value != true

        override fun onStartChangeValue() = vm.notifyRotateStart()

        override fun onSelectValueChanged(currAngle: Int, isFromUser: Boolean) {
            if (isFromUser.not()) return
            vm.notifyRotate(rotateRulerView.highPrecisionValue.toDouble())
            rotateRulerView.scrollerIndex.takeIf { it != lastScrollerIndex }?.let {
                lastScrollerIndex = it
                VibratorUtils.vibrateInSubThread(mContext, VibratorUtils.EffectType.VIBRATE_TYPE_WEAKEST, true)
            }
            if (rotateRulerView.isIdle) {
                clipFrameView.hide3x3GridAfter9x9Grid()
            } else if (rotateRulerView.isInTouch) {
                clipFrameView.show3x3Grid()
                clipFrameView.show9x9Grid()
            }
        }

        override fun onChangeValueComplete(finalValue: Int) {
            if (rotateRulerView.isIdle) {
                clipFrameView.hide3x3GridAfter9x9Grid()
            }
        }
    }

    /**
     * 比例选择器
     */
    private val ratioSelectorView: RatioSelectorView by lazy { findContainerChild(R.id.transform_ratio_selector) }

    /**
     * 预览图标布局，用作重置按钮容器
     */
    private var previewIconLayout: ViewGroup? = null

    private fun isEditorLandscape(): Boolean = EditorUIConfig.isEditorLandscape(currentAppUIConfig)

    private val previewContentAnimationListener = object : ContentAnimationListener {
        override fun onContentAnimationStart() = clipFrameView.onContentAnimationStart()

        override fun onContentAnimationUpdate(matrix: Matrix) = Unit

        override fun onContentAnimationEnd() = clipFrameView.onContentAnimationEnd(withAnimation = true)
    }

    override fun getContainerId(): Int = R.id.overlap_container

    override fun getContentLayoutId(appUiConfig: AppUiResponder.AppUiConfig): Int =
        R.layout.videoeditor_transform_menu_layout

    override fun getTitleId(): Int = R.string.videoeditor_video_tailor

    override fun needPlaySeekBar(): Boolean = true

    override fun createView() {
        super.createView()
        previewView.animController.addContentAnimationListener(previewContentAnimationListener)

        clipFrameContainer?.addView(clipFrameView)
        clipFrameView.run {
            toggleClipFrameVisibility(show = false, animate = false)
            toggleClipFrameVisibility(show = true)
        }

        mResetButton?.run {
            setOnClickListener { vm.notifyReset() }
            vm.isResetEnabled.observe(this@EditorTransformUIController) {
                isEnabled = it && vm.state.value == EditorTransformVMState.RUNNING
            }
        }

        rotateOrientationBtn.setOnClickListener { vm.notifyRotateOrientation() }
        mirrorBtn.setOnClickListener { vm.notifyMirrorHorizontal() }
        rotateRulerView.apply {
            startValue = MIN_ROTATE_ANGLE
            endValue = MAX_ROTATE_ANGLE
            setNeedHighPrecision(true)
            setDefaultIndex(DEFAULT_INDEX)
            setOnSelectValueChangeListener(rotateRulerChangeListener)
        }
        ratioSelectorView.apply {
            setRatioOptions(vm.ratioOptions)
            setOnRatioSelectedListener(vm::notifyRatioOptionChanged)
        }

        vm.rotationOrientation.observe(this) {
            val isVertical = (it == RotationOrientation.TOP) || (it == RotationOrientation.BOTTOM)
            rotateOrientationBtn.setState(
                if (isVertical) STATE_FIRST else STATE_SECOND,
                animate = true
            )
        }
        vm.mirrorHorizontal.observe(this) { mirrored ->
            mirrorBtn.setState(
                if (mirrored) STATE_SECOND else STATE_FIRST,
                animate = true
            )
        }
        vm.rotationDegree.observe(this) {
            // 由于 10.5 和 -10.5 roundToInt 后绝对值不一致（分别是11和10），所以这里先取绝对值
            val angle = abs(it).roundToInt() * it.sign.toInt()
            rotationAngleTxt.text = "$angle$DEGREE_UNIT"
            if (rotateRulerView.currentValue != angle) rotateRulerView.currentValue = angle
        }
        vm.clipRectInDisplay.observe(this) { clipFrameView.onClipRectChange(it) }
        vm.ratioOption.observe(this) { ratioSelectorView.setSelected(it?.ratioId) }
        vm.ratioValue.observe(this) { clipFrameView.setFrameRatio(it) }
        vm.state.observe(this) {
            val isRunning = it == EditorTransformVMState.RUNNING
            clipFrameView.isEnabled = isRunning
            rotateRulerView.isEnabled = isRunning
            mResetButton?.isEnabled = isRunning && vm.isResetEnabled.value == true
            rotateOrientationBtn.isEnabled = isRunning
            mirrorBtn.isEnabled = isRunning
            ratioSelectorView.isEnabled = isRunning
            previewView.animController.setGestureEnabled(isRunning)

            if (it == EditorTransformVMState.STOPPED) {
                mContentContainer?.post { finishMe() }
            }
        }
        previewView.animController.run {
            val previewCenter = previewArea.value?.let { PointF(it.centerX(), it.centerY()) } ?: PointF(0f, 0f)
            scale(vm.videoClipScale, previewCenter.x, previewCenter.y, true)
            doOnOperationEnd { vm.doTransformStartProcess(mEditorState.editorControlView.enginePlayingTimeManager, mEditorState.videoPlaybackMode) }
        }
    }

    override fun createEnterAnim(): Boolean {
        mResetButton?.let {
            AnimatorProcessor.createEnterAnim(it, false, true)
            return true
        } ?: return false
    }

    fun clickCancel() = vm.notifyCancel()

    fun clickDone() = vm.notifyConfirm()

    override fun destroyView() {
        clipFrameContainer?.removeView(clipFrameView)
        previewView.animController.removeContentAnimationListener(previewContentAnimationListener)
        super.destroyView()
    }

    override fun hasReset() = true

    private infix fun <T : View> findContainerChild(id: Int): T =
        checkNotNull(mContentContainer.findViewById<T>(id)) { "No child found for id $id" }

    override fun isBusying(): Boolean = true

    fun getOriginalTimeline(): ITimeline = vm.originalTimeline

    private fun finishMe() = mEditorState.editorControlView.editorStateManager.finish(mEditorState)

    companion object {
        private const val TAG = "EditorTransformUIController"

        private const val MAX_ROTATE_ANGLE = 45
        private const val MIN_ROTATE_ANGLE = -45

        /**
         * 旋转默认的刻度，对应数值为0
         */
        private const val DEFAULT_INDEX = 15

        /**
         * 角度单位
         */
        private const val DEGREE_UNIT = "°"
    }
}
