/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BusTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/31
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** huca<PERSON><PERSON>@Apps.Gallery		2021/05/31		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BusTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>       2020/08/14		1.0		OPLUS_ARCH_BUS
 ** W9002848                    2020/08/14		1.0		Add Suppress SpecifyReturnTypeRule
 *********************************************************************************/

package com.oplus.gallery.bus_lib

import com.oplus.gallery.bus_lib.annotations.Subscribe
import io.mockk.spyk
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Test

class BusTest {

    @Test
    @Suppress("ExternalMustSpecifyReturnTypeRule")
    fun should_register_success_when_register_with_obj() {
        // given
        val realBus = RealBus()
        val testObj = TestObj()
        // when
        realBus.register(testObj)
        // then
        var registerSuccess: Boolean = false

        run outer@{
            realBus.methodToObjMap.values.forEach {
                it.values.forEach { value ->
                    if (testObj == value) {
                        registerSuccess = true
                        return@outer
                    }
                }
            }
        }
        assertEquals(true, registerSuccess)
    }

    @Test
    @Suppress("ExternalMustSpecifyReturnTypeRule")
    fun should_register_failed_when_register_with_obj() {
        // given
        val realBus = RealBus()
        val testObj = TestNoObj()
        // when
        Bus.register(testObj)
        // then
        var registerSuccess: Boolean = false

        run outer@{
            realBus.methodToObjMap.values.forEach {
                it.values.forEach { value ->
                    if (testObj == value) {
                        registerSuccess = true
                        return@outer
                    }
                }
            }
        }
        assertEquals(false, registerSuccess)
    }

    @Test
    @Suppress("ExternalMustSpecifyReturnTypeRule")
    fun should_unregister_success_when_register_with_obj() {
        // given
        val realBus = RealBus()
        val testObj = TestObj()
        // when
        Bus.register(testObj)
        Bus.unregister(testObj)
        // then
        var unregisterSuccess: Boolean = true

        run outer@{
            realBus.methodToObjMap.values.forEach {
                it.values.forEach { value ->
                    if (testObj == value) {
                        unregisterSuccess = false
                        return@outer
                    }
                }
            }
        }
        assertEquals(true, unregisterSuccess)
    }

    @Test
    fun should_received_msg_when_post_msg() {
        // given
        val spyk = spyk(TestObj())
        // when
        Bus.register(spyk)
        Bus.post(TestData("test"))
        // then
        verify(exactly = 1) { spyk.checkMethod(any()) }
    }

    @Test
    fun should_received_msg_when_postSticky_msg() {
        // given
        val spyk = spyk(TestObj())
        // when
        Bus.register(spyk)
        Bus.postSticky(TestData("test"))
        // then
        verify(exactly = 1) { spyk.checkMethod(any()) }
        val spyk2 = spyk(TestObj())
        Bus.register(spyk2)
        verify(exactly = 1) { spyk2.checkMethod(any()) }
    }

    class TestObj {

        @Subscribe(supportSticky = true)
        @Suppress("ExternalMustSpecifyReturnTypeRule")
        fun checkMethod(testData: TestData) = Unit
    }

    class TestNoObj {

        @Suppress("ExternalMustSpecifyReturnTypeRule")
        fun checkMethod(testData: TestData) = Unit
    }

    data class TestData(val msg: String)
}