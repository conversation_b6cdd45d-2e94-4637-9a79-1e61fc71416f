/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetViewGetter.kt
 ** Description:以弱引用方式缓存WidgetView
 ** Version: 1.0
 ** Date : 2021/12/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><EMAIL>    2021/12/1        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item

import android.app.Application
import com.oplus.gallery.foundation.util.debug.GLog
import java.lang.ref.WeakReference

object WidgetViewGetter {
    private const val TAG = "WidgetViewManager"
    private val widgetViewMap = mutableMapOf<String, WeakReference<WidgetView>>()

    /**
     * 优先从弱引用缓存获取WidgetView，获取不到则新建
     * @param widgetCode
     * @return widgetCode相应的WidgetView
     */
    fun getWidgetView(application: Application, widgetCode: String): WidgetView {
        GLog.d(TAG, "getWidgetView: $widgetCode")
        return widgetViewMap.computeIfAbsent(widgetCode) {
            GLog.d(TAG, "getWidgetView: no ref, create")
            WeakReference(WidgetView(application, widgetCode))
        }.get() ?: let {
            GLog.d(TAG, "getWidgetView: ref recycled, create")
            val widgetView = WidgetView(application, widgetCode)
            widgetViewMap[widgetCode] = WeakReference(widgetView)
            return@let widgetView
        }
    }

    /**
     * 优先从弱引用缓存获取WidgetView
     * @param widgetCode
     * @return widgetCode相应的WidgetView，获取不到则返回null
     */
    fun getWidgetViewIfPresent(widgetCode: String): WidgetView? {
        return widgetViewMap[widgetCode]?.get()
    }
}