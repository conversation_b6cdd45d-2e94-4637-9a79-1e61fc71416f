/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RealPipeline.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/13
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_PIPELINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_PIPELINE
 *********************************************************************************/

package com.oplus.gallery.bus_lib

import androidx.annotation.VisibleForTesting
import com.oplus.gallery.bus_lib.annotations.FinderType
import com.oplus.gallery.bus_lib.beans.MethodInfo
import com.oplus.gallery.bus_lib.finders.ClassAllMethodFinder
import com.oplus.gallery.bus_lib.finders.ClassMethodFinder
import com.oplus.gallery.foundation.util.debug.GLog
import java.util.WeakHashMap
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

@VisibleForTesting(otherwise = VisibleForTesting.PACKAGE_PRIVATE)
class RealBus : IBus {
    private val lock = ReentrantReadWriteLock()

    @VisibleForTesting
    val methodMap = HashMap<Class<*>, MutableSet<MethodInfo>>()

    @VisibleForTesting
    val subscriberMap = HashMap<Any, MutableSet<MethodInfo>>()

    @VisibleForTesting
    val stickyEvents = HashMap<Class<*>, Any>()

    @VisibleForTesting
    val methodToObjMap = HashMap<MethodInfo, WeakHashMap<Any, Any>>()

    override fun register(subscriber: Any, finderType: Int) {
        lock.write {
            methodFinders[finderType]?.findMethods(subscriber)?.apply {
                unregister(subscriber)
                subscriberMap[subscriber] = this
            }?.forEach {
                methodMap.getOrPut(it.getParamClass()) { mutableSetOf() }.add(it)
                // deal sticky event
                if (it.supportSticky) {
                    dispatchStickyEvent(subscriber, it)
                }
                methodToObjMap.computeIfAbsent(it) {
                    WeakHashMap()
                }.apply {
                    put(subscriber, subscriber)
                }
            } ?: apply {
                GLog.e(TAG, "register: no found method @Subscribe")
            }
        }
    }

    override fun unregister(subscriber: Any) {
        lock.write {
            subscriberMap.remove(subscriber)?.forEach {
                methodToObjMap[it]?.remove(subscriber)?.apply {
                    GLog.w(TAG, "unregister: subscriber class: ${subscriber.javaClass}. obj is $subscriber")
                }
            }
        }
    }

    override fun post(event: Any) {
        lock.read {
            dispatchEvents(event)
        }
    }

    override fun postSticky(event: Any) {
        lock.write {
            dispatchEvents(event)
            stickyEvents[event.javaClass] = event
        }
    }

    private fun dispatchEvents(event: Any) {
        lock.read {
            methodMap[event.javaClass]?.forEach { methodInfo ->
                methodToObjMap[methodInfo]?.forEach {
                    dispatchEvent(it.value, methodInfo, event)
                }
            }
        }
    }

    private fun dispatchStickyEvent(subscriber: Any, methodInfo: MethodInfo) {
        lock.read {
            stickyEvents[methodInfo.getParamClass()]?.apply {
                dispatchEvent(subscriber, methodInfo, this)
            }
        }
    }

    private fun dispatchEvent(obj: Any, methodInfo: MethodInfo, event: Any) {
        methodInfo.invokeIt(obj, event)
    }

    fun clear() {
        methodMap.clear()
        methodToObjMap.clear()
        subscriberMap.clear()
        stickyEvents.clear()
        methodToObjMap.clear()
    }

    companion object {
        private const val TAG = "RealPipeline"
        private val methodFinders = mapOf(
            FinderType.CLASS_METHOD_FINDER to ClassMethodFinder(),
            FinderType.CLASS_ALL_METHOD_FINDER to ClassAllMethodFinder()
        )
    }
}