/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  RotatationOritation
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/21
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/21      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.transform

private const val DEGREE_0: Int = 0
private const val DEGREE_90: Int = 90
private const val DEGREE_180: Int = 180
private const val DEGREE_270: Int = 270

/**
 * 旋转朝向，默认朝向正上方
 */
enum class RotationOrientation(val degree: Int) {
    TOP(DEGREE_0),
    RIGHT(DEGREE_90),
    BOTTOM(DEGREE_180),
    LEFT(DEGREE_270);

    /**
     * 执行一次逆时针90度旋转
     * @return 返回旋转后的方向类型
     */
    fun anticlockwiseRotate(): RotationOrientation = when (this) {
        TOP -> LEFT
        LEFT -> BOTTOM
        BOTTOM -> RIGHT
        RIGHT -> TOP
    }
}