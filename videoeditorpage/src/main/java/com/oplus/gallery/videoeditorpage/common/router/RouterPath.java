/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RouterPath.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.router;

/**
 * 用于ARouter Fragment 跳转的统一路径注册
 *
 * <AUTHOR>
 */
public class RouterPath {

    public static class Home {
        public static final String HOME = "/home";
        public static final String HOME_MAIN = HOME + "/main";
        public static final String HOME_PUBLIC_INTERFACE = HOME + "/public_interface";
        public static final String OVERSEA_HOME = HOME + "/oversea_home";

        public static final String TEMPLATE_OVERSEA_MATERIAL_SPECIAL = HOME + "/oversea_special";
        public static final String TEMPLATE_OVERSEA_MATERIAL_FONT = HOME + "/oversea_font";
        public static final String TEMPLATE_OVERSEA_MATERIAL_STICKER = HOME + "/oversea_sticker";
        public static final String TEMPLATE_OVERSEA_MATERIAL_THEME = HOME + "/oversea_theme";
        public static final String TEMPLATE_OVERSEA_MATERIAL_MUSIC = HOME + "/oversea_music";
    }

    public static class Mine {
        public static final String MINE = "/mine";
        public static final String MINE_MAIN = MINE + "/main";

    }

    public static class Template {
        public static final String TEMPLATE = "/template";
        public static final String TEMPLATE_LIST = TEMPLATE + "/list";

        public static final String TEMPLATE_RELEASE = TEMPLATE + "/release";
        public static final String TEMPLATE_EDIT = TEMPLATE + "/edit";
        public static final String TEMPLATE_LOCAL_PLAY = TEMPLATE + "/local_play";

        public static final String TEMPLATE_OVERSEA_MATERIAL_TEMPLATE_MAIN = TEMPLATE + "/oversea_template_main";//
        public static final String TEMPLATE_OVERSEA_MATERIAL_TEMPLATE = TEMPLATE + "/oversea_template";
        public static final String TEMPLATE_FILTER_TEMPLATE = TEMPLATE + "/filter_template";   //模板筛选
        public static final String TEMPLATE_SEARCH = TEMPLATE + "/search";   //搜索
        public static final String TEMPLATE_LIKE = TEMPLATE + "/like";   //喜欢
        public static final String TEMPLATE_SPECIAL = TEMPLATE + "/special";  //专题
        public static final String TEMPLATE_UGC = TEMPLATE + "/ugc";  //ugc
    }

    public static class Material {
        public static final String MATERIAL = "/material";
        public static final String MATERIAL_ACTIVITY = MATERIAL + "/activity";
        public static final String MATERIAL_API = MATERIAL + "/api";
    }

    public static class Resource {
        public static final String RESOURCE = "/resource";
        public static final String RESOURCE_API = RESOURCE + "/api";
    }

    public static class Compile {
        public static final String COMPILE = "/Compile";
        public static final String COMPILE_ACTIVITY = COMPILE + "/activity";
    }

    public static class Camera {
        public static final String CAMERA = "/Camera";
        public static final String CAMERA_ACTIVITY = CAMERA + "/activity";
    }

    public static class SoloopWebExt {
        public static final String SOLOOP_WEB_EXT = "/SoloopWebExt";
        public static final String SOLOOP_WEB_EXT_ACTIVITY = SOLOOP_WEB_EXT + "/activity";
        public static final String SOLOOP_WEB_EXT_FRAGMENT = SOLOOP_WEB_EXT + "/fragment";
    }

    public static class Bridge {
        public static final String BRIDGE = "/Bridge";
        public static final String BRIDGE_ACTIVITY = BRIDGE + "/activity";
    }

    public static class Statement {
        public static final String STATEMENT = "/Statement";
        public static final String STATEMENT_ACTIVITY = STATEMENT + "/activity";
    }

    public static class TemplateOperation {
        public static final String TEMPLATE_OPERATION = "/TemplateOperation";
        public static final String TEMPLATE_OPERATION_ACTIVITY = TEMPLATE_OPERATION + "/activity";
    }

    public static class Draft {
        public static final String DRAFT = "/Draft";
        public static final String DRAFT_RESOURCE_API = DRAFT + "/resource_api";
    }

    public static class Guide {
        public static final String GUIDE = "/Guide";
        public static final String GUIDE_ACTIVITY = GUIDE + "/activity";
    }

    public static class Voice {
        public static final String VOICE = "/Voice";
        public static final String TEMPLATE_PREVIEW = VOICE + "/template_preview";

    }
}
