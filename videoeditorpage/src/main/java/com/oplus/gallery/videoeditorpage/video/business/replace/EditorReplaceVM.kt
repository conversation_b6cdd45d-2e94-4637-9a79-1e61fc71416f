/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorReplaceVM
 ** Description:
 ** Version: 1.0
 ** Date : 2025/7/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/7/7      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.video.business.replace

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1f
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.player.IVideoPlayerListener
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 视频替換相关的ViewModel
 */
class EditorReplaceVM(
    private val engine: EditorEngine,
    private val newTimeline: ITimeline,
    private val mediaItem: MediaItem,
    private val previousVideoClipDuration: Long,
    private val onSelectionListener: EditorReplaceHelper.ReplaceSelectionListener
) : ViewModel() {
    /**
     * 视频播放进度
     */
    private val _playProgress: MutableLiveData<Float> = MutableLiveData(0f)
    val playProgress: LiveData<Float> get() = _playProgress

    /**
     * 之前的时间线
     */
    private val previousTimeline: ITimeline = engine.currentTimeline

    /**
     * 之前的时间线位置
     */
    private val previousTimelinePosition: Long = engine.timelineCurrentPosition

    /**
     * 视频播放监听器
     */
    private val iVideoPlayerListener: IVideoPlayerListener = object : IVideoPlayerListener {

        override fun onPositionChange(absolutePosition: Long) {
            val progress = NUMBER_1f * (absolutePosition - startPlayPosition) / previousVideoClipDuration
            setPlayProgress(progress)
        }

        override fun onPreloadingCompletion() = Unit

        override fun onStopped() = Unit

        override fun onEOF(absolutePosition: Long) {
            startEnginePlay()
        }

        override fun onException(i: Int, s: String) = Unit

        override fun onHardwareError(i: Int, s: String) = Unit

        override fun onPlaybackDelayed(l: Long, b: Boolean) = Unit

        override fun onStartPlay(success: Boolean) = Unit
    }

    /**
     * 视频播放起始位置
     */
    private var startPlayPosition: Long = 0L

    init {
        engine.addVideoPlayerListener(iVideoPlayerListener)
        engine.restoreByTimeline(newTimeline, false)
        engine.seekTo(0, 0)
        viewModelScope.launch(Dispatchers.Main) {
            delay(PLAY_DELAY_TIME)
            startEnginePlay()
        }
    }

    /**
     * 设置播放进度
     * @param progress 播放进度值，范围通常为0.0到1.0之间
     */
    fun setPlayProgress(progress: Float) {
        _playProgress.value = progress
    }

    /**
     *完成视频加速操作
     */
    fun finishClipReplace() {
        val replaceEntry = EditorReplaceEntry(
            startPlayPosition, getPlayEndTime(), StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV
        )
        engine.restoreByTimeline(previousTimeline, false)
        engine.seekTo(previousTimelinePosition, 0)
        onSelectionListener.onSelectedMediaItem(mediaItem, replaceEntry)
        stopAndReleasePlayer()
    }

    /**
     * 销毁操作
     */
    fun destroy() {
        engine.restoreByTimeline(previousTimeline, true)
        engine.seekTo(previousTimelinePosition, 0)
        stopAndReleasePlayer()
    }

    /**
     * 停止播放，假如引擎正在播放中
     */
    fun stopPlaybackIfPlaying() {
        engine.takeIf { it.isPlaying }?.stopPlayer()
    }

    /**
     * 启动播放
     */
    fun startEnginePlay() {
        engine.startPlayer(startPlayPosition, getPlayEndTime())
    }

    /**
     * 引擎跳转到指定位置
     */
    fun setEngineStartPlayPosition(startPlayPosition: Long) {
        this.startPlayPosition = startPlayPosition
        engine.seekTo(startPlayPosition, 0)
    }

    /**
     * 引擎在指定位置开始播放
     */
    fun startEnginePlay(startPlayPosition: Long) {
        this.startPlayPosition = startPlayPosition
        startEnginePlay()
    }

    /**
     * 获取播放的结束时间
     */
    fun getPlayEndTime(): Long {
        val calculatedEnd = startPlayPosition + previousVideoClipDuration
        val timelineDuration = newTimeline.duration
        // 确保播放结束时间不超过时间线总长度
        return if (calculatedEnd > timelineDuration) {
            timelineDuration
        } else {
            calculatedEnd
        }
    }

    /**
     * 停止并释放播放器
     */
    private fun stopAndReleasePlayer() {
        engine.removeVideoPlayerListener(iVideoPlayerListener)
    }

    companion object {
        /**
         * 播放延迟时间
         */
        const val PLAY_DELAY_TIME = 100L
    }
}

class EditorReplaceVMFactory(
    private val editorEngine: EditorEngine,
    private val newTimeline: ITimeline,
    private val mediaItem: MediaItem,
    private val previousVideoClipDuration: Long,
    private val onSelectionListener: EditorReplaceHelper.ReplaceSelectionListener,
) : ViewModelProvider.Factory {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(EditorReplaceVM::class.java)) {
            @Suppress("UNCHECKED_CAST") return EditorReplaceVM(
                editorEngine, newTimeline, mediaItem, previousVideoClipDuration, onSelectionListener
            ) as T
        } else {
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
