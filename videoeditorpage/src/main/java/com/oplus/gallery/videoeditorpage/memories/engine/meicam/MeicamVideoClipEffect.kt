/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoClipEffect.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.oplus.gallery.foundation.util.debug.GLog
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.meicam.sdk.NvsVideoFx

open class MeicamVideoClipEffect : BaseVideoClipEffect {

    @Transient
    var mvsVideoFx: NvsVideoFx? = null

    constructor() : super("") {
        type = TYPE_BUILT_IN_FX
    }

    constructor(name: String?, type: Int) : super(name, type) {
        strength = DEFAULT_STRENGTH
    }

    fun setNvsVideoFx(nvsVideoFx: NvsVideoFx?) {
        mvsVideoFx = nvsVideoFx
    }

    override fun setFloatValue(paramName: String?, value: Float) {
        mvsVideoFx?.setFloatVal(paramName, value.toDouble())
    }

    override fun getFloatValue(paramName: String?): Double? {
        return mvsVideoFx?.getFloatVal(paramName)
    }

    override fun setStringValue(paramName: String?, value: String?) {
        mvsVideoFx?.setStringVal(paramName, value)
    }

    override fun getStringValue(paramName: String?): String? {
        return mvsVideoFx?.getStringVal(paramName)
    }

    override fun setBooleanVal(paramName: String?, value: Boolean) {
        mvsVideoFx?.setBooleanVal(paramName, value)
    }

    override fun getBooleanVal(paramName: String?): Boolean? {
        return mvsVideoFx?.getBooleanVal(paramName)
    }

    override fun setFilterIntensity(strength: Float) {
        mvsVideoFx?.filterIntensity = strength
    }

    public override fun clone(): MeicamVideoClipEffect {
        val gson: Gson = GsonBuilder().registerTypeAdapter(BaseVideoClipEffect::class.java, VideoClipEffectAdapter())
                .serializeNulls() // output null
                .setPrettyPrinting() // format output
                .serializeSpecialFloatingPointValues() // serialize special double/float value like NaN or POSITIVE_INFINITY etc.
                .create()
        var result: MeicamVideoClipEffect? = null
        var jsonString: String? = null
        try {
            jsonString = gson.toJson(this)
            result = gson.fromJson(jsonString, javaClass)
        } catch (e: Exception) {
            GLog.e(TAG, "clone, json = $jsonString, failed:", e)
        }
        return result ?: MeicamVideoClipEffect(name, type)
    }

    companion object {
        const val DEFAULT_STRENGTH = 0.8f
        const val JSON_TYPE_NAME = "common"
        private const val TAG = "MeicamVideoClipEffect"
    }
}