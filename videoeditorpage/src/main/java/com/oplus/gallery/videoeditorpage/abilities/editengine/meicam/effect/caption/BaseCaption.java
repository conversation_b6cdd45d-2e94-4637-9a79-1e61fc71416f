/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseCaption.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption;

import android.graphics.PointF;
import android.graphics.RectF;
import androidx.annotation.IntDef;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoFx;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionAlignment;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

/**
 * 字幕的基类，用于定义一些字幕相关的通用接口
 */
public abstract class BaseCaption extends BaseVideoFx {

    @IntDef({
            CaptionType.TYPE_NORMAL_CAPTION,
            CaptionType.TYPE_MODULAR_CONTEXT_CAPTION,
            CaptionType.TYPE_MODULAR_RENDERER_CAPTION,
            CaptionType.TYPE_MODULAR_ANIMATION_CAPTION,
            CaptionType.TYPE_MODULAR_IN_ANIMATION_CAPTION,
            CaptionType.TYPE_MODULAR_OUT_ANIMATION_CAPTION
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface CaptionType {

        /**
         * 标准字幕: 普通、边框、遂字、动态底图
         */
        int TYPE_NORMAL_CAPTION = 0;

        /**
         * 拼装字幕-气泡
         */
        int TYPE_MODULAR_CONTEXT_CAPTION = 1;

        /**
         * 拼装字幕-花字
         */
        int TYPE_MODULAR_RENDERER_CAPTION = 2;

        /**
         * 拼装字幕-组合动画
         */
        int TYPE_MODULAR_ANIMATION_CAPTION = 3;

        /**
         * 拼装字幕-入动画
         */
        int TYPE_MODULAR_IN_ANIMATION_CAPTION = 4;

        /**
         * 拼装字幕-出动画
         */
        int TYPE_MODULAR_OUT_ANIMATION_CAPTION = 5;
    }

    public static final float VALUE_INVALID = -1F;

    /**
     * 字幕样式资源包默认id值
     */
    public static final String DEFAULT_CAPTION_STYLE_ID = "";

    /**
     * 字幕样式资源包id值
     */
    protected String mCaptionStyleId = DEFAULT_CAPTION_STYLE_ID;

    /**
     * 标记是否组合字幕
     */
    protected boolean mIsCompoundCaption = false;

    /**
     * 组合字幕类型
     */
    protected int mCompoundCaptionType = 0;

    /**
     * 字幕实例id
     */
    private long mCaptionId = 0;

    /**
     * 字幕类型
     */
    @CaptionType
    private int mCaptionType = CaptionType.TYPE_NORMAL_CAPTION;

    /**
     * 字幕对齐方式
     */
    private int mCationAlignment = CaptionAlignment.ALIGNMENT_CENTER;

    /**
     * 字幕是否是默认字幕，默认字幕为“请输入文字”
     */
    private boolean mIsDefaultCaption = true;

    /**
     * 字幕样式id，用于记录当前字幕设置了哪个样式以便打开编辑时进行回显（服务器端返回的id）
     */
    private String mStyleId;

    /**
     * 字幕字体id，用于记录当前字幕设置了哪个字体以便打开编辑时进行回显（服务器端返回的id）
     */
    private String mFontId;

    /**
     * 构造函数
     *
     * @param name 类名称
     */
    public BaseCaption(String name) {
        super(name, TYPE_CUSTOMER_FX);
        // 初始化实例id为时间戳
        mCaptionId = System.currentTimeMillis();
    }

    /**
     * 获取字幕对齐方式
     *
     * @return 字幕对齐方式
     */
    public @CaptionAlignment
    int getCationAlignment() {
        return mCationAlignment;
    }

    /**
     * 设置字幕对齐方式
     *
     * @param cationAlignment 字幕对齐方式
     */
    public void setCaptionAlignment(@CaptionAlignment int cationAlignment) {
        mCationAlignment = cationAlignment;
    }

    /**
     * 设置字幕实例ID
     *
     * @param captionId 实例id
     */
    public void setCaptionId(long captionId) {
        mCaptionId = captionId;
    }

    /**
     * 获取字幕实例id
     *
     * @return 字幕实例id
     */
    public long getCaptionId() {
        return mCaptionId;
    }

    /**
     * 配置是否组合字幕
     *
     * @return true：组合字幕 false：普通字幕
     */
    public boolean isCompoundCaption() {
        // 默认配置成普通字幕
        return false;
    }

    /**
     * 获取字幕类型
     *
     * @return 返回字幕类型
     */
    public int getCaptionType() {
        return mCaptionType;
    }

    /**
     * 设置字幕类型
     *
     * @param captionType 字幕类型
     */
    public void setCaptionType(@CaptionType int captionType) {
        mCaptionType = captionType;
    }

    /**
     * 设置是否是默认的字幕
     * @param isDefaultCaption 是否默认字幕
     */
    public void setIsDefaultCaption(boolean isDefaultCaption) { mIsDefaultCaption = isDefaultCaption; }

    /**
     * 获取是否是默认字幕
     * @return 返回是否是默认字幕
     */
    public boolean getIsDefaultCaption() { return mIsDefaultCaption; }

    /**
     * 设置字幕样式id（服务器端id）
     * @param styleId 字幕样式id
     */
    public void setStyleId(String styleId) { mStyleId = styleId; }

    /**
     * 获取字幕样式id（服务器端id）
     * @return 字幕样式id
     */
    public String getStyleId() { return mStyleId; }

    /**
     * 设置字幕字体id（服务器端id）
     * @param fontId 字幕字体id
     */
    public void setFontId(String fontId) { mFontId = fontId; }

    /**
     * 获取字幕字体id（服务器端id）
     * @return 字幕字体id
     */
    public String getFontId() { return mFontId; }

    /**
     * 获取字体
     */
    public abstract String getFontFamily();

    /**
     * 设置字体
     *
     * @param fontFamily 字体
     */
    public abstract void setFontFamily(String fontFamily);

    /**
     * 获取字体本地路径
     *
     * @return 字体本地路径
     */
    public abstract String getFontPath();

    /**
     * 设置字体本地路径
     *
     * @param fontPath 字体本地路径
     */
    public abstract void setFontPath(String fontPath);

    /**
     * 获取字体颜色
     *
     * @return 字体颜色
     */
    public abstract String getTextColor();

    /**
     * 设置字体颜色
     *
     * @param textColor 字体颜色
     */
    public abstract void setTextColor(String textColor);

    /**
     * 设置字幕内容
     */
    public abstract void setText(String text);

    /**
     * 获取字幕内容
     */
    public abstract String getText();

    /**
     * 获取资源样式id
     *
     * @return 资源样式包id
     */
    public abstract String getCaptionStyleId();

    /**
     * 设置资源样式id
     *
     * @param captionStyleId 样式资源id
     */
    public abstract void setCaptionStyleId(String captionStyleId);

    /**
     * 应用样式
     * @param captionStyleId 样式资源id
     */
    public abstract void applyCaptionStyle(String captionStyleId);

    /**
     * 获取字体大小
     */
    public abstract float getFontSize();

    /**
     * 设置字体大小
     *
     * @param fontSize 字体大小
     */
    public abstract void setFontSize(float fontSize);

    /**
     * 获取字体是否描边
     */
    public abstract boolean getDrawOutline();

    /**
     * 设置字体是否描边
     *
     * @param drawOutline 是否描边
     */
    public abstract void setDrawOutline(boolean drawOutline);

    /**
     * 获取字体描边颜色
     */
    public abstract String getOutlineColor();

    /**
     * 设置字体描边颜色
     *
     * @param outlineColor 字体描边颜色
     */
    public abstract void setOutlineColor(String outlineColor);

    /**
     * 获取字体描边宽度
     */
    public abstract float getOutlineWidth();

    /**
     * 设置字体描边宽度
     *
     * @param outlineWidth 字体描边宽度
     */
    public abstract void setOutlineWidth(float outlineWidth);

    /**
     * 设置字体是否加粗
     *
     * @param bold 是否加粗
     */
    public abstract void setBold(boolean bold);

    /**
     * 获取字体是否加粗
     */
    public abstract boolean getBold();

    /**
     * 获取字间距
     */
    public abstract float getLetterSpacing();

    /**
     * 设置字间距
     *
     * @param letterSpacing 字间距
     */
    public abstract void setLetterSpacing(float letterSpacing);

    /**
     * 获取行间距
     */
    public abstract float getLineSpacing();

    /**
     * 设置行间距
     *
     * @param lineSpacing 行间距
     */
    public abstract void setLineSpacing(float lineSpacing);

    /**
     * 设置阴影是否开启
     *
     * @param drawShadow 是否开启
     */
    public abstract void setDrawShadow(boolean drawShadow);

    /**
     * 获取阴影设置开关
     *
     * @return 返回是否开启阴影开关
     */
    public abstract boolean getDrawShadow();

    /**
     * 获取阴影颜色
     *
     * @return 返回阴影颜色
     */
    public abstract String getShadowColor();

    /**
     * 设置阴影颜色
     *
     * @param shadowColor 阴影颜色
     */
    public abstract void setShadowColor(String shadowColor);

    /**
     * 获取阴影偏移量
     */
    public abstract PointF getShadowOffset();

    /**
     * 设置阴影偏移量
     *
     * @param shadowOffset 偏移量坐标点
     */
    public abstract void setShadowOffset(PointF shadowOffset);

    /**
     * 获取阴影羽化
     */
    public abstract float getShadowFeather();

    /**
     * 设置阴影羽化
     *
     * @param shadowFeather 羽化值
     */
    public abstract void setShadowFeather(float shadowFeather);

    /**
     * 获取文字透明度
     */
    public abstract float getOpacity();

    /**
     * 设置文字透明度
     *
     * @param opacity 透明度
     */
    public abstract void setOpacity(float opacity);

    /**
     * 获取背景颜色
     *
     * @return 背景色
     */
    public abstract String getBackgroundColor();

    /**
     * 设置背景色
     *
     * @param backgroundColor 背景色
     */
    public abstract void setBackgroundColor(String backgroundColor);

    /**
     * 设置背景圆角
     *
     * @param radius 背景圆角
     */
    public abstract void setBackgroundRadius(float radius);

    /**
     * 获取背景圆角
     *
     * @return 返回背景圆角
     */
    public abstract float getBackgroundRadius();

    /**
     * 设置字幕背景边界扩展比率
     *
     * @param ratio 扩展比率为字体大小的倍数，默认为0.15
     */
    public abstract void setBoundaryPaddingRatio(float ratio);

    /**
     * 获取字幕背景边界扩展比率
     *
     * @return 扩展比率为字体大小的倍数，默认为0.15
     */
    public abstract float getBoundaryPaddingRatio();

    /**
     * 获取是否设置斜体
     */
    public abstract boolean getItalic();

    /**
     * 设置斜体
     *
     * @param italic 斜体标识
     */
    public abstract void setItalic(boolean italic);

    /**
     * 获取是否设置下划线
     */
    public abstract boolean getUnderline();

    /**
     * 设置下划线
     *
     * @param underline 下划线标识
     */
    public abstract void setUnderline(boolean underline);

    /**
     * 获取是否设置删除线
     * @return 返回是否设置删除线
     */
    public abstract boolean getStrikeOut();

    /**
     * 设置删除线
     * @param strikeOut 删除线标识
     */
    public abstract void setStrikeOut(boolean strikeOut);

    /**
     * 将字幕进行位置平移
     *
     * @param translation 平移量
     */
    public abstract void translateCaption(PointF translation);

    /**
     * 设置字幕平移量
     *
     * @param translation 平移量
     */
    public abstract void setTranslation(PointF translation);

    /**
     * 获取字幕的平移量
     */
    public abstract PointF getTranslation();

    /**
     * 缩放字幕
     *
     * @param scaleFactor 缩放比例
     * @param anchor      锚点坐标
     */
    public abstract void scaleCaption(float scaleFactor, PointF anchor);

    /**
     * 旋转字幕
     *
     * @param angle  旋转角度
     * @param anchor 锚点坐标
     */
    public abstract void rotateCaption(float angle, PointF anchor);

    /**
     * 旋转字幕
     *
     * @param angle 旋转角度
     */
    public abstract void rotateCaption(float angle);

    /**
     * 设置字幕的X轴缩放比例
     *
     * @param scaleX X轴缩放比例
     */
    public abstract void setScaleX(float scaleX);

    /**
     * 获取X轴缩放比例
     */
    public abstract float getScaleX();

    /**
     * 设置Y轴缩放比例
     *
     * @param scaleY Y轴缩放比例
     */
    public abstract void setScaleY(float scaleY);

    /**
     * 获取字幕的Y轴缩放比例
     */
    public abstract float getScaleY();

    /**
     * 设置字幕的旋转角度
     *
     * @param angle 旋转角度
     */
    public abstract void setRotation(float angle);

    /**
     * 获取字幕的旋转角度
     */
    public abstract float getRotation();


    /**
     * 获取字幕的原始包围矩形框变换后的顶点位置
     *
     * @return 返回List<PointF>对象，包含四个顶点位置，依次分别对应原始边框的左上，左下，右下，右上顶点
     */
    public abstract List<PointF> getBoundingRectangleVertices();

    /**
     * 获取字幕的原始包围矩形框变换后的顶点位置
     *
     * @param boundingType 边框类型
     * @return 返回List<PointF>对象，包含四个顶点位置，依次分别对应原始边框的左上，左下，右下，右上顶点
     */
    public abstract List<PointF> getCaptionBoundingVertices(int boundingType);

    /**
     * 获取字幕文本矩形框
     */
    public abstract RectF getTextBoundingRect();

    /**
     * 获取字幕的z值，即轨道索引
     *
     * @return 轨道索引
     */
    public abstract float getZValue();

    /**
     * 设置字幕的z值，即轨道索引
     *
     * @param zValue 轨道索引
     */
    public abstract void setZValue(float zValue);

    public abstract void setClipAffinityEnabled(boolean enabled);

    public abstract boolean getClipAffinityEnabled();
}
