/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  ReplaceVideoMaskView
 ** Description: 替换视频的蒙层
 ** Version: 1.0
 ** Date : 2025/7/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/7/8      1.0     NEW
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.replace.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.graphics.toColorInt

/**
 * 替换视频的蒙层
 */
class ReplaceVideoMaskView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    /**
     * 画笔，用于绘制遮罩层
     */
    private val paint = Paint().apply {
        color = MASK_COLOR
    }
    /**
     * 左边遮罩层的宽度，单位为像素
     */
    private var leftMaskWidth = 0f
    /**
     * 右边遮罩层的宽度，单位为像素
     */
    private var rightMaskWidth = 0f

    /**
     * 设置遮罩层的宽度
     * @param leftWidth 左边遮罩层的宽度，单位为像素
     * @param rightWidth 右边遮罩层的宽度，单位为像素
     */
    fun setMaskWidth(leftWidth: Float, rightWidth: Float) {
        this.leftMaskWidth = leftWidth
        this.rightMaskWidth = rightWidth
        invalidate() // 触发重绘
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制左蒙层（从左侧开始，宽度为leftMaskWidth）
        if (leftMaskWidth > 0) {
            canvas.drawRect(0f, 0f, leftMaskWidth, height.toFloat(), paint)
        }

        // 绘制右蒙层（从右侧开始，宽度为rightMaskWidth）
        if (rightMaskWidth > 0) {
            canvas.drawRect(
                width - rightMaskWidth,
                0f,
                width.toFloat(),
                height.toFloat(),
                paint
            )
        }
    }

    companion object {
        /**
         * 半透明遮罩颜色（75% 不透明度的黑色）
         */
        private val MASK_COLOR = "#66000000".toColorInt()
    }
}