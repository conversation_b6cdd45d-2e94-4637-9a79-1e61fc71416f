/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoClipView.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.track.view;
import static com.meicam.sdk.NvsIconGenerator.GET_ICON_FLAGS_KEY_FRAME;
import static com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil.RESOLUTION_RATIO_2K;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.data.BaseKeyFrameInfo;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ScrollMode;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video.MeicamVideoClip;
import com.oplus.gallery.videoeditorpage.utlis.BitmapUtils;
import com.oplus.gallery.videoeditorpage.utlis.ResolutionUtil;
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper;
import com.meicam.sdk.NvsIconGenerator;
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;

import org.jetbrains.annotations.Contract;

import java.util.List;
import java.util.Locale;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 视频片段视图
 */
public class VideoClipView extends ClipViewDrawImpl {
    /** 片段视图边距 */
    public static final int CLIP_VIEW_PADDING = ClipViewWrapper.HORIZONTAL_PADDING;
    /** 各个标签间的间距 */
    public static final int LABEL_MARGIN_START = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_1_5);
    /** 标签文本背景水平内边距 */
    public static final int LABEL_TEXT_BG_HORIZONTAL_PADDING = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_3);
    /** icon的宽度 */
    public static final int LABEL_ICON_WIDTH = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_12);
    /** icon的左边边距 */
    public static final int LABEL_ICON_MARGIN_START = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_2);
    /** icon的距离上方的间距 */
    public static final int LABEL_ICON_MARGIN_TOP = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_2);
    /** 标签文本背景垂直内边距 */
    public static final int LABEL_TEXT_BG_VERTICAL_PADDING = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_2);
    /** 通用文本标签 (曲线变速) */
    public static final float LABEL_COMMON_TEXT_SIZE = ContextGetter.context.getResources().getDimension(R.dimen.text_size_9);
    /** 标签背景外边距 */
    public static final int LABEL_BG_HORIZONTAL_MARGIN
        = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.videoeditor_track_clip_label_margin);

    /** 曲线速度标签文本背景外边距 */
    public static final int LABEL_CURVE_SPEED_TEXT_MARGIN_START = ContextGetter.context.getResources().getDimensionPixelSize(R.dimen.dp_1);
    /** 数字标签文本大小（如常规变速，如时间标签） */
    public static final float LABEL_NUMBER_TEXT_SIZE = ContextGetter.context.getResources().getDimension(R.dimen.text_size_8);
    /** 图标大小 */
    protected static final int ICON_SIZE = ClipViewFactory.getMainTrackClipHeight();
    /** 图标大小所占的标准时长(不含缩放、变速) */
    protected static final long ICON_BASE_DURATION = TrackHelper.INSTANCE.getDurationNoScaleByCurrentLength(ICON_SIZE, ContextGetter.context);
    /** TAG */
    private static final String TAG = "VideoClipView";

    private static final int KEY_FRAME_ICON_HALF_WIDTH = (int) ContextGetter.context.getResources()
        .getDimension(R.dimen.editor_key_frame_half_width);
    private static final int KEY_FRAME_ICON_HALF_HEIGHT = (int) ContextGetter.context.getResources()
        .getDimension(R.dimen.editor_key_frame_half_height);
    private static final int KEY_FRAME_SELECT_ICON_HALF_WIDTH = (int) ContextGetter.context.getResources()
        .getDimension(R.dimen.editor_key_frame_select_half_width);
    private static final int KEY_FRAME_SELECT_ICON_HALF_HEIGHT = (int) ContextGetter.context.getResources()
        .getDimension(R.dimen.editor_key_frame_select_half_height);
    private final Bitmap mKeyFrame = BitmapFactory.decodeResource(ContextGetter.context.getResources(), R.drawable.engine_key_frame_icon);
    private final Bitmap mKeyFrameSelect = BitmapFactory.decodeResource(
            ContextGetter.context.getResources(),
            R.drawable.engine_key_frame_select_icon);
    /** 本地可见区域 */
    private final Rect mLocalVisibleRect = new Rect();
    /**
     * 缩略图请求任务缓存
     * key: 绘制位置
     * value: (任务Id，图标缓存)
     */
    private final HashMap<Long, Pair<Long, Bitmap>> mThumbnailTaskMap = new HashMap<>();
    /** 美摄图标生成对象 */
    private NvsIconGenerator mNvsIconGenerator;
    /** 入点 */
    private long mTrimInAdjusted;
    /** 出点 */
    private long mTrimOutAdjusted;
    private boolean mReverse = false;
    /** 文件路径 */
    private String mFilePath;
    private float mBaseLine;
    /** 矩阵 */
    private Matrix mMatrix;
    /** 最后一次绘制的缩略图 */
    private Bitmap mLastThumbnail;
    private Path mPath;
    private Paint.FontMetrics mFontMetrics;
    private float mTextHeight;
    private ClipViewAttacher mClipViewAttacher;
    /** 是否可见 */
    private boolean mIsVisible = true;
    /** 片段模型 */
    private ClipModel mClipMode;

    /** 是否为首个片段 */
    private Boolean mIsFirstClip = false;

    /**
     * 变速icon的位图
     */
    private Bitmap mSpeederBitmap = null;
    /** 是否绘制边界 */
    private boolean mIsDrawFloatingBoundary;
    /** 是否回滚 */
    private boolean mIsRollbackScroll = false;
    /** 上一个绘制位置 */
    private int mLastDrawPosition = 0;
    /** 当前缩略图绘制的起始位置 */
    private long mThumbnailStartPosition = AppConstants.Number.NUMBER_MINUS_1;
    /** 当前缩略图绘制的结束位置 */
    private long mThumbnailEndPosition = AppConstants.Number.NUMBER_MINUS_1;
    /**
     * 是否为2K或以上画质
     * 美摄建议高清画质视频解码速度缓慢，建议对高清视频采用flag为GET_ICON_FLAGS_KEY_FRAME的方式解码
     * 这种方式解码速度会大幅度提升，但解码的缩图和传入的position位置可能会有偏差
     */
    private boolean mIsResolution2K = false;

    /**
     * 设置缩略图
     */
    protected void setLastThumbnail(Bitmap thumbnail) {
        mLastThumbnail = thumbnail;
    }

    /**
     * 获取最后一次设置的缩略图
     * @return 缩略图
     */
    protected Bitmap getLastThumbnail() {
        return mLastThumbnail;
    }

    protected Matrix getMatrix() {
        return mMatrix;
    }

    protected void setTrimInAdjusted(long trimInAdjusted) {
        mTrimInAdjusted = trimInAdjusted;
    }

    protected long getTrimInAdjusted() {
        return mTrimInAdjusted;
    }

    protected void setTrimOutAdjusted(long trimOutAdjusted) {
        mTrimOutAdjusted = trimOutAdjusted;
    }

    protected void setFilePath(String filePath) {
        mFilePath = filePath;
    }

    protected String getFilePath() {
        return mFilePath;
    }

    protected void setNvsIconGenerator(NvsIconGenerator nvsIconGenerator) {
        mNvsIconGenerator = nvsIconGenerator;
    }

    protected NvsIconGenerator getNvsIconGenerator() {
        return mNvsIconGenerator;
    }

    @Override
    public void draw(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper) {
        mClipMode = clipViewWrapper.getRelativeData();
        mIsFirstClip = clipViewWrapper.getClipInTrackIndex() == AppConstants.Number.NUMBER_0;
        if (mClipMode == null) {
            GLog.e(TAG, LogFlag.DL, "[draw] mClipModel is null");
            return;
        }

        if (mClipViewAttacher == null) {
            mClipViewAttacher = new ClipViewAttacher(clipViewWrapper);
        } else {
            mClipViewAttacher.updateClip(clipViewWrapper);
        }
        if (getPaint() == null) {
            setPaint(new Paint());
        }
        getPaint().reset();
        getPaint().setAntiAlias(true);

        if (mMatrix == null) {
            mMatrix = new Matrix();
        }

        if (mPath == null) {
            mPath = new Path();
        }

        mIsVisible = clipViewWrapper.getLocalVisibleRect(mLocalVisibleRect);
        // 这里不能判断==，==时维持mIsRollbackScroll的值不变
        if (mLocalVisibleRect.left > mLastDrawPosition) {
            mIsRollbackScroll = isRTL();
        } else if (mLocalVisibleRect.left < mLastDrawPosition) {
            mIsRollbackScroll = !isRTL();
        }
        mLastDrawPosition = mLocalVisibleRect.left;

        drawBitmap(canvas, clipViewWrapper);
        // 绘制半透明背景
        if (clipViewWrapper.getClipConfig().isShowShadow()) {
            canvas.drawColor(clipViewWrapper.getContext().getColor(R.color.color_71000000));
        }


        mClipViewAttacher.onDrawSelf(canvas);

        // 绘制时间标签，timeLabelRectPair为绘制时间标签的区域，timeLabelRectPair.first为在VideoClipView中的绘制区域，timeLabelRectPair.second为需要在轨道控件上浮动显示绘制的区域
        Pair<RectF, RectF> timeLabelRectPair = drawTimeLabel(canvas, clipViewWrapper);
        // 绘制变速标签
        drawSpeedLabel(canvas, clipViewWrapper, timeLabelRectPair);
        drawKeyFrame(canvas, clipViewWrapper);
    }

    @Override
    public void refreshUi(ClipViewWrapper view, String path, long trimInAdjusted, long trimOutAdjusted) {
        if (TextUtils.isEmpty(path)) {
            return;
        }
        mClipMode = view.getRelativeData();
        MeicamVideoClip videoClip = (MeicamVideoClip) mClipMode.getClip();
        mIsResolution2K = ResolutionUtil.isAtLeastResolution(
            videoClip.getFileVideoSize(),
            ResolutionUtil.getResolutionRatioSize(RESOLUTION_RATIO_2K)
        );
        GLog.d(TAG, LogFlag.DL, "[refreshUi] trimIn = " + trimInAdjusted + ", trimOut = " + trimOutAdjusted
            + ", clipIndex = " + view.getClipInTrackIndex()  + ", is2K = " + mIsResolution2K + ", lastThumbnail = " + mLastThumbnail);

        if (mReverse != videoClip.isReversePlay()) {
            mReverse = videoClip.isReversePlay();
        }
        mTrimInAdjusted = trimInAdjusted;
        mTrimOutAdjusted = trimOutAdjusted;
        if (mNvsIconGenerator == null) {
            mNvsIconGenerator = new NvsIconGenerator();
            mNvsIconGenerator.setIconCallback((bitmap, position, taskId) -> {
                if (bitmap == null) {
                    GLog.e(TAG, LogFlag.DL, "[refreshUi]"
                        + " onIconReady: position = " + position + ", taskId = " + taskId + ", clipIndex = " + view.getClipInTrackIndex());
                    return;
                }
                if (position == AppConstants.Number.NUMBER_0) {
                    mLastThumbnail = bitmap;
                    if (mClipMode.getClipInTrackIndex() == AppConstants.Number.NUMBER_0) {
                        TrackHelper.INSTANCE.setFirstThumbnail(bitmap);
                    }
                }
                boolean needDraw = (position == AppConstants.Number.NUMBER_0) || isVisiblePosition(position);
                GLog.d(TAG, LogFlag.DL, "[refreshUi] " + (needDraw ? "onIconReadyAndDraw:" : "onIconReady:")
                    + " position = " + position + ", taskId = " + taskId + ", clipIndex = " + view.getClipInTrackIndex());
                // 首次获取缩略图或只在可见范围内才绘制
                if (needDraw) {
                    mThumbnailTaskMap.put(position, new Pair<>(taskId, bitmap));
                    view.postInvalidateClipView();
                }
            });
        }
        if (!path.equals(mFilePath)) {
            mFilePath = path;
        }
        if (mLastThumbnail == null) {
            long position = AppConstants.Number.NUMBER_0;
            int flag = mIsResolution2K ? GET_ICON_FLAGS_KEY_FRAME : AppConstants.Number.NUMBER_0;
            mLastThumbnail = mNvsIconGenerator.getIconFromCache(mFilePath, position, flag);
            if (mLastThumbnail == null) {
                Pair<Long, Bitmap> pair = mThumbnailTaskMap.get(position);
                if (pair != null) {
                    mLastThumbnail = pair.second;
                } else {
                    long taskId = mNvsIconGenerator.getIcon(mFilePath, position, flag);
                    mThumbnailTaskMap.put(position, new Pair<>(taskId, null));
                    GLog.d(TAG, LogFlag.DL, "[refreshUi]"
                        + " getIcon: position = " + position + ", taskId = " + taskId + ", clipIndex = " + view.getClipInTrackIndex());
                }
            }
        }
        view.postInvalidateClipView();
    }

    @Override
    public void clearDrawData() {
        int clipIndex = mClipMode != null ? mClipMode.getClipInTrackIndex() : -1;
        GLog.d(TAG, LogFlag.DL, "[clearDrawData] clipIndex = " + clipIndex);
        removeThumbnailTask(true);
        if (mNvsIconGenerator != null) {
            long zeroFrame = AppConstants.Number.NUMBER_0;
            mNvsIconGenerator.cancelTask(zeroFrame);
            Pair<Long, Bitmap> pair = mThumbnailTaskMap.get(zeroFrame);
            if ((pair != null) && (pair.second == null)) {
                mThumbnailTaskMap.remove(zeroFrame);
            }
            mNvsIconGenerator.setIconCallback(null);
            if (!mNvsIconGenerator.isReleased()) {
                mNvsIconGenerator.release();
            }
            mNvsIconGenerator = null;
        }
    }

    /**
     * 绘制的位置是否包在可见位置内
     * @param position 绘制的位置
     * @return 是否可见
     */
    private boolean isVisiblePosition(long position) {
        return (position >= mThumbnailStartPosition) && (position <= mThumbnailEndPosition);
    }

    /**
     * 绘制图片
     * @param canvas 画布
     * @param clipViewWrapper 片段视图
     */
    protected void drawBitmap(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper) {
        Pair<Integer, Integer> flipXPair = getDrawFlipXPair(canvas.getWidth());

        int drawableWidth = Math.abs(flipXPair.second - flipXPair.first);
        int drawableCount = drawableWidth / ICON_SIZE + AppConstants.Number.NUMBER_5;

        long startPosition = getOriginalDurationByLength(flipXPair.first) + mTrimInAdjusted;
        long stepDuration = getOriginalDurationByLength(ICON_SIZE);

        int startIndex = (int) (startPosition / stepDuration);
        int trimInLength = (int) getOriginalLengthByDuration(mTrimInAdjusted);

        updateVisiblePosition(canvas.getWidth());
        boolean needTask = (TrackHelper.INSTANCE.getScrollMode() == ScrollMode.PLAY) || (TrackHelper.INSTANCE.getScrollMode() == ScrollMode.NONE);
        int flag = mIsResolution2K ? GET_ICON_FLAGS_KEY_FRAME : AppConstants.Number.NUMBER_0;
        for (int i = startIndex; i <= drawableCount + startIndex; i++) {
            // 针对2K以上高清视频，鉴于GET_ICON_FLAGS_KEY_FRAME解码方式会取前次一帧图像，这里index向后偏移一个帧，补齐轨道缩图位置不准的情景
            int index = (mIsResolution2K ? i + AppConstants.Number.NUMBER_1 : i);
            long position = ((index * stepDuration) / ICON_BASE_DURATION) * ICON_BASE_DURATION;
            Bitmap bitmap = mNvsIconGenerator.getIconFromCache(mFilePath, position, flag);
            // 可见范围内，非滚动或非播放情况下，若没有缓存缩略图也没有正在请求的任务，则通过getIcon方法请求缩略图，结果将通过onIconReady回调返回
            if ((bitmap == null) && mIsVisible && isVisiblePosition(position)) {
                Pair<Long, Bitmap> pair = mThumbnailTaskMap.get(position);
                if (pair != null) {
                    bitmap = pair.second;
                } else if (needTask) {
                    // pair 为空，说明没有请求任务，需要请求缩略图
                    long taskId = mNvsIconGenerator.getIcon(mFilePath, position, flag);
                    mThumbnailTaskMap.put(position, new Pair<>(taskId, null));
                    GLog.d(TAG, LogFlag.DL, "[drawBitmap]"
                        + " getIcon: position = " + position + ", taskId = " + taskId + ", clipIndex = " + mClipMode.getClipInTrackIndex());
                }
            }
            if (bitmap != null) {
                mLastThumbnail = bitmap;
            } else if ((mLastThumbnail == null) && (TrackHelper.INSTANCE.getFirstThumbnail() != null)) {
                mLastThumbnail = TrackHelper.INSTANCE.getFirstThumbnail();
            }
            if (mLastThumbnail != null) {
                float rateW = AppConstants.Number.NUMBER_1f * ICON_SIZE / mLastThumbnail.getWidth();
                float rateH = AppConstants.Number.NUMBER_1f * ICON_SIZE / mLastThumbnail.getHeight();
                float rate = Math.max(rateH, rateW);
                int top = (rateW > rateH)
                    ? (int) ((mLastThumbnail.getHeight() * rateW - ICON_SIZE) / AppConstants.Number.NUMBER_2)
                    : AppConstants.Number.NUMBER_0;
                mMatrix.setScale(rate, rate);
                int flipX = isRTL()
                    ? canvas.getWidth() - (i + AppConstants.Number.NUMBER_1) * ICON_SIZE + trimInLength
                    : i * ICON_SIZE - trimInLength;
                mMatrix.postTranslate(flipX, -top);
                canvas.drawBitmap(mLastThumbnail, mMatrix, getPaint());
            }
        }
        // 移除不在可见范围内的请求任务
        removeThumbnailTask(!needTask);
    }

    /**
     * 获取绘制的范围
     * @param canvasWidth 画布宽度
     * @return 绘制范围(start, end)
     */
    @NonNull
    @Contract("_ -> new")
    private Pair<Integer, Integer> getDrawFlipXPair(int canvasWidth) {
        int screenWidth = ScreenUtils.getDisplayScreenWidth();
        int startX = AppConstants.Number.NUMBER_0;
        int endX = canvasWidth;
        if (mIsVisible) {
            // 缓存缩略图的绘制范围（可见范围的前后两屏）
            startX = mLocalVisibleRect.left - screenWidth * AppConstants.Number.NUMBER_2;
            if (startX < AppConstants.Number.NUMBER_0) {
                startX = AppConstants.Number.NUMBER_0;
            }
            endX = mLocalVisibleRect.right + screenWidth * AppConstants.Number.NUMBER_2;
            if (endX > canvasWidth) {
                endX = canvasWidth;
            }
        } else {
            if (mLocalVisibleRect.left < AppConstants.Number.NUMBER_0) {
                startX = canvasWidth - Math.min(canvasWidth, screenWidth);
            } else {
                endX = Math.min(canvasWidth, screenWidth);
            }
        }
        int flipStartX = isRTL() ? canvasWidth - endX : startX;
        int flipEndX = isRTL() ? canvasWidth - startX : endX;

        return new Pair<>(flipStartX, flipEndX);
    }

    /**
     * 更新时间线上绘制的可见范围
     * @param canvasWidth 画布宽度
     */
    private void updateVisiblePosition(int canvasWidth) {
        int screenWidth = ScreenUtils.getDisplayScreenWidth();
        int visibleStartX = mLocalVisibleRect.left;
        int visibleEndX = mLocalVisibleRect.right;
        if (!mIsVisible) {
            if (mLocalVisibleRect.left < AppConstants.Number.NUMBER_0) {
                visibleStartX = canvasWidth - Math.min(canvasWidth, screenWidth);
                visibleEndX = canvasWidth;
            } else {
                visibleStartX = AppConstants.Number.NUMBER_0;
                visibleEndX = Math.min(canvasWidth, screenWidth);
            }
        }
        int visibleFlipStartX = isRTL() ? canvasWidth - visibleEndX : visibleStartX;
        int visibleFlipEndX = isRTL() ? canvasWidth - visibleStartX : visibleEndX;

        long visibleStartPosition = getOriginalDurationByLength(visibleFlipStartX) + mTrimInAdjusted;
        long visibleEndPosition = getOriginalDurationByLength(visibleFlipEndX) + mTrimInAdjusted;

        mThumbnailStartPosition = (visibleStartPosition / ICON_BASE_DURATION) * ICON_BASE_DURATION;
        mThumbnailEndPosition = (visibleEndPosition / ICON_BASE_DURATION + AppConstants.Number.NUMBER_2) * ICON_BASE_DURATION;
    }

    /**
     * 获取移除速度的原始时长
     * @param length 当前时间线上的像素长度
     *
     * @return 移除速度的原始时长
     */
    protected long getOriginalDurationByLength(long length) {
        double speed = (mClipMode != null) ? mClipMode.getSpeed() : AppConstants.Number.NUMBER_1f;
        return (long) (TrackHelper.getDurationByLength(length) * speed);
    }

    /**
     * 获取移除速度的原始长度
     * @param duration 当前时间线上的时长
     *
     * @return 移除速度的原始长度
     */
    protected long getOriginalLengthByDuration(long duration) {
        double speed = (mClipMode != null) ? mClipMode.getSpeed() : AppConstants.Number.NUMBER_1f;
        return (long) (TrackHelper.getLengthByDuration(duration) / speed);
    }

    /**
     * 移除缩略图请求任务
     * @param isAll 是否移除所有任务（不含起始位置）
     */
    public void removeThumbnailTask(boolean isAll) {
        Iterator<Map.Entry<Long, Pair<Long, Bitmap>>> iterator = mThumbnailTaskMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, Pair<Long, Bitmap>> entry = iterator.next();
            if (mNvsIconGenerator == null) {
                continue;
            }
            // 留一个起始缩略图用于占位，避免轨道渲染为空
            if (entry.getKey() == AppConstants.Number.NUMBER_0) {
                continue;
            }
            // 排除可见范围内的请求任务
            if (!isAll && isVisiblePosition(entry.getKey())) {
                    continue;
            }
            mNvsIconGenerator.cancelTask(entry.getValue().first);
            iterator.remove();
            GLog.d(TAG, LogFlag.DL, "[removeThumbnailTask]"
                + " position = " + entry.getKey() + ", taskId = " + entry.getValue());
        }
    }

    /**
     * 绘制时间文本标签
     * @param canvas 画布
     * @param clipViewWrapper 片段视图
     * @return 时间标签的矩形区域对
     */
    @Nullable
    private Pair<RectF, RectF> drawTimeLabel(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper) {
        // 检查是否可以绘制标签
        if (isInvalidForDrawing(clipViewWrapper)) {
            return null;
        }

        // 设置抗锯齿
        getPaint().setAntiAlias(true);

        // 获取时长字符串
        String duration = getDurationString();

        // 获取文本宽度
        float textWidth = getTextWidth(duration, LABEL_COMMON_TEXT_SIZE);

        // 获取时间标签的矩形区域
        RectF timeLabelRect = getTimeLabelRect(canvas, textWidth);

        // 获取悬浮时间标签的矩形区域
        Pair<RectF, Boolean> durationPair = getTimeLabelFloatingRect(timeLabelRect, textWidth, canvas.getWidth());
        // 绘制悬浮标签或在画布上绘制标签
        if (durationPair != null) {
            if (durationPair.second && !mIsDrawFloatingBoundary) {
                mIsDrawFloatingBoundary = true;
                if (!mIsRollbackScroll) {
                    drawDurationLabelOnCanvas(canvas, duration, timeLabelRect);
                }
                clipViewWrapper.postInvalidateClipView();
            }
            drawFloatingDurationLabel(duration, durationPair.first);
            if (!durationPair.second) {
                mIsDrawFloatingBoundary = false;
            }
        } else {
            mIsDrawFloatingBoundary = false;
            drawDurationLabelOnCanvas(canvas, duration, timeLabelRect);
        }

        // 返回时间标签的矩形区域对
        return new Pair<>(timeLabelRect, durationPair == null ? null : durationPair.first);
    }

    /**
     * 绘制变速标签
     * @param canvas 画布
     * @param clipViewWrapper 片段视图
     * @param durationLabelRectPair 时间标签的矩形区域对
     */
    private void drawSpeedLabel(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper, Pair<RectF, RectF> durationLabelRectPair) {
        if (clipViewWrapper.getClipConfig() == null || !clipViewWrapper.getClipConfig().isShowCurveSpeedLabel()) {
            return;
        }
        // 检查是否可以绘制标签
        if (isInvalidForDrawing(clipViewWrapper)) {
            return;
        }
        if (durationLabelRectPair == null || durationLabelRectPair.first == null) {
            GLog.e(TAG, LogFlag.DL, "[drawCurveSpeedLabel] durationLabelRectPair is null or durationLabelRectPair.first is null");
            return;
        }
        // 设置抗锯齿
        getPaint().setAntiAlias(true);
        // 获取变速描述
        String speederDesc = mClipMode.getSpeederDesc();
        //是否是曲线变速特效
        boolean isCurveSpeedEffect = mClipMode.isCurveSpeedEffect();
        if (TextUtils.isEmpty(speederDesc)) {
            clearFloatingCurveSpeedLabel();
            return;
        }

        // 获取文本宽度
        float textWidth = getTextWidth(speederDesc, isCurveSpeedEffect ? LABEL_NUMBER_TEXT_SIZE : LABEL_COMMON_TEXT_SIZE);

        RectF durationLabelRectInCanvas = durationLabelRectPair.first;
        // 获取背景矩形区域
        RectF speederRect = getSpeederRect(durationLabelRectInCanvas, textWidth, isCurveSpeedEffect);

        // 获取曲线速度图标
        mSpeederBitmap = getSpeederBitmap();
        if ((mSpeederBitmap == null) || (mSpeederBitmap.isRecycled())) {
            GLog.e(TAG, LogFlag.DL, "[drawCurveSpeedLabel] mCurveSpeedBitmap is null or recycled");
            return;
        }

        // 绘制悬浮标签或在画布上绘制标签
        if (durationLabelRectPair.second != null) {
            drawFloatingSpeederLabel(speederDesc, durationLabelRectPair.second, textWidth, isCurveSpeedEffect);
        } else {
            drawSpeederLabelOnCanvas(canvas, speederDesc, speederRect);
        }
    }

    /**
     * 检查是否不能绘制标签
     * @param clipViewWrapper 片段视图
     * @return 是否不能绘制标签
     */
    private boolean isInvalidForDrawing(@NonNull ClipViewWrapper clipViewWrapper) {
        if (!mIsVisible) {
            return true;
        }
        if (getPaint() == null) {
            GLog.e(TAG, LogFlag.DL, "[drawEffectText] paint is null");
            return true;
        }
        if (!clipViewWrapper.isSelected() && !clipViewWrapper.isSubSelected()) {
            return true;
        }
        if (!mClipMode.getIsInMainTrack()) {
            GLog.e(TAG, LogFlag.DL, "[drawTimeLabel] Clip is not in main track");
            return true;
        }
        return false;
    }

    /**
     * 获取时长字符串
     * @return 时长字符串
     */
    @NonNull
    private String getDurationString() {
        long time = (long) ((mTrimOutAdjusted - mTrimInAdjusted) / mClipMode.getSpeed());
        if (time == 0) {
            time = mClipMode.getAdjustedDuration();
        }
        return String.format(Locale.getDefault(), "%.1fs", (float) time / (float) TrackHelper.TIME_BASE);
    }

    /**
     * 获取文本宽度
     * @param text 文本
     * @param textSize 文本大小
     * @return 文本宽度
     */
    private float getTextWidth(String text, float textSize) {
        getPaint().setTextSize(textSize);
        getPaint().setTypeface(VideoLabelDrawAttr.TEXT_TYPEFACE);
        mFontMetrics = getPaint().getFontMetrics();
        return getPaint().measureText(text);
    }

    /**
     * 获取时间标签的矩形区域
     * @param canvas 画布
     * @param textWidth 文本宽度
     * @return 时间标签的矩形区域
     */
    @NonNull
    @Contract("_, _ -> new")
    private RectF getTimeLabelRect(@NonNull Canvas canvas, float textWidth) {
        int left = Math.max(0, mLocalVisibleRect.left - CLIP_VIEW_PADDING) + LABEL_BG_HORIZONTAL_MARGIN;
        // 由于非首个片段绘制时减掉了一个dp的间隔，所以非首个片段的标签左边距需要加上这个间距，不然看上去间距不足
        if (!mIsFirstClip) {
            left = left + ClipView.SEPARATION;
        }
        int right = (int) ((left + LABEL_TEXT_BG_HORIZONTAL_PADDING) + textWidth + LABEL_TEXT_BG_HORIZONTAL_PADDING);
        if (isRTL()) {
            right = Math.min(canvas.getWidth(), mLocalVisibleRect.right - CLIP_VIEW_PADDING) - LABEL_BG_HORIZONTAL_MARGIN;
            if (!mIsFirstClip) {
                right = right - ClipView.SEPARATION;
            }
            left = (int) (right - LABEL_TEXT_BG_HORIZONTAL_PADDING - textWidth - LABEL_TEXT_BG_HORIZONTAL_PADDING);
        }
        int bottom = canvas.getHeight() - LABEL_BG_HORIZONTAL_MARGIN;
        mBaseLine = bottom - LABEL_TEXT_BG_VERTICAL_PADDING - mFontMetrics.descent;
        int top = (int) (mBaseLine - Math.abs(mFontMetrics.ascent) - LABEL_TEXT_BG_VERTICAL_PADDING);
        return new RectF(left, top, right, bottom);
    }

    /**
     * 获取悬浮时间标签的矩形区域
     * @param timeLabelRect 时间标签的矩形区域
     * @param textWidth 文本宽度
     * @return Pair(悬浮区域，是否为边界)
     */
    @Nullable
    private Pair<RectF, Boolean> getTimeLabelFloatingRect(RectF timeLabelRect, float textWidth, int canvasWidth) {
        if (isRTL()) {
            int rightMargin = mLocalVisibleRect.right - LABEL_BG_HORIZONTAL_MARGIN;
            int floatingMargin = canvasWidth - CLIP_VIEW_PADDING - (ClipViewWrapper.TRANSITION_SIZE / AppConstants.Number.NUMBER_2);
            if ((rightMargin <= floatingMargin) && getDrawListener() != null) {
                int right = ScreenUtils.getDisplayScreenWidth() - LABEL_BG_HORIZONTAL_MARGIN;
                int left = (int) (right - LABEL_TEXT_BG_HORIZONTAL_PADDING - textWidth - LABEL_TEXT_BG_HORIZONTAL_PADDING);
                return new Pair<>(new RectF(left, timeLabelRect.top, right, timeLabelRect.bottom), rightMargin == floatingMargin);
            }
        } else {
            int leftMargin = mLocalVisibleRect.left + LABEL_BG_HORIZONTAL_MARGIN;
            int floatingMargin = CLIP_VIEW_PADDING + (ClipViewWrapper.TRANSITION_SIZE / AppConstants.Number.NUMBER_2);
            if ((leftMargin >= floatingMargin) && (getDrawListener() != null)) {
                int left = LABEL_BG_HORIZONTAL_MARGIN;
                int right = (int) (left + LABEL_TEXT_BG_HORIZONTAL_PADDING + textWidth + LABEL_TEXT_BG_HORIZONTAL_PADDING);
                return new Pair<>(new RectF(left, timeLabelRect.top, right, timeLabelRect.bottom), leftMargin == floatingMargin);
            }
        }
        return null;
    }

    /**
     * 绘制悬浮标签
     * @param duration 时长字符串
     * @param timeLabelFloatingRect 悬浮时间标签的矩形区域
     */
    private void drawFloatingDurationLabel(@NonNull String duration, @NonNull RectF timeLabelFloatingRect) {
        VideoLabelDrawAttr.TextDrawAttr textDrawAttr = new VideoLabelDrawAttr.TextDrawAttr(
                duration,
                timeLabelFloatingRect.left + LABEL_TEXT_BG_HORIZONTAL_PADDING,
                mBaseLine,
                LABEL_COMMON_TEXT_SIZE
        );
        VideoLabelDrawAttr attr = new VideoLabelDrawAttr(
                textDrawAttr,
                new RectF(timeLabelFloatingRect),
                null
        );
        if (getDrawListener() != null) {
            getDrawListener().onDrawLabel(VideoLabelType.DURATION, attr);
        }
    }

    /**
     * 在画布上绘制标签
     * @param canvas 画布
     * @param duration 时长字符串
     * @param timeLabelRect 时间标签的矩形区域
     */
    private void drawDurationLabelOnCanvas(@NonNull Canvas canvas, String duration, @NonNull RectF timeLabelRect) {
        if (getPaint() == null) {
            GLog.e(TAG, LogFlag.DL, "[drawDurationLabelOnCanvas] paint is null");
            return;
        }
        drawBackground(canvas, timeLabelRect);
        // 绘制文本
        getPaint().setStyle(Paint.Style.FILL);
        getPaint().setColor(VideoLabelDrawAttr.TEXT_COLOR);
        canvas.drawText(duration, timeLabelRect.left + LABEL_TEXT_BG_HORIZONTAL_PADDING, mBaseLine, getPaint());

        if ((getDrawListener() != null) && (!mIsDrawFloatingBoundary)) {
            getDrawListener().onDrawLabel(VideoLabelType.DURATION, null);
        }
    }

    /**
     * 清除悬浮曲线速度标签
     */
    private void clearFloatingCurveSpeedLabel() {
        if (getDrawListener() != null) {
            getDrawListener().onDrawLabel(VideoLabelType.CURVE_SPEED_TEMPLATE, null);
        }
    }

    /**
     * 获取曲线速度标签的矩形区域
     * @param timeLabelRect 时间标签的矩形区域
     * @param textWidth 文本宽度
     * @return 曲线速度标签的矩形区域
     * @param isCurveSpeedEffect 是否为曲线速度效果
     */
    @NonNull
    @Contract("_, _, _ -> new")
    private RectF getSpeederRect(@NonNull RectF timeLabelRect, float textWidth, boolean isCurveSpeedEffect) {
        float rectLeft = LABEL_MARGIN_START + timeLabelRect.right;
        float rectRight = (int) (rectLeft + getSpeedLabelWidth(textWidth));
        if (isRTL()) {
            rectRight = timeLabelRect.left - LABEL_MARGIN_START;
            rectLeft = rectRight - getSpeedLabelWidth(textWidth);
        }
        float rectBottom = timeLabelRect.bottom;
        float rectTop = timeLabelRect.top;
        mBaseLine = rectBottom - LABEL_TEXT_BG_VERTICAL_PADDING - mFontMetrics.descent;
        if (isCurveSpeedEffect) {
            mBaseLine -= (LABEL_COMMON_TEXT_SIZE - LABEL_NUMBER_TEXT_SIZE) / AppConstants.Number.NUMBER_2;
        }
        return new RectF(rectLeft, rectTop, rectRight, rectBottom);
    }

    /**
     * 绘制浮动的速度标签，包括文本和图标。
     * 
     * @param curveSpeedTemplateDesc 速度模板描述文本
     * @param timeLabelFloatingRect 浮动标签的位置矩形区域
     * @param textWidth 文本的宽度
     * @param isCurveSpeedEffect 是否为曲线速度效果
     */
    private void drawFloatingSpeederLabel(
        String curveSpeedTemplateDesc,
        @NonNull RectF timeLabelFloatingRect,
        float textWidth,
        boolean isCurveSpeedEffect
    ) {
        float rectLeft = timeLabelFloatingRect.right + LABEL_MARGIN_START;
        float rectRight = rectLeft + getSpeedLabelWidth(textWidth);
        if (isRTL()) {
            rectRight = timeLabelFloatingRect.left - LABEL_MARGIN_START;
            rectLeft = rectRight - getSpeedLabelWidth(textWidth);
        }
        float textLeft = rectLeft + getSpeedLabelIconWidth();

        VideoLabelDrawAttr.TextDrawAttr textDrawAttr = new VideoLabelDrawAttr.TextDrawAttr(
                curveSpeedTemplateDesc,
                textLeft,
                mBaseLine,
                isCurveSpeedEffect ? LABEL_NUMBER_TEXT_SIZE : LABEL_COMMON_TEXT_SIZE
        );
        VideoLabelDrawAttr.IconDrawAttr iconDrawAttr = new VideoLabelDrawAttr.IconDrawAttr(
                mSpeederBitmap,
                rectLeft + LABEL_ICON_MARGIN_START,
                timeLabelFloatingRect.top + LABEL_ICON_MARGIN_TOP
        );
        VideoLabelDrawAttr attr = new VideoLabelDrawAttr(
                textDrawAttr,
                new RectF(rectLeft, timeLabelFloatingRect.top, rectRight, timeLabelFloatingRect.bottom),
                iconDrawAttr
        );
        if (getDrawListener() != null) {
            getDrawListener().onDrawLabel(VideoLabelType.CURVE_SPEED_TEMPLATE, attr);
        }
    }

    /**
     * 绘制变速标签在画布上
     * @param canvas 画布
     * @param curveSpeedTemplateDesc 曲线速度模板描述
     * @param curveSpeedRect 曲线速度标签的矩形区域
     */
    private void drawSpeederLabelOnCanvas(@NonNull Canvas canvas, String curveSpeedTemplateDesc, RectF curveSpeedRect) {
        // 绘制背景
        drawBackground(canvas, curveSpeedRect);

        // 绘制图标
        float iconLeft = curveSpeedRect.left + LABEL_ICON_MARGIN_START;
        canvas.drawBitmap(mSpeederBitmap, iconLeft, curveSpeedRect.top + LABEL_ICON_MARGIN_TOP, null);

        // 绘制文本
        getPaint().setColor(VideoLabelDrawAttr.TEXT_COLOR);
        float textLeft = curveSpeedRect.left + getSpeedLabelIconWidth();
        canvas.drawText(curveSpeedTemplateDesc, textLeft, mBaseLine, getPaint());

        // 回调给视频轨道，不需要绘制悬浮时长标签
        if (getDrawListener() != null) {
            getDrawListener().onDrawLabel(VideoLabelType.CURVE_SPEED_TEMPLATE, null);
        }
    }

    /**
     * 绘制背景
     * @param canvas 画布
     * @param rect 矩形区域
     */
    private void drawBackground(@NonNull Canvas canvas, RectF rect) {
        getPaint().setColor(VideoLabelDrawAttr.TEXT_BG_COLOR);
        canvas.drawRoundRect(rect, VideoLabelDrawAttr.TEXT_BG_RADIUS, VideoLabelDrawAttr.TEXT_BG_RADIUS, getPaint());
    }

    /**
     * 获取变速标签Icon的宽度
     * @return 变速标签Icon的宽度
     */
    private float getSpeedLabelIconWidth() {
        return LABEL_ICON_MARGIN_START + LABEL_ICON_WIDTH + LABEL_CURVE_SPEED_TEXT_MARGIN_START;
    }

    /**
     * 获取变速图标的Bitmap对象。
     * 如果当前的mCurveSpeedBitmap为null或者已经被回收，则从资源文件中加载新的Bitmap，
     * 并根据指定的宽度进行调整和居中裁剪。
     *
     * @return 曲线速度图标的Bitmap对象
     */
    private Bitmap getSpeederBitmap() {
        if ((mSpeederBitmap == null) || mSpeederBitmap.isRecycled()) {
            Bitmap bitmap = BitmapFactory.decodeResource(ContextGetter.context.getResources(), R.drawable.videoeditor_trim_submenu_speed, null);
            mSpeederBitmap = BitmapUtils.resizeAndCropCenter(bitmap, LABEL_ICON_WIDTH, false);
        }
        return mSpeederBitmap;
    }

    /**
     * 获取变速标签的宽度
     * @param textWidth 文本的宽度
     * @return 变速标签的宽度
     */
    private int getSpeedLabelWidth(float textWidth) {
        // 计算曲线速度模板矩形的右侧位置，包括图标左边距、图标宽度、文本左边距、文本宽度和文本背景水平内边距
        return (int) (getSpeedLabelIconWidth() + textWidth + LABEL_TEXT_BG_HORIZONTAL_PADDING);
    }

    /**
     * 绘制关键帧
     * @param canvas 画布
     * @param clipViewWrapper 片段视图
     */
    private void drawKeyFrame(@NonNull Canvas canvas, @NonNull ClipViewWrapper clipViewWrapper) {
        if (clipViewWrapper.isSelected() && (mClipMode.getClip() != null)) {
            List<BaseKeyFrameInfo> keyFrameList = mClipMode.getClip().getKeyFrameList();
            if (keyFrameList != null) {
                int selectKeyframeX = AppConstants.Number.NUMBER_MINUS_1;
                for (BaseKeyFrameInfo keyFrameInfo : keyFrameList) {
                    long keyframeTime = keyFrameInfo.getInPoint() - mTrimInAdjusted;
                    if (keyframeTime < AppConstants.Number.NUMBER_0) {
                        continue;
                    }
                    int keyframeTimeX = (int) TrackHelper.INSTANCE.getLengthByCurrentDuration(keyframeTime, ContextGetter.context);
                    if (keyFrameInfo.getInPoint() == clipViewWrapper.getSelectKeyframeInPoint()) {
                        selectKeyframeX = keyframeTimeX;
                    } else {
                        if (mKeyFrame != null) {
                            canvas.drawBitmap(
                                mKeyFrame,
                                keyframeTimeX - KEY_FRAME_ICON_HALF_WIDTH,
                                (float) canvas.getHeight() / AppConstants.Number.NUMBER_2 - KEY_FRAME_ICON_HALF_HEIGHT,
                                getPaint());
                        }
                    }
                }
                if (selectKeyframeX > AppConstants.Number.NUMBER_MINUS_1) {
                    if (mKeyFrameSelect != null) {
                        canvas.drawBitmap(
                            mKeyFrameSelect,
                            selectKeyframeX - KEY_FRAME_SELECT_ICON_HALF_WIDTH,
                            (float) canvas.getHeight() / AppConstants.Number.NUMBER_2 - KEY_FRAME_SELECT_ICON_HALF_HEIGHT,
                            getPaint());
                    }
                }
            }
        }
    }
}
