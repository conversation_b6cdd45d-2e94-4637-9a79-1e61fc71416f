/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetTrackConstant.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/12/29        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.track

object WidgetTrackConstant {
    // 事件大类：桌面卡片
    const val TYPE_WIDGET = "2006020"

    object EventId {
        const val WIDGET_ADD_OR_REMOVE = "2006020001"
        const val WIDGET_MODE_SELECT = "2006020002"
        const val WIDGET_CLICK = "2006020003"
    }

    object Key {
        const val ADD_OR_REMOVE = "widget_added_or_delete"
        const val TYPE = "type"
        const val MODE = "mode"
        const val LABEL = "label"
    }

    object Value {
        const val ADD = "add"
        const val REMOVE = "delete"
        const val TYPE_MODE_CHANGE = "mode_change"
        const val TYPE_MODE_NEW = "mode_new"
        const val MODE_RECOMMENDED = "feeded"
        const val MODE_CUSTOM = "customed"
    }
}