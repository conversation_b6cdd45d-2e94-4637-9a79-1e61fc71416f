/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - BottomActionBar.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/04
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/04    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.ui;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.ui.helper.RippleHelper;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.videoeditorpage.R;

public class BottomActionBar extends RelativeLayout {
    private static final String TAG = "BottomActionBar";
    private ViewGroup mBottomActionBar;
    private TextView mTitleView;
    private TextView mTvCancel;
    private TextView mTvDone;
    private TextView mTextBtnCancel;
    private TextView mTextBtnDone;
    private CornerPoint mCornerPoint;
    private int mNarrowModePadding = 0;
    private int mContentPaddingBottom = 0;
    private boolean mInContentNarrowMode = false;

    public BottomActionBar(Context context) {
        this(context, null);
    }

    public BottomActionBar(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BottomActionBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mBottomActionBar = createActionBar();
        mTitleView = (TextView) mBottomActionBar.findViewById(R.id.editor_id_title);
        mTvCancel = (TextView) mBottomActionBar.findViewById(R.id.editor_id_action_cancel);
        mTvDone = (TextView) mBottomActionBar.findViewById(R.id.editor_id_action_done);
        mTextBtnCancel = (TextView) mBottomActionBar.findViewById(R.id.editor_id_text_action_cancel);
        mTextBtnDone = (TextView) mBottomActionBar.findViewById(R.id.editor_id_text_action_done);
        mNarrowModePadding = getResources().getDimensionPixelSize(R.dimen.videoeditor_bottom_action_bar_adjust_padding);
        mContentPaddingBottom = getResources().getDimensionPixelSize(R.dimen.videoeditor_bottom_action_bar_content_padding_bottom);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.BottomActionBar, defStyleAttr, 0);

        mTitleView.setBackground(null);

        CharSequence cancelBtnText = a.getString(R.styleable.BottomActionBar_actionCancelDrawable);
        if (!TextUtils.isEmpty(cancelBtnText)) {
            mTextBtnCancel.setVisibility(VISIBLE);
            mTextBtnCancel.setText(cancelBtnText);
            RippleHelper.setRippleBackgroundBorderless(mTextBtnCancel);
            mTvCancel.setVisibility(GONE);
        } else {
            CharSequence cancelText = a.getString(R.styleable.BottomActionBar_actionCancelText);
            if (!TextUtils.isEmpty(cancelText)) {
                mTvCancel.setVisibility(VISIBLE);
                mTvCancel.setText(cancelText);
                mTvCancel.setContentDescription(cancelText);
                mTextBtnCancel.setVisibility(INVISIBLE);
            }
        }

        CharSequence doneBtnText = a.getString(R.styleable.BottomActionBar_actionDoneDrawable);
        if (!TextUtils.isEmpty(doneBtnText)) {
            mTextBtnDone.setVisibility(VISIBLE);
            mTextBtnDone.setText(doneBtnText);
            RippleHelper.setRippleBackgroundBorderless(mTextBtnDone);
            mTvDone.setVisibility(GONE);
        } else {
            CharSequence doneText = a.getString(R.styleable.BottomActionBar_actionDoneText);
            if (!TextUtils.isEmpty(doneText)) {
                mTvDone.setVisibility(VISIBLE);
                mTvDone.setText(doneText);
                mTvDone.setContentDescription(doneText);
                mTextBtnDone.setVisibility(INVISIBLE);
            }
        }
        ColorStateList actionTintColor = a.getColorStateList(R.styleable.BottomActionBar_actionTintColor);
        if (actionTintColor != null) {
            mTvCancel.setTextColor(actionTintColor);
            mTvDone.setTextColor(actionTintColor);
        }
        a.recycle();
        initCornerPoint(context);
    }

    public void setTitle(CharSequence title) {
        mTitleView.setVisibility(VISIBLE);
        mTitleView.setText(title);
        mTitleView.setContentDescription(title);
    }

    private ViewGroup createActionBar() {
        return (ViewGroup) LayoutInflater.from(getContext()).inflate(getContentLayoutId(), this, true);
    }

    protected int getContentLayoutId() {
        return R.layout.videoeditor_video_editor_base_bottom_action_bar_layout;
    }

    public void setTitleBackground(int res) {
        mTitleView.setBackgroundResource(res);
    }

    public void setTitleSelected(boolean selected) {
        mTitleView.setSelected(selected);
    }

    public void setTitleContentDescription(String text) {
        mTitleView.setContentDescription(text);
    }

    public void setActionDoneEnable(boolean enable) {
        if (mTvDone != null) {
            mTvDone.setEnabled(enable);
        }
    }

    public void setActionImageDoneEnable(boolean enable) {
        if (mTextBtnDone != null) {
            mTextBtnDone.setEnabled(enable);
        }
    }

    private void initCornerPoint(Context context) {
        mCornerPoint = (CornerPoint) mBottomActionBar.findViewById(R.id.editor_id_action_done_corner_point);
        if (mTvDone != null) {
            RelativeLayout.LayoutParams cornerPointLayoutParams = (RelativeLayout.LayoutParams) mCornerPoint.getLayoutParams();
            RelativeLayout.LayoutParams tvDoneLayoutParams = (RelativeLayout.LayoutParams) mTvDone.getLayoutParams();
            cornerPointLayoutParams.topMargin = tvDoneLayoutParams.topMargin - cornerPointLayoutParams.height;
            if (ResourceUtils.isRTL(context)) {
                int width = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
                int height = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
                mTvDone.measure(width, height);
                cornerPointLayoutParams.setMarginEnd(tvDoneLayoutParams.getMarginEnd() + mTvDone.getMeasuredWidth());
            } else {
                cornerPointLayoutParams.setMarginEnd(tvDoneLayoutParams.getMarginEnd() - cornerPointLayoutParams.width);
            }
            mCornerPoint.setLayoutParams(cornerPointLayoutParams);
        }
    }

    public void setIsDrawTips(boolean isDrawTips) {
        mCornerPoint.setShowState(isDrawTips);
    }


    public void setContentNarrowMode(boolean narrow) {
        if (narrow == mInContentNarrowMode) {
            return;
        }
        mInContentNarrowMode = narrow;
        int res = 0;
        if (narrow) {
            res = mNarrowModePadding;
        }
        int finalRes = res;
        post(new Runnable() {
            @Override
            public void run() {
                setPadding(finalRes, 0, finalRes, 0);
            }
        });
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        updateActionBarContentWidth();
    }

    /**
     * 当处于大屏90度且没有导航栏（非浮窗）时
     * actionBar会被摄像头遮挡，需要调整内容宽度
     * 需要在窗口切换，界面进出返回时触发更新
     */
    private void updateActionBarContentWidth() {
        BaseActivity activity = null;
        Context context = getContext();
        if (context instanceof BaseActivity) {
            activity = (BaseActivity) context;
        }
        if (activity != null) {
            /*setContentNarrowMode(
                    EditorUIConfig.INSTANCE.isActionBarContentNarrow(activity, mContentPaddingBottom)
            );*/
        }
    }

    /**
     * 设置titleView的起始边缘
     */
    public void setTitleViewLayout() {
        if (mTitleView == null) {
            return;
        }
        RelativeLayout.LayoutParams titleLayoutParams = (RelativeLayout.LayoutParams) mTitleView.getLayoutParams();
        if (isTitleViewShow()) {
            titleLayoutParams.addRule(RelativeLayout.START_OF, R.id.editor_id_text_action_done);
            titleLayoutParams.addRule(RelativeLayout.END_OF, R.id.editor_id_text_action_cancel);
        } else {
            titleLayoutParams.addRule(RelativeLayout.START_OF, R.id.editor_id_action_done);
            titleLayoutParams.addRule(RelativeLayout.END_OF, R.id.editor_id_action_cancel);
        }
        mTitleView.setLayoutParams(titleLayoutParams);
    }

    /**
     * 是否展示标题
     * @return 是否展示标题
     */
    private boolean isTitleViewShow() {
        if (mTitleView != null) {
            return (mTitleView.getVisibility() == ViewGroup.VISIBLE) && (!TextUtils.isEmpty(mTitleView.getText()));
        }
        return false;
    }
}
