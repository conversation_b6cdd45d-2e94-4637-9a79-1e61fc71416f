/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoDetailsConstants.kt
 ** Description : 大图页面详细信息字段定义
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.details

/**
 * 大图详情字段定义
 */
object PhotoDetailsConstants {

    object Key {
        /**
         * 媒体格式（Int）：RAW、XHD、YUV、HDR等
         */
        const val FORMAT_TYPE = "format_type"

        /**
         * 位置信息（String），如：深圳市
         */
        const val LOCATION = "location"

        /**
         * 日期信息（String），如：今天、昨天、4月3日
         */
        const val DATE = "date"

        /**
         * 时间信息（String），如：12:00
         */
        const val TIME = "time"

        /**
         * 按照指定规则格式化的日期和时间
         */
        const val FORMAT_DATE_TIME = "format_date_time"

        /**
         * 收藏状态（Boolean）
         */
        const val IS_FAVORITE = "favorite"

        /**
         * 是否为共享文件（Boolean）
         */
        const val IS_SHARED_FILE = "is_shared_file"

        /**
         * 是否为视频（Boolean）
         */
        const val IS_VIDEO = "is_video"
    }

    /**
     * 媒体格式
     * @see [PhotoDetailsConstants.Key.FORMAT_TYPE]
     */
    object FormatType {
        const val OTHER = 0
        const val RAW = 1
        const val XHD = 2
        const val YUV = 3
        const val HDR = 4
        const val DOLBY = 5
        const val OLIVE = 6
        // AI 风光图片
        const val AI_SCENERY = 7
        //Log视频
        const val LOG_VIDEO = 8
    }

    /**
     * 镜头字段
     */
    object PhotoDetailsLensModelKeys {
        /**
         * 前置摄像头，字段与相机对齐，请勿轻易改动
         */
        const val FRONT = "front"

        /**
         * 显微摄像头，字段与相机对齐，请勿轻易改动
         */
        const val MICRO = "micro"

        /**
         * 微距摄像头，字段与相机对齐，请勿轻易改动
         */
        const val MACRO = "macro"

        /**
         * 后置摄像头，字段与相机对齐，请勿轻易改动
         */
        const val BACK = "back"

        /**
         * 超广角摄像头，字段与相机对齐，请勿轻易改动
         */
        const val ULTRA_WIDE = "ultra wide"

        /**
         * 广角摄像头，字段与相机对齐，请勿轻易改动
         */
        const val WIDE = "wide"

        /**
         * 长焦摄像头，字段与相机对齐，请勿轻易改动
         */
        const val TELEPHOTO = "tele"

        /**
         * 有摄像头信息，但是不符合我们的展示逻辑，直接只展示内容
         */
        const val HAS_INFO = "has_info"

        /**
         * 无摄像头信息
         */
        const val NONE = "none"

        /**
         * 焦距0
         */
        const val ZERO = 0

        /**
         * 焦距20
         */
        const val TWENTY = 20

        /**
         * 焦距40
         */
        const val FORTY = 40
    }

    /**
     * 截屏、录屏Keys
     */
    object PhotoDetailsModelKeys {
        /**
         * 截屏
         */
        const val FORMAT_SCREENSHOT_IMAGE = 1001L

        /**
         * 录屏
         */
        const val FORMAT_SCREENSHOT_VIDEO = 1002L
    }
}