package com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle;

import android.graphics.Bitmap;
import androidx.annotation.Nullable;

abstract class LruPoolStrategy {

    protected final float maxReuseMultiple;

    LruPoolStrategy(float maxReuseMultiple) {
        this.maxReuseMultiple = maxReuseMultiple;
    }

    abstract boolean put(Bitmap bitmap);

    @Nullable
    abstract Bitmap get(int width, int height, Bitmap.Config config);

    @Nullable
    abstract Bitmap removeLast();

    abstract String logBitmap(Bitmap bitmap);

    abstract String logBitmap(int width, int height, Bitmap.Config config);

    abstract int getSize(Bitmap bitmap);
}
