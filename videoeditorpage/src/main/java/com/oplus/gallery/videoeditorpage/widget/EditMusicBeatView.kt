/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditTimelineTrailView.kt
 ** Description : EditTimelineTrailView
 ** Version     : 1.0
 ** Date        : 2025/8/1 16:07
 ** Author      : 16601329908
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  16601329908     2025/8/1  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper.getLengthNoScaleByCurrentDuration
import kotlin.math.max

class EditMusicBeatView @JvmOverloads constructor(
    private val context: Context,
    attrs: AttributeSet? = null
) : RelativeLayout(context, attrs) {

    /**
     * 未选中节拍点的颜色
     */
    @ColorInt
    private var circleColor = 0

    /**
     * 选中节拍点的颜色
     */
    @ColorInt
    private var selectCircleColor = 0

    /**
     * 存储节拍点时间戳的列表
     */
    private var beatList: ArrayList<Long>? = null

    /**
     *  音频剪辑的起始时间
     */
    private var trimIn: Long = 0 //

    /**
     * 音频剪辑的结束时间
     */
    private var trimOut: Long = 0

    /**
     *  当前编辑点的时间戳
     */
    private var curPoint: Long = 0 //

    /**
     * 选中节拍点的直径
     */
    private var editSelectDiameter = 0

    /**
     * 选中节拍点的直径
     */
    private var editSelectDiameterRadius = 0

    /**
     * 未选中节拍点的直径
     */
    private var editUnSelectDiameter = 0

    /**
     * 未选中节拍点的直径
     */
    private var editUnSelectDiameterRadius = 0

    /**
     * 用于显示节拍点线的视图
     */
    private var pointLineView: EditPointLineView? = null

    /**
     * 屏幕密度缩放比例，用于适配不同分辨率的设备
     */
    private var densityScaled = 1f

    /**
    卡点音频剪辑的起始边界
     *
     */
    private var end: Long = 0

    /**
    最后一个卡点音频剪辑的结束边界
     *
     */
    private var start: Long = 0

    constructor(context: Context, inMainTrack: Boolean) : this(context, null)

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.EditMusicBeatView)
        try {
            editSelectDiameter = typedArray.getDimensionPixelSize(R.styleable.EditMusicBeatView_item_edit_select_diameter, 0)
            editUnSelectDiameter = typedArray.getDimensionPixelSize(R.styleable.EditMusicBeatView_item_edit_un_select_diameter, 0)
            editSelectDiameterRadius = editSelectDiameter / 2
            editUnSelectDiameterRadius = editUnSelectDiameter / 2
            circleColor = typedArray.getColor(
                R.styleable.EditMusicBeatView_item_edit_round_color,
                ContextCompat.getColor(context, R.color.color_8AFFFFFF)
            )
            selectCircleColor = ThemeHelper.getCouiColorContainerTheme(context)
        } finally {
            typedArray.recycle()
        }
    }

    fun setMusicBeatHeight(unSelHeight: Int, selHeight: Int) {
        this.editUnSelectDiameter = unSelHeight
        this.editSelectDiameter = selHeight
        editSelectDiameterRadius = editSelectDiameter / 2
        editUnSelectDiameterRadius = editUnSelectDiameter / 2
        updateBeatArray(beatList)
    }

    fun setPixelPerMicrosecond(pixelPerMicrosecond: Double) {
        updateBeatArray(beatList)
    }

    fun setCurPoint(curPoint: Long) {
        this.curPoint = curPoint
        pointLineView?.invalidate()
    }

    fun initView(trimIn: Long, trimOut: Long, beatList: ArrayList<Long>?, pixelPerMicrosecond: Double) {
        this.beatList = beatList
        this.trimIn = trimIn
        this.trimOut = trimOut
        beatBoundry()
        updateBeatArray(this.beatList)
    }

    /**
     * 计算卡点编辑的显示时间区间边界
     */
    private fun beatBoundry() {
        end = trimOut - MIN_BEAT_LEN
        start = trimIn + MIN_BEAT_LEN
    }

    fun setTrimIn(trimIn: Long) {
        this.trimIn = trimIn
        start = this.trimIn + MIN_BEAT_LEN
        updateBeatArray(beatList)
    }

    fun setTrimOut(trimOut: Long) {
        this.trimOut = trimOut
        end = this.trimOut - MIN_BEAT_LEN
        updateBeatArray(beatList)
    }

    fun updateBeatArray(beatList: ArrayList<Long>?) {
        this.beatList = beatList
        removeAllViews()
        addMusicBeats()
    }

    private fun addMusicBeats() {
        if ((trimIn < 0) || (trimOut < 0) || (trimIn >= trimOut)) {
            e(TAG, LogFlag.DL, String.format("addMusicBeats,trimIn=%d trimOut=%d", trimIn, trimOut))
            return
        }
        val gap = trimOut - trimIn
        val lineWidth = getLengthNoScaleByCurrentDuration(gap, context).toInt()
        pointLineView = EditPointLineView(context).apply {
            val spanItemParams = LayoutParams(lineWidth, max(editUnSelectDiameter, editSelectDiameter))
            pointLineView?.layoutParams = spanItemParams
            <EMAIL>(this, spanItemParams)
        }
    }

    private inner class EditPointLineView(context: Context) : View(context) {
        private val paint = Paint()

        init {
            paint.isAntiAlias = true
            paint.color = circleColor
        }

        /**
         * 在画布上绘制节拍点。
         * 该函数根据节拍列表中的每个节拍点，计算其在画布上的位置，并根据当前点是否在节拍范围内，
         * 选择不同的半径和颜色进行绘制。如果节拍点在修剪范围内，则跳过绘制。
         *
         * @param canvas 用于绘制的画布对象，不能为null。
         */
        override fun onDraw(canvas: Canvas) {
            // 如果节拍列表为空，则直接返回
            beatList.getOrLog(TAG, "onDraw,mBeatList IS NULL")?.let {
                // 遍历节拍列表中的每个节拍点
                for (innerPoint in it) {
                    // 如果节拍点在修剪开始点之前或接近修剪开始点，则跳过绘制并记录日志
                    if (innerPoint <= start) {
                        continue
                    }
                    // 如果节拍点在修剪结束点之后或接近修剪结束点，则跳过绘制并记录日志
                    if (innerPoint > end) {
                        continue
                    }
                    /* 初始化绘制半径为未选中状态的半径
                     如果当前点在该范围内，则使用选中状态的半径和颜色*/
                    val radius = if (curPoint in (innerPoint - HALF_MIN_BEAT_LEN)..(innerPoint + HALF_MIN_BEAT_LEN)) {
                        paint.color = selectCircleColor
                        editSelectDiameterRadius.toFloat()
                    } else {
                        paint.color = circleColor
                        editUnSelectDiameterRadius.toFloat()
                    }

                    // 计算节拍点与修剪开始点之间的时间差
                    val gap = innerPoint - trimIn
                    // 根据是否在主轨道上，计算节拍点在画布上的x坐标
                    val x = getLengthNoScaleByCurrentDuration(gap, context)
                    // 在画布上绘制节拍点
                    canvas.drawCircle(x * densityScaled, max(editSelectDiameterRadius, editUnSelectDiameterRadius).toFloat(), radius, paint)
                }
            }
        }
    }

    companion object {
        private const val TAG = "EditMusicBeatView"

        /**
         * 最小节拍时间间隔
         */
        const val MIN_BEAT_LEN: Long = 200000L
        const val MIN_BEAT_LEN_MILLS: Int = (MIN_BEAT_LEN / 1000).toInt()
        const val HALF_MIN_BEAT_LEN: Long = MIN_BEAT_LEN / 2
    }
}
