/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorExportOliveUIScheme.kt
 ** Description: 导出实况业务UI适配
 ** Version: 1.0
 ** Date: 2025/6/24
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/24        1.0        created
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.exportolive

import android.content.res.Configuration
import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewStub
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.ui.EditorUIExecutor
import com.oplus.gallery.foundation.ui.systembar.OnSystemBarChangeListener
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
import com.oplus.gallery.videoeditorpage.controler.IVideoEditorUIScheme
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig.FORM_TRIM_PAGE
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView
import kotlinx.coroutines.launch

/**
 * 导出实况业务UI适配
 */
class VideoEditorExportOliveUIScheme(
    private val activity: BaseActivity,
    private val rootView: ViewGroup,
    private val editorEngine: EditorEngine
) : IVideoEditorUIScheme {

    /**
     * 左侧安全区
     */
    private val leftSafeArea by lazy {
        rootView.findViewById<View>(R.id.start_safe_area)
    }

    /**
     * 右侧安全区
     */
    private val rightSafeArea by lazy {
        rootView.findViewById<View>(R.id.end_safe_area)
    }

    /**
     * 预览区
     */
    private val previewContainer by lazy {
        rootView.findViewById<EditorPreviewView>(R.id.engine_preview_layout)
    }

    /**
     * 顶部操作栏，继续、取消的容器
     */
    private val actionContainer by lazy {
        rootView.findViewById<RelativeLayout>(R.id.action_bar)
    }

    /**
     * 底部安全间距控件，用来适配导航栏
     */
    private val bottomSafeView by lazy {
        rootView.findViewById<View>(R.id.bottom_safe)
    }

    /**
     * 预览播放按钮
     */
    private val exportPlayBackView by lazy {
        rootView.findViewById<ExportPlayBackView>(R.id.preview_export_video)
    }

    /**
     * 剪辑控件容器
     */
    private val toolbarContainer by lazy {
        rootView.findViewById<FrameLayout>(R.id.toolbar_container)
    }

    /**
     * 系统栏变化监听器
     */
    private val systemBarChangeListener: OnSystemBarChangeListener =
        object : OnSystemBarChangeListener {
            override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
                adaptSysBarMargin()
            }
        }

    /**
     * UI规则的执行者
     */
    private val uiExecutor = EditorUIExecutor()

    private val editorAppUiConfig by lazy {
        EditorAppUiConfig(activity.getCurrentAppUiConfig(), false)
    }

    init {
        val viewStub = rootView.findViewById<ViewStub>(R.id.video_editor_container_view_stub)
        if (viewStub != null) {
            viewStub.layoutResource = getContentLayoutId(activity.getCurrentAppUiConfig())
            viewStub.inflate()
        }
        uiExecutor.init(activity)
        uiExecutor.setScheme(this@VideoEditorExportOliveUIScheme, null)
        editorAppUiConfig.appUiConfig = activity.getCurrentAppUiConfig()
        uiExecutor.checkUpdateLayout(editorAppUiConfig)
        activity.registerSystemBarChangeListener(systemBarChangeListener)

        previewContainer.getChildAt(0).setBackgroundColor(Color.TRANSPARENT)

        activity.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> release()

                    else -> Unit
                }
            }
        })
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return R.layout.videoeditor_frame_exportolive_layout
    }

    override fun getReconfigureViewIds(): List<Int> {
        return listOf(
            R.id.action_bar,
            R.id.engine_preview_layout,
            R.id.toolbar_container
        )
    }

    override fun getRootView(): ViewGroup {
        return rootView
    }

    override fun onUiConfigChanged(
        baseActivity: BaseActivity,
        layoutId: Int,
        config: EditorAppUiConfig
    ) {
        adaptSysBarMargin()
        adaptSafeArea(config.appUiConfig)
        adaptToolbar(config.appUiConfig)
    }

    /**
     * 通知UI配置发生了变化
     *
     * @param config UI配置
     */
    override fun notifyAppUiConfigChange(config: AppUiResponder.AppUiConfig) {
        GLog.d(TAG, LogFlag.DL) {
            "[notifyAppUiConfigChange]"
        }
        uiExecutor.onAppUiConfigChanged(editorAppUiConfig.apply { appUiConfig = config })
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        activity.lifecycleScope.launch {
            editorEngine.seekTo(
                editorEngine.timelineCurrentPosition,
                STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
            )
        }
    }

    /**
     * 适配系统导航栏和状态栏间距
     */
    private fun adaptSysBarMargin() {
        (actionContainer.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.topMargin = activity.resources.getDimensionPixelSize(R.dimen.videoeditor_wallpaper_trim_safe_top_margin)
            actionContainer.requestLayout()
        }
        (bottomSafeView.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.height = activity.bottomNaviBarHeight()
            bottomSafeView.requestLayout()
        }
    }

    private fun adaptSafeArea(config: AppUiResponder.AppUiConfig) {
        VideoEditorUIConfig.adaptSafeAreaInVideo(activity, leftSafeArea, rightSafeArea, config)
        leftSafeArea.requestLayout()
        rightSafeArea.requestLayout()
    }

    private fun adaptToolbar(config: AppUiResponder.AppUiConfig) {
        val scale = VideoEditorUIConfig.getViewScaleByWindowSize(config, FORM_TRIM_PAGE)
        val viewSize = ScreenUtils.pixelToDp(config.windowWidth.current)

        VideoEditorUIConfig.getViewSizeByScale(
            R.dimen.dp_186,
            activity,
            config,
            FORM_TRIM_PAGE
        ).let { toolbarHeight ->
            toolbarContainer.layoutParams.let { lp ->
                lp.width = MATCH_PARENT
                lp.height = toolbarHeight
            }
        }

        val toolbarContentSize = ScreenUtils.dpToPixel(
            VideoEditorUIConfig.getToolBarContentWidthForTrimSize(viewSize, scale)
        )
        toolbarContainer.findViewById<FrameLayout>(R.id.export_olive_thumb_layout)?.let {
            (it.layoutParams as FrameLayout.LayoutParams).let { lp ->
                lp.width = toolbarContentSize
                lp.gravity = Gravity.CENTER_HORIZONTAL
            }
            it.scaleX = scale
            it.scaleY = scale
        }

        toolbarContainer.requestLayout()
    }

    /**
     * 释放、反注册、销毁相关资源
     */
    private fun release() {
        activity.unregisterSystemBarChangeListener(systemBarChangeListener)
    }

    companion object {
        private const val TAG = "VideoEditorExportOliveUIScheme"
    }
}