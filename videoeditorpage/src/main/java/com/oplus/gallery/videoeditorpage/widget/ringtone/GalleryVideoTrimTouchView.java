/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File: - GalleryVideoTrimTouchView.java
 ** Description: 时码线，左右把手交互
 ** Version: 1.0
 ** Date:2021/05/21
 ** Author:<EMAIL>
 ** OPLUS Java File Skip Rule:MethodLength,MethodComplexity,DeclarationOrder
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ringtone;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.NinePatch;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;

import com.oplus.gallery.addon.os.VibratorUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.videoeditorpage.R;

public class GalleryVideoTrimTouchView extends View {

    public static final float DEFAULT_DETAIL_MODE_SHOW_PERCENT = 0.04f;

    protected static final int LEFT_BORDER_TOUCHED = 1;
    protected static final int RIGHT_BORDER_TOUCHED = 2;
    protected static final float ONE_HUNDRED_PERCENT = 1.0f;
    /*min 1000ms for accurate,add extra 10ms*/
    protected static final int TRIM_MIN_TIME = 1010;
    protected static final int PLAY_POSITION_TOUCHED = 3;

    private static final String TAG = "GalleryVideoTrimTouchView";
    private static final int FULL_PERCENT = 1;
    private static final int TRIM_ALPHA = 180;
    private static final float MIN_POSITION = 1.0f;
    private static final int MASK_DIMS = 4;

    private final Paint mBorderBGPaint = new Paint();
    private final Paint mTrimTimePaint = new Paint();
    private final Paint mTrimTimeTextPaint = new Paint();
    private final Paint mPlayPositionPaint = new Paint();

    /*gap between position line and border line*/
    protected int mViewPadding = 0;
    protected float mCurrentPosGap;
    protected float mTrimMinGap;
    protected float mTrimMinPercent;
    /*左边把手所在位置*/
    protected float mLeftBorderPos = 0f;
    /*右边把手所在位置*/
    protected float mRightBorderPos = 0f;
    /*当前播放位置*/
    protected float mCurrentPlayPos = -1f;
    /*当前播放位置的百分比*/
    protected float mCurrentPlayPercent = 0f;
    protected int mCurrentTouchIndex = 0;
    protected boolean mShowPlayPosition = true;
    protected boolean mShowMaxMinGapEffect = false;
    protected int mViewWidth = 0;
    protected int mTrimWindowWidth;
    /*mTrimWindowDefaultMinGap =  mTrimWindowRealMinGap + 1秒宽度*/
    protected float mTrimWindowRealMinGap;
    protected long mDuration = 0;

    /*左边把手初始化时位置的百分比*/
    private float mInitLeftPosPercent = -1f;
    /*右边把手初始化时位置的百分比*/
    private float mInitRightPosPercent = -1f;
    private float mTouchDownX;
    private int mAutoMoveGap;
    private boolean mIsTrimPressed = false;
    private boolean mShowTrimTime = false;
    private int mViewHeight = 0;
    private int mTrimTimeOffset = 0;
    private int mTrimTimeLineOffset = 0;
    private int mTrimTimeViewHeight = 0;
    private int mTrimTimeViewWidth = 0;
    private int mTrimTimeRectangleHeight = 0;
    private int mTrimTimeRectangleWidth = 0;
    private int mTrimTimeViewBottomPadding = 0;
    private String mTrimTimeText = TextUtil.EMPTY_STRING;
    private int mTrimTimeTextWidthPadding = 0;
    private int mTrimTimeTextHeightPadding = 0;
    private int mTouchViewMargin = 0;
    private int mTrimTimeRadius = 0;
    private RectF mTrimTimeRect;
    private RectF mTrimTimeRectangleRect;
    private int mTrimFrame = 0;
    private NinePatch mTrimNormal;
    private NinePatch mTrimViewMaxMinGap;
    private Bitmap mPosBitmap;
    private Rect mSrcRect;
    private TrimPositionChangeListener mTrimPositionChangeListener;
    private boolean mIsInDetailMode = false;
    private GestureDetector mGestureDetector;
    private float mDetailStartCurrentPercent;

    /*固定值 在dimens配置*/
    private float mTrimWindowDefaultMinGap;
    private int mPostBitmapHeightGap;

    private GestureDetector.OnGestureListener mOnGestureListener = new GestureDetector.OnGestureListener() {

        @Override
        public boolean onDown(MotionEvent e) {
            return false;
        }

        @Override
        public void onShowPress(MotionEvent e) {
            // do nothing
        }

        @Override
        public boolean onSingleTapUp(MotionEvent e) {
            return false;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            return false;
        }

        @Override
        public void onLongPress(MotionEvent e) {
            GLog.d(TAG, "onLongPress() mCurrentTouchIndex = " + mCurrentTouchIndex);
            if (mTrimPositionChangeListener != null) {
                if ((mCurrentTouchIndex == PLAY_POSITION_TOUCHED) && !mIsInDetailMode
                        && isInPlayPositionRange(e.getX())) {
                    mDetailStartCurrentPercent = mCurrentPlayPercent;
                    mTrimPositionChangeListener.onEnterDetailMode(mCurrentPlayPercent);
                    mIsInDetailMode = true;
                    postInvalidate();
                } else if ((mCurrentTouchIndex == LEFT_BORDER_TOUCHED) && !mIsInDetailMode) {
                    mDetailStartCurrentPercent = getLeftBorderPosPercent();
                    mCurrentPlayPercent = mDetailStartCurrentPercent;
                    mTrimPositionChangeListener.onEnterDetailMode(mDetailStartCurrentPercent);
                    mIsInDetailMode = true;
                    postInvalidate();
                } else if ((mCurrentTouchIndex == RIGHT_BORDER_TOUCHED) && !mIsInDetailMode) {
                    mDetailStartCurrentPercent = getRightBorderPosPercent();
                    mCurrentPlayPercent = mDetailStartCurrentPercent;
                    mTrimPositionChangeListener.onEnterDetailMode(mDetailStartCurrentPercent);
                    mIsInDetailMode = true;
                    postInvalidate();
                }
                mDetailStartCurrentPercent = mDetailStartCurrentPercent * (ONE_HUNDRED_PERCENT - DEFAULT_DETAIL_MODE_SHOW_PERCENT);
            }
        }

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            return false;
        }
    };

    public GalleryVideoTrimTouchView(Context context) {
        super(context);
        init();
    }

    public GalleryVideoTrimTouchView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public GalleryVideoTrimTouchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private float getWidthWithoutDefaultGap() {
        return mViewWidth - mTrimWindowDefaultMinGap;
    }

    /**
     * 获取剪辑区域最大宽度（不包括左右把手的宽度）
     */
    protected float getMaxTrimContentWidth() {
        return mViewWidth - mTrimWindowRealMinGap;
    }

    public void setTrimPositionChangeListener(TrimPositionChangeListener listener) {
        mTrimPositionChangeListener = listener;
    }

    private void init() {
        mGestureDetector = new GestureDetector(getContext(), mOnGestureListener);
        mAutoMoveGap = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_auto_move_gap);
        mTrimTimeViewHeight = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_view_height);
        mTrimTimeViewWidth = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_view_width);
        mTrimTimeOffset = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_offset);
        mTrimTimeLineOffset = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_line_offset);
        mTrimTimeRectangleWidth = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_rectangle_width);
        mTrimTimeRectangleHeight = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_rectangle_height);
        mTrimTimeViewBottomPadding = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_view_bottom_padding);
        mTouchViewMargin = mTrimTimeViewHeight + mTrimTimeViewBottomPadding;
        mTrimTimeRadius = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_radius);
        mTrimTimeTextWidthPadding = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_text_width_padding);
        mTrimTimeTextHeightPadding = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_text_height_padding);
        mPostBitmapHeightGap = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_time_rectangle_gap);

        // 进度条提示文字区域
        mTrimTimeRect = new RectF(0, 0, mTrimTimeViewWidth, mTrimTimeViewHeight);
        // 进度条提示文字与进度条的连接线区域
        mTrimTimeRectangleRect = new RectF(0, mTrimTimeViewHeight, mTrimTimeRectangleWidth,
            mTrimTimeViewHeight + mTrimTimeRectangleHeight);
        mTrimTimePaint.setColor(getResources().getColor(R.color.videoeditor_trim_time_background, null));
        mTrimTimePaint.setAntiAlias(true);
        mTrimTimeTextPaint.setColor(getResources().getColor(R.color.videoeditor_trim_time_hint_textcolor, null));
        int textSize = getResources().getDimensionPixelSize(R.dimen.videoeditor_video_editor_suitable_text_size);
        mTrimTimeTextPaint.setTextSize(textSize);
        mTrimTimeTextPaint.setAntiAlias(true);
        mTrimTimeTextPaint.setTextAlign(Paint.Align.LEFT);
        mTrimFrame = getResources().getDimensionPixelSize(R.dimen.dp_46);
        mBorderBGPaint.setColor(getContext().getColor(com.oplus.gallery.basebiz.R.color.videoeditor_video_editor_background_color_edit));
        mBorderBGPaint.setAntiAlias(true);
        mBorderBGPaint.setAlpha(TRIM_ALPHA);
        Bitmap normal = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_editor_trim_normal);
        Bitmap maxMinGap = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_editor_trim_press);
        mTrimNormal = new NinePatch(normal, normal.getNinePatchChunk(), null);
        mTrimViewMaxMinGap = new NinePatch(maxMinGap, maxMinGap.getNinePatchChunk(), null);
        // 进度条指示器
        mPosBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_trim_play_pos);
        mSrcRect = new Rect(0, 0, mPosBitmap.getWidth(), mPosBitmap.getHeight());
        mTrimWindowWidth = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_window_width);
        mCurrentPosGap = mTrimWindowWidth
            + getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_pos_with_window_offset);

        mTrimWindowDefaultMinGap = (mTrimWindowWidth
            + getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_pos_with_window_offset)) * 2
            + getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_pos_with_window_min_gap);

        mViewHeight = getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_thumbnail_height);
        mViewPadding = getContext().getResources().getDimensionPixelSize(R.dimen.videoeditor_trim_to_padding_of_border);
    }

    public int getTrimWindowWidth() {
        return mTrimWindowWidth + mViewPadding;
    }

    public void setInitPos(long duration, long leftTrim, long rightTrim) {
        if (duration == 0) {
            GLog.e(TAG, "setInitPos() duration == 0");
            return;
        }
        long left = leftTrim;
        long right = rightTrim;
        mDuration = duration;
        if (TRIM_MIN_TIME > duration) {
            mTrimMinPercent = ONE_HUNDRED_PERCENT;
            mCurrentPlayPercent = 0f;
            mInitLeftPosPercent = 0f;
            mInitRightPosPercent = 1f;
        } else {
            if (right < TRIM_MIN_TIME) {
                right = TRIM_MIN_TIME;
            }

            if (left > (right - TRIM_MIN_TIME)) {
                left = right - TRIM_MIN_TIME;
            }
            mTrimMinPercent = (float) TRIM_MIN_TIME / duration;
            mCurrentPlayPercent = (float) left / (float) duration;
            mInitLeftPosPercent = mCurrentPlayPercent;
            mInitRightPosPercent = (float) right / (float) duration;
        }
        updateData();
        postInvalidate();
    }

    private void updateData() {
        mViewWidth = getWidth() - (mViewPadding * 2);
        GLog.d(TAG, "updateData mViewWidth:" + mViewWidth);
        if ((mInitLeftPosPercent != -1) && (mInitRightPosPercent != -1)) {
            if (TRIM_MIN_TIME > mDuration) {
                mTrimWindowRealMinGap = 0;
            } else {
                mTrimWindowRealMinGap = mTrimWindowDefaultMinGap - getWidthWithoutDefaultGap() * TRIM_MIN_TIME / (float) (mDuration - TRIM_MIN_TIME);
                if (mTrimWindowRealMinGap < 0) {
                    mTrimWindowRealMinGap = 0;
                }
            }
            mLeftBorderPos = mInitLeftPosPercent * getMaxTrimContentWidth();
            mRightBorderPos = mInitRightPosPercent * getMaxTrimContentWidth() + mTrimWindowRealMinGap;
            if (mCurrentPlayPos < mLeftBorderPos + mCurrentPosGap) {
                mCurrentPlayPos = mLeftBorderPos + mCurrentPosGap;
                mCurrentPlayPercent = mLeftBorderPos / getMaxTrimContentWidth();
            }

            mTrimMinGap = (int) (mViewWidth * mTrimMinPercent);
            if (mTrimMinGap < mTrimWindowDefaultMinGap) {
                mTrimMinGap = mTrimWindowDefaultMinGap;
            } else if (mTrimMinGap > getMaxTrimContentWidth()) {
                mTrimMinGap = mViewWidth;
            }
            GLog.d(TAG, "initViewWidth() mTrimMinPercent:" + mTrimMinPercent + ", mTrimMinGap:" + mTrimMinGap);
        } else {
            mRightBorderPos = mViewWidth;
            mLeftBorderPos = 0f;
            if (mCurrentPlayPos == -1) {
                mCurrentPlayPos = mCurrentPosGap;
                mCurrentPlayPercent = mCurrentPlayPos / getMaxTrimContentWidth();
            }
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //initView();
        drawBorder(canvas);
        drawTrimWindow(canvas);
        if (mShowTrimTime) {
            drawTrimTimeView(canvas);
        }
        if (mShowPlayPosition && isDrawPlayPositionEnable()) {
            drawPlayPosition(canvas);
        }
    }

    /**
     * 进度提示
     * @param canvas
     */
    private void drawTrimTimeView(Canvas canvas) {
        canvas.drawRoundRect(mTrimTimeRect, mTrimTimeRadius, mTrimTimeRadius, mTrimTimePaint);
        canvas.drawRect(mTrimTimeRectangleRect, mTrimTimePaint);
        canvas.drawText(mTrimTimeText, mTrimTimeRect.left + mTrimTimeTextWidthPadding,
            mTrimTimeRect.top + mTrimTimeViewHeight - mTrimTimeTextHeightPadding, mTrimTimeTextPaint);
    }

    /**
     * 进度指示线
     * @param canvas
     */
    private void drawPlayPosition(Canvas canvas) {
        RectF destRect = new RectF(
                mCurrentPlayPos - mPosBitmap.getWidth() / 2 + mViewPadding + getPlayPositionGap(),
                mTouchViewMargin + mPostBitmapHeightGap,
                mCurrentPlayPos + mPosBitmap.getWidth() / 2 + mViewPadding + getPlayPositionGap(),
                mTouchViewMargin + mSrcRect.height() - mPostBitmapHeightGap);
        canvas.drawBitmap(mPosBitmap, mSrcRect, destRect, mPlayPositionPaint);
    }

    protected boolean isDrawPlayPositionEnable() {
        return true;
    }

    protected int getPlayPositionGap() {
        return 0;
    }

    private void drawBorder(Canvas canvas) {
        if (mLeftBorderPos >= 0) {
            RectF bg = new RectF(
                    getBorderOffset(true),
                mTouchViewMargin + (mPosBitmap.getHeight() - mViewHeight) / 2,
                mLeftBorderPos + mViewPadding + 3 * MASK_DIMS,
                mTouchViewMargin + (mPosBitmap.getHeight() - mViewHeight) / 2 + mViewHeight);
            canvas.drawRect(bg, mBorderBGPaint);
        }
        if (mRightBorderPos <= getRightBorderLimit()) {
            RectF bg = new RectF(
                mRightBorderPos + mViewPadding - 3 * MASK_DIMS,
                mTouchViewMargin + (mPosBitmap.getHeight() - mViewHeight) / 2,
                getRightBorderLimit() + getBorderOffset(false),
                mTouchViewMargin + (mPosBitmap.getHeight() - mViewHeight) / 2 + mViewHeight
            );
            canvas.drawRect(bg, mBorderBGPaint);
        }
    }

    protected int getBorderOffset(boolean isLeft) {
        return mViewPadding;
    }

    protected int getRightBorderLimit() {
        return mViewWidth;
    }

    /**
     * 绘制裁剪框
     * @param canvas
     */
    private void drawTrimWindow(Canvas canvas) {
        RectF rect = new RectF(
                mLeftBorderPos + mViewPadding,
                mTouchViewMargin + (mPosBitmap.getHeight() - mTrimFrame) / 2,
                mRightBorderPos + mViewPadding,
                mTouchViewMargin + (mPosBitmap.getHeight() - mTrimFrame) / 2 + mTrimFrame);
        /*
         * 1. 当左把手所在位置大于0
         * 2. 右把手位置有值且小于裁剪框宽度
         * 3. 符合最大最小差值效果响应态时
         */
        if (isDrawMinMaxGapEffect()) {
            mTrimViewMaxMinGap.draw(canvas, rect);
        } else {
            mTrimNormal.draw(canvas, rect);
        }
    }

    protected boolean isDrawMinMaxGapEffect() {
        return (mLeftBorderPos > 0) || ((mRightBorderPos != 0) && (mRightBorderPos < mViewWidth)) || mShowMaxMinGapEffect;
    }

    protected boolean isDrawTrimViewBackground() {
        return true;
    }

    public void updateCurrentPosition(float postpercent) {
        if (!mIsInDetailMode) {
            mCurrentPlayPercent = postpercent;
            float offset = (postpercent * getMaxTrimContentWidth() - mLeftBorderPos)
                * (mRightBorderPos - mLeftBorderPos - mCurrentPosGap * 2)
                / (mRightBorderPos - mTrimWindowRealMinGap - mLeftBorderPos);
            if (Float.compare(offset, 0f) < 0) {
                offset = 0f;
                mCurrentPlayPercent = mLeftBorderPos / getMaxTrimContentWidth();
            }
            mCurrentPlayPos = offset + mLeftBorderPos + mCurrentPosGap;
            if (mShowPlayPosition) {
                postInvalidate();
            }
        }
    }

    public void setShowPlayPosition(boolean show) {
        mShowPlayPosition = show;
        postInvalidate();
    }

    public void updateTrimTime(String text) {
        if ((text != null) && (mTrimTimeText != null) && (text.length() != mTrimTimeText.length())) {
            updateTrimTimeViewWidth(text);
        }
        this.mTrimTimeText = text;
    }

    private void updateTrimTimeViewWidth(String trimTimeText) {
        float lastPosition = mTrimTimeRect.left + mTrimTimeOffset + mTrimTimeViewWidth / 2;
        float textWidth = mTrimTimeTextPaint.measureText(trimTimeText);
        mTrimTimeViewWidth = (int) (textWidth + mTrimTimeTextWidthPadding * 2);
        updateTrimTimeRect(lastPosition);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isMonitorGesture()) {
            boolean ans = mGestureDetector.onTouchEvent(event);
            if (ans) {
                return true;
            }
        }
        float touchX = event.getX() - mViewPadding;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (event.getY() < mTrimTimeViewHeight + mTrimTimeViewBottomPadding) {
                    //超出热区
                    return true;
                }
                mTouchDownX = touchX;
                evalTouchPosition(mTouchDownX);
                if (checkCurrentTouchIndex()) {
                    if (mCurrentTouchIndex != PLAY_POSITION_TOUCHED) {
                        mIsTrimPressed = true;
                    }
                    invalidate();
                    if (!isHandleTouchEvent()) {
                        return false;
                    }
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (checkCurrentTouchIndex() //手指按下的位置是否在有效范围内
                        && (Math.abs(touchX - mTouchDownX) > 1) //防止在手指按下的位置重复触发
                        && (event.getPointerId(event.getActionIndex()) == 0) //只触发pointerId为0的手指的TOUCH_MOVE事件
                ) {
                    moveTouchEvent(event);
                }
                break;

            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                mShowTrimTime = false;
                changeShowMaxMinGapEffect(false);
                if (mIsInDetailMode) {
                    mIsInDetailMode = false;
                    mTrimPositionChangeListener.onExitDetailMode(mCurrentPlayPercent);
                    changeCurrentPlayPos();
                }
                mIsTrimPressed = false;
                if (checkCurrentTouchIndex()) {
                    notifyTrim();
                    mShowPlayPosition = true;
                    invalidate();
                    mCurrentTouchIndex = 0;
                }
                break;

            default:
                break;
        }
        return super.onTouchEvent(event);
    }

    protected boolean isMonitorGesture() {
        return true;
    }

    protected boolean checkCurrentTouchIndex() {
        return mCurrentTouchIndex > 0;
    }

    protected boolean isHandleTouchEvent() {
        return true;
    }

    public float getBorderPosPercent(boolean isLeft) {
        float leftPercent = getLeftBorderPosPercent();
        float rightPercent = getRightBorderPosPercent();
        if (mIsInDetailMode) {
            if (mCurrentTouchIndex == LEFT_BORDER_TOUCHED) {
                leftPercent = mCurrentPlayPercent;
            } else if (mCurrentTouchIndex == RIGHT_BORDER_TOUCHED) {
                rightPercent = mCurrentPlayPercent;
            }
        }
        return isLeft ? leftPercent : rightPercent;
    }

    public float getLeftBorderPosPercent() {
        if (mViewWidth != 0) {
            return mLeftBorderPos / getMaxTrimContentWidth();
        }
        return -1;
    }

    protected float getRightBorderPosPercent() {
        if (mViewWidth != 0) {
            return (mRightBorderPos - mTrimWindowRealMinGap) / getMaxTrimContentWidth();
        }
        return -1;
    }

    private void notifyTrim() {
        switch (mCurrentTouchIndex) {
            case LEFT_BORDER_TOUCHED:
                mTrimPositionChangeListener.onTrimLeft(getLeftBorderPosPercent());
                break;
            case RIGHT_BORDER_TOUCHED:
                mTrimPositionChangeListener.onTrimRight(getRightBorderPosPercent());
                break;
            case PLAY_POSITION_TOUCHED:
                mTrimPositionChangeListener.onSeekChange();
                break;
            default:
                break;
        }
    }

    private void moveTrimTime(float x) {
        mShowTrimTime = true;
        updateTrimTimeRect(x);
    }

    private void updateTrimTimeRect(float x) {
        mTrimTimeRect.left = x - mTrimTimeViewWidth / 2 - mTrimTimeOffset;
        mTrimTimeRect.right = mTrimTimeRect.left + mTrimTimeViewWidth;
        mTrimTimeRectangleRect.left = mTrimTimeRect.left + mTrimTimeRect.width() / 2 + mTrimTimeLineOffset;
        mTrimTimeRectangleRect.right = mTrimTimeRectangleRect.left + mTrimTimeRectangleWidth;
    }

    private void changeCurrentPlayPos() {
        switch (mCurrentTouchIndex) {
            case LEFT_BORDER_TOUCHED:
                mLeftBorderPos = mCurrentPlayPercent * getMaxTrimContentWidth();
                if (mLeftBorderPos > (mRightBorderPos - mTrimMinGap)) {
                    mLeftBorderPos = Math.max(mRightBorderPos - mTrimMinGap, 0f);
                    mCurrentPlayPercent = getLeftBorderPosPercent();
                }
                mCurrentPlayPos = Math.min(mLeftBorderPos + mCurrentPosGap, mViewWidth);
                break;
            case PLAY_POSITION_TOUCHED:
                mCurrentPlayPos = mCurrentPlayPercent * getMaxTrimContentWidth();
                if (mCurrentPlayPos < (mLeftBorderPos + mCurrentPosGap)) {
                    mCurrentPlayPos = Math.min(mLeftBorderPos + mCurrentPosGap, mViewWidth);
                } else if (mCurrentPlayPos > (mRightBorderPos - mCurrentPosGap)) {
                    mCurrentPlayPos = Math.max(mRightBorderPos - mCurrentPosGap, 0);
                }
                break;
            case RIGHT_BORDER_TOUCHED:
                mRightBorderPos = mCurrentPlayPercent * getMaxTrimContentWidth() + mTrimWindowRealMinGap;
                if (mRightBorderPos < (mLeftBorderPos + mTrimMinGap)) {
                    mRightBorderPos = Math.min(mLeftBorderPos + mTrimMinGap, mViewWidth);
                    mCurrentPlayPercent = getRightBorderPosPercent();
                }
                mCurrentPlayPos = Math.max(mRightBorderPos - mCurrentPosGap, 0);
                break;
            default:
                break;
        }
    }

    private void moveTouchEvent(MotionEvent event) {
        boolean isShowMaxMinGapEffect = false;
        switch (mCurrentTouchIndex) {
            case LEFT_BORDER_TOUCHED:
                isShowMaxMinGapEffect = touchLeftBoarder(event);
                break;
            case PLAY_POSITION_TOUCHED:
                touchPlayPosition(event);
                break;
            case RIGHT_BORDER_TOUCHED:
                isShowMaxMinGapEffect = touchRightBorder(event);
                break;
            default:
                break;
        }
        changeShowMaxMinGapEffect(isShowMaxMinGapEffect);
        invalidate();
    }

    private boolean touchLeftBoarder(MotionEvent event) {
        float touchX = event.getX() - mViewPadding;
        float trimLeftPercent = 0;
        float trimRightPercent = 0;
        boolean isShowTrimTime = true;
        boolean isShowMaxMinGapEffect = false;

        isShowTrimTime = Float.compare(touchX, mRightBorderPos - mCurrentPosGap) <= 0;
        /*
        裁剪区域在最左边时,最短1s区域变速2x后,这个mRightBorderPos - mTrimMinGap会为负
        左右把手差小于最小宽度 或者右把手已经在最左边了
        */
        if ((Float.compare(touchX, mRightBorderPos - 2 * getPlayPositionGap() - mTrimMinGap) >= 0)
                || (Float.compare(mRightBorderPos - 2 * getPlayPositionGap() - mTrimMinGap, 0f) <= 0)) {
            touchX = mRightBorderPos - 2 * getPlayPositionGap() - mTrimMinGap;
            isShowMaxMinGapEffect = true;
        }

        if (Float.compare(touchX, mAutoMoveGap) <= 0) {
            touchX = 0;
            isShowTrimTime = false;
        }

        if (isShowTrimTime) {
            moveTrimTime(touchX + mViewPadding + mCurrentPosGap / 2);
        }

        if ((Float.compare(mRightBorderPos, getRightBorderLimit()) == 0) && (Float.compare(touchX, 0f) <= 0)) {
            isShowMaxMinGapEffect = true;
        }
        mLeftBorderPos = Math.round(touchX);
        if (mIsInDetailMode) {
            float tempPercent = touchX / (float) mViewWidth;
            mCurrentPlayPercent = mDetailStartCurrentPercent + tempPercent * DEFAULT_DETAIL_MODE_SHOW_PERCENT;
            if (Float.compare(mCurrentPlayPercent, ONE_HUNDRED_PERCENT) > 0) {
                mCurrentPlayPercent = ONE_HUNDRED_PERCENT;
            }
            trimLeftPercent = mCurrentPlayPercent;
            trimRightPercent = getRightBorderPosPercent();
            if (Float.compare((trimRightPercent - trimLeftPercent), mTrimMinPercent) < 0) {
                trimLeftPercent = Math.max(trimRightPercent - mTrimMinPercent, 0f);
                touchX = trimLeftPercent * mViewWidth;
                mLeftBorderPos = touchX;
            }
        } else {
            mCurrentPlayPercent = getLeftBorderPosPercent();
            mCurrentPlayPos = mLeftBorderPos + getCurrentPlayPosOffsetWhenTrim(true);
            trimLeftPercent = getLeftBorderPosPercent();
            trimRightPercent = getRightBorderPosPercent();
        }
        mTrimPositionChangeListener.onSeek(mCurrentPlayPercent, trimLeftPercent, trimRightPercent);
        return isShowMaxMinGapEffect;
    }

    private void touchPlayPosition(MotionEvent event) {
        float touchX = event.getX() - mViewPadding;
        int touchPos = 0;

        if ((Float.compare(touchX, mLeftBorderPos + mCurrentPosGap) >= 0)
                && (Float.compare(touchX, mRightBorderPos - mCurrentPosGap) <= 0)) {
            moveTrimTime(event.getX());
        }
        if (Float.compare(touchX, mLeftBorderPos + mCurrentPosGap) < 0) {
            touchX = mLeftBorderPos + mCurrentPosGap;
            touchPos = Math.round(touchX);
            // 滑杆滑到剪辑框最左边时设置滑杆上方线段位置为滑杆坐标位置
            if (mCurrentPlayPos != touchPos) {
                float x = touchPos - mPosBitmap.getWidth() / 2 + mViewPadding + mAutoMoveGap;
                moveTrimTime(x);
            }

        } else if (Float.compare(touchX, mRightBorderPos - mCurrentPosGap) > 0) {
            touchX = mRightBorderPos - mCurrentPosGap;
            touchPos = Math.round(touchX);
            // 滑杆滑到剪辑框最右边时设置滑杆上方线段位置为滑杆坐标位置
            if (mCurrentPlayPos != touchPos) {
                float x = touchPos - mPosBitmap.getWidth() / 2 + mViewPadding + mAutoMoveGap;
                moveTrimTime(x);
            }
        }

        mCurrentPlayPos = Math.round(touchX);
        if (mIsInDetailMode) {
            float tempPercent = touchX / (float) mViewWidth;
            mCurrentPlayPercent = mDetailStartCurrentPercent + tempPercent * DEFAULT_DETAIL_MODE_SHOW_PERCENT;
            mTrimPositionChangeListener.onSeek(mCurrentPlayPercent, -1, -1);
        } else {

            float offset = (touchX - mLeftBorderPos - mCurrentPosGap)
                    * (mRightBorderPos - mTrimWindowRealMinGap - mLeftBorderPos)
                    / (mRightBorderPos - mLeftBorderPos - mCurrentPosGap * 2);
            if (Float.compare(offset, 0f) < 0) {
                offset = 0f;
            }
            mCurrentPlayPercent = Math.round(offset + mLeftBorderPos) / getMaxTrimContentWidth();
            mTrimPositionChangeListener.onSeek(mCurrentPlayPercent, -1, -1);
        }
    }

    private boolean touchRightBorder(MotionEvent event) {
        float touchX = event.getX() - mViewPadding;
        float trimLeftPercent = 0;
        float trimRightPercent = 0;
        boolean isShowTrimTime = true;
        boolean isShowMaxMinGapEffect = false;
        isShowTrimTime = touchX >= (mLeftBorderPos + mCurrentPosGap);
        // 左右把手差小于最小宽度 或者左把手已经在最右边了
        if ((Float.compare(touchX, mLeftBorderPos + 2 * getPlayPositionGap() + mTrimMinGap) < 0)
                || (Float.compare(mLeftBorderPos + 2 * getPlayPositionGap() + mTrimMinGap, getRightBorderLimit()) >= 0)) {
            touchX = mLeftBorderPos + 2 * getPlayPositionGap() + mTrimMinGap;
            isShowMaxMinGapEffect = true;
        }
        if (Float.compare(touchX, getRightBorderLimit()) > 0) {
            touchX = getRightBorderLimit();
            isShowTrimTime = false;
        }
        if (isShowTrimTime) {
            moveTrimTime(touchX + mViewPadding - mCurrentPosGap / 2);
        }

        if ((Float.compare(mLeftBorderPos, 0f) == 0) && (Float.compare(touchX, getRightBorderLimit()) >= 0)) {
            isShowMaxMinGapEffect = true;
        }
        mRightBorderPos = Math.round(touchX);
        if (mIsInDetailMode) {
            float tempPercent = touchX / (float) mViewWidth;
            mCurrentPlayPercent = mDetailStartCurrentPercent + tempPercent * DEFAULT_DETAIL_MODE_SHOW_PERCENT;
            if (Float.compare(mCurrentPlayPercent, ONE_HUNDRED_PERCENT) > 0) {
                mCurrentPlayPercent = ONE_HUNDRED_PERCENT;
            }
            trimLeftPercent = getLeftBorderPosPercent();
            trimRightPercent = mCurrentPlayPercent;
            if (Float.compare((trimRightPercent - trimLeftPercent), mTrimMinPercent) < 0) {
                trimRightPercent = Math.min(trimLeftPercent + mTrimMinPercent, 1f);
                touchX = trimRightPercent * mViewWidth;
            }
        } else {
            mCurrentPlayPercent = getRightBorderPosPercent();
            mCurrentPlayPos = mRightBorderPos + getCurrentPlayPosOffsetWhenTrim(false);
            trimLeftPercent = getLeftBorderPosPercent();
            trimRightPercent = getRightBorderPosPercent();
        }
        mTrimPositionChangeListener.onSeek(mCurrentPlayPercent, trimLeftPercent, trimRightPercent);
        return isShowMaxMinGapEffect;
    }

    protected float getCurrentPlayPosOffsetWhenTrim(boolean isTrimLeft) {
        float offset = 0f;
        if (isTrimLeft) {
            offset = mCurrentPosGap;
        } else {
            offset = -mCurrentPosGap;
        }
        return offset;
    }

    protected void evalTouchPosition(float mTouchDownX) {
        boolean isTouchPos = false;
        if (mShowPlayPosition && (mTouchDownX >= mLeftBorderPos)
            && (mTouchDownX <= mRightBorderPos)) {
            mCurrentTouchIndex = PLAY_POSITION_TOUCHED;
            isTouchPos = true;
        }
        if ((mTouchDownX >= (mLeftBorderPos - mCurrentPosGap * 2)) && (mTouchDownX <= (mLeftBorderPos + mCurrentPosGap * 2))) {
            if (isTouchPos) {
                if (mTouchDownX <= (mLeftBorderPos + mCurrentPosGap)) {
                    mCurrentTouchIndex = LEFT_BORDER_TOUCHED;
                    mShowPlayPosition = false;
                    return;
                } else {
                    mCurrentTouchIndex = PLAY_POSITION_TOUCHED;
                }
            } else {
                mCurrentTouchIndex = LEFT_BORDER_TOUCHED;
                mShowPlayPosition = false;
            }
        }
        if ((mTouchDownX >= (mRightBorderPos - mCurrentPosGap * 2)) && (mTouchDownX <= (mRightBorderPos + mCurrentPosGap * 2))) {
            if (isTouchPos) {
                if (mTouchDownX < mRightBorderPos - mCurrentPosGap) {
                    mCurrentTouchIndex = PLAY_POSITION_TOUCHED;
                } else {
                    mCurrentTouchIndex = RIGHT_BORDER_TOUCHED;
                    mShowPlayPosition = false;
                    return;
                }
            } else {
                mCurrentTouchIndex = RIGHT_BORDER_TOUCHED;
                mShowPlayPosition = false;
            }
        }
    }

    private void changeShowMaxMinGapEffect(boolean isShow) {
        if (!mShowMaxMinGapEffect && isShow) {
            VibratorUtils.vibrate(getContext(), VibratorUtils.EffectType.VIBRATE_TYPE_WEAK);
        }
        mShowMaxMinGapEffect = isShow;
    }

    private boolean isInPlayPositionRange(float touchX) {
        return (touchX >= (mCurrentPlayPos - mCurrentPosGap * 2))
            && (touchX <= (mCurrentPlayPos + mCurrentPosGap * 2));
    }

    public float getCurrentPlayPos() {
        return mCurrentPlayPos;
    }

    public interface TrimPositionChangeListener {
        void onSeek(float pos, float trimleft, float trimright);

        void onSeekChange();

        void onTrimLeft(float pos);

        void onTrimRight(float pos);

        void onEnterDetailMode(float pos);

        void onExitDetailMode(float pos);
    }
}
