/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IpuBeautyExtendData
 ** Description: ipu美颜扩展数据
 **
 ** Version: 1.0
 ** Date: 2024/10/24
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2024/10/24  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cvimageprocess.detect.data

import com.oplus.gallery.foundation.util.text.TextUtil

/**
 * ipu美颜扩展数据
 */
data class IpuBeautyExtendData(
    /**
     * 总版本号
     */
    var version: Float = 0f,

    /**
     * fi算法版本
     */
    var fiLibVersion: Int = 0,

    /**
     * fb算法版本
     */
    var fbLibVersion: Int = 0,

    /**
     * 相机拍照模式
     */
    var capMode: Int = 0,

    /**
     * 拍照对应的摄像头id，0表示后置，1表示前置
     */
    var cameraId: Int = 0,

    /**
     * 拍照对应的摄像头logical id
     */
    var logicCameraId: Int = 0,

    /**
     * 拍照图片的摄像头感光度
     */
    var iso: Int = 0,

    /**
     * 拍照时的色温
     */
    var colorTemperature: Int = 0,

    /**
     *  美颜模型类型
     */
    var beautyType: Int = 0,

    /**
     * ae人脸相关参数
     */
    var faceLuma: Int = 0,

    /**
     * 拍照时的焦段x100
     */
    var zoomRatio: Float = 0f,

    /**
     * ae人脸相关参数
     */
    var faceLumaRatio: Float = 0f,

    /**
     * 人脸方向
     */
    var orientation: Int = 0,

    /**
     * 美颜策略
     */
    var faceBeautyStrategy: Int = 0,

    /**
     * 拍照用到的sensor
     */
    var sensorName: String = TextUtil.EMPTY_STRING
) : FaceDetectInputData() {
    override fun toString(): String {
        return "$TAG{version:$version, fiLibVersion:$fiLibVersion, fbLibVersion:$fbLibVersion, capMode:$capMode," +
            " cameraId:$cameraId, logicCameraId:$logicCameraId, iso:$iso, colorTemperature:$colorTemperature," +
            " beautyType:$beautyType, faceLuma:$faceLuma, zoomRatio:$zoomRatio, faceLumaRatio:$faceLumaRatio," +
            " orientation:$orientation, faceBeautyStrategy:$faceBeautyStrategy, sensorName:$sensorName }"
    }

    companion object {
        private const val TAG = "IpuBeautyExtendData"
    }
}