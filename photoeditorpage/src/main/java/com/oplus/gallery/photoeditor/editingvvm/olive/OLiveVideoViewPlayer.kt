/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OLiveVideoViewPlayer.kt
 ** Description: OLiveView播放器
 ** Version: 1.0
 ** Date: 2024/1/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2024/1/31      1.0        created
 *********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive

import android.content.Context
import android.graphics.ColorSpace
import android.net.Uri
import android.view.Surface
import android.view.SurfaceHolder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.olivesdk.player.OLiveMediaPlayer
import com.oplus.gallery.olivesdk.player.OLivePlayerListener
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.codec.player.AVPlayer.VideoPlayType
import com.oplus.gallery.standard_lib.codec.player.AVPlayer.VideoSourceDescriptor
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.io.FileDescriptor

/**
 * 使用 AVPlayer 实现的播放器
 */
class OLiveVideoViewPlayer(
    private val context: Context
) : OLiveMediaPlayer {
    private val avPlayer = AVPlayer.create(context)
    private val playerListenerList: MutableSet<OLivePlayerListener> = mutableSetOf()

    /**
     * player是否激活了快速预览模式
     */
    private var isEnablePreview = false

    init {
        avPlayer.addOnEventListener(object : AVController.OnEventListener {
            override fun onError(avController: AVController, what: Int, extra: Int, details: String) {
                GLog.e(TAG, "[onError] what: $what, extra: $extra, details: $details")
                playerListenerList.forEach { listener -> listener.onError(what) }
            }

            override fun onInfo(avController: AVController, what: Int, extra: Int, details: String) {
                playerListenerList.forEach { listener ->
                    (listener as? OLiveEditorPlayerListener)?.onInfo(avController, what, extra, details)
                }
            }

            override fun onPlaybackStateChanged(avController: AVController, state: AVController.PlaybackState) {
                GLog.d(TAG, "[onPlaybackStateChanged] state: $state")
                when (state) {
                    AVController.PlaybackState.PREPARED ->
                        playerListenerList.forEach { listener -> listener.onPrepared() }
                    AVController.PlaybackState.COMPLETED ->
                        playerListenerList.forEach { listener -> listener.onComplete() }
                    else -> {}
                }
            }
        })
    }

    /**
     * 设定livephoto数据源
     *
     * @param fd 文件描述符
     * @param offset 资源需偏移量
     * @param length 资源文件长度
     */
    override fun setDataSource(fd: FileDescriptor, offset: Long, length: Long) = Unit

    /**
     * 设定livephoto数据源
     * 注意：调用AVPlayer的setDataSource，是会运行在子线程的
     *
     * @param uri 视频的Uri
     * @param path 视频的路径
     * @param offset 资源需偏移量
     * @param length 资源文件长度
     */
    fun setDataSource(uri: Uri, path: String, offset: Long, length: Long) {
        val targetColorSpace = ContextGetter.context.getAppAbility<IColorManagementAbility>()?.use {
            if (it.isDisplaySupportWCG.not() && (it.isWideColorGamutEnabled?.not() == true)) {
                // 色彩管理1.0 生动模式下，olive视频需要强制关闭P3色域以保证与图片显示一致
                ColorSpace.get(ColorSpace.Named.SRGB)
            } else {
                null
            }
        }

        avPlayer.setDataSource(
            AVPlayer.VideoSource(
                context = context,
                uri = uri,
                path = path,
                videoSourceDescriptor = VideoSourceDescriptor.MIX(VideoPlayType.MAIN, offset, length, targetColorSpace = targetColorSpace, null)
            )
        )
    }

    override fun setDisplay(surfaceHolder: SurfaceHolder?) {
        if (surfaceHolder == null) {
            GLog.i(TAG, "[setDisplay] unbindVideoSink")
            avPlayer.unbindVideoSink()
        } else {
            GLog.i(TAG, "[setDisplay] bindVideoSink")
            avPlayer.bindVideoSink(surfaceHolder)
        }
    }

    override fun setSurface(surface: Surface?) {
        if (surface == null) {
            GLog.i(TAG, "[setSurface] unbindVideoSink")
            avPlayer.unbindVideoSink()
        } else {
            GLog.i(TAG, "[setSurface] bindVideoSink")
            avPlayer.bindVideoSink(surface)
        }
    }

    override fun release() {
        avPlayer.release()
        playerListenerList.clear()
    }

    override fun prepare() = Unit

    override fun prepareAsync() {
        // AVPlayer中只有prepare接口，TBLPlayerWrapper实现此接口，调用的就是 prepareAsync
        avPlayer.prepare()
    }

    override fun play() {
        seekTo(0)
        avPlayer.startPlayer()
    }

    override fun playTo(position: Long) {
        seekTo(position)
        avPlayer.startPlayer()
    }

    override fun getVideoWidth(): Int {
        return avPlayer.getVideoWidth()
    }

    override fun getVideoHeight(): Int {
        return avPlayer.getVideoHeight()
    }

    override fun getDuration(): Long {
        return avPlayer.getDuration()
    }

    override fun isPlaying(): Boolean {
        return avPlayer.isPlayerPlaying()
    }

    /**
     * 视频区间播放
     * @param range 视频播放区间
     */
    fun play(range: LongRange) {
        avPlayer.play(range, true, true)
    }

    /**
     * 激活快速预览模式
     */
    fun enablePreviewSeekType() {
        isEnablePreview = true
        avPlayer.seekTo(getCurrentTimeMs(), AVController.SeekType.ENABLE_PREVIEW)
    }

    /**
     * 设置当前视频进度
     *
     * @param position 对应时间戳
     */
    override fun seekTo(position: Long) {
        val seekType = if (isEnablePreview) AVController.SeekType.ENABLE_PREVIEW else AVController.SeekType.NORMAL
        avPlayer.seekTo(position, seekType)
    }

    /**
     * 禁用快速预览模式
     */
    fun disablePreviewSeekType() {
        isEnablePreview = false
        avPlayer.seekTo(getCurrentTimeMs(), AVController.SeekType.DISABLE_PREVIEW)
    }

    override fun setListener(oLivePlayerListener: OLivePlayerListener) {
        if (playerListenerList.contains(oLivePlayerListener)) {
            GLog.d(TAG, "[setListener] already add this listener")
            return
        }
        playerListenerList.add(oLivePlayerListener)
    }

    override fun getCurrentTimeMs(): Long {
        return avPlayer.getCurrentPosition()
    }

    override fun stop() {
        avPlayer.stop()
    }

    override fun reset() {
        avPlayer.reset()
    }

    override fun resetPosition() {
        avPlayer.seekTo(0)
    }

    override fun isPrepared(): Boolean {
        return avPlayer.playingInfo.loadingState == AVController.LoadingState.LOADED
    }

    fun pause() {
        avPlayer.pause()
    }

    /**
     * 设置是否静音
     *
     * @param isMute true静音，false不静音
     */
    fun setMute(isMute: Boolean) {
        avPlayer.setMute(isMute)
    }

    /**
     * 获取播放器的信息，在播放器prepared之后会更新此信息
     */
    fun getPlayingInfo(): AVController.PlayingInfo {
        return avPlayer.playingInfo
    }

    /**
     * olive编辑页，播放器状态监听
     */
    interface OLiveEditorPlayerListener : OLivePlayerListener {
        fun onInfo(avController: AVController, what: Int, extra: Int, details: String)
    }

    companion object {
        private const val TAG = "OLiveVideoViewPlayer"
    }
}