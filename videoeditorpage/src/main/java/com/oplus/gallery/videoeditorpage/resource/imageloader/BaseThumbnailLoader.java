/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: BaseThumbnailLoader
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.util.LruCache;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.thread.Future;
import com.oplus.gallery.standard_lib.thread.FutureListener;
import com.oplus.gallery.standard_lib.thread.ThreadPool;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class BaseThumbnailLoader<Data> implements IThumbnailLoader<Data> {
    private static final String TAG = "BaseThumbnailLoader";

    public static final int MSG_UPDATE_THUMBNAIL = 1;
    public static final int MSG_CANCEL = 2;
    public static final int EXECUTOR_OREDER_SEQUENCE = 0;
    public static final int EXECUTOR_OREDER_REVER = 1;

    public static final int ONE_KB = 1024;

    protected ThreadPool mThreadPool = null;
    private static final int TASK_LIMIT = 12;
    private static final int LIMIT_MEMORY_BLOCK = 6;
    private static final ConcurrentHashMap<String, ILoaderCallback> LOADER_CALLBACK_CACHE = new ConcurrentHashMap<>();
    private static ThumbNailLruCache sThumbNailLruCache;
    private final LinkedList<BaseThumbnailTask> mJobs = new LinkedList<>();
    private final ConcurrentHashMap<ImageView, BaseThumbnailTask> mTaskMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Data, BaseThumbnailTask> mSourceMap = new ConcurrentHashMap<>();
    private final Object mLock = new Object();

    private boolean mPause = false;
    private int mLimit = TASK_LIMIT;
    private int mTaskLimit = TASK_LIMIT;
    private boolean mNoCache = false;
    private int mExecutorOrder = EXECUTOR_OREDER_SEQUENCE;
    private ImageHandler mHandler;

    public void release() {
        clearTaskMap(mTaskMap);
        clearTaskMap(mSourceMap);
        if (mThreadPool != null) {
            mThreadPool = null;
        }
    }

    private <T> void clearTaskMap(ConcurrentHashMap<T, BaseThumbnailTask> map) {
        if (!map.isEmpty()) {
            for (Map.Entry<T, BaseThumbnailTask> entry : map.entrySet()) {
                BaseThumbnailTask thumbnailTask = entry.getValue();
                if (thumbnailTask.getRequestData() != null) {
                    thumbnailTask.getRequestData().setThumbnailListener(null);
                }
                thumbnailTask.cancelLoad();
            }
            map.clear();
        }
    }

    static {
        int maxMemorySize = (int) (Runtime.getRuntime().maxMemory() / ONE_KB / LIMIT_MEMORY_BLOCK);
        sThumbNailLruCache = new ThumbNailLruCache(maxMemorySize);
    }

    public void setExecutorOrder(int executorOrder) {
        mExecutorOrder = executorOrder;
    }

    public BaseThumbnailLoader() {
        mThreadPool = ThreadPool.getInstance();
        mHandler = new ImageHandler(this);
    }

    @Override
    public final void startLoader(final Data item, ThumbnailListener<Data> thumbnailListener) {
        if ((null == item) || (null == thumbnailListener)) {
            GLog.e(TAG, "startLoader error, item: " + item);
            return;
        }

        start(item, thumbnailListener, null);
    }

    @Override
    public void startLoaderWidthPlace(Data item, ImageView target, Drawable palaceHolder) {

    }

    @Override
    public final void startLoader(final Data item, ThumbnailListener<Data> thumbnailListener, IBitmapTransformOption bitmapHandle) {
        if ((null == item) || (null == thumbnailListener)) {
            GLog.e(TAG, "startLoader error, item: " + item);
            return;
        }

        start(item, thumbnailListener, bitmapHandle);
    }

    @Override
    public final void startLoader(final Data item, final ImageView target) {
        startLoader(item, target, null);
    }

    @Override
    public synchronized void removeTask(Data item) {
        if (item == null) {
            return;
        }
        GLog.d(TAG, "removeThumbNailLoadTask, item: " + item);

        BaseThumbnailTask task = mSourceMap.remove(item);

        boolean isRemoveFormJobs = mJobs.remove(task);
        if (null != task) {
            task.cancelLoad();
        }

        GLog.d(TAG, "removeThumbNailLoadTask,isRemoveFormJobs: " + isRemoveFormJobs);
    }

    @Override
    public void removeTask(View target) {
        GLog.d(TAG, "removeTask,target: " + target);
        BaseThumbnailTask thumbnailTask = mTaskMap.get(target);

        boolean isRemoveFormJobs = mJobs.remove(thumbnailTask);
        if (null != thumbnailTask) {
            thumbnailTask.cancelLoad();
        }

        GLog.d(TAG, "removeThumbNailLoadTask,isRemoveFormJobs: " + isRemoveFormJobs);
    }

    @Override
    public final void startLoader(final Data item, final ImageView target, IBitmapTransformOption bitmapHandle) {
        if ((null == item) || (null == target)) {
            GLog.e(TAG, "startLoader error, item: " + item);
            return;
        }
        target.setTag(item);
        start(item, new DefaultThumbnailListener(target), bitmapHandle);
    }

    private synchronized void start(final Data item, @NonNull final ThumbnailListener thumbnailListener,
                                    IBitmapTransformOption bitmapHandle) {
        ILoaderCallback<Data> loaderCallback = buildThumbnailCallback();

        final Bitmap bitmap = getBitmapFromMemoryCache(ThumbnailRequest.formRequestKey(loaderCallback.getDataUniqueTag(item),
                bitmapHandle));
        if (!mNoCache && (null != bitmap)) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    thumbnailListener.updateThumbnail(new ThumbnailRespond<>(item, bitmap));
                }
            });
        } else {
            mNoCache = false;
            ThumbnailRequest<Data> requestData = new ThumbnailRequest<>();
            requestData.setSource(item).setBitmapHandle(bitmapHandle).setThumbnailListener(thumbnailListener)
                    .setTargetTag(loaderCallback.getDataUniqueTag(item));

            BaseThumbnailTask thumbnailTask = generateThumbnailTask(requestData, loaderCallback);

            if (!mJobs.contains(thumbnailTask)) {
                if (mExecutorOrder == EXECUTOR_OREDER_SEQUENCE) {
                    mJobs.addLast(thumbnailTask);
                } else {
                    mJobs.addFirst(thumbnailTask);
                }

                mSourceMap.put(item, thumbnailTask);

            } else {
                thumbnailTask.resetLoadedState();
            }

            if (thumbnailListener instanceof DefaultThumbnailListener) {
                ImageView imageView = ((DefaultThumbnailListener) thumbnailListener).getTarget();
                removeSameTargetTask(imageView);
                mTaskMap.put(imageView, thumbnailTask);
            }

            submitLoaderIfAllowed();
        }
    }

    private synchronized void removeSameTargetTask(ImageView imageView) {
        BaseThumbnailTask task = mTaskMap.get(imageView);
        boolean isRemove = mJobs.remove(task);

        if (null != task) {
            task.cancelLoad();
        }
    }

    public synchronized void setNoCache(boolean noCache) {
        mNoCache = noCache;
    }

    private BaseThumbnailTask generateThumbnailTask(ThumbnailRequest<Data> requestData,
                                                    ILoaderCallback<Data> loaderCallback) {

        return new BaseThumbnailTask<>(mHandler, mThreadPool,
                requestData,
                loaderCallback);

    }

    protected abstract ILoaderCallback<Data> buildThumbnailCallback();

    @Override
    public int getWaitTask() {
        return mJobs.size();
    }

    @Override
    public void cancelAllWaitTask() {
        GLog.d(TAG, "cancelAllWaitTask, mJob.size: " + mJobs.size());

        while (!mJobs.isEmpty()) {
            BaseThumbnailTask bitmapTask = mJobs.removeFirst();
            if (null != bitmapTask) {
                bitmapTask.cancelLoad();
            }
        }

        mThreadPool.cancelTasks(BaseThumbnailTask.class);
        mHandler.removeMessages(BaseThumbnailLoader.MSG_UPDATE_THUMBNAIL);
        mLimit = mTaskLimit;
    }

    private void submitLoaderIfAllowed() {
        while (!mPause && (mLimit > 0) && !mJobs.isEmpty()) {
            BaseThumbnailTask bitmapTask = mJobs.removeFirst();
            if (bitmapTask != null) {
                --mLimit;
                LOADER_CALLBACK_CACHE.put(bitmapTask.mRequestData.getRequestKey(), bitmapTask.mLoaderCallback);
                bitmapTask.startLoad();
            }
        }
    }

    public void afterLoadComplete() {
        ++mLimit;
        submitLoaderIfAllowed();
    }

    public synchronized static void putBitmapToMemoryCache(String key, Bitmap thumbnail) {
        if ((key != null) && (thumbnail != null) && !thumbnail.isRecycled()) {
            sThumbNailLruCache.put(key, thumbnail);
        }
    }

    private synchronized Bitmap getBitmapFromMemoryCache(String key) {
        return sThumbNailLruCache.get(key);
    }

    public void setQueneTaskLimit(int i) {
        GLog.d(TAG, "setQueneTaskLimit, i:" + i);
        if (i >= 0) {
            mTaskLimit = i;
        }
    }

    private static class ThumbNailLruCache extends LruCache<String, Bitmap> {

        /**
         * @param maxSize for caches that do not override {@link #sizeOf}, this is
         *                the maximum number of entries in the cache. For all other caches,
         *                this is the maximum sum of the sizes of the entries in this cache.
         */
        public ThumbNailLruCache(int maxSize) {
            super(maxSize);
        }

        @Override
        protected void entryRemoved(boolean evicted, String key, Bitmap oldValue, Bitmap newValue) {
            GLog.d(TAG, "entryRemoved, evicted: " + evicted + ", key :" + key + "," + " oldValue: " + oldValue);

            if (evicted) {
                ILoaderCallback loaderCallback = LOADER_CALLBACK_CACHE.get(key);

                if (null != loaderCallback) {
                    loaderCallback.recycleBitmap(oldValue);
                }

                LOADER_CALLBACK_CACHE.remove(key);
            }
        }

        @Override
        protected int sizeOf(String key, Bitmap value) {
            return value.getByteCount() / ONE_KB;
        }
    }

    public void destroy() {
        synchronized (mLock) {
            mJobs.clear();
        }

        mHandler.removeCallbacksAndMessages(null);

        Iterator<Map.Entry<String, ILoaderCallback>> it = LOADER_CALLBACK_CACHE.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, ILoaderCallback> item = it.next();
            if (item.getKey() != null) {
                it.remove();
            }
        }

    }

    public static class BaseThumbnailTask<Data> extends BitmapTask implements ThreadPool.Job<Bitmap> {
        private final static String TAG = "BaseThumbnailTask";
        private ImageHandler mImageHandler;
        private ILoaderCallback<Data> mLoaderCallback;
        private ThumbnailRequest<Data> mRequestData;
        private ThreadPool mThreadPool;

        public BaseThumbnailTask(ImageHandler imageHandler, ThreadPool threadPool, ThumbnailRequest<Data> request,
                                 ILoaderCallback<Data> loaderCallback) {
            mImageHandler = imageHandler;
            mRequestData = request;
            mThreadPool = threadPool;
            mLoaderCallback = loaderCallback;
        }

        @Override
        protected Future<Bitmap> submitTask(FutureListener<Bitmap> l) {

            if (mThreadPool == null) {
                GLog.e(TAG, "submitTask mThreadPool is null");
                return null;
            }

            return mThreadPool.submit(this, l);
        }

        @Override
        public Bitmap run(ThreadPool.JobContext jc) {
            Bitmap bitmap = mLoaderCallback.onThumbNailLoad(mRequestData.getSource(), jc);

            if ((bitmap != null) && (mRequestData.getBitmapHandle() != null)) {
                bitmap = mRequestData.getBitmapHandle().transform(bitmap, null);
            }

            return bitmap;
        }


        public ThumbnailRequest<Data> getRequestData() {
            return mRequestData;
        }

        @Override
        protected void onLoadComplete(Bitmap bitmap) {
            GLog.d(TAG, "onLoadComplete, bitmap :" + bitmap);
            BaseThumbnailLoader.putBitmapToMemoryCache(this.getRequestData().getRequestKey(), this.getBitmap());

            mImageHandler.obtainMessage(BaseThumbnailLoader.MSG_UPDATE_THUMBNAIL, this).sendToTarget();
        }

        @Override
        protected void onCancel() {
            GLog.d(TAG, "onCancel");

            mImageHandler.obtainMessage(BaseThumbnailLoader.MSG_CANCEL, null).sendToTarget();
        }
    }

    public interface ILoaderCallback<Data> {

        @WorkerThread
        Bitmap onThumbNailLoad(Data item, ThreadPool.JobContext jc);

        void recycleBitmap(Bitmap bitmap);

        String getDataUniqueTag(Data item);
    }
}
