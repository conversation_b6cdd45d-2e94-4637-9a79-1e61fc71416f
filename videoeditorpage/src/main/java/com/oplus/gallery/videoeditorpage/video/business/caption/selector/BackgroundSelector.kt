/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : BackgroundSelector.kt
 ** Description : 文字背景效果面板
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.selector

import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.ProgressSeekbar

/**
 * 文字背景效果面板
 */
internal class BackgroundSelector(
    pageType: PageType
) : TextEffectsSelector(pageType) {

    override fun getTextEffectsTypeConfig(): List<TextEffectsType> {
        return listOf(
            TextEffectsType.TRANSPARENT,
            TextEffectsType.CORNERS,
            TextEffectsType.MARGIN
        )
    }

    override fun initData() = Unit

    /**
     * 滑动取值后进行更新字幕设置
     */
    override fun onChanged(seekBar: ProgressSeekbar, progress: Float, isIncreasing: Boolean) {
        when (seekBar.progressId) {
            TextEffectsType.TRANSPARENT.progressId -> updateTransparent(progress, isIncreasing)
            TextEffectsType.CORNERS.progressId -> updateCorners(progress, isIncreasing)
            TextEffectsType.MARGIN.progressId -> updateMargin(progress, isIncreasing)
        }
    }

    private fun updateTransparent(progress: Float, isIncreasing: Boolean) {
        // 更新透明度
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.TRANSPARENT,
            progress,
            isIncreasing
        )
    }

    private fun updateCorners(progress: Float, isIncreasing: Boolean) {
        // 更新圆角
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.CORNERS,
            progress,
            isIncreasing
        )
    }

    private fun updateMargin(progress: Float, isIncreasing: Boolean) {
        // 更新边距
        captionEffectsChangedListener?.textEffectsChanged(
            pageType,
            TextEffectsType.MARGIN,
            progress,
            isIncreasing
        )
    }

    /**
     * 资源销毁
     */
    override fun destroy() {
        textEffectsSeekbar.clear()
        super.destroy()
    }

    companion object {
        private const val TAG = "BackgroundSelector"
    }
}
