/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditWaveformViewExt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.waveform.ui.InternalWaveformView;

import java.util.ArrayList;

public class EditWaveformViewExt extends RelativeLayout {
    private static final String TAG = "EditWaveformViewExt";
    private final InternalWaveformView mInternalWaveformView;
    private final EditMusicBeatView mBeatView;
    private final int mColorId;

    public EditWaveformViewExt(Context context, int colorId) {
        super(context);
        mColorId = colorId;
        View view = LayoutInflater.from(context).inflate(R.layout.videoeditor_music_waveformext_layout, this);
        mBeatView = view.findViewById(R.id.editor_music_beat_view);
        mInternalWaveformView = view.findViewById(R.id.edit_waveform_view);
        mInternalWaveformView.setSingleChannelMode(true);
        mInternalWaveformView.setWaveformColor(context.getColor(mColorId));
    }

    public void init(long inPoint, long outPoint, long trimIn, long trimOut, ArrayList<Long> beatList, double pixelPerMicrosecond) {
        mInternalWaveformView.setInPoint(inPoint);
        mInternalWaveformView.setOutPoint(outPoint);
        mInternalWaveformView.setTrimIn(trimIn);
        mInternalWaveformView.setTrimOut(trimOut);
        mBeatView.initView(trimIn, trimOut, beatList, pixelPerMicrosecond);
    }


    public void updateCurPoint(long curTimelinePosition) {
        mBeatView.setCurPoint(curTimelinePosition);
    }


    public void setPixelPerMicrosecond(double pixelPerMicrosecond) {
        mBeatView.setPixelPerMicrosecond(pixelPerMicrosecond);
    }


    public long getTrimIn() {
        return mInternalWaveformView.getTrimIn();
    }

    public void setTrimIn(long trimIn) {
        mInternalWaveformView.setTrimIn(trimIn);
        mBeatView.setTrimIn(trimIn);
    }

    public long getTrimOut() {
        return mInternalWaveformView.getTrimOut();
    }

    public void setTrimOut(long trimOut) {
        mInternalWaveformView.setTrimOut(trimOut);
        mBeatView.setTrimOut(trimOut);
    }

    public long getOutPoint() {
        return mInternalWaveformView.getOutPoint();
    }

    public void setOutPoint(long outPoint) {
        mInternalWaveformView.setOutPoint(outPoint);
    }

    public long getInPoint() {
        return mInternalWaveformView.getInPoint();
    }

    public void setInPoint(long inPoint) {
        mInternalWaveformView.setInPoint(inPoint);
    }

    public void setBeatArray(ArrayList<Long> beatArray) {
        mBeatView.updateBeatArray(beatArray);
    }

    public void setAudioFilePath(String audioFilePath, long trimOut) {
        mInternalWaveformView.setAudioFilePath(audioFilePath, trimOut);
    }

    public String getAudioFilePath() {
        return mInternalWaveformView.getAudioFilePath();
    }
}
