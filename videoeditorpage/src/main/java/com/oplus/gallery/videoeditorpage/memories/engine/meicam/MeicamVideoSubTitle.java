/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MeicamVideoFilter.java
 ** Description: XXXXXXXXXXXXXXXXXXXXX.
 ** Version: 1.0
 ** Date : 2017/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/13    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import static com.oplus.gallery.business_lib.model.data.location.api.LocationReverseGeocode.READ_CACHE_OR_GEOCODER;
import static com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoEngine.MILLIS_TIME_BASE;

import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.framework.abilities.videoedit.data.SubTitleInfo;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.RectF;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineCaption;
import com.oplus.gallery.business_lib.model.data.location.LocationManager;
import com.oplus.gallery.business_lib.model.data.location.api.ConfigAddress;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoSubTitle;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class MeicamVideoSubTitle implements IGalleryVideoSubTitle {

    public static final int HEX_COLOR_OXOOFFOOOO = 0x00ff0000;
    public static final int HEX_COLOR_OXOOOOFFOO = 0x0000ff00;
    public static final int HEX_COLOR_OXOOOOOOFF = 0x000000ff;
    public static final int FONT_SIZE = 57;
    public static final int WATER_SIZE = 30;
    public static final String PARAM_DATA_FILE_PATH = "Data File Path";
    private static final HashMap<NvsTimelineCaption, Integer> sCaptionSizeMap = new HashMap<>();

    private static final String TAG = "MeicamVideoFilter";
    private static final String VIDEO_SUBTITLE_STYLE = "assets:/video/subtitle/6279013E-3407-4EAE-B813-0F3DC9B3DF4A.6.captionstyle";
    private static final String VIDEO_EDITOR_WATERMARK_ICON = "assets:/watermark/ic_watermark.webp";

    private static final int DEFAULT_OUTLINE_COLOR = 0x30000000;
    private static final int DEFAULT_TEXT_COLOR = Color.WHITE;
    private static final int HEX_COLOR_OXFFOOOOOO = 0xff000000;
    private static final int SUB_TITLE_DEFAULT_TIME = 2000; // ms
    private static final int SUB_TITLE_MIN_TIME = 500; // ms
    // default subtitle text size ratio given by gui
    private static final float OUTLINE_WIDTH = 1f;
    private static final float SHADOW_FEATHER = 4f;
    private static final float SHADOW_OFFSET = 0f;
    private static final float SUBTITLE_SHADOW_FEATHER = 2f;
    private static final int WATERMARK_SIZE = 84;
    private static final int WATERMARK_MARGIN = 30;

    /**
     * 视频编辑水印距离右边的距离
     */
    private static final float VIDEO_EDITOR_WATER_MARK_PADDING_START = 111F;
    /**
     * 视频编辑机型名水印距离底部的距离
     */
    private static final float VIDEO_EDITOR_MODEL_WATER_MARK_PADDING_BOTTOM = 24F;
    /**
     * 视频编辑时间和地址水印距离底部的距离
     */
    private static final float VIDEO_EDITOR_TIME_AND_ADDRESS_WATER_MARK_PADDING_BOTTOM = 27F;
    private static final float STANDARD_VIDEO_WIDTH = 1080f;
    private static final float STANDARD_VIDEO_HEIGHT = 1920f;
    private static final int CASE_MODEL_WATERMARK = 1;
    private static final int CASE_TIME_AND_ADDRESS_WATERMARK = 2;

    /**
     * 通过{@link NvsTimelineCaption#getCaptionBoundingVertices(int var1)}方法获取表示字幕宽高的矩形四个定点，
     * 索引从0到3从左上角开始按照逆时针排列
     */
    private static final int INDEX_CAPTION_LEFT_TOP_VERTEX = 0;
    private static final int INDEX_CAPTION_RIGHT_BOTTOM_VERTEX = 2;
    private static final int CAPTION_INDEX_NUM = 4;

    private final int mFontSize;
    private final int mWaterSize;
    private final int mWaterColor;
    private final int mCaptionShadowColor;
    private final int mSubtitleShadowColor;
    private final int mWaterMarkSize;
    private final int mWaterMarkMargin;

    private MeicamVideoEngine mEngine;
    private NvsStreamingContext mStreamingContext;
    private NvsTimelineCaption mModelWaterMark;
    private NvsTimelineCaption mTimeAndAddressWaterMark;
    private NvsLiveWindow mLiveWindow;
    private NvsTimeline mTimeline;
    private MeicamTimeline mMeicamTimeline;
    // when change template will clear mFilterItem
    // mFilterItem is not null only when you have select filter
    private HashMap<Long, MeicamTimelineCaption> mSubTitleHashMap = new HashMap<>();
    private String mSubTitleStyle = null;
    private String mModelString = "";
    private String mTimeAndAddressString = "";
    private boolean mHasWaterMark = false;
    private boolean mHadHideWaterMark = false;
    private ConfigAddress mReverseGeocode;

    public MeicamVideoSubTitle(Context context, NvsStreamingContext streamingContext, MeicamVideoEngine engine) {
        mStreamingContext = streamingContext;
        mEngine = engine;
        sCaptionSizeMap.clear();
        mFontSize = FONT_SIZE;
        mWaterSize = WATER_SIZE;
        mWaterMarkSize = WATERMARK_SIZE;
        mWaterMarkMargin = WATERMARK_MARGIN;
        mWaterColor = context.getColor(R.color.videoeditor_common_list_view_background);
        mCaptionShadowColor = context.getColor(R.color.videoeditor_watermark_shadow_color);
        mSubtitleShadowColor = context.getColor(R.color.videoeditor_subtitle_shadow_color);
    }

    public void setTimeline(MeicamTimeline meicamTimeline) {
        mTimeline = meicamTimeline.getNvsTimeline();
        mMeicamTimeline = meicamTimeline;
    }

    public void setLiveWindow(NvsLiveWindow liveWindow) {
        mLiveWindow = liveWindow;
    }

    @Override
    public void updateWaterMarkPosition(boolean hideWaterMark) {
        if (mHasWaterMark && (mModelWaterMark != null) && (mTimeAndAddressWaterMark != null)) {
            if (hideWaterMark) {
                removeWaterMark(true);
            } else {
                removeWaterMark(false);
                setWaterMark(mModelString, mTimeAndAddressString);
            }
        }
    }

    @Override
    public boolean hadHideWaterMark() {
        return mHadHideWaterMark;
    }

    private float getWaterMarkScaleRatio() {
        float videoWidth = mMeicamTimeline.getWidth();
        float videoHeight = mMeicamTimeline.getHeight();
        return Math.max((videoWidth / STANDARD_VIDEO_WIDTH), (videoHeight / STANDARD_VIDEO_HEIGHT));
    }

    private PointF getWaterMarkPos(NvsTimelineCaption waterMarkCaption, int waterMarkCase) {
        float actualWidth = 0;
        float actualHeight = 0;
        RectF captionBounding = getCaptionBounding(waterMarkCaption);
        float textWidth = Math.abs(captionBounding.width());
        float textHeight = Math.abs(captionBounding.height());
        float viewWidth = mLiveWindow.getWidth();
        float viewHeight = mLiveWindow.getHeight();
        float videoWidth = (mMeicamTimeline.getWidth() != 0) ? mMeicamTimeline.getWidth() : STANDARD_VIDEO_WIDTH;
        float videoHeight = (mMeicamTimeline.getHeight() != 0) ? mMeicamTimeline.getHeight() : STANDARD_VIDEO_HEIGHT;
        float pointX = 0;
        float pointY = 0;
        float scaleRatio = 1f;
        float paddingX = VIDEO_EDITOR_WATER_MARK_PADDING_START;
        float paddingY = 0f;
        switch (waterMarkCase) {
            case CASE_MODEL_WATERMARK:
                paddingY = VIDEO_EDITOR_MODEL_WATER_MARK_PADDING_BOTTOM;
                break;
            case CASE_TIME_AND_ADDRESS_WATERMARK:
            default:
                paddingY = VIDEO_EDITOR_TIME_AND_ADDRESS_WATER_MARK_PADDING_BOTTOM;
                break;
        }

        // 按照宽度进行缩放的情况
        if ((videoWidth / videoHeight) >= (viewWidth / viewHeight)) {
            scaleRatio = viewWidth / videoWidth;
            // 显示在屏幕上的视频宽度，按照宽度进行缩放的时等于viewWidth
            actualWidth = viewWidth;
            // 显示在屏幕上的视频高度，按照宽度进行缩放时小于等于viewHeight
            actualHeight = videoHeight * scaleRatio;
            /**
             * 1. paddingX：给定在x方向padding的px值
             * 2. * getWaterMarkScaleRatio：按照当前视频分辨率和1080*1920的比率进行调整，得到实际视频中的偏移值
             * 3. * scaleRatio：实际视频中的偏移值映射为预览view中的偏移值
             * 4. + textWidth * scaleRatio / 2f：默认位置(0,0)字幕中心在预览view视图坐标系的原点，
             * 所以要达到paddingX的效果，需要加上一半的字幕宽度；字幕偏移是基于时间线的，对应于实际视频中的宽度，故需要
             * 乘scaleRatio变换为览view中的偏移值
             * 5. = pointX： 最终得到的pointX值是字幕在视图坐标系中的x轴坐标。这个坐标需要通过{@link NvsLiveWindow.mapViewToCanonical}
             * 方法映射成时间线坐标（相册编辑中时间线的宽高设置就是视频的分辨率宽高），然后再调用{@link com.meicam.sdk.NvsCaption.translateCaption}
             * 方法进行实际的偏移操作
             */
            pointX = paddingX * getWaterMarkScaleRatio() * scaleRatio + textWidth * scaleRatio / 2f;
            switch (waterMarkCase) {
                case CASE_MODEL_WATERMARK:
                    /**
                     * 1. actualHeight + (viewHeight - actualHeight) / 2f：定位视频底部在预览view的视图坐标系中的y轴位置，
                     * 实际的视频高度可能比预览view的高度小，并且视频在预览view中居中。字幕位于左下角，所以采用缩减的方式确定字幕在视图坐标系中的y轴位置
                     * 2. - paddingY * getWaterMarkScaleRatio() * scaleRatio：对给定的y轴padding值先* getWaterMarkScaleRatio()
                     * 获得对应分辨率下的padding值，然后* scaleRatio将视频中的偏移尺寸映射为预览view中的偏移尺寸
                     * 3. - textHeight * scaleRatio * (1f / 2f + 1f)：可以分解成textHeight * scaleRatio / 2f
                     * 和textHeight * scaleRatio两部分。textHeight * scaleRatio / 2f同上面计算pointX时的场景，
                     * textHeight * scaleRatio设置了机型字幕和时间字幕之间的间距。**如果文字渲染机制改变引起文字高度变化，
                     * 机型字幕和时间字幕之间的间距会发生改变**
                     * 4. = pointY 最终得到的pointY值是字幕在视图坐标系中的y轴坐标。
                     */
                    pointY = actualHeight + (viewHeight - actualHeight) / 2f - textHeight * scaleRatio * (1f / 2f + 1f)
                        - paddingY * getWaterMarkScaleRatio() * scaleRatio;
                    break;
                case CASE_TIME_AND_ADDRESS_WATERMARK:
                    /**
                     * 1. actualHeight + (viewHeight - actualHeight) / 2f：定位视频底部在预览view的视图坐标系中的y轴位置，
                     * 实际的视频高度可能比预览view的高度小，并且视频在预览view中居中。字幕位于左下角，所以采用缩减的方式确定字幕在视图坐标系中的y轴位置
                     * 2. - paddingY * getWaterMarkScaleRatio() * scaleRatio：对给定的y轴padding值先* getWaterMarkScaleRatio()
                     * 获得对应分辨率下的padding值，然后* scaleRatio将视频中的偏移尺寸映射为预览view中的偏移尺寸
                     * 3. - textHeight * scaleRatio / 2f：textHeight * scaleRatio / 2f同上面计算pointX时的场景，
                     * 4. = pointY 最终得到的pointY值是字幕在视图坐标系中的y轴坐标。
                     */
                    pointY = actualHeight + (viewHeight - actualHeight) / 2f - textHeight * scaleRatio / 2f
                        - paddingY * getWaterMarkScaleRatio() * scaleRatio;
                    break;
                default:
                    pointY = actualHeight;
                    break;
            }
        } else {
            scaleRatio = viewHeight / videoHeight;
            actualWidth = videoWidth * scaleRatio;
            actualHeight = viewHeight;
            pointX = paddingX * getWaterMarkScaleRatio() * scaleRatio
                + (viewWidth - actualWidth) / 2f + textWidth * scaleRatio / 2f;
            switch (waterMarkCase) {
                case CASE_MODEL_WATERMARK:
                    pointY = actualHeight - textHeight * scaleRatio * (1f / 2f + 1f) - paddingY * getWaterMarkScaleRatio() * scaleRatio;
                    break;
                case CASE_TIME_AND_ADDRESS_WATERMARK:
                    pointY = actualHeight - textHeight * scaleRatio / 2f - paddingY * getWaterMarkScaleRatio() * scaleRatio;
                    break;
                default:
                    pointY = actualHeight;
                    break;
            }
        }
        GLog.d(TAG, "viewWidth:" + viewWidth + " viewHeight:" + viewHeight
            + ", videoWidth:" + videoWidth + " videoHeight:" + videoHeight
            + ", textWidth:" + textWidth + " textHeight:" + textHeight
            + ", actualWidth:" + actualWidth + " actualHeight:" + actualHeight
            + ", pointx:" + pointX + " pointy:" + pointY);
        return new PointF(pointX, pointY);
    }

    /**
     * 计算出表示给定字幕占用空间的矩形
     *
     * @param waterMarkCaption 给定字幕
     * @return 表示字幕占用空间的矩形
     */
    private RectF getCaptionBounding(NvsTimelineCaption waterMarkCaption) {
        // 一组从左上角顶点开始，逆时针方向排列的矩形的四个顶点
        List<PointF> vertexList = waterMarkCaption.getCaptionBoundingVertices(NvsTimelineCaption.BOUNDING_TYPE_TYPOGRAPHIC_TEXT);
        if ((vertexList == null) || (vertexList.size() != CAPTION_INDEX_NUM)) {
            GLog.e(TAG, "getCaptionBounding, illegal arg: vertexList");
            return new RectF(0f, 0f, 0f, 0f);
        }
        PointF vertexLeftTop = vertexList.get(INDEX_CAPTION_LEFT_TOP_VERTEX);
        PointF vertexRightBottom = vertexList.get(INDEX_CAPTION_RIGHT_BOTTOM_VERTEX);
        return new RectF(vertexLeftTop.x, vertexLeftTop.y, vertexRightBottom.x, vertexRightBottom.y);
    }

    private NvsColor convertHexToRGB(int hexColocr) {
        NvsColor color = new NvsColor(0, 0, 0, 0);
        color.a = (float) ((hexColocr & HEX_COLOR_OXFFOOOOOO) >>> 24) / 0xFF;
        color.r = (float) ((hexColocr & HEX_COLOR_OXOOFFOOOO) >> 16) / 0xFF;
        color.g = (float) ((hexColocr & HEX_COLOR_OXOOOOFFOO) >> 8) / 0xFF;
        color.b = (float) ((hexColocr) & HEX_COLOR_OXOOOOOOFF) / 0xFF;
        return color;
    }

    @Override
    public void installSubTitleStyleRes() {
        if (mTimeline == null) {
            GLog.w(TAG, "installSubTitleStyleRes mTimeline is null.");
            return;
        }
        mTimeline.setCaptionBoundingRectInActualMode(true);
        NvsAssetPackageManager packageManager = mStreamingContext.getAssetPackageManager();
        StringBuilder styleBuilder = new StringBuilder();
        if (packageManager != null) {
            int result = packageManager.installAssetPackage(VIDEO_SUBTITLE_STYLE,
                VIDEO_SUBTITLE_STYLE, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTIONSTYLE, false, styleBuilder);
            if (result == NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED) {
                GLog.d(TAG, "installSubTitleStyleRes(), style already installed");
                int newVersion = packageManager.getAssetPackageVersionFromAssetPackageFilePath(VIDEO_SUBTITLE_STYLE);
                String filterID = packageManager.getAssetPackageIdFromAssetPackageFilePath(VIDEO_SUBTITLE_STYLE);
                int oldVersion = packageManager.getAssetPackageVersion(filterID, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTIONSTYLE);
                if (newVersion > oldVersion) {
                    GLog.d(TAG, "installSubTitleStyleRes(), need update oldVersion = " + oldVersion + ", newVersion = " + newVersion);
                    result = packageManager.upgradeAssetPackage(VIDEO_SUBTITLE_STYLE,
                        VIDEO_SUBTITLE_STYLE, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_CAPTIONSTYLE, false, styleBuilder);
                }
            }
            GLog.d(TAG, "installSubTitleStyleRes(), result = " + result + ", styleBuilder = " + styleBuilder);
            mSubTitleStyle = styleBuilder.toString();
        } else {
            GLog.e(TAG, "installSubTitleStyleRes() Failed! packageManager is null");
        }
    }


    @Override
    public void updateWaterMarkDuration() {
        if (mTimeAndAddressWaterMark != null) {
            mTimeAndAddressWaterMark.changeOutPoint(mTimeline.getDuration() + MILLIS_TIME_BASE);
        }
        if (mModelWaterMark != null) {
            mModelWaterMark.changeOutPoint(mTimeline.getDuration() + MILLIS_TIME_BASE);
        }
    }

    @Override
    public boolean setWaterMark(String model, String timeAndAddress) {
        GLog.d(TAG, "addWaterMark() text:" + model + ", " + timeAndAddress);
        mModelString = model;
        mTimeAndAddressString = timeAndAddress;
        boolean success = false;
        if (mHasWaterMark && (mModelWaterMark != null) && (mTimeAndAddressWaterMark != null)) {
            removeWaterMark(false);
            success = true;
        } else if (mModelWaterMark == null) {
            if (mLiveWindow == null) {
                GLog.e(TAG, "setWaterMark error: set liveWindow is null external use required createLiveWindow to initialize");
                return false;
            }
            int waterMarkSize = (int) (getWaterMarkScaleRatio() * (float) mWaterMarkSize);
            int waterMarkDistance = (int) (getWaterMarkScaleRatio() * (float) mWaterMarkMargin);
            mTimeline.addWatermark(VIDEO_EDITOR_WATERMARK_ICON, waterMarkSize, waterMarkSize, 1,
                NvsTimeline.NvsTimelineWatermarkPosition_BottomLeft, waterMarkDistance, waterMarkDistance);

            mModelWaterMark = mTimeline.addCaption(model, 0, mTimeline.getDuration() + MILLIS_TIME_BASE, null);
            mTimeAndAddressWaterMark = mTimeline.addCaption(timeAndAddress, 0, mTimeline.getDuration() + MILLIS_TIME_BASE, null);
            if ((mModelWaterMark == null) || (mTimeAndAddressWaterMark == null)) {
                GLog.d(TAG, "addWaterMark() error");
                return false;
            }
            mModelWaterMark.applyCaptionStyle(mSubTitleStyle, NvsTimelineCaption.NOT_USE_ASSET_DEFAULT_PARAM);
            mTimeAndAddressWaterMark.applyCaptionStyle(mSubTitleStyle, NvsTimelineCaption.NOT_USE_ASSET_DEFAULT_PARAM);
            int size = (int) (getWaterMarkScaleRatio() * mWaterSize);
            PointF pointFs = new PointF(SHADOW_OFFSET, SHADOW_OFFSET);

            try {
                setCaptionProperties(mModelWaterMark, size, mCaptionShadowColor, pointFs);
                mModelWaterMark.setDrawOutline(true);
                PointF viewPointModelWaterMark = mLiveWindow.mapViewToCanonical(getWaterMarkPos(mModelWaterMark, CASE_MODEL_WATERMARK));
                mModelWaterMark.translateCaption(viewPointModelWaterMark);

                setCaptionProperties(mTimeAndAddressWaterMark, size, mCaptionShadowColor, pointFs);
                PointF viewPointTimeAndAddressWaterMark = mLiveWindow.mapViewToCanonical(getWaterMarkPos(mTimeAndAddressWaterMark, CASE_TIME_AND_ADDRESS_WATERMARK));
                mTimeAndAddressWaterMark.translateCaption(viewPointTimeAndAddressWaterMark);
            } catch (Exception e) {
                GLog.e(TAG, LogFlag.DL, "setWaterMark. error catch:： " + e.getMessage());
            }
            sCaptionSizeMap.put(mModelWaterMark, size);
            sCaptionSizeMap.put(mTimeAndAddressWaterMark, size);
            mHasWaterMark = true;
            success = true;
            mHadHideWaterMark = false;
        }
        return success;
    }

    private void removeWaterMark(boolean justHide) {
        if (!mHadHideWaterMark) {
            mTimeline.removeCaption(mModelWaterMark);
            mTimeline.removeCaption(mTimeAndAddressWaterMark);
            mTimeline.deleteWatermark();
            mHadHideWaterMark = true;
        }
        if (!justHide) {
            sCaptionSizeMap.remove(mModelWaterMark);
            sCaptionSizeMap.remove(mTimeAndAddressWaterMark);
            mHasWaterMark = false;
            mModelWaterMark = null;
            mTimeAndAddressWaterMark = null;
        }
    }

    private void setCaptionProperties(NvsTimelineCaption caption, int size, int shadowColor, PointF pointFs) {
        caption.setFontByFilePath(TypefaceUtil.getSansRobotoPath());
        caption.setTextColor(convertHexToRGB(mWaterColor));
        caption.setFontSize(size);
        caption.setDrawOutline(false);
        caption.setOutlineColor(convertHexToRGB(mWaterColor));
        caption.setOutlineWidth(OUTLINE_WIDTH);
        caption.setDrawShadow(true);
        caption.setShadowColor(convertHexToRGB(shadowColor));
        caption.setShadowFeather(SHADOW_FEATHER);
        caption.setShadowOffset(pointFs);
        caption.setClipAffinityEnabled(false);
        caption.setBold(false);
        caption.setTextAlignment(NvsTimelineCaption.TEXT_ALIGNMENT_LEFT);
    }

    @Override
    public boolean hasWaterMark() {
        return mHasWaterMark;
    }

    @Override
    public long addSubTitle(String text, Context context) {
        NvsTimelineCaption subTitle = null;
        long time = System.currentTimeMillis();
        long pos = mStreamingContext.getTimelineCurrentPosition(mTimeline) / MeicamVideoEngine.MILLIS_TIME_BASE;
        long totalTime = mEngine.getTotalTime();
        if ((totalTime > SUB_TITLE_MIN_TIME) && (pos > (totalTime - SUB_TITLE_MIN_TIME))) {
            pos = totalTime - SUB_TITLE_MIN_TIME;
        }
        subTitle = mTimeline.addCaption(text, pos * MeicamVideoEngine.MILLIS_TIME_BASE,
            SUB_TITLE_DEFAULT_TIME * MeicamVideoEngine.MILLIS_TIME_BASE, null);
        if (subTitle == null) {
            GLog.e(TAG, "addSubTitle, error pos:" + pos + " totalTime : " + totalTime);
            return SubTitleInfo.INDEX_INVALID;
        }
        boolean result = subTitle.applyCaptionStyle(mSubTitleStyle, NvsTimelineCaption.NOT_USE_ASSET_DEFAULT_PARAM);
        GLog.d(TAG, "addSubTitle() result = " + result
            + ", mSubTitleStyle = " + mSubTitleStyle
            + ", text = " + text);
        int size = getFontSize(mFontSize);
        setSubTitleCaption(subTitle);
        mSubTitleHashMap.put(time, new MeicamTimelineCaption(subTitle));
        sCaptionSizeMap.put(subTitle, size);
        GLog.d(TAG, "addSubTitle()"
            + ", pos:" + pos
            + ", getInPoint():" + subTitle.getInPoint()
            + ", getOutPoint():" + subTitle.getOutPoint());
        mEngine.seekTo(subTitle.getInPoint() / MeicamVideoEngine.MILLIS_TIME_BASE);
        return time;
    }

    public int getFontSize(int lookSize) {
        if (mLiveWindow == null) {
            GLog.e(TAG, "getFontSize error: set liveWindow is null external use required createLiveWindow to initialize");
            return 0;
        }
        int height = mMeicamTimeline.getHeight();
        int wHeight = mLiveWindow.getLayoutParams().height;
        float ra = ((float) wHeight) / height;
        int size = (int) (lookSize / ra);
        return size;
    }

    @Override
    public void addSubTitleInfo(SubTitleInfo info, Context context) {
        NvsTimelineCaption subTitle = null;
        if (info.getSubTitleIndex() == SubTitleInfo.INDEX_INVALID) {
            GLog.e(TAG, "addSubTitleInfo,sub title index is invalid, can not support add subTitle.");
            return;
        }
        subTitle = mTimeline.addCaption(info.getSubTitle(), info.getStartTimePos() * MeicamVideoEngine.MILLIS_TIME_BASE,
            (info.getEndTimePos() - info.getStartTimePos()) * MeicamVideoEngine.MILLIS_TIME_BASE, null);
        if (subTitle == null) {
            GLog.e(TAG, "addSubTitleInfo() error info.getStartTimePos():" + info.getStartTimePos()
                + ", info.getEndTimePos():" + info.getEndTimePos());
            return;
        }
        int size = getFontSize(mFontSize);
        setSubTitleCaption(subTitle);
        PointF point = info.getPointPos();
        subTitle.setCaptionTranslation(point);
        mSubTitleHashMap.put(info.getSubTitleIndex(), new MeicamTimelineCaption(subTitle));
        sCaptionSizeMap.put(subTitle, size);
    }

    private void setSubTitleCaption(NvsTimelineCaption subTitle) {
        if (subTitle != null) {
            PointF pointFs = new PointF(SHADOW_OFFSET, SHADOW_OFFSET);
            try {
                subTitle.setTextColor(convertHexToRGB(Color.WHITE));
                subTitle.setFontSize(getFontSize(mFontSize));
                subTitle.setDrawOutline(false);
                subTitle.setClipAffinityEnabled(false);
                subTitle.setOutlineColor(convertHexToRGB(DEFAULT_TEXT_COLOR));
                subTitle.setBold(false);
                subTitle.setDrawShadow(true);
                subTitle.setShadowColor(convertHexToRGB(mSubtitleShadowColor));
                subTitle.setShadowFeather(SUBTITLE_SHADOW_FEATHER);
                subTitle.setShadowOffset(pointFs);
                subTitle.setFontByFilePath(TypefaceUtil.getSansRobotoPath());
            } catch (Exception e) {
                GLog.e(TAG, LogFlag.DL, "error catch in setSubTitleCaption: " + e.getMessage());
            }
        }
    }

    @Override
    public void renameSubTitle(long subTitleIndex, String newText) {
        MeicamTimelineCaption caption = mSubTitleHashMap.get(subTitleIndex);
        if (caption != null) {
            caption.getCaption().setText(newText);
            mEngine.seekTo(mEngine.getCurrentTime());
        }
    }

    @Override
    public SubTitleInfo removeSubTitle(long subTitleIndex, boolean needSeek) {
        MeicamTimelineCaption meicamTimelineCaption = mSubTitleHashMap.get(subTitleIndex);
        if (meicamTimelineCaption != null) {
            NvsTimelineCaption nvsTimelineCaption = meicamTimelineCaption.getCaption();
            GLog.d(TAG, "removeSubTitle()");
            SubTitleInfo info = new SubTitleInfo(
                nvsTimelineCaption.getText(),
                subTitleIndex,
                nvsTimelineCaption.getInPoint() / MeicamVideoEngine.MILLIS_TIME_BASE,
                nvsTimelineCaption.getOutPoint() / MeicamVideoEngine.MILLIS_TIME_BASE,
                nvsTimelineCaption.getCaptionTranslation());
            mSubTitleHashMap.remove(subTitleIndex);
            sCaptionSizeMap.remove(nvsTimelineCaption);
            mTimeline.removeCaption(nvsTimelineCaption);
            if (needSeek) {
                mEngine.seekTo(mEngine.getCurrentTime());
            }
            return info;
        }
        return null;
    }

    @Override
    public void moveSubTitle(long subTitleIndex, PointF prePointF, PointF nowPointF) {
        if (mLiveWindow == null) {
            GLog.e(TAG, "moveSubTitle error: set liveWindow is null external use required createLiveWindow to initialize");
            return;
        }
        MeicamTimelineCaption meicamTimelineCaption = mSubTitleHashMap.get(subTitleIndex);
        if (meicamTimelineCaption != null) {
            NvsTimelineCaption nvsTimelineCaption = meicamTimelineCaption.getCaption();
            PointF pre = mLiveWindow.mapViewToCanonical(prePointF);
            PointF p = mLiveWindow.mapViewToCanonical(nowPointF);
            PointF timeLinePointF = new PointF(p.x - pre.x, p.y - pre.y);
            nvsTimelineCaption.translateCaption(timeLinePointF);
            mEngine.seekTo(mEngine.getCurrentTime());
        }
    }

    @Override
    public List<SubTitleInfo> checkAndGetSubTitleEditPos(long currentTime) {
        if (mLiveWindow == null) {
            GLog.e(TAG, "checkAndGetSubTitleEditPos error: set liveWindow is null external use required createLiveWindow to initialize");
            return null;
        }
        List<SubTitleInfo> subTitleInfoList = new ArrayList<>();
        for (Map.Entry<Long, MeicamTimelineCaption> entry : mSubTitleHashMap.entrySet()) {
            if ((entry != null) && (entry.getValue() != null)) {
                NvsTimelineCaption caption = entry.getValue().getCaption();
                if ((caption.getInPoint() / MeicamVideoEngine.MILLIS_TIME_BASE <= currentTime)
                    && (currentTime <= caption.getOutPoint() / MeicamVideoEngine.MILLIS_TIME_BASE)) {
                    SubTitleInfo info = new SubTitleInfo(
                        caption.getText(),
                        entry.getKey(),
                        caption.getInPoint() / MeicamVideoEngine.MILLIS_TIME_BASE,
                        caption.getOutPoint() / MeicamVideoEngine.MILLIS_TIME_BASE,
                        caption.getCaptionTranslation());
                    List<PointF> list = caption.getBoundingRectangleVertices();
                    List<PointF> newList = new ArrayList<>();
                    if ((list != null) && (list.size() > 0)) {
                        for (PointF point : list) {
                            PointF pointF = mLiveWindow.mapCanonicalToView(point);
                            if (pointF != null) {
                                newList.add(pointF);
                            }
                        }
                        info.setFourPointPos(newList);
                    }
                    subTitleInfoList.add(info);
                }
            }
        }
        return subTitleInfoList;
    }

    @Override
    public ArrayList<SubTitleInfo> getSubTitleList() {
        if (mLiveWindow == null) {
            GLog.e(TAG, "getSubTitleList error: set liveWindow is null external use required createLiveWindow to initialize");
            return null;
        }
        ArrayList<SubTitleInfo> subTitleList = new ArrayList<>();
        long endTime = mEngine.getTotalTime();
        for (Map.Entry<Long, MeicamTimelineCaption> entry : mSubTitleHashMap.entrySet()) {
            if ((entry != null) && (entry.getValue() != null)) {
                NvsTimelineCaption caption = entry.getValue().getCaption();
                long end = caption.getOutPoint() / MeicamVideoEngine.MILLIS_TIME_BASE;
                if (end > endTime) {
                    end = endTime;
                }
                SubTitleInfo info = new SubTitleInfo(
                    caption.getText(),
                    entry.getKey(),
                    caption.getInPoint() / MeicamVideoEngine.MILLIS_TIME_BASE,
                    end,
                    caption.getCaptionTranslation());
                List<PointF> list = caption.getBoundingRectangleVertices();
                List<PointF> newList = new ArrayList<>();
                if ((list != null) && (list.size() > 0)) {
                    for (PointF point : list) {
                        PointF pointF = mLiveWindow.mapCanonicalToView(point);
                        if (pointF != null) {
                            newList.add(pointF);
                        }
                    }
                    info.setFourPointPos(newList);
                }
                info.setBold(caption.getBold());
                info.setFontSize(caption.getFontSize());
                info.setTextColor(DEFAULT_TEXT_COLOR);
                info.setOutlineColor(DEFAULT_OUTLINE_COLOR);
                info.setClipAffinityEnabled(caption.getClipAffinityEnabled());
                info.setDrawOutline(caption.getDrawOutline());
                info.setVisibility(entry.getValue().getVisibility());
                subTitleList.add(info);
            }
        }
        return subTitleList;
    }

    public void moveAllSubTitle(long offsetUs) {
        for (Map.Entry<Long, MeicamTimelineCaption> entry : mSubTitleHashMap.entrySet()) {
            if ((entry == null) || (entry.getValue() == null)) {
                continue;
            }
            MeicamTimelineCaption caption = entry.getValue();
            caption.movePosition(offsetUs);
        }
    }

    public void moveAllSubTitle(Function<Long, Long> offsetCalculateFunction) {
        for (Map.Entry<Long, MeicamTimelineCaption> entry : mSubTitleHashMap.entrySet()) {
            if ((entry == null) || (entry.getValue() == null)) {
                continue;
            }
            MeicamTimelineCaption caption = entry.getValue();
            long offsetNs = offsetCalculateFunction.apply(caption.getInTime());
            caption.movePosition(offsetNs);
        }
    }

    public void moveAllSubTitle(float speed) {
        for (Map.Entry<Long, MeicamTimelineCaption> entry : mSubTitleHashMap.entrySet()) {
            if ((entry == null) || (entry.getValue() == null)) {
                continue;
            }
            MeicamTimelineCaption caption = entry.getValue();
            caption.movePosition(speed);
        }
    }


    @Override
    public void computeLocationInfo(double latitude, double longitude) {
        if (mReverseGeocode == null) {
            mReverseGeocode = LocationManager.INSTANCE.lookupAddress(latitude, longitude, READ_CACHE_OR_GEOCODER);
        }
    }

    @Override
    public void clearLocationInfo() {
        mReverseGeocode = null;
    }

    @Nullable
    @Override
    public ConfigAddress getAddress() {
        return mReverseGeocode;
    }

    public boolean isSubTitleChanged() {
        return (mHasWaterMark || mSubTitleHashMap.size() != 0);
    }

    /**
     * 获取标题字体大小
     */
    @Nullable
    public static Integer getCaptionFontSize(@NonNull NvsTimelineCaption caption) {
        return sCaptionSizeMap.get(caption);
    }
}
