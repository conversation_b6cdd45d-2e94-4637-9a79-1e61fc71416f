/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorTrimUIController
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.trim;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.math.MathUtils;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.ScrollMode;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.MusicClipView;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel;
import com.oplus.gallery.videoeditorpage.common.statistics.StatisticsConstant;
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil;
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil;
import com.oplus.gallery.videoeditorpage.utlis.ViewExtKt;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController;
import com.oplus.gallery.videoeditorpage.video.business.base.data.EffectRange;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;
import com.oplus.gallery.videoeditorpage.video.business.track.util.EffectTrackRangeHelper;
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper;
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipSelectType;
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewWrapper;
import com.oplus.gallery.videoeditorpage.video.config.UiConfigManager;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import android.app.Service;
import android.content.Context;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.view.View;
import static android.view.View.GONE;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV;
import static com.oplus.gallery.videoeditorpage.widget.EditTimelineView.STATE_PREVIEW;

/**
 * 编辑剪辑UI控制
 */
public class EditorTrimUIController extends EditorTrackBaseUIController<Object> {
    private static final String TAG = "EditorTrimUIController";
    private static final int VIBRATE_TIME = 100;
    private static final int KEY_STATISTICS = 3;
    private static final int EXPOSURE_TIME = 1000;

    private IVideoClip mSelectClip;
    private OnThumbnailListener mOnThumbnailListener;

    /** 剪辑菜单水平滚动视图 */
    private HorizontalScrollView mHorizontalScrollView;

    /** 分割按钮 */
    private LinearLayout mSegmentLayout;
    /** 分割按钮图标 */
    private ImageView mSegmentIcon;
    /** 分割按钮文本 */
    private TextView mSegmentText;

    /** 替换按钮 */
    private LinearLayout mReplaceLayout;
    /** 替换按钮图标 */
    private ImageView mReplaceIcon;
    /** 替换按钮文本 */
    private TextView mReplaceText;

    /** 删除按钮 */
    private LinearLayout mDeleteLayout;
    /** 删除按钮图标 */
    private ImageView mDeleteIcon;
    /** 删除按钮文本 */
    private TextView mDeleteText;

    /** 旋转裁剪按钮 */
    private LinearLayout mTailorLayout;
    /** 旋转裁剪按钮图标 */
    private ImageView mTailorIcon;
    /** 旋转裁剪按钮文本 */
    private TextView mTailorText;

    /** 变速按钮 */
    private LinearLayout mSpeedLayout;
    /** 变速按钮图标 */
    private ImageView mSpeedIcon;
    /** 变速按钮文本 */
    private TextView mSpeedText;

    /** 排序按钮 */
    private LinearLayout mSortLayout;
    /** 排序按钮图标 */
    private ImageView mSortIcon;
    /** 排序按钮文本 */
    private TextView mSortText;

    private LinearLayout mLinearLayoutVolume;
    private LinearLayout mLinearLayoutRotation;
    /** 滤镜item */
    private LinearLayout mLinearLayoutFilter;

    private ImageView mClipRotation;
    private ImageView mClipVolume;
    private ImageView mClipFilter;

    private TextView mRotationTV;
    private TextView mVolumeTV;
    private TextView mFilterTV;
    private OnButtonClickListener mOnButtonClickListener;
    private OnEffectTrackSelectListener mOnEffectTrackSelectListener;
    private String mMaxCountText;
    private boolean mIsInEditorMode = false;
    private String mState = STATE_PREVIEW;
    private Handler mHandler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case KEY_STATISTICS:
                    onMontageExposed();
                    break;
                default:
                    break;
            }
        }
    };

    /**
     * 视频剪辑页默认轨道可用范围是一整个，只有滑动到尾帧附近才是不可用
     */
    private ArrayList<EffectRange> mEmptyRanges = new ArrayList<>();
    /** 把手调节后时码线所在的位置 */
    private long mClipAdjustedPosition = -1;
    /** 是否为初始更新 */
    private boolean mIsInitUpdate = false;

    /**
     * 构建方法
     *
     * @param context           上下文
     * @param rootView          编辑控制视图
     * @param state             编辑视图状态
     * @param timelineViewModel 时间线视图模型
     */
    public EditorTrimUIController(
        Context context,
        ViewGroup rootView,
        EditorTrimState state,
        @Nullable TimelineViewModel timelineViewModel
    ) {
        super(context, rootView, state, timelineViewModel);
    }

    private boolean isVisible(View v) {
        if (v == null) {
            return false;
        }
        return v.getLocalVisibleRect(new Rect());
    }

    public String getMaxCountText() {
        return mMaxCountText;
    }

    public void setMaxCountText(String maxCountText) {
        mMaxCountText = maxCountText;
    }

    public void setOnThumbnailListener(OnThumbnailListener listener) {
        mOnThumbnailListener = listener;
    }

    public void setOnEffectTrackSelectListener(OnEffectTrackSelectListener listener) {
        mOnEffectTrackSelectListener = listener;
    }

    public void updateTimePositionChanged(long position) {
        if (getTimelineViewModel() != null) {
            WeakReference<EditorTrimUIController> weakThis = new WeakReference<>(this);
            mTrackView.post(() -> {
                if (weakThis.get() == null) {
                    GLog.e(TAG, LogFlag.DL, "[updateTimePositionChanged] mRootView.post: weakThis is null.");
                    return;
                }
                if (getTimelineViewModel() == null) {
                    GLog.e(TAG, LogFlag.DL, "[updateTimePositionChanged] mRootView.post: timelineViewModel is null.");
                    return;
                }
                getTimelineViewModel().layoutScrollToXPositionByPlay(position);
                int index = getTimelineViewModel().setSubSelectedMainTrack(position);
                if (index >= 0) {
                    List<IVideoClip> clips = weakThis.get().mEditorState.getCurrentTimelineClips();
                    if ((clips == null) || (clips.size() <= index)) {
                        return;
                    }
                    weakThis.get().mSelectClip = clips.get(index);
                }
            });
        }
    }

    public void clearSelectClip() {
        mSelectClip = null;
    }

    public void setFunctionEnable(long position) {
        setFunctionEnable(position, mSelectClip != null);
    }

    public void setFunctionEnable(long position, boolean enable) {
        if (mEditorState instanceof EditorTrimState) {
            setSegmentEnable(isEnableSegmentFromPosition(position));
            setReplaceEnable(enable);
            updateDeleteAndSortButtonStatus(isEnabledDeleteAndSort());
            setTailorEnable(enable);
            setSpeedEnable(enable);

            if (mClipRotation != null) {
                mClipRotation.setEnabled(enable);
                mRotationTV.setEnabled(enable);
            }

            if (mLinearLayoutVolume != null) {
                IVideoClip videoClip = getCurrentClip();
                if ((videoClip != null) && (videoClip.getVideoType() == VIDEO_CLIP_TYPE_AV)) {
                    mLinearLayoutVolume.setEnabled(enable);
                    mClipVolume.setEnabled(enable);
                    mVolumeTV.setEnabled(enable);
                } else {
                    mLinearLayoutVolume.setEnabled(false);
                    mClipVolume.setEnabled(false);
                    mVolumeTV.setEnabled(false);
                }
            }

            if (mClipFilter != null) {
                mClipFilter.setEnabled(enable);
                mFilterTV.setEnabled(enable);
            }
        }
    }

    /**
     * 设置分段按钮的可用性
     * @param enable 是否可用
     */
    public void setSegmentEnable(boolean enable) {
        if (mSegmentLayout != null) {
            mSegmentLayout.setEnabled(enable);
        }
        if (mSegmentIcon != null) {
            mSegmentIcon.setEnabled(enable);
        }
        if (mSegmentText != null) {
            mSegmentText.setEnabled(enable);
        }
    }

    /**
     * 设置替换按钮的可用性
     * @param enable 是否可用
     */
    public void setReplaceEnable(boolean enable) {
        if (mReplaceLayout != null) {
            mReplaceLayout.setEnabled(enable);
        }
        if (mReplaceIcon != null) {
            mReplaceIcon.setEnabled(enable);
        }
        if (mReplaceText != null) {
            mReplaceText.setEnabled(enable);
        }
    }

    /**
     * 设置删除按钮的可用性
     * @param enable 是否可用
     */
    public void setDeleteEnable(boolean enable) {
        if (mDeleteLayout != null) {
            mDeleteLayout.setEnabled(enable);
        }
        if (mDeleteIcon != null) {
            mDeleteIcon.setEnabled(enable);
        }
        if (mDeleteText != null) {
            mDeleteText.setEnabled(enable);
        }
    }

    /**
     * 设置旋转裁剪按钮的可用性
     * @param enable 是否可用
     */
    public void setTailorEnable(boolean enable) {
        if (mTailorLayout != null) {
            mTailorLayout.setEnabled(enable);
        }
        if (mTailorIcon != null) {
            mTailorIcon.setEnabled(enable);
        }
        if (mTailorText != null) {
            mTailorText.setEnabled(enable);
        }
    }

    /**
     * 设置变速按钮的可用性
     * @param enable 是否可用
     */
    public void setSpeedEnable(boolean enable) {
        if (mSpeedLayout != null) {
            mSpeedLayout.setEnabled(enable);
        }
        if (mSpeedIcon != null) {
            mSpeedIcon.setEnabled(enable);
        }
        if (mSpeedText != null) {
            mSpeedText.setEnabled(enable);
        }
    }

    /**
     * 设置排序按钮的可用性
     * @param enable 是否可用
     */
    public void setSortEnable(boolean enable) {
        if (mSortLayout != null) {
            mSortLayout.setEnabled(enable);
        }
        if (mSortIcon != null) {
            mSortIcon.setEnabled(enable);
        }
        if (mSortText != null) {
            mSortText.setEnabled(enable);
        }
    }

    /**
     * 根据时间线位置判断分割是否可用
     * @param position 位置
     */
    private boolean isEnableSegmentFromPosition(long position) {
        if (mIsInEditorMode) {
            return false;
        }
        IVideoClip currentClip = getCurrentClip();
        if (currentClip == null) {
            GLog.e(TAG, LogFlag.DL, "[isEnableSegmentFromPosition] currentClip == null");
            return false;
        }
        return ((currentClip.getInPoint() + EditorEngine.MIN_CLIP_DURATION) <= position)
            && ((position + EditorEngine.MIN_CLIP_DURATION) < currentClip.getOutPoint());
    }

    public IVideoClip getCurrentClip() {
        return mSelectClip;
    }

    private boolean isEnabledDeleteAndSort() {
        IVideoTrack videoTrack = mEditorState.getEditorEngine().getCurrentTimeline().getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "isEnabledDeleteAndSort: current track is invalid!");
            return true;
        }
        boolean enabled = (videoTrack.getUserClipCount() > 1) ? true : false;
        return enabled;
    }

    public void stopFling() {
        if ((getTimelineViewModel() != null)) {
            getTimelineViewModel().stopFling();
        }
    }

    @Override
    protected int getContainerId() {
        return R.id.sub_menu_container;
    }

    @Override
    public int getContentLayoutId(@NonNull AppUiResponder.AppUiConfig appUiConfig) {
        if (EditorUIConfig.isEditorLandscape(appUiConfig)) {
            return R.layout.videoeditor_trim_submenu_layout_landscape;
        } else {
            return R.layout.videoeditor_trim_submenu_layout;
        }
    }

    public void updateIconTextView() {
        mRotationTV.setVisibility(View.VISIBLE);
        mVolumeTV.setVisibility(View.VISIBLE);
        mFilterTV.setVisibility(View.VISIBLE);
        mSpeedLayout.setVisibility(UiConfigManager.isShowSpeedLayout() ? View.VISIBLE : View.GONE);
        mReplaceLayout.setVisibility(UiConfigManager.isShowReplaceLayout() ? View.VISIBLE : View.GONE);
    }

    @Override
    public void resume(boolean isActivityResume) {
        super.resume(isActivityResume);
        updateIconTextView();
    }

    @Override
    public void createView() {
        super.createView();
        mIsInitUpdate = true;
        init();
    }

    @Override
    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig appUiConfig) {
        super.onAppUiStateChanged(appUiConfig);
        boolean isWidthChanged = appUiConfig.getWindowWidth().isChanged();
        boolean isModeChanged = appUiConfig.getScreenMode().isChanged();
        if (isWidthChanged && (mContentView != null) && (mContentView.getParent() != null)) {
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mContentView.getLayoutParams();
            layoutParams.width = ViewGroup.MarginLayoutParams.MATCH_PARENT;
            layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
            if (EditorUIConfig.isEditorLandscape(appUiConfig) || ScreenAdaptUtil.isMiddleAndLargeWindow(mContext)) {
                layoutParams.width = ScreenAdaptUtil.getViewEnableWidth(mContext, mContext.getResources().getDisplayMetrics().widthPixels);
            }

            if (isModeChanged) {
                /*
                 * Mark by DuanYibin: 待后续完全适配中大屏、横竖屏时再移除
                 * 中大屏、横竖屏的view本应要根据getContentLayoutId适配重构view，这个重构工作本应要在base里重构
                 * 但是澍文适配的时候没有这么适配，取的还是小屏竖屏的view，导致getContentLayoutId根据配置获取横屏下的布局不生效
                 * 现在在base里重构会导致其他业务异常，这里仅针对剪辑页进行重构
                 */
                int index = mContentContainer.indexOfChild(mContentView);
                if (index >= AppConstants.Number.NUMBER_0) {
                    mContentContainer.removeView(mContentView);

                    mContentView = mLayoutInflater.inflate(getContentLayoutId(appUiConfig), mContentContainer, false);
                    mContentContainer.addView(mContentView, layoutParams);

                    init();
                }
            } else {
                mContentView.requestLayout();
            }
        }
    }

    /**
     * 初始方法
     */
    private void init() {
        initHorizontalScrollView();
        initSubmenus();

        mLinearLayoutVolume = mContentContainer.findViewById(R.id.montage_volume_layout);
        mLinearLayoutRotation = mContentContainer.findViewById(R.id.montage_rotation_layout);

        mClipRotation = mContentContainer.findViewById(R.id.montage_rotation);
        mRotationTV = mContentContainer.findViewById(R.id.edit_montage_rotation_tx);

        mClipVolume = mContentContainer.findViewById(R.id.montage_volume);
        mVolumeTV = mContentContainer.findViewById(R.id.edit_montage_volume_tx);

        mLinearLayoutFilter = mContentContainer.findViewById(R.id.montage_filter_layout);
        mClipFilter = mContentContainer.findViewById(R.id.montage_filter);
        mFilterTV = mContentContainer.findViewById(R.id.edit_montage_filter_tx);

        mLinearLayoutVolume.setOnClickListener(this);
        mLinearLayoutRotation.setOnClickListener(this);

        mClipRotation.setOnClickListener(this);
        mClipVolume.setOnClickListener(this);
        mClipFilter.setOnClickListener(this);

        updateIconTextView();
        hideMenuItem();
    }

    /**
     * 初始菜单项容器
     */
    private void initHorizontalScrollView() {
        mHorizontalScrollView = mContentContainer.findViewById(R.id.videoeditor_trim_submenu_scroll_view);
        mHorizontalScrollView.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                mHandler.removeMessages(KEY_STATISTICS);
                mHandler.sendEmptyMessageDelayed(KEY_STATISTICS, EXPOSURE_TIME);
            }
        });

        mHorizontalScrollView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                mHorizontalScrollView.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                mHandler.sendEmptyMessageDelayed(KEY_STATISTICS, EXPOSURE_TIME);
            }
        });
    }

    /**
     * 初始化菜单项
     */
    private void initSubmenus() {
        // 分割
        mSegmentLayout = mContentContainer.findViewById(R.id.videoeditor_trim_segment_layout);
        mSegmentIcon = mContentContainer.findViewById(R.id.videoeditor_trim_segment_icon);
        mSegmentText = mContentContainer.findViewById(R.id.videoeditor_trim_segment_text);
        mSegmentLayout.setOnClickListener(this);
        // 替换
        mReplaceLayout = mContentContainer.findViewById(R.id.videoeditor_trim_replace_layout);
        mReplaceIcon = mContentContainer.findViewById(R.id.videoeditor_trim_replace_icon);
        mReplaceText = mContentContainer.findViewById(R.id.videoeditor_trim_replace_text);
        mReplaceLayout.setOnClickListener(this);
        // 删除
        mDeleteLayout = mContentContainer.findViewById(R.id.videoeditor_trim_delete_layout);
        mDeleteIcon = mContentContainer.findViewById(R.id.videoeditor_trim_delete_icon);
        mDeleteText = mContentContainer.findViewById(R.id.videoeditor_trim_delete_text);
        mDeleteLayout.setOnClickListener(this);
        // 裁剪旋转
        mTailorLayout = mContentContainer.findViewById(R.id.videoeditor_trim_tailor_layout);
        mTailorIcon = mContentContainer.findViewById(R.id.videoeditor_trim_tailor_icon);
        mTailorText = mContentContainer.findViewById(R.id.videoeditor_trim_tailor_text);
        mTailorLayout.setOnClickListener(this);
        // 变速
        mSpeedLayout = mContentContainer.findViewById(R.id.videoeditor_trim_speed_layout);
        mSpeedIcon = mContentContainer.findViewById(R.id.videoeditor_trim_speed_icon);
        mSpeedText = mContentContainer.findViewById(R.id.videoeditor_trim_speed_text);
        mSpeedLayout.setOnClickListener(this);
        // 排序
        mSortLayout = mContentContainer.findViewById(R.id.videoeditor_trim_sort_layout);
        mSortIcon = mContentContainer.findViewById(R.id.videoeditor_trim_sort_icon);
        mSortText = mContentContainer.findViewById(R.id.videoeditor_trim_sort_text);
        mSortLayout.setOnClickListener(this);
        updateMenuMargin();
    }

    /**
     * 隐藏不需要的菜单
     */
    private void hideMenuItem() {
        // 音乐音量
        mLinearLayoutVolume.setVisibility(GONE);
        mClipVolume.setVisibility(GONE);
        mVolumeTV.setVisibility(GONE);
        // 旋转
        mLinearLayoutRotation.setVisibility(GONE);
        mClipRotation.setVisibility(GONE);
        mRotationTV.setVisibility(GONE);
        // 滤镜
        mLinearLayoutFilter.setVisibility(GONE);
        mClipFilter.setVisibility(GONE);
        mFilterTV.setVisibility(GONE);
    }

    private void onMontageExposed() {
        JsonArray items = new JsonArray();
        JsonObject item = null;
        if (isVisible(mClipVolume)) {
            item = new JsonObject();
            item.addProperty(StatisticsConstant.Exposure.ITEM_TYPE, StatisticsConstant.Exposure.EDIT_TAB);
            item.addProperty(StatisticsConstant.Exposure.ITEM_ID, StatisticsConstant.ClipFunctionId.VOLUME);
            items.add(item);
        }
        if (isVisible(mClipFilter)) {
            item = new JsonObject();
            item.addProperty(StatisticsConstant.Exposure.ITEM_TYPE, StatisticsConstant.Exposure.EDIT_TAB);
            item.addProperty(StatisticsConstant.Exposure.ITEM_ID, StatisticsConstant.ClipFunctionId.FILTER);
            items.add(item);
        }
        if (isVisible(mClipRotation)) {
            item = new JsonObject();
            item.addProperty(StatisticsConstant.Exposure.ITEM_TYPE, StatisticsConstant.Exposure.EDIT_TAB);
            item.addProperty(StatisticsConstant.Exposure.ITEM_ID, StatisticsConstant.ClipFunctionId.ROTATE);
            items.add(item);
        }
        // marked by yeguangjin 需要check是否需要曝光
    }

    public void setCurrentClipAvFileInfo(Long duration, boolean isLeftHand) {
        //        mEditTimelineView.setClipFileDuration(duration, isLeftHand);
    }

    public void setScrollPressed(boolean scrollPressed) {
        //        if (mEditTimelineView != null) {
        //            mEditTimelineView.setScrollPressed(scrollPressed);
        //        }
    }

    @Override
    public void resetStateByCurrentTimeline(ITimeline timeline) {
        resetMuteByTimeline(timeline);
    }

    @Override
    public void resetMuteByTimeline(@NonNull ITimeline timeline) {
        IVideoTrack videoTrack = timeline.getVideoTrack(0);
        if (videoTrack == null) {
            return;
        }
        setMuteIconRes();

        boolean isTrackMuted = resetVideoTrackMuted(videoTrack);
        if (getTimelineViewModel() != null) {
            getTimelineViewModel().updateSilenceIcon(isTrackMuted);
        }
    }

    public String getTimelineState() {
        //        return mEditTimelineView.getTimelineState();
        return "";
    }

    public int getCurrentMultiSequenceSelectedIndex() {
        if (mSelectClip != null) {
            List<IVideoClip> clips = mEditorState.getCurrentTimelineClips();
            if (clips != null) {
                int index = clips.indexOf(mSelectClip);
                if (index < 0) {
                    index = 0;
                }
                return index;
            }
        }
        return -1;
        //        return mEditTimelineView.getSelectClipIndex();
    }

    public void setOnButtonClickListener(OnButtonClickListener listener) {
        mOnButtonClickListener = listener;
    }

    public void changeTimelineState(String state) {
        mState = state;
    }

    public void updateDeleteAndSortButtonStatus(boolean enabled) {
        setDeleteEnable(mSelectClip != null && enabled);
        setSortEnable(enabled);
    }

    public void updateButtonStatusForTailClip(boolean isTailClip) {
        setSegmentEnable(!isTailClip);
        setReplaceEnable(!isTailClip);
        setDeleteEnable(isTailClip);
        setTailorEnable(!isTailClip);
        setSpeedEnable(!isTailClip);
        setSortEnable(!isTailClip);

        if (mClipRotation != null) {
            mClipRotation.setEnabled(!isTailClip);
            mRotationTV.setEnabled(!isTailClip);
        }
        if (mLinearLayoutRotation != null) {
            mLinearLayoutRotation.setEnabled(!isTailClip);
        }

        if (mClipVolume != null) {
            mClipVolume.setEnabled(!isTailClip);
            mVolumeTV.setEnabled(!isTailClip);
        }

        if (mLinearLayoutVolume != null) {
            mLinearLayoutVolume.setEnabled(!isTailClip);
        }

        if (mClipFilter != null) {
            mClipFilter.setEnabled(!isTailClip);
            mFilterTV.setEnabled(!isTailClip);
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.editor_timeline_center_line_up) {
            if (mOnThumbnailListener != null) {
                mOnThumbnailListener.onClipThumbnailClick(getCurrentMultiSequenceSelectedIndex());
            }
            return;
        }
        if (ClickUtil.isDoubleClick()) {
            GLog.w(TAG, "onClick() isDoubleClick v = " + v);
            return;
        }

        if (getTimelineViewModel() != null) {
            getTimelineViewModel().stopFling();
        }
        if (mOnButtonClickListener != null) {
            mOnButtonClickListener.onButtonClick(v);
        }
    }

    @Override
    public void onClipAdjustFinished(@NonNull ClipViewWrapper clipViewWrapper, long timeAdjusted, long position, boolean isTail) {
        ClipModel clipModel = clipViewWrapper.getRelativeData();
        if (clipModel == null) {
            return;
        }

        if (clipModel.getIsInMainTrack()) {
            /*
             * 剪辑业务下，调节视频片段后，可能会出现调节后的时码线不在当前调节片段的范围内，
             *     由于前面onClipAdjusting中调节时会实时seek，调节后会刷新时间线，导致从时间线读取的当前位置不在当前片段内，出现无法选中当前片段的情况。
             * 因此，在调节松手后，需主动seek到当前调节片段范围内，以便在更新时间线时，读取的当前时间线位置时准确无误的。
             */
            if ((getOnScrollListener() != null)) {
                long currentPosition = mEditorState.getEditorEngine().getTimelineCurrentPosition();
                if ((currentPosition < clipModel.getInPoint()) || (currentPosition > clipModel.getAdjustedOutPoint())) {
                    long offset = TrackHelper.getDurationByLength(AppConstants.Number.NUMBER_1);
                    long clipPoint = isTail ? clipModel.getAdjustedOutPoint() - offset : clipModel.getInPoint() + offset;
                    getOnScrollListener().onScrolling(clipPoint, false);
                }
            }

            if (mEditorState instanceof EditorTrimState) {
                long trim = isTail ? clipModel.getTrimOut() : clipModel.getTrimIn();
                if ((clipModel.getType() == ClipModel.ClipType.CLIP_PICTURE) && !clipModel.getIsOlivePhoto()) {
                    if (isTail) {
                        trim = clipModel.getClip().getTrimOut() + timeAdjusted;
                    } else {
                        trim = clipModel.getClip().getTrimOut() - timeAdjusted;
                    }
                }
                ((EditorTrimState) mEditorState).finishClipAdjust(clipViewWrapper.getClipInTrackIndex(), !isTail, trim);
            }
        }
        mClipAdjustedPosition = clipModel.getIsInMainTrack() ? position : AppConstants.Number.NUMBER_MINUS_1;
        mIsInEditorMode = false;
    }

    @Override
    public void onTrackUpdateFinished(long position) {
        super.onTrackUpdateFinished(position);

        if (getTimelineViewModel() == null) {
            return;
        }

        ClipModel clipModel = getTimelineViewModel().getClipModel(AppConstants.Number.NUMBER_0, position);
        if ((clipModel != null) && (clipModel.getClip() != null) && (clipModel.getClip() instanceof IVideoClip)) {
            mSelectClip = (IVideoClip) clipModel.getClip();
        }
        setFunctionEnable(position);
        // 如果首次更新完轨道且路由参数是强选中的标识则强选中片段
        if (mIsInitUpdate) {
            boolean isValidParams = (mEditorState.getRouteParams() != null)
                && (mEditorState.getRouteParams() instanceof String)
                && (mEditorState.getRouteParams() == EditorBaseState.CLIP_STRONG_STATE_FLAG);
            boolean isValidClip = (getTimelineViewModel() != null) && (clipModel != null);
            if (isValidParams && isValidClip) {
                ClipViewWrapper clipViewWrapper = getTimelineViewModel().getClipViewWrapper(clipModel);
                if (clipViewWrapper != null) {
                    clipViewWrapper.setSelectState(ClipSelectType.STRONG, false);
                }
            }
            mIsInitUpdate = false;
        }

        initSelectedValidEmptyRange();
        // 拖动把手结束后，根据条件滚动到指定位置
        if ((getTimelineViewModel() != null) && (mClipAdjustedPosition != AppConstants.Number.NUMBER_MINUS_1)) {
            getTimelineViewModel().layoutScrollToXPosition(mClipAdjustedPosition);
            mClipAdjustedPosition = AppConstants.Number.NUMBER_MINUS_1;
        }
    }

    @Override
    public void onScrolling(int scrollX, @NonNull ScrollMode mode, boolean isRollback) {
        super.onScrolling(scrollX, mode, isRollback);
        updateHasHasEnoughEmptyRange();
        boolean needSeek = (mode != ScrollMode.PLAY) && (mode != ScrollMode.SCALE) && (mode != ScrollMode.SMOOTH);
        if (!needSeek) {
            return;
        }

        long timeScrolled = TrackHelper.getDurationByLength(scrollX);
        if (getTimelineViewModel() != null) {
            int clipIndex = AppConstants.Number.NUMBER_MINUS_1;
            ClipViewWrapper clipViewWrapper = getTimelineViewModel().getClipViewWrapper(AppConstants.Number.NUMBER_0, timeScrolled);
            if ((clipViewWrapper != null) && clipViewWrapper.isSelected()) {
                clipIndex = getTimelineViewModel().setSelectedMainTrack(timeScrolled);
            } else {
                clipIndex = getTimelineViewModel().setSubSelectedMainTrack(timeScrolled);
            }
            EditorEngine editorEngine = mEditorState.getEditorEngine();
            if (editorEngine == null) {
                return;
            }
            ITimeline timeline = editorEngine.getCurrentTimeline();
            if (timeline == null) {
                return;
            }
            IVideoTrack videoTrack = timeline.getVideoTrack(0);
            if (videoTrack == null) {
                return;
            }
            IVideoClip videoClip = (IVideoClip) videoTrack.getClip(clipIndex);
            if (videoClip == null) {
                return;
            }
            mSelectClip = videoClip;
        }
    }

    /**
     * 初始化选中范围的可用性
     */
    private void initSelectedValidEmptyRange() {
        mEmptyRanges.add(new EffectRange(0, mEditorState.getEditorEngine().getTimelineDuration()));
        updateHasHasEnoughEmptyRange();
    }

    /**
     * 更新 isCurrentTimeHasEnoughEmptyRange 值
     * true 前时间（即时码线所在位置） 有足够的空白区域
     * false 空间不足
     */
    private void updateHasHasEnoughEmptyRange() {
        setCurrentTimeHasEnoughEmptyRange(EffectTrackRangeHelper.INSTANCE.isCurrentTimeHasEnoughEmptyRange(
                mEditorState.getEditorEngine().getTimelineCurrentPosition(),
                mEmptyRanges)
        );
    }

    @Override
    public void onBegin() {
        super.onBegin();
    }

    @Override
    public boolean onBlankAreaClick() {
        if (mEditorState instanceof EditorTrimState) {
            ((EditorTrimState) mEditorState).onPanelClick();
        }
        IVideoClip videoClip = getCurrentClip();
        if ((getTimelineViewModel() != null) && (videoClip != null)) {
            getTimelineViewModel().performClipSelected(videoClip.getTrackIndex(), videoClip.getInPoint(), false);
        }
        return true;
    }

    /** OnSelectListener选中片段监听方法 */
    @Override
    public void onSelected(ClipViewWrapper view, boolean selected, ClipModel clip, boolean withAnim) {
        if (view == null) {
            GLog.e(TAG, LogFlag.DL, "[onSelected] clipViewWrapper is null");
            return;
        }
        if (clip == null) {
            GLog.e(TAG, LogFlag.DL, "[onSelected] clip is null");
            return;
        }
        if (getTimelineViewModel() == null) {
            GLog.e(TAG, LogFlag.DL, "[onSelected] timelineViewModel is null");
            return;
        }
        //点击音乐轨道
        if ((view.getClipViewDraw() instanceof MusicClipView) && selected) {
            mOnEffectTrackSelectListener.onGotoMusic();
            return;
        }
        // 点击文字轨道
        if ((view.getRelativeData().getType() == ClipModel.ClipType.CLIP_TEXT) && selected) {
            mOnEffectTrackSelectListener.onGotoCaption();
            return;
        }
        // 点击视频轨道片段
        if (!clip.getIsInMainTrack()) {
            GLog.e(TAG, LogFlag.DL, "[onSelected] clip is not video");
            return;
        }
        mSelectClip = (IVideoClip) clip.getClip();
        if (selected) {
            if (clip.getInPoint() > mEditorState.getEditorEngine().getTimelineCurrentPosition()) {
                if (mEditorState instanceof EditorTrimState) {
                    ((EditorTrimState) mEditorState).seekToPosition(clip.getInPoint() + TimelineViewModel.ADJUST);
                }
            } else if (clip.getInPoint() + clip.getAdjustedDuration() < mEditorState.getEditorEngine().getTimelineCurrentPosition()) {
                if (mEditorState instanceof EditorTrimState) {
                    ((EditorTrimState) mEditorState).seekToPosition(clip.getInPoint() + clip.getAdjustedDuration() - TimelineViewModel.ADJUST);
                }
            }
        }
        setFunctionEnable(mEditorState.getEditorEngine().getTimelineCurrentPosition());
        if (selected) {
            getTimelineViewModel().selectClip(clip.getTrackIndex(), clip.getInPoint(), true, withAnim);
        } else {
            getTimelineViewModel().setSubSelectedMainTrack(clip.getInPoint(), false, withAnim);
        }
    }

    @Override
    public void onLongClick(ClipViewWrapper view, ClipModel clip) {
        if (mSortLayout.isEnabled()) {
            // 进入二级页面时剪辑不会销毁，避免从剪辑进入的二级页面也响应长按事件
            if (!(getEditorStateContext().getCurrentEditorState() instanceof EditorTrimState)) {
                return;
            }
            Vibrator vibrator = (Vibrator) ContextGetter.context.getSystemService(Service.VIBRATOR_SERVICE);
            if (vibrator.hasVibrator()) {
                vibrator.vibrate(VibrationEffect.createOneShot(VIBRATE_TIME, VibrationEffect.DEFAULT_AMPLITUDE));
            }
            mSortLayout.performClick();
        }
    }

    @Override
    public void onTransitionIconClick(int clipIndex, boolean isTail) {
        super.onTransitionIconClick(clipIndex, isTail);

        if (mEditorState == null) {
            GLog.e(TAG, LogFlag.DL, "[onTransitionIconClick] mEditorState is null");
            return;
        }

        if (mEditorState.getEditorEngine() == null) {
            GLog.e(TAG, LogFlag.DL, "[onTransitionIconClick] editorEngine is null");
            return;
        }

        if (mEditorState.getEditorEngine().getCurrentTimeline() == null) {
            GLog.e(TAG, LogFlag.DL, "[onTransitionIconClick] timeline is null");
            return;
        }

        ITimeline timeline = mEditorState.getEditorEngine().getCurrentTimeline();
        IVideoTrack videoTrack = timeline.getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, LogFlag.DL, "[onTransitionIconClick] videoTrack is null");
            return;
        }

        /*
         * 判断视频是否过短，过短则不添加转场
         *
         * 转场按钮在两个片段的中间，过渡需要取两个片段
         * 1、如果点击的是左侧转场按钮，需要取前一个片段和当前片段做过渡
         * 2、如果点击的是右侧转场按钮，需要取当前片段和后一个片段做过渡
         */
        IClip beforeClip = videoTrack.getClip(isTail ? clipIndex : clipIndex - 1);
        IClip afterClip = videoTrack.getClip(isTail ? clipIndex + 1 : clipIndex);
        boolean beforeClipIsOutOfTime = (beforeClip != null) && (beforeClip.getDuration() < TimelineViewModel.TRANSLATION_CARTOON_TIME_LIMIT);
        boolean afterClipIsOutOfTime = (afterClip != null) && (afterClip.getDuration() < TimelineViewModel.TRANSLATION_CARTOON_TIME_LIMIT);
        if (beforeClipIsOutOfTime || afterClipIsOutOfTime) {
            ToastUtil.showShortToast(R.string.videoeditor_transition_video_clip_too_short);
            GLog.e(TAG, LogFlag.DL, "[onTransitionIconClick] Video too short. Transition effect cannot be added");
            return;
        }

        if (mOnThumbnailListener != null) {
            mOnThumbnailListener.onTransitionClick(clipIndex, isTail);
        }

        updateClipTransitionBorder(clipIndex, isTail);
    }

    /**
     * 更新片段按钮的边框
     *
     * @param clickedClipIndex 点击的片段
     * @param isTail           是否点击的是片段的右侧转场按钮
     */
    public void updateClipTransitionBorder(int clickedClipIndex, boolean isTail) {
        mTrackView.updateVideoClipTransitionBorder(clickedClipIndex, isTail);
    }

    @Override
    public void onUiConfigChanged(@NonNull BaseActivity baseActivity, int i, @NonNull EditorAppUiConfig editorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, i, editorAppUiConfig);
        updateMenuMargin();
    }

    /**
     * 更新二级菜单的左右边距。
     */
    private void updateMenuMargin() {
        int margin = getSecondMenuMarginByWindow() / MathUtils.TWO;
        if ((mSegmentLayout == null)
                || (mReplaceLayout == null)
                || (mDeleteLayout == null)
                || (mTailorLayout == null)
                || (mSpeedLayout == null)
        ) {
            GLog.e(TAG, LogFlag.DL, "[updateMenuMargin] menuLayout is null");
            return;
        }
        ViewExtKt.setStartAndEndMargin(mSegmentLayout, margin);
        ViewExtKt.setStartAndEndMargin(mReplaceLayout, margin);
        ViewExtKt.setStartAndEndMargin(mDeleteLayout, margin);
        ViewExtKt.setStartAndEndMargin(mTailorLayout, margin);
        ViewExtKt.setStartAndEndMargin(mSpeedLayout, margin);
        if (mSortLayout != null) {
            ViewExtKt.setStartAndEndMargin(mSortLayout, margin);
        }
    }

    public interface OnThumbnailListener {
        void onClipThumbnailClick(int clipIndex);

        void onClipLongPress(int clipIndex);

        /**
         * 转场按钮点击
         *
         * @param clipIndex 点击的片段
         * @param isTail    点击的是否为片段右侧的转场按钮
         */
        void onTransitionClick(int clipIndex, boolean isTail);

        void onScrollChanged(long stamp);

        void onActionUp();

        void onSelectClipIndexChange(int index);
    }

    public interface OnButtonClickListener {
        void onButtonClick(View view);
    }

    public interface OnPanelClickListener {
        /**
         * parent panel click function
         */
        void onPanelClick();
    }

    /**
     * 定义了一个接口，用于监听音乐、文字等轨道选择事件。
     */
    public interface OnEffectTrackSelectListener {

        /**
         * 当用户选择音乐轨道时调用此方法。
         * 该方法通常用于导航到音乐播放界面或执行与音乐相关的操作。
         */
        void onGotoMusic();

        /**
         * 当用户在剪辑页选择文字轨道时调用此方法跳转到文字页面
         */
        void onGotoCaption();
    }

    @Override
    public void destroyView() {
        super.destroyView();
    }
}