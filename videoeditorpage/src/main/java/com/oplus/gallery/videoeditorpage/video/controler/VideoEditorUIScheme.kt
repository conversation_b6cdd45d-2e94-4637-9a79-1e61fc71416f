/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorUIScheme.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/4/7
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON><PERSON>@Apps.Gallery3D      2025/04/07    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.controler

import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
import android.content.res.Configuration
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.RelativeLayout
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.ui.EditorUIExecutor
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.ui.systembar.OnSystemBarChangeListener
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.controler.IVideoEditorUIScheme
import com.oplus.gallery.videoeditorpage.utlis.onSizeChanged
import com.oplus.gallery.videoeditorpage.utlis.removeSizeChangeListener
import com.oplus.gallery.videoeditorpage.video.business.track.view.TrackLayout
import com.oplus.gallery.videoeditorpage.widget.ResolutionTitleLayout

/**
 * 视频编辑UI框架规则，定义各个容器的位置和多屏的适配规则
 *
 * @param rootView 根布局
 * @param activity 视频编辑的Activity
 */
class VideoEditorUIScheme(
    private val rootView: ViewGroup,
    private val activity: BaseActivity
) : IVideoEditorUIScheme {

    /**
     * 顶部操作栏，保存、取消的容器
     */
    private val actionContainer by lazy {
        rootView.findViewById<RelativeLayout>(R.id.action_bar)
    }

    /**
     * 分辨率按钮
     */
    private val resolutionView by lazy {
        actionContainer.findViewById<ResolutionTitleLayout>(R.id.ll_action_bar_adjust)
    }

    /**
     * 播控区
     */
    private val playController by lazy {
        rootView.findViewById<RelativeLayout>(R.id.ll_preview_icon_layout)
    }

    private val minPreviewHeightShowPlayControl by lazy {
        activity.resources.getDimensionPixelSize(R.dimen.videoeditor_min_preview_height_show_play_control)
    }

    /**
     * 播控区
     */
    private val previewArea: View by lazy {
        rootView.findViewById<RelativeLayout>(R.id.engine_preview_layout)
    }

    private var previewAreaSizeListener: View.OnLayoutChangeListener? = null

    /**
     * 菜单容器
     */
    private val menuContainer by lazy {
        rootView.findViewById<RelativeLayout>(R.id.menu_container)
    }

    /**
     * 底部安全间距控件，用来适配导航栏
     */
    private val bottomSafeView by lazy {
        rootView.findViewById<View>(R.id.bottom_safe)
    }

    /**
     * 二级菜单容器
     */
    private val subMenuContainer by lazy {
        rootView.findViewById<ViewGroup>(R.id.sub_menu_container)
    }

    /**
     * 轨道控件
     */
    private val trackLayout by lazy {
        rootView.findViewById<TrackLayout>(R.id.editor_track_layout)
    }

    /**
     * 浮层容器
     */
    private val overlapContainer by lazy {
        rootView.findViewById<ViewGroup>(R.id.overlap_container)
    }

    private val editorAppUiConfig by lazy {
        EditorAppUiConfig(activity.getCurrentAppUiConfig(), false)
    }

    /**
     * 平板横屏的边距
     */
    private val tableMarginHorizontal = activity.resources
        .getDimensionPixelSize(R.dimen.videoeditor_main_horizontal_margin_table)

    /**
     * 小屏和中屏的边距
     */
    private val normalMarginHorizontal = activity.resources
        .getDimensionPixelSize(R.dimen.videoeditor_main_horizontal_margin_normal)

    /**
     * 分辨率文本的右边距
     */
    private val actionBarTextMarginEnd = activity.resources
        .getDimensionPixelSize(R.dimen.videoeditor_action_bar_button_text_margin_end)

    /**
     * UI规则的执行者
     */
    private val uiExecutor = EditorUIExecutor()

    /**
     * 系统栏变化监听器
     */
    private val systemBarChangeListener: OnSystemBarChangeListener = object : OnSystemBarChangeListener {
        override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
            adaptSysBarMargin()
        }
    }

    /**
     * 初始化
     */
    init {
        val viewStub = rootView.findViewById<ViewStub>(R.id.video_editor_container_view_stub)
        if (viewStub != null) {
            viewStub.layoutResource = getContentLayoutId(activity.getCurrentAppUiConfig())
            viewStub.inflate()
        }
        uiExecutor.init(activity)
        uiExecutor.setScheme(this@VideoEditorUIScheme, null)
        editorAppUiConfig.appUiConfig = activity.getCurrentAppUiConfig()
        uiExecutor.checkUpdateLayout(editorAppUiConfig)
        activity.registerSystemBarChangeListener(systemBarChangeListener)
        activity.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> release()

                    else -> Unit
                }
            }
        })
        previewAreaSizeListener = previewArea.onSizeChanged(::onPreviewSizeChanged)
    }

    /**
     * 获取UI框架的布局ID
     */
    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        return when {
            isLandscape -> R.layout.videoeditor_main_landscape
            else -> R.layout.videoeditor_main_layout
        }
    }

    /**
     * 当布局发生变化时，哪些view需要动态调整params，就将ID定义在这里返回
     */
    override fun getReconfigureViewIds(): List<Int> {
        return listOf(
            R.id.main_control_view,
            R.id.action_bar,
            R.id.engine_preview_layout,
            R.id.operation_layout,
            R.id.ll_preview_icon_layout,
            R.id.menu_container,
            R.id.sub_menu_container,
            R.id.overlap_container,
            R.id.editor_track_layout,
            R.id.play_seekbar,
            R.id.save_layout
        )
    }

    override fun getRootView(): ViewGroup? {
        return rootView
    }

    override fun onUiConfigChanged(
        baseActivity: BaseActivity,
        layoutId: Int,
        config: EditorAppUiConfig
    ) {
        adaptSysBarMargin()
        updateBaseUiLayout(baseActivity, config)
    }

    private fun onPreviewSizeChanged(newWidth: Int, newHeight: Int) {
        playController.visibility = if (newHeight < minPreviewHeightShowPlayControl) View.INVISIBLE else View.VISIBLE
    }

    /**
     * 切换横竖屏后更新公共布局，
     * 需重新设置菜单的宽度
     * 平板横屏需重新设置分辨率的位置
     * 平板横屏需重新设置顶部actionBar、播控区边距
     */
    private fun updateBaseUiLayout(baseActivity: BaseActivity, config: EditorAppUiConfig) {
        // 更新分辨率位置
        updateResolutionView(config)
        // 更新公共控件边距
        updateMargin(config)
    }

    /**
     * 更新分辨率按钮位置
     */
    private fun updateResolutionView(config: EditorAppUiConfig) {
        val resolutionLp = resolutionView.layoutParams as RelativeLayout.LayoutParams
        if (isTableLandscape(config)) {
            resolutionLp.apply {
                removeRule(RelativeLayout.END_OF)
                addRule(RelativeLayout.START_OF, R.id.action_bar_done)
                marginEnd = actionBarTextMarginEnd
            }
        } else {
            // 这里是直板机和中屏的设置，这两是一样的
            resolutionLp.apply {
                addRule(RelativeLayout.END_OF, R.id.action_bar_back)
                addRule(RelativeLayout.START_OF, R.id.action_bar_done)
                marginEnd = 0
            }
        }
    }

    /**
     * 更新边距
     */
    private fun updateMargin(config: EditorAppUiConfig) {
        val margin = if (isTableLandscape(config)) tableMarginHorizontal else normalMarginHorizontal
        changeTableLayoutParams(actionContainer, margin)
        changeTableLayoutParams(playController, margin)
    }

    /**
     * 设置view的边距
     */
    private fun changeTableLayoutParams(view: View, margin: Int) {
        (view.layoutParams as? RelativeLayout.LayoutParams)?.apply {
            marginStart = margin
            marginEnd = margin
            view.requestLayout()
        }
    }

    /**
     * 通知UI配置发生了变化
     *
     * @param config UI配置
     */
    override fun notifyAppUiConfigChange(config: AppUiResponder.AppUiConfig) {
        editorAppUiConfig.appUiConfig = config
        uiExecutor.onAppUiConfigChanged(editorAppUiConfig)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        //do nothing
    }

    /**
     * 适配系统导航栏和状态栏间距
     */
    private fun adaptSysBarMargin() {
        (actionContainer.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.topMargin = activity.resources.getDimensionPixelSize(R.dimen.dp_31)
            actionContainer.requestLayout()
        }
        (bottomSafeView.layoutParams as? RelativeLayout.LayoutParams)?.let {
            it.height = activity.bottomNaviBarHeight()
            bottomSafeView.requestLayout()
        }
    }

    /**
     * 判断是否平板横屏
     * marked by kangshuwen 这里发现平板第一次进来一定是 TABLET_LANDSCAPE ，导致竖屏时使用的横屏布局
     * 加上 orientation 的判断来保证准确性
     */
    private fun isTableLandscape(config: EditorAppUiConfig): Boolean {
        return (config.appUiConfig.orientation.current != SCREEN_ORIENTATION_PORTRAIT)
                && (WindowSizeForm.getSizeForm(config.appUiConfig) == WindowSizeForm.TABLET_LANDSCAPE)
    }

    /**
     * 释放、反注册、销毁相关资源
     */
    private fun release() {
        previewAreaSizeListener?.let { previewArea.removeSizeChangeListener(it) }
        activity.unregisterSystemBarChangeListener(systemBarChangeListener)
    }

    companion object {
        private const val TAG = "VideoEditorUIScheme"
    }
}