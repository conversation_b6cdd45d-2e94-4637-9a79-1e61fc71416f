/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - SdkCallbackInterface.java
 ** Description: sdk callback interface.
 ** Version: 1.0
 ** Date : 2017/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/13    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;

public class SdkCallbackInterface {

    private SDKCallback mSDKCallback;

    private NvsStreamingContext.CompileCallback mCompileCallback = new NvsStreamingContext.CompileCallback() {
        @Override
        public void onCompileProgress(NvsTimeline nvsTimeline, int position) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onCompileProgress(nvsTimeline, position);
            }
        }

        @Override
        public void onCompileFinished(NvsTimeline nvsTimeline) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onCompileFinished(nvsTimeline);
            }
        }

        @Override
        public void onCompileFailed(NvsTimeline nvsTimeline) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onCompileFailed(nvsTimeline);
            }
        }
    };

    private NvsStreamingContext.CompileCallback2 mCompileCallback2 = new NvsStreamingContext.CompileCallback2() {

        @Override
        public void onCompileCompleted(NvsTimeline nvsTimeline, boolean isCanceled) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onCompileCompleted(nvsTimeline, isCanceled);
            }
        }
    };

    private NvsStreamingContext.PlaybackCallback mPlaybackCallback = new NvsStreamingContext.PlaybackCallback() {

        @Override
        public void onPlaybackPreloadingCompletion(NvsTimeline nvsTimeline) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onPlaybackPreloadingCompletion(nvsTimeline);
            }
        }

        @Override
        public void onPlaybackStopped(NvsTimeline nvsTimeline) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onPlaybackStopped(nvsTimeline);
            }
        }

        @Override
        public void onPlaybackEOF(NvsTimeline nvsTimeline) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onPlaybackEOF(nvsTimeline);
            }
        }
    };

    private NvsStreamingContext.PlaybackCallback2 mPlaybackCallback2 = new NvsStreamingContext.PlaybackCallback2() {

        @Override
        public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long position) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onPlaybackTimelinePosition(nvsTimeline, position);
            }
        }
    };

    private NvsStreamingContext.StreamingEngineCallback mStreamingEngineCallback = new NvsStreamingContext.StreamingEngineCallback() {

        @Override
        public void onStreamingEngineStateChanged(int state) {
            // !!! Do not use this method, use onStreamingEngineStateChangedWithTimeline instead it.
        }

        @Override
        public void onFirstVideoFramePresented(NvsTimeline nvsTimeline) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onFirstVideoFramePresented(nvsTimeline);
            }
        }
    };

    private NvsStreamingContext.StreamingEngineCallback2 mStreamingEngineCallback2 = new NvsStreamingContext.StreamingEngineCallback2() {

        @Override
        public void onStreamingEngineStateChangedWithTimeline(NvsTimeline nvsTimeline, int state) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onStreamingEngineStateChanged(nvsTimeline, state);
            }
        }

    };

    private NvsStreamingContext.PlaybackExceptionCallback mExceptionCallback = new NvsStreamingContext.PlaybackExceptionCallback() {

        @Override
        public void onPlaybackException(NvsTimeline nvsTimeline, int errCode, String msg) {
            final SDKCallback sdkCallback = mSDKCallback;
            if (sdkCallback != null) {
                sdkCallback.onPlaybackException(nvsTimeline, errCode, msg);
            }
        }
    };

    public SdkCallbackInterface(SDKCallback callback) {
        mSDKCallback = callback;
    }

    public interface SDKCallback {
        /**
         * NvsStreamingContext.CompileCallback
         * @param nvsTimeline
         * @param position
         */
        void onCompileProgress(NvsTimeline nvsTimeline, int position);

        void onCompileFinished(NvsTimeline nvsTimeline);

        void onCompileFailed(NvsTimeline nvsTimeline);

        /**
         * NvsStreamingContext.CompileCallback2
         * @param nvsTimeline
         * @param isCanceled
         */
        void onCompileCompleted(NvsTimeline nvsTimeline, boolean isCanceled);

        /**
         * NvsStreamingContext.PlaybackCallback
         * @param nvsTimeline
         */
        void onPlaybackPreloadingCompletion(NvsTimeline nvsTimeline);

        void onPlaybackStopped(NvsTimeline nvsTimeline);

        void onPlaybackEOF(NvsTimeline nvsTimeline);

        /**
         * NvsStreamingContext.PlaybackCallback2
         * @param timeline
         * @param position
         */
        void onPlaybackTimelinePosition(NvsTimeline timeline, long position);

        /**
         * NvsStreamingContext.StreamingEngineCallback1&2
         * @param timeline
         * @param state
         */
        void onStreamingEngineStateChanged(NvsTimeline timeline, int state);

        void onFirstVideoFramePresented(NvsTimeline nvsTimeline);

        /**
         * NvsStreamingContext.PlaybackExceptionCallback
         * @param nvsTimeline
         * @param errCode
         * @param msg
         */
        void onPlaybackException(NvsTimeline nvsTimeline, int errCode, String msg);
    }

    public NvsStreamingContext.CompileCallback getCompileCallback() {
        return mCompileCallback;
    }

    public NvsStreamingContext.CompileCallback2 getCompileCallback2() {
        return mCompileCallback2;
    }

    public NvsStreamingContext.PlaybackCallback getPlaybackCallback() {
        return mPlaybackCallback;
    }

    public NvsStreamingContext.PlaybackCallback2 getPlaybackCallback2() {
        return mPlaybackCallback2;
    }

    public NvsStreamingContext.StreamingEngineCallback getStreamingEngineCallback() {
        return mStreamingEngineCallback;
    }

    public NvsStreamingContext.PlaybackExceptionCallback getExceptionCallback() {
        return mExceptionCallback;
    }

    public void removeSdkCallback() {
        mSDKCallback = null;
    }
}
