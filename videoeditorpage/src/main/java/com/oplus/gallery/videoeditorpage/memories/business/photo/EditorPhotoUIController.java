/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorPhotoUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/21
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/21    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.photo;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.foundation.ui.helper.RippleHelper;
import com.oplus.gallery.videoeditorpage.memories.base.EditorMemoriesBaseUiController;
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig;
import com.oplus.gallery.videoeditorpage.memories.ui.adapter.PhotoMenuDataAdapter;
import com.oplus.gallery.videoeditorpage.resource.room.bean.PhotoItem;

import java.util.ArrayList;
import java.util.List;

public class EditorPhotoUIController extends EditorMemoriesBaseUiController {
    private static final String TAG = "EditorPhotoUIController";
    private LinearLayout mMenuContentView;
    private LinearLayout mActionButtonViewGroup;

    public EditorPhotoUIController(Context context, ViewGroup rootView, EditorBaseState state) {
        super(context, rootView, state);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_memories_editor_sub_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_memories_editor_photo_menu_layout;
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_text_preview_editor_photo;
    }

    @Override
    public void onShow() {
        hidePlayButtonTimeContainer();
        setButtonClickListener(mContainer);
        setMenuListView(mContainer.findViewById(R.id.photo_horizontal_list));
        getMenuListView().setItemViewCacheSize(MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MAX);
        List<PhotoItem> data = initData();
        mAdapter = new PhotoMenuDataAdapter(mContext, data);
        mAdapter.setHasStableIds(true);
        mAdapter.setCanUnselectCurrentPosition(false);
        mAdapter.select(0);
        mAdapter.setItemClickListener(this);
        getMenuListView().setAdapter(mAdapter);
        getMenuListView().addOnScrollListener(mOnScrollListener);
        getMenuListView().setKeepFocusItemPosition(0);
        super.onShow();
    }

    @Override
    public void onItemClick(View v, int position, Object item) {
        super.onItemClick(v, position, item);
        getMenuListView().setKeepFocusItemPosition(position);
    }

    private List<PhotoItem> initData() {
        ArrayList<PhotoItem> data = new ArrayList<>();

        ArrayList<String> curVideoList = null;
        if (mEditorState != null) {
            curVideoList = mEditorState.getEngineManager().getThemeVideoClipList();
        }
        GLog.d(TAG, "show curVideoList.size = " + ((curVideoList != null) ? curVideoList.size() : null));

        if ((curVideoList != null) && !curVideoList.isEmpty()) {
            int size = curVideoList.size();
            String filePath = TextUtil.EMPTY_STRING;
            for (int i = 0; i < size; i++) {
                filePath = curVideoList.get(i);
                if (!TextUtils.isEmpty(filePath)) {
                    data.add(new PhotoItem(
                            -1, true, true, false, filePath));
                }
            }
        }

        return data;
    }

    private void setButtonClickListener(View view) {
        ImageButton imgBtnAdd = view.findViewById(R.id.editor_img_action_left);
        RippleHelper.setRippleBackgroundBorderless(imgBtnAdd);
        ImageButton imgBtnDelete = view.findViewById(R.id.editor_img_action_right);
        RippleHelper.setRippleBackgroundBorderless(imgBtnDelete);
        if (imgBtnAdd != null) {
            imgBtnAdd.setOnClickListener(this);
        }
        if (imgBtnDelete != null) {
            imgBtnDelete.setOnClickListener(this);
        }
    }

    private RecyclerView.OnScrollListener mOnScrollListener = new RecyclerView.OnScrollListener() {
        @Override
        public void onScrolled(RecyclerView recyclerView, int i, int i1) {
            super.onScrolled(recyclerView, i, i1);
            /*
            滑动时已选图片已经滑到列表外，就选择中间的为当前展示图片
            listView的记录位置功能，在布局变化时恢复选择点，也会触发滑动，应用的滑动需要跳过这个逻辑
            */
            if (!getMenuListView().isScrollingFromUser()) {
                return;
            }
            if (mAdapter != null) {
                final int curPos = mAdapter.getSelectedPosition();
                final int centerPos = getMenuListView().getCenterVisiblePosition();
                GLog.d(TAG, "onScrolled curPos = " + curPos + ", centerPos = " + centerPos);
                if (!getMenuListView().isItemVisible(curPos)) {
                    mAdapter.select(centerPos);
                    getMenuListView().setKeepFocusItemPosition(centerPos);
                    if (mEditorState != null) {
                        mEditorState.getEngineManager().seekToThemePosition(centerPos);
                    }
                }
            }
        }
    };


    @Override
    public void adaptToolBarContainer(int layoutId, @NonNull View view, @NonNull AppUiResponder.AppUiConfig config, @NonNull BaseActivity context) {
        boolean isLandscape = isLandscapeLayout(config);
        RelativeLayout toolBarContainer = (RelativeLayout) mToolBarMenuContainer;
        toolBarContainer.setGravity(isLandscape ? Gravity.RIGHT | Gravity.CENTER_VERTICAL : Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL);
        int paddingEndLandscape = context.getResources().getDimensionPixelOffset(
                R.dimen.memories_editor_photo_toolbar_container_padding_end_landscape);
        int paddingStartLandscape = context.getResources().getDimensionPixelOffset(
                R.dimen.memories_editor_photo_toolbar_container_padding_start_landscape);
        int paddingBottom = context.getResources().getDimensionPixelOffset(R.dimen.memories_editor_photo_toolbar_container_padding_bottom);
        toolBarContainer.setPadding(isLandscape ? paddingStartLandscape : 0, 0,
                isLandscape ? paddingEndLandscape : 0, isLandscape ? 0 : paddingBottom);
        if (isLandscape) {
            ViewGroup.LayoutParams ly = mToolBarMenuContainer.getLayoutParams();
            ly.width = mContext.getResources().getDimensionPixelOffset(R.dimen.video_editor_photo_toolbar_width_landscape);
            mToolBarMenuContainer.setLayoutParams(ly);
        }
        adaptMenuView(config, context);
    }

    private void adaptMenuView(@NonNull AppUiResponder.AppUiConfig config, @NonNull BaseActivity context) {
        if (mMenuContentView == null) {
            mMenuContentView = mContainer.findViewById(R.id.menu_layout);
        }
        mContainer.post(() -> {
            if (context.isFinishing() || context.isDestroyed()) {
                GLog.d(TAG, "adaptMenuView, UI controller has been destroyed, cancel perform post runnable");
                return;
            }
            boolean isLandscape = isLandscapeLayout(config);
            if (mMenuContentView != null) {
                int viewSize = config.getWindowWidth().getCurrent();
                int titleBarHeight = context.getResources().getDimensionPixelOffset(R.dimen.video_editor_titlebar_height);
                if (isLandscape) {
                    viewSize = config.getWindowHeight().getCurrent() - titleBarHeight - context.bottomNaviBarHeight(false);
                }
                viewSize = ScreenUtils.pixelToDp(viewSize);
                int toolBarContentSize = ScreenUtils.dpToPixel(VideoEditorUIConfig.getToolBarContentSize(viewSize, isLandscape));

                int width = toolBarContentSize;
                int height = ViewGroup.LayoutParams.WRAP_CONTENT;
                int gravity = Gravity.CENTER_HORIZONTAL;
                int orientation = LinearLayout.VERTICAL;
                if (isLandscape) {
                    height = toolBarContentSize;
                    width = ViewGroup.LayoutParams.WRAP_CONTENT;
                    gravity = Gravity.CENTER_VERTICAL;
                    orientation = LinearLayout.HORIZONTAL;
                }
                mMenuContentView.setGravity(gravity);
                mMenuContentView.setOrientation(orientation);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mMenuContentView.getLayoutParams();
                layoutParams.width = width;
                layoutParams.height = height;
                mMenuContentView.setLayoutParams(layoutParams);
            }

            if (mActionButtonViewGroup == null) {
                mActionButtonViewGroup = mContainer.findViewById(R.id.action_button_view_group);
            }
            if (mActionButtonViewGroup != null) {
                int orientation = LinearLayout.HORIZONTAL;
                int toPaddingOfBorder = context.getResources()
                        .getDimensionPixelOffset(R.dimen.memories_editor_photo_sub_bar_start_end_padding_border);
                int toPaddingOfPhotoList = context.getResources()
                        .getDimensionPixelOffset(R.dimen.memories_editor_photo_sub_bar_bottom_padding_photo_list);
                int leftPadding = toPaddingOfBorder;
                int topPadding = 0;
                int rightPadding = toPaddingOfBorder;
                int bottomPadding = toPaddingOfPhotoList;
                int width = ViewGroup.LayoutParams.MATCH_PARENT;
                int height = ViewGroup.LayoutParams.WRAP_CONTENT;
                if (isLandscape) {
                    orientation = LinearLayout.VERTICAL;
                    leftPadding = 0;
                    topPadding = toPaddingOfBorder;
                    rightPadding = toPaddingOfPhotoList;
                    bottomPadding = toPaddingOfBorder;
                    width = ViewGroup.LayoutParams.WRAP_CONTENT;
                    height = ViewGroup.LayoutParams.MATCH_PARENT;
                }
                mActionButtonViewGroup.setPadding(leftPadding, topPadding, rightPadding, bottomPadding);
                mActionButtonViewGroup.setOrientation(orientation);
                mActionButtonViewGroup.getLayoutParams().width = width;
                mActionButtonViewGroup.getLayoutParams().height = height;
                mActionButtonViewGroup.requestLayout();
            }

            if (getMenuListView() != null) {
                int paddingStart = context.getResources()
                        .getDimensionPixelOffset(R.dimen.memories_editor_photo_list_padding);
                int paddingHorizontal = paddingStart;
                int paddingVertical = 0;
                if (isLandscape) {
                    paddingHorizontal = 0;
                    paddingVertical = paddingStart;
                }
                getMenuListView().setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
                getMenuListView().setClipToPadding(false);
                int listOritation = EditorUIConfig.getListOrientation(config);
                getMenuListView().setOrientation(isLandscapeLayout(config) ? RecyclerView.VERTICAL : RecyclerView.HORIZONTAL);
                LinearLayoutManager layoutManager = (LinearLayoutManager) getMenuListView().getLayoutManager();
                if ((layoutManager != null) && (layoutManager.getOrientation() != listOritation)) {
                    layoutManager.setOrientation(listOritation);
                }
            }
        });
    }
}
