/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SDCardBroadcastReceiver.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.broadcast;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_ICON_ENGINE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_STREAMING_ENGINE;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.CacheResource.CLEAR_CACHE_FLAG_WAVEFORM_ENGINE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;

public class SDCardBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "SDCardBroadcastReceiver";
    private EditorEngine mEditorEngine;

    public void setEditorEngine(EditorEngine editorEngine) {
        mEditorEngine = editorEngine;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        if (mEditorEngine != null) {
            String action = intent.getAction();
            GLog.v(TAG, "onReceive, action = " + action);
            if (null == action) {
                return;
            }
            if (action.equals(Intent.ACTION_MEDIA_EJECT)) {
                if (mEditorEngine != null) {
                    mEditorEngine.stopPlayer();
                    mEditorEngine.clearCacheResource(
                        CLEAR_CACHE_FLAG_ICON_ENGINE | CLEAR_CACHE_FLAG_STREAMING_ENGINE | CLEAR_CACHE_FLAG_WAVEFORM_ENGINE);
                }
            }
        }
    }
}
