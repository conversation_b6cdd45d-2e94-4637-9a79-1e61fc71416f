/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AnimatorChoreography
 ** Description: 根据state的页面切换进行动画的编排
 ** Version: 1.0
 ** Date : 2025/7/1
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2025/07/01    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.animator

import com.oplus.gallery.videoeditorpage.video.business.animator.AnimatorProcessor.startEnterAnim
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController

/**
 * 根据state的页面切换时机进行动画的编排
 */
class AnimatorChoreography : IStateChangeLifecycle {

    /**
     * 是否需要执行进入动画，默认为true。
     */
    private var isNeedEnterAnim: Boolean = true

    /**
     * 页面切换，如果是非（调节/滤镜）页面A 直接跳转到 调节/滤镜二级页B，那么执行 A页面的退出动画，B页面的进入动画。
     * 其他情况下都需要执行 旧页面的淡出动画和新页面的淡入动画
     *
     * 针对差异元素做动画的逻辑处理：
     * 如果切换的两个页面都有轨道视图，那么轨道视图不做动画，其他元素的动画按照上述情况执行
     * 如果当前时码线对应的特效轨道存在 文字/调节/滤镜 特效，那么这几个页面的一级页之间互相切换不需要执行二级菜单栏的动画（这几个页面的二级菜单栏视图一样）
     */
    override fun preChangeState(
        oldState: EditorBaseState<EditorBaseUIController<Any>>?,
        newState: EditorBaseState<EditorBaseUIController<Any>>,
        func: () -> Unit
    ) {
        if (newState.isSkipAnim) {
            isNeedEnterAnim = false
            AnimatorProcessor.addExitCallback { func.invoke() }
            AnimatorProcessor.startExitAnim(Pair(false, false), Pair(false, false), false)
        } else if (oldState?.isSkipAnim == true) {
            isNeedEnterAnim = true
            AnimatorProcessor.removeAllExitAnim()
            func.invoke()
        } else {
            isNeedEnterAnim = true
            AnimatorProcessor.addExitCallback { func.invoke() }
            AnimatorProcessor.startExitAnim(
                Pair(oldState?.editorTrackUIConfig?.isShowTrack == true, newState.editorTrackUIConfig.isShowTrack),
                Pair(oldState?.isShowSubMenu == true, newState.isShowSubMenu),
                true
            )
        }
    }

    override fun changeState() {
        if (isNeedEnterAnim) {
            startEnterAnim()
        }
    }

    override fun resumeState(state: EditorBaseState<EditorBaseUIController<Any>>, func: () -> Unit) {
        AnimatorProcessor.addExitCallback {
            func.invoke()
            startEnterAnim()
        }
        AnimatorProcessor.startExitAnim(
            Pair(state.editorTrackUIConfig.isShowTrack, true),
            Pair(false, false),
            true
        )
    }
}

interface IStateChangeLifecycle {

    /**
     * 启动新state时的预处理，判断是否执行淡出动画等
     * @param oldState 旧的state
     * @param newState 新的state
     * @param func 执行完淡出动画后需要执行的回调，如果不需要执行淡出动画则直接执行回调
     */
    fun preChangeState(
        oldState: EditorBaseState<EditorBaseUIController<Any>>?,
        newState: EditorBaseState<EditorBaseUIController<Any>>,
        func: () -> Unit
    )

    /**
     * 真正启动新state时，根据不同的情况判断是否需要淡入动画
     */
    fun changeState()

    /**
     * 销毁当前state，对栈顶的state执行resume时需要执行淡入动画
     * @param state 需要执行淡出的state
     * @param func 执行完淡出动画后需要执行的回调，并且是在淡入动画执行前作用
     */
    fun resumeState(
        state: EditorBaseState<EditorBaseUIController<Any>>,
        func: () -> Unit
    )
}