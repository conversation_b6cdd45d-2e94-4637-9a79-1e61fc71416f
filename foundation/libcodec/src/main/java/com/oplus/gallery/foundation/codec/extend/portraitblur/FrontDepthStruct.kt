/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FrontDepthStruct
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/5/30 15:33
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2025/5/30		  1.0		 FrontDepthStruct
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.extend.portraitblur

import android.util.Log
import com.oplus.gallery.foundation.codec.extend.IInverseStructConvert
import com.oplus.gallery.foundation.codec.extend.portraitblur.FrontDepthStruct.Companion.DEFAULT_DATA_CAPACITY
import com.oplus.gallery.foundation.codec.extend.portraitblur.FrontDepthStruct.Companion.VERSION_V1_1
import java.nio.ByteBuffer
import java.nio.ByteOrder
import com.oplus.gallery.foundation.util.ext.put
import com.oplus.gallery.foundation.util.ext.putBoolean
import com.oplus.gallery.foundation.util.ext.toPercent
import com.oplus.gallery.foundation.util.math.toPercent

/**
 * 照片前置数据结构体
 */
class FrontDepthStruct(byteArray: ByteArray) : PortraitBlurDepthStruct<FrontDepthData>(byteArray) {
    private val version: Float32
    private val depthWidth: Signed32
    private val depthHeight: Signed32
    private val segmentWidth: Signed32
    private val segmentHeight: Signed32
    private val hairmaskWidth: Signed32
    private val hairmaskHeight: Signed32
    private val matteinfoWidth: Signed32
    private val matteinfoHeight: Signed32
    private val startXSrc: Signed32
    private val startYSrc: Signed32
    private val widthSrc: Signed32
    private val heightSrc: Signed32
    private val negevimgWidth: Signed32
    private val negevimgHeight: Signed32
    private val format: Signed32
    private val width: Signed32
    private val height: Signed32
    private val widthPadding: Signed32
    private val heightPadding: Signed32
    private val rotation: Signed32
    private val faceCount: Signed32
    private val faceLeft: Array<Signed32>
    private val faceTop: Array<Signed32>
    private val faceWidth: Array<Signed32>
    private val faceHeight: Array<Signed32>
    private val blurStrength: Float32
    private val lightStrength: Float32
    private val iso: Signed32
    private val lux: Signed32
    private val refocusModeAps: Signed32
    private val isNegEVImgEnable: Bool
    private val mRenderMode: Signed32
    private val blurApertures: Array<Float32>
    private val blurValue: Array<Float32>
    private val mirrorEnable: Signed32
    private val cameraRoll: Signed32
    private val fNumber: Float32

    init {
        version = Float32()
        depthWidth = Signed32()
        depthHeight = Signed32()
        segmentWidth = Signed32()
        segmentHeight = Signed32()
        hairmaskWidth = Signed32()
        hairmaskHeight = Signed32()
        matteinfoWidth = Signed32()
        matteinfoHeight = Signed32()
        startXSrc = Signed32()
        startYSrc = Signed32()
        widthSrc = Signed32()
        heightSrc = Signed32()
        negevimgWidth = Signed32()
        negevimgHeight = Signed32()
        format = Signed32()
        width = Signed32()
        height = Signed32()
        widthPadding = Signed32()
        heightPadding = Signed32()
        rotation = Signed32()
        faceCount = Signed32()
        faceLeft = array(arrayOfNulls<Signed32>(MAX_APS_FACE_ROI))
        faceTop = array(arrayOfNulls<Signed32>(MAX_APS_FACE_ROI))
        faceWidth = array(arrayOfNulls<Signed32>(MAX_APS_FACE_ROI))
        faceHeight = array(arrayOfNulls<Signed32>(MAX_APS_FACE_ROI))
        blurStrength = Float32()
        lightStrength = Float32()
        iso = Signed32()
        lux = Signed32()
        refocusModeAps = Signed32()
        isNegEVImgEnable = Bool()
        mRenderMode = Signed32()
        blurApertures = array(arrayOfNulls<Float32>(BLUR_NUM))
        blurValue = array(arrayOfNulls<Float32>(BLUR_NUM))
        mirrorEnable = Signed32()
        cameraRoll = Signed32()
        fNumber = Float32()
    }

    companion object {
        const val TAG = "FrontDepthStruct"
        // 开始支持IPU的版本
        const val VERSION_V1_1 = 1.1F
        const val MAX_APS_FACE_ROI = 10
        const val BLUR_NUM = 32
        /**
         * 转换为ByteArray分配的数组大小
         */
        const val DEFAULT_DATA_CAPACITY = 40000
    }

    override fun toData(): FrontDepthData? {
        return runCatching {
            FrontDepthData(
                version = version.get(),
                depthWidth = depthWidth.get(),
                depthHeight = depthHeight.get(),
                segmentWidth = segmentWidth.get(),
                segmentHeight = segmentHeight.get(),
                hairmaskWidth = hairmaskWidth.get(),
                hairmaskHeight = hairmaskHeight.get(),
                matteinfoWidth = matteinfoWidth.get(),
                matteinfoHeight = matteinfoHeight.get(),
                startXSrc = startXSrc.get(),
                startYSrc = startYSrc.get(),
                widthSrc = widthSrc.get(),
                heightSrc = heightSrc.get(),
                negevimgWidth = negevimgWidth.get(),
                negevimgHeight = negevimgHeight.get(),
                format = format.get(),
                width = width.get(),
                height = height.get(),
                widthPadding = widthPadding.get(),
                heightPadding = heightPadding.get(),
                rotation = rotation.get(),
                faceCount = faceCount.get(),
                faceLeft = faceLeft.get(),
                faceTop = faceTop.get(),
                faceWidth = faceWidth.get(),
                faceHeight = faceHeight.get(),
                blurStrength = blurStrength.get(),
                lightStrength = lightStrength.get(),
                iso = iso.get(),
                lux = lux.get(),
                refocusModeAps = refocusModeAps.get(),
                isNegEVImgEnable = isNegEVImgEnable.get(),
                mRenderMode = mRenderMode.get(),
                blurApertures = blurApertures.get(),
                blurValue = blurValue.get(),
                mirrorEnable = mirrorEnable.get(),
                cameraRoll = cameraRoll.get(),
                fNumber = if (version.get() >= VERSION_V1_1) fNumber.get() else 0f
            )
        }.onFailure {
            Log.e(TAG, "toData: is error.")
        }.getOrNull()
    }
}

/**
 * 相机前置镜头拍摄的照片数据，包含完整的人像景深相关数据。
 *
 * @property version 版本号
 * @property depthWidth 深度图的宽度
 * @property depthHeight 深度图的高度
 * @property segmentWidth 分割图的宽度
 * @property segmentHeight 分割图的高度
 * @property hairmaskWidth 发丝级蒙版图的宽度
 * @property hairmaskHeight 发丝级蒙版图的高度
 * @property matteinfoWidth 灰度图信息的高度
 * @property matteinfoHeight 灰度图信息的宽度
 * @property startXSrc 灰度图X起始位置
 * @property startYSrc 灰度图Y起始位置
 * @property widthSrc 灰度图宽度
 * @property heightSrc 灰度图高度
 * @property negevimgWidth 负像图宽度
 * @property negevimgHeight 负像图高度
 * @property format 图片格式
 * @property width 图像宽度
 * @property height 图像高度
 * @property widthPadding 宽度方向填充值
 * @property heightPadding 高度方向填充值
 * @property rotation 人脸角度（单位：度）
 * @property faceCount 检测到的人脸数量
 * @property faceLeft 人脸区域数组 Left
 * @property faceTop 人脸区域数组 faceTop
 * @property faceWidth 人脸宽度数组 faceWidth
 * @property faceHeight 人脸高度数组 faceHeight
 * @property blurStrength 虚化强度，取值范围 [0,100]
 * @property lightStrength 光强值
 * @property iso 感光度
 * @property lux 照度
 * @property refocusModeAps 效果模式：0-虚化，1-留色，2-虚化+留色，3-lut，4-虚化+lut
 * @property isNegEVImgEnable 是否支持负像图
 * @property mRenderMode 渲染模式：1-预览；2-保存
 * @property blurApertures 光圈值数组，范围 [1.4f,16f]，与 [blurValue] 一一对应
 * @property blurValue 景深虚化值数组，范围 [0,100]，与 [blurApertures] 一一对应
 * @property mirrorEnable 是否开启镜像（0: 关闭，1: 开启）
 * @property cameraRoll 照片角度（非人脸角度）
 * @property fNumber 光圈值
 */
class FrontDepthData(
    val version: Float? = 0f,
    val depthWidth: Int? = 0,
    val depthHeight: Int? = 0,
    val segmentWidth: Int? = 0,
    val segmentHeight: Int? = 0,
    val hairmaskWidth: Int? = 0,
    val hairmaskHeight: Int? = 0,
    val matteinfoWidth: Int? = 0,
    val matteinfoHeight: Int? = 0,
    val startXSrc: Int? = 0,
    val startYSrc: Int? = 0,
    val widthSrc: Int? = 0,
    val heightSrc: Int? = 0,
    val negevimgWidth: Int? = 0,
    val negevimgHeight: Int? = 0,
    val format: Int? = 0,
    val width: Int? = 0,
    val height: Int? = 0,
    val widthPadding: Int? = 0,
    val heightPadding: Int? = 0,
    val rotation: Int? = 0,
    val faceCount: Int? = 0,
    val faceLeft: IntArray? = null,
    val faceTop: IntArray? = null,
    val faceWidth: IntArray? = null,
    val faceHeight: IntArray? = null,
    val blurStrength: Float? = 0f,
    val lightStrength: Float? = 0f,
    val iso: Int? = 0,
    val lux: Int? = 0,
    val refocusModeAps: Int? = 0,
    val isNegEVImgEnable: Boolean? = false,
    val mRenderMode: Int? = 0,
    blurApertures: FloatArray? = null,
    blurValue: FloatArray? = null,
    val mirrorEnable: Int? = 0,
    val cameraRoll: Int? = 0,
    fNumber: Float? = 0f,
) : PortraitBlurDepthData(fNumber, blurApertures, blurValue?.toPercent(FACTOR_PERCENTAGE_TO_PROGRESS)), IInverseStructConvert {

    /**
     *  前置虚化强度和后置需求强度的值差100倍，为了保持一致前置这里乘以100
     */
    override val blurStrengthParam: Float? = blurStrength?.toPercent(FACTOR_PERCENTAGE_TO_PROGRESS)

    companion object {
        const val FACTOR_PERCENTAGE_TO_PROGRESS = 100
    }

    fun copy(
        version: Float? = this.version,
        depthWidth: Int? = this.depthWidth,
        depthHeight: Int? = this.depthHeight,
        segmentWidth: Int? = this.segmentWidth,
        segmentHeight: Int? = this.segmentHeight,
        hairmaskWidth: Int? = this.hairmaskWidth,
        hairmaskHeight: Int? = this.hairmaskHeight,
        matteinfoWidth: Int? = this.matteinfoWidth,
        matteinfoHeight: Int? = this.matteinfoHeight,
        startXSrc: Int? = this.startXSrc,
        startYSrc: Int? = this.startYSrc,
        widthSrc: Int? = this.widthSrc,
        heightSrc: Int? = this.heightSrc,
        negevimgWidth: Int? = this.negevimgWidth,
        negevimgHeight: Int? = this.negevimgHeight,
        format: Int? = this.format,
        width: Int? = this.width,
        height: Int? = this.height,
        widthPadding: Int? = this.widthPadding,
        heightPadding: Int? = this.heightPadding,
        rotation: Int? = this.rotation,
        faceCount: Int? = this.faceCount,
        faceLeft: IntArray? = this.faceLeft,
        faceTop: IntArray? = this.faceTop,
        faceWidth: IntArray? = this.faceWidth,
        faceHeight: IntArray? = this.faceHeight,
        blurStrength: Float? = this.blurStrength,
        lightStrength: Float? = this.lightStrength,
        iso: Int? = this.iso,
        lux: Int? = this.lux,
        refocusModeAps: Int? = this.refocusModeAps,
        isNegEVImgEnable: Boolean? = this.isNegEVImgEnable,
        mRenderMode: Int? = this.mRenderMode,
        blurApertures: FloatArray? = this.blurApertures,
        blurValue: FloatArray? = this.blurValue,
        mirrorEnable: Int? = this.mirrorEnable,
        cameraRoll: Int? = this.cameraRoll,
        fNumber: Float? = this.fNumber,
    ): FrontDepthData {
        return FrontDepthData(
            version,
            depthWidth,
            depthHeight,
            segmentWidth,
            segmentHeight,
            hairmaskWidth,
            hairmaskHeight,
            matteinfoWidth,
            matteinfoHeight,
            startXSrc,
            startYSrc,
            widthSrc,
            heightSrc,
            negevimgWidth,
            negevimgHeight,
            format,
            width,
            height,
            widthPadding,
            heightPadding,
            rotation,
            faceCount,
            faceLeft,
            faceTop,
            faceWidth,
            faceHeight,
            blurStrength,
            lightStrength,
            iso,
            lux,
            refocusModeAps,
            isNegEVImgEnable,
            mRenderMode,
            blurApertures,
            blurValue,
            mirrorEnable,
            cameraRoll,
            fNumber
        )
    }

    override fun toBytes(): ByteArray {
        val buffer: ByteBuffer = ByteBuffer.allocate(DEFAULT_DATA_CAPACITY)
        buffer.order(ByteOrder.LITTLE_ENDIAN)
        version?.let { buffer.putFloat(it) }
        depthWidth?.let { buffer.putInt(it) }
        depthHeight?.let { buffer.putInt(it) }
        segmentWidth?.let { buffer.putInt(it) }
        segmentHeight?.let { buffer.putInt(it) }
        hairmaskWidth?.let { buffer.putInt(it) }
        hairmaskHeight?.let { buffer.putInt(it) }
        matteinfoWidth?.let { buffer.putInt(it) }
        matteinfoHeight?.let { buffer.putInt(it) }
        startXSrc?.let { buffer.putInt(it) }
        startYSrc?.let { buffer.putInt(it) }
        widthSrc?.let { buffer.putInt(it) }
        heightSrc?.let { buffer.putInt(it) }
        negevimgWidth?.let { buffer.putInt(it) }
        negevimgHeight?.let { buffer.putInt(it) }
        format?.let { buffer.putInt(it) }
        width?.let { buffer.putInt(it) }
        height?.let { buffer.putInt(it) }
        widthPadding?.let { buffer.putInt(it) }
        heightPadding?.let { buffer.putInt(it) }
        rotation?.let { buffer.putInt(it) }
        faceCount?.let { buffer.putInt(it) }
        faceLeft?.let { buffer.put(it) }
        faceTop?.let { buffer.put(it) }
        faceWidth?.let { buffer.put(it) }
        faceHeight?.let { buffer.put(it) }
        blurStrength?.let { buffer.putFloat(it) }
        lightStrength?.let { buffer.putFloat(it) }
        iso?.let { buffer.putInt(it) }
        lux?.let { buffer.putInt(it) }
        refocusModeAps?.let { buffer.putInt(it) }
        isNegEVImgEnable?.let { buffer.putBoolean(it) }
        mRenderMode?.let { buffer.putInt(it) }
        blurApertures?.let { buffer.put(it) }
        blurValue?.let { buffer.put(it) }
        mirrorEnable?.let { buffer.putInt(it) }
        cameraRoll?.let { buffer.putInt(it) }
        version?.takeIf { it >= VERSION_V1_1 }?.let {
            fNumber?.let { buffer.putFloat(it) }
        }
        buffer.limit(buffer.position())
        buffer.rewind()
        val actualArray = ByteArray(buffer.limit())
        buffer.get(actualArray)
        return actualArray
    }
}

