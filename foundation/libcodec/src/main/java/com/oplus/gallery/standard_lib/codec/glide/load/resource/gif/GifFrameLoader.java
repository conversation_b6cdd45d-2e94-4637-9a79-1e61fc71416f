package com.oplus.gallery.standard_lib.codec.glide.load.resource.gif;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.util.Log;

import com.oplus.gallery.standard_lib.codec.glide.gifdecoder.GifDecoder;
import com.oplus.gallery.standard_lib.codec.glide.load.engine.bitmap_recycle.BitmapPool;
import com.oplus.gallery.standard_lib.codec.glide.load.resource.bitmap.TransformationUtils;

import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import androidx.core.util.Preconditions;

import org.jetbrains.annotations.NotNull;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

class GifFrameLoader {
    private final GifDecoder gifDecoder;
    private final Handler handler;
    private final List<FrameCallback> callbacks = new ArrayList<>();

    private final BitmapPool bitmapPool;

    private boolean isRunning;
    private boolean isLoadPending;
    private boolean startFromFirstFrame;
    private DelayTarget current;
    private boolean isCleared;
    private DelayTarget next;
    private Bitmap firstFrame;
    private DelayTarget pendingTarget;
    HandlerThread ht;
    Handler gifFrameHandler;
    @Nullable
    private OnEveryFrameListener onEveryFrameListener;
    private int firstFrameSize;
    private int width;
    private int height;
    private int targetFrameWidth;
    private int targetFrameHeight;

    public interface FrameCallback {
        void onFrameReady();
    }

    @SuppressWarnings("PMD.ConstructorCallsOverridableMethod")
    GifFrameLoader(BitmapPool bitmapPool, GifDecoder gifDecoder, int targetFrameWidth, int targetFrameHeight, Handler handler, Bitmap firstFrame) {
        if (handler == null) {
            handler = new Handler(Looper.getMainLooper(), new FrameLoaderCallback());
        }
        this.handler = handler;
        this.gifDecoder = gifDecoder;
        this.bitmapPool = bitmapPool;
        this.targetFrameWidth = targetFrameWidth;
        this.targetFrameHeight = targetFrameHeight;
        ht = new HandlerThread("gifFrame");
        ht.start();
        gifFrameHandler = new Handler(ht.getLooper());
        setFrameTransformation(firstFrame);
    }

    @SuppressLint("RestrictedApi")
    void setFrameTransformation(Bitmap firstFrame) {
        this.firstFrame = Preconditions.checkNotNull(firstFrame);
        firstFrameSize = firstFrame.isRecycled() ? 0 : firstFrame.getAllocationByteCount();
        width = firstFrame.getWidth();
        height = firstFrame.getHeight();
    }
    

    Bitmap getFirstFrame() {
        return firstFrame;
    }

    void subscribe(FrameCallback frameCallback) {
        if (isCleared) {
            throw new IllegalStateException("Cannot subscribe to a cleared frame loader");
        }
        if (callbacks.contains(frameCallback)) {
            throw new IllegalStateException("Cannot subscribe twice in a row");
        }
        boolean start = callbacks.isEmpty();
        callbacks.add(frameCallback);
        if (start) {
            start();
        }
    }

    void unsubscribe(FrameCallback frameCallback) {
        callbacks.remove(frameCallback);
        if (callbacks.isEmpty()) {
            stop();
        }
    }

    int getWidth() {
        return width;
    }

    int getHeight() {
        return height;
    }

    int getSize() {
        return gifDecoder.getByteSize() + firstFrameSize;
    }

    int getCurrentIndex() {
        return current != null ? current.index : -1;
    }

    ByteBuffer getBuffer() {
        return gifDecoder.getData().asReadOnlyBuffer();
    }

    int getFrameCount() {
        return gifDecoder.getFrameCount();
    }

    int getLoopCount() {
        return gifDecoder.getTotalIterationCount();
    }

    private void start() {
        if (isRunning) {
            return;
        }
        isRunning = true;
        isCleared = false;

        loadNextFrame();
    }

    private void stop() {
        isRunning = false;
    }

    void clear() {
        callbacks.clear();
        recycleFirstFrame();
        stop();
        /*if (current != null) {
            onFrameClear(current);
            current = null;
        }
        if (next != null) {
            onFrameClear(next);
            next = null;
        }
        if (pendingTarget != null) {
            onFrameClear(pendingTarget);
            pendingTarget = null;
        }*/
        
        //移除正在执行的任务
        gifFrameHandler.removeCallbacksAndMessages(null);
        ht.quitSafely();
        
        gifDecoder.clear();
        isCleared = true;
    }

    Bitmap getCurrentFrame() {
        return current != null ? current.getResource() : firstFrame;
    }

    @SuppressLint("RestrictedApi")
    private void loadNextFrame() {
        if (!isRunning || isLoadPending) {
            return;
        }
        if (startFromFirstFrame) {
            Preconditions.checkArgument(
                    pendingTarget == null, "Pending target must be null when starting from the first frame");
            gifDecoder.resetFrameIndex();
            startFromFirstFrame = false;
        }
        if (pendingTarget != null) {
            DelayTarget temp = pendingTarget;
            pendingTarget = null;
            onFrameReady(temp);
            return;
        }
        isLoadPending = true;
        // Get the delay before incrementing the pointer because the delay indicates the amount of time
        // we want to spend on the current frame.
        int delay = gifDecoder.getNextDelay();
        long targetTime = SystemClock.uptimeMillis() + delay;

        gifDecoder.advance();
        next = new DelayTarget(gifDecoder, bitmapPool, handler, gifDecoder.getCurrentFrameIndex(), targetTime,
                targetFrameWidth, targetFrameHeight);
        gifFrameHandler.post(next);
    }

    private void recycleFirstFrame() {
        if (firstFrame != null) {
            bitmapPool.put(firstFrame);
            firstFrame = null;
        }
    }

    @SuppressLint("RestrictedApi")
    void setNextStartFromFirstFrame() {
        Preconditions.checkArgument(!isRunning, "Can't restart a running animation");
        startFromFirstFrame = true;
        if (pendingTarget != null) {
            onFrameClear(pendingTarget);
            pendingTarget = null;
        }
    }

    @VisibleForTesting
    void setOnEveryFrameReadyListener(@Nullable OnEveryFrameListener onEveryFrameListener) {
        this.onEveryFrameListener = onEveryFrameListener;
    }

    @VisibleForTesting
    void onFrameReady(DelayTarget delayTarget) {
        if (onEveryFrameListener != null) {
            onEveryFrameListener.onFrameReady();
        }
        isLoadPending = false;
        if (isCleared) {
            handler.obtainMessage(FrameLoaderCallback.MSG_CLEAR, delayTarget).sendToTarget();
            return;
        }
        // If we're not running, notifying here will recycle the frame that we might currently be
        // showing, which breaks things (see #2526). We also can't discard this frame because we've
        // already incremented the frame pointer and can't decode the same frame again. Instead we'll
        // just hang on to this next frame until start() or clear() are called.
        if (!isRunning) {
            if (startFromFirstFrame) {
                handler.obtainMessage(FrameLoaderCallback.MSG_CLEAR, delayTarget).sendToTarget();
            } else {
                pendingTarget = delayTarget;
            }
            return;
        }

        if (delayTarget.getResource() != null) {
            recycleFirstFrame();
            DelayTarget previous = current;
            current = delayTarget;
            // The callbacks may unregister when onFrameReady is called, so iterate in reverse to avoid
            // concurrent modifications.
            for (int i = callbacks.size() - 1; i >= 0; i--) {
                FrameCallback cb = callbacks.get(i);
                cb.onFrameReady();
            }
            if (previous != null) {
                handler.obtainMessage(FrameLoaderCallback.MSG_CLEAR, previous).sendToTarget();
            }
        }

        loadNextFrame();
    }
    
    void onFrameClear(DelayTarget delayTarget) {
        Bitmap previous = delayTarget.getResource();
        if (previous != null && !previous.isRecycled()) {
            bitmapPool.put(previous);
        }
    }

    private class FrameLoaderCallback implements Handler.Callback {
        static final int MSG_DELAY = 1;
        static final int MSG_CLEAR = 2;

        FrameLoaderCallback() {}

        @Override
        public boolean handleMessage(Message msg) {
            if (msg.what == MSG_DELAY) {
                GifFrameLoader.DelayTarget target = (DelayTarget) msg.obj;
                onFrameReady(target);
                return true;
            } else if (msg.what == MSG_CLEAR) {
                GifFrameLoader.DelayTarget target = (DelayTarget) msg.obj;
                onFrameClear(target);
            }
            return false;
        }
    }

    @VisibleForTesting
    static class DelayTarget implements Runnable {
        private final BitmapPool bitmapPool;
        private final GifDecoder gifDecoder;
        private final Handler handler;
        private final int index;
        private final long targetTime;
        private final int targetFrameWidth;
        private final int targetFrameheight;
        private Bitmap resource;

        DelayTarget(@NotNull GifDecoder gifDecoder, BitmapPool bitmapPool, @NotNull Handler handler, int index, long targetTime,
                int targetFrameWidth, int targetFrameheight) {
            this.gifDecoder = gifDecoder;
            this.bitmapPool = bitmapPool;
            this.handler = handler;
            this.index = index;
            this.targetFrameWidth = targetFrameWidth;
            this.targetFrameheight = targetFrameheight;
            this.targetTime = targetTime;
        }

        Bitmap getResource() {
            return resource;
        }

        @Override
        public void run() {
            try {
                resource = gifDecoder.getNextFrame();
                if (resource != null) {
                    Bitmap transformed = TransformationUtils.fitCenter(bitmapPool, resource, targetFrameWidth, targetFrameheight);
                    if (transformed != resource) {
                        bitmapPool.put(resource);
                    }
                    resource = transformed;
                    Message msg = handler.obtainMessage(FrameLoaderCallback.MSG_DELAY, this);
                    handler.sendMessageAtTime(msg, targetTime);
                }
            } catch (Exception e) {
                Log.e("DelayTarget", "run, e=" + e);
            }
        }
    }

    @VisibleForTesting
    interface OnEveryFrameListener {
        void onFrameReady();
    }
}