/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  RuleViewAttributes
 ** Description: add
 ** Version: 1.0
 ** Date : 2025/4/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/4/28      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.widget.ruleview

import android.content.Context
import android.content.res.TypedArray
import android.graphics.Color
import android.util.AttributeSet
import android.view.ViewConfiguration
import androidx.annotation.IntDef
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils.dp2px
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils.sp2px

/**
 * RuleView刻度尺的属性（颜色，刻度，字体大小等）
 */
data class RuleViewAttributes(
    /*** 滑动阈值，手指移动超过此距离才视为有效滑动*/
    val flingVelocityMin: Int,
    /*** 惯性滑动的最小速度阈值*/
    val flingVelocityMax: Int,
    /*** 惯性滑动的最大速度阈值*/
    val touchSlop: Int,
    /** 背景色  */
    val bgColor: Int,
    /** 刻度颜色  */
    val gradationColor: Int,
    /** 短刻度线宽度  */
    val shortLineWidth: Float,
    /**长刻度线宽度*/
    val longLineWidth: Float,
    /** 短刻度长度  */
    val shortGradationLen: Float,
    /*** 长刻度长度*/
    val longGradationLen: Float,
    /** 中间指针线颜色  */
    val indicatorLineColor: Int,
    /** 中间指针线按压时颜色  */
    val indicatorLinePressedColor: Int,
    /** 中间指针线宽度  */
    val indicatorLineWidth: Float,
    /** 中间指针线长度  */
    val indicatorLineLen: Float,
    /** 中间指针线按压时长度  */
    val indicatorLinePressedWidth: Float,
    /** 刻度最小单位  */
    val gradationUnit: Float,
    /** 相邻两条长刻度线之间的刻度数量  */
    val numberPerCount: Int,
    /** 刻度条与刻度文字的间距  */
    val textGradationGap: Float,
    /** 吸附的检测阈值（像素） */
    val snapTriggerDistance: Float,
    /** 脱离吸附需要的最小距离（像素） */
    val snapEscapeDistance: Float,
    /*** 刻度数值最小单位：gradationUnit * 10*/
    val numberUnit: Int,
    /** 长刻度颜色  */
    val longLineColor: Int,
    /** 短刻度颜色  */
    val shortLineColor: Int,
    /** 刻度字体颜色  */
    var textColor: Int,
    /** 刻度字体大小  */
    var textSize: Float,
    /**获取最小值*/
    var minValue: Float,
    /*** 获取最大值*/
    var maxValue: Float,
    /*** 最小数值，放大10倍：minValue * 10*/
    var minNumber: Int,
    /*** 最大数值，放大10倍：maxValue * 10*/
    var maxNumber: Int,
) {
    companion object {
        /** 默认背景颜色  */
        private const val DEFAULT_BG_COLOR = "#f5f8f5"

        /** 默认指示器颜色  */
        private const val DEFAULT_INDICATOR_COLOR = "#48b975"

        /** 默认短刻度线宽度，单位dp  */
        private const val DEFAULT_SHORT_LINE_WIDTH_DP = 1f

        /** 默认短刻度线长度，单位dp  */
        private const val DEFAULT_SHORT_GRADATION_LEN_DP = 16f

        /** 默认指示器线宽度，单位dp  */
        private const val DEFAULT_INDICATOR_LINE_WIDTH_DP = 3f

        /** 默认指示器线长度，单位dp  */
        private const val DEFAULT_INDICATOR_LINE_LEN_DP = 35f

        /** 默认文字与刻度间距，单位dp  */
        private const val DEFAULT_TEXT_GRADATION_GAP_DP = 5f

        /** 默认最小值  */
        private const val DEFAULT_MIN_VALUE = 0f

        /** 默认最大值  */
        private const val DEFAULT_MAX_VALUE = 100f

        /** 默认刻度单位值  */
        private const val DEFAULT_GRADATION_UNIT = 0.1f

        /** 默认每个主刻度包含的子刻度数量  */
        private const val DEFAULT_NUMBER_PER_COUNT = 10

        //表示逃逸吸附的距离阈值
        private const val SNAP_ESCAPE_DISTANCE = 8f

        // 表示触发吸附的距离阈值
        private const val SNAP_TRIGGER_DISTANCE = 2f

        /** 默认文本大小，单位sp  */
        const val DEFAULT_TEXT_SIZE_SP = 12f

        // 数字到值的缩放因子，用于将数字转换为实际值
        const val VALUE_TO_NUMBER_SCALE = 10f

        fun fromAttributes(context: Context, attrs: AttributeSet?): RuleViewAttributes {
            val typedArray = context.obtainStyledAttributes(attrs, R.styleable.RuleView)
            val (bgColor, gradationColor, longLineColor, shortLineColor, textColor) = readColorsAndText(
                typedArray
            )
            val (shortLineWidth, shortGradationLen, longGradationLen, longLineWidth, indicatorLineWidth) = readGradationDimensions(
                typedArray, context
            )
            val (indicatorLinePressedWidth, indicatorLineLen, textGradationGap) = readIndicatorDimensions(
                typedArray, context
            )
            val (minValue, maxValue, gradationUnit) = readOtherAttributes(typedArray)
            val (touchSlop, flingVelocityMin, flingVelocityMax) = readSystemConstants(
                context
            )
            val snapEscapeDistance = dp2px(context, SNAP_ESCAPE_DISTANCE)
            val snapTriggerDistance = dp2px(context, SNAP_TRIGGER_DISTANCE)
            val numberUnit = calculateDerivedAttributes(gradationUnit)
            val textSize = typedArray.getDimension(
                R.styleable.RuleView_gradationTextSize,
                sp2px(context, DEFAULT_TEXT_SIZE_SP).toFloat()
            )
            val (indicatorLineColor, indicatorLinePressedColor) = getIndicatorLineColor(typedArray).let {
                it[0] to it[1]
            }
            val numberPerCount = typedArray.getInt(
                R.styleable.RuleView_numberPerCount, DEFAULT_NUMBER_PER_COUNT
            )
            typedArray.recycle()

            return RuleViewAttributes(
                flingVelocityMin,
                flingVelocityMax,
                touchSlop,
                bgColor,
                gradationColor,
                shortLineWidth,
                longLineWidth,
                shortGradationLen,
                longGradationLen,
                indicatorLineColor,
                indicatorLinePressedColor,
                indicatorLineWidth,
                indicatorLineLen,
                indicatorLinePressedWidth,
                gradationUnit,
                numberPerCount,
                textGradationGap,
                snapTriggerDistance.toFloat(),
                snapEscapeDistance.toFloat(),
                numberUnit,
                longLineColor,
                shortLineColor,
                textColor,
                textSize,
                minValue,
                maxValue,
                (minValue * VALUE_TO_NUMBER_SCALE).toInt(),
                (maxValue * VALUE_TO_NUMBER_SCALE).toInt(),
            )
        }

        /**
         * 获取指针颜色
         */
        private fun getIndicatorLineColor(typedArray: TypedArray): IntArray {
            val indicatorLineColor = typedArray.getColor(
                R.styleable.RuleView_indicatorLineColor, Color.parseColor(
                    DEFAULT_INDICATOR_COLOR
                )
            )
            val indicatorLinePressedColor = typedArray.getColor(
                R.styleable.RuleView_indicatorLinePressedColor, Color.parseColor(
                    DEFAULT_INDICATOR_COLOR
                )
            )
            return intArrayOf(indicatorLineColor, indicatorLinePressedColor)
        }

        /**
         * 读取颜色和文本属性
         * @param typedArray 属性集
         * @param context 上下文
         * @return 颜色和文本属性元组
         */
        private fun readColorsAndText(typedArray: TypedArray): IntArray {
            val bgColor = typedArray.getColor(
                R.styleable.RuleView_ruleViewBgColor, Color.parseColor(
                    DEFAULT_BG_COLOR
                )
            )
            val gradationColor =
                typedArray.getColor(R.styleable.RuleView_gradationColor, Color.LTGRAY)
            val longLineColor =
                typedArray.getColor(R.styleable.RuleView_longLineColor, gradationColor)
            val shortLineColor =
                typedArray.getColor(R.styleable.RuleView_shortLineColor, gradationColor)
            val textColor =
                typedArray.getColor(R.styleable.RuleView_gradationTextColor, Color.BLACK)
            val indicatorLineColor = typedArray.getColor(
                R.styleable.RuleView_indicatorLineColor, Color.parseColor(
                    DEFAULT_INDICATOR_COLOR
                )
            )
            val indicatorLinePressedColor = typedArray.getColor(
                R.styleable.RuleView_indicatorLinePressedColor, Color.parseColor(
                    DEFAULT_INDICATOR_COLOR
                )
            )
            return intArrayOf(
                bgColor,
                gradationColor,
                longLineColor,
                shortLineColor,
                textColor,
                indicatorLineColor,
                indicatorLinePressedColor
            )
        }

        /**
         * 读取尺寸属性
         * @param typedArray 属性集
         * @param context 上下文
         * @return 尺寸属性元组
         */
        private fun readGradationDimensions(typedArray: TypedArray, context: Context): FloatArray {
            val shortLineWidth = typedArray.getDimension(
                R.styleable.RuleView_shortLineWidth, dp2px(
                    context, DEFAULT_SHORT_LINE_WIDTH_DP
                ).toFloat()
            )
            val shortGradationLen = typedArray.getDimension(
                R.styleable.RuleView_shortGradationLen, dp2px(
                    context, DEFAULT_SHORT_GRADATION_LEN_DP
                ).toFloat()
            )
            val longGradationLen = typedArray.getDimension(
                R.styleable.RuleView_longGradationLen, shortGradationLen * 2
            )
            val longLineWidth =
                typedArray.getDimension(R.styleable.RuleView_longLineWidth, shortLineWidth * 2)
            val indicatorLineWidth = typedArray.getDimension(
                R.styleable.RuleView_indicatorLineWidth, dp2px(
                    context, DEFAULT_INDICATOR_LINE_WIDTH_DP
                ).toFloat()
            )
            return floatArrayOf(
                shortLineWidth,
                shortGradationLen,
                longGradationLen,
                longLineWidth,
                indicatorLineWidth
            )
        }

        /**
         * 读取指针属性
         * @param typedArray 属性集
         * @param context 上下文
         * @return 尺寸属性元组
         */
        private fun readIndicatorDimensions(typedArray: TypedArray, context: Context): FloatArray {
            val indicatorLinePressedWidth = typedArray.getDimension(
                R.styleable.RuleView_indicatorLinePressedWidth, dp2px(
                    context, DEFAULT_INDICATOR_LINE_WIDTH_DP
                ).toFloat()
            )
            val indicatorLineLen = typedArray.getDimension(
                R.styleable.RuleView_indicatorLineLength, dp2px(
                    context, DEFAULT_INDICATOR_LINE_LEN_DP
                ).toFloat()
            )
            val textGradationGap = typedArray.getDimension(
                R.styleable.RuleView_textGradationGap, dp2px(
                    context, DEFAULT_TEXT_GRADATION_GAP_DP
                ).toFloat()
            )

            return floatArrayOf(
                indicatorLinePressedWidth,
                indicatorLineLen,
                textGradationGap
            )
        }

        /**
         * 读取其他属性
         * @param typedArray 属性集
         * @return 其他属性元组
         */
        private fun readOtherAttributes(typedArray: TypedArray): FloatArray {
            val minValue = typedArray.getFloat(
                R.styleable.RuleView_gradationMinValue, DEFAULT_MIN_VALUE
            )
            val maxValue = typedArray.getFloat(
                R.styleable.RuleView_gradationMaxValue, DEFAULT_MAX_VALUE
            )
            val gradationUnit = typedArray.getFloat(
                R.styleable.RuleView_gradationUnit, DEFAULT_GRADATION_UNIT
            )

            return floatArrayOf(minValue, maxValue, gradationUnit)
        }

        /**
         * 读取系统相关常量
         * @param context 上下文
         * @return 系统常量元组
         */
        private fun readSystemConstants(context: Context): IntArray {
            val viewConfiguration = ViewConfiguration.get(context)
            val touchSlop = viewConfiguration.scaledTouchSlop
            val flingVelocityMin = viewConfiguration.scaledMinimumFlingVelocity
            val flingVelocityMax = viewConfiguration.scaledMaximumFlingVelocity
            return intArrayOf(
                touchSlop,
                flingVelocityMin,
                flingVelocityMax
            )
        }

        /**
         * 计算派生属性
         * @param gradationUnit 刻度单位
         * @return 派生属性
         */
        private fun calculateDerivedAttributes(gradationUnit: Float): Int {
            return (gradationUnit * VALUE_TO_NUMBER_SCALE).toInt()
        }
    }
}

/**
 * 滑动方向类
 * 用于表示刻度尺滑动和吸附的方向
 */
@IntDef(SlideDirection.NONE, SlideDirection.LEFT, SlideDirection.RIGHT)
@Retention(AnnotationRetention.SOURCE)
annotation class SlideDirection {
    companion object {
        // 无方向/静止
        const val NONE = 0
        // 向左滑动（手指右滑，刻度左移， 刻度越来越小）
        const val LEFT = 1
        // 向右滑动（手指左滑，刻度右移， 刻度越来越大）
        const val RIGHT = 2

        /**
         * 根据dx值确定滑动方向
         * @param dx 水平方向上的位移差值
         * @param threshold 判断有效移动的阈值，默认为1
         * @return 对应的滑动方向
         */
        fun fromDelta(dx: Int, threshold: Int = 1): Int {
            return when {
                dx >= threshold -> RIGHT   // 手指向右移动，刻度向左滑
                dx <= -threshold -> LEFT   // 手指向左移动，刻度向右滑
                else -> NONE               // 无明确方向或移动幅度太小
            }
        }
    }
}

/**
 * 表示刻度距离信息的数据类
 * 用于封装某个刻度点的距离、对应的数值和显示值
 *
 * @param distance 距离，表示该刻度点与参考点之间的距离，单位为像素
 * @param number 数值，表示该刻度点对应的放大10倍后的整数数值，避免浮点精度问题
 * @param value 显示值，表示该刻度点的实际值，通常为number除以10后得到的浮点数
 */
data class GradationDistanceInfo(
    val distance: Float,
    val number: Int,
    val value: Float,
)

/**
 * 特殊长刻度规则类
 * 定义需要显示为长刻度的特殊值
 */
data class SpecialGradationRule(
    val value: Float,  // 特殊刻度值
)

/**
 * 刻度间隔规则类
 * 定义一个值区间及其对应的像素间隔
 */
data class GradationGapRule(
    val startValue: Float,  // 区间起始值
    val endValue: Float,    // 区间结束值
    val gapPx: Int        // 像素间隔
)