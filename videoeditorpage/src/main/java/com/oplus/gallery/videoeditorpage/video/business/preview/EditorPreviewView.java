/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorPreviewView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.preview;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.BaseStoryBoardVideoClipEffect.StoryboardType.TYPE_BACKGROUND;
import static com.oplus.gallery.videoeditorpage.common.statistics.StatisticsConstant.INFO_LINK;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.IntEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Size;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;

import com.oplus.gallery.videoeditorpage.utlis.ScreenUtils;
import com.oplus.gallery.videoeditorpage.utlis.VideoUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.EditorStateContext;
import com.oplus.gallery.videoeditorpage.video.business.caption.EditorCaptionState;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.EditTextPreviewView;
import com.oplus.gallery.videoeditorpage.video.business.trim.EditorTrimState;
import com.oplus.gallery.videoeditorpage.widget.EditorMaskEditRectView;
import com.oplus.gallery.videoeditorpage.abilities.data.BaseKeyFrameInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.LocalExtraConstants;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamMaskRegionInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data.MeicamRegionInfo;
import com.oplus.gallery.videoeditorpage.widget.EditorPipEditRectView;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.helper.MaskHelper;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.BaseStoryBoardVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView;
import com.oplus.gallery.videoeditorpage.utlis.MatrixUtilKt;

import java.util.ArrayList;
import java.util.List;

/**
 * 预览页
 */
public class EditorPreviewView extends RelativeLayout implements LifecycleOwner {

    /**
     * 预览窗口的宽高比是否跟随视频素材的宽高比变化
     */
    public static final boolean SHOULD_PREVIEW_WINDOW_MATCH_VIDEO_ASPECT_RATIO = false;

    public final static int EDITOR_PREVIEW_STATUS_READY = 0;
    public final static int EDITOR_PREVIEW_STATUS_STOP = 1;
    public final static int EDITOR_PREVIEW_STATUS_PLAY = 2;
    public final static int EDITOR_PREVIEW_STATUS_FINISH = 3;

    public static final int NUMBER_TWO = 2;
    public static final float NUMBER_HALF = 2F;

    private static final float FLOAT_HALF = 0.5F;
    private static final int DELAYED_SIZE_CHANGED = 50;
    private static final int DELAYED_SIZE_CHANGED_DOUBLE = 100;
    private static final String TAG = "EditorPreviewView";
    private static final int LEFT_TOP_INDEX = 0;

    private static final int RIGHT_BOTTOM_INDEX = 2;

    private static final int EDITOR_PREVIEW_ASPECT_RATIO_ADJUST_ANIMATION_DURATION = 240;

    private static boolean sShowNavigationBar = ScreenUtils.isNavigationBarShowing();
    private final LifecycleRegistry mLifecycleRegistry = new LifecycleRegistry(this);
    private float mMinPreviewAreaHeight = 0F;
    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return mLifecycleRegistry;
    }

    private RelativeLayout mLiveWindow;

    /**
     * 字幕业务在预览区的字体框
     */
    private EditTextPreviewView mEditRectView;
    private EditorMaskEditRectView mEditMaskRectView;
    private EditorPipEditRectView mEditPipRectView;
    private RelativeLayout mHorizontalReferenceLine;
    private RelativeLayout mVerticalReferenceLine;

    private int mDisplayRatio = VideoUtils.VIDEO_ASPECT_RATIO_9_16;
    private boolean mHasAnimation = false;
    private boolean mNeedsUpdateViewLayout = false;
    private boolean mIgnoreAnimat = true;

    private PointF mLastTimelineSize = new PointF(-1, -1);

    private boolean mIsPlaying = false;

    /**
     * 监听预览区大小变化的监听器
     */
    private List<OnPreviewSizeChangedListener> mPreviewSizeChangedListeners = new ArrayList<>();
    private OnAnimationListener mAnimationListener;

    private EditorEngine mEditorEngine;
    private IVideoClip mPipVideoClip;
    private IVideoClip mMaskVideoClip;
    private EditorControlView mControlBar;
    private PointF mLastTranPointF = new PointF();
    private String mClipTrackIndexInfo;
    private Context mContext;
    private MyRunnable mMyRunnable;
    private int mEditPipRectViewVisi = 0;

    private boolean mNeedStopPlayer;

    private PreviewAnimControllerImpl mPreviewAnimController;

    public EditorPreviewView(Context context) {
        super(context);
        init(context);
    }

    public EditorPreviewView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public EditorPreviewView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    /**
     * 获取编辑器预览区域字幕编辑框
     * @return 预览区域的字幕编辑框
     */
    public EditTextPreviewView getEditTextPreviewView() {
        return mEditRectView;
    }

    public PreviewAnimController getAnimController() {
        return mPreviewAnimController;
    }

    public void setNeedStopPlayer(boolean needStopPlayer) {
        mNeedStopPlayer = needStopPlayer;
    }

    public void setAnimationListener(OnAnimationListener animationListener) {
        this.mAnimationListener = animationListener;
    }

    /**
     * 添加预览区域大小改变监听
     * @param previewSizeChangedListener 预览区域大小改变监听
     */
    public void addPreviewSizeChangedListener(OnPreviewSizeChangedListener previewSizeChangedListener) {
        if (!mPreviewSizeChangedListeners.contains(previewSizeChangedListener)) {
            mPreviewSizeChangedListeners.add(previewSizeChangedListener);
        }
    }

    /**
     * 移除预览区域大小改变监听
     * @param previewSizeChangedListener 预览区域大小改变监听
     */
    public void removePreviewSizeChangedListener(OnPreviewSizeChangedListener previewSizeChangedListener) {
        mPreviewSizeChangedListeners.remove(previewSizeChangedListener);
    }

    /**
     * 通知预览区域大小改变监听
     */
    public void notifyPreviewSizeChangedListeners() {
        for (OnPreviewSizeChangedListener listener : mPreviewSizeChangedListeners) {
            listener.onPreviewSizeChanged();
        }
    }

    public View getLiveWindow() {
        return mLiveWindow;
    }

    public void connectPreviewToEditorEngine(EditorEngine engine, boolean isAiEditor) {
        if (engine == null) {
            return;
        }
        engine.setPreviewWindow(mLiveWindow);
        //指定素材填充模式：保持宽高比自适应缩放
        engine.setFillMode(StreamingConstant.FillMode.FILLMODE_PRESERVEASPECTFIT);
        resetRatioByTimeline(engine.getCurrentTimeline(), false, isAiEditor);
    }

    public void resetRatioByTimeline(ITimeline timeline, boolean hasAnimation, boolean isAiEditor) {
        if (timeline != null) {
            int newDisplayRatio = VideoUtils.getAspectRatioFromVideoSize(timeline.getWidth(), timeline.getHeight(), isAiEditor);

            GLog.d(TAG, "resetRatioByTimeline,newDisplayRatio:" + newDisplayRatio
                    + ",mDisplayRatio" + mDisplayRatio);

            if ((newDisplayRatio == mDisplayRatio) && (newDisplayRatio != 0)) {
                return;
            }

            mDisplayRatio = newDisplayRatio;
            mHasAnimation = hasAnimation;
            if (mIgnoreAnimat) {
                mHasAnimation = false;
                mIgnoreAnimat = false;
            }
            setPreviewViewAspectRatio(mDisplayRatio, getWidth(), getHeight(), !mHasAnimation, new Size(timeline.getWidth(), timeline.getHeight()));
        }
    }

    public void showPipRectByVideoClip(IVideoClip videoClip) {
        if ((videoClip == null) || (videoClip.getClipType() == IVideoClip.ClipType.TYPE_PLACE_HOLDER_CLIP)) {
            removePipRect();
            return;
        }
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            GLog.e(TAG, "showPipRectByVideoClip, timeline is null");
            return;
        }
        int trackIndex = videoClip.getTrackIndex();
        if (trackIndex < 0) {
            return;
        }

        IVideoTrack videoTrack = timeline.getVideoTrack(trackIndex);
        if (videoTrack == null) {
            return;
        }
        int cipIndex = videoTrack.getClipIndex(videoClip);
        int userClipIndex = cipIndex;
        for (int i = 0; i < cipIndex; i++) {
            IVideoClip tmpVideoClip = (IVideoClip) videoTrack.getClip(i);
            if ((tmpVideoClip == null) || (tmpVideoClip.getClipType() != IVideoClip.ClipType.TYPE_USER_CLIP)) {
                userClipIndex--;
            }
        }
        mClipTrackIndexInfo = trackIndex + INFO_LINK + userClipIndex;
        mPipVideoClip = videoClip;
        float liveWindowHeight = mEditPipRectView.getLiveWindowHeight();
        float pixTimeLineRatio = liveWindowHeight / timeline.getHeight();
        mEditPipRectView.updateClipPipData(videoClip, pixTimeLineRatio, mDisplayRatio);
        if (videoClip.isInMainTrack() && !videoClip.hasKeyframeEffect()) {
            doChangeBackground(videoClip.getEffectByType(TYPE_BACKGROUND), videoClip, false);
        }
        if (!mIsPlaying) {
            mEditPipRectView.drawClipRectView(videoClip);
        }
    }

    private void removePipRect() {
        if (mEditPipRectView != null) {
            mEditPipRectView.removePipRectView();
        }
        mPipVideoClip = null;
        mClipTrackIndexInfo = null;
    }

    public void updatePipRectForPreview(boolean isPlaying) {
        mIsPlaying = isPlaying;
        mLifecycleRegistry.setCurrentState(isPlaying ? Lifecycle.State.RESUMED : Lifecycle.State.STARTED);
        if ((mEditPipRectView == null) || (mEditPipRectView.getVisibility() != VISIBLE)) {
            return;
        }
        if (isPlaying) {
            mEditPipRectView.removePipRectView();
        } else {
            if (mPipVideoClip != null) {
                long position = mEditorEngine.getTimelineCurrentPosition();
                long inPoint = mPipVideoClip.getInPoint();
                long outPoint = mPipVideoClip.getOutPoint();
                GLog.d(TAG, "updatePipRectForPreview: position " + position + "  inPoint：" + inPoint + "  outPoint：" + outPoint);
                if ((inPoint > position) || (position > outPoint)) {
                    mEditPipRectView.removePipRectView();

                    //处理问题:第一个clip为选中状态，此时滑动到第二个clip，然后在滑回第一个clip，mEditPipRectView又恢复为选中状态的情况
                    mPipVideoClip = null;

                    return;
                }
            }
            showPipRectByVideoClip(mPipVideoClip);
        }
    }

    public boolean isClipSelected() {
        return mPipVideoClip != null;
    }

    public void judgeDegreeAdsorbEvent(float degree) {
        if (mPipVideoClip == null) {
            return;
        }
        PointF adsorbPointF = mPipVideoClip.getPipAdsorbPointF();
        if (adsorbPointF == null) {
            return;
        }
        if (mLastTranPointF.x == adsorbPointF.x) {
            setVerticalReferenceLineVisibility(true);
        } else {
            setVerticalReferenceLineVisibility(false);
        }
        if (mLastTranPointF.y == adsorbPointF.y) {
            setHorizontalReferenceLineVisibility(true);
        } else {
            setHorizontalReferenceLineVisibility(false);
        }
    }

    public void setVerticalReferenceLineVisibility(boolean visible) {
        if (visible) {
            mVerticalReferenceLine.setVisibility(VISIBLE);
        } else {
            mVerticalReferenceLine.setVisibility(GONE);
        }
    }

    public void setHorizontalReferenceLineVisibility(boolean visible) {
        if (visible) {
            mHorizontalReferenceLine.setVisibility(VISIBLE);
        } else {
            mHorizontalReferenceLine.setVisibility(GONE);
        }
    }

    public void judgeDragAdsorbEvent(PointF transPointF) {
        PointF adsorbPointF = mPipVideoClip.getPipAdsorbPointF();
        if (adsorbPointF == null) {
            return;
        }
        if (transPointF.equals(mLastTranPointF.x, mLastTranPointF.y)) {
            if (mLastTranPointF.x == adsorbPointF.x) {
                setVerticalReferenceLineVisibility(true);
            } else {
                setVerticalReferenceLineVisibility(false);
            }
            if (mLastTranPointF.y == adsorbPointF.y) {
                setHorizontalReferenceLineVisibility(true);
            } else {
                setHorizontalReferenceLineVisibility(false);
            }
            return;
        }
        if (mEditPipRectView != null) {
            boolean isVibrate = false;
            if (transPointF.x == adsorbPointF.x) {
                setVerticalReferenceLineVisibility(true);
                if (mLastTranPointF.x != transPointF.x) {
                    isVibrate = true;
                }
            } else {
                setVerticalReferenceLineVisibility(false);
            }
            if (transPointF.y == adsorbPointF.y) {
                setHorizontalReferenceLineVisibility(true);
                if (mLastTranPointF.y != transPointF.y) {
                    isVibrate = true;
                }
            } else {
                setHorizontalReferenceLineVisibility(false);
            }
        }
        mLastTranPointF.x = transPointF.x;
        mLastTranPointF.y = transPointF.y;
    }

    public void setPipDrawRectVisible(int visibility) {
        if (mEditPipRectView != null) {
            mEditPipRectView.setVisibility(visibility);
            if (visibility != VISIBLE) {
                mEditPipRectView.removePipRectView();
                mClipTrackIndexInfo = null;
            }
        }
    }

    public void setDrawRect(List<PointF> list) {
        float top = 0;
        RectF rectf = mPreviewAnimController.getPreviewArea().getValue();
        if (rectf != null) {
            top = rectf.top;
        }
        if ((mEditRectView != null) && (list != null)) {
            // 将字幕区域编辑框顶点点转换到预览区域坐标系
            for (PointF point : list) {
                point.y -=  top;
            }
            mEditRectView.setDrawRect(list, true);
        }
    }

    public void setDrawRectVisible(int visibility) {
        if (mEditRectView != null) {
            mEditRectView.setVisibility(visibility);
        }
    }

    public void setDrawFlag(int flag) {
        if (mEditRectView != null) {
            mEditRectView.setDrawFlag(flag);
        }
    }

    public void setDrawLineFlag(boolean flag) {
        if (mEditRectView != null) {
            mEditRectView.setDrawLineFlag(flag);
        }
    }

    public int getLiveWindowHeight() {
        if (mLiveWindow != null) {
            return mLiveWindow.getHeight();
        }

        return 0;
    }

    public int getLiveWindowWidth() {
        if (mLiveWindow != null) {
            return mLiveWindow.getWidth();
        }

        return 0;
    }

    public void setEditorEngine(EditorEngine editorEngine) {
        this.mEditorEngine = editorEngine;
        if (mEditPipRectView != null) {
            mEditPipRectView.setEngine(editorEngine);
        }
        if (mEditorEngine != null) {
            mPreviewAnimController.getTimelineChangeListener().onCurrentTimelineChanged(mEditorEngine.getCurrentTimeline(), false);
            mEditorEngine.addTimelineChangeListener(mPreviewAnimController.getTimelineChangeListener());
            mEditorEngine.addSeekingListener(mPreviewAnimController.getOnSeekListener());
        }
    }

    public void setControlBar(EditorControlView mControlBar) {
        this.mControlBar = mControlBar;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mLifecycleRegistry.setCurrentState(Lifecycle.State.STARTED);

        mPreviewAnimController.getGestureEnabled().observe(this, enabled -> {
            setOnTouchListener(enabled ? mPreviewAnimController.getTouchListener() : null);
        });
        mPreviewAnimController.getPreviewArea().observe(this, rect -> {
            // 根据预览区域更新编辑区域布局，保证预览区域和编辑区域在Y轴上保持一致
            int height = Math.round(rect.height());
            if (height > 0) {
                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) mEditRectView.getLayoutParams();
                params.height = height;
                params.topMargin = Math.round(rect.top);
                mEditRectView.setLayoutParams(params);
                mEditRectView.requestLayout();
            }
        });
        mPreviewAnimController.getDisplayMatrix().observe(this, params -> {
            if (params != null) {
                mEditorEngine.setDisplayMatrix(params.getFirst(), params.getSecond());
                GLog.d(TAG, LogFlag.DL, "[displayMatrixChanged]: repaintNow=" + params.getSecond() + "\n" + MatrixUtilKt.formatMatrix(params.getFirst()));
            }
            // 预览区域发生变化时发送通知
            notifyPreviewSizeChangedListeners();
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mLifecycleRegistry.setCurrentState(Lifecycle.State.DESTROYED);
        if (mEditorEngine != null) {
            mEditorEngine.removeTimelineListener(mPreviewAnimController.getTimelineChangeListener());
            mEditorEngine.removeSeekingListener(mPreviewAnimController.getOnSeekListener());
        }
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        boolean isShowing = ScreenUtils.isNavigationBarShowing();
        boolean navigationBarVisibilityChanged = isShowing != sShowNavigationBar;
        if (navigationBarVisibilityChanged) {
            sShowNavigationBar = isShowing;
            GLog.d(TAG, "onLayout navigationBarVisibilityChanged sShowNavigationBar=" + sShowNavigationBar);
            onNavigationBarVisibilityChanged(sShowNavigationBar);
        }

        mLiveWindow.setVisibility((bottom - top) < mMinPreviewAreaHeight ? INVISIBLE : VISIBLE);
    }

    public void onNavigationBarVisibilityChanged(boolean isShowNavigationBar) {
        post(new Runnable() {
            @Override
            public void run() {
                if (mContext instanceof EditorStateContext) {
                    ((EditorStateContext) mContext).onNavigationBarVisibilityChanged(isShowNavigationBar);
                }
            }
        });
    }

    /**
     * 添加编辑框监听
     * @param listener 编辑框监听器
     */
    public void addEditTextListener(EditTextPreviewView.OnCaptionEditTextListener listener) {
        mEditRectView.addEditTextListener(listener);
    }

    /**
     * 移除编辑框监听
     * @param listener  编辑框监听器
     */
    public void removeEditTextListener(EditTextPreviewView.OnCaptionEditTextListener listener) {
        mEditRectView.removeEditTextListener(listener);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        if ((oldw != w) || (oldh != h)) {
            Size size = null;
            if (mEditorEngine != null) {
                size = mEditorEngine.getOriginalSizeFromTimeline();
            }
            updateView(size);
        }

        super.onSizeChanged(w, h, oldw, oldh);
    }

    private void init(Context context) {
        mContext = context;
        mLifecycleRegistry.setCurrentState(Lifecycle.State.INITIALIZED);
        View rootView = LayoutInflater.from(context).inflate(R.layout.videoeditor_preview_view_layout, this);
        mLiveWindow = rootView.findViewById(R.id.live_window);
        mEditRectView = rootView.findViewById(R.id.edit_rect_view);
        mEditMaskRectView = rootView.findViewById(R.id.edit_mask_rect_view);
        mEditPipRectView = rootView.findViewById(R.id.edit_pip_rect_view);
        mHorizontalReferenceLine = rootView.findViewById(R.id.pip_horizontal_reference_line);
        mVerticalReferenceLine = rootView.findViewById(R.id.pip_vertical_reference_line);

        mMinPreviewAreaHeight = context.getResources().getDimension(R.dimen.videoeditor_preview_area_min_height);

        mPreviewAnimController = new PreviewAnimControllerImpl(getContext(), getLiveWindow());

        mEditPipRectView.setOnEditTouchListener(new EditorPipEditRectView.OnEditTouchEventListener() {
            @Override
            public void onTouchDown(PointF curPoint) {
            }

            @Override
            public void onScaleAndRotate(float scale, float degree, BaseVideoClipEffect transformClipEffect) {
                if (mPipVideoClip == null) {
                    return;
                }
                judgeDegreeAdsorbEvent(degree);
                doChangeBackground(transformClipEffect, mPipVideoClip, false);
                mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
            }

            @Override
            public void onDrag(PointF transPoint, BaseVideoClipEffect transformClipEffect) {
                if (mPipVideoClip == null) {
                    return;
                }
                judgeDragAdsorbEvent(transPoint);
                doChangeBackground(transformClipEffect, mPipVideoClip, false);
                mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
            }

            @Override
            public void onTouchUp(PointF curPoint, IVideoClip operationClip) {
                GLog.d(TAG, "pip onTouchUp:" + curPoint + " mClipTrackIndexInfo:" + mClipTrackIndexInfo);
                setHorizontalReferenceLineVisibility(false);
                setVerticalReferenceLineVisibility(false);
            }

            @Override
            public void onOutOfPipRectClick(PointF curPoint) {
                checkKeyframeValues(mPipVideoClip);

                selectClickPipRect(curPoint);
            }

            @Override
            public void onPipRectClick() {
                removePipRect();
                mLastTranPointF.x = -1;
                mLastTranPointF.y = -1;
            }
        });

        mEditMaskRectView.setOnEditTouchListener(new EditorMaskEditRectView.OnTouchEventListener() {
            @Override
            public void onScaleAndRotate(float horizontalScaleRatio, float verticalScaleRatio, float rotation, int optType) {
                BaseVideoClipEffect maskEffect = (mMaskVideoClip == null) ? null : mMaskVideoClip.getEffect(StreamingConstant.MaskFx.MASKFX_NAME);
                MeicamRegionInfo regionInfo = getMeicamRegionInfo(maskEffect);
                if (regionInfo == null) {
                    return;
                }
                regionInfo.setHorizontalScaleRatio(Math.max(0, horizontalScaleRatio));
                regionInfo.setVerticalScaleRatio(Math.max(0, verticalScaleRatio));
                regionInfo.setRotation(regionInfo.getRotation() + rotation);

                if (mEditMaskRectView.getVisibility() == View.VISIBLE) {
                    updateMaskEffect(mMaskVideoClip);
                }
                if (optType == EditorMaskEditRectView.OnTouchEventListener.OPTTYPE_HORIZONTAL) {
                    maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_ADJUST, "1");
                    maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_SCALE, "1");
                } else if (optType == EditorMaskEditRectView.OnTouchEventListener.OPTTYPE_VERTICAL) {
                    maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_ADJUST, "1");
                    maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_SCALE, "1");
                } else {
                    maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_RORATE, "1");
                }
            }

            @Override
            public void onTranslation(float horizontalTranslationRatio, float verticalTranslationRatio) {
                BaseVideoClipEffect maskEffect = (mMaskVideoClip == null) ? null : mMaskVideoClip.getEffect(StreamingConstant.MaskFx.MASKFX_NAME);
                MeicamRegionInfo regionInfo = getMeicamRegionInfo(maskEffect);
                if (regionInfo == null) {
                    return;
                }
                horizontalTranslationRatio = Math.min(FLOAT_HALF, Math.max(-FLOAT_HALF, regionInfo.getHorizontalTranslationRatio() + horizontalTranslationRatio));
                verticalTranslationRatio = Math.min(FLOAT_HALF, Math.max(-FLOAT_HALF, regionInfo.getVerticalTranslationRatio() + verticalTranslationRatio));
                regionInfo.setHorizontalTranslationRatio(horizontalTranslationRatio);
                regionInfo.mHorizontalTranslationRelative = horizontalTranslationRatio;
                regionInfo.setVerticalTranslationRatio(verticalTranslationRatio);
                regionInfo.mVerticalTranslationRelative = verticalTranslationRatio;

                if (mEditMaskRectView.getVisibility() == View.VISIBLE) {
                    updateMaskEffect(mMaskVideoClip);
                }
                maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_TRANSLATE, "1");
            }

            @Override
            public void onCornerRatioVary(float cornerRatio) {
                BaseVideoClipEffect maskEffect = (mMaskVideoClip == null) ? null : mMaskVideoClip.getEffect(StreamingConstant.MaskFx.MASKFX_NAME);
                MeicamRegionInfo regionInfo = getMeicamRegionInfo(maskEffect);
                if (regionInfo == null) {
                    return;
                }
                cornerRatio = Math.max(0, Math.min(1, cornerRatio));
                regionInfo.setCornerRatio(cornerRatio);

                if (mEditMaskRectView.getVisibility() == View.VISIBLE) {
                    updateMaskEffect(mMaskVideoClip);
                }
                maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_CORNER, "1");
            }

            @Override
            public void onFeatherWidthVary(float featherWidth) {
                BaseVideoClipEffect maskEffect = (mMaskVideoClip == null) ? null : mMaskVideoClip.getEffect(StreamingConstant.MaskFx.MASKFX_NAME);
                MeicamRegionInfo regionInfo = getMeicamRegionInfo(maskEffect);
                if (regionInfo == null) {
                    return;
                }
                maskEffect.setRegionalFeatherWidth(featherWidth);

                if (mEditMaskRectView.getVisibility() == View.VISIBLE) {
                    updateMaskEffect(mMaskVideoClip);
                }
                maskEffect.getSatisticsMaps().put(LocalExtraConstants.VClipEffect.EXTRA_MASK_BLUR, "1");
            }
        });
    }

    /**
     * 预览区点击片段选中框以外的区域取消选中时重新检查关键帧的信息，
     * 防止点击时偶现误走到EditorPipEditRectView的calculateTranslatePointF事件把关键帧信息都清空了导致关键帧不生效
     *
     * @param videoClip
     */
    private void checkKeyframeValues(IVideoClip videoClip) {
        if (videoClip == null) {
            return;
        }
        BaseVideoClipEffect effect = videoClip.getEffect(StreamingConstant.VideoTransform.TRANSFORM_2D);
        if (effect == null) {
            return;
        }

        if ((effect.getTimeFloatParams() != null) && (!effect.getTimeFloatParams().isEmpty())) { //关键帧信息未被清空时无需重新再设置
            return;
        }

        for (BaseKeyFrameInfo keyframe : videoClip.getKeyFrameList()) {
            effect.setFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_X, keyframe.getTransX(), keyframe.getInPoint() - videoClip.getTrimIn());
            effect.setFloatValAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_Y, keyframe.getTransY(), keyframe.getInPoint() - videoClip.getTrimIn());
            effect.setFloatValAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_X, keyframe.getScaleX(), keyframe.getInPoint() - videoClip.getTrimIn());
            effect.setFloatValAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_Y, keyframe.getScaleY(), keyframe.getInPoint() - videoClip.getTrimIn());
            effect.setFloatValAtTime(StreamingConstant.VideoTransform.PARAM_ROTATION, keyframe.getRotationZ(), keyframe.getInPoint() - videoClip.getTrimIn());
        }
        mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
    }

    public void updateMaskEffect(IVideoClip videoClip) {
        mMaskVideoClip = videoClip;
        MaskHelper.buildMask(mEditorEngine, videoClip, false, null);
        if (!mEditorEngine.isPlaying()) {
            mEditorEngine.seekTo(mEditorEngine.getTimelineCurrentPosition(), 0);
        }
        updateMaskView(videoClip);
    }

    public void updateMaskView(IVideoClip videoClip) {
        mMaskVideoClip = videoClip;
        if ((videoClip == null)) {
            mEditMaskRectView.setMaskType(StreamingConstant.MaskFx.SHAPE_NONE);
            setMaskFrameVisibility(INVISIBLE);
            return;
        }
        BaseVideoClipEffect maskEffect = videoClip.getEffect(StreamingConstant.MaskFx.MASKFX_NAME);
        if ((maskEffect == null) || (maskEffect.getEffectPlayType() == StreamingConstant.MaskFx.SHAPE_NONE)) {
            mEditMaskRectView.setMaskType(StreamingConstant.MaskFx.SHAPE_NONE);
            setMaskFrameVisibility(INVISIBLE);
            return;
        } else {
            mEditMaskRectView.setMaskType(maskEffect.getEffectPlayType());
        }
        long position = mEditorEngine.getTimelineCurrentPosition();
        if ((videoClip.getInPoint() > position) || (videoClip.getOutPoint() < position)) {
            setMaskFrameVisibility(INVISIBLE);
            return;
        }
        MeicamRegionInfo regionInfo = getMeicamRegionInfo(maskEffect);
        if ((regionInfo == null) || (regionInfo.getType() == MeicamMaskRegionInfo.MASK_REGION_TYPE_NONE)) {
            setMaskFrameVisibility(INVISIBLE);
            return;
        }
        int[] liveWindowLocation = new int[2];
        mEditorEngine.getLiveWindowLocation(liveWindowLocation);

        mEditMaskRectView.setMaskFeatherWidth(maskEffect.getRegionalFeatherWidth());

        switch (maskEffect.getEffectPlayType()) {
            case StreamingConstant.MaskFx.SHAPE_LINEAR: {
                MaskHelper.RectInfo rectInfo = MaskHelper.buildSizeInfo(mEditorEngine, regionInfo, videoClip, null, false);
                mEditMaskRectView.buildLinearMask(rectInfo, MaskHelper.buildLinearMaskPointList(null, rectInfo), liveWindowLocation);
                break;
            }
            case StreamingConstant.MaskFx.SHAPE_MIRROR: {
                MaskHelper.RectInfo rectInfo = MaskHelper.buildSizeInfo(mEditorEngine, regionInfo, videoClip, null, false);
                mEditMaskRectView.buildMirrorMask(rectInfo, MaskHelper.buildMirrorMaskPointList(null, rectInfo), liveWindowLocation);
                break;
            }
            case StreamingConstant.MaskFx.SHAPE_CIRCLE: {
                MaskHelper.RectInfo rectInfo = MaskHelper.buildSizeInfo(mEditorEngine, regionInfo, videoClip, null, false);
                mEditMaskRectView.buildCircleMask(rectInfo, liveWindowLocation);
                break;
            }
            case StreamingConstant.MaskFx.SHAPE_RECTANGLE: {
                MaskHelper.RectInfo rectInfo = MaskHelper.buildSizeInfo(mEditorEngine, regionInfo, videoClip, null, false);
                mEditMaskRectView.buildCubicBezierMask(rectInfo, MaskHelper.buildRectMaskPointList(null, rectInfo), liveWindowLocation);
                break;
            }
            case StreamingConstant.MaskFx.SHAPE_LOVEHEART: {
                MaskHelper.RectInfo rectInfo = MaskHelper.buildSizeInfo(mEditorEngine, regionInfo, videoClip, null, false);
                mEditMaskRectView.buildCubicBezierMask(rectInfo, MaskHelper.buildLoveMaskPointList(null, rectInfo), liveWindowLocation);
                break;
            }
            case StreamingConstant.MaskFx.SHAPE_STARTLIKE: {
                MaskHelper.RectInfo rectInfo = MaskHelper.buildSizeInfo(mEditorEngine, regionInfo, videoClip, null, false);
                mEditMaskRectView.buildCubicBezierMask(rectInfo, MaskHelper.buildStarMaskPointList(null, rectInfo), liveWindowLocation);
                break;
            }
            default: {
                setMaskFrameVisibility(INVISIBLE);
                return;
            }
        }
        if (mEditorEngine.isPlaying()) {
            setMaskFrameVisibility(INVISIBLE);
        } else {
            setMaskFrameVisibility(VISIBLE);
        }
    }

    public void setMaskFrameVisibility(int visibility) {
        if (mEditMaskRectView == null) {
            return;
        }
        if (mEditMaskRectView.getVisibility() != visibility) {
            mEditMaskRectView.setVisibility(visibility);
        } else {
            mEditMaskRectView.invalidate();
        }
    }

    private MeicamRegionInfo getMeicamRegionInfo(BaseVideoClipEffect clipEffect) {
        if (clipEffect == null) {
            return null;
        }
        MeicamMaskRegionInfo maskRegionInfo = clipEffect.getRegionInfo();
        if (maskRegionInfo == null) {
            return null;
        }
        List<MeicamRegionInfo> regionInfoList = maskRegionInfo.getRegionInfoArray();
        if (regionInfoList.size() == 0) {
            return null;
        }
        return regionInfoList.get(0);
    }

    private void doChangeBackground(BaseVideoClipEffect transformClipEffect, IVideoClip videoClip, boolean hasKeyframe) {
        if ((transformClipEffect instanceof BaseStoryBoardVideoClipEffect)) {
            changeBackground((BaseStoryBoardVideoClipEffect) transformClipEffect, videoClip, hasKeyframe);
        }
    }

    private void selectClickPipRect(PointF curPoint) {
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            return;
        }
        List<IVideoTrack> videoTrackList = (List<IVideoTrack>) timeline.getVideoTrackList();
        if (videoTrackList == null) {
            return;
        }
        boolean isMontageState = false;
        int minTrack = 1;
        EditorBaseState state = mControlBar.getEditorStateManager().getCurrentEditorState();
        if (state instanceof EditorTrimState) {
            isMontageState = true;
            minTrack = 0;
        }
        long position = mEditorEngine.getTimelineCurrentPosition();
        float liveWindowHeight = mEditPipRectView.getLiveWindowHeight();
        float pixTimeLineRatio = liveWindowHeight / timeline.getHeight();
        for (int i = (videoTrackList.size() - 1); i >= minTrack; i--) {
            if (isMontageState) {
                i = minTrack;
            }
            IVideoTrack videoTrack = videoTrackList.get(i);
            for (int j = 0; j < videoTrack.getUserClipCount(); j++) {
                IVideoClip videoClip = (IVideoClip) videoTrack.getClip(j);
                if ((position >= videoClip.getInPoint()) && (position < videoClip.getOutPoint())) {
                    mEditPipRectView.updateClipPipData(videoClip, pixTimeLineRatio, mDisplayRatio);
                    if (mEditPipRectView.isDrawRectByPointF(videoClip, curPoint)) {
                        mClipTrackIndexInfo = i + INFO_LINK + j;
                        mPipVideoClip = videoClip;
                        return;
                    }
                }
            }
        }
        removePipRect();
    }

    public void onPanelWrapperChanged(boolean end) {
        if (!end) {
            mEditPipRectViewVisi = mEditPipRectView.getVisibility();
            mEditPipRectView.setVisibility(View.INVISIBLE);
            updatePipRectForPreview(true);// TODO: 2021/2/22
        } else {
            postDelayed(new Runnable() {
                @Override
                public void run() {
                    mEditPipRectView.setVisibility(mEditPipRectViewVisi);
                    updatePipRectForPreview(false);// TODO: 2021/2/22
                }
            }, DELAYED_SIZE_CHANGED_DOUBLE);
        }
    }

    private void updateView(Size liveWindowSize) {
        mNeedsUpdateViewLayout = true;
        if (mMyRunnable == null) {
            mMyRunnable = new MyRunnable(liveWindowSize);
        } else {
            mMyRunnable.mLiveWindowSize = liveWindowSize;
        }
        post(new Runnable() {
            @Override
            public void run() {
                setPreviewViewAspectRatioAnimating(mDisplayRatio, getWidth(), getHeight(), !mHasAnimation, liveWindowSize);
            }
        });
        removeCallbacks(mMyRunnable);
        postDelayed(mMyRunnable, DELAYED_SIZE_CHANGED);
    }

    private class MyRunnable implements Runnable {
        Size mLiveWindowSize;

        MyRunnable(Size liveWindowSize) {
            this.mLiveWindowSize = liveWindowSize;
        }

        @Override
        public void run() {
            doUpdateView(mLiveWindowSize);
        }
    }

    private void doUpdateView(Size liveWindowSize) {
        if (!mNeedsUpdateViewLayout) {
            return;
        }

        mNeedsUpdateViewLayout = false;
        setPreviewViewAspectRatio(mDisplayRatio, getWidth(), getHeight(), !mHasAnimation, liveWindowSize);
    }

    private void setPreviewViewAspectRatioAnimating(int ratio, int viewWidth, int viewheight, boolean noAnimation, Size liveWindowSize) {
        ViewGroup.LayoutParams layoutParams = mLiveWindow.getLayoutParams();
        Point pointF = setPreviewViewAspectRatio(ratio, viewWidth, viewheight, liveWindowSize);
        GLog.d(TAG, "setPreviewViewAspectRatioAnimating, size:" + pointF.x + "x" + pointF.y);
        int newWidth = pointF.x;
        int newHeight = pointF.y;
        layoutParams.width = newWidth;
        layoutParams.height = newHeight;
        mLiveWindow.setLayoutParams(layoutParams);
        View realLiveWindow = mEditorEngine.getLiveWindow();
//        if (realLiveWindow != null) {
//            realLiveWindow.addOnLayoutChangeListener(new OnLayoutChangeListener() {
//                @Override
//                public void onLayoutChange(View view, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
//                    Debugger.d(TAG, "setPreviewViewAspectRatioAnimating mNeedStopPlayer:" + mNeedStopPlayer);
//                    if (mEditorEngine != null) {
//                        if (!mEditorEngine.isPlaying()) {
//                            mEditorEngine.stopPlayer();
//                        }
//                    }
//                    post(new Runnable() {
//                        @Override
//                        public void run() {
//                            resetMaskEffect();
//                        }
//                    });
//                    view.removeOnLayoutChangeListener(this);
//                }
//            });
//        }
        mEditPipRectView.setLiveWindowWidthAndHeight(newWidth, newHeight);
    }

    private Point setPreviewViewAspectRatio(int ratio, int viewWidth, int viewheight, Size liveWindowSize) {
        // 根据开关状态决定是否跟随视频宽高比
        if (!SHOULD_PREVIEW_WINDOW_MATCH_VIDEO_ASPECT_RATIO) {
            // 不根据视频宽高比计算大小，而是直接使用父View的尺寸
            // 这样mLiveWindow将始终填充满父View
            return new Point(viewWidth, viewheight);
        }

        // 根据视频宽高比计算预览窗口的尺寸
        int newWidth = 0;
        int newHeight = 0;
        switch (ratio) {
            case VideoUtils.VIDEO_ASPECT_RATIO_ORIGINAL: //原始
            case VideoUtils.VIDEO_ASPECT_RATIO_ORIGINAL_VERTICAL: {
                if ((liveWindowSize != null) && (liveWindowSize.getWidth() != 0) && (liveWindowSize.getHeight() != 0)) {
                    int liveWindowWidth = liveWindowSize.getWidth();
                    int liveWindowHeight = liveWindowSize.getHeight();
                    if (liveWindowWidth * viewheight > liveWindowHeight * viewWidth) {
                        newWidth = viewWidth;
                        newHeight = (int) ((float) viewWidth * liveWindowHeight / liveWindowWidth);
                    } else {
                        newWidth = (int) ((float) viewheight * liveWindowWidth / liveWindowHeight);
                        newHeight = viewheight;
                    }
                } else {
                    if (ratio == VideoUtils.VIDEO_ASPECT_RATIO_ORIGINAL) {
                        newWidth = viewWidth;
                        newHeight = (int) (viewWidth * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_9V21);
                    } else {
                        newWidth = (int) (viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_9V21);
                        newHeight = viewheight;
                    }
                }
                break;
            }
            case VideoUtils.VIDEO_ASPECT_RATIO_1_1: { //1:1
                if (viewWidth < viewheight) {
                    newWidth = viewWidth;
                    newHeight = viewWidth;
                } else {
                    newWidth = viewheight;
                    newHeight = viewheight;
                }
                break;
            }
            case VideoUtils.VIDEO_ASPECT_RATIO_3_4: { //3:4
                if (viewWidth < viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_3V4) {
                    newWidth = viewWidth;
                    newHeight = (int) (viewWidth / VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_3V4);
                } else {
                    newWidth = (int) (viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_3V4);
                    newHeight = viewheight;
                }
                break;
            }
            case VideoUtils.VIDEO_ASPECT_RATIO_4_3: { //4:3
                if (viewWidth < viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_4V3) {
                    newWidth = viewWidth;
                    newHeight = (int) (viewWidth / VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_4V3);
                } else {
                    newWidth = (int) (viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_4V3);
                    newHeight = viewheight;
                }
                break;
            }
            case VideoUtils.VIDEO_ASPECT_RATIO_9_16: { //9:16
                if (viewWidth < viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_9V16) {
                    newWidth = viewWidth;
                    newHeight = (int) (viewWidth / VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_9V16);
                } else {
                    newWidth = (int) (viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_9V16);
                    newHeight = viewheight;
                }
                break;
            }
            case VideoUtils.VIDEO_ASPECT_RATIO_16_9:
            default: { //16:9
                if (viewWidth < viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_16V9) {
                    newWidth = viewWidth;
                    newHeight = (int) (viewWidth / VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_16V9);
                } else {
                    newWidth = (int) (viewheight * VideoUtils.VIDEO_ASPECT_RATIO_FLOAT_16V9);
                    newHeight = viewheight;
                }
                break;
            }
        }
        return new Point(newWidth, newHeight);
    }

    private void setPreviewViewAspectRatio(int ratio, int viewWidth, int viewheight, boolean noAnimation, Size liveWindowSize) {
        ViewGroup.LayoutParams layoutParams = mLiveWindow.getLayoutParams();
        Point pointF = setPreviewViewAspectRatio(ratio, viewWidth, viewheight, liveWindowSize);
        int newWidth = pointF.x;
        int newHeight = pointF.y;
        if (noAnimation) {
            layoutParams.width = newWidth;
            layoutParams.height = newHeight;
            mLiveWindow.setLayoutParams(layoutParams);
            notifyPreviewSizeChangedListeners();
            View realLiveWindow = mEditorEngine.getLiveWindow();
            if (realLiveWindow != null) {
                realLiveWindow.addOnLayoutChangeListener(new OnLayoutChangeListener() {
                    @Override
                    public void onLayoutChange(View view, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                        GLog.d(TAG, "setPreviewViewAspectRatio mNeedStopPlayer:" + mNeedStopPlayer);
                        if (mEditorEngine != null) {
                            if (!mEditorEngine.isPlaying()) {
                                mEditorEngine.stopPlayer();
                            }
                        }
                        post(new Runnable() {
                            @Override
                            public void run() {
                                resetMaskEffect();
                            }
                        });
                        view.removeOnLayoutChangeListener(this);
                    }
                });
            }
        } else {
            startPropertyAnimation(mLiveWindow, layoutParams.width, newWidth, layoutParams.height, newHeight);
        }
        mEditPipRectView.setLiveWindowWidthAndHeight(newWidth, newHeight);
        resetClipPipEffect();
        GLog.d(TAG, "Set display view width:" + layoutParams.width + " height:" + layoutParams.height);
    }

    private void changeBackground(BaseStoryBoardVideoClipEffect storyBackground, IVideoClip videoClip, boolean hasKeyframe) {
        ITimeline currentTimeline = mEditorEngine.getCurrentTimeline();
        if ((videoClip == null) || (!videoClip.isInMainTrack())) {
            GLog.e(TAG, "doChangeBackground: videoClip is null or is not in main track");
            return;
        }

        storyBackground.buildEffectParam(storyBackground.getDirPath(),
                storyBackground.getSourceFileName(), storyBackground.getEffectStrength(),
                currentTimeline.getWidth(), currentTimeline.getHeight(),
                videoClip.getWidth(), videoClip.getHeight(),
                videoClip.getDuration(), videoClip.getExtraVideoRotation(),
                storyBackground.getSubType(), hasKeyframe);
    }

    private void checkAndUpdateClipPosition(IVideoClip videoClip, float tranXScale, float tranYScale) {
        if (videoClip == null) {
            return;
        }
        BaseVideoClipEffect transformClipEffect = videoClip.getEffectByType(TYPE_BACKGROUND);
        ITimeline currentTimeline = mEditorEngine.getCurrentTimeline();
        if ((transformClipEffect == null) || (currentTimeline == null)) {
            return;
        }

        float hasTranX = videoClip.getTransFormFloatValue(transformClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X);
        float hasTranY = videoClip.getTransFormFloatValue(transformClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y);
        float newTranX = hasTranX * tranXScale;
        float newTranY = hasTranY * tranYScale;
        videoClip.setTransFormFloatValue(transformClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_X, newTranX);
        videoClip.setTransFormFloatValue(transformClipEffect, StreamingConstant.VideoTransform.PARAM_TRANS_Y, newTranY);
    }

    public void resetMaskEffect() {
        if (mEditorEngine == null) {
            return;
        }

        int flag = 0;
        EditorBaseState state = ((EditorStateContext) mContext).getCurrentEditorState();
        if (state instanceof EditorCaptionState) {
            flag = EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER | EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_ANIMATED_STICKER_POSTER;
        }
        mEditorEngine.resetMaskEffect(false, flag);
        if ((mEditMaskRectView != null) && (mEditMaskRectView.getVisibility() == VISIBLE)) {
            updateMaskView(mMaskVideoClip);
        }
    }

    private void resetClipPipEffect() {
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            return;
        }
        List<IVideoTrack> videoTrackList = (List<IVideoTrack>) timeline.getVideoTrackList();
        if (videoTrackList == null) {
            return;
        }
        int trackIndex = -1;
        int clipIndex = -1;
        if (mClipTrackIndexInfo != null) {
            String[] pipClipLocation = mClipTrackIndexInfo.split(INFO_LINK);
            if ((pipClipLocation != null) && (pipClipLocation.length > 1)) {
                trackIndex = Integer.parseInt(pipClipLocation[0]);
                clipIndex = Integer.parseInt(pipClipLocation[1]);
            }
        }
        float tranXScale = 1F;
        float tranYScale = 1F;
        if ((mLastTimelineSize.x == -1) || (mLastTimelineSize.y == -1)) {
            mLastTimelineSize.set(timeline.getWidth(), timeline.getHeight());
        } else {
            tranXScale = (float) timeline.getWidth() / mLastTimelineSize.x;
            tranYScale = (float) timeline.getHeight() / mLastTimelineSize.y;
            mLastTimelineSize.set(timeline.getWidth(), timeline.getHeight());
        }
        for (int i = (videoTrackList.size() - 1); i >= 0; i--) {
            IVideoTrack videoTrack = videoTrackList.get(i);
            if (videoTrack == null) {
                continue;
            }
            int userClipIndex = -1;
            for (int j = 0; j < videoTrack.getClipCount(); j++) {
                IVideoClip videoClip = (IVideoClip) videoTrack.getClip(j);
                if (videoClip != null) {
                    if (videoClip.getClipType() == IVideoClip.ClipType.TYPE_USER_CLIP) {
                        videoClip.setPipInitPointFList(null);
                        if (videoClip.isInMainTrack()) {
                            //                            checkAndUpdateClipPosition(videoClip, tranXScale, tranYScale);
                            doChangeBackground(videoClip.getEffectByType(TYPE_BACKGROUND), videoClip, videoClip.hasKeyframeEffect());
                        }
                        userClipIndex++;
                        if ((i == trackIndex) && (clipIndex == userClipIndex)) {
                            mPipVideoClip = videoClip;
                            float liveWindowHeight = mEditPipRectView.getLiveWindowHeight();
                            float pixTimeLineRatio = liveWindowHeight / timeline.getHeight();
                            mEditPipRectView.updateClipPipData(videoClip, pixTimeLineRatio, mDisplayRatio);
                            if (!mIsPlaying && (mEditPipRectView.getVisibility() == VISIBLE)) {
                                mEditPipRectView.drawClipRectView(videoClip);
                            }
                        }
                    }
                }
            }
        }
    }

    private void startPropertyAnimation(final View target, final int startWidth, final int endWidth, final int startHeight, final int endHeight) {
        final IntEvaluator intEvaluator = new IntEvaluator();
        ValueAnimator valueAnimator = ValueAnimator.ofInt(1, 100);
        valueAnimator.setDuration(EDITOR_PREVIEW_ASPECT_RATIO_ADJUST_ANIMATION_DURATION);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                Integer currentAnimatedValue = (Integer) valueAnimator.getAnimatedValue();
                float fraction = currentAnimatedValue / 100f;
                MarginLayoutParams layout = (MarginLayoutParams) target.getLayoutParams();
                layout.width = intEvaluator.evaluate(fraction, startWidth, endWidth);
                layout.height = intEvaluator.evaluate(fraction, startHeight, endHeight);
                target.requestLayout();
                resetMaskEffect();

                if (mAnimationListener != null) {
                    mAnimationListener.onAnimationUpdate();
                }
            }
        });

        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (mAnimationListener != null) {
                    mAnimationListener.onAnimationEnd();
                }
                notifyPreviewSizeChangedListeners();
                resetMaskEffect();
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                if (mAnimationListener != null) {
                    mAnimationListener.onAnimationCancel();
                }
            }
        });

        valueAnimator.start();
        mHasAnimation = false;
    }

    public interface OnAnimationListener {
        void onAnimationUpdate();

        void onAnimationEnd();

        void onAnimationCancel();
    }

    public interface OnPreviewSizeChangedListener {
        void onPreviewSizeChanged();
    }
}
