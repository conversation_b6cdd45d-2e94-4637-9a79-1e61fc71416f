/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TextDecorationViewHolder.kt
 ** Description : 文字装饰 viewHolder
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import android.content.Context
import android.view.ViewGroup
import android.widget.Button
import androidx.core.content.ContextCompat
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.BaseAdapter.BaseVH

/**
 * 字幕文字装饰view holder
 */
class TextDecorationViewHolder(parent: ViewGroup) : BaseVH<TextDecorationItem>(parent, R.layout.videoeditor_item_caption_decoration) {

    /**
     * 字体icon显示控件
     */
    private val captionFontItemView: Button by lazy {
        findViewById(R.id.caption_decoration_item_view)
    }

    init {
        val context = parent.context
        val width = context.getDimensionPixelSizeByScreenSize(
            R.dimen.videoeditor_item_caption_decoration_width,
            R.dimen.videoeditor_item_caption_decoration_middle_and_large_width
        )

        val height = context.getDimensionPixelSizeByScreenSize(
            R.dimen.videoeditor_item_caption_decoration_height,
            R.dimen.videoeditor_item_caption_decoration_middle_and_large_height
        )

        captionFontItemView.layoutParams = captionFontItemView.layoutParams.apply {
            this.width = width
            this.height = height
        }
    }

    override fun bind(textDecorationItem: TextDecorationItem) {
        // 设置选中状态
        captionFontItemView.isSelected = textDecorationItem.selected
        // 设置view的背景
        captionFontItemView.apply {
            background = ContextCompat.getDrawable(context, textDecorationItem.backgroundResId)
        }
    }

    companion object {
        private const val TAG = "TextDecorationViewHolder"
    }
}

/**
 * 根据屏幕大小获取对应的dimen
 * @param smallDimen 小屏幕的dimen
 * @param largeDimen 中大屏幕的dimen
 */
private fun Context.getDimensionPixelSizeByScreenSize(
    smallDimen: Int,
    largeDimen: Int
): Int {
    val dimen = if (isMiddleAndLargeWindow(this)) largeDimen else smallDimen
    return resources.getDimensionPixelSize(dimen)
}