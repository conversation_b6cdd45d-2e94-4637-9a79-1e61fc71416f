/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PlatformVideoThumbnailDecoder.kt
 ** Description : VideoThumbnailDecoder Based on Platform scheme.
 ** Version     : 1.0
 ** Date        : 2020/08/31
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** JinPeng@Apps.Gallery3D             2020/08/31   1.0         build this module
 ** xiewujie@Apps.Gallery3D            2021/11/11   1.1         杜比视频取帧需要使用系统接口
 *********************************************************************************/

package com.oplus.gallery.standard_lib.codec.videoframe

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import com.oplus.gallery.foundation.util.debug.GLog
import java.io.FileDescriptor
import kotlin.math.max
import kotlin.math.roundToInt

class PlatformVideoThumbnailDecoder : VideoThumbnailDecoder() {
    companion object {
        private const val TAG = "PlatformVideoThumbnailDecoder"
    }

    private var retriever: MediaMetadataRetriever? = MediaMetadataRetriever()
    private var height = 0
    private var width = 0

    override fun setDataSource(path: String) {
        runWithTryCatch { retriever?.setDataSource(path) }
    }

    override fun setDataSource(fd: FileDescriptor) {
        GLog.i(TAG, "setDataSource, fileDescriptor : $fd")
        runWithTryCatch { retriever?.setDataSource(fd) }
    }

    override fun setDataSource(context: Context, uri: Uri) {
        GLog.i(TAG, "setDataSource, Uri : $uri")
        runWithTryCatch { retriever?.setDataSource(context, uri) }
    }

    override fun setDataSource(fd: FileDescriptor, offset: Long, length: Long) {
        GLog.i(TAG, "setDataSource, offset : $offset, length : $length")
        runWithTryCatch { retriever?.setDataSource(fd, offset, length) }
    }

    private fun runWithTryCatch(setDataSourceFun: () -> Unit) {
        runCatching {
            setDataSourceFun()
        }.onFailure {
            GLog.e(TAG, " setDataSource, init source fail ${it.message}")
            /*
             * 2020/9/3 duchengsong 如果底层setDataSource出错，抛异常到业务层,通知业务层进行处理
             * 此时上层必须主动catch住，此异常抛出频率较高
             */
        }
    }

    override fun setThumbnailSize(size: Int) {
        if (size <= 0) {
            return
        }
        runCatching {
            height = Integer.decode(retriever?.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT) ?: "0")
            width = Integer.decode(retriever?.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH) ?: "0")
            if ((height != 0) && (width != 0)) {
                val scale = max(size / width.toFloat(), size / height.toFloat())
                // 需要缩放的时候，调整宽高，否则不调整
                if (scale < NO_SCALE) {
                    width = (width * scale).roundToInt()
                    height = (height * scale).roundToInt()
                }
            }
        }.onFailure {
            GLog.e(TAG, "setThumbnailSize, extract metadata failed ${it.message}")
            width = 0
            height = 0
        }
    }

    override fun decodeFrameBitmapAtTime(timeUs: Long, size: Int): Bitmap? {
        return decodeFrameBitmapAtTime(timeUs, size, OptionFlag.OPTION_CLOSEST)
    }

    override fun decodeFrameBitmapAtTime(timeUs: Long, size: Int, optionFlag: OptionFlag): Bitmap? {
        if (retriever == null) {
            GLog.w(TAG, "[decodeFrameBitmapAtTime] can't decode bitmap because retriever is null, it may be released")
            return null
        }
        val frameOptionFlag = getOptionFlag(optionFlag)
        return try {
            if (size <= 0) {
                if ((width != 0) && (height != 0)) {
                    retriever?.getScaledFrameAtTime(timeUs, frameOptionFlag, width, height)
                } else {
                    retriever?.getFrameAtTime(timeUs, frameOptionFlag)
                }
            } else {
                retriever?.getScaledFrameAtTime(timeUs, frameOptionFlag, size, size)
            }
        } catch (e: Exception) {
            GLog.e(TAG, "decodeFrameBitmapAtTime failed $e")
            null
        }
    }

    override fun close() {
        runCatching {
            retriever?.release()
            retriever = null
        }.onFailure {
            GLog.e(TAG, "close, retriever release failed ${it.message}")
        }
    }

    override fun isValid(): Boolean {
        return retriever != null
    }

    private fun getOptionFlag(flag: OptionFlag): Int {
        val frameFlag = when (flag) {
            OptionFlag.OPTION_PREVIOUS_SYNC -> MediaMetadataRetriever.OPTION_PREVIOUS_SYNC
            OptionFlag.OPTION_NEXT_SYNC -> MediaMetadataRetriever.OPTION_NEXT_SYNC
            OptionFlag.OPTION_CLOSEST_SYNC -> MediaMetadataRetriever.OPTION_CLOSEST_SYNC
            OptionFlag.OPTION_CLOSEST -> MediaMetadataRetriever.OPTION_CLOSEST
        }
        return frameFlag
    }
}