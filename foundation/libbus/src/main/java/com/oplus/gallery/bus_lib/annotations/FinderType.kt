/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FinderType.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/08/14		1.0		OPLUS_ARCH_BUS
 *********************************************************************************/

package com.oplus.gallery.bus_lib.annotations

import androidx.annotation.IntDef

@Retention(AnnotationRetention.SOURCE)
@IntDef(
        FinderType.CLASS_METHOD_FINDER,
        FinderType.CLASS_ALL_METHOD_FINDER
)
annotation class FinderType {
    companion object {
        const val CLASS_METHOD_FINDER = 0
        const val CLASS_ALL_METHOD_FINDER = 1
    }
}