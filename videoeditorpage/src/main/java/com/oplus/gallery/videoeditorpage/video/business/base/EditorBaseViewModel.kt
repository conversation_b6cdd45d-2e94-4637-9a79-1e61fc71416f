/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorBaseVM
 ** Description: viewmodel基类
 ** Version: 1.0
 ** Date : 2025/4/8
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2025/04/08    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.base

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip

/**
 * viewmodel基类
 * viewmodel的作用是替换掉state，旧的编辑框架使用的是state来实现业务逻辑，不过mvc的模式会导致controller和state互相持有来进行通行，
 * 使用viewmodel的话通过observe liveData数据的回调来进行监听。使用了viewmodel之后，state基本只是实现父类里边的逻辑进行转发（取消，保存）
 * 由于历史原因，其他页面扔在使用state，当前仍然保留state的实现。
 */
open class EditorBaseViewModel(application: Application) : AndroidViewModel(application) {

    /**
     * 获取当前视频段的clip
     * @param engine engine实现类
     * @return 返回当前clip
     */
    fun getCurrentVideoClip(engine: EditorEngine): IVideoClip? {
        val timeline = engine.currentTimeline ?: return null
        val videoTrack = timeline.getVideoTrack(TRACK_INDEX_ZERO) ?: return null
        return videoTrack.getClipByTimelinePostion(engine.timelineCurrentPosition)
    }

    /**
     * 计算当前片段在时间线中的开始时间和结束时间
     * @return 开始时间和结束时间
     */
    fun getClipStartEndTime(engine: EditorEngine): Pair<Long, Long>? {
        val currentVideoClip = getCurrentVideoClip(engine) ?: return null
        return currentVideoClip.inPoint.let { startTime ->
            Pair(startTime, startTime + currentVideoClip.duration)
        }
    }

    fun getInPoint(clip: IVideoClip): Long {
        return clip.inPoint
    }

    companion object {
        /**
         * 单个图片的默认时长
         */
        const val SINGLE_CLIP_SECONDS = 3
        const val SINGLE_CLIP_START = 0
        const val TRACK_INDEX_ZERO = 0
    }
}