/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseVideoTimelineEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

public class BaseVideoTimelineEffect extends BaseVideoEffect implements IBaseClip {
    protected long mInTime = 0;
    protected long mOutTime = 0;
    protected int mTrackIndex = 0;
    protected boolean mIsUseAllClipExceptTail = false;
    protected Map<String, String> mSatisticsMaps = new HashMap<>();//本地用，用来存放额外信息，如埋点需要的
    protected HashMap<String, Object> mAttachment = new HashMap<>();//存放额外信息

    private boolean mIsFromAiTemplate = false;
    // 代表这个特效是否需要自动联动timeline duration
    private boolean mIsNeedFixTimeline = false;

    public BaseVideoTimelineEffect(String name) {
        super(name);
    }

    public BaseVideoTimelineEffect(String name, long mInTime, long mOutTime, int mTrackIndex, boolean mIsUseAllClipExceptTail) {
        super(name);
        this.mInTime = mInTime;
        this.mOutTime = mOutTime;
        this.mTrackIndex = mTrackIndex;
        this.mIsUseAllClipExceptTail = mIsUseAllClipExceptTail;
    }

    @Override
    public long getInPoint() {
        return mInTime;
    }

    @Override
    public long getOutPoint() {
        return mOutTime;
    }

    public boolean isFromAiTemplate() {
        return mIsFromAiTemplate;
    }

    public void setIsFromAiTemplate(boolean isFromAiTemplate) {
        this.mIsFromAiTemplate = isFromAiTemplate;
    }

    public void setInTime(long inTime) {
        mInTime = inTime;
    }

    public long getInTime() {
        return mInTime;
    }

    public void setOutTime(long inTime) {
        mOutTime = inTime;
    }

    public long getOutTime() {
        return mOutTime;
    }

    public int getTrackIndex() {
        return mTrackIndex;
    }

    public void setTrackIndex(int mTrackIndex) {
        this.mTrackIndex = mTrackIndex;
    }

    public boolean getIsUseAllClipExceptTail() {
        return mIsUseAllClipExceptTail;
    }

    public void setIsUseAllClipExceptTail(boolean isUseAllClipExceptTail) {
        this.mIsUseAllClipExceptTail = isUseAllClipExceptTail;
    }

    public boolean isNeedFixTimeline() {
        return mIsNeedFixTimeline;
    }

    public void setNeedFixTimeline(boolean needFixTimeline) {
        mIsNeedFixTimeline = needFixTimeline;
    }

    @NonNull
    public Map<String, String> getSatisticsMaps() {
        if (mSatisticsMaps == null) {
            mSatisticsMaps = new HashMap<>();
        }
        return mSatisticsMaps;
    }

    public Object getAttachment(String key) {
        return mAttachment.get(key);
    }

    public void setAttachment(String key, Object value) {
        mAttachment.put(key, value);
    }

}
