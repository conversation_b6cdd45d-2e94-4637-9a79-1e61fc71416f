/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MeicamAudioClip.java
 ** Description: XXXXXXXXXXXXXXXXXXXXX.
 ** Version: 1.0
 ** Date : 2017/11/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/13    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import android.text.TextUtils;

import com.meicam.sdk.NvsAudioClip;
import com.meicam.sdk.NvsAudioResolution;
import com.meicam.sdk.NvsAudioTrack;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.framework.abilities.videoedit.data.SongInfo;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryAudioClip;

public class MeicamAudioClip implements IGalleryAudioClip {
    private static final String TAG = "MeicamAudioClip";

    public static final int DEFAULT_MUSIC_FADE_TIME = 5000 * 1000; // 5000ms
    private static final int SAMPLE_RETE = 44100;
    private static final int CHANNEL_COUNT = 2;

    private NvsTimeline mTimeline;
    private NvsAudioTrack mAudioTrack;
    private MeicamVideoEngine mEngine;
    private String mMusicContentUri;
    private boolean mIsAudioVolumeMuted = false;
    private int mCurrentMusicIndex = 0;
    private int mOldMusicIndex = 0;
    private int mLocalMusicIndex;
    private long mMusicStart = 0;
    private long mMusicEnd = 0;
    private long mMusicDuration;
    private SongInfo mCurrentSongInfo;

    public MeicamAudioClip(MeicamVideoEngine engine) {
        mEngine = engine;
    }

    public void setTimeline(NvsTimeline timeline) {
        mTimeline = timeline;
        GLog.d(TAG, "setTimeline audioTrackCount:" + mTimeline.audioTrackCount());
        // new another one audio track, timeline can have multi audio track
        mAudioTrack = timeline.appendAudioTrack();
    }

    public static NvsAudioResolution getAudioResolution() {
        NvsAudioResolution audioRes = new NvsAudioResolution();
        audioRes.sampleRate = SAMPLE_RETE;
        audioRes.channelCount = CHANNEL_COUNT;
        return audioRes;
    }

    private long getClipDuration(NvsAudioClip clip) {
        if (clip == null) {
            GLog.w(TAG, "addAudio getClipDuration clip is null.");
            return 0;
        }
        return clip.getOutPoint() - clip.getInPoint();
    }

    // fill audio clip to audio track, which duration greater than or equal to timeline duration
    private boolean fillClipToAudioTrack(String filePath, long startTime, boolean trim, long start, long end) {
        boolean result = true;
        long clipDuration = 0;
        NvsAudioClip clip = null;
        if (trim) {
            clip = mAudioTrack.addClip(filePath, startTime, start, end);
        } else {
            clip = mAudioTrack.addClip(filePath, startTime);
        }
        if (clip == null) {
            GLog.e(TAG, "fillClipToAudioTrack() addclip is null");
            return false;
        }
        clipDuration = getClipDuration(clip);
        long allTime = mTimeline.getDuration() - startTime;
        GLog.d(TAG, "fillClipInTimelineTrack"
                + ", clipCount:" + mAudioTrack.getClipCount()
                + ", trim:" + trim
                + ", start:" + start
                + ", end:" + end
                + ", allTime:" + allTime
                + ", clipDuration:" + clipDuration);
        if ((clipDuration > 0) && (clipDuration < allTime)) {
            int count = (int) ((allTime - clipDuration) / clipDuration + 1);
            for (int i = 0; i < count; i++) {
                if (trim) {
                    clip = mAudioTrack.appendClip(filePath, start, end);
                } else {
                    clip = mAudioTrack.appendClip(filePath);
                }
                clipDuration += getClipDuration(clip);
                GLog.d(TAG, "fillClipInTimelineTrack() clipDuration:" + clipDuration);
                if (clip == null) {
                    GLog.w(TAG, "fillClipInTimelineTrack clip is null. i = " + i);
                    result = false;
                }
            }
        }
        return result;
    }

    private void cleanSlowMotionMusic() {
        if (mTimeline == null) {
            GLog.w(TAG, "cleanSlowMotionMusic mTimeline is null.");
            return;
        }
        NvsVideoTrack videoTrack = mTimeline.getVideoTrackByIndex(0);
        if ((videoTrack == null) || (videoTrack.getClipCount() <= 0)) {
            GLog.w(TAG, "cleanSlowMotionMusic mVideoTrack is null or count is 0.");
            return;
        }
        if ((Float.compare(mEngine.getGalleryVideoClip().getSlowSpeed(), 1f) == 0)
                || (Float.compare(mEngine.getGalleryVideoClip().getSlowSpeed(), 0f) == 0)) {
            GLog.d(TAG, "cleanSlowMotionMusic is not slow motion, return.");
            return;
        }
        GLog.d(TAG, "cleanSlowMotionMusic, getClipCount = " + videoTrack.getClipCount()
                + ", getSlowSpeed = " + mEngine.getGalleryVideoClip().getSlowSpeed());
        try {
            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                NvsVideoClip clip = videoTrack.getClipByIndex(i);
                if ((clip != null) && (Float.compare((float) clip.getSpeed(), 1f) != 0)) {
                    GLog.d(TAG, "cleanSlowMotionMusic"
                            + ", getSpeed = " + clip.getSpeed()
                            + ", getInPoint = " + clip.getInPoint()
                            + ", getOutPoint = " + clip.getOutPoint());
                    // to remove the audio in slow motion area
                    removeMusic(clip.getInPoint(), clip.getOutPoint());
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "cleanSlowMotionAreaMusic error:", e);
        }
    }

    public boolean isAudioChanged() {
        return (mCurrentMusicIndex != 0);
    }

    @Override
    public boolean addSong(SongInfo songInfo) {
        if (songInfo == null) {
            return false;
        }
        mCurrentSongInfo = songInfo;
        mCurrentMusicIndex = songInfo.getPosition();
        String path = songInfo.getFilePath();
        if (TextUtils.isEmpty(path)) {
            GLog.d(TAG, "addSong, path is empty, return");
            return false;
        }
        boolean result = false;
        if ((mTimeline != null) && (mAudioTrack != null)) {
            GLog.d(TAG, "addSong count:" + mAudioTrack.getClipCount()
                    + ", path:" + path);
            mAudioTrack.removeAllClips();
            result = fillClipToAudioTrack(path, 0,
                    false, 0, 0);
            if (result) {
                openAudioVolume(true);
                cleanSlowMotionMusic();
            } else {
                mAudioTrack.removeAllClips();
            }
        }
        if (result) {
            mMusicContentUri = path;
            mMusicStart = 0;
            mMusicEnd = 0;
            mTimeline.setAudioFadeOutDuration(DEFAULT_MUSIC_FADE_TIME);
        }
        return result;
    }

    @Override
    public SongInfo getCurrentSongInfo() {
        return mCurrentSongInfo;
    }

    @Override
    public void setCurrentSongItem(SongInfo songInfo) {
        mCurrentSongInfo = songInfo;
    }

    @Override
    public boolean addTrimMusic(String contentUri, long start, long end, long duration) {
        boolean result = false;
        mCurrentMusicIndex = mLocalMusicIndex;
        if ((mTimeline != null) && (mAudioTrack != null)) {
            GLog.d(TAG, "addTrimMusic count:" + mAudioTrack.getClipCount()
                    + ", contentUri:" + contentUri
                    + ", start:" + start
                    + ", end:" + end);
            mAudioTrack.removeAllClips();
            result = fillClipToAudioTrack(contentUri, 0,
                    true, start * MeicamVideoEngine.MILLIS_TIME_BASE, end * MeicamVideoEngine.MILLIS_TIME_BASE);
            if (result) {
                openAudioVolume(true);
                cleanSlowMotionMusic();
            } else {
                mAudioTrack.removeAllClips();
            }
        }
        if (result) {
            mMusicStart = start * MeicamVideoEngine.MILLIS_TIME_BASE;
            mMusicEnd = end * MeicamVideoEngine.MILLIS_TIME_BASE;
            mMusicDuration = duration;
            mMusicContentUri = contentUri;
            mTimeline.setAudioFadeOutDuration(DEFAULT_MUSIC_FADE_TIME);
        }
        return result;
    }

    @Override
    public boolean reAlignMusic(long startTime) {
        boolean result = false;
        if ((mCurrentMusicIndex != 0) && !TextUtils.isEmpty(mMusicContentUri)) {
            GLog.d(TAG, "reAlignMusic mMusicPath:" + mMusicContentUri);
            mAudioTrack.removeAllClips();
            result = fillClipToAudioTrack(mMusicContentUri, startTime * MeicamVideoEngine.MILLIS_TIME_BASE,
                    (mMusicStart > 0) || (mMusicEnd > 0), mMusicStart, mMusicEnd);
            if (result) {
                openAudioVolume(true);
                cleanSlowMotionMusic();
            } else if (mCurrentMusicIndex == mLocalMusicIndex) {
                GLog.w(TAG, "reAlignMusic music file faild");
                removeMusic();
            }
        }
        return result;
    }

    //remove all audio from track.
    @Override
    public boolean removeMusic() {
        if (mAudioTrack != null) {
            GLog.d(TAG, "removeMusic count:" + mAudioTrack.getClipCount());
            mCurrentMusicIndex = 0;
            mMusicContentUri = null;
            mMusicStart = 0;
            mMusicEnd = 0;
            openAudioVolume(false);
            mTimeline.setAudioFadeOutDuration(0);
            mCurrentSongInfo = null;
            return mAudioTrack.removeAllClips();
        }
        return false;
    }

    @Override
    public boolean removeMusic(long start, long end) {
        return mAudioTrack.removeRange(start, end, true);
    }

    @Override
    public boolean changeAudioSpeed(float speed) {
        reAlignMusic(0);
        if (mAudioTrack != null) {
            GLog.d(TAG, "[changeAudioSpeed] speed:" + speed);
            try {
                for (int i = 0; i < mAudioTrack.getClipCount(); i++) {
                    NvsAudioClip clip = mAudioTrack.getClipByIndex(i);
                    if (clip != null) {
                        // 设置音频轨道倍速，且保持音调不变
                        clip.changeSpeed(speed, true);
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, "[changeAudioSpeed] cannot speed audio clip: " + e);
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public int getCurrentMusicIndex() {
        return mCurrentMusicIndex;
    }

    @Override
    public void setLocalMusicIndex(int index) {
        mLocalMusicIndex = index;
        mOldMusicIndex = mCurrentMusicIndex;
        mCurrentMusicIndex = -1;
    }

    @Override
    public void resetLocalMusicIndex() {
        mCurrentMusicIndex = mOldMusicIndex;
        mOldMusicIndex = 0;
    }

    @Override
    public String getLocalMusicUri() {
        if (mCurrentMusicIndex == mLocalMusicIndex) {
            return mMusicContentUri;
        }
        return null;
    }

    @Override
    public long getLocalMusicTrimStart() {
        return mMusicStart / MeicamVideoEngine.MILLIS_TIME_BASE;
    }

    @Override
    public long getLocalMusicTrimEnd() {
        return mMusicEnd / MeicamVideoEngine.MILLIS_TIME_BASE;
    }

    public long getLocalMusicDuration() {
        return mMusicDuration;
    }

    @Override
    public void saveMusicState() {
        if (mAudioTrack != null) {
            float leftVolume = mAudioTrack.getVolumeGain().leftVolume;
            float rightVolume = mAudioTrack.getVolumeGain().rightVolume;
            mIsAudioVolumeMuted = (Float.compare(leftVolume, 0f) == 0)
                    && (Float.compare(rightVolume, 0f) == 0);
            GLog.d(TAG, "saveMusicState, leftVolume:" + leftVolume
                    + ", rightVolume:" + rightVolume
                    + ", mIsAudioVolumeMuted:" + mIsAudioVolumeMuted);
        }
    }

    @Override
    public void resetMusicState() {
        GLog.d(TAG, "resetMusicState mIsAudioVolumeMuted:" + mIsAudioVolumeMuted);
        openAudioVolume(!mIsAudioVolumeMuted);
        if (!mIsAudioVolumeMuted) {
            cleanSlowMotionMusic();
        }
    }

    private void openAudioVolume(boolean open) {
        if (mAudioTrack != null) {
            GLog.d(TAG, "openAudioVolume open:" + open);
            float volume = open ? 1f : 0f;
            mAudioTrack.setVolumeGain(volume, volume);
            openThemeVolume(!open);
        }
    }

    private void openThemeVolume(boolean open) {
        if (mTimeline != null) {
            GLog.d(TAG, "openThemeVolume open:" + open);
            float volume = open ? 1f : 0f;
            mTimeline.setThemeMusicVolumeGain(volume, volume);
        }
    }
}