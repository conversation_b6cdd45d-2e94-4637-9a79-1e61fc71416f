/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - IVersionedCodec.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2018/04/12
 ** Author      : <PERSON><PERSON>.<PERSON>@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  Jian.Shu@Apps.Gallery3D  2018/04/12  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.foundation.codec.supertext;

import java.io.RandomAccessFile;
import java.nio.ByteBuffer;

public interface IVersionedCodec<T> {

    T decode(ByteBuffer buffer, int offset, int length);

    T decode(RandomAccessFile randomAccessFile, int offset, int length);

    ByteBuffer encode(ByteBuffer buffer, int offset, int length, T object);
}
