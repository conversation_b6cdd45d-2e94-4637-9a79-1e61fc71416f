/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : AiCompositionInterpolateStrategy.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/12/5 19:44
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2024/12/5  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aicomposition.ui

import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.gallery.foundation.ui.animationcontrol.AnimateOperation
import com.oplus.gallery.foundation.ui.animationcontrol.AnimationInterpolateStrategy
import com.oplus.gallery.foundation.ui.animationcontrol.IInterpolatorConfig

/**
 * AI构图裁切区域切换动画插值器
 */
class AiCompositionInterpolateStrategy : AnimationInterpolateStrategy {
    private val defaultInterpolatorConfig by lazy {
        IInterpolatorConfig.InterpolatorConfig(
            INTERPOLATOR,
            ANIMATION_DURATION
        )
    }

    override fun getInterpolatorConfig(operation: AnimateOperation): IInterpolatorConfig {
        return defaultInterpolatorConfig
    }

    companion object {
        const val ANIMATION_DURATION = 500L
        val INTERPOLATOR = COUIMoveEaseInterpolator()
    }
}