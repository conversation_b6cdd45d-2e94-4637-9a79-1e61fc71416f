/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MosaicGestureRecognizerView.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/6/27
 * Author: zhangjisong
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * zhangjisong      2024/6/27        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.mosaic.view

import android.content.Context
import android.graphics.PointF
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizerView2

/**
 * 马赛克手势识别器
 */
class MosaicGestureRecognizerView(context: Context) : GestureRecognizerView2(context) {

    fun mapPointTopDrawingBound(x: Float, y: Float): PointF {
        return mapPointTopDrawingBound(x, y, currentDrawingOutBound)
    }
}