/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/7/1
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/7/1  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.util;

import com.oplus.gallery.foundation.util.thread.MainSwitchHandler;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.memories.resource.callback.DaoResultCallback;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public abstract class AsyncDbCommand<T> {

    private static final String TAG = "AsyncDbCommand";
    private static ExecutorService sExecutorService = Executors.newSingleThreadExecutor();

    private DaoResultCallback mCallback;

    public AsyncDbCommand setResultCallback(DaoResultCallback callback) {
        this.mCallback = callback;
        return this;
    }

    public final void execute() {
        sExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    postResult(doInBackground());
                } catch (Exception e) {
                    GLog.e(TAG, "run", e);
                }
            }
        });
    }

    private void postResult(final T result) {
        MainSwitchHandler.getInstance().post(new Runnable() {
            @Override
            public void run() {
                if (mCallback != null) {
                    mCallback.onResult(result);
                }
            }
        });
    }

    protected abstract T doInBackground();
}
