/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - MeicamVideoFx.java
 ** Description: for meicam video fx.
 ** Version: 1.0
 ** Date : 2018/07/04
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                  <data>        <version>     <desc>
 **  <EMAIL>    2018/07/04    1.0           build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import android.text.TextUtils;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsStreamingContext;
import com.oplus.gallery.framework.abilities.videoedit.data.FxInfo;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoFx;

import java.util.function.Function;

public class MeicamVideoFx implements IGalleryVideoFx {

    private static final String TAG = "MeicamVideoFx";
    private static final String FX_FILE_TYPE = ".videofx";
    private static final String INVALID_FX_ID = "-1";
    private final static int ONE_SECOND = TimeUtils.TIME_1_SEC_IN_MS;
    private MeicamTimeline mMeicamTimeline;
    private MeicamTimelineEffect mMeicamTimelineEffect;
    private NvsStreamingContext mStreamingContext;
    private long mAppliedFxTime = 0;
    private boolean mVideoFxChanged = false;
    private FxInfo mCurrentFxImfo;
    private FxInfo mLastInVisibleFxInfo;
    private long mLastItemInTimeUs = 0L;
    private long mLastItemOutTimeUs = 0L;
    private long mInTimeUs = 0L;
    private long mOutTimeUs = 0L;

    public MeicamVideoFx(NvsStreamingContext streamingContext) {
        mStreamingContext = streamingContext;
    }

    public void setTimeline(MeicamTimeline timeline) {
        mMeicamTimeline = timeline;
    }

    @Override
    public long getAppliedFxTime() {
        GLog.d(TAG, "getAppliedFxTime() mAppliedFxTime:" + mAppliedFxTime
                + " mLastInVisibleFxItem " + mLastInVisibleFxInfo);
        if (isCurrentItemInvalid()) {
            return 0;
        }
        return mAppliedFxTime;
    }

    @Override
    public void setAppliedFxTime(long time) {
        GLog.d(TAG, "setAppliedFxTime() time:" + time);
        mAppliedFxTime = time;
    }

    @Override
    public boolean isVideoFxChanged() {
        return mVideoFxChanged;
    }

    @Override
    public void setCurrentFxInfo(FxInfo fxInfo) {
        mCurrentFxImfo = fxInfo;
    }

    @Override
    public FxInfo getCurrentFxInfo() {
        return mCurrentFxImfo;
    }

    @Override
    public void removeVideoFx() {
        mVideoFxChanged = false;
        if (mMeicamTimelineEffect != null) {
            mMeicamTimeline.removeEffectFromTimeline(mMeicamTimelineEffect);
            mMeicamTimelineEffect = null;
        }
    }

    @Override
    public void moveVideoFx(Function<Long, Long> offsetCalculateFunction) {
        long offset = offsetCalculateFunction.apply(mInTimeUs);
        moveVideoFx(offset);
    }

    private boolean isCurrentItemInvalid() {
        return (mCurrentFxImfo == null) || TextUtils.isEmpty(mCurrentFxImfo.getFilePath());
    }

    @Override
    public void moveVideoFx(long offsetUs) {
        if (mMeicamTimelineEffect == null) {
            // 如果上一个特效被移出,恢复它的时间
            if ((mLastInVisibleFxInfo != null) && isCurrentItemInvalid()) {
                mInTimeUs = mLastItemInTimeUs;
                mOutTimeUs = mLastItemOutTimeUs;
            } else {
                mLastInVisibleFxInfo = null;
                return;
            }
        }
        mInTimeUs += offsetUs;
        mOutTimeUs += offsetUs;

        if (mOutTimeUs <= 0) {
            setVisibility(false);
        } else {
            if (mInTimeUs >= mMeicamTimeline.getNvsTimeline().getDuration()) {
                setVisibility(false);
                return;
            }
            setVisibility(true);
            if (mMeicamTimelineEffect != null) {
                if (offsetUs <= 0) {
                    mMeicamTimelineEffect.changeInPoint(mInTimeUs);
                    mMeicamTimelineEffect.changeOutPoint(mOutTimeUs);
                } else {
                    mMeicamTimelineEffect.changeOutPoint(mOutTimeUs);
                    mMeicamTimelineEffect.changeInPoint(mInTimeUs);
                }
            }
            updateAppliedFxTime();
        }

    }

    @Override
    public void moveVideoFx(float speed) {
        if (Float.compare(speed, 0f) <= 0) {
            return;
        }
        if (mMeicamTimelineEffect == null) {
            // 如果上一个特效被移出,调速它依然不显示,仅更新时间
            if ((mLastInVisibleFxInfo != null) && isCurrentItemInvalid()) {
                mLastItemInTimeUs = (long) ((float) mLastItemInTimeUs / speed);
                mLastItemOutTimeUs = (long) ((float) mLastItemOutTimeUs / speed);
            }
            return;
        }

        mInTimeUs = (long) ((float) mInTimeUs / speed);
        mOutTimeUs = (long) ((float) mOutTimeUs / speed);

        if (speed < 1f) {
            mMeicamTimelineEffect.changeOutPoint(mOutTimeUs);
            mMeicamTimelineEffect.changeInPoint(mInTimeUs);
        } else {
            mMeicamTimelineEffect.changeInPoint(mInTimeUs);
            mMeicamTimelineEffect.changeOutPoint(mOutTimeUs);
        }
        updateAppliedFxTime();
    }

    private void updateAppliedFxTime() {
        long fxTime = (mInTimeUs > 0) ? mInTimeUs : 0L;
        if ((mCurrentFxImfo != null) && (fxTime + mCurrentFxImfo.getDuration() > mMeicamTimeline.getNvsTimeline().getDuration())) {
            fxTime = mMeicamTimeline.getNvsTimeline().getDuration() - mCurrentFxImfo.getDuration();
        }
        setAppliedFxTime(fxTime / MeicamVideoEngine.MILLIS_TIME_BASE);
    }

    /**
     * 业务可以通过currentFxItem判断当前是否有特效
     *
     * @param visibility
     */
    public void setVisibility(boolean visibility) {
        // lastItem不为空说明已有item隐藏了
        if ((visibility && (mLastInVisibleFxInfo == null))
                || (!visibility && (mLastInVisibleFxInfo != null))) {
            mLastItemInTimeUs = mInTimeUs;
            mLastItemOutTimeUs = mOutTimeUs;
            return;
        }
        if (!visibility) {
            mVideoFxChanged = false;
            mMeicamTimeline.removeEffectFromTimeline(mMeicamTimelineEffect);
            mLastInVisibleFxInfo = mCurrentFxImfo;
            // 移出特效时,记录时间,用以恢复(因为调试其他特效不保存时时间会和之前的不一致)
            mLastItemInTimeUs = mInTimeUs;
            mLastItemOutTimeUs = mOutTimeUs;
            setCurrentFxInfo(null);
        } else {
            mVideoFxChanged = true;
            setCurrentFxInfo(mLastInVisibleFxInfo);
            applyVideoFx(mCurrentFxImfo, mInTimeUs / ONE_SECOND);
            clearLastFx();
        }
    }

    public void clearLastFx() {
        mLastInVisibleFxInfo = null;
        mLastItemInTimeUs = 0;
        mLastItemOutTimeUs = 0;
    }

    @Override
    public int installVideoFx(FxInfo fxInfo) {
        if (fxInfo == null) {
            GLog.e(TAG, "installVideoFx, fxInfo is null return");
            return NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_IO;
        }
        String assetPackageFilePath = fxInfo.getFilePath();
        if (TextUtils.isEmpty(assetPackageFilePath)) {
            GLog.e(TAG, "installVideoFx, assetPackageFilePath is empty return");
            return NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_IO;
        }
        NvsAssetPackageManager assetPackageManager = mStreamingContext.getAssetPackageManager();
        String id = assetPackageManager.getAssetPackageIdFromAssetPackageFilePath(assetPackageFilePath);
        StringBuilder sb = new StringBuilder();
        int type = NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX;
        if (assetPackageManager.getAssetPackageStatus(id, type) == NvsAssetPackageManager.ASSET_PACKAGE_STATUS_NOTINSTALLED) {
            GLog.d(TAG, "installVideoFx, getAssetPackageStatus is ASSET_PACKAGE_STATUS_NOTINSTALLED");
            return assetPackageManager.installAssetPackage(assetPackageFilePath, null, type, false, sb);
        } else {
            boolean isNeedUpdate = assetPackageManager.getAssetPackageVersion(id, type)
                    < assetPackageManager.getAssetPackageVersionFromAssetPackageFilePath(assetPackageFilePath);
            GLog.d(TAG, "installVideoFx, getAssetPackageStatus is ASSET_PACKAGE_STATUS_INSTALLED, isNeedUpdate = " + isNeedUpdate);
            if (isNeedUpdate) {
                return assetPackageManager.upgradeAssetPackage(assetPackageFilePath, null, type, false, sb);
            }
            return NvsAssetPackageManager.ASSET_PACKAGE_STATUS_READY;
        }
    }

    @Override
    public void applyVideoFx(FxInfo fxInfo, long startPos) {
        if (fxInfo == null) {
            GLog.e(TAG, "applyVideoFx, fxInfo is null return");
            return;
        }
        if (TextUtils.isEmpty(fxInfo.getFilePath())) {
            GLog.e(TAG, "applyVideoFx, resourcePath is empty return");
            mVideoFxChanged = false;
            return;
        }
        long startTime = startPos * ONE_SECOND;
        long endTime = (startPos + fxInfo.getDuration()) * ONE_SECOND;
        if (endTime > mMeicamTimeline.getNvsTimeline().getDuration()) {
            endTime = mMeicamTimeline.getNvsTimeline().getDuration();
        }

        GLog.d(TAG, "applyVideoFx, startTime = " + startTime + ", endTime = " + endTime);
        MeicamTimelineEffect videoFxEffect = new MeicamTimelineEffect(getFxName(fxInfo.getFilePath()), fxInfo.getType());
        mInTimeUs = startTime;
        mOutTimeUs = endTime;
        videoFxEffect.setInTime(startTime);
        videoFxEffect.setOutTime(endTime);
        mMeicamTimeline.addEffectToTimeline(videoFxEffect);
        mMeicamTimelineEffect = videoFxEffect;
        mVideoFxChanged = true;
    }

    public String getFxName(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }

        File file = new File(filePath);
        String fileName = file.getName();
        if (TextUtils.isEmpty(fileName) || !fileName.endsWith(FX_FILE_TYPE)) {
            return null;
        }

        return fileName.substring(0, file.getName().indexOf(FX_FILE_TYPE));
    }
}
