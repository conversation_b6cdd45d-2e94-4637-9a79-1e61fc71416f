/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditorCaptionUIController.kt
 ** Description : 字幕编辑首页面板控制器
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption

import android.content.Context
import android.graphics.PointF
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.video.business.base.EditorEffectTrackUIController
import com.oplus.gallery.videoeditorpage.video.business.caption.widget.EditTextPreviewView
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewWrapper
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 字幕UI控制器
 *
 * @param context           上下文
 * @param editorControlView 编辑控制视图
 * @param state             编辑视图状态
 * @param liveWindow        编辑预览窗口
 * @param timelineViewModel 时间线视图模型
 * @param onEffectActionListener 编辑菜单点击监听器
 * @param hasFxs 是否已经有字幕特效
 * @param captionSelectedCallback 字幕选中回调
 */
class EditorCaptionUIController(
    private val context: Context,
    private val editorControlView: EditorControlView,
    private val state: EditorCaptionState,
    private val liveWindow: EditorPreviewView,
    timelineViewModel: TimelineViewModel?,
    onEffectActionListener: OnEffectActionListener,
    hasFxs: Boolean,
    private val captionSelectedCallback: (curTimelinePosition: Long, selectedCaption: BaseCaption) -> Unit
) : EditorEffectTrackUIController(
    context,
    editorControlView,
    state,
    onEffectActionListener,
    timelineViewModel,
    hasFxs
), EditTextPreviewView.OnCaptionEditTextListener {

    override fun createView() {
        super.createView()
        liveWindow.addEditTextListener(this)
    }

    override fun destroyView() {
        liveWindow.removeEditTextListener(this)
        super.destroyView()
    }

    override fun onSelected(
        view: ClipViewWrapper?,
        selected: Boolean,
        clip: ClipModel?,
        withAnim: Boolean
    ) {
        /**
         * 1.时间线不在文字轨道上的时候要获得点击文字轨道前的时间线位置
         * 2.在super后面拿只能拿到滑动终点的位置信息
         */
        val curTimelinePosition = mEditorState.editorEngine.timelineCurrentPosition
        super.onSelected(view, selected, clip, withAnim)
        if (view?.isAddClipView == true) return
        // 处理特效轨道某个字幕片段选中与否
        clip?.let { clipModel ->
            if (clipModel.isInMainTrack.not()
                && (clipModel.type == ClipModel.ClipType.CLIP_TEXT)
            ) {
                // 记录当前选中的caption
                captionSelectedCallback.invoke(curTimelinePosition, clipModel.caption)
            }
        }
    }

    /**
     * 移动片段后的回调
     *
     * @param clipViewWrapper 片段视图
     * @param beforeTrackIndex 移动前所在的轨道索引
     * @param nowTrackIndex 移动后所在的轨道索引
     * @param beforeClipInTrackIndex 移动前在轨道中的索引
     * @param inPoint 移动后片段的开始时间
     * @param clip 片段视图数据模型
     */
    override fun onClipIndexChanged(
        clipViewWrapper: ClipViewWrapper?,
        beforeTrackIndex: Int,
        nowTrackIndex: Int,
        beforeClipInTrackIndex: Int,
        nowClipInTrackIndex: Int,
        inPoint: Long,
        clip: ClipModel?
    ): ClipModel? {
        clip ?: return null
        val caption: BaseCaption? = clip.caption
        if (caption == null) {
            GLog.e(TAG, "onClipIndexChanged: caption is null!   InPoint: " + clip.inPoint + "  TrackIndex: " + clip.trackIndex)
            return clip
        }
        val oldInTime = caption.inTime
        val newOutTime = inPoint + clip.trimOut - clip.trimIn
        if (inPoint >= oldInTime) {
            caption.outTime = newOutTime
            caption.inTime = inPoint
        } else {
            caption.inTime = inPoint
            caption.outTime = newOutTime
        }

        clip.inPoint = caption.inTime
        clip.clipInTrackIndex = nowClipInTrackIndex
        checkCopyBtnEnable()
        state.editorEngine.seekTo(state.editorEngine.timelineCurrentPosition, EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER)
        mEditorState?.operationSaveHelper?.saveCurrentTimeline(OperationType.CAPTION_UPDATE)
        return clip
    }

    /**
     * 字幕片段时间长度发生改变的时候进行回调去修改字幕的出入时间点
     */
    override fun onClipAdjustFinished(clipViewWrapper: ClipViewWrapper, timeAdjusted: Long, position: Long, isTail: Boolean) {
        super.onClipAdjustFinished(clipViewWrapper, timeAdjusted, position, isTail)

        val clipModel = clipViewWrapper.relativeData ?: return
        if (clipModel.isInMainTrack.not()) {
            // 根据特效片段的调节结果修改字幕出入点
            clipModel.caption?.let {
                it.inTime = clipModel.inPoint
                it.outTime = clipModel.inPoint + (clipModel.trimOut - clipModel.trimIn)
                mEditorState?.operationSaveHelper?.saveCurrentTimeline(OperationType.CAPTION_UPDATE)
            }
        }
        // 拖动把手调节文字轨道长度时进行位置偏移调节后再seek解决拖动无法显示字幕的问题
        val seekPosition = if (isTail) {
            state.editorEngine.timelineCurrentPosition - TimelineViewModel.ADJUST
        } else {
            state.editorEngine.timelineCurrentPosition + TimelineViewModel.ADJUST
        }
        state.editorEngine.seekTo(seekPosition, EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER)
    }

    /**
     * 点击非轨道区域取消选中特效片段
     */
    override fun onBlankAreaClick(): Boolean {
        // 编辑框设置隐藏
        state.showEditRect = false
        return super.onBlankAreaClick()
    }


    override fun onTouchDown(curPoint: PointF) = Unit
    override fun onTouchUp(curPoint: PointF) = Unit
    override fun onScaleAndRotate(scalePercent: Float, anchor: PointF, rotateDegree: Float) = Unit
    override fun onDelete() = Unit
    override fun onDrag(prePointF: PointF, nowPointF: PointF) = Unit
    override fun onDoubleClick(pointF: PointF) = Unit

    /**
     * 点击文字预览区域外回调
     */
    override fun onOutOfCaptionRectClick() {
        // 取消轨道选中状态
        onBlankAreaClick()
    }

    /**
     * 点击文字预览区域内回调
     */
    override fun onCaptionClick() {
        // 设置轨道选中状态
        val curPosition = mEditorState?.editorEngine?.timelineCurrentPosition ?: 0
        processSelectState(curPosition, false)
    }

    companion object {
        private const val TAG = "EditorCaptionUIController"
    }
}