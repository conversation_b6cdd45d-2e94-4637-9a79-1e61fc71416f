/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DiskLruCacheLoader.kt
 ** Description:获取DiskLruCache的工具类。获取DiskLruCache数据同意使用DiskLruCacheUri做为入参。
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.foundation.cache.diskcache

import android.graphics.Bitmap
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.file.File

/**
 * 获取DiskLruCache的工具类。获取DiskLruCache数据同意使用DiskLruCacheUri做为入参。
 */
object DiskLruCacheLoader {

    private const val TAG = "DiskLruCacheLoader"
    const val READ_ONLY = true
    /**
     * 从DiskLruCache中获取Bitmap缓存。
     * @param uri DiskLruCacheUri
     * @return Bitmap?
     *          参数无效或未查找到结果，均返回空。
     */
    @JvmStatic
    fun loadBitmap(uri: DiskLruCacheUri): Bitmap? {
        if (uri.isInvalidUri()) {
            return null
        }

        var resultBitmap: Bitmap? = null
        kotlin.runCatching {
            getDiskLruCache(uri)?.get(uri.cacheKey).use { snapshot ->
                snapshot?.getInputStream(0).use {
                    resultBitmap = DecodeUtils.decode(it)
                }
            }
        }

        return resultBitmap
    }

    /**
     * 根据DiskLruCacheUri获取DiskLruCache的实例。
     * @param uri DiskLruCacheUri
     * @return DiskLruCache?
     */
    private fun getDiskLruCache(uri: DiskLruCacheUri): DiskLruCache? {
        var diskLruCache: DiskLruCache? = null
        File(uri.path).runCatching {
            diskLruCache = DiskLruCache.open(
                this,
                uri.appVersion,
                uri.valueCount,
                uri.maxSize,
                READ_ONLY
            )
        }.onFailure {
            GLog.w(TAG, LogFlag.DL, "getDiskLruCacheFile ex=${it.message}")
        }
        return diskLruCache
    }
}