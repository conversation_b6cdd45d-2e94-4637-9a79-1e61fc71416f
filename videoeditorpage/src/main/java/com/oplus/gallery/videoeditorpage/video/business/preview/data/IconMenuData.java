/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: IconMenuData
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.preview.data;

public class IconMenuData extends BaseMenuData {
    private int mIconResId;
    private int mHighlightIconResId;

    public IconMenuData(int viewId) {
        super(viewId);
    }

    public IconMenuData(int viewId, int iconResId) {
        super(viewId);
        mIconResId = iconResId;
    }

    public IconMenuData(int viewId, int iconResId, int highlightIconResId) {
        super(viewId);
        mIconResId = iconResId;
        mHighlightIconResId = highlightIconResId;
    }

    public int getIconResId() {
        return mIconResId;
    }

    public int getHighlightIconResId() {
        return mHighlightIconResId;
    }
}
