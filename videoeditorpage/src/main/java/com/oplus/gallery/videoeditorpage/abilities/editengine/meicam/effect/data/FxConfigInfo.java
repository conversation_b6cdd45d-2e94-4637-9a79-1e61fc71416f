/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - FxConfigInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/


package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data;

import com.google.gson.annotations.SerializedName;

public class FxConfigInfo {
    @SerializedName("type")
    private int mType;
    @SerializedName("passCount")
    private int mPassCount;
    @SerializedName("versionCode")
    private String mVersionCode;
    @SerializedName("repeat")
    private boolean mRepeat;    //循环标记
    @SerializedName("duration")
    private long mDuration; //单次特效默认时长，单位：s(秒)；repeat为true时使用
    @SerializedName("effectName")
    private String mEffectName; //特效名称，目前ai特效有用

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        this.mType = type;
    }

    public int getPassCount() {
        return mPassCount;
    }

    public void setPassCount(int passCount) {
        this.mPassCount = passCount;
    }

    public String getVersionCode() {
        return mVersionCode;
    }

    public void setVersionCode(String versionCode) {
        this.mVersionCode = versionCode;
    }

    public boolean isRepeat() {
        return mRepeat;
    }

    public void setRepeat(boolean repeat) {
        this.mRepeat = repeat;
    }

    public long getDuration() {
        return mDuration;
    }

    public void setDuration(long duration) {
        this.mDuration = duration;
    }

    public String getEffectName() {
        return mEffectName;
    }

    public void setEffectName(String effectName) {
        this.mEffectName = effectName;
    }

    public String toString() {
        return "FxConfigInfo{"
                + "mType='" + mType + '\''
                + ", mPassCount='" + mPassCount + '\''
                + ", mVersionCode='" + mVersionCode + '\''
                + ", mRepeat='" + mRepeat + '\''
                + ", mEffectName='" + mEffectName + '\''
                + '}';
    }
}
