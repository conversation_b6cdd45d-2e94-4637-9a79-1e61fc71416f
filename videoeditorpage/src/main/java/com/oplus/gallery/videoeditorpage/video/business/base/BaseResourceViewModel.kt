/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseResourceViewModel.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/10/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Ye<PERSON><PERSON><EMAIL>		2020/11/10		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.base

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener
import com.oplus.gallery.videoeditorpage.resource.base.BaseResourceManager
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import kotlinx.coroutines.Job

abstract class BaseResourceViewModel<T : Item, U>(application: Application) : AndroidViewModel(application) {
    val applyResourceState = MutableLiveData<ApplyResourceData>()
    open val currentResourceManager: BaseResourceManager<T>? = null

    /**
     * 因网络切换发起的下载重试协程任务
     */
    private var retryDownloadJobByNetworkChange: Job? = null
    /**
     * 记录当前网络类型
     */
    private var lastNetworkConnectType = NetworkMonitor.ConnectType.NONE


    fun setApplyResourceStateIdle() {
        applyResourceState.postValue(ApplyResourceData(ApplyResourceState.IDLE))
    }


    open fun hasDownloadTask(): Boolean {
        return currentResourceManager?.hasDownloadTask() == true
    }

    open fun download(id: String, listener: OnLoadFileListener<T>? = null) {
        GLog.w(TAG, LogFlag.DL, "[download], id= $id")
        // 取消之前的重试任务，避免同时下载冲突
        cancelRetryDownloadJobByNetworkChange()
        // 记录发起下载时的网路类型
        lastNetworkConnectType = NetworkMonitor.connectType
        applyResourceState.postValue(ApplyResourceData(ApplyResourceState.START_LOAD_FILE))
        currentResourceManager?.loadFile(id, object : OnLoadFileListener<T> {
            override fun onProgress(progress: Int, item: T) {
                listener?.onProgress(progress, item)
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOADING_FILE))
            }

            override fun onFinish(item: T) {
                listener?.onFinish(item)
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_FINISH))
            }

            override fun onError(errCode: ErrorCode, item: T?) {
                GLog.e(TAG, LogFlag.DL, "[onError], errCode=$errCode, [${item?.getName()}]")
                listener?.onError(errCode, item)
                // 判断若是由网络切换引发的网络错误情况且网络依然可用，则重新发起下载
                if (ErrorCode.NO_NETWORK_WHEN_DOWNLOADING == errCode) {
                    if (NetworkMonitor.isSwitchedToOtherValidatedNetwork(lastNetworkConnectType)) {
                        // 切网络后网络可用，直接发起重新下载
                        item?.let { download(it.itemUniqueId) }
                    } else {
                        // 切网络后暂不可用，发起网络切换监听任务，若切换则重新下载
                        retryDownloadJobByNetworkChange = NetworkMonitor.startValidatedNetworkSwitchedListening(viewModelScope) { isSwitched ->
                            if (isSwitched) {
                                item?.let { download(it.itemUniqueId) }
                            } else {
                                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_ERROR, errCode))
                            }
                        }
                    }
                    return
                }
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_ERROR, errCode))
            }

            override fun onCancel(item: T) {
                GLog.w(TAG, LogFlag.DL, "[onCancel]")
                listener?.onCancel(item)
                applyResourceState.postValue(ApplyResourceData(ApplyResourceState.LOAD_FILE_CANCEL))
            }
        })
    }

    /**
     * 取消网络变化重试下载任务，用户发起新下载时，需先取消该任务，避免下载冲突
     */
    private fun cancelRetryDownloadJobByNetworkChange() {
        GLog.d(TAG, LogFlag.DL, "cancelRetryDownloadJobByNetworkChange")
        if (retryDownloadJobByNetworkChange?.isCancelled == false) {
            retryDownloadJobByNetworkChange?.cancel()
            retryDownloadJobByNetworkChange = null
        }
    }

    open fun retryDownload() {
        GLog.d(TAG, "retryDownload")
        currentResourceManager?.retryAllDownloadTask()
    }

    open fun cancelDownloadTask() {
        GLog.d(TAG, "cancelDownloadTask")
        currentResourceManager?.cancelAllDownloadTask()
    }

    open fun removeDownloadTask() {
        GLog.d(TAG, "removeDownloadTask")
        currentResourceManager?.removeAllDownloadTask()
    }

    override fun onCleared() {
        super.onCleared()
        GLog.d(TAG, LogFlag.DL, "onCleared")
        cancelRetryDownloadJobByNetworkChange()
        cancelDownloadTask()
    }

    /**
     * 清除下载状态，将资源状态设置为空闲，并清空已下载和正在下载的映射表。
     */
    open fun clearDownloadStatus() {
        applyResourceState.value = ApplyResourceData(ApplyResourceState.IDLE)
        cancelRetryDownloadJobByNetworkChange()
    }

    enum class ApplyResourceState {
        IDLE, START_LOAD_FILE, LOADING_FILE, LOAD_FILE_FINISH,
        LOAD_FILE_ERROR, LOAD_FILE_CANCEL, APPLYING, APPLY_ERROR, APPLY_PUASE
    }


    companion object {
        const val TAG = "BaseResourceViewModel"

        /**
         * 默认需要刷新的列表索引，-1默认不刷新,无效索引
         */
        const val INVALID_INDEX = -1
    }
}

/**
 * 应用资源状态数据实体
 */
data class ApplyResourceData(
    /**
     * 应用资源的状态
     */
    val state: BaseResourceViewModel.ApplyResourceState,

    /**
     * 资源下载过程中的错误码
     */
    val errorCode: ErrorCode? = null,

    /**
     * 数据（可选）
     */
    val data: Any? = null
)