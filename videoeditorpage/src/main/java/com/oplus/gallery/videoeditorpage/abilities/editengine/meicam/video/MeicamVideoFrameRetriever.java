/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamVideoFrameRetriever.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video;

import android.graphics.Bitmap;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.meicam.sdk.NvsVideoFrameRetriever;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoFrameRetriever;

public class MeicamVideoFrameRetriever implements IVideoFrameRetriever {

    private static final String TAG = "MeicamVideoFrameRetriever";
    private NvsVideoFrameRetriever mNvsVideoFrameRetriever;

    public void bindNvsObject(NvsVideoFrameRetriever videoFrameRetriever) {
        mNvsVideoFrameRetriever = videoFrameRetriever;
    }

    @Override
    public Bitmap getFrameAtTime(long timestamp, int videoFrameHeightGrade) {
        if (mNvsVideoFrameRetriever == null) {
            GLog.d(TAG, "getFrameAtTime, mNvsVideoFrameRetriever == null");
            return null;
        }
        return mNvsVideoFrameRetriever.getFrameAtTime(timestamp, videoFrameHeightGrade);
    }

    @Override
    public Bitmap getFrameAtTimeWithCustomVideoFrameHeight(long timestamp, int videoFrameHeight) {
        if (mNvsVideoFrameRetriever == null) {
            GLog.d(TAG, "getFrameAtTimeWithCustomVideoFrameHeight," + "mNvsVideoFrameRetriever == null");
            return null;
        }
        return mNvsVideoFrameRetriever.getFrameAtTimeWithCustomVideoFrameHeight(timestamp, videoFrameHeight);
    }

    @Override
    public void release() {
        if (mNvsVideoFrameRetriever == null) {
            GLog.d(TAG, "release, mNvsVideoFrameRetriever == null");
            return;
        }
        mNvsVideoFrameRetriever.release();
    }
}
