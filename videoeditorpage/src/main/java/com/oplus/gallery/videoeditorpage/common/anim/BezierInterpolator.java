/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BezierInterpolator.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.common.anim;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.Resources.Theme;
import android.util.AttributeSet;
import android.view.animation.BaseInterpolator;

public class BezierInterpolator extends BaseInterpolator {
    private static final double EPSILON = 6.25E-5D;
    private static final float ABOVE_ONE = 1.0F;
    private static final float BELOW_ONE = 0.9999F;
    private static final float ABOVE_ZERO = 1.0E-4F;
    private static final double ABOVE_ZERO_DOUBLE = 1.0D;
    private UnitBezier mUnitBezier;
    private double mEpsilon;
    private float mAboveOne;
    private float mBelowOne;
    private float mAboveZero;
    private boolean mAbove;
    private boolean mLimit;

    public BezierInterpolator(Context context, AttributeSet attrs) {
        this(context.getResources(), context.getTheme(), attrs);
    }

    public BezierInterpolator(Resources res, Theme theme, AttributeSet attrs) {
        this.mEpsilon = EPSILON;
        this.mAboveOne = ABOVE_ONE;
        this.mBelowOne = BELOW_ONE;
        this.mAboveZero = ABOVE_ZERO;
        this.mAbove = false;
        this.mLimit = false;
        float pointAx = 0.5F;
        float pointAy = 0.5F;
        float pointBx = 0.7F;
        float pointBy = 0.7F;
        this.mLimit = true;
        this.mUnitBezier = new UnitBezier((double) pointAx, (double) pointAy, (double) pointBx, (double) pointBy);
    }

    public BezierInterpolator(double p1x, double p1y, double p2x, double p2y, boolean limit) {
        this.mEpsilon = EPSILON;
        this.mAboveOne = ABOVE_ONE;
        this.mBelowOne = BELOW_ONE;
        this.mAboveZero = ABOVE_ZERO;
        this.mAbove = false;
        this.mLimit = false;
        this.mLimit = limit;
        this.mUnitBezier = new UnitBezier(p1x, p1y, p2x, p2y);
    }

    public float getInterpolation(float input) {
        double interpolation = this.mUnitBezier.solve((double) input, EPSILON);
        if (this.mLimit) {
            if ((input < ABOVE_ZERO) || (input > BELOW_ONE)) {
                this.mAbove = false;
            }

            if ((interpolation > ABOVE_ZERO_DOUBLE) && !this.mAbove) {
                interpolation = ABOVE_ZERO_DOUBLE;
                this.mAbove = true;
            }

            if (this.mAbove) {
                interpolation = ABOVE_ZERO_DOUBLE;
            }
        }

        return (float) interpolation;
    }
}
