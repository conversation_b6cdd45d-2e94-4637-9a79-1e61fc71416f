/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : GridSpacingItemDecoration.kt
 ** Description : 样式字体item的间距配置类
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.widget

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils

/**
 * 字幕recyclerview网格布局item间距
 */
class GridSpacingItemDecoration(
    private val spanCount: Int,
    private val horizontalSpacing: Int,
    private val verticalSpacing: Int,
    private val includeEdge: Boolean = true
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val column = position % spanCount

        if (includeEdge) {
            // 水平间距：左右对称分配
            outRect.left = horizontalSpacing - column * horizontalSpacing / spanCount
            outRect.right = (column + 1) * horizontalSpacing / spanCount

            // 移除顶部间距（首行不加顶部间距）
            outRect.top = if (position < spanCount) 0 else verticalSpacing
            // 保留底部间距
            outRect.bottom = verticalSpacing
        } else {
            // 无边缘模式：仅内部间距
            val spacingForPosLeft = column * horizontalSpacing / spanCount
            val spacingForPosRight = horizontalSpacing - (column + 1) * horizontalSpacing / spanCount
            if (ResourceUtils.isRTL(view.context)) {
                outRect.left = spacingForPosRight
                outRect.right = spacingForPosLeft
            } else {
                outRect.left = spacingForPosLeft
                outRect.right = spacingForPosRight
            }
            outRect.top = if (position >= spanCount) verticalSpacing else 0
        }
    }
}