/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - GalleryVideoOliveCoverView.kt
 ** Description: view of clip box.
 ** Version: 1.0
 ** Date : 2025/1/18
 ** Author: 80410313
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  80410313    2025/1/18    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.exportolive

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.math.MathUtil.roundToInt
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoEngine.Companion.MILLIS_TIME_BASE
import com.oplus.gallery.videoeditorpage.utlis.VideoEditorHelper
import com.oplus.gallery.videoeditorpage.video.business.exportolive.ExportOliveParam.COVER_OFFSET_TIME
import com.oplus.gallery.videoeditorpage.video.business.exportolive.ExportOliveParam.MAX_EXPORT_TIME

/**
 * 视频导出olive时，缩图轴上方的剪辑框以及展示的气泡
 *
 * @param context 应用程序的上下文。
 * @param attrs 包含此视图的自定义属性的属性集。
 * @param defStyleAttr 应用到此视图的默认样式。
 * @param defStyleRes 应用到此视图的默认资源。
 */
class VideoOliveCoverView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : View(context, attrs, defStyleAttr, defStyleRes) {
    /**
     * 选择框上的图片显默认的大小
     */
    private var selectBoxBitmapDefaultSize: Int = 0

    /**
     * 选择框圆角半径
     */
    private var selectBoxRadius: Float = 0F

    /**
     * 选择框的描边宽度
     */
    private var selectBoxStrokeWidth: Float = 0F

    /**
     * 获取父容器的宽度
     */
    private var parentWidth: Int = 0

    /**
     * 选择框的宽度
     */
    private var selectBoxWidth: Int = 0

    /**
     * 选帧框把手宽度
     */
    private var selectBoxHandleWidth: Int = 0

    /**
     * 选帧框背景
     */
    private var selectBoxBackGround: Bitmap? = null
    private var selectBoxSrcRect:  Rect? = null
    private val selectBoxPaint = Paint()

    /**
     * 获取选择框的高度
     *
     * @return int
     */
    fun getSelectBoxHeight(): Int {
        return selectBoxBitmapDefaultSize
    }

    /**
     * 选择框Paint
     */
    private val selectBoxPositionPaint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)

    /**
     * 选择框阴影Paint
     */
    private val selectBoxShadowPaint = Paint(selectBoxPositionPaint)

    /**
     * ShadowLayer的Dx与Dy的默认值
     */
    private val selectBoxShadowLayerX: Float = 0F

    /**
     * 左右两边阴影Paint
     */
    private val shadowPaint = Paint()
    private var shadowAlpha = SHADOW_ALPHA_VALUE

    /**
     * 为了能让阴影遮罩层能够完全遮住圆角设置的偏移量
     */
    private var shadowOffset: Int = 0

    /**
     * 时间气泡
     */
    private var trimTimeViewHeight: Int = 0
    private var trimTimeViewWidth: Int = 0
    private var trimTimeTextWidthPadding: Int = 0
    private var trimTimeTextHeightPadding: Int = 0
    private var trimTimeOffset: Int = 0
    private var trimTimeRadius: Int = 0
    private var trimTimeRect: RectF = RectF()
    private val trimTimeTextPaint = Paint()
    private var _showTrimTime: Boolean = false
    private var trimTimeText = TextUtil.EMPTY_STRING
    private var totalTime: Long = -1
    var showTrimTime: Boolean
        get() = _showTrimTime
        set(value) {
            _showTrimTime = value
            startFadeInAnimation()
        }

    /**
     *  时间线竖线
     */
    private var trimTimeLineRectangleHeight: Int = 0
    private var trimTimeLineRectangleWidth: Int = 0
    private var trimTimeLineRectangleRect: RectF = RectF()
    private val trimTimeLinePaint = Paint()
    private var trimTimeViewBottomPadding: Int = 0
    private var touchViewMargin: Int = 0
    private var timePosHeight: Int = 0
    private var timePosBitmap: Bitmap? = null
    private var trimTimeLineSrcRect: Rect? = null
    private val timeLinePositionPaint = Paint()
    private var currentTime: Long = 0
    private var animator: ValueAnimator? = null
    private var alpha: Int = OPACITY_ALPHA_VALUE

    /**
     * 圆点
     */
    private var dotPaint = Paint()
    private var dotRadius: Int = 0
    private var dotMarginTop: Int = 0

    /**
     *  选帧框位置偏移量
     */
    private var selectViewLeftOffset = 0
    private var selectViewRightOffset = 0
    private var selectShadowOffset = 0
    private var selectViewTopOffset = 0

    init {
        initRes()
        initSelectShadowPaint()
    }

    /**
     * 初始化变量
     */
    private fun initRes() {
        // 选择框默认的大小
        selectBoxBitmapDefaultSize = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_bitmap_height)
        selectBoxRadius = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_select_radius).toFloat()
        selectBoxStrokeWidth = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_stroke_width).toFloat()
        selectBoxWidth = selectBoxBitmapDefaultSize * 2
        selectBoxHandleWidth = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_handle_width)
        selectShadowOffset = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_rectangle_width)
        selectViewTopOffset = resources.getDimensionPixelSize(R.dimen.dp_1_25)

        trimTimeViewHeight = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_view_height)
        trimTimeViewWidth = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_view_width)
        trimTimeOffset = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_offset)
        trimTimeLineRectangleWidth = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_rectangle_width)
        trimTimeLineRectangleHeight = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_rectangle_height)
        trimTimeViewBottomPadding = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_view_bottom_padding)
        touchViewMargin = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_time_pos_margin_top)
        timePosHeight = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_time_pos_height)
        shadowOffset = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_select_radius)
        trimTimeRadius = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_radius)
        trimTimeTextWidthPadding = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_text_width_padding)
        trimTimeTextHeightPadding = resources.getDimensionPixelSize(R.dimen.video_editor_trim_time_text_height_padding)
        // 进度条提示文字区域
        trimTimeRect = RectF(0f, 0f, trimTimeViewWidth.toFloat(), trimTimeViewHeight.toFloat())
        // 进度条提示文字与进度条的连接线区域
        trimTimeLineRectangleRect = RectF(
            0f,
            trimTimeViewHeight.toFloat(),
            trimTimeLineRectangleWidth.toFloat(),
            (trimTimeViewHeight + trimTimeLineRectangleHeight).toFloat()
        )
        trimTimeLinePaint.color = resources.getColor(R.color.videoeditor_trim_time_background, null)
        trimTimeLinePaint.isAntiAlias = true
        trimTimeTextPaint.color = resources.getColor(R.color.videoeditor_trim_time_hint_textcolor, null)
        val textSize = resources.getDimensionPixelSize(R.dimen.videoeditor_video_editor_suitable_text_size)
        trimTimeTextPaint.textSize = textSize.toFloat()
        trimTimeTextPaint.isAntiAlias = true
        trimTimeTextPaint.textAlign = Paint.Align.LEFT
        shadowPaint.color = resources.getColor(R.color.videoeditor_export_olive_toolbar_background)
        shadowPaint.isAntiAlias = true
        shadowPaint.alpha = SHADOW_ALPHA_VALUE
        // 进度条指示器
        timePosBitmap = BitmapFactory.decodeResource(resources, R.drawable.videoeditor_trim_play_pos)
        trimTimeLineSrcRect = timePosBitmap?.let { Rect(0, 0, it.width, it.height) }
        // 圆点
        dotPaint.color = Color.WHITE
        dotPaint.isAntiAlias = true
        dotPaint.style = Paint.Style.FILL
        dotRadius = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_dot_radius)
        dotMarginTop = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_dot_margin_top)
    }

    /**
     * 初始化选帧框画笔
     */
    private fun initSelectShadowPaint() {
        val selectDrawable = resources.getDrawable(R.drawable.videoeditor_editor_export, null)
        selectBoxBackGround = BitmapUtils.drawableToBitmap(selectDrawable, null)
        selectBoxSrcRect = selectBoxBackGround?.let { Rect(0, 0, it.width, it.height) }
        selectBoxPaint.isAntiAlias = true
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        parentWidth = MeasureSpec.getSize(widthMeasureSpec)
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (_showTrimTime) {
            drawPlayPosition(canvas)
        } else {
            drawDotView(canvas)
            drawShadowView(canvas)
            drawSelectView(canvas)
        }
    }

    /**
     * 更新选帧框左右起始绘制的偏移位置
     * @param time 开始时间
     * @param totalTime 总时长
     * @param refresh 是否重绘
     * @param position 滚动后的位置
     */
    internal fun changeSelectViewOffSet(time: Long, totalTime: Long, refresh: Boolean = false, position: Int = 0) {
        // 当总时长小于等于3s时 跟随滚动位置进行偏移
        if (totalTime <= MAX_EXPORT_TIME) {
            selectViewLeftOffset = -(position + selectBoxHandleWidth)
            selectViewRightOffset = selectBoxWidth - position + selectBoxHandleWidth + selectBoxHandleWidth / AppConstants.Number.NUMBER_2
        } else {
            // 当前时间位于开始1.5s内时
            if (time < COVER_OFFSET_TIME) {
                val timeToDimension = ((time / MILLIS_TIME_BASE.toFloat()) * (selectBoxWidth / AppConstants.Number.NUMBER_3f)).toInt()
                selectViewLeftOffset = -(timeToDimension + selectBoxHandleWidth)
                selectViewRightOffset = selectBoxWidth - timeToDimension + selectBoxHandleWidth
            } else if (time.plus(COVER_OFFSET_TIME) > totalTime) { // 当前时间位于末尾1.5s内时
                val realTime = totalTime - time
                val timeToDimension = ((realTime / MILLIS_TIME_BASE.toFloat()) * (selectBoxWidth / AppConstants.Number.NUMBER_3f)).toInt()
                selectViewLeftOffset = -(selectBoxWidth - timeToDimension + selectBoxHandleWidth)
                selectViewRightOffset = timeToDimension + selectBoxHandleWidth
            } else {
                selectViewLeftOffset = -(selectBoxWidth / 2 + selectBoxHandleWidth)
                selectViewRightOffset = selectBoxWidth / 2 + selectBoxHandleWidth
            }
        }
        if (refresh) {
            invalidate()
        }
    }

    /**
     * 绘制中心圆点
     */
    private fun drawDotView(canvas: Canvas) {
        dotPaint.alpha = alpha
        val centerX = parentWidth / 2
        canvas.drawCircle(centerX.toFloat(), dotMarginTop.toFloat(), dotRadius.toFloat(), dotPaint)
    }

    /**
     * 绘制选帧框左右两边区域遮罩层
     */
    private fun drawShadowView(canvas: Canvas) {
        shadowPaint.alpha = shadowAlpha.coerceIn(TRANSPARENT_ALPHA_VALUE, SHADOW_ALPHA_VALUE)
        val selectBoxViewStart = parentWidth / 2 + selectViewLeftOffset + selectShadowOffset * AppConstants.Number.NUMBER_3
        val selectBoxViewEnd =  parentWidth / 2 + selectViewRightOffset - selectShadowOffset * AppConstants.Number.NUMBER_3
        val left = 0
        val top = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_margin_top) + selectBoxStrokeWidth.toInt()
        val bottom = top + getSelectBoxHeight() + selectBoxStrokeWidth.toInt() * AppConstants.Number.NUMBER_2
        //左边阴影遮罩
        val destRect = Rect(left, top, selectBoxViewStart, bottom)
        canvas.drawRect(destRect, shadowPaint)
        //右边遮罩
        val secondLeft = selectBoxViewEnd
        val secondRight = parentWidth
        val secondRect = Rect(secondLeft, top, secondRight, bottom)
        canvas.drawRect(secondRect, shadowPaint)
    }

    /**
     * 绘制选帧框的外框
     */
    private fun drawSelectView(canvas: Canvas) {
        selectBoxPaint.alpha = alpha
        val left = parentWidth / 2 + selectViewLeftOffset + selectShadowOffset * AppConstants.Number.NUMBER_2
        val right = parentWidth / 2 + selectViewRightOffset - selectShadowOffset * AppConstants.Number.NUMBER_2
        val top = resources.getDimensionPixelSize(R.dimen.video_edit_olive_trim_margin_top) + selectViewTopOffset
        val bottom = top + getSelectBoxHeight() + selectBoxStrokeWidth.toInt() * AppConstants.Number.NUMBER_2 + selectShadowOffset
        val destRect = Rect(left, top, right, bottom)
        selectBoxBackGround?.let { canvas.drawBitmap(it, selectBoxSrcRect, destRect, selectBoxPaint) }
    }

    /**
     * 时间气泡绘制
     * @param canvas 画布
     */
    private fun drawTrimTimeView(canvas: Canvas) {
        // 进度条提示文字区域
        val trimTimeTextLeft = (parentWidth - trimTimeViewWidth) / 2F - selectBoxStrokeWidth / 2 - selectBoxWidth / 2
        trimTimeRect.left = trimTimeTextLeft
        trimTimeRect.right = trimTimeTextLeft + trimTimeViewWidth.toFloat()
        canvas.drawRoundRect(trimTimeRect, trimTimeRadius.toFloat(), trimTimeRadius.toFloat(), trimTimeLinePaint)
        //进度条连接线
        trimTimeLineRectangleRect.left = parentWidth / 2F - trimTimeOffset - selectBoxStrokeWidth / 2 - selectBoxWidth / 2
        trimTimeLineRectangleRect.right = (trimTimeLineRectangleRect.left) + trimTimeLineRectangleWidth
        trimTimeLineRectangleRect.let { canvas.drawRect(it, trimTimeLinePaint) }
        canvas.drawText(
            trimTimeText, trimTimeRect.left + trimTimeTextWidthPadding,
            trimTimeRect.top + trimTimeViewHeight - trimTimeTextHeightPadding, trimTimeTextPaint
        )
    }

    /**
     * 进度指示线
     * @param canvas 画布
     */
    private fun drawPlayPosition(canvas: Canvas) {
        timeLinePositionPaint.alpha = alpha
        val width = timePosBitmap?.width
        val left = (parentWidth - (width ?: 0)) / 2F
        val destRect = RectF(
            left,
            touchViewMargin.toFloat(),
            left + (width ?: 0),
            (touchViewMargin + timePosHeight).toFloat()
        )
        timePosBitmap?.let { canvas.drawBitmap(it, trimTimeLineSrcRect, destRect, timeLinePositionPaint) }
    }

    /**
     * 启动渐现动画
     */
    internal fun startFadeInAnimation() {
        animator?.takeIf { it.isRunning }?.cancel()
        animator = ValueAnimator.ofInt(TRANSPARENT_ALPHA_VALUE, OPACITY_ALPHA_VALUE).apply {
            duration = ANIM_TIME
            addUpdateListener {
                alpha = it.animatedValue as Int
                // 将数值映射为透明度(0-100)
                shadowAlpha = ((alpha.toFloat() / OPACITY_ALPHA_VALUE.toFloat()) * SHADOW_ALPHA_VALUE.toFloat()).roundToInt()
                invalidate()
            }
            start()
        }
    }

    /**
     * 更新时间戳文案
     */
    fun updateTrimTime(context: Context, tmpTimeStamp: Long, totalTime: Long) {
        if (tmpTimeStamp <= totalTime) {
            val timeText = VideoEditorHelper.formatTimeWithMillis(context, tmpTimeStamp)
            if (this.totalTime != totalTime) {
                this.totalTime = totalTime
            }
            if ((timeText != null) && (timeText.length != trimTimeText.length)) {
                updateTrimTimeViewWidth(timeText)
            }
            this.trimTimeText = timeText
            currentTime = tmpTimeStamp.coerceAtLeast(0)
        }
    }

    /**
     * 获取当前时间
     */
    fun getCurrentTime(): Long {
        return currentTime
    }

    /**
     * 更新选帧框的宽度
     * @param boxCount 选帧框占据缩图个数
     */
    fun setSelectBoxWidth(boxCount: Int) {
        selectBoxWidth = selectBoxBitmapDefaultSize * boxCount
        invalidate()
    }

    /**
     * 更新文案的宽度
     */
    private fun updateTrimTimeViewWidth(trimTimeText: String) {
        val textWidth = trimTimeTextPaint.measureText(trimTimeText)
        trimTimeViewWidth = (textWidth + trimTimeTextWidthPadding * 2).toInt()
        trimTimeLineRectangleRect.left = trimTimeRect.left + trimTimeRect.width() / 2
        trimTimeLineRectangleRect.right = trimTimeLineRectangleRect.left + trimTimeLineRectangleWidth
    }

    /**
     * 释放资源
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator?.cancel()
        timePosBitmap?.recycle()
        selectBoxBackGround?.recycle()
    }

    companion object {
        /**
         * 选择框阴影Paint的透明度
         */
        private const val ALPHA_VALUE = 38

        /**
         * 视频轴选帧宽外部的透明度
         */
        private const val SHADOW_ALPHA_VALUE = 100

        /**
         * 动画时长
         */
        private const val ANIM_TIME = 300L

        /**
         * 全透明
         */
        private const val TRANSPARENT_ALPHA_VALUE = 0

        /**
         * 不透明
         */
        private const val  OPACITY_ALPHA_VALUE = 255
    }
}