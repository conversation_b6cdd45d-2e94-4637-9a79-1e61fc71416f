/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamBackgroundStoryboard.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard;

import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.StoryBoardUtils.VALUE_ROTATION_Z;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.StoryBoardUtils.VALUE_SCALE_X;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.StoryBoardUtils.VALUE_SCALE_Y;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.StoryBoardUtils.VALUE_TRANS_X;
import static com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.StoryBoardUtils.VALUE_TRANS_Y;

import android.text.TextUtils;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.utlis.CollectionUtils;
import com.oplus.gallery.videoeditorpage.utlis.KeyFrameUtil;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.google.gson.Gson;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.MeicamVideoClipEffect;

import java.util.HashMap;
import java.util.Map;

public class MeicamBackgroundStoryboard extends BaseStoryBoardVideoClipEffect {
    public static final String JSON_TYPE_NAME = "background";
    private static final float FLOAT_DEFAULT_SCALE_VALUE = 1F;
    private static final float DEFAULT_STRENGTH = 100F;
    private static final String TAG = "MeicamBackgroundStoryboard";
    private static final float FLOAT_DEFAULT_VALUE = 0F;
    private final static String STORYBOARD_FILE_PATH = "bg/backgroundImage.xml";

    private final static String SOURCE_FILE_PATH = "effectDefaultBackgroundColor.png";
    private final static float STRENGTH_RATIO = 0.5F;
    private final static float DEGREE_NINETY = 90;
    private final static int NUMBER_ONE = 1;
    private final static int NUMBER_TOW = 2;
    private final static int NUMBER_THREE = 3;
    private final static int NUMBER_FOUR = 4;
    private final static float FLOAT_ONE = 1.0F;
    private final static long CLIP_DURATION = 24 * 60 * 60 * 1000;
    private int mSubType = INVALID_INT;
    private int mIndex = INVALID_INT;
    private float mBlurStrength;

    public MeicamBackgroundStoryboard() {
        super(null, 0, null, 0, 0, null);
        mEffectType = StoryboardType.TYPE_BACKGROUND;
        mClassType = JSON_TYPE_NAME;
        mStrength = FLOAT_ONE;
        setLocalFloatValue(StreamingConstant.VideoTransform.PARAM_SCALE_X, FLOAT_DEFAULT_SCALE_VALUE);
        setLocalFloatValue(StreamingConstant.VideoTransform.PARAM_SCALE_Y, FLOAT_DEFAULT_SCALE_VALUE);
        setLocalFloatValue(StreamingConstant.VideoTransform.PARAM_TRANS_X, FLOAT_DEFAULT_VALUE);
        setLocalFloatValue(StreamingConstant.VideoTransform.PARAM_TRANS_Y, FLOAT_DEFAULT_VALUE);
        setLocalFloatValue(StreamingConstant.VideoTransform.PARAM_ROTATION, FLOAT_DEFAULT_VALUE);
    }

    public BaseVideoClipEffect buildEffectParam(String sourceDir, String sourcePath, float strength, int width, int height, int imageWidth, int imageHeight,
                                                long clipDuration, int clipRotation, int type, boolean hasKeyframe) {
        mDirPath = sourceDir;
        mSourceFileName = sourcePath;
        mEffectStrength = strength;
        mSubType = type;
        if (type == BackgroundStoryType.TYPE_BLUR) {
            return buildBlurEffect(sourceDir, strength, width, height, imageWidth, imageHeight, clipRotation, hasKeyframe);
        }
        return buildCommonEffect(sourceDir, sourcePath, width, height, type, hasKeyframe);
    }

    @Override
    public String getBlurDescription(String keyframeDescription, int width, int height, int imageWidth, int imageHeight, int clipRotation) {
        return KeyFrameUtil.addBlurDataToXml(keyframeDescription, getBlurTransData(width, height, imageWidth, imageHeight, clipRotation), mBlurStrength);
    }

    private BaseVideoClipEffect buildBlurEffect(String sourceDir, float strength, int width, int height, int imageWidth, int imageHeight, int clipRotation, boolean hasKeyframe) {
        mBlurStrength = strength * STRENGTH_RATIO;
        setStringValue(StreamingConstant.StoryBoard.STORYBOARD_RES_DIR, sourceDir);

        String backgroundStory = null;
        if (hasKeyframe) {
            if ((mSubEffectList != null) && (mSubEffectList.size() > 1)) {
                //第一次添加模糊背景走这里拼装关键帧与模糊的xml
                String keyframeDescription = mSubEffectList.get(1).getStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION);
                backgroundStory = KeyFrameUtil.addBlurDataToXml(keyframeDescription, getBlurTransData(width, height, imageWidth, imageHeight, clipRotation), mBlurStrength);
            } else {
                //更新模糊强度和画幅大小走这里重新拼装xml
                String description = getStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION);
                backgroundStory = KeyFrameUtil.updateBlurStrength(description, getBlurTransData(width, height, imageWidth, imageHeight, clipRotation),
                        width, height, mBlurStrength);
            }
        } else {
            backgroundStory = StoryBoardUtils.getBlurBackgroundStory(mBlurStrength, getClipTransData(),
                    getBlurTransData(width, height, imageWidth, imageHeight, clipRotation), width, height);
        }
        setStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION, backgroundStory);

       /* String clipTransStory = StoryBoardUtils.getClipTransStory(getClipTransData(), width, height);
        String clipTransData = StoryBoardUtils.getClipTransData(getClipTransData(), width, height);
        int realStrength = (int) (strength * STRENGTH_RATIO);
        String backgroundDescription = StoryBoardUtils.getBlurBackground(realStrength, getBlurTransData(width, height, imageWidth, imageHeight), width, height);
        String noTransBackground = StoryBoardUtils.getBlurBackgroundNoTrans(realStrength, getBlurRelativeTransData(width, height, imageWidth, imageHeight), width, height);
        String description = StoryBoardUtils.composeStory(backgroundDescription, clipTransStory);
        setStringValue(STORYBOARD_DESCRIPTION, description);
        this.setAttachment(EXTENDS_DATA_KEY_BACKGROUND, noTransBackground);
        this.setAttachment(EXTENDS_DATA_KEY_BACKGROUND_TRANS, clipTransStory);*/
        return this;
    }


    private BaseVideoClipEffect buildCommonEffect(String sourceDir, String sourcePath, int width, int height, int type, boolean hasKeyframe) {
        mSourceFileName = sourcePath;
        mDirPath = sourceDir;
        mSubType = type;
        long clipDuration = CLIP_DURATION;
        if (CollectionUtils.isEmpty(mSubEffectList)) {
            return buildEffectList(sourceDir, sourcePath, width, height, clipDuration, hasKeyframe);
        }
        for (int index = 0; index < mSubEffectList.size(); index++) {
            BaseVideoClipEffect videoClipEffect = mSubEffectList.get(index);
            String description = null;
            if (index == 0) {
                description = StoryBoardUtils.wrapClip(width, height, clipDuration);
            } else if (index == 1) {
                if (hasKeyframe) {
                    description = videoClipEffect.getStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION);
                } else {
                    description = StoryBoardUtils.wrapClipTrans(width, height, getClipTransData(), clipDuration);
                }
            }
            reBuildStoryboard(videoClipEffect, description, sourceDir);
        }
        String description = StoryBoardUtils.wrapCommonBackgroundTrans(width, height, sourcePath, clipDuration);
        reBuildStoryboard(this, description, sourceDir);
        return this;
    }

    private Map<String, String> getBlurTransData(int width, int height, int imageWidth, int imageHeight, int clipRotation) {
        Map<String, String> clipTrans = new HashMap<>();
        Float rotationZ = mLocalFloatParams.get(StreamingConstant.VideoTransform.PARAM_ROTATION);
        if ((clipRotation == NUMBER_ONE) || (clipRotation == NUMBER_THREE)) {
            int tempWidth = imageWidth;
            imageWidth = imageHeight;
            imageHeight = tempWidth;
        }
        float scaleParam = 1.0F;
        float scaleDelta = 1.0F;
        float imageRatio = imageWidth * FLOAT_ONE / imageHeight;
        float timelineRatio = width * FLOAT_ONE / height;
        if (imageRatio > timelineRatio) {
            float scale = width * FLOAT_ONE / imageWidth;
            scaleDelta = height * FLOAT_ONE / (scale * imageHeight);
        } else {
            float scale = height * FLOAT_ONE / imageHeight;
            scaleDelta = width * FLOAT_ONE / (scale * imageWidth);
        }
        if (rotationZ != null) {
            float rotationIn = rotationZ - DEGREE_NINETY * NUMBER_FOUR;
            if ((Math.abs(rotationIn) == DEGREE_NINETY) || (Math.abs(rotationIn) == DEGREE_NINETY * NUMBER_THREE)) {
                if (height > width) {
                    scaleParam = height * FLOAT_ONE / width;
                } else {
                    scaleParam = width * FLOAT_ONE / height;
                }
            } else {
                rotationIn = Math.abs(rotationIn) % DEGREE_NINETY;
                if (height > width) {
                    double delta = DEGREE_NINETY - rotationIn - Math.atan(width * FLOAT_ONE / height) * DEGREE_NINETY * NUMBER_TOW / Math.PI;
                    double radians = Math.toRadians(delta);
                    double centerLine = Math.sqrt(Math.pow(width * FLOAT_ONE, NUMBER_TOW) + Math.pow(height * FLOAT_ONE, NUMBER_TOW));
                    scaleParam = (float) (centerLine / width * Math.cos(radians));
                } else {
                    double delta = DEGREE_NINETY - rotationIn - Math.atan(height * FLOAT_ONE / width) * DEGREE_NINETY * NUMBER_TOW / Math.PI;
                    double radians = Math.toRadians(delta);
                    double centerLine = Math.sqrt(Math.pow(width * FLOAT_ONE, NUMBER_TOW) + Math.pow(height * FLOAT_ONE, NUMBER_TOW));
                    scaleParam = (float) (centerLine / height * Math.cos(radians));
                }
            }
            clipTrans.put(VALUE_SCALE_X, String.valueOf(scaleParam * scaleDelta));
            clipTrans.put(VALUE_SCALE_Y, String.valueOf(scaleParam * scaleDelta));
        }
        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_ROTATION, VALUE_ROTATION_Z);
        return clipTrans;
    }

    private Map<String, String> getBlurRelativeTransData(int width, int height, int imageWidth, int imageHeight) {
        Map<String, String> clipTrans = new HashMap<>();
        float scaleDelta = 1.0F;
        float imageRatio = imageWidth * FLOAT_ONE / imageHeight;
        float timelineRatio = width * FLOAT_ONE / height;
        if (imageRatio > timelineRatio) {
            float scale = width * FLOAT_ONE / imageWidth;
            scaleDelta = height * FLOAT_ONE / (scale * imageHeight);
        } else {
            float scale = height * FLOAT_ONE / imageHeight;
            scaleDelta = width * FLOAT_ONE / (scale * imageWidth);
        }

        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_ROTATION, VALUE_ROTATION_Z);
        Float scaleX = mLocalFloatParams.get(StreamingConstant.VideoTransform.PARAM_SCALE_X);
        float scaleParam = FLOAT_ONE / scaleX;
        clipTrans.put(VALUE_SCALE_X, String.valueOf(scaleParam * scaleDelta));
        clipTrans.put(VALUE_SCALE_Y, String.valueOf(scaleParam * scaleDelta));

        Float transX = mLocalFloatParams.get(StreamingConstant.VideoTransform.PARAM_TRANS_X);
        clipTrans.put(VALUE_TRANS_X, String.valueOf(-transX * scaleParam));
        Float transY = mLocalFloatParams.get(StreamingConstant.VideoTransform.PARAM_TRANS_Y);
        clipTrans.put(VALUE_TRANS_Y, String.valueOf(-transY * scaleParam));

        return clipTrans;
    }

    private Map<String, String> getClipTransData() {
        Map<String, String> clipTrans = new HashMap<>();
        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_TRANS_X, VALUE_TRANS_X);
        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_TRANS_Y, VALUE_TRANS_Y);
        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_SCALE_X, VALUE_SCALE_X);
        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_SCALE_Y, VALUE_SCALE_Y);
        addKeyValue(clipTrans, StreamingConstant.VideoTransform.PARAM_ROTATION, VALUE_ROTATION_Z);
        return clipTrans;
    }

    private void addKeyValue(Map<String, String> newMap, String oldKey, String newKey) {
        Float value = mLocalFloatParams.get(oldKey);
        if (value != null) {
            newMap.put(newKey, String.valueOf(value));
        }
    }

    public BaseVideoClipEffect buildDefaultList(int width, int height, long clipDuration) {
        return buildCommonEffect(StreamingConstant.StoryBoardPath.BACKGROUND_ASSETS_FILE_DIR, SOURCE_FILE_PATH, width, height, BackgroundStoryType.TYPE_COLOR, true);
    }

    private BaseVideoClipEffect buildEffectList(String sourceDir, String sourcePath, int width, int height, long clipDuration, boolean hasKeyframe) {
        mSourceFileName = sourcePath;
        mDirPath = sourceDir;
        mSubEffectList.clear();
        String description = StoryBoardUtils.wrapClip(width, height, clipDuration);
        BaseVideoClipEffect clipEffect = buildStoryboard(StreamingConstant.StoryBoardPath.BACKGROUND_ASSETS_FILE_DIR, description);
        mSubEffectList.add(clipEffect);
        String middleDescription = "";
        if (hasKeyframe) {
            middleDescription = KeyFrameUtil.removeBlurDataFromXml(getStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION));

            if (TextUtils.isEmpty(middleDescription)) {
                middleDescription = StoryBoardUtils.wrapClipTrans(width, height, getClipTransData(), clipDuration);
            }
        } else {
            middleDescription = StoryBoardUtils.wrapClipTrans(width, height, getClipTransData(), clipDuration);
        }
        BaseVideoClipEffect middle = buildStoryboard(StreamingConstant.StoryBoardPath.BACKGROUND_ASSETS_FILE_DIR, middleDescription);
        mSubEffectList.add(middle);

        String result = StoryBoardUtils.wrapCommonBackgroundTrans(width, height, sourcePath, clipDuration);
        this.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_RES_DIR, sourceDir);
        this.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION, result);
        this.setIsMainEffect(true);
        return this;
    }

    private BaseVideoClipEffect buildStoryboard(String dirPath, String description) {
        MeicamBackgroundStoryboard story = new MeicamBackgroundStoryboard();
        story.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_RES_DIR, dirPath);
        story.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION, description);
        story.setBooleanVal(StreamingConstant.StoryBoard.STORYBOARD_NO_BACKGROUND, true);
        story.setIsMainEffect(false);
        return story;
    }

    public void reBuildStoryboard(BaseVideoClipEffect effect, String description, String dirPath) {
        effect.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_RES_DIR, dirPath);
        effect.setStringValue(StreamingConstant.StoryBoard.STORYBOARD_DESCRIPTION, description);
        effect.setBooleanVal(StreamingConstant.StoryBoard.STORYBOARD_NO_BACKGROUND, true);
    }

    @Override
    public MeicamVideoClipEffect clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamVideoClipEffect result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamVideoClipEffect(getName(), getType());
        }
        return result;
    }

    @Override
    public int getSubType() {
        return mSubType;
    }

    @Override
    public void setSubType(int subType) {
        mSubType = subType;
    }

    @Override
    public int getIndex() {
        return mIndex;
    }

    @Override
    public void setIndex(int index) {
        mIndex = index;
    }

}
