/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : BytesBufferPool.kt
 ** Description: BytesBuffer复用池
 ** Version    : 1.0
 ** Date       : 2025/1/8
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2025/1/8      1.0              init
 *************************************************************************************************/

package com.oplus.gallery.foundation.cache.memorycache

import android.content.ComponentCallbacks2
import android.content.res.Configuration
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.SystemClock
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.inRange
import com.oplus.gallery.foundation.util.ext.isIndexOutOfBounds
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.concurrent.CopyOnWriteArrayList
import kotlinx.coroutines.launch

/**
 * BytesBuffer复用池，根据请求size，去对应的分级缓存池获取对象复用
 */
object BytesBufferPool {
    private const val TAG = "BytesBufferPool"
    private val DEBUG by lazy { GProperty.DEBUG_BYTE_ARRAY }

    /**
     * 缓存池的默认配置
     */
    private val defaultConfig by lazy { PoolConfig("default", AppConstants.Capacity.MEM_10M) }

    /**
     * 缓存池分级配置，由外部设置进来保存在这里
     */
    private val configList = CopyOnWriteArrayList<PoolConfig>()

    /**
     * 全局缓存池，每个级别保存一个全局实例，存储该级别的Buffer
     */
    private val globalPools = mutableListOf<IBytesBufferPool>()

    /**
     * 访问缓存的总次数，用来统计命中率：[hitCount] / [totalAccessCount]
     */
    private var totalAccessCount: Int = 0

    /**
     * 访问缓存的命中次数，用来统计命中率：[hitCount] / [totalAccessCount]
     */
    private var hitCount: Int = 0

    init {
        ContextGetter.context.registerComponentCallbacks(object : ComponentCallbacks2 {
            override fun onConfigurationChanged(newConfig: Configuration) {
                // Do nothing.
            }

            override fun onLowMemory() {
                GLog.d(TAG, DL) { "[onLowMemory]" }
                clear()
            }

            override fun onTrimMemory(level: Int) {
                GLog.d(TAG, DL) { "[onTrimMemory]  level:$level" }
                trimMemory(level)
            }
        })
    }

    /**
     * 设置缓存池的配置。在进程启动阶段 或 能力初始化时调用。
     *
     * 不设置缓存配置，则不会启用缓存机制，每次都会创建新的对象。
     */
    @JvmStatic
    fun setPoolConfig(configs: List<PoolConfig>) {
        if (configs.isEmpty()) {
            GLog.w(TAG, DL) { "[setPoolConfig], configs is empty" }
            return
        }
        configList.addAll(configs)
    }

    /**
     * 创建一个缓存池，对外公开的目的是外部可以自行建立独立的缓存池，局部使用和管理
     * @param config 缓存池配置
     * @return 缓存池对象
     */
    @JvmStatic
    fun createPool(config: PoolConfig): IBytesBufferPool {
        return LruBytesBufferPool(if (config.checkConfigValid()) config else defaultConfig)
    }

    /**
     * 获取指定大小的buf。如果指定的大小超出缓存池配置的单个buffer上限，则创建新的buffer返回
     */
    @JvmStatic
    fun get(length: Int): BytesBuffer {
        val pool = selectPool(length) ?: let {
            if (DEBUG) GLog.e(TAG, DL) { "[get] not found cache pool. length:$length" }
            return BytesBuffer(length)
        }
        val buffer = pool.get(length)?.apply {
            totalAccessCount++
            hitCount++
        } ?: let {
            totalAccessCount++
            BytesBuffer(length).apply {
                if (DEBUG) GLog.d(TAG, DL) { "[get] new buffer:$this" }
            }
        }
        if (DEBUG) GLog.d(TAG, DL) { "[get] hitCount/totalCount:$hitCount/$totalAccessCount length:$length buffer:$buffer $pool" }
        return buffer
    }

    /**
     * 类似kt的use语法，获取一个buffer，使用完后立即回收
     */
    @JvmStatic
    fun use(length: Int, callBack: (BytesBuffer) -> Unit) {
        val pool = selectPool(length) ?: let {
            if (GProperty.DEBUG) GLog.e(TAG, DL) { "[use] not found cache pool. length:$length" }
            callBack.invoke(BytesBuffer(length))
            return
        }
        val buffer = pool.get(length)?.apply {
            totalAccessCount++
            hitCount++
        } ?: let {
            totalAccessCount++
            BytesBuffer(length)
        }
        try {
            callBack.invoke(BytesBuffer(length))
        } finally {
            pool.recycle(buffer)
        }
        if (DEBUG) GLog.d(TAG, DL) { "[use] hitCount/totalCount:$hitCount/$totalAccessCount length:$length buffer:$buffer $pool" }
    }

    /**
     * 回收buf，超出当前缓存池的设置上限，则直接释放
     */
    @JvmStatic
    fun recycle(buffer: BytesBuffer) {
        val pool = selectPool(buffer.data.size) ?: let {
            if (GProperty.DEBUG) GLog.e(TAG, DL) { "[recycle] not found cache pool. release buffer:$buffer" }
            buffer.release()
            return
        }
        pool.recycle(buffer)
        if (DEBUG) GLog.d(TAG, DL) { "[recycle] buffer:$buffer \n $pool" }
    }

    /**
     * 清空缓存池，释放所有buffer占用的内存
     */
    private fun clear() {
        GLog.d(TAG, DL) { "[clear]" }
        synchronized(globalPools) {
            globalPools.forEach { it.clear() }
        }
    }

    /**
     * 裁剪缓存
     * @param level 剪裁的上下文，提示应用程序可能要执行的剪裁量
     */
    private fun trimMemory(level: Int) {
        synchronized(globalPools) {
            globalPools.forEach { it.onTrimMemory(level) }
        }
    }

    /**
     * 根据指定的大小，去缓存池列表选择符合单个buf上限的缓存池
     */
    private fun selectPool(singleSize: Int): IBytesBufferPool? {
        if (globalPools.isEmpty() && configList.isNotEmpty()) {
            synchronized(globalPools) {
                GTrace.trace("initGlobalPools<BytesBufferPool>") {
                    initGlobalPools()
                }
            }
        }
        if (globalPools.isEmpty()) return null
        return synchronized(globalPools) {
            globalPools.firstOrNull { singleSize < it.singleMaxSize } ?: globalPools.last()
        }
    }

    /**
     * 初始化缓存池
     */
    private fun initGlobalPools() {
        val logBuilder = StringBuilder("globalPools init, ")
        configList.forEach {
            globalPools.add(createPool(it))
            if (GProperty.DEBUG_GLOBAL_POOL) {
                val totalSize = FilePathUtils.getUnitValue(ContextGetter.context, it.totalMaxSize)
                val singleSize = FilePathUtils.getUnitValue(ContextGetter.context, it.singleMaxSize)
                val idleSize = FilePathUtils.getUnitValue(ContextGetter.context, it.idleMaxSize)
                logBuilder.append("[${it.tag}: total=$totalSize, single=$singleSize, idle=$idleSize, reuse=${it.maxReuseMultiple}]")
            }
        }
        globalPools.sortBy { it.singleMaxSize }
        if (GProperty.DEBUG_GLOBAL_POOL) GLog.d(TAG, DL) { logBuilder.toString() }
    }
}

/**
 * ByteArray数据类，可以存储key-value样式的数据，并用offset来标记value的起始位置。
 *
 * 例如：[data] = [0,1,2,3,4,5,6,7,8,9]，若[offset] = 4，则 [0, 3]为 key，[4, 9]为 value, length = 6, capacity = 10
 */
class BytesBuffer {
    /**
     * buffer中存储的数据内容大小
     */
    var length: Int = 0

    /**
     * 数据对象片段在存储文件的偏移量
     */
    var offset: Int = 0

    /**
     * 存储数据的ByteArray对象
     */
    var data: ByteArray

    /**
     * 对象复用标记，打印log后，方便分析复用情况
     * - true：代表buffer是从缓存池复用的，分析log时看此标记就知道是否是复用buffer
     * - false：代表buffer是新创建的
     */
    private var reused: Boolean = false

    /**
     * 构建指定容量的Buffer
     * @param capacity buffer的最大容量
     */
    constructor(capacity: Int) {
        data = ByteArray(capacity)
    }

    /**
     * 使用已有的byte数组构建buffer
     * @param length buffer中存储的数据内容大小
     * @param offset 数据对象片段在存储文件的偏移量
     * @param data 存储数据的ByteArray对象
     */
    constructor(length: Int, offset: Int, data: ByteArray) {
        this.length = length
        this.offset = offset
        this.data = data
    }

    /**
     * 重置对象状态，以便后续复用
     */
    fun reset() {
        length = 0
        offset = 0
        reused = true
    }

    /**
     * 释放对象
     */
    fun release() {
        length = 0
        offset = 0
    }

    override fun toString(): String {
        return "BytesBuffer(reused=$reused, length=$length, offset=$offset, data=$data - ${data.size}})"
    }
}

/**
 * BytesBuffer缓存池接口
 * @param singleMaxSize 单个buffer的容量上限，用来对 buffer pool 进行分级。
 */
abstract class IBytesBufferPool(val singleMaxSize: Long) {

    /**
     * 获取指定大小的buffer
     */
    abstract fun get(length: Int = 0): BytesBuffer?

    /**
     * 回收buffer，放入缓存池
     */
    abstract fun recycle(buffer: BytesBuffer)

    /**
     * 清空缓存，低内存时，可以清理缓存，释放内存
     */
    abstract fun clear()

    /**
     * 裁剪缓存，内存降低后进行缓存裁剪，释放内存
     */
    abstract fun onTrimMemory(level: Int)
}

/**
 * BytesBuffer缓存池实现，采用Lru算法
 * @param config 缓存池的配置参数类
 */
private class LruBytesBufferPool(
    val config: PoolConfig,
) : IBytesBufferPool(config.singleMaxSize) {
    private val logTag: String = "$TAG.${config.tag}"

    /**
     * 缓存存储数组
     */
    private val list: MutableList<BytesBuffer> = mutableListOf()

    /**
     * 闲置缓存超时时间，用来延迟裁剪缓存
     */
    private val idleDurationThreshold = config.idleDurationThreshold.toLong()

    /**
     * 闲置缓存清理Handler，用来进行延时控制
     */
    private val idleCheckHandler: Handler by lazy {
        object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                if (currentSize <= config.idleMaxSize) return
                AppScope.launch { trimToSize(config.idleMaxSize, "idleCheck") }
            }
        }
    }

    /**
     * 缓存池超过闲置大小时记录的系统时间，缓存变更后判定是否超时，超时则释放缓存到idle限定的上限
     */
    private var lastOverIdleSizeTime: Long = 0

    /**
     * 当前缓存大小
     */
    private var currentSize: Long = 0

    @Synchronized
    override fun get(length: Int): BytesBuffer? {
        var index = list.indexOfFirst { it.data.size == length }
        // length = 0则无需再去扩容复用
        if ((index < 0) && (length > 0)) {
            index = list.indexOfFirst { it.data.size.inRange(length, length * config.maxReuseMultiple.toInt()) }
        }
        return (if (list.isIndexOutOfBounds(index).not()) list.removeAt(index) else null)?.apply {
            currentSize -= this.data.size
            resetLastOverIdleSizeTime()
        }
    }

    @Synchronized
    override fun recycle(buffer: BytesBuffer) {
        list.add(buffer.apply { reset() })
        currentSize += buffer.data.size
        evict()
        idleCheck()
    }

    /**
     * 逐出超过上限的缓存
     */
    private fun evict() {
        trimToSize(config.totalMaxSize, "evict")
    }

    /**
     * 逐出长时间闲置的缓存
     */
    private fun idleCheck() {
        if ((lastOverIdleSizeTime <= 0) && (currentSize > config.idleMaxSize)) {
            lastOverIdleSizeTime = SystemClock.elapsedRealtime()
            idleCheckHandler.sendEmptyMessageDelayed(MSG_IDLE_CHECK, idleDurationThreshold)
        }
    }

    /**
     * 缓存大小裁剪到[targetSize]
     * @param targetSize 缓存缩减的目标大小
     */
    @Synchronized
    private fun trimToSize(targetSize: Long, trimReason: String) {
        if (currentSize <= targetSize) {
            return
        }
        GLog.d(logTag, DL) { "[trimToSize] [$trimReason] release buffer from $currentSize to $targetSize" }
        while (currentSize > targetSize) {
            currentSize -= list.removeAt(0).let { removedBuffer ->
                removedBuffer.data.size.apply { removedBuffer.release() }
            }
        }
        resetLastOverIdleSizeTime()
    }

    /**
     * 重置闲置缓存超时起点时间
     */
    private fun resetLastOverIdleSizeTime() {
        if ((lastOverIdleSizeTime > 0) && (currentSize <= config.idleMaxSize)) {
            lastOverIdleSizeTime = 0
            idleCheckHandler.removeMessages(MSG_IDLE_CHECK)
        }
    }

    @Synchronized
    override fun clear() {
        GLog.d(logTag, DL) { "[clear] release buffer count:${list.size}" }
        trimToSize(0, "clear")
    }

    @Synchronized
    override fun onTrimMemory(level: Int) {
        if (level > ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            clear()
        } else if (level == ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL || level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            trimToSize(config.totalMaxSize / 2, "onTrimMemory-$level")
        } else {
            trimToSize(config.idleMaxSize, "onTrimMemory-$level")
        }
    }

    @Synchronized
    override fun toString(): String {
        val details = StringBuilder()
        list.forEach { details.append("[$it]") }
        return "LruBytesBufferPool(listSize=${list.size}, config=$config, memoryUsage:${list.sumOf { it.data.size }} \n details:$details)"
    }

    companion object {
        private const val TAG = "LruBytesBufferPool"

        /**
         * 闲置缓存清理msg
         */
        private const val MSG_IDLE_CHECK: Int = 0
    }
}