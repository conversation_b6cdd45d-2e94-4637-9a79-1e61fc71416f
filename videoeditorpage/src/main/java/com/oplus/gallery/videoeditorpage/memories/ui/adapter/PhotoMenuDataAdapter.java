/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - PhotoMenuDataAdapter.java
 * * Description: xxxxxxxxx
 * * Version: 1.0
 * * Date : 2017/11/21
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/21    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.ui.adapter;

import static com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolderKt.setViewSelectedState;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.coui.appcompat.theme.COUIThemeUtils;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorViewAlphaAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable;
import com.oplus.gallery.business_lib.template.editor.anim.EditorPhotoBorderAnimation;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesManager;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.videoeditorpage.resource.room.bean.PhotoItem;

import java.util.List;

public class PhotoMenuDataAdapter extends BaseRecyclerAdapter<PhotoItem> {

    private static final String TAG = "PhotoMenuDataAdapter";

    public PhotoMenuDataAdapter(Context context, List<PhotoItem> data) {
        super(context, data);
    }

    @Override
    public int getItemLayoutId(int viewType) {
        return R.layout.videoeditor_memories_editor_photo_menu_item_layout;
    }

    @Override
    public void bindData(BaseRecycleViewHolder viewHolder, int position, PhotoItem item) {
        ImageView imageView = viewHolder.findViewById(R.id.icon_image);
        MediaInfo mediaInfo = new MediaInfo(mContext, item.getFilePath());
        String stringUri = mediaInfo.mUri;
        imageView.setTag(stringUri);
        Bitmap bmp = MemoriesManager.getThumbnailCache(stringUri);
        GLog.d(TAG, "bindData position = " + position
                + ", imageView.uri = " + imageView.getTag()
                + ", uri = " + stringUri
                + ", bmp = " + bmp);
        if (bmp != null) {
            imageView.setImageBitmap(bmp);
        } else {
            MemoriesManager.loadThumbnailTask(mediaInfo, imageView);
        }
        setViewSelectedState(viewHolder, item.isSelected() ? Selectable.SELECTED : Selectable.UNSELECTED);
    }

    @Override
    public BaseRecycleViewHolder createViewHolder(@NonNull View itemView, int viewType) {
        EditorViewAlphaAnimViewHolder holder = new EditorViewAlphaAnimViewHolder(itemView, new EditorPhotoBorderAnimation(), null) {
            private ImageView mImageView = itemView.findViewById(R.id.icon_image);

            @Override
            public void updateAnimValue(@NonNull Object value) {
                if (value instanceof Integer) {
                    Drawable foreground = mImageView.getForeground();
                    if (foreground != null) {
                        foreground.setAlpha((int) value);
                    }
                }
            }
        };
        ImageView imageView = holder.findViewById(R.id.icon_image);
        Drawable drawable = mContext.getDrawable(R.drawable.videoeditor_memories_editor_menu_photo_item_foreground);
        if ((drawable != null) && (imageView != null)) {
            int srcAlphaValue = Color.alpha(
                COUIThemeUtils.getThemeAttrColor(itemView.getContext(), com.oplus.gallery.foundation.ui.R.attr.gColorPrimary)
            );

            drawable.setAlpha(srcAlphaValue);
            imageView.setForeground(drawable);

            holder.setSelectedAnimEnable(true);
            holder.setSelectedAnimView(holder);
            float selectedAlpha = srcAlphaValue * 1f / EditorUIConfig.MAX_ALPHA_VALUE;
            float unselectedAlpha = 0f;
            holder.setUnselectedAlpha(unselectedAlpha);
            holder.setDisableAlpha(unselectedAlpha);
            holder.setSelectedAlpha(selectedAlpha);
        }
        return holder;
    }
}
