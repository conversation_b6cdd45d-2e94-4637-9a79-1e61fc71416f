/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PhotoItem.kt
 * Description:
 * Version: 1.0
 * Date: 2021/5/10
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>   2021/5/10          1.0         OPLUS_FEATURE_APP_LOG_MONITOR
</desc></version></date></author> */
package com.oplus.gallery.videoeditorpage.resource.room.bean

import com.oplus.gallery.business_lib.template.editor.data.BaseViewData

class PhotoItem(
    viewId: Int = -1,
    isEnable: Boolean = true,
    isSelectable: Boolean = true,
    isSelected: Boolean = false,
    var filePath: String
) : BaseViewData(viewId, isEnable, isSelectable, isSelected)