/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ProgressDialog.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/6/6
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/6/6        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.content.DialogInterface
import androidx.annotation.StyleRes
import com.oplus.gallery.videoeditorpage.R
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.oplus.gallery.foundation.ui.dialog.base.BaseAlertDialog
import com.oplus.gallery.foundation.ui.dialog.base.BaseBuilder
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 导出视频进度弹窗 目前只有壁纸业务使用
 */
class ProgressDialog private constructor(
    builder: Builder,
    private var max: Int
) : BaseAlertDialog<ProgressDialog>(builder) {
    private var progressBar: COUIHorizontalProgressBar? = null

    /**
     * 设置进度值
     */
    fun setProgress(progress: Int) {
        if (ensureProgressBar()) {
            progressBar?.progress = progress
        } else {
            GLog.d(TAG, "setProgress: progressBar uninit")
        }
    }

    /**
     * 获取进度值
     */
    fun getProgress(): Int = progressBar?.progress ?: 0

    /**
     * 设置最大进度
     */
    fun setMaxProgress(max: Int) {
        this.max = max
        if (ensureProgressBar()) {
            progressBar?.max = max
        } else {
            GLog.d(TAG, "setMaxProgress: progressBar uninit")
        }
    }

    private fun ensureProgressBar(): Boolean {
        if (progressBar == null) {
            progressBar = realDialog?.findViewById(com.support.dialog.R.id.progress)
        }
        return null != progressBar
    }

    override fun show(): ProgressDialog {
        super.show()
        setMaxProgress(max)
        return this
    }

    class Builder : BaseBuilder<ProgressDialog> {

        constructor(
            context: Context,
            @StyleRes dialogStyleResId: Int
        ) : super(context, dialogStyleResId)

        constructor(
            context: Context,
            hasNegativeButton: Boolean = false
        ) : this(
            context,
            if (hasNegativeButton) {
                com.support.dialog.R.style.COUIAlertDialog_Progress_Cancelable
            } else {
                com.support.dialog.R.style.COUIAlertDialog_Progress
            }
        )

        private var max: Int = MAX_PROGRESS

        fun setMaxProgress(max: Int): Builder {
            this.max = max
            return this
        }

        fun setNegativeButton(
            textId: Int,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            realBuilder.setNegativeButton(textId, listener)
            return this
        }

        fun setNegativeButton(
            text: CharSequence?,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            realBuilder.setNegativeButton(text, listener)
            return this
        }

        fun setPositiveButton(
            textId: Int,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            realBuilder.setPositiveButton(textId, listener)
            return this
        }

        fun setPositiveButton(
            text: CharSequence?,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            realBuilder.setPositiveButton(text, listener)
            return this
        }

        override fun build(): ProgressDialog {
            return ProgressDialog(this, max)
        }
    }

    companion object {
        private const val TAG = "ProgressDialog"
        private const val MAX_PROGRESS = 100
    }
}