/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  EditorSpeederVM
 ** Description: 视频变速相关的工具类
 ** Version: 1.0
 ** Date : 2025/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/5/7      1.0     NEW
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.speeder

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import org.json.JSONObject

/**
 * 视频变速相关的工具类（读取曲线变速的特效json等）
 */
object CurveSpeedHelper {
    private const val TAG = "CurveSpeedUtil"

    // JSON字段名称
    private const val SPEED_TEMPLATE_JSON_KEY = "speed_template"

    // JSON字段 速度原始文件
    private const val JSON_KEY_SPEED_ORIGINAL = "speedCurveRawData"

    // JSON字段 图片路径
    private const val JSON_KEY_IMAGE_PATH = "image_path"

    // JSON字段 模板类型
    private const val JSON_KEY_TEMPLATE_TYPE = "type"

    // 资源文件路径名字
    private const val TEMPLATE_ASSET_PATH = "speed/videoeditor_curves_speed_templates.json"

    // 图片资源类型
    private const val RESOURCE_TYPE_DRAWABLE = "drawable"

    /**
     * 获取曲线变速的模板
     * @param context
     * @return
     */
    fun getCurveSpeedTemplate(context: Context): MutableList<SpeederCurveInfo> {
        val curveInfoList: MutableList<SpeederCurveInfo> = ArrayList()
        runCatching {
            val assetJsonFile = TEMPLATE_ASSET_PATH

            val jsonString = context.assets.open(assetJsonFile).bufferedReader().use { reader ->
                reader.readText()
            }
            val data = JSONObject(jsonString)
            val array = data.getJSONArray(SPEED_TEMPLATE_JSON_KEY)

            for (i in 0 until array.length()) {
                val role = array.getJSONObject(i)

                val templateTypeValue = role.optInt(JSON_KEY_TEMPLATE_TYPE)
                val nameRes = getStringRes(templateTypeValue)
                val localizedTitle = if (nameRes > 0) context.resources.getString(nameRes) else ""
                val speedCurveRawData = role.optString(JSON_KEY_SPEED_ORIGINAL)
                val imagePath = role.optString(JSON_KEY_IMAGE_PATH)
                val imageDrawable = getDrawableFromResource(context, imagePath) ?: continue

                val curveInfo = SpeederCurveInfo(
                    localizedTitle, speedCurveRawData, imagePath, templateTypeValue, imageDrawable
                )

                curveInfoList.add(curveInfo)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[getCurveSpeedTemplate] error message: ${it.message}")
            return mutableListOf()
        }
        return curveInfoList
    }

    /**
     * 根据图片路径获取对应的drawable对象
     */
    private fun getDrawableFromResource(context: Context, imagePath: String?): Drawable? {
        if (imagePath.isNullOrBlank()) {
            return null
        }
        val resourceId = context.resources.getIdentifier(
            imagePath, RESOURCE_TYPE_DRAWABLE, context.packageName
        )
        return if (resourceId != 0) ContextCompat.getDrawable(context, resourceId) else null
    }

    /**
     * 根据模板资源类型获取对应的字符串资源id
     */
    private fun getStringRes(templateTypeValue: Int): Int {
        val speederCurveTemplate = SpeederCurveTemplate.fromValue(templateTypeValue)

        return when (speederCurveTemplate) {
            SpeederCurveTemplate.NONE -> R.string.videoeditor_editor_text_none
            SpeederCurveTemplate.MONTAGE -> R.string.videoeditor_speed_template_montage
            SpeederCurveTemplate.HEROIC_MOMENT -> R.string.videoeditor_speed_template_hero_moment
            SpeederCurveTemplate.BULLET_TIME -> R.string.videoeditor_speed_template_bullet_time
            SpeederCurveTemplate.JUMP -> R.string.videoeditor_speed_template_jump
            SpeederCurveTemplate.FLASH_IN -> R.string.videoeditor_speed_template_flash_in
            SpeederCurveTemplate.FLASH_OUT -> R.string.videoeditor_speed_template_flash_out
        }
    }
}