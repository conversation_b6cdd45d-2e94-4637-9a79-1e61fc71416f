/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - EditorReplaceHelper
 ** Description: 替换业务辅助类
 ** Version: 1.0
 ** Date : 2025/06/12
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >      <desc>
 **  80320709      2025/06/12  1.0             created
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.replace

import android.app.Activity
import android.content.Context
import android.util.Size
import androidx.core.net.toUri
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectType
import com.oplus.gallery.business_lib.selectionpage.SelectionFrom
import com.oplus.gallery.business_lib.util.formatNvmFilePath
import com.oplus.gallery.foundation.fileaccess.FileConstants.MediaType.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_270
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_90
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.ClipMotionMode
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity
import com.oplus.gallery.videoeditorpage.video.app.TimelineGenerator
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.joint.JointEntry
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.trim.IEffectClipResponder
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView

/**
 * 替换业务辅助类
 */
object EditorReplaceHelper {
    private const val TAG = "EditorReplaceHelper"
    /** 跳转至相册选图时的请求Key */
    private const val KEY_REQUEST_PICKER = "videoEditorPicker.replace.requestKey"

    /**
     * 跳转到相册选视频
     * @param context 上下文
     * @param controlView 控制视图
     * @param selectedClipIndex 选中的片段索引
     */
    @JvmStatic
    fun gotoGallerySelection(
        context: Context,
        controlView: EditorControlView,
        currentClip: IVideoClip,
        onSelectionListener: ReplaceSelectionListener
    ) {
        val editorActivity = context as? EditorActivity ?: run {
            GLog.d(TAG, LogFlag.DL, "[gotoGallerySelection] context is not EditorActivity. context = ${context.javaClass.name}")

            return
        }
        Starter.DialogFragmentStarter<PanelDialog>(
            editorActivity.supportFragmentManager,
            bundle = SelectInputData(
                selectMulti = false,
                selectType = SelectType.ALL,
                fromWhere = SelectionFrom.VIDEO_EDITOR,
                minVideoDuration = currentClip.normalSpeedDuration / TimeUtils.TIME_1_MS_IN_US
            ).createBundle().apply {
                putString(KEY_REQUEST_KEY, KEY_REQUEST_PICKER)
            },
            postCard = PostCard(SELECTION_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(KEY_REQUEST_PICKER) { _, bundle ->
                if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
                    GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] result is not ok: code = ${bundle.getInt(KEY_RESULT_CODE)}")
                    return@setFragmentResultListenerSafety
                }
                val pathStr = bundle.getString(KEY_RESULT_DATA) ?: run {
                    GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] result path is null")
                    return@setFragmentResultListenerSafety
                }
                val path = Path.fromString(pathStr)
                LocalMediaDataHelper.getLocalMediaItem(path)?.let {
                    val jointEntry = JointEntry.fromMediaItem(it)
                    val supportEntries = VideoParser.getInstance().parseByJointEntryList(listOf(jointEntry))
                    if (supportEntries.isEmpty()) {
                        ToastUtil.showLongToast(R.string.videoeditor_notif_not_supported)
                        return@setFragmentResultListenerSafety
                    }
                    VideoEditorTrackerHelper.buildTrackVideoData(it, jointEntry)
                    if (it.isVideo) {
                        val replaceTimeline = TimelineGenerator(context.applicationContext).buildTimeline(it)
                            ?: return@setFragmentResultListenerSafety
                        // 跳到替换业务选视频帧
                        val state = EditorReplaceState(context, controlView, replaceTimeline, currentClip, onSelectionListener, it)
                        controlView.editorStateManager.changeState(state)
                    } else {
                        onSelectionListener.onSelectedMediaItem(it, null)
                    }
                }
            }
        }
    }

    /**
     * 替换视频片段
     * @param preTimeline 之前的时间线
     * @param mediaItem 媒体对象
     * @param controlView 控制视图
     * @param selectedClipIndex
     */
    @JvmStatic
    fun replaceVideoClip(
        mediaItem: MediaItem,
        controlView: EditorControlView,
        selectedClipIndex: Int,
        replaceEntry: EditorReplaceEntry?,
        effectClipResponderList: List<IEffectClipResponder>
    ) {
        val timeline = controlView.editorEngine.currentTimeline ?: run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] timeline is null")
            return
        }
        val videoTrack = timeline.getVideoTrack(AppConstants.Number.NUMBER_0) ?: run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] videoTrack is null")
            return
        }
        if ((selectedClipIndex < AppConstants.Number.NUMBER_0) || (selectedClipIndex > videoTrack.clipCount - AppConstants.Number.NUMBER_1)) {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] clipIndex out of size: selectedClipIndex = $selectedClipIndex")
            return
        }
        val videoClip = videoTrack.getClip(selectedClipIndex) as? IVideoClip ?: run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] videoTrack is null")
            return
        }

        val mediaType = if (mediaItem.mediaType == MEDIA_TYPE_VIDEO) VIDEO_CLIP_TYPE_AV else VIDEO_CLIP_TYPE_IMAGE

        val mediaSize = when (mediaItem.rotation) {
            DEGREE_90, DEGREE_270 -> Size(mediaItem.height, mediaItem.width)
            else -> Size(mediaItem.width, mediaItem.height)
        }

        var mediaUri = mediaItem.contentUri.toString()
        if (mediaItem.isOlivePhoto()) {
            OLiveDecode.create(mediaItem.filePath).decode()?.microVideo?.let {
                mediaUri = formatNvmFilePath(mediaUri.toUri(), it.offset, it.length)
            }
        }

        videoClip.replaceFile(
            mediaUri,
            mediaUri,
            mediaType,
            false,
            replaceEntry?.trimIn ?: videoClip.trimIn,
            replaceEntry?.trimOut ?: videoClip.trimOut,
            mediaSize.width,
            mediaSize.height,
            mediaItem.filePath,
            true
        )
        videoClip.imageMotionMode = ClipMotionMode.CLIP_MOTION_MODE_LETTERBOX_ZOOMIN
        videoClip.pipInitPointFList = null
        // 重置旋转角度
        videoClip.extraVideoRotation = AppConstants.Number.NUMBER_0
        videoClip.setCuttedTailorSize(AppConstants.Number.NUMBER_0, AppConstants.Number.NUMBER_0)
        controlView.editorEngine.restoreByTimeline(timeline, false)
        controlView.seekTimeline(controlView.editorEngine.timelineCurrentPosition, AppConstants.Number.NUMBER_0, true)
        effectClipResponderList.forEach { it.update(timeline) }
        controlView.operationSaveHelper.saveCurrentTimeline(OperationType.CLIP_REPLACE)
        controlView.editorEngine.resetMaskEffect(false, AppConstants.Number.NUMBER_0)
    }

    /**
     * 替换选择媒体监听
     */
    interface ReplaceSelectionListener {
        /**
         * 选中的媒体对象监听
         * @param mediaItem 选中的媒体对象
         * @param replaceEntry 替换实体（图片的替换实体为null，视频需要裁剪才有这个数据）
         */
        fun onSelectedMediaItem(mediaItem: MediaItem, replaceEntry: EditorReplaceEntry?) { }
    }
}