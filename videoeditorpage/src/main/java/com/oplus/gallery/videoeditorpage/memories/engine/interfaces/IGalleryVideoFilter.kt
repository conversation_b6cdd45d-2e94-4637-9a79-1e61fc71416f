/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IGalleryVideoFilter
 ** Description: 视频编辑滤镜接口类。
 **
 ** Version: 1.0
 ** Date: 2022/07/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/07/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.interfaces

import com.oplus.gallery.framework.abilities.videoedit.data.FilterInfo

interface IGalleryVideoFilter {
    fun applyPackagedFx(path: String?): Boolean

    fun removeVideoFilter()

    fun applyLutFx(path: String?): Boolean

    fun applyLutFx(path: String?, strength: Float, index: Int): Boolean

    fun setFilter(filterItem: FilterInfo?, index: Int): Boolean

    fun resetFilter(): Boolean

    fun getFilterPath(): String?

    fun getCurrentInfo(): FilterInfo?

    fun getCurrentFilterIndex(): Int

    fun setCurrentInfo(filterInfo: FilterInfo?)

    fun removeFilter(index: Int): Boolean
}