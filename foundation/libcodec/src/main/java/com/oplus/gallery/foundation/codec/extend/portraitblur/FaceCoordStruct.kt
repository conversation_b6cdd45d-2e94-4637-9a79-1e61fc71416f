/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceCoordStruct
 ** Description:
 ** Version: 1.0
 ** Date : 2025/5/20
 ** Author: sunwenli
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  sunwenli                      2025/5/20    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.extend.portraitblur

import com.oplus.gallery.foundation.codec.extend.ExtendStruct

/**
 * 形变相关结构体
 */
class FaceCoordStruct(byteArray: ByteArray) : ExtendStruct<FaceCoordData>(byteArray) {
    val version: Float32
    val faceNum: Signed32
    var type: Signed32? = null
    var triangleNum: Signed32? = null

    init {
        version = Float32()
        faceNum = Signed32()

        if (version.get() >= VERSION_V2_0) {
            type = Signed32()
            triangleNum = Signed32()
        }
    }

    override fun toData(): FaceCoordData {
        return FaceCoordData().also {
            it.version = version.get()
            it.faceNum = faceNum.get()
            it.type = type?.get()
            it.triangleNum = triangleNum?.get()
        }
    }

    companion object {
        /**
         * 开始有多中形变的版本
         * 新增type，triangleNum
         */
        private const val VERSION_V2_0 = 2.0F
    }
}

/**
 * 人脸角点相关参数
 */
data class FaceCoordData(

    /**
     * 版本号
     */
    var version: Float = 0f,

    /**
     * 人脸个数，仅用于美颜形变
     */
    var faceNum: Int = 0,

    /**
     * 角点数组样式 0：美颜形变 1：人脸反畸变
     */
    var type: Int? = 0,

    /**
     * 三角形个数
     */
    var triangleNum: Int? = TRIANGLE_DEFAULT_NUM,

    /**
     * 角点数组
     */
    var facecCoordArray: FloatArray = floatArrayOf()
) {

    override fun toString(): String {
        return "FaceCoordData{ version: $version, faceNum: $faceNum, type: $type," +
            " triangleNum: $triangleNum, facecCoordArray.size: ${facecCoordArray.size} }"
    }

    companion object {

        const val VERSION_SUPPORT_FONT_MESH = 2.0F

        /**
         * 默认三角形数量
         */
        const val TRIANGLE_DEFAULT_NUM = 488
    }
    /**
     * 是否支持形变（包括美颜形变，人脸反畸变）
     * 1,对于前置，除判断数据是否有效，还需要版本号大于[VERSION_SUPPORT_FONT_MESH]
     * 2,对于后置，以往就支持，只需要判断数据是否有效
     * @return 返回是否支持
     */
    fun FaceCoordData.isSupportMeshWarp(isRear: Boolean): Boolean {
        return (isRear || (version >= VERSION_SUPPORT_FONT_MESH))
            && (faceNum > 0)
            && facecCoordArray.isNotEmpty()
    }
}
