/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoMenuEditActionRule.kt
 ** Description : 大图页菜单编辑操作规则
 ** Version     : 1.0
 ** Date        : 2022/03/18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON>@Apps.Gallery              2022/03/18  1.0         create file
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.menucontrol.menuactionrules

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.IBinder
import android.os.RemoteException
import android.os.SystemClock
import androidx.annotation.IdRes
import androidx.annotation.WorkerThread
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.addon.graphics.display.HdrSdrManagerOs13
import com.oplus.gallery.addon.view.getEdrSurfaceControl
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_DROP_PICTURE_URI
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_BRIGHTEN_GRAY_IMAGE
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_BRIGHTEN_METADATA
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_ROTATION
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_HASSEL_WATERMARK_EDITABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_HAS_HASSEL_WATERMARK
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_IS_CANCEL_FROM_EDIT
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_MEDIA_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_SHOULD_DECODE_MICRO_PREVIEW_SHOT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_DISPLAY_SET_COVER_MENU
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_IS_VIDEO_WALLPAPER_CUT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_MODEL_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_NOT_DISPLAY_CSHOT_BTN
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.PICTURE_FRAGMENT
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.findFullScreenContainerId
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.util.SystemConfigs.isExport
import com.oplus.gallery.basebiz.util.SystemConfigs.isOnePlus
import com.oplus.gallery.basebiz.util.SystemConfigs.isOppo
import com.oplus.gallery.basebiz.util.SystemConfigs.isRealme
import com.oplus.gallery.basebiz.util.SystemConfigs.productBrand
import com.oplus.gallery.business.aiunitdownload.groupphoto.GroupPhotoEntranceHelper
import com.oplus.gallery.business.contentloading.ContentFactory.Companion.LOAD_TYPE_PAGE_THUMBNAIL
import com.oplus.gallery.business.contentloading.SizeType
import com.oplus.gallery.business_lib.aiunitdownload.EntranceListener
import com.oplus.gallery.business_lib.aiunitdownload.RefuseNextAction
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_GALLERY_PAGE
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_GALLERY_PAGE_FROM_CAMERA
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_GALLEY_WALLPAPER_PAGE
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction.Companion.VALUE_INVOKER_SEARCH_PAGE
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_BESTTAKE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_COMPOSITION
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_ID_PHOTO
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_LIGHTING
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_REPAIR_DEBLUR
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_REPAIR_DEREFLECTION
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.AI_SCENERY
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.DROP_PICTURE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.ENHANCE_TEXT
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.EXPORT_OLIVE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.GROUP_PHOTO
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.NORMAL
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.PASSERBY_ELIMINATE
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.PRIVACY_WATERMARK
import com.oplus.gallery.business_lib.model.config.PhotoEditorType.SUPER_TEXT
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.business_lib.model.data.base.item.isDolbyVideo
import com.oplus.gallery.business_lib.model.data.base.item.isLhdrPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.item.isUhdrPhoto
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_LOCAL_HDR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_LOSS_LESS_CACHE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_OPLUS_ULTRA_HDR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_ULTRA_HDR
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter
import com.oplus.gallery.business_lib.util.formatNvmFilePath
import com.oplus.gallery.business_lib.videoedit.MetadataGetterFactory
import com.oplus.gallery.business_lib.videoedit.MetadataKey
import com.oplus.gallery.business_lib.videoedit.VideoEditSpecHelper
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_SEARCH_RESULT
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_GROUP_PHOTO
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_IDPHOTO
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_PORTRAIT_BLUR
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_TEXT
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_PHOTO_EDIT
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.ui.transition.ITransitionBoundsProvider
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.DisplayUtils
import com.oplus.gallery.foundation.util.ext.Cancellable
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.ext.getFileDescriptorSafely
import com.oplus.gallery.foundation.util.ext.getString
import com.oplus.gallery.foundation.util.ext.observeOnce
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.runOnUiThreadImmediate
import com.oplus.gallery.foundation.util.ext.runOnWorkThread
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.multiprocess.ITransBitmap
import com.oplus.gallery.foundation.util.systemcore.IntentUtils.NAVIGATE_UP_TITLE_TEXT
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG_ENTER_SUB_EDIT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_LUMO_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_UHDR_TRANSFORM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.DialogWindow.NO_MORE_REMIND_IN_PHOTO_EDIT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.DialogWindow.NO_MORE_REMIND_WHEN_EDIT_LOCAL_HDR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.DialogWindow.NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.HDR_IMAGE_SWITCH
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_LOCAL_ALBUM
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.hardware.screen.hdr.IBrightenSession
import com.oplus.gallery.framework.abilities.hardware.screen.hdr.IBrightenSession.SessionEvent.DISABLE_HDR_IMAGE
import com.oplus.gallery.framework.abilities.hardware.screen.hdr.IBrightenSession.SessionEvent.ENTER_EDIT_PAGE
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.ITaskManageAbility
import com.oplus.gallery.framework.abilities.taskmanage.config.TaskConfigStrategy
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import com.oplus.gallery.framework.abilities.watermark.IWatermarkAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkFileOperator
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.file.ShootingInfo.Companion.isValid
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkParams
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkPattern
import com.oplus.gallery.framework.abilities.watermark.file.isFrameTypeWatermark
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.pagecontainer.PhotoContainerSection
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.OPERATOR_TOKEN_DATA
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.inputarguments.enter.PhotoEnterHdrImageArgument
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuScope
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoTransitionBoundsProvider
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState.DisableDataInBackground
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState.NoneOperation
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack
import com.oplus.gallery.standard_lib.graphics.drawable.ThumbnailDrawable
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.system.measureTimeMillis
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 大图页菜单编辑操作规则。在进入编辑状态之前如果当前图片是缩图，进入原图下载流程，用户可选择下载原图后进入编辑，也可选择直接进入编辑
 */
@Suppress("LongParameterList")
internal class PhotoMenuEditActionRule(
    @IdRes ruleAction: Int,
    viewModel: PhotoViewModel,
    private val fragment: BaseFragment,
    private val activity: BaseActivity,
    private val editType: PhotoEditorType,
    menuScope: MenuScope? = null,
    ruleResponseCodes: IntArray? = null,
    private val invokerToken: String = TextUtil.EMPTY_STRING
) : PhotoMenuActionRule(TAG, ruleAction, viewModel, ruleResponseCodes, menuScope) {

    /**
     * [ITaskManageAbility]配置能力的对象，避免频繁创建
     */
    private val taskManageAbility: ITaskManageAbility? by lazy {
        activity.getAppAbility<ITaskManageAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[taskManageAbility]init taskManageAbility failed , taskManageAbility is null" }
            }
        }
    }

    /**
     * [IConfigAbility]配置能力的对象，避免频繁创建
     */
    private val configAbility: IConfigAbility? by lazy {
        activity.getAppAbility<IConfigAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[configAbility]init configAbility failed , configAbility is null" }
            }
        }
    }

    /**
     * [ISettingsAbility]配置能力的对象，避免频繁创建
     */
    private val settingsAbility: ISettingsAbility? by lazy {
        activity.getAppAbility<ISettingsAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[settingsAbility]init settingsAbility failed , settingsAbility is null" }
            }
        }
    }

    /**
     * [IHardwareAbility]配置能力的对象，避免频繁创建
     */
    private val hardwareAbility: IHardwareAbility? by lazy {
        activity.getAppAbility<IHardwareAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[hardwareAbility]init hardwareAbility failed , hardwareAbility is null" }
            }
        }
    }

    /**
     * [IWatermarkAbility]配置能力的对象，避免频繁创建
     */
    private val watermarkAbility: IWatermarkAbility? by lazy {
        activity.getAppAbility<IWatermarkAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[watermarkAbility]init watermarkAbility failed , watermarkAbility is null" }
            }
        }
    }

    /**
     * [IWatermarkMasterAbility]配置能力的对象，避免频繁创建
     */
    private val watermarkMasterAbility: IWatermarkMasterAbility? by lazy {
        activity.getAppAbility<IWatermarkMasterAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[watermarkMasterAbility]init watermarkMasterAbility failed , watermarkMasterAbility is null" }
            }
        }
    }

    /**
     * [IResourcingAbility]配置能力的对象，避免频繁创建
     */
    private val resourcingAbility: IResourcingAbility? by lazy {
        activity.getAppAbility<IResourcingAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[resourcingAbility]init resourcingAbility failed , resourcingAbility is null" }
            }
        }
    }

    /**
     * 记录上一次的大图操作状态
     * 注意：此标记目前只用于编辑后返回大图，其他场景慎用！！
     */
    private var lastPageOperationState: PageOperationState = NoneOperation

    /**
     * 记录上一次的大图操作状态的observer cancellable
     *
     * 注意：此标记目前只用于编辑后返回大图，其他场景慎用！！
     */
    private var lastPageOperationStateCancellable: Cancellable? = null

    /**
     * 点击编辑图标，触发PhotoMenuEditActionRule的初始时间
     *
     * 用于统计用户点击一级菜单项（合影优化）到点击二级菜单项（人脸超分或闭眼修复按键）的间隔时间，
     * 需要将这个参数传递给编辑
     * */
    private var startClickTime: Long = System.currentTimeMillis()

    /**
     * 抑制菜单显示，超时Runnable
     * 兜底用
     */
    private val suppressMenuTimeOutRunnable: Runnable = Runnable {
        GLog.d(TAG, LogFlag.DL) { "[suppressTopMenuTimeOutRunnable] suppress top menu icon timeout" }
        lastPageOperationStateCancellable?.cancel()
        lastPageOperationState = NoneOperation
        viewModel.menuControl.suppressMenuIconShow(isSuppress = false)
    }

    /**
     * 通用弹窗
     * 使用场景：
     * 1.hdr图片点击编辑时
     * 2.olive图片点击隐私水印时
     */
    private var normalNotSupportDialog: ConfirmDialog? = null

    override fun execute(viewData: PhotoItemViewData, mediaItem: MediaItem, onDone: () -> Unit) {
        doEditAction(viewData, onDone, mediaItem)
    }

    private fun doEditAction(
        viewData: PhotoItemViewData,
        onDone: () -> Unit,
        mediaItem: MediaItem
    ) {
        startClickTime = System.currentTimeMillis()
        if (viewData.contentQuality != PhotoItemViewData.ContentQuality.HIGH) {
            ToastUtil.showShortToast(BasebizR.string.base_loading_photo_tip)
            onDone()
            return
        }
        // 允许大图后台数据加载
        viewModel.pageManagement.requestPageOperation(NoneOperation, OPERATOR_TOKEN_DATA)
        when (editType) {
            NORMAL -> trackMenuClick(MENU_ITEM_PHOTO_EDIT)
            ENHANCE_TEXT -> trackPictureClick(CLICK_TEXT)
            AI_ID_PHOTO -> trackPictureClick(CLICK_IDPHOTO)
            PORTRAIT_BLUR -> trackPictureClick(CLICK_PORTRAIT_BLUR)
            PRIVACY_WATERMARK -> trackPrivacyWatermarkClick(mediaItem)
            GROUP_PHOTO -> {
                trackGroupPhotoHasClicked()
                trackPictureClick(CLICK_GROUP_PHOTO)
            }
            else -> GLog.d(TAG, LogFlag.DL, "execute, unTrack type:$editType")
        }

        if (viewData.mediaType == MEDIA_TYPE_VIDEO) {
            if ((editType == EXPORT_OLIVE) && (mediaItem.duration <= VIDEO_EXPORT_OLIVE_MIN_TIME)) {
                ToastUtil.showLongToast(R.string.photopage_export_must_300ms_longer)
                onDone()
                return
            }
            downloadOriginalVideoAndEdit(mediaItem, onDone)
        } else {
            downloadOriginalAndEdit(
                mediaItem, onDone,
                (editType == DROP_PICTURE) && dropPictureNeedShowOLiveXDRNotSupportDialog(mediaItem) ||
                        needShowHdrImageNotSupportDialog(mediaItem) ||
                        ((ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK).not() && (editType == PRIVACY_WATERMARK))
                                || (editType == GROUP_PHOTO)) && needShowLoseOliveEffectDialog(mediaItem)
            )
        }
    }

    override fun isOperationSupportedForQuick(): Boolean {
        return false
    }

    /**
     * 下载原视频并进入编辑
     */
    private fun downloadOriginalVideoAndEdit(mediaItem: MediaItem, onDone: () -> Unit) {
        viewModel.viewModelScope.launch(Dispatchers.CPU) {
            val thumbnail = loadPreviewThumbnail(mediaItem)
            withContext(Dispatchers.UI) {
                viewModel.cloudSync.triggerDownloadOriginal(
                    activity = activity,
                    scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_VIDEO_EDIT,
                    onDownloadOriginalFailAction = { _, _ -> onDone() },
                    onDownloadOriginalSuccessAction = { doEditVideo(mediaItem, onDone, thumbnail) })
            }
        }
    }

    /**
     * 根据条件确认是否需要弹出 失去实况效果 的弹窗。
     * @param mediaItem MediaItem
     * @return Boolean
     */
    private fun needShowLoseOliveEffectDialog(mediaItem: MediaItem): Boolean {
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false).not()) return false
        val isNeverRemind = ConfigAbilityWrapper.getBoolean(NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG_ENTER_SUB_EDIT)
        return (mediaItem.isOlivePhoto()) && isNeverRemind.not()
    }

    /**
     * 根据条件确认是否需要弹出 Local HDR图片不支持编辑 的弹窗。
     * @param mediaItem MediaItem
     * @return Boolean
     */
    private fun needShowHdrImageNotSupportDialog(mediaItem: MediaItem, hdrImageContent: IHdrImageContent? = null): Boolean {
        /**
         * 与产品确认，当前仅普通图片的编辑弹出提示弹窗即可。
         * - 超级文本2.0的图片虽然可能有Local HDR效果，但是点击"T"这个按钮后进入的页面，对用户来说实际还没进入到编辑。但是我们这时候是在编辑页面实现的。
         *   产品期望在这个时候不提示，但是到了“真正”编辑页面的时候再提亮。但是对代码实现来说当前只要进入了编辑页，我们就无法支持提亮。与产品和测试达成一致，这个时候
         *   干脆就不弹窗提示了。
         * - 超级文本1.0, 人像景深、AI证件照等模式下拍的照片，本身就不支持Local HDR，所以这里直接使用[NORMAL]来做判断。
         */

        // 当前是未提亮状态或是不支持提亮
        val isBrightDisable =
            ((viewModel.brighten.isBrightnessToggleStateEnabled().not()) && HdrSdrManagerOs13.SUPPORT_NOTIFY_BRIGHTNESS_SCALE)
                    || viewModel.brighten.isSupportHdrImagePresent.not()

        val config = hdrImageContent?.grayImage?.config
        val isHdrTransformSupport = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_UHDR_TRANSFORM)

        // 是否支持Hdr编辑
        val isSupportHdrEdit = if (config == null) {
            viewModel.brighten.isSupportSpecifyHdrEdit(mediaItem)
        } else {
            (mediaItem.isLhdrPhoto() && (config == Bitmap.Config.ALPHA_8) && viewModel.brighten.isLocalHdrEditBrightenFeatureEnabled)
                    || (mediaItem.isUhdrPhoto() && (config == Bitmap.Config.ALPHA_8) && viewModel.brighten.isUHdrEditBrightenFeatureEnabled)
                    || (mediaItem.isUhdrPhoto() && (config != Bitmap.Config.ALPHA_8) &&
                    viewModel.brighten.isUHdrEditBrightenFeatureEnabled && isHdrTransformSupport)
        }

        GLog.d(TAG, LogFlag.DL) {
            "needShowHdrImageNotSupportDialog isBrightDisable: $isBrightDisable," +
                    "isSupportHdrImagePresent: ${viewModel.brighten.isSupportHdrImagePresent}, \n" +
                    "isLocalHdrEditBrightenFeatureEnabled: ${viewModel.brighten.isLocalHdrEditBrightenFeatureEnabled}, " +
                    " isLHdrImage: ${mediaItem.isLhdrPhoto()}, \n" +
                    "isUHdrEditBrightenFeatureEnabled: ${viewModel.brighten.isUHdrEditBrightenFeatureEnabled}, " +
                    " isUHdrImage: ${mediaItem.isUhdrPhoto()}, " +
                    "editType: $editType, isSupportHdrEdit:$isSupportHdrEdit"
        }

        /*
         * 满足以下条件之一则不弹框
         * 1，支持Hdr编辑
         * 2，当前是未提亮状态或是不支持提亮
         * 3，不是普通编辑
         * 4，不是Hdr图（包括LocalHdr和UHdr）
         */
        if (isSupportHdrEdit
            || isBrightDisable
            || (editType != NORMAL)
            || isHdrImageResource(mediaItem).not()
        ) {
            return false
        }

        configAbility?.let {
            return (it.getBooleanConfig(NO_MORE_REMIND_WHEN_EDIT_LOCAL_HDR, false) ?: false).not()
        }
        return false
    }

    /**
     * 拖拽编辑根据条件确认是否需要弹出 OLive、HDR图片编辑后 失去OLive、HDR效果的弹窗。
     * @param mediaItem MediaItem
     * @return Boolean
     */
    private fun dropPictureNeedShowOLiveXDRNotSupportDialog(mediaItem: MediaItem): Boolean {
        // 不支持oLive的设备即使是livephoto也会作为静态图处理
        val isOlivePhoto = mediaItem.isOlivePhoto() && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE)
        val shouldShowXdrNotSupportDialog = dropPictureNeedShowXDRNotSupportDialog(mediaItem)
        GLog.d(TAG, LogFlag.DL) {
            "[dropPictureNeedShowOLiveXDRNotSupportDialog] isOlivePhoto: $isOlivePhoto," +
                    "shouldShowXdrNotSupportDialog: $shouldShowXdrNotSupportDialog"
        }
        if (isOlivePhoto && shouldShowXdrNotSupportDialog) {
            return ConfigAbilityWrapper.getBoolean(NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG).not()
                    || ConfigAbilityWrapper.getBoolean(NO_MORE_REMIND_IN_PHOTO_EDIT).not()
        }
        if (isOlivePhoto) {
            return ConfigAbilityWrapper.getBoolean(NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG).not()
        }
        if (shouldShowXdrNotSupportDialog) {
            return ConfigAbilityWrapper.getBoolean(NO_MORE_REMIND_IN_PHOTO_EDIT).not()
        }
        return false
    }

    private fun dropPictureNeedShowXDRNotSupportDialog(mediaItem: MediaItem): Boolean {
        // 当前已经提亮起来了或者支持提亮
        val isBrightEnable =
            ((viewModel.brighten.isBrightnessToggleStateEnabled()) && HdrSdrManagerOs13.SUPPORT_NOTIFY_BRIGHTNESS_SCALE)
                    || viewModel.brighten.isSupportHdrImagePresent

        // 当前正在显示的内容，是否有HDR效果
        val isFocusSupportHdrImage = viewModel.brighten.isFocusSupportHdrImage()

        //local hdr的开关打开
        val hdrImageSwitchState = ConfigAbilityWrapper.getBoolean(HDR_IMAGE_SWITCH)
        //没点击过不再提示
        val isNeverRemind = ConfigAbilityWrapper.getBoolean(NO_MORE_REMIND_IN_PHOTO_EDIT)

        GLog.d(TAG, LogFlag.DL) {
            "brightenState:${viewModel.brighten.isBrightnessToggleStateEnabled()}," +
                    "SUPPORT_NOTIFY_BRIGHTNESS_SCALE:${HdrSdrManagerOs13.SUPPORT_NOTIFY_BRIGHTNESS_SCALE}," +
                    "isSupportHdrImagePresent:${viewModel.brighten.isSupportHdrImagePresent}，isBrightEnable:$isBrightEnable," +
                    "switch:$hdrImageSwitchState, isNeverRemind:$isNeverRemind,isHdrImageResource:${isHdrImageResource(mediaItem)}," +
                    "isFocusSupportHdrImage:$isFocusSupportHdrImage"
        }

        /**
         * 显示hdr tips的条件
         * 1. 是XDR图
         * 2. local hdr的开关打开
         * 3. 当前已经提亮起来了或者支持提亮
         * 4. 当前正在显示的内容，有HDR效果
         * 5. 不处于浮窗
         * 6. 不处于分屏
         * 7. 没点击过不再提示
         */
        return isHdrImageResource(mediaItem)
                && hdrImageSwitchState
                && isBrightEnable
                && isFocusSupportHdrImage
                && activity.isFloatingWindowMode().not()
                && activity.isInMultiWindowMode.not()
                && isNeverRemind.not()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (normalNotSupportDialog?.isShowing() == true) {
            normalNotSupportDialog?.cancel()
            normalNotSupportDialog = null
        }
        //关闭 ability
        closeAbilities()
    }

    override fun handleResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        // 禁止大图后台数据加载
        viewModel.pageManagement.requestPageOperation(DisableDataInBackground, OPERATOR_TOKEN_DATA)
        if (intent?.getBooleanExtra(KEY_IS_CANCEL_FROM_EDIT, true) == false) {
            viewModel.pageManagement.setShouldDisableThumbLineAnimation(true)
        }
        intent?.let {
            /*
             * 放在最前面，保证最快被执行到
             * 规避bug：7154928(人像/olive/合影/连拍/证件照/超级文本1.0 编辑保存新图，大图顶部闪现其图标问题)
             */
            suppressTopMenuIconShowIfNeed(requestCode, resultCode, it)
        }

        if (requestCode == MenuRequestCode.VIDEO_EDIT) {
            viewModel.playback.changeAudioFocusRequestState(isNeedRequest = true)
        }
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == MenuRequestCode.VIDEO_EDIT) {
                // 产品逻辑如此，视频编辑完，需要保持非静音
                viewModel.playback.notifyAudioStateChanged(isMuted = false)
            }
            when (editType) {
                NORMAL, PRIVACY_WATERMARK, DROP_PICTURE, AI_BESTTAKE, PORTRAIT_BLUR -> handleResultForNormalInvokerType(intent)
                GROUP_PHOTO -> handleResultForGroupPhotoInvokerType(intent)
                else -> GLog.d(TAG, LogFlag.DL, "handleResult, else branch.")
            }
        }
        // 退出编辑时，取消支持编辑触发大图后台加载
        taskManageAbility?.let {
            it.unregister(PhotoEditTaskOwner.apply {
                isEnterEditor = false
            })
            it.unregister(VideoEditTaskOwner.apply {
                isEnterEditor = false
            })
        }
    }

    /**
     * 在需要的时候抑制菜单展示icon
     * 场景：规避 人像，olive图 编辑后另存为新图时 大图时闪现图标问题
     */
    private fun suppressTopMenuIconShowIfNeed(requestCode: Int, resultCode: Int, intent: Intent) {
        // 当有新一次的编辑返回时，先清理掉上一次的监听及超时逻辑
        clearSuppressTopMenuTimeOutRunnable()
        lastPageOperationStateCancellable?.cancel()

        // 是否从图片编辑返回
        val isFromEditPhoto = (resultCode == Activity.RESULT_OK) && (requestCode == MenuRequestCode.PHOTO_EDIT_DEFAULT)

        // 是否为有缩图轴的大图：闪图标目前只在缩图轴版本出现，非缩图轴有翻页效果视觉上不会感觉闪
        val isThumbLineEnabled = viewModel.inputArguments.features.value?.isThumbLineEnabled ?: false

        // 编辑返回后，需要跳转到的对应path: 编辑保存必有值且已准备好media item
        val hintPath = intent.getStringExtra(KEY_MEDIA_ITEM_PATH)

        // 当前焦点数据的path
        val focusPath = viewModel.dataLoading.focusItemViewData?.id

        if (isFromEditPhoto.not() || isThumbLineEnabled.not() || (hintPath == null) || (hintPath == focusPath)) {
            GLog.d(TAG, LogFlag.DL) {
                "[suppressTopMenuIconShowIfNeed] no need to suppress top menu display, return." +
                        " isFromEditPhoto = $isFromEditPhoto," +
                        " isThumbLineEnabled = $isThumbLineEnabled," +
                        " hintPath = $hintPath," +
                        " focusPath = $focusPath"
            }
            return
        }

        // 判断新图是否和当前图片为同一数据源
        viewModel.inputArguments.updateSlotByNewDataSource(intent) { _, isNewDataSource, _, _ ->

            // 当使用新数据时，会开新大图，不做处理
            if (isNewDataSource) {
                GLog.d(TAG, LogFlag.DL) { "[suppressTopMenuIconShowIfNeed] no need to suppress top menu show, return. isNewDataSource = true" }
                return@updateSlotByNewDataSource
            }
            // 抑制icon显示
            viewModel.menuControl.suppressMenuIconShow()

            //添加兜底操作，万一pageOperationState的更新出了啥飞机，也不能导致图标一直不显示
            activity.window?.decorView?.postDelayed(suppressMenuTimeOutRunnable, DELAY_REFRESH_TOP_MENU)

            lastPageOperationStateCancellable = viewModel.pageManagement.pageOperationState.observeOnce(fragment) { state ->
                // 当已更新到新的焦点图且大图处于非抑制状态时，不需要再抑制菜单图标显示
                val noNeedSuppressTopMenuIcon: Boolean = lastPageOperationState.hasOperations
                        && (state == NoneOperation)
                        && hintPath == viewModel.dataLoading.focusItemViewData?.id

                lastPageOperationState = state

                if (noNeedSuppressTopMenuIcon) {
                    clearSuppressTopMenuTimeOutRunnable()
                    viewModel.menuControl.suppressMenuIconShow(isSuppress = false)
                }

                noNeedSuppressTopMenuIcon
            }
        }
    }

    private fun clearSuppressTopMenuTimeOutRunnable() {
        activity.window?.decorView?.removeCallbacks(suppressMenuTimeOutRunnable)
    }

    private fun handleHdrImageResult(intent: Intent?) {
        // HDR资源在当前状态下不支持提亮,跳过后续逻辑
        if (viewModel.brighten.isSupportHdrImagePresent.not()) {
            GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause HDR present not supported" }
            return
        }
        val isEditBrightenFeatureEnabled =
            viewModel.brighten.isLocalHdrEditBrightenFeatureEnabled || viewModel.brighten.isUHdrEditBrightenFeatureEnabled
        // 系统未配置支持编辑页面提亮,跳过后续逻辑
        if (isEditBrightenFeatureEnabled.not()) {
            GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause feature not supported" }
            return
        }

        // 当前屏幕背光未真正提亮(包括Local HDR的提亮和杜比视界的提亮,该标记不区分Layer,不区分业务),跳过后续逻辑
        if (viewModel.brighten.isBrightnessToggleStateEnabled().not()) {
            GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause toggle state not enabled" }
            return
        }

        fragment.view?.getEdrSurfaceControl()?.let { surfaceControl ->
            hardwareAbility.let {
                it?.screen?.notifyBrightenSessionEvent(surfaceControl, IBrightenSession.SessionEvent.EXIT_EDIT_PAGE)
            }
        }

        val extras = intent?.extras ?: let {
            GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause extras is null" }
            return
        }

        val grayImageBinder: IBinder = extras.getBinder(KEY_EDIT_BRIGHTEN_GRAY_IMAGE) ?: let {
            GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause grayImageBinder is null" }
            return
        }

        try {
            val transBitmapBinder: ITransBitmap = ITransBitmap.Stub.asInterface(grayImageBinder) ?: let {
                GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause transBitmapBinder is null" }
                return
            }

            val maskImage = transBitmapBinder.getBitmap()
            val metaData: IHdrMetadataPack = extras.getParcelable(KEY_EDIT_BRIGHTEN_METADATA) ?: let {
                GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] skip , cause metaData is null" }
                return
            }

            val mediaId = extras.getInt(KEY_MEDIA_ID)
            val rotation = extras.getInt(KEY_EDIT_ROTATION)
            if (maskImage.isRecycled) {
                GLog.e(TAG, LogFlag.DL, "[handleHdrImageResult] maskImage isRecycled true")
                return
            }
            // 这里因为图片直接从binder中获取，需要主动recycle一下，加快内存释放，直接拷贝是为了避免后续这份内存无法锁定
            val copyMaskBmp = maskImage.copy(maskImage.getConfigSafely(), maskImage.isMutable)
            BitmapUtils.recycleSilently(maskImage)
            val hdrImageContent = HdrImageDrawable(grayImage = copyMaskBmp, metadata = metaData)
            val hdrImageArgument = PhotoEnterHdrImageArgument(mediaId, hdrImageContent, rotation)
            viewModel.brighten.setTargetHdrImageArgument(hdrImageArgument)
        } catch (e: RemoteException) {
            GLog.w(TAG, LogFlag.DL) { "[handleHdrImageResult] failed, exception: $e" }
        }
    }

    private fun handleResultForGroupPhotoInvokerType(intent: Intent?) {
        GLog.d(TAG, LogFlag.DL, "handleResultForGroupPhotoInvokerType")
        intent?.let {
            viewModel.inputArguments.updateSlotByNewDataSource(
                intent = intent,
                updateSlotAction = ::performUpdateSlot
            )
        } ?: GLog.w(TAG, LogFlag.DL, "handleResultForGroupPhotoInvokerType, intent is null")
    }

    private fun handleResultForNormalInvokerType(intent: Intent?) {
        GLog.d(TAG, LogFlag.DL, "handleResultForNormalInvokerType")
        intent?.let {
            handleHdrImageResult(intent)
            viewModel.inputArguments.updateSlotByNewDataSource(
                intent = intent,
                updateSlotAction = ::performUpdateSlot
            )
        } ?: GLog.w(TAG, LogFlag.DL, "handleResultForNormalInvokerType, intent is null")
    }

    private fun performUpdateSlot(data: Intent, isDifferenceDataSource: Boolean, targetItemPath: Path, targetAlbumPath: Path) {
        if (isDifferenceDataSource) {
            val pageData = data.extras?.let(::Bundle) ?: Bundle()
            pageData.apply {
                putString(KEY_MEDIA_SET_PATH, targetAlbumPath.toString())
                putString(KEY_MEDIA_ITEM_PATH, targetItemPath.toString())
                putString(KEY_MEDIA_MODEL_TYPE, TYPE_LOCAL_ALBUM)
                putBoolean(KEY_NOT_DISPLAY_CSHOT_BTN, true)
                putBoolean(KEY_SHOULD_DECODE_MICRO_PREVIEW_SHOT, true)
                putCharSequence(NAVIGATE_UP_TITLE_TEXT, viewModel.inputArguments.title.value?.titleContent)
                viewModel.inputArguments.features.value?.screenOrientation?.let {
                    putInt(IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION, it)
                }
                putBoolean(KEY_DISPLAY_SET_COVER_MENU, viewModel.dataLoading.isCoverSupported())
            }
            GLog.d(TAG, LogFlag.DL, "doUpdateSlotOrStartNewPhotoPage, startPage")

            /**
             * 虚拟图集进编辑保存回到大图时，会另起新大图页，如果两个大图页Fragment都有SurfaceView，并且先后addView（非同一帧），
             * 那么后add的那个就会显示在上方，而无视View层级关系，导致显示错误。这里提前给原大图设置Flag，抑制原大图加载资源，待新的Page销毁后，再重新走加载流程。
             * 具体异常场景：Olive虚拟图集->大图A->编辑保存->跳到大图B，高概率出现大图B先add SurfaceView，大图A后add，界面显示大图A的SurfaceView
             **/
            viewModel.contentLoading.updateSuppressRendererFlag(true)
            menuScope?.startPage(activity.findFullScreenContainerId(), PICTURE_FRAGMENT, pageData)
        } else {
            GLog.d(TAG, LogFlag.DL, "doUpdateSlotOrStartNewPhotoPage, notifyChangeFocusHint")
            viewModel.brighten.requestForceUpdateBrightenRegion(FORCE_UPDATE_BRIGHTEN_REGION_DURATION, TAG)
            viewModel.inputArguments.notifyChangeFocusHint(
                itemPath = targetItemPath.toString(),
                itemUri = data.data,
                itemType = data.type
            )
            /**
             * 当前大图显示OLive资源时，左右两边的OLive资源需要提前进入滑动播放的第一帧，并做放大处理
             * 再编辑回到大图的时候，很容易透出旁边的视频边缘，这里做个规避。
             */
            viewModel.olive.updateSupressOLiveVisibilityFlag(true)
        }
    }

    /**
     * 下载原图并进入编辑
     * @param mediaItem 媒体对象
     * @param onDone 菜单操作执行完成后，不论成败，都需要会通过此接口回调操作已结束
     * @param isCallFromXDRDialog 是否从XDR的Dialog中call过来
     */
    private fun downloadOriginalAndEdit(mediaItem: MediaItem, onDone: () -> Unit, isCallFromXDRDialog: Boolean = false) {
        val scene = getOriginalDownloadScene()
        if (null == scene) {
            // 不触发解原图进编辑的场景下，不提前解缩图
            doEditImage(mediaItem, onDone, null, isCallFromXDRDialog)
            GLog.d(TAG, LogFlag.DL) {
                "[execute] don't match scene to download original, do action directly,editType = ${editType.tag}, return!"
            }
            return
        }
        viewModel.viewModelScope.launch(Dispatchers.CPU) {
            val thumbnail = loadPreviewThumbnail(mediaItem)
            withContext(Dispatchers.UI) {
                viewModel.cloudSync.triggerDownloadOriginal(
                    activity = activity,
                    scene = scene,
                    onDownloadOriginalFailAction = { _, _ -> onDone() },
                    onDownloadOriginalSuccessAction = { doEditImage(mediaItem, onDone, thumbnail, isCallFromXDRDialog) })
            }
        }
    }

    /**
     * 触发原图下载的场景
     */
    private fun getOriginalDownloadScene(): FileProcessScene? {
        return when (editType) {
            NORMAL -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_EDIT
            AI_ID_PHOTO -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_AI_ID_PHOTO_EDIT
            ENHANCE_TEXT -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_ENHANCE_TEXT_EDIT
            SUPER_TEXT -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_ENTER_SUPER_TEXT_EDIT
            PRIVACY_WATERMARK -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_ENTER_PRIVACY_WATERMARK_EDIT
            IMAGE_QUALITY_ENHANCE -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_ENTER_IMAGE_QUALITY_ENHANCE_EDIT
            GROUP_PHOTO -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_GROUP_PHOTO
            DROP_PICTURE -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_DROP_PICTURE
            AI_REPAIR_DEBLUR -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_AI_REPAIR_DEBLUR
            AI_REPAIR_DEREFLECTION -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_AI_REPAIR_DEREFLECTION
            PASSERBY_ELIMINATE -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_PASSERBY_ELIMINATE
            AI_BESTTAKE -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_AI_BEST_TAKE
            AI_LIGHTING -> FileProcessScene.DOWNLOAD_ORIGIN_SCENE_AI_LIGHTING
            else -> null
        }
    }

    /**
     * 视频编辑
     */
    private fun doEditVideo(
        mediaItem: MediaItem,
        onDone: () -> Unit,
        preThumbnail: Drawable? = null
    ) {
        if (MeicamEngineLimiter.getInstance().allowCreateNewEngine().not()) {
            ToastUtil.showShortToast(R.string.photopage_menu_can_not_create_multiple_editor_and_export)
            GLog.d(TAG, LogFlag.DL, "[doEditVideo] have exist an editor, can`t do edit video. ignore!")
            onDone()
            return
        }

        val invoker = when {
            extras?.getBoolean(KEY_IS_VIDEO_WALLPAPER_CUT) == true -> VALUE_INVOKER_GALLEY_WALLPAPER_PAGE
            invokerToken.isNotEmpty() -> invokerToken
            parentPage == CUR_PAGE_SEARCH_RESULT -> VALUE_INVOKER_SEARCH_PAGE
            isInvokeFromCameraForEdit -> VALUE_INVOKER_GALLERY_PAGE_FROM_CAMERA
            editType == EXPORT_OLIVE -> VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE
            else -> VALUE_INVOKER_GALLERY_PAGE
        }
        val mediaItemContentUri = mediaItem.contentUri ?: let {
            GLog.e(TAG, LogFlag.DL, "[doEditVideo] mediaItem#contentUri is null")
            return
        }

        // 同disableHdrImageIfNeed dolby视频进编辑也有背光闪烁问题
        disableDolbyIfNeed(mediaItem)

        GLog.d(TAG, LogFlag.DL) {
            "[doEditVideo] type = $editType, fromPage = $parentPage, invoker = $invoker, contentUri = ${mediaItem.contentUri}"
        }
        val colorSpace = (preThumbnail as? ThumbnailDrawable)?.bitmap?.colorSpace
        val positionStrategy = makePositionStrategyForVideoEditor()
        MenuOperationManager.doAction(
            action = MenuAction.EDIT_VIDEO,
            paramMap = MenuActionGetter.editVideo.builder
                .setFragment(fragment)
                .setInvoker(invoker)
                .setInvokerToken(invokerToken)
                .setMediaItem(mediaItem)
                .setModelType(modelType)
                .setVideoUri(mediaItemContentUri)
                .setMediaItemPath(mediaItem.path.toString())
                .setThumbnail(preThumbnail)
                .setColorSpace(colorSpace)
                .setScreenOrientation(viewModel.inputArguments.features.value?.screenOrientation)
                .setChainFrom(viewModel.inputArguments.features.value?.chainFrom)
                .setPositionStrategy(positionStrategy)
                .setEditTypeTag(editType.tag)
                .setTrackCallerEntry(trackCaller)
                .build(),
            onCompleted = { resultCode, _ ->
                if (resultCode == MenuAction.RESULT_SUCCESS) {
                    taskManageAbility?.let {
                        it.register(VideoEditTaskOwner.apply {
                            isEnterEditor = true
                        })
                    }
                }
                onDone()
            }
        )
        viewModel.pageManagement.notifyTransferToEditorPage()
        viewModel.playback.changeAudioFocusRequestState(isNeedRequest = false)
    }

    /**
     * 图片编辑
     * @param mediaItem 当前的mediaItem
     * @param onDone 菜单操作执行完成后，不论成败，都需要会通过此接口回调操作已结束
     * @param preThumbnail 提前解码的thumbnailDrawable
     * @param isCallFromXDRDialog 是否从XDR的Dialog中call过来
     */
    private fun doEditImage(
        mediaItem: MediaItem,
        onDone: () -> Unit,
        preThumbnail: Drawable? = null,
        isCallFromXDRDialog: Boolean = false
    ) {
        when {
            editType.tag == GROUP_PHOTO.tag -> doGroupPhotoEdit(mediaItem, onDone, preThumbnail, isCallFromXDRDialog)
            mediaItem.isOlivePhoto() -> doOlivePhotoEdit(mediaItem, onDone, preThumbnail, isCallFromXDRDialog)
            else -> doEditImageAfterCheck(mediaItem, onDone, preThumbnail, isCallFromXDRDialog)
        }
    }

    private fun doGroupPhotoEdit(
        mediaItem: MediaItem,
        onDone: () -> Unit,
        preThumbnail: Drawable?,
        isCallFromXDRDialog: Boolean
    ) {
        runOnUiThread {
            (fragment as? PhotoFragment)?.activity?.let {
                val groupPhotoEntranceHelper = GroupPhotoEntranceHelper(it)
                groupPhotoEntranceHelper.setGroupPhotoEntranceListener(object : EntranceListener {
                    override fun onEnter() {
                        GLog.d(TAG, LogFlag.DL) { "[onEnter] success, go to editablePage!" }
                        doEditImageAfterCheck(mediaItem, onDone, preThumbnail, isCallFromXDRDialog)
                        groupPhotoEntranceHelper.removeGroupPhotoEntranceListener()
                    }

                    override fun onRefuse(reason: RefuseNextAction?) {
                        GLog.d(TAG, LogFlag.DL) { "[onRefuse] refuse to install AI-GroupPhoto, return!" }
                        onDone()
                        groupPhotoEntranceHelper.removeGroupPhotoEntranceListener()
                        return
                    }

                    override fun onRetryLoadSo() {
                        GLog.d(TAG, LogFlag.DL) { "[onRetryLoadSo] maybe install failed, just return!" }
                        onDone()
                        groupPhotoEntranceHelper.removeGroupPhotoEntranceListener()
                        return
                    }
                })
                groupPhotoEntranceHelper.start()
            }
        }
    }

    /**
     * Olive 图进编辑
     */
    private fun doOlivePhotoEdit(
        mediaItem: MediaItem,
        onDone: () -> Unit,
        preThumbnail: Drawable? = null,
        isCallFromXDRDialog: Boolean = false
    ) {
        runOnWorkThread(coroutineContext = Dispatchers.IO) {
            val checkResult = GTrace.trace("$TAG.checkOlivePhoto") {
                checkOlivePhoto(mediaItem)
            }

            runOnUiThreadImmediate {
                when (checkResult) {
                    CheckResult.Pass -> doEditImageAfterCheck(mediaItem, onDone, preThumbnail, isCallFromXDRDialog)
                    CheckResult.Fail -> {
                        ToastUtil.showShortToast(R.string.photopage_edit_toast_frame_too_hight)
                        onDone()
                    }
                    CheckResult.Error -> onDone()
                }
            }
        }
    }

    /**
     * 判断 olive 中的视频是否是杜比视频
     */
    @WorkerThread
    private fun checkOlivePhoto(mediaItem: MediaItem): CheckResult {
        val timeStart = SystemClock.elapsedRealtime()

        // 解析 Olive 文件，获取视频信息和指向视频的路径
        val oliveVideo = OLiveDecode.create(mediaItem.filePath).decode()?.microVideo ?: let {
            GLog.e(TAG, LogFlag.DL) { "[checkOlivePhoto] failed to open olive file, mediaId: ${mediaItem.mediaId}" }
            return CheckResult.Error
        }
        if (oliveVideo.offset <= 0 || oliveVideo.length <= 0) {
            // Olive 文件无视频信息
            GLog.e(TAG, LogFlag.DL) { "[checkOlivePhoto] video is invalid, offset: ${oliveVideo.offset}, length: ${oliveVideo.length}" }
            return CheckResult.Error
        }
        val videoPath = formatNvmFilePath(mediaItem.contentUri, oliveVideo.offset, oliveVideo.length)

        // 如果通过美摄接口获取不到视频信息，先不拦截，使其能进入编辑页，编辑页有对应的处理
        val nvsFileInfo = specHelper.getNvsFileInfo(videoPath, activity)
        nvsFileInfo ?: run {
            GLog.e(TAG, LogFlag.DL) { "[checkOlivePhoto] video is invalid, nvsFileInfo is null" }
            return CheckResult.Pass
        }

        // 1. 超规格的 Olive 图不能进入编辑，检查规格和是否被编辑支持
        val specificationValid = specHelper.checkSpecValidOnPlatform(videoPath, activity)
        val supportedByEditor = specHelper.checkSupportedByEditor(videoPath, activity)
        if (!specificationValid || !supportedByEditor) {
            GLog.w(TAG, LogFlag.DL) { "[checkOlivePhoto] videoSpecValid: $specificationValid, videoSupported: $supportedByEditor" }
            return CheckResult.Fail
        }

        val isDolbyVideoOlive = isDolbyVideoOlive(mediaItem, oliveVideo.offset, oliveVideo.length) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[checkOlivePhoto] isDolbyVideoOlive is null" }
            return CheckResult.Error
        }
        if (isDolbyVideoOlive) {
            GLog.w(TAG, LogFlag.DL) { "[checkOlivePhoto] isDolbyVideoOlive is false" }
            return CheckResult.Fail
        }

        GLog.d(TAG, LogFlag.DL) { "[checkOlivePhoto] Olive check finished, time used: ${SystemClock.elapsedRealtime() - timeStart}" }
        return CheckResult.Pass
    }

    /**
     * 判断 olive 中的视频是否是杜比视频
     *
     * @return null 如果判断失败，true 如果符合条件，false 如果不符合条件
     */
    private fun isDolbyVideoOlive(mediaItem: MediaItem, offset: Long, length: Long): Boolean? {
        // 从数据库获取
        mediaItem.extra?.let { extra ->
            return extra.isDolbyVideo()
        }
        // 从文件获取
        val videoTypeTimeStart = SystemClock.elapsedRealtime()
        return mediaItem.contentUri.getFileDescriptorSafely(activity, OpenFileMode.MODE_READ.mode, TAG)?.use { pfd ->
            val metadataKeys = listOf(MetadataKey.CODEC_TYPE)
            val metadataGetter = MetadataGetterFactory.create(MetadataGetterFactory.DEFAULT_GETTER)
            val metadataMap = metadataGetter.parse(metadataKeys, pfd.fileDescriptor, Pair(offset, length))
            VideoTypeUtils.isDolbyVideo(metadataMap.getString(MetadataKey.CODEC_TYPE))
        }.also {
            GLog.d(TAG, LogFlag.DL) { "[isDolbyVideoOlive] isDolbyVideo: $it, time used: ${SystemClock.elapsedRealtime() - videoTypeTimeStart}" }
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "[isDolbyVideoOlive] failed to open file" }
            null
        }
    }

    /**
     * 进入视频编辑页前，为视频编辑创建一个位置策略：[ITransitionBoundsProvider]
     */
    private fun makePositionStrategyForVideoEditor(): ITransitionBoundsProvider {
        val boundsProvider = PhotoTransitionBoundsProvider()

        // 获取当前视频内容的显示区域
        val contentDisplayRect = Rect()
        (fragment as? PhotoFragment)?.let { fragment ->
            val containerSection = fragment.requireSection<PhotoContainerSection>() ?: let {
                GLog.e(TAG, LogFlag.DL) { "[makePositionStrategy] why is containerSection null, no config? CHECK CODE!" }
                null
            }
            containerSection?.currentSlotContentRect?.let { contentRect -> contentDisplayRect.set(contentRect) } ?: let {
                GLog.e(TAG, LogFlag.DL) { "[makePositionStrategy] failed to get contentRect, black flash may happen!" }
            }
        }

        return boundsProvider.apply { setEnterBounds(contentDisplayRect) }
    }

    /**
     * 图片编辑
     * @param mediaItem 当前的mediaItem
     * @param onDone 菜单操作执行完成后，不论成败，都需要会通过此接口回调操作已结束
     * @param preThumbnail 提前解码的thumbnailDrawable
     * @param isCallFromXDRDialog 是否从XDR的Dialog中call过来
     */
    private fun doEditImageAfterCheck(
        mediaItem: MediaItem,
        onDone: () -> Unit,
        preThumbnail: Drawable? = null,
        isCallFromXDRDialog: Boolean = false
    ) {
        if (MeicamEngineLimiter.getInstance().allowCreateNewEngine().not()) {
            ToastUtil.showShortToast(R.string.photopage_menu_can_not_create_multiple_editor_and_export)
            GLog.d(TAG, LogFlag.DL, "[doEditImageAfterCheck] have exist an editor, can't do edit. ignore!")
            onDone()
            return
        }

        viewModel.viewModelScope.launch(Dispatchers.UI) {
            val paramMap = withContext(Dispatchers.CPU) {
                doPrepareEditImageParam(mediaItem, onDone, preThumbnail, isCallFromXDRDialog)
            } ?: return@launch

            // 进入编辑页
            val enterEditor = fun() {
                MenuOperationManager.doAction(
                    action = MenuAction.EDIT_PHOTO,
                    paramMap = paramMap,
                    onCompleted = { resultCode, _ ->
                        if (resultCode == MenuAction.RESULT_SUCCESS) {
                            // 进入编辑后，支持编辑触发大图数据加载，见[PhotoViewModel]
                            taskManageAbility?.let {
                                it.register(PhotoEditTaskOwner.apply {
                                    isEnterEditor = true
                                })
                            }
                        }
                        if (resultCode != MenuAction.RESULT_SUCCESS) {
                            // 如果进入编辑失败，要退出沉浸式
                            viewModel.pageManagement.changeImmersionInteractive(isImmersiveEnabled = false)
                        }
                        onDone()

                        viewModel.pageManagement.notifyTransferToEditorPage()
                    }
                )
            }

            /**
             * 进入编辑之前，尝试关闭Local HDR效果
             * 页面切换时，如果当前Layer有Local HDR效果，显示服务关闭Local HDR效果时使用的是带动画关闭。使用该方式关闭在一些场景下会造成屏幕背光闪烁
             * 屏显是建议统一改成不带动画关闭，但显示服务直接修改影响范围很大，需要做充分评估和测试才会修改。
             * 鉴于该问题在相册大图进编辑的时候比较容易出现，因此此处由相册先做规避。在进入编辑之前，提前将Local HDR 1帧将亮度的方式关闭。
             */
            disableHdrImageIfNeed(mediaItem)

            // 进入编辑之前，先隐藏大图的顶部和底部菜单
            PhotoDecorationState.Hide(
                PhotoDecorationState.DecorationAnimator(
                    onEnd = {
                        // 菜单隐藏动画结束后，进入编辑
                        enterEditor()
                    }
                ), shouldHideNaviBar = false, shouldCancelAnimator = true).let(viewModel.pageManagement::requestDecorationState)
        }
    }

    /**
     * 进编辑原图下载前提前获取PagerThumb
     * @param item 待解析缩图的mediaItem
     * @return 解析后的thumbnail
     * */
    @WorkerThread
    private fun loadPreviewThumbnail(item: MediaItem): Drawable? {
        var thumbnail: Drawable? = null
        measureTimeMillis {
            var mediaItem = item.toOriginalItem()
            if ((mediaItem is LocalMediaItem) && mediaItem.isLoaded.not()) {
                mediaItem = LocalMediaDataHelper.getLocalMediaItem(mediaItem.path)
            }
            val mediaResKey = mediaItem?.let(ResourceKeyFactory::createResourceKey) ?: run {
                GLog.w(TAG, LogFlag.DL) { "[loadPreviewThumbnail] createResourceKey fail! return null!" }
                return null
            }
            val isSupportLossLessCache = (MimeTypeUtils.isBmp(mediaItem.mimeType) || ImageTypeUtils.supportLosslessCache(mediaItem))
            val resourceGetOptions = ResourceGetOptions(
                inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(),
                inCacheOperation = CacheOperation.ReadWriteAllCache - CacheOperation.WriteMemCache,
                inCropParams = CropParams.noCrop(),
                inStorageQuality = if (isSupportLossLessCache) StorageQuality.LOSSLESS else StorageQuality.NOT_CARE
            )
            viewModel.context.withAbility<IResourcingAbility, Bitmap?> {
                it?.requestBitmap(mediaResKey, resourceGetOptions)?.result
            }?.let {
                thumbnail = ThumbnailDrawable(it, mediaItem.getThumbnailRotation())
            }
        }.apply {
            GLog.d(TAG, LogFlag.DL, "[loadPreviewThumbnail], cost time:$this")
        }
        return thumbnail
    }

    /**
     * @param mediaItem 当前的mediaItem
     * @param onDone 菜单操作执行完成后，不论成败，都需要会通过此接口回调操作已结束
     * @param preThumbnail 提前解码的thumbnailDrawable
     * @param isCallFromXDRDialog 是否从XDR的Dialog中call过来
     * @return 进入编辑前收集的参数paramMap
     * */
    @Suppress("UnsafeCast", "LongMethod")
    private suspend fun doPrepareEditImageParam(
        mediaItem: MediaItem,
        onDone: () -> Unit,
        preThumbnail: Drawable? = null,
        isCallFromXDRDialog: Boolean = false,
    ): Map<String, Any?>? {
        val startTime = System.currentTimeMillis()
        val positionStrategy = PhotoTransitionBoundsProvider()
        // invoker是调用者，不要跟要使用的编辑能力混在一起，这是两个不同概念
        val invoker = when {
            invokerToken.isNotEmpty() -> invokerToken
            parentPage == CUR_PAGE_SEARCH_RESULT -> VALUE_INVOKER_SEARCH_PAGE
            isInvokeFromCameraForEdit -> VALUE_INVOKER_GALLERY_PAGE_FROM_CAMERA
            else -> VALUE_INVOKER_GALLERY_PAGE
        }
        // editSkill是需要使用的编辑能力
        val editSkill: String = editType.tag

        GLog.d(TAG, LogFlag.DL) {
            "[doPrepareEditImageParam] editType = $editType, fromPage = $parentPage, invoker = $invoker"
        }

        val isEditCShot = if (mediaItem is LocalImage) {
            mediaItem.cShotID != 0L
        } else false

        val isSupportLossLessCache = (mediaItem.getSupportFormat(FORMAT_LOSS_LESS_CACHE) == FORMAT_LOSS_LESS_CACHE)

        var thumbnail: Drawable? = null
        val contentDisplayRect = Rect()
        var colorSpace: ColorSpace? = null
        var extras: Bundle? = null
        (fragment as? PhotoFragment)?.let {
            val containerSection = fragment.requireSection<PhotoContainerSection>() ?: let {
                GLog.e(TAG, LogFlag.DL) { "[doPrepareEditImageParam] why is containerSection null, no config? CHECK CODE!" }
                null
            }
            containerSection?.currentSlotContentRect?.let { contentRect -> contentDisplayRect.set(contentRect) }

            when (editType) {
                /* 超级文本识文页需要高清预览图，否则进去时候文字会模糊一下
                   mark by youpeng 这里如果要请求高清图，应该在跳转后的超级文本业务里做 */
                SUPER_TEXT -> thumbnail = getLargeThumbnailDrawable(mediaItem)
                PORTRAIT_BLUR -> {
                    thumbnail = getWithoutWatermarkThumbnailDrawable(mediaItem, preThumbnail, contentDisplayRect)
                    val resultRect = getWithoutWatermarkContentRect(mediaItem, contentDisplayRect)
                    contentDisplayRect.set(resultRect)
                }
                GROUP_PHOTO -> {
                    thumbnail = getWithoutWatermarkThumbnailDrawable(mediaItem, preThumbnail, contentDisplayRect)
                    GLog.d(TAG, LogFlag.DL) { "<doPrepareEditImageParam> after contentDisplayRect=${contentDisplayRect.toShortString()}" }
                }
                DROP_PICTURE -> {
                    thumbnail = getWithoutWatermarkThumbnailDrawable(mediaItem, preThumbnail, contentDisplayRect)
                    extras = Bundle().apply {
                        viewModel.common.dropPictureUri?.let { uri ->
                            putString(KEY_DROP_PICTURE_URI, uri.toString())
                        }
                    }
                }
                AI_SCENERY,
                AI_REPAIR_DEBLUR,
                AI_REPAIR_DEREFLECTION,
                AI_LIGHTING,
                IMAGE_QUALITY_ENHANCE,
                AI_COMPOSITION,
                PASSERBY_ELIMINATE,
                AI_BESTTAKE -> {
                    thumbnail = getWithoutWatermarkThumbnailDrawable(mediaItem, preThumbnail, contentDisplayRect)
                    extras = getWatermarkExtras(mediaItem)
                }
                else -> {
                    thumbnail = preThumbnail ?: run {
                        GLog.w(TAG, LogFlag.DL, "[doPrepareEditImageParam], preThumbnail is null, need decode thumbnail drawable!")
                        getThumbnailDrawable()
                    }
                    extras = getWatermarkExtras(mediaItem)
                }
            }
            colorSpace = (thumbnail as? ThumbnailDrawable)?.bitmap?.colorSpace
        }
        val requestCode = when (editType) {
            ENHANCE_TEXT -> MenuRequestCode.PHOTO_EDIT_ENHANCE_TEXT
            SUPER_TEXT -> MenuRequestCode.PHOTO_EDIT_SUPER_TEXT
            PORTRAIT_BLUR -> MenuRequestCode.PHOTO_EDIT_PORTRAIT_BLUR
            PRIVACY_WATERMARK -> MenuRequestCode.PHOTO_EDIT_PRIVACY_WATERMARK
            GROUP_PHOTO -> MenuRequestCode.GROUP_PHOTO_REQUEST_CODE
            DROP_PICTURE -> MenuRequestCode.DROP_PICTURE_REQUEST_CODE
            AI_BESTTAKE -> MenuRequestCode.AI_BESTTAKE_REQUEST_CODE
            else -> MenuRequestCode.PHOTO_EDIT_DEFAULT
        }

        // 将点击菜单时的附加参数，加入到进编辑intent的extras中
        <EMAIL>?.let { extraParams ->
            extras = (extras ?: Bundle()).apply {
                putAll(extraParams)
            }
        }

        // 手机支持提亮，且当前是普通编辑或隐私水印时，尝试获取Hdr的Drawable
        val hdrImageContent =
            if (isCallFromXDRDialog.not() && viewModel.brighten.isSupportSpecifyHdrEdit(mediaItem) && ((editType == NORMAL) || (editType == PRIVACY_WATERMARK))) {
                getContentDrawable(mediaItem)
            } else null

        // 这里判断是三通道的UHdr图，则进行不支持编辑弹框，后续支持后去掉此处代码
        if (isCallFromXDRDialog.not() && needShowHdrImageNotSupportDialog(mediaItem, hdrImageContent)) {
            withContext(Dispatchers.UI.immediate) {
                doEditImage(mediaItem, onDone, thumbnail, true)
            }
            return null
        }

        if (viewModel.brighten.isSupportHdrImagePresent
            && viewModel.brighten.isSupportSpecifyHdrEdit(mediaItem)
            && (viewModel.brighten.isBrightnessToggleStateEnabled())
            && isHdrImageResource(mediaItem)
        ) {
            fragment.view?.getEdrSurfaceControl()?.let { surfaceControl ->
                hardwareAbility?.screen?.notifyBrightenSessionEvent(surfaceControl, ENTER_EDIT_PAGE)
            }
        }
        positionStrategy.setEnterBounds(contentDisplayRect)
        GLog.d(TAG, LogFlag.DL) { "[doPrepareEditImageParam] cost time=${GLog.getTime(startTime)}" }
        return MenuActionGetter.editPhoto.builder
            .setRequestCode(requestCode)
            .setFragment(fragment)
            .setMediaSetPath(mediaSetPath)
            .setPositionStrategy(positionStrategy)
            .setInvoker(invoker)
            .setInvokerToken(invokerToken)
            .setModelType(modelType)
            .setEditSkill(editSkill)
            .setIsCshot(isEditCShot)
            .setIsEditFromPhoto(true)
            .setIsFromPhotoPage(true)
            .setIsShowBackTitle(true)
            .setChainFrom(viewModel.inputArguments.features.value?.chainFrom)
            .setIsSupportLossLessCache(isSupportLossLessCache)
            .setMediaItem(mediaItem)
            .setThumbnail(thumbnail)
            .setExtras(extras)
            .setColorSpace(colorSpace)
            .setHdrImageContent(hdrImageContent)
            .setScreenOrientation(viewModel.inputArguments.features.value?.screenOrientation)
            .setTrackCallerEntry(trackCaller)
            .build()
    }

    private fun checkAndUpdateWaterMarkRect(
        displayRect: Rect,
        originDrawable: Drawable?,
        targetDrawable: Drawable?,
        mediaItem: MediaItem,
        watermarkParams: WatermarkParams
    ) {
        originDrawable ?: return
        targetDrawable ?: return
        if (DisplayUtils.isPortrait(activity).not()
            || mediaItem.getThumbnailRotation() != AppConstants.Degree.DEGREE_0 ||
            watermarkParams.rotation != AppConstants.Degree.DEGREE_0) {
            // @黄红军：其他方向先不处理
            return
        }
        // 去掉水印区域
        if (originDrawable.intrinsicHeight != targetDrawable.intrinsicHeight) {
            val diff = (originDrawable.intrinsicHeight - targetDrawable.intrinsicHeight) *
                    displayRect.height() / originDrawable.intrinsicHeight.toFloat()
            displayRect.bottom -= diff.toInt()
        }
    }

    private fun getLargeThumbnailDrawable(mediaItem: MediaItem): Drawable? {
        val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem.toOriginalItem()) ?: let {
            GLog.w(TAG, LogFlag.DL) { "[getLargeThumbnailDrawable] fail to create resourceKey, path=${mediaItem.path} return null" }
            return null
        }
        val options = ResourceGetOptions(
            inThumbnailType = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL,
            inCacheOperation = CacheOperation.ReadWriteAllCache,
            inCropParams = CropParams.noCrop()
        )
        val bitmap = viewModel.context.withAbility<IResourcingAbility, Bitmap?> {
            it?.requestBitmap(resourceKey, options, null)?.result
        } ?: let {
            GLog.w(TAG, LogFlag.DL) { "[getLargeThumbnailDrawable] fail to request bitmap, resourceKey=$resourceKey return null" }
            return null
        }
        return ThumbnailDrawable(
            bitmap = bitmap,
            rotation = mediaItem.getThumbnailRotation()
        )
    }

    private suspend fun getWatermarkExtras(mediaItem: MediaItem): Bundle = withContext(Dispatchers.CPU) {
        Bundle().apply {
            watermarkAbility?.let { ability ->
                ability.newWatermarkFileOperator(mediaItem.contentUri)?.use { operator ->
                    operator.readWatermarkInfo().let { info ->
                        val isHasselWatermarkEditable = (info.device?.isHasselDevice == true) && info.content?.shootingInfo.isValid()
                        val hasHasselWatermark =
                            (info.device?.isHasselDevice == true) && (info.params?.pattern == WatermarkPattern.PATTERN_FRAME)
                        putBoolean(KEY_HASSEL_WATERMARK_EDITABLE, isHasselWatermarkEditable)
                        putBoolean(KEY_HAS_HASSEL_WATERMARK, hasHasselWatermark)
                    }
                }
            }
        }
    }

    private suspend fun getThumbnailDrawable() = suspendCoroutine {
        viewModel.dataLoading.focusItemViewData?.let { viewData ->
            viewModel.contentLoading.startLoadContent(
                viewData = viewData,
                SizeType.FullThumb(LOAD_TYPE_PAGE_THUMBNAIL)
            ) { drawable ->
                it.resume(drawable)
            }
        }
    }

    /**
     * 获取去水印的缩图Drawable
     * @param mediaItem 当前MediaItem
     * @param preThumbnail 提前解码的thumbnailDrawable
     */
    private suspend fun getWithoutWatermarkThumbnailDrawable(
        mediaItem: MediaItem,
        preThumbnail: Drawable? = null,
        contentDisplayRect: Rect
    ): Drawable? =
        withContext(Dispatchers.CPU) {
            // 默认获取提前解码的thumbnailDrawable; 如果没有传入，则手动去获取Bitmap并转为需要的thumbnailDrawable; 如果还是获取不到就不做操作了
            val drawable: Drawable? = preThumbnail ?: loadPreviewThumbnail(mediaItem)
            drawable ?: run {
                GLog.e(TAG, LogFlag.DL) {
                    "[getWithoutWatermarkThumbnailDrawable] preThumbnail is null, loadPreviewThumbnail is null, we could not get targetDrawable."
                }
                return@withContext null
            }
            val colorSpace = (drawable as? ThumbnailDrawable)?.bitmap?.colorSpace
            val srcBitmap = BitmapUtils.drawableToBitmap(drawable, colorSpace)
            watermarkMasterAbility?.let { watermarkMasterAbility ->
                watermarkMasterAbility.newWatermarkFileOperator(mediaItem.contentUri)?.use { operator ->
                    var watermarkInfo = operator.readWatermarkInfo()
                    watermarkInfo = watermarkInfo.copy(aiWatermarkMasterParams = operator.adapterAiWatermarkMasterParams(watermarkInfo))
                    if (watermarkInfo.isAiMasterWatermark()) {
                        val isSupportLumo = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)
                        if (watermarkInfo.isAiWatermarkMasterAvailable(productBrand, isOppo, isRealme, isOnePlus, isExport, isSupportLumo).not()) {
                            return@withContext drawable
                        }
                        return@withContext removeAiWatermarkMaster(
                            operator,
                            watermarkInfo,
                            watermarkMasterAbility,
                            mediaItem,
                            preThumbnail,
                            contentDisplayRect,
                            srcBitmap
                        ) ?: drawable
                    } else {
                        return@withContext removeWatermark(mediaItem, preThumbnail, contentDisplayRect, srcBitmap) ?: drawable
                    }
                }
            }
            return@withContext drawable
        }

    /**
     * 获取去除水印的rect区域
     * @param mediaItem 当前的MediaItem
     * @param contentDisplayRect 带水印的Rect
     */
    private fun getWithoutWatermarkContentRect(mediaItem: MediaItem, contentDisplayRect: Rect): Rect {
        var resultRect = contentDisplayRect
        watermarkMasterAbility?.newWatermarkFileOperator(mediaItem.contentUri)?.use { operator ->
            val imageDisplayRectF = operator.readWatermarkInfo().aiWatermarkFileExtendInfo?.imageDisplayRect
            if (imageDisplayRectF != null) {
                val left = contentDisplayRect.left + imageDisplayRectF.left * contentDisplayRect.width() / imageDisplayRectF.width()
                val top = contentDisplayRect.top + imageDisplayRectF.top * contentDisplayRect.height() / imageDisplayRectF.height()
                val right = left + contentDisplayRect.width() * imageDisplayRectF.width() / mediaItem.width
                val bottom = top + contentDisplayRect.height() * imageDisplayRectF.height() / mediaItem.height
                resultRect = Rect(left.toInt(), top.toInt(), right.toInt(), bottom.toInt())
            }
        }
        return resultRect
    }

    @Suppress("UnnecessarySafeCall")
    private fun removeAiWatermarkMaster(
        operator: IWatermarkFileOperator,
        watermarkInfo: WatermarkInfo,
        watermarkMasterAbility: IWatermarkMasterAbility,
        mediaItem: MediaItem,
        preThumbnail: Drawable? = null,
        contentDisplayRect: Rect,
        srcBitmap: Bitmap
    ): Drawable? {
        GLog.d(TAG, LogFlag.DL) {
            "removeAiWatermarkMaster: hasWatermark = ${watermarkInfo.params != null}"
        }
        val watermarkParams = watermarkInfo.params ?: return null
        // 有水印才需要特殊处理，无水印按默认流程即可
        val aiWatermarkFileExtendInfo = watermarkInfo.aiWatermarkFileExtendInfo

        if (aiWatermarkFileExtendInfo?.isOnlyFrameWatermark() == true) {
            GLog.d(TAG, LogFlag.DL) { "removeAiWatermarkMaster isOnlyFrameWatermark" }
            val destBitmap = watermarkMasterAbility.removeFrameWatermark(
                mediaItem.rotation,
                watermarkInfo.aiWatermarkFileExtendInfo?.imageDisplayRect,
                srcBitmap,
                watermarkInfo.photoSize
            )
            val destDrawable = ThumbnailDrawable(destBitmap)
            checkAndUpdateWaterMarkRect(contentDisplayRect, preThumbnail, destDrawable, mediaItem, watermarkParams)
            return destDrawable
        } else if (aiWatermarkFileExtendInfo?.isOnlyOverlay() == true) {
            GLog.d(TAG, LogFlag.DL) { "removeAiWatermarkMaster isOnlyOverlay" }
            val scale = mediaItem.width.coerceAtLeast(mediaItem.height).toFloat() /
                    srcBitmap.width.coerceAtLeast(srcBitmap.height)
            // 如果获取不到截图片段，就用默认图了
            val captureBitmap = operator.readCaptureBitmap(srcBitmap.colorSpace, scale, watermarkInfo) ?: return null
            val captureRect = Rect().also { captureRect ->
                watermarkInfo.aiWatermarkFileExtendInfo?.bitmaps?.get(0)?.overlayDisplayRectF?.apply {
                    captureRect.set(
                        floor(left / scale).toInt(),
                        floor(top / scale).toInt(),
                        ceil(right / scale).toInt(),
                        ceil(bottom / scale).toInt()
                    )
                }
            }
            val destBitmap = watermarkMasterAbility.removeOverlayWatermark(
                mediaItem.rotation,
                captureRect,
                srcBitmap,
                captureBitmap
            )
            return ThumbnailDrawable(destBitmap)
        } else if (aiWatermarkFileExtendInfo?.isFrameAndOverlayWatermark() == true) {
            GLog.d(TAG, LogFlag.DL) { "removeAiWatermarkMaster isFrameAndOverlayWatermark" }
        }
        return null
    }

    private fun removeWatermark(
        mediaItem: MediaItem,
        preThumbnail: Drawable? = null,
        contentDisplayRect: Rect,
        srcBitmap: Bitmap
    ): Drawable? {
        watermarkAbility?.let { ability ->
            ability.newWatermarkFileOperator(mediaItem.contentUri)?.use { operator ->
                val params = operator.readWatermarkInfo().params
                GLog.d(TAG, LogFlag.DL) {
                    "removeWatermark: hasWatermark = ${params != null}"
                }
                val watermarkParams = params ?: return null
                // 有水印才需要特殊处理，无水印按默认流程即可
                if (watermarkParams.pattern.isFrameTypeWatermark()) {
                    val destBitmap = ability.removeWatermark(mediaItem.rotation, watermarkParams.displayRect, srcBitmap)
                    val destDrawable = ThumbnailDrawable(destBitmap)
                    checkAndUpdateWaterMarkRect(contentDisplayRect, preThumbnail, destDrawable, mediaItem, watermarkParams)
                    return destDrawable
                } else if (watermarkParams.pattern == WatermarkPattern.PATTERN_NORMAL) {
                    val scale = mediaItem.width.coerceAtLeast(mediaItem.height).toFloat() /
                            srcBitmap.width.coerceAtLeast(srcBitmap.height)
                    // 如果获取不到截图片段，就用默认图了
                    val captureBitmap = operator.readCaptureBitmap(srcBitmap.colorSpace, scale) ?: return null
                    val captureRect = Rect().also { captureRect ->
                        watermarkParams.displayRect.apply {
                            captureRect.set(
                                floor(left / scale).toInt(),
                                floor(top / scale).toInt(),
                                ceil(right / scale).toInt(),
                                ceil(bottom / scale).toInt()
                            )
                        }
                    }
                    val destBitmap = ability.removeWatermarkWithCaptureBitmap(
                        mediaItem.rotation,
                        captureRect,
                        srcBitmap,
                        captureBitmap
                    )
                    return ThumbnailDrawable(destBitmap)
                }
            }
        }
        return null
    }

    private suspend fun getContentDrawable(mediaItem: MediaItem) = suspendCoroutine<IHdrImageContent?> { continuation ->
        val mediaResKey = ResourceKeyFactory.createHdrResourceKey(mediaItem) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getContentDrawable] failed, with mediaResKey null." }
            continuation.resume(null)
            return@suspendCoroutine
        }
        val hdrDrawable = resourcingAbility?.let {
            it.requestDrawable(mediaResKey, specifiedOutputType = HdrImageDrawable::class.java)?.result
        }
        continuation.resume(hdrDrawable)
    }

    /**
     * 根据mediaItem确定当前的图片是否为Local HDR的资源
     * @param mediaItem MediaItem
     * @return Boolean
     */
    private fun isHdrImageResource(mediaItem: MediaItem): Boolean {
        return (mediaItem.getSupportFormat(FORMAT_LOCAL_HDR) == FORMAT_LOCAL_HDR)
                || (mediaItem.getSupportFormat(FORMAT_ULTRA_HDR) == FORMAT_ULTRA_HDR)
                || (mediaItem.getSupportFormat(FORMAT_OPLUS_ULTRA_HDR) == FORMAT_OPLUS_ULTRA_HDR)
    }

    /**
     * 根据条件决定是否需要将当前Layer的HDR效果关闭
     * @param mediaItem MediaItem
     */
    private fun disableHdrImageIfNeed(mediaItem: MediaItem) {
        if (viewModel.brighten.isSupportHdrImagePresent
            && viewModel.brighten.isSupportSpecifyHdrEdit(mediaItem).not()
            && (viewModel.brighten.isBrightnessToggleStateEnabled())
            && isHdrImageResource(mediaItem)
        ) {
            fragment.view?.getEdrSurfaceControl()?.let { surfaceControl ->
                hardwareAbility.let {
                    it?.screen?.notifyBrightenSessionEvent(surfaceControl, DISABLE_HDR_IMAGE)
                }
            }
        }
    }

    /**
     * 根据条件决定是否需要将当前Layer的dolby vision效果关闭， 关联场景：[disableHdrImageIfNeed]
     * @param mediaItem MediaItem
     */
    private fun disableDolbyIfNeed(mediaItem: MediaItem) {
        if (VideoTypeUtils.isDolbyVideo(mediaItem.codecType).not()) {
            GLog.w(TAG, LogFlag.DL, "disableDolbyIfNeed. codecType: ${mediaItem.codecType}")
            return
        }
        viewModel.brighten.setForceDisableDolbyEdrEffect(true)
    }


    private fun trackPrivacyWatermarkClick(mediaItem: MediaItem) {
        TrackScope.launch(Dispatchers.IO) {
            watermarkAbility?.newPrivacyWatermarkAbility()?.use {
                val imageLabel: String? =
                    SearchDBHelper.getLabelNameByPathFromDB(mediaItem.filePath)
                        ?.joinToString(activity.getString(BasebizR.string.base_join_symbol))
                viewModel.track.trackPrivacyWatermarkClick(
                    imageLabel,
                    it.isInCardCase(mediaItem)
                )
            }
        }
    }

    /**
     * 大图页面，点击合影按键的埋点：进入AI最佳合影功能前在大图停留的时间
     */
    private fun trackGroupPhotoHasClicked() {
        TrackScope.launch(Dispatchers.IO) {
            val startTime = if (viewModel.pageManagement.startShowTime.value == 0L) {
                startClickTime
            } else {
                viewModel.pageManagement.startShowTime.value ?: startClickTime
            }
            viewModel.track.trackGroupPhotoClick(GLog.getTime(startTime))
        }
    }

    /**
     * 关闭Ability
     */
    private fun closeAbilities() {
        taskManageAbility?.close()
        configAbility?.close()
        settingsAbility?.close()
        hardwareAbility?.close()
        watermarkAbility?.close()
        watermarkMasterAbility?.close()
        resourcingAbility?.close()
    }

    private enum class CheckResult {
        /**
         * 校验结果：通过
         */
        Pass,

        /**
         * 校验结果：不通过
         */
        Fail,

        /**
         * 校验过程中有错误、失败
         */
        Error,
    }

    companion object {
        private const val TAG = "PhotoMenuEditActionRule"

        /**
         * 编辑完成后，强制大图启动刷新提亮区域的时间
         */
        private const val FORCE_UPDATE_BRIGHTEN_REGION_DURATION = 1000L

        /**
         * 延时刷新菜单
         *
         * 抑制菜单显示的兜底操作
         */
        private const val DELAY_REFRESH_TOP_MENU = 500L

        /**
         * 导出实况支持不低于300ms的视频
         */
        private const val VIDEO_EXPORT_OLIVE_MIN_TIME = 300

        /**
         * 进编辑场景共享规格检查器
         */
        private val specHelper = VideoEditSpecHelper.create(VideoEditSpecHelper.EngineType.Meicam)
    }
}

/**
 * 实现一个编辑场景的TaskOwner
 */
private object PhotoEditTaskOwner : ILoadTaskOwner {

    /**
     * 是否进入编辑
     */
    var isEnterEditor = false

    override val id: String
        get() = TaskOwnerConst.PHOTO_EDIT

    override val lifeCycleEvent: Lifecycle.Event
        get() = if (isEnterEditor) {
            Lifecycle.Event.ON_RESUME
        } else {
            Lifecycle.Event.ON_PAUSE
        }

    override val linkages: Set<String>? = null

    override val affinities: Set<String>? = null

    override val container: String? = null

    override val taskConfigStrategy: TaskConfigStrategy by lazy {
        object : TaskConfigStrategy() {
            private val TASK_NAME = "PhotoEditLoad"
            override fun <T> getTaskName(condition: T?): String = TASK_NAME
        }
    }
}

/**
 * 实现一个视频编辑场景的TaskOwner
 */
private object VideoEditTaskOwner : ILoadTaskOwner {

    /**
     * 是否进入编辑
     */
    var isEnterEditor = false

    override val id: String
        get() = TaskOwnerConst.VIDEO_EDIT

    override val lifeCycleEvent: Lifecycle.Event
        get() = if (isEnterEditor) {
            Lifecycle.Event.ON_RESUME
        } else {
            Lifecycle.Event.ON_PAUSE
        }

    override val linkages: Set<String>? = null

    override val affinities: Set<String>? = null

    override val container: String? = null

    override val taskConfigStrategy: TaskConfigStrategy by lazy {
        object : TaskConfigStrategy() {
            private val TASK_NAME = "VideoEditLoad"
            override fun <T> getTaskName(condition: T?): String = TASK_NAME
        }
    }
}