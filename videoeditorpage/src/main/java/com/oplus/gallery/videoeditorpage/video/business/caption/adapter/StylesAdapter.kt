/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : StylesAdapter.kt
 ** Description : 文字样式面板适配器
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewHolder
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewData

/**
 * 字幕样式适配器
 */
class StylesAdapter(recyclerView: RecyclerView) : BaseAdapter<StyleViewData, StyleViewHolder>(
    recyclerView,
    /**
     * 创建VH的回调接口
     */
    { parent: ViewGroup, viewType: Int -> StyleViewHolder(parent) }
) {

    /**
     * 配置单选控件
     */
    override var multipleSelectable = false

    init {
        // 设置item数据差异计算回调
        diffCallback = StyleDiffCallback()
    }

    /**
     * 设置选中styleId的样式
     */
    fun setSelectedPositionForStyle(styleId: String) {
        // 重置选中位置
        selectedPosition = -1
        data.forEachIndexed { index, styleItem ->
            if (styleId == styleItem.resourceId) {
                // 根据字幕的样式id，选中对应的样式item
                selectedPosition = index
                return@forEachIndexed
            }
        }
        // 如果没有设置样式，则默认选中第一个
        if (selectedPosition == -1) selectedPosition = DEFAULT_SELECTED_STYLE_POSITION
    }

    /**
     * 文字样式数据变化差异回调器
     */
    private class StyleDiffCallback : DiffCallback<StyleViewData>() {

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return false
        }
    }

    companion object {
        /**
         * 样式的默认选中位置
         */
        const val DEFAULT_SELECTED_STYLE_POSITION = 0
    }
}