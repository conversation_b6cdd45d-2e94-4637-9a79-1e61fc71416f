/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BreakpadMaster.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import android.content.ContentValues
import android.content.Context
import android.os.Looper
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.JsonUtil.fromJson
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_1_SEC_IN_MS
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock


object BreakpadMaster {

    const val TAG = "BreakpadMaster"
    const val RESULT_SUCCESS = 0
    const val RESULT_NATIVE_FAIL = 1
    const val RESULT_APP_FAIL = 2
    const val RESULT_NOT_INIT = 3
    const val RESULT_NOT_GUARD = 4
    const val SCENE_CRASH_FOLDER_NAME = "crashFolder"
    const val SCENE_CRASH_FILE_EXTENSION = ".cb"
    const val SCENE_CRASH_FILE_EXTENSION_WITHOUT_SEPARATOR = "cb"
    const val TEST_BROADCAST_ACTION = "action.breakpad.gallery.test"
    const val TEST_BORADCAST_INTENT_PARAMS_KEY = "key"
    const val TEST_BROADCAST_SPLIT = "+"

    const val SIGILL = 4
    const val SIGABRT = 6
    const val SIGFPE = 8
    const val SIGBUS = 7
    const val SIGSEGV = 11

    const val BREAKPAD_MAIN_SWITCH = true

    private var sceneCrashes: HashMap<String, List<SceneCrashInfo>?> = HashMap()
    private val lock = ReentrantLock()
    private val dbLock = Any()
    private var isInitialized = AtomicBoolean(false)
    private var exceptionSignals: IntArray? = null
    private var hasCrashBefore: Boolean? = null
    private var sceneCrashMap: HashMap<String, Boolean> = hashMapOf()

    /**
     * 初始化，会读取crash后的文件记录，然后保存到数据库中，保存成功后删除crash文件记录,在所有方法之前执行
     */
    private fun initialize() {
        if (isInitialized.compareAndSet(false, true)) {
            lock.withLock {
                GLog.d(TAG, "initialize: with lock")
                val isCustomSignals = exceptionSignals != null
                if (isCustomSignals) {
                    BreakpadManager.registerBreakpadWithSignals(null, exceptionSignals)
                } else {
                    BreakpadManager.registerBreakpad(null)
                }
                BreakpadManager.setCrashFolder(getCrashFolderPath(ContextGetter.context))
                saveCrashFilesToDB()
                GLog.d(TAG, "initialize: finished")
            }
        }
    }

    fun initializeOnNonUiThread() {
        CoroutineScope(Job()).launch {
            initialize()
        }
    }

    /**
     * 旧版接口，设置缓存路径（目前扫描在用）
     * 不依赖初始化
     */
    fun setCachedFile(cachedFile: String) {
        BreakpadManager.setCachedFile(cachedFile)
    }

    /**
     * 旧版接口，设置是否重命名缓存文件（目前扫描在用）
     * 不依赖初始化
     */
    fun setRename(isRename: Boolean) {
        BreakpadManager.setRename(isRename)
    }

    /**
     * 旧版接口，记录文件路径用作crash后遗言（目前扫描在用）
     * 不依赖初始化
     */
    fun setFilePath(filePath: String) {
        BreakpadManager.setFilePath(filePath)
    }

    /**
     * 设置需要监听的信号集合，不设置的话默认监听android常见信号,需要在initialize之前调用
     */
    fun setExceptionSignals(signals: IntArray) {
        exceptionSignals = signals
    }

    /**
     * 场景注册方法（对应无需返回值的场景）
     * @scene：场景名
     * @carriedMsg：可以是被扫描或要解码的一个文件路径，也可以是一个完整的json，记录这个场景关联的一些信息
     * @guardRule: 由业务侧实现的监控规则，获取该场景的异常数据由业务决定是否忽略该异常，不传使用默认实现
     * @guardCallback: 结果回调
     * @block：要监控的代码端,需要保证原子调用，里面不再嵌套线程
     *
     * 如果监控的代码在native层发生异常,会写一条{"scene":"","carriedMsg":"","signalNo":"","signalInfo":"","timestamp":""}的数据到指定文件下，
     * 下次启动应用后，会把这条记录存到数据库中。
     */
    fun guardScene(
        scene: String,
        carriedMsg: String,
        guardRule: IGuardRule = DefaultGuardRule(),
        guardCallback: IGuardCallback,
        block: () -> Unit
    ) {
        if (!BREAKPAD_MAIN_SWITCH) {
            block()
            guardCallback.onResult(RESULT_NOT_GUARD)
        }
        if (isMainThread()) {
            CoroutineScope(Job()).launch {
                initialize()
                val crashes: List<SceneCrashInfo>? = getSceneCrashesWithLock(scene, guardRule)
                // 需要切换回主线程，保证线程信息是正确的
                withContext(Dispatchers.Main) {
                    innerGuard(guardRule, crashes, guardCallback, scene, carriedMsg, block)
                }
            }
        } else {
            initialize()
            val crashes: List<SceneCrashInfo>? = getSceneCrashesWithLock(scene, guardRule)
            innerGuard(guardRule, crashes, guardCallback, scene, carriedMsg, block)
        }
    }

    /**
     * 场景注册方法，直接返回执行结果（对应有返回值的场景）
     * @scene：场景名
     * @carriedMsg：可以是被扫描或要解码的一个文件路径，也可以是一个完整的json，记录这个场景关联的一些信息,注意这个信息不能为空！
     * @guardRule: 由业务侧实现的监控规则，获取该场景的异常数据由业务决定是否忽略该异常，不传使用默认实现
     * @block：要监控的代码端,需要保证原子调用，里面不再嵌套线程
     */
    fun <T> guardScene(
        scene: String,
        carriedMsg: String?,
        guardRule: IGuardRule = DefaultGuardRule(),
        block: () -> T?
    ): GuardResult<T> {
        var executeResult: T? = null
        if (!BREAKPAD_MAIN_SWITCH) {
            executeResult = block()
            return GuardResult(RESULT_NOT_GUARD, executeResult)
        }
        // 如果是主线程调用，则直接返回原调用结果，不作场景监控
        if (isMainThread()) {
            executeResult = block()
            return GuardResult(RESULT_NOT_GUARD, executeResult)
        }
        // 如果carriedMsg为空，则直接返回原调用结果，不作场景监控
        if (carriedMsg.isNullOrEmpty()) {
            executeResult = block()
            return GuardResult(RESULT_NOT_GUARD, executeResult)
        }
        initialize()
        val crashes: List<SceneCrashInfo>? = getSceneCrashesWithLock(scene, guardRule)
        if (!guardRule.checkIfNormal(crashes)) {
            return GuardResult(RESULT_NATIVE_FAIL, executeResult)
        }
        runCatching {
            BreakpadManager.guardScene(scene, carriedMsg)
            executeResult = block()
            BreakpadManager.unguardScene(scene, carriedMsg)
        }.onFailure {
            BreakpadManager.unguardScene(scene, carriedMsg)
            return GuardResult(RESULT_APP_FAIL, executeResult)
        }
        return GuardResult(RESULT_SUCCESS, executeResult)
    }

    private fun isMainThread(): Boolean {
        return Looper.getMainLooper().isCurrentThread
    }

    private fun innerGuard(
        guardRule: IGuardRule,
        crashes: List<SceneCrashInfo>?,
        guardCallback: IGuardCallback,
        scene: String,
        carriedMsg: String,
        block: () -> Unit
    ) {
        if (!guardRule.checkIfNormal(crashes)) {
            guardCallback.onResult(RESULT_NATIVE_FAIL)
            return
        }
        runCatching {
            BreakpadManager.guardScene(scene, carriedMsg)
            block()
            BreakpadManager.unguardScene(scene, carriedMsg)
        }.onFailure {
            BreakpadManager.unguardScene(scene, carriedMsg)
            guardCallback.onResult(RESULT_APP_FAIL)
        }.onSuccess {
            guardCallback.onResult(RESULT_SUCCESS)
        }
    }

    private fun getSceneCrashesWithLock(scene: String, guardRule: IGuardRule): List<SceneCrashInfo>? {
        if (hasCrashBefore == null) {
            hasCrashBefore = hasCrashBefore()
            GLog.d(TAG, "getSceneCrashesWithLock: scene = $scene , hasCrashBefore = $hasCrashBefore")
        }
        val isLastCrash = if (hasCrashBefore == true) {
            isLastCrash(scene)
        } else {
            false
        }
        // 如果最近一次发生了native的crash，那需要等待初始化完成后再去读取数据库，否则可以直接读取数据库无需等待
        return if (isLastCrash) {
            lock.withLock {
                GLog.d(TAG, "getSceneCrashesWithLock: with lock , scene = $scene")
                getSceneCrashes(scene, guardRule.cacheDuration())
            }
        } else {
            getSceneCrashes(scene, guardRule.cacheDuration())
        }
    }

    /**
     * 场景注册方法，可自定义监控的线程和场景，但是尽量调用上面那个方法
     *
     * @nativeTid：要守护的线程tid，注意这里需要的是系统的tid，不是java层获取的tid
     * @scene：场景名
     * @carriedMsg：可以是被扫描或要解码的一个文件路径，也可以是一个完整的json，记录这个场景关联的一些信息
     *
     * 区别于上面的接口，这里可以指定线程进行守护，适用于一些无法控制是原子调用的场景，比如线程套线程且被嵌套的线程够不着这种场景。
     */
    fun guardScene(nativeTid: Int, scene: String, carriedMsg: String) {
        runAfterInitialized {
            BreakpadManager.guardSceneWithTid(nativeTid, scene, carriedMsg)
        }
    }

    private fun runAfterInitialized(block: () -> Unit) {
        if (isMainThread()) {
            if (!isInitialized.get()) {
                CoroutineScope(Job()).launch {
                    initialize()
                    // 需要切换回主线程，保证线程信息是正确的
                    withContext(Dispatchers.Main) {
                        block()
                    }
                }
            } else {
                block()
            }
        } else {
            initialize()
            block()
        }
    }

    /**
     * 场景注册取消方法
     *
     * @nativeTid：要取消注册的线程tid，不传的话，默认删除该场景注册的所有监控
     * @scene：场景名
     * @carriedMsg：可以是被扫描或要解码的一个文件路径，也可以是一个完整的json，记录这个场景关联的一些信息
     *
     * 需要取消对某个场景的守护的时候调用
     */
    fun unguardScene(nativeTid: Int = -1, scene: String, carriedMsg: String) {
        runAfterInitialized {
            if (nativeTid > 0) {
                BreakpadManager.unguardSceneWithTid(nativeTid, scene, carriedMsg)
            } else {
                BreakpadManager.unguardScene(scene, carriedMsg)
            }
        }
    }

    /**
     * 判断指定监控规则下场景是否存在异常
     *
     * @scene: 场景名
     * @guardRule: 由业务侧实现的监控规则，获取该场景的异常数据由业务决定是否忽略该异常，不传使用默认实现
     */
    fun checkIfSceneNormal(scene: String, guardRule: IGuardRule = DefaultGuardRule(), guardCallback: IGuardCallback) {
        CoroutineScope(Job()).launch {
            val crashes: List<SceneCrashInfo>? = getSceneCrashesWithLock(scene, guardRule)
            val isNormal = guardRule.checkIfNormal(crashes)
            guardCallback.onResult(if (isNormal) RESULT_SUCCESS else RESULT_NATIVE_FAIL)
        }
    }

    /**
     * 获取当前线程的tid
     */
    fun getNativeTid(): Int {
        return BreakpadManager.getNativeTid()
    }

    /**
     * 判断返回结果是否失败，只有这2种情况，对应的返回值为null，其它情况，返回原调用的结果
     */
    fun isRunFailed(resultCode: Int): Boolean {
        return resultCode == RESULT_APP_FAIL || resultCode == RESULT_NATIVE_FAIL
    }

    fun getCrashFolderPath(context: Context): String {
        val folderPath = context.filesDir.absolutePath + File.separator + SCENE_CRASH_FOLDER_NAME
        val folderFile = File(folderPath)
        if (!folderFile.exists()) {
            folderFile.mkdirs()
        }
        return folderPath
    }

    /**
     * 删掉指定场景和指定时间之前的异常记录
     *
     * @scene： 场景名
     * @startTime: 删除这个时间之前的记录
     */
    fun deleteSceneCrashes(scene: String, startTime: String) {
        if (isMainThread()) {
            CoroutineScope(Job()).launch {
                deleteAtDB(scene, startTime)
                sceneCrashes.remove(scene)
            }
        } else {
            deleteAtDB(scene, startTime)
            sceneCrashes.remove(scene)
        }
    }

    private fun deleteAtDB(scene: String, startTime: String) {
        DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCENE_CRASH)
            .setWhere(
                GalleryStore.SceneCrashColumns.SCENE + " = ? and " +
                        GalleryStore.SceneCrashColumns.CRASH_TIME + " < ? "
            )
            .setWhereArgs(arrayOf(scene, startTime))
            .build().exec()
    }

    /**
     * 判断该场景是否刚发生过native crash
     *
     * @scene: 场景名
     */
    private fun isLastCrash(scene: String): Boolean {
        return if (sceneCrashMap[scene] == null) {
            val path = getCrashFolderPath(ContextGetter.context) + File.separator + scene + SCENE_CRASH_FILE_EXTENSION
            val isLastCrash = File(path).exists()
            sceneCrashMap[scene] = isLastCrash
            isLastCrash
        } else {
            sceneCrashMap[scene] == true
        }
    }

    private fun hasCrashBefore(): Boolean? {
        val folder = getCrashFolderPath(ContextGetter.context)
        return !File(folder).listFiles().isNullOrEmpty()
    }

    private fun getSceneCrashes(scene: String, cacheDuration: Long): List<SceneCrashInfo>? {
        if (sceneCrashes.containsKey(scene)) {
            return sceneCrashes[scene]
        }

        var crashes: List<SceneCrashInfo>?

        synchronized(dbLock) {
            if (sceneCrashes.containsKey(scene)) {
                return sceneCrashes[scene]
            }
            val currentTime = System.currentTimeMillis()
            val startTime = ((currentTime - cacheDuration) / TIME_1_SEC_IN_MS).toString()
            crashes = queryFromDB(scene, startTime)
            // 删除此次查询所选时间之前的数据
            deleteAtDB(scene, startTime)
            sceneCrashes[scene] = crashes
        }

        return crashes
    }

    private fun queryFromDB(scene: String, startTime: String): List<SceneCrashInfo>? {
        return QueryReq.Builder<List<SceneCrashInfo>?>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCENE_CRASH)
            .setWhere(
                GalleryStore.SceneCrashColumns.SCENE + " = ? and " +
                        GalleryStore.SceneCrashColumns.CRASH_TIME + " >= ? "
            )
            .setWhereArgs(arrayOf(scene, startTime))
            .setConvert(SceneCrashConvert())
            .build().exec()
    }

    private fun addToDB(info: SceneCrashInfo?) {
        InsertReq.Builder().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCENE_CRASH)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert {
                val contentValues = ContentValues()
                contentValues.put(GalleryStore.SceneCrashColumns.SCENE, info?.scene)
                contentValues.put(GalleryStore.SceneCrashColumns.CARRIED_MSG, info?.carriedMsg)
                contentValues.put(GalleryStore.SceneCrashColumns.CRASH_TIME, info?.crashTime)
                contentValues.put(GalleryStore.SceneCrashColumns.BACKTRACE, info?.backtrace)
                contentValues.put(GalleryStore.SceneCrashColumns.STRERROR, info?.strerror)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_SIGNO, info?.si_signo)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_ERRNO, info?.si_errno)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_CODE, info?.si_code)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_TID, info?.si_tid)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_OVERRUN, info?.si_overrun)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_PID, info?.si_pid)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_UID, info?.si_uid)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_STATUS, info?.si_status)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_BAND, info?.si_band)
                contentValues.put(GalleryStore.SceneCrashColumns.SI_FD, info?.si_fd)
                contentValues
            }.build().exec()
    }

    fun saveCrashFilesToDB() {
        val folderPath = getCrashFolderPath(ContextGetter.context)
        val fileTree: FileTreeWalk = File(folderPath).walk()
        val removables = arrayListOf<File>()
        fileTree.maxDepth(1)
            .filter { it.isFile }
            .filter { it.extension in listOf(SCENE_CRASH_FILE_EXTENSION_WITHOUT_SEPARATOR) }
            .forEach {
                val jsonStr = File(it.path).readText()
                val crashInfo = fromJson(jsonStr, SceneCrashInfo::class.java)
                addToDB(crashInfo)
                removables.add(File(it.path))
            }
        for (removable in removables) {
            if (removable.exists()) {
                removable.delete()
            }
        }
        removables.count {
            it.exists().apply { if (this) it.delete() }
        }.takeIf { it > 0 }?.let {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCENE_CRASH, IDao.DaoType.GALLERY)
        }
    }

    /**
     * only for test
     */
    fun testCrash() {
        BreakpadManager.testCrash()
    }

    /**
     * only for test
     */
    fun fakeCrash(scene: String, carriedMsg: String) {
        BreakpadManager.fakeCrash(scene, carriedMsg)
    }
}

