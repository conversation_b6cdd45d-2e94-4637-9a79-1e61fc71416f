/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - AspectRatio.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard;

public class AspectRatio {
    private int mAspectRatio;
    private String mAspectRatioXml;
    private float mAspectRatioSize;

    public AspectRatio(int aspectRatio, String aspectRatioXml, float aspectRatioSize) {
        this.mAspectRatio = aspectRatio;
        this.mAspectRatioXml = aspectRatioXml;
        this.mAspectRatioSize = aspectRatioSize;
    }

    public int getAspectRatio() {
        return mAspectRatio;
    }

    public void setAspectRatio(int aspectRatio) {
        this.mAspectRatio = aspectRatio;
    }

    public String getAspectRatioXml() {
        return mAspectRatioXml;
    }

    public void setAspectRatioXml(String aspectRatioXml) {
        this.mAspectRatioXml = aspectRatioXml;
    }

    public float getAspectRatioSize() {
        return mAspectRatioSize;
    }

    public void setAspectRatioSize(float aspectRatioSize) {
        this.mAspectRatioSize = aspectRatioSize;
    }
}
