/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  RatioOption
 ** Description: 80377011 created
 ** Version: 1.0
 ** Date : 2025/4/9
 ** Author: 80377011
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80377011      2025/4/9      1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.widget.ratioselector

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes

/**
 * 用于描述一个“比例”选项的数据
 *
 * @param ratioId 唯一标识
 * @param ratioValue 比例值，W/H，为0代表自由
 * @param ratioNameRes 文案资源（多语言支持）
 * @param iconRes 图标资源，例如一个 drawable 资源 ID
 */
data class RatioOption(
    val ratioId: Int,
    val ratioValue: Float,
    @StringRes val ratioNameRes: Int,
    @DrawableRes val iconRes: Int,
)
