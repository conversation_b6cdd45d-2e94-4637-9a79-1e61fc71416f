/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : CopyOriginalFileToDataDirTask.kt
 ** Description : 备份原图到私有目录的任务类
 ** Version     : 1.0
 ** Date        : 2024/5/28
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D          2024/5/28     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.output.task.parametric

import android.annotation.TargetApi
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.net.Uri
import android.os.Build
import android.os.ParcelFileDescriptor
import com.oplus.gallery.addon.graphics.OplusImageHdrWrapper
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toGainmap
import com.oplus.gallery.business_lib.helper.BitmapSaveHelper
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_EDIT_RENDERING
import com.oplus.gallery.business_lib.photoeditor.ParametricDataEntity
import com.oplus.gallery.business_lib.photoeditor.ParametricProjectDataHelper
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_LINEAR_MASK
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_META_DATA
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.exif.raw.ExifUtils
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.foundation.fileaccess.data.CopyFileRequest
import com.oplus.gallery.foundation.fileaccess.extension.FILE_EXTENDED_CONTAINER_TAG
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.graphic.BitmapUtils.DEFAULT_JPEG_QUALITY
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.parameterization.ParametricConst.DIR_PROJECT_ORIGINAL_FILE
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_COLOR_SPACE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_CONTENT_EDIT_BITMAP
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_CONTEXT
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_ORIGIN_CONTENT_VALUES
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_QUALITY
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_TARGET_PARAMETRIC_DATA
import com.oplus.gallery.photoeditor.editingvvm.output.task.ISaveTask
import com.oplus.gallery.photoeditor.editingvvm.output.task.SaveTaskException
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageType
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import java.io.FileNotFoundException

/**
 * 拷贝原图到私有目录的任务类
 */
internal class CopyOriginalFileToDataDirTask(override val saveData: SaveData) : ISaveTask {

    override val name: String = TAG

    private val newParametricDataEntity = ParametricDataEntity()

    override fun run() {
        val context = saveData.find<Context>(KEY_CONTEXT, TAG) ?: return
        val originContentValues = saveData.find<ContentValues>(KEY_ORIGIN_CONTENT_VALUES, TAG) ?: return
        val sourcePath = originContentValues.getAsString(LocalColumns.DATA) ?: return
        val sourceUri: Uri? = saveData.find<DataSource>(KEY_DATA_SOURCE, TAG)?.imageUri
        val colorSpace = saveData.find<ColorSpace>(KEY_COLOR_SPACE, TAG)
        val quality = saveData.find<Int>(KEY_QUALITY, TAG) ?: DEFAULT_JPEG_QUALITY
        val lastContentEditBitmap = saveData.find<Bitmap>(KEY_CONTENT_EDIT_BITMAP, TAG)
        val tagFlag = originContentValues.getAsLong(LocalColumns.TAGFLAGS)
        val isOriginalFileParametricValid = (tagFlag and FLAG_EDIT_RENDERING == FLAG_EDIT_RENDERING)
        val oldParametricDataEntity = if (isOriginalFileParametricValid) {
            sourceUri?.let { ParametricProjectDataHelper.queryProjectEntityByUri(it) }
        } else {
            null
        }
        copyOriginalFileToProjectDir(context, oldParametricDataEntity, sourcePath, sourceUri)
        saveContentEditFileToProjectDir(context, lastContentEditBitmap, originContentValues, colorSpace, quality)
        // 主动回收一下
        BitmapUtils.recycleSilently(lastContentEditBitmap)
        saveData.data[KEY_CONTENT_EDIT_BITMAP] = null
        saveData.data[KEY_TARGET_PARAMETRIC_DATA] = newParametricDataEntity.also {
            GLog.d(TAG, LogFlag.DF) { "[run] newParametricDataEntity=$newParametricDataEntity" }
        }
    }

    /**
     * 拷贝原图到项目化原图保存目录下
     */
    private fun copyOriginalFileToProjectDir(
        context: Context,
        oldParametricDataEntity: ParametricDataEntity?,
        filePath: String,
        fileUri: Uri?
    ) {
        val isSecondEdit = (oldParametricDataEntity != null) && oldParametricDataEntity.isValid()

        // 二次编辑，不需要拷贝
        if (isSecondEdit) {
            newParametricDataEntity.originFileSubPath = oldParametricDataEntity?.originFileSubPath
                .also { GLog.d(TAG) { "copyOriginalFileToProjectDir, already copy, originFileSubPath=$it" } }
            return
        }

        // 首次编辑或者失效图编辑，进行拷贝
        copyFileToProjectDir(context, filePath, fileUri)
            ?.let { newParametricDataEntity.originFileSubPath = FilePathUtils.getDataFileSubPath(it) }
    }

    /**
     * 拷贝文件到项目化原图存储目录
     */
    private fun copyFileToProjectDir(context: Context, srcPath: String, srcUri: Uri?): String? {
        if (srcPath.isEmpty()) {
            GLog.e(TAG) { "copyFileToProjectDir, srcPath=$srcPath is null, return" }
            return null
        }
        if (StorageLimitHelper.isFileSpaceAvailable(srcPath).not()) {
            GLog.e(TAG, "copyFileToProjectDir, space not available, return")
            throw SaveTaskException(ResultCode.SAVE_FAILED_NO_SPACE, "space not available!")
        }
        val copyFileRequest = CopyFileRequest.Builder()
            .setSrcFile(File(srcPath))
            .setTargetFile(getProjectOriginalFile(context, srcPath))
            .setUri(srcUri)
            .setImage(true)
            .setCoverFile(true)
            .builder()
        val response = FileAccessManager.getInstance().copyFile(context, copyFileRequest)
        GLog.d(TAG) { "copyFileToProjectDir, response=$response" }
        if ((response != null) && (response.isSuccess)) {
            return response.filePath
        }
        return null
    }

    /**
     * 存储内容编辑图片到项目化原图保存目录下
     */
    private fun saveContentEditFileToProjectDir(
        context: Context,
        contentBitmap: Bitmap?,
        originContentValues: ContentValues,
        colorSpace: ColorSpace?,
        quality: Int
    ) {
        // 判断是否有进行内容编辑，如果编辑时有进行内容编辑，此处需要获取到 bitmap，如果没有获取到，是bug；如果编辑时没有进行内容编辑，可以获取不到bitmap，直接返回
        contentBitmap.getOrLog(TAG, "[saveContentEditFileToProjectDir] contentBitmap")
        contentBitmap ?: return

        GLog.d(TAG, LogFlag.DL) { "[saveContentEditFileToProjectDir] contentBitmap = ${contentBitmap.width} x ${contentBitmap.height}" }

        if (StorageLimitHelper.isBitmapSpaceAvailable(contentBitmap).not()) {
            GLog.w(TAG, "[saveContentEditFileToProjectDir] space not available!")
            throw SaveTaskException(ResultCode.SAVE_FAILED_NO_SPACE, "space not available!")
        }

        /* 以下流程基于本次编辑有内容编辑图片的前提执行：
            1. 首次编辑，不管是是覆盖原图还是存为新图，需要新增一张内容编辑图片
            2. 二次编辑
            2.1 覆盖原图，需要新增一张内容编辑图片，然后删除上次编辑使用的内容编辑图片，为了兼容中断场景，最后修改完数据库表之后才删除
            2.2 存为新图，需要新增一张内容编辑图片
           综上，都需要新增一张内容编辑图片 */
        val contentEditFile = getEditOriginalFile(context)
        val mimeType = originContentValues.getAsString(LocalColumns.MIME_TYPE)

        val hdrContent = saveData.find<IHdrImageContent>(SaveData.KEY_HDR_IMAGE_CONTENT_FOR_CONTENT_EDIT, TAG)

        // 写 U HDR
        processUHDR(contentBitmap, hdrContent)

        // 保存文件
        BitmapSaveHelper.saveBitmapToFile(contentBitmap, mimeType, colorSpace, quality, contentEditFile)
        if (contentEditFile.exists()) {
            newParametricDataEntity.contentEditFileSubPath = FilePathUtils.getDataFileSubPath(contentEditFile.absolutePath)
        }

        val uri = GalleryFileProvider.getUriFromFile(context, contentEditFile.absolutePath)
            .getOrLog(TAG, "[processLHDR] content uri is null") ?: return

        // 写 L HDR
        processLHDR(context, uri, hdrContent)

        // 写 tagFlag
        processTagFlags(context, uri, hdrContent)
    }

    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun processUHDR(bitmap: Bitmap, hdrContent: IHdrImageContent?) {
        hdrContent ?: return
        if (ApiLevelUtil.isAtLeastAndroidU().not()) {
            return
        }
        if (hdrContent.metadata.getHdrType() != HdrImageType.UHDR) {
            return
        }
        val ultraHdrInfo = (hdrContent.metadata.metadata as? UltraHdrInfo) ?: return
        bitmap.gainmap = ultraHdrInfo.toGainmap(hdrContent.grayImage)
        GLog.d(TAG, LogFlag.DF) { "[processUHDR] write gainmap ${hdrContent.grayImage.width} x ${hdrContent.grayImage.height}" }
    }

    private fun processLHDR(context: Context, uri: Uri, hdrContent: IHdrImageContent?) {
        hdrContent ?: return
        if (hdrContent.metadata.getHdrType() != HdrImageType.LHDR) {
            return
        }
        OplusImageHdrWrapper.alpha8BitmapToByteBuffer(hdrContent.grayImage)?.let { maskImage ->
            FileExtendedContainer().use { container ->
                container.setDataSource(context, uri, OpenFileMode.MODE_READ_AND_WRITE)
                val editor = container.editor().getOrLog(TAG, "[processLHDR] container editor is null") ?: return@use
                editor.addExtensionData(EXTEND_KEY_LOCAL_HDR_LINEAR_MASK, maskImage)
                (hdrContent.metadata.metadata as? ByteArray)?.also {
                    editor.addExtensionData(EXTEND_KEY_LOCAL_HDR_META_DATA, it)
                } ?: kotlin.run {
                    GLog.w(TAG) { "[processLHDR] skip commit editor , metadata is not equal ByteArray" }
                    return@use
                }
                editor.setTag(FILE_EXTENDED_CONTAINER_TAG)
                editor.commit()
            }
        }
    }

    private fun processTagFlags(context: Context, uri: Uri, hdrContent: IHdrImageContent?) {
        hdrContent ?: return
        // 获取 tagFlag
        var tagFlags: Long = Constants.CameraMode.FLAG_UNKNOWN
        if (hdrContent.metadata.getHdrType() == HdrImageType.LHDR) {
            tagFlags = tagFlags or Constants.CameraMode.FLAG_LOCAL_HDR
        }
        if (hdrContent.metadata.getHdrType() == HdrImageType.UHDR) {
            tagFlags = tagFlags or Constants.CameraMode.FLAG_ULTRA_HDR
        }

        // 写 tagFlag
        getFileDescriptorSafely(uri, OpenFileMode.MODE_READ_AND_WRITE.mode, context).use { fd ->
            val exifEntry = ExifUtils.ExifEntry()
            GLog.d(TAG, LogFlag.DL) { "[processTagFlags] tagFlag = ${Constants.CameraMode.getTagFlagDesc(tagFlags)}" }
            exifEntry.userComment = OplusExifTag.EXIF_TAG_PREFIX + tagFlags
            ExifUtils.writeExif(fd?.fileDescriptor, exifEntry)
        }
    }

    private fun getFileDescriptorSafely(uri: Uri, mode: String, context: Context): ParcelFileDescriptor? {
        try {
            return context.contentResolver.openFileDescriptor(uri, mode)
        } catch (e: FileNotFoundException) {
            GLog.w(TAG, "getFileDescriptorSafely, open fd fail:" + e.message)
        }
        return null
    }

    /**
     * 获取私有目录原图的路径
     * /data/user/0/com.coloros.gallery3d/files/edit/parametric/files/origin_1714962610182.jpg
     * @param data 效果图的路径，原图将会使用相同的后缀
     * @return 私有目录原图的路径
     */
    private fun getProjectOriginalFile(context: Context, data: String): File {
        val fileName = ORIGINAL_FILE_PREFIX + System.currentTimeMillis()
        val suffix = data.substring(data.lastIndexOf(File.SEPARATOR_EXTENSION), data.length)
        return File(getProjectOriginalFileDir(context), fileName + suffix)
    }

    /**
     * 获取私有目录内容编辑图片的路径
     * /data/user/0/com.coloros.gallery3d/files/edit/parametric/files/content_edit_1714962610182.jpg
     */
    private fun getEditOriginalFile(context: Context): File {
        val fileName = CONTENT_EDIT_PREFIX + System.currentTimeMillis() + File.SEPARATOR_EXTENSION + JPG
        return File(getProjectOriginalFileDir(context), fileName)
    }

    private fun getProjectOriginalFileDir(context: Context): File {
        return File(context.filesDir.absolutePath, DIR_PROJECT_ORIGINAL_FILE)
    }

    companion object {
        private const val TAG = "CopyOriginalFileToDataDirTask"
        private const val ORIGINAL_FILE_PREFIX = "origin_"
        private const val CONTENT_EDIT_PREFIX = "content_edit_"
        private const val JPG = "jpg"
    }
}