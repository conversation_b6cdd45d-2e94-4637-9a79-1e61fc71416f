/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MemoriesVIewGalleryInnerActivity.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/20 10:12
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>             2024/2/20      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.memories

import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.viewgallery.ViewGalleryActivity
import com.oplus.gallery.router_lib.annotations.RouterNormal

/**
 * 参考ViewGalleryInner
 * 包装了PhotoFragment的Activity，包含多种业务
 * 1. 启动大图页面
 * 2. 跳转到图集列表等
 * 与ViewGallery的区别在于，这个的theme配置的背景为透明
 * 本意是给内部的某些业务使用，以activity的形式启动大图，比如大图预览功能
 *
 * 主要是为了解决MemoriesActivity是在memories进程时数据同步问题，其他情况下禁止使用！！！
 */
@RouterNormal(RouterConstants.RouterName.MEMORIES_INNER_GALLERY_ACTIVITY)
class MemoriesViewGalleryInnerActivity : ViewGalleryActivity()