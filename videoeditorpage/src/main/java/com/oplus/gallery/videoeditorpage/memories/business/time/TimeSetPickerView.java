/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - TimeSetPickerView.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/29    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.business.time;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.ui.LocalNumberPicker;

import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.MINUTES_60;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.SECONDS_1000;

public class TimeSetPickerView extends ConstraintLayout {
    private static final String TAG = "TimeSetPickerView";

    private static final String TIME_FORMAT = "%02d";
    private static final int TOTAL_TIME_MAX = 90; // 90 seconds
    private static final int TOTAL_TIME_MIN = 6; // 6 seconds
    private static final int MINUTE_VALUE_MAX = 1;
    private static final int DEFAULT_SCROLL_LINE = 3;
    private static final int DELAY_TIME = 500;
    private static final int MSG_TIME_CHANGE = 100;
    private static final int TOTAL_MINUTE_SIZE = 6;
    private static final int TOTAL_SECOND_SIZE = 60;

    private final String[] mDisplayedMinutes = new String[TOTAL_MINUTE_SIZE];
    private final String[] mDisplayedSeconds = new String[TOTAL_SECOND_SIZE];
    private OnDataChangeListener mDataChangeListener;
    private LocalNumberPicker mMinutePicker;
    private LocalNumberPicker mSecondPicker;
    private int mMinTotalTime = 0;
    private int mMaxTotalTime = 0;
    private int mCurrentSecond = 0;
    private int mCurrentMinute = 0;
    private int mTimeTextSize;
    private int mMinPickerWidth;

    private Handler mHandler = new Handler(Looper.myLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_TIME_CHANGE:
                    updateTime();
                    break;
                default:
                    break;
            }
        }
    };

    public TimeSetPickerView(Context context) {
        this(context, null);
    }

    public TimeSetPickerView(Context context, AttributeSet attr) {
        this(context, attr, 0);
    }

    public TimeSetPickerView(Context context, AttributeSet attr, int defStyleAttr) {
        super(context, attr, defStyleAttr);

        for (int i = 0; i < TOTAL_MINUTE_SIZE; i++) {
            mDisplayedMinutes[i] = String.format(TIME_FORMAT, i);
        }
        for (int i = 0; i < TOTAL_SECOND_SIZE; i++) {
            mDisplayedSeconds[i] = String.format(TIME_FORMAT, i);
        }
        mTimeTextSize = context.getResources().getDimensionPixelSize(R.dimen.memories_editor_timeset_menu_time_text_size);
        mMinPickerWidth = context.getResources().getDimensionPixelSize(R.dimen.memories_editor_timeset_menu_number_width);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        mMinutePicker = findViewById(R.id.num_minute);
        mSecondPicker = findViewById(R.id.num_second);
        //minute
        mMinutePicker.setDisplayedValues(mDisplayedMinutes);
        mMinutePicker.setMaxValue(MINUTE_VALUE_MAX);
        mMinutePicker.setMinValue(0);
        mMinutePicker.setPickerRowNumber(DEFAULT_SCROLL_LINE);
        String minute = getContext().getString(R.string.videoeditor_timeset_minute);
        mMinutePicker.setUnitText(minute);
        mMinutePicker.setOnTouchEndListener(value -> {
            GLog.d(TAG, "onTouchEnd mCurrentMinute = " + mCurrentMinute + ", value = " + value);
            sendTimeChangeMessage();
            mCurrentMinute = value;
        });
        mMinutePicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
            if (newVal != oldVal) {
                sendTimeChangeMessage();
            }
            mCurrentMinute = newVal;
        });

        //second
        mSecondPicker.setDisplayedValues(mDisplayedSeconds);
        mSecondPicker.setMaxValue(MINUTES_60 - 1); //59 seconds
        mSecondPicker.setMinValue(0);
        mSecondPicker.setPickerRowNumber(DEFAULT_SCROLL_LINE);
        String second = getContext().getString(R.string.videoeditor_timeset_second);
        mSecondPicker.setUnitText(second);
        mSecondPicker.setOnTouchEndListener(value -> {
            GLog.d(TAG, "onTouchEnd mCurrentSecond = " + mCurrentSecond + ", value = " + value);
            sendTimeChangeMessage();
            mCurrentSecond = value;
        });
        mSecondPicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
            if (newVal != oldVal) {
                sendTimeChangeMessage();
            }
            mCurrentSecond = newVal;
        });
        mMinutePicker.setWrapSelectorWheel(true);
        mSecondPicker.setWrapSelectorWheel(true);
        GLog.d(TAG, "onFinishInflate minute = " + mCurrentMinute + ", second = " + mCurrentSecond);
    }

    public void setTotalTime(int maxTime, int minTime, int curTime) {
        // min
        mMinTotalTime = Math.max(minTime / SECONDS_1000, TOTAL_TIME_MIN);
        mMinTotalTime = Math.min(TOTAL_TIME_MAX, mMinTotalTime);
        // max
        mMaxTotalTime = Math.min(maxTime / SECONDS_1000, TOTAL_TIME_MAX);
        mMaxTotalTime = Math.max(mMinTotalTime, mMaxTotalTime);
        GLog.d(TAG, "setTotalTime maxTime = " + (maxTime / SECONDS_1000)
                + ", minTime = " + (minTime / SECONDS_1000)
                + ", mMinTotalTime = " + mMinTotalTime
                + ", mMaxTotalTime = " + mMaxTotalTime
                + ", curTime = " + curTime);
        mMinutePicker.setMaxValue(mMaxTotalTime / MINUTES_60);
        if (mMaxTotalTime < MINUTES_60) {
            mSecondPicker.setMaxValue(mMaxTotalTime);
        }
        updateValue(curTime / SECONDS_1000);
    }

    private void updateValue(int time) {
        if (mMinutePicker != null) {
            mCurrentMinute = time / MINUTES_60;
            mMinutePicker.setValue(mCurrentMinute);
        }
        if (mSecondPicker != null) {
            mCurrentSecond = time % MINUTES_60;
            mSecondPicker.setValue(mCurrentSecond);
        }
        GLog.d(TAG, "updateValue minute = " + mCurrentMinute + ", second = " + mCurrentSecond);
    }

    private void updateTime() {
        if ((mMinutePicker == null) || !mMinutePicker.isTouchEnd()) {
            GLog.d(TAG, "updateTime mMinutePicker.isTouchEnd = "
                    + ((mMinutePicker != null) ? mMinutePicker.isTouchEnd() : null));
            return;
        }
        if ((mSecondPicker == null) || !mSecondPicker.isTouchEnd()) {
            GLog.d(TAG, "updateTime mSecondPicker.isTouchEnd = "
                    + ((mSecondPicker != null) ? mSecondPicker.isTouchEnd() : null));
            return;
        }
        GLog.d(TAG, "updateTime minute = " + mCurrentMinute
                + ", second = " + mCurrentSecond + ", mMaxTotalTime = " + mMaxTotalTime);
        int currentTotalTime = mCurrentMinute * MINUTES_60 + mCurrentSecond;
        if (currentTotalTime > mMaxTotalTime) {
            updateValue(mMaxTotalTime);
        } else if (currentTotalTime < mMinTotalTime) {
            updateValue(mMinTotalTime);
        }
        if (mDataChangeListener != null) {
            mDataChangeListener.onTimeDataChange((mCurrentMinute * MINUTES_60 + mCurrentSecond) * SECONDS_1000);
            String durationStr = getResources().getString(R.string.videoeditor_talkback_timeset_duration,
                    mCurrentMinute, mCurrentSecond);
            setContentDescription(durationStr);
        }
    }

    private void sendTimeChangeMessage() {
        mHandler.removeMessages(MSG_TIME_CHANGE);
        mHandler.sendEmptyMessageDelayed(MSG_TIME_CHANGE, DELAY_TIME);
    }

    public void setOnDataChangeListener(OnDataChangeListener listener) {
        mDataChangeListener = listener;
    }

    public interface OnDataChangeListener {
        void onTimeDataChange(long milliTime);
    }
}
