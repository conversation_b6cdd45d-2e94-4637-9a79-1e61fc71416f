/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SquareProgressBar
 ** Description: 视频保存预览图进度显示容器
 ** Version: 1.0
 ** Date : 2025/04/21
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/04/21    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.common.ui.SuitableSizeG2TextView

/**
 * 视频保存预览图进度显示容器
 */
class SquareProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 预览图片
     */
    var imageBitmap: Bitmap? = null
        set(value) {
            if ((value == null) || value.isRecycled) return
            imageView?.setImageBitmap(value)
            field = value
        }

    /**
     * 进度值
     */
    var progress: Float = 0f
        set(value) {
            if (value < 0) return
            progressBar?.drawProgress = value
            field = value
        }

    /**
     * 是否显示进度值文本
     */
    var showProgressValueText: Boolean = false
        set(value) {
            progressValueTextView?.visibility = if (value) View.VISIBLE else View.GONE
            field = value
        }

    /**
     * 进度值文本内容
     */
    var progressValueText: String = TextUtil.EMPTY_STRING
        set(value) {
            progressValueTextView?.text = value
            field = value
        }

    /**
     * 预览图组件
     */
    private val imageView: ImageView?

    /**
     * 图片周围进度条组件
     */
    private val progressBar: SquareProgressView?

    /**
     * 进度值文本显示组件
     */
    private val progressValueTextView: SuitableSizeG2TextView?

    /**
     * 图片最大高度
     */
    private var bitmapMaxHeight: Int = 0

    init {
        val array = context.obtainStyledAttributes(attrs, R.styleable.SquareProgressBar, defStyleAttr, defStyleRes)
        try {
            bitmapMaxHeight = array.getDimensionPixelOffset(
                R.styleable.SquareProgressBar_bitmapMaxHeight,
                resources.getDimensionPixelOffset(R.dimen.videoeditor_save_video_image_max_height)
            )
        } finally {
            array.recycle()
        }
        val rootView = LayoutInflater.from(context).inflate(R.layout.editor_save_suqare_progress_bar_layout, this, true)
        imageView = rootView.findViewById(R.id.wrap_progress_image)
        imageView.maxHeight = bitmapMaxHeight
        progressBar = rootView.findViewById(R.id.wrap_progress_bar)
        progressValueTextView = rootView.findViewById(R.id.wrap_progress_value_text)
    }
}