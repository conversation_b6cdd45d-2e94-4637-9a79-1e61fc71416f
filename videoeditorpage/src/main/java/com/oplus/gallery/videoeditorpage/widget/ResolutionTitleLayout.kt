/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ResolutionTitleLayout
 ** Description: 选择分辨率标题栏组件
 ** Version: 1.0
 ** Date : 2025/05/08
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/05/08    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import androidx.constraintlayout.widget.ConstraintLayout
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.common.ui.SuitableSizeG2TextView

/**
 * 选择分辨率标题栏组件
 */
class ResolutionTitleLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 标题文本
     */
    private val titleView: SuitableSizeG2TextView

    /**
     * 箭头图标
     */
    private val indicator: ImageView

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.videoeditor_resolution_title_layout, this, true)
        titleView = view.findViewById(R.id.action_bar_adjust_quality)
        indicator = view.findViewById(R.id.action_bar_adjust_quality_arrow)
    }

    /**
     * 设置标题
     */
    fun setTitle(text: String) {
        titleView.text = text
    }

    /**
     * 设置选中状态
     */
    fun setTitleSelected(select: Boolean) {
        titleView.isSelected = select
        indicator.isSelected = select
    }
}