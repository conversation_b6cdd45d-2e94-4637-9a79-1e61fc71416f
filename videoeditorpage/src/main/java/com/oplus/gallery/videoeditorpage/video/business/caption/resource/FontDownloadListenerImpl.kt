/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FontDownloadListenerImpl.kt
 ** Description : 字幕字体资源下载监听器
 ** Version     : 1.0
 ** Date        : 2025/6/20 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/20  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.resource

import androidx.lifecycle.LifecycleCoroutineScope
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle.isRunningForeground
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.memories.resource.listener.OnLoadFileListener
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionFontItem
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.DownloadItem
import com.oplus.gallery.videoeditorpage.video.business.caption.util.CaptionResourceHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * 样式文件下载资源监听器
 *
 * @param item 下载的资源item
 * @param position 下载item在列表中的位置
 * @param notifyItemChangedCallback 通知列表更新item的回调
 */
class FontDownloadListenerImpl(
    /**
     * 文字协程作用域
     */
    private val captionScope: LifecycleCoroutineScope,
    private val position: Int,
    private val notifyItemChangedCallback: (item: CaptionFontItem, position: Int, finished: Boolean) -> Unit
) : OnLoadFileListener<CaptionFontItem> {

    override fun onProgress(progress: Int, item: CaptionFontItem) {
        // 资源文件下载进度更新后，通知列表更新item
        notifyItemChangedCallback.invoke(item, position, false)
    }

    override fun onFinish(item: CaptionFontItem) {
        val filePath = item.zipLocalPath
        // 解压zip文件到指定的样式字体资源目录
        captionScope.launch(Dispatchers.IO) {
            val file = File(filePath)
            if (CaptionResourceHelper.isZipFile(file).not()) return@launch
            val actualMd5 = CaptionResourceHelper.calculateMD5(file)
            if (item.resourceMd5 != actualMd5) return@launch
            val unzipPath = CaptionResourceHelper.unzipFontFile(filePath)
            if (unzipPath.isNotEmpty()) {
                captionScope.launch(Dispatchers.Main) {
                    item.downloadState = DownloadItem.DOWNLOADED
                    item.localPath = unzipPath
                    // 资源文件下载解压成功后，更新UI
                    notifyItemChangedCallback.invoke(item, position, true)
                }
            }
        }
    }

    override fun onError(errCode: ErrorCode, item: CaptionFontItem?) {
        if (isRunningForeground()) {
            val stringRes = when (errCode) {
                ErrorCode.NO_NETWORK_WHEN_DOWNLOADING -> R.string.videoeditor_download_network_disconnect
                ErrorCode.NO_NETWORK -> R.string.videoeditor_editor_no_network
                else -> R.string.videoeditor_download_fail
            }
            captionScope.launch(Dispatchers.UI) {
                ToastUtil.showShortToast(stringRes)
            }
            item ?: return
            // 下载失败后下载进度需要重置为0并刷新UI
            item.progress = 0
            notifyItemChangedCallback.invoke(item, position, false)
        }
    }

    override fun onCancel(item: CaptionFontItem) = Unit
}