/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ImageDecoderGuardRule.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.foundation.util.debug.GLog

class DefaultNativeCrashGuardRule(private val carriedMsg: String?) : IGuardRule {

    companion object {
        private const val TAG = "DefaultNativeCrashGuardRule"
        private const val CRASH_COUNT_THRESHOLD = 1
    }

    override fun checkIfNormal(crashes: List<SceneCrashInfo>?): Boolean {
        // 如果无crash记录，或者传入的匹配信息缺失，都认定为正常场景
        if (crashes.isNullOrEmpty() || carriedMsg.isNullOrEmpty()) return true
        var count = 0
        for (crash in crashes) {
            if (crash.carriedMsg == carriedMsg) {
                GLog.e(TAG, "checkIfNormal: scene = ${crash.scene} , carriedMsg = $carriedMsg")
                count++
                if (count >= CRASH_COUNT_THRESHOLD) {
                    return false
                }
            }
        }
        return true
    }

    override fun cacheDuration(): Long {
        return TimeUtils.TIME_2_MONTH_IN_MS
    }
}