/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RearDepthStruct
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/5/30 17:01
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2025/5/30		  1.0		 RearDepthStruct
 *********************************************************************************/
package com.oplus.gallery.foundation.codec.extend.portraitblur

import com.oplus.gallery.foundation.codec.extend.IInverseStructConvert
import com.oplus.gallery.foundation.codec.extend.portraitblur.FrontDepthStruct.Companion.DEFAULT_DATA_CAPACITY
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.MAX_APS_FACE_ROI
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.MAX_APS_SNAP_FACE_POINT_NUM
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.VERSION_V2
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.VERSION_V2_2
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.VERSION_V2_3
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.VERSION_V2_4
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.VERSION_V2_5
import com.oplus.gallery.foundation.codec.extend.portraitblur.RearDepthStruct.Companion.VERSION_V4_0
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.nio.ByteBuffer
import java.nio.ByteOrder
import com.oplus.gallery.foundation.util.ext.put
import com.oplus.gallery.foundation.util.ext.putBoolean

/**
 * 照片后置相关数据结构体
 * 后置虚化强度，协议中用的  blurStrength 是int
 * 前置虚化强度，协议中用的 blurStrength 是float
 */
class RearDepthStruct(byteArray: ByteArray) : PortraitBlurDepthStruct<RearDepthData>(byteArray) {
    private val version: Float32
    private val depthWidth: Signed32
    private val depthHeight: Signed32
    private val focusX: Signed32
    private val focusY: Signed32
    private val blurApertures: Array<Float32>
    private val blurValue: Array<Float32>
    private val blurStrength: Signed32
    private val cameraRoll: Signed32

    private var spotlightWidth: Signed32? = null
    private var spotlightHeight: Signed32? = null
    private var fNumber: Float32? = null
    private var distance: Signed32? = null
    private var isTeleMaster: Bool? = null
    private var refIndexEv: Signed32? = null
    private var minEV: Signed32? = null
    private var sceneMode: Signed32? = null
    private var focusRect: Array<Signed32>? = null
    private var focusRectValid: Bool? = null
    private var mirrorEnable: Signed32? = null
    private var refocusMode: Signed32? = null
    private var lightSpotStrength: Signed32? = null
    private var triggerLightSpotBright: Signed32? = null
    private var curveVal: Float32? = null
    private var shineThres: Signed32? = null
    private var shineLevel: Signed32? = null
    private var spotSharpenAmount: Signed32? = null
    private var spotSharpenRadius: Signed32? = null
    private var foregroundBlurScale: Signed32? = null
    private var masterType: Signed32? = null
    private var isBigfaceEnable: Signed32? = null
    private var isPetsEnable: Signed32? = null
    private var isMultisemsegEnable: Signed32? = null
    private var oplusBokehVersion: Signed32? = null
    private var iso: Signed32? = null
    private var zoomRatio: Signed32? = null
    private var focusRoiType: Signed32? = null
    private var shutter: Float32? = null
    private var aecLuxIdx: Float32? = null
    private var faceCount: Signed32? = null
    private var faces: Array<Array<Signed32>>? = null
    private var faceAngles: Array<Signed32>? = null
    private var keyPointX: Array<Array<Signed32>>? = null
    private var keyPointY: Array<Array<Signed32>>? = null
    private var keyInter: Array<Array<Signed8>>? = null

    init {
        version = Float32()
        depthWidth = Signed32()
        depthHeight = Signed32()
        focusX = Signed32()
        focusY = Signed32()
        blurApertures = array(arrayOfNulls<Float32>(BLUR_NUM))
        blurValue = array(arrayOfNulls<Float32>(BLUR_NUM))
        blurStrength = Signed32()
        cameraRoll = Signed32()

        GLog.d(TAG, LogFlag.DL) { "asCameraRearConfig: version = $version" }

        if (version.get() >= VERSION_V2) {
            spotlightWidth = Signed32()
            spotlightHeight = Signed32()
            fNumber = Float32()
            distance = Signed32()
            isTeleMaster = Bool()
            refIndexEv = Signed32()
            minEV = Signed32()
            sceneMode = Signed32()
        }

        if (version.get() >= VERSION_V2_2) {
            focusRect = array(arrayOfNulls<Signed32>(FOCUS_RECT_LENGTH))
            focusRectValid = Bool()
        }

        if (version.get() >= VERSION_V2_3) {
            mirrorEnable = Signed32()
        }

        if (version.get() >= VERSION_V2_4) {
            refocusMode = Signed32()
            lightSpotStrength = Signed32()
            triggerLightSpotBright = Signed32()
            curveVal = Float32()
            shineThres = Signed32()
            shineLevel = Signed32()
            spotSharpenAmount = Signed32()
            spotSharpenRadius = Signed32()
            foregroundBlurScale = Signed32()
            masterType = Signed32()
        }

        if (version.get() >= VERSION_V2_5) {
            isBigfaceEnable = Signed32()
            isPetsEnable = Signed32()
            isMultisemsegEnable = Signed32()
        }

        if (version.get() >= VERSION_V4_0) {
            oplusBokehVersion = Signed32()
            iso = Signed32()
            zoomRatio = Signed32()
            focusRoiType = Signed32()
            shutter = Float32()
            aecLuxIdx = Float32()
            faceCount = Signed32()
            faces = Array(MAX_APS_FACE_ROI) { Array(FACE_PARAM_NUM) { Signed32() } }
            faceAngles = array(arrayOfNulls<Signed32>(MAX_APS_FACE_ROI))
            keyPointX = Array(MAX_APS_FACE_ROI) { Array(MAX_APS_SNAP_FACE_POINT_NUM) { Signed32() } }
            keyPointY = Array(MAX_APS_FACE_ROI) { Array(MAX_APS_SNAP_FACE_POINT_NUM) { Signed32() } }
            keyInter = Array(MAX_APS_FACE_ROI) { Array(MAX_APS_SNAP_FACE_POINT_NUM) { Signed8() } }
            GLog.d(TAG, LogFlag.DL) {
                "asCameraRearConfig oplusBokehVersion:$oplusBokehVersion, iso:$iso, zoomRatio:$zoomRatio, focusRoiType:$focusRoiType" +
                    ", shutter:$shutter, aecLuxIdx:$aecLuxIdx, faceCount:$faceCount, faces:$faces, faceAngles:$faceAngles"
            }
        }
    }

    override fun toData(): RearDepthData? {
        return runCatching {
            RearDepthData(
                version.get(),
                depthWidth.get(),
                depthHeight.get(),
                focusX.get(),
                focusY.get(),
                blurApertures.get(),
                blurValue.get(),
                blurStrength.get(),
                cameraRoll.get(),
                spotlightWidth?.get(),
                spotlightHeight?.get(),
                fNumber?.get(),
                distance?.get(),
                isTeleMaster?.get(),
                refIndexEv?.get(),
                minEV?.get(),
                sceneMode?.get(),
                focusRect?.get(),
                focusRectValid?.get(),
                mirrorEnable?.get(),
                refocusMode?.get(),
                lightSpotStrength?.get(),
                triggerLightSpotBright?.get(),
                curveVal?.get(),
                shineThres?.get(),
                shineLevel?.get(),
                spotSharpenAmount?.get(),
                spotSharpenRadius?.get(),
                foregroundBlurScale?.get(),
                masterType?.get(),
                isBigfaceEnable?.get(),
                isPetsEnable?.get(),
                isMultisemsegEnable?.get(),
                oplusBokehVersion?.get(),
                iso?.get(),
                zoomRatio?.get(),
                focusRoiType?.get(),
                shutter?.get(),
                aecLuxIdx?.get(),
                faceCount?.get(),
                faces?.get(),
                faceAngles?.get(),
                keyPointX?.get(),
                keyPointY?.get(),
                keyInter?.get(),
            )
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "toData: is error. $it" }
        }.getOrNull()
    }

    companion object {
        private const val TAG = "RearDepthStruct"
        const val MAX_APS_FACE_ROI = 10

        /**
         * 单人脸的参数有4个
         */
        const val FACE_PARAM_NUM = 4

        const val MAX_APS_SNAP_FACE_POINT_NUM = 296

        const val FOCUS_RECT_LENGTH = 4

        private const val BLUR_NUM = 32

        /**
         * 2.0版本：新增光斑图宽高、初始光圈、焦距、主摄、场景模式等数据，此前版本不解析上述数据
         */
        const val VERSION_V2 = 2.0F

        /**
         * 2.1版本：新增光斑压缩图，只有在2.1版本之后才解析光斑图，此前版本按无光斑图的效果处理
         */
        const val VERSION_V2_1 = 2.1F

        /**
         * 2.2版本：新增focus_rect、focusRectValid，焦点框相关数据
         */
        const val VERSION_V2_2 = 2.2F

        /**
         * 2.3版本：新增后置镜像的参数mirrorEnable
         */
        const val VERSION_V2_3 = 2.3F

        /**
         * 2.4版本：新增后置参数：
         *  refocusMode
         *  lightSpotStrength
         *  trigerLightSpotBright
         *  curveVal
         *  shineThres
         *  shineLevel
         *  spotSharpenAmount
         *  spotSharpenRadius
         *  foregroundBlurScale
         *  masterType
         */
        const val VERSION_V2_4 = 2.4F

        /**
         * 开始支持IPU的版本
         * 2.5版本：新增后置开关参数：
         * isBigfaceEnable
         * isPetsEnable
         * isMultisemsegEnable
         */
        const val VERSION_V2_5 = 2.5F

        /**
         * 新增 CropRegion结构体，可以解析出裁剪区域
         */
        const val VERSION_V2_6 = 2.6F

        /**
         * 开始转自研的版本
         * 4.0版本：新增后置自研相关参数：
         * oplusBokehVersion
         * depthScale
         * depthImgFormat
         * hairMaskFlag
         * portraitMaskFlag
         * petSegFlag
         * plantObjects
         * nearObjects
         * nearObjectsConf
         * focalLen
         * baseLine
         * iso_value
         * zoomRatio
         * focusRoiType
         * shutter
         * aecLuxIdx
         * faceCount
         * faces[MAX_APS_FACE_ROI][4]
         * faceAngles[MAX_APS_FACE_ROI]
         * keyPointX[MAX_APS_FACE_ROI][MAX_APS_SNAP_FACE_POINT_NUM]
         * keyPointY[MAX_APS_FACE_ROI][MAX_APS_SNAP_FACE_POINT_NUM]
         * keyInter[MAX_APS_FACE_ROI][MAX_APS_SNAP_FACE_POINT_NUM]
         */
        const val VERSION_V4_0 = 4.0F
    }
}

/**
 * 相机后置摄像头拍摄出来的文件数据
 *
 * @property version 版本
 * @property depthWidth 深度图宽度
 * @property depthHeight 深度图高度
 * @property focusX 焦点坐标x
 * @property focusY 焦点坐标y
 * @property blurApertures 光圈值，[1.4f,16f]，与[blurValue]一一对应
 * @property blurValue 景深虚化值：[0,100]，与[blurApertures]一一对应
 * @property blurStrength 虚化强度:[0,100]
 * @property cameraRoll 照片角度，非人脸角度
 * @property spotlightWidth 光斑图宽度
 * @property spotlightHeight 光斑图高度
 * @property fNumber 虚化滑杆对应的虚化值
 * @property distance 焦距
 * @property isTeleMaster 相机id，主摄是否tele镜头
 * @property refIndexEv 基准帧ev
 * @property minEV 最小曝光值（ev list中最小值）
 * @property sceneMode 算法场景
 * @property focusRect 对焦区域
 * @property focusRectValid 对焦区域合理性判断
 * @property mirrorEnable 是否开启镜像
 * @property refocusMode 重调焦距模式
 * @property lightSpotStrength 光斑强度
 * @property triggerLightSpotBright 激活光斑
 * @property curveVal 曲线值（影响虚化渐进效果）
 * @property shineThres
 * @property shineLevel
 * @property spotSharpenAmount 光斑锐化量
 * @property spotSharpenRadius 光斑锐化圆角半径
 * @property foregroundBlurScale 前景虚化范围
 * @property masterType 主摄类型
 * @property isBigfaceEnable 大人脸近距离效果优化开关
 * @property isPetsEnable 宠物优化开关
 * @property isMultisemsegEnable 多语义优化开关
 * @property oplusBokehVersion
 * @property iso 亮度
 * @property zoomRatio 点切倍率
 * @property focusRoiType 对接类型
 * @property shutter 曝光值
 * @property aecLuxIdx 环境亮度
 * @property faceCount 人脸数量
 * @property faces 人脸区域坐标
 * @property faceAngles 人脸角度数组
 * @property keyPointX 特征组X点
 * @property keyPointY 特征组Y点
 * @property keyInter 特征值
 */
class RearDepthData(
    val version: Float? = 0f,
    val depthWidth: Int? = 0,
    val depthHeight: Int? = 0,
    val focusX: Int? = 0,
    val focusY: Int? = 0,
    blurApertures: FloatArray? = null,
    blurValue: FloatArray? = null,
    val blurStrength: Int? = 0,
    val cameraRoll: Int? = 0,
    var spotlightWidth: Int? = 0,
    var spotlightHeight: Int? = 0,
    fNumber: Float? = 0f,
    var distance: Int? = 0,
    var isTeleMaster: Boolean? = false,
    var refIndexEv: Int? = 0,
    var minEV: Int? = 0,
    var sceneMode: Int? = 0,
    var focusRect: IntArray? = null,
    var focusRectValid: Boolean? = false,
    var mirrorEnable: Int? = 0,
    var refocusMode: Int? = 0,
    var lightSpotStrength: Int? = 0,
    var triggerLightSpotBright: Int? = 0,
    var curveVal: Float? = 0f,
    var shineThres: Int? = 0,
    var shineLevel: Int? = 0,
    var spotSharpenAmount: Int? = 0,
    var spotSharpenRadius: Int? = 0,
    var foregroundBlurScale: Int? = 0,
    var masterType: Int? = 0,
    var isBigfaceEnable: Int? = 0,
    var isPetsEnable: Int? = 0,
    var isMultisemsegEnable: Int? = 0,
    var oplusBokehVersion: Int? = 0,
    var iso: Int? = 0,
    var zoomRatio: Int? = 0,
    var focusRoiType: Int? = 0,
    var shutter: Float? = 0f,
    var aecLuxIdx: Float? = 0f,
    var faceCount: Int? = 0,
    var faces: Array<IntArray>? = null,
    var faceAngles: IntArray? = null,
    var keyPointX: Array<IntArray>? = null,
    var keyPointY: Array<IntArray>? = null,
    var keyInter: Array<IntArray>? = null,
) : PortraitBlurDepthData(fNumber, blurApertures, blurValue), IInverseStructConvert {
    override val blurStrengthParam: Float? = blurStrength?.toFloat()

    fun copy(
        version: Float? = this.version,
        depthWidth: Int? = this.depthWidth,
        depthHeight: Int? = this.depthHeight,
        focusX: Int? = this.focusX,
        focusY: Int? = this.focusY,
        blurApertures: FloatArray? = this.blurApertures,
        blurValue: FloatArray? = this.blurValue,
        blurStrength: Int? = this.blurStrength,
        cameraRoll: Int? = this.cameraRoll,
        spotlightWidth: Int? = this.spotlightWidth,
        spotlightHeight: Int? = this.spotlightHeight,
        fNumber: Float? = this.fNumber,
        distance: Int? = this.distance,
        isTeleMaster: Boolean? = this.isTeleMaster,
        refIndexEv: Int? = this.refIndexEv,
        minEV: Int? = this.minEV,
        sceneMode: Int? = this.sceneMode,
        focusRect: IntArray? = this.focusRect,
        focusRectValid: Boolean? = this.focusRectValid,
        mirrorEnable: Int? = this.mirrorEnable,
        refocusMode: Int? = this.refocusMode,
        lightSpotStrength: Int? = this.lightSpotStrength,
        triggerLightSpotBright: Int? = this.triggerLightSpotBright,
        curveVal: Float? = this.curveVal,
        shineThres: Int? = this.shineThres,
        shineLevel: Int? = this.shineLevel,
        spotSharpenAmount: Int? = this.spotSharpenAmount,
        spotSharpenRadius: Int? = this.spotSharpenRadius,
        foregroundBlurScale: Int? = this.foregroundBlurScale,
        masterType: Int? = this.masterType,
        isBigfaceEnable: Int? = this.isBigfaceEnable,
        isPetsEnable: Int? = this.isPetsEnable,
        isMultisemsegEnable: Int? = this.isMultisemsegEnable,
        oplusBokehVersion: Int? = this.oplusBokehVersion,
        iso: Int? = this.iso,
        zoomRatio: Int? = this.zoomRatio,
        focusRoiType: Int? = this.focusRoiType,
        shutter: Float? = this.shutter,
        aecLuxIdx: Float? = this.aecLuxIdx,
        faceCount: Int? = this.faceCount,
        faces: Array<IntArray>? = this.faces,
        faceAngles: IntArray? = this.faceAngles,
        keyPointX: Array<IntArray>? = this.keyPointX,
        keyPointY: Array<IntArray>? = this.keyPointY,
        keyInter: Array<IntArray>? = this.keyInter,
    ): RearDepthData {
        return RearDepthData(
            version,
            depthWidth,
            depthHeight,
            focusX,
            focusY,
            blurApertures,
            blurValue,
            blurStrength,
            cameraRoll,
            spotlightWidth,
            spotlightHeight,
            fNumber,
            distance,
            isTeleMaster,
            refIndexEv,
            minEV,
            sceneMode,
            focusRect,
            focusRectValid,
            mirrorEnable,
            refocusMode,
            lightSpotStrength,
            triggerLightSpotBright,
            curveVal,
            shineThres,
            shineLevel,
            spotSharpenAmount,
            spotSharpenRadius,
            foregroundBlurScale,
            masterType,
            isBigfaceEnable,
            isPetsEnable,
            isMultisemsegEnable,
            oplusBokehVersion,
            iso,
            zoomRatio,
            focusRoiType,
            shutter,
            aecLuxIdx,
            faceCount,
            faces,
            faceAngles,
            keyPointX,
            keyPointY,
            keyInter
        )
    }

    override fun toBytes(): ByteArray {
        val buffer: ByteBuffer = ByteBuffer.allocate(DEFAULT_DATA_CAPACITY)
        buffer.order(ByteOrder.LITTLE_ENDIAN)
        version?.let { buffer.putFloat(it) }
        depthWidth?.let { buffer.putInt(it) }
        depthHeight?.let { buffer.putInt(it) }
        focusX?.let { buffer.putInt(it) }
        focusY?.let { buffer.putInt(it) }
        blurApertures?.let { buffer.put(it) }
        blurValue?.let { buffer.put(it) }
        blurStrength?.let { buffer.putInt(it) }
        cameraRoll?.let { buffer.putInt(it) }

        version?.takeIf { it >= VERSION_V2 }?.let {
            spotlightWidth?.let { buffer.putInt(it) }
            spotlightHeight?.let { buffer.putInt(it) }
            fNumber?.let { buffer.putFloat(it) }
            distance?.let { buffer.putInt(it) }
            isTeleMaster?.let { buffer.putBoolean(it) }
            refIndexEv?.let { buffer.putInt(it) }
            minEV?.let { buffer.putInt(it) }
            sceneMode?.let { buffer.putInt(it) }
        }

        version?.takeIf { it >= VERSION_V2_2 }?.let {
            focusRect?.let { buffer.put(it) }
            focusRectValid?.let { buffer.putBoolean(it) }
        }

        version?.takeIf { it >= VERSION_V2_3 }?.let {
            mirrorEnable?.let { buffer.putInt(it) }
        }

        version?.takeIf { it >= VERSION_V2_4 }?.let {
            refocusMode?.let { buffer.putInt(it) }
            lightSpotStrength?.let { buffer.putInt(it) }
            triggerLightSpotBright?.let { buffer.putInt(it) }
            curveVal?.let { buffer.putFloat(it) }
            shineThres?.let { buffer.putInt(it) }
            shineLevel?.let { buffer.putInt(it) }
            spotSharpenAmount?.let { buffer.putInt(it) }
            spotSharpenRadius?.let { buffer.putInt(it) }
            foregroundBlurScale?.let { buffer.putInt(it) }
            masterType?.let { buffer.putInt(it) }
        }

        version?.takeIf { it >= VERSION_V2_5 }?.let {
            isBigfaceEnable?.let { buffer.putInt(it) }
            isPetsEnable?.let { buffer.putInt(it) }
            isMultisemsegEnable?.let { buffer.putInt(it) }
        }

        version?.takeIf { it >= VERSION_V4_0 }?.let {
            oplusBokehVersion?.let { buffer.putInt(it) }
            iso?.let { buffer.putInt(it) }
            zoomRatio?.let { buffer.putInt(it) }
            focusRoiType?.let { buffer.putInt(it) }
            shutter?.let { buffer.putFloat(it) }
            aecLuxIdx?.let { buffer.putFloat(it) }
            faceCount?.let { buffer.putInt(it) }
            faces?.let { buffer.put(it) }
            faceAngles?.let { buffer.put(it) }
            keyPointX?.let { buffer.put(it) }
            keyPointY?.let { buffer.put(it) }
            keyInter?.let { buffer.put(it) }
        }

        buffer.limit(buffer.position())
        buffer.rewind()
        val actualArray = ByteArray(buffer.limit())
        buffer.get(actualArray)
        return actualArray
    }
}