/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetSetViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/26        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.set

import android.app.Application
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.business_lib.model.data.local.set.WidgetAlbum
import com.oplus.gallery.business_lib.viewmodel.base.BaseViewModel
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.model.OnContentChangedListener
import com.oplus.gallery.framework.abilities.data.model.WidgetSetModel
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.widgetpage.WidgetConstants
import com.oplus.gallery.widgetpage.track.WidgetTrackHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class WidgetSetViewModel(application: Application) : BaseViewModel(application) {

    private val model by lazy {
        DataRepository.getAlbumSetModel(DataRepository.LocalAlbumModelGetter.TYPE_WIDGET_SET, Bundle()) as WidgetSetModel
    }
    private val onContentChangedListener = OnContentChangedListener { model, _ ->
        GLog.d(TAG, "onContentChanged: ")
        launch(Dispatchers.ReloadTask) {
            model.apply {
                reload()
                getItems(0, getCount()).map {
                    (it as WidgetAlbum).widgetCode
                }.let {
                    internalLiveData.postValue(it)
                }
            }
        }
    }
    private val internalLiveData = MutableLiveData<List<String>>()
    val liveData: LiveData<List<String>> = internalLiveData

    override fun onResume() {
        super.onResume()
        generateRecommendedDisplayListIfNeeded()
    }

    private fun generateRecommendedDisplayListIfNeeded() {
        val context = ContextGetter.context
        SPUtils.getLong(context, spName = null, WidgetConstants.SPName.LAST_GENERATE_RECOMMENDED_LIST_TIME, 0).takeIf {
            !TimeUtils.isToday(it)
        }?.run {
            SPUtils.setLong(context, spName = null, WidgetConstants.SPName.LAST_GENERATE_RECOMMENDED_LIST_TIME, System.currentTimeMillis())
            AppScope.launch(Dispatchers.IO) {
                delay(WidgetConstants.LastTime.DELAY_GENERATE_RECOMMENDED_LIST)
                // 相册在前台时跳过，避免用户停留在大图时发生数据变化
                if (ActivityLifecycle.isRunningForeground()) {
                    GLog.d(TAG, "generateRecommendedDisplayListIfNeeded skip for foreground")
                } else {
                    model.generateRecommendedDisplayListIfNeeded()
                }
            }
        } ?: GLog.d(TAG, "generateRecommendedDisplayListIfNeeded last_generate_recommended_list_time TimeUtils.isToday so return ")
    }

    /**
     * 注册数据变化监听
     */
    fun registerContentChangedListener() {
        GLog.d(TAG, "registerContentChangedListener")
        model.reload()
        model.registerContentChangedListener(onContentChangedListener)
    }

    /**
     * 反注册数据变化监听
     */
    fun unregisterContentChangedListener() {
        GLog.d(TAG, "unregisterContentChangedListener")
        model.unregisterContentChangedListener(onContentChangedListener)
    }

    /**
     * 插入卡片数据
     * @param widgetCode 卡片标识
     */
    fun addWidget(widgetCode: String) {
        AppScope.launch(Dispatchers.ReloadTask) {
            if (model.addWidget(widgetCode)) {
                WidgetTrackHelper.trackAddWidget()
            }
        }
    }

    /**
     * 删除卡片数据
     * @param widgetCode 卡片标识
     */
    fun removeWidget(widgetCode: String) {
        AppScope.launch(Dispatchers.IO) {
            model.removeWidget(widgetCode)
            WidgetTrackHelper.trackRemoveWidget()
        }
    }

    fun updateAllWidget() {
        AppScope.launch(Dispatchers.IO) {
            model.setAllWidgetDirty()
        }
    }

    override fun getTag(): String {
        return TAG
    }

    companion object {
        private const val TAG = "WidgetSetViewModel"
    }
}