/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorTrackerHelper.kt
 ** Description: 视频编辑2.0 点击事件退出时上报
 **
 ** Version: 1.0
 ** Date: 2025/07/25
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		       2025/07/25		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.util

import android.content.Context
import android.net.Uri
import androidx.annotation.WorkerThread
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.foundation.fileaccess.FileConstants.MediaType.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.fileaccess.FileConstants.MediaType.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.EventId
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_DURATION
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_FPS
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_HDR_TYPE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_MIMETYPE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_RESOLUTION
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_SIZE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Key.SOURCE_TYPE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.SOURCE_TYPE_IMAGE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.SOURCE_TYPE_IMAGE_OLIVE
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.SOURCE_TYPE_UNKNOWN
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.SOURCE_TYPE_VIDEO
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.EXPORT_OLIVE_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.GET_FRAME_TASK_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.MEICAM_FORCE_STOP_SAVE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_ERROR
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_FAILED_NO_SPACE
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.SAVE_SUCCESS
import com.oplus.gallery.videoeditorpage.memories.business.output.ResultCode.Companion.TASK_STOP_FORCE_STOP_SAVE
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.joint.JointEntry
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveType
import com.oplus.gallery.videoeditorpage.video.business.picker.data.PickerItemInfo

/**
 * 视频编辑2.0 点击事件退出时上报
 */
object VideoEditorTrackerHelper {

    /**
     * 逗号分隔符
     */
    private const val COMMA_SEPARATOR = ","

    /**
     * 横线分隔符
     */
    private const val DASH_SEPARATOR = "-"

    /**
     * 星号分隔符
     */
    private const val ASTERISK_CONNECTOR = "*"

    /**
     * 日志TAG
     */
    private const val TAG = "VideoEditorTrackerHelper"

    /**
     * 记录点击过的功能，以拼接方式存储
     *
     * 一级菜单
     * {"0":"剪辑","1":"音乐","2":"文字","3":"调节","4":"滤镜","5":"画幅"，“6”设置封面, "7"保存}
     *
     * 二级菜单
     * 剪辑子功能——{"101":"分割","102":"替换","103":"删除，“104”：“裁剪旋转”，“105”：“变速”，
     *            “106”：常规变速，“107”：“曲线变速”，“108”：“排序”，““109”:"添加"，“110”：“原声音乐开关”}
     * 音乐子功能——{“111”：“添加音乐”、“112”：原声音量、“113”：“音乐分割”、“114”：“音乐音量”、
     *           “115”：“淡化”、“116”：“卡点”、“117”：“复制”、“118”：“音乐删除”}
     *
     * 设置封面子功能——“119 从视频中选择”、“120 从相册中选择”
     * 保存子功能——“121”：“调整分辨率”、“122”：“调整帧率”、“123”：“调整视频保存类型”
     *
     * item_click="0-101-102,3-109,0-102"
     */
    private val clickDataBuilder = StringBuilder()

    /**
     * 记录异常信息
     */
    private val exceptionDataBuilder = StringBuilder()

    /*** 多段视频的数据信息*/
    private val multiVideoDataMap by lazy { HashMap<String, ArrayList<TrackVideoData>>() }

    /**
     * 清空重置数据
     */
    @JvmStatic
    fun reset() {
        clickDataBuilder.clear()
        exceptionDataBuilder.clear()
        multiVideoDataMap.clear()
    }

    /**
     * 拼接视频编辑菜单功能信息
     * 示例："0-101-102,3-109,0-102"
     */
    @JvmStatic
    fun appendMenuClickData(videoMenuTypeValue: String, menuLevelType: MenuLevelType) {
        runCatching {
            // 处理分隔符
            when (menuLevelType) {
                MenuLevelType.FIRST_LEVEL -> {
                    if (clickDataBuilder.isNotEmpty()) {
                        clickDataBuilder.append(COMMA_SEPARATOR)
                    }
                }

                MenuLevelType.SECONDARY_LEVEL -> clickDataBuilder.append(DASH_SEPARATOR)
            }
            clickDataBuilder.append(videoMenuTypeValue)
        }
    }

    /**
     * 拼接异常信息
     *
     * 异常分类：效果应用失效0，功能异常1，直接退出2，保存退出3
     *
     * 效果应用的异常类型
     * {"0":"变速","1":"设为封面","2":"音乐","3":"文字","4":"调节","5":"滤镜","6":"转场"}
     *
     * 功能的异常类型
     * {"0":"关闭原声","1":"提取音乐"}
     *
     * 直接退出的异常类型
     * {"0":"gop值过长","1":"分辨率不支持","2":"帧率不支持","3":"格式不支持","4":"HDR不支持"}
     *
     * 保存退出的异常类型
     * {"1":"美摄引擎保存失败"}
     * 示例：exit_msg=0-0,1-0
     */
    @JvmStatic
    fun appendExceptionData(firstLevelExceptionId: String, secondLevelExceptionId: String) {
        // 处理分隔符
        runCatching {
            if (exceptionDataBuilder.isNotEmpty()) {
                exceptionDataBuilder.append(COMMA_SEPARATOR)
            }
            exceptionDataBuilder.append("$firstLevelExceptionId$DASH_SEPARATOR$secondLevelExceptionId")
            GLog.d(TAG, LogFlag.DL) {
                "[appendExceptionData] firstLevelExceptionId: $firstLevelExceptionId, " +
                    "secondLevelExceptionId: $secondLevelExceptionId"
            }
        }
    }

    /**
     * 2006011001 视频编辑功能一级界面点击事件
     * @param ifSaveVideo 是否保存视频
     * @param editFrom 视频编辑入口
     */
    @JvmStatic
    fun trackVideoEditMenuClickUserAction(
        ifSaveVideo: Int,
        saveType: String,
        editFrom: String
    ) {
        track(EventId.VIDEO_EDIT_FIRST_PAGE_CLICK_EXIT) {
            it.putProperty(Key.ITEM_CLICK, clickDataBuilder.toString())
            it.putProperties(getMultiVideoTrackDataMap())
            it.putProperty(Key.EXIT_MSG, exceptionDataBuilder.toString())
            it.putProperty(Key.SAVE_VIDEO, ifSaveVideo)
            it.putProperty(Key.SAVE_TYPE, saveType)
            it.putProperty(Key.EDIT_FROM, editFrom)
            it.save()
            reset()
        }
    }

    @JvmStatic
    fun getSaveType(saveType: SaveType): String {
        var saveTypeTrackValue = Value.VIDEO_SAVE_TYPE
        if (saveType == SaveType.OLIVE) {
            saveTypeTrackValue = Value.LIVE_SAVE_TYPE
        }
        return saveTypeTrackValue
    }

    @JvmStatic
    fun getEnterFrom(isMultipleEdit: Boolean): String {
        return if (isMultipleEdit) { // 是否从“多选”进入编辑。
            Value.FROM_EXTERNAL
        } else {
            Value.FROM_GALLERY
        }
    }

    /**
     * 在初始化时构造埋点视频数据
     */
    @JvmStatic
    fun buildTrackVideoData(info: PickerItemInfo) {
        runCatching {
            val mediaItem = LocalMediaDataHelper.getLocalMediaItem(Uri.parse(info.path))
            val mimeType = MimeTypeUtils.getMimeType(info.path, mediaItem?.mimeType)
            val fileInfo = EditorEngineGlobalContext.getInstance().getAVFileInfo(info.path)
            val videoData = TrackVideoData(
                originSourceSize = mediaItem?.fileSize ?: 0L,
                sourceSize = mediaItem?.fileSize ?: 0L,
                sourceResolution = info.width.toString() + ASTERISK_CONNECTOR + info.height,
                sourceDuration = info.duration,
                sourceHdrType = info.mediaHdrType.toString(),
                sourceType = getSourceType(mediaItem, info.isOlivePhoto).toString(),
                sourceMimeType = mimeType ?: "",
                sourceFps = fileInfo.fps.toString()
            )
            if (multiVideoDataMap.contains(info.path)) { // 分割场景：同一个视频路径，多个VideoData。
                multiVideoDataMap[info.path]?.apply { add(videoData) }
            } else {
                multiVideoDataMap[info.path] = ArrayList<TrackVideoData>().apply { add(videoData) }
            }
        }
    }

    /**
     * 在替换、新增时构造埋点数据（删除只需要最终过滤即可）
     */
    @JvmStatic
    fun buildTrackVideoData(mediaItem: MediaItem, jointEntry: JointEntry) {
        runCatching {
            val mimeType = MimeTypeUtils.getMimeType(jointEntry.path, mediaItem.mimeType)
            val fileInfo = EditorEngineGlobalContext.getInstance().getAVFileInfo(jointEntry.path)
            val videoData = TrackVideoData(
                originSourceSize = mediaItem.fileSize,
                sourceSize = mediaItem.fileSize,
                sourceResolution = jointEntry.width.toString() + ASTERISK_CONNECTOR + jointEntry.height.toString(),
                sourceDuration = jointEntry.duration,
                sourceHdrType = jointEntry.mediaHdrType.toString(),
                sourceType = getSourceType(mediaItem, jointEntry.isOlivePhoto).toString(),
                sourceMimeType = mimeType ?: "",
                sourceFps = fileInfo.fps.toString()
            )
            if (multiVideoDataMap.contains(jointEntry.path)) { // 分割场景：同一个视频路径，多个VideoData。
                multiVideoDataMap[jointEntry.path]?.apply { add(videoData) }
            } else {
                multiVideoDataMap[jointEntry.path] = ArrayList<TrackVideoData>().apply { add(videoData) }
            }
        }
    }

    /**
     * 在分割时处理，原视频分割需要更新sourceSize和sourceDuration参数，其余参数不变。
     */
    @JvmStatic
    fun buildTrackVideoData(path: String, duration: Long, fileDuration: Long) {
        if (fileDuration <= 0) {
            GLog.e(TAG, "buildTrackVideoData. invalid fileDuration = $fileDuration")
            return
        }
        runCatching {
            if (multiVideoDataMap[path]?.isNotEmpty() == true) {
                val videoData = multiVideoDataMap[path]?.get(0)?.copy()
                videoData?.apply {
                    sourceSize = ((duration.toFloat() / fileDuration.toFloat()) * this.originSourceSize).toLong()
                    sourceDuration = duration
                    multiVideoDataMap[path]?.add(videoData)
                }
            }
        }
    }

    /**
     * 获取素材资源类型
     * {"0":"图片","1":"视频","2":"实况","3":"未知类型"}
     */
    private fun getSourceType(mediaItem: MediaItem?, isOlivePhoto: Boolean = false): Int {
        val sourceType = when (mediaItem?.mediaType) {
            MEDIA_TYPE_VIDEO -> SourceType.VIDEO
            MEDIA_TYPE_IMAGE -> {
                if (isOlivePhoto) { // 图片类型 && 实况图片
                    SourceType.OLIVE
                } else { // 图片类型 && 非实况图片
                    SourceType.IMAGE
                }
            }
            else -> SourceType.UNKNOWN
        }
        return sourceType.code
    }

    /**
     * 2006011018 视频编辑-导出实况 或者导出照片
     * @param context Context 上下文
     * @param info PickerItemInfo 资源信息
     * @param oliveUri String 导出LivePhoto的 uri
     * @param costTime Long 导出执行用时（ms）
     * @param saveType: SaveType 导出类型，olive、photo
     * @param resultType Int 导出的结果code码 0:成功 1:其他类型失败 2:存储空间不足失败 3:抽帧失败 4:算法执行失败
     */
    @JvmStatic
    fun trackVideoEditorExportOLiveOrPhoto(
        context: Context,
        info: PickerItemInfo?,
        oliveUri: String?,
        costTime: Long,
        saveType: SaveType,
        resultType: Int
    ) {
        val eventId = if (saveType == SaveType.NORMAL) {
            EventId.VIDEO_EDIT_EXPORT_PHOTO_ITEM_CLICK
        } else {
            EventId.VIDEO_EDIT_EXPORT_OLIVE_ITEM_CLICK
        }
        track(eventId) {
            val videoData = multiVideoDataMap[info?.path]?.get(0) ?: return@track
            it.putProperty(Key.VIDEO_EDIT_VIDEO_TYPE, videoData.sourceType)
            it.putProperty(Key.VIDEO_EDIT_EXPORT_PHOTO_IMAGE_TYPE, "image/jpeg")
            it.putProperty(Key.VIDEO_EDIT_VIDEO_WIDTH_HEIGHT, videoData.sourceResolution)
            oliveUri?.let { uri ->
                val oliveHdrType = getHdrType(VideoTypeUtils.getOliveHdrType(context, uri))
                it.putProperty(Key.VIDEO_EDIT_EXPORT_OLIVE_TYPE, oliveHdrType)
            }
            it.putProperty(Key.VIDEO_EDIT_EXPORT_COST_TIME, costTime)
            it.putProperty(Key.VIDEO_EDIT_EXPORT_RESULT_TYPE, getResultType(resultType))
            it.save()
        }
    }

    /**
     * 获取埋点需要的hdrType
     * @param hdrType 视频的类型
     * @return String 返回hdr的类型
     */
    private fun getHdrType(hdrType: Int): String {
        return when (hdrType) {
            VideoTypeUtils.HDR_TYPE_DOLBY -> Value.VIDEO_HDR_TYPE_DOLBYVISION
            VideoTypeUtils.HDR_TYPE_HLG -> Value.VIDEO_HDR_TYPE_HLG
            VideoTypeUtils.HDR_TYPE_HDR10 -> Value.VIDEO_HDR_TYPE_HDR10
            VideoTypeUtils.HDR_TYPE_HDR10_PLUS -> Value.VIDEO_HDR_TYPE_HDR10PLUS
            VideoTypeUtils.HDR_TYPE_NO_HDR -> Value.VIDEO_HDR_TYPE_SDR
            else -> Value.VIDEO_HDR_TYPE_UNKNOWN
        }
    }

    /**
     * 导出资源（图片或者olive）的code码映射
     * @param resultType Int 导出资源（图片或者olive）成功失败的code码
     * @return Int 埋点所需要的值
     */
    private fun getResultType(resultType: Int): Int {
        return when (resultType) {
            SAVE_SUCCESS -> Value.EXPORT_RESOURCE_RESULT_TYPE_SUCCESS
            SAVE_FAILED_NO_SPACE -> Value.EXPORT_RESOURCE_RESULT_TYPE_NO_SPACE_FAILED
            GET_FRAME_TASK_ERROR -> Value.EXPORT_RESOURCE_RESULT_TYPE_FRAME_FAILED
            MEICAM_FORCE_STOP_SAVE -> Value.EXPORT_RESOURCE_RESULT_TYPE_MEICAM_FORCE_STOP_SAVE
            TASK_STOP_FORCE_STOP_SAVE -> Value.EXPORT_RESOURCE_RESULT_TYPE_TASK_STOP_FORCE_STOP_SAVE
            EXPORT_OLIVE_TASK_ERROR -> Value.EXPORT_RESOURCE_RESULT_TYPE_OLIVE_TASK_ERROR
            SAVE_FAILED_ERROR -> Value.EXPORT_RESOURCE_RESULT_TYPE_SAVE_FAILED_ERROR
            else -> Value.EXPORT_RESOURCE_RESULT_TYPE_OTHER_FAILED
        }
    }

    private fun getMultiVideoTrackDataMap(): Map<String, String> {
        // videoInfoMap是实时更新的（在进行删除、替换、新增时）
        val trackDataMap = HashMap<String, String>()
        runCatching {
            val pathList = VideoParser.getInstance().videoInfoMap.values.map { it.path }.toList()
            val videoDataList = multiVideoDataMap.filter { pathList.contains(it.key) }.values.flatten().toList()
            trackDataMap[SOURCE_SIZE] = videoDataList.map { it.sourceSize }.toList().joinToString(COMMA_SEPARATOR)
            trackDataMap[SOURCE_RESOLUTION] = videoDataList.map { it.sourceResolution }.toList().joinToString(COMMA_SEPARATOR)
            trackDataMap[SOURCE_DURATION] = videoDataList.map { it.sourceDuration }.toList().joinToString(COMMA_SEPARATOR)
            trackDataMap[SOURCE_MIMETYPE] = videoDataList.map { it.sourceMimeType }.toList().joinToString(COMMA_SEPARATOR)
            trackDataMap[SOURCE_HDR_TYPE] = videoDataList.map { it.sourceHdrType }.toList().joinToString(COMMA_SEPARATOR)
            trackDataMap[SOURCE_TYPE] = videoDataList.map { it.sourceType }.toList().joinToString(COMMA_SEPARATOR)
            trackDataMap[SOURCE_FPS] = videoDataList.map { it.sourceFps }.toList().joinToString(COMMA_SEPARATOR)
        }
        return trackDataMap
    }

    private fun track(eventId: String, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = eventId,
            type = VideoEditorTrackConstant.TYPE_VIDEO_EDITOR,
            func = func
        )
    }

    /**
     * 当前点击的菜单属于几级菜单
     */
    enum class MenuLevelType {
        FIRST_LEVEL,
        SECONDARY_LEVEL
    }

    /**
     * 素材资源类型
     *  {"0":"图片","1":"视频","2":"实况","3":"未知类型"}
     */
    enum class SourceType(val code: Int) {
        IMAGE(SOURCE_TYPE_IMAGE),
        VIDEO(SOURCE_TYPE_VIDEO),
        OLIVE(SOURCE_TYPE_IMAGE_OLIVE),
        UNKNOWN(SOURCE_TYPE_UNKNOWN)
    }

    /**
     * 埋点上报的视频相关数据
     */
    data class TrackVideoData(
        /**
         * 原视频大小（视频存在被分割的情况）
         */
        val originSourceSize: Long,
        /**
         * 视频大小
         */
        var sourceSize: Long,
        /**
         * 视频分辨率
         * 1800*3600
         */
        var sourceResolution: String,
        /**
         * 视频耗时
         * 单位是秒
         */
        var sourceDuration: Long,
        /**
         * 视频格式类型
         */
        val sourceMimeType: String,
        /**
         * 视频HDR格式
         * {"0":"未知类型","1":"SDR","2":"HLG","3":"HDR10",
         * “4”：HDR10_PLUS”，"5":"Dolby Vision","6":"UHdr","7":"LocalHdr"}
         */
        val sourceHdrType: String,
        /**
         * 视频资源类型
         * {"0":"图片","1":"视频","2":"实况","3":"未知类型"}
         */
        val sourceType: String,
        /**
         * 视频帧率
         */
        var sourceFps: String
    )
}