/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OnLoadFileListener.kt
 ** Description:监听文件加载
 **
 ** Version: 1.0
 ** Date: 2020/08/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/19		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.listener

import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode

/**
 * 监听文件加载
 */
interface OnLoadFileListener<in T> {

    /**
     * 进度刷新
     * @param item 文件
     */
    fun onProgress(progress: Int, item: T)

    /**
     * 加载完成
     * @param item 文件
     */
    fun onFinish(item: T)

    /**
     * 加载失败
     * @param errCode 错误码
     * @param item 文件
     */
    fun onError(errCode: ErrorCode, item: T?)

    /**
     * 取消加载
     * @param item 文件
     */
    fun onCancel(item: T)
}

/**
 * 简化监听文件加载，也可以传入listener代理传递事件
 */
open class SimpleLoadFileListener<T>(val listener: OnLoadFileListener<T>? = null) : OnLoadFileListener<T> {
    override fun onProgress(progress: Int, item: T) {
        listener?.onProgress(progress, item)
    }

    override fun onFinish(item: T) {
        listener?.onFinish(item)
    }

    override fun onError(errCode: ErrorCode, item: T?) {
        listener?.onError(errCode, item)
    }

    override fun onCancel(item: T) {
        listener?.onCancel(item)
    }
}