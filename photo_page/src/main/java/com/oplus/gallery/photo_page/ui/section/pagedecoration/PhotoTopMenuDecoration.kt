/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoTopMenuDecoration.kt
 ** Description : 大图顶部菜单
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.pagedecoration

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import android.view.Display
import android.view.Gravity
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.CompoundButton
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import androidx.annotation.MainThread
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.view.menu.MenuBuilder
import androidx.core.graphics.drawable.toBitmap
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.scale
import androidx.core.view.forEach
import androidx.core.view.updatePadding
import androidx.core.view.updatePaddingRelative
import androidx.lifecycle.coroutineScope
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.state.COUIMaskRippleDrawable
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.gallery.business.drawable.recycle
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.MENU_ITEM_MORE
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.foundation.util.ext.show
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.math.Math2DUtil
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.isolatedlayer.IPhotoTopMenuAdapter
import com.oplus.gallery.photo_page.ui.isolatedlayer.PhotoTopMenu
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoSubMenuCreator.PopupListWindowItem
import com.oplus.gallery.photo_page.ui.section.pagedecoration.PhotoSubMenuCreator.SubMenuItem
import com.oplus.gallery.photo_page.ui.section.photomenu.IPhotoMenuTextTips
import com.oplus.gallery.photo_page.ui.section.photomenu.MenuIcon
import com.oplus.gallery.photo_page.ui.section.photomenu.PhotoMenuIconDrawableManager
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoAnimationConfig
import com.oplus.gallery.photo_page.ui.theme.MenuDecorationTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.menucontrol.IconTipType
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuItemState
import com.oplus.gallery.photo_page.viewmodel.menucontrol.MenuState
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuExecutor
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuItemTips
import com.oplus.gallery.photo_page.viewmodel.menucontrol.PhotoMenuTitle
import com.oplus.gallery.photo_page.widget.menu.MenuActionView
import com.oplus.gallery.photo_page.widget.menu.MenuAnimateView
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AtFrontOfMainQueueDispatcher
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.tools.removeShapeOutline
import com.oplus.gallery.tools.setCircularOutline
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import java.util.LinkedList
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.removeLast as ktRemoveLast
import androidx.core.view.isNotEmpty
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageAnimationEvent

/**
 * 大图页顶部菜单实现。
 * 顶部菜单会在不同的屏幕尺寸下有不同的表现：
 * - 屏幕宽度小于临界值时，只显示顶部菜单
 * - 屏幕宽度超出临界值时，会将底部菜单项一起显示在顶部菜单中。
 *
 * 底部菜单项和底部菜单项的行为会有所不同，底部菜单项会被认为是常驻的，具体表现如下：
 *  - 顶部菜单项：当 [menu]中存在而 [currentMenuState] 中不存在对应菜单项时，将该菜单项隐藏掉（menuItem.isVisible = false）。
 *  - 底部菜单项：当 [menu]中存在而 [currentMenuState] 中不存在对应菜单项时，将该菜单项禁用（menuItem.isEnabled = false）。
 *  @see PhotoBottomMenuDecoration
 */
@ExperimentalUnsignedTypes
class PhotoTopMenuDecoration(
    private val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,

    /**
     * 是否显示包括底部菜单项在内的全部菜单
     */
    private val isShowAllMenuItems: () -> Boolean,

    /**
     * 底层真正的菜单对象
     */
    override var photoTopMenu: PhotoTopMenu,

    /**
     * 文本提示
     */
    private val textTips: IPhotoMenuTextTips,

    /**
     * 菜单项点击回调
     */
    private val onItemClicked: (action: Int) -> Unit = {},

    /**
     * 菜单项长按后松手的回调
     */
    private val onItemLongClickUp: (action: Int) -> Unit = {},

    /**
     * PhotoMenuExecutor对象，用于设置锚点view
     */
    private val photoMenuExecutor: PhotoMenuExecutor
) : ITopMenuSection, IPhotoTopMenuAdapter {

    /**
     * 当前收到的菜单的状态数据，收到此数据后并不会刷新菜单，而需要
     * 调用[invalidateMenu]才会根据[MenuState]来刷新菜单。
     */
    var currentMenuState: MenuState? = null
        set(value) {
            val isFreshMoreSubMenuView = isFreshMoreSubMenuView(value?.menuItemStates, field?.menuItemStates)
            field = value
            if (isFreshMoreSubMenuView) {
                updateMoreSubMenuView()
            }
        }

    /**
     * 装饰器主题
     */
    internal var theme: MenuDecorationTheme = sectionPage.pageViewModel.pageManagement.pageTheme.value.menuDecorationTheme
        private set

    /**
     * 是否在半沉浸模式
     */
    private val inHalfImmersiveTheme get() = theme == MenuDecorationTheme.HalfImmersive

    /**
     * 顶部菜单实现
     */
    private var topToolbar: COUIToolbar? = null

    /**
     * 子菜单生成器：“更多”
     */
    private val moreMenuCreator = PhotoSubMenuCreator(
        parentMenuItem = R.id.action_more
    )

    /**
     * 当前大图标题
     */
    private var currentTitle: PhotoMenuTitle? = null

    /**
     * ”更多“ 被点击时弹出的菜单
     */
    private var moreSubMenuView: COUIPopupListWindow? = null

    /**
     * 上一个mediaItem的菜单的状态记录
     * key = menuItem.itemId
     * value = MenuItemState
     */
    private val lastMenuItemStatesMap: HashMap<Int, MenuItemState?> = HashMap()

    /**
     * [topToolbar] navigationIcon 的缓存，如果是同一个资源 id，没必要重复加载，复用即可
     */
    private var navigationIconCache: NavigationIconCache? = null

    /**
     * Menu的图标加载任务
     *
     * key : menuItem的action id
     *
     * value : AsyncObj异步加载任务
     */
    private val runningPrepareMenuJobs = mutableMapOf<Int, AsyncObj<Drawable?>>()

    /**
     * 当前 [activity] 是否展示在默认 [Display.DEFAULT_DISPLAY] 上
     */
    private val isShowedOnDefaultDisplay: Boolean
        get() = sectionPage.pageViewModel.inputArguments.features.value?.isShowedOnDefaultDisplay ?: true

    /**
     * Menu 的图标缓存
     *
     * key : Resource id
     *
     * value : Drawable
     */
    private val menuIconCaches: MutableMap<Int, Drawable> = ConcurrentHashMap()

    /**
     * 修改过大小的 Drawable 的缓存
     *
     * key : ScaledDrawableKey 含 resId 和 大小
     *
     * value : Drawable
     */
    private val sizedDrawableCaches: MutableMap<SizedDrawableKey, Drawable> = ConcurrentHashMap()

    /**
     * dimension 资源的缓存
     * key：DimenRes
     * value: Px
     */
    private val dimensResources: ResourcesCaches<Int, Int> = ResourcesCaches {
        sectionPage.pageContext?.resources?.getDimensionPixelSize(it)
    }

    /**
     * 颜色资源的缓存
     * key：ColorRes
     * value: ColorInt
     */
    private val colorResources: ResourcesCaches<Int, Int> = ResourcesCaches {
        sectionPage.pageContext?.getColor(it)
    }

    /**
     * dimension 资源的缓存
     * key：ColorRes（selector）
     * value: ColorStateList
     */
    private val colorStateListResources: ResourcesCaches<Int, ColorStateList> = ResourcesCaches {
        sectionPage.pageContext?.getColorStateList(it)
    }

    /**
     * 菜单创建/刷新完成后的回调列表。
     * 顶部菜单初进大图、旋转屏幕后，都会重新创建、刷新菜单项。
     * 如果有逻辑需要菜单项ready，请将逻辑注册到此列表中，待菜单刷新后统一在主线程回调。
     *
     * 滞留栈式设计，执行即弹出，不会再次被执行。
     * @see onTopMenuPrepared
     */
    private val onMenuPreparedCallbacks: MutableList<(() -> Unit)> = mutableListOf()

    // 展示水印角标的菜单项
    private var showWatermarkCornerMenuItem: MenuItem? = null

    /**
     * Menu 菜单项Icon对应MenuAnimateView集合
     *
     * key : actionId
     *
     * value : MenuItem 对应地Icon地MenuAnimateView
     */
    private val menuItemAnimationViewMap = ConcurrentHashMap<Int, MenuAnimateView>()

    /**
     * menuItem 的 actionView，倒序，index 0 为最右边的图标
     */
    private val actionViews: MutableList<MenuActionView> = mutableListOf()

    /**
     * 记录 menuItem 图标显示后需要调用的回调，
     * 因每个 menuItem 只会走一次显示动画，多次执行会走最后一次的，所以在这里将回调收集起来，动画完成后统一一起回调。
     */
    private val onMenuItemShownCallbacks: MutableMap<Int, MutableList<() -> Unit>> = ConcurrentHashMap()

    /**
     * 当前菜单使用的是否是暗色主题
     */
    private var currentPopupWindowNightMode: Boolean? = null

    /**
     * 菜单栏按键能否被点击，true为可以点击，false为不可以点击
     */
    var canItemClick: Boolean = true

    init {
        photoTopMenu.adapter = this
    }

    override fun onTopMenuLayout(menuView: COUIToolbar) {
        /**
         * 适配规则：
         * - 左侧：
         *     有左侧导航栏： 规避导航栏；
         *     无左侧导航栏： 根据情况决定是否规避屏幕打孔。
         * - 右侧：
         *     有右侧导航栏： 规避导航栏；
         *     无右侧导航栏： 根据情况觉得是否规避屏幕打孔。
         *
         *  屏幕打孔规避规则：
         *  当是 横屏 && 不是分屏时，主动规避屏幕打孔，否则不规避（规避状态栏时会自动规避打孔）
         */
        val horizontalPadding =
            if (sectionPage.pageInstance.isLandscape() && sectionPage.pageInstance.isInMultiWindow().not()) {
                sectionPage.pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_top_menu_horizontal_padding_in_landscape_mode)
            } else 0
        ((menuView.parent) as? ViewGroup)?.updatePadding(
            left = sectionPage.pageInstance.leftNaviBarHeight().takeIf { it != 0 } ?: horizontalPadding,
            right = sectionPage.pageInstance.rightNaviBarHeight().takeIf { it != 0 } ?: horizontalPadding,
            top = if (sectionPage.pageInstance.couldShowSystemBar()) {
                sectionPage.pageInstance.statusBarHeight()
            } else 0
        )
        // 需设置为false，否则设置的 padding 会无效
        menuView.setUseResponsivePadding(false)

        /**
         * 如果当前[activity]不在默认[Display.DEFAULT_DISPLAY]上显示
         * 适配外屏Toolbar的间距
         */
        if (isShowedOnDefaultDisplay.not()) {
            menuView.updatePadding(
                top = sectionPage.pageInstance.resources.getDimensionPixelSize(
                    R.dimen.concisephoto_menu_toolbar_padding_top
                )
            )
        }
        ((menuView.parent) as? ViewGroup)?.post {
            val measuredHeight = ((menuView.parent) as? ViewGroup)?.measuredHeight
            if (measuredHeight != null) {
                sectionPage.pageViewModel.menuControl.updateTopMenuHeight(measuredHeight)
            }
        }
    }

    /**
     *菜单颜色变化监听
     *不是菜单主题颜色变化，临时动画过渡需要更改颜色，事件是成对的，更改后需要依赖后续事件恢复
     */
    internal fun changeMenuColorByEvent(event: PhotoPageAnimationEvent, isPhotoTopBelowMenu: Boolean) {
        if (isPhotoTopBelowMenu.not()) {
            return
        }

        val menuView = ((this.topToolbar) as? ViewGroup).getOrLog("[changeMenuColorByEvent] topToolbar is null") ?: return
        when (event) {
            PhotoPageAnimationEvent.ANIMATION_START -> ((menuView.parent) as? ViewGroup)?.setBackgroundColor(Color.TRANSPARENT)

            PhotoPageAnimationEvent.ANIMATION_END -> {
                colorResources[theme.background]?.let {
                    ((menuView.parent) as? ViewGroup)?.setBackgroundColor(it)
                }
            }
        }
    }

    override fun onTopMenuCreated(menuView: COUIToolbar) {
        this.topToolbar = menuView
        setToolBarNavigationBehavior(menuView)
        subscribeLiveDataFromViewModel()
    }

    override fun onInflateTopMenu() = sectionPage.pageViewModel.inputArguments.menuPresent.value.let { menuState ->
        if (isShowAllMenuItems() && menuState != null) {
            listOf(menuState.topMenuResId, menuState.bottomMenuResId)
        } else {
            listOf(menuState?.topMenuResId ?: Resources.ID_NULL)
        }
    }

    override fun onPrepareTopMenu(menuView: COUIToolbar) {
        /**
         * 如果当前[activity]不在默认[Display.DEFAULT_DISPLAY]上显示，设置AppBarLayout背景为透明
         */
        if (isShowedOnDefaultDisplay.not()) {
            ((menuView.parent) as? ViewGroup)?.setBackgroundResource(android.R.color.transparent)
        } else {
            val isInDetailsMode = sectionPage.pageViewModel.details.isInDetailsMode.value == true
            if (isInDetailsMode && (shouldUpdateTopMenuTheme())
            ) {
                val color = menuView.context.getColor(R.color.photopage_menu_detail_display_background_color)
                ((menuView.parent) as? ViewGroup)?.setBackgroundColor(color)
            } else {
                colorResources[theme.background]?.let {
                    ((menuView.parent) as? ViewGroup)?.setBackgroundColor(it)
                }
            }
        }

        val context = menuView.context

        menuView.updatePaddingRelative(
            start = dimensResources[theme.topMenuExtraPaddingStart, AppConstants.Number.NUMBER_0],
            end = dimensResources[theme.topMenuExtraPaddingEnd, AppConstants.Number.NUMBER_0]
        )

        dimensResources[theme.topMenuTitleMargin, AppConstants.Number.NUMBER_0].let {
            menuView.titleMarginStart = it
            menuView.titleMarginEnd = it
        }

        menuView.setTitleTextAppearance(context, theme.topMenuTitleAppearance)
        colorResources[theme.titleColor]?.let(menuView::setTitleTextColor)
        menuView.setSubtitleTextAppearance(context, theme.topMenuSubtitleAppearance)
        colorResources[theme.subTitleColor]?.let(menuView::setSubtitleTextColor)

        setupNavigationIcon(sectionPage.pageViewModel.menuControl.photoTitle.value)

        onPrepareTopMenu(menuView.context, menuView.menu)
    }

    /**
     * 是否需要更新topMenu的样式
     *
     */
    private fun shouldUpdateTopMenuTheme(): Boolean {
        val isMiddleAndLargeScreen = topToolbar?.context?.let { context ->
            ScreenUtils.isMiddleAndLargeScreen(context)
        } ?: run {
            GLog.d(TAG, LogFlag.DL) {
                "[shouldUpdateTopMenuTheme] return, because topToolbar or context is null"
            }
            false
        }
        // 平板中大屏(是平板，但不是中大屏，如分屏模式下，宽度不足则表现和竖屏手机一致)
        val isTabletLargeScreen = sectionPage.pageInstance.isTablet && isMiddleAndLargeScreen
        // 折叠展开中大屏(满足三个条件才是折叠展开的中大屏，isLargeScreen为展开，isMiddleAndLargeScreen为尺寸判断中大屏)
        val isFoldLargeScreen = sectionPage.pageInstance.isFold && sectionPage.pageInstance.isLargeScreen() && isMiddleAndLargeScreen
        val smallScreen = isTabletLargeScreen.not() && isFoldLargeScreen.not()

        //是否显示顶部菜单栏，true为展示，false不展示;当view处于alpha-》0.0f动效时，不修改颜色
        val shouldUpdateTopMenuTheme = (smallScreen && sectionPage.pageViewModel.details.isDetailModeByTransition()).not()
        return shouldUpdateTopMenuTheme
    }

    @Suppress("LongMethod")
    private fun onPrepareTopMenu(context: Context, menu: Menu) = GTrace.trace("$TAG - onPrepareTopMenu") {
        if ((currentMenuState?.isMenuItemDataValid != true) || (topToolbar == null)) {
            GLog.d(TAG) { "[onPrepareTopMenu] return, because currentMenuState isMenuItemDataValid is not true" }
            return@trace
        }

        val theme = this.theme

        // 如果 visible 的没有 add ,就 add 一下
        currentMenuState?.menuItemStates?.forEach { state ->
            if (state.value.isVisible && menu.findItem(state.key) == null) {
                menu.addAction(state.key)
            }
        }

        // 将 actionView 与可见的 menuItem 进行绑定
        prepareAndBindActionViews(context, menu)

        menu.forEach { menuItem ->
            val currentMenuItemState = currentMenuState?.menuItemStates?.get(menuItem.itemId)
            val lastMenuItemState = lastMenuItemStatesMap[menuItem.itemId]
            if (currentMenuItemState != null) {
                /*
                 * 需要在此处主动显示菜单项，
                 * 因为顶部菜单中的全部菜单项（除占位符），默认均为不可见
                 */
                if (currentMenuItemState.isVisible) {
                    menuItem.isVisible = true
                } else {
                    hideMenuItem(menuItem)
                    return@forEach
                }

                // 启用/禁用菜单项
                if (menuItem.isEnabled != currentMenuItemState.isEnabled) {
                    menuItem.isEnabled = currentMenuItemState.isEnabled
                }

                if (menuItem.actionView?.isEnabled != currentMenuItemState.isEnabled) {
                    menuItem.actionView?.isEnabled = currentMenuItemState.isEnabled
                }

                (menuItem.actionView as? MenuActionView)?.let { actionView ->
                    actionView.shouldGrayDisplay = currentMenuItemState.isGrayDisplay
                }

                if (currentMenuItemState.tips != lastMenuItemState?.tips) {
                    /**
                     * 更新提示
                     * - 数字/红点提示： 每个菜单项都可能有红点提示，且允许存在多个。
                     * - 文字提示： 只允许同时存在一个文字提示。
                     */
                    addOnMenuShownCallback(menuItem) {
                        menuItem.updateTips(currentMenuItemState.tips)

                        // 记录需要展示限定水印角标的菜单项
                        val currentMenuItemTips = currentMenuItemState.tips as? PhotoMenuItemTips.ItemIconTips
                        if (currentMenuItemTips?.type == IconTipType.RESTRICT_WATERMARK) {
                            showWatermarkCornerMenuItem = menuItem
                        }
                        textTips.invalidate()
                    }
                }

                //动画 或者 drawable 资源更改
                configureMenuItemDisplay(
                    menuItem,
                    theme,
                    currentMenuItemState,
                    lastMenuItemState
                ) {
                    val shouldExecuteCallbacks = (menuItem.actionView as? MenuActionView)?.isNotEmpty() != false
                    // 如果actionView为MenuActionView时，确保他包含有子view 的时候才执行回调
                    if (shouldExecuteCallbacks) {
                        // 符合条件时才移除并执行回调
                        onMenuItemShownCallbacks.remove(menuItem.itemId)?.forEach { it.invoke() }
                    }
                }
                if (menuItem.title == context.getString(com.oplus.gallery.basebiz.R.string.common_select)) {
                    setTalkBack(currentMenuItemState)
                }
            } else {
                if (menuItem.groupId == R.id.group_tab) {
                    // groupId 为 group_tab 时，为底部菜单项，禁用而非隐藏
                    if (menuItem.isVisible.not()) {
                        menuItem.isVisible = true
                    }
                    if (menuItem.isEnabled) {
                        menuItem.isEnabled = false
                    }

                    if (menuItem.actionView?.isEnabled == true) {
                        menuItem.actionView?.isEnabled = false
                    }

                    //这里禁用的menu也需要根据主题设置对应的icon
                    sectionPage.pageViewModel.menuControl.queryMenuIconWithActionId(menuItem.itemId)?.let {
                        if (it is MenuIcon.Static) {
                            updateMenuIconDrawable(menuItem, it.getIconRes(theme)) {
                                // 移除全部回调，并调用
                                onMenuItemShownCallbacks.remove(menuItem.itemId)?.forEach {
                                    it.invoke()
                                }
                            }
                        } else {
                            // 目前此处不会返回其他类型，添加 log 为后续变更做提醒。
                            GLog.w(TAG, LogFlag.DL) { "[onPrepareBottomMenu] unimplemented for MenuIcon: ${it::class.simpleName}" }
                        }
                    }
                } else {
                    // 顶部菜单项，隐藏不支持的菜单项
                    hideMenuItem(menuItem)
                }
                /**
                 * 菜单项不存在或不支持（isEnable=false）时，也应该隐藏所有提示
                 * mark by zhuangweihao:
                 * hideTips方法会调用getIcon方法加载drawable资源，TopMenu xml布局中的图标都会都遍历一遍，导致很耗时
                 * 添加isVisible判断，不显示的图标不需要进行对应操作
                 */
                if (menuItem.isVisible) {
                    menuItem.hideTips()
                }
            }
            val menuItemView = menuItem.actionView ?: topToolbar?.findViewById<View>(menuItem.itemId)

            menuItem.actionView?.setOnClickListener {
                onItemClick(menuItem)
            }
            menuItemView?.setOnLongClickListener {
                currentMenuItemState?.isLongPressed = true
                true
            }
            menuItemView?.setOnTouchListener(object : View.OnTouchListener {
                private var downX = 0f
                private var downY = 0f
                private val touchSlop: Int = ViewConfiguration.get(menuItemView.context).scaledTouchSlop

                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    val isLongPressed = currentMenuItemState?.isLongPressed ?: false
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            downX = event.x
                            downY = event.y
                        }

                        MotionEvent.ACTION_MOVE -> {
                            // 与按下时的距离 超过阈值则认为滑动了，就取消自定义长按的检测
                            val distance = Math2DUtil.distance(downX, downY, event.x, event.y)
                            if (distance >= touchSlop) {
                                currentMenuItemState?.isLongPressed = false
                            }
                        }

                        MotionEvent.ACTION_UP -> {
                            if (isLongPressed) {
                                onItemLongClickUp(menuItem.itemId)
                                currentMenuItemState?.isLongPressed = false
                            }
                        }

                        MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_OUTSIDE -> currentMenuItemState?.isLongPressed = false
                    }
                    return false
                }
            })

            lastMenuItemStatesMap[menuItem.itemId] = currentMenuItemState?.also {
                it.iconTheme = theme
            }
        }
    }

    /**
     * 隐藏水印的角标
     */
    fun hideWatermarkTip() {
        showWatermarkCornerMenuItem ?: return
        showWatermarkCornerMenuItem?.hideIconTips()
        showWatermarkCornerMenuItem = null
    }

    /**
     * 设置talkBack无障碍模式朗读的控件类型以及选中状态
     */
    private fun setTalkBack(currentMenuItemState: MenuItemState) {
        (topToolbar?.findViewById(R.id.action_preview_check) as? MenuActionView)?.apply {
            accessibilityDelegate = object : View.AccessibilityDelegate() {
                override fun onInitializeAccessibilityNodeInfo(host: View, info: AccessibilityNodeInfo) {
                    super.onInitializeAccessibilityNodeInfo(host, info)
                    info.className = CompoundButton::class.java.name
                    info.stateDescription = if (currentMenuItemState.isChecked) {
                        resources.getString(com.oplus.gallery.basebiz.R.string.base_talkback_checkbox_checked)
                    } else {
                        resources.getString(com.oplus.gallery.basebiz.R.string.base_talkback_checkbox_uncheck)
                    }
                }
            }
        }
    }

    /**
     * 按照当前可见 menuItem 的数量准备对应的 actionViews，
     * 并将 actionView 与 menuItem 进行绑定。
     */
    @SuppressLint("RestrictedApi")
    private fun prepareAndBindActionViews(context: Context, menu: Menu) {
        // 重新计算 menuItem 的可见性，头插法，index 0 为最右边的图标（优先级最高）
        val visibleMenuItems = LinkedList<MenuItem>()
        menu.forEach { menuItem ->
            currentMenuState?.menuItemStates?.get(menuItem.itemId)?.let { state ->
                if (state.isVisible) {
                    visibleMenuItems.addFirst(menuItem)
                }
            } ?: let {
                if (menuItem.groupId == R.id.group_tab) {
                    visibleMenuItems.addFirst(menuItem)
                }
            }
        }

        // 补充 ActionView，以及与 menuItem 进行绑定
        GTrace.trace("$TAG - prepareAndBindActionViews - visibleMenuItems") {
            // 待全部都 bind 后再通知 menu 有变化，不然每次都会有耗时操作
            (menu as? MenuBuilder)?.stopDispatchingItemsChanged()
            visibleMenuItems.forEachIndexed { idx, visibleItem ->
                val actionView = if (idx < actionViews.size) {
                    actionViews[idx]
                } else {
                    val menuActionView = MenuActionView(context).also { actionViews.add(it) }
                    currentMenuState?.menuItemStates?.get(visibleItem.itemId)?.let { state ->
                        /*
                           背景:
                           这里创建的 MenuActionView,会替换 xml 解析并 inflate 出来的那个,
                           刚替换的时候,此处的 MenuActionView 还没有对应的 icon ,所以旧的 icon 会消失,直到调用了 updateIcon 设置新icon
                           解决方案:
                           在创建 MenuActionView 的时候,就 updateIcon ,这样替换 MenuActionView 的时候不会闪一下(仅 action_more 做此处理)
                        */
                        if (state.id == R.id.action_more) {
                            menuActionView.updateIcon(
                                menuItem = visibleItem,
                                iconKey = R.drawable.photopage_ic_menu_more_selector,
                                icon = visibleItem.icon,
                                isHalfImmersive = false,
                                forceAnimate = false,
                                forceSkipAnimate = true,
                                animationDelayTime = 0L
                            ) {}
                        }
                    }
                    menuActionView
                }
                actionView.bindTo(visibleItem)
            }
            (menu as? MenuBuilder)?.startDispatchingItemsChanged()
        }
    }

    private fun hideMenuItem(menuItem: MenuItem) {
        (menuItem.actionView as? MenuActionView)?.let {
            // 多余的 menuItem 需先做消失动画再隐藏
            it.removeIcon {
                // 快速反复切图时，此时可能已经变成了需要显示
                val currentState = currentMenuState?.menuItemStates?.get(menuItem.itemId)
                if (currentState?.isVisible != true) {
                    menuItem.isVisible = false
                }

                // 此 actionView 与其他 menuItem 绑定了，则无需移除
                val canRemoveByItemId = (it.currentMenuItemId == null) || (menuItem.itemId == it.currentMenuItemId)
                if (canRemoveByItemId) {
                    it.unbind()
                    actionViews.remove(it)
                }
            }
        } ?: let {
            menuItem.setVisible(false)
        }
    }

    /**
     * 根据主题更新图标到 ActionView 中
     * @param menuItem 当前操作的 menuItem
     * @param iconResId 图标资源 id
     * @param icon 图标资源 id对应的drawable文件
     * @param onIconShown 图标显示后回调
     */
    private fun updateIconViewByTheme(
        menuItem: MenuItem,
        iconResId: Int,
        icon: Drawable?,
        onIconShown: () -> Unit,
    ) {
        val isInDetailsMode = sectionPage.pageViewModel.details.isInDetailsMode.value == true
        val shouldUseHalfImmersive = inHalfImmersiveTheme && !isInDetailsMode
        val shouldAnimateWhenRemoved = sectionPage.pageViewModel.pageManagement.shouldAnimateWhenRemoved.value == true
        val animationDelayTime = if (shouldAnimateWhenRemoved) REMOVED_DELAY_ALPHA_SHOW_TIME else 0L
        // 初次进入大图时，不对图标做动画
        val shouldSkipAnim = sectionPage.pageViewModel.dataLoading.diffedFocusViewData.value?.oldItem == null
        (menuItem.actionView as? MenuActionView)?.updateIcon(
            menuItem = menuItem,
            iconKey = "$iconResId",
            icon = icon,
            isHalfImmersive = shouldUseHalfImmersive,
            forceAnimate = shouldAnimateWhenRemoved,
            animationDelayTime = animationDelayTime,
            onIconShown = onIconShown,
            forceSkipAnimate = shouldSkipAnim
        )
    }

    private fun resizeDrawable(context: Context, resId: Int, drawable: Drawable, targetSize: Int): Drawable {
        val key = SizedDrawableKey(resId = resId, size = targetSize)
        return sizedDrawableCaches[key] ?: resizeDrawable(context, drawable, targetSize).also {
            sizedDrawableCaches[key] = it
        }
    }

    private fun resizeDrawable(context: Context, drawable: Drawable, targetSize: Int): Drawable {
        return if (drawable is StateListDrawable) {
            StateListDrawable().apply {
                for (i in 0 until drawable.stateCount) {
                    val state = drawable.getStateSet(i)
                    drawable.getStateDrawable(i)?.let { innerDrawable ->
                        addState(state, resizeDrawable(context, innerDrawable, targetSize))
                    }
                }
            }
        } else {
            if (drawable is BitmapDrawable) {
                // 此方法会复用 BitmapDrawable 内的 BitmapState，从而共享 mBaseAlpha 属性，保持透明度一致。
                val newDrawable = drawable.constantState?.newDrawable(context.resources)
                if (newDrawable is BitmapDrawable) {
                    newDrawable.bitmap = drawable.bitmap?.scale(targetSize, targetSize)
                    newDrawable.bitmap?.density?.let { newDrawable.setTargetDensity(it) } ?: run {
                        GLog.w(TAG, LogFlag.DL) { "<resizeDrawable> density is null! set density failed!" }
                    }
                    return newDrawable
                }
            }

            drawable.toBitmap(targetSize, targetSize).apply {
                density = context.resources.displayMetrics.densityDpi
            }.toDrawable(context.resources)
        }
    }

    /**
     * 此方法使用[AsyncObj]加载drawable，如果同时异步加载多个drawable可能存在多线程之间资源锁竞争的问题
     * 导致性能下降，如果存在这种情况考虑更改此方法的实现
     */
    private fun asyncLoadDrawable(iconRes: Int): AsyncObj<Drawable?> = AsyncObj(sectionPage.pageViewModel) {
        menuIconCaches[iconRes] ?: sectionPage.pageContext?.let { context ->
            AppCompatResources.getDrawable(context, iconRes)?.also { drawable ->
                menuIconCaches[iconRes] = drawable
            }
        }
    }

    override fun onTopMenuPrepared() {
        while (onMenuPreparedCallbacks.isEmpty().not()) {
            onMenuPreparedCallbacks.ktRemoveLast().invoke()
        }
    }

    override fun onItemClick(menuItem: MenuItem) {
        if (canItemClick.not()) {
            GLog.w(TAG, LogFlag.DL) { "[onItemClick] item clicked, but canItemClick is false" }
            return
        }
        if (DoubleClickUtils.isFastDoubleClick()
            && (sectionPage.pageViewModel.menuControl.shouldSuppressedItems(menuItem.itemId).not())
        ) {
            return
        }
        if (menuItem.itemId == R.id.action_more) {
            showMoreSubMenu()
            // 埋点： 更多菜单被点击。只要被点击就会触发
            sectionPage.pageViewModel.track.trackPictureClick(value = MENU_ITEM_MORE)
        } else {
            onItemClicked(menuItem.itemId)
        }
    }

    private fun showMoreSubMenu() {
        currentMenuState?.let { state ->
            val topBar = topToolbar ?: return
            moreMenuCreator.createSubMenu(
                presentMenuItems = moreMenuCreator.createAllMoreMenuItems(sectionPage.pageViewModel),
                menuState = state
            ).let { subMenu ->
                val anchorView = topBar.findViewById<View?>(subMenu.parentMenuItem) ?: return
                val subMenuMargin = topBar.resources.getDimensionPixelSize(R.dimen.photopage_sub_menu_horizontal_margin_for_top_menu)
                val currentScreenHeight = sectionPage.pageInstance.getCurrentAppUiConfig().windowHeight.current

                /**
                 * 更多菜单被获取可用空间后，需要和屏幕底部有一定的 margin。
                 * 但 PopupWindow 不支持设置 margin，因此使用 maxHeight 进行限制。
                 *
                 *  最大高 = 屏幕高 - 顶部菜单高  - 期望的间距
                 */

                ensureMoreSubMenuView(topBar.context).show(
                    anchor = anchorView,
                    items = subMenu.menuItems.map { it.toPopupListWindowItem(topBar.resources) },
                    onItemClick = { position, _ ->
                        onItemClicked(subMenu.menuItems[position].action)
                        moreSubMenuView?.dismiss()
                    }
                )
            }
        }
    }

    /**
     * 获取展示更多菜单的视图
     */
    private fun ensureMoreSubMenuView(context: Context): COUIPopupListWindow {
        fun createPopWindow(context: Context): COUIPopupListWindow {
            return COUIPopupListWindow(context).also { popWindow ->
                // 开启模糊背景效果
                popWindow.setUseBackgroundBlur(true)
                // 禁用弹窗外区域的点击事件
                popWindow.setDismissTouchOutside(true)
                // 关闭视窗发生变化是自动隐藏更多菜单功能,原因:在小屏幕横屏情况下,点击更多视窗大小会发生变化,默认会自动隐藏更多菜单,导致更多菜单无法显示问题;
                popWindow.setDismissWhenWindowSizeChange(false)
                // 启用滑动条
                popWindow.listView.isVerticalScrollBarEnabled = true
            }
        }

        // app 亮暗色变更，则重新创建更多菜单
        val isNightMode = COUIDarkModeUtil.isNightMode(context)
        if (isNightMode != currentPopupWindowNightMode) {
            moreSubMenuView?.setOnItemClickListener(null)
            moreSubMenuView?.setSubMenuClickListener(null)
            moreSubMenuView?.setOnDismissListener(null)

            moreSubMenuView = createPopWindow(context)
            currentPopupWindowNightMode = isNightMode
        }

        return moreSubMenuView ?: createPopWindow(context).also {
            moreSubMenuView = it
        }
    }

    override fun invalidateMenu(isResetMenuStatus: Boolean) {
        if (isResetMenuStatus) {
            GLog.d(TAG) { "invalidateMenu isResetMenuStatus" }
            lastMenuItemStatesMap.clear()
        }
        photoTopMenu.invalidateMenu()
        GLog.d(TAG) { "[invalidateMenu] currentTitle = ${currentTitle?.mediaKey}" }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        // 从PhotoContainerSection中监听onAppUiStateChanged，当系统配置发生变化后，topMenu需要清除MenuItemStates
        invalidateMenu(true)
        photoMenuExecutor.onAppUiStateChanged(config)
        //屏幕大小发生变化隐藏更多菜单弹出,避免显示位置不对
        moreSubMenuView?.dismiss()
    }

    override fun show() {
        // 展示时，尝试刷新标题
        GLog.d(TAG) { "[show] currentTitle = ${currentTitle?.mediaKey}" }
        photoTopMenu.show()
    }

    override fun hide() {
        photoTopMenu.hide()
    }

    override fun onMenuCleared() {
        // 内部的 menu 被清空时，表示内部的 MenuItem 及其顺序失效，需将当前的 actionViews 也清空
        clearActionViews()
    }

    override fun onTopMenuDestroyed() {
        lastMenuItemStatesMap.clear()
        onMenuPreparedCallbacks.clear()
        menuIconCaches.clear()
        menuItemAnimationViewMap.forEach {
            it.value.cancelAnimation()
        }
        menuItemAnimationViewMap.clear()
        // 清空所有正在执行的菜单准备任务
        runningPrepareMenuJobs.forEach { _, asyncObj -> asyncObj.destroy() }
        runningPrepareMenuJobs.clear()
        dimensResources.clear()
        colorResources.clear()
        colorStateListResources.clear()
        sizedDrawableCaches.forEach { it.value.recycle() }
        sizedDrawableCaches.clear()
        clearActionViews()
        onMenuItemShownCallbacks.clear()
    }

    private fun clearActionViews() {
        actionViews.forEach { it.unbind() }
        actionViews.clear()
        onMenuItemShownCallbacks.clear()
    }

    /**
     * 更新菜单的主题
     *
     * @param theme 新的主题
     * @param shouldInvalidate 是否需要 invalidate 菜单
     */
    internal fun updateTheme(theme: MenuDecorationTheme, shouldInvalidate: Boolean): Boolean {
        if (this.theme == theme) return false
        if (shouldUpdateTopMenuTheme().not()) return false
        this.theme = theme
        if (shouldInvalidate) {
            invalidateMenu(true)
        }
        return true
    }

    private fun subscribeLiveDataFromViewModel() {
        val viewModel = sectionPage.pageViewModel
        /*
        *  背景： 大图本地加载时间和日期，异步网络加载地点，当网络晚一点到时，有可能会导致标题闪烁。
        *        即主标题先显示时间，而后变为地点，为了防止闪烁，有以下逻辑。
        *  标题更新逻辑：
        *  - 如果当前是一个新的图片的标题(mediaKey发生改变)，立即改变标题。
        *  - 如果图片未更新，保存信息，等用户触发沉浸式后，标题再次显示时更新。
        *  */
        viewModel.menuControl.launch {
            viewModel.menuControl.photoTitle
                .filterNotNull()
                .filter { it.mediaKey != EMPTY_STRING }
                .collectLatest {
                    GLog.i(TAG) { "[updateTitle] new:${it.mediaKey}" }
                    currentTitle = it
                    sectionPage.pageLifecycle.coroutineScope.launch(Dispatchers.AtFrontOfMainQueueDispatcher) {
                        updateTitle(it)
                    }
                }
        }
    }

    /**
     * 传入的逻辑将在菜单创建刷新完成后执行。
     * 执行时机：顶部菜单创建、刷新完成后。业务方需要在顶部菜单创建前调用，否则不会生效
     * @see [onTopMenuPrepared]
     */
    @MainThread
    internal fun doOnMenuPrepared(action: () -> Unit) {
        onMenuPreparedCallbacks.add(action)
    }

    /**
     * 设置顶栏导航键的行为
     */
    private fun setToolBarNavigationBehavior(topToolbar: COUIToolbar) {
        topToolbar.setNavigationOnClickListener {
            sectionPage.pageInstance.activity?.onBackPressed()
        }
    }

    /**
     * 刷新更多菜单
     */
    private fun updateMoreSubMenuView() {
        // 如果”更多“ 被点击时弹出的菜单正在显示，则刷新，这里需先隐藏后显示，直接显示会隐藏当前菜单
        if (moreSubMenuView?.isShowing == true) {
            GLog.d(TAG, LogFlag.DF) { "[updateMoreSubMenuView] moreSubMenuView dismiss and showMoreSubMenu." }
            moreSubMenuView?.dismiss()
            showMoreSubMenu()
        }
    }

    /**
     * 是否刷新更多菜单弹窗:当前仅在系统播放器中打开的id需要刷新更多菜单弹窗
     *
     * @param currentMenuItemStates 当前菜单项状态
     * @param oldMenuItemStates 旧菜单项状态
     * @return 是否需要刷新更多菜单弹窗，true：需要刷新，false：不需要刷新
     */
    private fun isFreshMoreSubMenuView(currentMenuItemStates: Map<Int, MenuItemState>?, oldMenuItemStates: Map<Int, MenuItemState>?): Boolean {
        oldMenuItemStates ?: return false
        currentMenuItemStates ?: return false
        // 获取在系统播放器中打开的action
        val oldMenuItemState = oldMenuItemStates[R.id.action_open_in_system_player]
        val currentMenuItemState = currentMenuItemStates[R.id.action_open_in_system_player]
        // 如果之前显示，后为空或不显示的情况，就刷新”更多“ 被点击时弹出的菜单弹窗
        if ((oldMenuItemState?.isVisible == true) && (currentMenuItemState == null || !currentMenuItemState.isVisible)) {
            return true
        }
        return false
    }

    /**
     * 更新标题
     */
    private fun updateTitle(menuTitle: PhotoMenuTitle) {
        GTrace.trace("$TAG.updateTitle") {
            if (menuTitle.title != topToolbar?.title) {
                GLog.i(TAG) { "[updateTitle] update title , is title empty : ${menuTitle.title.isNullOrEmpty()}" }
                topToolbar?.title = menuTitle.title
            }
            if (menuTitle.subTitle != topToolbar?.subtitle) {
                topToolbar?.subtitle = menuTitle.subTitle
            }
            setupNavigationIcon(menuTitle)
        }
    }

    @MainThread
    private fun setupNavigationIcon(menuTitle: PhotoMenuTitle) {
        val iconId = getIconResFromTitle(menuTitle)

        // 优先获取预加载的缓存
        val context = topToolbar?.context ?: ContextGetter.context
        if (navigationIconCache == null) {
            if (theme == MenuDecorationTheme.Light) {
                val drawable = photoTopMenu.topMenuPreloader.getNavLightIconAsyncObj(context).obj
                if (drawable != null) {
                    navigationIconCache = NavigationIconCache(R.drawable.photopage_ic_menu_back_selector_light, drawable)
                }
            } else {
                val drawable = photoTopMenu.topMenuPreloader.getNavDarkIconAsyncObj(context).obj
                if (drawable != null) {
                    navigationIconCache = NavigationIconCache(R.drawable.photopage_ic_menu_back_selector, drawable)
                }
            }
        }

        val currentNaviIconCache = navigationIconCache

        // 1. 检查resID是否有对应的缓存 & 有效，如果有，则直接使用
        when (iconId) {
            Resources.ID_NULL -> {
                GLog.w(TAG, LogFlag.DL) { "[setupNavigationIcon] iconId is invalid. failed to set navi icon." }
                return
            }

            currentNaviIconCache?.resId -> {
                topToolbar?.let {
                    GLog.w(TAG, LogFlag.DL) { "[setupNavigationIcon] use currentNaviIconCache" }
                    setupNavView(it, currentNaviIconCache)
                }
                return
            }
        }

        // 2. 没有缓存或者缓存失效，则先重置缓存
        navigationIconCache = null

        val isIconIdNotChanged = iconId == currentTitle?.let(::getIconResFromTitle)
        val isIconNotLoaded = iconId != navigationIconCache?.resId
        GLog.d(TAG) { "[setupNavigationIcon] updating, isIconIdNotChanged=$isIconIdNotChanged, isIconNotLoaded=$isIconNotLoaded" }
        // 3. 如果多次执行下面逻辑，会启动多次加载，为增强健壮性和满足性能，添加条件，减少额外刷新
        if (isIconIdNotChanged && isIconNotLoaded) {
            sectionPage.pageLifecycle.coroutineScope.launch(Dispatchers.IO) {
                // 4. 异步加载图标，图标加载会触发 io，在低端机比较耗时，异步加载
                val newNaviIconCache = GTrace.trace("$TAG.NavigationIconCache") {
                    sectionPage.pageContext?.let {
                        AppCompatResources.getDrawable(it, iconId)
                            ?.let { iconDrawable -> NavigationIconCache(iconId, iconDrawable) }
                    }
                } ?: let {
                    GLog.w(TAG) { "[setupNavigationIcon] failed to load naviIcon($iconId)" }
                    return@launch
                }

                sectionPage.pageLifecycle.coroutineScope.launch(Dispatchers.AtFrontOfMainQueueDispatcher) {
                    GTrace.trace("$TAG.setupNavigationIcon") {
                        // 5. 保存缓存，并更新到控件
                        topToolbar?.let { toolBar ->
                            navigationIconCache = newNaviIconCache
                            setupNavView(toolBar, newNaviIconCache)
                        }
                    }
                }
            }
        }
    }

    private fun getIconResFromTitle(menuTitle: PhotoMenuTitle): Int {
        return menuTitle.navigationIcon.let {
            if (it is MenuIcon.Static) {
                it.getIconRes(theme)
            } else {
                Resources.ID_NULL
            }
        }
    }

    /**
     * 更新导航按钮的样式
     */
    private fun setupNavView(toolbar: COUIToolbar, naviIconCache: NavigationIconCache) = GTrace.trace("$TAG.setupNavView") {
        toolbar.navigationIcon = if (inHalfImmersiveTheme) {
            resizeDrawable(toolbar.context, naviIconCache.resId, naviIconCache.resDrawable, ICON_SIZE.toPx)
        } else {
            naviIconCache.resDrawable
        }
        toolbar.findViewById<ImageButton>(com.support.toolbar.R.id.coui_toolbar_back_view)?.apply {
            layoutDirection = if (ResourceUtils.isRTL(context)) View.LAYOUT_DIRECTION_RTL else View.LAYOUT_DIRECTION_LTR
            val targetSize = if (inHalfImmersiveTheme) {
                IMMERSIVE_ICON_VIEW_SIZE.toPx
            } else {
                NORMAL_ICON_VIEW_SIZE.toPx
            }
            if (layoutParams.width != targetSize) {
                layoutParams = layoutParams.apply {
                    width = targetSize
                    height = targetSize
                }
            }

            if (inHalfImmersiveTheme) {
                // MarkedBy linkailong 质感需重新适配
                setBackgroundColor(context.getColor(R.color.photopage_menu_half_immersive_icon_bg))
                setCircularOutline()
            } else {
                removeShapeOutline()

                val rippleDrawable = COUIMaskRippleDrawable(context)
                rippleDrawable.setCircleRippleMask(
                    COUIMaskRippleDrawable.getMaskRippleRadiusByType(context, COUIMaskRippleDrawable.RIPPLE_TYPE_ICON_RADIUS)
                )
                background = rippleDrawable
            }
        } ?: GLog.w(TAG, LogFlag.DL) { "[setUpNavView] nav view not found" }
    }

    /**
     * 更新提示
     */
    private fun MenuItem.updateTips(tips: PhotoMenuItemTips) {
        when (tips) {
            is PhotoMenuItemTips.ItemTipsNumber -> {
                hideIconTips()
                showRedNumberTips(tips)
            }

            is PhotoMenuItemTips.ItemTipsText -> showTextTips(tips)
            // 文字提示不在这里取消，而是所有菜单都没有文本提示时，才会取消。
            is PhotoMenuItemTips.ItemTipsNone -> hideTips()
            is PhotoMenuItemTips.ItemIconTips -> showIconTips(tips)
        }
    }

    /**
     * 隐藏菜单项所有提示
     */
    private fun MenuItem.hideTips() {
        hideNumberTips()
        hideIconTips()
    }

    /**
     * 显示文字提示
     */
    private fun MenuItem.showTextTips(tips: PhotoMenuItemTips.ItemTipsText) {
        textTips.show(this@showTextTips, tips) { id ->
            (actionView as? MenuActionView)?.takeIf {
                // 只有在有图标，且不在消失动画中，且 id 相等时才显示 tips。
                (it.currentContent != null) && (it.currentMenuItemId == id)
            }
        }
    }

    /**
     * 显示红点数字提示
     */
    private fun MenuItem.showRedNumberTips(
        tipStyle: PhotoMenuItemTips.ItemTipsNumber
    ) {
        topToolbar?.setRedDot(itemId, tipStyle.number)
    }

    /**
     * 隐藏数字提示
     */
    private fun MenuItem.hideNumberTips() {
        topToolbar?.setRedDot(itemId, HIDE_RED_DOT)
    }

    /**
     * 在菜单项右上角显示个提示图标
     */
    private fun MenuItem.showIconTips(tips: PhotoMenuItemTips.ItemIconTips) {
        if (tips.type != IconTipType.RESTRICT_WATERMARK) return
        val context = sectionPage.pageContext ?: return
        val tipIcon = tips.tipsDrawable ?: return

        (actionView as? MenuActionView)?.apply {
            if (findViewById<ImageView>(ID_VIEW_ICON_TIP) == null) {
                val view = ImageView(context)
                view.id = ID_VIEW_ICON_TIP

                // 高度固定为18，宽度与父布局一致，角标图片靠右对齐，当图片宽度变化时，高度不变，宽度自适应变化，最宽不会超出父布局宽度
                val fixedHeight = context.resources.getDimensionPixelSize(R.dimen.photopage_top_menu_icon_tips_height)
                val marginTop = context.resources.getDimensionPixelSize(R.dimen.photopage_top_menu_icon_tips_margin_top)
                view.layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, fixedHeight).apply {
                    gravity = Gravity.CENTER_HORIZONTAL or Gravity.TOP
                }
                view.updateMargin(top = marginTop)
                view.scaleType = ImageView.ScaleType.FIT_END
                view.setImageDrawable(tipIcon)
                addView(view)
            }
        }
    }

    /**
     * 隐藏菜单项右上角的提示图标
     */
    private fun MenuItem.hideIconTips() {
        (actionView as? MenuActionView)?.let { menuActionView ->
            menuActionView.findViewById<ImageView>(ID_VIEW_ICON_TIP)?.let { iconView ->
                menuActionView.removeView(iconView)
            }
        }
    }

    /**
     * 将 SubMenuItem 转换为 PopupListWindowItem
     */
    private fun SubMenuItem.toPopupListWindowItem(resources: Resources): PopupListWindowItem {
        val itemTitle = moreMenuCreator.makeMoreMenuItemTitleId(sectionPage.pageViewModel, this)
            .takeIf { it != Resources.ID_NULL }
            ?.let(resources::getString)
            ?: TextUtil.EMPTY_STRING

        return PopupListWindowItem(
            itemTitle = itemTitle,
            isItemEnable = true,
            tipsCount = if (hasTips) PopupListWindowItem.SHOW_TIPS_WITH_RED_DOT else PopupListWindowItem.HIDE_TIPS,
            mGroupId = groupId
        )
    }

    /**
     * 配置菜单项显示内容
     * @param menuItem 菜单项
     * @param currentState 当前菜单状态
     * @param previousState 先前菜单状态（可为null）
     * @param onIconShown 图标显示动画完成后的回调
     */
    private fun configureMenuItemDisplay(
        menuItem: MenuItem,
        theme: MenuDecorationTheme,
        currentState: MenuItemState,
        previousState: MenuItemState?,
        onIconShown: () -> Unit,
    ) {
        val currentIcon = currentState.menuIcon
        val previousIcon = previousState?.menuIcon

        when (currentIcon) {
            is MenuIcon.NULL -> Unit
            // 如果为静态图标，直接更新图标, 这里涉及主题变化，需要更新图标
            is MenuIcon.Static -> {
                configureMenuItemDisplayForStatic(
                    menuItem, currentState, theme, currentIcon, onIconShown, previousIcon, previousState
                )
            }
            // Lottie 动画图标
            is MenuIcon.Lottie -> {
                configureMenuItemDisplayForLottie(
                    menuItem, currentState, theme, currentIcon, previousIcon, previousState, onIconShown
                )
            }
        }
    }

    private fun configureMenuItemDisplayForStatic(
        menuItem: MenuItem,
        currentState: MenuItemState,
        theme: MenuDecorationTheme,
        menuIcon: MenuIcon.Static,
        onIconShown: () -> Unit,
        previousIcon: MenuIcon?,
        previousState: MenuItemState?,
    ) {
        val iconRes = menuIcon.getIconRes(theme)
        val previousIconRes = previousState?.iconTheme?.let { (previousIcon as? MenuIcon.Static)?.getIconRes(it) }

        val isPreNotGray = previousState?.let { it.isEnabled && !it.isGrayDisplay }
        val isCurrentNotGray = currentState.let { it.isEnabled && !it.isGrayDisplay }
        val isGrayChange = isPreNotGray != isCurrentNotGray

        if ((iconRes == previousIconRes) && !isGrayChange) {
            onIconShown.invoke()
            return
        }
        updateMenuIconDrawable(menuItem, iconRes, onIconShown)
    }

    private fun configureMenuItemDisplayForLottie(
        menuItem: MenuItem,
        currentState: MenuItemState,
        theme: MenuDecorationTheme,
        menuIcon: MenuIcon.Lottie,
        previousIcon: MenuIcon?,
        previousState: MenuItemState?,
        onIconShown: () -> Unit,
    ) {
        val currentAni = menuIcon.getAsset(theme)
        val previousAni = previousState?.iconTheme?.let { (previousIcon as? MenuIcon.Lottie)?.getAsset(it) }

        val isPreNotGray = previousState?.let { it.isEnabled && !it.isGrayDisplay }
        val isCurrentNotGray = currentState.let { it.isEnabled && !it.isGrayDisplay }
        val isGrayChange = isPreNotGray != isCurrentNotGray

        // 获取先前是否是选中状态
        val isPreSelected = previousIcon?.let {
            PhotoMenuIconDrawableManager.isSelectStateForMenuIcon(menuItem.itemId, previousIcon)
        } ?: false

        when {
            // 从选中状态回到普通状态，无需开始动画
            isPreSelected -> setupLottieAnimation(menuItem, currentAni, true, onIconShown = onIconShown)
            // 动画没变，但是主题变更了或是否置灰变更了，无需开始动画，MarkedBy linkailong 如果上一个动画还在进行中要如何处理？写此行时资源暂时未区分亮暗色
            (menuIcon == previousIcon) && ((theme != previousState?.iconTheme) || isGrayChange) -> {
                setupLottieAnimation(
                    menuItem, currentAni, true, isOnlyChangeTheme = true, onIconShown = onIconShown
                )
            }
            // 动画变更，重新开始动画
            currentAni != previousAni -> {
                setupLottieAnimation(
                    menuItem, currentAni, false, shouldIconChangeWithAnim = true, onIconShown = onIconShown
                )
            }
            else -> onIconShown.invoke()
        }
    }

    /**
     * 给MenuIcon设置drawable
     */
    private fun updateMenuIconDrawable(
        menuItem: MenuItem,
        drawableResId: Int,
        onContentShown: () -> Unit,
    ) {
        if (drawableResId == Resources.ID_NULL) {
            GLog.d(TAG, LogFlag.DL) { "[updateMenuIconDrawable] drawableResId ID_NULL" }
            return
        }
        val asyncLoadDrawable = asyncLoadDrawable(drawableResId)
        runningPrepareMenuJobs[menuItem.itemId]?.destroy()
        runningPrepareMenuJobs[menuItem.itemId] = asyncLoadDrawable
        asyncLoadDrawable.getIt { cacheDrawable ->
            runningPrepareMenuJobs.remove(menuItem.itemId)
            cacheDrawable?.let(menuItem::setIcon) ?: let {
                menuItem.setIcon(drawableResId)
            }
            updateIconViewByTheme(menuItem, drawableResId, menuItem.icon, onContentShown)
        }
    }

    /**
     * 配置动画参数
     * @param menuItem 菜单项
     * @param animationRes 动画资源
     * @param isScrollToMaxFrame 是否滚动到最大帧
     * @param shouldIconChangeWithAnim icon 切换是否需要动画
     * @param isOnlyChangeTheme 是否只更改主题
     * @param onIconShown 图标显示动画完成后的回调
     */
    @MainThread
    private fun setupLottieAnimation(
        menuItem: MenuItem,
        animationRes: String,
        isScrollToMaxFrame: Boolean,
        shouldIconChangeWithAnim: Boolean = false,
        isOnlyChangeTheme: Boolean = false,
        onIconShown: () -> Unit,
    ) {
        getOrCreateAnimationView(menuItem)?.apply {
            GLog.d(TAG, LogFlag.DL) { "[setupLottieAnimation] isScrollToMaxFrame$isScrollToMaxFrame " }
            runningPrepareMenuJobs.remove(menuItem.itemId)?.destroy()
            // 初次进入大图时，不对图标做动画
            val shouldSkipAnim = sectionPage.pageViewModel.dataLoading.diffedFocusViewData.value?.oldItem == null
            // 设置到 actionView 中
            (menuItem.actionView as? MenuActionView)?.updateIcon(
                menuItem = menuItem,
                iconKey = animationRes,
                icon = this,
                isHalfImmersive = inHalfImmersiveTheme,
                shouldIconChangeWithAnim = shouldIconChangeWithAnim,
                onIconShown = onIconShown,
                forceSkipAnimate = shouldSkipAnim
            )
            if (isOnlyChangeTheme) {
                return
            }

            setAnimation(animationRes)
            if (isScrollToMaxFrame) {
                scrollToMaxFrame()
            } else {
                playAnimation()
            }
        }
    }

    /**
     * 获取菜单项的EffectiveAnimationView（用于动效展示）
     * @param menuItem 底部菜单项
     * @return EffectiveAnimationView对象
     */
    @MainThread
    private fun getOrCreateAnimationView(menuItem: MenuItem): MenuAnimateView? {
        val pageContext = sectionPage.pageContext
        if (pageContext == null) {
            GLog.e(TAG, LogFlag.DL) { "[getOrCreateAnimationView] sectionPage.pageContext is null " }
            return null
        }
        val menuAnimateView = menuItemAnimationViewMap.getOrPut(menuItem.itemId) {
            MenuAnimateView(pageContext)
        }
        menuAnimateView.updateAnimationSize(menuItem, inHalfImmersiveTheme)
        return menuAnimateView
    }

    private fun addOnMenuShownCallback(menuItem: MenuItem, callback: () -> Unit) {
        val callbacks = onMenuItemShownCallbacks[menuItem.itemId] ?: ArrayList()
        callbacks.add(callback)
        onMenuItemShownCallbacks[menuItem.itemId] = callbacks
    }

    /**
     * 添加 Action
     * order 规则如下:
     * 给所有 menu 按类型划分 order (showAsAction="always" 的情况下 order 小的排在左边):
     * group_actionbar 动态添加: 100-199
     * group_actionbar xml: 300-399
     * group_tab 动态添加: 700-799
     * group_tab xml: 800-899
     * action_more: 999
     *
     * marked by Mountain:这里仅做临时处理,建议田主对所有 action 统一规划一个 order ,定义在一个配置文件中
     * marked by linkailong：菜单这块后续可以改为全部通过代码添加，不使用 xml inflate，每个菜单项单独定义 order（如用 enum 类确保顺序）。
     */
    private fun Menu.addAction(itemId: Int) {
        when (itemId) {
            R.id.placeholder -> addActionPlaceHolder()
            R.id.action_cloud_download -> addActionCloudDownload()
            R.id.action_group_photo -> addActionGroupPhoto()
            R.id.action_portrait_blur -> addActionPortraitBlur()
            R.id.action_olive_photo -> addActionOlivePhoto()
            R.id.action_cshot -> addActionCshot()
            R.id.action_enhancetext -> addActionEnhanceText()
            R.id.action_aiidphoto -> addActionAiIdPhoto()
            R.id.action_getvideoframe -> addActionGetVideoFrame()
            R.id.action_screencast -> addActionScreenCast()
            R.id.action_self_splitting -> addActionSelfSplitting()
            else -> GLog.e(TAG, "[addAction] invalid id $itemId , skip add action")
        }
    }

    private fun Menu.addActionSelfSplitting() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_self_splitting,
            ORDER_SELF_SPLITTING, null
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_selfsplit_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_self_splitting")
    }

    private fun Menu.addActionScreenCast() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_screencast,
            ORDER_SCREEN_CAST, R.string.photopage_photomenu_talkback_action_screencast
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_screencast_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_screencast")
    }

    private fun Menu.addActionGetVideoFrame() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_getvideoframe,
            ORDER_GET_VIDEO_FRAME, R.string.photopage_photomenu_talkback_capture_video_frame
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_getvideoframe_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_getvideoframe")
    }

    private fun Menu.addActionAiIdPhoto() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_aiidphoto,
            ORDER_AI_ID_PHOTO, R.string.photopage_photomenu_talkback_ai_id_photo_edit
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_aiidphoto_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_aiidphoto")
    }

    private fun Menu.addActionEnhanceText() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_enhancetext,
            ORDER_ENHANCE_TEXT, R.string.photopage_photomenu_talkback_super_text_edit
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_enhancetext_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_enhancetext")
    }

    private fun Menu.addActionCshot() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_cshot,
            ORDER_CSHOT, R.string.photopage_photomenu_talkback_photo_burst
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_cshot_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_cshot")
    }

    private fun Menu.addActionOlivePhoto() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_olive_photo,
            ORDER_OLIVE_PHOTO, com.oplus.gallery.basebiz.R.string.model_title_olive
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_olive_selector)
            .setContentDescription(sectionPage.pageContext?.getString(com.oplus.gallery.basebiz.R.string.model_title_olive))
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_olive_photo")
    }

    private fun Menu.addActionPortraitBlur() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_portrait_blur,
            ORDER_PORTRAIT_BLUR, com.oplus.gallery.basebiz.R.string.picture3d_editor_text_portrait_blur
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_portrait_blur_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_portrait_blur")
    }

    private fun Menu.addActionGroupPhoto() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_group_photo,
            ORDER_GROUP_PHOTO, R.string.photopage_photomenu_talkback_group_photo
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_group_photo_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_group_photo")
    }

    private fun Menu.addActionCloudDownload() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.action_cloud_download,
            ORDER_CLOUD_DOWNLOAD, null
        )
        menuItem.setIcon(R.drawable.photopage_ic_menu_cloud_download_selector)
            .setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action action_cloud_download")
    }

    private fun Menu.addActionPlaceHolder() {
        val menuItem: MenuItem = add(
            R.id.group_actionbar, R.id.placeholder,
            ORDER_PLACE_HOLDER, null
        )
        menuItem.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        GLog.i(TAG, "[addAction] add menu Action placeholder")
    }

    /**
     * 顶部菜单图片缓存
     *
     * @param resId 资源 id，可以作为身份标识
     * @param resDrawable 资源内容，用于绘制显示
     */
    private data class NavigationIconCache(
        val resId: Int,
        var resDrawable: Drawable
    )

    /**
     * 变更大小的 Drawable 的缓存 Key
     *
     * @param resId 资源 id
     * @param size 新的大小
     */
    private data class SizedDrawableKey(
        val resId: Int,
        val size: Int
    )

    companion object {
        private const val TAG = "PhotoTopMenuDecoration"
        private const val SHOW_RED_DOT = 0
        private const val HIDE_RED_DOT = -1
        private val ID_VIEW_ICON_TIP = R.id.view_icon_tip

        /**
         * 图标大小, dp
         */
        private const val ICON_SIZE = 18

        /**
         * 半沉浸模式导航键 view 的大小, dp
         */
        private const val IMMERSIVE_ICON_VIEW_SIZE = 30

        /**
         * 导航键 view 的大小, dp
         */
        private const val NORMAL_ICON_VIEW_SIZE = 48

        /**
         * 删除后延迟显示新图标的时间
         */
        const val REMOVED_DELAY_ALPHA_SHOW_TIME = PhotoAnimationConfig.PHOTO_REMOVED_DELAY_ALPHA_SHOW_TIME

        private const val ORDER_PLACE_HOLDER = 101
        private const val ORDER_CLOUD_DOWNLOAD = 102
        private const val ORDER_GROUP_PHOTO = 103
        private const val ORDER_PORTRAIT_BLUR = 104
        private const val ORDER_OLIVE_PHOTO = 105
        private const val ORDER_CSHOT = 106
        private const val ORDER_ENHANCE_TEXT = 107
        private const val ORDER_AI_ID_PHOTO = 108
        private const val ORDER_GET_VIDEO_FRAME = 109
        private const val ORDER_SCREEN_CAST = 110
        private const val ORDER_SELF_SPLITTING = 111
    }
}
