/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoIntelliFuncSection
 ** Description:16.0 智能功能推荐由独立切片 改为内嵌详情panel内部
 ** Version: 2.0
 ** Date: 2025-04-17
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2025-04-17     2.0
 ********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.intelliRecommend

import android.app.Activity
import android.content.Intent
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.details.PanelStructure
import com.oplus.gallery.photo_page.ui.section.details.getPanelSize
import com.oplus.gallery.photo_page.ui.section.details.getPanelStructure
import com.oplus.gallery.photo_page.ui.section.details.isSmallHorizontalScreenModePanel
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.anim.FuncFadeAnimator
import com.oplus.gallery.photo_page.ui.section.intelliRecommend.anim.NormalFuncItemAnimator
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncDetectInfo
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.FuncItem
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.PhotoFuncViewModel.Companion.filterNonNeedShowInDetailFuncItems
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.Status
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.IntelliFuncTrackDataHelper
import kotlin.collections.reversed as ktReversed

internal class PhotoIntelliFuncComponent(
    val sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>
) : LifecycleOwner {
    private var rvFunc: RecyclerView? = null
    private var funcAdapter: FuncAdapter? = null
    private var fadeAnimator: FuncFadeAnimator? = null
    private var currentFuncDetectInfo: FuncDetectInfo? = null

    /**
     * 当前大图展示的mediaItem的path.toString()作为唯一的Id，用来和发送的智能推荐信息中的path.toString()进行比对
     */
    private var mediaPath: String? = null

    /**
     * 当前智能推荐动画需要移动的X轴距离
     */
    private var offsetX: Float = 0f

    /**
     * 当前智能推荐动画需要移动的Y轴距离
     */
    private var offsetY: Float = 0f

    private val logTag = "$TAG${this.hashCode()}"

    /**
     * 上次更新页面时的主题色
     */
    private var lastUiMode: Int? = null

    /**
     * 在selection的onResponsePageResult进行调用，用于防止闪现老推荐项 （旧功能保留）
     */
    internal fun onResponsePageResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            // 为防止闪现老推荐项，页面返回后先隐藏推荐项，等待新的推荐结果再显示
            currentFuncDetectInfo = null
            changedFuncRvVisible(false)
        }
    }

    /**
     * 详情view创建，初始化智能推荐view
     * @param view 包含智能推荐的详情view
     */
    internal fun onViewCreated(view: View) {
        val pageContext = sectionPage.pageContext
        if (pageContext == null) {
            return
        }
        val rvFunc = view.findViewById<RecyclerView>(R.id.vs_intelli_func)
        rvFunc.setHasFixedSize(true)
        rvFunc.clipToPadding = false
        rvFunc.layoutManager = FuncLayoutManager()
        rvFunc.itemAnimator = NormalFuncItemAnimator(rvFunc)
        rvFunc.adapter = FuncAdapter(rvFunc.context).apply {
            funcAdapter = this
        }
        this.rvFunc = rvFunc
        fadeAnimator = FuncFadeAnimator(rvFunc)
        adaptScreen()
        sectionPage.pageViewModel.photoFunc.funcDetectInfo.observe(this, ::onFuncDetectInfoChanged)
        sectionPage.pageViewModel.details.isInDetailsMode.observe(this, ::onInDetailsModeChanged)
    }

    /**
     * 详情模式变更
     * @param isDetailsDisplaying 详情是否展示
     */
    private fun onInDetailsModeChanged(isDetailsDisplaying: Boolean) {
        //是否是属于当前焦点页
        val isFocusItem = currentFuncDetectInfo?.mediaPath == sectionPage.pageViewModel.dataLoading.focusItemViewData?.id
        //是否有数据
        val hasItems = (funcAdapter?.itemCount ?: 0) > 0
        // 是否需要动画，只有在详情模式变需要动画，且有数据，且是处于当前焦点页的详情overlay中才需要动画
        val shouldAnimate = sectionPage.pageViewModel.details.needAnimOnDetailsModeChanged && (isFocusItem) && (hasItems)

        when {
            shouldAnimate && isDetailsDisplaying -> fadeInFuncView()
            shouldAnimate -> fadeOutFuncView()
            else -> changedFuncRvVisible(isDetailsDisplaying && hasItems)
        }
    }

    /**
     * 过渡属性变更，智能推荐联动
     * @param progress 动画进度
     */
    internal fun updateTransitionPosition(progress: Float) {
        val appUiConfig = sectionPage.pageInstance.getCurrentAppUiConfig()
        val panelStructure = getPanelStructure(appUiConfig)
        val (w, h) = getPanelSize(appUiConfig, panelStructure)
        when (panelStructure) {
            PanelStructure.VERTICAL_SLIDE_UP_SINGLE,
            PanelStructure.VERTICAL_SLIDE_UP_DOUBLE -> setTransition(0f, h * (1 - progress))

            PanelStructure.HORIZONTAL_SLIDE_SINGLE -> setTransition(w * (1 - progress), 0f)
        }
    }

    private fun setTransition(transitionX: Float, transitionY: Float) {
        rvFunc?.translationX = if (ResourceUtils.isRTL(sectionPage.pageContext)) -transitionX else transitionX
        rvFunc?.translationY = transitionY
    }

    /**
     * 更新mediaId,用来控制智能推荐展示的条目跟图片对应
     * @param path 图片的mediaItem的path.toString()作为唯一的Id，用来和发送的智能推荐信息中的path.toString()进行比对
     */
    internal fun updateMediaPath(path: String) {
        if (this.mediaPath == path) {
            GLog.d(logTag, LogFlag.DL) { "[updateMediaId] mediaId not changed" }
            return
        }
        this.mediaPath = path
        currentFuncDetectInfo?.let {
            if (it.mediaPath != path) {
                currentFuncDetectInfo = null
                changedFuncRvVisible(false)
                funcAdapter?.refresh(emptyList(), false)
            }
        }
        sectionPage.pageViewModel.photoFunc.funcDetectInfo.value?.let {
            if (it.mediaPath == path) {
                onFuncDetectInfoChanged(it)
            }
        }
    }

    /**
     * 更新mediaId,用来控制智能推荐展示的条目跟图片对应
     * @param mediaPath 图片的mediaId
     */
    internal fun clearData() {
        this.mediaPath = null
        currentFuncDetectInfo = null
        setTransition(0f, 0f)
        changedFuncRvVisible(false)
        funcAdapter?.refresh(emptyList(), false)
    }

    private fun onFuncDetectInfoChanged(info: FuncDetectInfo) {
        GLog.d(logTag, LogFlag.DL) {
            "onFuncDetectInfoChanged old:$currentFuncDetectInfo, new:$info, " +
                    "canVisible:${sectionPage.pageViewModel.photoFunc.visible.value}"
        }
        if ((info.status != Status.END) || info == currentFuncDetectInfo) return

        if (mediaPath != info.mediaPath) {
            GLog.d(logTag, LogFlag.DL) { "current view need show info’s id not same get Info" }
            return
        }

        val oldFuncDetectInfo = currentFuncDetectInfo
        currentFuncDetectInfo = info
        //智能推荐最优先的在最下面
        val funcList = toFuncPresentList(info.funcItems).ktReversed()
        rvFunc?.itemAnimator?.isRunning {
            if (info == currentFuncDetectInfo) {
                refreshFuncList(oldFuncDetectInfo, funcList)
            } else {
                GLog.w(logTag, LogFlag.DL) { "onFuncDetectInfoChanged, skip!" }
            }
        }
    }

    internal fun refreshUiStateChanged() {
        //如果上次刷新时的主题色与当前主题色不一致，则刷新所有View的资源设置
        val currentUiMode = sectionPage.pageViewModel.pageManagement.uiMode.value
        if (currentUiMode != null && lastUiMode != currentUiMode) {
            lastUiMode = currentUiMode
            //智能推荐最优先的在最下面
            currentFuncDetectInfo?.let { info ->
                val funcList = toFuncPresentList(info.funcItems).ktReversed()
                funcAdapter?.refresh(funcList, false)
            }
        }
    }

    private fun refreshFuncList(oldFuncDetectInfo: FuncDetectInfo?, funcList: List<FuncPresent>) {
        when {
            // 无推荐项到有推荐项，显示推荐区
            (oldFuncDetectInfo?.funcItems?.isNotEmpty() != true) && funcList.isNotEmpty() -> {
                IntelliFuncTrackDataHelper.recordIntelliFuncShowCount(funcList)
                if (canShowFunc()) {
                    funcAdapter?.refresh(funcList, false)
                    rvFunc?.post {
                        fadeInFuncView(
                            noRequired = { funcAdapter?.refresh(funcList, true) }
                        )
                    }
                    return
                }
                // 如果列表正在消失，这时候需要刷新数据也要等它消失后再执行，否则会闪现内容变化
                fadeAnimator?.doEnd {
                    funcAdapter?.refresh(funcList, false)
                }
            }
            // 有推荐项到无推荐项，隐藏推荐区
            (oldFuncDetectInfo?.funcItems?.isNotEmpty() == true) && funcList.isEmpty() -> {
                fadeOutFuncView {
                    GLog.d(logTag, LogFlag.DL) { "refreshFuncList, current must not visible" }
                    funcAdapter?.refresh(emptyList(), false)
                }
            }
            // 推荐项项刷新
            else -> {
                IntelliFuncTrackDataHelper.recordIntelliFuncShowCount(funcList)
                funcAdapter?.refresh(funcList, rvFunc?.isVisible == true)
                /*
                 * onResponsePageResult时可能隐藏view，需要再检测更新下
                 * VM设置可见，并且要有功能区列表，才能设置为可见；不然这个View可见会拦截触碰消息导致大图功能区不能左右滑动
                 */
                changedFuncRvVisible(canShowFunc() && (funcList.isEmpty().not()))
            }
        }
    }

    private fun toFuncPresentList(funcItems: List<FuncItem>): List<FuncPresent> {
        return filterNonNeedShowInDetailFuncItems(funcItems).mapNotNull { item ->
            when (item) {
                is FuncItem.GooglePassScan -> FuncPresent.GooglePassScan(sectionPage.pageViewModel)
                is FuncItem.TextTranslate -> FuncPresent.TextTranslate(sectionPage.pageViewModel)
                is FuncItem.OcrScanner -> FuncPresent.OcrScanner(sectionPage.pageViewModel)
                is FuncItem.ImageQualityEnhance -> FuncPresent.ImageQualityEnhance(sectionPage.pageViewModel)
                is FuncItem.PasserbyEliminate -> FuncPresent.PasserbyEliminate(sectionPage.pageViewModel)
                is FuncItem.DeBluring -> FuncPresent.DeBluring(sectionPage.pageViewModel)
                is FuncItem.DeReflectiveLight -> FuncPresent.DeReflectiveLight(sectionPage.pageViewModel)
                is FuncItem.PrivacyWatermark -> FuncPresent.PrivacyWatermark(sectionPage.pageViewModel)
                is FuncItem.GroupPhoto -> FuncPresent.GroupPhoto(sectionPage.pageViewModel)
                is FuncItem.BestTake -> FuncPresent.BestTake(sectionPage.pageViewModel)
                is FuncItem.AiLighting -> FuncPresent.AiLighting(sectionPage.pageViewModel)
                else -> null
            }
        }
    }

    private fun fadeOutFuncView(needTranslation: Boolean = false, onEnd: (() -> Unit)? = null) {
        val fadeAnimator = fadeAnimator ?: let {
            onEnd?.invoke()
            return
        }
        if (needTranslation) {
            fadeAnimator.setTranslationOffset(offsetX, offsetY)
        } else {
            fadeAnimator.setTranslationOffset()
        }
        fadeAnimator.fadeInOrOut(false, doEnd = onEnd)
    }

    private fun fadeInFuncView(needTranslation: Boolean = false, doStart: (() -> Unit)? = null, noRequired: (() -> Unit)? = null) {
        if (currentFuncDetectInfo?.funcItems?.isNotEmpty() != true) {
            GLog.d(logTag, LogFlag.DL) { "fadeInFuncView, not data" }
            return
        }
        if (needTranslation) {
            fadeAnimator?.setTranslationOffset(offsetX, offsetY)
        } else {
            fadeAnimator?.setTranslationOffset()
        }
        fadeAnimator?.fadeInOrOut(
            true,
            doStart = {
                doStart?.invoke()
            },
            doEnd = {
                noRequired?.invoke()
            }
        )
    }

    /**
     *   see [com.oplus.gallery.photo_page.ui.section.details.PhotoDetailsOverlay]
     *  详情overlay的Ui状态变化时动态修改布局展示
     *  - 智能推荐view的展示跟详情overlay中的Panel的展示存在依赖关系，根据Overlay的展示动态调整智能推荐view的布局
     */
    internal fun onDetailsOverlayUiStateChanged() {
        rvFunc?.post {
            adaptScreen()
        }
    }

    private fun adaptScreen() {
        val context = sectionPage.pageContext ?: return
        val rvFunc = rvFunc ?: return

        val appUiConfig = sectionPage.pageInstance.getCurrentAppUiConfig()
        val funcLayoutManager = rvFunc.layoutManager as? FuncLayoutManager
        val panelStructure = getPanelStructure(appUiConfig)
        var extBottomPadding = 0

        if (panelStructure == PanelStructure.HORIZONTAL_SLIDE_SINGLE) {
            funcLayoutManager?.setRightAligned(false)
            extBottomPadding = when {
                sectionPage.pageInstance.couldShowSystemBar() -> sectionPage.pageInstance.bottomNaviBarHeight()
                else -> 0
            }
        } else {
            funcLayoutManager?.setRightAligned(true)
        }

        val marginEnd = if (ResourceUtils.isRTL(context)) {
            sectionPage.pageInstance.leftNaviBarHeight()
        } else {
            sectionPage.pageInstance.rightNaviBarHeight()
        }

        val start = context.resources.getDimensionPixelSize(
            if (isSmallHorizontalScreenModePanel(appUiConfig, panelStructure)) {
                R.dimen.photopage_intelli_func_small_horizontal_screen_margin_start
            } else {
                R.dimen.photopage_intelli_func_normal_margin_start
            }
        )
        val marginStart = if (ResourceUtils.isRTL(context)) {
            sectionPage.pageInstance.rightNaviBarHeight()
        } else {
            sectionPage.pageInstance.leftNaviBarHeight()
        } + start

        // 避免导航栏和其他控件挡住推荐功能区
        rvFunc.updateMarginRelative(start = marginStart, end = marginEnd, bottom = extBottomPadding)
        GLog.d(logTag, LogFlag.DL) { "[adaptScreen] end:$marginEnd, extBottomPadding:$extBottomPadding" }
    }

    /**
     * 退出翻译浮窗
     */
    private fun exitTranslate() {
        if (AppBrandConstants.Package.PACKAGE_NAME_TRANSLATE.isEmpty()) {
            GLog.w(logTag, LogFlag.DL, "exitTranslate, translate package is not exit")
        } else {
            GLog.d(logTag, LogFlag.DL, "exitTranslate, do translate exit")
            val intent = Intent(IntentConstant.TranslateConstant.GLOBAL_TRANSLATION_EXIT_ACTION)
            intent.setPackage(AppBrandConstants.Package.PACKAGE_NAME_TRANSLATE)
            intent.putExtra(IntentConstant.TranslateConstant.KEY_TRANSLATION_FROM_PACKAGE, sectionPage.pageContext?.packageName)
            sectionPage.pageContext?.sendBroadcast(intent)
        }
    }

    /**
     * 失去焦点
     */
    internal fun onPause() {
        exitTranslate()
    }

    /**
     * release当前组件的资源。需要在页面销毁时调用，避免产生泄漏。
     */
    internal fun releaseComponent() {
        sectionPage.pageViewModel.track.trackIntelliFuncRecommendEvent()
        fadeAnimator?.releaseAnimations()
    }

    private fun canShowFunc(): Boolean = sectionPage.pageViewModel.details.isInDetailsMode.value == true

    private fun changedFuncRvVisible(visible: Boolean) {
        fadeAnimator?.apply {
            cancelAnimations()
        }
        rvFunc?.apply {
            visibility = if (visible) View.VISIBLE else View.INVISIBLE
            alpha = if (visible) 1f else 0f
        }
    }

    override val lifecycle: Lifecycle
        get() = sectionPage.pageLifecycle

    companion object {
        private const val TAG = "PhotoIntelliFuncComponent"
    }
}