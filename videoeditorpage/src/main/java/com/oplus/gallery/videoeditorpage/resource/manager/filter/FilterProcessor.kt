/********************************************************************************
 * Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - FilterProcessor
 * Description: 滤镜处理类，用于处理ipu滤镜和非ipu滤镜，给缩略图加滤镜
 * Version: 1.0
 * Date : 2025/03/26
 * Author: kangshuwen
 * TAG:
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>        <version>    <desc>
 * ------------------------------------------------------------------------------
 * kang<PERSON>wen                      2025/03/26    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.manager.filter

import androidx.annotation.WorkerThread
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.videoeditorpage.R
import com.meicam.sdk.NvsVideoClip
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.video.business.filter.EditorFilterSubUIController
import com.oplus.gallery.videoeditorpage.resource.room.entity.FilterEntity
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_3
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_4
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.resource.manager.filter.FilterConstant.FILTER_CONFIG
import com.oplus.ocs.camera.ipuapi.process.filter.key.FilterProcessResultKey

/**
 * 滤镜处理类，用于处理ipu滤镜和非ipu滤镜，给缩略图加滤镜
 */
class FilterProcessor {

    /**
     * 是否支持ipu滤镜
     */
    private val isSupportIpuFilter: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_FILTER, false)
    }

    /**
     * 从Assets目录中获取滤镜列表
     */
    private val filterListFromAssets: List<FilterItemAssets> by lazy {
        val json = AssetHelper.getAssetData(ContextGetter.context, FILTER_JSON_PATH)
        val filterInformation = JsonUtil.fromJson(json, FilterItemAssetsInformation::class.java)
        filterInformation.filterList
    }

    /**
     * 加载IPU滤镜列表
     * @return IPU滤镜列表
     */
    @WorkerThread
    fun loadFilterEntityList(callback: (MutableList<FilterEntity>) -> Unit) {
        if (isSupportIpuFilter) {
            val ipuFilterManager = IpuFilterManager(ContextGetter.context)
            val ipuFilterItems = ipuFilterManager.ipuFilterItems
            GLog.d(TAG, LogFlag.DF, "[loadFilterEntityList] ipu filterItems:$ipuFilterItems")
            var filterItems: List<HashMap<String?, Any?>>? = null
            var supportHdr: Boolean? = null
            if (ipuFilterItems != null) {
                filterItems = ipuFilterItems.first
                supportHdr = ipuFilterItems.second
            }
            val filterEntities = filterItems?.toFilterEntities() ?: emptyList()
            GLog.d(TAG, LogFlag.DF, "[loadFilterEntityList] supportHdr:$supportHdr")
            addFilterToIcon(filterEntities.toMutableList(), callback)
        } else {
            val filterEntities = mutableListOf<FilterEntity>()
            for (filterItem in filterListFromAssets) {
                GLog.d(TAG, LogFlag.DF, "[loadFilterEntityList] filterItem = $filterItem")
                FILTER_CONFIG[filterItem.fxKey]?.let {
                    val filterName = ContextGetter.context.getString(it)
                    filterEntities.add(
                        FilterEntity().apply {
                            filePath = "$FILTER_PARENT_DIR${filterItem.fxPath}"
                            this.filterName = filterName
                            zhName = filterName
                            chName = filterName
                            enName = filterName
                            isBuiltin = IS_BUILD_IN
                            downloadState = DOWNLOADED
                            iconPath = DEFAULT_FILTER_PNG
                        }
                    )
                }
            }
            addFilterToIcon(filterEntities, callback)
        }
    }

    /**
     * 给滤镜图标添加滤镜效果
     * @param filterEntities 滤镜列表
     * @return 滤镜列表
     */
    @WorkerThread
    private fun addFilterToIcon(filterEntities: MutableList<FilterEntity>, callback: (MutableList<FilterEntity>) -> Unit) {
        var widthAndHeight = ContextGetter.context.resources.getDimensionPixelSize(R.dimen.videoeditor_filter_item_icon_width)
        if ((widthAndHeight % NUMBER_4) != 0) {
            // 创建美摄时间线要求宽必须是4的倍数，高必须是2的倍数，这里宽同高，取ui宽度转成px后，向上最近4的倍数
            widthAndHeight = ((widthAndHeight + NUMBER_3) / NUMBER_4) * NUMBER_4
        }

        // 创建美摄引擎
        val editorEngine = EditorEngineGlobalContext.getInstance().createEditorEngine()
        editorEngine.initContext(ContextGetter.context)

        // 创建时间线
        val timeline = EditorEngineGlobalContext.getInstance().createTimeline(
            widthAndHeight, widthAndHeight,
            EditorEngine.DEFAULT_VIDEO_RESOLUTION_FPS
        )
        // 绑定时间线
        editorEngine.trySetTimeline(timeline)
        editorEngine.setFillMode(StreamingConstant.FillMode.FILLMODE_PRESERVEASPECTFIT)

        // 添加视频轨道
        val videoTrack = timeline.appendVideoTrack()
        val srcFilePath = ASSETS_DIR + DEFAULT_FILTER_PNG

        // 添加视频片段
        val iVideoClip = videoTrack.appendVideoClip(
            srcFilePath,
            srcFilePath,
            NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE,
            false,
            0,
            MICROSECOND,
            widthAndHeight,
            widthAndHeight,
            srcFilePath
        )

        // 循环添加滤镜效果
        val filterManager = EditorEngineGlobalContext.getInstance().filterManager
        filterEntities.forEach { filterEntity ->
            val videoClipEffect = filterManager.getVideoClipFilter(StreamingConstant.VideoFilter.FILTER_LUT)
            iVideoClip.appendBuiltinFx(videoClipEffect).getOrLog(TAG, "[addFilterToIcon], appendBuiltinFx")?.apply {
                setStringVal(StreamingConstant.VideoFilter.PARAM_DATA_FILE_PATH, filterEntity.filePath)
                setFloatVal(StreamingConstant.VideoFilter.INTENSITY, EditorFilterSubUIController.FILTER_DEFAULT_STRENGTH.toDouble())
            } ?: return@forEach

            // 抓取滤镜效果图片
            filterEntity.icon = timeline.grabImageFromTimeline(MICROSECOND shr 1, widthAndHeight)
            iVideoClip.removeEffect(videoClipEffect)
        }
        videoTrack.removeAllClips()
        editorEngine.removeCurrentTimeline()
        //在第0位添加一个"无"滤镜对象
        filterEntities.add(MathUtils.ZERO, FilterManager.createNoneFilter())
        callback.invoke(filterEntities)
    }

    /**
     * 将 filterItems 转换为 FilterEntities
     */
    private fun List<HashMap<String?, Any?>>.toFilterEntities(): List<FilterEntity> {
        return map { filterItem ->
            val filterType = filterItem[FilterProcessResultKey.RESULT_DRAWING_ITEM_FILTER_TYPE] as? String
            val filterName = filterItem[FilterProcessResultKey.RESULT_DRAWING_ITEM_FILTER_NAME] as? String
            val masterType = filterItem[FilterProcessResultKey.RESULT_DRAWING_ITEM_KEY_IS_MASTER]
            val type = if (masterType is Int) masterType else 0

            FilterEntity().apply {
                filePath = FILTER_PARENT_IPU_DIR + filterType
                this.filterName = filterName
                zhName = filterName
                chName = filterName
                enName = filterName
                isBuiltin = IS_BUILD_IN
                downloadState = DOWNLOADED
                this.type = type
                iconPath = DEFAULT_FILTER_PNG
            }
        }
    }

    companion object {
        /**
         * IPU滤镜的目录
         */
        private const val FILTER_PARENT_IPU_DIR = "/odm/etc/camera/meishe_lut/"

        /**
         * 非IPU的滤镜目录
         */
        private const val FILTER_PARENT_DIR = "assets:/tbluniformeditor/filter/"

        /**
         * 滤镜信息json文件
         */
        private const val FILTER_JSON_PATH = "tbluniformeditor/filter/tbl_uniform_editor_filter_information.json"
        private const val TAG = "IpuFilterProcessor"
        private const val ASSETS_DIR = "assets:/"
        private const val DEFAULT_FILTER_PNG = "filter/videoeditor_filter_default.png"

        /**
         * 是否内置滤镜特效
         */
        private const val IS_BUILD_IN = 1
        /**
         * 滤镜已下载，目前都是本地或者ipu滤镜，所以直接设置为3
         */
        private const val DOWNLOADED = 3

        /**
         * 100微妙，用来给图标上滤镜效果
         */
        private const val MICROSECOND = 100L
    }
}

internal class FilterItemAssetsInformation {
    @SerializedName("mFxAssetList")
    val filterList: List<FilterItemAssets> = ArrayList()
}

internal data class FilterItemAssets(
    @SerializedName("mFxKey") val fxKey: String = EMPTY_STRING,

    @SerializedName("mFxIndex") val fxIndex: String = EMPTY_STRING,

    @SerializedName("mFxPath") val fxPath: String = EMPTY_STRING,

    @SerializedName("mFxSuffix") val fxSuffix: String = EMPTY_STRING,

    @SerializedName("mIsBuiltIn") val isBuiltIn: String = EMPTY_STRING

) {
    override fun toString(): String {
        return "fxKey= $fxKey, fxIndex = $fxIndex, fxPath = $fxPath, fxSuffix = $fxSuffix, isBuiltIn = $isBuiltIn"
    }
}
