/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FontViewHolder.kt
 ** Description : 文字样式item viewHolder
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ViewAnimator
import com.oplus.gallery.videoeditorpage.widget.RoundProgressView
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.BaseAdapter.BaseVH

/**
 * 字幕样式的view holder
 */
class StyleViewHolder(parent: ViewGroup) : BaseVH<StyleViewData>(parent, R.layout.videoeditor_item_caption_style) {

    /**
     * 字幕样式的主view，用于显示点击未点击状态
     */
    private val captionStyleItemView: FrameLayout by lazy {
        findViewById(R.id.item_caption_style_parent)
    }

    /**
     * 字样样式icon显示
     */
    private val captionStyleItemViewImg: ImageView by lazy {
        findViewById(R.id.iv_item_caption_style_img)
    }

    /**
     * 样式资源下载状态显示
     */
    private val captionStyleDownloadStateView: ViewAnimator by lazy {
        findViewById(R.id.va_caption_style_download_state)
    }

    /**
     * 样式下载进度显示
     */
    private val downloadProgressView: RoundProgressView by lazy {
        findViewById(R.id.download_progress)
    }

    override fun bind(captionStyle: StyleViewData) {
        this.captionStyleItemView.isSelected = captionStyle.selected
        val fontViewData = captionStyle.fontViewData
        val fontNeedDownload = if (fontViewData != null) {
            // 样式绑定了字体
            fontViewData.downloadState == DownloadItem.NOT_DOWNLOADED
        } else {
            // 样式没有绑定字体
            false
        }
        val styleNeedDownload = (captionStyle.downloadState == DownloadItem.NOT_DOWNLOADED)
        if (fontNeedDownload || styleNeedDownload) {
            captionStyleDownloadStateView.visibility = View.VISIBLE
            val fontDownloadProgress = captionStyle.fontViewData?.progress ?: 0
            if (captionStyle.progress > 0 || fontDownloadProgress > 0) {
                captionStyleDownloadStateView.displayedChild = INDEX_DOWNLOADING
                downloadProgressView.progress = (captionStyle.progress + fontDownloadProgress) / PROGRESS_NUM2
            } else {
                captionStyleDownloadStateView.displayedChild = INDEX_DOWNLOAD
            }
        } else {
            captionStyleDownloadStateView.visibility = View.GONE
        }
        loadImage(captionStyle.iconUrl, captionStyleItemViewImg, R.drawable.ic_caption_style_default)
    }

    companion object {
        private const val TAG = "StyleViewHolder"

        /**
         * 用于索引显示下载icon
         */
        const val INDEX_DOWNLOAD: Int = 0

        /**
         * 用于索引显示下载进度条
         */
        const val INDEX_DOWNLOADING: Int = 1

        /**
         * 计算样式和字体两个文件下载进度的平均除数
         */
        private const val PROGRESS_NUM2 = 2
    }
}