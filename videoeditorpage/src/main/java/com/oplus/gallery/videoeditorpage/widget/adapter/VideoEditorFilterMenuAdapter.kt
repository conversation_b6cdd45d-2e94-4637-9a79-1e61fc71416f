/*********************************************************************************
 ** Copyright (C), 2019-2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorFilterMenuAdapter.kt
 ** Description: 滤镜业务使用的adapter
 **
 ** Version: 1.0.0
 ** Date: 2025/7/9
 ** Author: 80411952@OppoGallery3D
 ** TAG:[功能标签]
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** 80411952@OppoGallery3D         2025/7/9      1.0.0        初始创建
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.adapter

import android.content.Context
import android.content.res.Resources
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.widget.EditorMenuItemView
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 滤镜业务使用的适配器，目前有部分业务仍在UIControl中，下一笔田主处理下
 * marked by kangshuwen
 */
open class VideoEditorFilterMenuAdapter(
    context: Context,
    data: MutableList<EditorMenuItemViewData>
) : VideoEditorMenuAdapter(context, data) {

    private val mDefaultTextColor by lazy {
        context.resources.getColor(R.color.base_white, null)
    }

    private val textHeight by lazy {
        mContext.resources.getDimensionPixelOffset(R.dimen.base_editor_menu_item_text_height)
    }

    override fun bindData(viewHolder: BaseRecycleViewHolder, position: Int, item: EditorMenuItemViewData) {
        val viewGroup = viewHolder.itemView
        viewGroup.id = item.viewId

        val nameTextView = getNameTextView(viewHolder)
        bindNameText(nameTextView, item)
        val dotView = getDotView(viewHolder)
        bindDotView(dotView, item)

        val menuItemView = getMenuItemView(viewHolder)
        bindMenuItemView(menuItemView, item, position)
    }

    private fun bindMenuItemView(menuItemView: EditorMenuItemView?, item: EditorMenuItemViewData, position: Int) {
        menuItemView ?: run {
            GLog.e(TAG, LogFlag.DL) { "bindMenuItemView. menuItemView is null" }
            return
        }
        menuItemView.normalDrawFlag = (EditorMenuItemView.DRAW_BACKGROUND_COLOR
            or EditorMenuItemView.DRAW_IMAGE
            or EditorMenuItemView.DRAW_CENTER_ICON
            or EditorMenuItemView.DRAW_BORDER)
        menuItemView.selectedDrawFlag = (EditorMenuItemView.DRAW_BACKGROUND_COLOR
            or EditorMenuItemView.DRAW_IMAGE
            or EditorMenuItemView.DRAW_FOREGROUND_COLOR
            or EditorMenuItemView.DRAW_CENTER_ICON
            or EditorMenuItemView.DRAW_BORDER
            or EditorMenuItemView.DRAW_CENTER_TEXT)
        val icon = item.image
        if (icon != null) {
            menuItemView.setImageIcon(icon)
        }
        if (item.iconResId != Resources.ID_NULL) {
            menuItemView.setIconResource(item.iconResId)
        }
        menuItemView.isDrawBackgroundColor = true
        menuItemView.isDrawForegroundColor = true
        menuItemView.centerText = item.centerText
        if (item.loadProgress != item.oldLoadProgress) {
            menuItemView.setProgressAnim(item.oldLoadProgress, item.loadProgress)
        } else {
            menuItemView.progress = if ((!item.isDisableStyle)) item.loadProgress else 0f
        }
        menuItemView.isDrawCenterIcon = TextUtils.isEmpty(item.centerText)
        menuItemView.setTextColor(item.textColor)
    }

    private fun bindDotView(dotView: View?, item: EditorMenuItemViewData) {
        if (dotView != null) {
            dotView.visibility = if (item.isTopTipsShow) View.VISIBLE else View.INVISIBLE
        }
    }

    private fun bindNameText(nameTextView: TextView?, item: EditorMenuItemViewData) {
        nameTextView ?: run {
            GLog.e(TAG, LogFlag.DL) { "bindNameText. nameTextView is null" }
            return
        }
        if (item.textId != 0) {
            nameTextView.setText(item.textId)
        } else {
            nameTextView.text = item.text
        }
        if (item.textColor != 0) {
            nameTextView.setTextColor(item.textColor)
        } else {
            nameTextView.setTextColor(mDefaultTextColor)
        }
        val layoutParams = nameTextView.layoutParams
        if (orientation == LinearLayout.VERTICAL) {
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        } else {
            layoutParams.height = textHeight
        }
        nameTextView.layoutParams = layoutParams
    }

    companion object {
        private const val TAG = "VideoEditorFilterMenuAdapter"
    }
}