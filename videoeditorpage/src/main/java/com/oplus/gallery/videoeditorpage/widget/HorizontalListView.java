/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: HorizontalListView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.PointF;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;

import java.util.ArrayList;

public class HorizontalListView extends RecyclerView {
    private static final String TAG = "HorizontalListView";

    private static final int INVALID_POSITION = -1;

    /**
     * 装饰边距，用于所有item宽度小于父视图时计算居中对齐
     */
    private final ArrayList<Rect> mDecorationOutRectList = new ArrayList<>();

    private LinearLayoutManager mLayoutManager;
    private int mItemSpacing;
    private boolean mIsStartEndCenter;
    private boolean mIsRTL = false;

    private LinearSmoothScroller mLinearSmoothScroller;

    private int mTargetPosition;

    private boolean mDisableAutoClick;

    public HorizontalListView(Context context) {
        this(context, null);
    }

    public HorizontalListView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public HorizontalListView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.HorizontalListView, defStyle, 0);
        mItemSpacing = a.getDimensionPixelSize(R.styleable.HorizontalListView_itemSpacing, 0);
        mIsStartEndCenter = a.getBoolean(R.styleable.HorizontalListView_isStartEndCenter, false);
        a.recycle();
        mIsRTL = TextUtils.getLayoutDirectionFromLocale(context.getResources().getConfiguration().locale)
                == LAYOUT_DIRECTION_RTL;

        mLayoutManager = new LinearLayoutManager(context) {
            @Override
            public void smoothScrollToPosition(RecyclerView view, RecyclerView.State state, int position) {
                if (mLinearSmoothScroller != null) {
                    mLinearSmoothScroller.setTargetPosition(position);
                    startSmoothScroll(mLinearSmoothScroller);
                    mTargetPosition = position;
                } else {
                    super.smoothScrollToPosition(view, state, position);
                }
            }
        };
        mLayoutManager.setOrientation(HORIZONTAL);
        setLayoutManager(mLayoutManager);

        GLog.d(TAG, "HorizontalListView mItemSpacing = " + mItemSpacing
                + ", mIsStartEndCenter = " + mIsStartEndCenter + ", mIsRTL = " + mIsRTL);
    }

    public void setDisableAutoClick(boolean disableAutoClick) {
        mDisableAutoClick = disableAutoClick;
    }

    public int getTargetPosition() {
        return mTargetPosition;
    }

    public void setLinearSmoothScroller(LinearSmoothScroller linearSmoothScroller) {
        mLinearSmoothScroller = linearSmoothScroller;
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        GLog.d(TAG, "onFinishInflate, w = " + ScreenUtils.getDisplayScreenWidth()
                + ", h = " + ScreenUtils.getDisplayScreenHeight());
        if ((mItemSpacing > 0) || mIsStartEndCenter) {
            addItemDecoration(mItemDecoration);
        }

        addOnItemTouchListener(mOnItemTouchListener);
    }

    public boolean isItemVisible(int position) {
        return (position >= mLayoutManager.findFirstVisibleItemPosition())
                && (position <= mLayoutManager.findLastVisibleItemPosition());
    }

    public int findFirstVisibleItemPosition() {
        return mLayoutManager.findFirstVisibleItemPosition();
    }

    public int getCenterVisiblePosition() {
        try {
            return (mLayoutManager.findFirstVisibleItemPosition() + mLayoutManager.findLastVisibleItemPosition()) / 2;
        } catch (Exception e) {
            GLog.w(TAG, "getCenterVisiblePosition error = ", e);
        }
        return 0;
    }

    public int getVisibleItemNum() {
        try {
            return (mLayoutManager.findLastVisibleItemPosition() - mLayoutManager.findFirstVisibleItemPosition() + 1);
        } catch (Exception e) {
            GLog.e(TAG, "getVisibleItemNum error = ", e);
        }
        return 0;
    }

    private RecyclerView.ItemDecoration mItemDecoration = new RecyclerView.ItemDecoration() {
        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView colorRecyclerView, State state) {
            int position = colorRecyclerView.getChildViewHolder(view).getAdapterPosition();
            if (position < 0) {
                GLog.w(TAG, LogFlag.DL, "current position value is -1,because viewHolder maybe removed");
                return;
            }
            int size = colorRecyclerView.getAdapter().getItemCount();
            int verticalInset = 0;
            int horizontalInset = mItemSpacing >> 1;
            int displayScreenWidth = ScreenUtils.getDisplayScreenWidth();
            if (position == 0) {
                if (mIsRTL && (colorRecyclerView.getLayoutDirection() == LAYOUT_DIRECTION_RTL)) {
                    outRect.set(horizontalInset, verticalInset, mIsStartEndCenter ? displayScreenWidth / 2 : 0, verticalInset);
                } else {
                    outRect.set(mIsStartEndCenter ? displayScreenWidth / 2 : 0, verticalInset,
                            horizontalInset, verticalInset);
                }
            } else if (position == size - 1) {
                if (mIsRTL && (colorRecyclerView.getLayoutDirection() == LAYOUT_DIRECTION_RTL)) {
                    outRect.set(mIsStartEndCenter ? displayScreenWidth / 2 : 0, verticalInset, horizontalInset, verticalInset);
                } else {
                    outRect.set(horizontalInset, verticalInset,
                            mIsStartEndCenter ? displayScreenWidth / 2 : 0, verticalInset);
                }
            } else {
                outRect.set(horizontalInset, verticalInset, horizontalInset, verticalInset);
            }

            // item总宽度小于父视图时计算居中对齐会用到
            if (position < mDecorationOutRectList.size()) {
                mDecorationOutRectList.set(position, new Rect(outRect));
            } else {
                mDecorationOutRectList.add(new Rect(outRect));
            }
        }
    };

    /**
     * 读取所有item的边距
     */
    public int getDecorationOffset() {
        int offset = 0;
        for (int i = 0; i < mDecorationOutRectList.size(); i++) {
            Rect rect = mDecorationOutRectList.get(i);
            offset = offset + rect.left + rect.right;
        }
        return offset;
    }

    private OnItemTouchListener mOnItemTouchListener = new OnItemTouchListener() {
        View mTouchChildView = null;
        PointF mDownPosition = new PointF();
        boolean mShouldCancelPerformClick = false;
        float mTouchSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();

        @Override
        public boolean onInterceptTouchEvent(RecyclerView colorRecyclerView, MotionEvent motionEvent) {
            float x = motionEvent.getX();
            float y = motionEvent.getY();
            int action = motionEvent.getAction();

            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    return handleActionDown(x, y);
                case MotionEvent.ACTION_MOVE:
                    return handleActionMove(x, y);
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    return handleActionUp(x, y);
                default:
                    return false;
            }
        }
        private boolean handleActionDown(float x, float y) {
            mDownPosition.x = x;
            mDownPosition.y = y;
            mTouchChildView = detectTouchedView(x, y);
            if (mTouchChildView != null) {
                mTouchChildView.setPressed(true);
            }
            mShouldCancelPerformClick = false;
            return false;
        }

        private boolean handleActionMove(float x, float y) {
            float deltaX = x - mDownPosition.x;
            float deltaY = y - mDownPosition.y;

            if (!mShouldCancelPerformClick) {
                mShouldCancelPerformClick = (deltaX * deltaX + deltaY * deltaY) > (mTouchSlop * mTouchSlop);
            }

            if (mTouchChildView != null) {
                View child = detectTouchedView(x, y);
                if (mTouchChildView != child) {
                    cleanupTouchState();
                    return false;
                }
            } else {
                return false;
            }
            return false;
        }

        private boolean handleActionUp(float x, float y) {
            if (mTouchChildView != null) {
                View child = detectTouchedView(x, y);
                if (mTouchChildView == child) {
                    if (!mShouldCancelPerformClick) {
                        handleItemClick();
                    }
                }
                cleanupTouchState();
            }
            return false;
        }

        private void handleItemClick() {
            ViewHolder holder = getChildViewHolder(mTouchChildView);
            int position = holder.getAdapterPosition();

            if (mTouchChildView.isEnabled()) {
                handleEnabledItemClick(position);
            } else {
                handleDisabledItemClick(position);
            }
        }

        private void handleEnabledItemClick(int position) {
            if (!mDisableAutoClick) {
                mTouchChildView.performClick();
            }
            scrollToPosition(position);
        }

        private void handleDisabledItemClick(int position) {
            if (position == INVALID_POSITION) {
                return;
            }

            int firstPosition = mLayoutManager.findFirstVisibleItemPosition();
            int lastPosition = mLayoutManager.findLastVisibleItemPosition();
            int itemCount = mLayoutManager.getItemCount();

            int targetPosition = calculateTargetPosition(position, firstPosition, lastPosition, itemCount);
            smoothScrollToPosition(targetPosition);
        }

        private int calculateTargetPosition(int position, int firstPosition, int lastPosition, int itemCount) {
            // 边界情况直接滚动到该位置
            if ((position == 0) || (position == itemCount - 1)) {
                return position;
            }

            // 根据距离最近的可见项决定滚动方向
            if (position - firstPosition < lastPosition - position) {
                // 向前滚动
                int targetPosition = position - 1;
                return Math.max(0, targetPosition);
            } else {
                // 向后滚动
                int targetPosition = position + 1;
                return Math.min(itemCount - 1, targetPosition);
            }
        }

        private void cleanupTouchState() {
            if (mTouchChildView != null) {
                mTouchChildView.setPressed(false);
                mTouchChildView = null;
            }
        }

        @Override
        public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
            // 该方法留空
        }

        @Override
        public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            // 该方法留空
        }

        private View detectTouchedView(float x, float y) {
            View childView = findChildViewUnder(x, y);
            return childView;
        }
    };
}
