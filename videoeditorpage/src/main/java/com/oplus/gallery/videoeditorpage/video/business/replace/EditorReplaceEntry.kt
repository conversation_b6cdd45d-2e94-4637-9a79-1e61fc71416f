/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - EditorReplaceEntry
 ** Description: XXX.
 ** Version: 1.0
 ** Date : 2025/06/12
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >      <desc>
 **  80320709      2025/06/12  1.0             created
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.replace

import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant

/**
 * // Marked by <PERSON><PERSON><PERSON>ibin: 未具体实现，待开发替换业务再补充
 * 替换对象实体，用于替换面板选视频片段
 */
data class EditorReplaceEntry(
    val trimIn: Long,
    val trimOut: Long,
    val mediaType: Int
) {
    companion object {
        const val TAG = "EditorReplaceEntry"
    }
}