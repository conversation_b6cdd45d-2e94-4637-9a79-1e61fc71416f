/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditableCloudEffectInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class EditableCloudEffectInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @SerializedName("cloudEffectId")
    protected int mCloudEffectId;  //云效果id
    @SerializedName("limitType")
    protected int mLimitType;  //限制使用类型

    public int getCloudEffectId() {
        return mCloudEffectId;
    }

    public void setCloudEffectId(int cloudEffectId) {
        this.mCloudEffectId = cloudEffectId;
    }

    public int getLimitType() {
        return mLimitType;
    }

    public void setLimitType(int limitType) {
        this.mLimitType = limitType;
    }

    @Override
    public String toString() {
        return "EditableCloudEffectInfo{"
                + "mCloudEffectId="
                + mCloudEffectId
                + ", mLimitType="
                + mLimitType
                + '}';
    }
}
