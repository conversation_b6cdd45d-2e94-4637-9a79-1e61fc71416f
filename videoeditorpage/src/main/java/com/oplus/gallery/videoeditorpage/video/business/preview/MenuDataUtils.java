/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: MenuDataUtils
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguang<PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.preview;

import android.content.Context;
import android.content.res.TypedArray;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.video.business.preview.data.IconTextMenuData;

import java.util.ArrayList;
import java.util.List;

public class MenuDataUtils {

    private static final String TAG = "MenuDataUtils";

    public static int[] getResourceIdArrays(Context context, int resourceArrayResId) {
        TypedArray typedArray = context.getResources().obtainTypedArray(resourceArrayResId);
        int resourcesSize = typedArray.length();
        int[] resourceIds = new int[resourcesSize];
        for (int i = 0; i < resourcesSize; i++) {
            resourceIds[i] = typedArray.getResourceId(i, 0);
        }
        typedArray.recycle();
        return resourceIds;
    }

    public static List<IconTextMenuData> initAdapterData(Context context, int idArrayResId,
                                                         int iconArrayResId, int textArrayResId) {
        ArrayList<IconTextMenuData> data = new ArrayList<>();
        int[] ids = getResourceIdArrays(context, idArrayResId);
        int[] icons = getResourceIdArrays(context, iconArrayResId);
        int[] texts = getResourceIdArrays(context, textArrayResId);
        int size = Math.min(Math.min(ids.length, icons.length), texts.length);
        for (int i = 0; i < size; i++) {
            data.add(new IconTextMenuData(ids[i], icons[i], texts[i]));
        }
        return data;
    }

    public static ArrayList<IconTextMenuData> initRatioAdapterData(Context context, int iconArrayResId, int textArrayResId) {
        ArrayList<IconTextMenuData> data = new ArrayList<>();
        int[] icons = getResourceIdArrays(context, iconArrayResId);
        int[] texts = getResourceIdArrays(context, textArrayResId);
        int size = Math.min(icons.length, texts.length);
        for (int i = 0; i < size; i++) {
            IconTextMenuData iconTextMenuData = new IconTextMenuData(i, icons[i], texts[i]);
            try {
                String content = context.getResources().getString(texts[i]);
                iconTextMenuData.setText(content);
            } catch (Exception e) {
                GLog.e(TAG, e.toString());
            }
            data.add(iconTextMenuData);
        }
        return data;
    }
}
