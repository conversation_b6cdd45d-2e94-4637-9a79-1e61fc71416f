/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransitionEntity
 ** Description:编辑视频转场动效的数据载体
 ** Version: 1.0
 ** Date: 2025-07-09
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2025-07-09     1.0
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.bean

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.resource.room.entity.TransitionEntity
import com.oplus.gallery.videoeditorpage.resource.util.ResourceUtils

@Entity(tableName = "transition")
class TransitionItem : Item() {

    /**
     * 唯一id
     */
    @PrimaryKey
    @ColumnInfo(name = "_id")
    var id: String = TextUtil.EMPTY_STRING

    /**
     * 名称词条文本内容，用于显示
     */
    @Ignore
    var nameText: String = TextUtil.EMPTY_STRING

    /**
     * 名称翻译ini包词条id
     */
    @ColumnInfo(name = "name_id")
    var nameId: String = TextUtil.EMPTY_STRING

    /**
     * 名称翻译ini包的请求地址
     */
    @ColumnInfo(name = "name_url")
    var nameUrl: String? = null

    /**
     * 名称翻译ini包的存储路径，这个暂时没必要存入数据库，因为相关词条数据都存入词条表
     */
    @Ignore
    var namePath: String? = null

    /**
     * 缩图路径
     */
    @ColumnInfo(name = "icon_url")
    var iconUrl: String? = null

    /**
     * 缩图路径
     */
    @ColumnInfo(name = "icon_path")
    var iconPath: String? = null

    /**
     * 资源请求地址
     */
    @ColumnInfo(name = "resource_url")
    var resourceUrl: String? = null

    /**
     * 资源id，用于传给美摄SDK指定效果
     */
    @Ignore
    var resourceId: String? = null

    /**
     * 资源存储路径
     */
    @ColumnInfo(name = "resource_path")
    var resourcePath: String? = null
        set(value) {
            resourceId = if (value?.endsWith(ResourceUtils.TRANSITION_SUFFIX) == true) {
                ResourceUtils.splitStringToGetId(value)
            } else {
                value
            }
            field = value
        }

    /**
     * 资源md5
     */
    @ColumnInfo(name = "resource_md5")
    var resourceMd5: String? = null

    /**
     * 下载状态 0：未下载 1：下载完成
     */
    @ColumnInfo(name = "download_state")
    var downloadState: Int = TYPE_NOT_DOWNLOADED

    /**
     * 下载状态 0：未下载 1：下载完成
     */
    @ColumnInfo(name = "builtin")
    var builtin: Int = NO_BUILTIN

    @ColumnInfo(name = "order_num")
    var orderNum = 0

    @ColumnInfo
    var version: String = TextUtil.EMPTY_STRING

    /**
     * 字体文件下载进度值
     */
    @Ignore
    var progress: Int = 0

    override fun isBuiltin(): Boolean {
        return builtin == BUILTIN
    }

    override fun getItemUniqueId(): String = id

    override fun getItemDownloadState(): Int = downloadState

    override fun getItemVersion(): String = version ?: TextUtil.EMPTY_STRING

    fun toEntity(): TransitionEntity {
        return TransitionEntity(
            id,
            nameText,
            nameId,
            iconPath ?: TextUtil.EMPTY_STRING,
            resourceId ?: TextUtil.EMPTY_STRING,
            resourcePath ?: TextUtil.EMPTY_STRING,
            resourceUrl ?: TextUtil.EMPTY_STRING,
            resourceMd5 ?: TextUtil.EMPTY_STRING,
            downloadState,
            progress,
            builtin
        )
    }

    override fun toString(): String {
        return "TransitionItem(id='$id', " +
                "nameText='$nameText', " +
                "nameId='$nameId', " +
                "nameUrl=$nameUrl, " +
                "namePath=$namePath, " +
                "iconUrl=$iconUrl, " +
                "iconPath=$iconPath, " +
                "resourceUrl=$resourceUrl, " +
                "resourceId=$resourceId, " +
                "resourcePath=$resourcePath, " +
                "resourceMd5=$resourceMd5, " +
                "downloadState=$downloadState, " +
                "builtin=$builtin, " +
                "orderNum=$orderNum, " +
                "version='$version', " +
                "progress=$progress" +
                ")"
    }
}