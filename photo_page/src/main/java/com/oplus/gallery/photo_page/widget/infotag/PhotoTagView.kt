/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoTagView.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-05-16
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/05/16  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photo_page.widget.infotag

import android.content.Context
import android.content.res.Resources
import android.graphics.Outline
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.isVisible
import androidx.core.view.updatePaddingRelative
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.details.AlignTextImageSpan
import com.oplus.gallery.photo_page.ui.theme.PhotoTagTheme
import com.oplus.gallery.photo_page.ui.theme.PhotoThemedDrawableRes

/**
 * 大图角标的实际 view，
 * 支持在文字前后设置图片。
 */
internal class PhotoTagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    var theme: PhotoTagTheme = PhotoTagTheme.Dark
        set(value) {
            if (field == value) return
            field = value
            applyTheme()
        }

    private val hasDrawable: Boolean
        get() = startImageView.isVisible || endImageView.isVisible

    private val textView by lazy {
        AppCompatTextView(context).apply {
            setTextAppearance(R.style.Photo_PhotoTagView_Appearance)
        }
    }

    private val startImageView by lazy {
        AppCompatImageView(context).apply {
            updatePaddingRelative(end = PADDING_DRAWABLE_TEXT.toPx)
            isVisible = false
        }
    }

    private val endImageView by lazy {
        AppCompatImageView(context).apply {
            updatePaddingRelative(start = PADDING_DRAWABLE_TEXT.toPx)
            isVisible = false
        }
    }

    private var currentContent: TagContent? = null
    private var startDrawableRes: PhotoThemedDrawableRes? = null
    private var endDrawableRes: PhotoThemedDrawableRes? = null

    val text: CharSequence? get() = textView.text

    init {
        val tagHeight = context.resources.getDimensionPixelSize(R.dimen.photopage_detail_tag_height)
        layoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, tagHeight)
        gravity = Gravity.CENTER_VERTICAL
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View?, outline: Outline) {
                outline.setRoundRect(Rect(0, 0, width, height), ROUNDED_CORNER_RADIUS.toPx)
            }
        }
        clipToOutline = true
        applyTheme()

        useNoDrawablePadding()
        addView(startImageView)
        addView(textView)
        addView(endImageView)
    }

    fun setStartDrawable(drawableRes: PhotoThemedDrawableRes? = null) {
        startDrawableRes = drawableRes
        setDrawableTo(startImageView, drawableRes?.getDrawableRes(theme == PhotoTagTheme.Light))
    }

    fun setEndDrawable(drawableRes: PhotoThemedDrawableRes? = null) {
        endDrawableRes = drawableRes
        setDrawableTo(endImageView, drawableRes?.getDrawableRes(theme == PhotoTagTheme.Light))
    }

    fun setContent(content: TagContent) {
        currentContent = content
        textView.text = content.getCharSequence(context, theme)
    }

    private fun applyTheme() {
        val backgroundColor = context.getColor(theme.background)
        // 添加描边效果
        val borderColor = context.getColor(theme.borderColor)
        val shapeDrawable = GradientDrawable().apply {
            shape = GradientDrawable.RECTANGLE
            setColor(backgroundColor)
            setStroke(1, borderColor)
            setCornerRadius(ROUNDED_CORNER_RADIUS.toPx)
        }
        background = shapeDrawable

        currentContent?.let(::setContent)
        val tintColor = context.getColor(theme.tintColor)
        textView.setTextColor(tintColor)

        startDrawableRes?.let(::setStartDrawable)
        endDrawableRes?.let(::setEndDrawable)
    }

    private fun setDrawableTo(imageView: ImageView, @DrawableRes res: Int?) {
        imageView.setImageResource(res ?: Resources.ID_NULL)
        imageView.isVisible = res != null

        if (hasDrawable) {
            useHasDrawablePadding()
        } else {
            useNoDrawablePadding()
        }
    }

    private fun useNoDrawablePadding() {
        updatePaddingRelative(start = PADDING_START_NO_DRAWABLE.toPx, end = PADDING_END_NO_DRAWABLE.toPx)
    }

    private fun useHasDrawablePadding() {
        updatePaddingRelative(start = PADDING_START_HAS_DRAWABLE.toPx, end = PADDING_END_HAS_DRAWABLE.toPx)
    }

    sealed interface TagContent {
        fun getCharSequence(context: Context, theme: PhotoTagTheme): CharSequence

        data class Text(val text: String) : TagContent {
            override fun getCharSequence(context: Context, theme: PhotoTagTheme): CharSequence {
                return text
            }
        }

        data class Image(val res: PhotoThemedDrawableRes) : TagContent {
            override fun getCharSequence(context: Context, theme: PhotoTagTheme): CharSequence {
                val resId = res.getDrawableRes(theme == PhotoTagTheme.Light)
                return generateImageSpan(context, resId)
            }

            /**
             * 生成带有图片的富文本字符串
             * @param context Context
             * @param imageResourceId 图片的资源ID
             * @param isHeightAlign 是否要将带有图片的富文本字符串高度对齐
             * @return 返回带有图片的富文本字符串
             */
            private fun generateImageSpan(context: Context?, @DrawableRes imageResourceId: Int, isHeightAlign: Boolean = true): CharSequence {
                if (context == null) {
                    GLog.w(TextUtil.TAG, "generateImageSpan, can't generate image span because context is null")
                    return ""
                }
                val imageSpan = AlignTextImageSpan(context, imageResourceId)
                val spannableString = SpannableString(SPANNABLE_STR)
                val startIdx = if (isHeightAlign) ALIGN_HEIGHT_IDX else NOT_ALIGN_HEIGHT_IDX
                val endIdx = SPANNABLE_STR.length
                //当startIdx值取保留ALIGN_HEIGHT_IDX时，保留ZERO_WIDTH_SPACE占位符用于维持Textview的高度与字体高度一致
                spannableString.setSpan(imageSpan, startIdx, endIdx, SpannableString.SPAN_INCLUSIVE_INCLUSIVE)
                return spannableString
            }

            companion object {
                /**
                 * "spannable_str"用于富文本占位符需要，只要不是空字符串即可；"\u200B"是零宽度空格(ZERO_WIDTH_SPACE)，属于不可见unicode字符，
                 * ZERO_WIDTH_SPACE可以用于维持维持富文本的高度与文字高度一致,且对宽度无影响
                 */
                private const val SPANNABLE_STR = "\u200B" + "spannable_str"
                private const val NOT_ALIGN_HEIGHT_IDX = 0
                private const val ALIGN_HEIGHT_IDX = 1
            }
        }
    }

    companion object {
        /**
         * 此 view 的圆角半径，单位：dp
         */
        private const val ROUNDED_CORNER_RADIUS = 4f

        /**
         * 图片和文字之间的间距（如有图片），单位：dp
         */
        private const val PADDING_DRAWABLE_TEXT = 2

        /**
         * 100% Alpha
         */
        private const val FULL_ALPHA = 0xFF

        /**
         * 此 view 没有设置图片时的前间距，单位：dp
         */
        private const val PADDING_START_NO_DRAWABLE = 6

        /**
         * 此 view 没有设置图片时的后间距，单位：dp
         */
        private const val PADDING_END_NO_DRAWABLE = 6

        /**
         * 此 view 设置图片时的前间距，单位：dp
         */
        private const val PADDING_START_HAS_DRAWABLE = 4

        /**
         * 此 view 设置图片时的后间距，单位：dp
         */
        private const val PADDING_END_HAS_DRAWABLE = 6
    }
}