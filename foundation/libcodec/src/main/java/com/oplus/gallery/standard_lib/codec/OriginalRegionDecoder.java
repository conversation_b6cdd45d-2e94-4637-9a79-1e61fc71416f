/*******************************************************
 * Copyright 2010 - 2012 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description    :
 * History      :
 * (ID, Date, Author, Description)
 *
 *******************************************************/
package com.oplus.gallery.standard_lib.codec;

import static java.lang.Math.min;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory.Options;
import android.graphics.BitmapRegionDecoder;
import android.graphics.Rect;

import java.io.FileDescriptor;
import java.io.IOException;

/**
 * <AUTHOR>
 * <p>
 * 2013-11-12
 */
// <PERSON><PERSON><PERSON><PERSON><PERSON> modify for MPO file
public class OriginalRegionDecoder extends RegionDecoder {
    public static final String TAG = "OriginalRegionDecoder";
    private final BitmapRegionDecoder mBitmapRegionDecoder;

    public static OriginalRegionDecoder newInstance(String pathName) throws IOException {
        return new OriginalRegionDecoder(pathName);
    }

    public static OriginalRegionDecoder newInstance(byte[] data, int offset, int length)
            throws IOException {
        return new OriginalRegionDecoder(data, offset, length);
    }

    public static OriginalRegionDecoder newInstance(FileDescriptor fd) throws IOException {
        return new OriginalRegionDecoder(fd);
    }

    private OriginalRegionDecoder(String pathName) throws IOException {
        mBitmapRegionDecoder = BitmapRegionDecoder.newInstance(pathName, false);
    }

    private OriginalRegionDecoder(byte[] data, int offset, int length) throws IOException {
        mBitmapRegionDecoder = BitmapRegionDecoder.newInstance(data, offset, length, false);
    }

    private OriginalRegionDecoder(FileDescriptor fd) throws IOException {
        mBitmapRegionDecoder = BitmapRegionDecoder.newInstance(fd, false);
    }

    @Override
    public Bitmap decodeRegion(int index, Rect rect, Options options) {
        if (index != 0) {
            return null;
        }
        // OS16.0版本gainmap解码逻辑有变化，如果给的区域解码rect超出图片尺寸，解出图片的gainmap会有压缩，这里要做下纠正
        Rect decodeRect = new Rect(rect.left, rect.top, min(getWidth(), rect.right), min(getHeight(), rect.bottom));

        if ((mBitmapRegionDecoder != null)
                && !mBitmapRegionDecoder.isRecycled()
                && (rect != null) && !rect.isEmpty()) {
            return mBitmapRegionDecoder.decodeRegion(decodeRect, options);
        } else {
            return null;
        }
    }

    @Override
    public int getWidth() {
        return mBitmapRegionDecoder.getWidth();
    }

    @Override
    public int getHeight() {
        return mBitmapRegionDecoder.getHeight();
    }

    @Override
    public void recycle() {
        if (mBitmapRegionDecoder != null) {
            mBitmapRegionDecoder.recycle();
        }
    }

    @Override
    protected void finalize() throws Throwable {
        recycle();
        super.finalize();
    }

    @Override
    public boolean isRecycled() {
        return mBitmapRegionDecoder.isRecycled();
    }

}
