/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RecognizeFaceTaskManager.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

import android.graphics.Bitmap;
import android.graphics.Rect;

import com.meicam.sdk.NvsVideoClip;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video.MeicamVideoClip;
import com.oplus.gallery.standard_lib.thread.Future;
import com.oplus.gallery.standard_lib.thread.FutureListener;
import com.oplus.gallery.standard_lib.thread.ThreadPool;
import com.oplus.gallery.videoeditorpage.utlis.AppUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

public class RecognizeFaceTaskManager {
    private static final String TAG = "RecognizeFaceTaskManager";
    private static final int IMAGE_MOVE_MIN_DURATION = 500000;
    private static RecognizeFaceTaskManager sInstance;
    private LinkedBlockingQueue<RecognizeFaceTask> mQueue;
    private volatile boolean mIsRuning;
    private HashMap<String, AutoRect> mCacheFaceMap;
    private HashMap<String, Boolean> mFaceResultMap;
    private Object mLock = new Object();

    public synchronized static RecognizeFaceTaskManager getInstance() {
        if (sInstance == null) {
            sInstance = new RecognizeFaceTaskManager();
        }
        return sInstance;
    }

    public RecognizeFaceTaskManager() {
        this.mQueue = new LinkedBlockingQueue<>();
        this.mCacheFaceMap = new HashMap<>();
        this.mFaceResultMap = new HashMap<>();
    }


    public void start() {
        mIsRuning = true;
        AppUtil.getInstance().getAppGlobal().getThreadPool().submit(new ThreadPool.Job<List<MeicamVideoClip>>() {
            @Override
            public List<MeicamVideoClip> run(ThreadPool.JobContext jc) {
                while (mQueue.size() > 0) {
                    try {
                        RecognizeFaceTask task = mQueue.take();
                        if (task != null) {
                            if (checkCacheFace(task.mVideoClip, task.isLessMinTime())) {
                                continue;
                            }
                            boolean isRecognize = task.recognize();
                            IVideoClip videoClip = task.mVideoClip;
                            if (isRecognize) {
                                synchronized (mLock) {
                                    mFaceResultMap.put(videoClip.getFilePath(), true);
                                    PhotoAutoRect photoAutoRect = videoClip.getPhotoAutoRect();
                                    AutoRect autoRect = task.getCenterFace();
                                    if ((autoRect != null) && (photoAutoRect != null)) {
                                        mCacheFaceMap.put(videoClip.getFilePath(), autoRect);
                                        task.mPhotoAutoRect.referenceFaceRect(autoRect, task.isLessMinTime());
                                        videoClip.setImageMotionROI(photoAutoRect.getStartROI(), photoAutoRect.getEndROI());
                                    }
                                }

                            } else {
                                mFaceResultMap.put(videoClip.getFilePath(), false);
                            }
                        }
                    } catch (InterruptedException e) {
                        GLog.e(TAG, LogFlag.DL, "start, fail:" + e.getMessage());
                    }

                }
                return null;
            }
        }, new FutureListener<List<MeicamVideoClip>>() {

            @Override
            public void onFutureDone(Future<List<MeicamVideoClip>> future) {
                mIsRuning = false;
            }
        });
    }

    public synchronized void addTask(IVideoClip videoClip) {
        if ((videoClip == null) || (videoClip.getVideoType() != NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE)
                || (videoClip.getPhotoAutoRect() == null)
                || !videoClip.isAutoRecognizeRoI()) {
            GLog.i(TAG, "videoClip  is  null or not allow auto setRoI");
            return;
        }
        boolean isLessMinTime = videoClip.getDuration() <= IMAGE_MOVE_MIN_DURATION;
        if (checkCacheFace(videoClip, isLessMinTime)) {
            return;
        }
        try {
            mQueue.put(new RecognizeFaceTask(videoClip, isLessMinTime));
        } catch (InterruptedException e) {
            GLog.e(TAG, LogFlag.DL, "addTask, fail:" + e.getMessage());
        }
        if (!mIsRuning) {
            start();
        }
    }

    public synchronized void addTask(List<MeicamVideoClip> meicamVideoClips) {
        if ((meicamVideoClips == null) || (meicamVideoClips.isEmpty())) {
            return;
        }
        for (MeicamVideoClip videoClip : meicamVideoClips) {
            if ((videoClip == null) || (videoClip.getVideoType() != NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE) || (videoClip.getPhotoAutoRect() == null)) {
                continue;
            }
            boolean isLessMinTime = videoClip.getDuration() <= IMAGE_MOVE_MIN_DURATION;
            if (checkCacheFace(videoClip, isLessMinTime)) {
                continue;
            }
            try {
                mQueue.put(new RecognizeFaceTask(videoClip, isLessMinTime));
            } catch (InterruptedException e) {
                GLog.e(TAG, LogFlag.DL, "addTask, fail:" + e.getMessage());
            }
        }
        if (!mIsRuning) {
            start();
        }
    }


    public boolean checkCacheFace(IVideoClip videoClip, boolean lessMinTime) {
        synchronized (mLock) {
            if (mFaceResultMap.get(videoClip.getFilePath()) == null) {
                return false;
            }
        }
        Boolean faceResult = mFaceResultMap.get(videoClip.getFilePath());
        if (faceResult == null || !faceResult) {
            return true;
        }
        synchronized (mLock) {
            if (mCacheFaceMap.get(videoClip.getFilePath()) != null) {
                AutoRect autoRect = mCacheFaceMap.get(videoClip.getFilePath());
                PhotoAutoRect photoAutoRect = videoClip.getPhotoAutoRect();
                photoAutoRect.referenceFaceRect(autoRect, lessMinTime);
                videoClip.setImageMotionROI(photoAutoRect.getStartROI(), photoAutoRect.getEndROI());
                return true;
            }
        }
        return false;
    }

    class RecognizeFaceTask {
        private List<Rect> mList;
        private float mCurMakeRatio;
        private PhotoAutoRect mPhotoAutoRect;
        private float mWidth;
        private float mHeight;
        private IVideoClip mVideoClip;
        private boolean mLessMinTime;

        public RecognizeFaceTask(IVideoClip videoClip, boolean lessMinTime) {
            this.mVideoClip = videoClip;
            mPhotoAutoRect = videoClip.getPhotoAutoRect();
            mCurMakeRatio = mPhotoAutoRect.getCurMakeRatio();
            mWidth = videoClip.getWidth();
            mHeight = videoClip.getHeight();
            mLessMinTime = lessMinTime;
            mList = new ArrayList<>();
        }

        public boolean isLessMinTime() {
            return mLessMinTime;
        }

        public boolean recognize() {
            return true;
        }

        public AutoRect getCenterFace() {
            if ((mList == null) || mList.isEmpty()) {
                return null;
            }
            float maxAera = 0f;
            AutoRect autoRect = null;
            for (Rect rectF : mList) {
                float aera = (rectF.right - rectF.left) * (rectF.bottom - rectF.top);
                if (aera > maxAera) {
                    maxAera = aera;
                    autoRect = new AutoRect(rectF.left, rectF.top, rectF.right, rectF.bottom, mWidth, mHeight, mCurMakeRatio);
                }
            }
            return autoRect;
        }
    }
}