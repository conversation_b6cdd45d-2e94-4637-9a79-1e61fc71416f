/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TimelineTabFragment.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/07/17
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yegua<PERSON><PERSON>@Apps.Gallery 		2020/07/17		1.0		OPLUS_FEATURE_TAB
 ** luy<PERSON>.<EMAIL>  	    2020/08/10		1.0.1	add fast track view
 *********************************************************************************/
package com.oplus.gallery.timelinepage

import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Point
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.util.LogPrinter
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.forEachIndexed
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.emptyview.COUIEmptyStateView
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.google.android.material.appbar.AppBarLayout
import com.oplus.gallery.basebiz.app.OnFragmentVisibilityChange
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.initBottomMenuBgBlur
import com.oplus.gallery.basebiz.helper.setRoundCornerBgColor
import com.oplus.gallery.basebiz.permission.GalleryNetworkStateListener
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.permission.helper.CTAHelper
import com.oplus.gallery.basebiz.sidepane.ForegroundViewsVisibilityAdjuster
import com.oplus.gallery.basebiz.sidepane.RESTORE_DELAY_TIME
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.isClose
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.sidepane.isVisible
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.IMainTabController
import com.oplus.gallery.basebiz.uikit.fragment.IMenuClickListener
import com.oplus.gallery.basebiz.uikit.fragment.ITabContentFragment
import com.oplus.gallery.basebiz.uikit.fragment.ITimelineTabButtonStateCallback
import com.oplus.gallery.basebiz.uikit.toolbar.OverflowMenuItem
import com.oplus.gallery.basebiz.widget.AnchorFrameLayout
import com.oplus.gallery.basebiz.widget.BlurButtonWrapperView
import com.oplus.gallery.basebiz.widget.MainTabToolbar
import com.oplus.gallery.basebiz.widget.MultiLayerImageView
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cloudsync.OnCloudSwitchListener
import com.oplus.gallery.business_lib.cloudsync.SyncStateInfo
import com.oplus.gallery.business_lib.helper.SeniorPickedTrackManager
import com.oplus.gallery.business_lib.helper.TimelineTrackHelper
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.timeline.TimelineConfig
import com.oplus.gallery.business_lib.timeline.TimelineConfig.DAY
import com.oplus.gallery.business_lib.timeline.TimelineConfig.DAY_LESS_COL
import com.oplus.gallery.business_lib.timeline.TimelineConfig.DAY_MORE_COL
import com.oplus.gallery.business_lib.timeline.TimelineConfig.MONTH
import com.oplus.gallery.business_lib.timeline.TimelineConfig.YEAR
import com.oplus.gallery.business_lib.timeline.animation.FastInSlowOutInterpolator
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_REMAIN_BUTTON
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_SLOT_ITEM
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_TITLE
import com.oplus.gallery.business_lib.timeline.layout.LayoutConfig.Companion.EXTRA_FLOATING_TITLE_OFFSET
import com.oplus.gallery.business_lib.timeline.presentation.Presentation
import com.oplus.gallery.business_lib.timeline.presentation.SwitchAnimationListener
import com.oplus.gallery.business_lib.timeline.view.CloudSyncStateHelper
import com.oplus.gallery.business_lib.timeline.view.TimelineView
import com.oplus.gallery.business_lib.timeline.view.TimelineView.OnScrollListener
import com.oplus.gallery.business_lib.timeline.viewmodel.BaseSlidingWindow
import com.oplus.gallery.business_lib.timeline.viewmodel.TimelineViewModel
import com.oplus.gallery.business_lib.ui.fragment.BaseTimeNodeFragment
import com.oplus.gallery.business_lib.util.FastScrollerMessageFormater.Formater
import com.oplus.gallery.business_lib.util.TimelineUtils
import com.oplus.gallery.business_lib.util.TimelineUtils.DEBUG_LOG
import com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.util.draganddrop.DropAlbumManager
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Key.CROP_RATIO
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Key.PICKED_LABEL
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Key.PICKED_SCORE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Key.PRESENTATION_CARD_VIEW_TYPE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_COLON
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_VERTICAL_LINE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_PICKED_DAY_PAGE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_PICKED_MONTH_PAGE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_PICKED_YEAR_PAGE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_TIMELINE_PAGE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.PHOTO_PAGE
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.lifecycle.LocalBroadcastRegister
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils.toPx
import com.oplus.gallery.foundation.util.ext.increaseValue
import com.oplus.gallery.foundation.util.ext.isActivityInvalid
import com.oplus.gallery.foundation.util.ext.isHighlight
import com.oplus.gallery.foundation.util.ext.toStringBySplit
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.math.isInvalid
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.cloudsync.EmptyInfo
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SENIOR_PICKED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_BRIGHTEN_UNIFORMITY
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.PickedModelGetter.Companion.TYPE_PICKED_DAY_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.PickedModelGetter.Companion.TYPE_PICKED_YEAR_MONTH_ALBUM
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.data.timeline.BaseTimelineModel
import com.oplus.gallery.framework.abilities.data.timeline.PickedDayModel
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppConstants.Keys.CLOUD_FUNC_ENABLE
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.ViewVisibilityAlphaAnimatorHelper
import com.oplus.gallery.standard_lib.ui.scroller.FullScreenFastScroller
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.timelinepage.TimelineTabConfig.PICKED_DAY
import com.oplus.gallery.timelinepage.TimelineTabConfig.PICKED_MONTH
import com.oplus.gallery.timelinepage.TimelineTabConfig.PICKED_YEAR
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDayDateLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDayLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDayLessColumnLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDayLessColumnSWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDayMoreColumnLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDayMoreColumnSWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateDaySWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateMonthLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateMonthSWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generatePickedDayLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generatePickedDaySWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generatePickedMonthLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generatePickedMonthSWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generatePickedYearLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generatePickedYearSWConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateYearLayoutConfig
import com.oplus.gallery.timelinepage.TimelineTabConfig.generateYearSWConfig
import com.oplus.gallery.timelinepage.childview.NetworkFloatingViewCallback
import com.oplus.gallery.timelinepage.childview.NetworkFloatingViewConfig
import com.oplus.gallery.timelinepage.childview.QuestionViewConfig
import com.oplus.gallery.timelinepage.childview.TimelineEmptyView
import com.oplus.gallery.timelinepage.childview.TimelineFrameLayout
import com.oplus.gallery.timelinepage.childview.TimelineNetworkFloatingView
import com.oplus.gallery.timelinepage.childview.TimelineQuestionView
import com.oplus.gallery.timelinepage.debug.TimelineDebug
import com.oplus.gallery.timelinepage.header.TimelineHeaderLayout
import com.oplus.gallery.timelinepage.menu.FilterMenuHelper
import com.oplus.gallery.timelinepage.menu.FilterMenuHelper.AlignRule
import com.oplus.gallery.timelinepage.presentation.DayPresentation
import com.oplus.gallery.timelinepage.presentation.PickedDayPresentation
import com.oplus.gallery.timelinepage.presentation.PickedMonthPresentation
import com.oplus.gallery.timelinepage.presentation.PickedYearPresentation
import com.oplus.gallery.timelinepage.presentation.TabImmersiveDayPresentation
import com.oplus.gallery.timelinepage.presentation.YearMonthPresentation
import com.oplus.gallery.timelinepage.slidingwindow.BlockSlidingWindow
import com.oplus.gallery.timelinepage.slidingwindow.DaySlidingWindow
import com.oplus.gallery.timelinepage.slidingwindow.PickedDaySlidingWindow
import com.oplus.gallery.timelinepage.slidingwindow.PickedMonthSlidingWindow
import com.oplus.gallery.timelinepage.slidingwindow.PickedYearSlidingWindow
import com.oplus.gallery.timelinepage.slotcarouse.SlotCarouselController
import com.oplus.gallery.timelinepage.slotcarouse.SlotCarouselView
import com.oplus.gallery.timelinepage.strategy.PickedDayRemain2MyPhotoStrategy
import com.oplus.gallery.timelinepage.strategy.PickedMonthTitle2PickedDayStrategy
import com.oplus.gallery.timelinepage.strategy.TimelineTabSwitchFocusStrategy
import com.oplus.gallery.timelinepage.strategy.TimelineTabSyncScrollStrategy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.FileDescriptor
import java.io.PrintWriter
import java.util.Locale
import kotlin.collections.set
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.foundation.ui.R as LibUIR

@Suppress("LargeClass")
@RouterNormal(RouterConstants.RouterName.TIMELINE_TAB_FRAGMENT)
class TimelineTabFragment : BaseTimeNodeFragment(),
    TimelineView.OnVisibleChangedListener,
    ITabContentFragment, IMenuClickListener, ITimelineTabButtonStateCallback, OnFragmentVisibilityChange {
    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_TIMELINE_FRAGMENT
    override val supportOneTouchShare = true
    override val isSupportedPersonalFilter: Boolean = true
    private val timelineViewMarginTop: Int by lazy { resources.getDimensionPixelSize(BasebizR.dimen.main_timeline_tab_margin_top) }
    private val bottomTabNaviHeight: Int by lazy { resources.getDimensionPixelSize(BasebizR.dimen.common_bottom_navigation_menu_height) }
    private val bottomTabNaviPaddingVertical: Int by lazy { resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_segment_padding_vertical) }
    private val mainTabMarginBottom: Int by lazy { resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_bar_margin_bottom) }
    private val mainTabMarginBottomExtra: Int by lazy { resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_bar_margin_bottom_extra) }
    private val animationDuration by lazy { resources.getInteger(LibUIR.integer.duration_switch_no_follow).toLong() }
    private val pickedTypes by lazy { arrayOf(PICKED_DAY, PICKED_MONTH, PICKED_YEAR) }
    private val myPhotoTypes by lazy { arrayOf(YEAR, MONTH, DAY_MORE_COL, DAY, DAY_LESS_COL) }
    private val zoomPresentationScope by lazy { ZoomPresentationScope() }
    private val appBarLayout: AppBarLayout? by lazy { activity?.findViewById(BasebizR.id.appbar_layout) }
    private val anchorView: AnchorFrameLayout? by lazy { activity?.findViewById(BasebizR.id.fl_tab_left) }
    private val filterButtonWrapper: BlurButtonWrapperView? by lazy { activity?.findViewById(BasebizR.id.filterButtonWrapperView) }
    private val filterHighlightButton: ImageView? by lazy { activity?.findViewById(BasebizR.id.iv_segment_filter_highlight) }
    private val switchButtonWrapper: BlurButtonWrapperView? by lazy { activity?.findViewById(BasebizR.id.switchButtonWrapperView) }
    private val switchHighlightButton: ImageView? by lazy { activity?.findViewById(BasebizR.id.iv_segment_present_switcher_highlight) }
    private var screenHeight: Int = 0
    private var timelineParentView: TimelineFrameLayout? = null
    private var timelineHeaderView: TimelineHeaderLayout? = null
    private var timelineEmptyViewStub: ViewStub? = null
    private var slotCarouselStub: ViewStub? = null
    private var timelineEmptyView: TimelineEmptyView? = null
    private var disableAccessibilityViews = mutableListOf<View>()
    private var slotCarouselView: SlotCarouselView? = null
    private var slotCarouselController: SlotCarouselController? = null
    private var appBarAnimatorHelper: ViewVisibilityAlphaAnimatorHelper? = null
    private val filterMenuHelper by lazy {
        activity?.let { FilterMenuHelper(it, anchorView, filterButtonWrapper, filterHighlightButton, timelineViewModel) }
    }
    private val bottomMenuBarContainer: ConstraintLayout? by lazy { view?.findViewById(BasebizR.id.bottomMenuBarContainer) }
    private val bottomMenuBlurLayer: View? by lazy { bottomMenuBarContainer?.findViewById(BasebizR.id.bottomMenuBlurLayer) }

    private val mainTabToolbar: MainTabToolbar? by lazy { (parentFragment as? IMainTabController)?.getToolbar() }

    // 在viewPager中是否可见，和fragment生命周期无关
    private var isPageVisible: Boolean = false
    private val commonAdditionPaddingBottom by lazy { resources.getDimensionPixelSize(BasebizR.dimen.main_timeline_common_addition_padding_bottom) }

    private val isChineseLanguage by lazy { TimeUtils.isChineseLocale(Locale.getDefault()) }

    /**
     * 底部tab沉浸/非沉浸边界线
     *
     * 1. 设计上，最后一行item底部越过边界线时，进行沉浸/非沉浸切换；
     * 2. 边界线为底部Tab按钮的顶部位置
     *
     * TopY = 屏幕高度 - 底部导航Tab的高度  - 底部导航栏高度（注意是底部导航栏高度）- 额外间距高度 + 导航栏Tab内部padding
     */
    private val bottomTabImmersiveBorderlineY: Int
        get() {
            // 额外间距：目前仅全面屏手势（导航条显示）的状态下，设计额外增加了间距
            val extraMargin = if (isFullScreenGesture) mainTabMarginBottomExtra else 0

            // 无虚拟键（全面屏手势） && 手势导航条隐藏 ：底部导航栏高度为0，设计上会增加一个Margin弥补手势条高度
            val bottomNaviBarHeight = if (isFullScreenGestureAndNoBar) mainTabMarginBottom else bottomNaviBarHeight()
            return screenHeight - bottomTabNaviHeight - bottomNaviBarHeight - extraMargin + bottomTabNaviPaddingVertical
        }

    /**
     * 当前是否是小横屏:普通手机/折叠屏副屏（合起） 横屏
     */
    private val isLandscapeAndSmallScreen: Boolean
        get() {
            val config = getCurrentAppUiConfig()
            val isLandscape = config.orientation.current == Configuration.ORIENTATION_LANDSCAPE
            val isSmallScreen = config.screenMode.current == AppUiResponder.ScreenMode.SMALL
            return isLandscape && isSmallScreen
        }

    // 顶栏下拉菜单
    private var popupListWindow: COUIPopupListWindow? = null

    private var dropManager: DropAlbumManager? = null

    private val isTimelinePageMultiViewSwitchSupported by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH)
    }

    private val isSeniorPickedSupported by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SENIOR_PICKED)
    }

    /**
     * 标记的是滑块是否滑动，即一次完成的滑动过程，down （false）-> move（true）-> up（false)
     *
     * @Note 需要注意的是，滑块停止滑动2s后会自动消失，此时需要也需要将标记复位
     */
    private var isFastScrolled: Boolean = false
    // 用于第一次有数据后校验下可见范围
    private var isFirstLoadData = true

    private val floatingTitleOffset
        get() = context?.resources?.getDimensionPixelSize(BasebizR.dimen.base_picked_day_floating_title_offset) ?: 0

    override var trackAlbumPageInfo: String?
        get() {
            return getTrackAlbumPageInfo(timelineView.curPresentationType())
        }
        set(value) {}

    private val networkSupportedList by lazy {
        arrayOf(PICKED_YEAR, PICKED_MONTH, PICKED_DAY, DAY_MORE_COL, DAY, DAY_LESS_COL)
    }

    private val networkFloatingViewConfig: NetworkFloatingViewConfig by lazy {
        object : NetworkFloatingViewConfig {
            override fun getFloatingTipsViewMarginBottom(): Int = getFloatingTipsViewMarginBottomHeight()

            override suspend fun isCanShow(): Boolean {
                return isCurPresentationDataEmpty().not()
                        && timelineView.isSelectionMode.not()
                        && networkSupportedList.contains(timelineView.curPresentation().type)
            }
        }
    }

    private val networkFloatingViewCallback by lazy {
        object : NetworkFloatingViewCallback {
            override fun onViewCreated(view: View, layoutParams: FrameLayout.LayoutParams) {
                // 快速滑动条需显示在网络浮窗上面
                timelineParentView?.post {
                    timelineParentView?.addView(view, findScrollerViewIndex(), layoutParams)
                }
            }

            override fun onClickCallback() {
                questionView.refreshQuestionnaireFloatingView()
            }
        }
    }

    private val networkFloatingView by lazy {
        TimelineNetworkFloatingView(this, networkFloatingViewConfig, networkFloatingViewCallback)
    }

    private val questionViewConfig by lazy {
        object : QuestionViewConfig {
            override fun getFloatingTipsViewMarginBottom(): Int = getFloatingTipsViewMarginBottomHeight()

            override fun isCanShow(): Boolean {
                return isCurPresentationDataEmpty().not()
                        && timelineView.isSelectionMode.not()
                        && isCurrentInPickedPresentation().not()
            }
        }
    }

    private val questionView by lazy {
        TimelineQuestionView(this, questionViewConfig)
    }

    private var myPhotoPresentationType: String = DAY
    private val hideHeaderFooterRunnable = Runnable {
        timelineParentView?.apply {
            setHeaderEnable(false, needAnimation = true, forceHideHeader = true)
            ensureRefreshSyncState()
        }
        networkFloatingView.refreshNetworkFloatingView()
        questionView.refreshQuestionnaireFloatingView()
    }

    private val networkStateListener = object : GalleryNetworkStateListener {
        override fun onStateChanged(state: Int) {
            lifecycleScope.launch(Dispatchers.UI) {
                if (supportMapTitle()) {
                    timelineViewModel.reloadTimeLineMapTitleMaker()
                }
                networkFloatingView.refreshNetworkFloatingView()
            }
        }
    }

    private val positiveOrderObserver = Observer<Boolean> { isPositiveOrder ->
        switchOrder(isPositiveOrder, refreshCloudState = false)
        timelineView.clearFocusIndexCache()
    }

    private val pageExposureCollector = PageExposureCollector()
    private val cloudSyncStateHelper by lazy { activity?.let { CloudSyncStateHelper(it) } }

    @Volatile
    private var syncStateInfo: SyncStateInfo? = null
    private val syncStateChangeListener = object : CloudSyncStateHelper.SyncStateChangeListener() {
        override fun onSyncStateChange(stateInfo: SyncStateInfo) {
            syncStateInfo = if ((stateInfo.applicableScene and SyncStateInfo.SceneFlag.NORMAL) == 0) EmptyInfo() else stateInfo
            view?.post {
                timelineEmptyView?.applySyncStateInfo(syncStateInfo)

                /**
                 * stateInfo为EmptyInfo时，无法替换掉正在显示的同步状态提示，手动设置云同步状态提示不可见
                 */
                if ((syncStateInfo is EmptyInfo) || TextUtils.isEmpty(syncStateInfo?.text)) {
                    timelineParentView?.setHeaderEnable(false)
                } else {
                    timelineParentView?.setHeaderEnable(true)
                    timelineHeaderView?.applySyncStateInfo(syncStateInfo)
                }
            }
        }
    }

    private fun switchOrder(isPositiveOrder: Boolean, refreshCloudState: Boolean = true) {
        if (isCurrentInPickedPresentation()) {
            GLog.d(TAG, LogFlag.DL, "switchOrder. Picked presentation no need header skip.")
            return
        }
        updateHeaderPositionIfNeed(isPositiveOrder)
        if (refreshCloudState) {
            ensureRefreshSyncState()
        }
    }

    private fun updateHeaderPositionIfNeed(isPositiveOrder: Boolean) {
        val headerOnTop = (isPositiveOrder && !timelineView.hashMoreThanOneScreenPhoto()) || !isPositiveOrder
        timelineParentView?.updateHeaderPosition(headerOnTop)
    }

    private val cloudSwitchChangeListener = object : OnCloudSwitchListener {
        override fun onCloudSwitchChange(switchType: OnCloudSwitchListener.SwitchType, open: Boolean) {
            if (switchType == OnCloudSwitchListener.SwitchType.ALBUM_SYNC) {
                timelineEmptyView?.updateCloudSyncLoadingStatus(open)
            }
        }
    }

    private val fastScrollerStateChangeListener = object : TimelineFrameLayout.OnScrollerStateChangeListener {
        override fun onScrollerActionDown() {
            onFastScrollerTouchDown()
        }

        override fun onScrollerActionMove(isToTop: Boolean, isLeaveFromTop: Boolean, isToBottom: Boolean) {
            onFastScrollerTouchMove(isToTop, isLeaveFromTop, isToBottom)
        }

        override fun onScrollerTouchActionUp() {
            onFastScrollerTouchUp()
        }

        override fun onScrollerDismiss() {
            onFastScrollerDismiss()
        }
    }

    private val mainTabController by lazy { parentFragment as? IMainTabController }

    private fun findScrollerViewIndex(): Int {
        timelineParentView?.forEachIndexed { index, view ->
            if (view is FullScreenFastScroller) {
                return index
            }
        }
        return INVALID_INDEX
    }

    override fun getLayoutId(): Int = R.layout.timeline_tab_fragment_layout
    override fun getFastScrollerId(): Int = View.NO_ID

    override fun onCreateSlidingWindows(): List<BaseSlidingWindow> {
        GTrace.traceBegin("TTF.onCreateSlidingWindows")
        val slidingWindows = mutableListOf<BaseSlidingWindow>()
        val context = requireContext()
        val currentAppUiConfig = getCurrentAppUiConfig()
        val contentWidth = getContentWidth()
        val isPositiveOrder = isPositiveOrder()
        val supportDayWithMoreCol = TimelineConfig.isDayWithMoreColumnSupported(context)
        if (isTimelinePageMultiViewSwitchSupported) {
            val yearSwConfig = generateYearSWConfig(context, contentWidth, currentAppUiConfig)
            val monthSwConfig = generateMonthSWConfig(context, contentWidth, currentAppUiConfig)
            val daySwConfig = generateDaySWConfig(context, contentWidth, getCurrentAppUiConfig().windowHeight.current, isImmersivePresentation)
            val dayLessColumnSwConfig = generateDayLessColumnSWConfig(context, contentWidth, getCurrentAppUiConfig().windowHeight.current)

            slidingWindows.add(BlockSlidingWindow(yearSwConfig, { getModel() }, isPositiveOrder))
            slidingWindows.add(BlockSlidingWindow(monthSwConfig, { getModel() }, isPositiveOrder))
            if (supportDayWithMoreCol) {
                val dayMoreColumnSwConfig = generateDayMoreColumnSWConfig(context, contentWidth, getCurrentAppUiConfig().windowHeight.current)
                slidingWindows.add(DaySlidingWindow(dayMoreColumnSwConfig, { getModel() }, isPositiveOrder))
            }
            slidingWindows.add(DaySlidingWindow(daySwConfig, { getModel() }, isPositiveOrder))
            slidingWindows.add(DaySlidingWindow(dayLessColumnSwConfig, { getModel() }, isPositiveOrder))
        } else {
            val daySwConfig = generateDaySWConfig(context, contentWidth, getCurrentAppUiConfig().windowHeight.current, isImmersivePresentation)
            slidingWindows.add(DaySlidingWindow(daySwConfig, { getModel() }, isPositiveOrder))
        }
        if (isSeniorPickedSupported) {
            val pickedDaySwConfig = generatePickedDaySWConfig(context, currentAppUiConfig)
            slidingWindows.add(
                PickedDaySlidingWindow(
                    pickedDaySwConfig,
                    { getModel(TYPE_PICKED_DAY_ALBUM) as PickedDayModel },
                    isPositiveOrder
                )
            )
            val pickedMonthSwConfig = generatePickedMonthSWConfig(context, currentAppUiConfig)
            slidingWindows.add(
                PickedMonthSlidingWindow(
                    pickedMonthSwConfig,
                    { getModel(TYPE_PICKED_YEAR_MONTH_ALBUM) as BaseTimelineModel },
                    isPositiveOrder
                )
            )
            val pickedYearSwConfig = generatePickedYearSWConfig(context, currentAppUiConfig)
            slidingWindows.add(
                PickedYearSlidingWindow(
                    pickedYearSwConfig,
                    { getModel(TYPE_PICKED_YEAR_MONTH_ALBUM) as BaseTimelineModel },
                    isPositiveOrder
                )
            )
        }
        GTrace.traceEnd()
        return slidingWindows
    }

    /**
     * 创建一系列视图，根据设备的情况，比如轻量os，os版本等不同因素，可能结果不同
     * 同时把视图捏合的范围也进行赋值，注意！！顺序很重要！！会决定捏合的顺序
     */
    override fun onCreatePresentations(): List<Presentation> {
        GLog.d(TAG, LogFlag.DL) {
            "onCreatePresentations MultiViewSupported:$isTimelinePageMultiViewSwitchSupported " +
                    "SeniorPickedSupported:$isSeniorPickedSupported isTimelineImmersiveSwitchSupported=$isImmersivePresentation"
        }
        GTrace.traceBegin("$TAG.onCreatePresentations")
        zoomPresentationScope.clearAll()
        val presentations = mutableListOf<Presentation>()
        val context = requireContext()
        val currentAppUiConfig = getCurrentAppUiConfig()
        val supportDayWithMoreCol = TimelineConfig.isDayWithMoreColumnSupported(context)
        val contentWidth = getContentWidth()
        when {
            isTimelinePageMultiViewSwitchSupported && isImmersivePresentation -> {
                presentations.add(YearMonthPresentation(generateYearLayoutConfig(context, contentWidth), lifecycleScope))
                presentations.add(YearMonthPresentation(generateMonthLayoutConfig(context, contentWidth), lifecycleScope))
                if (supportDayWithMoreCol) {
                    presentations.add(TabImmersiveDayPresentation(generateDayMoreColumnLayoutConfig(context, contentWidth), lifecycleScope))
                }
                presentations.add(TabImmersiveDayPresentation(generateDayLayoutConfig(context, contentWidth), lifecycleScope))
                presentations.add(TabImmersiveDayPresentation(generateDayLessColumnLayoutConfig(context, contentWidth), lifecycleScope))
            }
            isTimelinePageMultiViewSwitchSupported -> {
                presentations.add(YearMonthPresentation(generateYearLayoutConfig(context, contentWidth), lifecycleScope))
                presentations.add(YearMonthPresentation(generateMonthLayoutConfig(context, contentWidth), lifecycleScope))
                if (supportDayWithMoreCol) {
                    presentations.add(DayPresentation(generateDayMoreColumnLayoutConfig(context, contentWidth), lifecycleScope))
                }
                presentations.add(DayPresentation(generateDayDateLayoutConfig(context, contentWidth), lifecycleScope))
                presentations.add(DayPresentation(generateDayLessColumnLayoutConfig(context, contentWidth), lifecycleScope))
            }
            isImmersivePresentation ->
                presentations.add(TabImmersiveDayPresentation(generateDayLayoutConfig(context, contentWidth), lifecycleScope))

            else -> presentations.add(DayPresentation(generateDayDateLayoutConfig(context, contentWidth), lifecycleScope))
        }
        presentations.forEach {
            zoomPresentationScope.myPhotoZoomList.add(it.type)
            if (it.supportSelection()) zoomPresentationScope.selectionMyPhotoZoomList.add(it.type)
        }
        if (isSeniorPickedSupported) {
            val pickedPresentations = mutableListOf<Presentation>()
            pickedPresentations.add(
                PickedYearPresentation(
                    generatePickedYearLayoutConfig(context, currentAppUiConfig),
                    lifecycleScope
                )
            )
            pickedPresentations.add(
                PickedMonthPresentation(
                    generatePickedMonthLayoutConfig(context, currentAppUiConfig),
                    lifecycleScope
                )
            )
            val config = generatePickedDayLayoutConfig(context, currentAppUiConfig)
            config.extraInfo[EXTRA_FLOATING_TITLE_OFFSET] = getPickedDayFloatTitleOffset()
            pickedPresentations.add(PickedDayPresentation(config, lifecycleScope))
            pickedPresentations.forEach {
                zoomPresentationScope.pickedPhotoZoomList.add(it.type)
            }
            presentations.addAll(pickedPresentations)
        }
        GTrace.traceEnd()
        return presentations
    }

    override fun shouldUpdateLayouter(uiConfig: AppUiResponder.AppUiConfig): Boolean {
        val contextNonNull = context ?: return false
        val supportDayMoreColBefore = TimelineConfig.isDayWithMoreColumnSupported(
            contextNonNull,
            uiConfig.windowWidth.previous,
            uiConfig.windowHeight.previous
        )
        val supportDayMoreColAfter = TimelineConfig.isDayWithMoreColumnSupported(
            contextNonNull,
            uiConfig.windowWidth.current,
            uiConfig.windowHeight.current
        )
        // 是否支持 7 列视图发生了变化时，需要刷新 layouter
        return (supportDayMoreColBefore != supportDayMoreColAfter)
    }

    override fun onCreateViewModel(): TimelineViewModel = ViewModelProvider(this)[TimelineTabViewModel::class.java].apply {
        isSupportedPersonalFilter = <EMAIL>
    }

    private fun refreshSwitchView() {
        activity ?: return
        GLog.i(TAG, LogFlag.DL) {
            "[refreshSwitchView] isSeniorPickedSupported = $isSeniorPickedSupported , " +
                    " isSelectionMode = ${timelineView.isSelectionMode} , " +
                    " isFastScrollerMessageVisible = ${isFastScrollerMessageVisible()}"
        }
        // 检查下是否需要刷新画廊参数，再做是否开启画廊的判断
        if (isSeniorPickedSupported) {
            networkFloatingView.refreshNetworkFloatingView()
            questionView.refreshQuestionnaireFloatingView()
            refreshTimelineViewPadding()
        }
    }

    private fun refreshSlotCarouselView() {
        activity ?: return
        GLog.i(TAG, LogFlag.DL) {
            "[refreshSlotCarouselView],isPickedSupported=$isSeniorPickedSupported,isFastVisible=${isFastScrollerMessageVisible()}"
        }
        if (isFastScrollerMessageVisible()) {
            slotCarouselView?.visibility = GONE
            return
        }
        if (isSeniorPickedSupported && (slotCarouselView == null)) {
            slotCarouselStub = view?.findViewById(R.id.slot_carousel_view_stub)
            slotCarouselView = slotCarouselStub?.inflate()?.findViewById(R.id.slotCarouselView)
            slotCarouselView?.visibility = GONE
            slotCarouselView?.setLifecycleOwner(this@TimelineTabFragment)
            initSlotCarouselController()
        }
    }

    /**
     * 选中当前的presentType
     *
     * @param type 精选年月日等
     * @param anim 是否执行动画，目前通过控制动画时间控制动画
     * @param skipCondition 是否跳过条件，特殊情况不会按照顺序进行跳跃，如删空所有照片，会从任意状态回我的照片
     */
    private fun selectedTab(type: String, anim: Boolean, skipCondition: Boolean = false) {
        GLog.d(TAG, LogFlag.DL, "onTabSelected, type=$type")
        // 不做动画的场景，可以省略不可达的场景
        if (timelineView.isPresentationReachable(type) || isPersonalFiltered(type) || skipCondition) {
            // 如果A视图处于overscroll状态，切到B视图会中断回弹，进入错误显示状态
            if (!timelineView.isOverScrolling()) {
                timelineView.stopScroll()
            }
            timelineView.setTouchEventEnabled(true)

            // 根据state得到将要跳转的目标视图的presentationType
            if (timelineView.curPresentation().type != type) {
                timelineView.startSwitchAnimation(
                    timelineView.curPresentation().touchFocusIndex,
                    type,
                    if (anim) animationDuration else 0,
                    switchStrategy = TimelineTabSwitchFocusStrategy(timelineViewModel),
                    isCurPresentationDataEmpty()
                )
            }
        }
    }


    override fun getModel(modelType: String, bundle: Bundle): BaseModel<MediaItem> =
        GTrace.trace("getModel-$modelType") {
            DataRepository.getAlbumModel(modelType, bundle)
        }

    override fun onTotalCountChanged(type: String, totalCount: Int) {
        GTrace.traceBegin("onTotalCountChanged")
        if (!isTimelineViewInit()) {
            GLog.d(TAG, LogFlag.DL) { "onTotalCountChanged. timelineView is not initialized" }
            return
        }
        val currentPresentationType = timelineView.curPresentationType()
        GLog.d(logTag, LogFlag.DL) { "onTotalCountChanged, type=$type, currentPresentationType=$currentPresentationType, totalCount=$totalCount" }
        refreshCurrentEmptyPage(type)
        if ((totalCount > 0) && timelineView.visibleItemRange().isInvalid() && isFirstLoadData) {
            // 有数据但是可见范围无效，需要执行布局校验一次矫正可见范围
            timelineView.postLayoutDelay(0)
            isFirstLoadData = false
        }
        // 照片页总个数变化需要刷新页面，以便更新画廊页的+x
        timelineView.invalidate()
        if ((currentPresentationType == PICKED_DAY) && (totalCount == 0)) {
            timelineView.postDelayed({ refreshEmptyPage() }, animationDuration)
        }
        if (type == currentPresentationType) {
            val moreThanOneScreen = timelineView.hashMoreThanOneScreenPhoto()
            val headShowed = timelineParentView?.isHeadViewShow() == true
            if (isPositiveOrder() && !moreThanOneScreen && headShowed) {
                // 最新照片在下方且云服务在上方，照片数量变化后会滑动列表对齐到底部，此时需要主动隐藏云服务提示
                timelineParentView?.hideHeadWithAnim()
            }
            updateHeaderPositionIfNeed(isPositiveOrder())
        }
        GTrace.traceEnd()
    }

    override fun onDataReloadFinish(type: String) {
        super.onDataReloadFinish(type)
        // 在空页面条件下，刷新空页面。解决问题：一开始是空页面，然后去筛选，也是要变成无筛选结果提示
        lifecycleScope.launch(Dispatchers.UI) {
            val isPageEmpty = isCurPresentationDataEmpty()
            val isPresentationSwitching = timelineView.isSwitchingAnimation() || timelineView.isZooming()
            val isCurrentType = (type == timelineView.curPresentationType())
            if (isCurrentType && (isPageEmpty || !isPresentationSwitching)) {
                val filterMenuShowed = filterMenuHelper?.popupIsShowing() ?: false
                mainTabController?.refreshBottomButtonDisableState(isPageEmpty, isPersonalFiltered(), filterMenuShowed)
                updateToolbar()
            }
            if (isPageEmpty) {
                refreshCurrentEmptyPage(type)
            }
        }
    }

    override fun onSpecialCountChange(type: String, bundle: Bundle) {
        GLog.d(TAG, LogFlag.DL, "onSpecialCountChange. type=$type, bundle=$bundle")
        if (!pickedTypes.contains(type)) {
            timelineParentView?.updateSpecialCount(bundle)
        }
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        initTimelineView(view)

        timelineView.apply { canShowZoomInMode = true }
        scrollPositionToImmersive = resources.getDimensionPixelSize(BasebizR.dimen.main_timeline_tab_margin_top)
        registerCloudFuncChangeListener()
        registerObserver()
        timelineView.visibleChangeListener = this
        refreshSlotCarouselView()

        if (isSupportDrop()) {
            contentView?.let {
                dropManager = DropAlbumManager(this)
                dropManager?.bindDropView(it)
            }
        }
        updateToolbar(true)
    }

    private fun updateToolbar(force: Boolean = false) {
        mainTabController?.updateToolbar(force)
    }

    private fun checkPageImmersiveState(
        position: Int = timelineView.scrollPositionExtend(),
        isForceStart: Boolean = false,
        isNeedAnimation: Boolean = true
    ) {
        mainTabController?.checkPageImmersiveState(position, isForceStart, isNeedAnimation)
    }

    /**
     * 页面初始化时，注册相关的 liveData 监听，为了方便后续拓展，先作为方法存在
     */
    private fun registerObserver() {
        timelineView.registerObserver(this)
    }

    /**
     * 获取顶栏标题中时间和地址对应的照片 item 索引范围
     */
    private fun getItemRangeForToolbarUI(): IntRange? {
        return timelineView.findItemRangeFromRect(
            startPoint = Point(0, mainTabToolbar?.bottom ?: 0),
            endPoint = Point(timelineView.right, bottomTabImmersiveBorderlineY),
            isPositiveOrder = isCurPositiveOrder
        )
    }

    private fun initSlotCarouselController() {
        slotCarouselController?.release()
        slotCarouselController = null
        if (pickedTypes.contains(timelineView.curPresentationType()).not()) {
            return
        }
        slotCarouselView?.let {
            slotCarouselController = SlotCarouselController(timelineView, timelineViewModel, it).apply {
                // 设置轮播控件可以播放的位置的上下界，让轮播控件位于顶部和底部的模糊视图下时，停止播放
                minimalPlayableY = mainTabController?.getToolbarBottomY() ?: 0
                maximalPlayableY = bottomTabImmersiveBorderlineY
            }
        }
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    override fun onVisibleChanged(type: String, nodeRange: IntRange, blockRange: IntRange, itemRange: IntRange) {
        if (type == timelineView.curPresentationType()) {
            for (item in itemRange) {
                pageExposureCollector.collect(item)
            }
        }
    }

    override fun isSupportEmptyPage(): Boolean = true

    private fun registerCloudFuncChangeListener() {
        val localContext = context
        if (localContext == null) {
            GLog.d(TAG, LogFlag.DL, "registerCloudFuncChangeListener: context is null")
            return
        }
        LocalBroadcastRegister.Builder(
            localContext,
            lifecycle,
            Lifecycle.State.RESUMED,
            IntentFilter(AppConstants.Action.LOCAL_ACTION_CLOUD_FUNC_CHANGE)
        ).withCheck(AppConstants.Action.LOCAL_ACTION_CLOUD_FUNC_CHANGE) {
            GLog.d(TAG, LogFlag.DL, "registerCloudFuncChangeListener: $it")
            if (IntentUtils.getBooleanExtra(it, CLOUD_FUNC_ENABLE, true)) {
                switchOrder(isPositiveOrder())
            } else {
                timelineParentView?.setHeaderEnable(false)
                ensureRefreshSyncState()
            }
            refreshEmptyPage()
        }.build().register()
    }

    private fun refreshEmptyPage() {
        refreshTimelineEmptyPage()
    }

    private fun refreshCurrentEmptyPage(type: String) {
        if (type == timelineView.curPresentationType()) {
            refreshEmptyPage()
            networkFloatingView.refreshNetworkFloatingView()
            questionView.refreshQuestionnaireFloatingView()
            checkPageImmersiveState(isForceStart = true, isNeedAnimation = false)
        }
    }

    /**
     * 精选年月日的数据应该是一体的，要么都有，要么都没有。
     */
    private fun refreshTimelineEmptyPage() {
        val lastIsTimelineEmptyPage = timelineParentView?.isTimelineEmptyPage
        if (isCurPresentationDataEmpty()) {
            if ((timelineView.getPresentation(myPhotoPresentationType)?.totalCount ?: 0) == 0) {
                showEmptyPageView()
            } else {
                hideEmptyPageView()
            }
            updateToolbar()
        } else {
            hideEmptyPageView()
        }
        if (lastIsTimelineEmptyPage != timelineParentView?.isTimelineEmptyPage) {
            ensureRefreshSyncState()
        }
    }

    override fun showEmptyPageView(resetEmptyPage: Boolean) {
        if (isPersonalFiltered()) {
            timelineView.visibility = GONE
            timelineEmptyView?.visibility = GONE
            super.showEmptyPageView(resetEmptyPage)
            context?.let {
                if (ScreenUtils.isMiddleHeight(it).not() && ScreenUtils.isLargeHeight(it).not()) {
                    emptyPageView?.emptyViewSizeType = COUIEmptyStateView.EMPTY_VIEW_SIZE_TYPE_SMALL
                } else {
                    emptyPageView?.emptyViewSizeType = COUIEmptyStateView.EMPTY_VIEW_SIZE_TYPE_AUTO
                }
            }
            timelineParentView?.isTimelineEmptyPage = true
            return
        }
        super.hideEmptyPageView()
        switchPresentType(myPhotoPresentationType, true)
        if (timelineEmptyViewStub == null) {
            timelineEmptyViewStub = view?.findViewById(R.id.timeline_empty_view_stub)
            timelineEmptyView = timelineEmptyViewStub?.inflate() as? TimelineEmptyView

            /*
            timelineEmptyView被inflate出来的时候先行初始化同步一次当前fragment内缓存的同步状态，
            后面才可以考虑根据前后同步状态判断是否需要去应用新的同步状态
            */
            timelineEmptyView?.applySyncStateInfo(syncStateInfo)
            // 由于timelineEmptyView是有可能在onResumed才被inflate出来，所以这种case下，需要初始化一下它的状态
            if (isResumed) {
                timelineEmptyView?.onStart()
            }
        }
        timelineEmptyView?.apply {
            visibility = VISIBLE
            checkStatus()
            val additionPaddingTop =
                resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_min_height) + statusBarHeight()
            emptyPageView.updateMargin(top = additionPaddingTop)

            activity?.let {
                refreshEmptyViewMarginBottom(getEmptyViewAdditionMarginBottom())
            }
        }
        timelineParentView?.isTimelineEmptyPage = true
    }

    /**
     * EmptyView底部margin需要适配系统导航条形态
     */
    private fun getEmptyViewAdditionMarginBottom(): Int {
        val bottomNaviHeight = resources.getDimensionPixelSize(BasebizR.dimen.common_bottom_navigation_menu_height)
        val bottomTabMarginExtra = resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_bar_margin_bottom_extra)
        return when {
            // 全屏手势(手势指示条显示)
            isFullScreenGesture -> bottomNaviBarHeight() + bottomNaviHeight

            // 全屏手势(手势指示条隐藏)
            isFullScreenGestureAndNoBar -> bottomNaviHeight + bottomTabMarginExtra

            // 虚拟键(这里要减去的是bottomNaviHeight的top_padding , bottomTabMarginExtra 正好等于 bottomNaviHeight 的top_padding)
            else -> bottomNaviBarHeight() + bottomNaviHeight - bottomTabMarginExtra
        }
    }

    override fun hideEmptyPageView() {
        super.hideEmptyPageView()
        timelineView.visibility = VISIBLE
        timelineEmptyView?.visibility = GONE
        timelineParentView?.isTimelineEmptyPage = false
    }

    override fun isShowEmptyPage() = timelineParentView?.isTimelineEmptyPage == true

    override fun isPersonalFiltered(): Boolean {
        return isPersonalFiltered(timelineView.curPresentationType())
    }

    override fun isFilterPanelShowed(): Boolean = filterMenuHelper?.popupIsShowing() ?: false

    override fun refreshTimelineViewPadding() {
        val activityNonNull = activity ?: return
        screenHeight = ScreenUtils.getWindowSize(activityNonNull).y
        timelineView.apply {
            val toolbarTop = statusBarHeight()
            val toolbarMinHeight = resources.getDimensionPixelSize(BasebizR.dimen.main_tab_toolbar_min_height)
            val fontScale = getCurrentAppUiConfig().fontScale.current
            val extraMarginTop = when {
                fontScale >= FONT_SCALE_EXTRA_LARGE -> TIMELINE_VIEW_XL_FONT_EXTRA_MARGIN_TOP_DP.toPx
                fontScale >= FONT_SCALE_LARGE -> TIMELINE_VIEW_L_FONT_EXTRA_MARGIN_TOP_DP.toPx
                else -> 0
            }
            val timelineViewMarginTop = timelineViewMarginTop + extraMarginTop

            val toolbarRect = Rect(left, toolbarTop, right, toolbarTop + toolbarMinHeight)
            val commonTimelineTopPadding = toolbarRect.bottom + timelineViewMarginTop
            val pickedDayTopPadding = 0

            // 视图均与底部Tab保持一定距离，因此 commonTimelineBottomPadding = 屏幕高度 - 底部Tab的top位置即沉浸线 + 额外设计距离（即留白）
            val commonTimelineBottomPadding = screenHeight - bottomTabImmersiveBorderlineY + commonAdditionPaddingBottom

            GLog.d(logTag, LogFlag.DL) {
                "[refreshTimelineViewPadding]:commonTimelineTopPadding = $commonTimelineTopPadding ," +
                        "commonTimelineBottomPadding = $commonTimelineBottomPadding ," +
                        "commonAdditionPaddingBottom = $commonAdditionPaddingBottom ," +
                        "pickedDayTopPadding = $pickedDayTopPadding"
            }

            setPresentationToolbarRect(PICKED_DAY, toolbarRect)
            setPresentationPadding(PICKED_DAY, pickedDayTopPadding, commonTimelineBottomPadding)
            setPresentationPadding(PICKED_YEAR, commonTimelineTopPadding, commonTimelineBottomPadding)
            setPresentationPadding(PICKED_MONTH, commonTimelineTopPadding, commonTimelineBottomPadding)
            listOf(YEAR, MONTH, DAY_MORE_COL, DAY, DAY_LESS_COL).forEach {
                setPresentationPadding(it, commonTimelineTopPadding, commonTimelineBottomPadding)
            }
            timelineParentView?.setPadding(commonTimelineTopPadding, commonTimelineBottomPadding)
        }
    }

    private fun initTimelineView(view: View) {
        GTrace.trace("initTimelineView") {
            registerConfigChangeListener()
            timelineHeaderView = view.findViewById(R.id.timeline_header)
            timelineParentView = view.findViewById(R.id.timeline_frame_layout)
            initViewVisibilityAdjuster(timelineHeaderView)
            // 卖场模式下，13.2及以上版本，冷启动进入相册，默认选中"精选月"，此时需要隐藏顶部及底部提示
            if (isCurrentInPickedPresentation()) {
                timelineParentView?.removeCallbacks(hideHeaderFooterRunnable)
                hideHeaderFooterRunnable.run()
            } else {
                timelineHeaderView?.apply {
                    showCloudSyncGuide((activity as? BaseActivity)?.isFirstColdStartToday ?: false)
                    /*
                    timelineHeaderView被inflate出来的时候先行初始化同步一次当前fragment内缓存的同步状态，
                    后面才可以考虑根据前后同步状态判断是否需要去应用新的同步状态
                    */
                    applySyncStateInfo(syncStateInfo)
                }
            }

            fastScroller = timelineParentView?.findViewById(R.id.timeline_fast_scroller)
            timelineParentView?.onScrollerStateChangeListener = this.fastScrollerStateChangeListener

            switchOrder(isPositiveOrder())
            timelineView.addSwitchAnimationListener(object : SwitchAnimationListener {

                override fun onSwitchAnimationStart(fromType: String, toType: String) {
                    if (!isSwitchBetweenTimeTypes(fromType, toType)) return
                    // 缩小动画开始时，更新沉浸式状态
                    if (timelineViewModel.isPresentationScaleUp(fromType, toType)) {
                        checkPageImmersiveState()
                    }
                }

                override fun onSwitchAnimationEnd(fromType: String, toType: String) {
                    if (!isSwitchBetweenTimeTypes(fromType, toType)) return
                    // 放大动画结束时，更新沉浸式状态
                    if (timelineViewModel.isPresentationScaleDown(fromType, toType)) {
                        checkPageImmersiveState()
                    }
                }
            })
            timelineView.switchCallback = fun(oldType, newType) {
                if (isSeniorPickedSupported) {
                    initSlotCarouselController()
                }

                if (myPhotoTypes.contains(newType)) {
                    myPhotoPresentationType = newType
                }

                if (getTrackAlbumPageInfo(oldType) != getTrackAlbumPageInfo(newType)) {
                    pageExposureCollector.end()
                }
                pageExposureCollector.start(getTrackAlbumPageInfo(newType))

                // 精选日和其他视图间切换时，沉浸式遮罩不做淡入淡出过渡动画，避免闪烁
                if ((oldType == PICKED_DAY) || (newType == PICKED_DAY)) {
                    enterNeedAnimation = false
                }

                if (isCurrentInPickedPresentation()) {
                    timelineParentView?.removeCallbacks(hideHeaderFooterRunnable)
                    hideHeaderFooterRunnable.run()
                } else {
                    timelineParentView?.removeCallbacks(hideHeaderFooterRunnable)
                    timelineParentView?.apply {
                        setHeaderEnable(enable = true, needAnimation = true)
                        ensureRefreshSyncState()
                    }
                    networkFloatingView.refreshNetworkFloatingView()
                    questionView.refreshQuestionnaireFloatingView()
                }
                refreshTimelineViewPadding()
                // 精选切精选，时间轴和精选互相切换，才需要在这里更新沉浸式状态，时间轴内的视图切换在 [SwitchAnimationListener] 中处理
                if (!isSwitchBetweenTimeTypes(oldType, newType)) {
                    checkPageImmersiveState()
                }
                updateToolbar()
                updateHeaderPositionIfNeed(isPositiveOrder())

                changeFilterButtonVisibility(newType)
                changePresentSwitchButtonIcon(newType)
            }
            timelineView.setOnElementClickListener(this)
            timelineView.addOnScrollListener(object : OnScrollListener {
                override fun onScrolled(scrollPosition: Int) {
                    // 滑动过程中的进入需要动画
                    if (sidePaneAccessor?.isSliding() == false) {
                        enterNeedAnimation = true
                    }
                    if (!timelineView.isOverScrolling()) {
                        mainTabController?.updateToolbar()
                    }
                    // 视图切换动画导致的 onScroll 回调不需要更新沉浸式状态
                    if (!timelineView.isZooming() && !timelineView.isSwitchingAnimation()) {
                        checkPageImmersiveState()
                    }
                }

                override fun onScrollStateChanged(newState: Int, scrollPosition: Int) {
                    // do nothing
                }
            })
            timelineView.layoutCompleteListener = {
                updateHeaderPositionIfNeed(isCurPositiveOrder)
            }

            timelineView.setZoomConfigUpdater { zoomConfig ->
                val currentType = timelineView.curPresentationType()
                when {
                    timelineView.isSelectionMode && zoomPresentationScope.selectionMyPhotoZoomList.contains(currentType) -> {
                        zoomConfig.setPresentationList(zoomPresentationScope.selectionMyPhotoZoomList)
                        zoomConfig.isFollowFingers = true
                    }

                    timelineView.isSelectionMode -> zoomConfig.setPresentationList(emptyList()) // 精选日视图进选择模式后禁止捏合
                    zoomPresentationScope.myPhotoZoomList.contains(currentType) -> {
                        val zoomList = timelineViewModel.getMyPhotoZoomList(timelineView.curPresentation(), zoomPresentationScope.myPhotoZoomList)
                        zoomConfig.setPresentationList(zoomList)
                        zoomConfig.isFollowFingers = true
                    }

                    zoomPresentationScope.pickedPhotoZoomList.contains(currentType) -> {
                        zoomConfig.setPresentationList(zoomPresentationScope.pickedPhotoZoomList)
                        zoomConfig.isFollowFingers = false
                    }

                    else -> zoomConfig.setPresentationList(emptyList())
                }
                zoomConfig.interpolator = if (zoomPresentationScope.pickedPhotoZoomList.contains(currentType)
                    || zoomPresentationScope.pickedPhotoZoomList.contains(timelineView.lastPresentation().type)
                ) {
                    LinearInterpolator()
                } else {
                    FastInSlowOutInterpolator()
                }
                zoomConfig.focusStrategy = TimelineTabSwitchFocusStrategy(timelineViewModel)
            }

            timelineView.setSyncScrollStrategy(TimelineTabSyncScrollStrategy(timelineViewModel) { myPhotoPresentationType })

            // 正序需要在设置padding之后滚动到顶部
            if (!isPositiveOrder()) {
                timelineView.scrollToTop()
            }
            timelineViewModel.isPositiveOrder.observeForever(positiveOrderObserver)
        }
    }

    /**
     * @return 是否为时间轴内的视图切换
     */
    private fun isSwitchBetweenTimeTypes(fromType: String, toType: String): Boolean {
        return TimelineTabConfig.isTimelineType(fromType) && TimelineTabConfig.isTimelineType(toType)
    }

    /**
     * 在侧边栏展开收起过程中,将前台视图不需要测量布局的view置为gone
     */
    private fun initViewVisibilityAdjuster(headerView: TimelineHeaderLayout?) {
        sidePaneAccessor?.takeIf { isSidePaneEnabled() }?.let { accessor ->
            // 侧边栏展开收起过程中,禁掉resume页面中不必要的view的绘制和测量,避免测量、绘制耗时
            ForegroundViewsVisibilityAdjuster(this, accessor, { RESTORE_DELAY_TIME }) { view ->
                // 头部不可见只是高度为0,view还是visible状态，所以需要置为gone,避免侧边栏展开收起过程中还会测量和布局
                val isSetViewGone = (view != headerView) || (timelineParentView?.isHeadViewShow() != true)
                isSetViewGone
            }.apply {
                headerView?.let { bindView(it) }
            }
        }
    }

    /**
     * 空实现， initTimelineView() 中 timelineParentView 里面已经有 fastScroller, 并其初始化
     */
    override fun initFastScroller(view: View) {}

    private fun getTrackAlbumPageInfo(type: String): String {
        return when (type) {
            PICKED_DAY -> CUR_PAGE_PICKED_DAY_PAGE
            PICKED_MONTH -> CUR_PAGE_PICKED_MONTH_PAGE
            PICKED_YEAR -> CUR_PAGE_PICKED_YEAR_PAGE
            else -> CUR_PAGE_TIMELINE_PAGE
        }
    }

    override fun getEmptyPageTitleRes(): Int = if (isPersonalFiltered()) {
        BasebizR.string.main_filter_empty_result
    } else {
        super.getEmptyPageTitleRes()
    }

    override fun getBottomBarMenuId(): Int =
        if (isNeedShowCreateMenuFlag()) {
            BasebizR.menu.base_selection_album_with_creation
        } else {
            BasebizR.menu.base_selection_album_without_creation
        }

    override fun onResume() {
        super.onResume()
        GTrace.trace("$TAG.onResume") {
            mainTabController?.setSelectionMode(timelineView.isSelectionMode)
            networkFloatingView.refreshNetworkFloatingView()
            refreshSwitchView()
            refreshEmptyPage()
            questionView.refreshQuestionnaireFloatingView()
            NetworkPermissionManager.addListener(networkStateListener)
            restoreAccessibility(disableAccessibilityViews)
            slotCarouselController?.isPaused = false
            pageExposureCollector.start(getTrackAlbumPageInfo(timelineView.curPresentationType()))
            // 用于防呆
            timelineHeaderView?.isSkipMeasureAndLayout = false
            // 里面有HandlerThread的创建，有耗时，负载高的情况下，会卡主线程
            AppScope.launch(Dispatchers.SINGLE_UN_BUSY) {
                if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                    cloudSyncStateHelper?.addStateChangeListener(syncStateChangeListener)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        preLoadFeature()
    }

    override fun isNeedPredictiveBack(): Boolean = true

    /**
     * 子线程提前调用binder缓存,防止后续使用时阻塞主线程
     * 原因：高负载进大图时卡主线程、提早时机调用
     */
    private fun preLoadFeature() {
        lifecycleScope.launch(Dispatchers.IO) {
            GTrace.trace("[preLoadFeature]") {
                FeatureUtils.isSupport10Bit
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE)
                ConfigAbilityWrapper.getBoolean(IS_SUPPORT_BRIGHTEN_UNIFORMITY)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        timelineEmptyView?.stopProgress()
        slotCarouselController?.isPaused = true
        pageExposureCollector.end()
        disableAccessibility((view as? ViewGroup), disableAccessibilityViews)
        cloudSyncStateHelper?.removeStateChangeListener(syncStateChangeListener)
    }

    /**
     * 确认同步状态是否需要进行强制刷新
     */
    private fun ensureRefreshSyncState() {
        if (timelineHeaderView?.canApplySyncStateInfo() == true
            || timelineEmptyView?.canApplySyncStateInfo() == true
        ) {
            cloudSyncStateHelper?.sendForceRefreshMsg(syncStateChangeListener)
        }
    }

    override fun onStart() {
        super.onStart()
        GTrace.trace("$TAG.onStart") {
            timelineEmptyView?.onStart()
            timelineParentView?.onStart()
        }
    }

    override fun onStop() {
        super.onStop()
        timelineEmptyView?.onStop()
        timelineParentView?.onStop()
        appBarAnimatorHelper?.cancel()
        cloudSyncStateHelper?.removeStateChangeListener(syncStateChangeListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        cloudSyncStateHelper?.cleanStateChangeListener()
        timelineViewModel.isPositiveOrder.removeObserver(positiveOrderObserver)
        slotCarouselController?.release()
        slotCarouselController = null
        unRegisterConfigChangeListener()
        NetworkPermissionManager.removeListener(networkStateListener)
        popupListWindow?.dismiss()
        filterMenuHelper?.dismissPopup()
    }

    /**
     * 注册配置项、设置项变化监听
     */
    private fun registerConfigChangeListener() {
        AppScope.launch(Dispatchers.SINGLE_UN_BUSY) {
            // 云服务开关
            ApiDmManager.getCloudSyncDM().registerSwitchListener(cloudSwitchChangeListener)
        }
    }

    /**
     * 反注册配置项、设置项变化监听
     */
    private fun unRegisterConfigChangeListener() {
        AppScope.launch(Dispatchers.SINGLE_UN_BUSY) {
            ApiDmManager.getCloudSyncDM().unregisterSwitchListener(cloudSwitchChangeListener)
        }
    }

    /**
     * 第一次进入是需要用到
     * @param op
     */
    override fun onUserOPStatementDialog(op: CTAHelper.CTAUserOp) {
        networkFloatingView.refreshNetworkFloatingView()
        questionView.refreshQuestionnaireFloatingView()
    }

    override fun dump(
        prefix: String,
        fd: FileDescriptor?,
        writer: PrintWriter,
        args: Array<out String>?
    ) {
        super.dump(prefix, fd, writer, args)
        timelineView.dump(writer)
    }

    /**
     * 展示顶栏下拉菜单弹窗
     */
    private fun showOverflowMenu() {
        val contextNonNull = context ?: return
        val overflowButtonNonNull = mainTabToolbar?.overflowButtonWrapperView?.getButton<MultiLayerImageView>() ?: return
        if (activity.isActivityInvalid()) {
            return
        }

        // BUG[9378980]极端场景： 更多菜单中，点击选择，在进入选择模式的动画执行过程中，再次点击了更多按钮
        if (isToolBarAnimating()) {
            popupListWindow?.takeIf { it.isShowing }?.dismiss()
            return
        }

        val itemList = getPopupMenuList()
        popupListWindow?.let {
            if (it.isShowing) {
                it.dismiss()
            } else {
                it.itemList = itemList
                it.show(overflowButtonNonNull)
                updateIconPressedEffect(
                    overflowButtonNonNull,
                    isPressed = true
                )
            }
            return
        }

        // 弹出下拉弹窗
        popupListWindow = COUIPopupListWindow(contextNonNull).apply {
            this.itemList = itemList
            setOnItemClickListener { _, _, position, _ ->
                if (this.isShowing && this.itemList?.get(position)?.hasSubMenu() != true) {
                    dismiss()
                }
                when (this.itemList[position].id) {
                    OverflowMenuItem.ITEM_SETTING.id -> {
                        // 进入搜索页或设置页时会出现activity转场动画，由于视频播放用到的SurfaceView不支持alpha动画，会导致出现视频残影，所以需要先中断播放，隐藏SurfaceView
                        slotCarouselController?.stopPlay()
                        enterSettingPage()
                    }
                    OverflowMenuItem.ITEM_SELECT.id -> {
                        slotCarouselController?.stopPlay()
                        timelineViewModel.enterSelectionMode()
                    }
                    OverflowMenuItem.ITEM_DEBUG_ANIM_CONFIG.id -> context?.apply { TimelineDebug.showDebugAnimationConfigDialog(this) }
                    else -> {}
                }
            }
            setUseBackgroundBlur(true)
            show(overflowButtonNonNull)
            updateIconPressedEffect(overflowButtonNonNull, isPressed = true)
            setOnDismissListener {
                updateIconPressedEffect(
                    overflowButtonNonNull,
                    isPressed = false
                )
            }
        }
    }

    /**
     * 获取顶栏下拉菜单列表
     */
    private fun getPopupMenuList(): List<PopupListItem> {
        val contextNonNull = context ?: return listOf()
        val currentPresentation = timelineView.curPresentation().type
        return mutableListOf(OverflowMenuItem.ITEM_SETTING).apply {
            // 非空状态并且是日视图才展示 “选择” 按钮
            if (isCurPresentationDataEmpty().not() && TimelineTabConfig.isDayType(currentPresentation)) {
                add(0, OverflowMenuItem.ITEM_SELECT)
            }
            if (TimelineUtils.DEBUG_ANIMATION_CONFIG) {
                add(OverflowMenuItem.ITEM_DEBUG_ANIM_CONFIG)
            }
        }.map { item ->
            PopupListItem.Builder()
                .setId(item.id)
                .setTitle(contextNonNull.getString(item.titleRes))
                .build()
        }
    }

    override fun showFastScrollerMessageView() {
        super.showFastScrollerMessageView()
        setAppbarVisibilityWithAnimation(GONE)
    }

    override fun hideFastScrollerMessageView() {
        super.hideFastScrollerMessageView()
        setAppbarVisibilityWithAnimation(VISIBLE)
    }

    override fun onFastScrollerTouchDown() {
        isFastScrolled = false
        super.showFastScrollerMessageView()
        updateFastScrollerMessage()
    }

    /**
     * 边界场景：
     * 1. 滑块已经在顶部，此时按下滑块，并向上滑动 -> isFastScrolled.not() 和 isToTop 二者同时为 true
     * => 按照交互要求，标题栏需要显示
     *
     * 2. 按住滑块滑动到顶部，松手，在滑块消失前，再次按下滑块，并向下滑 -> isFastScrolled.not() 和 isLeaveFromTop 二者同时为 true
     * => 按照交互要求，标题栏需要隐藏
     *
     * @Note
     * isToTop 和 isLeaveFromTop 二者不可能同时为 true
     */
    private fun onFastScrollerTouchMove(isToTop: Boolean, isLeaveFromTop: Boolean, isToBottom: Boolean) {
        super.onFastScrollerTouchMove(isToTop, isToBottom)
        /** 从未开始滑动到滑动 */
        if (isFastScrolled.not() && isToTop.not()) {
            isFastScrolled = true
            setAppbarVisibilityWithAnimation(GONE)
        }

        when {
            /** 滑动到顶部 */
            isToTop -> setAppbarVisibilityWithAnimation(VISIBLE)

            /** 滑动到顶部后又滑离 */
            isLeaveFromTop -> setAppbarVisibilityWithAnimation(GONE)
        }
    }

    override fun onFastScrollerTouchUp() {
        isFastScrolled = false
        super.onFastScrollerTouchUp()
    }

    private fun onFastScrollerDismiss() {
        isFastScrolled = false
        setAppbarVisibilityWithAnimation(VISIBLE)
    }

    override fun formatDateMessage(type: String, timestamp: Long): String {
        if (timestamp == 0L) return EMPTY_STRING
        return when (type) {
            YEAR, PICKED_YEAR -> fastScrollerMessageFormater.format(timestamp, Formater.YYYY)
            MONTH, PICKED_MONTH -> fastScrollerMessageFormater.format(timestamp, Formater.YYYY_MM)
            DAY_MORE_COL, DAY, DAY_LESS_COL, PICKED_DAY -> fastScrollerMessageFormater.format(timestamp, Formater.YYYY_MM_DD)
            else -> EMPTY_STRING
        }
    }

    private fun openSearchActivity() {
        Starter.ActivityStarter(context, Bundle().apply {
            putString(IntentConstant.SearchConstant.KEY_JUMP_SOURCE, SearchTrackConstant.Value.USER_FROM_TIMELINE_DATE)
        }, PostCard(RouterConstants.RouterName.SEARCH_ACTIVITY)).start()
    }

    private fun isFastScrollerMessageVisible(): Boolean {
        return fastScroller?.isMessageVisible() ?: false
    }

    private fun setAppbarVisibilityWithAnimation(visibility: Int) {
        if (appBarAnimatorHelper == null) {
            appBarAnimatorHelper = appBarLayout?.let { ViewVisibilityAlphaAnimatorHelper(it) }
        }
        appBarAnimatorHelper?.setVisibilityWithAlphaAnimation(if (visibility == VISIBLE) VISIBLE else GONE)
    }

    override fun onPermissionDialogOKClick() {
        super.onPermissionDialogOKClick()
        questionView.refreshQuestionnaireFloatingView()
    }

    /**
     * 跳转进入设置页
     */
    private fun enterSettingPage() {
        val activityNonNull = activity ?: return
        MenuActionGetter.goToSetting.builder
            .setActivity(activityNonNull)
            .setTrackCallerEntry(
                TrackCallerEntry(trackPage, timelineView.timelineViewModel.modelPath)
            )
            .build().apply {
                MenuOperationManager.doAction(MenuAction.GO_TO_SETTING, this)
            }
    }

    override fun onEnterSelectionMode() {
        super.onEnterSelectionMode()
        context ?: return
        mainTabController?.setSelectionMode(true)
        // 由于 mainTabController.setSelectionMode中bottomMenuBar才初始化，需要在它之后才bind
        bottomMenuHelper.bindMenuView(bottomMenuBar)
        refreshSwitchView()
        networkFloatingView.refreshNetworkFloatingView()
        questionView.refreshQuestionnaireFloatingView()
        refreshTimelineViewPadding()

        slotCarouselController?.isSelectionMode = true
        timelineParentView?.updateCloudSyncViewInteractiveStatus(true)
    }

    override fun onExitSelectionMode() {
        super.onExitSelectionMode()
        context ?: return
        mainTabController?.setSelectionMode(false)
        refreshSwitchView()
        networkFloatingView.refreshNetworkFloatingView()
        questionView.refreshQuestionnaireFloatingView()
        refreshTimelineViewPadding()

        slotCarouselController?.isSelectionMode = false
        timelineParentView?.updateCloudSyncViewInteractiveStatus(false)
    }

    /**
     *TimelineTabFragment的BottomMenu的显示逻辑在Tabragment的setSelectonMode方法中，
     *此处需要空实现以屏蔽super中其他列表的显示逻辑。
     */
    override fun showBottomMenuBar() {
        createBottomMenuIfNeed(bottomMenuBarContainer)
        // 显示底部菜单的实现在[TabFragment.setSelectionMode]，会统一在那里做动画
        bottomMenuBar?.setBackgroundColor(Color.TRANSPARENT)
        bottomMenuBlurLayer?.initBottomMenuBgBlur(menuShowed = true)
    }

    override fun hideBottomMenuBar() {
        // 隐藏底部菜单的实现在[TabFragment.setSelectionMode]，会统一在那里做动画
        bottomMenuBlurLayer?.initBottomMenuBgBlur(menuShowed = false)
    }

    override fun getUserActionCurPage(trackType: String?): String =
        LaunchExitPopupConstant.Value.TIMELINE_PAGE

    /**
     * 卖场模式下，13.2及以上版本，冷启动进入相册，默认选中"精选月". 否则默认选中”我的照片“日视图
     */
    override fun getDefaultPresentationType(): String = DAY

    override fun onPictureChanged(index: Int) {
        super.onPictureChanged(index)
        if ((index != INVALID_INDEX) && !timelineView.isInTop()) {
            timelineParentView?.hideHeadWithoutAnim()
        }
    }

    /**
     * 和 [onPictureChanged] 一样，是大图回调过来的接口，用于退出大图找到图片在时间轴的实际位置
     */
    override fun getItemRect(index: Int, ignoreInvisibility: Boolean): Rect {
        val rect = super.getItemRect(index, ignoreInvisibility)
        if ((index != INVALID_INDEX) && !isPositiveOrder() && !timelineView.isInTop()) {
            timelineParentView?.hideHeadWithoutAnim()
        }
        return rect
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        /*
         * 高度变化也需要把云服务隐藏，否则会出现横屏分屏时修改导航方式，云服务会覆盖到时间轴之上
         * 需要先调用子类实现刷新presentations数组，防止在父类中更新presentations数组时因新旧数组size不一致发生越界闪退问题
         */
        if (uiConfig.windowWidth.isChanged() || uiConfig.windowHeight.isChanged() || uiConfig.screenMode.isChanged()) {
            if (isSeniorPickedSupported && slotCarouselController != null) {
                slotCarouselController?.release()
                slotCarouselView?.let {
                    slotCarouselController = SlotCarouselController(timelineView, timelineViewModel, it).apply {
                        // 设置轮播控件可以播放的位置的上下界，让轮播控件位于顶部和底部的模糊视图下时，停止播放
                        minimalPlayableY = mainTabController?.getToolbarBottomY() ?: 0
                        maximalPlayableY = bottomTabImmersiveBorderlineY
                    }
                }
            }

            timelineParentView?.hideHeadWithoutAnim(force = true)
            networkFloatingView.refreshNetworkFloatingView()
            // 由于侧边栏的需求，需要用使用contentview的宽度来计算空页面的展示区域，而不是config的宽度。这样就需要post到下一帧才能拿到有效的contentview宽度
            timelineEmptyView?.post {
                refreshEmptyPage()
            }
            refreshSwitchView()
            checkPageImmersiveState(isForceStart = true, isNeedAnimation = false)
        }
        super.onAppUiStateChanged(uiConfig)
        // 由于精选年月的设计中，在不同屏幕宽高比下，卡片的尺寸不同，所以需要在屏幕宽度不变，高度发生变化的时候，也去调整精选年月的config，重新读取数据，刷新布局
        if (!isUiConfigChanged(uiConfig) && uiConfig.windowHeight.isChanged() && isSeniorPickedSupported) {
            updatePresentationsWhenHeightChanged()
        }
        timelineHeaderView?.onAppUiStateChanged(uiConfig)
    }

    override fun isUiConfigChanged(config: AppUiResponder.AppUiConfig): Boolean {
        val superUiConfigChange = super.isUiConfigChanged(config)
        var widthChange = false
        if (superUiConfigChange.not()
            && (timelineView.height > 0)
            && (timelineView.height != config.windowHeight.current)) {
            // 正常情况下，timelineView的高度应该和config.windowHeight.current一致 不一致需要重新获取高度
            val currentConfig = getCurrentAppUiConfig().apply {
                activity?.let {
                    val screenSize = ScreenUtils.getWindowSize(it)
                    windowWidth.current = screenSize.x
                    windowHeight.current = screenSize.y
                }
            }
            widthChange = currentConfig.windowWidth.isChanged()
            GLog.d(TAG, LogFlag.DL) { "isUiConfigChanged, widthChange=$widthChange, config=$currentConfig" }
        }
        return superUiConfigChange || widthChange
    }

    private fun updatePresentationsWhenHeightChanged() {
        context ?: return
        GTrace.trace("updatePresentationsWhenHeightChanged") {
            val slidingWindows = onCreateSlidingWindows()
            timelineViewModel.updateSlidingWindows(slidingWindows)
            val presentations = onCreatePresentations()
            if (presentations.size != timelineViewModel.windowCount()) {
                throw IllegalStateException("Presentations size should equals to slidingWindows size.")
            }
            timelineView.updatePresentations(presentations)
            timelineView.postDelayed({
                timelineView.requestLayout()
            }, POST_LAYOUT_DELAY)
        }
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        if (timelineParentView?.layoutParams !is ViewGroup.MarginLayoutParams) {
            GLog.d(TAG, LogFlag.DL, "onSystemBarChanged timelineView err")
            return
        }
        refreshTimelineViewPadding()
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            if (isForeground) {
                setStatusBarAppearance(isStatusBarLightAppearance())
                sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint())
            }
            questionView.refreshQuestionnaireFloatingView()
            networkFloatingView.refreshNetworkFloatingView()
            refreshSwitchView()
            windowInsets.naviBarInsets().apply {
                bottomMenuBar?.updateMargin(
                    bottom = if (isFullScreenGestureAndNoBar) {
                        // 无虚拟键（全面屏手势） && 手势导航条隐藏
                        resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_bar_margin_bottom)
                    } else {
                        bottom
                    }
                )
            }
        }
    }

    override fun currentShouldToolbarTint(): Boolean {
        return when {
            isShowEmptyPage() -> false
            timelineView.curPresentation().type == PICKED_DAY -> true
            else -> timelineView.scrollPositionExtend() + timelineView.scrollY.coerceAtLeast(0) > scrollPositionToImmersive
        }
    }

    fun isCurrentInPickedPresentation(): Boolean {
        return isTimelineViewInit() && pickedTypes.contains(timelineView.curPresentation().type)
    }

    override fun onElementClick(nodeIndex: Int, itemIndex: Int, elementType: String, extra: Bundle) {
        when (elementType) {
            ELEMENT_TYPE_TITLE -> dispatchTitleClick(itemIndex)
            ELEMENT_TYPE_SLOT_ITEM -> dispatchItemClick(itemIndex)
            ELEMENT_TYPE_REMAIN_BUTTON -> dispatchRemainButtonClick(nodeIndex)
            else -> super.onElementClick(nodeIndex, itemIndex, elementType, extra)
        }
    }

    private fun dispatchTitleClick(itemIndex: Int) {
        GLog.d(TAG, LogFlag.DL, "dispatchTitleClick. itemIndex=$itemIndex")
        when (timelineView.curPresentationType()) {
            PICKED_MONTH -> {
                // 避免影响按压动画效果，延迟100ms再开始视图切换动画
                timelineView.postDelayed({
                    timelineView.startSwitchAnimation(
                        itemIndex,
                        PICKED_DAY,
                        switchStrategy = PickedMonthTitle2PickedDayStrategy(timelineViewModel)
                    )
                }, SWITCH_ANIMATION_DELAY)
            }
            YEAR -> {
                timelineView.startSwitchAnimation(
                    itemIndex,
                    MONTH,
                    SWITCH_ANIMATION_TIME_TITLE,
                    switchStrategy = TimelineTabSwitchFocusStrategy.YearTimeLabelToMonthStrategy(timelineViewModel)
                )
            }
            MONTH -> {
                timelineView.startSwitchAnimation(
                    itemIndex,
                    getNearDayPresentation(),
                    SWITCH_ANIMATION_TIME_TITLE,
                    switchStrategy = TimelineTabSwitchFocusStrategy.MonthTimeLabelToDayStrategy(timelineViewModel)
                )
            }
        }
    }

    private fun getNearDayPresentation(): String {
        return if (timelineView.getPresentation(DAY_MORE_COL) != null) {
            DAY_MORE_COL
        } else {
            DAY
        }
    }

    private fun dispatchItemClick(itemIndex: Int) {
        if (timelineView.isSelectionMode) {
            GLog.d(TAG, LogFlag.DL, "dispatchItemClick. Current is selection mode, skip.")
            return
        }

        if (TimelineUtils.debugCrop) {
            TimelineDebug.showCropRect(timelineView, session, itemIndex)
            return
        }

        if (TimelineUtils.debugSeniorSelect) {
            TimelineDebug.showSeniorSelectResult(timelineView, itemIndex)
            return
        }

        GLog.d(TAG, LogFlag.DL, "dispatchItemClick. itemIndex=$itemIndex, currentPType=${timelineView.curPresentationType()}")
        if (DEBUG_LOG) {
            timelineView.handler.dump(LogPrinter(Log.VERBOSE, TAG), TIMELINE_TAG)
        }

        when (timelineView.curPresentationType()) {
            PICKED_YEAR -> {
                // 避免影响按压动画效果，延迟100ms再开始视图切换动画
                timelineView.postDelayed(
                    {
                        timelineView.startSwitchAnimation(
                            itemIndex, PICKED_MONTH, animationDuration,
                            switchStrategy = TimelineTabSwitchFocusStrategy(timelineViewModel)
                        )
                        // 点击年度精选条目后，切换标题及精选按钮图标-月度精选
                        updateToolbar()
                    },
                    if (timelineView.isSwitchingAnimation()) 0 else SWITCH_ANIMATION_DELAY
                )
            }

            PICKED_MONTH -> {
                // 避免影响按压动画效果，延迟100ms再开始视图切换动画
                timelineView.postDelayed(
                    {
                        timelineView.startSwitchAnimation(
                            itemIndex, PICKED_DAY, animationDuration,
                            switchStrategy = TimelineTabSwitchFocusStrategy(timelineViewModel)
                        )
                        // 点击月度精选条目后，切换标题及精选按钮图标-每日精选
                        updateToolbar()
                    },
                    if (timelineView.isSwitchingAnimation()) 0 else SWITCH_ANIMATION_DELAY
                )
            }

            YEAR -> timelineView.startSwitchAnimation(itemIndex, MONTH, switchStrategy = TimelineTabSwitchFocusStrategy(timelineViewModel))
            MONTH -> {
                timelineView.startSwitchAnimation(
                    itemIndex,
                    getNearDayPresentation(),
                    switchStrategy = TimelineTabSwitchFocusStrategy(timelineViewModel)
                )
            }
            DAY_MORE_COL, DAY, DAY_LESS_COL, PICKED_DAY -> {
                if (!timelineView.isSwitchingAnimation()) {
                    startPhotoPage(itemIndex)
                }
            }
        }
    }

    override fun startPhotoPage(position: Int, isPreview: Boolean) {
        super.startPhotoPage(position, isPreview)
        // 在视频播放过程进入大图时，需要中断播放，使用视频封面来做过渡动画
        slotCarouselController?.stopPlay()
        pageExposureCollector.trackStartPhoto()
    }

    private fun dispatchRemainButtonClick(nodeIndex: Int) {
        timelineView.startSwitchAnimation(
            focusIndex = nodeIndex,
            targetPresentationType = DAY,
            duration = animationDuration,
            switchStrategy = PickedDayRemain2MyPhotoStrategy(timelineViewModel)
        )
        GLog.d(TAG, LogFlag.DL, "dispatchRemainButtonClick, switch to stateTimeline")
    }

    /**
     * 在当前tab页，点击当前tab页的回调
     */
    override fun onClickSelectedTabAgain() {
        scrollToLatestContent()
    }

    /**
     * 收集页面曝光信息
     * */
    private class PageExposureCollector {

        /** 收集浏览的照片数，用于埋点信息 */
        private val exposureItemSet = mutableSetOf<Int>()

        /** 开始展示年月日精选UI的时间 */
        private var startShowPickedTime = 0L

        /** 当前统计的视图 */
        private var pageType: String = EMPTY_STRING
        private var fromPage: String = EMPTY_STRING
        private var isStartedPhoto: Boolean = false

        fun start(pageType: String) {
            this.pageType = pageType
            exposureItemSet.clear()
            startShowPickedTime = System.currentTimeMillis()
            if (isStartedPhoto) {
                isStartedPhoto = false
                fromPage = PHOTO_PAGE
            }
        }

        fun collect(itemIndex: Int) {
            exposureItemSet.add(itemIndex)
        }

        /**
         * 记录从精选年月日视图进入大图，后续下一次切换视图上报时使用
         */
        fun trackStartPhoto() {
            isStartedPhoto = true
        }

        fun end() {
            if ((startShowPickedTime == 0L) || pageType.isEmpty()) return
            TimelineTrackHelper.trackPickedPage(
                pageType,
                fromPage,
                System.currentTimeMillis() - startShowPickedTime,
                exposureItemSet.size
            )
            fromPage = pageType
            pageType = EMPTY_STRING
        }
    }

    override fun getSelectedViewTypes(path: String): String {
        return mutableMapOf<String, Int>().apply {
            increaseValue(timelineView.getItemViewType(path).toString())
        }.toStringBySplit(
            SYMBOL_COLON,
            SYMBOL_VERTICAL_LINE
        )
    }

    override fun getSelectedCrops(path: String): String {
        return mutableMapOf<String, Int>().apply {
            increaseValue(timelineView.getItemCrop(path))
        }.toStringBySplit(
            SYMBOL_COLON,
            SYMBOL_VERTICAL_LINE
        )
    }

    override fun getTrackExtraMap(menuItemId: Int): Map<String, String>? {
        when (menuItemId) {
            BasebizR.id.action_share, BasebizR.id.action_recycle -> {
                if (timelineView.curPresentationType() != PICKED_DAY) {
                    GLog.w(TAG, LogFlag.DL, "addPickedLabelAndScore must supportSelection, but now type:${timelineView.curPresentationType()}")
                    return emptyMap()
                }
                return mutableMapOf<String, String>().apply {
                    val viewTypeMap = mutableMapOf<String, Int>()
                    val cropMap = mutableMapOf<String, Int>()
                    timelineView.timelineViewModel.getSelectedItems().forEach {
                        viewTypeMap.increaseValue(timelineView.getItemViewType(it.toString()).toString())
                        cropMap.increaseValue(timelineView.getItemCrop(it.toString()))
                    }
                    this[PRESENTATION_CARD_VIEW_TYPE] = viewTypeMap.toStringBySplit(
                        SYMBOL_COLON,
                        SYMBOL_VERTICAL_LINE
                    )
                    this[CROP_RATIO] = cropMap.toStringBySplit(
                        SYMBOL_COLON,
                        SYMBOL_VERTICAL_LINE
                    )
                    val selectedList = timelineView.timelineViewModel.getSelectedItems().map {
                        (it.`object` as? LocalMediaItem)?.filePath ?: EMPTY_STRING
                    }
                    this[PICKED_LABEL] = SeniorPickedTrackManager.getLabelTrack(selectedList)
                    this[PICKED_SCORE] = SeniorPickedTrackManager.getScoreTrack(selectedList)
                }
            }
        }
        return null
    }

    override fun onSidePaneStateChanged(newState: SidePaneState, oldState: SidePaneState) {
        timelineView.getPresentation(PICKED_DAY)?.layoutConfig?.extraInfo?.put(EXTRA_FLOATING_TITLE_OFFSET, getPickedDayFloatTitleOffset())
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        timelineView.getPresentation(PICKED_DAY)?.takeIf { isResumed && it == timelineView.curPresentation() }
            ?.layoutConfig?.extraInfo?.put(EXTRA_FLOATING_TITLE_OFFSET, (floatingTitleOffset * (1 - slideProgress)).toInt())
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        super.onSidePaneSlideStart(newState)
        if (timelineParentView?.isHeadViewShow() == true) {
            // head显示时,跳过view的测量和布局,head不显示时(height=0),已经在ForegroundViewsVisibilityAdjuster将其gone掉了,不需要特殊处理为跳过
            timelineHeaderView?.isSkipMeasureAndLayout = true
            if (isCurPositiveOrder) {
                /**
                 * Marked by zhuodp: 最新照片再底部时，目前存在时间轴侧边栏展开收起时，列表位置跳变的问题【9295553】，会导致云服务提示的位置也发生不稳定跳变
                 * 因此在侧边栏开始展开收起前，先将云服务提示和照片数量提示条整体隐藏
                 */
                timelineParentView?.hideHeadWithoutAnim(force = true)
            }
        }
    }

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        if (timelineHeaderView?.isSkipMeasureAndLayout == true) {
            timelineHeaderView?.postDelayed({ timelineHeaderView?.isSkipMeasureAndLayout = false }, RESTORE_DELAY_TIME)
        }
    }

    override fun onMenuClick(id: Int) {
        if (DoubleClickUtils.isFastDoubleClick()) return
        when (id) {
            BasebizR.id.switchButtonWrapperView,
            BasebizR.id.iv_segment_present_switcher_highlight -> doPresentSwitchAction()
            BasebizR.id.filterButtonWrapperView, BasebizR.id.iv_segment_filter_highlight -> doFilterAction()
            else -> {}
        }
    }

    override fun isPickedPresentation(): Boolean {
        return isCurrentInPickedPresentation()
    }

    override fun getPickedPresentationTitle(): String {
        val titleId = when (timelineView.curPresentationType()) {
            PICKED_YEAR -> R.string.presentation_title_picked_year
            PICKED_MONTH -> R.string.presentation_title_picked_month
            PICKED_DAY -> R.string.presentation_title_picked_day
            else -> BasebizR.string.main_fragment_title_timeline
        }
        return context?.resources?.getString(titleId) ?: ""
    }

    override fun changeFilterButtonState() {
        // 个性化筛选菜单显示时，不切换按钮样式
        if (filterMenuHelper?.popupIsShowing() == true) return
        filterMenuHelper?.changeFilterButtonState()
    }

    /**
     * 获取底部提示（网络、问卷调查）距离底部的高度
     *
     *  【小横屏】: 设计高度（64dp）+ 底部导航栏高度
     *  【非小横屏】：设计高度（88dp）+ 底部导航栏高度
     */
    private fun getFloatingTipsViewMarginBottomHeight(): Int {
        val newMarginBottom = if (isLandscapeAndSmallScreen) {
            resources.getDimensionPixelSize(BasebizR.dimen.main_timeline_float_tips_view_margin_bottom_landspace_small) + bottomNaviBarHeight()
        } else {
            resources.getDimensionPixelSize(BasebizR.dimen.main_timeline_float_tips_view_margin_bottom) + bottomNaviBarHeight()
        }
        return newMarginBottom
    }

    /**
     * 精选日视图悬浮标题到toolbar之间的偏移
     * 侧边栏收起时,侧边栏icon和悬浮标题会重叠，所以需要设置两者之间的间距
     */
    private fun getPickedDayFloatTitleOffset() = if (sidePaneAccessor.isClose()) floatingTitleOffset else 0

    override fun getBottomMenuBarContainer(): ViewGroup? = bottomMenuBarContainer

    override fun updateToolbarSelectedTitle(count: Int) {
        if (isToolBarAnimating()) return
        mainTabToolbar?.setTitle(getToolbarSelectedTitle(count))
    }

    private fun isToolBarAnimating(): Boolean {
        return mainTabController?.isToolBarAnimating() == true
    }

    /**
     * 当前底部是否为沉浸式
     *
     * 按照设计，
     * 1. 最后一个item底部滑动到底部tab栏分段按钮顶部以下时，底部进入沉浸式
     * 2. 最后一个item底部滑动到底部tab栏分段按钮顶部以上时，底部进入非沉浸式
     */
    override fun isBottomImmersiveCurrently(): Boolean {
        return if (isShowEmptyPage()) {
            false
        } else {
            isLastItemUnderBottomTab()
        }
    }

    override fun isTopImmersiveCurrently(): Boolean = currentShouldToolbarTint()

    override fun getToolbarTitle(presentationType: String?): String {
        val contextNonNull = context ?: return EMPTY_STRING
        if (isTimelineViewInit().not()) {
            GLog.d(TAG, LogFlag.DL) { "getToolbarTitle. timelineView is not initialized" }
            return EMPTY_STRING
        }
        val type = presentationType ?: timelineView.curPresentationType()
        if (isInSelectionMode()) {
            return getToolbarSelectedTitle()
        }
        // 非沉浸式日视图或精选视图, 对应各自的固定标题
        if (!isImmersiveDayType(type) || isShowEmptyPage()) {
            return contextNonNull.getString(
                when (type) {
                    PICKED_YEAR -> R.string.presentation_title_picked_year
                    PICKED_MONTH -> R.string.presentation_title_picked_month
                    PICKED_DAY -> R.string.presentation_title_picked_day
                    else -> BasebizR.string.main_fragment_title_timeline
                }
            )
        }
        // 沉浸视图时间轴的日视图，对应的标题为当前时间范围
        return getToolbarTimeRangeText()
    }

    override fun getToolbarTitleAppearanceType(presentationType: String?): Int {
        val type = presentationType ?: timelineView.curPresentationType()
        if (!isImmersiveDayType(type) || isShowEmptyPage()) {
            return MainTabToolbar.TYPE_TITLE_TEXT_APPEARANCE_PRIMARY
        }
        return MainTabToolbar.TYPE_TITLE_TEXT_APPEARANCE_SECONDARY
    }

    override fun getToolbarTitleColor(): Int {
        return if (currentShouldToolbarTint()) titleLightColor else titleDarkColor
    }

    override fun getToolbarSubtitle(presentationType: String?): String {
        val type = presentationType ?: timelineView.curPresentationType()
        if (!isImmersiveDayType(type)) {
            return EMPTY_STRING
        }
        // 沉浸视图日视图下，副标题: 1.选择模式：时间  2.普通模式：无文本
        return if (isInSelectionMode()) {
            getToolbarTimeRangeText(textInOneLine = true)
        } else {
            EMPTY_STRING
        }
    }

    override fun onTopSearchClick() = openSearchActivity()

    override fun onTopOverflowClick() = showOverflowMenu()

    override fun onTopCancelClick() {
        ClickUtil.clickable()
        timelineViewModel.exitSelectionMode()
    }

    /**
     * @param textInOneLine 把获取到的信息放在一行返回
     */
    private fun getToolbarTimeRangeText(textInOneLine: Boolean = false): String {
        val contextNonNull = context ?: return EMPTY_STRING
        val indexRange = getItemRangeForToolbarUI() ?: return EMPTY_STRING
        val timeRangeViewData = timelineViewModel.getFormatedTimelineRange(indexRange.first, indexRange.last) ?: return EMPTY_STRING
        val fromDate = timeRangeViewData.fromDate
        val toDate = timeRangeViewData.toDate

        val sameDayOrSameMonth = timeRangeViewData.isSameDay || timeRangeViewData.isSameMonth

        return when {
            (!isChineseLanguage && timeRangeViewData.isSameYear) -> timeRangeViewData.commonDateRange
            toDate.isBlank() -> fromDate
            (sameDayOrSameMonth || textInOneLine) -> contextNonNull.getString(BasebizR.string.base_date_range_separator, fromDate, toDate)
            else -> {
                val fromDataString = contextNonNull.getString(BasebizR.string.base_date_range_separator, fromDate, EMPTY_STRING)
                "$fromDataString\n$toDate"
            }
        }
    }

    /**
     * 是否为沉浸视图下的日视图
     */
    private fun isImmersiveDayType(presentationType: String): Boolean {
        return TimelineConfig.isTimelineDayType(presentationType) && isImmersivePresentation
    }

    /**
     * 最后一个item是否在底部tab栏分段按钮顶部以下
     */
    private fun isLastItemUnderBottomTab(): Boolean {
        val presentation = timelineView.curPresentation()
        val scrollLimitExtend = timelineView.scrollLimitExtend()
        if (TimelineConfig.isTimelineDayType(presentation.type) && (scrollLimitExtend != 0)) {
            //满一屏时日视图距离底部空白是固定已知的，可以直接判断滑动到底部距离，但不适用于精选年月视图，精选年月视图不同卡片类型高度不统一，底部存在不可控的多余留白
            val distanceToListBottom = scrollLimitExtend - (timelineView.scrollPositionExtend() + timelineView.scrollY)
            return distanceToListBottom > commonAdditionPaddingBottom
        } else {
            // 查找到最后一个item在屏幕中的Rect
            val lastItemOutRect = Rect()
            val lastItemIndex = if (!isCurPositiveOrder || TimelineTabConfig.isPickedType(presentation.type)) {
                presentation.totalCount - 1
            } else {
                0
            }
            presentation.layouter.rectOfItem(lastItemIndex, lastItemOutRect, true)
            val borderLineY = bottomTabImmersiveBorderlineY + timelineView.scrollY
            return lastItemOutRect.bottom > borderLineY
        }
    }

    private fun doPresentSwitchAction() {
        val curType = timelineView.curPresentationType()
        val switchType: String = when (curType) {
            PICKED_YEAR -> PICKED_MONTH
            PICKED_MONTH -> PICKED_DAY
            PICKED_DAY -> myPhotoPresentationType
            else -> PICKED_YEAR
        }
        GLog.d(TAG, LogFlag.DL) {
            "[doPresentSwitchAction] myPhotoPresentationType = $myPhotoPresentationType, curPresentationType = $curType -> switchType = $switchType"
        }
        switchPresentType(switchType)
    }

    /**
     * 切换 时间轴精选，年，月，日，等状态，切换完成后会有监听处理toolbar状态
     *
     * @param switchType 切换类型，表示要切换到的显示类型
     * @param force 是否强制切换，正常都是按顺序切换，特殊场景切换可用true
     */
    private fun switchPresentType(switchType: String, force: Boolean = false) {
        val curType = timelineView.curPresentationType()
        if (curType == switchType) return
        /* 当前fragment可见时才做动画，fragment看不见时不使用动画 */
        selectedTab(switchType, isPageVisible && timelineViewModel.isResumed(), force)
    }

    private fun changePresentSwitchButtonIcon(type: String) {
        if (isSeniorPickedSupported.not()) return
        val isHighLight = when (type) {
            PICKED_YEAR, PICKED_MONTH, PICKED_DAY -> true
            else -> false
        }
        switchButtonWrapper?.setTag(com.oplus.gallery.foundation.util.R.id.tag_is_highlight, isHighLight)
        if (!isHighLight) {
            // 非高亮状态，显示沉浸式按钮，仅修改显隐逻辑
            switchButtonWrapper?.isVisible = true
            switchHighlightButton?.isVisible = false
            return
        }
        val buttonIconId = when (type) {
            PICKED_YEAR -> R.drawable.timeline_ic_picked_year
            PICKED_MONTH -> R.drawable.timeline_ic_picked_month
            PICKED_DAY -> R.drawable.timeline_ic_picked_day
            else -> BasebizR.drawable.main_ic_segment_present_switcher
        }

        // 交互设计：Talkback播报的是下一个状态，比如当前是精选年视图，双击之后，将会切换成精选月，所以播报内容为精选月
        val talkbackResId = when (type) {
            PICKED_YEAR -> BasebizR.string.talkback_main_tab_picked_month
            PICKED_MONTH -> BasebizR.string.talkback_main_tab_picked_day
            PICKED_DAY -> BasebizR.string.talkback_main_tab_photos
            else -> BasebizR.string.talkback_main_tab_picked_year
        }

        switchButtonWrapper?.isVisible = false
        switchHighlightButton?.let {
            it.isVisible = true
            it.setImageResource(buttonIconId)
            it.contentDescription = resources.getString(talkbackResId)
            it.setRoundCornerBgColor(context?.getColor(com.support.appcompat.R.color.coui_color_primary_blue) ?: 0)
        }
    }

    private fun changeFilterButtonVisibility(switchPresentationType: String) {
        val isVisible = when (switchPresentationType) {
            PICKED_YEAR, PICKED_MONTH, PICKED_DAY -> false
            DAY -> true
            else -> true
        }
        filterButtonWrapper?.isVisible = isVisible && (filterButtonWrapper?.isHighlight() != true)
        filterHighlightButton?.isVisible = isVisible && (filterButtonWrapper?.isHighlight() == true)
    }

    private fun doFilterAction() {
        val filterButton = filterButtonWrapper?.getButton<MultiLayerImageView>() ?: return
        context?.let {
            filterMenuHelper?.onMenuStateUpdated = { isShow ->
                updateIconPressedEffect(
                    filterButton,
                    isPressed = isShow
                )
                mainTabController?.refreshBottomButtonDisableState(
                    isShowEmptyPage(),
                    isPersonalFiltered(),
                    isShow
                )
            }
        }

        filterMenuHelper?.let {
            if (it.popupIsShowing()) {
                it.dismissPopup()
            } else {
                // 支持侧边栏 & 侧边栏可见（折叠屏合起状态等同于手机，无侧边栏导航）
                val isPopupMenuRuleEnable = sidePaneAccessor.isSupportSidePane() && sidePaneAccessor.isVisible()
                it.showPopup(
                    isEnablePopupMenuRule = isPopupMenuRuleEnable,
                    alignRule = getFilterMenuAlignRule()
                )
            }
        }
    }


    /**
     * 获取筛选菜单AlignRule
     *
     * 1. 不支持侧边栏或支持侧边栏但不可见（折叠屏合起状态）: 禁用自定义AlignRule，返回null即可；
     * 2. 支持侧边栏：启用自定义AlignRule，展开/收起分别为右/左对齐，RTL则反向；
     */
    private fun getFilterMenuAlignRule(): AlignRule? {
        // 不支持侧边栏（手机）|| 侧边栏不存在（如折叠屏合起状态）
        if (sidePaneAccessor.isSupportSidePane().not() || sidePaneAccessor.isVisible().not()) return null

        return if (ResourceUtils.isRTL(context)) {
            if (sidePaneAccessor.isOpen()) AlignRule.ALIGN_ANCHOR_LEFT else AlignRule.ALIGN_ANCHOR_RIGHT
        } else {
            if (sidePaneAccessor.isOpen()) AlignRule.ALIGN_ANCHOR_RIGHT else AlignRule.ALIGN_ANCHOR_LEFT
        }
    }

    /**
     * 顶部“更多”、底部“筛选”图标按压效果实现
     *
     * @param isPressed 是否按压态
     * @param iconView 图标View
     */
    private fun updateIconPressedEffect(iconView: ImageView?, isPressed: Boolean) {
        if ((iconView is MultiLayerImageView).not()) {
            GLog.w(TAG, LogFlag.DL) { "[updateIconPressedEffect] Icon view is not MultiLayerImageView!" }
            return
        }
        (iconView as MultiLayerImageView).updatePressEffect(isPressed)
    }

    /**
     * Tab页切换时，当前页面可见性回调
     */
    override fun onVisibilityChange(isVisible: Boolean, view: View) {
        this.isPageVisible = isVisible
        if (isVisible.not()) filterMenuHelper?.dismissPopup()
        if (isVisible && timelineView.isLayoutOffsetChange()) {
            timelineView.restPresentLayoutOffset()
            timelineView.postLayoutDelay(0)
        }
    }

    private val titleLightColor by lazy {
        activity?.getColor(BasebizR.color.base_toolbar_title_color_sliding_up) ?: Color.WHITE
    }

    private val titleDarkColor by lazy {
        activity?.getColor(BasebizR.color.base_toolbar_title_color) ?: Color.BLACK
    }

    companion object {
        private const val TAG = "TimelineTabFragment"

        private const val SWITCH_ANIMATION_DELAY = 100L
        private const val SWITCH_ANIMATION_TIME_TITLE = 900L

        private const val FONT_SCALE_LARGE = 1.35
        private const val FONT_SCALE_EXTRA_LARGE = 1.60
        private const val TIMELINE_VIEW_L_FONT_EXTRA_MARGIN_TOP_DP = 12
        private const val TIMELINE_VIEW_XL_FONT_EXTRA_MARGIN_TOP_DP = 25
    }

    /**
     * 时间轴视图捏合变化的范围，不同设备可能数据不同
     * 列表中的顺序即捏合的顺序
     */
    internal data class ZoomPresentationScope(
        // 普通模式，我的照片tab
        val myPhotoZoomList: MutableList<String> = mutableListOf(),
        // 普通模式，精选视图
        val pickedPhotoZoomList: MutableList<String> = mutableListOf(),
        // 选择模式，我的照片tab
        val selectionMyPhotoZoomList: MutableList<String> = mutableListOf()
    ) {
        fun clearAll() {
            myPhotoZoomList.clear()
            pickedPhotoZoomList.clear()
            selectionMyPhotoZoomList.clear()
        }
    }
}