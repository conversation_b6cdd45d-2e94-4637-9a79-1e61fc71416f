/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoOliveViewModel.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/02/01
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/02/01      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.olive

import android.app.Activity
import androidx.annotation.OptIn
import androidx.annotation.UiThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business.contentloading.loader.effect.OliveTBLVideoPlayEffect.Companion.TBL_CUSTOM_EFFECT_TRANSFORM
import com.oplus.gallery.business.renderer.olive.OLiveState
import com.oplus.gallery.business.renderer.olive.OLiveStateOperator
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter.setOliveStatus
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOliveHlgVideo
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.item.isOriginalFile
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_SUPPORT_OLIVE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.ext.updateValueIfChanged
import com.oplus.gallery.foundation.util.lifecycle.SingleLiveEvent
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.Brighten.IS_SUPPORT_ADJUST_OLIVE_EDR_RATE
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.hardware.IScreen.EdrScene.IMAGE_EDR_EXIT
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.brighten.PhotoBrightenViewModel.Companion.isUHdrViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.MediaItemGetter
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.VisibleFrom.VISIBLE_FROM_OLIVE
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PageOperationState
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.OLiveFileFormat
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.OLiveFileStatus
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.OLivePlayType
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.codec.player.effect.TBLVideoPlayEffect
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.tbl.exoplayer2.util.UnstableApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.launch
import kotlin.math.max

/**
 * Olive的ViewModel
 * 执行具体的ViewModel的动作，包括下载原图，构造OliveDrawable等
 */
internal class PhotoOliveViewModel(
    override val pageViewModel: PhotoViewModel,
    private val mediaItemGetter: MediaItemGetter
) : PhotoSubViewModel(pageViewModel) {

    /**
     * 大图是否支持HDR视频的Olive的下变换
     */
    private val isSupportHdrVideoOliveTransform: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM, false)
    }

    /**
     * 当前资源是否支持Olive，每次切换资源后都会重新判断
     */
    val supportOlive: LiveData<Boolean> get() = _supportOlive
    private val _supportOlive = MutableLiveData(false)

    /**
     * Olive长按处理中，
     *
     * 通过获取值的方式判断Olive是否长按了，
     * Olive长按之后，只管通知出去
     *
     * 处理中，则如果之前有进行抠图（但还未显示），则抠图结果不会再显示
     */
    val isLongPressHandling: LiveData<Boolean> get() = _isLongPressHandling
    private val _isLongPressHandling = MutableLiveData(false)

    /**
     * 是否为手动点击播放(包括长按大图，长按/点击播放按钮播放)
     */
    val manualPlayFocusOLiveFormUser: LiveData<Boolean> get() = _manualPlayFocusOLiveFormUser
    private val _manualPlayFocusOLiveFormUser: MutableLiveData<Boolean> = MutableLiveData(false)

    /**
     * 焦点位置
     * olive图片播放状态，外部可以在这里取，或者observer
     */
    val focusSlotOLiveState: LiveData<OLiveState?> get() = _focusSlotOLiveState
    private val _focusSlotOLiveState: MutableLiveData<OLiveState?> = MutableLiveData()

    /**
     * 焦点位置olive播放失败：用于Olive播放失败通知出去
     */
    val focusOLivePlayError: LiveData<Unit> get() = _focusOLivePlayError
    private val _focusOLivePlayError: MutableLiveData<Unit> = MutableLiveData()

    /**
     * olive播放状态
     */
    val oliveEnableStatus: LiveData<Boolean> get() = _oliveEnableStatus
    private val _oliveEnableStatus: MutableLiveData<Boolean> = MutableLiveData(true)

    /**
     * olive已关闭Tips可见性
     */
    val oliveTipsVisible: LiveData<Boolean> get() = _oliveTipsVisible
    private val _oliveTipsVisible: MutableLiveData<Boolean> = MutableLiveData(false)

    /**
     * 引导弹窗可见性
     */
    val guideDialogVisible: LiveData<Boolean?> get() = _guideDialogVisible
    private val _guideDialogVisible: MutableLiveData<Boolean> = MutableLiveData(null)

    /**
     * Olive播放是否完毕
     * 返回值意义：
     * - null 当前资源不是Olive资源
     * - true 当前资源是Olive资源，并且Olive播放完毕了
     * - false 当前资源是Olive资源，但是Olive播放没有完毕
     *
     * 如果之前有抠图，松手的时候，Olive播放已经完毕，则会不显示抠图结果页
     *                         Olive播放没有完毕，则会继续显示抠图结果页
     *
     */
    val isFocusPlayedCompleted: Boolean?
        get() = if (supportOlive.value == true) currentOLiveStateOperator?.isPlayedCompleted() else null

    /**
     * 当前OLive文件，视频裁剪的范围；如果当前不是OLive文件，或并未裁剪过，则返回null。
     */
    val currentOLiveVideoPlayRange: LongRange?
        get() = currentOLiveStateOperator?.videoPlayRange

    /**
     * OLive 播放开关状态
     */
    val oliveEnable: Boolean
        get() = currentOLiveStateOperator?.oliveEnable ?: true


    /**
     * OLive 封面是否已变更
     */
    private val oliveCoverChanged: Boolean
        get() = currentOLiveStateOperator?.oliveCoverChanged ?: false

    /**
     * OLive 声音开关状态
     */
    private val oliveSoundEnable: Boolean
        get() = currentOLiveStateOperator?.oliveSoundEnable ?: true

    /**
     * 事件通知：准备播放Olive资源。
     */
    val prepareToPlayOLiveResource: LiveData<Boolean> get() = _prepareToPlayOLiveResource
    private val _prepareToPlayOLiveResource: SingleLiveEvent<Boolean> = SingleLiveEvent()

    /**
     * 标记当前是否需要强制将Olive资源设置为不可见。
     * 该地方了逻辑为了规避Olive从编辑回到大图后，此时播放会将旁边的Olive资源透过来的问题。
     * 在播放当前Olive资源的时候，会将左右两边的Olive内容给设置为不可见，并在滑动的时候再恢复可见。
     */
    internal var shouldSupressOLiveVisibility: Boolean = false
        private set

    private val focusSlotIndex: Int get() = pageViewModel.dataLoading.focusSlot

    private val currentMediaItem: MediaItem? get() = mediaItemGetter(focusSlotIndex)

    /**
     * 滑动后的可播放的状态
     */
    val scrollPlayableState: LiveData<OliveScrollPlayableState> get() = _scrollPlayableState
    private val _scrollPlayableState: MutableLiveData<OliveScrollPlayableState> = MutableLiveData()

    /**
     * 是否投屏的监听
     */
    private val isCastingObserver = Observer<Boolean> { _ ->
        _supportOlive.updateValueIfChanged(checkIsSupportOlive())
    }

    private val focusSlotViewDataDiffCollector = FlowCollector<DiffedPhotoItemViewData?> {
        GTrace.trace({ "$TAG.focusSlotViewDataDiff" }) {
            val viewDataDiff = pageViewModel.dataLoading.diffedFocusViewData.value
            if (isItemChanged(viewDataDiff).not()) {
                GLog.e(TAG, "<focusSlotViewDataDiffObserver> item is not changed")
                // 未切换资源则直接返回
                return@trace
            }
            // 滑动/数据变化期间，应该是没有OliveState的，因为数据们没有准备好或者根本就不是Olive资源
            _focusSlotOLiveState.updateValueIfChanged(null)
            // 数据变更阶段，资源不可播放
            _scrollPlayableState.updateValueIfChanged(OliveScrollPlayableState.NONE)
            // 菜单操作的状态在每次切换图片或视频后置空
            val isSupportOlive = checkIsSupportOlive()
            if (isSupportOlive.not()) {
                isOliveSlotPausedBefore = false
            }
            // 依据是否是Olive资源设置功能区可见性
            pageViewModel.pageManagement.updateFunctionAreaVisibleState(
                isVisible = isSupportOlive.not(),
                reason = "$TAG.SupportOlive.$isSupportOlive"
            )
            GLog.d(TAG, LogFlag.DL) { "focusSlotViewDataDiffObserver, index:$focusSlotIndex, isSupportOlive:$isSupportOlive" }
            pageViewModel.photoFunc.notifyVisible(VISIBLE_FROM_OLIVE, isSupportOlive.not())
            _supportOlive.updateValueIfChanged(isSupportOlive)
            adjustEdrRateIfNeed()
        }
    }

    /**
     * 是否支持调整图片提亮速率
     */
    private val isSupportAdjustOLiveEdrRate: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(IS_SUPPORT_ADJUST_OLIVE_EDR_RATE, defValue = false)
    }

    /**
     * [IHardwareAbility]配置能力的对象，避免频繁创建
     */
    private val hardwareAbility: IHardwareAbility? by lazy {
        pageViewModel.context.getAppAbility<IHardwareAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[hardwareAbility]init hardwareAbility failed , hardwareAbility is null" }
            }
        }
    }

    /**
     * 检查 olive 是否需要调整提亮速率
     * 4k视频的 olive 左右滑动到到 focus 还是 t 状态，到 c 状态过程中退亮（缩图不支持提亮）再提亮（高清图提亮）会出现亮度骤增，
     * 退亮的速率其实不快，是增加了压暗参数导致用户看起来已经降低亮度了，实际上一做提亮就会去除压暗，导致亮度骤增。
     * 通过设置高速率退亮解决亮度骤增问题。
     */
    private fun adjustEdrRateIfNeed() {
        // 是否支持调整图片提亮速率
        if (isSupportAdjustOLiveEdrRate.not()) {
            GLog.d(TAG, LogFlag.DL) { "adjustEdrRateIfNeed, not support adjust olive edr" }
            return
        }

        // 只有左右滑动的场景会出现骤亮
        if (isPhotoSlotChangedFormUserSlide().not()) {
            GLog.d(TAG, LogFlag.DL) { "adjustEdrRateIfNeed, not user slide" }
            return
        }

        // olive 已经是 video 状态，不需要修改
        if (focusSlotOLiveState.value == OLiveState.VIDEO) {
            GLog.d(TAG, LogFlag.DL) { "adjustEdrRateIfNeed, olive state video" }
            return
        }

        // 新的 focus 文件是否为 4k 视频，封面为 hdr 的 olive
        val newItem = pageViewModel.dataLoading.diffedFocusViewData.value?.newItem.getOrLog("adjustEdrRateIfNeed.newItem") ?: return
        val isNewItemNeedAdjust = newItem.isHdrCover4kVideoLivePhoto()

        // 旧的 focus 文件是否也满足上述条件，且新的 focus 文件是 hdr 封面的 olive，这种情况也会闪亮，需要做快速提亮。如果没有旧的 focus 文件，这个判断置否
        val oldItem = pageViewModel.dataLoading.diffedFocusViewData.value?.oldItem.getOrLog("adjustEdrRateIfNeed.oldItem")
        val isOldItemNeedAdjust = oldItem?.let {
            it.isHdrCover4kVideoLivePhoto() && newItem.isOLive && (newItem.isUHdrViewData() == true)
        } ?: false

        GLog.d(TAG, LogFlag.DL) { "adjustEdrRateIfNeed, isNewItemNeedAdjust=$isNewItemNeedAdjust, isOldItemNeedAdjust=$isOldItemNeedAdjust" }
        if (isNewItemNeedAdjust || isOldItemNeedAdjust) {
            hardwareAbility?.screen?.adjustEdrEnterRateTemporary(
                IMAGE_EDR_EXIT,
                OLIVE_THUMB_TO_CONTENT_EDR_EXIT_RATE,
                OLIVE_THUMB_TO_CONTENT_CUSTOM_EDR_RATE_DURATION
            )
        }
    }

    /**
     * 文件是否为 hdr 封面、4k 视频的 olive
     */
    private fun PhotoItemViewData.isHdrCover4kVideoLivePhoto(): Boolean {
        val videoWidth = extra?.getIntegerExtra(EXTRA_KEY_OLIVE_VIDEO_WIDTH).getOrLog("isHdrCover4kVideoLivePhoto.videoWidth") ?: return false
        val videoHeight = extra?.getIntegerExtra(EXTRA_KEY_OLIVE_VIDEO_HEIGHT).getOrLog("isHdrCover4kVideoLivePhoto.videoHeight") ?: return false
        return isOLive() && (isUHdrViewData() == true) && (max(videoWidth, videoHeight) >= VIDEO_SIZE_THRESHOLD_ON_CACHE_CONTENT)
    }

    /**
     * 标记回到当前界面时，该界面还是Olive资源，且此界面之前是否pause过，用来识别是否为界面的恢复操作
     */
    private var isOliveSlotPausedBefore: Boolean = false

    /**
     * 焦点slot是否是由PhotoPager手动滑动过来的
     * 手动左右滑动大图，OnPageChangeCallback.onPageSelected可以依据scrollState不为PhotoPager.SCROLL_STATE_IDLE判断为是PhotoPager手动滑动过来的。
     * 如果是缩图轴滑动的，OnPageChangeCallback没有onPageScrollStateChanged，所以onPageSelected调用的时候可以判断为非PhotoPager手动滑动过来的。
     *
     * 第一个值：Int 焦点index
     * 第二个值：Boolean 是否通过PhotoPager滑动过来的
     */
    private var isFocusSlotScrollFromPhotoPager: Pair<Int, Boolean> = Pair(-1, false)

    private var currentOLiveStateOperator: OLiveStateOperator? = null

    private val oliveOperatorEventListener by lazy {
        object : OLiveStateOperator.OLiveOperatorEventListener {
            override fun onOLiveStateChanged(operator: OLiveStateOperator, state: OLiveState) {
                if (state == OLiveState.VIDEO) {
                    _scrollPlayableState.updateValueIfChanged(OliveScrollPlayableState.PLAYED)
                }
                _focusSlotOLiveState.updateValueIfChanged(state)
            }

            override fun onError(operator: OLiveStateOperator, errCode: Int) {
                notifyWhenFocusOLivePlayError()
            }
        }
    }

    private val isFeatureSupportOLive by lazy {
        ConfigAbilityWrapper.getBoolean(FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE)
    }

    private val pageOperationStateObserver: Observer<PageOperationState> = Observer<PageOperationState> { operationState ->
        if (operationState.contains(PageOperationState.SuppressPlayback) && checkIsSupportOlive()) {
            stopPlayOLive()
        }
    }

    /**
     * 开始播放olive图片
     *
     *  @param positionRange olive图片里的视频需播放到的指定位置
     *  默认为null: olive图片里的视频会从头播放到其最后一帧
     *  playToPosition： olive图片里的视频会从头播放到position位置
     *  playToPosition: 不做任何操作
     *
     *  @param isMute 播放时是否设置播放器静音
     *
     *  @param autoChangeToCover  true   封面状态的切换会根据播放器状态/其他条件自动维护，默认为true  推荐
     *                            false  在stopPlayOLive时会调用切换到封面，不响应内部播放器状态的变更
     *
     *  @param autoChangeToVideo  true   视频状态的切换会根据播放器状态/其他条件自动维护，默认为true  推荐
     *                            false  只会在外部调用 startPlayOLive 函数时被执行一次，慎用！！！- 如果播放器第一帧没渲染，会存在闪黑问题
     *
     *  @param playEffectList 播放特效列表
     *
     * 详见：[OLiveStateOperator.startPlayOLive]
     */
    @OptIn(UnstableApi::class)
    @UiThread
    internal fun startPlayOLive(
        positionRange: LongRange? = null,
        isMute: Boolean = false,
        autoChangeToCover: Boolean = true,
        autoChangeToVideo: Boolean = true,
        playEffectList: List<String>? = null,
        playType: AVPlayer.VideoPlayType = AVPlayer.VideoPlayType.MAIN

    ) {
        var isMutePlay = isMute
        if (checkIsSupportOlive().not() || currentMediaItem.isOriginalFile().not()) {
            GLog.d(TAG, "[startPlayOLive] unsupported play, focusSlotIndex = $focusSlotIndex, id = ${currentMediaItem?.id}, return")
            return
        }
        // 详情模式下，不让播放Olive
        if (pageViewModel.details.isInDetailsMode.value == true) {
            GLog.d(TAG, LogFlag.DL) { "<startPlayOLive> in details mode, wont play olive" }
            return
        }
        if (oliveEnable.not()) {
            GLog.d(TAG, "[startPlayOLive] oliveEnable is false, unable to support play.")
            return
        }
        if (oliveSoundEnable.not()) {
            isMutePlay = true
        }

        GLog.d(TAG, "[startPlayOLive] focusSlotIndex = $focusSlotIndex, id = ${currentMediaItem?.id}, mediaId = ${currentMediaItem?.mediaId}, "
                + "isMutePlay = $isMutePlay")

        // 下变换效果list
        val transformEffectList: List<String>? = if (isSupportHdrVideoOliveTransform && (currentMediaItem?.isOliveHlgVideo() == true)) {
            listOf(TBL_CUSTOM_EFFECT_TRANSFORM)
        } else null
        // 最终要播放的效果list
        val finalPlayEffectList: List<String>? = if (playEffectList == null) {
            transformEffectList
        } else {
            ArrayList<String>().apply {
                // 下变换效果要放在第0个
                transformEffectList?.let(::addAll)
                addAll(playEffectList)
            }
        }
        currentOLiveStateOperator?.startPlayOLive(
            positionRange = positionRange,
            isMute = isMutePlay,
            autoChangeToCover = autoChangeToCover,
            autoChangeToVideo = autoChangeToVideo,
            playEffectList = finalPlayEffectList,
            playType = playType
        )

        _prepareToPlayOLiveResource.setOrPostValue(true)
    }

    /**
     * 停止播放OLive图片
     */
    @UiThread
    internal fun stopPlayOLive() {
        GLog.d(TAG, "[stopPlayOLive] focusSlotIndex = $focusSlotIndex, id = ${currentMediaItem?.id}")
        currentOLiveStateOperator?.stopPlayOLive()
    }

    /**
     * 赋予Olive播放控制权：当Olive图片资源处于焦点位置时
     *
     * @param oliveOperator 焦点olive的播放器
     */
    @UiThread
    internal fun enduePlaybackControlOfOLive(oliveOperator: OLiveStateOperator) {
        GLog.d(TAG) { "[enduePlaybackControlOfOLive] oliveOperator = $oliveOperator" }
        val isPhotoSlotChangedFormUserSlide = isPhotoSlotChangedFormUserSlide()
        isOliveSlotPausedBefore = false
        _scrollPlayableState.updateValueIfChanged(
            //必须是手动滑动，且当前图片不是详情模式（详情模式不支持播放Olive），且当前图片支持剪辑播放
            if (isPhotoSlotChangedFormUserSlide
                && (pageViewModel.details.isInDetailsMode.value != true)
                && (oliveOperator.isSupportClipPlay == true)
            ) {
                OliveScrollPlayableState.CAN_PLAY
            } else {
                OliveScrollPlayableState.CANNOT_PLAY
            }
        )
        resetMarkStateWhenFocusSlotChanged()
        currentOLiveStateOperator = oliveOperator.apply {
            setOLiveOperatorEventListener(oliveOperatorEventListener, true)
        }
        // 提亮倍数默认为1，切换资源后需要主动更新一下
        pageViewModel.brighten.hdrSdrRatio.value?.let {
            currentOLiveStateOperator?.updateHdrSdrRatio(it)
        }
        _oliveEnableStatus.setOrPostValue(oliveEnable)
        playOLiveIfNeed(isPhotoSlotChangedFormUserSlide)
    }

    /**
     * 设置oliveEnable状态
     * @param enable 开关状态
     * @param fragment 所在的fragment
     * @param done
     */
    internal fun setOliveStatusChange(enable: Boolean, fragment: BaseFragment, done: () -> Unit) {
        currentMediaItem?.let {
            MenuOperationManager.doAction(
                action = MenuAction.SET_OLIVE_ENABLE,
                paramMap = setOliveStatus.builder
                    .setMediaItem(it)
                    .setOliveEnable(enable)
                    .setFragment(fragment)
                    .build()
            )
            done.invoke()
        } ?: GLog.d(TAG, "currentMediaItem is null!")
    }

    /**
     * 通知olive已关闭提示弹窗是否可见
     * @param visible 可见状态
     */
    internal fun notifyOliveTipsVisible(visible: Boolean) {
        _oliveTipsVisible.setOrPostValue(visible)
    }

    /**
     * 释放Olive播放控制权：当Olive图片资源失去焦点时
     *
     * @param oliveOperator 焦点olive的播放器
     *
     * @param resetOLiveState 是否需要重置Olive的播放状态
     */
    @UiThread
    internal fun releasePlaybackControlOfOLive(oliveOperator: OLiveStateOperator) {
        GLog.d(TAG) { "[releasePlaybackControlOfOLive] oliveOperator = $oliveOperator" }
        if (oliveOperator == currentOLiveStateOperator) {
            currentOLiveStateOperator = null
        }
    }

    /**
     * 更新焦点是否来自PhotoPager滑动过来的。
     * 手动左右滑动大图，OnPageChangeCallback.onPageSelected可以依据scrollState不为PhotoPager.SCROLL_STATE_IDLE判断为是PhotoPager手动滑动过来的。
     * 如果是缩图轴滑动的，OnPageChangeCallback没有onPageScrollStateChanged，所以onPageSelected调用的时候可以判断为非PhotoPager手动滑动过来的。
     * @param slot 焦点index
     * @param isFromPhotoPagerScroll 是否通过PhotoPager滑动过来的
     */
    @UiThread
    fun updateFocusSlotIsScrollFromViewPager(slot: Int, isFromPhotoPagerScroll: Boolean) {
        isFocusSlotScrollFromPhotoPager = slot to isFromPhotoPagerScroll
    }

    /**
     * 通知长按处理中
     */
    fun notifyLongPressHandling(isLongPressHandling: Boolean = true) {
        GLog.d(TAG) { "[notifyLongPressHandling]" }
        _isLongPressHandling.setOrPostValue(isLongPressHandling)
    }

    /**
     * 通知焦点olive视频播放失败
     */
    fun notifyWhenFocusOLivePlayError() {
        GLog.d(TAG) { "[notifyWhenFocusOLivePlayError] focus olive play error" }
        _focusOLivePlayError.setOrPostValue(Unit)
    }

    /**
     * 当用户手动播放olive时通知
     *
     * @param isManual 是否为主动播放 true 主动播放，反之为非手动触发
     *
     * 视为主动场景：
     * 1.长按大图/播放按钮
     * 2.单击播放按钮
     */
    fun notifyWhenManualPlayFocusOLiveFromUser(isManual: Boolean = true) {
        GLog.d(TAG) { "[notifyWhenManualPlayFocusOLiveFromUser] isManual = $isManual" }
        _manualPlayFocusOLiveFormUser.setOrPostValue(isManual)

        if (shouldShowDecreaseFluencyHint(isManual)) {
            ToastUtil.showShortToast(R.string.photopage_olive_play_tips_decreased_fluency)
        }
    }

    /**
     * 当olive弹窗消失时通知
     *
     * @param isVisible 是否消失
     */
    internal fun notifyGuideDialogVisibleChanged(isVisible: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[notifyGuideDialogVisibleChanged] isVisible = $isVisible" }
        _guideDialogVisible.setOrPostValue(isVisible)
    }

    /**
     * 检查是否能处理长按
     * - 1.feature不支持olive时，则不能处理长按
     * - 2.如果正在投屏，则不能处理长按
     * - 3.如果触碰到的是超文本，则不执行olive的长按处理
     * - 4.抠图结果出来了，并拖拽了，则无需处理olive的长按
     * - 5.如果文件为非原图，则需要下载原图，不管是否下载成功，都无需再管
     * - 6.其他场景在Olive长按之前就已经判断过或拦截过，所以此处就可以处理长按了。
     */
    fun checkCanHandleLongPressed(activity: Activity?, resultCallback: (conditions: OLiveLongPressConditions) -> Unit) {

        // 当资源不支持olive时，则不能处理长按
        if (checkIsSupportOlive().not()) {
            GLog.d(TAG, "[checkCanHandleLongPressed] checkIsSupportOlive = false, unable to support olive. return")
            resultCallback(OLiveLongPressConditions.NOT_SUPPORT)
            return
        }
        // 2.如果正在投屏，则不能处理长按
        if (pageViewModel.outputChannels.castingManager.isCasting.value == true) {
            GLog.d(TAG, "<checkCanHandleLongPressed> it is casting, skip handle long press. return")
            resultCallback(OLiveLongPressConditions.CASTING)
            return
        }
        // 3.如果触碰到的是超文本，则不执行olive的长按处理
        if (pageViewModel.superText.isTouchDownOnSuperText.value == true) {
            GLog.d(TAG, "<checkCanHandleLongPressed> touch down on super text, skip handle long press. return")
            resultCallback(OLiveLongPressConditions.SUPER_TEXT_CONFLICT)
            return
        }
        if (pageViewModel.superText.isSuperTextSelected.value == true) {
            GLog.d(TAG, "<checkCanHandleLongPressed> super text selected, skip handle long press. return")
            resultCallback(OLiveLongPressConditions.SUPER_TEXT_CONFLICT)
            return
        }

        // 4.抠图结果出来了，并拖拽了，则无需处理olive的长按
        if (pageViewModel.lnS.isDragging.value == true) {
            GLog.d(TAG, "<checkCanHandleLongPressed> lns isDragging, skip handle long press. return")
            resultCallback(OLiveLongPressConditions.LNS_CONFLICT)
            return
        }

        // 5.如果文件为非原图，则需要下载原图，不管是否下载成功，都无需再管
        if (currentMediaItem.isOriginalFile().not()) {
            GLog.d(TAG, "<checkCanHandleLongPressed> isOriginalFile is false, should not support olive, skip long press. return")
            pageViewModel.cloudSync.triggerDownloadOriginal(
                activity = activity,
                scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_OLIVE,
                onDownloadOriginalFailAction = { errCode, errMsg ->
                    GLog.d(TAG) { "<checkCanHandleLongPressed> onDownloadOriginalFailAction errCode=$errCode errMsg=$errMsg" }
                },
                onDownloadOriginalSuccessAction = {
                    GLog.d(TAG) { "<checkCanHandleLongPressed> onDownloadOriginalSuccessAction do nothing. " }
                })
            resultCallback(OLiveLongPressConditions.SLIMMING_FILE)
            return
        }
        resultCallback(OLiveLongPressConditions.READY)
    }

    /**
     * 上报Olive播放信息相关埋点
     * @param playType OLivePlayType OLive播放类型
     * @param triggerLiftShift 是否触发了抠图
     */
    fun trackOlivePlayEvent(playType: OLivePlayType, triggerLiftShift: Boolean = true) {
        val mediaItem = currentMediaItem ?: let {
            GLog.w(TAG, "[trackOlivePlayEvent] currentMediaItem is null, unable to track event.")
            return
        }
        val formatType = when {
            MimeTypeUtils.isJpeg(mediaItem.mimeType) -> OLiveFileFormat.JPEG
            MimeTypeUtils.isHeifOrHeic(mediaItem.mimeType) -> OLiveFileFormat.HEIF
            else -> {
                GLog.w(TAG) { "[trackOlivePlayEvent] not support type:${mediaItem.mimeType}, ignore trace event." }
                return
            }
        }
        val fileStatus = if (mediaItem.isOriginalFile()) OLiveFileStatus.LOCAL_FILE else OLiveFileStatus.SLIM_FILE
        pageViewModel.track.trackOLiveManualPlayEvent(mediaItem.fileSize, formatType, fileStatus, playType, triggerLiftShift)
    }

    /**
     * 是否要获取资源类型为Olive类型
     * 主要用于资源加载时用于判断资源是何种ContentType
     *
     * 判断条件：
     * - 1.当前相册的feature等条件控制当前版本支持Olive
     * - 2.MediaItem的tagFlag能够表明这是一张Olive资源
     * - 3.mimeType是否为jpeg：因相机写入flag的不稳定性 - heif也写，但是吗没写入视频，且当前相册能力仅仅支持jpeg，这里过滤mimeType兜底保证相册显示正常
     *
     * @param tagFlags MediaItem的tagFlag
     * @param mimeType 资源的mimeType
     *
     * @return 是否可以认定资源为Olive类型
     */
    fun shouldTakeOliveContentType(tagFlags: Long, mimeType: String): Boolean {
        // 判断tagFlag中是否支持Olive
        val isTagFlagSupportOlive = (tagFlags and FLAG_SUPPORT_OLIVE == FLAG_SUPPORT_OLIVE)

        return isFeatureSupportOLive && isTagFlagSupportOlive && MimeTypeUtils.isJpeg(mimeType)
    }

    /**
     * UI触发，焦点变更，当丢失、获取焦点后有不同操作
     *
     * 注意：这是 WindowFocus，不是 slotFocus！
     * 注意：这是 WindowFocus，不是 slotFocus！
     * 注意：这是 WindowFocus，不是 slotFocus！
     */
    @UiThread
    fun notifyWindowFocusChanged(isFocused: Boolean) {
        // 当系统焦点丢失时，停止播放olive图片,获取焦点是不做处理，保持原有状态
        if (isFocused.not()) {
            GLog.d(TAG) { "[notifyWindowFocusChanged] stopPlayOLive when window focus changed , isFocused = true" }
            stopPlayOLive()
        }
    }

    /**
     * 标记当前是否需要强制将Olive资源设置为不可见。
     * 该地方了逻辑为了规避Olive从编辑回到大图后，此时播放会将旁边的Olive资源透过来的问题。
     * 在播放当前Olive资源的时候，会将左右两边的Olive内容给设置为不可见，并在滑动的时候再恢复可见。
     *
     * @param shouldSupress
     */
    fun updateSupressOLiveVisibilityFlag(shouldSupress: Boolean) {
        shouldSupressOLiveVisibility = shouldSupress
    }

    /**
     * 判断资源Item是否变更了
     */
    private fun isItemChanged(viewDataDiff: DiffedPhotoItemViewData?): Boolean {
        val newItem = viewDataDiff?.newItem
        val oldItem = viewDataDiff?.oldItem
        // id就是path,path发生变化要更新;
        val isIdTheSame = (newItem?.id == oldItem?.id)
        if (isIdTheSame.not()) {
            if (GProperty.DEBUG_OLIVE_PHOTO == GProperty.FEATURE_FORCE_ENABLE) {
                GLog.d(TAG) { "<isItemChanged> item id is not the same(old=${oldItem?.id} ,new=${newItem?.id}), return true." }
            }
            return true
        }
        // 或者tagFlags发生变化要更新
        val isTagFlagContainsOlive = fun(tagFlags: Long?): Boolean {
            return tagFlags?.let { it and FLAG_SUPPORT_OLIVE == FLAG_SUPPORT_OLIVE } ?: false
        }
        val isOldHasOliveTag = isTagFlagContainsOlive(oldItem?.tagFlags)
        val isNewHasOliveTag = isTagFlagContainsOlive(newItem?.tagFlags)
        if (isOldHasOliveTag != isNewHasOliveTag) {
            if (GProperty.DEBUG_OLIVE_PHOTO == GProperty.FEATURE_FORCE_ENABLE) {
                GLog.d(TAG) { "<isItemChanged> item id is not the same(old=${oldItem?.tagFlags} ,new=${newItem?.tagFlags}), return true." }
            }
            return true
        }
        // 其他认为未变更，无需处理
        return false
    }

    /**
     * 判断当前资源（图片本身及设备其他状态）是否支持Olive
     * - 当feature不支持olive时，则认为不支持OLive
     * - 当currentMediaItem 为null时，则认为不支持OLive
     * - 处于投屏中，不支持Olive
     * - 格式检查，目前仅支持jpg
     * - Olive特有的标志tag
     * - 注意：不是原文件的时候也能长按，只不过长按之后会触发下载原图
     */
    private fun checkIsSupportOlive(): Boolean {

        // 当feature不支持olive时，则认为不支持OLive
        if (isFeatureSupportOLive.not()) {
            GLog.d(TAG, "[checkIsSupportOlive] feature = false, unable to support olive. return false")
            return false
        }
        // 当currentMediaItem 为null时，则认为不支持OLive
        val mediaItem = currentMediaItem ?: let {
            GLog.w(TAG, "<checkIsSupportOlive> currentMediaItem is null, unable to support olive.")
            return false
        }
        // 处于投屏中，不支持Olive
        if (pageViewModel.outputChannels.castingManager.isCasting.value == true) {
            GLog.w(TAG, "<checkIsSupportOlive> casting, not support olive.")
            return false
        }

        // Olive特有的标志tag
        if (mediaItem.isOlivePhoto().not()) {
            GLog.w(TAG, "<checkIsSupportOlive> mediaItem is not a olive photo, skip olive operate.")
            return false
        }
        return true
    }

    /**
     * 当新的焦点界面显示后，重置一些焦点标记
     * 目的:在新的焦点Olive显示时，校正/保证一些状态是一致的
     */
    private fun resetMarkStateWhenFocusSlotChanged() {
        // 重置焦点手势点击状态标志位
        notifyWhenManualPlayFocusOLiveFromUser(false)
        // 重置olive显示状态标志位
        _focusSlotOLiveState.updateValueIfChanged(null)
    }

    /**
     * 在需要的时候播放olive--场景 滑动自动播放OLive
     * 并行条件如下：
     * 1.用户左右滑动大图切换时
     * 2.当前前资源支持区间播放时
     *
     * 注意：此方法配套enduePlaybackControlOfOLive或者焦点界面显示是使用，不可以在其他时机使用/重复调用
     */
    private fun playOLiveIfNeed(slideFromUser: Boolean) {
        if ((currentOLiveStateOperator?.isSupportClipPlay == true)
            && (pageViewModel.details.isInDetailsMode.value != true)
            && (slideFromUser)
        ) {
            //如果olive有切换封面，则只进行补帧特效，不需要执行FOV相关特效
            val olivePlayEffects = if (oliveCoverChanged) oliveClickPlayEffects else oliveSlidePlayEffects
            startPlayOLive(
                positionRange = currentOLiveStateOperator?.clipPositionRange,
                isMute = true,
                playEffectList = olivePlayEffects,
                playType = AVPlayer.VideoPlayType.SUB
            )
        } else {
            _scrollPlayableState.updateValueIfChanged(OliveScrollPlayableState.PLAYED)
            // 不支持自动播放时，且新入的界面又是olive时，强行矫正一次显示状态
            currentOLiveStateOperator?.notifyResetOLiveStateToCoverIfNeed()
            GLog.d(TAG) { "[playOLiveIfNeed] unsupported auto play, slideFromUser = $slideFromUser" }
        }
    }

    /**
     * 判断回到大图界面时，是否为用户主动滑动带来的变更
     *
     * 该方法来自LoadingProxy.shouldStartPresentation;
     * 来自WindowStateMachine.fireStateEvent.post{sendToTarget};
     * 来自WindowStateMachine.onHandleDirtyState;
     * 来自notifyStateDirty;
     * 来自onRequestTransferState;
     * 来自WindowLoadingStrategy.transferSlotToExpectState;
     * 来自doUpdateSlotStates;
     * 来自notifySlotFocusChanged;
     * 来自PhotoPager.onPageSelected;
     * 也就是说：由于会post一帧执行该方法，所以该方法一定在onPageSelected之后执行。
     * 所以，不管是手动滑动大图还是滑动缩图轴的都会执行onPageSelected并更新isFocusSlotScrollFromPhotoPager之后才会执行该方法判断是否手动滑动大图了。
     *
     * 使用方：
     * 1. [enduePlaybackControlOfOLive] 会使用此方法判断使用是用户滑动。
     * 2. 在 slot 初始初始化 contentRenderer 时，会用此方法判断是否是由用户滑动触发的初始化。[PhotoOliveSection.contentRendererStatusListener]
     */
    internal fun isPhotoSlotChangedFormUserSlide(): Boolean {
        //当前olive的滑动是否来源于用户的主动滑动事件
        val viewDataDiff = pageViewModel.dataLoading.diffedFocusViewData.value
        val oldPosition = viewDataDiff?.oldItem?.position
        val newPosition = viewDataDiff?.newItem?.position
        val isItemChanged = isItemChanged(viewDataDiff)
        val isFromPhotoPagerScroll = (newPosition == isFocusSlotScrollFromPhotoPager.first) && (isFocusSlotScrollFromPhotoPager.second)
        val isPhotoSlotChangedFormUser = (oldPosition != null)  // 为null说明是外部第一次进入，不播放
                && (oldPosition != PhotoDataLoadingViewModel.INVALID_INDEX)
                && (newPosition != oldPosition)
                && isItemChanged
                && isOliveSlotPausedBefore.not() // 当之前pause过，此次就认为是home退出等场景的再入场景，不做自动播放
                && isFromPhotoPagerScroll // 来自大图滑动的才能播放，缩图轴的点击或滑动不会播放（交互要求）
        return isPhotoSlotChangedFormUser.also {
            GLog.d(TAG) {
                "[isPhotoSlotChangedFormUserSlide] isOliveSlotPausedBefore = $isOliveSlotPausedBefore isItemChanged=$isItemChanged " +
                        "oldPosition=$oldPosition newPosition=$newPosition isFocusSlotScrollFromPhotoPager=$isFocusSlotScrollFromPhotoPager " +
                        "isFromPhotoPagerScroll=$isFromPhotoPagerScroll result=$it"
            }
        }
    }

    /**
     * 是否需要显示播放会不流畅的提示。
     * 1. 需是用户手动播放
     * 2. 规格超平台能力（不支持自动播放）
     *
     * @param isManual 是否是用户手动触发的
     * @return 是否需要显示。
     */
    private fun shouldShowDecreaseFluencyHint(isManual: Boolean): Boolean {
        if (!isManual) return false

        // 仅明确不支持时才需要显示提示
        return currentOLiveStateOperator?.isVideoSupportByDecoder == false
    }


    /**--------------------------------VM生命周期相关-----------------------------------------*/

    override fun onBind() {
        super.onBind()
        _supportOlive.updateValueIfChanged(false)
        launch(Dispatchers.Main.immediate) {
            pageViewModel.dataLoading.diffedFocusViewData.collectNotNull(focusSlotViewDataDiffCollector)
        }
        pageViewModel.outputChannels.castingManager.isCasting.observeForever(isCastingObserver)
        pageViewModel.pageManagement.pageOperationState.observeForever(pageOperationStateObserver)
    }

    override fun onPause() {
        super.onPause()
        // isOliveSlotPausedBefore 主要用于从home/编辑等场景回到大图时播放Olive资源，但如果当前资源不是Olive资源则无需设置该值
        if (_supportOlive.value == true) {
            isOliveSlotPausedBefore = true
        }
    }

    override fun onCleared() {
        pageViewModel.outputChannels.castingManager.isCasting.removeObserver(isCastingObserver)
        pageViewModel.pageManagement.pageOperationState.removeObserver(pageOperationStateObserver)
        super.onCleared()
    }

    override fun onDestroy() {
        super.onDestroy()
        hardwareAbility?.close()
    }

    /**
     * Olive资源长按时，前置条件
     */
    internal enum class OLiveLongPressConditions {
        /**
         * 播放的前置条件ready，可以播放
         */
        READY,

        /**
         * 当前正在投屏中
         */
        CASTING,

        /**
         * feature不支持Olive或当前资源不是Olive资源
         */
        NOT_SUPPORT,

        /**
         * 与超级文本产生冲突
         */
        SUPER_TEXT_CONFLICT,

        /**
         * 与抠图有冲突
         */
        LNS_CONFLICT,

        /**
         * 当前的文件是瘦身文件
         */
        SLIMMING_FILE
    }

    companion object {
        private const val TAG = "PhotoOliveViewModel"
        private const val VIDEO_SIZE_THRESHOLD_ON_CACHE_CONTENT = 3840

        /**
         * 封面的 t 状态到 c 状态阶段定制的提亮速率
         */
        private const val OLIVE_THUMB_TO_CONTENT_EDR_EXIT_RATE = 12000
        /**
         * 封面的 t 状态到 c 状态阶段定制的提亮持续时间
         */
        private const val OLIVE_THUMB_TO_CONTENT_CUSTOM_EDR_RATE_DURATION = 500L

        /**
         * 滑动播放特效列表
         */
        val oliveSlidePlayEffects =
            listOf(
                TBLVideoPlayEffect.TBL_CUSTOM_CROP_EFFECT,
                TBLVideoPlayEffect.TBL_FOV_EFFECT,
                TBLVideoPlayEffect.TBL_FRAME_FILL_EFFECT,
                TBLVideoPlayEffect.TBL_CROP_EFFECT,
                TBLVideoPlayEffect.TBL_PRESENTATION_EFFECT
        )

        /**
         * 点击播放特效列表
         */
        val oliveClickPlayEffects =
            listOf(
                TBLVideoPlayEffect.TBL_FRAME_FILL_EFFECT,
                TBLVideoPlayEffect.TBL_SCALE_EFFECT
            )

        /**
         * 判定当前PhotoItemViewData是否是Olive资源
         */
        fun PhotoItemViewData?.isOLive() = (this != null) && isImage && (tagFlags and FLAG_SUPPORT_OLIVE == FLAG_SUPPORT_OLIVE)
    }
}