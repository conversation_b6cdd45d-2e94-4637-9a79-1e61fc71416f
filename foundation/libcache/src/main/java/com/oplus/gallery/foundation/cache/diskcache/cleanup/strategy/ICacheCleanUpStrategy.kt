/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ICacheCleanUpStrategy.kt
 ** Description : 对操作过程中临时使用的文件缓存进行清理的策略抽象接口
 ** Version     : 1.0
 ** Date        : 2023/07/09
 ** Author      : <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2023/07/09      1.0
 *********************************************************************************/
package com.oplus.gallery.foundation.cache.diskcache.cleanup.strategy

import com.oplus.gallery.standard_lib.file.File

/**
 * 用于对操作过程中临时使用的文件缓存进行清理的策略，需自定义触发条件和清理动作
 */
interface ICacheCleanUpStrategy {
    /**
     * 获取清理的策略实现类用于哪个缓存目录
     */
    fun getTargetCleanUpDir(): File

    /**
     * 检查是否需要进行清理
     * @param fileToCheck 当前待检查的缓存文件
     * @param totalCacheSize 所有缓存文件总共占用的大小
     * @param totalCount 所有缓存文件总共占用的数目
     * @return 返回true，将触发对[fileToCheck]的清理操作[cleanUpCacheFile]
     */
    fun shouldCleanUpCache(fileToCheck: File, totalCacheSize: Long, totalCount: Int): Boolean

    /**
     * 执行缓存文件清理动作
     * @param fileToCleanUp 待清理的目标缓存文件
     * @return 清理是否成功
     */
    fun cleanUpCacheFile(fileToCleanUp: File): Boolean

    /**
     * 获取log打印tag
     */
    fun getTag(): String
}