/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryVideoTheme.java
 * * Description: IGalleryVideoTheme interface.
 * * Version: 1.0
 * * Date : 2018/06/02
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2018/06/02    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

public interface IGalleryVideoTheme<T> {
    void installVideoThemeRes(String[] themeList, String[] licList);

    void installVideoThemeFilterRes(String[] filterList, String[] licList);

    void installDownloadTheme(int pos, T themeItem);

    boolean addVideoTheme(T theme, int pos);

    boolean addVideoTheme(String themeId);

    void removeVideoTheme();

    T getCurrentVideoThemePos();

    String getCurrentVideoTheme();

    void saveVideoThemeState();

    void resetVideoThemeState();

    void setThemeMusicMute(boolean isMute);
}
