/**************************************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -   VideoAudioDao.kt
 ** Description: VideoAudioDao接口继承了SimpleDao，用于处理VideoAudioEntity实体相关的数据库操作.
 ** Version: 1.0
 ** Date :  2025/06/10
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 **  <EMAIL>  2025/06/10     1.0          VideoAudioDao接口继承了SimpleDao，用于处理VideoAudioEntity实体相关的数据库操作.
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.dao

import androidx.room.Dao
import androidx.room.Query
import com.oplus.gallery.videoeditorpage.resource.room.entity.VideoAudioItem

/**
 * VideoAudioDao接口继承了SimpleDao，用于处理VideoAudioEntity实体相关的数据库操作.
 * 继承SimpleDao使得VideoAudioDao能够直接使用一系列基础的数据库操作方法，
 * 如插入、更新、删除和查询等，而无需重复实现这些基本操作.
 */
@Dao
interface VideoAudioDao : SimpleDao<VideoAudioItem?> {

    @get:Query("SELECT * FROM resource_video_audio ORDER BY _id DESC")
    val all: MutableList<VideoAudioItem>?

    @Query("SELECT * FROM resource_video_audio WHERE date_taken LIKE :dateTaken")
    fun getEntityByDateTaken(dateTaken: String?): MutableList<VideoAudioItem>?

    @Query("SELECT * FROM resource_video_audio WHERE original_video_path LIKE :originalVideoPath")
    fun getEntityByOriginalVideoPath(originalVideoPath: String?): VideoAudioItem?

    @Query("SELECT * FROM resource_video_audio WHERE file_path LIKE :filePath")
    fun getEntityByFilePath(filePath: String?): VideoAudioItem?

    @Query("DELETE FROM resource_video_audio WHERE file_path LIKE :filePath")
    fun deleteEntityByFilePath(filePath: String?): Int

    @Query("DELETE FROM resource_video_audio")
    fun deleteAll(): Int

    /**
     * 查询内置资源歌曲的数量
     *
     *
     * 此方法通过执行SQL查询来统计VideoAudioEntity表中所有 数量
     * 使用了注解@Query来定义SQL查询语句，这种方式使得数据访问层的方法定义变得直观且易于管理
     *
     * @return 内置资源歌曲的总数
     */
    @Query("SELECT COUNT(*) FROM resource_video_audio")
    fun queryBuiltinCount(): Int
}
