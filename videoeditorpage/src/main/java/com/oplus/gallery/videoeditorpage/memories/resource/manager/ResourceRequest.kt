/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ResourceRequest.kt
 ** Description : 资源相关的网络请求
 ** Version     : 1.0
 ** Date        : 2024/3/11
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D          2024/3/11     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.manager

import android.content.Context
import com.google.gson.reflect.TypeToken
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusAesJsonResponseConvert
import com.oplus.gallery.foundation.networkaccess.INetSendRule
import com.oplus.gallery.foundation.networkaccess.callback.ProgressListener
import com.oplus.gallery.foundation.networkaccess.convert.FileResponseConvert
import com.oplus.gallery.foundation.networkaccess.param.JsonRequestParam
import com.oplus.gallery.foundation.security.AppsSecurityData
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.download.networkrequest.CommonCallbackConvert
import com.oplus.gallery.framework.abilities.download.networkrequest.GetRequest
import com.oplus.gallery.framework.abilities.download.networkrequest.header.HttpHeader
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusJsonResponseConvert
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusNetServiceManager
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusResponseData
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusResponseCallbackConvert
import com.oplus.gallery.framework.abilities.download.networkrequest.PostRequest
import com.oplus.gallery.framework.abilities.download.networkrequest.header.ParamKey
import com.oplus.gallery.framework.abilities.download.networkrequest.header.RequestParam
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.videoeditorpage.memories.resource.data.MusicResponseBean
import com.oplus.gallery.videoeditorpage.memories.resource.data.SongByTagResponseBean
import com.oplus.gallery.videoeditorpage.memories.resource.data.TemplateByTagResponseBean
import com.oplus.gallery.videoeditorpage.memories.resource.data.TemplateResponseBean

/**
 * 获取歌曲列表，提供回忆编辑使用
 */
class GetSongRequest(context: Context, allowRequestRule: INetSendRule) :
    GetRequest<OplusResponseData<MusicResponseBean>?>(context, allowRequestRule) {

    override val logTag: String = TAG

    override val url: String? = OplusNetServiceManager.getInstance().buildUrl(SUB_PATH)

    override val header: MutableMap<String, String?> = HttpHeader.getOplusHeaders(
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not() && PROPERTY_IS_OP_BRAND)

    override val responseConvert = OplusJsonResponseConvert(object : TypeToken<MusicResponseBean>() {})

    override val callbackConvert = OplusResponseCallbackConvert<MusicResponseBean>()

    companion object {
        private const val TAG = "GetSongRequest"
        private const val SUB_PATH = "/album/getSongs/v1"
    }
}

/**
 * 获取主题列表，提供回忆编辑使用
 */
class GetThemeRequest(context: Context, allowRequestRule: INetSendRule) :
    GetRequest<OplusResponseData<TemplateResponseBean>?>(context, allowRequestRule) {

    override val logTag: String = TAG

    override val url: String? = OplusNetServiceManager.getInstance().buildUrl(SUB_PATH)

    override val header: MutableMap<String, String?> = HttpHeader.getOplusHeaders(
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not() && PROPERTY_IS_OP_BRAND
    )

    override val responseConvert = OplusJsonResponseConvert(object : TypeToken<TemplateResponseBean>() {})

    override val callbackConvert = OplusResponseCallbackConvert<TemplateResponseBean>()

    companion object {
        private const val TAG = "GetThemeRequest"
        private const val SUB_PATH = "/album/getThemes/v1"
    }
}

/**
 * 获取模板列表，提供视频编辑使用
 */
class GetThemeByTagRequest(context: Context, allowRequestRule: INetSendRule, data: AppsSecurityData, requestData: String?) :
    PostRequest<Any, OplusResponseData<TemplateByTagResponseBean>?>(context, allowRequestRule) {

    override val logTag: String = TAG

    override val url: String?
        get() = OplusNetServiceManager.getInstance().buildUrl(SUB_PATH)

    override val header: MutableMap<String, String?> = HttpHeader.getOplusHeaders(
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not() && PROPERTY_IS_OP_BRAND
    ).apply {
        this[ParamKey.AES_KEY] = data.aesEncryptKey
    }

    override val requestParam = JsonRequestParam(mutableMapOf<String, Any?>().also {
        it[RequestParam.REQUEST_DATA] = requestData
    })

    override val responseConvert = OplusAesJsonResponseConvert(data.aesKey, object : TypeToken<TemplateByTagResponseBean>() {})

    override val callbackConvert = OplusResponseCallbackConvert<TemplateByTagResponseBean>()

    companion object {
        private const val TAG = "GetThemeByTagRequest"
        private const val SUB_PATH = "/album/getThemesByTag"
    }
}

/**
 * 获取歌曲列表，提供视频编辑使用
 */
class GetSongByTagRequest(context: Context, allowRequestRule: INetSendRule, data: AppsSecurityData, requestData: String?) :
    PostRequest<Any, OplusResponseData<SongByTagResponseBean>?>(context, allowRequestRule) {

    override val logTag: String = TAG

    override val url: String?
        get() = OplusNetServiceManager.getInstance().buildUrl(SUB_PATH)

    override val header: MutableMap<String, String?> = HttpHeader.getOplusHeaders(
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not() && PROPERTY_IS_OP_BRAND
    ).apply {
        this[ParamKey.AES_KEY] = data.aesEncryptKey
    }

    override val requestParam = JsonRequestParam(mutableMapOf<String, Any?>().also {
        it[RequestParam.REQUEST_DATA] = requestData
    })

    override val responseConvert =
        OplusAesJsonResponseConvert(
            data.aesKey,
            object : TypeToken<SongByTagResponseBean>() {})

    override val callbackConvert = OplusResponseCallbackConvert<SongByTagResponseBean>()

    companion object {
        private const val TAG = "GetSongByTagRequest"
        private const val SUB_PATH = "/album/getSongsByTag"
    }
}

/**
 * 资源文件下载的网络请求
 */
class DownloadFileRequest(
    context: Context,
    allowRequestRule: INetSendRule?,
    override val url: String?,
    filePath: String,
    downloadListener: ProgressListener?,
    filterContentType: Boolean = true
) : GetRequest<File?>(context, allowRequestRule) {

    override val logTag: String = TAG

    override val header: MutableMap<String, String?>? = null

    override val responseConvert = FileResponseConvert(File(filePath), downloadListener, filterContentType)

    override val callbackConvert = CommonCallbackConvert<File?>()

    companion object {
        private const val TAG = "DownloadFileRequest"
    }
}