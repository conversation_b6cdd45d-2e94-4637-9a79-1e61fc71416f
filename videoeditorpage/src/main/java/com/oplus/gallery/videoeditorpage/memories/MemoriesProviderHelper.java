/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  - MemoriesProviderHelper.java
 * * Description : MemoriesProviderHelper
 * * Version     : 1.0
 * * Date        : 2017/11/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/22    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories;

import static android.provider.MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.getFileLastTime;
import static com.oplus.gallery.videoeditorpage.memories.data.MemoriesStore.NAME_USER_DEFINED;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;

import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.convert.IConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.DataReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.videoeditorpage.memories.data.MemoriesInfo;
import com.oplus.gallery.videoeditorpage.memories.data.MemoriesStore;

import java.util.ArrayList;
import java.util.List;

public final class MemoriesProviderHelper {
    private static final String TAG = "MemoriesProviderHelper";


    private static final String[] ITEM_QUERY_PROJECT = new String[]{
            GalleryStore.MemoriesSetmapViewColumns.SET_ID,
            GalleryStore.MemoriesSetmapViewColumns.MEDIA_ID,
            GalleryStore.MemoriesSetmapViewColumns.DATA,
            GalleryStore.MemoriesSetmapViewColumns.MEDIA_TYPE,
            GalleryStore.MemoriesSetmapViewColumns.MEDIA_SCORE,
            GalleryStore.MemoriesSetmapViewColumns.IN_VIDEO,
            GalleryStore.MemoriesSetmapViewColumns.IS_COVER,
            GalleryStore.MemoriesSetmapViewColumns.IS_SINGLE_FACE,
            GalleryStore.MemoriesSetmapViewColumns.TAG_ID
    };
    private static final String[] TOP_ITEM_QUERY_PROJECT = new String[]{
            GalleryStore.MemoriesSetmapViewColumns.DATA,
            "max(" + GalleryStore.MemoriesSetmapViewColumns.MEDIA_SCORE + ")"
    };
    private static final String[] FACE_QUERY_PROJECT = new String[]{

            GalleryStore.ScanFaceColumns.DATA,
            GalleryStore.ScanFaceColumns.THUMB_W,
            GalleryStore.ScanFaceColumns.THUMB_H,
            "min(" + GalleryStore.ScanFaceColumns.LEFT + ") AS " + GalleryStore.ScanFaceColumns.LEFT,
            "min(" + GalleryStore.ScanFaceColumns.TOP + ") AS " + GalleryStore.ScanFaceColumns.TOP,
            "max(" + GalleryStore.ScanFaceColumns.RIGHT + ") AS " + GalleryStore.ScanFaceColumns.RIGHT,
            "max(" + GalleryStore.ScanFaceColumns.BOTTOM + ") AS " + GalleryStore.ScanFaceColumns.BOTTOM
    };

    private static final String[] SET_QUERY_PROJECT = new String[]{
            GalleryStore.MemoriesSetColumns._ID,
            GalleryStore.MemoriesSetColumns.NAME,
            GalleryStore.MemoriesSetColumns.TYPE,
            GalleryStore.MemoriesSetColumns.TAKEN,
            GalleryStore.MemoriesSetColumns.START_TIME,
            GalleryStore.MemoriesSetColumns.END_TIME,
            GalleryStore.MemoriesSetColumns.THEME,
            GalleryStore.MemoriesSetColumns.MUSIC,
            GalleryStore.MemoriesSetColumns.META_NUM
    };
    private static final String ORDER_BY = (GalleryStore.MemoriesSetmapViewColumns.TAG_ID + " ASC, "
            + GalleryStore.MemoriesSetmapViewColumns.MEDIA_SCORE + " DESC");

    private MemoriesProviderHelper() {

    }

    private static String getWhereClause(List<MediaInfo> pathList) {
        if ((pathList == null) || pathList.isEmpty()) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" _data IN (");
        for (MediaInfo info : pathList) {
            sb.append("\"");
            sb.append(info.mPath);
            sb.append("\"");
            sb.append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(") AND ");
        sb.append(GalleryStore.ScanFaceColumns.NO_FACE + " != 1");
        sb.append(") GROUP BY (" + GalleryStore.ScanFaceColumns.DATA);
        return sb.toString();
    }

    public static MemoriesInfo getMemoriesInfo(Context context, int setId) {
        Cursor cursor = null;
        MemoriesInfo memoriesInfo = new MemoriesInfo();
        try {
            long time = System.currentTimeMillis();
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setProjection(SET_QUERY_PROJECT)
                    .setWhere(GalleryStore.MemoriesSetColumns._ID + " = " + setId)
                    .setOrderBy(null)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.w(TAG, "getMemoriesInfo cursor is null or empty.");
                return memoriesInfo;
            }
            List<MemoriesInfo> list = MemoriesInfo.buildMemoriesInfoList(cursor);
            if (list.isEmpty()) {
                GLog.w(TAG, "getMemoriesInfo list is empty.");
                return memoriesInfo;
            }
            memoriesInfo = list.get(0);
            GLog.d(TAG, "getMemoriesInfo cost time = " + (System.currentTimeMillis() - time)
                    + ", getCount = " + cursor.getCount() + ", memoriesInfo = " + memoriesInfo);
            List<MediaInfo> infoList = getMediaInfoList(context, setId);
            if ((infoList != null) && !infoList.isEmpty()) {
                memoriesInfo.mCover = MemoriesInfo.getCover(infoList);
                GLog.d(TAG, "getMemoriesInfo.getCover cover = " + memoriesInfo.mCover + ", memoriesInfo = " + memoriesInfo);
                if (memoriesInfo.mCover == null) {
                    memoriesInfo.mCover = MemoriesInfo.getNextCover(infoList);
                    GLog.d(TAG, "getMemoriesInfo new cover = " + memoriesInfo.mCover);
                }
                memoriesInfo.mMediaInfos.addAll(infoList);
                findTopMediaInfoList(setId, infoList);
            }
        } catch (Exception e) {
            GLog.w(TAG, "getMemoriesInfo error:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return memoriesInfo;
    }

    public static List<MediaInfo> getMediaInfoList(Context context, int setId) {
        Cursor cursor = null;
        try {
            long time = System.currentTimeMillis();
            cursor = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setQuerySql(getMediaInfoQuerySql(setId))
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursor == null) || (cursor.getCount() <= 0)) {
                GLog.w(TAG, "getMediaInfoList cursor is null or empty.");
                return new ArrayList<>();
            }
            List<MediaInfo> list = buildMediaInfoList(context, cursor);
            GLog.d(TAG, "getMediaInfoList cost time = " + (System.currentTimeMillis() - time)
                    + ", size = " + list.size() + ", getCount = " + cursor.getCount());
            return queryMediaInfosFace(list);
        } catch (Exception e) {
            GLog.w(TAG, "getMediaInfoList error:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return new ArrayList<>();
    }

    /**
     * SELECT memories_setmap_view.set_id,memories_setmap_view.media_id,memories_setmap_view._data,
     * memories_setmap_view.media_type,memories_setmap_view.media_score,memories_setmap_view.in_video,
     * memories_setmap_view.is_cover,memories_setmap_view.is_single_face,memories_setmap_view.tag_id,local_media._id
     * FROM memories_setmap_view INNER JOIN local_media
     * ON local_media._data = memories_setmap_view._data WHERE set_id = setId
     * ORDER BY local_media.datetaken DESC,local_media.media_id DESC
     *
     * @param setId 回忆图集id
     * @return String
     */
    public static String getMediaInfoQuerySql(int setId) {
        /*
         * 查询数据的排序方式跟回忆详情保持一致
         * 精彩回忆排序不跟随设置，保持最新照片在最前面
         */
        return "SELECT " + DatabaseUtils.projectionsToString(ITEM_QUERY_PROJECT, GalleryStore.MemoriesSetmapView.TAB)
                + "," + GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns._ID
                + " FROM " + GalleryStore.MemoriesSetmapView.TAB
                + " INNER JOIN " + GalleryStore.GalleryMedia.TAB + " ON "
                + GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns.DATA
                + " = " + GalleryStore.MemoriesSetmapView.TAB + "." + GalleryStore.MemoriesSetmapViewColumns.DATA
                + " WHERE " + GalleryStore.MemoriesSetmapViewColumns.SET_ID + " = " + setId
                + " ORDER BY " + DatabaseUtils.getOrderClauseMemories(false);
    }

    private static void updateFace(MediaInfo info, int thumbW, int thumbH) {
        if ((info == null) || TextUtils.isEmpty(info.mPath)) {
            GLog.w(TAG, "updateFace info is null or mPath is null. info = " + info);
            return;
        }
        if (info.mFace.isEmpty()) {
            GLog.w(TAG, "updateFace face rect is empty. info = " + info);
            return;
        }
        int imgWidth = 0;
        int imgHeight = 0;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            // fd相较path慢20多倍
            if (RuntimePermissionUtils.hasManageExternalStorage()) {
                BitmapFactory.decodeFile(info.mPath, options);
            } else {
                parcelFileDescriptor = FileAccessManager.getInstance().openFile(ContextGetter.context, Uri.parse(info.mUri));
                if (parcelFileDescriptor == null) {
                    GLog.w(TAG, "updateFace failed to open file.");
                    return;
                }
                BitmapFactory.decodeFileDescriptor(parcelFileDescriptor.getFileDescriptor());
            }
            imgWidth = options.outWidth;
            imgHeight = options.outHeight;
        } catch (Exception e) {
            GLog.w(TAG, "updateFace e = ", e);
        } finally {
            IOUtils.closeQuietly(parcelFileDescriptor);
        }
        final float scale = (float) imgWidth / (float) thumbW;
        info.mFace.left = info.mFace.left * scale;
        info.mFace.top = info.mFace.top * scale;
        info.mFace.right = info.mFace.right * scale;
        info.mFace.bottom = info.mFace.bottom * scale;
        info.mWidth = imgWidth;
        info.mHeight = imgHeight;
    }

    /* find top media info list : group by tag id, top score */
    private static boolean findTopMediaInfoList(int setId, List<MediaInfo> allList) {
        Cursor cursor = null;
        try {
            long time = System.currentTimeMillis();
            String whereClause = GalleryStore.MemoriesSetmapViewColumns.SET_ID + " = " + setId
                    + ") GROUP BY (" + GalleryStore.MemoriesSetmapViewColumns.TAG_ID;

            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SETMAP_VIEW)
                    .setProjection(TOP_ITEM_QUERY_PROJECT)
                    .setWhere(whereClause)
                    .setOrderBy(ORDER_BY)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);

            if ((cursor == null) || (cursor.getCount() <= 0)) {
                GLog.w(TAG, "findTopMediaInfoList cursor is null or empty. whereClause = " + whereClause);
                return false;
            }
            final int pathIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.DATA);
            while (cursor.moveToNext()) {
                String filePath = cursor.getString(pathIndex);
                MediaInfo info = findMediaInfo(allList, filePath);
                GLog.d(TAG, "findTopMediaInfoList info = " + info);
                if (info != null) {
                    info.mOptimal = true;
                }
            }
            GLog.d(TAG, "findTopMediaInfoList cost time = " + (System.currentTimeMillis() - time)
                    + ", getCount = " + cursor.getCount());
            return true;
        } catch (Exception e) {
            GLog.w(TAG, "findTopMediaInfoList error:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return false;
    }

    private static MediaInfo findMediaInfo(List<MediaInfo> mediaInfos, String path) {
        for (MediaInfo info : mediaInfos) {
            if ((info != null) && (TextUtils.equals(info.mPath, path) || TextUtils.equals(info.mUri, path))) {
                return info;
            }
        }
        return null;
    }

    public static List<MediaInfo> queryMediaInfosFace(List<MediaInfo> mediaInfos) {
        Cursor cursor = null;
        try {
            long time = System.currentTimeMillis();
            String whereClause = getWhereClause(mediaInfos);
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setProjection(FACE_QUERY_PROJECT)
                    .setWhere(whereClause)
                    .setOrderBy(null)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() <= 0)) {
                GLog.w(TAG, "getMediaInfoFaceList cursor is null or empty");
                return mediaInfos;
            }
            final int dataIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.DATA);
            final int thumbWIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.THUMB_W);
            final int thumbHIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.THUMB_W);
            final int leftIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.LEFT);
            final int topIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.TOP);
            final int rightIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.RIGHT);
            final int bottomIndex = cursor.getColumnIndex(GalleryStore.ScanFaceColumns.BOTTOM);
            String path = null;
            int thumbW = 0;
            int thumbH = 0;
            while (cursor.moveToNext()) {
                path = cursor.getString(dataIndex);
                MediaInfo info = findMediaInfo(mediaInfos, path);
                if (info != null) {
                    info.mFace.set(
                            cursor.getInt(leftIndex),
                            cursor.getInt(topIndex),
                            cursor.getInt(rightIndex),
                            cursor.getInt(bottomIndex));
                    thumbW = cursor.getInt(thumbWIndex);
                    thumbH = cursor.getInt(thumbHIndex);
                    updateFace(info, thumbW, thumbH);
                }
            }
            GLog.d(TAG, "getMediaInfoFaceList cost time = " + (System.currentTimeMillis() - time)
                    + ", size = " + mediaInfos.size() + ", getCount = " + cursor.getCount());
        } catch (Exception e) {
            GLog.w(TAG, "getMediaInfoFaceList error:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return mediaInfos;
    }


    public static void setTheme(long setId, String theme) {
        try {
            GLog.d(TAG, "setTheme setId:" + setId + ", theme:" + theme);
            new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setWhere(MemoriesStore.SetView._ID + " = " + setId)
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            ContentValues cv = new ContentValues();
                            cv.put(GalleryStore.MemoriesSetColumns.THEME, theme);
                            return cv;
                        }
                    }).build().exec();
        } catch (Exception e) {
            GLog.w(TAG, "setTheme error:", e);
        }
    }

    public static void setMusic(long setId, String music) {
        try {
            GLog.d(TAG, "setMus;ic setId:" + setId + ", music:" + music);
            new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setWhere(MemoriesStore.SetView._ID + " = " + setId)
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            ContentValues cv = new ContentValues();
                            cv.put(GalleryStore.MemoriesSetColumns.MUSIC, music);
                            return cv;
                        }
                    }).build().exec();
        } catch (Exception e) {
            GLog.w(TAG, "setMusic error:", e);
        }
    }

    public static void updateMemoriesInfo(MemoriesInfo newInfo, MemoriesInfo oldInfo) {
        if (newInfo.mCover == null) {
            GLog.w(TAG, LogFlag.DL, () -> "updateMemoriesInfo, mCover is null! newInfo = " + newInfo + ", oldInfo = " + oldInfo);
            return;
        }
        GLog.d(TAG, LogFlag.DL, () -> "updateMemoriesInfo, newInfo: " + newInfo);
        try {
            int count = new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setWhere(GalleryStore.MemoriesSetColumns._ID + " = " + newInfo.mSetId)
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            ContentValues cv = new ContentValues();
                            cv.put(GalleryStore.MemoriesSetColumns.COVER_PATH, newInfo.mCover.mPath);
                            cv.put(GalleryStore.MemoriesSetColumns.THEME, newInfo.mTheme);
                            cv.put(GalleryStore.MemoriesSetColumns.MUSIC, newInfo.mMusic);
                            if (!TextUtils.equals(newInfo.mName, oldInfo.mName)) {
                                cv.put(GalleryStore.MemoriesSetColumns.NAME, newInfo.mName);
                                cv.put(GalleryStore.MemoriesSetColumns.NAME_TYPE, NAME_USER_DEFINED);
                            }
                            return cv;
                        }
                    }).build().exec();
            GLog.d(TAG, LogFlag.DL, () -> "updateMemoriesInfo, count: " + count);
        } catch (Exception e) {
            GLog.w(TAG, "updateMemoriesInfo error:", e);
        }
    }

    public static void updateMediaList(List<MediaInfo> mediaList) {
        if ((mediaList == null) || (mediaList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL, "updateMediaList, mediaList is null or empty!");
            return;
        }
        int size = mediaList.size();
        GLog.d(TAG, LogFlag.DL, () -> "updateMediaList, mediaList.size: " + size);
        try {
            ArrayList<DataReq<?>> dataReqList = new ArrayList<>();
            for (MediaInfo info : mediaList) {
                String where = GalleryStore.MemoriesSetmap.SET_ID + " = ? AND "
                        + GalleryStore.MemoriesSetmapColumns.DATA + " = ? ";
                UpdateReq updateReq = new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.MEMORIES_SETMAP)
                        .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                        .setWhere(where)
                        .setWhareArgs(new String[]{String.valueOf(info.mSetId), String.valueOf(info.mPath)})
                        .setConvert(new IConvert<Void, ContentValues>() {
                            @Override
                            public ContentValues convert(Void aVoid) {
                                ContentValues cv = new ContentValues();
                                cv.put(GalleryStore.MemoriesSetmapColumns.IS_COVER, (info.mIsCover) ? 1 : 0);
                                cv.put(GalleryStore.MemoriesSetmapColumns.IN_VIDEO, (info.mInVideo) ? 1 : 0);
                                return cv;
                            }
                        }).build();
                dataReqList.add(updateReq);
            }
            int count = new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SETMAP)
                    .addDataReqs(dataReqList)
                    .build().exec().length;
            GLog.d(TAG, LogFlag.DL, () -> "updateMemoriesInfo, count: " + count);
        } catch (Exception e) {
            GLog.w(TAG, "updateMediaList, error:", e);
        } finally {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.MEMORIES_SETMAP, IDao.DaoType.GALLERY);
        }
    }

    private static List<MediaInfo> buildMediaInfoList(Context context, Cursor cursor) {
        ArrayList<MediaInfo> list = new ArrayList<>();
        if (cursor == null) {
            return list;
        }
        final int setIdIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.SET_ID);
        final int mediaIdIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.MEDIA_ID);
        final int pathIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.DATA);
        final int typeIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.MEDIA_TYPE);
        final int scoreIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.MEDIA_SCORE);
        final int inVideoIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.IN_VIDEO);
        final int coverIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.IS_COVER);
        final int singleFaceIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.IS_SINGLE_FACE);
        final int tagIdIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetmapViewColumns.TAG_ID);
        final int galleryId = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns._ID);
        while (cursor.moveToNext()) {
            MediaInfo info = new MediaInfo();
            info.mSetId = cursor.getInt(setIdIndex);
            info.mMediaId = cursor.getInt(mediaIdIndex);
            info.mGalleryId = cursor.getInt(galleryId);
            info.mTagId = cursor.getInt(tagIdIndex);
            info.mPath = cursor.getString(pathIndex);
            info.mType = cursor.getInt(typeIndex);
            info.mUri = MediaStoreUriHelper.getMediaUri(context, info.mPath, null, info.mType == MEDIA_TYPE_IMAGE).toString();
            info.mScore = cursor.getFloat(scoreIndex);
            info.mInVideo = cursor.getInt(inVideoIndex) == 1;
            info.mIsCover = cursor.getInt(coverIndex) == 1;
            info.mSingleFace = cursor.getInt(singleFaceIndex) == 1;
            if (!TextUtils.isEmpty(info.mPath) && (new File(info.mPath).exists())) {
                info.mFileTime = getFileLastTime(info);
                list.add(info);
            }
        }
        for (MediaInfo info : list) {
            GLog.d(TAG, "buildMediaInfoList fileTime = " + info.mFileTime + ", info = " + info);
        }
        return list;
    }
}
