/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamVideoClipEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip;

import android.graphics.PointF;
import android.text.TextUtils;

import com.oplus.gallery.foundation.util.debug.GLog;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamMaskRegionInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamEllipse2D;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data.MeicamRegionInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask.MeicamTransform2D;
import com.google.gson.Gson;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsVideoFx;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基于视频片段的特效类
 * 可以修改特效的范围、强度等效果
 */
public class MeicamVideoClipEffect extends BaseVideoClipEffect {
    private static final String TAG = "MeicamVideoClipEffect";

    public static final float DEFAULT_STRENGTH = 0.8F;
    public static final int REGION_SIZE = 8;
    public static final String JSON_TYPE_NAME = "common";
    protected transient NvsVideoFx mNvsVideoFx;
    protected int mType;
    protected float mStrength;
    protected int mEffectId = DEFAULT_FX_ID;
    /**
     * 动画时：为动画的类型{@link StreamingConstant.Cartoon};
     * 蒙版时：为蒙版遮罩的形状{@link StreamingConstant.MaskFx}
     */
    protected int mEffectPlayType = -1;
    protected long mInTime;
    protected long mOutTime;
    protected long mEffectPlayDuration;
    protected boolean mIsFilterMask = false;
    protected boolean mIsIgnoreBackground = false;
    protected boolean mIsRegional = false;
    protected float[] mRegion = null;
    protected HashMap<Long, float[]> mTimeRegions;
    protected int mTrackIndex;
    protected HashMap<String, Float> mFloatParams;
    protected HashMap<String, HashMap<Long, Float>> mTimeFloatParams;
    protected HashMap<String, Boolean> mBooleanParams;
    protected HashMap<String, String> mStringParams;
    protected HashMap<String, Integer> mIntParams;
    protected Map<String, Object> mAttachment;

    protected boolean mIsInverseRegion;
    protected float mRegionalFeatherWidth;
    protected MeicamMaskRegionInfo mMaskRegionInfo;

    /**
     * 是否是原始特效
     */
    protected boolean mIsRawFx;

    /**
     * there must have a default constructor
     * in case of change of class members, Gson can't read a valid value
     */
    protected MeicamVideoClipEffect() {
        this("", TYPE_BUILT_IN_FX);
    }

    public MeicamVideoClipEffect(String name, int type) {
        super(name);
        mType = type;
        mStrength = DEFAULT_STRENGTH;
        mFloatParams = new HashMap<>();
        mTimeFloatParams = new HashMap<>();
        mStringParams = new HashMap<>();
        mBooleanParams = new HashMap<>();
        mAttachment = new HashMap<>();
        mIntParams = new HashMap<>();
        mTimeRegions = new HashMap<>();
        mClassType = JSON_TYPE_NAME;
    }

    public int getType() {
        return mType;
    }

    @Override
    public void setRegionInfo(MeicamMaskRegionInfo maskRegionInfo) {
        mMaskRegionInfo = maskRegionInfo;
        convert2NvsMaskRegionInfo(mMaskRegionInfo);
    }

    @Override
    public MeicamMaskRegionInfo getRegionInfo() {
        return mMaskRegionInfo;
    }

    private void convert2NvsMaskRegionInfo(MeicamMaskRegionInfo maskRegionInfo) {
        if (mNvsVideoFx == null) {
            return;
        }
        List<MeicamRegionInfo> regionInfoList = (maskRegionInfo == null) ? null : maskRegionInfo.getRegionInfoArray();
        if ((regionInfoList == null) || (regionInfoList.size() == 0)) {
            mNvsVideoFx.setRegionInfo(null);
            return;
        }
        NvsMaskRegionInfo nvsMaskRegionInfo = null;
        for (MeicamRegionInfo regionInfo : regionInfoList) {
            if (regionInfo == null) {
                continue;
            }
            NvsMaskRegionInfo.RegionInfo info = null;
            switch (regionInfo.getType()) {
                case MeicamMaskRegionInfo.MASK_REGION_TYPE_ELLIPSE2D: {
                    info = new NvsMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_ELLIPSE2D);
                    MeicamEllipse2D ellipse2D = regionInfo.getMeicamEllipse2D();
                    if (ellipse2D != null) {
                        NvsPosition2D center = new NvsPosition2D(ellipse2D.getCenter().x, ellipse2D.getCenter().y);
                        info.setEllipse2D(new NvsMaskRegionInfo.Ellipse2D(
                                        center,
                                        ellipse2D.getWidthPercent(),
                                        ellipse2D.getHeightPercent(),
                                        ellipse2D.getTheta())
                        );
                    }
                    MeicamTransform2D transform2D = regionInfo.getMeicamTransform2D();
                    if (transform2D != null) {
                        NvsMaskRegionInfo.Transform2D nvsTransform2D = new NvsMaskRegionInfo.Transform2D();
                        nvsTransform2D.setRotation(transform2D.getRotation());
                        nvsTransform2D.setAnchor(new NvsPosition2D(transform2D.getAnchor().x, transform2D.getAnchor().y));
                        info.setTransform2D(nvsTransform2D);
                    }
                    break;
                }
                case MeicamMaskRegionInfo.MASK_REGION_TYPE_CUBIC_CURVE: {
                    info = new NvsMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_CUBIC_CURVE);
                    List<NvsPosition2D> position2DS = toPositions(regionInfo.getCubicBezierPoints());
                    if (position2DS != null) {
                        info.getPoints().addAll(position2DS);
                    }
                    break;
                }
                case MeicamMaskRegionInfo.MASK_REGION_TYPE_POLYGON: {
                    info = new NvsMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_POLYGON);
                    List<NvsPosition2D> position2DS = toPositions(regionInfo.getCubicBezierPoints());
                    if (position2DS != null) {
                        info.getPoints().addAll(position2DS);
                    }
                    break;
                }
                default:
                    break;
            }
            if (info != null) {
                if (nvsMaskRegionInfo == null) {
                    nvsMaskRegionInfo = new NvsMaskRegionInfo();
                }
                nvsMaskRegionInfo.addRegionInfo(info);
            }
        }
        mNvsVideoFx.setRegionInfo(nvsMaskRegionInfo);
    }

    private List<NvsPosition2D> toPositions(List<PointF> pointFS) {
        if (pointFS == null) {
            return null;
        }

        List<NvsPosition2D> position2DS = new ArrayList<>(pointFS.size());
        for (PointF pointF : pointFS) {
            position2DS.add(new NvsPosition2D(pointF.x, pointF.y));
        }
        return position2DS;
    }

    @Override
    public void setRegionalFeatherWidth(float value) {
        mRegionalFeatherWidth = value;
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setRegionalFeatherWidth(value);
        }
    }

    @Override
    public void setIntVal(String key, int value) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setIntVal(key, value);
        }
        mIntParams.put(key, value);
    }

    @Override
    public int getIntVal(String key) {
        if (key == null) {
            return -1;
        }
        Integer integer = mIntParams.get(key);
        if (integer != null) {
            return integer;
        }
        return -1;
    }

    @Override
    public void setCutRectF(SLRectF rectF) {

    }

    @Override
    public SLRectF getCutRectF() {
        return null;
    }

    @Override
    public void setInverseRegion(boolean isInverseRegion) {
        mIsInverseRegion = isInverseRegion;
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setInverseRegion(isInverseRegion);
        }
    }

    @Override
    public boolean isInverseRegion() {
        return mIsInverseRegion;
    }

    public float getRegionalFeatherWidth() {
        return mRegionalFeatherWidth;
    }

    public MeicamMaskRegionInfo getMaskRegionInfo() {
        return mMaskRegionInfo;
    }

    public int getEffectId() {
        return mEffectId;
    }

    public void setEffectId(int mEffectId) {
        this.mEffectId = mEffectId;
    }

    public int getEffectPlayType() {
        return mEffectPlayType;
    }

    @Override
    public void setAttachment(String key, Object value) {
        mAttachment.put(key, value);
    }

    @Override
    public Object getAttachment(String key) {
        return mAttachment.get(key);
    }

    @Override
    public void setFilterMask(boolean filterMask) {
        this.mIsFilterMask = filterMask;
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setFilterMask(filterMask);
        }
    }

    @Override
    public void setIgnoreBackground(boolean ignoreBackground) {
        this.mIsIgnoreBackground = ignoreBackground;
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setIgnoreBackground(ignoreBackground);
        }
    }

    @Override
    public boolean getIgnoreBackground() {
        return mIsIgnoreBackground;
    }

    @Override
    public void setRegion(float[] region) {
        if (region == null) {
            GLog.e(TAG, "setRegion region empty");
        }

        mRegion = region;
        if ((mNvsVideoFx != null) && (mRegion != null)) {
            mNvsVideoFx.setRegion(mRegion);
        }
    }

    @Override
    public void setRegionAtTime(float[] region, long time) {
        if ((region == null) || (region.length != REGION_SIZE)) {
            GLog.e(TAG, "setRegionAtTime region error");
            return;
        }
        NvsMaskRegionInfo maskRegionInfo = new NvsMaskRegionInfo();
        NvsMaskRegionInfo.RegionInfo regionInfo = new NvsMaskRegionInfo.RegionInfo(NvsMaskRegionInfo.MASK_REGION_TYPE_POLYGON);

        List<NvsPosition2D> position2DList = new ArrayList<>();
        for (int i = 0; i < region.length; i += 2) {
            position2DList.add(new NvsPosition2D(region[i], region[i + 1]));
        }
        regionInfo.setPoints(position2DList);
        maskRegionInfo.addRegionInfo(regionInfo);

        mTimeRegions.put(time, region);
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setRegionInfoAtTime(maskRegionInfo, time);
        }
    }

    @Override
    public void setRegional(boolean regional) {
        mIsRegional = regional;
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setRegional(mIsRegional);
        }
    }

    @Override
    public boolean isRegional() {
        return mIsRegional;
    }

    @Override
    public float[] getRegion() {
        return mRegion;
    }

    @Override
    public HashMap<Long, float[]> getTimeRegions() {
        return mTimeRegions;
    }

    @Override
    public boolean isFilterMask() {
        return mIsFilterMask;
    }

    @Override
    public boolean setDuration(long duration, long clipDuration) {
        return false;
    }

    public void setEffectPlayType(int mEffectPlayType) {
        this.mEffectPlayType = mEffectPlayType;
    }

    public long getEffectPlayDuration() {
        return mEffectPlayDuration;
    }

    public void setEffectPlayDuration(long mEffectPlayDuration) {
        this.mEffectPlayDuration = mEffectPlayDuration;
    }

    public void setNvsVideoFx(NvsVideoFx nvsVideoFx) {
        mNvsVideoFx = nvsVideoFx;
    }

    public MeicamVideoClipEffect clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamVideoClipEffect result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamVideoClipEffect(getName(), getType());
        }
        return result;
    }

    @Override
    public Map<String, Integer> getIntParams() {
        if (mIntParams == null) {
            mIntParams = new HashMap<>();
        }
        return (Map<String, Integer>) mIntParams.clone();
    }

    @Override
    public HashMap<String, Float> getFloatParams() {
        return (HashMap<String, Float>) mFloatParams.clone();
    }

    @Override
    public HashMap<String, String> getStringParams() {
        return (HashMap<String, String>) mStringParams.clone();
    }

    @Override
    public HashMap<String, Boolean> getBooleanParams() {
        return (HashMap<String, Boolean>) mBooleanParams.clone();
    }

    @Override
    public HashMap<String, HashMap<Long, Float>> getTimeFloatParams() {
        return (HashMap<String, HashMap<Long, Float>>) mTimeFloatParams.clone();
    }

    @Override
    public void setFloatValue(String paramName, float value) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setFloatVal(paramName, value);
        }

        if (mFloatParams.containsKey(paramName)) {
            mFloatParams.remove(paramName);
        }
        mFloatParams.put(paramName, value);
    }

    @Override
    public float getFloatValue(String paramName) {
        if (mFloatParams.containsKey(paramName)) {
            return mFloatParams.get(paramName);
        }

        return 0;
    }

    @Override
    public void setFloatValAtTime(String paramName, float value, long time) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setFloatValAtTime(paramName, value, time);
        }

        HashMap<Long, Float> timeMap = null;
        if (mTimeFloatParams.containsKey(paramName)) {
            timeMap = mTimeFloatParams.get(paramName);
            if (timeMap != null) {
                timeMap.remove(time);
            }
        }

        if (timeMap == null) {
            timeMap = new HashMap<>();
        }
        timeMap.put(time, value);
        mTimeFloatParams.put(paramName, timeMap);
    }

    @Override
    public float getFloatValAtTime(String paramName, long time) {
        if (mTimeFloatParams.containsKey(paramName)) {
            HashMap<Long, Float> timeMap = mTimeFloatParams.get(paramName);
            if (timeMap != null) {
                List<Long> timeList = new ArrayList<>();
                for (Map.Entry<Long, Float> entry : timeMap.entrySet()) {
                    timeList.add(entry.getKey());
                }

                Collections.sort(timeList, new Comparator<Long>() {
                    @Override
                    public int compare(Long o1, Long o2) {
                        return (int) (o1 - o2);
                    }
                });

                if (timeList.isEmpty()) {
                    return 0;
                }
                if ((timeList.size() == 1) || (time <= timeList.get(0))) {
                    Float value = timeMap.get(timeList.get(0));
                    if (value != null) {
                        return value;
                    }
                } else if (time >= timeList.get(timeList.size() - 1)) {
                    Float value = timeMap.get(timeList.get(timeList.size() - 1));
                    if (value != null) {
                        return value;
                    }
                }
                for (int i = 0; i < timeList.size(); i++) {
                    if (time == timeList.get(i)) {
                        Float value = timeMap.get(timeList.get(i));
                        if (value != null) {
                            return value;
                        }
                    } else if (i + 1 < timeList.size()) {
                        if ((time > timeList.get(i)) && (time < timeList.get(i + 1))) {
                            float percent = (float) (((double) (time - timeList.get(i))) / (timeList.get(i + 1) - timeList.get(i)));
                            Float firstValue = timeMap.get(timeList.get(i));
                            Float lastValue = timeMap.get(timeList.get(i + 1));
                            if ((firstValue != null) && (lastValue != null)) {
                                return percent * (lastValue - firstValue) + firstValue;
                            }
                        }
                    }
                }
            }
        }

        return 0;
    }

    @Override
    public void setFloatOffset(String paramName, float offset) {
        HashMap<Long, Float> timeMap = null;
        if (mTimeFloatParams.containsKey(paramName)) {
            timeMap = mTimeFloatParams.get(paramName);
            if (timeMap != null) {
                for (Map.Entry<Long, Float> timeEntry : timeMap.entrySet()) {
                    if (TextUtils.equals(StreamingConstant.VideoTransform.PARAM_SCALE_X, paramName)
                            || TextUtils.equals(StreamingConstant.VideoTransform.PARAM_SCALE_Y, paramName)) {
                        timeMap.put(timeEntry.getKey(), timeEntry.getValue() * offset);
                    } else {
                        timeMap.put(timeEntry.getKey(), timeEntry.getValue() + offset);
                    }
                }
            }
        }
    }

    @Override
    public void removeAllKeyframe() {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.removeAllKeyframe(StreamingConstant.VideoTransform.PARAM_TRANS_X);
            mNvsVideoFx.removeAllKeyframe(StreamingConstant.VideoTransform.PARAM_TRANS_Y);
            mNvsVideoFx.removeAllKeyframe(StreamingConstant.VideoTransform.PARAM_SCALE_X);
            mNvsVideoFx.removeAllKeyframe(StreamingConstant.VideoTransform.PARAM_SCALE_Y);
            mNvsVideoFx.removeAllKeyframe(StreamingConstant.VideoTransform.PARAM_ROTATION);
        }
        mTimeFloatParams.clear();
    }

    @Override
    public boolean hasKeyframe() {
        if (mTimeFloatParams != null) {
            return !mTimeFloatParams.isEmpty();
        }
        return false;
    }

    @Override
    public void removeKeyframeAtTime(long time) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.removeKeyframeAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_X, time);
            mNvsVideoFx.removeKeyframeAtTime(StreamingConstant.VideoTransform.PARAM_TRANS_Y, time);
            mNvsVideoFx.removeKeyframeAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_X, time);
            mNvsVideoFx.removeKeyframeAtTime(StreamingConstant.VideoTransform.PARAM_SCALE_Y, time);
            mNvsVideoFx.removeKeyframeAtTime(StreamingConstant.VideoTransform.PARAM_ROTATION, time);
        }
        HashMap<Long, Float> timeMap = mTimeFloatParams.get(StreamingConstant.VideoTransform.PARAM_TRANS_X);
        if (timeMap != null) {
            timeMap.remove(time);
        }
        timeMap = mTimeFloatParams.get(StreamingConstant.VideoTransform.PARAM_TRANS_Y);
        if (timeMap != null) {
            timeMap.remove(time);
        }
        timeMap = mTimeFloatParams.get(StreamingConstant.VideoTransform.PARAM_SCALE_X);
        if (timeMap != null) {
            timeMap.remove(time);
        }
        timeMap = mTimeFloatParams.get(StreamingConstant.VideoTransform.PARAM_SCALE_Y);
        if (timeMap != null) {
            timeMap.remove(time);
        }
        timeMap = mTimeFloatParams.get(StreamingConstant.VideoTransform.PARAM_ROTATION);
        if (timeMap != null) {
            timeMap.remove(time);
        }
    }

    @Override
    public void setLocalFloatValue(String paramName, float value) {

    }

    @Override
    public float getLocalFloatValue(String paramName) {
        return 0;
    }

    @Override
    public void setStringValue(String paramName, String value) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setStringVal(paramName, value);
        }

        if (mStringParams.containsKey(paramName)) {
            mStringParams.remove(paramName);
        }
        mStringParams.put(paramName, value);
    }

    @Override
    public String getStringValue(String paramName) {

        if (mStringParams.containsKey(paramName)) {
            return mStringParams.get(paramName);
        }

        return null;
    }

    @Override
    public void setBooleanVal(String paramName, boolean value) {
        if (mNvsVideoFx != null) {
            mNvsVideoFx.setBooleanVal(paramName, value);
        }

        if (mBooleanParams.containsKey(paramName)) {
            mBooleanParams.remove(paramName);
        }
        mBooleanParams.put(paramName, value);
    }

    @Override
    public Boolean getBooleanVal(String paramName) {

        if (mBooleanParams.containsKey(paramName)) {
            return mBooleanParams.get(paramName);
        }
        return null;
    }

    @Override
    public void setStrength(float strength) {
        mStrength = strength;
        if (mNvsVideoFx == null) {
            GLog.d(TAG, "mNvsVideoFx is null");
            return;
        }
        mNvsVideoFx.setFilterIntensity(strength);
    }

    @Override
    public float getStrength() {
        return mStrength;
    }

    public long getInTime() {
        return mInTime;
    }

    public void setInTime(long mInTime) {
        this.mInTime = mInTime;
    }

    public long getOutTime() {
        return mOutTime;
    }

    public void setOutTime(long mOutTime) {
        this.mOutTime = mOutTime;
    }

    public int getTrackIndex() {
        return mTrackIndex;
    }

    public void setTrackIndex(int mTrackIndex) {
        this.mTrackIndex = mTrackIndex;
    }

    @Override
    public boolean isRawFx() {
        return mIsRawFx;
    }

    @Override
    public void setIsRawFx(boolean isRawFx) {
        mIsRawFx = isRawFx;
    }
}
