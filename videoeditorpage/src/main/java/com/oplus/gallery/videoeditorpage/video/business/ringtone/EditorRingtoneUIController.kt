/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorRingtoneUIController
 ** Description:视频铃声编辑 UIController
 ** Version: 1.0
 ** Date : 2025/06/10
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.ringtone

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_DRAGGING
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_IDLE
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_SETTLING
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtil.clamp
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.VideoEditorHelper
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseUIController
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.output.ResultCode
import com.oplus.gallery.videoeditorpage.video.business.output.task.OutputData
import com.oplus.gallery.videoeditorpage.video.business.output.task.ResultSaveFileData
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.GalleryVideoThumbnailView
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.IGalleryThumbController.ThumbScrollerListener
import com.oplus.gallery.videoeditorpage.widget.ProgressDialog
import com.oplus.gallery.videoeditorpage.widget.ringtone.GalleryVideoCustomTrimTouchView
import com.oplus.gallery.videoeditorpage.widget.ringtone.GalleryVideoTrimTouchView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min

/**
 * 视频铃声编辑专用 UI controller
 */
class EditorRingtoneUIController(
    private val context: Context,
    private val rootView: ViewGroup,
    private val state: EditorRingtoneState,
    private val videoTrimMinDuration: Long,
    private val videoTrimMaxDuration: Long
) : EditorBaseUIController<Any>(context, rootView, state) {
    /**
     * 视频帧缩图轴
     */
    private lateinit var thumbScrollView: GalleryVideoThumbnailView

    /**
     * 视频帧裁剪框
     */
    private lateinit var videoTrimView: GalleryVideoCustomTrimTouchView

    private lateinit var playButton: ImageView
    private lateinit var currentTimeTextView: TextView
    private lateinit var videoDurationTimeTextView: TextView

    /**
     * 最后滚动到的位置（可能会超出视频长度或小于 0）
     * 用于判断目前滚动位置是否位于 Olive 导出开头时间点。
     */
    private var lastScrollTime: Long = 0L

    /**
     * 记录滚动状态
     */
    private var thumbScrollViewLastScrollState = SCROLL_STATE_IDLE

    /**
     * 这里标识的是一个手势操作后，带来的一些列连带效应（跟手滚动、惯性滑动、极限阻尼），一直持续到禁止态结束
     * 即：当前可滚动view的内容位置变更（scroll、fling、overScroll）是否为用户触发。
     * <p>
     * true - 用户触发：
     * 1.当前状态处于SCROLL_STATE_DRAGGING
     * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
     * <p>
     * false - 非用户触发。
     */
    private var isThumbScrollViewScrollFromUserOp = false

    private var mStartPlayPercent = -1f

    private var videoDuration: Long = 0
    private var initTrimInTime: Long = 0
    private var initTrimOutTime: Long = 0
    private var mCurrentScrollStartTime: Long = 0
    private var timeStamp: Long = -1L

    private var compileDialog: ProgressDialog? = null

    private val toolbarContainer = mRootView.findViewById<FrameLayout>(R.id.toolbar_container)
    private val toolbarPlayContainer = mRootView.findViewById<ViewGroup>(R.id.toolbar_playback_container)

    private val ringtoneVM: EditorRingtoneVM by lazy {
        ViewModelProvider(this, EditorRingtoneVM.RingtoneVMFactory(mActivity.application, state.editorEngine))[EditorRingtoneVM::class.java]
    }

    /**
     * 缩图轴布局更改监听
     */
    private val onThumbScrollViewLayoutChangeListener =
        View.OnLayoutChangeListener { view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            // 判断布局是否真正发生改变
            if ((left != oldLeft) || (right != oldRight) || (top != oldTop) || (bottom != oldBottom)) {
                // 在view尚未附加到窗口时，会等待视图附加后在执行任务
                view.post {
                    GLog.d(TAG, LogFlag.DL) { "OnLayoutChangeListener, left =$left, right =$right, top =$top, bottom =$bottom" }
                    thumbScrollView.setPadding(0, thumbScrollView.paddingTop, 0, thumbScrollView.paddingBottom)
                    // 由于布局改变时需要更新缩图轴与选帧框位置，因此当布局发生改变时都需要设置一下缩图轴
                    ringtoneVM.showRingtoneTrimThumbLoader(thumbScrollView, getCurrentDuration(), getThumbScrollViewPadding())
                    thumbScrollView.setScrollListener(scrollChangeListener)
                }
            }
        }

    /**
     * 裁剪框布局监听
     */
    private val onTrimTouchViewLayoutChangeListener =
        View.OnLayoutChangeListener { view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            GLog.d(TAG, LogFlag.DL) { "onTrimTouchViewLayoutChange, width =${videoTrimView.width}, ${right - left}" }
            if (((left != oldLeft)
                        || (right != oldRight)
                        || (top != oldTop)
                        || (bottom != oldBottom))
            ) {
                view.post {
                    videoTrimView.setInitParam(videoTrimMinDuration, videoTrimMaxDuration, getMillionsVideoDuration())
                    onPlayPositionChange(0)
                }
            }
        }

    /**
     * 缩图轴滚动监听
     */
    private val scrollChangeListener: ThumbScrollerListener = object : ThumbScrollerListener {
        override fun onScrolled(pos: Int) {
            // 通过当前坐标获取对应的时间线
            val tmpTimeStamp = (floor(pos / thumbScrollView.pixelPerMicrosecond + PIXEL_OFFSET)).toLong() / TIME_BASE
            GLog.d(TAG, LogFlag.DL) { "onScrolled, pos =$pos, tmpTimeStamp:$tmpTimeStamp" }
            lastScrollTime = tmpTimeStamp
            if (isThumbScrollViewScrollFromUserOp) {
                refreshTimeAfterScroll(tmpTimeStamp)
            }
            resetTrimPosition()
        }

        override fun onScrollStateChanged(newState: Int) {
            /*
             * 此次操作为用户触发：
             * 1.当前状态处于SCROLL_STATE_DRAGGING
             * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
             */
            if ((newState == SCROLL_STATE_DRAGGING)
                || ((thumbScrollViewLastScrollState == SCROLL_STATE_DRAGGING) && (newState == SCROLL_STATE_SETTLING))) {
                if (!isThumbScrollViewScrollFromUserOp) {
                    ringtoneVM.stopPlayer()
                }

                isThumbScrollViewScrollFromUserOp = true
            }

            /*
             * 用户触发的操作停止：当前状态为SCROLL_STATE_IDLE 且 变更前处于用户触发态 mIsThumbScrollViewScrollFromUserOp = true
             */
            if (isThumbScrollViewScrollFromUserOp && (newState == SCROLL_STATE_IDLE)) {
                isThumbScrollViewScrollFromUserOp = false
                val tmpTimeStamp: Long = thumbScrollView.mapTimelinePosFromX(getThumbScrollViewPadding())
                refreshTimeAfterScroll(tmpTimeStamp)
                mStartPlayPercent = 0f
                resetTrimPosition()
                GLog.d(TAG, LogFlag.DL) { "onScrollStateChanged() tmpTimeStamp:$tmpTimeStamp" }
            }
            thumbScrollViewLastScrollState = newState
        }
    }

    /**
     * 裁剪框操作监听
     */
    private val trimPositionChangeListener: GalleryVideoTrimTouchView.TrimPositionChangeListener = object :
        GalleryVideoTrimTouchView.TrimPositionChangeListener {
        override fun onSeek(pos: Float, trimLeft: Float, trimRight: Float) {
            mStartPlayPercent = pos
            ringtoneVM.seekTo(getCurrentPlayTime() * TIME_BASE, 0)
            val currentTime: Long = mCurrentScrollStartTime + ((pos * getMicroMillionsDuration()) / TIME_BASE).toLong()
            videoTrimView.updateTrimTime(VideoEditorHelper.formatTimeWithMillis(mContext, checkValidTime(currentTime)))
            updateShowTime(currentTime - getTrimInPlayTime())
        }

        override fun onSeekChange() {
            ringtoneVM.setResetStatus(false)
        }

        override fun onTrimLeft(pos: Float) {
            mStartPlayPercent = pos
            ringtoneVM.seekTo(getStartPlayPosition(), 0)
            updateTrimViewText(true)
            updateShowTime(0)
        }

        override fun onTrimRight(pos: Float) {
            mStartPlayPercent = pos
            ringtoneVM.seekTo(getStartPlayPosition(), 0)
            updateTrimViewText(false)
            updateShowTime(getTrimmedVideoDuration())
        }

        override fun onEnterDetailMode(pos: Float) {
            // Do nothing
        }

        override fun onExitDetailMode(pos: Float) {
            // Do nothing
        }
    }

    override fun getTitleId(): Int {
        return R.string.videoeditor_ringtone_title
    }

    override fun createView() {
        val view = LayoutInflater.from(context).inflate(
            R.layout.videoeditor_video_editor_ringtone_trim_bottom_menu_layout,
            toolbarContainer,
            false
        )
        toolbarContainer.addView(view)
        initView()

        videoDuration = ringtoneVM.getVideoDuration()
        initTrimInTime = 0
        initTrimOutTime = videoDuration
        ringtoneVM.seekTo(initTrimInTime, 0)
        updateVideoDurationTextView(videoDuration)
        GLog.d(TAG, LogFlag.DL) { "EditorTrimState() trimMin:$videoTrimMinDuration, " +
                " trimMax:$videoTrimMaxDuration, videoDuration:$videoDuration" }

        // add 各类 listener
        thumbScrollView.addOnLayoutChangeListener(onThumbScrollViewLayoutChangeListener)
        videoTrimView.addOnLayoutChangeListener(onTrimTouchViewLayoutChangeListener)
        videoTrimView.setTrimPositionChangeListener(trimPositionChangeListener)
        toolbarPlayContainer.setOnClickListener {
            play()
        }

        initData()
        ringtoneVM.create()
    }

    override fun destroyView() {
        super.destroyView()
        thumbScrollView.removeOnLayoutChangeListener(onThumbScrollViewLayoutChangeListener)
        videoTrimView.removeOnLayoutChangeListener(onTrimTouchViewLayoutChangeListener)
        ringtoneVM.destroy()
    }

    private fun initView() {
        thumbScrollView = findContainerChild(R.id.video_thumb_view)
        videoTrimView = findContainerChild(R.id.trim_touch_view)
        currentTimeTextView = findContainerChild(R.id.video_current_position)
        videoDurationTimeTextView = findContainerChild(R.id.video_duration)
        playButton = findContainerChild(R.id.editor_btn_play)
        // 初始化BottomActionBar并设置title
        mBottomActionBar = mRootView?.findViewById(R.id.title_bar_container)
        mBottomActionBar?.let {
            it.title = mContext.resources.getString(titleId)
            it.switchTitleView(false)
            it.findViewById<TextView>(R.id.editor_id_action_done).text =
                mContext.resources.getString(R.string.videoeditor_ringtone_done)
        }
        // 隐藏部分view
        mRootView?.findViewById<ViewGroup>(R.id.ll_preview_icon_layout)?.visibility = View.GONE
        mRootView?.findViewById<ViewGroup>(R.id.action_bar)?.visibility = View.INVISIBLE
    }

    private fun initData() {
        ringtoneVM.playState.observe(this) { playState ->
            onPlayStatusChange(playState)
        }
        ringtoneVM.playPosition.observe(this) { position ->
            onPlayPositionChange(position)
        }
        ringtoneVM.compileProgress.observe(this) { progress ->
            compileDialog?.setProgress(progress.toInt())
        }
        ringtoneVM.compileResult.observe(this) { outputData ->
            onCompileResultChange(outputData)
        }
    }

    /**
     * 当播放进度变化时，更新裁剪框播放位置及播放时间信息
     * @param currentPosition 当前播放时间
     */
    fun onPlayPositionChange(currentPosition: Long?) {
        val currentTime: Long = ringtoneVM.getCurrentTime()
        updateCurrentPosition(currentTime)
        val startTime = max((currentTime - getTrimInPlayTime()).toDouble(), 0.0).toLong()
        updateShowTime(startTime)
    }

    /**
     * 点击应用后，进行视频保存导出
     */
    fun startCompileOperation() {
        // 当没有做任何编辑操作时，直接返回原视频的uri即可
        if (getTrimmedVideoDuration() == (videoDuration / TIME_BASE)) {
            val uri = VideoParser.getInstance().videoInfoMap.values.first().path
            state.setStateResult(Intent().apply {
                putExtra(RINGTONE_RESULT, RINGTONE_RESULT_VALID)
                data = Uri.parse(uri)
            })
            return
        }

        showCompileDialog()
        ringtoneVM.startExportTask(
            getTrimInPlayTime() * TIME_BASE,
            getTrimOutPlayTime() * TIME_BASE,
            timeStamp
        )
    }

    private fun onCompileResultChange(outputData: OutputData<ResultSaveFileData?>) {
        if (compileDialog?.isShowing() == true) {
            compileDialog?.dismiss()
            compileDialog = null
        }
        GLog.d(TAG, LogFlag.DL) { "onCompileResultChange resultCode:${outputData.resultCode}" }
        if (ResultCode.FORCE_STOP_SAVE == outputData.resultCode) {
            GLog.d(TAG, LogFlag.DL) { "onCompileResultChange cancel compile" }
            return
        }
        val result = when (outputData.resultCode) {
            ResultCode.SAVE_SUCCESS -> RINGTONE_RESULT_VALID
            else -> RINGTONE_RESULT_INVALID
        }
        AppScope.launch(Dispatchers.Main) {
            state.setStateResult(Intent().apply {
                putExtra(RINGTONE_RESULT, result)
                if (result == 1) {
                    data = outputData.resultData?.uri
                }
            })
        }
    }

    /**
     * 设置当前视频时间戳
     */
    fun setTimeStamp(time: Long) {
        timeStamp = time
    }

    /**
     * 当预览轴已经到裁剪框最右侧极限了，将时间按照裁剪框的时间进行处理，最小为 1s
     */
    private fun checkValidTime(time: Long): Long {
        // 由于裁剪框目前在计算时间，采用的 pos(float) * videoDuration(ms) 的方式，精度上存在误差，这里允许他在计算时有一定偏移
        return if (abs(time - getTrimOutPlayTime()) <= OFF_SET) {
            VideoEditorHelper.getVideoCurrentUnitTime(time)
        } else {
            time
        }
    }

    private fun showCompileDialog() {
        if (compileDialog == null) {
            val builder = ProgressDialog.Builder(context, true)
            builder.setTitle(R.string.videoeditor_ringtone_dialog_msg)
            builder.setCancelable(false)
            builder.setPositiveButton(context.resources.getString(android.R.string.cancel)) { dialog, whichButton ->
                ringtoneVM.stopPlayer()
            }
            compileDialog = builder.build().show()
        } else if (compileDialog?.isShowing() == false) {
            compileDialog?.show()
        }
    }

    /**
     * 更新编辑框当前预览播放进度条位置
     */
    private fun updateCurrentPosition(currentTime: Long) {
        val currentPlayDuration: Long = getCurrentDuration()
        if ((currentPlayDuration > 0) && (videoTrimView != null)) {
            mStartPlayPercent =
                (checkPosition(currentTime) - mCurrentScrollStartTime.toFloat()) / currentPlayDuration.toFloat()
            videoTrimView.updateCurrentPosition(mStartPlayPercent)
        }
    }

    /**
     * 更新当前时间及总时间
     * @param startTime 左侧当前时间
     */
    private fun updateShowTime(startTime: Long) {
        updateCurrentTimeTextView(startTime)
        updateVideoDurationTextView(getTrimmedVideoDuration())
    }

    /**
     * 更新当前时间，当前时间等于裁剪框最大时间时，需要按照裁剪框最大时间去做时间映射，即最小值为 1s
     */
    private fun updateCurrentTimeTextView(startTime: Long) {
        updateCurrentTimeTextView(
            checkValidTime(startTime),
            getTrimmedVideoDuration()
        )
    }

    private fun getMillionsVideoDuration(): Long {
        return videoDuration / TIME_BASE
    }

    private fun updateCurrentTimeTextView(millis: Long, duration: Long) {
        currentTimeTextView.text = VideoEditorHelper.formatTimeWithMillisByDuration(mContext, millis, duration)
    }

    private fun updateVideoDurationTextView(millis: Long) {
        videoDurationTimeTextView.text = VideoEditorHelper.formatTimeWithMillis(mContext, VideoEditorHelper.getVideoCurrentUnitTime(millis))
    }

    private fun updateTrimViewText(isTrimIn: Boolean) {
        var time = 0L
        time = if (isTrimIn) getTrimInPlayTime() else getTrimOutPlayTime()
        videoTrimView.updateTrimTime(VideoEditorHelper.formatTimeWithMillis(mContext, VideoEditorHelper.getVideoCurrentUnitTime(time / TIME_BASE)))
    }

    private fun getTrimmedVideoDuration(): Long {
        return getTrimOutPlayTime() - getTrimInPlayTime()
    }

    private fun getStartPlayPosition(): Long {
        return getStartPlayPosition(mStartPlayPercent)
    }

    private fun getStartPlayPosition(percent: Float): Long {
        val time: Long = (mCurrentScrollStartTime * TIME_BASE + percent * getCurrentDurationByMicroMillions()).toLong()
        return checkPositionByMicroMillions(time)
    }

    private fun checkPositionByMicroMillions(position: Long): Long {
        return position.coerceIn(getTrimInPlayTime() * TIME_BASE, getTrimOutPlayTime() * TIME_BASE)
    }

    private fun checkPosition(position: Long): Long {
        return position.coerceIn(getTrimInPlayTime(), getTrimOutPlayTime())
    }

    private fun getTrimInPlayTime(): Long {
        val realTrimInTime: Long = videoTrimView.getTrimInTime(mCurrentScrollStartTime)
        return realTrimInTime
    }

    private fun getTrimOutPlayTime(): Long {
        val realTrimOutTime: Long = videoTrimView.getTrimOutTime(mCurrentScrollStartTime)
        return realTrimOutTime
    }

    private fun getCurrentPlayTime(): Long {
        val currentPlayTime: Long = videoTrimView.getCurrentPlayTime(mCurrentScrollStartTime)
        GLog.d(TAG, LogFlag.DL) { "getCurrentPlayTime, currentPlayTime =$currentPlayTime" }
        return currentPlayTime
    }

    /**
     * 滚动后更新播放进度和播放时间
     * @param time 当前时间
     */
    private fun refreshTimeAfterScroll(time: Long) {
        mCurrentScrollStartTime = clamp(time, 0L, ringtoneVM.getTotalTime())
        ringtoneVM.seekTo(getCurrentPlayTime())
        updateShowTime(0)
        ringtoneVM.setResetStatus(false)
    }

    /**
     * 获取当前视频时长，单位为 ms
     */
    private fun getCurrentDuration(): Long {
        return min(videoTrimMaxDuration, getMillionsVideoDuration())
    }

    /**
     * 获取当前视频时长，单位为 us
     */
    private fun getMicroMillionsDuration(): Long {
        return min(videoTrimMaxDuration * TIME_BASE, videoDuration)
    }

    private fun getCurrentDurationByMicroMillions(): Long {
        return min(videoTrimMaxDuration * TIME_BASE, videoDuration)
    }

    private fun getThumbScrollViewPadding(): Int {
        return videoTrimView.trimWindowWidth
    }

    private fun resetTrimPosition() {
        // 重置预览条位置
        if (videoTrimView.leftBorderPosPercent != videoTrimView.currentPlayPos) {
            videoTrimView.updateCurrentPosition(videoTrimView.leftBorderPosPercent)
        }
    }

    private fun play() {
        ringtoneVM.adjustClip()

        GLog.d(TAG, LogFlag.DL) { "play_click, isPlaying:${ringtoneVM.isPlaying()}, " +
                "getCurrentTime:${ringtoneVM.getCurrentTime()}, " +
                "getStartPlayPosition:${getStartPlayPosition()}, " +
                "getTrimOutPlayTime:${getTrimOutPlayTime()}" }

        if (ringtoneVM.isPlaying()) {
            ringtoneVM.stopPlayer()
            playButton.isSelected = false
        } else {
            if (ringtoneVM.getResetStatus() || (ringtoneVM.getCurrentTime() > (getTrimOutPlayTime() - TRIM_OFFSET))) {
                mStartPlayPercent = videoTrimView.getBorderPosPercent(true)
                ringtoneVM.setResetStatus(false)
            }
            ringtoneVM.startPlayer(getStartPlayPosition(), getTrimOutPlayTime() * TIME_BASE)
            playButton.isSelected = true
        }
    }

    private fun onPlayStatusChange(playState: Int) {
        toolbarPlayContainer.postDelayed({
            GLog.d(TAG, LogFlag.DL) { "[playState] change to:$playState" }
            val window = (context as Activity).window
            when (playState) {
                PlayState.PLAYING -> {
                    playButton.isSelected = true
                    playButton.contentDescription =
                        context.getString(R.string.videoeditor_editor_pause_description)
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }
                PlayState.FINISH -> {
                    playButton.isSelected = false
                    playButton.contentDescription =
                        context.getString(R.string.videoeditor_editor_play_description)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    onPlayFinish()
                }
                else -> {
                    playButton.isSelected = false
                    playButton.contentDescription =
                        context.getString(R.string.videoeditor_editor_play_description)
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                }
            }
        }, STATUS_CHANGE_REFRESH_DELAY.toLong())
    }

    private fun onPlayFinish() {
        GLog.d(TAG, LogFlag.DL, "onPlayFinish()")
        updateCurrentPosition(getTrimOutPlayTime())
        updateShowTime(getTrimmedVideoDuration())
    }

    /**
     * 查找container下的子view控件
     */
    private fun <T : View> findContainerChild(id: Int): T = mRootView.findViewById(id)

    companion object {
        private const val TAG = "EditorRingtoneUIController"
        private const val TIME_BASE = 1000L
        // 像素偏移，用于参与通过坐标得到映射的时间线
        private const val PIXEL_OFFSET = 0.5
        private const val OFF_SET = 10
        private const val TRIM_OFFSET = 100
        private const val STATUS_CHANGE_REFRESH_DELAY = 50
        // 返回导出的结果
        private const val RINGTONE_RESULT = "ringtone_result"
        // 视频铃声设置有效，返回结果
        private const val RINGTONE_RESULT_VALID = 1
        // 视频铃声设置无效，出现异常或主动取消
        private const val RINGTONE_RESULT_INVALID = 0
    }
}