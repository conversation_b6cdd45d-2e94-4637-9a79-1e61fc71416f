/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoSlotLoadingProxy.kt
 ** Description : 大图内容资源加载代理
 ** Version     : 1.0
 ** Date        : 2021/09/15
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2021/09/15  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.details

import androidx.collection.LruCache
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.activeWhen
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.ext.updateValueIfChanged
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.PanelContractEffectState
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.ProgressEffectController
import com.oplus.gallery.photo_page.ui.section.details.transition.controller.DetailsTransitionController.Companion.TRANSITION_STATE_IDLE
import com.oplus.gallery.photo_page.ui.section.details.transition.controller.DetailsTransitionController.Companion.TRANSITION_STATE_DRAGGING
import com.oplus.gallery.photo_page.ui.section.details.transition.controller.DetailsTransitionController.Companion.TRANSITION_STATE_SETTLING
import com.oplus.gallery.photo_page.ui.section.details.transition.controller.DetailsTransitionController.DisplayState
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDerivativeData
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.diffedFocusViewDataDistinctUi
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.dataloading.photoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.FirstFrameRenderingStatus
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.app.AppConstants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicReference

internal typealias PhotoInfoMediaItemGetter = (index: Int) -> MediaItem?

/**
 * 详情获取接口
 */
internal typealias PhotoInfoIndexConverter<T> = (T) -> Int?

/**
 * 大图页详情管理
 */
class PhotoDetailsViewModel internal constructor(
    override val pageViewModel: PhotoViewModel,
    private val cacheSize: Int,
    private val mediaItemGetter: PhotoInfoMediaItemGetter
) : PhotoSubViewModel(pageViewModel) {

    /**
     * 当前图片的详细信息
     */
    val currentPhotoInfo: StateFlow<PhotoInfo?> get() = _currentPhotoInfo
    private val _currentPhotoInfo = MutableStateFlow<PhotoInfo?>(null)

    /**
     * 是否显示杜比视界的新手引导
     */
    val shouldShowDolbyTips: LiveData<Boolean> get() = _shouldShowDolbyTips
    private val _shouldShowDolbyTips = MutableLiveData<Boolean>()

    /**
     * 信息加载器缓冲，指定大小[cacheSize]。
     * 以index作为key，将该数组作为hash表存放加载任务，发生hash冲突时抛弃之前的任务。
     */
    private val cachedPhotoInfoLoaderRefs = arrayOfNulls<PhotoInfoLoader>(cacheSize)

    /**
     * index的转换器，将图片的index转换为在缓存中的key。
     */
    private val indexConverter: PhotoInfoIndexConverter<Int> = { it % cacheSize }

    /**
     * 当前加载任务。
     */
    private val currentLoader = AtomicReference<PhotoInfoLoader?>()

    /**
     * 是否正在上拉
     */
    val isSlidingUp: LiveData<Boolean> get() = _isSlidingUp
    private val _isSlidingUp = MutableLiveData(false)

    /**
     * 是否处于详情展示模式。
     * 详情 panel 的 Expand 动画和 Back 动画由此值驱动，详见：[DetailOverlayManger.notifyDetailsModeChanged]
     */
    val isInDetailsMode: LiveData<Boolean> get() = _isInDetailsMode
    private val _isInDetailsMode = MutableLiveData<Boolean>()

    /**
     * 是否处于详情展示模式（从展开 Transition 开始到收起 Transition 结束），
     * 原本的 [isInDetailsMode] 不包含退出动画部分和手势展开部分
     */
    val isInDetailsModeInclTransition: LiveData<Boolean> get() = _isInDetailsModeInclTransition
    private val _isInDetailsModeInclTransition = MutableLiveData<Boolean>()

    /**
     * focus 位置的详情 panel 的过度状态
     * 可以配合 [isInDetailsMode] 一起判断是否是被阻断。
     *
     * 状态流转详见：[DetailsPanelTransitionStatus]
     * 赋值详见：[DetailsModeLifecycleContract]
     *
     * 注意：这个从逻辑看只是服务于某一部分业务的，并不等同于详情页面的过渡状态，如需要详情的过渡状态 @see [focusTransitionState]
     */
    val transitionStatus: LiveData<DetailsPanelTransitionStatus> get() = _transitionStatus
    private val _transitionStatus = MutableLiveData<DetailsPanelTransitionStatus>()

    /**
     *  【唯一可信数据源】 focus 位置的详情 panel 的过渡状态
     *
     *  等同于大图Pager的 [photoPagerScrollState]
     *
     *  @see TRANSITION_STATE_IDLE
     *  @see TRANSITION_STATE_DRAGGING
     *  @see TRANSITION_STATE_SETTLING
     */
    val focusTransitionState: LiveData<Int> get() = _focusTransitionState
    private val _focusTransitionState = MutableLiveData<Int>(TRANSITION_STATE_IDLE)

    /**
     *  【唯一可信数据源】 focus 位置的详情 panel 展开状态
     *
     *  @see DisplayState.EXPAND     展开态
     *  @see DisplayState.COLLAPSE   收起态
     */
    val focusPanelDisplayState: LiveData<DisplayState> get() = _focusPanelDisplayState
    private val _focusPanelDisplayState = MutableLiveData<DisplayState>()

    /**
     * 详情details缓存。采用Lru。String为mediaItem的path，PhotoDetails为详情信息
     */
    private val detailsCache = LruCache<String, PhotoDetails>(DETAILS_CACHE_SIZE)

    /**
     * 是否需要执行动画在详情模式变化时。
     */
    internal var needAnimOnDetailsModeChanged: Boolean = true

    /**
     * 详情panel滑动状态
     */
    internal val transitionPanelEffectState: LiveData<PanelContractEffectState?> get() = _transitionPanelContractEffectState
    private val _transitionPanelContractEffectState: MutableLiveData<PanelContractEffectState?> = MutableLiveData()

    /**
     * 获取经过 入场动画时机 + 滑动时机 + 首帧渲染已经准备好 控制的流。
     * 该流中的diffed数据只有在大图【入场动画结束 且 滑动idle 且首帧渲染已经准备好】 时才会下发。
     * - 注意！：仅作为时机监听，不能用来取值 (值可能不是最新的)
     * - 数据源头：[PhotoDataLoadingViewModel.diffedFocusViewData] 原始数据
     * - 数据发送时机：在大图【入场动画结束 且 滑动idle 且首帧渲染已经准备好】 时才会下发
     *
     * 这里不能直接使用[PhotoDerivativeData.diffedFocusViewDataWhenRenderedAndIdle]，否则会有问题：从相机点击缩图进大图-上拉没有详情面板-因为diffedFocusViewDataWhenRenderedAndIdle
     * 会先触发，然后就把DetailsOverlay给加上去了，但是数据还没加载，PhotoSlotLoadingProxy中shouldBindData会将所有Overlay移除，导致当前PhotoSlot没有DetailsOverlay，所以拉不出panel
     */
    internal val diffedFocusViewDataWhenRenderedAndIdle: Flow<DiffedPhotoItemViewData?> by lazy {
        pageViewModel.dataLoading.diffedFocusViewData.activeWhen(
            pageViewModel.pageManagement.scrollState,
            pageViewModel.pageManagement.transitionState,
            pageViewModel.pageManagement.firstFrameRenderStatus
        ) { result ->
            val scrollState = result[AppConstants.Number.NUMBER_1]
            val transitionState = result[AppConstants.Number.NUMBER_2]
            val firstFrameRenderStatus = result[AppConstants.Number.NUMBER_3]
            (scrollState == ViewPager.SCROLL_STATE_IDLE) && (transitionState == PhotoPageManagementViewModel.PageTransitionState.PRESENTED) &&
                    ((firstFrameRenderStatus as? FirstFrameRenderingStatus)?.isAtLeastOneReady == true)
        }
    }

    override fun onBind() {
        launch(Dispatchers.Main.immediate) {
            pageViewModel.dataLoading.diffedFocusViewDataDistinctUi.collectNotNull {
                //focus切换时
                GTrace.traceBegin { "$TAG.focusSlotViewDataDiff" }
                doUpdateFocusPhotoInfo()
                GTrace.traceEnd()
            }
        }

        //详情信息变更@MarkBy伍子俊理应overlay能把握刷新时机，但是现在出现人像图片加载完之后，刷新数据还是旧的，需要监听diffedFocusViewData变化再刷新才正确
        launch(Dispatchers.Main.immediate) {
            pageViewModel.dataLoading.diffedFocusViewData.collectNotNull {
                //是否是展开详情的情况下切换
                val focusSlot = pageViewModel.dataLoading.focusSlot
                loadPhotoDetailsBySlot(focusSlot)
            }
        }
    }

    /**
     * 更新焦点的图片详情
     */
    private fun doUpdateFocusPhotoInfo() {
        val focusSlot = pageViewModel.dataLoading.focusSlot
        if (focusSlot == INVALID_INDEX) {
            GLog.w(TAG) { "[doUpdateFocusPhotoInfo] focusSlot is invalid" }
            return
        }

        // 1. 获取焦点对应的详情加载器，内部会去重。
        val focusInfoLoader = getPhotoInfoLoaderByIndex(focusSlot) ?: let {
            GLog.w(TAG) { "[doUpdateFocusPhotoInfo] focusInfoLoader is null" }
            return
        }

        // 2. 更新 currentLoader并切换观察对象
        val oldLoader = currentLoader.getAndSet(focusInfoLoader)
        oldLoader?.setPhotoInfoListener(null)
        oldLoader?.cancel()

        val listener = listener@{ photoInfo: PhotoInfo ->
            if (photoInfo.index != pageViewModel.dataLoading.focusSlot) return@listener
            _currentPhotoInfo.tryEmit(photoInfo)
            updateDolbyTipsLiveData(photoInfo)
        }
        focusInfoLoader.setPhotoInfoListener(listener, currentPhotoInfo.value)

        // 3. 请求开始加载
        if (pageViewModel.dataLoading.diffedFocusViewData.value?.isNeedUpdateUI == true) {
            focusInfoLoader.requestStartLoadPhotoInfo()
        }
    }

    /**
     * 根据[index]获取详情加载器
     */
    private fun getPhotoInfoLoaderByIndex(index: Int): PhotoInfoLoader? {
        // index无效
        val cachedIndex = indexConverter(index) ?: let {
            GLog.d(TAG) { "[getPhotoInfoLoaderByIndex] index is invalid, index=$index" }
            return null
        }

        // 超出缓存范围
        if (cachedIndex !in (0 until cacheSize)) {
            GLog.d(TAG) {
                "[getPhotoInfoLoaderByIndex] index is out of bound, index=$index, cacheIndex=$cachedIndex, cacheSize=$cacheSize"
            }
            return null
        }

        // 获取MediaItem
        val mediaItem = mediaItemGetter(index)?.toOriginalItem() ?: let {
            GLog.d(TAG) {
                "[getPhotoInfoLoaderByIndex] can't get mediaItem, index=$index, cacheIndex=$cachedIndex, cacheSize=$cacheSize"
            }
            return null
        }

        // 获取已缓存的任务
        val cachedLoader = cachedPhotoInfoLoaderRefs[cachedIndex]
        val isCachedLoaderNotExpired = cachedLoader?.let { (it.index == index) && (mediaItem == it.mediaItem) } ?: false

        // 如果缓存的任务仍然有效，返回缓存任务
        if (isCachedLoaderNotExpired) {
            return cachedLoader
        }

        // 缓存任务无效，终止旧任务，创建新任务并返回
        cachedLoader?.cancel()
        val photoInfoLoader = PhotoInfoLoader(
            context = pageViewModel.viewModelApplication,
            coroutineScope = pageViewModel,
            mediaItem = mediaItem,
            index = index
        )
        cachedPhotoInfoLoaderRefs[cachedIndex] = photoInfoLoader
        return photoInfoLoader
    }

    override fun onStop() {
        //当前跳转编辑页后，退出详情模式
        if (shouldExitDetailsModeWhenStop()) {
            changeDetailsMode(false, false)
        }
    }

    private fun shouldExitDetailsModeWhenStop(): Boolean =
        (pageViewModel.pageManagement.currentPage.value?.isEditorPage() == true
                || pageViewModel.pageManagement.currentPage.value?.isSynthesisGifPage() == true
                ) && (_isInDetailsMode.value == true)

    override fun onCleared() {
        super.onCleared()
        cachedPhotoInfoLoaderRefs.forEach { it?.cancel() }
    }

    private fun updateDolbyTipsLiveData(photoInfo: PhotoInfo) {
        val formatType = photoInfo.details.getInt(PhotoDetailsConstants.Key.FORMAT_TYPE, PhotoDetailsConstants.FormatType.OTHER)
        // 1. 视频不是杜比视频，更新liveData为false，return
        if (formatType != PhotoDetailsConstants.FormatType.DOLBY) {
            _shouldShowDolbyTips.updateValueIfChanged(false)
            return
        }
        // 2. 不支持杜比编解码，更新liveData为false，return
        val isSupportDolbyCodec: Boolean = ConfigAbilityWrapper.let {
            it.getBoolean(IS_SUPPORT_DOLBY_DECODE) && it.getBoolean(IS_SUPPORT_DOLBY_ENCODE)
        }
        if (isSupportDolbyCodec.not()) {
            _shouldShowDolbyTips.updateValueIfChanged(false)
            return
        }
        // 3. 更新liveData，显示新手引导
        GLog.d(TAG, "[updateDolbyTipsLiveData], update dolby tips liveData:true, show dolby vision tips")
        _shouldShowDolbyTips.updateValueIfChanged(true)
    }

    /**
     * 详情弹窗使用
     * 根据[slot]获取photoItemViewData信息和缓存信息，看是否需要更新或创建PhotoDetails
     * @param slot 当前需要创建PhotoDetails的焦点
     */
    internal fun loadPhotoDetailsBySlot(slot: Int): PhotoDetails? {
        // 获取slot对应photoItemViewData
        val photoItemViewData = loadPhotoItemViewDataByIndex(slot) ?: let {
            //获取当前焦点下标与创建用的focusSlot的差值，用来判断是当前focus还是之前、之后的photoItemViewData获取失败
            val focusSlotOffset = pageViewModel.dataLoading.focusSlot - slot
            GLog.w(TAG, LogFlag.DL) { "[loadPhotoDetailsBySlot] can't load photoItemViewData, slot=$slot, focusSlot offset=$focusSlotOffset" }
            return null
        }

        //获取缓存信息
        val photoDetails: PhotoDetails? = getCachePhotoDetail(photoItemViewData.id)
        //如果为空、需要更新，则创建新的PhotoDetails，更新规则看[needUpdateData]逻辑
        return if (photoDetails == null) {
            //没有缓存值时创建一个
            createPhotoDetailsByPhotoItemViewData(photoItemViewData)
        } else if (photoDetails.needUpdateData(photoItemViewData)) {
            //有变化时缓存值时在初始值为上次数据的情况下重新加载数据
            photoDetails.loadPhotoDetails(photoItemViewData)
            photoDetails
        } else {
            //直接返回缓存信息
            photoDetails
        }
    }

    /**
     * 详情弹窗使用
     * 根据焦点对应的[photoItemViewData]来创建PhotoDetails
     */
    private fun createPhotoDetailsByPhotoItemViewData(photoItemViewData: PhotoItemViewData): PhotoDetails {
        //则创建details并放入缓存中
        val photoDetails: PhotoDetails =
            PhotoDetails(pageViewModel = pageViewModel, photoItemViewData = photoItemViewData, viewModelScope = this@PhotoDetailsViewModel).also {
                detailsCache.put(photoItemViewData.id, it)
            }
        return photoDetails
    }

    /**
     * 详情弹窗使用
     * 根据焦点[slot]获取已缓存的PhotoItemViewData对象（MediaItem的封装）
     */
    private fun loadPhotoItemViewDataByIndex(slot: Int): PhotoItemViewData? {
        val photoItemViewData = pageViewModel.dataLoading.photoViewDataSet?.get(slot) ?: let {
            GLog.w(TAG, LogFlag.DL) { "[loadPhotoItemViewDataByIndex]slot not in data set, slot:$slot, dataLoading:${pageViewModel.dataLoading}" }
            return null
        }
        return photoItemViewData
    }

    /**
     * 更换详情页模式
     * @param enter
     *             true：进入详情页
     *             false:退出详情页
     * @param withAnim  是否执行动画，默认值为true。
     *              true: 执行动画
     *              false: 不执行动画
     */
    fun changeDetailsMode(enter: Boolean, withAnim: Boolean = true) {
        GLog.d(TAG, LogFlag.DL) { "[changeDetailsMode] <isInDetailsMode> will update. enter=$enter, withAnim=$withAnim" }
        needAnimOnDetailsModeChanged = withAnim
        _isInDetailsMode.setOrPostValue(enter)
    }

    fun notifyTransitionStatusChanged(status: DetailsPanelTransitionStatus) {
        GLog.d(TAG, LogFlag.DL) { "[notifyTransitionStatusChanged] status: $status" }
        _transitionStatus.updateValueIfChanged(status)

        when (status) {
            DetailsPanelTransitionStatus.EnterStart -> _isInDetailsModeInclTransition.setOrPostValue(true)
            DetailsPanelTransitionStatus.ExitEnd -> _isInDetailsModeInclTransition.setOrPostValue(false)
            else -> {}
        }
    }

    /**
     * 更新焦点面板过渡状态
     */
    fun notifyFocusTransitionStateChanged(state: Int) {
        _focusTransitionState.updateValueIfChanged(state)
    }

    /**
     * 通知焦点Panel的装填改变
     */
    fun notifyFocusPanelDisplayStateChanged(state: DisplayState) {
        _focusPanelDisplayState.setOrPostValue(state)
    }

    /**
     * 获取详情信息
     * @param mediaPath 图片mediaItem的path.toString
     */
    internal fun getCachePhotoDetail(mediaPath: String): PhotoDetails? {
        return detailsCache.get(mediaPath)
    }

    /**
     * 详情panel滑动状态
     * @param state 动效状态
     */
    fun changeTransitionPanelEffectState(panelEffectState: PanelContractEffectState) {
        _transitionPanelContractEffectState.setOrPostValue(panelEffectState)
    }

    /**
     * 是否处于滑动展开或滑动收缩阻断状态
     * @param panelContractEffectState 动效状态
     */
    fun isDetailModeByTransition(panelContractEffectState: PanelContractEffectState? = null): Boolean {
        val state = panelContractEffectState ?: _transitionPanelContractEffectState.value
        return ProgressEffectController.isDetailsMode(state)
    }

    companion object {
        private const val TAG = "PhotoDetailsViewModel"

        /**
         * 详情details缓存的大小。
         */
        private const val DETAILS_CACHE_SIZE = 128
    }
}