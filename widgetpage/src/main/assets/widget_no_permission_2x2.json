{"type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "id": "mainLayout", "background": "@color/widget_color_card_background", "package": "com.coloros.gallery3d", "child": [{"type": "image", "id": "widget_image_logo", "layout_width": "@dimen/widget_logo_width", "layout_height": "@dimen/widget_logo_height", "layout_marginStart": "@dimen/widget_logo_margin", "layout_marginTop": "@dimen/widget_logo_margin", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "src": "@drawable/photo_logo", "adjustViewBounds": true, "scaleType": "fitXY"}, {"type": "text", "id": "app_name", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingStart": "@dimen/widget_title_padding_left", "layout_constraintStart_toEndOf": "widget_image_logo", "layout_constraintTop_toTopOf": "widget_image_logo", "layout_constraintBottom_toBottomOf": "widget_image_logo", "text": "@string/app_name", "textColor": "@color/widget_mode_item_subtitle_color", "fontFamily": "sans-serif-medium", "textSize": "@dimen/widget_desc_size", "gravity": "center_vertical", "maxLines": "1", "ellipsize": "end"}, {"type": "text", "id": "no_permission_desc", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingStart": "@dimen/dp_16", "paddingEnd": "@dimen/dp_16", "layout_constraintTop_toBottomOf": "app_name", "layout_constraintBottom_toTopOf": "widget_setting", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "text": "@string/widget_no_permission_desc", "textColor": "@color/widget_mode_item_title_color", "fontFamily": "sans-serif-medium", "gravity": "center_horizontal", "textSize": "@dimen/widget_desc_size", "maxLines": "1", "ellipsize": "end"}, {"type": "text", "id": "widget_setting", "layout_width": "116dp", "layout_height": "wrap_content", "minHeight": "32dp", "background": "@drawable/widget_setting_bg", "layout_marginBottom": "@dimen/dp_16", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "text": "@string/common_settings", "textColor": "@color/white", "fontFamily": "sans-serif-medium", "textSize": "@dimen/widget_desc_size", "maxLines": "1", "enabled": "false", "clickable": "false", "ellipsize": "end", "gravity": "center"}]}