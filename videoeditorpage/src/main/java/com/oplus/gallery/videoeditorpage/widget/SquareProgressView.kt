/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SquareProgressView
 ** Description: 视频保存预览图进度条组件
 ** Version: 1.0
 ** Date : 2025/04/21
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/04/21    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PathMeasure
import android.util.AttributeSet
import android.view.View
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper

/**
 * 视频保存预览图进度条组件
 */
class SquareProgressView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : View(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 绘制的进度值
     */
    var drawProgress: Float = 0f
        set(value) {
            if (field >= value) return
            field = value
            invalidate()
        }

    /**
     * 是否镜像
     */
    private val isRtl: Boolean = ResourceUtils.isRTL(context)

    /**
     * 底色画笔
     */
    private val backgroundBarPaint: Paint = Paint()

    /**
     * 进度画笔
     */
    private val progressBarPaint: Paint = Paint()

    /**
     * 圆角（线条有一定宽度，这个是线条中线位置，到圆心的距离）
     */
    private var lineRadius: Float = 0f

    /**
     * 外圆角（线条有一定宽度，这个是线条外围位置，到圆心的距离）
     */
    private var outLineRadius: Float = 0f

    /**
     * 线宽度
     */
    private var lineWidth: Float = 0f

    /**
     * 画笔宽度的一半
     */
    private var halfStrokeWidth: Float = 0f

    /**
     * 进度条底色背景
     */
    private var progressBackgroundColor: Int = 0

    /**
     * 进度条颜色
     */
    private var progressColor: Int = 0

    /**
     * 底色圆角矩形路径
     */
    private val backgroundPath: Path = Path()

    /**
     * 路径测量
     */
    private val pathMeasure = PathMeasure()

    init {
        val array = context.obtainStyledAttributes(attrs, R.styleable.SquareProgressView, defStyleAttr, defStyleRes)
        try {
            lineRadius = array.getDimension(
                R.styleable.SquareProgressView_progressLineRadius,
                resources.getDimension(R.dimen.videoeditor_save_video_layout_radius)
            )
            lineWidth = array.getDimension(
                R.styleable.SquareProgressView_progressLineWidth,
                resources.getDimension(R.dimen.videoeditor_save_video_progress_width)
            )
            halfStrokeWidth = lineWidth / MathUtils.TWO_F
            outLineRadius = lineRadius + halfStrokeWidth
            progressBackgroundColor = array.getColor(
                R.styleable.SquareProgressView_progressLineBgColor,
                resources.getColor(R.color.videoeditor_save_progress_bg_color, null)
            )
            progressColor = array.getColor(
                R.styleable.SquareProgressView_progressLineColor,
                ThemeHelper.getCouiColorContainerTheme(context)
            )
        } catch (e: IllegalArgumentException) {
            GLog.e(TAG, LogFlag.DL) { "[init] Illegal argument: ${e.message}" }
        } catch (e: Resources.NotFoundException) {
            GLog.e(TAG, LogFlag.DL) { "[init] Resource not found: ${e.message}" }
        } finally {
            array.recycle()
        }
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        backgroundBarPaint.apply {
            color = progressBackgroundColor
            strokeWidth = lineWidth
            isAntiAlias = true
            isDither = true
            style = Paint.Style.STROKE
        }
        progressBarPaint.apply {
            color = progressColor
            strokeWidth = lineWidth
            isAntiAlias = true
            isDither = true
            style = Paint.Style.STROKE
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        backgroundPath.reset()
        backgroundPath.addRoundRect(
            halfStrokeWidth,
            halfStrokeWidth,
            width - halfStrokeWidth,
            height - halfStrokeWidth,
            lineRadius,
            lineRadius,
            Path.Direction.CW,
        )
        pathMeasure.setPath(backgroundPath, false)
    }

    override fun onDraw(canvas: Canvas) {
        if (drawProgress > PROGRESS_MAX_VALUE || drawProgress < PROGRESS_MIN_VALUE) {
            return
        }

        super.onDraw(canvas)
        canvas.drawPath(backgroundPath, backgroundBarPaint)

        // 一圈总周长
        val pathLength: Float = pathMeasure.length
        // 水平方向的直线长度
        val horizontalLineLength: Float = width - outLineRadius * MathUtils.TWO_F
        // 垂直方向的直线长度
        val verticalLineLength: Float = height - outLineRadius * MathUtils.TWO_F
        // 4个圆角的弧长
        val circumference: Float = pathLength - horizontalLineLength * MathUtils.TWO_F - verticalLineLength * MathUtils.TWO_F
        // 矩阵终点位置到顶部中间位置距离
        val topCenter: Float = verticalLineLength + circumference / MathUtils.FOUR_F + horizontalLineLength / MathUtils.TWO_F
        // 进度对应的长度
        val startLength: Float = pathLength * drawProgress / PROGRESS_MAX_VALUE

        if (isRtl) {
            drawProgressRTL(canvas, topCenter, startLength, pathLength)
        } else {
            drawProgress(canvas, topCenter, startLength, pathLength)
        }
    }

    private fun drawProgress(canvas: Canvas, topCenter: Float, startLength: Float, pathLength: Float) {
        // 1. 从顶部中间位置，绘制到矩阵终点位置
        val startProgressPath = Path()
        pathMeasure.getSegment(topCenter, topCenter + startLength, startProgressPath, true)
        canvas.drawPath(startProgressPath, progressBarPaint)

        // 2. 从矩阵起点位置，绘制到顶部中间位置
        if (startLength > pathLength - topCenter) {
            val endLength = startLength - pathLength + topCenter
            val endProgressPath = Path()
            pathMeasure.getSegment(0f, endLength, endProgressPath, true)
            canvas.drawPath(endProgressPath, progressBarPaint)
        }
    }

    private fun drawProgressRTL(canvas: Canvas, topCenter: Float, startLength: Float, pathLength: Float) {
        // 1. 从顶部中间位置，绘制到矩阵起点位置
        val startProgressPath = Path()
        pathMeasure.getSegment(topCenter - startLength, topCenter, startProgressPath, true)
        canvas.drawPath(startProgressPath, progressBarPaint)

        // 2. 从矩阵终点位置，绘制到顶部中间位置
        if (startLength > topCenter) {
            val endLength = startLength - topCenter
            val endProgressPath = Path()
            pathMeasure.getSegment(pathLength - endLength, pathLength, endProgressPath, true)
            canvas.drawPath(endProgressPath, progressBarPaint)
        }
    }

    internal companion object {
        private const val TAG = "SquareProgressView"

        /**
         * 进度最大值
         */
        private const val PROGRESS_MAX_VALUE = 100.0F

        /**
         * 进度最小值
         */
        private const val PROGRESS_MIN_VALUE = 0F
    }
}