/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - NativeRenderer.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/31
 ** Author      : YongQi.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/31  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec

import android.view.Surface

interface NativeRenderer {
    fun render(surface: Surface)

    interface OnRenderComplete {
        fun onRenderComplete(surface: Surface)
    }
}