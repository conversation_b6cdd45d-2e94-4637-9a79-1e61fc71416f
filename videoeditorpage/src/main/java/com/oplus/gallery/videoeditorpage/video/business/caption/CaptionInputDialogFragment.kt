/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : CaptionInputDialogFragment.kt
 ** Description : 文字输入弹窗
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.button.COUIButton
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.MeicamCaption
import com.oplus.gallery.videoeditorpage.databinding.VideoeditorCaptionInputLayoutBinding
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil
import com.oplus.gallery.videoeditorpage.utlis.ScreenAdaptUtil.Companion.isMiddleAndLargeWindow
import com.oplus.gallery.videoeditorpage.utlis.ScreenUtils
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.PageType
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.ViewPagerAdapter

/**
 * 字幕输入主弹窗
 */
class CaptionInputDialogFragment() : DialogFragment(), View.OnClickListener {

    /**
     * 标识当前弹窗是否显示
     */
    val isShowing: Boolean
        get() {
            return dialog?.isShowing ?: false
        }

    /**
     * 默认文字
     */
    private val defaultText: String by lazy {
        getString(R.string.videoeditor_caption_edit_input_text_hide)
    }

    /**
     * 上下文
     */
    private var context: Context? = null

    /**
     * 字幕编辑状态管理
     */
    private var onOperationListener: CaptionOperationListener? = null

    /**
     * 弹窗弹起时是否显示键盘
     */
    private var shouldShowKeyboard: Boolean = false

    constructor(context: Context, onOperationListener: CaptionOperationListener, shouldShowKeyboard: Boolean = false) : this() {
        this.context = context
        this.onOperationListener = onOperationListener
        this.manager = (context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager)
        this.shouldShowKeyboard = shouldShowKeyboard
    }

    var selectedCaption: MeicamCaption? = null
        set(value) {
            field = value
            // 当前编辑的caption发生变化时同步修改pageAdapter的caption
            pagerAdapter?.let {
                it.caption = value
            }
        }

    /**
     * 页面类型数组，配置包含几个弹窗页
     */
    private val pageTypes: MutableList<PageType>
        get() = mutableListOf(
            PageType.PAGE_TYPE_STYLE,
            PageType.PAGE_TYPE_FONT,
            PageType.PAGE_TYPE_TEXT,
            PageType.PAGE_TYPE_STROKE,
            PageType.PAGE_TYPE_SHADOW,
            PageType.PAGE_TYPE_BACKGROUND
        )

    /**
     * 输入法管理器
     */
    private var manager: InputMethodManager? = null


    /**
     * 输入完成按钮
     */
    private var inputDoneButton: COUIButton? = null

    /**
     * 文字输入框
     */
    private var inputEditText: EditText? = null

    /**
     * 字幕属性设置切换tab
     */
    private var captionTabLayout: TabLayout? = null

    /**
     * 弹窗切换面板
     */
    private var viewPager: ViewPager2? = null

    /**
     * 切换面板适配器
     */
    private var pagerAdapter: ViewPagerAdapter? = null

    /**
     * 输入弹窗主view
     */
    private var contentView: View? = null

    /**
     * 输入框+tab导航栏部分，用于当键盘弹出时设置弹框高度保证不被键盘遮罩
     */
    private var topLayout: LinearLayout? = null

    /**
     * 输入框内容变化监听器
     */
    private var editTextListener: EditTextListener? = null

    /**
     * 键盘弹出监听器
     */
    private var insetsListener: OnApplyWindowInsetsListener? = null

    /**
     * 记录TabLayout高度
     */
    private var lastTopLayoutHeight = 0

    /**
     * 输入框布局布局监听器
     */
    private var inputEditTextLayoutListener: OnGlobalLayoutListener? = null

    /**
     * 用户是否点击完成
     */
    private var isUserClickDone: Boolean = false

    /**
     * 输入法是否显示
     */
    private var isSoftInputShowing: Boolean = false

    /**
     * 底部导航栏高度
     */
    private var bottomNaviBarHeight = 0

    /**
     * 调整dialogHeight的动画
     */
    private var heightAnimator: ValueAnimator? = null

    /**
     * 是否有虚拟按键
     */
    private var hasVirtualKey: Boolean = false

    /**
     * 屏幕状态
     */
    private var isScreenOn = true

    /**
     * 屏幕状态监听
     */
    private var screenReceiver: BroadcastReceiver? = null

    /**
     * 屏幕息屏记录输入法是否可见
     */
    private var wasSoftInputVisible = false

    /**
     * 显示字幕输入弹窗
     */
    fun show() {
        editTextListener = editTextListener ?: EditTextListener()
        show((context as FragmentActivity).supportFragmentManager, TAG)
    }

    /**
     * 更新tabLayout和viewPager
     */
    fun updateLayout() {
        initTabLayoutAndViewPager()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        GLog.d(TAG, LogFlag.DL) { "onCreate savedInstanceState:$savedInstanceState" }
        setStyle(STYLE_NORMAL, R.style.VideoEditorCaptionDialog)
        if (savedInstanceState != null) {
            closeDialogSafely()
        }
    }

    /**
     * 关闭弹窗
     */
    private fun closeDialogSafely() {
        if ((activity == null) || (activity?.isFinishing == true) || (activity?.isDestroyed == true)) {
            GLog.d(TAG, LogFlag.DL) { "dismiss, activity is finish ." }
            return
        }
        dismissAllowingStateLoss()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        contentView = VideoeditorCaptionInputLayoutBinding.inflate(inflater, container, false).root
        return contentView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        registerScreenReceiver()
        setDialogHeight(resources.getDimensionPixelOffset(R.dimen.videoeditor_caption_layout_height))
        initScreenParams()
        initView()
        initTabLayoutAndViewPager()
        initEvent()
        initContentData()
        ScreenUtils.transparentStatusBarAndNavigationBar(dialog?.window, true)
    }

    /**
     * 注册屏幕状态监听
     */
    private fun registerScreenReceiver() {
        screenReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_OFF -> {
                        isScreenOn = false
                        wasSoftInputVisible = isSoftInputShowing
                    }
                    Intent.ACTION_SCREEN_ON -> {
                        isScreenOn = true
                        if (wasSoftInputVisible) {
                            showSoftKeyBoard()
                        }
                    }
                }
            }
        }

        context?.registerReceiver(screenReceiver, IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_SCREEN_ON)
        })
    }

    /**
     * 销毁屏幕状态监听
     */
    private fun unregisterScreenReceiver() {
        screenReceiver?.let {
            context?.unregisterReceiver(it)
            screenReceiver = null
        }
    }

    /**
     * 从当前 Activity 获取底部导航栏高度和是否存在虚拟按键。
     */
    private fun initScreenParams() {
        val activity = activity as? EditorActivity ?: return
        bottomNaviBarHeight = activity.bottomNaviBarHeight()
        hasVirtualKey = activity.hasVirtualKey()
    }

    override fun onResume() {
        super.onResume()
        if (shouldShowKeyboard) showSoftKeyBoard(true)
    }

    private fun initView() {
        inputDoneButton = contentView?.findViewById(R.id.input_done_button)
        inputDoneButton?.apply {
            maxWidth = context.getDimensionPixelSizeByScreenSize(
                R.dimen.videoeditor_caption_input_done_max_width,
                R.dimen.videoeditor_caption_input_done_middle_and_large_max_width
            )
            setOnClickListener(this@CaptionInputDialogFragment)
        }
        inputEditText = contentView?.findViewById(R.id.input_edit_text)
        topLayout = contentView?.findViewById(R.id.top_layout)
    }

    /**
     * 设置输入弹窗的高度
     */
    private fun setDialogHeight(targetHeight: Int, animate: Boolean = false) {
        // 如果息屏了，不使用动画
        val shouldAnimate = animate && isScreenOn
        val dialog = dialog ?: return
        dialog.setOnKeyListener(DialogInterface.OnKeyListener { _, i, keyEvent ->
            if ((i == KeyEvent.KEYCODE_BACK) && (keyEvent.action == KeyEvent.ACTION_UP)) {
                inputDoneButton?.performClick()
                return@OnKeyListener true
            }
            false
        })
        dialog.setCanceledOnTouchOutside(false)
        val window = dialog.window ?: return
        window.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
        )
        window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        window.setWindowAnimations(R.style.AnimationColorSupportDropDownUp)
        val layoutParams = window.attributes?.apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            dimAmount = 0f
            gravity = Gravity.BOTTOM
        } ?: return

        val currentHeight = layoutParams.height
        if (currentHeight == targetHeight) return
        // 取消之前的动画
        heightAnimator?.cancel()
        if (shouldAnimate) {
            heightAnimator = ValueAnimator.ofInt(currentHeight, targetHeight).apply {
                duration = ANIMATION_DURATION
                addUpdateListener { animator ->
                    layoutParams.height = animator.animatedValue as Int
                    window.attributes = layoutParams
                }
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        // 动画结束时清理引用
                        if (animation === heightAnimator) {
                            //移除监听器
                            heightAnimator?.removeAllListeners()
                            heightAnimator = null
                        }
                    }
                    override fun onAnimationCancel(animation: Animator) {
                        super.onAnimationCancel(animation)
                        // 动画取消时清理引用
                        if (animation === heightAnimator) {
                            //移除监听器
                            heightAnimator?.removeAllListeners()
                            heightAnimator = null
                        }
                        /**
                         * 避免动画取消时，高度没有及时更新
                         */
                        layoutParams.height = targetHeight
                        window.attributes = layoutParams
                    }
                })
                start()
            }
        } else {
            layoutParams.height = targetHeight
            window.attributes = layoutParams
        }
    }

    /**
     * 初始化文字效果选择tab及页面
     */
    private fun initTabLayoutAndViewPager() {
        val activity = activity as? EditorActivity ?: return
        val isInMultiWindow  = isInMultiOrFloatingWindowMode(activity)
        contentView?.findViewById<RelativeLayout>(R.id.caption_content_view)?.let {
            // 添加底部安全距离
            if (hasVirtualKey && isInMultiWindow.not()) {
                it.setPadding(
                    it.paddingLeft,
                    it.paddingTop,
                    it.paddingRight,
                    it.paddingBottom + bottomNaviBarHeight
                )
            }
        }
        if (hasVirtualKey) {
            bottomNaviBarHeight = 0
        }
        viewPager = contentView?.findViewById(R.id.caption_view_pager)

        captionTabLayout = contentView?.findViewById(R.id.caption_style_tab_layout)

        viewPager?.let { pager ->
            pager.isUserInputEnabled = false
            pagerAdapter = onOperationListener?.let {
                ViewPagerAdapter(pager.context, isInMultiWindow, bottomNaviBarHeight, pageTypes, lifecycleScope, it).apply {
                    caption = selectedCaption
                }
            }
            pager.adapter = pagerAdapter ?: return@let
            val tabLayout = captionTabLayout ?: return@let
            TabLayoutMediator(tabLayout, pager) { tab, position ->
                tab.text = resources.getString(pageTypes[position].resId)
            }.attach()
            captionTabLayout?.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    hideSoftKeyBoard()
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) = Unit

                override fun onTabReselected(tab: TabLayout.Tab?) = Unit
            })
        }
        setViewPagerVisibility(true)
        disableTabLongClick()
    }

    /**
     * 判断当前是否处于多窗口模式或多任务悬浮窗模式。
     * 用于适配 UI 行为，例如在特殊窗口模式下调整布局、输入法行为或交互方式。
     * @param activity 当前的 [EditorActivity] 实例，用于获取当前的 UI 配置
     * @return 如果当前处于多窗口模式或多任务悬浮窗模式，返回 `true`；否则返回 `false`
     */
    private fun isInMultiOrFloatingWindowMode(activity: EditorActivity): Boolean {
        val config = activity.getCurrentAppUiConfig()
        return config.isInMultiWindow.current || config.isInFloatingWindow.current
    }

    /**
     * 禁用tabLayout里面view的长按
     */
    private fun disableTabLongClick() {
        captionTabLayout?.let {
            for (i in 0 until it.tabCount) {
                val tabView = it.getTabAt(i)?.view ?: continue
                tabView.background = null
                tabView.isLongClickable = false
                tabView.setOnLongClickListener { true }
            }
        }
    }

    /**
     * 设置文字效果选择面板是否显示
     */
    private fun setViewPagerVisibility(visibility: Boolean) {
        viewPager?.let {
            if (visibility) {
                // 设置页面显示
                it.visibility = View.VISIBLE
            } else {
                // 设置页面隐藏
                it.visibility = View.GONE
                // 设置键盘弹起
                it.postDelayed({
                    var inputIsShow = false
                    activity?.let { ac ->
                        if (ac.isFinishing.not()) {
                            inputIsShow = (ac.window?.attributes?.softInputMode == WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
                            // 如果当前键盘是显示弹出状态，则返回
                            if (inputIsShow) return@postDelayed
                        }
                    }
                    inputEditText?.let { input ->
                        manager?.showSoftInput(input, InputMethodManager.SHOW_IMPLICIT)
                    }
                }, POPUP_SOFT_KEYBOARD_TIME_DELAY.toLong())
            }
        }
    }

    /**
     * 初始化输入框内容
     */
    private fun initContentData() {
        if (selectedCaption?.text != defaultText) {
            // 如果不是默认的引导文案，则将字幕内容显示在编辑框内
            setText(selectedCaption?.text)
            /***
             * 再次显示弹窗时输入框里面的数据超过1行
             * 需要延迟获取的inputEditText高度动态设置弹窗的高度
             */
            if (isSoftInputShowing.not()) {
                inputEditText?.post {
                    if (isSoftInputShowing.not()) {
                        setDialogHeight(resources.getDimensionPixelOffset(R.dimen.videoeditor_caption_layout_height) + getInputHeightDiff())
                    }
                }
            }
        }
    }

    private fun initEvent() {
        var softInputHeight = 0
        view?.let {
            insetsListener = OnApplyWindowInsetsListener { v, insets ->
                val imeInsets = insets.getInsets(WindowInsetsCompat.Type.ime())
                // 键盘是否可见（高度 > 0）
                val isVisible = imeInsets.bottom > 0
                if (isSoftInputShowing != isVisible) {
                    isSoftInputShowing = isVisible
                } else {
                    return@OnApplyWindowInsetsListener insets
                }
                if (isSoftInputShowing) {
                    softInputHeight = imeInsets.bottom
                    topLayout?.height?.let { topLayoutHeight ->
                        setDialogHeight((softInputHeight + topLayoutHeight), true)
                    }
                } else {
                    val heightDiff = resources.getDimensionPixelOffset(R.dimen.videoeditor_caption_layout_height) + getInputHeightDiff()
                    if (isAdded) setDialogHeight(heightDiff, true)
                }
                // 返回处理后的 Insets（通常直接返回 insets）
                insets
            }
            ViewCompat.setOnApplyWindowInsetsListener(it, insetsListener)
        }
        setEditTextListener()
        inputEditTextLayoutListener = OnGlobalLayoutListener {
            topLayout?.height?.let { topLayoutHeight ->
                // 顶部布局高度不为0时，记录初始顶部高度
                if ((topLayoutHeight != 0) && (this.lastTopLayoutHeight == 0)) {
                    this.lastTopLayoutHeight = topLayoutHeight
                    return@OnGlobalLayoutListener
                }
                // 获取输入框高度变化
                if (topLayoutHeight != lastTopLayoutHeight) {
                    this.lastTopLayoutHeight = topLayoutHeight
                    setDialogHeight(topLayoutHeight + softInputHeight, true)
                }
            }
        }
        inputEditText?.viewTreeObserver?.addOnGlobalLayoutListener(inputEditTextLayoutListener)
    }

    /**
     * 获取输入框当前高度与指定默认高度之间的差值
     */
    private fun getInputHeightDiff(): Int {
        val inputEditTextHeight = inputEditText?.height ?: 0
        val default = resources.getDimensionPixelOffset(R.dimen.videoeditor_caption_input_edit_text_height)
        return if (inputEditTextHeight > default) {
            inputEditTextHeight - default
        } else {
            0
        }
    }

    /**
     * 移出注册的事件
     */
    private fun removeEvent() {
        clearEditTextListener()
    }

    /**
     * 设置输入框内容监听器
     */
    private fun setEditTextListener() {
        if (editTextListener == null) {
            editTextListener = EditTextListener()
        }
        inputEditText?.addTextChangedListener(editTextListener)
    }

    /**
     * 移除输入框内容监听器
     */
    private fun clearEditTextListener() {
        editTextListener?.let {
            inputEditText?.removeTextChangedListener(it)
        }
        editTextListener = null
    }

    override fun onClick(v: View) {
        if (v.id == R.id.input_done_button) {
            if (ClickUtil.isDoubleClick()) {
                return
            }
            isUserClickDone = true
            onOperationListener?.onCaptionDialogStateChanged(CaptionDialogState.FINISHED, isCaptionAdded())
            dismiss()
        }
    }

    override fun dismiss() {
        super.dismiss()
        inputEditTextLayoutListener?.let {
            topLayout?.viewTreeObserver?.removeOnGlobalLayoutListener(it)
        }
        heightAnimator?.cancel()
        heightAnimator = null
        // 移除键盘监听器（避免潜在内存泄漏）
        view?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it, null)
        }
        insetsListener = null
        hideSoftKeyBoard()
        removeEvent()
    }

    override fun onDestroy() {
        unregisterScreenReceiver()
        // 用户未点击完成、非正常情况下销毁弹窗（如旋转屏幕、语言切换、意外关闭）
        if (isUserClickDone.not()) {
            onOperationListener?.onCaptionDialogStateChanged(CaptionDialogState.DISMISSED, isCaptionAdded())
        }
        pagerAdapter?.destroy()
        pagerAdapter = null
        super.onDestroy()
    }

    /**
     * 判断当前字幕是否属于“新增字幕”状态：
     * - 是默认字幕类型
     * - 输入内容非空
     * - 输入内容不是默认提示文案
     * @return true：新增字幕 ｜ false：非新增字幕
     */
    private fun isCaptionAdded(): Boolean {
        val caption = selectedCaption ?: return false
        val inputContent = inputEditText?.text.toString()
        return caption.isDefaultCaption.not() && inputContent.isNotEmpty() && (inputContent != defaultText)
    }

    /**
     * 设置文字到输入框
     */
    private fun setText(text: CharSequence?) {
        text?.let {
            inputEditText?.setText(text)
            inputEditText?.setSelection(text.length)
        }
    }

    /**
     * 显示键盘
     * @param delay 延迟显示键盘
     */
    private fun showSoftKeyBoard(delay: Boolean = false) {
        manager?.let {
            if (!it.isActive(inputEditText)) {
                inputEditText?.requestFocus()
            }
            inputEditText?.postDelayed({
                it.showSoftInput(inputEditText, InputMethodManager.SHOW_IMPLICIT)
            }, if (delay) POPUP_SOFT_KEYBOARD_TIME_DELAY.toLong() else 0)
        }
    }

    /**
     * 隐藏键盘
     */
    private fun hideSoftKeyBoard() {
        if (isShowing) {
            inputEditText?.clearFocus()
            inputEditText?.let { editText ->
                editText.clearFocus()
                manager?.let {
                    if (it.isActive(editText)) {
                        it.hideSoftInputFromWindow(editText.windowToken, 0)
                    }
                }
            }
        }
    }

    /**
     * 输入框内容变化监听器
     */
    internal inner class EditTextListener : TextWatcher {
        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            val inputContent = inputEditText?.text.toString()
            selectedCaption?.let {
                // 如果为空或等于默认文案，则使用默认提示文字
                 val actualText = when {
                    TextUtils.isEmpty(inputContent) -> defaultText
                    inputContent == defaultText -> defaultText
                    else -> inputContent
                }
                // 设置最终文本和状态
                it.text = actualText
                // 根据内容重新更新是否默认字幕属性
                it.isDefaultCaption = (actualText == defaultText)
                if (it.isDefaultCaption.not()) {
                    onOperationListener?.onDefaultCaptionTextChanged(it)
                }
            }
            // 更新预览
            onOperationListener?.onCaptionPropertyUpdated()
        }
    }

    companion object {
        private const val TAG = "CaptionInputDialogFragment"

        /**
         * 页面隐藏时键盘延迟弹起的时间
         */
        private const val POPUP_SOFT_KEYBOARD_TIME_DELAY = 400

        /**
         * 输入当输入内容为空时的占位字符串
         */
        private const val STR_NONE_RTN = " "

        /**
         * 底部虚拟按键阈值
         */
        private const val BOTTOM_KEYBOARD_THRESHOLD = 90

        /**
         * 动画时长
         */
        private const val ANIMATION_DURATION = 300L
    }
}

/**
 * 根据屏幕大小获取对应的dimen
 * @param smallDimen 小屏幕的dimen
 * @param largeDimen 中大屏幕的dimen
 */
private fun Context.getDimensionPixelSizeByScreenSize(
    smallDimen: Int,
    largeDimen: Int
): Int {
    val dimen = if (isMiddleAndLargeWindow(this)) largeDimen else smallDimen
    return resources.getDimensionPixelSize(dimen)
}
