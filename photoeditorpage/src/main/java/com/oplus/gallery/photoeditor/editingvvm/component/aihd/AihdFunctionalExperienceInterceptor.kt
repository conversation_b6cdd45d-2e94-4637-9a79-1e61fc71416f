/************************************************************
 * * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * *
 * * File: FunctionalExperienceIntercepter
 * * Description: FunctionalExperienceIntercepter
 * * Version: 1.0
 * * Date: 2024/10/9
 * * Author: ********
 * *
 * *---------------------Revision History:---------------------
 * *  <author>        <data>     <version>   <desc>
 * *  ********       2024/10/9       1.0     build this module
 ************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.aihd

import android.app.Activity
import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.authorizing.R as AuthorizingR
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConfirmInterceptor
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.USE_AI_HD_FUNCTIONAL_EXPERIENCE
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.notification.EditorConfirmDialogAction
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorR

/**
 * AI 超清 功能体验授权
 */
class AihdFunctionalExperienceInterceptor(
    private val context: Context,
    updateUI: (NotificationAction) -> Unit,
) : ConfirmInterceptor(updateUI) {

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): EditorConfirmDialogAction {
        return EditorConfirmDialogAction(
            titleString = context.getString(
                AuthorR.string.base_aihd_dialog_title_functional_experience_description,
                context.getString(R.string.picture3d_editor_text_aihd)
            ),
            messageString = context.getString(
                AuthorR.string.base_aihd_dialog_message_functional_experience_description,
                context.getString(R.string.picture3d_editor_text_aihd)
            ),
            positiveButtonTextResId = AuthorizingR.string.authorizing_option_agree_and_use,
            negativeButtonTextResId = AuthorizingR.string.authorizing_option_disagree,
            confirmCallback = confirmCallback
        )
    }

    override fun onCheckCondition(param: Bundle): Boolean {
        return ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN)
            .not() || ConfigAbilityWrapper.getBoolean(USE_AI_HD_FUNCTIONAL_EXPERIENCE, false)
    }

    override fun onAgreed(activity: Activity) {
        super.onAgreed(activity)
        context.getAppAbility<ISettingsAbility>()?.use {
            it.useAihdAuthorizeAccess(true)
        }
    }
}