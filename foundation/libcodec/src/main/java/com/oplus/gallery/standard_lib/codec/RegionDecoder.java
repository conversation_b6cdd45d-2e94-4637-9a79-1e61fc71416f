/*******************************************************
 * Copyright 2010 - 2012 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description    :
 * History      :
 * (ID, Date, Author, Description)
 *
 *******************************************************/
package com.oplus.gallery.standard_lib.codec;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapFactory.Options;
import android.graphics.Rect;
import android.text.TextUtils;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;

import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> modify for MPO file
 * <AUTHOR>
 * <p>
 * 2013-11-12
 */
public abstract class RegionDecoder {
    private static final String TAG = "RegionDecoder";
    private boolean mSupportMultiRegionDecode = true;

    public void setSupportMultiRegionDecode(boolean support) {
        mSupportMultiRegionDecode = support;
    }

    public boolean isSupportMultiRegionDecode() {
        return mSupportMultiRegionDecode;
    }

    public static RegionDecoder decodeFile(String pathName) throws IOException {
        if (pathName == null) {
            return null;
        }

        Options options = new Options();
        options.inJustDecodeBounds = true;
        try {
            BitmapFactory.decodeFile(pathName, options);
        } catch (Exception e) {
            GLog.e(TAG, "decodeFile", e);
        }

        /*
         * Cocoonshu: if UbiFocus read with byte[],
         * options.outMimeType will be equals with TYPE_JPEG,
         * we blocked here and give up to decode byte[]
         * UbiFocus has been discarded from 2016-03-01,
         * but these codes seems like ain't harm, and
         * TYPE_BMP ISNOT supported by RegionDecoder anyway.
         * Then we keep this code here.
         */
        if (isSupportRegionDecode(options.outMimeType)) {
            OriginalRegionDecoder decoder = OriginalRegionDecoder.newInstance(pathName);
            decoder.setSupportMultiRegionDecode(isSupportMultiRegionDecode(options.outMimeType));
            return decoder;
        } else {
            return null;
        }
    }

    public static RegionDecoder decodeByteArray(byte[] data, int offset, int length)
            throws IOException {
        Options options = new Options();
        options.inJustDecodeBounds = true;
        try {
            BitmapFactory.decodeByteArray(data, offset, length, options);
        } catch (Exception e) {
            GLog.e(TAG, "decodeByteArray", e);
        }

        /*
         * Cocoonshu: if UbiFocus read with byte[],
         * options.outMimeType will be equals with TYPE_JPEG,
         * we blocked here and give up to decode byte[]
         *
         * UbiFocus has been discarded from 2016-03-01,
         * but these codes seems like ain't harm, and
         * TYPE_BMP ISNOT supported by RegionDecoder anyway.
         * Then we keep this code here.
         */
        if (isSupportRegionDecode(options.outMimeType)) {
            OriginalRegionDecoder decoder = OriginalRegionDecoder.newInstance(data, offset, length);
            decoder.setSupportMultiRegionDecode(isSupportMultiRegionDecode(options.outMimeType));
            return decoder;
        } else {
            return null;
        }
    }

    public static RegionDecoder decodeFileDescriptor(FileDescriptor fd) throws IOException {
        Options options = new Options();
        options.inJustDecodeBounds = true;

        try {
            Rect rect = new Rect();
            BitmapFactory.decodeFileDescriptor(fd, rect, options);
        } catch (Exception e) {
            GLog.e(TAG, "decodeFileDescriptor", e);
        }

        /*
         * Cocoonshu: if UbiFocus read with FileDescriptor,
         * options.outMimeType will be equals with TYPE_JPEG,
         * we blocked here and give up to decode FileDescriptor
         *
         * UbiFocus has been discarded from 2016-03-01,
         * but these codes seems like ain't harm, and
         * TYPE_BMP ISNOT supported by RegionDecoder anyway.
         * Then we keep this code here.
         */
        if (isSupportRegionDecode(options.outMimeType)) {
            OriginalRegionDecoder decoder = OriginalRegionDecoder.newInstance(fd);
            decoder.setSupportMultiRegionDecode(isSupportMultiRegionDecode(options.outMimeType));
            return decoder;
        } else {
            return null;
        }
    }

    public static RegionDecoder decodeFileDescriptor(String path) throws IOException {
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(path);
            FileDescriptor fd = fileInputStream.getFD();
            if (fd != null) {
                return decodeFileDescriptor(fd);
            }
        } catch (Exception e) {
            GLog.e(TAG, "decodeFileDescriptor", e);
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
        }
        return null;
    }

    public Bitmap decodeRegion(Rect rect, Options options) {
        return decodeRegion(0, rect, options);
    }

    public static boolean isSupportRegionDecode(String mimeType) {
        if (TextUtils.isEmpty(mimeType)) {
            return false;
        }
        return MimeTypeUtils.isJpeg(mimeType)
                || MimeTypeUtils.isPng(mimeType)
                || MimeTypeUtils.isWebp(mimeType)
                || MimeTypeUtils.isHeifOrHeic(mimeType);
    }

    /**
     * 根据图片的mimeType判断此图片格式是否支持多区域解码
     * 部分格式（heif）在多区域解码时会导致mediaServer内存不能及时回收导致lowmemkiller
     * 此接口不带有是否支持区域解码的判断，需要在isSupportRegionDecode之后使用
     * @param mimeType
     * @return 多区域解码是普遍情况，mimeType传入空时返回支持多区域解码
     */
    public static boolean isSupportMultiRegionDecode(String mimeType) {
        if (TextUtils.isEmpty(mimeType)) {
            return true;
        }
        return !MimeTypeUtils.isHeifOrHeic(mimeType);
    }

    public abstract Bitmap decodeRegion(int index, Rect rect, Options options);

    public abstract int getWidth();

    public abstract int getHeight();

    public abstract void recycle();

    public abstract boolean isRecycled();
}
