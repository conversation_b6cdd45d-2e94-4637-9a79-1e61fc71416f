/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - DefaultDataPack.kt
 ** Description:默认样式等不显示照片的卡片样式
 ** Version: 1.0
 ** Date : 2021/11/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  zhen<PERSON><EMAIL>    2021/11/1        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item.packer

import android.content.Intent
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_NEED_LAUNCHER_ANIM
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.KEY_WIDGET_CODE
import com.oplus.gallery.basebiz.constants.IntentConstant.WidgetConstant.NEED_LAUNCHER_ANIM_FALSE
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.smartenginehelper.dsl.DSLCoder
import com.oplus.smartenginehelper.entity.StartActivityClickEntity

class DefaultDataPack(private val widgetCode: String, private val targetAction: String, private val imageResId: Int? = null) : BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        GLog.d(TAG, "onPack,widgetCode: $widgetCode")
        imageResId?.let {
            coder.setImageViewResource(VIEW_ID_IMAGE, it)
        }
        val startActivityClickEntity = StartActivityClickEntity()
        startActivityClickEntity.setParams(KEY_WIDGET_CODE, widgetCode)
        startActivityClickEntity.setPackageName(ContextGetter.context.packageName)
        startActivityClickEntity.setAction(targetAction)
        startActivityClickEntity.setCategory(Intent.CATEGORY_DEFAULT)
        startActivityClickEntity.setParams(KEY_NEED_LAUNCHER_ANIM, NEED_LAUNCHER_ANIM_FALSE)
        /*
         * 桌面默认启动activity会有个从点击处放大的动画，这个页面是个弹框，如果启动就弹会和弹框从下至上的动画同时播放，但如果
         * 等桌面动画播放完毕（大概500ms）再弹框， 给用户的感觉就会像点击响应慢，所以加这个禁用桌面的activity启动动画
         */
        startActivityClickEntity.addFlag(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        // 负一屏主界面没有activity，是个window，需要加FLAG_ACTIVITY_NEW_TASK
        startActivityClickEntity.addFlag(Intent.FLAG_ACTIVITY_NEW_TASK)
        coder.setOnClickStartActivity(VIEW_ID_MAIN_LAYOUT, startActivityClickEntity)
        return true
    }

    companion object {
        private const val VIEW_ID_IMAGE = "image"
        private const val VIEW_ID_MAIN_LAYOUT = "mainLayout"
    }
}