/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MeicamVideoCutRotate.java
 * * Description: MeicamVideoCutRotate
 * * Version: 1.0
 * * Date : 2020/03/07
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2020/03/07    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam;

import static com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio.VIDEO_ASPECT_RATIO_ORIGINAL_REVERT_TYPE;
import static com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio.calculateFitSize;

import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.app.AppConstants.Number;
import com.oplus.gallery.videoeditorpage.memories.engine.GalleryEditorVideoView;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoCutRotate;
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio;

public class MeicamVideoCutRotate implements IGalleryVideoCutRotate {

    private static final String TAG = "MeicamVideoCutRotate";
    private GalleryEditorVideoView mGalleryEditorVideoView;

    private int mTimelineWidth;
    private int mTimelineHeight;
    private MeicamTimeline mTimeline;
    private int mRotation = NvsVideoClip.ClIP_EXTRAVIDEOROTATION_0;
    private int mItemPosition = 0;

    private VideoRatio mRatio = new VideoRatio();

    @Override
    public boolean isCutRotateChanged() {
        if ((mRotation != NvsVideoClip.ClIP_EXTRAVIDEOROTATION_0) || (mItemPosition != 0)) {
            return true;
        }
        return false;
    }

    public void setTimeline(MeicamTimeline timeline, int width, int height) {
        mTimeline = timeline;
        mTimelineWidth = width;
        mTimelineHeight = height;
    }

    public void setGalleryVideoView(GalleryEditorVideoView galleryEditorVideoView) {
        mGalleryEditorVideoView = galleryEditorVideoView;
    }

    @Override
    public void intoCutRotateMode() {
        mGalleryEditorVideoView.intoEditCutMode();
    }

    @Override
    public void exitCutRotateMode() {
        mGalleryEditorVideoView.exitEditCutMode();
    }

    @Override
    public void cutVideo(boolean withAnim) {
        if (mGalleryEditorVideoView == null) {
            return;
        }
        VideoRatio ratio = mGalleryEditorVideoView.getCutViewRatio();
        float[] newSize = calculateFitSize(ratio, (float) mTimelineWidth, (float) mTimelineHeight);
        setPanAndScan(0f, 1f);
        mTimeline.changeVideoSize(newSize[0], newSize[1]);
        mGalleryEditorVideoView.updateVideoViewRatio(ratio, withAnim);
        resetFontSize();
    }

    private void setPanAndScan(float pan, float scan) {
        NvsVideoTrack videoTrack = mTimeline.getNvsTimeline().getVideoTrackByIndex(0);
        if (videoTrack == null) {
            GLog.e(TAG, "setPanAndScan videoTrack is null");
            return;
        }
        int size = videoTrack.getClipCount();
        for (int i = 0; i < size; i++) {
            NvsVideoClip clip = videoTrack.getClipByIndex(i);
            if (clip != null) {
                clip.setPanAndScan(pan, scan);
            }
        }
    }

    public void resetFontSize() {
        NvsTimelineCaption caption = mTimeline.getNvsTimeline().getFirstCaption();
        GLog.d(TAG, "resetFontSize");
        while (caption != null) {
            Integer size = MeicamVideoSubTitle.getCaptionFontSize(caption);
            if ((size != null) && (size > 0)) {
                caption.setFontSize(size);
            }
            caption = mTimeline.getNvsTimeline().getNextCaption(caption);
        }
    }

    public void setSelectRect(VideoRatio ratio) {
        updateRatio(ratio);
        if (mRatio.getAspectRatioType() == VIDEO_ASPECT_RATIO_ORIGINAL_REVERT_TYPE) {
            mRatio = new VideoRatio(mTimelineWidth, mTimelineHeight);
            if (mRotation % Number.NUMBER_2 == 1) {
                mRatio = mRatio.invert();
            }
        }
        mGalleryEditorVideoView.updateCutRect(mRatio, true);
    }

    public void updateRatio(VideoRatio ratio) {
        mRatio = ratio;
    }


    /**
     * 这个旋转对于view来说就是宽高比互换
     * 只有90度的旋转才触发view旋转
     * 180度的旋转不触发view旋转
     *
     * @param rotation
     */
    public void rotateVideo(int rotation) {
        boolean isRotate = (mRotation % 2 != rotation % 2);
        mRotation = rotation;
        if (isRotate) {
            mGalleryEditorVideoView.rotateVideoViewRatio(true);
        }
        resetFontSize();
    }

    @Override
    public void setSelectPosition(int position) {
        mItemPosition = position;
    }

    @Override
    public int getSelectPosition() {
        return mItemPosition;
    }

    @Override
    public int getRotation() {
        return mRotation;
    }

    @Override
    public int getRatioType() {
        return mRatio.getAspectRatioType();
    }

    @Override
    public VideoRatio getRatio() {
        return mRatio;
    }

    @Override
    public void recover(VideoRatio ratio, int rotation, boolean isCut, boolean withAnim) {
        mRotation = rotation;
        updateRatio(ratio);

        if (mRatio.getRatio() <= 0) {
            mRatio = new VideoRatio(mTimelineWidth, mTimelineHeight);
            if (rotation % Number.NUMBER_2 == 1) {
                mRatio = mRatio.invert();
            }
        }
        // 先针对角度调整视频宽高
        int timelineW = mTimelineWidth;
        int timelineH = mTimelineHeight;
        if (rotation % Number.NUMBER_2 == 1) {
            timelineH = mTimelineWidth;
            timelineW = mTimelineHeight;
        }

        if (!isCut) {
            setPanAndScan(0f, 0f);
            mTimeline.changeVideoSize(timelineW, timelineH);
            mGalleryEditorVideoView.updateVideoViewRatio(new VideoRatio(timelineW, timelineH), withAnim);
        } else {
            setPanAndScan(0f, 1f);
            float[] newVideoSize = calculateFitSize(mRatio, (float) timelineW, (float) timelineH);
            mTimeline.changeVideoSize(newVideoSize[0], newVideoSize[1]);
            mGalleryEditorVideoView.updateVideoViewRatio(mRatio, withAnim);
        }
        mGalleryEditorVideoView.updateCutRect(mRatio, withAnim);
        GLog.d(TAG, "mRatio = " + mRatio + " withAnim = " + withAnim);
        resetFontSize();
    }

}
