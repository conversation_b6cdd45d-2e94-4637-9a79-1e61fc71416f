/**************************************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -   BaseMusicEntity.kt
 ** Description: 抽象类BaseMusicEntity继承自Item类，用于表示音乐相关实体的基础属性和行为
 ** Version: 1.0
 ** Date :  2025/06/10
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 **  <EMAIL>  2025/06/10     1.0          用于表示音乐相关实体的基础属性和行为
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.entity

import androidx.room.ColumnInfo
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.oplus.gallery.standard_lib.codec.player.AVController
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item

/**
 * 抽象类BaseMusicEntity继承自Item类，用于表示音乐相关实体的基础属性和行为
 * 该类提供了音乐实体的通用结构，具体的行为和属性由子类实现
 */
abstract class BaseMusicItem : Item() {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    open var id: Int = 0

    @ColumnInfo(name = "zh_name")
    var zhName: String? = null

    @ColumnInfo(name = "ch_name")
    var chName: String? = null

    @ColumnInfo(name = "en_name")
    var enName: String? = null

    @ColumnInfo(name = "icon_url")
    var iconUrl: String? = null

    @ColumnInfo(name = "file_path")
    var filePath: String? = null

    @ColumnInfo(name = "song_type")
    var songType: Int = SONG_TYPE_SOLOOP

    @ColumnInfo(name = "version")
    var version: String? = null

    @ColumnInfo(name = "update_time")
    var updateTime: String? = null

    @ColumnInfo(name = "is_builtin")
    var builtin: Int = 0

    @ColumnInfo(name = "time_length")
    var timeSecondLength: Int = 0

    @ColumnInfo(name = "download_state")
    var downloadState: Int = 0

    @ColumnInfo(name = "song_id")
    open var songId: Int = 0

    @Ignore
    var playState: AVController.PlaybackState = AVController.PlaybackState.IDLE

    override fun isBuiltin(): Boolean {
        return builtin == BUILTIN
    }

    fun isDownloaded(): Boolean {
        val flag = (this.downloadState and TYPE_DOWNLOAD_FILE) == TYPE_DOWNLOAD_FILE
        return flag
    }

    override fun getItemUniqueId(): String = songId.toString()

    override fun getItemDownloadState(): Int = downloadState

    override fun getItemVersion(): String = version.toString()

    companion object {
        const val TAG = "BaseMusicItem"
        const val SONG_TYPE_SOLOOP: Int = 0
        const val SONG_TYPE_LOCAL_VIDEO: Int = 3
    }
}
