/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ThumbnailRespond
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.graphics.Bitmap;

public class ThumbnailRespond<Data> {
    private Data mItem;
    private Bitmap mResult;

    public ThumbnailRespond(Data item, Bitmap result) {
        mItem = item;
        mResult = result;
    }

    public Data getItem() {
        return mItem;
    }

    public Bitmap getResult() {
        return mResult;
    }
}
