/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.util;

import static android.media.MediaCodecInfo.CodecProfileLevel.HEVCProfileMain10HDR10;
import static android.media.MediaCodecInfo.CodecProfileLevel.HEVCProfileMain10HDR10Plus;
import static com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.DOLBY_VIDEO_TYPE;
import static com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.HDR10PLUS_VIDEO_TYPE;
import static com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.HDR10_VIDEO_TYPE;
import static com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.HLG_VIDEO_TYPE;
import static com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.SDR_VIDEO_TYPE;
import static com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever.METADATA_KEY_VIDEO_CODEC_MIME_TYPE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_HDR_VISION_BRIGHTEN;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_HLG_ENCODE;
import static com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio.VIDEO_ASPECT_RATIO_INVALID_TYPE;
import static com.oplus.gallery.standard_lib.util.chrono.TimeUtils.getFormatDateTime;
import static com.oplus.gallery.standard_lib.util.chrono.TimeUtils.getWatermarkTime;

import android.app.ActivityOptions;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences.Editor;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.icu.util.Calendar;
import android.media.ExifInterface;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Bundle;
import android.os.ParcelFileDescriptor;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.provider.MediaStore.Files.FileColumns;
import android.provider.Settings;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.webkit.MimeTypeMap;

import androidx.preference.PreferenceManager;

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils;
import com.oplus.gallery.business_lib.videoedit.IMetadataGetter;
import com.oplus.gallery.business_lib.videoedit.MetadataGetterFactory;
import com.oplus.gallery.business_lib.videoedit.MetadataKey;
import com.oplus.gallery.business_lib.videoedit.VideoSpecHelper;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.ConstantUtils;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode;
import com.oplus.gallery.foundation.fileaccess.helper.ContentValuesHelper;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper;
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.ext.MapExtKt;
import com.oplus.gallery.foundation.util.ext.UriExtKt;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;
import com.oplus.gallery.framework.abilities.config.IConfigAbility;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.videoeditorpage.R;

import java.io.Closeable;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
public final class VideoEditorHelper {

    public static final int MILLISECOND_IN_SECOND = TimeUtils.MILLISECOND_IN_SECOND;
    public static final int WAIT_NEW_VIDEO_RELOAD_TIME = MILLISECOND_IN_SECOND;  //1000ms
    public static final int SIZE_NUM = 1024;

    //for time
    public static final int MINUTES_60 = 60;
    public static final int SECONDS_1000 = 1000;
    public static final int SIX_MINUTES_3600 = 3600;

    //save
    public static final String DEFAULT_VIDEO_FILE_NAME_HEAD = "VID_";
    public static final String DEFAULT_VIDEO_SUFFIX = ".mp4";
    public static final String DEFAULT_VIDEO_SUFFIX_TMP = ".videoedit";

    public static final String KEY_MEMORIES_VIDEO_PREF = "key_memories_video_pref";
    public static final String KEY_MEMORIES_VIDEO_URI_PREF = "key_memories_video_uri_pref";

    public static final String KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF = "key_video_editor_temp_save_name_pref";
    public static final String KEY_VIDEO_EDITOR_TEMP_SAVE_DIR_PREF = "key_video_editor_temp_save_dir_pref";
    public static final String KEY_VIDEO_EDITOR_TEMP_SAVE_URI_PREF = "key_video_editor_temp_save_uri_pref";

    //type
    private static final String IMAGE_TYPE = "image";
    private static final String AUDIO_TYPE = "audio";
    private static final String VIDEO_TYPE = "video";

    private static final String PREF_FIRST_CLIP_WITH_SLOW_MODE = "pref_first_clip_with_slow_mode";
    private static final String PATH_FLAG_CONTENT = "content";
    private static final String TAG = "VideoUtils";
    // feature

    private static final String TALKBACK_COMPONENT_NAME =
            "com.google.android.marvin.talkback/com.google.android.marvin.talkback.TalkBackService";

    /**
     * 获取当前杜比视频是否支持导出HLG的实况
     */
    private static final String FEATURE_DOLBY_TO_HLG_SUPPORTED = "feature-oplus-dolby-vision-color-mode";

    private static final String PATH_FLAG_DOC = "com.android.providers.media.documents";
    private static final String PATH_FLAG_FILE = "file";

    private static final String FILE_DOT = ".";

    private static final int NUM_8 = 8;
    private static final int DEGREE_0 = 0;
    private static final int DEGREE_90 = 90;
    private static final int DEGREE_180 = 180;
    private static final int DEGREE_270 = 270;

    private static boolean sHasCheckFirstClip;

    /**
     * check cursor is valid
     *
     * @param cursor
     * @return boolean
     */
    public static boolean checkCursorValid(Cursor cursor) {
        return ((cursor != null) && (!cursor.isClosed())
                && (cursor.getCount() > 0) && (cursor.getColumnCount() > 0));
    }

    public static void setBooleanPref(Context context, String name, boolean value) {
        Editor ed = PreferenceManager.getDefaultSharedPreferences(context).edit();
        ed.putBoolean(name, value);
        ed.commit();
    }

    public static boolean getBooleanPref(Context context, String name, boolean def) {
        boolean mPref = false;
        try {
            mPref = PreferenceManager.getDefaultSharedPreferences(context).getBoolean(name, def);
        } catch (Exception e) {

        }
        return mPref;
    }

    public static void setLongPref(Context context, String key, long value) {
        Editor editor = PreferenceManager.getDefaultSharedPreferences(context).edit();
        editor.putLong(key, value);
        editor.commit();
    }

    public static long getLongPref(Context context, String key, long defValue) {
        long mPref = defValue;
        try {
            mPref = PreferenceManager.getDefaultSharedPreferences(context).getLong(key, defValue);
        } catch (Exception e) {

        }
        return mPref;
    }

    public static void setStringPref(Context context, String key, String value) {
        Editor editor = PreferenceManager.getDefaultSharedPreferences(context).edit();
        editor.putString(key, value);
        editor.commit();
    }

    public static String getStringPref(Context context, String key, String defValue) {
        String mPref = defValue;
        try {
            mPref = PreferenceManager.getDefaultSharedPreferences(context).getString(key, defValue);
        } catch (Exception e) {

        }
        return mPref;
    }

    public static String getPath(Context context, Uri uri) {
        final String column = "_data";
        Cursor cursor = null;
        if (PATH_FLAG_DOC.equals(uri.getAuthority())) {
            final String docId = DocumentsContract.getDocumentId(uri);
            final String[] split = docId.split(":");
            final String type = split[0];

            Uri contentUri = null;
            if (IMAGE_TYPE.equals(type)) {
                contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
            } else if (VIDEO_TYPE.equals(type)) {
                contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
            } else if (AUDIO_TYPE.equals(type)) {
                contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
            }
            String[] projection = {column};
            final String selection = "_id=?";
            final String[] selectionArgs = new String[]{
                    split[1]
            };
            try {
                if (contentUri != null) {
                    cursor = context.getContentResolver().query(contentUri, projection, selection, selectionArgs, null);
                    if ((cursor != null) && (cursor.getCount() > 0)) {
                        cursor.moveToFirst();
                        return cursor.getString(cursor.getColumnIndexOrThrow(column));
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, "getPath error:" + e);
            } finally {
                closeSilently(cursor);
            }
        } else if (PATH_FLAG_CONTENT.equalsIgnoreCase(uri.getScheme())) {
            String[] projection = {column};
            try {
                cursor = context.getContentResolver().query(uri, projection, null, null, null);
                if ((cursor != null) && (cursor.getCount() > 0)) {
                    cursor.moveToFirst();
                    return cursor.getString(cursor.getColumnIndexOrThrow(column));
                }
            } catch (Exception e) {
                GLog.e(TAG, "getPath error:" + e);
            } finally {
                closeSilently(cursor);
            }
        } else if (PATH_FLAG_FILE.equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }
        return null;
    }

    public static long getFileSize(File file) {
        if (file == null) {
            return 0;
        }
        long size = 0;
        FileInputStream fis = null;
        try {
            if (file.exists()) {
                fis = new FileInputStream(file.getFile());
                size = fis.available() / SIZE_NUM;
            }
        } catch (Exception e) {
            GLog.e(TAG, "getFileSize error:" + e);
        } finally {
            closeSilently(fis);
        }
        return size;
    }

    public static void closeSilently(Closeable c) {
        if (c == null) {
            return;
        }
        try {
            c.close();
        } catch (Exception e) {
            GLog.w(TAG, "closeSilently.Closeable fail to close", e);
        }
    }

    public static void closeSilently(Cursor cursor) {
        try {
            if ((cursor != null) && !cursor.isClosed()) {
                cursor.close();
            }
        } catch (Exception e) {
            GLog.w(TAG, "closeSilently.Cursor fail to close", e);
        }
    }

    /*
     * check whether is first time to clip video in slow video mode, only check once during process
     * always return false after first invoke
     */
    public static boolean checkFirstClipInSlowModeAndUpdate(Context context) {
        boolean result = false;
        if (!sHasCheckFirstClip) {
            result = getBooleanPref(context, PREF_FIRST_CLIP_WITH_SLOW_MODE, true);
            if (result) {
                setBooleanPref(context, PREF_FIRST_CLIP_WITH_SLOW_MODE, false);
            }
            sHasCheckFirstClip = true;
        }
        GLog.d(TAG, "checkFirstClipInSlowModeAndUpdate, result = " + result);
        return result;
    }

    public static boolean isTalkbackEnabled(ContentResolver resolver) {
        try {
            final boolean accessibilityEnabled = Settings.Secure.getInt(resolver, Settings.Secure.ACCESSIBILITY_ENABLED, 0) == 1;
            if (accessibilityEnabled) {
                final String accessibilityComponent = Settings.Secure.getString(resolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
                if (accessibilityComponent.contains(TALKBACK_COMPONENT_NAME)) {
                    return true;
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "isTalkbackEnabled, Exception: " + e);
        }
        return false;
    }

    public static void startShareVideoActivity(Context context, Uri fileUri) {
        if ((context == null) || (fileUri == null)) {
            GLog.e(TAG, "startShareVideoActivity, arg error");
            return;
        }
        try {
            Intent intent = new Intent(AppBrandConstants.Action.getACTION_SHARE_VIDEO());
            intent.setPackage(context.getPackageName());
            intent.setDataAndType(fileUri, MimeTypeUtils.MIME_TYPE_VIDEO_MP4);
            Bundle opts = ActivityOptions.makeCustomAnimation(context, 0, 0).toBundle();
            context.startActivity(intent, opts);
        } catch (Exception e) {
            GLog.e(TAG, "startShareVideoActivity, e:", e);
        }
    }

    /**
     * 将保存后的文件插入相册数据库，保证返回大图前能preload到过渡图
     *
     * @param file
     * @param videoFileName
     * @param dateTaken
     * @param uri
     * @return
     */
    public static void insertVideo(File file, String videoFileName, long dateTaken, Uri uri) {
        final String filePath = file.getAbsolutePath();
        final long dateModified = file.lastModified() / DateUtils.SECOND_IN_MILLIS;
        final int[] widthAndHeight = new int[2];
        int duration = 0;
        int orientation = 0;

        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        ContentValues values = new ContentValues();
        try {
            retriever.setDataSource(filePath);
            final String durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            final String widthStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            final String heightStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            final String orientationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_IMAGE_ROTATION);
            duration = TextUtils.isEmpty(durationStr) ? 0 : Integer.parseInt(durationStr);
            orientation = TextUtils.isEmpty(orientationStr) ? 0 : Integer.parseInt(orientationStr);
            String codecType = retriever.extractMetadata(METADATA_KEY_VIDEO_CODEC_MIME_TYPE);
            widthAndHeight[0] = TextUtils.isEmpty(widthStr) ? 0 : Integer.parseInt(widthStr);
            widthAndHeight[1] = TextUtils.isEmpty(heightStr) ? 0 : Integer.parseInt(heightStr);
            /**
             * retriever提取指定Key的Metadata值失败时会返回null；
             * VideoTypeUtils#isDolbyVideo方法中如果读取到值为null的codec type会提取Metadata，影响性能
             */
            if (codecType == null) {
                codecType = GalleryStore.GalleryMedia.CODEC_TYPE_EMPTY;
                GLog.d(TAG, "updateLocalMedia, empty string for null codecType");
            }

            values.put(GalleryStore.GalleryColumns.LocalColumns.TITLE, videoFileName.replace(DEFAULT_VIDEO_SUFFIX, ""));
            values.put(GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME, videoFileName);
            values.put(GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE, MimeTypeUtils.MIME_TYPE_VIDEO_MP4);
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN, dateTaken);
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED, dateModified);
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_ADDED, System.currentTimeMillis() / DateUtils.SECOND_IN_MILLIS);
            values.put(GalleryStore.GalleryColumns.LocalColumns.SIZE, file.length());
            values.put(GalleryStore.GalleryColumns.LocalColumns.RELATIVE_PATH, FilePathUtils.getRelativePath(filePath));
            if (widthAndHeight[0] != 0) {
                values.put(GalleryStore.GalleryColumns.LocalColumns.WIDTH, widthAndHeight[0]);
            }
            if (widthAndHeight[1] != 0) {
                values.put(GalleryStore.GalleryColumns.LocalColumns.HEIGHT, widthAndHeight[1]);
            }
            if (duration != 0) {
                values.put(GalleryStore.GalleryColumns.LocalColumns.DURATION, duration);
            }
            values.put(GalleryStore.GalleryColumns.LocalColumns.DURATION, duration);
            values.put(GalleryStore.GalleryColumns.LocalColumns.ORIENTATION, orientation);
            values.put(GalleryStore.GalleryColumns.LocalColumns.CODEC_TYPE, codecType);
            insertLocalVideo(uri, file, values);
        } catch (Exception e) {
            GLog.e(TAG, "insertVideo getVideoWidthAndHeight get info error.", e);
        } finally {
            try {
                retriever.release();
            } catch (Exception e) {
                GLog.e(TAG, "insertVideo getVideoWidthAndHeight, release exception", e);
            }
        }
    }

    private static void insertLocalVideo(Uri uri, final File file, final ContentValues values) {
        if ((uri != null) && (values != null)) {
            final long mediaID = ContentUris.parseId(uri);
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, file.getAbsolutePath());
            values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, mediaID);
            values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE, FileColumns.MEDIA_TYPE_VIDEO);
            values.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID, FilePathUtils.getBucketId(file));
            values.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME, FilePathUtils.getBucketName(file));
            values.put(GalleryStore.GalleryColumns.LocalColumns.VOLUME_NAME, FilePathUtils.getLocalVolumeName(file.getAbsolutePath()));
            new InsertReq.Builder()
                    .setDaoType(GalleryDbDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setConvert(aVoid -> values).build().exec();
        }
    }

    public static Uri insertVideoSandbox(Context context, File file, String videoFileName, long mSavingTimestamp) {
        long dateModify = mSavingTimestamp / MILLISECOND_IN_SECOND;
        String filePath = file.getAbsolutePath();

        ContentValues values = new ContentValues();
        values.put(MediaStore.Video.Media.TITLE, videoFileName.replace(DEFAULT_VIDEO_SUFFIX, ""));
        values.put(MediaStore.Video.Media.DISPLAY_NAME, videoFileName);
        values.put(MediaStore.Video.Media.MIME_TYPE, MimeTypeUtils.MIME_TYPE_VIDEO_MP4);
        values.put(MediaStore.Video.Media.DATE_TAKEN, mSavingTimestamp);
        values.put(MediaStore.Video.Media.DATE_MODIFIED, dateModify);
        values.put(MediaStore.Video.Media.DATE_ADDED, dateModify);
        values.put(MediaStore.Video.Media.RELATIVE_PATH, FilePathUtils.getRelativePath(filePath));
        values.put(MediaStore.Video.Media.IS_PENDING, ContentValuesHelper.IS_PENDING_TRUE);
        Uri contentUri = MediaStoreUriHelper.getInsertContentUri(filePath, false);

        if (null == contentUri) {
            return null;
        }

        Uri uri = context.getContentResolver().insert(contentUri, values);
        insertLocalMedia(uri, file, values);
        return uri;
    }

    public static String getMimeType(int videoCodeType) {
        return VideoSpecHelper.convertNvsCodecTypeToMimeType(videoCodeType);
    }

    /**
     * 判断指定的video clip type是否具有Hdr效果
     * @param videoClipType 需要进行hdr效果判断的video clip type
     * @return 指定的video clip type是否具有Hdr效果
     */
    public static boolean isHdrVideoClipType(int videoClipType) {
        return isSpecifyType(videoClipType, DOLBY_VIDEO_TYPE)
            || isSpecifyType(videoClipType, HLG_VIDEO_TYPE);
    }

    /**
     * 判断指定类型是否支持Hdr的编辑
     *
     * @param videoClipType 需要进行hdr效果判断的视频类型，见[VideoEditConstant.VideoType]
     * @return 此类型是否支持Hdr编辑
     */
    public static boolean isTypeSupportHdrEdit(int videoClipType) {
        return isTypeSupportDolbyEdit(videoClipType) || isTypeSupportHlgEdit(videoClipType);
    }

    /**
     * 判断指定类型是否支持Dolby的编辑
     *
     * @param videoClipType 需要进行Dolby效果判断的视频类型，见[VideoEditConstant.VideoType]
     * @return 此类型是否支持Dolby编辑
     */
    public static boolean isTypeSupportDolbyEdit(int videoClipType) {
        return isSpecifyType(videoClipType, DOLBY_VIDEO_TYPE) && isSupportDolbyEncode();
    }

    /**
     * 判断指定类型是否支持Hlg的编辑
     *
     * @param videoClipType 需要进行Hlg效果判断的视频类型，见[VideoEditConstant.VideoType]
     * @return 此类型是否支持Hlg编辑
     */
    public static boolean isTypeSupportHlgEdit(int videoClipType) {
        return isSpecifyType(videoClipType, HLG_VIDEO_TYPE) && isSupportHlgEncode();
    }

    public static boolean isSpecifyType(int videoClipType, int typeMask) {
        return (videoClipType & typeMask) == typeMask;
    }

    /**
     * 设备是否支持杜比的编码能力
     *
     * @return 支持返回 true, 不支持返回false
     */
    private static boolean isSupportDolbyEncode() {
        IConfigAbility configAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(IConfigAbility.class);
        boolean isDolbyEncode = false;
        if (configAbility != null) {
            if (Boolean.TRUE.equals(configAbility.getBooleanConfig(IS_SUPPORT_DOLBY_ENCODE, false))) {
                isDolbyEncode = true;
            }
            GLog.d(TAG, "isSupportDolbyEncode isDolbyEncode: " + isDolbyEncode);
            IOUtils.closeQuietly(configAbility);
        }
        return isDolbyEncode;
    }

    /**
     * 设备是否支持HLG的编码能力
     *
     * @return 支持返回 true, 不支持返回false
     */
    private static boolean isSupportHlgEncode() {
        IConfigAbility configAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(IConfigAbility.class);
        boolean isHlgEncode = false;
        if (configAbility != null) {
            if (Boolean.TRUE.equals(configAbility.getBooleanConfig(IS_SUPPORT_HLG_ENCODE, false))) {
                isHlgEncode = true;
            }
            GLog.d(TAG, "isSupportHlgEncode isHlgEncode: " + isHlgEncode);
            IOUtils.closeQuietly(configAbility);
        }
        return isHlgEncode;
    }

    /**
     * 获取视频的hdrType
     * @param uri 视频的uri
     * @return 返回当前视频的编辑类型
     */
    public static int getVideoEditHdrType(String uri) {
        if (uri == null) {
            return SDR_VIDEO_TYPE;
        }
        try (ParcelFileDescriptor fileDescriptor = UriExtKt.getFileDescriptorSafely(
                Uri.parse(uri), ContextGetter.context, OpenFileMode.MODE_READ.getMode(), TAG)) {
            if (fileDescriptor == null) {
                return SDR_VIDEO_TYPE;
            }

            final List<Integer> metadataKeys = Arrays.asList(MetadataKey.CODEC_TYPE, MetadataKey.PROFILE, MetadataKey.COLOR_TRANSFER);
            final IMetadataGetter metadataGetter = MetadataGetterFactory.create(MetadataGetterFactory.DEFAULT_GETTER);
            final Map<Integer, Object> metadata = metadataGetter.parse(metadataKeys, fileDescriptor.getFileDescriptor(), null);

            // 要判断杜比的视频，因为杜比的传输特性也是HLG的
            final String codecType = MapExtKt.getString(metadata, MetadataKey.CODEC_TYPE);
            if (VideoTypeUtils.isDolbyVideo(codecType)) {
                return DOLBY_VIDEO_TYPE;
            }

            final Integer colorTransfer = MapExtKt.getInt(metadata, MetadataKey.COLOR_TRANSFER);
            if (colorTransfer != null && VideoTypeUtils.isHlgVideo(colorTransfer)) {
                return HLG_VIDEO_TYPE;
            }

            Integer profile = MapExtKt.getInt(metadata, MetadataKey.PROFILE);
            if (profile != null) {
                if (profile == HEVCProfileMain10HDR10) {
                    return HDR10_VIDEO_TYPE;
                } else if (profile == HEVCProfileMain10HDR10Plus) {
                    return HDR10PLUS_VIDEO_TYPE;
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "[getVideoType] Error processing video metadata", e);
        }
        return SDR_VIDEO_TYPE;
    }

    /**
     * 当前设备以及视频是否支持导出uhdr图片
     * * @param videoHdrType 需要进行hdr效果判断的视频类型，见[NvsVideoStreamInfo]
     * @return Boolean 是否支持hdr
     */
    public static boolean isSupportExportUhdrPhoto(int videoHdrType) {
        return  ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ULTRA_HDR)
                && (videoHdrType != SDR_VIDEO_TYPE)
                && ((videoHdrType != HLG_VIDEO_TYPE) || isTypeSupportHlgEdit(videoHdrType));
    }

    /**
     * 判断当前条件是否支持转 hdr：1.视频格式和该格式在本机的编码能力支持HDR 2.设备支持臻彩视界提亮
     *
     * @param videoHdrType 需要进行hdr效果判断的视频类型，见[NvsVideoStreamInfo]
     * @return 此类型是否支持Hdr
     */
    public static boolean isDeviceAndVideoSupportHdrEdit(int videoHdrType) {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ULTRA_HDR)
                && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_HDR_VISION_BRIGHTEN)
                && isVideoSupportHdrEdit(videoHdrType);
    }

    /**
     * 判断视频资源是否支持HDR能力
     * hdr10以及hdr10+，判断类型是否匹配；HLG以及杜比要判断类型是否匹配以及是否支持编码
     * @param videoHdrType 视频的hdr类型
     * @return 支持返回 true, 不支持返回false
     */
    private static boolean isVideoSupportHdrEdit(int videoHdrType) {
        return isSpecifyType(videoHdrType, HDR10_VIDEO_TYPE)
                || isSpecifyType(videoHdrType, HDR10PLUS_VIDEO_TYPE)
                || isTypeSupportDolbyEdit(videoHdrType)
                || isTypeSupportHlgEdit(videoHdrType);
    }

    private static void insertLocalMedia(Uri uri, final File file, final ContentValues contentValues) {
        if ((uri != null) && (contentValues != null)) {
            final long mediaID = ContentUris.parseId(uri);
            new InsertReq.Builder()
                .setDaoType(GalleryDbDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setConvert(aVoid -> {
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.DATA, file.getAbsolutePath());
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, mediaID);
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE, FileColumns.MEDIA_TYPE_VIDEO);
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID, FilePathUtils.getBucketId(file));
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME, FilePathUtils.getBucketName(file));
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.VOLUME_NAME, FilePathUtils.getLocalVolumeName(file.getAbsolutePath()));
                    contentValues.put(GalleryStore.GalleryColumns.LocalColumns.IS_PENDING, ContentValuesHelper.IS_PENDING_TRUE);
                    return contentValues;
                }).build().exec();
        }
    }

    public static void updateLocalMedia(Context context, Uri uri, final File file) {
        updateLocalMedia(context, uri, file, -1L);
    }

    public static void updateLocalMedia(Context context, Uri uri, final File file, long dateTaken) {
        if (uri != null) {

            final long mediaID = ContentUris.parseId(uri);
            int[] widthAndHeight = new int[2];
            int duration = 0;
            int orientation = 0;
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            ContentValues contentValues = new ContentValues();

            try {
                retriever.setDataSource(context, uri);

                String widthStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
                String heightStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
                String durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                String orientationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_IMAGE_ROTATION);
                String codecType = retriever.extractMetadata(METADATA_KEY_VIDEO_CODEC_MIME_TYPE);
                /**
                 * retriever提取指定Key的Metadata值失败时会返回null；
                 * VideoTypeUtils#isDolbyVideo方法中如果读取到值为null的codec type会提取Metadata，影响性能
                 */
                if (codecType == null) {
                    codecType = GalleryStore.GalleryMedia.CODEC_TYPE_EMPTY;
                    GLog.d(TAG, "updateLocalMedia, empty string for null codecType");
                }

                widthAndHeight[0] = TextUtils.isEmpty(widthStr) ? 0 : Integer.parseInt(widthStr);
                widthAndHeight[1] = TextUtils.isEmpty(heightStr) ? 0 : Integer.parseInt(heightStr);
                duration = TextUtils.isEmpty(durationStr) ? 0 : Integer.parseInt(durationStr);
                orientation = TextUtils.isEmpty(orientationStr) ? 0 : Integer.parseInt(orientationStr);

                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.DATA, file.getAbsolutePath());
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.IS_PENDING, ContentValuesHelper.IS_PENDING_FALSE);
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.WIDTH, widthAndHeight[0]);
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.HEIGHT, widthAndHeight[1]);
                if (dateTaken > 0) {
                    contentValues.put(MediaStore.Video.Media.DATE_TAKEN, dateTaken);
                }
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.DURATION, duration);
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.ORIENTATION, orientation);
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.SIZE, file.length());
                contentValues.put(GalleryStore.GalleryColumns.LocalColumns.CODEC_TYPE, codecType);

            } catch (Exception e) {
                GLog.e(TAG, "updateLocalMedia, e: ", e);
            } finally {
                try {
                    retriever.release();
                } catch (Exception e) {
                    GLog.e(TAG, "updateLocalMedia, retriever release exception, e: ", e);
                }
            }

            new UpdateReq.Builder()
                    .setDaoType(GalleryDbDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setWhere(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + ConstantUtils.EQUAL_TO)
                    .setWhareArgs(new String[]{String.valueOf(mediaID)})
                    .setConvert(aVoid -> contentValues).build().exec();
        }
    }

    // The minimum video display time is 1 second
    public static long getVideoCurrentUnitTime(long time) {
        return (time < SECONDS_1000) ? SECONDS_1000 : time;
    }

    public static int getAspectRatioFromVideoSize(int w, int h) {
        if (h <= 0) {
            return VIDEO_ASPECT_RATIO_INVALID_TYPE;
        }
        return VideoRatio.getBestAspectRatioType(((float) w) / h);
    }

    public static String getDefaultVideoSaveName() {
        String dateTime = getFormatDateTime();
        return DEFAULT_VIDEO_FILE_NAME_HEAD + dateTime + DEFAULT_VIDEO_SUFFIX;
    }

    public static String getVideoSaveNameByMimeType(String mimeType) {
        if (TextUtils.isEmpty(mimeType)) {
            return getDefaultVideoSaveName();
        }
        String dateTime = getFormatDateTime();
        String videoSuffix = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType);
        if (TextUtils.isEmpty(videoSuffix)) {
            return getDefaultVideoSaveName();
        }
        return DEFAULT_VIDEO_FILE_NAME_HEAD + dateTime + FILE_DOT + videoSuffix;
    }

    public static String getDefaultVideoSaveTmpName() {
        String dateTime = getFormatDateTime();
        return DEFAULT_VIDEO_FILE_NAME_HEAD + dateTime + DEFAULT_VIDEO_SUFFIX_TMP;
    }

    private static long[] splitLongMilliTime(long milliSecond) {
        long duration = milliSecond / SECONDS_1000;
        long h = duration / SIX_MINUTES_3600;
        long m = duration % SIX_MINUTES_3600 / MINUTES_60;
        long s = duration % MINUTES_60;

        return new long[]{h, m, s};
    }

    /**
     * 返回视频编辑，添加水印中的时间
     *
     * @return
     */
    public static String getVideoFormatDateTime() {
        String rel = "";
        rel = getWatermarkTime(ContextGetter.context, Calendar.getInstance().getTimeInMillis());
        return rel;
    }

    public static String formatTimeWithMillis(Context context, long millis) {
        String timeStr = null;
        if (context == null) {
            return null;
        }
        long[] times = splitLongMilliTime(millis);
        long hh = times[0];
        long mm = times[1];
        long ss = times[2];
        if (millis == 0) {
            timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_ms), 0, 0);
        } else if (hh > 0) {
            timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_hms), hh, mm, ss);
        } else {
            timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_ms), mm, ss);
        }
        return timeStr;
    }

    public static String formatTimeWithMillisByDuration(Context context, long millis, long duration) {
        String timeStr = null;
        if (context == null) {
            return null;
        }
        long[] times = splitLongMilliTime(millis);
        long hh = times[0];
        long mm = times[1];
        long ss = times[2];
        if (duration < TimeUtils.TIME_1_HOUR_IN_MS) {
            if (millis == 0) {
                timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_ms), 0, 0);
            } else if (hh > 0) {
                timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_hms), hh, mm, ss);
            } else {
                timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_ms), mm, ss);
            }
        } else {
            if (millis == 0) {
                timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_hms), 0, 0, 0);
            } else if (hh > 0) {
                timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_hms), hh, mm, ss);
            } else {
                timeStr = String.format(context.getString(R.string.videoeditor_video_editor_details_hms), hh, mm, ss);
            }
        }
        return timeStr;
    }

    // This computes a sample size which makes the shorter side at least
    // minSideLength long. If that's not possible, return 1.
    public static int computeSampleSizeSmaller(int w, int h,
                                               int minSideLength) {
        int initialSize = Math.min(w / minSideLength, h / minSideLength);
        if (initialSize <= 1) {
            return 1;
        }

        return prevPowerOf2(initialSize);
    }

    // Returns the previous power of two.
    // Returns the input if it is already power of 2.
    // Throws IllegalArgumentException if the input is <= 0
    public static int prevPowerOf2(int n) {
        if (n <= 0) {
            throw new IllegalArgumentException();
        }
        return Integer.highestOneBit(n);
    }

    public static long getFileLastTime(MediaInfo info) {
        ExifInterface exif = null;
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            if (!TextUtils.isEmpty(info.mUri)) {
                parcelFileDescriptor = FileAccessManager.getInstance()
                        .openFile(ContextGetter.context, Uri.parse(info.mUri));
                if (parcelFileDescriptor == null) {
                    GLog.w(TAG, "getFileLastTime failed to open exif.");
                } else {
                    FileDescriptor fd = parcelFileDescriptor.getFileDescriptor();
                    if (fd != null) {
                        exif = new ExifInterface(fd);
                    }
                }
            } else {
                if (info.mPath != null) {
                    exif = new ExifInterface(info.mPath);
                }
            }
        } catch (FileNotFoundException e) {
            GLog.w(TAG, "getFileLastTime openFile error:", e);
            exif = null;
        } catch (IOException e) {
            GLog.w(TAG, "getFileLastTime ExifInterface error:", e);
            exif = null;
        } finally {
            IOUtils.closeQuietly(parcelFileDescriptor);
        }
        if (exif != null) {
            String dataTime = exif.getAttribute("DateTime");
            if (!TextUtils.isEmpty(dataTime)) {
                Date date = null;
                SimpleDateFormat format = new SimpleDateFormat("yyyy:MM:dd HH:mm:ss");
                try {
                    date = format.parse(dataTime);
                } catch (ParseException e) {
                    GLog.w(TAG, "getFileLastTime parse error:", e);
                }

                if (date == null) {
                    format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                        date = format.parse(dataTime);
                    } catch (ParseException e) {
                        GLog.w(TAG, "getFileLastTime parse error:", e);
                    }
                }
                if (date != null) {
                    return date.getTime();
                }
            }
        }
        if (info.mPath == null) {
            GLog.w(TAG, "getFileLastTime info.mPath is null.");
            return 0;
        }
        File file = new File(info.mPath);
        return file.lastModified();
    }


    public static int getImageDegree(MediaInfo info) {
        int degree = 0;
        ExifInterface exif = null;
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            parcelFileDescriptor = FileAccessManager.getInstance()
                    .openFile(ContextGetter.context, Uri.parse(info.mUri));
            if (parcelFileDescriptor != null) {
                exif = new ExifInterface(parcelFileDescriptor.getFileDescriptor());
            }
        } catch (FileNotFoundException e) {
            GLog.w(TAG, "getImageDegree openFile error:", e);
            exif = null;
        } catch (IOException e) {
            GLog.w(TAG, "getImageDegree ExifInterface error:", e);
            exif = null;
        } finally {
            IOUtils.closeQuietly(parcelFileDescriptor);
        }
        if (exif != null) {
            // get the degree of image file
            int ori = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED);
            // compute the rotation of file.
            switch (ori) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = DEGREE_90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = DEGREE_180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = DEGREE_270;
                    break;
                default:
                    degree = DEGREE_0;
                    break;
            }
        }

        return degree;
    }

    public static Bitmap getRotatedImage(Bitmap bitmap, int degrees) {
        if ((bitmap != null) && (degrees != 0)) {
            int w = bitmap.getWidth();
            int h = bitmap.getHeight();
            Matrix m = new Matrix();
            m.postRotate(degrees, (float) w / 2, (float) h / 2);
            Bitmap bm = Bitmap.createBitmap(bitmap, 0, 0, w, h, m, true);
            if (bm != bitmap) {
                bitmap.recycle();
            }
            bitmap = bm;
        }
        return bitmap;
    }


    public static String getDateString(long time) {
        Date date = new Date(time);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        return sd.format(date);
    }
}
