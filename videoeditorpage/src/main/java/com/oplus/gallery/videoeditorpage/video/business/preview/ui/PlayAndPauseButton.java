/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: PlayAndPauseButton
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/


package com.oplus.gallery.videoeditorpage.video.business.preview.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.accessibility.AccessibilityNodeInfo;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import com.oplus.gallery.videoeditorpage.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class PlayAndPauseButton extends AppCompatImageView {
    @IntDef({PlayAndPauseType.TYPE_PLAY, PlayAndPauseType.TYPE_PAUSE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PlayAndPauseType {
        int TYPE_PLAY = 0;
        int TYPE_PAUSE = 1;
    }

    @PlayAndPauseType
    private int mPlayAndPauseType;

    public PlayAndPauseButton(Context context) {
        this(context, null);
    }

    public PlayAndPauseButton(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PlayAndPauseButton(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setStopState();
    }

    @Override
    public void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo info) {
        super.onInitializeAccessibilityNodeInfo(info);
        int resId = mPlayAndPauseType == PlayAndPauseType.TYPE_PAUSE
                ? R.string.videoeditor_talkback_play
                : R.string.videoeditor_talkback_stop;
        setContentDescription(getResources().getString(resId));
    }

    public void setStopState() {
        setImageResource(R.drawable.editor_play_button_selector);
        mPlayAndPauseType = PlayAndPauseType.TYPE_PAUSE;
    }

    public void setPlayState() {
        setImageResource(R.drawable.editor_pause_button_selector);
        mPlayAndPauseType = PlayAndPauseType.TYPE_PLAY;
    }

    @PlayAndPauseType
    public int getPlayAndPauseType() {
        return mPlayAndPauseType;
    }
}
