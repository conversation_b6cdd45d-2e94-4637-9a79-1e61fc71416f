/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoClipEffectAdapter.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.google.gson.JsonSerializer
import com.google.gson.JsonDeserializer
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonParseException
import com.google.gson.JsonElement
import com.google.gson.JsonSerializationContext
import java.lang.reflect.Type

import kotlin.jvm.Throws

class VideoClipEffectAdapter() : JsonSerializer<BaseVideoClipEffect>, JsonDeserializer<BaseVideoClipEffect> {

    @Throws(JsonParseException::class)
    override fun deserialize(json: JsonElement, typeOfT: Type?, context: JsonDeserializationContext): BaseVideoClipEffect? {
        var targetClass: Class<*> = MeicamVideoClipEffect::class.java
        val jsonObject = json.asJsonObject
        if (jsonObject != null) {
            val classNameElement = jsonObject[JSON_NAME_TYPE]
            if (classNameElement != null) {
                val type = classNameElement.asString
                if (MeicamVideoClipEffect.JSON_TYPE_NAME == type) {
                    targetClass = MeicamVideoClipEffect::class.java
                }
            }
        }
        return context.deserialize(json, targetClass)
    }

    override fun serialize(src: BaseVideoClipEffect?, typeOfSrc: Type?, context: JsonSerializationContext): JsonElement? {
        return context.serialize(src)
    }

    companion object {
        private const val JSON_NAME_TYPE = "class_type"
    }
}