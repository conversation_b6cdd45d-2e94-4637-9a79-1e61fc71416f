/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetView.kt
 ** Description:在卡片刷新功能里承担MVVM的View层的逻辑，并不是真正的View
 ** Version: 1.0
 ** Date : 2021/12/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2021/12/1        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item

import android.app.Application
import androidx.annotation.MainThread
import androidx.lifecycle.Observer
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.widgetpage.ui.cardwidget.item.packer.DefaultDataPack
import com.oplus.gallery.widgetpage.ui.cardwidget.item.packer.ImageDataPack

class WidgetView(
    application: Application,
    private val widgetCode: String
) {
    companion object {
        private const val TAG = "WidgetView"
    }
    private var viewModel: WidgetViewModel = WidgetViewModel(application).apply {
        setWidgetCode(widgetCode)
    }

    /**
     * 卡片变为可见
     */
    @MainThread
    fun onResume() {
        GLog.d(TAG, "onResume: $widgetCode")
        // 在liveData已有数据的情况下调用observeForever会直接用当前值回调onChanged，所以需要先把liveData置空
        viewModel.resetLiveData()
        viewModel.liveData.observeForever(object : Observer<WidgetUpdateViewData?> {
            override fun onChanged(it: WidgetUpdateViewData?) {
                GLog.d(TAG, "onChanged: onResume: $widgetCode $it")
                it?.let {
                    postUpdateCommand(it)
                    viewModel.liveData.removeObserver(this)
                }
            }
        })
        viewModel.onResume()
    }

    /**
     * 卡片变为不可见
     */
    @MainThread
    fun onPause() {
        GLog.d(TAG, "onPause: $widgetCode")
        viewModel.onPause()
    }

    /**
     * 强制刷新
     */
    @MainThread
    fun forceUpdate() {
        GLog.d(TAG, "forceUpdate: $widgetCode")
        // 在liveData已有数据的情况下调用observeForever会直接用当前值回调onChanged，所以需要先把liveData置空
        viewModel.resetLiveData()
        viewModel.liveData.observeForever(object : Observer<WidgetUpdateViewData?> {
            override fun onChanged(it: WidgetUpdateViewData?) {
                GLog.d(TAG, "onChanged: forceUpdate: $widgetCode $it")
                it?.let {
                    postUpdateCommand(it)
                    viewModel.liveData.removeObserver(this)
                }
            }
        })
        viewModel.forceUpdate()
    }

    private fun postUpdateCommand(viewData: WidgetUpdateViewData) {
        val dataPack: BaseDataPack? = when (viewData.getViewType()) {
            ImageWidgetUpdateViewData::class.java.name -> {
                (viewData as ImageWidgetUpdateViewData).run {
                    ImageDataPack(
                        currentUri,
                        previousUri,
                        itemPath,
                        setPath,
                        extra
                    )
                }
            }
            DefaultWidgetUpdateViewData::class.java.name -> {
                (viewData as DefaultWidgetUpdateViewData).run {
                    DefaultDataPack(widgetCode, targetAction, defaultImageResId)
                }
            }
            else -> null
        }
        GLog.d(TAG, "postUpdateCommand: widgetCode = $widgetCode, dataPack = $dataPack")
        dataPack?.run {
            CardWidgetAction.postUpdateCommand(this, widgetCode, viewData.layoutName)
        } ?: run {
            GLog.w(TAG, "postUpdateCommand: unknown view type")
        }
    }
}