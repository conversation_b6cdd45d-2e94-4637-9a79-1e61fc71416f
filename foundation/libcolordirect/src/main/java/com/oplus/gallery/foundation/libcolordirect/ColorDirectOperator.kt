/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ColorDirectOperator.kt
 ** Description : 小布识屏操作类
 ** Version     : 1.0
 ** Date        : 2024/03/18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/03/18      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.libcolordirect

import android.app.Activity
import android.provider.Settings
import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils

/**
 * 小布识屏操作类
 *
 * 目前仅支持 禁用/恢复启用 小布识屏的服务
 * - 调用[disableColorDirectService], 禁用小布识屏服务，禁用之后，小布识屏不再拦截对应Activity页面的手势
 * - 调用[enableColorDirectService]，恢复启用小布识屏服务，恢复之后，小布识屏在下次手势操作的时候监听是否响应
 *
 * 接入文档：https://odocs.myoas.com/docs/2wAlXKbPKEuGbNAP?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
 */
object ColorDirectOperator {
    private const val TAG = "ColorDirectOperator"

    /**
     * 判断识屏服务是否支持控制识屏启动的meta data信息
     */
    private const val META_DATA_KEY_SUPPORT_SETTING_RULE_PREVENT = "isSupportSettingRulePrevent"

    /**
     * 小布识屏服务的包名
     */
    private const val COLOR_DIRECT_SERVICE_PKG = "com.coloros.colordirectservice"

    /**
     * 用于在Settings.System中设置哪个activity
     */
    private const val KEY_SETTING_PREVENT = "close_direct_touch_class_name"

    /**
     * 是否支持控制识屏启动
     */
    private val isSupportSettingRulePrevent: Boolean by lazy {
        (PackageInfoUtils.getMetaData<Boolean>(COLOR_DIRECT_SERVICE_PKG, META_DATA_KEY_SUPPORT_SETTING_RULE_PREVENT) ?: false).also {
            GLog.d(TAG) { "<isSupportSettingRulePrevent> support=$it" }
        }
    }

    /**
     * 恢复启用小布识屏服务，恢复之后，小布识屏在下次手势操作的时候监听是否响应
     *
     * @param activity 要恢复小布识屏服务的activity
     * @return 操作是否成功。如果小布识屏服务不支持控制，则会返回false
     */
    @JvmStatic
    @WorkerThread
    fun enableColorDirectService(activity: Activity): Boolean {
        if (isSupportSettingRulePrevent.not()) {
            GLog.w(TAG) { "<enableColorDirectService> isSupportSettingRulePrevent is false, we cannot recover service." }
            return false
        }
        return runCatching {
            Settings.System.putString(activity.contentResolver, KEY_SETTING_PREVENT, null)
        }.onFailure {
            GLog.e(TAG) { "<enableColorDirectService> something got wrong: err=${it.message}" }
        }.getOrDefault(false)
    }

    /**
     * 禁用小布识屏服务，禁用之后，小布识屏不再拦截对应Activity页面的手势
     *
     * @param activity 要禁用小布识屏服务的activity
     * @return 操作是否成功。如果小布识屏服务不支持控制，则会返回false
     */
    @JvmStatic
    @WorkerThread
    fun disableColorDirectService(activity: Activity): Boolean {
        if (isSupportSettingRulePrevent.not()) {
            GLog.w(TAG) { "<disableColorDirectService> isSupportSettingRulePrevent is false, we cannot disable service." }
            return false
        }
        val activityClassName = activity.componentName.className
        return runCatching {
            Settings.System.putString(activity.contentResolver, KEY_SETTING_PREVENT, activityClassName)
        }.onFailure {
            GLog.e(TAG) { "<disableColorDirectService> something got wrong: err=${it.message}" }
        }.getOrDefault(false)
    }
}
