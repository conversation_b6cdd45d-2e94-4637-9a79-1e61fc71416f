/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - SpiceHelper
 ** Description: XXX.
 ** Version: 1.0
 ** Date : 2025/04/21
 ** Author: 80320709
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>     <version >     <desc>
 **  80320709       2025/04/21     1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.joint

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.SparseArray
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectType
import com.oplus.gallery.business_lib.selectionpage.SelectionFrom
import com.oplus.gallery.business_lib.util.decodeRawUriFromNvmPath
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant.VideoClipType
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.transition.BaseTransition
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorTrackerHelper
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseState
import com.oplus.gallery.videoeditorpage.video.business.input.VideoParser
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import com.oplus.gallery.videoeditorpage.widget.ImportDialogListener
import com.oplus.gallery.videoeditorpage.widget.ImportVideoDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 拼接辅助类
 */
object JointHelper {

    private const val TAG = "JointHelper"

    /**
     * 一帧持续时长，单位为微秒（和时间线计算保持一致，取一秒为30帧）
     * 片头：导入时的位置在片段首帧范围内，则计为片头
     * 片尾：若导入时的位置在片尾尾帧范围内，则计为片尾
     * 片中：其他情况计为片中
     */

    private const val FPS_30: Int = 30

    /**
     * 图片默认时长(3ms)
     */
    private const val DEFAULT_IMAGE_DURATION = 3 * 1000 * 1000

    /**
     *  最大选择数量
     */
    private const val SELECTION_MAX_COUNT = 50

    /**
     * 跳转至相册选图时的请求Key
     */
    const val KEY_REQUEST_PICKER = "videoEditorPicker.requestKey"

    /**
     * 支持导入的片段列表
     */
    private val supportImportEntries: MutableList<JointEntry> = mutableListOf()

    /**
     * 导入视频片段弹窗
     */
    private var importDialog: ImportVideoDialog? = null

    private const val KEY_DEBUG_VIDEO_BRIGHTNESS_DELAY_TIME = "debug.delay.brightness.time"

    private const val BRIGHTNESS_DELAY_TIME = 500L

    const val BRIGHTNESS_DELAY_TOKEN = "video_brightness_delay_time"

    /**
     * 视频编辑2.0 导入素材提亮延迟时间
     */
    @JvmStatic
    val DEBUG_VIDEO_BRIGHTNESS_DELAY_TIME: Long by lazy {
        GallerySystemProperties.getLong(KEY_DEBUG_VIDEO_BRIGHTNESS_DELAY_TIME, BRIGHTNESS_DELAY_TIME)
    }

    /**
     * 导入视频片段
     * @param entriesList 导入的素材
     * @param currentPosition 导入时当前定位在时间线上的位置
     * @param editorEngine 编辑引擎
     * @param controlView 控制视图
     * @param trackCallback 拼接到轨道上的回调
     *
     * @return 导入是否成功
     */
    @MainThread
    @JvmStatic
    fun importVideoClips(
        editorActivity: EditorActivity,
        entriesList: List<JointEntry>,
        currentPosition: Long,
        editorEngine: EditorEngine,
        controlView: EditorControlView,
        trackCallback: (Int) -> Unit
    ): Boolean {
        val timeline: ITimeline = editorEngine.getCurrentTimeline()

        // 时间线的第一个轨道为视频轨道
        val videoTrack = timeline.getVideoTrack(0)
        if (videoTrack == null) {
            GLog.e(TAG, LogFlag.DL, "[importVideoClips] video track is null")
            return false
        }

        // 获取导入时位置的当前视频片段
        val currentVideoClip = videoTrack.getClipByTimelinePostion(currentPosition)
        if (currentVideoClip == null) {
            GLog.e(TAG, LogFlag.DL, "[importVideoClips] current video clip is null")
            return false
        }

        // 当前时间线视频计算取的是30帧每秒，这里保持一致
        val duration = TrackHelper.getFPSDuration(FPS_30)

        val isFirstFPS = currentPosition in currentVideoClip.inPoint..currentVideoClip.inPoint + duration
        val isLastFPS = currentPosition in currentVideoClip.outPoint - duration..currentVideoClip.outPoint

        GLog.d(TAG, LogFlag.DL, "[importVideoClips] isFirstFPS = $isFirstFPS, isLastFPS = $isLastFPS")

        val result = prepareToInsertVideoClips(entriesList, videoTrack, timeline, currentVideoClip, isFirstFPS, trackCallback)
        if (!result) {
            GLog.e(TAG, LogFlag.DL, "[importVideoClips] Prepare to insert video clips has failed")
            return false
        }
        /**
         * sdr转hdr会出现画面暗压
         * 先 seek(seek+时间线BitDepth 会触发美摄LiveWindow刷新) 触发视频提亮；再延迟 DEBUG_BRIGHTNESS_DELAY_TIME ms 提升屏幕亮度，兼容协调(视频压暗和屏幕背光提亮)提亮效果
         */
        Handler(Looper.getMainLooper()).postDelayed({
            editorActivity.updateBrightness()
        }, BRIGHTNESS_DELAY_TOKEN, DEBUG_VIDEO_BRIGHTNESS_DELAY_TIME)
        editorActivity.updateVideoSpecInfo()
        seekVideoClipsToTimeline(currentVideoClip, controlView, isFirstFPS, isLastFPS)
        editorEngine.notifyTimelineChanged(true)
        return true
    }

    /**
     * 准备插入视频片段到视频轨道上
     * @param entriesList 导入的视频数据
     * @param videoTrack 视频轨道
     * @param timeline 时间线
     * @param currentVideoClip 当前选中的视频片段
     * @param isFirstFPS 导入时是否在片段首帧
     * @param callback 导入到视频轨道的回调
     *
     * @return 导入是否成功
     */
    private fun prepareToInsertVideoClips(
        entriesList: List<JointEntry>,
        videoTrack: IVideoTrack,
        timeline: ITimeline,
        currentVideoClip: IVideoClip,
        isFirstFPS: Boolean,
        callback: (Int) -> Unit,
    ): Boolean {
        // 导入视频片段的数量
        val importSize = entriesList.size

        var clipIndex = videoTrack.getClipIndex(currentVideoClip)
        // 当前时间线不是首帧时从下一个位置插入
        if (!isFirstFPS) {
            clipIndex += 1
        }

        var transitionCount = videoTrack.transitionCount
        // 计算现有片段在导入片段后数组中的位置
        val transitionSparseArray = SparseArray<BaseTransition>()
        for (i in 0 until transitionCount) {
            val index = if (i < clipIndex) i else i + importSize
            transitionSparseArray.put(index, videoTrack.getTransition(i))
        }

        val result = insertVideoClipsToTrack(entriesList, timeline, videoTrack, currentVideoClip, clipIndex)
        if (!result) {
            GLog.e(TAG, LogFlag.DL, "[prepareToInsertVideoClips] Insert video clips to track has failed")
            return false
        }

        // 这里需要处理用户选择器UserSegmentSelector，为避免持有EditorActivity，这里通过callback回调由EditorActivity自己去处理
        callback(clipIndex)

        transitionCount = videoTrack.transitionCount
        for (i in 0 until transitionCount) {
            videoTrack.setTransition(i, transitionSparseArray[i])
        }

        return true
    }

    /**
     * 插入导入的视频片段到轨道
     * @param entriesList 导入所选择的视频素材
     * @param timeline 时间线
     * @param videoTrack 视频轨
     * @param currentVideoClip 当前片段
     * @param startClipIndex 导入的起始片段索引
     *
     * @return 导入是否成功
     */
    private fun insertVideoClipsToTrack(
        entriesList: List<JointEntry>,
        timeline: ITimeline,
        videoTrack: IVideoTrack,
        currentVideoClip: IVideoClip,
        startClipIndex: Int
    ): Boolean {
        GLog.d(TAG, LogFlag.DL, "[insertVideoClipsToTrack] startClipIndex = $startClipIndex")

        // startClipIndex == videoTrack.clipCount为插入末位
        if ((startClipIndex < 0) || (startClipIndex > videoTrack.clipCount)) {
            GLog.e(TAG, LogFlag.DL, "[insertVideoClipsToTrack] startClipIndex is out of range")
            return false
        }

        var clipIndex = startClipIndex

        val size = entriesList.size
        for (i in 0 until size) {
            val info = entriesList[i]

            val videoClip: IVideoClip? = if (info.videoClipType == VideoClipType.VIDEO_CLIP_TYPE_AV) {
                videoTrack.insertVideoClip(
                    clipIndex, info.path, info.filePath, info.videoClipType, info.needConvert, 0,
                    info.duration, info.width, info.height, info.systemFilePath
                )
            } else {
                val duration = if (info.duration > 0) info.duration else DEFAULT_IMAGE_DURATION.toLong()
                videoTrack.insertVideoClip(clipIndex, info.path, info.filePath, info.videoClipType, info.needConvert, 0,
                    duration, info.width, info.height, info.systemFilePath
                )
            }

            if (videoClip != null) {
                // 智能成片迭代30修改与自由剪辑统一，不充满画面
                videoClip.setFullPicture(false)
                if ((videoClip.videoType == VideoClipType.VIDEO_CLIP_TYPE_IMAGE)) {
                    videoClip.imageMotionAnimationEnabled = false
                }
                videoClip.setDefaultSourceBackground()
                videoClip.setIsOlivePhoto(info.isOlivePhoto)
            }

            changeCaptionPosition(timeline, currentVideoClip, videoClip, info.videoClipType)

            if (videoClip != null) {
                clipIndex += 1
            }
        }
        return true
    }

    /**
     * 导入视频片段后定位时间线位置
     * @param currentPosition 导入时当前选中的片段
     * @param controlView 控制视图
     * @param isFirstFPS 导入时是否在片段首帧
     * @param isLastFPS 导入时是否在片段尾帧
     */
    private fun seekVideoClipsToTimeline(
        currentPosition: IVideoClip,
        controlView: EditorControlView,
        isFirstFPS: Boolean,
        isLastFPS: Boolean
    ) {
        // 在时间线上，滚动位置和时间线时长相互转换会有精度损失，为确保准确定位，加入半帧的偏移量
        val offset = TrackHelper.getFPSDuration(FPS_30) / 2
        // 定位时间线位置
        if (isFirstFPS) {
            // 首帧添加片段时，定位在当前首帧
            controlView.seekTimeline(currentPosition.inPoint + offset, 0, true)
        } else if (isLastFPS) {
            // 尾帧添加片段时，定位在下个片段的首帧
            controlView.seekTimeline(currentPosition.outPoint + offset, 0, true)
        } else {
            // 片段中间添加时，定位在当前片段尾帧
            controlView.seekTimeline(currentPosition.outPoint - offset, 0, true)
        }
    }

    /**
     * 更新字幕位置
     * @param timeline 时间线
     * @param currentVideoClip 导入时的当前片段
     * @param newAddVideoClip 新添加的视频片段
     * @param mediaType 视频轨道的媒体类型
     */
    private fun changeCaptionPosition(
        timeline: ITimeline,
        currentVideoClip: IVideoClip,
        newAddVideoClip: IVideoClip?,
        mediaType: Int
    ) {
        if (newAddVideoClip == null) {
            GLog.e(TAG, LogFlag.DL, "[changeCaptionPosition] newAddVideoClip is null")
            return
        }

        val captions = timeline.captionList
        if (captions == null) {
            GLog.e(TAG, LogFlag.DL, "[changeCaptionPosition] captions is null")
            return
        }

        var changeStartIndex = -1
        val outTime = currentVideoClip.outPoint
        for (i in captions.indices) {
            if (captions[i].inTime >= outTime) {
                changeStartIndex = i
                break
            }
        }
        if (changeStartIndex != -1) {
            if (mediaType == VideoClipType.VIDEO_CLIP_TYPE_AV) {
                timeline.addCaptionInAndOutTime(changeStartIndex, newAddVideoClip.duration)
            } else {
                timeline.addCaptionInAndOutTime(
                    changeStartIndex,
                    DEFAULT_IMAGE_DURATION.toLong()
                )
            }
        }
    }
    //endregion

    /**
     * 内部跳至相册选择素材
     * @param context 上下文
     * @param state 当前所在业务
     */
    @JvmStatic
    fun gotoGallerySelection(context: Context, state: EditorBaseState<*>) {
        val editorActivity = context as? EditorActivity ?: run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] context is not EditorActivity. context = ${context.javaClass.name}")
            return
        }
        state.editorEngine ?: run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] editorEngine is null")
            return
        }
        state.editorEngine.currentTimeline ?: run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] currentTimeline is null")
            return
        }
        val videoClips = state.currentTimelineClips
        videoClips ?: kotlin.run {
            GLog.e(TAG, LogFlag.DL, "[gotoGallerySelection] videoClips is null")
            return
        }
        //判断已经在编辑中的视频素材是否超过上限，如果超出弹出提示信息
        if (videoClips.size >= SELECTION_MAX_COUNT) {
            ToastUtil.showShortToast(
                String.format(
                    ContextGetter.context.getString(R.string.videoeditor_import_tip), SELECTION_MAX_COUNT
                )
            )
            return
        }

        val selectedPaths = ArrayList<String>()
        videoClips.forEach({ videoClip ->
            videoClip.srcFilePath?.let {
                val rawFilePath = decodeRawUriFromNvmPath(it) ?: it
                if ((rawFilePath.isNotEmpty()) && (!selectedPaths.contains(rawFilePath))) {
                    selectedPaths.add(rawFilePath)
                }
            }
        })
        Starter.DialogFragmentStarter<PanelDialog>(
            editorActivity.supportFragmentManager,
            bundle = SelectInputData(
                selectMulti = true,
                selectType = SelectType.ALL,
                fromWhere = SelectionFrom.VIDEO_EDITOR,
                countAtMost = SELECTION_MAX_COUNT - selectedPaths.size,
                countAtMostHintResId = R.string.videoeditor_import_tip,
            ).createBundle().apply {
                putString(KEY_REQUEST_KEY, KEY_REQUEST_PICKER)
                putStringArrayList(IntentConstant.SelectionConstant.SELECTED_PATHS, selectedPaths)
            },
            postCard = PostCard(SELECTION_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(KEY_REQUEST_PICKER) { _, bundle ->
                onVideoImportResult(editorActivity, bundle, state)
            }
        }
    }

    private fun onVideoImportResult(editorActivity: EditorActivity, bundle: Bundle, state: EditorBaseState<*>) {
        if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
            GLog.d(TAG, LogFlag.DL, "[gotoGallerySelection] result code error: code = ${bundle.getInt(KEY_RESULT_CODE)}")
            return
        }
        val pathStrList = bundle.getStringArray(KEY_RESULT_DATA_LIST) ?: run {
            GLog.d(TAG, LogFlag.DL, "[gotoGallerySelection] result data error: data = ${bundle.getStringArray(KEY_RESULT_DATA_LIST)}")
            return
        }
        val job = editorActivity.lifecycleScope.launch(Dispatchers.IO) {
            checkImportData(editorActivity, pathStrList)
        }
        showImportDialog(editorActivity, state, job)
    }

    private fun showImportDialog(editorActivity: EditorActivity, state: EditorBaseState<*>, importTask: Job) {
        importDialog = ImportVideoDialog(editorActivity).apply {
            showIfNeed(false)
        }
        importDialog?.importDialogListener = object : ImportDialogListener {
            override fun onCancelClick() {
                //取消导入任务
                importTask.cancel()
                supportImportEntries.clear()
            }

            override fun onFinished() {
                supportImportEntries.let { list ->
                    val currentPosition = state.editorEngine.timelineCurrentPosition
                    importVideoClips(editorActivity, list, currentPosition, state.editorEngine, state.editorControlView) {
                        state.clipResponders?.forEach { responder ->
                            responder.update(state.editorEngine.currentTimeline)
                        }
                    }
                    state.operationSaveHelper.saveCurrentTimeline(OperationType.IMPORT_CLIP)
                    supportImportEntries.clear()
                }
            }
        }
    }

    /**
     * 检查导入的视频
     */
    @WorkerThread
    private fun checkImportData(editorActivity: EditorActivity, pathStrList: Array<String>) {
        val pathList: List<Path> = pathStrList.map { pathStr -> Path.fromString(pathStr) }
        val jointEntries: List<JointEntry> = LocalMediaDataHelper.getLocalMediaItems(pathList, false)
            .filterNotNull().map { mediaItem ->
                val jointEntry = JointEntry.fromMediaItem(mediaItem)
                VideoEditorTrackerHelper.buildTrackVideoData(mediaItem, jointEntry)
                jointEntry
            }
        val supportEntries = VideoParser.getInstance().parseByJointEntryList(jointEntries)
        editorActivity.lifecycleScope.launch(Dispatchers.UI) {
            if (supportEntries.isEmpty()) {
                // 导入的素材，全部不支持时
                ToastUtil.showLongToast(R.string.videoeditor_notif_not_supported)
                importDialog?.destroy()
            } else {
                importDialog?.unSupportCount = jointEntries.size - supportEntries.size
                supportImportEntries.clear()
                supportImportEntries.addAll(supportEntries)
                importDialog?.finishIfNeed()
            }
        }
    }
}