/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamStickerWatermark.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.watermark;

import androidx.annotation.NonNull;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoTimelineEffect;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsTimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.sticker.MeicamStickerEffect;

public class MeicamStickerWatermark extends BaseWaterMark {
    private static final String TAG = "MeicamStickerWatermark";
    public static final String JSON_TYPE_NAME = "meicamStickerWatermark";
    @SerializedName("class_type")
    private final String mClassType = JSON_TYPE_NAME;
    private NvsTimeline mNvsTimeline;
    private MeicamStickerEffect mStickerEffect;

    public MeicamStickerWatermark() {
        super(TAG);
    }

    @NonNull
    @Override
    public MeicamStickerWatermark clone() {
        Gson gson = EditorEngineGlobalContext.getInstance().getGson();
        MeicamStickerWatermark result = null;
        String jsonString = null;
        try {
            jsonString = gson.toJson(this);
            result = gson.fromJson(jsonString, getClass());
        } catch (Exception e) {
            GLog.e(TAG, "clone, json = " + jsonString + ", failed:", e);
        }
        if (result == null) {
            return new MeicamStickerWatermark();
        }
        return result;
    }

    @Override
    public BaseVideoTimelineEffect addSticker(String filePath, int displayWidth, int displayHeight, float opacity, int transitionX, int transitionY) {
        MeicamStickerEffect stickerEffect = new MeicamStickerEffect(filePath);
        stickerEffect.setSize(displayWidth, displayHeight);
        stickerEffect.setOpacity(opacity);
        stickerEffect.setTransition(transitionX, transitionY);
        stickerEffect.setInTime(WATERMARK_IN_POINT);
        stickerEffect.setRepeatType(StreamingConstant.Sticker.REPEAT_TYPE_LAST_FRAME);
        stickerEffect.setIsUseAllClipExceptTail(false);
        this.mStickerEffect = stickerEffect;
        return stickerEffect;
    }


    @Override
    public BaseCaption addCaption(@NonNull CaptionParams captionParam) {
        return null;
    }

    @Override
    public BaseVideoTimelineEffect getSticker() {
        return mStickerEffect;
    }

    @Override
    public BaseCaption getCaption() {
        return null;
    }
}
