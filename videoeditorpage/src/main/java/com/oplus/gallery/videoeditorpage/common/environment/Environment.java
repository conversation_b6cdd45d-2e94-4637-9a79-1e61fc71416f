/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Environment.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.environment;

import android.content.Context;
import android.os.StatFs;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.videoeditorpage.resource.data.File;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Locale;

public class Environment {
    private static final String TAG = "Environment";

    public static final String DCIM = android.os.Environment.DIRECTORY_DCIM;

    public static final int NONE = -1;
    public static final int INTERNAL = 1;
    public static final int EXTERNAL = 2;
    public static final int OTG = 3;

    private static StorageManager sStorageManager;
    private static String sInternalPath = null;
    private static String sExternalPath = null;
    private static String sMultiAppPath = null;
    private static boolean sFirstLoad = true;

    public static String getInternalPath() {
        if (sInternalPath == null) {
            sInternalPath = android.os.Environment.getExternalStorageDirectory().getAbsolutePath();
            GLog.d(TAG, "getInternalPath, sInternalPath = " + sInternalPath);
        }
        return sInternalPath;
    }

    public static String getExternalPath(Context context) {
        if (sFirstLoad) {
            sExternalPath = getVolumePath(context);
            GLog.d(TAG, "getExternalPath, sExternalPath = " + sExternalPath);

            if (!TextUtils.isEmpty(sExternalPath)) {
                sFirstLoad = false;
            } else if (isSingleVolume(context)) { //if has not external sdcard, save cache and
                // set mFirstLoad false
                GLog.d(TAG, "getExternalPath, singleVolume = true");
                sFirstLoad = false;
            }
        }
        return sExternalPath;
    }

    public static boolean isInternalMounted(Context context) {
        String internalPath = getInternalPath();
        GLog.d(TAG, "isInternalMounted, internalPath = " + internalPath);
        if (null == internalPath || internalPath.isEmpty()) {
            return false;
        }
        return isVolumeMounted(context, internalPath);
    }

    public static boolean isExternalMounted(Context context) {
        String externalPath = getExternalPath(context);
        GLog.d(TAG, "isExternalMounted, externalPath = " + externalPath);
        if (null == externalPath || externalPath.isEmpty()) {
            return false;
        }
        return isVolumeMounted(context, externalPath);
    }


    public static boolean isStorageMounted(Context context) {
        return isInternalMounted(context) || isExternalMounted(context);
    }

    public static int getInternalSdBucketId(Context context) {
        String internalSdDir = getInternalPath();
        return (internalSdDir == null) ? 0 : getBucketId(internalSdDir);
    }

    public static int getExternalSdBucketId(Context context) {
        String externalSdDir = getExternalPath(context);
        return (externalSdDir == null) ? 0 : getBucketId(externalSdDir);
    }

    public static boolean isMultiStorage(Context context) {
        boolean result = false;
        String internalPath = getInternalPath();
        String externalPath = getExternalPath(context);
        boolean isValidInternalPath = (internalPath != null) && !internalPath.isEmpty();
        boolean isValidExternalPath = (externalPath != null) && !externalPath.isEmpty();
        if (isValidInternalPath && isInternalMounted(context) && isValidExternalPath && isExternalMounted(context)) {
            if (!internalPath.equals(externalPath)) {
                result = true;
            }
        }
        return result;
    }

    public static boolean isInternalCardType(int type) {
        return type == INTERNAL;
    }

    public static boolean isExternalCardType(int type) {
        return type == EXTERNAL;
    }

    public static boolean isValibPath(Context context, int type) {
        if (type == OTG || type == NONE) {
            GLog.w(TAG, "isValibPath, OTG message or invalib massage!");
            return false;
        } else {
            return true;
        }
    }

    public static int judgeSourceType(Context context, String path) {
        int type = DataSourceType.TYPE_NOT_CATEGORIZED;
        String internalSdDir = getInternalPath();
        String externalSdDir = getExternalPath(context);
        if (path != null) {
            boolean startWithInternal = false;
            boolean startWithExternal = false;
            if (null != internalSdDir) {
                startWithInternal = path.startsWith(internalSdDir);
            }
            if (null != externalSdDir) {
                startWithExternal = path.startsWith(externalSdDir);
            }

            if (startWithInternal && startWithExternal) {
                if (internalSdDir.length() >= externalSdDir.length()) {
                    type = DataSourceType.TYPE_PHONE;
                } else {
                    type = DataSourceType.TYPE_SDCARD;
                }
            } else if (startWithInternal) {
                type = DataSourceType.TYPE_PHONE;
            } else if (startWithExternal) {
                type = DataSourceType.TYPE_SDCARD;
            }
        }
        return type;
    }

    public static int getVolumeCount(Context context) {
        sStorageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
        StorageVolume[] volumes = getVolumeList(sStorageManager);
        if (null != volumes) {
            // return volumes.length;
            boolean moreMounted = false;
            for (StorageVolume volume : volumes) {
                if (volume.isRemovable()) {
                    String path = getPath(volume);
                    if (null != path
                            && android.os.Environment.MEDIA_MOUNTED.equals(
                            getVolumeState(sStorageManager, path))) {
                        moreMounted = true;
                        break;
                    }
                }
            }
            if (moreMounted && ((volumes.length > 1))) {
                return AppConstants.Number.NUMBER_2;
            } else {
                return AppConstants.Number.NUMBER_1;
            }
        } else {
            return AppConstants.Number.NUMBER_1;
        }
    }

    public static StorageVolume[] getVolumeList(StorageManager storageManager) {
        try {
            Class clz = StorageManager.class;
            Method getVolumeList = clz.getMethod("getVolumeList");
            StorageVolume[] result = (StorageVolume[]) getVolumeList.invoke(storageManager);
            return result;
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getVolumeList, fail:" + e.getMessage());
        }
        return null;
    }

    public static String getPath(StorageVolume volume) {
        try {
            Class clz = StorageVolume.class;
            Method getPath = clz.getMethod("getPath");
            String result = (String) getPath.invoke(volume);
            return result;
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getPath, fail:" + e.getMessage());
        }
        return null;
    }


    public static String getVolumeState(StorageManager storageManager, String path) {
        String result = "";
        if (null == storageManager || TextUtils.isEmpty(path)) {
            return result;
        }
        try {
            Class clz = StorageManager.class;
            Method getVolumeList = clz.getMethod("getVolumeState", String.class);
            result = (String) getVolumeList.invoke(storageManager, path);
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getVolumeState, fail:" + e.getMessage());
        }
        return result;
    }


    public static String getVolumePath(Context context) {
        sStorageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
        StorageVolume[] volumes = getVolumeList(sStorageManager);
        String path = null;
        if (null != volumes) {
            for (StorageVolume volume : volumes) {
                if (volume.isRemovable()) {
                    path = getPath(volume);
                    if (null != path
                            && android.os.Environment.MEDIA_MOUNTED.equals(
                            getVolumeState(sStorageManager, path))) {
                        break;
                    }
                }
            }
        }
        return path;
    }


    public static ArrayList<File> getVolumeList(Context context) {
        sStorageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
        StorageVolume[] volumes = getVolumeList(sStorageManager);
        if (null != volumes && volumes.length > 0) {
            ArrayList<File> list = new ArrayList<>();
            for (StorageVolume volume : volumes) {
                String path = getPath(volume);
                GLog.v(TAG, "path = " + path);
                if (null != path
                        && android.os.Environment.MEDIA_MOUNTED.equals(getVolumeState(sStorageManager, path))) {
                    File file = new File(path);
                    list.add(file);
                }
            }
            return list;
        } else {
            return null;
        }
    }

    public static boolean isSingleVolume(Context context) {
        boolean flag = true;
        int volumeCount = getVolumeCount(context);
        // 2 means two volume
        if (volumeCount < 2) {
            flag = true;
        } else {
            flag = false;
        }
        return flag;
    }

    public static boolean isVolumeMounted(Context context, String path) {
        StorageManager sm = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
        if ((path != null) && (sm != null)) {
            return (android.os.Environment.MEDIA_MOUNTED).equals(getVolumeState(sm, path));
        }
        return false;
    }

    public static String getExternalSdState(Context context) {
        String path = getExternalPath(context);
        if (null == path) {
            return android.os.Environment.MEDIA_UNKNOWN;
        }
        StorageManager sm = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
        if (null != sm) {
            return getVolumeState(sm, path);
        } else {
            return android.os.Environment.MEDIA_UNKNOWN;
        }
    }

    /**
     * FIXME: others
     */
    /**
     * @param context
     * @param sizeMb
     * @return boolean
     */
    public static boolean isAvaiableSpaceForIntenal(Context context, long sizeMb) {
        if (Environment.isInternalMounted(context)) {
            String sdcard = Environment.getInternalPath();
            StatFs statFs = new StatFs(sdcard);
            long blockSize = statFs.getBlockSize();
            long blocks = statFs.getAvailableBlocks();
            long availableSpare = (blocks * blockSize) / (1024 * 1024);
            GLog.d(TAG, "isAvaiableSpaceForIntenal, availableSpare=" + availableSpare
                    + ", sizeMb is " + sizeMb);
            if (sizeMb <= availableSpare) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param context
     * @param sizeMb
     * @return boolean
     */
    public static boolean isAvaiableSpaceForExternal(Context context, long sizeMb) {
        if (Environment.isExternalMounted(context)) {
            String sdcard = Environment.getExternalPath(context);
            StatFs statFs = new StatFs(sdcard);
            long blockSize = statFs.getBlockSize();
            long blocks = statFs.getAvailableBlocks();
            long availableSpare = (blocks * blockSize) / (1024 * 1024);
            GLog.d(TAG, "isAvaiableSpaceForExternal, availableSpare=" + availableSpare
                    + ", sizeMb is " + sizeMb);
            if (sizeMb <= availableSpare) {
                return true;
            }
        }
        return false;
    }


    public static int getBucketId(String path) {
        return path.toLowerCase(Locale.US).hashCode();
    }
}
