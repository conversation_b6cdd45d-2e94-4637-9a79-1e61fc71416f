/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - GalleryEditorVideoView.java
 * * Description: XXXXXXXXXXXXXXX
 * * Version: 1.0
 * * Date : 2017/11/10
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/10    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine;

import static com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio.calculateFitSize;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.SurfaceView;
import android.view.View;
import android.widget.FrameLayout;

import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio;
import com.oplus.gallery.standard_lib.app.AppConstants.Number;
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoView;
import com.oplus.gallery.videoeditorpage.memories.ui.VideoSubTitleEditTextView;

public class GalleryEditorVideoView extends FrameLayout {

    private MeicamVideoView mLiveWindow;
    private GalleryVideoEditCutView mGalleryVideoEditCutView;
    private VideoSubTitleEditTextView mVideoSubTitleEditTextView;

    private float mVideoViewWidth = 0f;
    private float mVideoViewHeight = 0f;

    private VideoRatio mVideoViewRatio = new VideoRatio();

    private VideoRatio mCutViewRatio = new VideoRatio();
    private boolean mSyncVideoViewSize = true;
    private int mPaddingLeft;
    private int mPaddingTop;
    private int mPaddingRight;
    private int mPaddingBottom;
    private boolean mPaddingIsChanged = false;

    public GalleryEditorVideoView(Context context) {
        super(context);
    }

    public GalleryEditorVideoView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public GalleryEditorVideoView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 是否在onLayout时同步更新VideoView的大小
     * 默认为true
     * 需要在添加liveWindow前调用
     * 否则会覆盖原本liveWindow的布局参数
     *
     * @param sync
     */
    public void setSyncVideoViewSize(boolean sync) {
        mSyncVideoViewSize = sync;
    }

    public void addLiveWindow(MeicamVideoView view) {
        mLiveWindow = view;
        if (mSyncVideoViewSize) {
            updateViewParamsToSize(mLiveWindow, mVideoViewWidth, mVideoViewHeight);
        }
        addView(view);

        // 保持字幕框在顶
        if (mVideoSubTitleEditTextView != null) {
            mVideoSubTitleEditTextView.bringToFront();
        }
    }

    /**
     * 移除mLiveWindow，避免ViewPager复用异常
     */
    public void removeLiveWindow() {
        if (mLiveWindow != null) {
            removeView(mLiveWindow);
        }
    }

    /**
     * 获取视频预览窗口 LiveWindow
     *
     * @return 视频预览窗口View
     */
    public SurfaceView getSurfaceView() {
        return mLiveWindow;
    }

    public void setGalleryVideoSubTitleEditTextView(VideoSubTitleEditTextView videoSubTitleEditTextView) {
        mVideoSubTitleEditTextView = videoSubTitleEditTextView;
        if (mSyncVideoViewSize) {
            updateViewParamsToSize(mVideoSubTitleEditTextView, mVideoViewWidth, mVideoViewHeight);
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        if (changed || mPaddingIsChanged) {
            mPaddingIsChanged = false;
            // 每次layout发生布局改变,都会触发预览等页面的尺寸更新
            updateVideoViewRatio(mVideoViewRatio, false);
        }
    }

    @Override
    public void setPadding(int left, int top, int right, int bottom) {
        boolean paddingIsChange = (mPaddingLeft != left)
            || (mPaddingTop != top)
            || (mPaddingRight != right)
            || (mPaddingBottom != bottom);
        if (paddingIsChange) {
            /**
             *  setPadding与layout的动作并不是一对一的串行执行
             *  故需要在padding变化时才能修改mPaddingIsChanged标记
             *  以保证当padding变化时至少layout一次
             */
            mPaddingLeft = left;
            mPaddingTop = top;
            mPaddingRight = right;
            mPaddingBottom = bottom;
            mPaddingIsChanged = true;
        }
        super.setPadding(left, top, right, bottom);
    }

    @Override
    public void setBackground(Drawable background) {
        /**
         * 此处设置view背景会导致多任务有bug，去掉自检无影响
         */
    }

    public void updateVideoViewRatio(VideoRatio ratio) {
        updateVideoViewRatio(ratio, false);
    }

    /**
     * 更新视频预览view的比例
     * 此方法会同时更新内部视频预览控件的大小,字幕控件的大小,裁剪控件的大小
     * 受SyncVideoViewSize开关控制,不同步时方法不会执行
     *
     * @param ratio
     * @param withAnim 如果有裁剪控件,裁剪控件的变化是否带动画
     */
    public void updateVideoViewRatio(VideoRatio ratio, boolean withAnim) {
        if (!mSyncVideoViewSize) {
            return;
        }
        mVideoViewRatio = ratio;
        updateVideoViewSize();
        updateViewParamsToSize(mLiveWindow, mVideoViewWidth, mVideoViewHeight);
        updateViewParamsToSize(mVideoSubTitleEditTextView, mVideoViewWidth, mVideoViewHeight);
        updateCutRect(mCutViewRatio, withAnim);
    }

    /**
     * 单次旋转90°视频预览窗口的比例
     * 旋转会改变预览界面等的长宽比
     * 包括视频预览界面,字幕控件,裁剪控件
     * 视频预览画面具体的内容旋转需要外部控制预览画面旋转
     *
     * @param withAnim 如果有裁剪控件,裁剪控件的变化是否带动画
     */
    public void rotateVideoViewRatio(boolean withAnim) {
        mCutViewRatio = mCutViewRatio.invert();
        updateVideoViewRatio(mVideoViewRatio.invert(), withAnim);
    }

    /**
     * 通过提供裁剪区域的比例
     * 自动计算当前的裁剪区域在videoView预览区域的位置
     * 并更新到裁剪View上
     *
     * @param ratio
     * @param withAnim
     */
    public void updateCutRect(VideoRatio ratio, boolean withAnim) {
        if (mGalleryVideoEditCutView == null) {
            return;
        }
        mCutViewRatio = ratio;
        float[] newSize = calculateFitSize(ratio, mVideoViewWidth, mVideoViewHeight);
        int preViewWidth = getWidth() - getPaddingLeft() - getPaddingRight();
        int preViewHeight = getHeight() - getPaddingTop() - getPaddingBottom();
        int l = (preViewWidth - (int) newSize[0]) / Number.NUMBER_2;
        int r = l + (int) newSize[0];
        int t = (preViewHeight - (int) newSize[1]) / Number.NUMBER_2;
        int b = t + (int) newSize[1];
        if (withAnim) {
            mGalleryVideoEditCutView.startAnimationSetRect(new Rect(l, t, r, b));
        } else {
            mGalleryVideoEditCutView.setSelectRect(l, t, r, b);
        }
    }

    /**
     * 调整view的布局参数到指定尺寸
     * 必须是RelativeLayout.LayoutParams
     *
     * @param view
     * @param targetWidth
     * @param targetHeight
     */
    private void updateViewParamsToSize(View view, float targetWidth, float targetHeight) {
        if ((view == null) || (targetWidth <= 0) || (targetHeight <= 0)) {
            //targetWidth == 0 会导致ViewPage内 子布局onLayout不执行
            return;
        }
        LayoutParams layoutParams = (LayoutParams) view.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new FrameLayout.LayoutParams(0, 0);
        }
        layoutParams.width = (int) targetWidth;
        layoutParams.height = (int) targetHeight;
        layoutParams.gravity = Gravity.CENTER;
        view.setLayoutParams(layoutParams);
    }

    private void updateVideoViewSize() {
        int videoViewWidth = getWidth() - getPaddingLeft() - getPaddingRight();
        int videoViewHeight = getHeight() - getPaddingTop() - getPaddingBottom();
        float[] videoViewSize = calculateFitSize(mVideoViewRatio, (float) videoViewWidth, (float) videoViewHeight);
        mVideoViewWidth = videoViewSize[0];
        mVideoViewHeight = videoViewSize[1];
    }

    /**
     * 第一次进入裁剪模式
     * 会创建EditCutView添加到布局
     * EditCutView会填充layout
     */
    public void intoEditCutMode() {
        if (mGalleryVideoEditCutView == null) {
            mGalleryVideoEditCutView = createEditCutView();
            addView(mGalleryVideoEditCutView);

            mCutViewRatio = mVideoViewRatio;
            updateCutRect(mCutViewRatio, false);
        } else {
            mGalleryVideoEditCutView.setVisibility(View.VISIBLE);
        }
    }

    private GalleryVideoEditCutView createEditCutView() {
        GalleryVideoEditCutView galleryVideoEditCutView = new GalleryVideoEditCutView(getContext());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        layoutParams.gravity = Gravity.CENTER;
        galleryVideoEditCutView.setLayoutParams(layoutParams);
        return galleryVideoEditCutView;
    }

    public void exitEditCutMode() {
        if (mGalleryVideoEditCutView != null) {
            mGalleryVideoEditCutView.setVisibility(GONE);
        }
    }

    public void setSelectRectWithAnimation(int l, int t, int r, int b) {
        mGalleryVideoEditCutView.startAnimationSetRect(new Rect(l, t, r, b));
    }

    public void setFillMode(int mode) {
        if (mLiveWindow != null) {
            mLiveWindow.setFillMode(mode);
        }
    }

    public VideoRatio getCutViewRatio() {
        return mCutViewRatio;
    }

    public Rect getCutSelectRect() {
        if (mGalleryVideoEditCutView != null) {
            return mGalleryVideoEditCutView.getSelectRect();
        } else {
            return null;
        }
    }

}
