/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorVideoBrightenManager.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.video.ui.brighten

import android.view.SurfaceView
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView

/**
 * 编辑视频提亮 管理类
 */
internal class EditorVideoBrightenManager(
    private val surfaceView: SurfaceView,
    private var videoType: Int
) {

    private var editorVideoBrighten: EditorVideoBrighten? = null

    init {
        initVideoBrighten()
    }

    /**
     * 初始化 视频提亮
     */
    private fun initVideoBrighten() {
        EditorVideoBrighten(surfaceView, videoType).apply {
            editorVideoBrighten = this
            brightnessVideo()
        }
    }

    /**
     * 关闭提亮
     */
    fun close() {
        editorVideoBrighten?.apply {
            clearBrightness()
            close()
        }
        editorVideoBrighten = null
    }

    /**
     * 更新提亮
     */
    fun updateBrighten(newVideoType: Int) {
        this.videoType = newVideoType
        editorVideoBrighten?.updateBrightness(newVideoType)
    }

    /**
     * 处理播放器状态
     * @param status 播放器状态
     */
    fun handlerPlayerStatus(status: Int) {
        editorVideoBrighten?.apply {
            when (status) {
                EditorPreviewView.EDITOR_PREVIEW_STATUS_READY, EditorPreviewView.EDITOR_PREVIEW_STATUS_PLAY -> {
                    //开始播放
                    switchOffScreenBurn("play_start", true)
                }

                EditorPreviewView.EDITOR_PREVIEW_STATUS_STOP -> {
                    //暂停播放
                    switchOnScreenBurn("play_pause")
                }

                EditorPreviewView.EDITOR_PREVIEW_STATUS_FINISH -> {
                    //结束播放
                    switchOnScreenBurn("play_finish")
                }
            }
        }
    }

    /**
     * 处理 用户交互
     * @param type 交互类型
     */
    fun handlerUserInteraction(type: BaseActivity.InteractionType) {
        editorVideoBrighten?.apply {
            if (type == BaseActivity.InteractionType.TOUCH) {
                // 用户任何触屏操作都会触发，但仅在屏幕因防烧屏而降亮度后才会实际执行提亮操作
                highlightScreen()
            }
        }
    }
}