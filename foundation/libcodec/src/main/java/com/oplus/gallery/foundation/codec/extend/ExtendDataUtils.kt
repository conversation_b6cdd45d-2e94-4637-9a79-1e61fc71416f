/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExtendDataUtils
 ** Description:ExtendDataUtils 工具类
 ** Version: 1.0
 ** Date : 2025/04/25
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/04/25       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.foundation.codec.extend

import android.net.Uri
import android.os.ParcelFileDescriptor
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.util.os.ContextGetter

object ExtendDataUtils {
    private const val TAG = "HdrTransformDataUtils"

    /**
     * 通过 FileExtendedContainer 读取写入到文件中的数据
     * @param pfd 传入的 ParcelFileDescriptor
     * @param key 需要读取的文件 KEY ，参考 ExtendKey.kt
     * @param clazz 读取返回的 data 类型，继承自 ExtendStruct 的实现类
     *
     * @return 返回 ExtendStruct 的实现类转化为实际数据的数据类
     */
    @JvmStatic
    fun <R, T : ExtendStruct<R>> getExtendData(pfd: ParcelFileDescriptor, key: String, clazz: Class<T>): R? {
        return runCatching {
            FileExtendedContainer().use { container ->
                container.setDataSource(pfd)
                container.getExtensionData(key)?.let {
                    clazz.getConstructor(ByteArray::class.java).newInstance(it).toData()
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "[getExtendData] error:$it" }
        }.getOrNull()
    }

    /**
     * 通过 FileExtendedContainer 读取写入到文件中的数据
     * @param uri 传入的视频 uri
     * @param key 需要读取的文件 KEY ，参考 ExtendKey.kt
     * @param clazz 读取返回的 data 类型，继承自 ExtendStruct 的实现类
     *
     * @return 返回 ExtendStruct 的实现类转化为实际数据的数据类
     */
    @JvmStatic
    fun <R, T : ExtendStruct<R>> getExtendData(uri: Uri, key: String, clazz: Class<T>): R? {
        return runCatching {
            FileExtendedContainer().use { container ->
                container.setDataSource(ContextGetter.context, uri)
                container.getExtensionData(key)?.let {
                    clazz.getConstructor(ByteArray::class.java).newInstance(it).toData()
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "[getExtendData] error:$it" }
        }.getOrNull()
    }
}