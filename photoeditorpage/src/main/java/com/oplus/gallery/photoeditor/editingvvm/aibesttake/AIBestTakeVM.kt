/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIBestTakeVM
 ** Description: AI 最佳表情的页面ViewModel
 **
 ** Version: 1.0
 ** Date: 2024/12/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>  2024/12/26  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aibesttake

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.PointF
import android.graphics.Rect
import android.graphics.RectF
import android.util.Pair
import android.util.Size
import android.util.SizeF
import android.util.SparseArray
import androidx.collection.arrayMapOf
import androidx.core.graphics.scale
import androidx.core.graphics.toRect
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.asAIFaceInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo
import com.oplus.gallery.business_lib.model.data.face.data.getTargetRectF
import com.oplus.gallery.business_lib.model.data.face.data.mirror.AIFaceBestFaceInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.mirror.AIFaceGroupInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.mirror.AIFaceInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceAttrInfoMirror
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.firstOrNull
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.TextUtil.COLON
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_COMMA_SEPARATOR
import com.oplus.gallery.foundation.util.text.TextUtil.STRIKE
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.SensitiveFuncBanItem
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.scan.face.AIFaceConstants
import com.oplus.gallery.framework.abilities.scan.face.IFaceScanAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config.Loader.PREVIEW_PHOTO_MAX_LENGTH
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EmptyReplier
import com.oplus.gallery.photoeditor.editingvvm.EmptyUnitReplier
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.REPLY_TOPIC_AI_BEST_TAKE_CONTINUE_REPLACING
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.REPLY_TOPIC_AI_BEST_TAKE_WINDOWS_FOCUS_CHANGED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.REPLY_TOPIC_CANCEL_AI_BEST_TAKE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_IS_SCANNER_IDLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_NEED_SCAN
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_BACKGROUND_SCAN_TARGET
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_FACE_CONTENT_LOADED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_FACE_CONTENT_REPLACED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_FACE_FRAMES
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_LAUNCH
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_MANUAL_FACE_CONTENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_REPLACE_FACE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_SELECT_TARGET
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_TARGET_SIZE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_THUMBNAIL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIBestTake.TOPIC_AI_BEST_TAKE_UNSELECT_TARGET
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_IMAGE_PACK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_REDO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_UNDO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_SAVE_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_IS_PREVIEW_LOADED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_RENDER_STATE
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeProcessor.Companion.EXPECT_REF_FACE_COUNT
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeProcessor.Companion.LEAST_REF_FACE_COUNT
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeUtils.area
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeUtils.containsBadFaces
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeVM.Companion.KEY_ORIGIN_HEIGHT
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeVM.Companion.KEY_ORIGIN_WIDTH
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeVM.Companion.TAG
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.bean.AIBestTakeState
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.bean.FaceContent
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.bean.FaceFrame
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.bean.FaceFrameState
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.gson.JsonHelper
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.BEST_TAKE_ENHANCED
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.EXCEPTION_TYPE_AI_UNIT
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.EXCEPTION_TYPE_CLOUD
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.EXCEPTION_TYPE_GALLERY
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.FROM_EDIT_PAGE
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.FROM_RECOMMEND_PAGE
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.NORMAL_DIAGRAM
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.RESULT_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackConstant.Value.RESULT_SAVE
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.track.AIBestTakeTrackHelper
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncPermissionObserver
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIFuncPermissionResId
import com.oplus.gallery.photoeditor.editingvvm.airepair.IAIFuncPermissionObserver
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.operating.ExitPageListener
import com.oplus.gallery.photoeditor.editingvvm.operating.FindRecordParams
import com.oplus.gallery.photoeditor.editingvvm.operating.NonParametricRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.NullRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingAction
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.GLOBAL_KEY_EXECUTE_COMMAND
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.KEY_SHOULD_CACHED_FOR_USER
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_PERFORM_REDO
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_PERFORM_UNDO
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingResultListener
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingType
import com.oplus.gallery.photoeditor.editingvvm.operating.RecordFindType
import com.oplus.gallery.photoeditor.editingvvm.operating.STACK_INDEX_ONE
import com.oplus.gallery.photoeditor.editingvvm.operating.STACK_INDEX_THREE
import com.oplus.gallery.photoeditor.editingvvm.operating.STACK_INDEX_TWO
import com.oplus.gallery.photoeditor.editingvvm.operating.SaveStatus
import com.oplus.gallery.photoeditor.editingvvm.preview.DisplayRectWrapper
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewRenderState
import com.oplus.gallery.photoeditor.editingvvm.subscribeOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.subscribeUntilConsumed
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.util.IntelligentEditingRecommendationUtils
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.system.measureTimeMillis
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingR

/**
 * AI 最佳表情的页面ViewModel，负责页面业务逻辑
 * @param editingVM 主VM，开放对所有业务VM访问
 */
@Suppress("LargeClass")
internal class AIBestTakeVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    private var defaultBitmapSink: DefaultBitmapSink? = null

    /**
     * 人脸框信息+状态
     */
    private var faceFramesPub: PublisherChannel<List<FaceFrame>>? = null

    /**
     * 人脸框加载状态
     */
    private var faceContentLoadedPub: PublisherChannel<FaceContent>? = null

    /**
     * 表情替换完成状态
     */
    private var faceContentReplacedPub: PublisherChannel<FaceContent?>? = null

    /**
     * 表情处理状态
     */
    private var aiBestTakeStatePub: PublisherChannel<AIBestTakeState>? = null

    /**
     * 动效用960缩图
     */
    private var thumbnailPub: PublisherChannel<Bitmap>? = null

    private val allVerified = mutableMapOf<AIFaceInfoMirror, List<AIFaceInfoMirror>>()

    /**
     * 扫描是否空闲
     */
    private val iScannerIdleRep = object : EmptyReplier<Boolean>() {
        override fun onEmptyReply(): Boolean {
            return bestTakeProcessor.isScannerIdle()
        }
    }

    /**
     * 是否需要扫描
     */
    private val needScanRep = object : EmptyReplier<Boolean>() {
        override fun onEmptyReply(): Boolean {
            return bestTakeProcessor.needFaceScan()
        }
    }

    /**
     * 目标图尺寸
     */
    private var targetSizePub: PublisherChannel<Size>? = null

    /**
     * 可替换的人脸扫描结果
     */
    private var replaceableFaceMap = mapOf<CvFaceInfo, List<CvFaceInfo>>()

    private val isFromRecommend by lazy {
        PhotoEditorType.AI_BESTTAKE.tag == vmBus?.get<String>(TopicID.InputArguments.TOPIC_INPUTS_EDIT_TYPE)
    }

    private var previewColorSpace: ColorSpace? = null

    /**
     * 目标图尺寸，即当前预览图的尺寸
     */
    private var targetSize: Size? = null

    /**
     * 缩略图大小
     */
    private var thumbnailSizeF: SizeF? = null

    /**
     * 人脸信息
     */
    private var saveBestTakeFaceList: MutableList<CvFaceInfo> = mutableListOf()

    /**
     * 预览区域大小
     */
    private var previewDisplayRectF: RectF? = null

    private var containsOtherBestTakeRecords = false

    private var continueReplacingListener: (() -> Unit)? = null

    /**
     * 继续表情替换
     */
    private var continueReplacingRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            continueReplacingListener?.invoke()
        }
    }

    private val launchRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            vmBus?.subscribeOnce<Boolean>(TOPIC_IS_PREVIEW_LOADED) {
                notifyLaunch()
            }
        }
    }

    private val aiBestTakeStateRep = object : UnitReplier<AIBestTakeState>() {
        override fun onSingleReply(arg: AIBestTakeState) {
            aiBestTakeStatePub?.publish(arg)
        }
    }

    /**
     * 选中人脸
     */
    private val selectTargetRep = object : UnitReplier<FaceFrame>() {
        override fun onSingleReply(arg: FaceFrame) {
            selectFaceFrame(targetFaceFrame = arg)
        }
    }

    /**
     * 取消选中人脸
     */
    private val unselectTargetRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            selectFaceFrame(targetFaceFrame = null)
        }
    }

    /**
     * 后台扫描
     */
    private val backgroundScanTargetRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            launchFaceScannersOnBackground()
        }
    }

    /**
     * 手动选择表情并加载
     */
    private val manualFaceContentRep = object : UnitReplier<FaceContent>() {
        override fun onSingleReply(arg: FaceContent) {
            notifyManualFaceContent(arg)
        }
    }

    /**
     * 替换表情
     */
    private val replaceFaceRep = object : UnitReplier<FaceContent>() {
        override fun onSingleReply(arg: FaceContent) {
            replaceFace(arg)
        }
    }

    // 自动优化任务，提出来便于取消操作
    private var autoOptimizingJob: Job? = null

    // 手动优化任务
    private var manualOptimizingJob: Job? = null

    // 超时任务
    private var timeoutJob: Job? = null

    private var cancelAIBestTakeRep = object : EmptyUnitReplier() {
        override fun onEmptyReply() {
            bestTakeSessionProxy.cancelEffect()
            autoOptimizingJob?.cancel()
            manualOptimizingJob?.cancel()
            timeoutJob?.cancel()
            vmBus?.notifyOnce(REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.action_finish_strategy, "cancelAIBestTakeRep"))
        }
    }

    /**
     * 点击撤销前回调
     */
    private val undoRep = object : UnitReplier<OperatingResultListener>() {
        override fun onSingleReply(arg: OperatingResultListener) {
            notifyUndo(arg)
        }
    }

    /**
     * 点击重做前回调
     */
    private val redoRep = object : UnitReplier<OperatingResultListener>() {
        override fun onSingleReply(arg: OperatingResultListener) {
            notifyRedo(arg)
        }
    }

    /**
     * 点击取消前回调
     */
    private val cancelRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            AIBestTakeTrackHelper.trackAIBestTakeData(RESULT_CANCEL)
            GLog.d(TAG, LogFlag.DL) { "has no effect and execute cancel" }
            bestTakeSessionProxy.cancelEffect()
            autoOptimizingJob?.cancel()
            manualOptimizingJob?.cancel()
            timeoutJob?.cancel()
            arg.onExitPage(isConfirm = false)
        }
    }

    /**
     * 点击完成前回调
     */
    private val confirmRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            vmBus?.subscribeUntilConsumed<PreviewRenderState>(TOPIC_PREVIEW_RENDER_STATE, publishAtOnce = false) {
                // 刷新后才退出
                arg.onExitPage(isConfirm = true)
                true
            }
            GLog.d(TAG, LogFlag.DL) { "confirmRep" }
            confirmFinalEffect()
            AIBestTakeTrackHelper.trackAIBestTakeData(RESULT_SAVE)
        }
    }

    private val saveStatusObserver: TObserver<SaveStatus> = {
        if (it == SaveStatus.SAVE_BEGIN) {
            GLog.d(TAG, LogFlag.DL) { "saveStatusObserver  SAVE_BEGIN" }
            confirmFinalEffect()
        }
    }

    private fun confirmFinalEffect() {
        GLog.d(TAG, LogFlag.DL) { "confirmFinalEffect" }
        editingVM.sessionProxy.renderOnce {
            val state = vmBus?.get<Boolean>(TopicID.Operating.TOPIC_OPERATING_MODIFY_STATE)
            // 将所有表情特效清除后，最后查找所有选中的表情，生成一个新的 record 来做一次表情特效。
            clearAllFaceEffect()
            if (state == false) {
                GLog.d(TAG, LogFlag.DL) { " has no effect and no need re add final effect" }
                return@renderOnce
            }
            getSelectedCoverFaces().takeIf { it.isNotEmpty() }?.let { bestTakeFaceList ->
                AIBestTakeRecord(CMD_BEST_TAKE_REPLACE).apply {
                    addArgument(KEY_GROUP_FACE_ID, getGroupFaceId(bestTakeFaceList))
                    addArgument(KEY_SHOULD_CACHED_FOR_USER, true)
                    targetSize?.let {
                        addArgument(KEY_ORIGIN_WIDTH, it.width)
                        addArgument(KEY_ORIGIN_HEIGHT, it.height)
                    }
                    saveBestTakeFaceList = bestTakeFaceList
                    setFaceList(bestTakeFaceList)
                    setCacheable(true)
                    this.isAiBestTakeEnhanced = true
                    emitRecord(this, OperatingType.RECORD_AND_EFFECT, render = false)
                }
            }
        }
    }

    /**
     * 当撤销、重做时，根据当前 record 来刷新人脸表情选中状态。
     */
    private val currentRecordObserver: TObserver<OperatingRecordWithInvoker> = {
        onCurrentRecordChanged(it)
    }

    /**
     * 窗口焦点发生了变化监听
     */
    private val windowsFocusChangeRep = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            if (arg) {
                updateAIUnitConfig {
                    permissionObserver?.checkPermission()
                }
            }
        }
    }

    // 人脸信息处理类
    private val bestTakeProcessor: AIBestTakeProcessor = AIBestTakeProcessor(app)

    // 人脸优化处理类
    private val bestTakeOptimizer: AIBestTakeOptimizer by lazy {
        AIBestTakeOptimizer().apply {
            initialize(AUTO_OPTIMIZING_SIZE, MAX_BACKGROUND_OPTIMIZING_SIZE, emitter = object : OptimizeEmitter {
                override suspend fun emit(toOptimizedMap: Map<CvFaceInfo, List<CvFaceInfo>>): Map<CvFaceInfo, List<CvFaceInfo>> {
                    // 跟管线的交互限制在VM内部，所以设计这个接口，实际提交任务给管线
                    return executeOptimizing(toOptimizedMap)
                }
            })
        }
    }

    /**
     * 最佳表情插件代理
     */
    private val bestTakeSessionProxy = AIBestTakeSessionProxy(app)

    private val errorPredicate: (CvFaceInfo, AIBestTakeState) -> Boolean = { face, state ->
        when (state) {
            AIBestTakeState.OPTIMIZING_SUCCESS -> face.errorCode == OPTIMIZING_CODE_SUCCESS

            AIBestTakeState.OPTIMIZING_FAIL_SENSITIVE_CONTENT -> {
                face.errorCode == OPTIMIZING_CODE_DETECT_SENSITIVE_CONTENT ||
                    face.errorCode == OPTIMIZING_CODE_DETECT_ERROR
            }

            AIBestTakeState.OPTIMIZING_FAIL_TIMEOUT -> face.errorCode == OPTIMIZING_CODE_REQUEST_TIMEOUT

            AIBestTakeState.OPTIMIZING_FAIL_NETWORK_ERROR -> face.errorCode == OPTIMIZING_CODE_NETWORK_ERROR

            AIBestTakeState.OPTIMIZING_FAIL_DAILY_REQUEST_LIMIT -> face.errorCode == OPTIMIZING_CODE_DAILY_REQUEST_LIMIT

            AIBestTakeState.OPTIMIZING_FAIL_BUSY -> face.errorCode == OPTIMIZING_CODE_BUSY

            AIBestTakeState.OPTIMIZING_FAIL -> {
                face.errorCode == OPTIMIZING_CODE_INVALID_IMAGE ||
                    face.errorCode == OPTIMIZING_CODE_UNPROCESSABLE_IMAGE ||
                    face.errorCode == OPTIMIZING_CODE_ENCRYPT_ERROR ||
                    face.errorCode == OPTIMIZING_CODE_UNKNOWN_ERROR ||
                    face.errorCode == OPTIMIZING_CODE_DUPLICATE_ID
            }

            else -> false
        }
    }

    /**
     * AI相关权限监听器
     */
    private var permissionObserver: IAIFuncPermissionObserver? = null

    init {
        vmBus?.apply {
            aiBestTakeStatePub = registerDuplex(TOPIC_AI_BEST_TAKE_STATE, aiBestTakeStateRep)
            faceFramesPub = register(TOPIC_AI_BEST_TAKE_FACE_FRAMES)
            faceContentLoadedPub = register(TOPIC_AI_BEST_TAKE_FACE_CONTENT_LOADED)
            faceContentReplacedPub = register(TOPIC_AI_BEST_TAKE_FACE_CONTENT_REPLACED)
            thumbnailPub = register(TOPIC_AI_BEST_TAKE_THUMBNAIL)
            targetSizePub = register(TOPIC_AI_BEST_TAKE_TARGET_SIZE)
            register(TOPIC_AI_BEST_IS_SCANNER_IDLE, iScannerIdleRep)
            register(TOPIC_AI_BEST_NEED_SCAN, needScanRep)
            register(TOPIC_AI_BEST_TAKE_LAUNCH, launchRep)
            register(REPLY_TOPIC_CANCEL_AI_BEST_TAKE, cancelAIBestTakeRep)
            register(TOPIC_AI_BEST_TAKE_SELECT_TARGET, selectTargetRep)
            register(TOPIC_AI_BEST_TAKE_UNSELECT_TARGET, unselectTargetRep)
            register(TOPIC_AI_BEST_TAKE_BACKGROUND_SCAN_TARGET, backgroundScanTargetRep)
            register(TOPIC_AI_BEST_TAKE_MANUAL_FACE_CONTENT, manualFaceContentRep)
            register(TOPIC_AI_BEST_TAKE_REPLACE_FACE, replaceFaceRep)
            register(REPLY_TOPIC_AI_BEST_TAKE_WINDOWS_FOCUS_CHANGED, windowsFocusChangeRep)
            register(REPLY_TOPIC_AI_BEST_TAKE_CONTINUE_REPLACING, continueReplacingRep)
            subscribeT(TOPIC_OPERATING_RECORD, currentRecordObserver)
            subscribeT(TOPIC_OPERATING_SAVE_STATUS, saveStatusObserver)
        }
    }

    override fun onCreate() {
        super.onCreate()
        startObservePermission()
        val fromPage = if (isFromRecommend) FROM_RECOMMEND_PAGE else FROM_EDIT_PAGE
        AIBestTakeTrackHelper.recordEnterPage(fromPage)
        val isAiBestTakeEnhanced = IntelligentEditingRecommendationUtils.isPerformBestTake(vmBus)
        val photoType = if (isAiBestTakeEnhanced) BEST_TAKE_ENHANCED else NORMAL_DIAGRAM
        AIBestTakeTrackHelper.recordPhotoType(photoType)
    }

    override fun onResume() {
        super.onResume()
        vmBus?.apply {
            register(TOPIC_OPERATING_ACTION_REDO, redoRep)
            register(TOPIC_OPERATING_ACTION_UNDO, undoRep)
            register(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
            register(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
        }
    }

    override fun onPause() {
        super.onPause()
        vmBus?.apply {
            unregister(TOPIC_OPERATING_ACTION_REDO, redoRep)
            unregister(TOPIC_OPERATING_ACTION_UNDO, undoRep)
            unregister(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
            unregister(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        continueReplacingListener = null
        autoOptimizingJob?.cancel()
        manualOptimizingJob?.cancel()
        timeoutJob?.cancel()
        bestTakeOptimizer.stopOptimizing()
        // 创建和销毁要在同个线程
        bestTakeSessionProxy.cancelEffect()
        bestTakeSessionProxy.destroySession()
        // 释放processor
        bestTakeProcessor.release()
        // 释放图片内存缓存，轻量低的话直接清掉不需要的缓存，避免内存过高；非轻量低加个临时缓存上限，达到上限清空，否则等到退出编辑再清空
        AIBestTakeFaceCache.clear(saveBestTakeFaceList, clearRetainedFaces = containsOtherBestTakeRecords.not())
        removeDefaultBitmapSink()
        vmBus?.apply {
            unregisterDuplexT(TOPIC_AI_BEST_TAKE_STATE, aiBestTakeStateRep, aiBestTakeStatePub)
            unregisterT(TOPIC_AI_BEST_TAKE_FACE_FRAMES, faceFramesPub)
            unregisterT(TOPIC_AI_BEST_TAKE_FACE_CONTENT_LOADED, faceContentLoadedPub)
            unregisterT(TOPIC_AI_BEST_TAKE_FACE_CONTENT_REPLACED, faceContentReplacedPub)
            unregisterT(TOPIC_AI_BEST_TAKE_THUMBNAIL, thumbnailPub)
            unregisterT(TOPIC_AI_BEST_TAKE_TARGET_SIZE, targetSizePub)
            unregister(TOPIC_AI_BEST_IS_SCANNER_IDLE, iScannerIdleRep)
            unregister(TOPIC_AI_BEST_NEED_SCAN, needScanRep)
            unregister(TOPIC_AI_BEST_TAKE_LAUNCH, launchRep)
            unregister(REPLY_TOPIC_CANCEL_AI_BEST_TAKE, cancelAIBestTakeRep)
            unregister(TOPIC_AI_BEST_TAKE_SELECT_TARGET, selectTargetRep)
            unregister(TOPIC_AI_BEST_TAKE_UNSELECT_TARGET, unselectTargetRep)
            unregister(TOPIC_AI_BEST_TAKE_BACKGROUND_SCAN_TARGET, backgroundScanTargetRep)
            unregister(TOPIC_AI_BEST_TAKE_MANUAL_FACE_CONTENT, manualFaceContentRep)
            unregister(TOPIC_AI_BEST_TAKE_REPLACE_FACE, replaceFaceRep)
            unregister(REPLY_TOPIC_AI_BEST_TAKE_WINDOWS_FOCUS_CHANGED, windowsFocusChangeRep)
            unregister(REPLY_TOPIC_AI_BEST_TAKE_CONTINUE_REPLACING, continueReplacingRep)
            unsubscribe(TOPIC_OPERATING_RECORD, currentRecordObserver)
            unsubscribe(TOPIC_OPERATING_SAVE_STATUS, saveStatusObserver)
        }
        permissionObserver?.endObserve()
    }

    private fun containsOtherBestTakeRecords(): Boolean {
        val subStackRecords = vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS,
            FindRecordParams(
                stackIndex = STACK_INDEX_TWO,
                findType = RecordFindType.FIND_ALL,
                predicate = { record -> record.effectName == AvEffect.AiBestTakeEffect.name }
            )
        )
        if (!subStackRecords.isNullOrEmpty()) {
            return true
        }
        val mainStackRecords = vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS,
            FindRecordParams(
                stackIndex = STACK_INDEX_ONE,
                findType = RecordFindType.FIND_ALL,
                predicate = { record -> record.effectName == AvEffect.AiBestTakeEffect.name }
            )
        )
        return !mainStackRecords.isNullOrEmpty()
    }

    /**
     * 启动相册后台人脸扫描
     */
    private fun launchFaceScannersOnBackground() {
        bestTakeProcessor.launchFaceScannersOnBackground()
        GLog.d(TAG, LogFlag.DL) { "launchBackgroundFaceScan" }
    }

    /**
     * 启动AI最佳表情权限监听
     */
    private fun startObservePermission() {
        updateAIUnitConfig()
        permissionObserver = AIFuncPermissionObserver(
            ContextGetter.context,
            AIFuncPermissionResId(
                AuthorizingR.string.authorizing_ai_group_photo_statement,
                R.string.photoeditorpage_best_take_need_connect_network
            ),
            AIFaceConstants.DETECT_NAME
        )
        permissionObserver?.startObserve(
            postNotificationAction = {
                vmBus?.notifyOnce(TopicID.Notification.TOPIC_NOTIFICATION_ACTION, it)
            },
            refuseAction = {
                vmBus?.notifyOnce(REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.action_finish_strategy, "startObservePermission"))
            }
        )
    }

    /**
     * 更新AIUnit配置
     * @param delay
     * @param block
     */
    private fun updateAIUnitConfig(delay: Long = 0, block: () -> Unit = {}) {
        // 进入AI最佳表情面后，刷新一下下载配置
        launch(Dispatchers.IO) {
            delay(delay)
            app.getAppAbility<ISettingsAbility>()?.use {
                it.updateBlockingConfig(
                    app,
                    IS_AGREE_AI_UNIT_PRIVACY,
                    AI_BEST_TAKE_DETECT_STATE
                )
            }
            withContext(Dispatchers.UI) {
                block.invoke()
            }
        }
    }

    /**
     * 选择人脸，刷新人脸框的高亮状态
     * @param faceFrameList 全部人脸框列表
     * @param targetFaceFrame 目标人脸框，为空时表示没有选中任何人脸框
     */
    private fun selectFaceFrame(faceFrameList: MutableList<FaceFrame>? = null, targetFaceFrame: FaceFrame?) {
        val oldFrames = faceFrameList ?: let { vmBus?.get<List<FaceFrame>>(TOPIC_AI_BEST_TAKE_FACE_FRAMES) }
        oldFrames?.onEach {
            val isSelected = (it.faceID == targetFaceFrame?.faceID)
            it.isSelected = isSelected
            it.frameState = when {
                // 如果小于最少可参考的人脸图的话，人脸框不显示，但是可被点击，点击后弹出不支持优化
                it.faceContentList.size <= LEAST_REF_FACE_COUNT -> FaceFrameState.INVISIBLE

                isSelected -> FaceFrameState.HIGHLIGHT

                else -> FaceFrameState.NORMAL
            }
        }
        oldFrames?.let { publishFaceFrames(mutableListOf<FaceFrame>().also { it.addAll(oldFrames) }) }
    }

    /**
     * 点击撤销前回调
     */
    private fun notifyUndo(callback: OperatingResultListener) {
        selectFaceFrame(targetFaceFrame = null)
        editingVM.sessionProxy.renderOnce {
            callback.onResult(isConsumed = false)
        }
    }

    /**
     * 点击重做前回调
     */
    private fun notifyRedo(callback: OperatingResultListener) {
        selectFaceFrame(targetFaceFrame = null)
        editingVM.sessionProxy.renderOnce {
            callback.onResult(isConsumed = false)
        }
    }

    /**
     * 撤销、重做完成后，根据当前的 record 对象，更新 FaceFrame 表情选中状态。
     */
    private fun onCurrentRecordChanged(currentRecord: OperatingRecordWithInvoker?) {
        currentRecord?.let {
            when (it.invokerType) {
                INVOKE_TYPE_PERFORM_REDO,
                INVOKE_TYPE_PERFORM_UNDO -> {
                    faceContentReplacedPub?.publish(null)
                    if (it.operatingRecord is AIBestTakeRecord) {
                        it.operatingRecord.getFaceList().forEach { cvFaceInfo ->
                            updateFaceFrameSelectedState(cvFaceInfo)
                        }
                    } else {
                        resetFaceFrameSelectedState()
                    }
                }

                else -> Unit
            }
        }
    }

    /**
     * 更新 cvFaceInfo 对应的 FaceFrame 表情选中状态
     */
    private fun updateFaceFrameSelectedState(cvFaceInfo: CvFaceInfo) {
        vmBus?.get<List<FaceFrame>>(TOPIC_AI_BEST_TAKE_FACE_FRAMES)?.let {
            GLog.d(TAG, LogFlag.DL) { "updateFaceFrameSelectedState  cvFaceInfo[${cvFaceInfo.id}][${cvFaceInfo.groupId}]" }
            it.forEach { faceFrame ->
                GLog.d(TAG, LogFlag.DL) { "updateFaceFrameSelectedState  faceFrame[${faceFrame.faceID}][${faceFrame.groupId}]" }
                if (faceFrame.groupId == cvFaceInfo.groupId) {
                    faceFrame.faceContentList.forEach { faceContent ->
                        GLog.d(TAG, LogFlag.DL) {
                            "updateFaceFrameSelectedState  faceContent[${faceContent.isSelected}][${faceContent.faceId}][${faceContent.mainFaceID}]"
                        }
                        faceContent.isSelected = (faceContent.faceId == cvFaceInfo.id)
                    }
                    publishFaceFrames(it)
                    return
                }
            }
        }
    }

    /**
     * 重置 FaceFrame 列表的选中状态，全部恢复选中默认头像。
     */
    private fun resetFaceFrameSelectedState() {
        vmBus?.get<List<FaceFrame>>(TOPIC_AI_BEST_TAKE_FACE_FRAMES)?.let {
            it.forEach { faceFrame ->
                faceFrame.faceContentList.withIndex().forEach { (index, faceContent) ->
                    faceContent.isSelected = (index == 0)
                }
            }
            publishFaceFrames(it)
        }
    }

    /**
     * 【主流程】自动优化分成2步：
     * 1.识别和寻找可替换表情；
     * 2.自动优化并替换表情/后台优化但不替换表情
     */
    @Suppress("LongMethod")
    private fun notifyLaunch() {
        GLog.d(TAG, LogFlag.DL) { "notifyLaunch start" }
        autoOptimizingJob = launch {
            // 是否做了其它表情优化效果
            containsOtherBestTakeRecords = containsOtherBestTakeRecords()
            var targetBitmap: Bitmap? = loadTargetBitmap()
            targetSize = Size(targetBitmap?.width ?: 0, targetBitmap?.height ?: 0).also {
                targetSizePub?.publish(it)
            }
            GLog.w(TAG, LogFlag.DL) { "notifyLaunch: targetSize: $targetSize" }
            previewColorSpace = targetBitmap?.colorSpace
            val thumbnail = scaleToThumbnail(targetBitmap ?: return@launch)
            withContext(Dispatchers.UI) {
                thumbnailPub?.publish(thumbnail)
            }
            // 还是用缩图吧，不然内存占用有点大
            bestTakeSessionProxy.createSession(thumbnail)
            publishState(AIBestTakeState.RECOGNIZING)
            // 放在这里，还是先拉起识别动画先
            if (isFunctionBanned()) {
                publishState(AIBestTakeState.OPTIMIZING_FAIL_BAN)
                GLog.d(TAG, LogFlag.DL) { "[notifyLaunch] AI best take function is banned." }
                return@launch
            }
            measureTimeMillis {
                // 1. 识别并寻找可替换表情
                replaceableFaceMap = recognizeFaces(targetBitmap ?: return@launch, previewColorSpace)
                AIBestTakeTrackHelper.recordFocusFaceNum(replaceableFaceMap.size)
            }.also {
                GLog.d(TAG, LogFlag.DL) { "$BEST_TAKE_COMMON notifyLaunch, recognizeFaces cost time:$it" }
            }
            val recognizingState = checkRecognizingError(replaceableFaceMap)
            if (recognizingState != AIBestTakeState.RECOGNIZING_SUCCESS) {
                GLog.w(TAG, LogFlag.DL) { "notifyLaunch: recognizing failed." }
                publishState(recognizingState)
                return@launch
            }
            // 裁剪主图和保存主图需要放在聚类后，否则没有groupID信息
            corpMainFaces(targetBitmap)
            targetBitmap = null
            // 给动效传入绘制人脸框的数据，此时不触发动效
            publishFaceFrames(replaceableFaceMap.asAnimatingFaceFrames())
            val frameList = replaceableFaceMap.asFaceFrames()
            // 如果所有的参考图size都小于1，直接给识别失败异常
            if (frameList.isEmpty() || frameList.all { it.faceContentList.size <= LEAST_REF_FACE_COUNT }) {
                publishState(AIBestTakeState.RECOGNIZING_FAIL)
            } else {
                // 3. 后台优化
                optimizeOnBackground(replaceableFaceMap)
                publishState(AIBestTakeState.RECOGNIZING_SUCCESS)
                waitAnimationEnd {
                    collectAndSelectFrames(frameList, mutableMapOf())
                    publishState(AIBestTakeState.COMPLETE)
                }
            }
        }
    }

    /**
     * 裁剪主图并缓存
     * @param target 预览图
     */
    private fun corpMainFaces(target: Bitmap) {
        replaceableFaceMap.keys.onEach {
            val mainBitmap = AIBestTakeUtils.cropFace(it, target, previewColorSpace ?: DISPLAY_P3)
            GLog.d(AIBestTakeProcessor.TAG, LogFlag.DL) {
                "corpMainFaces: crop main picture, w = ${mainBitmap?.width} , h = ${mainBitmap?.height} , rect = ${it.rect}"
            }
            mainBitmap?.apply {
                // 逐一缓存参考图（内存缓存，当次编辑有效）
                AIBestTakeFaceCache.saveRefBitmap(faceInfo = it, bitmap = this)
                AIBestTakeFaceCache.saveOptimizedBitmap(faceInfo = it.also {
                    it.mainId = it.id
                    it.cacheTime = System.currentTimeMillis()
                }, bitmap = this)
            }
        }
    }

    private fun waitAnimationEnd(callback: () -> Unit) {
        continueReplacingListener = {
            callback.invoke()
            continueReplacingListener = null
        }
    }

    private fun scaleToThumbnail(previewBitmap: Bitmap): Bitmap {
        val scale = ImageUtils.adviseImageScale(
            previewBitmap.width, previewBitmap.height,
            ThumbnailSizeUtils.THUMBNAIL_SIZE_PHOTO,
            ThumbnailSizeUtils.THUMBNAIL_SIZE_PHOTO
        )
        val thumbnail = if (scale < 1.0f) {
            val destWidth = previewBitmap.width * scale
            val destHeight = previewBitmap.height * scale
            previewBitmap.scale(destWidth.toInt(), destHeight.toInt(), true)
        } else previewBitmap
        GLog.d(TAG, LogFlag.DL) {
            "scaleToThumbnail, thumbnail[${thumbnail.width}x${thumbnail.height}] , scale:$scale}"
        }
        return thumbnail
    }

    /**
     * 手动替换表情时，检查优化的表情是否正常。
     * @param face 优化的表情
     */
    private fun checkOptimizingError(face: CvFaceInfo?): AIBestTakeState {
        // 为空直接fail
        if (face == null) {
            GLog.w(TAG, LogFlag.DL) { "checkOptimizingError: manual optimizing result empty, replace failed." }
            return AIBestTakeState.OPTIMIZING_FAIL
        }
        // 包含任意敏感内容fail
        if (errorPredicate(face, AIBestTakeState.OPTIMIZING_FAIL_SENSITIVE_CONTENT)) {
            return AIBestTakeState.OPTIMIZING_FAIL_SENSITIVE_CONTENT
        }
        // 如果有任意一组成功了，则返回成功
        if (errorPredicate(face, AIBestTakeState.OPTIMIZING_SUCCESS)) {
            return AIBestTakeState.OPTIMIZING_SUCCESS
        }
        if (errorPredicate(face, AIBestTakeState.OPTIMIZING_FAIL_NETWORK_ERROR)) {
            return AIBestTakeState.OPTIMIZING_FAIL_NETWORK_ERROR
        }
        if (errorPredicate(face, AIBestTakeState.OPTIMIZING_FAIL_BUSY)) {
            return AIBestTakeState.OPTIMIZING_FAIL_BUSY
        }
        if (errorPredicate(face, AIBestTakeState.OPTIMIZING_FAIL_DAILY_REQUEST_LIMIT)) {
            return AIBestTakeState.OPTIMIZING_FAIL_DAILY_REQUEST_LIMIT
        }
        // 有超时先超时
        if (errorPredicate(face, AIBestTakeState.OPTIMIZING_FAIL_TIMEOUT)) {
            return AIBestTakeState.OPTIMIZING_FAIL_TIMEOUT
        }
        return AIBestTakeState.OPTIMIZING_FAIL
    }

    private fun checkRecognizingError(replaceableFaceMap: Map<CvFaceInfo, List<CvFaceInfo>>): AIBestTakeState {
        // 超过最大处理个数
        if (replaceableFaceMap.size > MAX_SUPPORT_FACE_COUNT) {
            return AIBestTakeState.RECOGNIZING_FAIL_EXCEED_LIMIT
        }
        // 为空默认找不到相似图
        if (replaceableFaceMap.isEmpty()) {
            trackException(allVerified)
            val failError = allVerified.filterNot { it.key.error == ERROR_CODE_SUCCESS }.keys.map { it.error }
            return when {
                failError.contains(RECOGNIZE_CODE_FACE_SMALL) -> AIBestTakeState.RECOGNIZING_FAIL_FACE_SMALL

                failError.contains(RECOGNIZE_CODE_FACE_RANGE_ERROR) -> AIBestTakeState.RECOGNIZING_FAIL_FACE_INCOMPLETE

                else -> AIBestTakeState.RECOGNIZING_FAIL
            }
        }
        return AIBestTakeState.RECOGNIZING_SUCCESS
    }

    private fun collectAndSelectFrames(
        faceFrameList: MutableList<FaceFrame>,
        autoOptimizedMap: Map<CvFaceInfo, List<CvFaceInfo>>
    ) {
        // 自动优化找到优化成功，且人脸面积最大的一张人脸高亮，无自动优化直接找到人脸最大的一张高亮
        selectFaceFrame(
            faceFrameList = faceFrameList,
            targetFaceFrame = if (autoOptimizedMap.isEmpty()) {
                faceFrameList.filter { it.faceContentList.size > LEAST_REF_FACE_COUNT }.maxByOrNull { it.faceRect.area() }
            } else {
                faceFrameList.filter {
                    it.faceContentList.size > LEAST_REF_FACE_COUNT
                }.filter {
                    autoOptimizedMap.any { entry ->
                        (entry.key.id == it.faceID) && (entry.value.any { it.errorCode == OPTIMIZING_CODE_SUCCESS })
                    }
                }.maxByOrNull {
                    it.faceRect.area()
                }
            }
        )
    }

    /**
     * 更新UI状态
     * @param state 目标UI状态
     */
    private fun publishState(state: AIBestTakeState) {
        if (state == AIBestTakeState.OPTIMIZING_FAIL_SENSITIVE_CONTENT) {
            // 统计下拒识次数
            countRefuse()
        }
        launch(Dispatchers.UI) {
            aiBestTakeStatePub?.publish(state)
        }
    }

    /**
     * 更新人脸框UI状态
     * @param faceFrames 人脸框集合
     */
    private fun publishFaceFrames(faceFrames: List<FaceFrame>) {
        launch(Dispatchers.UI) {
            faceFramesPub?.publish(faceFrames)
        }
    }

    /**
     * 设置缩略图大小
     */
    private fun setThumbnailSize() {
        val bitmap = vmBus?.get<Bitmap>(TOPIC_AI_BEST_TAKE_THUMBNAIL)
        thumbnailSizeF = bitmap?.let { SizeF(it.width.toFloat(), it.height.toFloat()) } ?: SizeF(0f, 0f)
        GLog.d(TAG, LogFlag.DL) {
            "setThumbnailSize  thumbnailSizeF: ${thumbnailSizeF?.width} , ${thumbnailSizeF?.height}"
        }
    }

    /**
     * 设置预览区域大小，缓存在工具类中。
     */
    private fun setPreviewDisplayRectF() {
        previewDisplayRectF =
            vmBus?.get<DisplayRectWrapper>(TopicID.Preview.TOPIC_PREVIEW_DISPLAY_RECT_WRAPPER)?.displayRect ?: RectF()
        GLog.d(TAG, LogFlag.DL) { "setPreviewDisplayRectF  previewDisplayRectF: $previewDisplayRectF" }
    }

    /**
     * 识别并寻找可替换表情
     * @return 若干组“待优化的人脸+参考人脸”
     */
    private suspend fun recognizeFaces(bitmap: Bitmap, colorSpace: ColorSpace?): Map<CvFaceInfo, List<CvFaceInfo>> {
        GLog.d(TAG, LogFlag.DL) { "recognizeFaces: " }
        val mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        mediaItem ?: let {
            GLog.d(TAG, LogFlag.DL) { "recognizeFaces: mediaItem is null" }
            return emptyMap()
        }
        setThumbnailSize()
        setPreviewDisplayRectF()
        // 初始化processor，需要阻塞等待初始化结果
        val isProcessorInitialized: Boolean
        measureTimeMillis {
            isProcessorInitialized = bestTakeProcessor.initialize(colorSpace, emitter = object : VerifyEmitter {
                override suspend fun emit(toVerifiedMap: Map<CvFaceCluster, List<CvFaceCluster>>): Map<CvFaceCluster, List<CvFaceCluster>> {
                    // 跟管线的交互限制在VM内部，所以设计这个接口，实际提交任务给管线
                    return executeVerifying(toVerifiedMap)
                }
            })
        }.also {
            GLog.d(TAG, LogFlag.DL) { "$BEST_TAKE_COMMON recognizeFaces, initialize cost time:$it" }
        }
        if (isProcessorInitialized.not()) {
            GLog.w(TAG, LogFlag.DL) { "recognizeFaces: BestTakeProcessor initialize failed." }
            return emptyMap()
        }
        // 1.获取当前图片上的全部人脸信息
        val currentFaces: List<CvFaceCluster>
        measureTimeMillis {
            currentFaces = bestTakeProcessor.getFaceInfoList(mediaItem, bitmap, hasNoEffect())
        }.also {
            GLog.d(TAG, LogFlag.DL) { "$BEST_TAKE_COMMON recognizeFaces, getFaceInfoList cost time:$it" }
        }
        if (currentFaces.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "recognizeFaces: Current picture contains no face." }
            return emptyMap()
        }
        AIBestTakeTrackHelper.recordFaceNum(currentFaces.size)
        // 2.每张人脸都获取其参考图人脸列表
        val refFacesMap: Map<CvFaceCluster, List<CvFaceCluster>>
        measureTimeMillis {
            refFacesMap = bestTakeProcessor.getRefFacesMap(currentFaces, mediaItem)
        }.also {
            GLog.d(TAG, LogFlag.DL) { "$BEST_TAKE_COMMON recognizeFaces, getRefFacesMap cost time:$it" }
        }
        if (refFacesMap.isEmpty() || refFacesMap.all { it.value.isEmpty() }) {
            GLog.d(TAG, LogFlag.DL) { "recognizeFaces: Cannot find any referring face." }
            return emptyMap()
        }
        return refFacesMap as Map<CvFaceInfo, List<CvFaceInfo>>
    }

    private suspend fun loadTargetBitmap(): Bitmap {
        return suspendCoroutine {
            defaultBitmapSink = DefaultBitmapSink(
                PREVIEW_PHOTO_MAX_LENGTH,
                PREVIEW_PHOTO_MAX_LENGTH
            ) { bitmap, _, _, _ ->
                removeDefaultBitmapSink()
                it.resume(bitmap)
            }.also {
                editingVM.sessionProxy.addAsOnceFrameSink(IAvSink.AvSinkType.BITMAP, sink = it)
            }
        }
    }

    private fun removeDefaultBitmapSink() {
        defaultBitmapSink?.also {
            editingVM.sessionProxy.removeSink(it.sinkType, it)
            defaultBitmapSink = null
        }
    }

    /**
     * 判断是否编辑效果，注意水印的特殊情况，进入次级页面水印会被去掉此时认为还是有编辑效果的
     */
    private fun hasNoEffect(): Boolean {
        // 获取当前record，能获取到的话栈就不为空
        val record = vmBus?.get<OperatingRecordWithInvoker>(TOPIC_OPERATING_RECORD)?.operatingRecord
        val isStackEmpty = (record == null) || (record is NullRecord)

        // 进入次级页面水印会被去掉,而此时栈有可能为空，此时认为还是有编辑效果的
        val hasWatermark = vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.watermarkInfo?.hasWatermark() ?: false
        return isStackEmpty && !hasWatermark
    }

    /**
     * 数据校验
     * @param refFaceMap 送校验的人脸map
     * @return 可优化的人脸
     */
    private suspend fun emitVerifyingCmd(refFaceMap: Map<String, List<String>>): Map<AIFaceInfoMirror, List<AIFaceInfoMirror>> {
        // 这里直接批量校验，避免反复触发管线
        return suspendCoroutine { continuation ->
            bestTakeSessionProxy.addObservableEffect(
                avEffect = AvEffect.AiBestTakeEffect,
                params = arrayMapOf(
                    GLOBAL_KEY_EXECUTE_COMMAND to CMD_BEST_TAKE_VERIFY,
                    KEY_BEST_TAKE_VERIFY_INPUTS to refFaceMap
                ),
                KEY_BEST_TAKE_VERIFY_OUTPUTS
            ) { result ->
                GLog.d(TAG, LogFlag.DL) { "emitVerifyingCmd: result = $result" }
                val outputMaps = result as? Map<String, List<String>>
                val verifyMaps = mutableMapOf<AIFaceInfoMirror, List<AIFaceInfoMirror>>()
                outputMaps?.onEachIndexed { _, (key, value) ->
                    val aiFaceInfoKey = JsonHelper.fromJson(key, AIFaceInfoMirror::class.java) ?: AIFaceInfoMirror()
                    val aiFaceInfoValue = value.mapNotNull { JsonHelper.fromJson(it, AIFaceInfoMirror::class.java) }
                    verifyMaps[aiFaceInfoKey] = aiFaceInfoValue
                }
                runCatching { continuation.resume(verifyMaps) }.getOrLog(TAG, "emitVerifyingCmd try to resume twice.")
            }
        }
    }

    /**
     * 执行校验
     * @param refFacesMap 待校验的人脸数据
     * @return 返回通过校验的人脸数据
     */
    private suspend fun executeVerifying(
        refFacesMap: Map<CvFaceCluster, List<CvFaceCluster>>
    ): Map<CvFaceCluster, List<CvFaceCluster>> {
        if (refFacesMap.isEmpty() || refFacesMap.values.all { it.isEmpty() }) {
            GLog.w(TAG, LogFlag.DL) { "executeVerifying: find no ref face at all." }
            return refFacesMap
        }
        // 这里直接批量校验，避免反复触发管线
        val result = mutableMapOf<CvFaceCluster, List<CvFaceCluster>>()
        // 组合成列表后送检
        val faceInfoMirrorMap = refFacesMap.asAIFaceInfoMirrorMap()
        GLog.d(TAG, LogFlag.DL) { "executeVerifying: before verifying, faceInfoMirrorMap size = ${faceInfoMirrorMap.size}" }
        faceInfoMirrorMap.onEach {
            GLog.d(TAG, LogFlag.DL) { "executeVerifying: before verifying, each face ref size = ${it.value.size}" }
        }
        val verified = emitVerifyingCmd(faceInfoMirrorMap)
        val inputFaceCount = verified.flatMap { it.value }.size
        val outputFaceCount = verified.flatMap { it.value }.filter { it.error == ERROR_CODE_SUCCESS }.size
        AIBestTakeTrackHelper.recordFilterCount(inputFaceCount, outputFaceCount)
        allVerified.putAll(verified)
        val verifiedSuccess = mutableMapOf<AIFaceInfoMirror, List<AIFaceInfoMirror>>()
        verified.filter { it.key.error == ERROR_CODE_SUCCESS }.onEach {
            val refSuccess = it.value.filter { it.error == ERROR_CODE_SUCCESS }
            if (refSuccess.isNotEmpty()) {
                verifiedSuccess.put(it.key, refSuccess)
            }
        }

        GLog.d(TAG, LogFlag.DL) { "executeVerifying: after verifying, verified success size = ${verifiedSuccess.size}" }
        verifiedSuccess.values.onEach {
            val old = refFacesMap.firstOrNull { entry ->
                entry.key.id.toString() == it[0].tag
            } ?: return@onEach
            val main = old.key.also { face ->
                face.rect = it[0].faceRect ?: face.rect
                face.faceLandmark = (it[0].faceLandmark ?: face.faceLandmark) as? Array<PointF>
                face.isSupportAutoEdit = it[0].autoEdit
            }
            val subs = mutableListOf<CvFaceCluster>()
            it.onEachIndexed { index, aiFaceInfo ->
                // 0是主人脸，得过滤掉
                if (index > 0) {
                    val sub = old.value.firstOrNull { face -> face.id.toString() == aiFaceInfo.tag } ?: return@onEach
                    sub.rect = aiFaceInfo.faceRect ?: sub.rect
                    sub.faceLandmark = (aiFaceInfo.faceLandmark ?: sub.faceLandmark) as? Array<PointF>
                    sub.isSupportAutoEdit = aiFaceInfo.autoEdit
                    subs.add(sub)
                }
            }
            // 返回结果赋值回来
            result[main] = subs
        }
        GLog.d(TAG, LogFlag.DL) {
            "executeVerifying: after verifying, result size = ${result.size} , " +
                "isEmpty = ${result.isEmpty() || result.values.isEmpty() || result.values.all { it.isEmpty() }}"
        }
        return result
    }

    private fun trackException(
        verified: Map<AIFaceInfoMirror, List<AIFaceInfoMirror>>
    ) {
        val mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        mediaItem ?: let {
            GLog.d(TAG, LogFlag.DL) { "trackException: mediaItem is null" }
            return
        }
        // 主图过滤失败
        val mainFail = verified.filterNot { it.key.error == ERROR_CODE_SUCCESS }.keys.groupBy { it.error }
        mainFail.forEach {
            AIBestTakeTrackHelper.trackEditException(
                EXCEPTION_TYPE_AI_UNIT,
                it.key,
                it.value.size.toString(),
                mediaItem
            )
        }
        // 主图成功参考图过滤失败
        val refFail = verified
            .filter { it.key.error == ERROR_CODE_SUCCESS }.values
            .flatMap { it }
            .filterNot { it.error == ERROR_CODE_SUCCESS }
            .groupBy { it.error }
        refFail.forEach {
            AIBestTakeTrackHelper.trackEditException(
                EXCEPTION_TYPE_AI_UNIT,
                it.key,
                it.value.size.toString(),
                mediaItem
            )
        }
    }

    /**
     * 提交自动优化操作到管线
     * @param toOptimizedMap 待优化map
     * @return 优化结果
     */
    @Suppress("LongMethod")
    private suspend fun executeOptimizing(toOptimizedMap: Map<CvFaceInfo, List<CvFaceInfo>>): Map<CvFaceInfo, List<CvFaceInfo>> {
        return suspendCoroutine { continuation ->
            val isAutoOpt = toOptimizedMap.containsBadFaces()
            val unoptimized = toOptimizedMap.asAIFaceGroupInfoMirrorList()
            val manualOptStart = if (isAutoOpt) 0L else System.currentTimeMillis()
            GLog.d(TAG, LogFlag.DL) { "executeOptimizing: before optimizing, unoptimized size = ${unoptimized.size}" }
            emitOptimizingCmd(unoptimized) { optimizedList ->
                GLog.d(TAG, LogFlag.DL) { "executeOptimizing: after optimizing, optimizedList size = ${optimizedList.size}" }
                // 说明整批数据异常
                if ((optimizedList.size == 1) && (optimizedList[0].error != OPTIMIZING_CODE_SUCCESS)) {
                    GLog.w(TAG, LogFlag.DL) { "executeOptimizing: failed, error = ${optimizedList[0].error}" }
                    continuation.resume(toOptimizedMap.onEach { it.value.onEach { face -> face.errorCode = optimizedList[0].error } })
                    vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.let {
                        AIBestTakeTrackHelper.trackEditException(
                            EXCEPTION_TYPE_CLOUD,
                            optimizedList[0].error,
                            optimizedList[0].messages ?: EMPTY_STRING,
                            it
                        )
                    }
                    return@emitOptimizingCmd
                }
                val optimizeSuccess = optimizedList.filter { it.error == OPTIMIZING_CODE_SUCCESS }
                if (optimizeSuccess.isEmpty()) {
                    vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.let {
                        optimizedList.onEach { bestTakeInfo ->
                            AIBestTakeTrackHelper.trackEditException(
                                EXCEPTION_TYPE_CLOUD,
                                bestTakeInfo.error,
                                bestTakeInfo.messages ?: EMPTY_STRING,
                                it
                            )
                        }
                    }
                }
                optimizedList.onEach { optimized ->
                    val entry = toOptimizedMap.firstOrNull {
                        optimized.faceId == it.key.id.toInt()
                    }
                    val mainCvFaceInfo = entry?.key ?: let {
                        GLog.w(TAG, LogFlag.DL) { "executeOptimizing: match no main cvFaceInfo." }
                        return@onEach
                    }
                    val cvFaceInfo = entry.value.firstOrNull { face ->
                        optimized.id == AIBestTakeUtils.generateFaceId(face)
                    } ?: let {
                        GLog.w(TAG, LogFlag.DL) { "executeOptimizing: match no cvFaceInfo." }
                        return@onEach
                    }
                    GLog.d(TAG, LogFlag.DL) {
                        "executeOptimizing: cvFaceInfo.data = ${PathMask.mask(cvFaceInfo.data)} , bitmap = ${optimized.dstImg}" +
                            " , optimized.error = ${optimized.error}"
                    }
                    // 这两次赋值的结果，直到退出编辑前都需要保证有效
                    cvFaceInfo.errorCode = optimized.error
                    cvFaceInfo.mainId = optimized.faceId.toLong()
                    cvFaceInfo.cacheTime = System.currentTimeMillis()
                    if (optimized.error == OPTIMIZING_CODE_SUCCESS) {
                        optimized.dstImg?.let { dstImg ->
                            // 如果unionRect不为空走裁剪生成图图回帖逻辑，否则直接按照原有的生成图直接回帖
                            optimized.unionRect?.let {
                                val cropImg = AIBestTakeUtils.cropDstFace(cvFaceInfo, targetSize, dstImg, optimized.unionRect, previewColorSpace)
                                val mainRectOnOrigin = convertDstRectOnOrigin(mainCvFaceInfo.rect, it, dstImg.width, dstImg.height)
                                AIBestTakeFaceCache.saveOptimizedBitmap(cvFaceInfo, cropImg)
                                AIBestTakeFaceCache.saveOptimizedRect(cvFaceInfo, mainRectOnOrigin)
                                // 需要同步更新下主图的，注意这里再循环里只更新一次
                                if (AIBestTakeFaceCache.getOptimizedRect(mainCvFaceInfo) == null) {
                                    AIBestTakeFaceCache.getRefBitmap(mainCvFaceInfo)?.let { mainImg ->
                                        mainCvFaceInfo.mainId = optimized.faceId.toLong()
                                        mainCvFaceInfo.cacheTime = System.currentTimeMillis()
                                        val mainCropImg =
                                            AIBestTakeUtils.cropDstFace(mainCvFaceInfo, targetSize, mainImg, optimized.unionRect, previewColorSpace)
                                        AIBestTakeFaceCache.saveOptimizedBitmap(mainCvFaceInfo, mainCropImg)
                                        AIBestTakeFaceCache.saveOptimizedRect(mainCvFaceInfo, mainRectOnOrigin)
                                    }
                                }
                            } ?: let {
                                AIBestTakeFaceCache.saveOptimizedBitmap(cvFaceInfo, dstImg.also {
                                    BitmapUtils.trySetBitmapColorSpace(it, previewColorSpace ?: DISPLAY_P3, "saveOptimizedBitmap")
                                })
                            }
                        }
                    }
                }
                if (isAutoOpt.not()) {
                    AIBestTakeTrackHelper.recordManualOptimizeTime(System.currentTimeMillis() - manualOptStart)
                }
                runCatching { continuation.resume(toOptimizedMap) }.getOrLog(TAG, "emitOptimizingCmd try to resume twice.")
                return@emitOptimizingCmd
            }
        }
    }

    private fun emitOptimizingCmd(unoptimized: List<AIFaceGroupInfoMirror>, callback: (List<AIFaceBestFaceInfoMirror>) -> Unit) {
        val srcBitmaps = unoptimized.map { it.srcImage }
        val dstBitmaps = unoptimized.map { it.dstImage }
        val unoptimizedList = unoptimized.map { JsonHelper.toJson(it) }
        bestTakeSessionProxy.addObservableEffect(
            avEffect = AvEffect.AiBestTakeEffect,
            params = arrayMapOf(
                GLOBAL_KEY_EXECUTE_COMMAND to CMD_BEST_TAKE_OPTIMIZE,
                KEY_BEST_TAKE_OPTIMIZE_INPUTS to unoptimizedList,
                KEY_BEST_TAKE_OPTIMIZE_INPUT_SRC_BITMAPS to srcBitmaps,
                KEY_BEST_TAKE_OPTIMIZE_INPUT_DST_BITMAPS to dstBitmaps
            ),
            KEY_BEST_TAKE_OPTIMIZE_OUTPUTS
        ) { result ->
            val outputMap = result as? Map<*, *>
            val outputJsonList = outputMap?.get(PARAM_KEY_BEST_TAKE_OPTIMIZING_OUTPUT_JSON) as? List<String>
            val dstImgMap = outputMap?.get(PARAM_KEY_BEST_TAKE_OPTIMIZING_OUTPUT_DST_BITMAPS) as? Map<String, Bitmap>
            val jsonToList = outputJsonList?.mapNotNull { JsonHelper.fromJson(it, AIFaceBestFaceInfoMirror::class.java) } ?: emptyList()
            jsonToList.forEach { aiFaceBestFaceInfoMirror ->
                val key = "${aiFaceBestFaceInfoMirror.id}-${aiFaceBestFaceInfoMirror.faceId}"
                dstImgMap?.get(key)?.let {
                    aiFaceBestFaceInfoMirror.dstImg = it
                }
            }
            GLog.d(TAG, LogFlag.DL) { "emitOptimizingCmd result jsonToList = $jsonToList" }
            AIBestTakeTrackHelper.recordErrorCode(jsonToList.map { it.error })
            callback.invoke(jsonToList)
        }
    }

    /**
     * 收集已选中的人脸表情
     * @return 已选中的人脸表情
     */
    private fun getSelectedCoverFaces(): MutableList<CvFaceInfo> {
        val coverFaces = mutableListOf<CvFaceInfo>()
        val faceFrameList = vmBus?.get<List<FaceFrame>>(TOPIC_AI_BEST_TAKE_FACE_FRAMES) ?: return coverFaces

        replaceableFaceMap.onEach { entry ->
            val mainId = entry.key.id
            faceFrameList.find { it.faceID == mainId }?.let { faceFrame ->
                faceFrame.faceContentList.find { it.isSelected }?.let { faceContent ->
                    val oldFace = entry.value.firstOrNull { it.id == faceContent.faceId } ?: return@let
                    // 构建送插件贴图实体
                    coverFaces.add(
                        CvFaceInfo().also {
                            it.id = oldFace.id
                            it.mainId = oldFace.mainId
                            it.cacheTime = oldFace.cacheTime
                            it.groupId = oldFace.groupId
                            it.data = entry.key.data
                            it.rect = getOptimizedRectOnOrigin(oldFace, entry.key.rect)
                            it.thumbWidth = entry.key.thumbWidth
                            it.thumbHeight = entry.key.thumbHeight
                        }
                    )
                    GLog.d(TAG, LogFlag.DL) {
                        "collectSelectedCoverFaces: add coverFace, mainId = $mainId, selectedFaceID = ${faceContent.faceId}"
                    }
                }
            }
        }
        GLog.d(TAG, LogFlag.DL) { "collectSelectedCoverFaces: coverFaces size = ${coverFaces.size}" }
        return coverFaces
    }

    /**
     * 清除所有表情的特效，并且不渲染效果
     */
    private fun clearAllFaceEffect() {
        val param = FindRecordParams(
            stackIndex = STACK_INDEX_THREE,
            findType = RecordFindType.FIND_ALL,
            predicate = { record -> record.effectName == AvEffect.AiBestTakeEffect.name }
        )
        vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS,
            param
        )?.let { operateRecords ->
            operateRecords.forEach { record ->
                editingVM.sessionProxy.removeEffect(record, render = false)
            }
        }
    }

    /**
     * 后台开启优化任务，但不替换表情
     * @param toOptimizedMap 待优化人脸
     */
    private fun optimizeOnBackground(toOptimizedMap: Map<CvFaceInfo, List<CvFaceInfo>>) {
        // 1.装填数据
        bestTakeOptimizer.fillSource(toOptimizedMap)
        // 2.开始优化，无需监听结果
        bestTakeOptimizer.startOptimizing(isAutoOptimizing = false)
    }

    /**
     * 替换表情
     * @param bestTakeFaceList 待替换的表情列表
     */
    private fun replaceFaces(bestTakeFaceList: List<CvFaceInfo>, isAutoOptimize: Boolean = false) {
        AIBestTakeRecord(CMD_BEST_TAKE_REPLACE).apply {
            addArgument(KEY_GROUP_FACE_ID, getGroupFaceId(bestTakeFaceList))
            addArgument(KEY_SHOULD_CACHED_FOR_USER, true)
            targetSize?.let {
                addArgument(KEY_ORIGIN_WIDTH, it.width)
                addArgument(KEY_ORIGIN_HEIGHT, it.height)
            }
            setFaceList(bestTakeFaceList)
            emitRecord(this, OperatingType.RECORD_AND_EFFECT, render = true)
            AIBestTakeTrackHelper.recordRepairFaceNumAndType(bestTakeFaceList, isAutoOptimize)
        }
    }

    /**
     * 手动优化并替换表情
     * @param faceContent 手动选择的预览表情
     */
    @Suppress("LongMethod")
    private fun notifyManualFaceContent(faceContent: FaceContent) {
        if (isFunctionBanned()) {
            publishState(AIBestTakeState.OPTIMIZING_FAIL_BAN)
            GLog.d(TAG, LogFlag.DL) { "[notifyManualFaceContent] AI best take function is banned." }
            return
        }
        manualOptimizingJob?.cancel()
        manualOptimizingJob = null
        timeoutJob?.cancel()
        timeoutJob = null
        manualOptimizingJob = launch {
            var mainCvFaceInfo: CvFaceInfo? = null
            var targetCvFaceInfo: CvFaceInfo? = null
            GLog.d(TAG, LogFlag.DL) { "notifyManualFaceContent  faceContent.mainID:${faceContent.mainFaceID} , faceId:${faceContent.faceId}" }

            run {
                replaceableFaceMap.firstOrNull {
                    it.key.id == faceContent.mainFaceID
                }?.let { entry ->
                    mainCvFaceInfo = entry.key
                    // 选中了原图表情
                    if (faceContent.mainFaceID == faceContent.faceId) {
                        GLog.d(TAG, LogFlag.DL) { "notifyManualFaceContent  select main face. notify loading complete." }
                        // 通知 UI 结束加载动画。
                        faceContentLoadedPub?.publish(faceContent)
                        timeoutJob?.cancel()
                        timeoutJob = null
                        return@launch
                    }
                    // 选中了其他候选表情
                    entry.value.firstOrNull { cvFaceInfo ->
                        cvFaceInfo.id == faceContent.faceId
                    }?.also {
                        targetCvFaceInfo = it
                        if (targetCvFaceInfo?.mainId == 0L) {
                            targetCvFaceInfo?.mainId = entry.key.id
                        }
                        return@run
                    }
                }
            }

            // 找到了点击的参考表情
            targetCvFaceInfo?.let { targetCvFace ->
                // 先找缓存是否有优化后的表情，如果有其它表情优化的，需要构建完整缓存key，如果没有可以使用不带cacheTime的key
                val optBitmap = if (containsOtherBestTakeRecords) {
                    AIBestTakeFaceCache.getOptimizedBitmap(targetCvFace)
                } else {
                    AIBestTakeFaceCache.getOptimizedBitmapLike(targetCvFace)
                }
                var finalCvFace: CvFaceInfo? = null
                if (optBitmap == null) {
                    // 找不到缓存，就通过arg.mainFaceID找到主图，调用 fetchSingleOptimizingResult 方法，获取到优化结果列表。
                    mainCvFaceInfo?.let { mainCvFace ->
                        publishState(AIBestTakeState.REPLACING)
                        val coverFaceList = bestTakeOptimizer.fetchSingleOptimizingResult(mainCvFace, targetCvFace)
                        // 找到 targetCvFaceInfo 对应的那一个表情贴图
                        finalCvFace = coverFaceList.find { (it.id == targetCvFace.id) && (it.groupId == targetCvFace.groupId) }
                        // 检查优化结果是否正常
                        val optimizingState = checkOptimizingError(finalCvFace)
                        if (optimizingState != AIBestTakeState.OPTIMIZING_SUCCESS) {
                            GLog.w(TAG, LogFlag.DL) { "notifyManualFaceContent: optimize failed.[${optimizingState.name}]" }
                            publishState(optimizingState)
                            return@launch
                        }
                    }
                } else {
                    finalCvFace = targetCvFace
                }
                GLog.d(TAG, LogFlag.DL) { "notifyManualFaceContent  select [${finalCvFace?.id}] face. notify loading complete." }
                // 通知 UI 结束加载动画。
                faceContentLoadedPub?.publish(faceContent)
            }
            // 超时job
            timeoutJob?.cancel()
            timeoutJob = null
        }
        // 加个业务的超时逻辑
        timeoutJob = launch {
            delay(MANUAL_OPTIMIZING_TIMEOUT)
            bestTakeSessionProxy.cancelEffect()
            manualOptimizingJob?.cancel()
            manualOptimizingJob = null
            publishState(AIBestTakeState.OPTIMIZING_FAIL_TIMEOUT)
            vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.let {
                AIBestTakeTrackHelper.trackEditException(
                    EXCEPTION_TYPE_GALLERY,
                    OPTIMIZING_CODE_REQUEST_TIMEOUT_GALLERY,
                    "optimizing request timeout gallery",
                    it
                )
            }
        }
    }

    /**
     * 判断当前功能是否被禁用
     * @return true/false 当前功能是否被禁用
     */
    private fun isFunctionBanned(): Boolean {
        // 检查是否是触发过太多次拒识而导致画质增强不能用
        val bannedTime = ConfigAbilityWrapper.getLong(ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_BANNED_TIME, 0)
        GLog.d(TAG, LogFlag.DL) { "isFunctionBaned: bannedTime = $bannedTime , current time = ${System.currentTimeMillis()}" }
        return bannedTime > System.currentTimeMillis()
    }

    /**
     * 替换表情
     * @param faceContent 手动选择的预览表情
     */
    private fun replaceFace(faceContent: FaceContent) {
        launch {
            fun replaceFaceContent(mainCvFaceInfo: CvFaceInfo?, selectedCvFaceInfo: CvFaceInfo?) {
                getCvFaceInfo(mainCvFaceInfo, selectedCvFaceInfo)?.let {
                    val optBitmap = AIBestTakeFaceCache.getOptimizedBitmap(it) ?: AIBestTakeFaceCache.getOptimizedBitmapLike(it)
                    if (optBitmap != null) {
                        replaceFaces(listOf(it))
                        faceContentReplacedPub?.publish(faceContent)
                    } else {
                        GLog.w(TAG, LogFlag.DL) {
                            "replaceFace  fail. Get [${AIBestTakeFaceCache.buildOptimizedCacheKey(it)}] OptimizedBitmap is null."
                        }
                        publishState(AIBestTakeState.OPTIMIZING_FAIL)
                    }
                }
            }

            GLog.d(TAG, LogFlag.DL) { "replaceFace  faceContent.mainID:${faceContent.mainFaceID} , faceId:${faceContent.faceId}" }

            replaceableFaceMap.firstOrNull {
                // 先匹配到 faceContent 的主人脸对象
                it.key.id == faceContent.mainFaceID
            }?.let { entry ->
                val mainCvFaceInfo = entry.key
                if (faceContent.mainFaceID == faceContent.faceId) {
                    // 选中了原图表情
                    GLog.d(TAG, LogFlag.DL) { "replaceFace  select main face." }
                    // 替换表情
                    replaceFaceContent(mainCvFaceInfo, mainCvFaceInfo)
                } else {
                    // 选中了其他候选表情
                    entry.value.firstOrNull { cvFaceInfo ->
                        cvFaceInfo.id == faceContent.faceId
                    }?.also { targetCvFaceInfo ->
                        GLog.d(TAG, LogFlag.DL) { "replaceFace  select [${targetCvFaceInfo.id}] face." }
                        // 替换表情
                        replaceFaceContent(mainCvFaceInfo, targetCvFaceInfo)
                    } ?: { GLog.w(TAG, LogFlag.DL) { "replaceFace  select null face." } }
                }
            }
        }
    }

    /**
     * 手动选择表情时，获取选中的表情贴图信息对象，用于表情贴图。
     * @param mainCvFaceInfo 原始的表情
     * @param selectedCvFace 选中的表情
     */
    private fun getCvFaceInfo(mainCvFaceInfo: CvFaceInfo?, selectedCvFace: CvFaceInfo?): CvFaceInfo? {
        return if ((selectedCvFace != null) && (mainCvFaceInfo != null)) {
            CvFaceInfo().apply {
                id = selectedCvFace.id
                groupId = selectedCvFace.groupId
                mainId = selectedCvFace.mainId
                cacheTime = selectedCvFace.cacheTime
                rect = getOptimizedRectOnOrigin(selectedCvFace, mainCvFaceInfo.rect)
                data = mainCvFaceInfo.data
                thumbWidth = mainCvFaceInfo.thumbWidth
                thumbHeight = mainCvFaceInfo.thumbHeight
            }
        } else {
            null
        }
    }

    /**
     * 将dstRect转换到原图位置
     * @param mainRect 主图在原图的位置
     * @param unionRect dst图在主图的位置
     * @param width 主图宽（相对于预览图）
     * @param height 主图高（相对于预览图）
     * @return dstRect在原图上的位置
     */
    private fun convertDstRectOnOrigin(mainRect: Rect, unionRect: Rect, width: Int, height: Int): Rect {
        if (width <= 0) return mainRect
        if (height <= 0) return mainRect
        val xScale = mainRect.width().toFloat() / width
        val yScale = mainRect.height().toFloat() / height
        val dstRect = RectF(
            unionRect.left * xScale,
            unionRect.top * yScale,
            unionRect.right * xScale,
            unionRect.bottom * yScale
        )
        val rect = RectF()
        rect.left = mainRect.left + dstRect.left
        rect.top = mainRect.top + dstRect.top
        rect.right = rect.left + dstRect.width()
        rect.bottom = rect.top + dstRect.height()
        return rect.toRect()
    }

    /**
     * 将生成图的rect最后转化成原图上的坐标
     * @param selectedCvFace 选中人脸
     * @param mainRect 主图的rect
     */
    private fun getOptimizedRectOnOrigin(selectedCvFace: CvFaceInfo, mainRect: Rect): Rect {
        return AIBestTakeFaceCache.getOptimizedRect(selectedCvFace) ?: AIBestTakeFaceCache.getOptimizedRectLike(selectedCvFace) ?: mainRect
    }

    /**
     * 获取人脸列表的 ID 和 group ID 拼接的字符串，作为特效的缓存匹配条件。
     * 格式：key_group_face_id:1-1,2-1,...
     */
    private fun getGroupFaceId(bestTakeFaceList: List<CvFaceInfo>): String {
        val groupFaceId = StringBuilder(KEY_GROUP_FACE_ID + COLON)
        bestTakeFaceList.forEach {
            groupFaceId.append(it.id).append(STRIKE).append(it.groupId).append(SPLIT_COMMA_SEPARATOR)
        }
        GLog.d(TAG, LogFlag.DL) { "getGroupFaceId  $groupFaceId" }
        return groupFaceId.toString()
    }

    /**
     * 拒识模块统计次数
     */
    private fun countRefuse() {
        app.getAppAbility<ISettingsAbility>()?.use {
            it.markAIFuncBanTime(
                SensitiveFuncBanItem.AI_BEST_TAKE
            )
        }
    }

    private fun Map<CvFaceInfo, List<CvFaceInfo>>.asAnimatingFaceFrames(): List<FaceFrame> {
        val faceFrameList = mutableListOf<FaceFrame>()
        forEach { entry ->
            entry.value.pickBestFace()?.let {
                val faceFrame = entry.asFaceFrame().apply {
                    frameState = FaceFrameState.ANIMATING
                    this.replaceFaceKey = AIBestTakeFaceCache.buildOptimizedCacheKey(it)
                }
                if (faceFrame.faceContentList.size > LEAST_REF_FACE_COUNT) {
                    faceFrameList.add(faceFrame)
                }
            }
        }
        return faceFrameList
    }

    private fun Map<CvFaceInfo, List<CvFaceInfo>>.asFaceFrames(): MutableList<FaceFrame> {
        val faceFrameList = mutableListOf<FaceFrame>()
        forEach { replaceableFaceEntry ->
            val face = replaceableFaceEntry.value
                // 选择有优化结果的图
                .filter { AIBestTakeFaceCache.getOptimizedBitmap(it) != null }
                // 选择表情分数最高的一张去替换
                .pickBestFace()
            val isOrgSelected = (face == null)
            // 当无法找到自动优化结果，使用原始的人脸结果进行添加。
            faceFrameList.add(replaceableFaceEntry.asFaceFrame(isOrgSelected))
        }
        return faceFrameList
    }

    private fun List<CvFaceInfo>.pickBestFace(): CvFaceInfo? {
        return maxByOrNull { it.faceExpressionScore }
    }

    private fun Map.Entry<CvFaceInfo, List<CvFaceInfo>>.asFaceFrame(
        isOrgSelected: Boolean = true
    ): FaceFrame {
        val faceContents = mutableListOf<FaceContent>()
        key.let {
            AIBestTakeFaceCache.getRefBitmap(it)?.let { refBitmap ->
                faceContents.add(
                    FaceContent(
                        mainFaceID = key.id,
                        faceId = it.id,
                        isSelected = isOrgSelected,
                        refFaceKey = AIBestTakeFaceCache.buildRefCacheKey(it)
                    )
                )
            }
        }
        value.withIndex().forEach { (index, cvFaceInfo) ->
            AIBestTakeFaceCache.getRefBitmap(cvFaceInfo)?.let { refBitmap ->
                /**
                 * 当前表情分类已经有数据了，且剩余未挑选的表情足够满足挑选要求
                 * @return true/false true的话break出去
                 */
                fun containsExpressionAndEnoughForPick(): Boolean {
                    val remainSize = value.size - index - 1
                    return faceContents.any {
                        it.expressionDetail == cvFaceInfo.expressionDetail
                    } && ((faceContents.size + remainSize) > EXPECT_REF_FACE_COUNT)
                }

                // 超过上限或者（已经有该类型的表情且剩余表情足够后续挑选）
                if ((faceContents.size > EXPECT_REF_FACE_COUNT) || containsExpressionAndEnoughForPick()) {
                    GLog.d(TAG, LogFlag.DL) { "asFaceFrame: exceed max ref count, no need to fill in data." }
                    return@forEach
                }
                faceContents.add(
                    FaceContent(
                        mainFaceID = key.id,
                        faceId = cvFaceInfo.id,
                        isSelected = !isOrgSelected && (index == 0), // 0 表示默认选中第一个自动优化后的表情
                        refFaceKey = AIBestTakeFaceCache.buildRefCacheKey(cvFaceInfo),
                        expressionDetail = cvFaceInfo.expressionDetail
                    )
                )
            }
        }
        val faceRect = targetSize?.let { key.getTargetRectF(it).toRect() } ?: Rect()
        return FaceFrame(
            faceID = key.id,
            groupId = key.groupId,
            faceRect = faceRect,
            previewSize = AIBestTakeUtils.getFaceSizeOnPreview(faceRect, targetSize, thumbnailSizeF, previewDisplayRectF),
            frameState = FaceFrameState.NORMAL,
            faceContentList = faceContents
        )
    }

    internal companion object {
        const val TAG = "AIBestTakeVM"

        /**
         * 最大人脸检测数，高于20个，则不支持优化
         */
        private const val MAX_SUPPORT_FACE_COUNT = 20
        private const val AUTO_OPTIMIZING_SIZE = 6
        private const val MAX_BACKGROUND_OPTIMIZING_SIZE = 8
        private const val CMD_BEST_TAKE_VERIFY = "cmd_best_take_verify"
        private const val CMD_BEST_TAKE_OPTIMIZE = "cmd_best_take_optimize"
        private const val CMD_BEST_TAKE_REPLACE = "cmd_best_take_replace"
        private const val KEY_BEST_TAKE_VERIFY_INPUTS = "key_best_take_verify_inputs"
        private const val KEY_BEST_TAKE_VERIFY_OUTPUTS = "key_best_take_verify_outputs"
        private const val KEY_BEST_TAKE_OPTIMIZE_INPUTS = "key_best_take_optimize_inputs"
        private const val KEY_BEST_TAKE_OPTIMIZE_INPUT_SRC_BITMAPS = "key_best_take_optimize_input_src_bitmaps"
        private const val KEY_BEST_TAKE_OPTIMIZE_INPUT_DST_BITMAPS = "key_best_take_optimize_input_dst_bitmaps"
        private const val KEY_BEST_TAKE_OPTIMIZE_OUTPUTS = "key_best_take_optimize_outputs"
        private const val PARAM_KEY_BEST_TAKE_OPTIMIZING_OUTPUT_JSON = "key_best_take_optimizing_output_json"
        private const val PARAM_KEY_BEST_TAKE_OPTIMIZING_OUTPUT_DST_BITMAPS = "key_best_take_optimizing_output_dst_bitmaps"
        private const val KEY_GROUP_FACE_ID = "key_group_face_id"
        private const val MANUAL_OPTIMIZING_TIMEOUT = 60000L

        const val KEY_ORIGIN_WIDTH = "key_origin_width"
        const val KEY_ORIGIN_HEIGHT = "key_origin_height"

        /**
         * 表示请求成功
         */
        const val OPTIMIZING_CODE_SUCCESS = 0

        /**
         * 识别成功
         */
        const val ERROR_CODE_SUCCESS = 0

        /**
         * 人脸尺寸过小
         */
        const val RECOGNIZE_CODE_FACE_SMALL = 3001001

        /**
         * 人脸范围超出区域
         */
        const val RECOGNIZE_CODE_FACE_RANGE_ERROR = 3001002

        /**
         * 相册端兜底请求超时
         */
        const val OPTIMIZING_CODE_REQUEST_TIMEOUT_GALLERY = 250


        /**
         * 无效的图片数据
         */
        private const val OPTIMIZING_CODE_INVALID_IMAGE = 3000400

        /**
         * 重复id
         */
        private const val OPTIMIZING_CODE_DUPLICATE_ID = 3000405

        /**
         * 算法模型无法处理图片数据
         */
        private const val OPTIMIZING_CODE_UNPROCESSABLE_IMAGE = 3000505

        /**
         * 图片检测错误（拒识）
         */
        private const val OPTIMIZING_CODE_DETECT_ERROR = 3000600

        /**
         * 图像检测结果不通过（拒识）：对应敏感内容拒识等场景
         */
        private const val OPTIMIZING_CODE_DETECT_SENSITIVE_CONTENT = 3000603

        /**
         * 加解密异常
         */
        private const val OPTIMIZING_CODE_ENCRYPT_ERROR = 3000509

        /**
         * 请求超时
         */
        private const val OPTIMIZING_CODE_REQUEST_TIMEOUT = 3000502

        /**
         * 服务端未知错误
         */
        private const val OPTIMIZING_CODE_UNKNOWN_ERROR = 3000500

        /**
         * 请求次数过多
         */
        private const val OPTIMIZING_CODE_DAILY_REQUEST_LIMIT = 3000429

        /**
         * 当前请求接口人数过多
         */
        private const val OPTIMIZING_CODE_BUSY = 429

        /**
         * 网络异常
         */
        private const val OPTIMIZING_CODE_NETWORK_ERROR = 504

        /**
         * 最佳表情耗时日志，打印通用搜索字段
         */
        const val BEST_TAKE_COMMON = "[BestTakeTimeCosting]"
    }
}

private fun CvFaceInfo.asAIFaceInfoMirror(): AIFaceInfoMirror {
    var attr = FaceAttrInfoMirror()
    var embedding: FloatArray? = null
    // 这里纯数学计算，开销可忽略不计
    ContextGetter.context.getAppAbility<IFaceScanAbility>()?.use { ability ->
        ability.newFaceScoreCalculator().use { calculator ->
            attr = calculator.calcExpressionParams(this)
            feature?.let {
                embedding = calculator.getDeserializeFeature(it)
            }
        }
    }
    return asAIFaceInfoMirror(this, attr, embedding)
}

private fun Map<CvFaceCluster, List<CvFaceCluster>>.asAIFaceInfoMirrorMap(): Map<String, List<String>> {
    val map = mutableMapOf<String, List<String>>()
    onEach {
        val key = JsonHelper.toJson(it.key.asAIFaceInfoMirror()) ?: EMPTY_STRING
        map[key] = it.value.mapNotNull { value -> JsonHelper.toJson(value.asAIFaceInfoMirror()) }
    }
    return map
}

/**
 * 主图+参考图，两两组合，构建一个待优化的AIFaceGroupInfoMirror列表
 * @return AIFaceGroupInfoMirror列表
 */
private fun Map<CvFaceInfo, List<CvFaceInfo>>.asAIFaceGroupInfoMirrorList(): List<AIFaceGroupInfoMirror> {
    val toOptimizedList = mutableListOf<AIFaceGroupInfoMirror>()
    this.onEach {
        val dstImage = AIBestTakeFaceCache.getRefBitmap(it.key)?.let { old ->
            old.copy(old.getConfigSafely(), old.isMutable)
        } ?: let { return@onEach }
        val dstPoints = it.key.faceLandmark
        val dstRect = it.key.rect
        val dstHappyScore = it.key.happyScore
        it.value.onEach { face ->
            val srcImage = AIBestTakeFaceCache.getRefBitmap(face)?.let { old ->
                old.copy(old.getConfigSafely(), old.isMutable)
            } ?: AIBestTakeUtils.cropOriginFace(face)
            if (srcImage == null) {
                GLog.w(TAG, LogFlag.DL) { "asAIFaceGroupInfoMirrorList: srcImage is null" }
            } else {
                val srcPoints = face.faceLandmark
                val groupInfo = AIFaceGroupInfoMirror(
                    // 主图id来作faceId
                    faceId = it.key.id.toInt(),
                    id = AIBestTakeUtils.generateFaceId(face),
                    dstImage = dstImage,
                    dstRect = dstRect,
                    dstPoints = dstPoints as? Array<PointF?>,
                    srcImage = srcImage,
                    srcPoints = srcPoints as? Array<PointF?>,
                    dstBitmapTag = EMPTY_STRING,
                    dstHappyScore = dstHappyScore,
                    srcHappyScore = face.happyScore,
                    srcBitmapTag = EMPTY_STRING,
                    // 默认全脸替换
                    isAutoEdit = false
                )
                toOptimizedList.add(groupInfo)
            }
        }
    }
    return toOptimizedList
}

/**
 * 表情优化record
 */
internal class AIBestTakeRecord(
    command: String
) : NonParametricRecord(
    cmd = AvEffect.AiBestTakeEffect.name,
    arguments = arrayMapOf(GLOBAL_KEY_EXECUTE_COMMAND to command)
) {
    private val cvFaceInfoList = mutableListOf<CvFaceInfo>()
    private var isCacheable = false

    /**
     * resultFloatArrays 用于作为算法相关的数据
     * FloatArray即使数组里面内容相同  它hash也不一样 这里把他存起来 手动判断 如果内容一致 就用保存过的数组
     */
    private val resultFloatArrays: SparseArray<FloatArray> = SparseArray()

    override val isModifiable: Boolean = false

    /**
     * 是否进行表情优化操作
     */
    var isAiBestTakeEnhanced = false

    /**
     * 设置是否允许保存 bestTakeFaceCache 缓存
     */
    fun setCacheable(value: Boolean) {
        isCacheable = value
    }

    private fun convertCvFaceInfoList(cvFaceInfoList: List<CvFaceInfo>): List<Pair<Bitmap, FloatArray>> {
        val resultList = mutableListOf<Pair<Bitmap, FloatArray>>()
        cvFaceInfoList.forEach { cvFaceInfo ->
            (AIBestTakeFaceCache.getOptimizedBitmap(cvFaceInfo) ?: AIBestTakeFaceCache.getOptimizedBitmapLike(cvFaceInfo))?.let { bitmap ->
                GLog.d(TAG, LogFlag.DL) {
                    "convertCvFaceInfoList: get optimized pic, w = ${bitmap.width} , h = ${bitmap.height} ," +
                            " colorspace = ${bitmap.colorSpace} rect = ${cvFaceInfo.rect}"
                }
                val previewBitmapSize = Size(arguments[KEY_ORIGIN_WIDTH] as Int, arguments[KEY_ORIGIN_HEIGHT] as Int)
                var floatArray = AIBestTakeUtils.getVertexCoordinates(cvFaceInfo.getTargetRectF(previewBitmapSize).toRect())
                val array = resultFloatArrays.get(floatArray.contentHashCode())
                if (array != null) {
                    floatArray = array
                } else {
                    resultFloatArrays.put(floatArray.contentHashCode(), floatArray)
                }
                resultList.add(Pair(bitmap, floatArray))
            }
        }
        return resultList.toList()
    }

    /**
     * 给人脸列表赋值。该人脸列表在生成效果时，需要用它来转换成对应的 bitmap 和顶点坐标等参数，传递给插件进行贴脸运算。
     */
    fun setFaceList(list: List<CvFaceInfo>) {
        cvFaceInfoList.clear()
        cvFaceInfoList.addAll(list)
        resultFloatArrays.clear()
    }

    /**
     * 获取人脸列表
     */
    fun getFaceList(): List<CvFaceInfo> = cvFaceInfoList.toList()

    fun addArgument(key: String, value: Any) {
        arguments[key] = value
    }

    /**
     * 在点击完成时，会清理所有换脸特效，然后重新生成选中的表情合集的特效。因此这里不再需要实现合并策略。
     */
    override fun plusAssign(operatingRecord: OperatingRecord) = Unit

    override fun argumentsForAlgo(algoArguments: List<AvEffect.Argument>?): Map<String, Any> {
        val result = mutableMapOf<String, Any>()
        result.putAll(super.argumentsForAlgo(algoArguments))
        result[KEY_BEST_TAKE_FACES] = convertCvFaceInfoList(cvFaceInfoList)
        return result
    }

    companion object {
        private const val TAG = "AIBestTakeRecord"
        private const val KEY_BEST_TAKE_FACES: String = "key_best_take_faces"
    }
}