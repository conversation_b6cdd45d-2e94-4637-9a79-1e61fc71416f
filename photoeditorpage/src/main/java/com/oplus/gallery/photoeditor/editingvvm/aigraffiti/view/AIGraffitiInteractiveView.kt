/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIGraffitiInteractiveView.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2024/10/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2024/10/8       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aigraffiti.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.RelativeLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import com.coui.appcompat.button.COUIButton
import com.oplus.gallery.foundation.util.display.ScreenUtils.isLand
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.util.ThemeColorConfigHelper

class AIGraffitiInteractiveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr, defStyleRes) {

    val aiGraffitiTipsButton: ImageButton by lazy {
        findViewById(R.id.ai_graffiti_tips_button)
    }

    val aiGraffitiGenerateButtonView: View by lazy {
        findViewById(R.id.btn_ai_graffiti_layout)
    }

    val aiGraffitiGenerateButton: COUIButton by lazy {
        findViewById(R.id.btn_ai_graffiti)
    }

    val aiGraffitiAdjustmentBar: View by lazy {
        findViewById(R.id.editor_id_adjustment)
    }

    init {
        val layoutId = if (context.isLand()) {
            R.layout.picture3d_editor_ai_graffiti_interactive_view_landscape // 横屏布局
        } else {
            R.layout.picture3d_editor_ai_graffiti_interactive_view // 竖屏布局
        }
        LayoutInflater.from(context).inflate(layoutId, this, true)
        aiGraffitiGenerateButton.apply {
            //配置按钮主题色
            drawableColor = ThemeColorConfigHelper.getThemeColor(context)
        }
    }

    /**
     * 引导按钮是否启用
     */
    var isTipsButtonEnable
        get() = aiGraffitiTipsButton.isEnabled
        set(value) {
            aiGraffitiTipsButton.isEnabled = value
        }

    fun setOnTipsClickListener(listener: OnClickListener?) {
        aiGraffitiTipsButton.setOnClickListener(listener)
    }

    fun setOnGenerateButtonClickListener(listener: OnClickListener?) {
        aiGraffitiGenerateButton.setOnClickListener(listener)
    }

    fun setOnAdjustmentBarClickListener(listener: OnClickListener?) {
        aiGraffitiAdjustmentBar.setOnClickListener(listener)
    }

    companion object {
        private const val TAG = "AIGraffitiInteractiveView"
    }
}