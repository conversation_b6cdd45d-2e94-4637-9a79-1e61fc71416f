/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetDisplayListViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/01/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/01/19      1.0          Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.displaylist

import android.app.Application
import com.oplus.gallery.framework.abilities.data.model.WidgetDisplayListModel
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.viewmodel.AlbumViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class WidgetDisplayListViewModel(application: Application) : AlbumViewModel(application) {

    suspend fun getItemPaths(start: Int, count: Int): List<Path>? = withContext(Dispatchers.IO) {
        if (count > 0) {
            model?.getItems(start, count)?.mapNotNull { item -> item.path }
        } else {
            emptyList()
        }
    }

    suspend fun getCount(): Int {
        return model?.getCount() ?: 0
    }

    fun addToDisplayListByGalleryId(galleryIds: List<Int>, displayListId: String): Int {
        return (model as WidgetDisplayListModel).addToDisplayListByGalleryId(galleryIds, displayListId)
    }

    fun updateWidgetMode(mode: Int): Boolean {
        return (model as WidgetDisplayListModel).updateWidgetMode(mode)
    }

    override fun getTag() = TAG

    companion object {
        private const val TAG = "WidgetDisplayListViewModel"
    }
}