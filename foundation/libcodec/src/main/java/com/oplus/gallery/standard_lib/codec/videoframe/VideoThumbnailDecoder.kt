/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : VideoThumbnailDecoder.kt
 ** Description : Decoder which get video thumbnail.
 ** Version     : 1.0
 ** Date        : 2020/08/31
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 * Yong<PERSON><PERSON>.<PERSON>@Apps.Gallery3D           2019/11/26   1.0         build this module
 * duchengsong@Apps.Gallery3D          2020/08/17   2.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.standard_lib.codec.videoframe

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import java.io.Closeable
import java.io.FileDescriptor

abstract class VideoThumbnailDecoder : Closeable {
    companion object {
        const val NO_SCALE = 1.0f
        const val INVALID_SIZE = -1
    }

    /**
     * 设置数据源
     * @param path 文件路径
     */
    abstract fun setDataSource(path: String)

    /**
     * 设置数据源
     * @param fd 文件描述符
     */
    abstract fun setDataSource(fd: FileDescriptor)


    /**
     * 设置数据源
     * @param context Context
     * @param  uri Uri
     */
    abstract fun setDataSource(context: Context, uri: Uri)

    /**
     * 通过文件描述符，进行偏移后设置资源
     *
     * @param fd 文件描述符
     * @param offset 视频所在位置的偏移量
     * @param length 视频总长度
     */
    abstract fun setDataSource(fd: FileDescriptor, offset: Long, length: Long)

    /**
     * 在获取缩略图之前设置该参数
     * 取出的缩略图将被缩放为最短边size的大小，如果缩略图的宽高解析失败，将保持取全图大小
     *
     * @param size 接下来要获取的缩略图的尺寸,仅大于0时有效
     */
    abstract fun setThumbnailSize(size: Int)

    /**
     * 解码视频，获得某一帧图片
     *
     * @param timeUs 时间戳，指定要解码的帧的时间
     * @param size 位图的大小
     *
     * @return 返回的圖片
     */
    abstract fun decodeFrameBitmapAtTime(timeUs: Long, size: Int = -1): Bitmap?

    /**
     * 抽帧视频中的某一帧
     * @param timeUs 时间，单位为微秒
     * @param size 大小
     * @param optionFlag OptionFlag
     * @return Bitmap
     */
    abstract fun decodeFrameBitmapAtTime(timeUs: Long, size: Int = -1, optionFlag: OptionFlag): Bitmap?
    /**
     * 解码器是否有效
     */
    abstract fun isValid(): Boolean

    open fun decodeCoverBitmap(size: Int = -1): Bitmap? = decodeFrameBitmapAtTime(0, size)
}

enum class OptionFlag {
    /**
     * 前一个关键帧
     */
    OPTION_PREVIOUS_SYNC,

    /**
     * 后一个关键帧
     */
    OPTION_NEXT_SYNC,

    /**
     * 最近的关键帧，不管前后
     */
    OPTION_CLOSEST_SYNC,

    /**
     * 最近的帧，不一定是关键帧
     */
    OPTION_CLOSEST
}