/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - IImageDecoder.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2020/10/02
 ** Author      : YongQi.Liu@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQi.Liu@Apps.Gallery3D  2020/10/02  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.standard_lib.codec

import android.graphics.BitmapFactory
import java.io.FileDescriptor
import java.io.InputStream


interface IImageDecoder {

    fun createDecoder()

    fun destroyDecoder()

    fun isYuvImage(fd: Int): Boolean

    fun decode(fd: FileDescriptor): ImageData?

    fun decode(fd: FileDescriptor, options: BitmapFactory.Options?): ImageData?

    fun decode(inputStream: InputStream): ImageData?

    fun decode(inputStream: InputStream, options: BitmapFactory.Options?): ImageData?

    fun decode(filePath: String): ImageData?

    fun decode(filePath: String, options: BitmapFactory.Options?): ImageData?

    fun getDecoderName(): String
}