/*******************************************************
 * Copyright 2010 - 2012 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description    :
 * History      :
 * (ID, Date, Author, Description)
 *
 *******************************************************/
package com.oplus.gallery.standard_lib.codec;

import android.graphics.Bitmap;

import com.oplus.decoder.Movie;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.io.BufferedInputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 * <p>
 * 2012-7-24
 */
public class GifDecoder {
    private static final String TAG = "GifDecoder";
    // private static final String TAG = "GifDecoder";
    /**
     * Specify the minimal frame duration in GIF file, unit is ms. Set as 100,
     * then gif animation hehaves mostly as other gif viewer.
     */
    // private static final int MINIMAL_DURATION = 100;

    /**
     * Invalid returned value of some memeber function, such as getWidth()
     * getTotalFrameCount(), getFrameDuration()
     */
    public static final int INVALID_VALUE = 0;
    public static final int SIZE = 8 * 1024;
    public static final int READLIMIT = 1024;

    /**
     * Movie object maitained by GifDecoder, it contains raw GIF info like
     * graphic control informations, color table, pixel indexes, called when
     * application is no longer interested in gif info. It is contains GIF frame
     * bitmap, 8-bits per pixel, using SkColorTable to specify the colors, which
     * is much memory efficient than ARGB_8888 config. This is why we maintain a
     * Movie object rather than a set of ARGB_8888 Bitmaps.
     */
    private Movie mMovie;

    /**
     * Constructor of GifDecoder, which receives file path name as parameter.
     * Decode a file path into Movie object. If the specified file name is null,
     * no decoding will be performed
     *
     * @param pathName complete path name for the file to be decoded.
     */
    public GifDecoder(String pathName) {
        if (pathName == null) {
            return;
        }

        mMovie = openFile(pathName);
    }

    public GifDecoder(InputStream stream) {
        if (stream == null) {
            return;
        }
        mMovie = openInputStream(stream);
    }

    public static GifDecoder createGifDecoder(String pathName) {
        GifDecoder gifDecoder = new GifDecoder(pathName);
        if (!gifDecoder.isGifStream()) {
            gifDecoder.close();
            gifDecoder = null;
        }
        return gifDecoder;
    }

    public static GifDecoder createGifDecoder(InputStream stream) {
        GifDecoder gifDecoder = new GifDecoder(stream);
        if (!gifDecoder.isGifStream()) {
            gifDecoder.close();
            gifDecoder = null;
        }
        return gifDecoder;
    }

    private Movie openFile(String pathName) {
        InputStream is = null;
        try {
            OpenFileRequest openFileRequest = new OpenFileRequest.Builder()
                .setFile(new File(pathName))
                .setImage(true)
                .builder();
            is = FileAccessManager.getInstance().getInputStream(ContextGetter.context, openFileRequest);
            return openInputStream(is);
        } catch (java.io.FileNotFoundException e) {
            GLog.e(TAG, "openFile" + e);
        } finally {
            IOUtils.closeQuietly(is);
        }
        return null;
    }

    private Movie openInputStream(InputStream is) {
        if (is == null) {
            return null;
        }
        Movie moov = null;
        try {
            if (!is.markSupported()) {
                is = new BufferedInputStream(is, SIZE);
            }
            is.mark(READLIMIT);
            moov = Movie.openInputStream(is);
        } catch (Exception e) {
            GLog.e(TAG, "openInputStream", e);
        } finally {
            IOUtils.closeQuietly(is);
        }
        return moov;
    }

    /**
     * Close gif file, release all informations like frame count, graphic
     * control informations, color table, pixel indexes, called when application
     * is no longer interested in gif info. It will release all the memory
     * mMovie occupies. After close() is call, GifDecoder should no longer been
     * used.
     */
    public synchronized void close() {
        if (!isMovieValid()) {
            return;
        }
        mMovie.close();
        mMovie = null;
    }

    public synchronized boolean isGifStream() {
        if (!isMovieValid()) {
            return false;
        }
        return mMovie.isGifStream();
    }

    /**
     * Get width of images in gif file. if member mMovie is null, returns
     * INVALID_VALUE
     *
     * @return The total frame count of gif file, or INVALID_VALUE if the mMovie
     * is null
     */
    public synchronized int getWidth() {
        if (!isMovieValid()) {
            return INVALID_VALUE;
        }
        return mMovie.width();
    }

    /**
     * Get height of images in gif file. if member mMovie is null, returns
     * INVALID_VALUE
     *
     * @return The total frame count of gif file, or INVALID_VALUE if the mMovie
     * is null
     */
    public synchronized int getHeight() {
        if (!isMovieValid()) {
            return INVALID_VALUE;
        }
        return mMovie.height();
    }

    /**
     * Get total frame count of gif file. if member mMovie is null, returns
     * INVALID_VALUE
     *
     * @return The total frame count of gif file, or INVALID_VALUE if the mMovie
     * is null
     */
    public synchronized int getTotalFrameCount() {
        if (!isMovieValid()) {
            return INVALID_VALUE;
        }
        return mMovie.getTotalFrameCount();
    }

    /**
     * Get frame duration specified with frame index of gif file. if member
     * mMovie is null, returns INVALID_VALUE
     *
     * @param frameIndex index of frame interested.
     * @return The duration of the specified frame, or INVALID_VALUE if the
     * mMovie is null
     */
    public synchronized int getFrameDuration(int frameIndex) {
        if (!isMovieValid()) {
            return INVALID_VALUE;
        }
        int duration = mMovie.getFrameDuration(frameIndex);
        // if (duration < MINIMAL_DURATION) {
        // duration = MINIMAL_DURATION;
        // }
        return duration;
    }

    public synchronized void setLimit(int limit) {
        if (!isMovieValid()) {
            return;
        }
        mMovie.setLimit(limit);
    }

    public synchronized void setLimit(int widthLimit, int heightLimit) {
        if (!isMovieValid()) {
            return;
        }
        mMovie.setLimit(widthLimit, heightLimit);
    }

    public synchronized Bitmap getFrameBitmap(int frameIndex) {
        if (!isMovieValid()) {
            return null;
        }
        return mMovie.getBitmap(frameIndex);
    }

    private boolean isMovieValid() {
        if ((mMovie == null) || !mMovie.isGifStream()) {
            return false;
        } else {
            return true;
        }
    }
}
