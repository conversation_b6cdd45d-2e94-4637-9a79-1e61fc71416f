/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoEditorGridSystemUtils.kt
 * Description:
 * Version: 1.0
 * Date: 2022/3/1
 * Author: Luya<PERSON>.Tan@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Luyao.Tan@Apps.Gallery3D 2022/3/1     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 **************************************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.config

import android.content.Context
import android.view.View
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.display.ScreenUtils.toDp
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import kotlin.math.max

object VideoEditorUIConfig {
    /**
     * 窗口高度 540dp
     */
    const val WINDOW_HEIGHT_540 = 540
    const val SCALE_DEFAULT = 1f
    const val FORM_COMMON_PAGE = 1
    const val FORM_TRIM_PAGE = 2
    private const val TOOLBAR_SCALE_IN_MICRO_WINDOW = 0.9f
    private const val TOOLBAR_TRIM_SCALE = 0.7f

    /**
     * 栅格数量
     */
    private const val ROW_COLUMN_NUM_T0 = 4
    private const val ROW_COLUMN_NUM_T1 = 8
    private const val ROW_COLUMN_NUM_T2 = 12


    /**
     * 工具栏需要占用的栅格数量
     *  适用于：
     *      回忆编辑（进度条长度，片长时间选择器）
     * 备注：这里的宽度/高度（最长边）不是窗口的，而是工具栏实际占位view的宽度/高度（最长边）
     * 300(包含)-540  分4行(列)    占4行(列)  间隔8     SPAN_SIZE_T0
     * 540(包含)-840  分8行(列)    占6行(列)  间隔8     SPAN_SIZE_T1
     * 840及以上      分12行(列)   占8行(列)   间隔8     SPAN_SIZE_T2
     */
    private const val SPAN_SIZE_T0 = 4
    private const val SPAN_SIZE_T1 = 6
    private const val SPAN_SIZE_T2 = 8

    /**
     * 工具栏需要占用的栅格数量
     *  适用于：
     *      视频编辑（首页，剪辑，裁剪旋转，主题，滤镜，配乐，特效，倍数，文本）
     *      回忆编辑（首页，主题，音乐，照片）
     * 备注：这里的宽度/高度（最长边）不是窗口的，而是工具栏实际占位view的宽度/高度（最长边）
     * 300(包含)-540  分4行(列)    占4行(列)  间隔8     SPAN_TOOLBAR_SIZE_T0
     * 540(包含)-840  分8行(列)    占8行(列)  间隔8     SPAN_TOOLBAR_SIZE_T1
     * 840及以上      分12行(列)   占8行(列)   间隔8     SPAN_TOOLBAR_SIZE_T2
     */
    private const val SPAN_TOOLBAR_SIZE_T0 = 4
    private const val SPAN_TOOLBAR_SIZE_T1 = 8
    private const val SPAN_TOOLBAR_SIZE_T2 = 8

    /**
     * 栅格间距
     */
    private const val GAP = 8
    private const val EDGE_24DP = 24

    /**
     * 工具栏区域：toolBar默认的栅格断点值
     */
    private const val TOOLBAR_T1 = 540
    private const val TOOLBAR_T2 = 840

    /**
     * 工具栏区域：特殊界面断点值：裁剪，文字 , 回忆进度条等
     */
    private const val TOOLBAR_TRIM_T1 = 600
    private const val TOOLBAR_TRIM_T2 = 840

    /**
     * window：栅格断点
     */
    private const val WINDOW_T1 = 600
    private const val WINDOW_T2 = 840
    private const val WINDOW_T4 = 400
    private const val WINDOW_TRIM_T0 = 540

    private enum class GridLevel {
        T0, T1, T2
    }

    private var leftEdgeViewWidth = 0
    private var rightEdgeViewWidth = 0

    @JvmStatic
    private fun getRowColumnNum(level: GridLevel) = when (level) {
        GridLevel.T0 -> ROW_COLUMN_NUM_T0
        GridLevel.T1 -> ROW_COLUMN_NUM_T1
        GridLevel.T2 -> ROW_COLUMN_NUM_T2
    }

    /**
     * 获取栅格数量
     */
    @JvmStatic
    private fun getRowColumnSize(size: Int, rowColumnNum: Int, gap: Int = GAP, edge: Int): Float {
        return (size - edge - (gap.toFloat() * (rowColumnNum - 1))) / rowColumnNum
    }

    @JvmStatic
    private fun getGridLevel(size: Int) = when {
        size < TOOLBAR_T1 -> GridLevel.T0
        size < TOOLBAR_T2 -> GridLevel.T1
        else -> GridLevel.T2
    }

    @JvmStatic
    private fun getContentSpanSize(level: GridLevel) = when (level) {
        GridLevel.T0 -> SPAN_SIZE_T0
        GridLevel.T1 -> SPAN_SIZE_T1
        GridLevel.T2 -> SPAN_SIZE_T2
    }

    /**
     * 根据栅格的级别，及安全区域，获取toobar区域的宽/高
     * 返回和计算单位都是dp
     */
    @JvmStatic
    private fun getContentSize(level: GridLevel, viewSize: Int, edge: Int = 0): Int {
        val spanSize = getContentSpanSize(level)
        val rowColumnNum = getRowColumnNum(level)
        val rowColumnSize = getRowColumnSize(size = viewSize, rowColumnNum = rowColumnNum, edge = edge)
        return (rowColumnSize * spanSize + GAP * (spanSize - 1)).toInt()
    }

    /**
     * 回忆编辑界面，进度条宽度
     * 计算的单位是dp
     */
    @JvmStatic
    fun getTimeSeekBarWidth(windowWidthDp: Int): Int {
        val gridLevel = getToolBarGridLevelForTrim(windowWidthDp)
        val edge = if (gridLevel == GridLevel.T0) 0 else EDGE_24DP * NUMBER_2
        return getContentSize(gridLevel, windowWidthDp, edge)
    }

    /**
     * 回忆编辑界面 - 片长时长选择器长度 单位：dp
     */
    @JvmStatic
    fun getPickerSelectedViewWidthForTimeSet(windowWidthDp: Int): Int {
        val gridLevel = getToolBarGridLevelForTrim(windowWidthDp)
        val edge = if (gridLevel == GridLevel.T0) 0 else EDGE_24DP * NUMBER_2
        return getContentSize(gridLevel, windowWidthDp, edge)
    }

    @JvmStatic
    private fun getToolBarGridLevel(size: Int) = when {
        size < TOOLBAR_T1 -> GridLevel.T0
        size < TOOLBAR_T2 -> GridLevel.T1
        else -> GridLevel.T2
    }

    @JvmStatic
    private fun getToolBarContentSpanSize(level: GridLevel) = when (level) {
        GridLevel.T0 -> SPAN_TOOLBAR_SIZE_T0
        GridLevel.T1 -> SPAN_TOOLBAR_SIZE_T1
        GridLevel.T2 -> SPAN_TOOLBAR_SIZE_T2
    }

    /**
     * 根据栅格的级别，及安全区域，获取toobar区域的宽/高
     * 返回和计算单位都是dp
     */
    @JvmStatic
    private fun getToolBarContentSize(level: GridLevel, viewSize: Int, edge: Int = 0): Int {
        val spanSize = getToolBarContentSpanSize(level)
        val rowColumnNum = getRowColumnNum(level)
        val rowColumnSize = getRowColumnSize(size = viewSize, rowColumnNum = rowColumnNum, edge = edge)
        return (rowColumnSize * spanSize + GAP * (spanSize - 1)).toInt()
    }

    /**
     * 计算的单位是dp
     */
    @JvmStatic
    fun getToolBarContentSize(toolBarSize: Int, isLandscape: Boolean): Int {
        val gridLevel = getToolBarGridLevel(toolBarSize)
        val edge = if (gridLevel != GridLevel.T0) EDGE_24DP * NUMBER_2 else 0
        return getToolBarContentSize(gridLevel, toolBarSize, edge)
    }


    @JvmStatic
    fun isToolBarGridLevelT0(toolBarSize: Int): Boolean = getToolBarGridLevel(toolBarSize) == GridLevel.T0

    private fun getToolBarGridLevelForTrim(viewSize: Int) = when {
        viewSize < TOOLBAR_TRIM_T1 -> GridLevel.T0
        viewSize < TOOLBAR_TRIM_T2 -> GridLevel.T1
        else -> GridLevel.T2
    }

    /**
     * 计算的单位是dp
     */
    @JvmStatic
    fun getToolBarContentWidthForTrim(windowWidth: Int): Int {
        val gridLevel = getToolBarGridLevelForTrim(windowWidth)
        return getToolBarContentSize(gridLevel, windowWidth)
    }

    /**
     * 通过缩放比来判定使用中屏占8栏还是占6栏
     * 计算的单位是dp
     * @param windowWidth 窗口宽度
     * @param scale 缩放比
     * @return 实际宽度
     */
    @JvmStatic
    fun getToolBarContentWidthForTrimSize(windowWidth: Int, scale: Float = SCALE_DEFAULT): Int {
        val gridLevel = getToolBarGridLevelForTrim(windowWidth)
        return if (scale < SCALE_DEFAULT) getToolBarContentSize(gridLevel, windowWidth) else getContentSize(gridLevel, windowWidth)
    }

    /**
     * 中屏占4栏，大屏占6栏，要比通常情况小一号
     * 计算的单位是dp
     * 目前用于视频编辑 滤镜和调节的seekbar宽度适配
     * @param windowWidth 窗口宽度
     * @return 实际宽度
     */
    @JvmStatic
    fun getSmallLevelContentSize(windowWidth: Int): Int {
        val gridLevel = getToolBarGridLevelForTrim(windowWidth)
        val spanSize = getSmallLevelContentSpanSize(gridLevel)
        val rowColumnNum = getSmallLevelRowColumnNum(gridLevel)
        val rowColumnSize = getRowColumnSize(size = windowWidth, rowColumnNum = rowColumnNum, edge = 0)
        return (rowColumnSize * spanSize + GAP * (spanSize - 1)).toInt()
    }

    /**
     * 根据等级返回占用的栅格数
     */
    @JvmStatic
    private fun getSmallLevelContentSpanSize(level: GridLevel) = when (level) {
        GridLevel.T0,
        GridLevel.T1 -> SPAN_SIZE_T0
        GridLevel.T2 -> SPAN_SIZE_T1
    }

    /**
     * 根据等级返回栅格总数
     */
    @JvmStatic
    private fun getSmallLevelRowColumnNum(level: GridLevel) = when (level) {
        GridLevel.T0,
        GridLevel.T1 -> ROW_COLUMN_NUM_T1
        GridLevel.T2 -> ROW_COLUMN_NUM_T2
    }

    /**
     * 根据窗口宽度获取安全边距
     * 1.当屏幕宽度在 600（包含）与840之间时为12dp
     * 2.当屏幕宽度在 840以上时为24dp
     * 3.600以下为0
     */
    @JvmStatic
    fun getDefSafeWidth(config: AppUiResponder.AppUiConfig): Int {
        return if ((config.windowWidth.current.toDp >= WINDOW_T1) && (config.windowWidth.current.toDp < WINDOW_T2)) {
            EditorUIConfig.safeEdgeWidthDefaultLandscape
        } else if (config.windowWidth.current.toDp >= WINDOW_T2) {
            EditorUIConfig.safeEdgeWidthTableLandscape
        } else {
            0
        }
    }

    /**
     * 安全区域调整：对视频编辑界面安全边距适配
     */
    @JvmStatic
    fun adaptSafeAreaInVideo(
        context: BaseActivity,
        leftEdgeView: View,
        rightEdgeView: View,
        config: AppUiResponder.AppUiConfig
    ) {
        val safeWidth = getDefSafeWidth(config)
        if (leftEdgeView.width != safeWidth) {
            leftEdgeView.layoutParams = leftEdgeView.layoutParams.apply {
                width = safeWidth
            }
        }

        if (rightEdgeView.width != rightEdgeViewWidth) {
            rightEdgeView.layoutParams = rightEdgeView.layoutParams.apply {
                width = safeWidth
            }
        }
        leftEdgeViewWidth = safeWidth
        rightEdgeViewWidth = safeWidth
    }

    /**
     * 安全区域调整：对回忆编辑界面安全边距适配
     * @param isSymmetry 当导航栏出现时，是否需要保证左右安全区域一样（左右都加上导航栏宽度）
     */

    @JvmStatic
    fun adaptSafeAreaInMemories(
        context: BaseActivity,
        leftEdgeView: View,
        rightEdgeView: View,
        config: AppUiResponder.AppUiConfig
    ) {
        val safeWidth = getDefSafeWidth(config)
        /**
         * 左边安全间距
         */
        val leftNaviBarHeight = context.leftNaviBarHeight(false)
        val leftEdgeViewWidth = safeWidth + leftNaviBarHeight
        if (leftEdgeView.width != leftEdgeViewWidth) {
            leftEdgeView.layoutParams = leftEdgeView.layoutParams.apply {
                width = leftEdgeViewWidth
            }
        }
        /**
         * 右边安全间距
         */
        val rightNaviBarHeight = context.rightNaviBarHeight(false)
        val rightEdgeViewWidth = safeWidth + rightNaviBarHeight
        if (rightEdgeView.width != rightEdgeViewWidth) {
            rightEdgeView.layoutParams = rightEdgeView.layoutParams.apply {
                width = rightEdgeViewWidth
            }
        }
        VideoEditorUIConfig.leftEdgeViewWidth = leftEdgeViewWidth
        VideoEditorUIConfig.rightEdgeViewWidth = rightEdgeViewWidth
    }

    @JvmStatic
    fun getRightSafeWidth(): Int = rightEdgeViewWidth

    @JvmStatic
    fun getLeftSafeWidth(): Int = leftEdgeViewWidth

    @JvmStatic
    fun checkSafeAreaIsRight(context: BaseActivity, leftEdgeView: View, rightEdgeView: View): Boolean {
        val leftNaviBarHeight = context.leftNaviBarHeight(false)
        val rightNaviBarHeight = context.rightNaviBarHeight(false)
        val horizontalNaviBarH = max(leftNaviBarHeight, rightNaviBarHeight)
        if (horizontalNaviBarH <= 0) {
            return rightEdgeView.layoutParams.width == leftEdgeView.layoutParams.width
        }
        return if (leftNaviBarHeight > rightNaviBarHeight) {
            rightEdgeView.layoutParams.width == leftEdgeView.layoutParams.width + horizontalNaviBarH
        } else {
            leftEdgeView.layoutParams.width == rightEdgeView.layoutParams.width + horizontalNaviBarH
        }
    }

    @JvmStatic
    fun getViewScaleByWindowSize(config: AppUiResponder.AppUiConfig, from: Int = FORM_COMMON_PAGE): Float {
        /**
         * 默认不缩小
         */
        var scale = SCALE_DEFAULT
        if ((from == FORM_COMMON_PAGE) && (config.windowWidth.current.toDp < WINDOW_T4) && (config.windowHeight.current.toDp < WINDOW_T4)) {
            /**
             * 窗口宽高都小于400dp时，view缩小到0.9
             */
            scale = TOOLBAR_SCALE_IN_MICRO_WINDOW
        } else if ((from == FORM_TRIM_PAGE) && (config.windowHeight.current.toDp <= WINDOW_TRIM_T0)) {
            /**
             * 裁剪，特效，文本界面 窗口高度都小于或者等于540dp时，工具栏view缩小到0.7
             */
            scale = TOOLBAR_TRIM_SCALE
        }
        return scale
    }

    @JvmStatic
    fun getViewSizeByScale(size: Int, config: AppUiResponder.AppUiConfig, from: Int = FORM_COMMON_PAGE): Int =
        (size * getViewScaleByWindowSize(config, from)).toInt()

    @JvmStatic
    fun getViewSizeByScale(resId: Int, context: Context, config: AppUiResponder.AppUiConfig, from: Int = FORM_COMMON_PAGE): Int =
        getViewSizeByScale(context.resources.getDimensionPixelOffset(resId), config, from)

    /**
     * 获取视频编辑左右两边的安全边距值
     * @param context
     * @return Pair first = 左边安全边距 second = 右边安全边距
     */
    @JvmStatic
    fun getEdgeViewSize(context: BaseActivity?): Pair<Int, Int> {
        context?.let {
            val safeWidth = getDefSafeWidth(context.getCurrentAppUiConfig())
            return Pair(safeWidth, safeWidth)
        }
        return Pair(0, 0)
    }
}