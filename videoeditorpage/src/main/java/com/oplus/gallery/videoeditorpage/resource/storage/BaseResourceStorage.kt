/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IResourceStorage.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/19		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.storage

import com.oplus.gallery.videoeditorpage.resource.room.bean.Item

abstract class BaseResourceStorage<T : Item> {

    open fun getByPath(path: String): T? {
        return null
    }

    /**
     * 批量删除资源项，在子类中实现
     * @param items 资源项列表
     * @return 返回删除操作的成功的数量
     */
    open fun batchDelete(items: List<T>): Int = -1

    /**
     *  保存资源项
     *  @param item 资源项
     *  @return 返回保存操作的结果
     */
    abstract fun save(item: T): Long

    /**
     * 保存资源项
     * @param items 资源项列表
     */
    abstract fun save(items: List<T>): Boolean

    /**
     * 清除所有资源项
     */
    abstract fun clear()

    /**
     * 清除所有内置资源项
     */
    abstract fun clearBuiltin()

    /**
     * 获取资源项
     * @param id 资源id，唯一标识
     * @return 资源项
     */
    abstract fun get(id: String): T?

    /**
     * 返回已加载的资源项列表
     * @return 已加载的资源项列表
     */
    abstract fun getLoaded(): MutableList<T>

    /**
     * 返回内置的资源项列表
     * @return 内置的资源项列表
     */
    abstract fun getBuiltin(): MutableList<T>

    /**
     * 返回内置的资源项列表 数量
     * @return 内置的资源项列表 数量size
     */
    abstract fun getBuiltinCount(): Int

    /**
     * 返回非内置资源项列表
     * @return 内置的资源项列表
     */
    abstract fun getNetwork(): MutableList<T>

    /**
     * 返回按位置排序的资源项列表
     * @return 按位置排序的资源项列表
     */
    abstract fun getAllOrderByPosition(): List<T>?
}