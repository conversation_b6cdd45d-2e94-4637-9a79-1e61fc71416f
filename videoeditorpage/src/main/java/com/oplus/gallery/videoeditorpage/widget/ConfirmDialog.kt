/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ConfirmDialog.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/6/6
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/6/6        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.content.DialogInterface
import android.view.View
import androidx.annotation.StyleRes
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.ui.dialog.base.BaseAlertDialog
import com.oplus.gallery.foundation.ui.dialog.base.BaseBuilder
import com.oplus.gallery.foundation.util.ext.setWeight

/**
 * 提示弹窗 目前只有壁纸业务使用
 */
class ConfirmDialog private constructor(private val builder: Builder) : BaseAlertDialog<ConfirmDialog>(builder) {

    override fun show(): ConfirmDialog {
        super.show()
        if (builder.getCustomView() != null) {
            findViewById<View>(androidx.preference.R.id.customPanel)?.setWeight(CUSTOM_VIEW_WEIGHT)
        }
        return this
    }

    class Builder : BaseBuilder<ConfirmDialog> {
        constructor(
            context: Context,
            @StyleRes dialogStyleResId: Int,
            @StyleRes colorThemeResId: Int? = null
        ) : super(context, dialogStyleResId, colorThemeResId)

        private var customView: View? = null

        constructor(context: Context) : this(context, com.support.dialog.R.style.COUIAlertDialog_BottomWarning)

        /**
         * 当弹窗内容比较多，在当前弹窗高度不够时，设置该值为true
         * 确保内容不会截断，而无法显示的内容，将以滑动的形式显示
         */
        fun setHasMessageMerge(hasMessageMerge: Boolean): Builder {
            realBuilder.setHasMessageMerge(hasMessageMerge)
            return this
        }

        fun setMessage(message: CharSequence?): Builder {
            realBuilder.setMessage(message)
            return this
        }

        fun setMessage(resId: Int): Builder {
            realBuilder.setMessage(resId)
            return this
        }

        fun setView(view: View?): Builder {
            customView = view
            realBuilder.setView(view)
            return this
        }

        internal fun getCustomView() = customView

        fun setNegativeButton(
            textId: Int,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            realBuilder.setNegativeButton(textId, listener)
            return this
        }

        fun setNegativeButton(
            text: CharSequence?,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            realBuilder.setNegativeButton(text, listener)
            return this
        }

        fun setPositiveButton(
            textId: Int,
            listener: DialogInterface.OnClickListener?
        ): Builder {
            realBuilder.setPositiveButton(textId, listener)
            return this
        }

        fun setPositiveButton(
            text: CharSequence?,
            listener: DialogInterface.OnClickListener?
        ): Builder {
            realBuilder.setPositiveButton(text, listener)
            return this
        }

        fun setNeutralButton(
            text: CharSequence?,
            listener: DialogInterface.OnClickListener?
        ): Builder {
            realBuilder.setNeutralButton(text, listener)
            return this
        }

        fun setNeutralButton(
            textId: Int,
            listener: DialogInterface.OnClickListener?
        ): Builder {
            realBuilder.setNeutralButton(textId, listener)
            return this
        }

        /**
         * 设置button是否动态布局：例如：true 根据字体宽度自动计算上下布局与左右布局； false 为固定左右布局
         */
        fun setButtonLayoutDynamicLayout(buttonLayoutDynamicLayout: Boolean): Builder {
            realBuilder.setButtonLayoutDynamicLayout(buttonLayoutDynamicLayout)
            return this
        }

        override fun setOnKeyListener(onKeyListener: DialogInterface.OnKeyListener?): Builder {
            realBuilder.setOnKeyListener(onKeyListener)
            return this
        }

        override fun build(): ConfirmDialog {
            //设置背景模糊效果 mark by zhongfonan: 有Otest crash问题，先临时关闭高斯模糊
            realBuilder.setBlurBackgroundDrawable(false)
            return ConfirmDialog(this)
        }

        /**
         * 多个按钮选项样式
         * @param itemsId 选项资源id
         * @param listener 点击回调事件
         */
        fun setItems(
            itemsId: Int,
            listener: DialogInterface.OnClickListener?
        ): Builder {
            realBuilder.setItems(itemsId, listener)
            return this
        }
    }

    companion object {
        private const val CUSTOM_VIEW_WEIGHT = 1f
    }
}
