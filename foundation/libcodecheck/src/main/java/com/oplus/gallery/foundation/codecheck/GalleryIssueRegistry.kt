/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryIssueRegistry.kt
 ** Description: 相册框架代码检查注册表
 ** Version: 1.0
 ** Date: 2023/4/26
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/4/26      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.codecheck

import com.android.tools.lint.client.api.IssueRegistry
import com.android.tools.lint.detector.api.CURRENT_API
import com.android.tools.lint.detector.api.Issue
import com.oplus.gallery.foundation.codecheck.detector.AbilityCallLocationDetector
import com.oplus.gallery.foundation.codecheck.detector.ConfigApiCallDetector

/**
 * 要检查的问题Issue注册列表配置
 */
class GalleryIssueRegistry : IssueRegistry() {

    override val issues: List<Issue>
        get() = mutableListOf(
            ConfigApiCallDetector.ISSUE,
            AbilityCallLocationDetector.ISSUE,
        )

    override val api: Int
        get() = CURRENT_API
}