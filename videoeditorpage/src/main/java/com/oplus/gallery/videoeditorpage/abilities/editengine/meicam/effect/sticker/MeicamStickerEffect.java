/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamStickerEffect.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.sticker;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.resource.data.File;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.google.gson.annotations.SerializedName;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.timeline.MeicamTimelineEffect;

public class MeicamStickerEffect extends MeicamTimelineEffect {

    public static final String JSON_TYPE_NAME = "sticker";
    public static final float DEFAULT_OPACITY = 1;
    private static final String TAG = "MeicamStickerEffect";
    private static final String EFFECT_NAME = "Storyboard";

    // 定义贴纸重复模式的常量字符串
    private static final String REPEAT_STRING_NONE = "\"";
    private static final String REPEAT_STRING_REPEAT = "\" cafLoopMode=\"repeat\"";
    private static final String REPEAT_STRING_LAST_FRAME = "\" cafLoopMode=\"repeatLastFrame\"";

    @SerializedName("class_type")
    private String mEffectNameForSave = JSON_TYPE_NAME;// use to mark the type in saved json file
    @SerializedName("file")
    private String mFilePath;
    @SerializedName("width")
    private int mWidth;
    @SerializedName("height")
    private int mHeight;
    @SerializedName("opacity")
    private Float mOpacity = DEFAULT_OPACITY;
    @SerializedName("trans_x")
    private float mTransX;
    @SerializedName("trans_y")
    private float mTransY;
    @SerializedName("repeat")
    private int mRepeatType = StreamingConstant.Sticker.REPEAT_TYPE_NONE;

    public MeicamStickerEffect(String filePath) {
        super(EFFECT_NAME, TYPE_BUILT_IN_FX);
        mFilePath = filePath;
    }

    public int getWidth() {
        return mWidth;
    }

    public int getHeight() {
        return mHeight;
    }

    public String getFilePath() {
        return mFilePath;
    }

    public float getOpacity() {
        return mOpacity;
    }

    public void setOpacity(float opacity) {
        this.mOpacity = opacity;
    }

    public void setTransition(float transX, float transY) {
        mTransX = transX;
        mTransY = transY;
    }

    public float getTransX() {
        return mTransX;
    }

    public float getTransY() {
        return mTransY;
    }

    public void setSize(int width, int height) {
        mWidth = width;
        mHeight = height;
    }

    public void setRepeatType(int type) {
        mRepeatType = type;
    }

    public void syncPropertyToNvs(int timelineWidth, int timelineHeight) {
        if (mNvsVideoFx == null) {
            GLog.e(TAG, "syncPropertyToNvs, mNvsVideoFx is null");
            return;
        }

        if (mFilePath == null) {
            GLog.e(TAG, "syncPropertyToNvs, mFilePath is null");
            return;
        }
        int separatorIndex = mFilePath.lastIndexOf(File.SEPARATOR);
        if ((separatorIndex <= 0) || (separatorIndex >= mFilePath.length() - 1)) {
            GLog.e(TAG, "syncPropertyToNvs, mFilePath illegal:" + mFilePath);
            return;
        }
        String dir = mFilePath.substring(0, separatorIndex);
        String fileName = mFilePath.substring(separatorIndex + 1);

        long during = mOutTime - mInTime;

        String repeatValue = getRepeatString();
        String descString = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                + "<storyboard sceneWidth=\"" + timelineWidth
                + "\" sceneHeight=\"" + timelineHeight + "\">"
                + "<track source=\"" + fileName + repeatValue
                + " width=\"" + mWidth + "\" height=\"" + mHeight
                + "\" clipStart=\"0\" clipDuration=\"" + during + "\">"
                + "<effect name=\"transform\">"
                + "<param name=\"opacity\" value=\""
                + ((mOpacity == null) ? Float.valueOf(DEFAULT_OPACITY) : mOpacity) + "\"/>"
                + "<param name=\"transX\" value=\"0\"/>"
                + "<param name=\"transY\" value=\"0\"/>"
                + "</effect></track></storyboard>";
        mNvsVideoFx.setStringVal("Resource Dir", dir);
        mNvsVideoFx.setStringVal("Description String", descString);
        mNvsVideoFx.setBooleanVal("Is Animated Sticker", true);
        mNvsVideoFx.setFloatVal("Sticker TransX", mTransX);
        mNvsVideoFx.setFloatVal("Sticker TransY", -mTransY);
        GLog.d(TAG, "syncPropertyToNvs,mTransX:" + mTransX + ", mTransY:" + -mTransY);
    }

    private String getRepeatString() {
        switch (mRepeatType) {
            case StreamingConstant.Sticker.REPEAT_TYPE_REPEAT:
                return REPEAT_STRING_REPEAT;
            case StreamingConstant.Sticker.REPEAT_TYPE_LAST_FRAME:
                return REPEAT_STRING_LAST_FRAME;
            default:
                return REPEAT_STRING_NONE;
        }
    }
}
