/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamMaskRegionInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.mask;

import com.google.gson.annotations.SerializedName;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.data.MeicamRegionInfo;

import java.util.ArrayList;
import java.util.List;

public class MeicamMaskRegionInfo {

    public static final int MASK_REGION_TYPE_NONE = 0;
    public static final int MASK_REGION_TYPE_POLYGON = 1;
    public static final int MASK_REGION_TYPE_CUBIC_CURVE = 2;
    public static final int MASK_REGION_TYPE_ELLIPSE2D = 3;

    @Deprecated
    @SerializedName("a")
    private List<MeicamRegionInfo> mRegionInfoArrayTmp = new ArrayList<>();//旧版，以后逐渐删除
    @SerializedName("mRegionInfoArray")
    private List<MeicamRegionInfo> mRegionInfoArray = new ArrayList<>();//以这个为准

    private transient StringBuilder mStringBuilder;

    public List<MeicamRegionInfo> getRegionInfoArray() {
        if ((mRegionInfoArray == null) || (mRegionInfoArray.isEmpty())) {
            mRegionInfoArray = mRegionInfoArrayTmp;
        }
        if (mRegionInfoArray == null) {
            mRegionInfoArray = new ArrayList<>();
        }
        return mRegionInfoArray;
    }

    public void addRegionInfo(MeicamRegionInfo regionInfo) {
        if ((mRegionInfoArray != null) && (!mRegionInfoArray.contains(regionInfo))) {
            this.mRegionInfoArray.add(regionInfo);
        }
        mRegionInfoArrayTmp = mRegionInfoArray;
    }

    public void removeRegionInfoByIndex(int index) {
        if ((index >= 0) && (index < this.mRegionInfoArray.size())) {
            this.mRegionInfoArray.remove(index);
        }
    }

    public String logTestIfNeed() {
        if ((mRegionInfoArray == null) || (mRegionInfoArray.isEmpty())) {
            return null;
        }
        if (mStringBuilder == null) {
            mStringBuilder = new StringBuilder();
        } else {
            mStringBuilder.delete(0, mStringBuilder.length());
        }

        for (MeicamRegionInfo regionInfo : mRegionInfoArray) {
            mStringBuilder.append(regionInfo.toString()).append(";");
        }
        return mStringBuilder.toString();
    }
}
