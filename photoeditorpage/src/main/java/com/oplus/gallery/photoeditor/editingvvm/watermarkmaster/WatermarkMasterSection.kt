/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - WatermarkMasterSection.kt
 ** Description: 大师水印编辑切片
 ** Version: 1.0
 ** Date : 2024/8/12
 ** Author: <EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2024/8/12      1.0        created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.watermarkmaster

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Rect
import android.graphics.RectF
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.RelativeLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.toRectF
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIInEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.animation.COUIOutEaseInterpolator
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter.INVALID_VALUE
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.business_lib.template.editor.data.ItemStatus
import com.oplus.gallery.business_lib.template.editor.data.TextViewData
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.findFirstVisiblePosition
import com.oplus.gallery.foundation.util.ext.findLastVisiblePosition
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_OPPO_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl.Companion.LAST_SAVE_STYLE_ID_KEY
import com.oplus.gallery.framework.abilities.watermark.file.PrivacyWatermarkStyle
import com.oplus.gallery.framework.abilities.watermark.file.ShootingInfo.Companion.isValid
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkPattern
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle.Companion.TYPE_STYLE_NONE
import com.oplus.gallery.basebiz.helper.restrict.RestrictButtonConfigUtils
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseWatermarkSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_CURRENT_SERIES_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.TOPIC_RESTRICT_WATERMARK_SERIES_STYLE_ID
import com.oplus.gallery.photoeditor.editingvvm.UnitNotifier
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.preview.LayoutChaneAnimationType
import com.oplus.gallery.photoeditor.editingvvm.subscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.editingvvm.watermark.PrivacyDataStatus
import com.oplus.gallery.photoeditor.editingvvm.watermark.dialog.CustomInfoEditDialogHelper
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.LOCAL_BUTTON_VIEW_ID_LIST
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.NONE
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.RESTRICT
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.RESTRICT_BUTTON_LIST
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.RESTRICT_BUTTON_VIEW_ID_LIST
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.ItemMaskClickListener
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.PrivacyMenuAdapter
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.WatermarkMasterStyleAdapter
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.WatermarkMasterTypeAdapter
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.WatermarkMasterTypeHeaderOrFooterAdapter
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.WatermarkSeriesTitleAdapter
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkStyleItemViewData
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkTypeItemViewData
import com.oplus.gallery.photoeditor.widget.PrimaryAndSubTitleView
import com.oplus.gallery.photoeditor.widget.layout.ControlledRelativeLayout
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlin.math.abs

/**
 * 水印大师编辑页面切片
 * 管理平台配置词条文件(ini文件)制作脚本见：photoeditorpage\docs\限定水印\资源制作脚本\convert_ini.py
 * 管理平台配置YUV420底纹图片时，可以用脚本：photoeditorpage\docs\限定水印\资源制作脚本\convert_png.py
 */
internal class WatermarkMasterSection(
    sectionBus: ISectionBus<EditingFragment, EditingVM>
) : BaseWatermarkSection(sectionBus) {
    // view相关
    private var watermarkTypeContainer: View? = null
    private var watermarkTypeListView: EditorLinearListView? = null
    private var watermarkStyleListView: EditorLinearListView? = null
    private var watermarkSeriesListView: EditorLinearListView? = null
    private var watermarkRealmeBrandListView: EditorLinearListView? = null
    private var watermarkSeriesTitleView: PrimaryAndSubTitleView? = null
    private var privacyListView: EditorLinearListView? = null
    private var previewArea: View? = null
    private var listContainer: View? = null
    private var titleBarContainer: View? = null

    // adapter
    private var typeAdapter: WatermarkMasterTypeAdapter? = null
    private var styleAdapter: WatermarkMasterStyleAdapter? = null
    private var privacyAdapter: PrivacyMenuAdapter? = null
    private var titleAdapter: WatermarkSeriesTitleAdapter? = null
    private var realmeBrandTitleAdapter: WatermarkSeriesTitleAdapter? = null

    // 发布通道
    private var watermarkUiBeanNtf: UnitNotifier? = null
    private var previewRectChangeNtf: UnitNotifier? = null
    private var previewAnimateNtf: UnitNotifier? = null
    private var previewLayoutRevertNtf: NotifierChannel<Unit>? = null
    private var viewDataNtf: NotifierChannel<MutableList<WatermarkStyleItemViewData>>? = null

    /**
     * 发送Action话题
     * 1，true：注册Action话题
     * 2，false：反注册Action话题
     */
    private var actionTopicsNtf: NotifierChannel<Unit>? = null

    //是否禁止layout change更新preview rect
    private var forbidUpdatePreviewRect: Boolean = false

    //强制刷新preview rect
    private var forceUpdatePreviewRect: Boolean = false

    //是否首次进入WatermarkMasterSection页面，首次进入和从下一级页面返回页面元素动效不一样
    private var firstTimeEntering: Boolean = false

    //preview动画时，是否需要检查做延迟操作
    private var checkPreviewRectAnimatorDelay: Boolean = false

    //进退场动效集合
    private var fadeInAnimator: AnimatorSet? = null
    private var fadeOutAnimator: AnimatorSet? = null

    /**
     * 1.section在create阶段会收到两次onUiConfigChanged，会导致section在创建阶段就inflate多次布局
     * 2.横竖屏旋转后返回上一级页面，会在BaseEditingSection的onPageAssembled方法中回调一次onAppUiStateChanged
     *  但是实际上onUiConfigChanged方法传进来的config.isChanged方法并没有changed，导致没有触发布局刷新显示异常
     * 3.因此在section自己维护一个lastAppUiConfig，用于对比当前最新config与上一次config是否有改变，在有改变时，触发布局刷新
     */
    private var lastAppUiConfig: AppUiResponder.AppUiConfig? = null

    private var privacySelectPosition: Int = 0

    private var lastPosition = INVALID_VALUE
    private var lastPositionIsCustom = false
    private var contentText: String? = null
    private var lastClickDownloadStyleId: String? = null
    private var isUiConfigChange: Boolean = false

    /**
     * operatingSection只是将子布局里面的内容隐藏了，titleBar自身并没有隐藏，会有个黑色背景遮挡住preview
     * 因此在进入和退出个性编辑页面的时候对titleBar也做alpha动画，但是在MasterSection页面退出场景不需要做alpha
     * 不然会导致返回上一级页面时底部titleBar被设置为透明，显示异常问题
     */
    private var enteringPersonalizedSection: Boolean = false

    private var currentSelectedStyleId: String = TextUtil.EMPTY_STRING

    private var currentSelectedTypeId: Int = 0
    private var lastSelectedTypeId: Int = 0

    private var currentRestrictTitle: String? = null
    private var currentRestrictDescription: String? = null
    private var currentRestrictDescription1: String? = null
    private var currentRestrictDescription2: String? = null

    /**
     * 需要记录去水印，当我们查询到一个编辑周期记忆了去水印的操作
     */
    private val needRecordNone by lazy {
        (WatermarkStyleUserConfigLoaderImpl.getInstance().queryUserConfig(LAST_SAVE_STYLE_ID_KEY, false)?.styleId
                == TYPE_STYLE_NONE)
    }

    /**
     * 监听了个性画框样式列表滑动事件，去设置系列列表高亮选中显示
     * 1.如果是点击系列列表触发的滑动，不需要更新系列列表选中状态
     * 2.如果是点击水印样式触发的滑动，也需要更新系列列表选中状态
     */
    private var isSmoothScrolling: Boolean = false

    /**
     * 是否需要滑动水印类型焦点，当默认进入水印也不需要滑动
     */
    private var needScrollToPosition: Boolean = true

    /**
     * 记录当前点击的水印类型Data
     */
    private var currentSelectedTypeData: EditorMenuItemViewData? = null

    /**
     * 发送布局是否为横屏的话题
     * 1，true：横屏
     * 2，false：竖屏
     */
    private var layoutIsLandscapeNft: NotifierChannel<Unit>? = null

    private val smoothScroller = object : LinearSmoothScroller(context) {
        override fun getHorizontalSnapPreference(): Int {
            var snap = SNAP_TO_START
            context?.let {
                val config = it.resources.configuration
                if (config.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                    snap = SNAP_TO_END
                }
            }
            return snap
        }
    }

    private val confirmListener = object : CustomInfoEditDialogHelper.ConfirmListener {
        override fun onConfirmed(text: String?) {
            resetCurrentSelectedStyleId()
            selectAndResetTypeId(lastSelectedTypeId)
            privacyWatermarkCustomDialogHelper?.dismiss()
            setPrivacyWatermarkCustomInfo(text)
        }

        override fun onCancelled() {
            GLog.d(TAG, LogFlag.DL) { "onCancelled lastPositionIsCustom=$lastPositionIsCustom, lastPosition=$lastPosition" }
            privacyWatermarkCustomDialogHelper?.dismiss()
            if (lastPositionIsCustom) {
                watermarkUiBeanNtf?.notify(
                    WatermarkMasterUIBean(
                        id = WatermarkMasterUiBeanId.CHANGE_PRIVACY_WATERMARK_CUSTOM_INFO,
                        privacyContentText = contentText
                    )
                )
            } else {
                if (lastPosition != INVALID_VALUE) {
                    privacyAdapter?.select(lastPosition)
                    privacySelectPosition = lastPosition
                    watermarkUiBeanNtf?.notify(
                        WatermarkMasterUIBean(
                            id = WatermarkMasterUiBeanId.ON_PRIVACY_WATERMARK_STYLE_SELECTED,
                            privacyLastSelectPosition = lastPosition
                        )
                    )
                } else {
                    privacyAdapter?.clearSingleSelectedPosition()
                    privacySelectPosition = INVALID_VALUE
                    if (currentSelectedStyleId.isEmpty()) {
                        /**
                         * 当隐私水印自定义信息输入框为空且当前选中的样式ID为空，取消自定义隐私水印时，需要移除水印：
                         * onStyleItemSelected会去添加大师水印，传的水印样式WatermarkPattern为大师水印，currentSelectedStyleId为空时获取的大师水印具体水印样式为空，
                         * 传给算法那边的key_ai_master_info数据信息也为空，算法那边判断是大师水印且key_ai_master_info数据信息为空，会直接返回null，
                         * 执行添加水印效果失败，不会返回对应的纹理，导致isDrawingWatermark的值一直为true，点击“无”图标时无反应，且预览图显示的是输入自定义信息时的自定义隐私水印。
                         * 场景：大师水印图片->编辑水印->点击“无”图标->点击隐私水印->点击自定义图标->输入自定义信息后点击“取消”按钮
                         */
                        removeWatermark()
                    } else {
                        onStyleItemSelected(currentSelectedStyleId)
                    }
                }
            }
        }

        override fun onTextChanged(text: String?) {
            watermarkUiBeanNtf?.notify(
                WatermarkMasterUIBean(
                    id = WatermarkMasterUiBeanId.CHANGE_PRIVACY_WATERMARK_CUSTOM_INFO,
                    privacyContentText = text
                )
            )
        }
    }

    private val inputListener = object : CustomInfoEditDialogHelper.InputListener {
        override fun onInputVerified(type: Int) {
            when (type) {
                CustomInfoEditDialogHelper.INPUT_CONTAINS_EMOJI -> {
                    context?.let {
                        privacyWatermarkCustomDialogHelper?.showError(
                            it.getString(R.string.picture_editor_privacy_watermark_diy_input_invalid)
                        )
                    }
                }
            }
        }
    }

    private val maskClickListener = object : ItemMaskClickListener {
        override fun onItemMaskClick(position: Int, item: WatermarkStyleItemViewData) {
            if (item.status == ItemStatus.DEFAULT) {
                // DEFAULT场景响应onItemClick即可
                GLog.w(TAG, LogFlag.DL, "[onItemMaskClick] item.status is DEFAULT no need response, return")
                return
            }
            if (item.status == ItemStatus.READY_LOAD) {
                lastClickDownloadStyleId = item.styleId
            }
            notifyEnteringPersonalizedSection(item)
        }
    }

    private val isPrivacyWatermarkEditType: Boolean by lazy {
        PhotoEditorType.PRIVACY_WATERMARK.tag == vBus.get<String>(TopicID.InputArguments.TOPIC_INPUTS_EDIT_TYPE)
    }

    // 监听通道
    private val privacyStatusObserver: TObserver<PrivacyDataStatus> = {
        when (it) {
            PrivacyDataStatus.LOADING -> {
                // loading时同时禁用隐私水印菜单，避免效果做完前提交新效果
                privacyListView?.apply {
                    isClickable = false
                    isEnabled = false
                }
                showLoadingDialog()
            }

            PrivacyDataStatus.READY -> {
                hideLoadingDialog(0)
                // 恢复隐私水印菜单
                privacyListView?.apply {
                    isClickable = true
                    isEnabled = true
                }
            }
        }
    }

    private var watermarkUiBeanObserver: TObserver<WatermarkMasterUIBean> = {
        when (it.id) {
            WatermarkMasterUiBeanId.CHECK_PRIVACY_AUTHORIZE -> {
                if (it.needShowPrivacyDialog) {
                    showPrivacyDialog(activity)
                }
            }

            WatermarkMasterUiBeanId.INIT_DATA -> {
                if (isPrivacyWatermarkEditType.not() && isUiConfigChange.not()) {
                    privacySelectPosition = INVALID_VALUE
                }
                it.watermarkStyleId?.also { styleId ->
                    currentSelectedStyleId = styleId
                }

                it.watermarkTypeListViewData?.also { datas ->
                    initWatermarkTypeListView(datas, it.typeButtonId)
                }
                it.privacyWatermarkListViewData?.also { datas ->
                    initPrivacyWatermarkListView(datas)
                }
                currentRestrictTitle = it.restrictTitle
                currentRestrictDescription = it.restrictDescription
                currentRestrictDescription1 = it.restrictDescription1
                currentRestrictDescription2 = it.restrictDescription2
                updateSeriesTitleByStyleId(it.watermarkStyleId)
            }

            WatermarkMasterUiBeanId.REFRESH_WATERMARK_TYPE_STATE -> refreshWatermarkTypeState(
                it.currentWatermarkInfo,
                it.isSupportFormatForHassel,
                it.isFromWatermarkDownloadLoader
            )

            WatermarkMasterUiBeanId.START_SCALE_ANIMATION -> startFrameAnimation()

            WatermarkMasterUiBeanId.ON_SCALE_ANIMATION_STOPPED -> onScaleAnimationEnd()

            WatermarkMasterUiBeanId.REFRESH_WATERMARK_STYLE_STATE -> refreshWatermarkStyleState(it.watermarkStyleListViewData)

            WatermarkMasterUiBeanId.RESTRICT_TYPE_VISIBILITY_CHANGED -> onRestrictTypeVisibilityChanged(it.needShowRestrictType)

            WatermarkMasterUiBeanId.UPDATE_STYLE_ITEM_STATUS -> {
                it.watermarkStyleId?.also { styleId ->
                    updateStyleItemStatus(styleId, it.watermarkStyleStatus)
                }
            }

            WatermarkMasterUiBeanId.UPDATE_STYLE_ITEM_LOADING_PROGRESS -> {
                it.watermarkStyleId?.also { styleId ->
                    updateStyleItemLoadingProgress(styleId, it.watermarkStyleProgress)
                }
            }

            WatermarkMasterUiBeanId.RESET_CURRENT_SELECTED_STYLE_ID -> resetCurrentSelectedStyleId()

            WatermarkMasterUiBeanId.REFRESH_RESTRICT_WATERMARK_STYLE -> refreshRestrictWatermarkStyle(it.seriesId, it.watermarkStyleListViewData)

            WatermarkMasterUiBeanId.CHANGE_SERIES_TITLE_VISIBILITY -> updateSeriesTitleByStyleId(it.watermarkStyleId)

            else -> Unit
        }
    }

    /**
     * 添加水印的loading窗口显示或隐藏
     * pair.first：isShowLoadingDialog - true：显示loading窗口，false：隐藏loading窗口
     * pair.second：delayTimes - 延迟执行时间，单位：ms
     */
    private val addWatermarkLoadingObserver: TObserver<Pair<Boolean, Long>> = {
        when (it.first) {
            true -> showLoadingDialog(it.second)
            false -> hideLoadingDialog(it.second)
        }
    }

    private val previewLayoutListener: View.OnLayoutChangeListener =
        View.OnLayoutChangeListener { _, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            if (forbidUpdatePreviewRect) {
                return@OnLayoutChangeListener
            }
            val rectChange = (right != oldRight) || (left != oldLeft) || (bottom != oldBottom) || (top != oldTop)
            if (rectChange || forceUpdatePreviewRect) {
                forceUpdatePreviewRect = false
                if (checkPreviewRectAnimatorDelay) {
                    checkPreviewRectAnimatorDelay = false
                    val layoutId = getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig())
                    if (layoutId == PORTRAIT_LAYOUT_ID) {
                        postDelayed({
                            notifyPreviewRectToChange(Rect(left, top, right, bottom).toRectF())
                        }, PREVIEW_RECT_ANIMATION_DELAY_TIME)
                    } else {
                        notifyPreviewRectToChange(Rect(left, top, right, bottom).toRectF())
                    }
                } else {
                    notifyPreviewRectToChange(Rect(left, top, right, bottom).toRectF())
                }
            }
        }

    /**
     * 更新preview rect显示区域
     * @param rectF 对应显示区域位置
     */
    private fun notifyPreviewRectToChange(rectF: RectF) {
        previewAnimateNtf?.notify(true)
        previewLayoutRevertNtf?.notify(LayoutChaneAnimationType.REVERT)
        previewRectChangeNtf?.notify(rectF)
    }

    override fun onCreate() {
        lastAppUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
        needScrollToPosition = true
        super.onCreate()
        firstTimeEntering = true
        registerAndSubscribeTopic()
        initView()
        adaptSysNaviBar()
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        val currentAppUiConfig = config.appUiConfig
        lastAppUiConfig?.let {
            if (currentAppUiConfig.isChanged(it)) {
                isUiConfigChange = true
                reInitView(layoutId)
            }
        }
        lastAppUiConfig = currentAppUiConfig
        updatePreviewAreaMargin(currentAppUiConfig)
        adaptSysNaviBar()
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        val isInMultiWindow = config.isInMultiWindow.current
        val isInFloatingWindow = config.isInFloatingWindow.current
        layoutIsLandscapeNft?.notify(isLandscape && isInMultiWindow.not() && isInFloatingWindow.not())
        return when {
            isInMultiWindow || isInFloatingWindow -> PORTRAIT_LAYOUT_ID
            isLandscape -> LANDSCAPE_LAYOUT_ID
            else -> PORTRAIT_LAYOUT_ID
        }
    }

    override fun getContainerViewID(): Int = R.id.photo_editor_ui_framework

    override fun onDestroy() {
        super.onDestroy()
        // 由于在退出水印页面时，section可能先于vm销毁，vm发送隐藏loading的topic不被接收处理，所以在section销毁前，主动去隐藏loading
        hideLoadingDialog(0)
        styleAdapter?.clear()
        watermarkSeriesTitleView?.clear()
        previewArea?.removeOnLayoutChangeListener(previewLayoutListener)
        removeContentViewIfNeed()
        recoverContainerView()
        unsubscribeAndUnregisterTopic()
    }

    private fun registerAndSubscribeTopic() {
        vBus.apply {
            watermarkUiBeanNtf = subscribeDuplexT(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN, watermarkUiBeanObserver)
            subscribeT(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS, privacyStatusObserver)
            previewRectChangeNtf = subscribeR(TopicID.Preview.REPLY_TOPIC_PREVIEW_DISPLAY_RECT_CHANGED_WITH_ANIMATOR)
            previewAnimateNtf = subscribeR(TopicID.Preview.REPLY_TOPIC_PREVIEW_ANIMATE)
            previewLayoutRevertNtf = subscribeR(TopicID.Preview.REPLY_TOPIC_PREVIEW_LAYOUT_EXECUTE_TYPE)
            viewDataNtf = subscribeR(WatermarkMasterTopic.TOPIC_STYLE_VIEW_DATA)
            actionTopicsNtf = subscribeR(TopicID.WatermarkMaster.REPLY_TOPIC_WATERMARK_MASTER_ACTION_TOPICS)
            layoutIsLandscapeNft = subscribeR(TopicID.WatermarkMaster.TOPIC_WATERMARK_LAYOUT_LANDSCAPE)
            subscribeT(TopicID.WatermarkMaster.TOPIC_ADD_WATERMARK_LOADING, addWatermarkLoadingObserver)
        }
    }

    private fun unsubscribeAndUnregisterTopic() {
        vBus.apply {
            unsubscribeDuplexT(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN, watermarkUiBeanNtf, watermarkUiBeanObserver)
            unsubscribe(TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS, privacyStatusObserver)
            unsubscribeR(TopicID.Preview.REPLY_TOPIC_PREVIEW_DISPLAY_RECT_CHANGED_WITH_ANIMATOR, previewRectChangeNtf)
            unsubscribeR(TopicID.Preview.REPLY_TOPIC_PREVIEW_ANIMATE, previewAnimateNtf)
            unsubscribeR(TopicID.Preview.REPLY_TOPIC_PREVIEW_LAYOUT_EXECUTE_TYPE, previewLayoutRevertNtf)
            unsubscribeR(WatermarkMasterTopic.TOPIC_STYLE_VIEW_DATA, viewDataNtf)
            unsubscribeR(TopicID.WatermarkMaster.REPLY_TOPIC_WATERMARK_MASTER_ACTION_TOPICS, actionTopicsNtf)
            unsubscribeR(TopicID.WatermarkMaster.TOPIC_WATERMARK_LAYOUT_LANDSCAPE, layoutIsLandscapeNft)
            unsubscribe(TopicID.WatermarkMaster.TOPIC_ADD_WATERMARK_LOADING, addWatermarkLoadingObserver)
        }
    }

    private fun initView() {
        initTitleBar()
        initToolbar()
        removeContainerView()
        watermarkUiBeanNtf?.notify(WatermarkMasterUIBean(id = WatermarkMasterUiBeanId.INIT_DATA))
        if (isPrivacyWatermarkEditType) {
            watermarkTypeListView?.visibility = View.INVISIBLE
            watermarkStyleListView?.apply {
                layoutParams.height = 0
            }
        }
    }

    /**
     * 创建alpha渐变入场动画
     * @param view 属性动画操作view对象
     * @param fromPersonalizedEditSection 是否从个性编辑页面返回
     */
    private fun createEnteringFadeInAnimator(view: View?, fromPersonalizedEditSection: Boolean): ObjectAnimator {
        view?.visibility = View.VISIBLE
        view?.alpha = 0f
        val fadeInAnimator = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        fadeInAnimator.duration = if (fromPersonalizedEditSection) {
            TOOL_CONTAINER_FADE_IN_FROM_PERSONALIZED_ANIMATOR_DURATION
        } else {
            TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION
        }
        fadeInAnimator.interpolator = COUIInEaseInterpolator()
        return fadeInAnimator
    }

    /**
     * 创建x轴做位移的进场动画
     * @param view 属性动画操作view对象
     * @param layoutId 当前布局Id
     * @param fromPersonalizedEditSection 是否需要x轴位移动画，区分首次进入页面还是从上一页面返回
     */
    private fun createEnteringTranslationXAnimator(view: View?, layoutId: Int, fromPersonalizedEditSection: Boolean): ObjectAnimator? {
        var enteringAnimator: ObjectAnimator? = null
        if (fromPersonalizedEditSection && (layoutId == LANDSCAPE_LAYOUT_ID)) {
            val offset = activity.resources.getDimensionPixelSize(R.dimen.picture3d_editor_personalise_page_transform_translation_x_offset)
            val translationX = (-offset).toFloat()
            view?.translationX = translationX
            enteringAnimator = ObjectAnimator.ofFloat(view, "translationX", translationX, 0f)
            enteringAnimator.duration = TOOL_CONTAINER_FADE_TRANSLATION_X_DURATION
            enteringAnimator.interpolator = COUIMoveEaseInterpolator()
        }
        return enteringAnimator
    }

    /**
     * 创建top裁剪进场动画
     * @param view 属性动画操作view对象
     * @param totalHeight type list和底部title bar的高度和
     * @param layoutId 当前布局Id
     * @param fromPersonalizedEditSection 是否从个性编辑页返回
     */
    private fun createEnteringClipTopAboveAnimator(
        view: View?,
        totalHeight: Int,
        layoutId: Int,
        fromPersonalizedEditSection: Boolean
    ): ValueAnimator? {
        var clipAnimator: ValueAnimator? = null
        if (fromPersonalizedEditSection && (layoutId == LANDSCAPE_LAYOUT_ID)) {
            view?.let {
                clipAnimator = ValueAnimator.ofInt(totalHeight, 0)
                clipAnimator?.let { valueAnimator ->
                    valueAnimator.duration = TOOL_CONTAINER_FADE_IN_FROM_PERSONALIZED_ANIMATOR_DURATION
                    valueAnimator.interpolator = COUIInEaseInterpolator()
                    valueAnimator.addUpdateListener { animation ->
                        val value = animation.animatedValue as Int
                        it.clipBounds = Rect(0, value, it.width, it.height)
                    }
                    valueAnimator.addListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            it.clipBounds = null
                        }
                    })
                }
            }
        }
        return clipAnimator
    }

    /**
     * 创建top裁剪进场动画
     * @param view 属性动画操作view对象
     * @param totalHeight type list和底部title bar的高度和
     * @param layoutId 当前布局Id
     * @param fromPersonalizedEditSection 是否从个性编辑页返回
     */
    private fun createEnteringClipTopBelowAnimator(
        view: View?,
        totalHeight: Int,
        layoutId: Int,
        fromPersonalizedEditSection: Boolean
    ): ValueAnimator? {
        var clipAnimator: ValueAnimator? = null
        if (fromPersonalizedEditSection && (layoutId == LANDSCAPE_LAYOUT_ID)) {
            view?.let {
                clipAnimator = ValueAnimator.ofInt(0, totalHeight)
                clipAnimator?.let { valueAnimator ->
                    valueAnimator.duration = TOOL_CONTAINER_FADE_IN_FROM_PERSONALIZED_ANIMATOR_DURATION
                    valueAnimator.interpolator = COUIInEaseInterpolator()
                    valueAnimator.addUpdateListener { animation ->
                        val value = animation.animatedValue as Int
                        it.clipBounds = Rect(0, it.height - value, it.width, it.height)
                    }
                    valueAnimator.addListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            it.clipBounds = null
                        }
                    })
                }
            }
        } else {
            view?.clipBounds = null
        }
        return clipAnimator
    }

    /**
     * 创建alpha渐变退场动画
     * @param view 属性动画操作view对象
     */
    private fun createExitingFadeOutAnimator(view: View?): ObjectAnimator {
        val fadeOutAnimator = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        fadeOutAnimator.duration = TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION
        fadeOutAnimator.interpolator = COUIOutEaseInterpolator()
        return fadeOutAnimator
    }

    /**
     * 创建x轴做位移的退场动画
     * @param view 属性动画操作view对象
     * @param layoutId 当前布局Id
     */
    private fun createExitingTranslationXAnimator(view: View?, layoutId: Int): ObjectAnimator? {
        var exitingAnimator: ObjectAnimator? = null
        if (layoutId == LANDSCAPE_LAYOUT_ID) {
            val offset = activity.resources.getDimensionPixelSize(R.dimen.picture3d_editor_personalise_page_transform_translation_x_offset)
            val translationX = (-offset).toFloat()
            view?.translationX = 0f
            exitingAnimator = ObjectAnimator.ofFloat(view, "translationX", 0f, translationX)
            exitingAnimator.duration = TOOL_CONTAINER_FADE_TRANSLATION_X_DURATION
            exitingAnimator.interpolator = COUIMoveEaseInterpolator()
        }
        return exitingAnimator
    }

    /**
     * 创建顶部裁剪退场动画
     * @param view 属性动画操作view对象
     * @param layoutId 当前布局Id
     */
    private fun createExitingClipTopAboveAnimator(view: View?, totalHeight: Int, layoutId: Int): ValueAnimator? {
        var clipAnimator: ValueAnimator? = null
        if ((layoutId == LANDSCAPE_LAYOUT_ID)) {
            view?.let {
                clipAnimator = ValueAnimator.ofInt(0, totalHeight)
                clipAnimator?.duration = TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION
                clipAnimator?.interpolator = COUIOutEaseInterpolator()
                clipAnimator?.addUpdateListener { animation ->
                    val value = animation.animatedValue as Int
                    it.clipBounds = Rect(0, value, it.width, it.height)
                }
            }
        }
        return clipAnimator
    }

    /**
     * 创建顶部裁剪退场动画
     * @param view 属性动画操作view对象
     * @param layoutId 当前布局Id
     */
    private fun createExitingClipTopBelowAnimator(view: View?, aboveView: View?, totalHeight: Int, layoutId: Int): ValueAnimator? {
        var clipAnimator: ValueAnimator? = null
        if ((layoutId == LANDSCAPE_LAYOUT_ID)) {
            view?.let {
                clipAnimator = ValueAnimator.ofInt(0, totalHeight)
                clipAnimator?.duration = TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION
                clipAnimator?.interpolator = COUIOutEaseInterpolator()
                clipAnimator?.addUpdateListener { animation ->
                    val value = animation.animatedValue as Int
                    aboveView?.let { above ->
                        it.clipBounds = Rect(0, value - above.height, it.width, it.height)
                    }
                }
            }
        }
        return clipAnimator
    }

    /**
     * 预览区域不同屏幕下设置安全距离
     *
     * - 距离左右及顶部距离
     *     - 小屏+竖屏 安全距离 == 顶部42dp，其他24dp
     *     - 小屏+横屏  安全距离== 24dp
     *     - 中屏 安全距离 == 24dp
     *     - 大屏  安全距离 == 40dp
     */
    private fun updatePreviewAreaMargin(appUiConfig: AppUiResponder.AppUiConfig?) {
        val resources = sectionBus.rootView.resources
        val configMode = getConfigMode(appUiConfig)
        val safeMargin = when (configMode) {
            LARGE_MODE -> resources.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_master_style_first_item_margin_top_land_large)
            else -> resources.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_master_style_first_item_margin_top_land)
        }

        // 竖屏非窗口模式下，预览区域距离顶部的安全距离为42dp
        val topSafeMargin = if (configMode == PORTRAIT_MODE) {
            resources.getDimensionPixelSize(R.dimen.picture3d_editor_personalise_preview_margin_top)
        } else {
            safeMargin
        }

        (previewArea?.layoutParams as? MarginLayoutParams)?.let {
            it.setMargins(safeMargin, topSafeMargin, safeMargin, it.bottomMargin)
            previewArea?.layoutParams = it
        }
    }

    private fun getTypeContainerAndTitleBarContainerHeight(): Int {
        var height = 0
        watermarkTypeContainer?.let { typeContainer ->
            titleBarContainer?.let { titleBar ->
                height = typeContainer.height + titleBar.height
            }
        }
        return height
    }

    override fun onPageAssembled() {
        forbidUpdatePreviewRect = false
        if (fadeInAnimator?.isRunning == true) {
            fadeInAnimator?.cancel()
        }
        if (fadeOutAnimator?.isRunning == true) {
            fadeOutAnimator?.cancel()
        }

        var fromPersonalizedEditSection = true
        if (firstTimeEntering) {
            firstTimeEntering = false
            fromPersonalizedEditSection = false
        } else {
            forceUpdatePreviewRect = true
            checkPreviewRectAnimatorDelay = true
            // 三级返回二级需要触发一下app ui状态，防止进入三级页面横竖屏旋转后返回二级页面没刷新
            onAppUiStateChanged(sectionBus.hostInstance.getCurrentAppUiConfig())
        }

        if (isPrivacyWatermarkEditType.not()) {
            watermarkStyleListView?.visibility = View.VISIBLE
            watermarkTypeListView?.visibility = View.VISIBLE
            watermarkSeriesTitleView?.visibility = View.VISIBLE
        }
        val layoutId = getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig())
        val listContainerFadeIn = createEnteringFadeInAnimator(sectionContentView, fromPersonalizedEditSection)
        val styleListViewTranslationAnimator =
            createEnteringTranslationXAnimator(watermarkStyleListView, layoutId, fromPersonalizedEditSection)
        val totalHeight = getTypeContainerAndTitleBarContainerHeight()
        val typeListViewClipAnimator =
            createEnteringClipTopAboveAnimator(watermarkTypeContainer, totalHeight, layoutId, fromPersonalizedEditSection)
        fadeInAnimator = AnimatorSet()
        fadeInAnimator?.let { animatorSet ->
            animatorSet.play(listContainerFadeIn).after(TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION)
            typeListViewClipAnimator?.let {
                animatorSet.play(it).after(TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION)
            }
            styleListViewTranslationAnimator?.let {
                animatorSet.play(it)
            }
            animatorSet.start()
        }
    }

    override fun onPageDisassembled() {
        forbidUpdatePreviewRect = true
        if (fadeInAnimator?.isRunning == true) {
            fadeInAnimator?.cancel()
        }
        if (fadeOutAnimator?.isRunning == true) {
            fadeOutAnimator?.cancel()
        }
        val layoutId = getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig())
        val listContainerFadeOut = createExitingFadeOutAnimator(sectionContentView)
        val styleListViewTranslationAnimator = createExitingTranslationXAnimator(watermarkStyleListView, layoutId)
        val totalHeight = getTypeContainerAndTitleBarContainerHeight()
        val typeListViewClipAnimator = createExitingClipTopAboveAnimator(watermarkTypeContainer, totalHeight, layoutId)
        fadeOutAnimator = AnimatorSet()
        fadeOutAnimator?.let { animatorSet ->
            animatorSet.play(listContainerFadeOut)
            if (enteringPersonalizedSection) {
                typeListViewClipAnimator?.let {
                    animatorSet.play(it)
                }
                enteringPersonalizedSection = false
            }
            styleListViewTranslationAnimator?.let {
                animatorSet.play(it)
            }
            animatorSet.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    watermarkStyleListView?.visibility = View.GONE
                    watermarkTypeListView?.visibility = View.GONE
                    watermarkSeriesTitleView?.visibility = View.GONE
                    styleListViewTranslationAnimator?.let {
                        watermarkStyleListView?.translationX = 0f
                    }
                    // 出场动画结束，取消裁剪动画区域
                    watermarkTypeContainer?.clipBounds = null
                }
            })
            animatorSet.start()
        }
    }

    private fun initTitleBar() {
        titleBarContainer = sectionBus.rootView.findViewById<View>(R.id.title_bar_container)?.apply {
            setBackgroundResource(R.color.picture3d_editor_watermark_title_bar_bg_color)
        }
        sectionBus.rootView.findViewById<View>(R.id.right_masking)?.apply {
            visibility = View.VISIBLE
        }
    }

    private fun initToolbar() {
        sectionContainerView?.findViewById<ConstraintLayout>(R.id.watermark_master_layout)?.apply {
            (layoutParams as? RelativeLayout.LayoutParams)?.addRule(RelativeLayout.ABOVE, R.id.title_bar_container)
            previewArea = findViewById<View?>(R.id.watermark_master_preview_area).apply {
                addOnLayoutChangeListener(previewLayoutListener)
            }
            //预览区域不同屏幕下设置安全距离
            updatePreviewAreaMargin(lastAppUiConfig)
            watermarkTypeContainer = findViewById(R.id.watermark_master_type_list_layout)
            watermarkTypeListView = findViewById<EditorLinearListView?>(R.id.watermark_master_type_list).apply {
                visibility = View.VISIBLE
            }
            watermarkStyleListView?.clearOnScrollListeners()
            watermarkStyleListView = findViewById(R.id.watermark_master_style_list)
            val apiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
            val layoutId = getContentLayoutId(apiConfig)
            watermarkStyleListView?.apply {
                if (layoutId == PORTRAIT_LAYOUT_ID) {
                    layoutManager = object : LinearLayoutManager(context) {
                        override fun canScrollHorizontally(): Boolean {
                            val size = styleAdapter?.data?.size ?: 0
                            return ((layoutId == PORTRAIT_LAYOUT_ID) && (size > SCROLL_HORIZONTALLY_MIN))
                        }

                        override fun setOrientation(orientation: Int) {
                            super.setOrientation(RecyclerView.HORIZONTAL)
                        }
                    }
                }

                //#8343175 中屏顶部安全间距24dp，大屏顶部安全间距40dp，现在照片和右侧选项距离顶部太近
                if (layoutId == LANDSCAPE_LAYOUT_ID) {
                    val topMargin = when (getConfigMode(apiConfig)) {
                        LARGE_MODE -> {
                            resources.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_master_style_first_item_margin_top_land_large)
                        }

                        else -> resources.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_master_style_first_item_margin_top_land)
                    }
                    updateFirstItemAndBottomMargin(topMargin, -1)
                }
            }
            watermarkSeriesListView = findViewById(R.id.watermark_master_series_title_list)
            watermarkSeriesTitleView = findViewById<PrimaryAndSubTitleView?>(R.id.watermark_master_series_title_layout).apply {
                // 拦截文字描述区域的触摸事件
                setOnTouchListener { _, _ -> true }
            }
            //竖屏进入个性编辑会隐藏style list，切横屏返回二级页，再切回竖屏没有重新设置会显示状态，这里重置一下状态
            privacyListView = findViewById(R.id.watermark_master_privacy_list)
            privacyListView?.apply {
                if (layoutId == PORTRAIT_LAYOUT_ID) {
                    layoutManager = object : LinearLayoutManager(context) {
                        override fun canScrollHorizontally(): Boolean {
                            val size = privacyAdapter?.data?.size ?: 0
                            return ((layoutId == PORTRAIT_LAYOUT_ID) && (size > SCROLL_HORIZONTALLY_MIN))
                        }

                        override fun setOrientation(orientation: Int) {
                            super.setOrientation(RecyclerView.HORIZONTAL)
                        }
                    }
                }
            }
            if (isPrivacyWatermarkEditType.not()) {
                watermarkStyleListView?.visibility = View.VISIBLE
                privacyListView?.visibility = View.INVISIBLE
            } else {
                watermarkStyleListView?.visibility = View.GONE
            }
            listContainer = findViewById(R.id.watermark_master_toolbar_layout)

            listContainer?.setOnTouchListener { _, event ->
                if (privacyListView?.visibility == View.VISIBLE) {
                    val privacyRect = Rect()
                    privacyListView?.getGlobalVisibleRect(privacyRect)
                    if (privacyRect.contains(event.x.toInt(), event.y.toInt()).not()) {
                        return@setOnTouchListener true
                    }
                }

                if (watermarkStyleListView?.visibility == View.VISIBLE) {
                    val styleRect = Rect()
                    watermarkStyleListView?.getGlobalVisibleRect(styleRect)
                    if (styleRect.contains(event.x.toInt(), event.y.toInt()).not()) {
                        return@setOnTouchListener true
                    }
                }

                return@setOnTouchListener false
            }
        }
    }

    private fun adaptSysNaviBar() {
        val rightNavBarH = sectionBus.hostInstance.rightNaviBarHeight(false)
        (sectionContentView?.layoutParams as? ViewGroup.MarginLayoutParams)?.let {
            it.rightMargin = rightNavBarH
            sectionContentView?.layoutParams = it
        }
    }

    private fun reInitView(layoutId: Int) {
        removeContentViewIfNeed()
        sectionContentView = (editorUIExecutor.getLayoutFromLayoutCache(layoutId) ?: let {
            layoutInflater.inflate(layoutId, sectionContainerView, false)
        }) as? ConstraintLayout
        sectionContentView?.let {
            addExtraViewToBaseUIView(it)
            forceUpdatePreviewRect = true
            //竖屏进入个性编辑会将content view的alpha设置为0，切横屏返回二级页，再切回竖屏没有重新设置回1状态，这里重置一下状态
            it.alpha = 1f
        }
        initView()
    }

    private fun removeContentViewIfNeed() {
        sectionContentView?.also { removeExtraViewFromBaseUIView(it) }
    }

    /**
     * 地理位置个人信息权限弹框
     * 进入水印编辑页，判断图片是否含有地理位置水印（showLocation），
     * showLocation为 true --> 弹出权限弹框，
     *      同意  --> 「地理位置」高亮选中，保持选中状态；
     *      不同意 --> 「地理位置」不高亮，未选中状态，抹除图片上的地理位置；
     * showLocation为 false --> 不弹出权限弹框，点击地理位置按钮才弹出权限弹框，
     *      同意  --> 「地理位置」高亮选中，在图片上添加地理位置；
     *      不同意 --> 「地理位置」不高亮，保持未选中状态；
     */
    private fun showPrivacyDialog(context: Context) {
        PermissionDialogHelper.showLocationPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    watermarkUiBeanNtf?.notify(WatermarkMasterUIBean(id = WatermarkMasterUiBeanId.PRIVACY_DIALOG_POSITIVE_CLICK))
                }
            },
            object : PermissionDialogHelper.NegativeClickListener {
                override fun onNegativeClick() {
                    watermarkUiBeanNtf?.notify(WatermarkMasterUIBean(id = WatermarkMasterUiBeanId.PRIVACY_DIALOG_NEGATIVE_CLICK))
                }
            }
        )
    }

    private fun initWatermarkTypeListView(
        typeListViewData: MutableList<WatermarkTypeItemViewData>,
        typeButtonId: String?
    ) {
        currentSelectedTypeData = typeListViewData.firstOrNull {
            typeButtonId.isNullOrEmpty().not() && it.buttonInfo?.buttonId == typeButtonId
        }
        typeAdapter = WatermarkMasterTypeAdapter(activity, typeListViewData) {
            vBus.notifyOnce<Boolean>(TopicID.WatermarkMaster.REPLY_QUERY_IS_DRAWING_WATERMARK)
        }
        typeAdapter?.setItemClickListener(OnTypeItemClickListener())
        val headerAdapter = WatermarkMasterTypeHeaderOrFooterAdapter()
        val footerAdapter = WatermarkMasterTypeHeaderOrFooterAdapter()
        val concatAdapter = ConcatAdapter(headerAdapter, typeAdapter, footerAdapter)
        watermarkTypeListView?.adapter = concatAdapter
        if (isPrivacyWatermarkEditType) {
            typeAdapter?.selectById(WatermarkMasterVM.PRIVACY)
        }
    }

    private fun initWatermarkStyleListView(styleListViewData: MutableList<WatermarkStyleItemViewData>) {
        val styleId = styleListViewData.getOrNull(0)?.styleId ?: return
        when {
            WatermarkMasterStyle.isPersonalizeFrame(styleId) -> initPersonalizeFrameListView(styleListViewData)
            WatermarkMasterStyle.isRealmeBrandImprint(styleId) -> initRealmeBrandListView(styleListViewData)
            else -> initNoSeriesWatermarkStyleList(styleListViewData)
        }
    }

    private fun initNoSeriesWatermarkStyleList(styleListViewData: MutableList<WatermarkStyleItemViewData>) {
        GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] initNoSeriesWatermarkStyleList:${styleListViewData.size}" }
        val layoutId = if (getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig()) == LANDSCAPE_LAYOUT_ID) {
            R.layout.picture3d_editor_watermark_master_style_item_land_layout
        } else {
            R.layout.picture3d_editor_watermark_master_style_item_layout
        }
        styleAdapter = WatermarkMasterStyleAdapter(
            activity,
            layoutId,
            R.drawable.watermark_master_style_list_item_background,
            styleListViewData.toMutableList()
        ).apply {
            setItemClickListener(OnStyleItemClickListener())
            setItemMaskClickListener(maskClickListener)
            setCanUnselectCurrentPosition(false)
        }

        watermarkStyleListView?.adapter = styleAdapter
        selectedStyleListByStyleId(currentSelectedStyleId)
        watermarkStyleListView?.clearOnScrollListeners()
        watermarkSeriesListView?.visibility = View.GONE
    }

    private fun initPersonalizeFrameListView(styleListViewData: MutableList<WatermarkStyleItemViewData>) {
        watermarkSeriesListView?.apply {
            val textViewDataList = mutableListOf<TextViewData>()
            ResourceUtils.getResourceIdArrays(context, R.array.picture3d_editor_array_series_text_array).forEachIndexed { index, stringId ->
                textViewDataList.add(TextViewData(index, isEnable = true, isSelectable = true, textId = stringId))
            }
            titleAdapter = WatermarkSeriesTitleAdapter(context, textViewDataList).apply {
                setCanUnselectCurrentPosition(false)
                setFastClickEnabled(true)
                setItemClickListener(OnSeriesTitleItemClickListener())
            }
            adapter = titleAdapter
            /* UX变更，个性画框不用显示系列的type列表，这里直接GONE
             * visibility = View.VISIBLE
             */
            visibility = View.GONE
        }
        watermarkStyleListView?.clearOnScrollListeners()
        //添加个性画框style列表滑动监听，并根据显示位置更新series高亮item
        addOnScrollListenerIfNeeded()
        val dividingPosition = mutableSetOf<Int>()
        styleListViewData.forEachIndexed { index, itemViewData ->
            val currentSeries = WatermarkMasterStyle.getSeries(itemViewData.styleId)
            val previousSeries = WatermarkMasterStyle.getSeries(styleListViewData.getOrNull(index - 1)?.styleId) ?: currentSeries
            if (previousSeries != currentSeries) {
                dividingPosition.add(index)
            }
        }
        val layoutId = if (getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig()) == LANDSCAPE_LAYOUT_ID) {
            R.layout.picture3d_editor_watermark_master_style_item_land_layout
        } else {
            R.layout.picture3d_editor_watermark_master_style_item_layout
        }
        styleAdapter = WatermarkMasterStyleAdapter(
            activity,
            layoutId,
            R.drawable.watermark_master_style_list_item_background,
            styleListViewData.toMutableList(),
            dividingPosition
        ).apply {
            setItemClickListener(OnStyleItemClickListener())
            setItemMaskClickListener(maskClickListener)
            setCanUnselectCurrentPosition(false)
        }
        watermarkStyleListView?.adapter = styleAdapter
        selectedStyleListByStyleId(currentSelectedStyleId)
        selectedSeriesListByStyleId(currentSelectedStyleId)
    }

    private fun initRealmeBrandListView(styleListViewData: MutableList<WatermarkStyleItemViewData>) {
        if (!ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND)) {
            return
        }
        watermarkRealmeBrandListView?.apply {
            val textViewDataList = mutableListOf<TextViewData>()
            ResourceUtils.getResourceIdArrays(context, R.array.picture3d_editor_array_realme_brand_text_array).forEachIndexed { index, stringId ->
                textViewDataList.add(TextViewData(index, isEnable = true, isSelectable = true, textId = stringId))
            }
            realmeBrandTitleAdapter = WatermarkSeriesTitleAdapter(context, textViewDataList).apply {
                setCanUnselectCurrentPosition(false)
                setFastClickEnabled(true)
                setItemClickListener(OnRealmeBrandTitleItemClickListener())
            }
            adapter = realmeBrandTitleAdapter
            /* UX变更，个性画框不用显示系列的type列表，这里直接GONE
             * visibility = View.VISIBLE
             */
            visibility = View.GONE
        }
        watermarkStyleListView?.clearOnScrollListeners()
        //添加realme品牌印记style列表滑动监听，并根据显示位置更新series高亮item
        addOnRealmeBrandScrollListenerIfNeeded()
        val dividingPosition = mutableSetOf<Int>()
        styleListViewData.forEachIndexed { index, itemViewData ->
            val currentSeries = WatermarkMasterStyle.getSeries(itemViewData.styleId)
            val previousSeries = WatermarkMasterStyle.getSeries(styleListViewData.getOrNull(index - 1)?.styleId) ?: currentSeries
            if (previousSeries != currentSeries) {
                dividingPosition.add(index)
            }
        }
        val layoutId = if (getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig()) == LANDSCAPE_LAYOUT_ID) {
            R.layout.picture3d_editor_watermark_master_style_item_land_layout
        } else {
            R.layout.picture3d_editor_watermark_master_style_item_layout
        }
        styleAdapter = WatermarkMasterStyleAdapter(
            activity,
            layoutId,
            R.drawable.watermark_master_style_list_item_background,
            styleListViewData,
            dividingPosition
        ).apply {
            setItemClickListener(OnStyleItemClickListener())
            setItemMaskClickListener(maskClickListener)
            setCanUnselectCurrentPosition(false)
        }
        selectedStyleListByStyleId(currentSelectedStyleId)
        selectedRealmeBrandListByStyleId(currentSelectedStyleId)
        watermarkStyleListView?.adapter = styleAdapter
    }

    /**
     * 添加个性画框style列表滑动监听，并根据显示位置更新series高亮item
     */
    private fun addOnScrollListenerIfNeeded() {
        if (getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig()) == PORTRAIT_LAYOUT_ID) {
            isSmoothScrolling = false
            watermarkStyleListView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    val isAnyItemSelected = styleAdapter?.isAnyItemSelected == true
                    if (!isSmoothScrolling && !isAnyItemSelected) {
                        watermarkStyleListView?.let {
                            var position = it.layoutManager?.findFirstVisiblePosition()
                            if (it.layoutManager?.findLastVisiblePosition() == styleAdapter?.data?.lastIndex) {
                                position = styleAdapter?.data?.lastIndex
                            }
                            if ((position != RecyclerView.NO_POSITION) && (position != null)) {
                                val itemViewData = styleAdapter?.data?.getOrNull(position) ?: return
                                val currentShowingSeries = WatermarkMasterStyle.getSeries(itemViewData.styleId)
                                currentShowingSeries?.let { series ->
                                    titleAdapter?.select(WatermarkMasterStyle.SERIES_ARRAY.indexOf(series))
                                }
                            }
                        }
                    }
                }

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    when (newState) {
                        RecyclerView.SCROLL_STATE_IDLE,
                        RecyclerView.SCROLL_STATE_DRAGGING -> isSmoothScrolling = false
                    }
                }
            })
        }
    }

    /**
     * 添加个性画框style列表滑动监听，并根据显示位置更新series高亮item
     */
    private fun addOnRealmeBrandScrollListenerIfNeeded() {
        if (getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig()) == PORTRAIT_LAYOUT_ID) {
            isSmoothScrolling = false
            watermarkStyleListView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    val isAnyItemSelected = styleAdapter?.isAnyItemSelected == true
                    if (!isSmoothScrolling && !isAnyItemSelected) {
                        watermarkStyleListView?.let {
                            var position = it.layoutManager?.findFirstVisiblePosition()
                            if (it.layoutManager?.findLastVisiblePosition() == styleAdapter?.data?.lastIndex) {
                                position = styleAdapter?.data?.lastIndex
                            }
                            if ((position != RecyclerView.NO_POSITION) && (position != null)) {
                                val itemViewData = styleAdapter?.data?.getOrNull(position) ?: return
                                val currentShowingSeries = WatermarkMasterStyle.getSeries(itemViewData.styleId)
                                currentShowingSeries?.let { series ->
                                    realmeBrandTitleAdapter?.select(WatermarkMasterStyle.REALME_BRAND_ARRAY.indexOf(series))
                                }
                            }
                        }
                    }
                }

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    when (newState) {
                        RecyclerView.SCROLL_STATE_IDLE,
                        RecyclerView.SCROLL_STATE_DRAGGING -> isSmoothScrolling = false
                    }
                }
            })
        }
    }

    /**
     * 初始化UI时，选中图片当前水印样式
     * @param styleId 当前图片对应水印样式
     */
    private fun selectedStyleListByStyleId(styleId: String) {
        currentSelectedStyleId = styleId
        styleAdapter?.selectedByStyleId(styleId)
        var position = styleAdapter?.getPositionByStyleId(styleId) ?: INVALID_VALUE
        styleAdapter?.initLastClickPosition(position)
        //水印列表小概率出现数据更新不及时的问题，增加判断防止数组越界
        if (position >= (watermarkStyleListView?.adapter?.itemCount ?: 0)) {
            position = INVALID_VALUE
        }

        // 获取要定位的系列限定水印styleId，多系列限定水印第一次进入时，根据角标所属系列需要定位到对应水印位置来展示给用户
        vBus.get<String>(TOPIC_RESTRICT_WATERMARK_SERIES_STYLE_ID)?.let {
            // 无水印或当前非限定水印的图片选择限定水印类别时，获取要定位的系列水印位置
            if (currentSelectedStyleId == TextUtil.EMPTY_STRING
            ) {
                position = styleAdapter?.getPositionByStyleId(it) ?: INVALID_VALUE
            }
            if (((styleId.isEmpty()) || (WatermarkMasterStyle.isRestrictStyle(styleId)))
                && (currentSelectedTypeData == null)
            ) {
                val typeButtonId = RestrictButtonConfigUtils.getTypeButtonIdByStyleId(it)
                val button = RESTRICT_BUTTON_LIST.find { button -> button.buttonInfo?.buttonId == typeButtonId }
                button?.viewId?.let { viewId ->
                    typeAdapter?.selectById(viewId)
                    currentSelectedTypeId = viewId
                    selectedTypeListByViewId(viewId)
                }
                currentSelectedTypeData = button
            }
        }
        watermarkStyleListView?.scrollToPosition(if (position <= INVALID_VALUE) 0 else position)
    }

    private fun selectedSeriesListByStyleId(styleId: String) {
        val series = WatermarkMasterStyle.getSeries(styleId)
        series?.let { titleAdapter?.select(WatermarkMasterStyle.SERIES_ARRAY.indexOf(series)) }
    }

    private fun selectedRealmeBrandListByStyleId(styleId: String) {
        val series = WatermarkMasterStyle.getSeries(styleId)
        series?.let { realmeBrandTitleAdapter?.select(WatermarkMasterStyle.REALME_BRAND_ARRAY.indexOf(series)) }
    }

    private fun selectedTypeListByViewId(
        viewId: Int,
        hasInsertItem: Boolean = false,
        insertIndex: Int = 0,
        lastPosition: Int = INVALID_VALUE,
        itemIncrease: Int = MathUtils.ONE,
        removeAll: Boolean = false,
    ) {
        val position = typeAdapter?.selectedPosition ?: INVALID_VALUE
        if (lastPosition != position && removeAll.not()) {
            watermarkTypeListView?.scrollToPosition(position + itemIncrease)
        }

        watermarkUiBeanNtf?.notify(
            WatermarkMasterUIBean(
                id = WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED,
                selectTypeId = viewId
            )
        )
        // 当限定水印在列表头部被添加时，需要更新typeAdapter的选中位置
        if (hasInsertItem && (lastPosition >= insertIndex)) {
            if (lastPosition == position && removeAll) {
                typeAdapter?.selectedPosition = position
                typeAdapter?.notifyDataSetChanged()
                return
            }
            if (itemIncrease > 0) {
                typeAdapter?.selectedPosition = lastPosition.plus(itemIncrease)
            } else {
                typeAdapter?.selectedPosition = lastPosition.minus(abs(itemIncrease))
            }
        }
    }

    private fun updateSeriesTitleByStyleId(styleId: String?) {
        when {
            (WatermarkMasterStyle.isHasselFrame(styleId) || WatermarkMasterStyle.isHasselText(styleId))
                    && !ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND) -> {
                watermarkSeriesTitleView?.showTitle(
                    R.string.pitcure_edtior_text_watermark_hasselblad_image,
                    R.string.pitcure_edtior_text_watermark_hasselblad_image_description
                )
            }

            WatermarkMasterStyle.isTextStyle(styleId) -> {
                watermarkSeriesTitleView?.showTitle(
                    R.string.pitcure_edtior_text_watermark_text,
                    R.string.pitcure_edtior_text_watermark_text_description
                )
            }

            WatermarkMasterStyle.isPersonalizeFrame(styleId) -> {
                when (WatermarkMasterStyle.getSeries(styleId)) {
                    WatermarkMasterStyle.SERIES_MASTER_SIGN -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_master_seal,
                            R.string.pitcure_edtior_text_watermark_master_seal_description2
                        )
                    }

                    WatermarkMasterStyle.SERIES_RETRO_CAMERA -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_classic_camera,
                            R.string.pitcure_edtior_text_watermark_classic_camera_description
                        )
                    }

                    WatermarkMasterStyle.SERIES_FILM -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_retro_film,
                            R.string.pitcure_edtior_text_watermark_retro_film_description
                        )
                    }

                    WatermarkMasterStyle.SERIES_BRAND -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_brand_power,
                            R.string.pitcure_edtior_text_watermark_brand_power_description
                        )
                    }

                    else -> watermarkSeriesTitleView?.hidTitle()
                }
            }

            WatermarkMasterStyle.isRealmeBrandImprint(styleId) -> {
                when (WatermarkMasterStyle.getSeries(styleId)) {
                    WatermarkMasterStyle.REALME_SERIES_BRAND -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_realme_brand_power,
                            R.string.pitcure_edtior_text_watermark_realme_brand_power_description
                        )
                    }

                    WatermarkMasterStyle.REALME_INSPIRATION_PHOTO -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_inspiration_photo,
                            R.string.pitcure_edtior_text_watermark_inspiration_photo_description
                        )
                    }

                    WatermarkMasterStyle.REALME_EXCLUSIVE_MEMORY -> {
                        watermarkSeriesTitleView?.showTitle(
                            R.string.pitcure_edtior_text_watermark_exclusive_memory,
                            R.string.pitcure_edtior_text_watermark_exclusive_memory_description
                        )
                    }

                    else -> watermarkSeriesTitleView?.hidTitle()
                }
            }

            //限制水印展示水印样式说明
            WatermarkMasterStyle.isRestrictStyle(styleId) -> {
                watermarkSeriesTitleView?.showTitle(
                    currentRestrictTitle,
                    currentRestrictDescription,
                    currentRestrictDescription1,
                    currentRestrictDescription2,
                    isRestrictWatermark = true
                )
            }

            else -> watermarkSeriesTitleView?.hidTitle()
        }
    }

    private fun resetCurrentSelectedStyleId() {
        currentSelectedStyleId = TextUtil.EMPTY_STRING
    }

    private fun refreshRestrictWatermarkStyle(seriesId: String?, dataList: MutableList<WatermarkStyleItemViewData>?) {
        if (currentSelectedTypeId == 0) return
        if ((WatermarkMasterStyle.isRemoteRestrictStyle(currentSelectedStyleId) || currentSelectedStyleId.isBlank())
            && (dataList?.isNotEmpty() == true) && (RestrictButtonConfigUtils.isOnLineTime())) {
            if (RESTRICT_BUTTON_VIEW_ID_LIST.contains(currentSelectedTypeId)) {
                dataList.firstOrNull { it.styleId == currentSelectedStyleId }
                    ?.apply { isSelected = true }
                val finalList = dataList.filter { style -> style.seriesId == seriesId }.toMutableList()
                initWatermarkStyleListView(finalList)
            } else {
                // 当前页面如果还有水印列表，判断一下水印列表与限定水印之中相同的元素，如果有，则不需要隐藏页面内容，只刷新列表
                val currentStyleDataList = ArrayList<WatermarkStyleItemViewData>()
                styleAdapter?.data?.forEach { adapterData ->
                    dataList.filter { it.styleId == adapterData.styleId }
                        .toCollection(currentStyleDataList)
                }

                if (currentStyleDataList.isEmpty()) {
                    // 判断当前选中的类型是否是本地水印类型
                    val isLocalTypeId = LOCAL_BUTTON_VIEW_ID_LIST.contains(currentSelectedTypeId)

                    // 判断当前选中类型是否是可以选择的类型
                    val isCurrentTypeSelectable = typeAdapter?.data?.firstOrNull { it.viewId == currentSelectedTypeId }?.isSelectable ?: false

                    // 如果是本地水印类型，且水印类型是可以选择的，则使用当前的类型，否则使用第一个可选类型按键。
                    val firstTypeViewId = if (isLocalTypeId && isCurrentTypeSelectable) { currentSelectedTypeId } else {
                        typeAdapter?.data?.firstOrNull {
                            (it.viewId != NONE) && it.isSelectable && (it.viewId != currentSelectedTypeId)
                        }?.viewId ?: INVALID_VALUE
                    }

                    // 通知刷新选中类型对应的样式列表
                    watermarkUiBeanNtf?.notify(
                        WatermarkMasterUIBean(
                            id = WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED,
                            selectTypeId = firstTypeViewId
                        )
                    )

                    if (isLocalTypeId.not() || isCurrentTypeSelectable.not()) {
                        // 隐藏水印描述、清除当前选择标记，置为未选择状态
                        watermarkSeriesTitleView?.visibility = View.GONE
                        currentSelectedStyleId = TextUtil.EMPTY_STRING
                    }
                    if (firstTypeViewId == INVALID_VALUE) {
                        initNoSeriesWatermarkStyleList(currentStyleDataList)
                        currentSelectedTypeId = INVALID_VALUE
                        typeAdapter?.selectedPosition = INVALID_VALUE
                    } else {
                        typeAdapter?.selectById(firstTypeViewId)
                    }
                } else {
                    initNoSeriesWatermarkStyleList(currentStyleDataList)
                }
            }
        }
    }

    private fun initPrivacyWatermarkListView(privacyWatermarkMenuData: MutableList<EditorMenuItemViewData>) {
        GLog.d(TAG, LogFlag.DF) { "initPrivacyWatermarkListView $privacyWatermarkMenuData" }
        privacyAdapter = PrivacyMenuAdapter(activity, privacyWatermarkMenuData)
        privacyAdapter?.setCanUnselectCurrentPosition(false)
        privacyAdapter?.setItemClickListener(onPrivacyItemClickListener)
        privacyListView?.adapter = privacyAdapter

        if (isPrivacyWatermarkEditType) {
            privacyListView?.visibility = View.VISIBLE
            privacyAdapter?.select(privacySelectPosition)
        } else {
            privacyListView?.visibility = View.INVISIBLE
        }
    }

    /**
     * 刷新水印类型状态
     */
    @Suppress("LongMethod")
    private fun refreshWatermarkTypeState(
        watermarkInfo: WatermarkInfo?,
        isSupportFormatForHassel: Boolean?,
        isFromWatermarkDownloadLoader: Boolean = false
    ) {
        GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] refreshWatermarkTypeState, " +
                "isFromWatermarkDownloadLoader:$isFromWatermarkDownloadLoader, isUiConfigChange$isUiConfigChange" }
        if (isUiConfigChange) {
            if (currentSelectedTypeId != WatermarkMasterVM.NONE) {
                watermarkUiBeanNtf?.notify(
                    WatermarkMasterUIBean(
                        id = WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED,
                        selectTypeId = currentSelectedTypeId
                    )
                )
                typeAdapter?.selectById(currentSelectedTypeId)
                typeAdapter?.selectedPosition?.also {
                    watermarkTypeListView?.scrollToPosition(it + 1)
                }
                if (currentSelectedTypeId == WatermarkMasterVM.PRIVACY) {
                    privacyAdapter?.select(privacySelectPosition)
                    privacyAdapter?.selectedPosition?.also {
                        privacyListView?.scrollToPosition(it)
                    }
                }
            } else {
                watermarkUiBeanNtf?.notify(
                    WatermarkMasterUIBean(
                        id = WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED,
                        selectTypeId = lastSelectedTypeId
                    )
                )
                typeAdapter?.selectById(WatermarkMasterVM.NONE)
                typeAdapter?.selectedPosition?.also {
                    watermarkTypeListView?.scrollToPosition(it + 1)
                }
            }
            if (isPrivacyWatermarkEditType) {
                privacyListView?.visibility = View.VISIBLE
                watermarkStyleListView?.visibility = View.GONE
            } else {
                if (lastSelectedTypeId == WatermarkMasterVM.PRIVACY) {
                    privacyListView?.visibility = View.VISIBLE
                    watermarkStyleListView?.visibility = View.GONE
                } else {
                    privacyListView?.visibility = View.GONE
                    watermarkStyleListView?.visibility = View.VISIBLE
                }
            }
            isUiConfigChange = false
        } else {
            if (watermarkInfo?.hasWatermark() != true) {
                updateUiWhenWithoutWatermark(isFromWatermarkDownloadLoader)
                return
            }
            if (watermarkInfo.params?.pattern == WatermarkPattern.PATTERN_PRIVACY) {
                updateUiWhenPrivacyWatermark(watermarkInfo)
                return
            } else {
                if (isPrivacyWatermarkEditType.not()) {
                    privacyListView?.visibility = View.GONE
                }
            }

            val deviceNameValid = watermarkInfo.device?.deviceName?.isNotBlank() == true
            val shootingInfoValid = watermarkInfo.content?.shootingInfo.isValid()
            val styleId = watermarkInfo.aiWatermarkMasterParams?.styleId ?: watermarkInfo.aiWatermarkFileExtendInfo?.styleId ?: return
            if (isFromWatermarkDownloadLoader && (RESTRICT_BUTTON_VIEW_ID_LIST.contains(currentSelectedTypeData?.viewId))) {
                GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] refreshWatermarkTypeState, updateUiWhenRestrictWatermark" }
                updateUiWhenRestrictWatermark(styleId)
            } else {
                when {
                    WatermarkMasterStyle.isHasselFrame(styleId) || WatermarkMasterStyle.isHasselText(styleId) -> {
                        if (watermarkInfo.hasselWatermarkEditable() && (isSupportFormatForHassel != false)) {
                            updateUiWhenHasselWatermark(styleId, isFromWatermarkDownloadLoader)
                        }
                    }

                    WatermarkMasterStyle.isPersonalizeFrame(styleId) -> {
                        if (deviceNameValid && shootingInfoValid) {
                            updateUiWhenFrameWatermark(styleId, isFromWatermarkDownloadLoader)
                        } else {
                            updateUiWhenTextWatermark(styleId)
                            watermarkSeriesTitleView?.visibility = View.GONE
                        }
                    }

                    WatermarkMasterStyle.isTextStyle(styleId) -> updateUiWhenTextWatermark(styleId, isFromWatermarkDownloadLoader)
                    WatermarkMasterStyle.isRestrictStyle(styleId) -> {
                        if (deviceNameValid && shootingInfoValid) {
                            updateUiWhenRestrictWatermark(styleId, isFromWatermarkDownloadLoader)
                        } else {
                            updateUiWhenTextWatermark(styleId)
                            watermarkSeriesTitleView?.visibility = View.GONE
                        }
                    }

                    WatermarkMasterStyle.isRealmeBrandImprint(styleId) -> updateUiWhenRealmeBrandWatermark(styleId)
                }
            }
            sectionContentView?.requestLayout()
        }
    }

    private fun findWatermarkItem(
        list: MutableList<WatermarkTypeItemViewData>?,
        viewId: Int
    ): Pair<Int, WatermarkTypeItemViewData?> {
        if (list == null) {
            return Pair(WatermarkMasterVM.INDEX_NONE, null)
        }
        val iterator = list.iterator().withIndex()
        while (iterator.hasNext()) {
            val next = iterator.next()
            val iconTextMenuData: WatermarkTypeItemViewData = next.value
            if (iconTextMenuData.viewId == viewId) {
                return Pair(next.index, iconTextMenuData)
            }
        }
        return Pair(WatermarkMasterVM.INDEX_NONE, null)
    }

    private fun setButtonEnable(buttonId: Int, enable: Boolean) {
        findWatermarkItem(typeAdapter?.data, buttonId).let {
            it.second?.apply {
                if (isEnable) {
                    isEnable = enable
                    typeAdapter?.updateItemView(it.first)
                }
            }
        }
    }

    private fun updateUiWhenWithoutWatermark(isFromWatermarkDownloadLoader: Boolean) {
        //在进水印的时候，如果快速点击选了别的系列，这里做下拦截，防止style列表跳变
        if (isFromWatermarkDownloadLoader && (currentSelectedTypeId != 0)) {
            GLog.d(TAG, LogFlag.DL) { "[updateUiWhenWithoutWatermark] type is selected" }
            return
        }

        //一个周期内去水印，再次进入水印时需要记忆无
        if (needRecordNone) {
            typeAdapter?.selectById(WatermarkMasterVM.NONE)
            return
        }

        //无水印图片默认选择第一个限定水印分类
        if (RESTRICT_BUTTON_VIEW_ID_LIST.size > 0) {
            findWatermarkItem(typeAdapter?.data, RESTRICT_BUTTON_VIEW_ID_LIST[0]).let {
                //如果限定水印按钮可以点击，无水印图片默认选中限定水印
                if (it.second?.isSelectable == true) {
                    //无水印进入
                    updateUiWhenRestrictWatermark(null)
                    return
                }
            }
        }

        findWatermarkItem(typeAdapter?.data, WatermarkMasterVM.HASSELBLAD).let {
            //如果哈苏按钮可以点击，无水印图片默认选中哈苏按钮
            if (it.second?.isSelectable == true) {
                updateUiWhenHasselWatermark(null)
                return
            }
        }
        findWatermarkItem(typeAdapter?.data, WatermarkMasterVM.FRAME).let {
            //如果哈苏按钮不可以点击，个性画框可以点击，无水印图片默认选中个性画框按钮
            if (it.second?.isSelectable == true) {
                updateUiWhenFrameWatermark(null)
                return
            }
        }
        // 如果哈苏按钮和个性画框都不可以点击，无水印图片默认选中文字水印按钮
        updateUiWhenTextWatermark(null)
        if (isPrivacyWatermarkEditType.not()) {
            privacyListView?.visibility = View.GONE
        }
    }

    private fun updateUiWhenHasselWatermark(styleId: String?, isFromWatermarkDownloadLoader: Boolean = false) {
        if (isFromWatermarkDownloadLoader.not()) {
            typeAdapter?.selectById(WatermarkMasterVM.HASSELBLAD)
            currentSelectedTypeId = WatermarkMasterVM.HASSELBLAD
            lastSelectedTypeId = currentSelectedTypeId
            needScrollToPosition(isFromWatermarkDownloadLoader)
        }
        // 限定水印下载完成后，如果当前图片是哈苏水印，但选择其他水印类型，不去刷新水印列表
        if (isFromWatermarkDownloadLoader.not() ||
            (WatermarkMasterStyle.isHasselFrame(currentSelectedStyleId).not() && WatermarkMasterStyle.isHasselText(currentSelectedStyleId).not()) ||
            (currentSelectedTypeId == WatermarkMasterVM.HASSELBLAD)
        ) {
            viewDataNtf?.notify(
                R.string.picture_editor_text_watermark_master_hasselblad
            )?.let {
                it.find { item ->
                    item.styleId == styleId
                }?.isSelected = true
                initWatermarkStyleListView(it)
            }
        }
    }

    private fun updateUiWhenFrameWatermark(styleId: String?, isFromWatermarkDownloadLoader: Boolean = false) {
        if (isFromWatermarkDownloadLoader.not()) {
            typeAdapter?.selectById(WatermarkMasterVM.FRAME)
            currentSelectedTypeId = WatermarkMasterVM.FRAME
            lastSelectedTypeId = currentSelectedTypeId
            needScrollToPosition(isFromWatermarkDownloadLoader)
        }
        // 限定水印下载完成后，如果当前图片是画框水印，但选择其他水印类型，不去刷新水印列表
        if (isFromWatermarkDownloadLoader.not() ||
            (WatermarkMasterStyle.isPersonalizeFrame(currentSelectedStyleId).not()) ||
            (currentSelectedTypeId == WatermarkMasterVM.FRAME)
        ) {
            viewDataNtf?.notify(
                R.string.picture_editor_text_watermark_master_frame
            )?.let {
                it.find { item ->
                    item.styleId == styleId
                }?.isSelected = true
                initWatermarkStyleListView(it)
            }
        }
    }

    private fun updateUiWhenRealmeBrandWatermark(styleId: String?) {
        typeAdapter?.selectById(WatermarkMasterVM.REALME_BRAND)
        currentSelectedTypeId = WatermarkMasterVM.REALME_BRAND
        lastSelectedTypeId = currentSelectedTypeId
        needScrollToPosition()
        viewDataNtf?.notify(
            R.string.picture_editor_text_watermark_master_realme_brand
        )?.let {
            it.find { item ->
                item.styleId == styleId
            }?.isSelected = true
            initWatermarkStyleListView(it)
        }
    }

    private fun updateUiWhenTextWatermark(styleId: String?, isFromWatermarkDownloadLoader: Boolean = false) {
        if (isFromWatermarkDownloadLoader.not()) {
            typeAdapter?.selectById(WatermarkMasterVM.TEXT)
            currentSelectedTypeId = WatermarkMasterVM.TEXT
            lastSelectedTypeId = currentSelectedTypeId
            needScrollToPosition(isFromWatermarkDownloadLoader)
        }

        // 限定水印下载完成后，如果当前图片是文字水印，但选择其他水印类型，不去刷新水印列表
        if (isFromWatermarkDownloadLoader.not() ||
            (WatermarkMasterStyle.isTextStyle(currentSelectedStyleId).not()) ||
            (currentSelectedTypeId == WatermarkMasterVM.TEXT)
        ) {
            viewDataNtf?.notify(
                R.string.picture_editor_text_watermark_master_text
            )?.let {
                it.find { item ->
                    item.styleId == styleId
                }?.isSelected = true
                initWatermarkStyleListView(it)
            }
        }
    }

    private fun updateUiWhenRestrictWatermark(styleId: String?, isFromWatermarkDownloadLoader: Boolean = false) {
        if (isFromWatermarkDownloadLoader.not()) {
            currentSelectedTypeData?.run {
                typeAdapter?.selectById(viewId)
                currentSelectedTypeId = viewId
            } ?: run {
                RESTRICT_BUTTON_VIEW_ID_LIST.firstOrNull()?.let { viewId ->
                    typeAdapter?.selectById(viewId)
                    currentSelectedTypeId = viewId
                }
            }
            lastSelectedTypeId = currentSelectedTypeId
            needScrollToPosition(isFromWatermarkDownloadLoader)
        }

        GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] updateUiWhenRestrictWatermark: " +
                "isFromWatermarkDownloadLoader$isFromWatermarkDownloadLoader, " +
                "currentSelectedTypeId${(currentSelectedTypeId == RESTRICT)}, " +
                "currentSelectedStyleId$currentSelectedStyleId" }
        // 限定水印下载完成后，如果当前图片是限定水印，但选择其他水印类型，不去刷新水印列表
        if (isFromWatermarkDownloadLoader.not() ||
            (WatermarkMasterStyle.isRestrictStyle(currentSelectedStyleId).not()) ||
            (RESTRICT_BUTTON_VIEW_ID_LIST.contains(currentSelectedTypeId))
        ) {
            viewDataNtf?.notify(
                R.string.picture_editor_text_watermark_master_restrict
            )?.let {
                it.find { item ->
                    item.styleId == styleId
                }?.isSelected = true

                val seriesId = vBus.get<String>(TOPIC_PRIVACY_WATERMARK_MASTER_CURRENT_SERIES_ID)
                val finalList = it.filter { style -> style.seriesId == seriesId }
                GLog.i(TAG, LogFlag.DL) { "[downloadRestrictWatermark] updateUiWhenRestrictWatermark:${it.size}" }
                initWatermarkStyleListView(finalList.toMutableList())
            }
        }
    }

    /**
     * 默认进入水印，水印类型滑动不需要滑动到焦点位置
     */
    private fun needScrollToPosition(isFromWatermarkDownloadLoader: Boolean = false) {
        if (needScrollToPosition && isFromWatermarkDownloadLoader.not()) {
            typeAdapter?.selectedPosition?.also {
                watermarkTypeListView?.scrollToPosition(it + 1)
            }
        } else {
            needScrollToPosition = true
        }
    }

    /**
     * 刷新隐私水印菜单
     */
    private fun updateUiWhenPrivacyWatermark(watermarkInfo: WatermarkInfo) {
        typeAdapter?.selectById(WatermarkMasterVM.PRIVACY)
        currentSelectedTypeId = WatermarkMasterVM.PRIVACY
        lastSelectedTypeId = currentSelectedTypeId
        selectedTypeListByViewId(currentSelectedTypeId)
        privacyListView?.visibility = View.VISIBLE
        watermarkStyleListView?.visibility = View.GONE
        watermarkSeriesListView?.visibility = View.GONE
        watermarkInfo.privacyWatermarkParams?.style?.let {
            privacyAdapter?.select(it.ordinal)
        }
    }

    private fun refreshWatermarkStyleState(data: MutableList<WatermarkStyleItemViewData>?) {
        data?.let {
            initWatermarkStyleListView(it)
            watermarkStyleListView?.requestLayout()
        }
    }

    private fun startFrameAnimation() {
        GLog.d(TAG, LogFlag.DF) { "startFrameAnimation" }
    }

    private fun onScaleAnimationEnd() {
        GLog.d(TAG, LogFlag.DF) { "onScaleAnimationEnd" }
    }

    private fun removeContainerView() {
        sectionBus.rootView.findViewById<ControlledRelativeLayout>(R.id.toolbar_container)?.apply {
            if (visibility == View.VISIBLE) {
                visibility = View.GONE
            }
        }
    }

    private fun recoverContainerView() {
        sectionBus.rootView.findViewById<ControlledRelativeLayout>(R.id.toolbar_container)?.apply {
            if (visibility == View.GONE) {
                visibility = View.VISIBLE
            }
        }
        sectionBus.rootView.findViewById<View>(R.id.right_masking)?.apply {
            visibility = View.GONE
        }
    }

    /**
     * 限定水印类型可见性变化
     */
    private fun onRestrictTypeVisibilityChanged(needShow: Boolean) {
        var itemIncrease: Int = 0
        if (needShow && (RestrictButtonConfigUtils.isOnLineTime())) {
            val itemCount = typeAdapter?.itemCount ?: 0
            val insertIndex = if (itemCount > 0) 1 else 0
            val currentItems = typeAdapter?.data?.filter { it.buttonInfo != null }

            if (currentItems.isNullOrEmpty()) {
                // 当前无有效按钮，直接添加所有预设按钮
                RESTRICT_BUTTON_LIST.forEach { item ->
                    typeAdapter?.addItem(item, insertIndex)
                }
                itemIncrease = RESTRICT_BUTTON_LIST.size
            } else {
                val currentSize = currentItems.size
                val expectedSize = RESTRICT_BUTTON_VIEW_ID_LIST.size

                when {
                    currentSize == expectedSize -> return // 数量一致，无需操作

                    currentSize > expectedSize -> {
                        // 下线：删除不在新列表中的项
                        val toRemove = currentItems.filterNot { it.viewId in RESTRICT_BUTTON_VIEW_ID_LIST }
                        toRemove.forEach { typeAdapter?.removeItem(it) }
                        itemIncrease = -(toRemove.size)
                    }

                    else -> {
                        // 直接从currentItems中映射出viewId列表
                        val currentItemViewIds = currentItems.map { it.viewId }.toMutableList()
                        // 上线：添加新加入的项
                        val toAdd = RESTRICT_BUTTON_LIST.filterNot { it.viewId in currentItemViewIds }
                        toAdd.forEach { item ->
                            typeAdapter?.addItem(item, insertIndex)
                        }
                        itemIncrease = toAdd.size
                    }
                }
            }

            val lastPosition = typeAdapter?.selectedPosition ?: INVALID_VALUE
            if ((WatermarkMasterStyle.isRestrictStyle(currentSelectedStyleId)) && (lastPosition == INVALID_VALUE)) {
                val typeButtonId = RestrictButtonConfigUtils.getTypeButtonIdByStyleId(currentSelectedStyleId)
                val viewId = RESTRICT_BUTTON_LIST.find { it.buttonInfo?.buttonId == typeButtonId }?.viewId
                if (viewId != null) {
                    typeAdapter?.selectById(viewId)
                }
                selectedStyleListByStyleId(currentSelectedStyleId)
            }
            selectedTypeListByViewId(currentSelectedTypeId, true, insertIndex, lastPosition, itemIncrease)
        } else {
            val toRemove = mutableListOf<WatermarkTypeItemViewData>()
            typeAdapter?.data?.forEach { item ->
                if (item.buttonInfo != null) toRemove.add(item)
            }
            toRemove.forEach { typeAdapter?.removeItem(it) }
            itemIncrease = -(toRemove.size)

            val lastPosition: Int
            if (LOCAL_BUTTON_VIEW_ID_LIST.contains(currentSelectedTypeId)) {
                lastPosition = typeAdapter?.data?.indexOfFirst { it.viewId == currentSelectedTypeId } ?: INVALID_VALUE
            } else {
                lastPosition = typeAdapter?.selectedPosition ?: INVALID_VALUE
                // 如果上一次选择的不是限定水印，也不是本地水印，可能选中了无，则要继续选中当前选项；如果上次一是限定水印，或者选择越界了，则选中第一个水印选项
                currentSelectedTypeId = if ((WatermarkMasterStyle.isRestrictStyle(currentSelectedStyleId).not())
                    && (lastPosition > INVALID_VALUE)
                    && (lastPosition < (typeAdapter?.data?.size ?: 0))) {
                    typeAdapter?.data?.get(lastPosition)?.viewId ?: INVALID_VALUE
                } else {
                    typeAdapter?.data?.get(MathUtils.ONE)?.viewId ?: INVALID_VALUE
                }
            }
            val position = lastPosition
            if ((position > INVALID_VALUE) && (position < (typeAdapter?.data?.size ?: 0))) {
                // 如果选项状态需要更新，需要刷新一下。
                typeAdapter?.data?.get(position)?.apply {
                    if (isSelected.not()) {
                        isSelected = true
                        typeAdapter?.selectedPosition = position
                        typeAdapter?.notifyItemChanged(position)
                    }
                }
            } else {
                GLog.e(TAG, LogFlag.DL) { "onRestrictTypeVisibilityChanged: position=$position, size=${typeAdapter?.data?.size}" }
            }
            selectedTypeListByViewId(currentSelectedTypeId, false, 0, lastPosition, itemIncrease, true)
        }
    }

    private fun updateStyleItemStatus(styleId: String, status: ItemStatus) {
        styleAdapter?.updateItemStatus(styleId, status)

        // 水印素材下载完成后，如果是最后一次点击选中项，执行选中逻辑
        if ((status == ItemStatus.DEFAULT) && (lastClickDownloadStyleId != null) && (styleId == lastClickDownloadStyleId)) {
            val position = styleAdapter?.getPositionByStyleId(styleId) ?: INVALID_VALUE
            onStyleItemClicked(position)

            styleAdapter?.selectedByStyleId(styleId)
            onStyleItemSelected(styleId)
        }
    }

    private fun updateStyleItemLoadingProgress(styleId: String, progress: Float) {
        styleAdapter?.updateItemLoadingProgress(styleId, progress)
    }

    override fun onResume() {
        super.onResume()
        actionTopicsNtf?.notify(true)
        GLog.d(TAG, LogFlag.DL) { "onResume currentSelectedTypeId=$currentSelectedTypeId" }
        watermarkUiBeanNtf?.notify(
            WatermarkMasterUIBean(
                id = WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED,
                selectTypeId = currentSelectedTypeId
            )
        )
    }

    override fun onPause() {
        super.onPause()
        actionTopicsNtf?.notify(false)
    }

    inner class OnTypeItemClickListener : BaseRecyclerAdapter.OnItemClickListener<WatermarkTypeItemViewData> {
        override fun onItemClick(view: View, position: Int, item: WatermarkTypeItemViewData?) {
            GLog.d(TAG, LogFlag.DF) { "TypeItem Click ${item?.itemId}" }
            // 如果哈苏水印isSelectable为false的话不走onItemSelected
            if ((view.id == WatermarkMasterVM.HASSELBLAD) && (item?.isSelectable?.not() == true)) {
                toastOnHasselbladItemClick()
                return
            }

            // 如果个性画框或限定水印isSelectable为false的话不走onItemSelected
            if (((view.id == WatermarkMasterVM.FRAME) || (RESTRICT_BUTTON_VIEW_ID_LIST.contains(view.id))) && (item?.isSelectable?.not() == true)) {
                toastOnFrameItemClick()
                return
            }

            if (view.id != WatermarkMasterVM.NONE) {
                currentSelectedTypeId = view.id
                lastSelectedTypeId = currentSelectedTypeId
                if (view.id != WatermarkMasterVM.PRIVACY) {
                    watermarkStyleListView?.visibility = View.VISIBLE
                    privacyListView?.visibility = View.GONE
                }
            } else {
                currentSelectedTypeId = WatermarkMasterVM.NONE
                styleAdapter?.setClickPosition(INVALID_VALUE)
                privacySelectPosition = INVALID_VALUE
                privacyAdapter?.clearSingleSelectedPosition()
                privacyAdapter?.notifyDataSetChanged()
            }
        }

        override fun onItemSelected(view: View, position: Int, item: WatermarkTypeItemViewData?) {
            needScrollToPosition = false
            currentSelectedTypeData = item
            lastClickDownloadStyleId = null
            if (view.id == WatermarkMasterVM.PRIVACY) {
                watermarkStyleListView?.visibility = View.GONE
                privacyListView?.visibility = View.VISIBLE
                privacyListView?.requestLayout()
                privacyAdapter?.select(privacySelectPosition)
            } else {
                if (view.id == WatermarkMasterVM.NONE) {
                    styleAdapter?.clearSingleSelectedPosition()
                    currentSelectedStyleId = TextUtil.EMPTY_STRING
                }
                if (privacyListView?.visibility == View.VISIBLE) {
                    watermarkStyleListView?.visibility = View.GONE
                } else {
                    watermarkStyleListView?.visibility = View.VISIBLE
                }
            }
            /* UX变更，个性画框不用显示系列的type列表，这里直接GONE
             *
             * // 个性画框要显示系列列表，点击无时，并不隐藏系列列表，点击其他水印列表时，隐藏系列列表
             * if (view.id == WatermarkMasterVM.FRAME) {
             *     watermarkSeriesListView?.visibility = View.VISIBLE
             * } else if (view.id != WatermarkMasterVM.NONE) {
             *     watermarkSeriesListView?.visibility = View.GONE
             * }
             */
            watermarkSeriesListView?.visibility = View.GONE
            if (view.id == WatermarkMasterVM.NONE) {
                watermarkSeriesTitleView?.hidTitle(false)
            }
            watermarkUiBeanNtf?.notify(
                WatermarkMasterUIBean(
                    id = WatermarkMasterUiBeanId.ON_WATERMARK_TYPE_SELECTED,
                    selectTypeId = view.id
                )
            )
        }

        override fun onItemUnselected(view: View?, position: Int, item: WatermarkTypeItemViewData?) {
            GLog.d(TAG, LogFlag.DF) { "TypeItem Unselected ${item?.itemId}" }
        }

        private fun toastOnHasselbladItemClick() {
            val bean = vBus.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)
            val watermarkInfo =
                bean?.currentWatermarkInfo ?: vBus.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.watermarkInfo
            if (watermarkInfo?.isHasselDevice() == true) {
                toastPanoramaOrShootInfo(bean, watermarkInfo)
            } else {
                ToastUtil.showShortToast(R.string.picture_editor_text_watermark_hasselblad_limit_toast)
                return
            }
        }

        private fun toastOnFrameItemClick() {
            val bean = vBus.get<WatermarkMasterUIBean>(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN)
            val watermarkInfo =
                bean?.currentWatermarkInfo ?: vBus.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.watermarkInfo
            toastPanoramaOrShootInfo(bean, watermarkInfo)
        }

        private fun toastPanoramaOrShootInfo(bean: WatermarkMasterUIBean?, watermarkInfo: WatermarkInfo?) {
            val isPanorama = bean?.isSupportFormatForHassel?.not() ?: false
            if (isPanorama) {
                // 暂不支持全景照片
                ToastUtil.showShortToast(R.string.picture_editor_text_watermark_panorama_limit_toast)
                return
            }

            val isNotSupportAddHasselAndFrame = (watermarkInfo?.isSupportAddHasselOrFrame(context) == false)
            val deviceNameValid = (watermarkInfo?.device?.deviceName?.isNotBlank() == true)
            GLog.d(TAG, LogFlag.DL) {
                "[onItemClick] isNotSupportAddHasselAndFrame=$isNotSupportAddHasselAndFrame, deviceNameValid=$deviceNameValid"
            }
            val messageId = when {
                //未获取到机型名称时是弹出“未获取到照片详细信息”
                deviceNameValid.not() ->
                    R.string.picture3d_editor_toast_photo_detail_info_not_acquired

                //当前设备为oneplus时弹出“仅支持一加设备拍摄的照片”
                ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND, false) && isNotSupportAddHasselAndFrame ->
                    R.string.picture3d_editor_only_support_oneplus_device_shoot

                //当前设备为oppo时弹出“仅支持 OPPO 设备拍摄的照片”
                ConfigAbilityWrapper.getBoolean(IS_OPPO_BRAND, false) && isNotSupportAddHasselAndFrame ->
                    R.string.picture3d_editor_only_support_oppo_device_shoot

                else -> R.string.picture3d_editor_toast_photo_detail_info_not_acquired
            }
            ToastUtil.showShortToast(messageId)
        }
    }

    inner class OnStyleItemClickListener : BaseRecyclerAdapter.OnItemClickListener<WatermarkStyleItemViewData> {
        override fun onItemClick(view: View?, position: Int, item: WatermarkStyleItemViewData) {
            GLog.d(TAG, LogFlag.DF) { "styleItem Click ${item.itemId}, position=$position" }
            onStyleItemClicked(position)

            if (item.status == ItemStatus.DEFAULT && item.isSelected) {
                notifyEnteringPersonalizedSection(item)
            }
        }

        override fun onItemSelected(view: View, position: Int, item: WatermarkStyleItemViewData?) {
            isSmoothScrolling = true
            //限定水印样式说明赋值
            currentRestrictTitle = item?.title
            currentRestrictDescription = item?.description
            currentRestrictDescription1 = item?.descr1
            currentRestrictDescription2 = item?.descr2
            onStyleItemSelected(item?.styleId)
        }

        override fun onItemUnselected(view: View?, position: Int, item: WatermarkStyleItemViewData) {
            GLog.d(TAG, LogFlag.DF) { "styleItem Unselected ${item.itemId}" }
        }
    }

    inner class OnSeriesTitleItemClickListener : BaseRecyclerAdapter.OnItemClickListener<TextViewData> {
        override fun onItemClick(view: View, position: Int, item: TextViewData) {
            GLog.d(TAG, LogFlag.DF) { "seriesItem Click ${item.itemId}" }
        }

        override fun onItemSelected(view: View, position: Int, item: TextViewData) {
            styleAdapter?.getSeriesPosition(WatermarkMasterStyle.SERIES_ARRAY[position])?.let {
                isSmoothScrolling = true
                smoothScroller.targetPosition = it
                watermarkStyleListView?.layoutManager?.startSmoothScroll(smoothScroller)
            }
        }

        override fun onItemUnselected(view: View, position: Int, item: TextViewData) = Unit
    }

    /**
     * 样式被点击后回调
     */
    private fun onStyleItemClicked(position: Int) {
        styleAdapter?.setClickPosition(position)
        typeAdapter?.selectById(lastSelectedTypeId)
        val position = typeAdapter?.selectedPosition ?: INVALID_VALUE
        watermarkTypeListView?.scrollToPosition(position + 1)
        currentSelectedTypeId = lastSelectedTypeId
        if (currentSelectedTypeId != WatermarkMasterVM.PRIVACY) {
            privacySelectPosition = INVALID_VALUE
            privacyAdapter?.clearSingleSelectedPosition()
            privacyAdapter?.notifyDataSetChanged()
        }
    }

    /**
     * 移除水印
     */
    private fun removeWatermark() {
        watermarkUiBeanNtf?.notify(WatermarkMasterUIBean(id = WatermarkMasterUiBeanId.REMOVE_WATERMARK))
    }

    /**
     * 样式被选中后回调
     */
    inner class OnRealmeBrandTitleItemClickListener : BaseRecyclerAdapter.OnItemClickListener<TextViewData> {
        override fun onItemClick(view: View, position: Int, item: TextViewData) {
            GLog.d(TAG, LogFlag.DF) { "RealmeBrand Click ${item.itemId}" }
        }

        override fun onItemSelected(view: View, position: Int, item: TextViewData) {
            styleAdapter?.getSeriesPosition(WatermarkMasterStyle.REALME_BRAND_ARRAY[position])?.let {
                isSmoothScrolling = true
                smoothScroller.targetPosition = it
                watermarkStyleListView?.layoutManager?.startSmoothScroll(smoothScroller)
            }
        }

        override fun onItemUnselected(view: View, position: Int, item: TextViewData) = Unit
    }

    private fun onStyleItemSelected(styleId: String?) {
        currentSelectedStyleId = styleId ?: TextUtil.EMPTY_STRING
        selectedSeriesListByStyleId(currentSelectedStyleId)
        lastClickDownloadStyleId = null
        watermarkUiBeanNtf?.notify(
            WatermarkMasterUIBean(
                id = WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_SELECTED,
                watermarkStyleId = styleId
            )
        )
    }

    private fun notifyEnteringPersonalizedSection(item: WatermarkStyleItemViewData) {
        if (TextUtils.isEmpty(item.styleId).not() && (item.status == ItemStatus.DEFAULT)) {
            enteringPersonalizedSection = true
        }
        watermarkUiBeanNtf?.notify(
            WatermarkMasterUIBean(
                id = WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_MASK_CLICK,
                watermarkStyleId = item.styleId,
                watermarkStyleStatus = item.status
            )
        )
    }

    private fun selectAndResetTypeId(typeId: Int) {
        typeAdapter?.selectById(typeId)
        val position = typeAdapter?.selectedPosition ?: INVALID_VALUE
        watermarkTypeListView?.scrollToPosition(position + 1)
        currentSelectedTypeId = typeId
    }

    private val onPrivacyItemClickListener: BaseRecyclerAdapter.OnItemClickListener<EditorMenuItemViewData?> =
        object : BaseRecyclerAdapter.OnItemClickListener<EditorMenuItemViewData?> {
            override fun onItemClick(view: View, position: Int, item: EditorMenuItemViewData?) {
                if (view.id == WatermarkMasterVM.PRIVACY_CUSTOM) {
                    lastPosition = privacyAdapter?.selectedPosition ?: INVALID_VALUE
                    lastPositionIsCustom = lastPosition == PrivacyWatermarkStyle.CUSTOM.ordinal
                    contentText = getPrivacyWatermarkCustomInfo()
                    showPrivacyWatermarkCustomDialogBuilder(activity, COUNT_MAX_WATERMARK_CUSTOM_INFO, confirmListener, inputListener)
                    watermarkUiBeanNtf?.notify(
                        WatermarkMasterUIBean(
                            id = WatermarkMasterUiBeanId.CHANGE_PRIVACY_WATERMARK_CUSTOM_INFO,
                            privacyContentText = contentText
                        )
                    )
                } else {
                    resetCurrentSelectedStyleId()
                    selectAndResetTypeId(lastSelectedTypeId)
                }
            }

            override fun onItemSelected(view: View, position: Int, item: EditorMenuItemViewData?) {
                if (view.id != WatermarkMasterVM.PRIVACY_CUSTOM) {
                    watermarkUiBeanNtf?.notify(
                        WatermarkMasterUIBean(
                            id = WatermarkMasterUiBeanId.ON_PRIVACY_WATERMARK_STYLE_SELECTED,
                            privacyLastSelectPosition = position
                        )
                    )
                }
                privacySelectPosition = position
            }

            override fun onItemUnselected(view: View, position: Int, item: EditorMenuItemViewData?) {
                // do nothing
            }
        }

    private fun getConfigMode(config: AppUiResponder.AppUiConfig?): Int {
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        val isMedium =
            context?.let { isLandscape && ScreenUtils.isMiddleAndLargeScreen(it) && ScreenUtils.isMiddleScreenWidth(it) } ?: false
        val isLarge = context?.let { isLandscape && ScreenUtils.isMiddleAndLargeScreen(it) && ScreenUtils.isLargeScreenWidth(it) } ?: false
        val isInMultiWindow = config?.isInMultiWindow?.current == true
        val isInFloatingWindow = config?.isInFloatingWindow?.current == true
        return when {
            isInMultiWindow || isInFloatingWindow -> SMALL_WINDOW_MODE
            isLarge -> LARGE_MODE
            isMedium -> MEDIUM_MODE
            else -> PORTRAIT_MODE
        }
    }

    internal companion object {
        private const val TAG = "WatermarkMasterSection"
        private const val COUNT_MAX_WATERMARK_CUSTOM_INFO = 20
        private const val SCROLL_HORIZONTALLY_MIN = 3
        private const val PORTRAIT_MODE = 0
        private const val MEDIUM_MODE = 1
        private const val LARGE_MODE = 2
        private const val SMALL_WINDOW_MODE = 3
        private val PORTRAIT_LAYOUT_ID = R.layout.picture3d_editor_watermark_master_toolbar
        private val LANDSCAPE_LAYOUT_ID = R.layout.picture3d_editor_watermark_master_toolbar_landscape
        const val TOOL_CONTAINER_FADE_COMMON_ANIMATOR_DURATION = 180L
        const val TOOL_CONTAINER_FADE_TRANSLATION_X_DURATION = 450L
        const val TOOL_CONTAINER_FADE_IN_FROM_PERSONALIZED_ANIMATOR_DURATION = 250L
        const val PREVIEW_RECT_ANIMATION_DELAY_TIME = 50L

        val SPEC = SectionSpec(WatermarkMasterSection::class.java)
    }
}