/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ClassMethodCache.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** huca<PERSON><PERSON>@Apps.Gallery		2020/08/14		1.0		OPLUS_ARCH_BUS
 *********************************************************************************/

package com.oplus.gallery.bus_lib.finders

import com.oplus.gallery.bus_lib.annotations.Subscribe
import com.oplus.gallery.bus_lib.beans.MethodInfo
import com.oplus.gallery.bus_lib.utils.BusUtil.isPipelineAnnotion

object ClassMethodCache {
    private val cacheMethodMap = HashMap<Class<*>, HashSet<MethodInfo>>()

    @Synchronized
    fun findMethodByClass(clazz: Class<*>): MutableSet<MethodInfo> {
        return cacheMethodMap[clazz] ?: clazz.declaredMethods.filter {
            it.isPipelineAnnotion()
        }.mapTo(HashSet()) {
            val subscribe = it.getAnnotation(Subscribe::class.java)!!
            MethodInfo(it, subscribe.supportSticky, subscribe.threadType)
        }.apply {
            cacheMethodMap[clazz] = this
        }
    }
}