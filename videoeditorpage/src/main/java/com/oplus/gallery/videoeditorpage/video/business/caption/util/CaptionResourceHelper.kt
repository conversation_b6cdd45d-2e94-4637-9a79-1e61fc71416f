/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : CaptionResourceHelper.kt
 ** Description : 字幕资源工具类
 ** Version     : 1.0
 ** Date        : 2025/6/23 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/6/23  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.util

import android.annotation.SuppressLint
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.file.IniFileParser
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest

/**
 * 字幕资源工具类，负责字幕资源文件的内置资源copy、网络zip资源MD5校验及解压、文件删除、翻译ini文件解析等
 *
 */
internal object CaptionResourceHelper {
    private const val TAG = "CaptionResourceHelper"

    /**
     * 字幕样式资源下载
     */
    private const val RESOURCE_CAPTION_STYLE_DIRECTORY = "/caption/styles/download"

    /**
     * 字幕字体资源下载
     */
    private const val RESOURCE_CAPTION_FONT_DIRECTORY = "/caption/fonts/download"

    /**
     * 文件缓存大小
     */
    private const val BUFFER_SIZE = 1024

    /**
     * zip算法需要用到的一些魔数定义
     */
    private const val ZIP_FILE_MINIMUM_SIZE = 4L
    private const val ZIP_SIGNATURE_SIZE = 4
    private const val ZIP_SIGNATURE_FIRST_BYTE = 0x50.toByte()
    private const val ZIP_SIGNATURE_SECOND_BYTE = 0x4B.toByte()
    private const val ZIP_SIGNATURE_THIRD_BYTE = 0x03.toByte()
    private const val ZIP_SIGNATURE_FOURTH_BYTE = 0x04.toByte()
    private const val ZIP_SIGNATURE_INDEX_ZERO = 0
    private const val ZIP_SIGNATURE_INDEX_ONE = 1
    private const val ZIP_SIGNATURE_INDEX_TWO = 2
    private const val ZIP_SIGNATURE_INDEX_THREE = 3

    /**
     * 获取文件的后缀名
     */
    @JvmStatic
    fun getFileExtension(fileName: String): String {
        val lastDotIndex = fileName.lastIndexOf('.')
        return if (lastDotIndex != -1 && lastDotIndex < fileName.length - 1) {
            fileName.substring(lastDotIndex)
        } else {
            ""
        }
    }

    /**
     * 判断当前文件是否存在
     */
    @JvmStatic
    fun isFileExists(filePath: String): Boolean {
        return FileOperationUtils.isFileExist(filePath)
    }

    /**
     * 删除文件
     * @param filePath 文件路径
     */
    @JvmStatic
    fun deleteFile(filePath: String): Boolean {
        return FileOperationUtils.deleteExistFile(filePath)
    }

    /**
     * 检查文件是否为有效的 ZIP 文件
     */
    @JvmStatic
    fun isZipFile(file: File): Boolean {
        if (file.length() < ZIP_FILE_MINIMUM_SIZE) return false

        FileInputStream(file).use { fis ->
            val signature = ByteArray(ZIP_SIGNATURE_SIZE)
            fis.read(signature)

            return signature[ZIP_SIGNATURE_INDEX_ZERO] == ZIP_SIGNATURE_FIRST_BYTE &&
                    signature[ZIP_SIGNATURE_INDEX_ONE] == ZIP_SIGNATURE_SECOND_BYTE &&
                    signature[ZIP_SIGNATURE_INDEX_TWO] == ZIP_SIGNATURE_THIRD_BYTE &&
                    signature[ZIP_SIGNATURE_INDEX_THREE] == ZIP_SIGNATURE_FOURTH_BYTE
        }
    }

    /**
     * 计算文件的 MD5 值
     * @SuppressLint("UnsafeHashAlgorithmDetector") 避免安全漏洞误报
     */
    @JvmStatic
    @SuppressLint("UnsafeHashAlgorithmDetector")
    fun calculateMD5(file: File): String {
        val md = MessageDigest.getInstance("MD5")
        FileInputStream(file).use { fis ->
            val buffer = ByteArray(BUFFER_SIZE)
            var read: Int
            while (fis.read(buffer).also { read = it } != -1) {
                md.update(buffer, 0, read)
            }
        }
        return md.digest().joinToString("") { "%02x".format(it) }
    }

    /**
     * 解压字体zip文件
     * @param zipFilePath 压缩文件路径
     */
    @JvmStatic
    fun unzipFontFile(zipFilePath: String): String {
        val destDirectory = ContextGetter.context.filesDir.absolutePath + RESOURCE_CAPTION_FONT_DIRECTORY
        return FileOperationUtils.unzipFolder(zipFilePath, destDirectory)?.first()?.absolutePath ?: return TextUtil.EMPTY_STRING
    }

    /**
     * 解析字体翻译的ini文件
     * @param filePath 字体翻译ini文件路径
     * @return 返回解析后的map, key为对应的多语言区域编码, value为翻译后的字体名称词条
     */
    @JvmStatic
    fun parseFontNameTransFile(filePath: String): Map<String, String> {
        val resourceParser = IniFileParser.readIni(filePath)
        val resourceCodes = resourceParser.sections()
        val resourceMap = HashMap<String, String>()
        resourceCodes.forEach { section ->
            val key = section.key
            val resourceList = resourceParser.getParamsWithSplitBySection(key)
            resourceMap[key] = resourceList
        }
        return resourceMap
    }

    /**
     * 解压样式zip文件
     * @param zipFilePath 压缩文件路径
     */
    @JvmStatic
    fun unzipStyleFile(zipFilePath: String): String {
        val destDirectory = ContextGetter.context.filesDir.absolutePath + RESOURCE_CAPTION_STYLE_DIRECTORY
        return FileOperationUtils.unzipFolder(zipFilePath, destDirectory)?.first()?.absolutePath ?: return TextUtil.EMPTY_STRING
    }

    /**
     * 获取字体名称文件存储目录
     */
    @JvmStatic
    fun getFontEntryIniFileDirectory(): String {
        return ContextGetter.context.filesDir.absolutePath + RESOURCE_CAPTION_FONT_DIRECTORY
    }
}