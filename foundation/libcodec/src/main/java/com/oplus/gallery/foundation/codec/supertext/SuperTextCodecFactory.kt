/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SuperTextCodecFactory
 ** Description: SuperTextCodecFactory
 *  超级文本编解码的工厂类
 ** Version: 1.0
 ** Date : 2022/5/17
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D        2022/05/17    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.foundation.codec.supertext

import com.oplus.gallery.foundation.security.CrcUtil

object SuperTextCodecFactory {

    /**
     * 获取编解码器的类型
     * 1.0图片 ： EnhanceTextImageCodec
     * 非1.0图片：SuperTextImageCodec
     * @param filePath 文件路径
     * @param mediaId 媒体库_id
     * @param parseLengthByExtender 是否由FileExtender来解析文件的原始长度
     * 前提：只有相机拍摄的图片既支持超级文本格式，又支持扩展文件格式（先超级文本，再扩展文件）
     * 1、如果是超级文本1.0的tagFlags，说明是相机拍摄的超级文本图片，parseLengthByExtender为true；
     * 2、如果非超级文本1.0的tagFlags，说明非相机拍摄的超级文本图片，这里有几种情况：
     *  a.三方图片未经过超级文本2.0编辑，因为三方图片没有扩展信息，此时直接用超级文本格式来解析即可
     *  b.三方图片经过超级文本2.0编辑，因为保存后是超级文本格式，此时直接用超级文本格式来解析即可
     *  c.相机拍摄的没带扩展信息的图片，非超级文本格式，此时直接用超级文本格式来解析即可判断
     *  d.相机拍摄的没带扩展信息的图片经过超级文本2.0编辑，因为保存后是超级文本格式，此时直接用超级文本格式来解析即可判断
     *  e.相机拍摄的带有扩展信息的图片，非超级文本格式，此时直接用超级文本格式来解析即可判断
     *  f.相机拍摄的带有扩展信息的图片经过超级文本2.0编辑，因为保存后会清除扩展信息，此时直接用超级文本格式来解析即可判断
     * 综上，只有相机拍摄的超级文本格式照片，parseLengthByExtender才为true
     */
    @JvmStatic
    fun create(filePath: String, mediaId: Int = -1, parseLengthByExtender: Boolean = true): ISuperTextImageCodec {
        val magicNumber = SuperTextImageUtils.getMagicNumber(filePath, mediaId, parseLengthByExtender)
        return if (CrcUtil.byteEquals(magicNumber, SuperTextProperties.ENHANCE_MAGIC_NUMBER)) {
            EnhanceTextImageCodec(filePath, magicNumber)
        } else {
            SuperTextImageCodec(filePath, magicNumber)
        }
    }
}