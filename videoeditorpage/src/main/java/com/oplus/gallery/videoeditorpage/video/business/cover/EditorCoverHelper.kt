/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorCoverHelper
 ** Description: 编辑封面辅助类
 ** Version: 1.0
 ** Date : 2025/07/21
 ** Author: tianzhang
 ** TAG:EditorCoverState
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** tianzhang                           2025/07/21    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.cover

import android.app.Activity.RESULT_OK
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.media.ThumbnailUtils
import android.net.Uri
import androidx.annotation.WorkerThread
import androidx.lifecycle.coroutineScope
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectType
import com.oplus.gallery.business_lib.selectionpage.SelectionFrom
import com.oplus.gallery.business_lib.util.decodeRawUriFromNvmPath
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.utlis.FileUtils
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity
import com.oplus.gallery.videoeditorpage.video.business.output.task.GetFrameTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import kotlin.system.measureTimeMillis

/**
 * 编辑封面辅助类 异步获取封面缩略图，原图。
 */
object EditorCoverHelper {

    private const val TAG = "EditorCoverHelper"

    /**
     * 跳转至相册选图时的请求Key
     */
    private const val KEY_REQUEST_PICKER = "videoEditorPicker.requestKey"

    /**
     * 相册选择的封面时间戳
     */
    const val COVER_TIMESTAMP = 0L

    /**
     * 设置默认封面的协程
     */
    private var job: Job? = null

    /**
     *  根据美摄接口grabImageFromTimelineAsync 函数的定义 默认为0是原图，
     *  否则为默认的裁剪缩略宽度
     */
    private const val ORIGINAL_SIZE = 0

    /**
     * 默认显示缩略图，封面缩略图的size_width
     */
    private val MICRO_THUMBNAIL_TARGET_SIZE: Int
        get() = ContextGetter.context.resources.getDimensionPixelSize(R.dimen.dp_72)

    /**
     * 用于当前视频轨道上的片段个数，为一时在进行Olive判断
     */
    private const val CLIP_LIST_SIZE = 1

    /**
     * 资源释放
     */
    fun release() {
        if (job?.isActive == true) {
            job?.cancel()
        }
        job = null
    }

    /**
     * 异步耗时
     * 获取封面的Bitmap
     */
    @WorkerThread
    fun coverBitmapJob(
        context: Context,
        editorEngine: EditorEngine,
        coverMode: CoverMode,
        frameTime: Long,
        filePath: String?,
        isThumbnail: Boolean = true,
        request: (coverBitmap: Bitmap?, frameTime: Long?, filePath: String?) -> Unit
    ) {
        if (filePath == null) {
            when (coverMode) {
                CoverMode.DEFAULT_COVER -> requestDefaultCover(context, editorEngine, isThumbnail, request)

                CoverMode.VIDEO_COVER -> requestVideoCover(context, editorEngine, frameTime, isThumbnail, request)

                CoverMode.GALLERY_COVER -> requestGalleryCover(context, editorEngine, isThumbnail, request)
            }
        } else {
            editorCoverJob(context, editorEngine, coverMode, frameTime, filePath, isThumbnail, request)
        }
    }

    /**
     * 设置封面的协程
     */
    private fun editorCoverJob(
        context: Context,
        editorEngine: EditorEngine,
        coverMode: CoverMode,
        frameTime: Long,
        pathStr: String?,
        isThumbnail: Boolean,
        request: (coverBitmap: Bitmap?, frameTime: Long?, filePath: String?) -> Unit
    ) {
        val editorActivity = context as? EditorActivity ?: run {
            GLog.d(TAG, LogFlag.DL, "[gotoGallerySelection] context is not EditorActivity. context = ${context.javaClass.name}")
            return
        }
        if (job?.isActive == true) {
            job?.cancel()
        }
        val weakRefContext = WeakReference(context)
        val weakRefEditorEngine = WeakReference(editorEngine)
        job = editorActivity.lifecycle.coroutineScope.launch(Dispatchers.IO) {
            val cost = measureTimeMillis {
                var coverBitmap: Bitmap? = null
                var coverTime: Long = frameTime
                when (coverMode) {
                    CoverMode.DEFAULT_COVER -> {
                        weakRefEditorEngine.get()?.let {
                            isOliveCover(it) { isOlive, filePath ->
                                // 这里不能用视频抽帧，抽出的图比原来的封面图偏红
                                if (isOlive && (filePath != null)) {
                                    oliveCover(filePath) { bitmap, time ->
                                        bitmap?.let {
                                            val originBitmap = bitmapFilling(bitmap, weakRefEditorEngine.get())
                                            coverBitmap = if (isThumbnail) createThumbnailBitmap(originBitmap) else originBitmap
                                            time?.let { coverTime = time }
                                        }
                                    }
                                } else {
                                    val outputData = GetFrameTask(
                                        frameTime,
                                        weakRefEditorEngine.get(),
                                        null,
                                        if (isThumbnail) MICRO_THUMBNAIL_TARGET_SIZE else ORIGINAL_SIZE
                                    ).run()
                                    coverBitmap = outputData.uhdrBitmap
                                }
                            }
                        }
                    }

                    CoverMode.VIDEO_COVER -> {
                        val outputData = GetFrameTask(
                            frameTime,
                            weakRefEditorEngine.get(),
                            null,
                            if (isThumbnail) MICRO_THUMBNAIL_TARGET_SIZE else ORIGINAL_SIZE
                        ).run()
                        coverBitmap = outputData.uhdrBitmap
                    }

                    CoverMode.GALLERY_COVER -> {
                        Path.fromString(pathStr)?.let { path ->
                            LocalMediaDataHelper.getLocalMediaItem(path, true)?.let { mediaItem ->
                                var bitmap: Bitmap? = null
                                if (mediaItem.isOlivePhoto()) {
                                    oliveCover(mediaItem.filePath) { oliveBitmap, _ ->
                                        oliveBitmap?.let { bitmap = it }
                                    }
                                } else {
                                    bitmap = FileUtils.getBitmapFromJsonPath(weakRefContext.get(), mediaItem.filePath)
                                }
                                val originBitmap = bitmapFilling(bitmap, weakRefEditorEngine.get())
                                coverBitmap = if (isThumbnail) createThumbnailBitmap(originBitmap) else originBitmap
                            } ?: run { GLog.e(TAG, LogFlag.DL, "[editorCoverJob] mediaItem is null.") }
                        } ?: run { GLog.e(TAG, LogFlag.DL, "[editorCoverJob]: path is null.") }
                    }
                }

                // 需要再次判断协程的活跃状态，防止协程取消后继续执行
                if (job?.isActive == true) {
                    withContext(Dispatchers.Main) {  // 确保回调在主线程
                        request(coverBitmap, coverTime, pathStr)
                    }
                }
            }
            GLog.d(TAG, LogFlag.DL, "[captureFrame] GetFrameTask time cost = ${cost}")
        }
    }

    /**
     * 默认封面的业务
     */
    private fun requestDefaultCover(
        context: Context,
        editorEngine: EditorEngine,
        isThumbnail: Boolean,
        callback: (coverBitmap: Bitmap?, frameTime: Long?, filePath: String?) -> Unit
    ) {
        if ((editorEngine.getCurrentTimeline().coverMode == CoverMode.DEFAULT_COVER)
            && ((job?.isActive != true) || (job == null))
        ) {
            // 默认封面 调用频繁，防止极限并发时ITimeline数据不同步，覆盖掉相册，视频的封面。
            editorCoverJob(context, editorEngine, CoverMode.DEFAULT_COVER, COVER_TIMESTAMP, null, isThumbnail, callback)
        }
    }

    /**
     * 从视频抽帧的封面的业务
     */
    private fun requestVideoCover(
        context: Context,
        editorEngine: EditorEngine,
        frameTime: Long,
        isThumbnail: Boolean,
        callback: (coverBitmap: Bitmap?, frameTime: Long?, filePath: String?) -> Unit
    ) {
        editorCoverJob(context, editorEngine, CoverMode.VIDEO_COVER, frameTime, null, isThumbnail, callback)
    }

    /**
     * 从相册选择封面的业务
     */
    private fun requestGalleryCover(
        context: Context,
        editorEngine: EditorEngine,
        isThumbnail: Boolean,
        callback: (coverBitmap: Bitmap?, frameTime: Long?, filePath: String?) -> Unit
    ) {
        val editorActivity = context as? EditorActivity ?: run {
            GLog.d(TAG, LogFlag.DL, "[gotoGallerySelection] context is not EditorActivity. context = ${context.javaClass.name}")
            return
        }
        gotoGallerySelectionCover(editorActivity) { pathStr ->
            editorCoverJob(context, editorEngine, CoverMode.GALLERY_COVER, COVER_TIMESTAMP, pathStr, isThumbnail, callback)
        }
    }

    /**
     * 判断当前是否需要显示实况封面
     * 单素材为实况图时为实况图封面帧，其他默认视频首帧为封面帧
     */
    private fun isOliveCover(editorEngine: EditorEngine, callback: (isOlive: Boolean, filePath: String?) -> Unit) {
        if (editorEngine.currentTimeline?.videoTrackList?.first()?.clipList?.size == CLIP_LIST_SIZE) {
            val clip = editorEngine.currentTimeline?.videoTrackList?.first()?.clipList?.first()
            val url = clip?.filePath?.let { decodeRawUriFromNvmPath(it) }
            url?.let {
                LocalMediaDataHelper.getLocalMediaItem(Uri.parse(url))?.let { mediaItem ->
                    if (mediaItem.isOlivePhoto()) {
                        return callback(true, mediaItem.filePath)
                    }
                } ?: run { GLog.e(TAG, LogFlag.DL, "[isOliveCover] mediaItem is null.") }
            } ?: run { GLog.d(TAG, LogFlag.DL, "[isOliveCover] clip is video.") }
        }
        callback(false, null)
    }

    /**
     * 获取缩略图
     */
    private fun createThumbnailBitmap(srcBitmap: Bitmap?): Bitmap? {
        if (srcBitmap == null) {
            GLog.d(TAG, LogFlag.DL, "[thumbnail]: srcBitmap is null.")
            return null
        }
        val height = srcBitmap.height * MICRO_THUMBNAIL_TARGET_SIZE / srcBitmap.width
        return ThumbnailUtils.extractThumbnail(srcBitmap, MICRO_THUMBNAIL_TARGET_SIZE, height)
    }

    /**
     * 内部跳至相册选择封面
     * @param invoke 回调出图片自定义的相对路径
     */
    private fun gotoGallerySelectionCover(activity: EditorActivity, invoke: (pathStr: String) -> Unit) {
        Starter.DialogFragmentStarter<PanelDialog>(
            activity.supportFragmentManager,
            bundle = SelectInputData(
                canShowChecked = true,
                title = activity.getString(R.string.videoeditor_settings_cover_select_project),
                selectType = SelectType.IMAGE,
                fromWhere = SelectionFrom.VIDEO_EDITOR
            ).createBundle().also {
                it.putString(KEY_REQUEST_KEY, KEY_REQUEST_PICKER)
            },
            postCard = PostCard(SELECTION_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(KEY_REQUEST_PICKER) { _, bundle ->
                GLog.d(TAG, LogFlag.DL, "[gotoGallerySelectionCover] result = $bundle")
                if (bundle.getInt(KEY_RESULT_CODE) != RESULT_OK) return@setFragmentResultListenerSafety
                val pathStr = bundle.getString(KEY_RESULT_DATA) ?: return@setFragmentResultListenerSafety
                invoke(pathStr)
            }
        }
    }

    /**
     * 获取实况的封面
     */
    private fun oliveCover(filePath: String, callback: (bitmap: Bitmap?, time: Long?) -> Unit) {
        runCatching {
            val decoder: OLiveDecode = OLiveDecode.create(filePath)
            val oLivePhoto = decoder.decode()
            val time: Long = oLivePhoto?.coverTimeInUs ?: 0L
            decoder.getCoverStream()?.use { input ->
                val bitmap = BitmapFactory.decodeStream(input)
                return callback.invoke(bitmap, time)
            } ?: let {
                GLog.e(TAG, LogFlag.DL, "[oliveCover] error!! inputStreamSrc is null, oriOliveDecode:$decoder")
                return callback.invoke(null, null)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "[oliveCover] decodeOri = ${it.message}" }
        }
    }

    /**
     * 画幅背景填充
     * @param srcBitmap 源位图
     * @param editorEngine 视频编辑总引擎
     */
    private fun bitmapFilling(srcBitmap: Bitmap?, editorEngine: EditorEngine?): Bitmap? {
        if (srcBitmap == null || editorEngine == null) {
            GLog.d(TAG, LogFlag.DL, "[bitmapFilling]: srcBitmap is null.")
            return null
        }
        val desWidth = editorEngine.currentTimeline.width.toFloat()
        val desHeight = editorEngine.currentTimeline.height.toFloat()
        val srcWidth = srcBitmap.width
        val srcHeight = srcBitmap.height
        val enlargeWidth = srcWidth * desHeight / srcHeight
        val scale = if (enlargeWidth > desWidth) {
            desWidth / srcWidth
        } else {
            desHeight / srcHeight
        }
        // 矩阵 位图缩放
        val matrix = Matrix()
        matrix.postScale(scale, scale)
        val bitmap = Bitmap.createBitmap(srcBitmap, 0, 0, srcWidth, srcHeight, matrix, true)
        val left = ((desWidth - bitmap.width) / 2).toInt()
        val top = ((desHeight - bitmap.height) / 2).toInt()
        val paint = Paint()
        // 创建画幅同尺寸的位图
        val bg = Bitmap.createBitmap(desWidth.toInt(), desHeight.toInt(), Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bg)
        // 绘制背景 默认黑色
        paint.color = Color.BLACK
        // 获取轨道第一个视频片段的背景色 默认不操作画幅 background为空，
        editorEngine.currentTimeline.getVideoTrack(0)?.let { track ->
            if (track.clipList.isNotEmpty()) {
                track.clipList[0].background?.let { paint.color = it.color }
            }
        }
        canvas.drawRect(0f, 0f, desWidth, desHeight, paint)
        // 绘制位图
        canvas.drawBitmap(bitmap, left.toFloat(), top.toFloat(), paint)
        return bg
    }

    enum class CoverMode {
        DEFAULT_COVER,  // 默认封面
        VIDEO_COVER,    // 视频封面
        GALLERY_COVER   // 相册封面
    }
}