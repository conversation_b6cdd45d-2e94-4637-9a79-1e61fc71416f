/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoPageManagementViewModel.kt
 ** Description : 大图页面管理
 ** Version     : 1.0
 ** Date        : 2022/02/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery              2022/02/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.viewmodel.pagemanagement

import android.app.Activity
import android.app.KeyguardManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Resources
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.IBinder
import android.util.Size
import android.view.Surface
import android.view.Surface.ROTATION_0
import android.view.Surface.ROTATION_180
import android.view.Surface.ROTATION_270
import android.view.Surface.ROTATION_90
import android.view.Window
import androidx.annotation.VisibleForTesting
import androidx.core.graphics.toRect
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.addon.app.OplusJankWrapper
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_ENTER_PHOTO_ANIMATION
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_ENTER_PHOTO_FROM_CAMERA_OTHER
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_ENTER_PHOTO_FROM_OTHER
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_EXIT_PHOTO_ANIMATION
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_EXIT_PHOTO_FROM_CAMERA_OTHER
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_EXIT_PHOTO_FROM_CAMERA_SEAMLESS
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_PHOTO_ANIMATION
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_INTEGRATIONUI
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_OTHER
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_SEAMLESS
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_PHOTO_FROM_OTHER
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_EXIT_PHOTO_ANIMATION
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_INTEGRATIONUI
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_OTHER
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_SEAMLESS
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_EXIT_PHOTO_FROM_OTHER
import com.oplus.gallery.addon.view.WindowManagerWrapper
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_CHAIN_FROM_CAMERA
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.transition.AnimateStyle
import com.oplus.gallery.basebiz.transition.PageTransitionMaker.PageActivityTransition.Companion.emptySourceBounds
import com.oplus.gallery.basebiz.transition.PhotoPageTransitionManager
import com.oplus.gallery.basebiz.transition.PhotoPageTransitionManager.TransitionArgument
import com.oplus.gallery.basebiz.transition.TransitionPreviewData
import com.oplus.gallery.business_lib.cshot.FastCShotImageInfo
import com.oplus.gallery.business_lib.cshot.QuickImageMapManager
import com.oplus.gallery.business_lib.helper.PhotoDataHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.isFastCShotQuickImage
import com.oplus.gallery.business_lib.synergy.PhotoSynergyTouchInterceptor
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.libcolordirect.ColorDirectOperator
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_SLIDE_LEFT
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_SLIDE_LEFT_DETAIL
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_SLIDE_RIGHT
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.CLICK_SLIDE_RIGHT_DETAIL
import com.oplus.gallery.foundation.ui.animation.AnimationKeyParams
import com.oplus.gallery.foundation.ui.split.SelfSplittingMode
import com.oplus.gallery.foundation.ui.split.SplitOperationUsability
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils.isLand
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.ext.updateValueIfChanged
import com.oplus.gallery.foundation.util.lifecycle.SingleLiveEvent
import com.oplus.gallery.foundation.util.math.Math2DUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_QUICK_PHOTO_PUBLIC
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.PreDecodeService.GALLERY_CAMERA_CONFIG_VERSION
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.hardware.IScreen
import com.oplus.gallery.framework.abilities.ipc.IIPCAbility
import com.oplus.gallery.framework.abilities.ipc.IIPCAbility.ICallCamera.Companion.ParamsValue.PHOTO_TYPE_CONTENT
import com.oplus.gallery.framework.abilities.ipc.IIPCAbility.ICallCamera.Companion.ParamsValue.PHOTO_TYPE_QUICK
import com.oplus.gallery.framework.abilities.ipc.IIPCAbility.ICallCamera.Companion.ParamsValue.PHOTO_TYPE_TMP
import com.oplus.gallery.framework.abilities.moduleaccess.IModuleAccessAbility
import com.oplus.gallery.framework.abilities.moduleaccess.PhotoPageModuleTopics
import com.oplus.gallery.framework.abilities.moduleaccess.PhotoPageTransitionState
import com.oplus.gallery.framework.abilities.open.OpenAbilityConstant
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.theme.PhotoPageTheme
import com.oplus.gallery.photo_page.viewmodel.PhotoSubViewModel
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoDataLoadingViewModel.Companion.INVALID_INDEX
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.dataloading.photoViewDataSet
import com.oplus.gallery.photo_page.viewmodel.inputarguments.PhotoInputArgumentsViewModel
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState.SpringAnimationParam.Companion.DEFAULT_LIGHT_OS
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState.SpringAnimationParam.Companion.DEFAULT_NORMAL
import com.oplus.gallery.basebiz.transition.PageTransitionMaker
import com.oplus.gallery.photo_page.ui.section.details.collaborator.contract.ProgressEffectController
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState.Hide
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState.Hide.Companion.OPERATION_TYPE_EXIT
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoDecorationState.Hide.Companion.OPERATION_TYPE_ENTER
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.DESTROYED
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.ENTERING
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.EXITING
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.NEW
import com.oplus.gallery.photo_page.viewmodel.pagemanagement.PhotoPageManagementViewModel.PageTransitionState.PRESENTED
import com.oplus.gallery.photo_page.viewmodel.reactivefold.PhotoReactiveFoldViewModel.FoldStatus
import com.oplus.gallery.photopager.PhotoPager
import com.oplus.gallery.photopager.PhotoSlot
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.picture_page.predecode.CameraThumbInfoProcessor.Companion.THUMB_INFO_CONFIG
import com.oplus.gallery.picture_page.predecode.ThumbInfoMethodDefinition.CURRENT_CONFIG_VERSION
import com.oplus.gallery.picture_page.predecode.ThumbInfoMethodDefinition.INVALID_CONFIG_VERSION
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.CShot
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.graphics.drawable.ThumbnailDrawable
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.collections.set
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * 大图页面管理
 */
internal class PhotoPageManagementViewModel(
    override val pageViewModel: PhotoViewModel,
) : PhotoSubViewModel(pageViewModel) {

    /**
     * [IIPCAbility]配置能力的对象，避免频繁创建
     */
    private val ipcAbility: IIPCAbility? by lazy {
        pageViewModel.context.getAppAbility<IIPCAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[ipcAbility]init ipcAbility failed , ipcAbility is null" }
            }
        }
    }

    /**
     * [IHardwareAbility]配置能力的对象，避免频繁创建
     */
    private val hardwareAbility: IHardwareAbility? by lazy {
        pageViewModel.context.getAppAbility<IHardwareAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[hardwareAbility]init hardwareAbility failed , hardwareAbility is null" }
            }
        }
    }

    /**
     * 大图主题管理
     * 进入时就需确定目前的主题，所以直接初始化
     */
    private val pageThemeManager = PhotoPageThemeManager(pageViewModel)

    /**
     * [IModuleAccessAbility]配置能力的对象，避免频繁创建
     */
    private val moduleAccessAbility: IModuleAccessAbility? by lazy {
        if ((pageViewModel.inputArguments.invokeFrom.value?.fromSelfSplit == true)
            || (pageViewModel.inputArguments.features.value?.hasPreTransition == false)) {
            GLog.i(TAG, LogFlag.DL) { "[moduleAccessAbility] fromSelfSplit, moduleAccessAbility should be null" }
            return@lazy null
        }
        pageViewModel.context.getAppAbility<IModuleAccessAbility>().apply {
            GLog.i(TAG, LogFlag.DL) { "[moduleAccessAbility] init moduleAccessAbility failed , moduleAccessAbility is null" }
        }
    }

    /////////////////// 以下为对外输出状态 //////////////////////

    /**
     * 当前跳转到的页面
     * 未完全实现完，后续业务需要可在[JumpablePage]添加
     */
    internal val currentPage: LiveData<JumpablePage> get() = _currentPage
    private val _currentPage = MutableLiveData(JumpablePage.PHOTO_PAGE)

    /**
     * [PhotoPager] 可见的第一个 item 下标
     *
     * 来源于[ViewPager.OnPageChangeCallback.onPageScrolled]
     */
    var slotScrolled: Int = -1

    /**
     * [PhotoPager] 的滚动比例
     *
     * 来源于[ViewPager.OnPageChangeCallback.onPageScrolled]
     */
    var positionOffset: Float = 0f

    /**
     * [PhotoPager] 的可见 item 下标范围
     */
    val visiblePagerRange: IntRange
        get() {
            val start = slotScrolled
            val end = if (positionOffset == 0f) {
                start
            } else {
                start + 1
            }
            return IntRange(start, end)
        }

    /**
     * 大图页是否显示在锁屏界面只上。
     * 如果大图页或大图页的父页面是在锁屏界面解锁前启动的，则大图页会显示在锁屏界面只上
     */
    internal val isPageDisplayedAboveKeyguardPage: LiveData<Boolean> by lazy {
        MutableLiveData(
            pageViewModel.context.run {
                (getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager)?.isKeyguardLocked
            } ?: false
        )
    }

    /**
     * Marked by guozihao: to lichengli 自动化测试目前是照搬老大图，后续重构下。
     */
    internal val photoPageDebugger: PhotoPageDebugger by lazy { PhotoPageDebugger(pageViewModel) }


    // Marked by: 2022/3/17 charlie 和文任沟通，是否可以在AppUIConfig中加入，屏幕旋转角度
    // 可以的话，此处保存AppUIConfig
    /**
     * 当前屏幕旋转角度，值为
     *  - [Surface.ROTATION_0]
     *  - [Surface.ROTATION_90]
     *  - [Surface.ROTATION_180]
     *  - [Surface.ROTATION_270]
     */
    internal val appUiRotation: LiveData<Int> get() = _appUiRotation
    private val _appUiRotation = MutableLiveData<Int>()

    /**
     * 暗亮色模式
     */
    internal val uiMode: LiveData<Int> get() = _uiMode
    private val _uiMode = MutableLiveData<Int>()

    /**
     * 当前自分裂操作可用性。
     */
    val isSupportSelfSplitting: LiveData<SplitOperationUsability> get() = selfSplittingManager.isSupportSelfSplitting

    /**
     * 页面尺寸
     */
    internal val pageSize: LiveData<Size> get() = _pageSize
    private val _pageSize = MutableLiveData<Size>()


    /**
     * 页面进场动画
     */
    internal val enterTransitionMaker: LiveData<PageTransitionMaker> get() = _enterTransitionMaker
    private val _enterTransitionMaker = MutableLiveData<PageTransitionMaker>()

    /**
     * 页面出场动画
     */
    internal val exitTransitionMaker: LiveData<PageTransitionMaker> get() = _exitTransitionMaker
    private val _exitTransitionMaker = MutableLiveData<PageTransitionMaker>()

    /**
     * 页面背景转场
     */
    internal val pageBackgroundTransition: LiveData<AnimationKeyParams<Color>> get() = _pageBackgroundTransition
    private val _pageBackgroundTransition = MutableLiveData<AnimationKeyParams<Color>>()

    /**
     * 页面位置进场动画
     */
    internal val enterThumbnailPositionTransition: LiveData<AnimationKeyParams<Rect>> get() = _enterThumbnailPositionTransition
    private val _enterThumbnailPositionTransition = MutableLiveData<AnimationKeyParams<Rect>>()

    /**
     * 页面出场动画
     */
    internal val exitTransition: LiveData<TransitionArgument> get() = _exitTransition
    private val _exitTransition = MutableLiveData<TransitionArgument>()

    /**
     * 当前PhotoPager的可见性
     */
    internal val photoPagerVisibility: LiveData<Boolean> get() = _photoPagerVisibility
    private val _photoPagerVisibility = MutableLiveData<Boolean>()

    /**
     * 当前PhotoPager的滑动状态
     */
    internal val photoPagerScrollState: LiveData<Int> get() = _photoPagerScrollState
    private val _photoPagerScrollState = MutableLiveData<Int>(PhotoPager.SCROLL_STATE_IDLE)

    /**
     * 时机流：大图滑动进入idle。进大图时无滑动，默认IDLE
     */
    internal val scrollState: Flow<Int> get() = _scrollState
    private val _scrollState = MutableStateFlow(ViewPager.SCROLL_STATE_IDLE)

    /**
     * 当前PreTransitionView的可见性
     */
    internal val preTransitionViewVisibility: LiveData<Boolean> get() = _preTransitionViewVisibility
    private val _preTransitionViewVisibility = MutableLiveData<Boolean>()

    /**
     * 当前TransitionView的可见性
     */
    internal val transitionViewVisibility: LiveData<Boolean> get() = _transitionViewVisibility
    private val _transitionViewVisibility = MutableLiveData<Boolean>()

    /**
     * 当前大图页装饰器的状态，其代表装饰器是否应该显示，并指定了执行状态变更时的动画参数。
     *
     * 该可见性是所有装饰器的一个总开关：
     * - 为 Show 表示装饰器允许被显示，但不能保证所有装饰器一定可见，不同装饰器有不同的可见条件。
     * - 为 Hide 时，装饰器不允许被显示，所有装饰器应不可见。
     */
    internal val photoDecorationState: LiveData<PhotoDecorationState> get() = _photoDecorationState
    private val _photoDecorationState = MutableLiveData<PhotoDecorationState>()

    /**
     * 当前大图页是否处于沉浸式交互中（初始默认为正常交互式）：
     *  - 沉浸交互式：顶部菜单、底部菜单、播控栏、状态栏隐藏
     *  - 正常交互式：顶部菜单、底部菜单、状态栏显示，播控栏按需显示
     */
    internal val isUnderImmersionInteractive: LiveData<Boolean> get() = _isUnderImmersionInteractive
    private val _isUnderImmersionInteractive = MutableLiveData<Boolean>(false)

    /**
     * 当前大图页面背景透明度。值为：
     *  - 0f（全透明） ~ 1f（全显示）
     */
    internal val photoBackgroundAlpha: LiveData<Float> get() = _photoBackgroundAlpha
    private val _photoBackgroundAlpha = MutableLiveData<Float>()

    /**
     * 当前大图页的状态
     *
     * @see PageTransitionState
     */
    internal val photoPageTransitionState: LiveData<PageTransitionState> get() = _photoPageTransitionState
    private val _photoPageTransitionState = MutableLiveData(NEW)

    /**
     * 时机流：大图入场动画结束
     */
    internal val transitionState: Flow<PageTransitionState> get() = _transitionState
    private val _transitionState = MutableStateFlow(NEW)

    /**
     * 进入大图的无缝衔接 Transition 是否已经隐藏掉
     *
     * 目前场景：相机进入相册时，相机会自己做 Transition 动画
     *
     * 注意：当 [PhotoInputArgumentsViewModel.Features.hasSeamlessTransition] 值为 true 时，
     * [isSeamlessTransitionHided] 才会起作用；否则 [isSeamlessTransitionHided] 始终为 true
     */
    internal val isSeamlessTransitionHided: LiveData<Boolean> get() = _isSeamlessTransitionHided
    private val _isSeamlessTransitionHided: MutableLiveData<Boolean> = MutableLiveData()

    private val isSeamlessTransitionHidedAtomic: AtomicBoolean = AtomicBoolean(false)

    /**
     * pre transition入场动画状态
     */
    private var preTransitionState: String = PhotoPageTransitionState.TRANSITION_NEW.name

    /**
     * 焦点资源渲染状态
     */
    internal val focusRenderingStatus: LiveData<PhotoSlot.RenderingStatus> get() = _focusRenderingStatus
    private val _focusRenderingStatus: MutableLiveData<PhotoSlot.RenderingStatus> = MutableLiveData()

    /**
     * 进入相册大图后，第一帧是否已经被渲染上屏了
     *
     * @see notifyFirstFrameRenderingStatus
     */
    internal val firstFrameRenderingStatus: LiveData<FirstFrameRenderingStatus> get() = _firstFrameRenderingStatus
    private val _firstFrameRenderingStatus: MutableLiveData<FirstFrameRenderingStatus> = MutableLiveData()

    /**
     * 时机流：进入相册大图后，第一帧是否已经被渲染上屏了
     */
    internal val firstFrameRenderStatus: Flow<FirstFrameRenderingStatus> get() = _firstFrameRenderStatus
    private val _firstFrameRenderStatus = MutableStateFlow(FirstFrameRenderingStatus.NONE_READY)

    /**
     * 无缝动画结束通知
     */
    internal val isSeamlessTransitionFinish: LiveData<Boolean> get() = _isSeamlessTransitionFinish
    private val _isSeamlessTransitionFinish: MutableLiveData<Boolean> = MutableLiveData()

    /**
     * 退出动画默认drawable
     */
    internal val defaultExitTransitionDrawable: Drawable by lazy {
        ColorDrawable(Color.BLACK)
    }

    /**
     * 动画的预览信息
     */
    internal val transitionPreviewData: LiveData<TransitionPreviewData> get() = _transitionPreviewData
    private val _transitionPreviewData = MutableLiveData<TransitionPreviewData>()

    /**
     * 页面操作状态
     *
     * @see requestPageOperation
     * @see PageOperationState
     */
    internal val pageOperationState: LiveData<PageOperationState> get() = _pageOperationState
    private val _pageOperationState: MutableLiveData<PageOperationState> = MutableLiveData(PageOperationState.DisableDataAll)

    /**
     * 当前大图是否处于下拉过程当中
     */
    internal val isInSlideDown: LiveData<Boolean> get() = _isInSlideDown
    private val _isInSlideDown = MutableLiveData<Boolean>(false)

    /**
     * 显示短时 toast
     */
    internal val toastShort: LiveData<String> get() = _toastShort
    private val _toastShort: SingleLiveEvent<String> = SingleLiveEvent()

    /**
     * 退出页面
     */
    internal val exitPage: LiveData<Unit> get() = _exitPage
    private val _exitPage: SingleLiveEvent<Unit> = SingleLiveEvent()

    /**
     * 功能区域，是否处于可见状态。
     */
    internal val isFunctionAreaVisible: LiveData<Boolean> get() = _isFunctionAreaVisible
    private val _isFunctionAreaVisible = MutableLiveData<Boolean>(true)

    /**
     * 开始展示大图UI的时间
     * */
    internal val startShowTime: LiveData<Long> get() = _startShowTime
    private val _startShowTime = MutableLiveData(0L)

    /**
     * 回正大图的事件，将大图缩放、位移、旋转、背景等变换全部回正到默认值。
     */
    internal val resetPhotoPagerStateEvent: LiveData<Boolean> get() = _resetPhotoPagerStateEvent
    private val _resetPhotoPagerStateEvent: SingleLiveEvent<Boolean> = SingleLiveEvent()

    /**
     * 大图底部虚拟导航栏-高度稳定后的值
     * 模糊层需要根据这个更改底部margin
     */
    internal val naviBarHeight: LiveData<Int> get() = _naviBarHeight
    private val _naviBarHeight: MutableLiveData<Int> = MutableLiveData(0)

    /**
     * 大图当前的主题
     */
    internal val pageTheme: StateFlow<PhotoPageTheme> get() = pageThemeManager.pageTheme

    /**
     * 缩图轴的高度
     */
    internal val thumbLineHeight: LiveData<Int> get() = _thumbLineHeight
    private val _thumbLineHeight = MutableLiveData(calcThumbLineHeight())

    /**
     * 缩图轴是否可见
     */
    internal val isThumbLineVisible: StateFlow<Boolean> get() = _isThumbLineVisible
    private val _isThumbLineVisible: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /**
     * 是否应该Disable缩图轴自定义的动画
     */
    internal val shouldDisableThumbLineAnimation: LiveData<Boolean> get() = _shouldDisableThumbLineAnimation
    private val _shouldDisableThumbLineAnimation = SingleLiveEvent<Boolean>()

    /**
     * 缩图轴上面的功能按钮的默认底部间距
     */
    internal val defaultFabCenterToTargetDistance: LiveData<Int> get() = _defaultFabCenterToTargetDistance
    private val _defaultFabCenterToTargetDistance = MutableLiveData(
        if (ScreenUtils.isMiddleAndLargeScreen(pageViewModel.context)) {
            pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_bottom_fab_ml_screen_center_to_bottom)
        } else {
            pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_bottom_fab_center_to_bottom)
        }
    )

    /**
     * 当删除时需执行动画效果
     */
    internal val shouldAnimateWhenRemoved: LiveData<Boolean> get() = _shouldAnimateWhenRemoved
    private val _shouldAnimateWhenRemoved = MutableLiveData(false)


    /**
     * 大图背景动画执行事件
     */
    internal val photoBgAnimationEvent: LiveData<PhotoPageAnimationEvent> get() = _photoBgAnimationEvent
    private val _photoBgAnimationEvent = MutableLiveData<PhotoPageAnimationEvent>()

    /**
     * 互联事件处理器
     */
    internal val synergyTouchInterceptor by lazy {
        PhotoSynergyTouchInterceptor {
            GLog.d(TAG, LogFlag.DL, "[getSelectedItemPaths]")
            pageViewModel.dataLoading.focusItemViewData?.let {
                setOf(Path.fromString(it.id))
            } ?: emptySet<Path>()
        }
    }

    /**
     * 页面自分裂状态管理器
     */
    private val selfSplittingManager: PhotoPageSelfSplittingManager by lazy { PhotoPageSelfSplittingManager() }

    /**
     * 是否支持Quick图公开，相机错峰后处理
     */
    private val isSupportQuickPhotoPublic: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_QUICK_PHOTO_PUBLIC)
    }

    /**
     * 有 [String] 类型的 token 对应的 [PageOperationState] 容器
     *
     * 此处一定要用 key 可以为 null 的 [MutableMap]，因为 null 作为缺省 token 值存在
     */
    private val pageOperationTokenMapping: MutableMap<String?, PageOperationState?> = HashMap()

    private val hasSeamlessTransition: Boolean
        get() = pageViewModel.inputArguments.features.value?.hasSeamlessTransition ?: false

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val hasIntegrationUITransition: Boolean
        get() = pageViewModel.inputArguments.features.value?.hasIntegrationUITransition ?: false

    /**
     * 是否支持单独做提亮策略
     * 某些项目上，相机进入相册时，需要相册单独做提亮策略，不需要保持跟相机预览界面一样的亮度显示
     */
    private val isBrightnessEnhanceSupported: Boolean
        get() = pageViewModel.inputArguments.features.value?.isBrightnessEnhanceSupported ?: false

    /**
     * 是否是相机进入相册模式
     */
    private val isInvokeFromCamera: Boolean
        get() = (pageViewModel.inputArguments.features.value?.chainFrom == VALUE_CHAIN_FROM_CAMERA)

    /**
     * 是否支持单独做提亮策略以及是否是相机进入相册模式
     * 备注：相机进入相册时，某些项目上需要相册单独做提亮策略，不需要保持跟相机预览界面一样的亮度显示
     */
    internal val isUsedCameraBrightness: Boolean
        get() = isBrightnessEnhanceSupported && isInvokeFromCamera

    /**
     * 是否有通知过相机，相册预览过quick图。
     *
     * 如果通知过，在大图界面销毁后，通知相机，相册已退出大图浏览，@see [notifyQuitFastCaptureBrowsingIfNeeded]
     */
    private var hasNotifyCameraBrowsingQuick: Boolean = false

    /**
     * 小布识屏服务是否被禁用了
     */
    private var isColorDirectServiceDisabled: Boolean = false

    /**
     *
     * 标记当前通知给相机，相册正在浏览/焦点的图片mediaId
     *
     * 使用限制：只在支持错峰版本使用
     *
     * 详见[notifyFastCaptureBrowsingIfNeeded]
     */
    private var triggerFocusMediaId: Int? = null

    /**
     * 是否需要通知相机相册final图片已加载完成。
     * - 大图从quick到final后，需要通知相机，记录时间戳，用来进行s6阶段的埋点
     * - 此变量目的是在一些异常场景下，拦截掉非法的报点，防止相机计算的s6异常。
     * - 例如：quick图-home-final图生成-最近任务再次进入，此时final图其实不是在相册界面生成的，那就无法判定final图生成的具体时间，那就不能乱报点。
     */
    private var shouldNotifyCameraPhotoLoadingCompleted: Boolean = false

    /**
     * 是否需要在onPause时对窗口背景进行非透明处理
     */
    internal var shouldSetOpaqueOnPause: Boolean = false
        private set

    /////////////////// 以下为对外公开方法 //////////////////////

    /**
     * 刷新开始进入大图的时间搓，可用于埋点上报等功能
     * */
    private fun updateStartTime(time: Long) {
        _startShowTime.updateValueIfChanged(time)
    }

    /**
     * 更改小布识屏服务能力
     *
     * 通过Settings.System设置哪个activity禁用/恢复启用小布识屏服务的能力
     *
     * @param activity 1.用于获取contentResolver; 2.用于获取类名
     * @param enable true：启用/恢复使用 小布识屏能力；
     *               false：禁用 小布识屏能力
     * @param from 从哪里调用的
     */
    fun updateColorDirectAbility(activity: Activity, enable: Boolean, from: String) {
        launch(Dispatchers.CPU) {
            GLog.d(TAG, LogFlag.DL) {
                "<updateColorDirectAbility> activity=$activity enable=$enable isServiceDisabled=$isColorDirectServiceDisabled from=$from"
            }
            if (enable) {
                // 如果已经被禁用了，但是需要启用，此时才去启用。（避免 没有禁用还要去启用 这种无用功）
                if (isColorDirectServiceDisabled) {
                    ColorDirectOperator.enableColorDirectService(activity)
                    isColorDirectServiceDisabled = false
                }
            } else {
                ColorDirectOperator.disableColorDirectService(activity)
                isColorDirectServiceDisabled = true
            }
        }
    }

    /**
     * 通知相册大图第一帧已经被渲染了，内部有去重，不会重复分发
     *
     * @see firstFrameRenderingStatus
     */
    fun notifyFirstFrameRenderingStatus(status: FirstFrameRenderingStatus) {
        val isFirstFrameReady = status.isThumbnailReady || status.isContentReady
        val params = Bundle().apply {
            putBoolean(OpenAbilityConstant.IS_FIRST_FRAME_READY, isFirstFrameReady)
        }
        //通知Pre Transition，大图第一帧渲染状态
        moduleAccessAbility?.processCallCrossModule(PhotoPageModuleTopics.FIRST_FRAME_RENDERING_STATE.name, params = params)

        _firstFrameRenderingStatus.postValueIfChanged(status)
        _firstFrameRenderStatus.value = status
    }

    /**
     * 通知尝试隐藏无缝切换 Transition
     *
     * 注意：
     *
     * - 可以重复调用，但内部影响是幂等的，即设置过就不会再次设置
     * - 当 [PhotoInputArgumentsViewModel.Features.hasSeamlessTransition] 值为 true 时，本函数才会起作用
     * - 本函数会影响 [isSeamlessTransitionHided] 值，调用本函数后后，如果 [isSeamlessTransitionHided] 为 false，则会通知其被改为 true，否则无影响（不会重复设置）
     *
     * 场景：
     *
     * - 相机进入相册大图，相机会做进入大图的放大动画，做完之后定在那里不消失，然后启动大图
     * - 大图启动中，大图的 Activity 没有动画，并且是透明的
     * - 当大图启动完成并显示图片之后，就可以通知相机隐藏这个无缝切换的 Transition View，即本函数做的操作
     */
    fun notifyTryingHideSeamlessTransition() {
        if (hasSeamlessTransition.not()) {
            _isSeamlessTransitionHided.postValueIfChanged(true)
            return
        }

        launch {
            GTrace.traceBegin("hideSeamlessTransition")
            if (isSeamlessTransitionHidedAtomic.compareAndSet(false, true)) {
                GLog.d(TAG, LogFlag.DL) { "notifyTryingHideSeamlessTransition" }
                ipcAbility?.callCamera?.hideSeamlessTransition(pageViewModel.context)
                _isSeamlessTransitionHided.postValueIfChanged(true)
            }
            GTrace.traceEnd()
        }
    }

    /**
     * 更新PhotoPager的Scroll状态
     */
    fun updatePhotoPagerScrollState(state: Int) {
        GLog.d(TAG, LogFlag.DL) { "updatePhotoPagerScrollState:$state" }
        _photoPagerScrollState.postValueIfChanged(state)
        _scrollState.value = state
    }

    /**
     * 通知准备结束page，做返回动效
     */
    fun notifyFinishPageRequested() {
        notifyPrepareReturningFromGallery()
    }

    /**
     * 通知Pre Transition入场动画开始
     */
    private fun notifyEnterPreTransitionStart() {
        sceneBegin(getJankSceneID(true))
        GTrace.trace("notifyEnterPreTransitionStart") {
            if (hasSeamlessTransition.not()) {
                GLog.d(TAG, LogFlag.DL) { "notifyEnterPreTransitionStart:not seamlessTransition" }
                _transitionViewVisibility.setOrPostValue(HIDE)
                _photoPagerVisibility.setOrPostValue(HIDE)
            }
            val isDefaultImmersiveStyle = pageViewModel.inputArguments.features.value?.isDefaultImmersiveStyle ?: false
            if (isDefaultImmersiveStyle) {
                _photoDecorationState.setOrPostValue(PhotoDecorationState.Hide.IMMERSIVE)
            } else {
                _photoDecorationState.setOrPostValue(PhotoDecorationState.Show.IMMERSIVE)
            }
            changePhotoPageTransitionState(ENTERING)
        }
    }

    /**
     * 进入大图动画开始
     *
     * Marked by zhangjisong 副作用太多。
     * [_photoPageTransitionState] 一定要放到最后再更新状态，外部有监听此时机，并且设置沉浸式，否则外部设置沉浸式不生效
     */
    fun notifyEnterTransitionStart() {
        sceneBegin(getJankSceneID(true))
        GTrace.trace("notifyEnterTransitionStart") {
            if (hasSeamlessTransition.not()) {
                GLog.d(TAG, LogFlag.DL) { "notifyEnterTransitionStart:not seamlessTransition" }
                _transitionViewVisibility.setOrPostValue(SHOW)
                _photoPagerVisibility.setOrPostValue(HIDE)
            }
            val isDefaultImmersiveStyle = pageViewModel.inputArguments.features.value?.isDefaultImmersiveStyle ?: false
            if (isDefaultImmersiveStyle) {
                val hideState = Hide(Hide.IMMERSIVE.animator, Hide.IMMERSIVE.shouldHideNaviBar, operationType = OPERATION_TYPE_ENTER)
                _photoDecorationState.setOrPostValue(hideState)
            } else {
                val showState = PhotoDecorationState.Show(PhotoDecorationState.Show.IMMERSIVE.animator, OPERATION_TYPE_ENTER)
                _photoDecorationState.setOrPostValue(showState)
            }
            changePhotoPageTransitionState(ENTERING)
        }
    }

    /**
     * 设置Disable缩图轴自定义的动画
     */
    fun setShouldDisableThumbLineAnimation(disable: Boolean) {
        _shouldDisableThumbLineAnimation.setOrPostValue(disable)
    }

    /**
     * 设置删除时需执行动画效果
     */
    fun setShouldAnimateWhenRemoved(isShouldAnimate: Boolean) {
        _shouldAnimateWhenRemoved.setOrPostValue(isShouldAnimate)
    }

    /**
     * 设置是否需要在onPause时对窗口背景进行非透明处理
     */
    fun notifyShouldSetOpaqueOnPause(isShouldSetOpaqueOnPause: Boolean) {
        shouldSetOpaqueOnPause = isShouldSetOpaqueOnPause
    }

    /**
     * 通知大图动画执行时机事件
     *
     * @param event
     */
    fun notifyPhotoBgAnimationEvent(event: PhotoPageAnimationEvent) {
        _photoBgAnimationEvent.value = event
    }

    /**
     * 相机进入退出相册一体化UI动画、相机进入相册时无缝动画、其他应用退出大图不上报，所以无对应描述
     */
    private fun getSceneDescription(sceneId: Int): String {
        return when (sceneId) {
            JANK_SCENE_ID_ENTER_PHOTO_ANIMATION -> JANK_SCENE_DES_ENTER_PHOTO_ANIMATION
            JANK_SCENE_ID_EXIT_PHOTO_ANIMATION -> JANK_SCENE_DES_EXIT_PHOTO_ANIMATION
            JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_SEAMLESS -> JANK_SCENE_DES_EXIT_PHOTO_FROM_CAMERA_SEAMLESS
            JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_OTHER -> JANK_SCENE_DES_ENTER_PHOTO_FROM_CAMERA_OTHER
            JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_OTHER -> JANK_SCENE_DES_EXIT_PHOTO_FROM_CAMERA_OTHER
            JANK_SCENE_ID_ENTER_PHOTO_FROM_OTHER -> JANK_SCENE_DES_ENTER_PHOTO_FROM_OTHER
            else -> EMPTY_STRING
        }
    }

    private fun sceneBegin(sceneId: Int) {
        val desc: String = getSceneDescription(sceneId)
        if (desc.isEmpty()) {
            return
        }
        OplusJankWrapper.gfxSceneBegin(pageViewModel.context, sceneId, desc)
    }

    private fun sceneEnd(sceneId: Int) {
        val desc: String = getSceneDescription(sceneId)
        if (desc.isEmpty()) {
            return
        }
        OplusJankWrapper.gfxSceneEnd(pageViewModel.context, sceneId)
    }

    private fun getJankSceneID(enter: Boolean): Int {
        return when {
            // 相册内部进入退出大图场景
            pageViewModel.inputArguments.inputData.getBoolean(IntentConstant.ViewGalleryConstant.KEY_EXTERNAL).not() -> {
                if (enter) JANK_SCENE_ID_ENTER_PHOTO_ANIMATION else JANK_SCENE_ID_EXIT_PHOTO_ANIMATION
            }

            isInvokeFromCamera && hasIntegrationUITransition -> {
                if (enter) JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_INTEGRATIONUI else JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_INTEGRATIONUI
            }

            isInvokeFromCamera && hasSeamlessTransition -> {
                if (enter) JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_SEAMLESS else JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_SEAMLESS
            }

            isInvokeFromCamera -> if (enter) JANK_SCENE_ID_ENTER_PHOTO_FROM_CAMERA_OTHER else JANK_SCENE_ID_EXIT_PHOTO_FROM_CAMERA_OTHER
            // 其他应用进入退出大图场景，比如文件管理
            else -> if (enter) JANK_SCENE_ID_ENTER_PHOTO_FROM_OTHER else JANK_SCENE_ID_EXIT_PHOTO_FROM_OTHER
        }
    }

    /**
     * 通知Pre Transition入场动画结束
     */
    private fun notifyEnterPreTransitionEnd() {
        GTrace.trace("notifyEnterPreTransitionEnd") {
            if (hasSeamlessTransition.not()) {
                GLog.d(TAG, LogFlag.DL) { "notifyEnterPreTransitionEnd:not seamlessTransition" }
                _transitionViewVisibility.setOrPostValue(HIDE)
                _preTransitionViewVisibility.setOrPostValue(HIDE)
                _photoPagerVisibility.setOrPostValue(SHOW)
            }
            changePhotoPageTransitionState(PRESENTED)
        }
        sceneEnd(getJankSceneID(true))
    }

    /**
     * 进入大图动画结束
     *
     * Marked by zhangjisong 副作用太多。
     * [_photoPageTransitionState] 一定要放到最后再更新状态，外部有监听此时机，并且设置沉浸式，否则外部设置沉浸式不生效
     */
    fun notifyEnterTransitionEnd() {
        GTrace.trace("notifyEnterTransitionEnd") {
            if (hasSeamlessTransition.not()) {
                GLog.d(TAG, LogFlag.DL) { "notifyEnterTransitionEnd:not seamlessTransition" }
                _transitionViewVisibility.setOrPostValue(HIDE)
                _photoPagerVisibility.setOrPostValue(SHOW)
            }
            changePhotoPageTransitionState(PRESENTED)
        }
        sceneEnd(getJankSceneID(true))
    }

    /**
     * 退出大图动画开始
     */
    fun notifyExitTransitionStart() {
        sceneBegin(getJankSceneID(false))
        GTrace.trace("notifyExitTransitionStart") {
            changePhotoPageTransitionState(EXITING)
            _transitionViewVisibility.setOrPostValue(SHOW)
            _photoPagerVisibility.setOrPostValue(HIDE)
            _photoDecorationState.setOrPostValue(
                Hide(Hide.IMMERSE_SYSTEM_NOT_HIDE.animator, Hide.IMMERSE_SYSTEM_NOT_HIDE.shouldHideNaviBar, operationType = OPERATION_TYPE_EXIT)
            )
            /**
             * 大图退场动画开始，通知pre transition，做状态更新和资源回收等操作
             */
            processPhotoPageTransitionState(PhotoPageTransitionState.TRANSITION_EXITING.name)
        }
    }

    /**
     * 退出大图动画结束
     * @param isHidePhotoView 退出动画结束的时候是否要隐藏大图相关的View
     */
    fun notifyExitTransitionEnd(isHidePhotoView: Boolean = true) {
        sceneEnd(getJankSceneID(false))
        GTrace.trace("notifyExitTransitionEnd") {
            if (isHidePhotoView) {
                _transitionViewVisibility.setOrPostValue(HIDE)
                _photoPagerVisibility.setOrPostValue(HIDE)

                val curDecorationState = _photoDecorationState.value
                val needHideDecoration = (curDecorationState !is Hide) || (curDecorationState.operationType != OPERATION_TYPE_EXIT)
                if (needHideDecoration) {
                    _photoDecorationState.setOrPostValue(
                        Hide(
                            Hide.IMMERSE_SYSTEM_NOT_HIDE.animator,
                            Hide.IMMERSE_SYSTEM_NOT_HIDE.shouldHideNaviBar,
                            operationType = OPERATION_TYPE_EXIT
                        )
                    )
                }
            }
            photoPageDebugger.notifyExitTransitionEnd()
            changePhotoPageTransitionState(DESTROYED)

            /**
             * 大图退场动画结束，通知pre transition，做状态更新和资源回收等操作
             */
            processPhotoPageTransitionState(PhotoPageTransitionState.TRANSITION_DESTROYED.name)
        }
    }

    /**
     * 准备自分裂数据
     */
    fun notifyPrepareSelfSplittingSupporting(activity: FragmentActivity) {
        this.selfSplittingManager.prepare(activity)
    }

    /**
     * 请求进自分屏
     */
    fun requestSplitScreen(splitStyle: SelfSplittingMode) {
        selfSplittingManager.requestSplitSelf(splitStyle)
    }

    /**
     * 更新退出动画的位置过渡信息，更新后会立即触发退出动画
     * @param suggestingTransitionType 建议使用的过渡动画类型
     * @param overrideStartPosition 指定要复写动画起始位置的Rect，随后退场动画将从指定位置开始
     * @param overrideEndPosition 指定要复写动画结束位置的Rect，随后退场动画将从指定位置截止
     * @param exitTransitionFromType 退出动画的来自的类型
     * @param rotation 当前屏幕旋转角度
     */
    fun refreshExitTransitionBeforeExitTransitionStarting(
        suggestingTransitionType: PhotoPageTransitionManager.TransitionType,
        overrideStartPosition: Rect? = null,
        overrideEndPosition: Rect? = null,
        exitTransitionFromType: PhotoPageTransitionManager.ExitTransitionFromType,
        rotation: Int = ROTATION_0
    ) = launch(Dispatchers.UI) {
        val exitActivityTransition = withTimeoutOrNull(PhotoPageTransitionManager.MAKE_TRANSITION_TIMEOUT) {
            val region = pageViewModel.inputArguments.transitionThumbRegion.value?.let { region ->
                //通过context.display.rotation获取到的旋转角度是逆时针的，但是相机传的值实际是顺时针的，所以横屏角度没对上
                when (rotation) {
                    ROTATION_0, ROTATION_180 -> region.rect
                    ROTATION_90 -> region.rectOrientation270
                    ROTATION_270 -> region.rectOrientation90
                    else -> region.rect
                }
            }

            exitTransitionMaker.value?.makeActivityTransition(pageViewModel.context, rotation, region)
        }
        val correctedTransitionType = if (hasIntegrationUITransition) PhotoPageTransitionManager.TransitionType.NONE else suggestingTransitionType

        GLog.d(TAG, LogFlag.DL) {
            "[refreshExitTransitionBeforeExitTransitionStarting] transitionType=[$suggestingTransitionType -> $correctedTransitionType]"
        }

        /**
         * 针对Fragment，在back的时候，会先协程准备动画参数，然后再切到主线程做动画，动画完成后再执行页面退出逻辑。如果这时候再back退出
         * - 再back，此时Activity需要走退出流程；如果动画正在执行，会调用 doCancelAnimationIfExitRunning，终止动画，并立即退出。
         * - 有低概率此时动画还未执行，比如正在执行上面的协程，此时 PageTransitionState 状态尚未置位，无法准确拦截，导致协程走完后会继续触发退场动画
         * - 退场动画执行完成后，会触发PhotoFragment退出的逻辑，此时fragment大概率是退出失败，导致UI异常。
         *
         * 因此这里在onPause之后，需要再次在此判定一下是否需要拦截。
         */
        if (isCurrentLifeEventResumed().not()) {
            GLog.w(TAG, LogFlag.DL) {
                "[refreshExitTransitionBeforeExitTransitionStarting] " +
                        "shouldInterceptExitTransition=true, will ignore exit transition."
            }
            _resetPhotoPagerStateEvent.setOrPostValue(true)
            return@launch
        }

        doRefreshExitTransitionBeforeExitTransitionStarting(
            transitionType = correctedTransitionType,
            overrideStartPosition = overrideStartPosition,
            overrideEndPosition = overrideEndPosition,
            exitTransitionFromType = exitTransitionFromType,
            exitActivityTransition = exitActivityTransition
        )
    }

    /**
     * 显示短时 toast
     */
    fun showShortToast(toast: String) {
        _toastShort.setOrPostValue(toast)
    }

    /**
     * 退出大图页
     */
    fun exitPage() {
        _exitPage.setOrPostValue(Unit)
    }

    /**
     * Notify photo page theme update
     *
     * @param expectTheme 期望的主题
     */
    fun notifyPhotoPageThemeUpdate(expectTheme: PhotoPageTheme? = null) {
        pageThemeManager.notifyThemeMayChanged(expectTheme)
    }

    /**
     * 当前是否支持亮色主题
     *
     */
    fun isSupportLightTheme() = pageThemeManager.isSupportLightTheme()

    /**
     * 根据当前状态计算期望的主题
     *
     * @return
     */
    fun calculateExceptTheme(isHalfImmersive: Boolean): PhotoPageTheme {
        val state = pageViewModel.details.transitionPanelEffectState.value
        val detailsPageState = ProgressEffectController.isDetailsMode(state)
        return pageThemeManager.calculateExceptTheme(detailsPageState, isHalfImmersive)
    }

    internal fun isCurrentLifeEventResumed(): Boolean {
        return pageViewModel.lifeEvent.targetState == Lifecycle.State.RESUMED
    }

    private fun doRefreshExitTransitionBeforeExitTransitionStarting(
        transitionType: PhotoPageTransitionManager.TransitionType,
        overrideStartPosition: Rect? = null,
        overrideEndPosition: Rect? = null,
        exitTransitionFromType: PhotoPageTransitionManager.ExitTransitionFromType,
        exitActivityTransition: PageTransitionMaker.PageActivityTransition?
    ) {
        // 埋点： 通知photoPageDebugger 退出动作开始
        photoPageDebugger.notifyRequestFinishPageBegin()

        when (transitionType) {
            // 指定执行缩图过渡
            PhotoPageTransitionManager.TransitionType.NONE -> makeTransitionArgumentTypeNone()
            // 指定执行缩图过渡
            PhotoPageTransitionManager.TransitionType.THUMBNAIL_TRANSITION -> {
                makeTransitionArgumentTypeThumbnail(
                    overrideStartPosition,
                    overrideEndPosition,
                    exitTransitionFromType,
                    exitActivityTransition
                )
            }
            // 指定执行Activity过渡
            PhotoPageTransitionManager.TransitionType.PAGE_TRANSITION -> makeTransitionArgumentTypePage(exitActivityTransition)
            // 自主选择
            PhotoPageTransitionManager.TransitionType.AUTO_TRANSITION -> {
                makeTransitionArgumentTypeAuto(
                    overrideStartPosition,
                    overrideEndPosition,
                    exitTransitionFromType,
                    exitActivityTransition
                )
            }
        }
    }

    private fun makeTransitionArgumentTypeNone() {
        TransitionArgument.PageTransitionArgument(
            enterTransitionRes = Resources.ID_NULL,
            exitTransitionRes = Resources.ID_NULL
        ).let(_exitTransition::setOrPostValue)

        GLog.d(TAG, LogFlag.DL) {
            "[refreshExitTransitionBeforeExitTransitionStarting] assign NONE_TRANSITION."
        }
    }

    private fun makeTransitionArgumentTypeThumbnail(
        overrideStartPosition: Rect? = null,
        overrideEndPosition: Rect? = null,
        exitTransitionFromType: PhotoPageTransitionManager.ExitTransitionFromType,
        exitActivityTransition: PageTransitionMaker.PageActivityTransition?
    ) {
        val isCleanPageTransition = PhotoDataHelper.isFromInnerAlbum(pageViewModel.inputArguments.inputIntentAction)
        val hasActivityTransition = (exitActivityTransition != null)
        val isInvalid = (exitActivityTransition?.transition == Resources.ID_NULL)
        TransitionArgument.ThumbnailTransitionArgument(
            shouldCleanPageTransition = hasActivityTransition || isCleanPageTransition,
            animateStyle = if (hasActivityTransition && isInvalid.not()) AnimateStyle.FADE else AnimateStyle.NONE,
            colorKeyParams = findExitBackgroundTransition(shouldOverrideStartColor = (hasActivityTransition.not() || isInvalid)),
            positionKeyParams = findExitPositionTransition(exitActivityTransition, overrideStartPosition, overrideEndPosition),
            exitTransitionFromType = exitTransitionFromType
        ).let(_exitTransition::setOrPostValue)

        GLog.d(TAG, LogFlag.DL) {
            "[refreshExitTransitionBeforeExitTransitionStarting] assign THUMBNAIL_TRANSITION."
        }
    }

    private fun makeTransitionArgumentTypePage(
        exitActivityTransition: PageTransitionMaker.PageActivityTransition?
    ) {
        TransitionArgument.PageTransitionArgument(
            enterTransitionRes = Resources.ID_NULL, // 生成退出动画时，不需要关心进入动画
            exitTransitionRes = exitActivityTransition?.transition ?: Resources.ID_NULL
        ).let(_exitTransition::setOrPostValue)

        GLog.d(TAG, LogFlag.DL) {
            "[refreshExitTransitionBeforeExitTransitionStarting] assign ACTIVITY_TRANSITION."
        }
    }

    private fun makeTransitionArgumentTypeAuto(
        overrideStartPosition: Rect? = null,
        overrideEndPosition: Rect? = null,
        exitTransitionFromType: PhotoPageTransitionManager.ExitTransitionFromType,
        exitActivityTransition: PageTransitionMaker.PageActivityTransition?
    ) {
        exitActivityTransition?.let {
            // 0. 如果有动效资源为0，执行缩图动画过渡
            if (it.transition == Resources.ID_NULL) {
                GLog.d(TAG, LogFlag.DL) {
                    "[refreshExitTransitionBeforeExitTransitionStarting] activity transition is null, do thumbnail transition"
                }

                TransitionArgument.ThumbnailTransitionArgument(
                    shouldCleanPageTransition = true,
                    colorKeyParams = findExitBackgroundTransition(),
                    positionKeyParams = findExitPositionTransition(
                        exitActivityTransition,
                        overrideStartPosition,
                        overrideEndPosition
                    ),
                    exitTransitionFromType = exitTransitionFromType

                ).let { argument ->
                    _exitTransition.setOrPostValue(argument)
                }
            } else {
                // 1. 如果有activity动画，执行activity动画
                TransitionArgument.PageTransitionArgument(
                    enterTransitionRes = Resources.ID_NULL, // 生成退出动画时，不需要关心进入动画
                    exitTransitionRes = it.transition
                ).let { argument ->
                    _exitTransition.setOrPostValue(argument)
                }
            }

            GLog.d(TAG, LogFlag.DL) {
                "[refreshExitTransitionBeforeExitTransitionStarting] assign AUTO_TRANSITION： ActivityTransitionArgument."
            }
            return
        } ?: let {
            // 内部以activity启动大图时，退出动画完需要清除页面动画
            val isCleanPageTransition = PhotoDataHelper.isFromInnerAlbum(pageViewModel.inputArguments.inputIntentAction)
            // 2. 没有activity动画，执行缩图动画过渡
            TransitionArgument.ThumbnailTransitionArgument(
                shouldCleanPageTransition = isCleanPageTransition,
                colorKeyParams = findExitBackgroundTransition(),
                positionKeyParams = findExitPositionTransition(
                    exitActivityTransition,
                    overrideStartPosition,
                    overrideEndPosition
                ),
                exitTransitionFromType = exitTransitionFromType
            ).let { argument ->
                _exitTransition.setOrPostValue(argument)
            }

            GLog.d(TAG, LogFlag.DL) {
                "[refreshExitTransitionBeforeExitTransitionStarting] assign AUTO_TRANSITION： ImageTransitionArgument."
            }
        }
    }

    /**
     * 屏幕方向发生改变。
     */
    fun notifyUiRotationChanged(rotation: Int) {
        if (rotation != appUiRotation.value) {
            _appUiRotation.setOrPostValue(rotation)
        }
    }

    /**
     * 暗亮色发生改变。
     */
    fun notifyUiModeChanged(uiMode: Int) {
        _uiMode.postValueIfChanged(uiMode)
    }

    /**
     * 获取window用于屏幕亮度提升
     */
    fun notifyScreenBrightnessControlGained(window: Window) {
        if (null == pageViewModel.inputArguments.features.value?.brightnessMode) return
        val brightnessMode =
            if (PhotoInputArgumentsViewModel.BrightnessMode.HIGH == pageViewModel.inputArguments.features.value?.brightnessMode) {
                IScreen.BrightnessMode.CAMERA
            } else IScreen.BrightnessMode.NORMAL

        adjustScreenBrightness(window, brightnessMode)
    }

    /**
     * 通知从大图跳转到相册内部页
     */
    fun notifyTransferToInternalPage() {
        _currentPage.postValueIfChanged(JumpablePage.INTERNAL_PAGE)
    }

    /**
     * 通知从大图跳转到连拍页面
     */
    fun notifyTransferToCShotPage() {
        _currentPage.postValueIfChanged(JumpablePage.CSHOT_PAGE)
    }

    /**
     * 通知从大图跳转到合成GIF页面了
     */
    fun notifyTransferToGifPage() {
        _currentPage.postValueIfChanged(JumpablePage.SYNTHESIS_GIF_PAGE)
    }

    /**
     * 通知从大图跳转到编辑页面了
     */
    fun notifyTransferToEditorPage() {
        _currentPage.postValueIfChanged(JumpablePage.EDITOR_PAGE)
    }

    /**
     * 通知从大图跳转到内部分享页面了---GalleryShareInnerActivity
     */
    fun notifyTransferToInternalSharePage() {
        _currentPage.postValueIfChanged(JumpablePage.INTERNAL_SHARE_PAGE)
    }

    /**
     * 通知从大图跳转到“移动到”页面了，或退出“移动到”页面了
     * @param into true：进入移动到 false：退出移动到
     */
    fun notifyTransferToMoveToPage(into: Boolean) {
        _currentPage.postValueIfChanged(if (into) JumpablePage.MOVE_TO_PAGE else JumpablePage.PHOTO_PAGE)
    }

    /**
     * 通知清除BrightnessMode
     */
    fun notifyClearBrightnessMode(clearMode: IScreen.ClearBrightnessMode) {
        hardwareAbility?.screen?.clearBrightnessMode(clearMode, isUsedCameraBrightness)
    }

    /**
     * 通知页面尺寸发生变化
     */
    fun notifyPageSizeChanged(width: Int, height: Int) {
        if ((_pageSize.value?.width != width) || (_pageSize.value?.height != height)) {
            _pageSize.setOrPostValue(Size(width, height))
        }
    }

    /**
     * 变更页面背景透明度
     */
    fun changePageBackgroundAlpha(alpha: Float) {
        _photoBackgroundAlpha.setOrPostValue(alpha)
    }

    /**
     * 变更沉浸式界面交互：
     *  - 启动沉浸式：顶部菜单、底部菜单、播控栏、状态栏隐藏
     *  - 退出沉浸式：顶部菜单、底部菜单、状态栏显示，播控栏按需显示
     *
     *  目前有的模式：
     *  1. （全）沉浸式，isImmersiveEnabled 为 true，此时没有额外的 UI。
     *  2. 普通、非沉浸式模式，isImmersiveEnabled 为 false，此时显示正常的 UI。
     *  3. 半沉浸式、沉浸预览态，isImmersiveEnabled 为 false，此时会淡化额外 UI 的显示样式。（根据当前图片的大小显示此模式）
     *
     *  @param isImmersiveEnabled 是否使用全沉浸式（不显示额外 UI）
     *  @param needShowSystemBar 是否需要展示系统栏
     *  @param operationType 操作方式来源
     */
    fun changeImmersionInteractive(isImmersiveEnabled: Boolean, needShowSystemBar: Boolean = false, operationType: String? = null) {
        _isUnderImmersionInteractive.postValueIfChanged(isImmersiveEnabled)
        _photoDecorationState.postValueIfChanged(
            when {
                isImmersiveEnabled && needShowSystemBar -> {
                    Hide(Hide.IMMERSE_SYSTEM_NOT_HIDE.animator, Hide.IMMERSE_SYSTEM_NOT_HIDE.shouldHideNaviBar, operationType = operationType)
                }

                isImmersiveEnabled -> Hide(Hide.IMMERSIVE.animator, Hide.IMMERSIVE.shouldHideNaviBar, operationType = operationType)

                else -> PhotoDecorationState.Show(PhotoDecorationState.Show.IMMERSIVE.animator, operationType)
            })
    }


    /**
     * 请求改变装饰器状态
     */
    fun requestDecorationState(photoDecorationState: PhotoDecorationState) {
        _photoDecorationState.postValueIfChanged(photoDecorationState)
    }

    /**
     * 通知页面滑动到另一图片
     * @param from 滑动起始图片位置
     * @param to 滑动终止图片位置
     */
    fun notifyPhotoSlide(from: Int, to: Int) {
        if ((from == INVALID_INDEX) ||
            (to == INVALID_INDEX) ||
            (abs(from - to) != 1)
        ) {
            /**
             * 不是滑动到另一张图片，直接退出
             * - from 无效
             * - to 无效
             * - 二者差值不是 1
             * 满足任意一个条件，即认为不是滑动到另一张图片。
             */
            return
        }

        // 产品埋点，用户滑动事件
        val isInDetailsMode = pageViewModel.details.isInDetailsMode.value == true
        if (from > to) {
            if (isInDetailsMode) {
                pageViewModel.track.trackGestureClick(value = CLICK_SLIDE_LEFT_DETAIL)
            } else {
                pageViewModel.track.trackGestureClick(value = CLICK_SLIDE_LEFT)
            }
        } else if (to > from) {
            if (isInDetailsMode) {
                pageViewModel.track.trackGestureClick(value = CLICK_SLIDE_RIGHT_DETAIL)
            } else {
                pageViewModel.track.trackGestureClick(value = CLICK_SLIDE_RIGHT)
            }
        }
        pageViewModel.playback.notifyPhotoSlide(from, to)
    }

    /**
     * 请求页面操作，通过此函数业务承接做对应操作。
     *
     * 注意：请求后，会重置请求前存在的状态值
     *
     * @param pageOperationState 期望操作状态
     * @param operatorToken 身份值，默认值为 null，为缺省通用
     * @param shouldDrainOperators 是否先排空之前的操作记录，缺省值为 false。true：则会将之前的 [operatorToken] 对应的记录清空掉；false：无其他效果
     * @see PageOperationState
     */
    fun requestPageOperation(
        pageOperationState: PageOperationState,
        operatorToken: String? = null,
        shouldDrainOperators: Boolean = false
    ) {
        if (shouldDrainOperators) {
            // 排空之前的操作记录
            pageOperationTokenMapping.clear()
        }

        when {
            // 记录对应 token 下的新操作
            pageOperationState.hasOperations -> pageOperationTokenMapping[operatorToken] = pageOperationState
            // 如果新纪录没有任何操作 && 没有排空，则将对应 token 值移除
            shouldDrainOperators.not() -> pageOperationTokenMapping.remove(operatorToken)
        }

        // 计算汇总新记录
        val actualPageOperationState = when {
            pageOperationTokenMapping.isEmpty() -> PageOperationState.NoneOperation
            else -> pageOperationTokenMapping.values.filterNotNull().reduce { previous, next -> previous + next }
        }

        if (_pageOperationState.value == actualPageOperationState) {
            GLog.d(TAG, LogFlag.DL) { "[requestPageOperation] :_pageOperationState == actualPageOperationState,return" }
            return
        }

        GLog.d(TAG, LogFlag.DL) {
            "[requestPageOperation] " +
                    "token=$operatorToken, " +
                    "shouldDrainOperators=$shouldDrainOperators, " +
                    "pageOperationState=$pageOperationState, " +
                    "actualPageOperationState=$actualPageOperationState"
        }
        _pageOperationState.setOrPostValue(actualPageOperationState)
    }

    /**
     * 通知slot滑动事件
     *
     * @param slideEvent slot滑动的事件
     * @see SlideEvent
     */
    fun notifySlideEvent(slideEvent: SlideEvent) {
        when (slideEvent) {
            SlideEvent.SlideDownBegin -> {
                notifyPrepareReturningFromGallery()
                if (ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_CAMERA_SUPPORT_PREVIEW_ROTATE)) {
                    // 相机横屏启动竖屏大图时由于延时隐藏时间比较大，防止启动后快速下拉看到无缝动画，需要执行隐藏无缝动画
                    notifyTryingHideSeamlessTransition()
                }
                _isInSlideDown.setOrPostValue(true)
            }

            SlideEvent.SlideDownCancel ->
                _isInSlideDown.setOrPostValue(false)

            SlideEvent.SlideDownEnd -> {
                _isInSlideDown.setOrPostValue(false)
                notifyPrepareReturningFromGallery()
            }
        }
    }

    /**
     * 将显示设备 [displayId] 上的 touch 焦点转让到 [inputToken] 上
     */
    fun transferTouch(inputToken: IBinder, displayId: Int): Boolean {
        return WindowManagerWrapper.transferTouch(inputToken, displayId)
    }

    /**
     * 通知大图焦点正在显示内容的区域 [region] 有变更
     *
     * @param region 显示内容的区域
     */
    fun notifySlotPresentRegionChanged(region: Rect) {
        GLog.d(TAG, LogFlag.DL) { "[notifySlotPresentRegionChanged] region=$region" }

        if (hasIntegrationUITransition) {
            GTrace.trace("$TAG.notifyPhotoPresentRegion") {
                ipcAbility?.callCamera?.notifyPhotoPresentRegion(pageViewModel.context, region)
            }
        }
    }

    /**
     * 设置当前功能区域是否对用户可见
     * @param isVisible Boolean
     */
    fun updateFunctionAreaVisibleState(isVisible: Boolean, reason: String = EMPTY_STRING) {
        GLog.d(TAG, LogFlag.DL) { "[updateFunctionAreaVisibleState] reason:$reason, isVisible:$isVisible" }
        _isFunctionAreaVisible.updateValueIfChanged(isVisible)
    }

    /**
     * 关闭Ability
     */
    private fun closeAbilities() {
        ipcAbility?.close()
        hardwareAbility?.close()
        moduleAccessAbility?.close()
    }

    override fun onCreate() {
        super.onCreate()
        registerModuleCallback()
        subscribeLiveDataFromViewModel()
        notifyGalleryConfigIfChanged()
    }

    override fun onStart() {
        super.onStart()
        // 重新回到大图页面后，没有跳转到某个页面，所以需要重置
        _currentPage.postValueIfChanged(JumpablePage.PHOTO_PAGE)
        updateStartTime(System.currentTimeMillis())
    }

    override fun onStop() {
        super.onStop()
        updateStartTime(0L)
        shouldNotifyCameraPhotoLoadingCompleted = false
    }

    override fun onDestroy() {
        super.onDestroy()
        updateStartTime(0L)
        notifyQuitFastCaptureBrowsingIfNeeded()
        unRegisterModuleCallback()
        //关闭 ability
        closeAbilities()
    }

    /////////////////// 以下为内部私有方法 //////////////////////

    /**
     * 有相机"无缝衔接过渡动作"，需在下拉大图开始及结束时，通知相机准备返回动效，可以提升相机启动效能。
     *
     * 不准暴露出本 VM 之外
     */
    private fun notifyPrepareReturningFromGallery() {
        // 有相机"无缝衔接过渡动作"，需在下拉大图开始及结束时，通知相机准备返回动效，可以提升相机启动效能。
        if (hasSeamlessTransition) {
            launch {
                GTrace.trace("$TAG.prepareReturningFromGallery") {
                    ipcAbility?.callCamera?.prepareReturningFromGallery(pageViewModel.context)
                }
            }
        }
    }

    /**
     * 注册跨模块监听，监听Pre Transition动画状态和是否允许菜单加载等操作
     */
    private fun registerModuleCallback() {
        moduleAccessAbility?.registerCallback(
            topic = PhotoPageModuleTopics.PRE_PHOTO_PAGE_TRANSITION_STATE.name,
            sticky = true
        ) {
            when (val transitionState = it?.getString(OpenAbilityConstant.TRANSITION_STATE)) {
                PhotoPageTransitionState.TRANSITION_ENTERING.name -> {
                    preTransitionState = PhotoPageTransitionState.TRANSITION_ENTERING.name
                    notifyEnterPreTransitionStart()
                }

                PhotoPageTransitionState.TRANSITION_PRESENTED.name -> {
                    if (preTransitionState == PhotoPageTransitionState.TRANSITION_NEW.name) {
                        notifyEnterPreTransitionStart()
                    }
                    preTransitionState = PhotoPageTransitionState.TRANSITION_PRESENTED.name
                    notifyEnterPreTransitionEnd()
                }

                PhotoPageTransitionState.TRANSITION_EXITING.name -> preTransitionState = PhotoPageTransitionState.TRANSITION_EXITING.name

                PhotoPageTransitionState.TRANSITION_DESTROYED.name -> preTransitionState = PhotoPageTransitionState.TRANSITION_DESTROYED.name

                else -> GLog.w(TAG, LogFlag.DL, "[registerModuleCallback] transitionState no matched  for [$transitionState].")
            }
            null
        }

        moduleAccessAbility?.registerCallback(PhotoPageModuleTopics.PHOTO_PAGE_COULD_INVALIDATE_MENU.name, sticky = true) {
            // 如果此机型需要推迟菜单UI加载，那之前的所有加载必将被拦截。此处需要放开
            if (it?.getBoolean(OpenAbilityConstant.COULD_INVALIDATE_MENU) == true) {
                pageViewModel.menuControl.permitMenuInflateAfterPostponed()
            }
            null
        }
    }

    /**
     * 注销跨模块监听
     */
    private fun unRegisterModuleCallback() {
        moduleAccessAbility?.unregisterCallback(PhotoPageModuleTopics.PRE_PHOTO_PAGE_TRANSITION_STATE.name)
        moduleAccessAbility?.unregisterCallback(PhotoPageModuleTopics.PHOTO_PAGE_COULD_INVALIDATE_MENU.name)
    }

    private fun subscribeLiveDataFromViewModel() {
        pageViewModel.dataLoading.diffedPhotoViewDataSet.observeForever {
            if (it.newDataSet.totalCount == 0) {
                exitPage()
            }
        }

        pageViewModel.inputArguments.features.observeForever {
            updateSeamlessTransitionIfNeeded()
        }

        pageViewModel.inputArguments.transition.observeForever { transition ->
            _exitTransitionMaker.value = transition.exitTransition
            _enterTransitionMaker.value = transition.enterTransition
            _pageBackgroundTransition.value = transition.enterTransition.backgroundColors
            _enterThumbnailPositionTransition.value = transition.enterTransition.thumbnailPositions
            updateTransitionPreviewDataIfNeeded("transition")
        }

        pageViewModel.inputArguments.previewShot.observeForever {
            updateTransitionPreviewDataIfNeeded("previewShot")
        }

        pageViewModel.contentLoading.focusPagerThumbnail.observeForever {
            GTrace.trace({ "$TAG.focusPagerThumbnail" }) {
                updateTransitionPreviewDataIfNeeded("focusPagerThumbnail")
            }
        }

        pageViewModel.reactiveFold.foldStatusDiff.observeForever { value ->
            if (value?.isChanged == true) {
                val isOpened = value.newFoldStatus.isOpened
                _defaultFabCenterToTargetDistance.updateValueIfChanged(
                    if (isOpened) {
                        pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_bottom_fab_ml_screen_center_to_bottom)
                    } else {
                        pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_bottom_fab_center_to_bottom)
                    }
                )
                _thumbLineHeight.updateValueIfChanged(calcThumbLineHeight(value.newFoldStatus))
            }
        }

        appUiRotation.observeForever {
            _thumbLineHeight.updateValueIfChanged(calcThumbLineHeight())
        }

        launch(Dispatchers.Main.immediate) {
            pageViewModel.dataLoading.diffedFocusViewData.collectNotNull {
                GTrace.traceBegin("$TAG.focusSlotViewDataDiff")
                notifyPhotoRotationToIntegrationUITransitionIfNeeded(it)
                updateTransitionPreviewDataIfNeeded("focusSlotViewDataDiff")
                notifyFastCaptureBrowsingIfNeeded(it)
                GTrace.traceEnd()
            }
        }
    }

    private fun calcThumbLineHeight(newFoldStatus: FoldStatus? = null): Int {
        val isMiddleAndLargeScreen = ScreenUtils.isMiddleAndLargeScreen(pageViewModel.context) && newFoldStatus?.isOpened != false
        return if (isMiddleAndLargeScreen) {
            pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_thumbline_ml_screen_list_height)
        } else if (pageViewModel.context.isLand()) {
            pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_thumbline_list_height_landscape)
        } else {
            pageViewModel.context.resources.getDimensionPixelSize(R.dimen.photopage_thumbline_list_height)
        }
    }

    /**
     * 通知大图旋转角度信息给 IntegrationUITransition
     */
    private fun notifyPhotoRotationToIntegrationUITransitionIfNeeded(diffedFocusViewData: DiffedPhotoItemViewData?) {
        if (hasIntegrationUITransition.not()) {
            return
        }

        if (diffedFocusViewData?.isContentChanged != true) {
            return
        }

        val oldItem = diffedFocusViewData.oldItem ?: return
        val newItem = diffedFocusViewData.newItem ?: return
        if (oldItem.id != newItem.id) {
            return
        }

        val isRotationChanged = oldItem.rotation != newItem.rotation
        if (isRotationChanged.not()) {
            return
        }

        GLog.d(TAG, LogFlag.DL) {
            "[notifyPhotoRotationToIntegrationUITransitionIfNeeded] " +
                    "id=${newItem.mediaId}, " +
                    "rotation=(${oldItem.rotation} -> ${newItem.rotation})"
        }
        launch(Dispatchers.SINGLE_UN_BUSY) {
            GTrace.trace("$TAG.notifyPhotoRotated") {
                ipcAbility?.callCamera?.notifyPhotoRotated(pageViewModel.context, newItem.mediaId, newItem.rotation)
            }
        }
    }

    /**
     * 获取图片type: quick：1, tmp：2, 原图：3
     */
    private fun getPhotoType(photoItem: PhotoItemViewData): Int {
        return when (photoItem.contentQuality) {
            PhotoItemViewData.ContentQuality.MEDIUM -> PHOTO_TYPE_QUICK
            PhotoItemViewData.ContentQuality.LOW -> PHOTO_TYPE_TMP
            else -> PHOTO_TYPE_CONTENT
        }
    }

    private fun updateSeamlessTransitionIfNeeded() {
        if (hasSeamlessTransition.not()) {
            _isSeamlessTransitionHided.postValueIfChanged(true)
            _isSeamlessTransitionFinish.postValueIfChanged(true)
        } else if (pageViewModel.inputArguments.features.value?.shouldWaitForSeamlessTransitionFinish == true) {
            GLog.d(TAG, LogFlag.DL) { "updateSeamlessTransitionIfNeeded:hasSeamlessTransition and waitSeamlessTransitionFinish" }
            GTrace.trace("$TAG.waitSeamlessTransitionFinish") {
                waitSeamlessTransitionFinish() {
                    GLog.d(TAG, LogFlag.DL) { "updateSeamlessTransitionIfNeeded:onSeamlessTransitionFinish,isTimeOut = $it" }
                    _photoPagerVisibility.setOrPostValue(SHOW)
                    _isSeamlessTransitionFinish.postValueIfChanged(true)
                }
            }
        } else {
            //无缝动画 + 无需等待相机动画通知
            GLog.d(TAG, LogFlag.DL) { "updateSeamlessTransitionIfNeeded:hasSeamlessTransition and no need waitForCameraAnimation" }
            _photoPagerVisibility.setOrPostValue(SHOW)
            _isSeamlessTransitionFinish.postValueIfChanged(true)
        }
    }

    private fun waitSeamlessTransitionFinish(onDone: (Boolean) -> Unit) {
        launch {
            val receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    GLog.d(TAG, LogFlag.DL) { "waitSeamlessTransitionFinish::onReceive" }
                    cancel()
                }
            }
            GLog.d(TAG, LogFlag.DL) { "waitSeamlessTransitionFinish: register" }
            BroadcastDispatcher.registerExportedReceiver(
                pageViewModel.context,
                receiver,
                IntentFilter(IIPCAbility.ICallCamera.Companion.Other.ACTION_SEAMLESS_TRANSITION_FINISH),
                IIPCAbility.ICallCamera.Companion.Other.PERMISSION_OPLUS_COMPONENT_SAFE,
                null
            )
            var isTimeOut = false
            try {
                delay(TIME_OUT_MS_SEAMLESS_TRANSITION)
                isTimeOut = true
                GLog.d(TAG, LogFlag.DL) { "waitSeamlessTransitionFinish:time out" }
            } finally {
                onDone.invoke(isTimeOut)
                //不论超时还是被取消，都需要进行unregisterReceiver
                GLog.d(TAG, LogFlag.DL) { "waitSeamlessTransitionFinish: unregister" }
                runCatching {
                    BroadcastDispatcher.unregisterReceiver(pageViewModel.context, receiver)
                }
            }
        }
    }

    /**
     * 获取缩图，并使用这张缩图创建transitionPreviewData。后者将用于启动大图入场动画。
     * 缩图的来源有两个：
     * 1. 大图资源加载所得到的focusPagerThumbnail，其size为TYPE_FULL_THUMBNAIL
     * 2. 大图入参collect时，发起的previewShot加载任务所得到的缩图，
     *    其size为TYPE_FULL_THUMBNAIL或TYPE_MICRO_THUMBNAIL（当大缩图不存在缓存）。
     *
     * 注：由于previewShot是在collect大图入参时就发起的任务，因此大概率使用的缩图是previewShot。
     *
     * @see com.oplus.gallery.photo_page.ui.section.pagecontainer.PhotoContainerSection.launchEnterTransitionIfReady
     */
    private fun updateTransitionPreviewDataIfNeeded(entrance: String) {
        val thumbnail = pageViewModel.contentLoading.focusPagerThumbnail.value?.thumbnail
            ?: pageViewModel.inputArguments.previewShot.value?.thumbnail

        val newPreviewData = pageViewModel.dataLoading.focusItemViewData?.let { TransitionPreviewData.from(it, thumbnail) }
            ?: pageViewModel.inputArguments.previewShot.value?.let { TransitionPreviewData(it.size, it.mimeType, it.thumbnail, it.extra) }
            ?: transitionPreviewData.value?.copy(drawable = thumbnail)
            ?: let {
                GLog.d(TAG, LogFlag.DL) { "[updateTransitionPreviewDataIfNeeded] waiting for newPreviewData, entrance=$entrance" }
                return
            }

        GLog.d(TAG, LogFlag.DL) {
            "[updateTransitionPreviewDataIfNeeded] ready for newPreviewData" +
                    ", entrance=$entrance, transitionPreviewData=$newPreviewData"
        }
        _transitionPreviewData.postValueIfChanged(newPreviewData)
    }

    /**
     * 通知相机是否正在大图浏览Quick图
     * - 相机支持错峰
     * - 相机收到消息后，后台生成final图
     */
    @Synchronized
    @Suppress("LongMethod")
    private fun notifyFastCaptureBrowsingIfNeeded(focusSlotViewDataDiff: DiffedPhotoItemViewData?) {
        if (isSupportQuickPhotoPublic.not()) {
            GLog.d(TAG, LogFlag.DL) { "[notifyFastCaptureBrowsingIfNeeded] return by quick public not support" }
            return
        }

        if (focusSlotViewDataDiff?.isContentChanged != true) {
            return
        }

        val oldItem = focusSlotViewDataDiff.oldItem
        val focusViewData = focusSlotViewDataDiff.newItem ?: return

        if (focusViewData.mediaId != oldItem?.mediaId) {
            //当图片id变化，重置
            triggerFocusMediaId = null
        }

        val adjacentMediaIdList: ArrayList<Int> = arrayListOf()
        val focusMediaId = focusViewData.mediaId
        val focusSrcMediaId = QuickImageMapManager.getSrcMediaId(focusMediaId)
        var isBrowsingFinal = true

        // focus 内容
        if ((focusViewData.contentQuality != PhotoItemViewData.ContentQuality.HIGH) || (focusSrcMediaId != focusMediaId)) {
            isBrowsingFinal = false
            triggerFocusMediaId = focusSrcMediaId
            shouldNotifyCameraPhotoLoadingCompleted = true
        }

        // focus 左边的内容
        val preViewData = pageViewModel.dataLoading.photoViewDataSet?.get(focusViewData.position - 1)
        val preViewMediaId = preViewData?.mediaId ?: -1
        val preViewSrcMediaId = QuickImageMapManager.getSrcMediaId(preViewMediaId)
        if ((preViewData?.contentQuality != PhotoItemViewData.ContentQuality.HIGH || (preViewSrcMediaId != preViewMediaId))) {
            preViewData?.mediaId?.let { adjacentMediaIdList.add(it) }
        }

        // focus 右边的内容
        val nextViewData = pageViewModel.dataLoading.photoViewDataSet?.get(focusViewData.position + 1)
        val nextViewMediaId = nextViewData?.mediaId ?: -1
        val nextSrcMediaId = QuickImageMapManager.getSrcMediaId(nextViewMediaId)
        if ((nextViewData?.contentQuality != PhotoItemViewData.ContentQuality.HIGH) || (nextSrcMediaId != nextViewMediaId)) {
            nextViewData?.mediaId?.let { adjacentMediaIdList.add(it) }
        }

        // 通知相机
        val time = System.currentTimeMillis()
        // 浏览到连拍图时 需要传递groupId
        pageViewModel.dataLoading.getMediaItem(focusViewData)?.let { mediaItem ->
            var groupId = -1L // 与相机的约定，默认传-1，即不是连拍图
            if (DatabaseUtils.isCShotIdValid(mediaItem.cShotID)) {
                groupId = getMediaGroupID(mediaItem.filePath)
            }
            // 是相册生成的quick图，但没有源mediaId，则需要将这张quick图需要移除tagFlag
            if (mediaItem.isFastCShotQuickImage() && (focusSrcMediaId == focusMediaId)) {
                launch(Dispatchers.CShot) {
                    QuickImageMapManager.correctQuickImageToFinal(
                        mediaItem.mediaId,
                        FastCShotImageInfo(mediaItem.mediaId.toLong(), mediaItem.filePath, null, null)
                    )
                }
            }
            // 通知相机
            launch(Dispatchers.SINGLE_UN_BUSY) {
                GTrace.trace("$TAG.notifyBrowsingCShotMedia") {
                    GLog.i(TAG, LogFlag.DL) {
                        "[notifyBrowsingMedia] notify browsing media, " +
                                "focusSrcMediaId: $focusSrcMediaId, groupId=$groupId, isBrowsingFinal: $isBrowsingFinal, " +
                                "adjacent: ${adjacentMediaIdList.getOrNull(0)} & ${adjacentMediaIdList.getOrNull(1)}, isBrowsing: true"
                    }
                    ipcAbility?.callCamera?.notifyBrowsingMedia(
                        pageViewModel.context, focusSrcMediaId, groupId, isBrowsingFinal, adjacentMediaIdList, true, time
                    )
                }
            }
            hasNotifyCameraBrowsingQuick = true
        }
    }

    /**
     * 相册与相机的交互配置变更后，通知相机新的配置
     */
    private fun notifyGalleryConfigIfChanged() {
        launch(Dispatchers.SINGLE_UN_BUSY) {
            val lastVersion = ConfigAbilityWrapper.getInt(GALLERY_CAMERA_CONFIG_VERSION, INVALID_CONFIG_VERSION)
            GLog.d(TAG, LogFlag.DL) { "[notifyGalleryConfigIfChanged] lastVersion:$lastVersion curVersion:$CURRENT_CONFIG_VERSION" }
            if (CURRENT_CONFIG_VERSION > lastVersion) {
                ipcAbility?.callCamera?.notifyGalleryConfigChanged(pageViewModel.context, THUMB_INFO_CONFIG)
                ContextGetter.context.getAppAbility<ISettingsAbility>()?.use {
                    it.setGalleryCameraConfigVersion(CURRENT_CONFIG_VERSION)
                }
            }
        }
    }

    /**
     * 获取groupId（最后一级文件夹名称）
     * 此处如果发生异常会传-1，因为正常是相机拍摄的,需要传递groupId,而且都是数字类型的.用户的场景是搬家后的场景 传-1L
     */
    private fun getMediaGroupID(filePath: String): Long {
        val currentDirectory = File(filePath)
        val parentPath: String = currentDirectory.parent
        val lastIndex = parentPath.lastIndexOf("/")
        return parentPath.substring(lastIndex + 1).toLongOrNull() ?: -1L
    }

    /**
     * 通知相机退出大图浏览Quick图
     *
     * stop后不会渲染，不需要通知相机埋点，否则start后，埋点会将退出相册界面后的时间也包含进来。
     */
    @Synchronized
    private fun notifyQuitFastCaptureBrowsingIfNeeded() {
        GLog.d(TAG, LogFlag.DL) {
            "[notifyQuitFastCaptureBrowsingIfNeeded] is support quick public: $isSupportQuickPhotoPublic" +
                    ", has notify: $hasNotifyCameraBrowsingQuick"
        }

        triggerFocusMediaId = null

        if (isSupportQuickPhotoPublic && hasNotifyCameraBrowsingQuick) {
            val time = System.currentTimeMillis()
            launch(Dispatchers.SINGLE_UN_BUSY) {
                GTrace.trace("$TAG.notifyBrowsingMedia") {
                    GLog.i(TAG, LogFlag.DL) {
                        "[notifyBrowsingMedia] notify browsing media, " +
                                "focusMediaId: -1, isBrowsingFinal: false, adjacent: arrayListOf, isBrowsing: false"
                    }
                    pageViewModel.context.getAppAbility<IIPCAbility>()?.use {
                        it.callCamera.notifyBrowsingMedia(pageViewModel.context, -1, false, arrayListOf(), false, time)
                    }
                }
            }
            hasNotifyCameraBrowsingQuick = false
        }
    }

    /**
     * 通知：当PhotoSlot渲染状态发生变更时
     *
     * @param slot       改变状态的slot/position
     * @param currentFocusSlot 当前焦点slot/position
     * @param slotData   slot对应的PhotoSlot上绑定的数据
     * @param oldStatus  旧的渲染状态
     * @param newStatus  新的渲染状态
     */
    @Synchronized
    fun notifyRenderingStatusChange(
        slot: Int,
        currentFocusSlot: Int,
        slotData: Any?,
        oldStatus: PhotoSlot.RenderingStatus,
        newStatus: PhotoSlot.RenderingStatus
    ) {
        if ((slot >= 0) && (slot == currentFocusSlot)) {
            _focusRenderingStatus.setOrPostValue(newStatus)
        }
        notifyCameraPhotoLoadingCompletedIfNeed(slot, slotData, oldStatus, newStatus)
    }

    /**
     * 通知相机图片加载完成
     *
     * @param slot       photoSlot对应的position
     * @param slotData   slot对应的PhotoSlot上绑定的数据
     * @param oldStatus  旧的渲染状态
     * @param newStatus  新的/当前的渲染状态
     */
    @Synchronized
    private fun notifyCameraPhotoLoadingCompletedIfNeed(
        slot: Int,
        slotData: Any?,
        oldStatus: PhotoSlot.RenderingStatus,
        newStatus: PhotoSlot.RenderingStatus
    ) {
        // 不支持错峰的版本不通知
        if (isSupportQuickPhotoPublic.not()) {
            return
        }

        // 渲染状态变化是非焦点slot，不通知
        if ((slot < 0) || (pageViewModel.dataLoading.focusSlot != slot)) {
            return
        }

        // 没有triggerFocusMediaId，不通知
        val triggerMediaId = triggerFocusMediaId ?: return

        // 异常场景拦截后，不通知
        if (shouldNotifyCameraPhotoLoadingCompleted.not()) {
            return
        }

        // 焦点数据为null时，不通知
        val focusItemViewData = pageViewModel.dataLoading.focusItemViewData ?: let {
            GLog.d(TAG, LogFlag.DL) { "[notifyCameraPhotoLoadingCompletedIfNeed] focusItemViewData = null. return" }
            return
        }

        // 焦点数据mediaId 与 triggerFocusMediaId 不一致时，不通知
        if (focusItemViewData.mediaId != triggerMediaId) {
            GLog.d(TAG, LogFlag.DL) {
                "[notifyCameraPhotoLoadingCompletedIfNeed] triggerMediaId = $triggerMediaId," +
                        " focusSlotMediaId = ${focusItemViewData.mediaId}. return"
            }
            return
        }

        // 焦点数据非final图，不通知
        if (focusItemViewData.contentQuality != PhotoItemViewData.ContentQuality.HIGH) {
            return
        }

        // 渲染状态是NoReady,或者状态变没有变化时，不通知
        if ((newStatus == PhotoSlot.RenderingStatus.NoReady) || (oldStatus == newStatus)) {
            return
        }

        // 当焦点图加载完成，triggerFocusMediaId需要被置null，避免重复通知
        triggerFocusMediaId = null

        GLog.d(TAG, LogFlag.DL) { "[notifyCameraPhotoLoadingCompletedIfNeed] slot = $slot, triggerMediaId = $triggerMediaId" }
        val timeStamp = System.currentTimeMillis()
        launch(Dispatchers.SINGLE_UN_BUSY) {
            GTrace.trace("$TAG.notifyPhotoLoadingCompleted") {
                ipcAbility?.let {
                    val allMediaCount = ConfigAbilityWrapper.getInt(ConfigID.Business.Statistics.GALLERY_MEDIA_COUNT, -1)
                    it.callCamera.notifyPhotoLoadingCompleted(
                        pageViewModel.context, triggerMediaId, newStatus.toString(), timeStamp, allMediaCount
                    )
                }
            }
        }
    }

    private fun <T> MutableLiveData<T>.postValueIfChanged(newValue: T?) {
        if (value != newValue) {
            setOrPostValue(newValue)
        }
    }

    /**
     * 获取退出时背景色的参数。
     * @param shouldOverrideStartColor 是否重新起始颜色
     */
    private fun findExitBackgroundTransition(
        shouldOverrideStartColor: Boolean = true
    ): AnimationKeyParams<Color>? =
        exitTransitionMaker.value?.backgroundColors
            ?.let { transition ->
                // copy 时， animations 要单独处理，否则会导致复用大图页的数据时出错。
                transition.copy(
                    animations = transition.animations.clone()
                )
            }?.also { transition ->
                // 从[viewModel.pageManagement.photoBackgroundAlpha]中读取需要复写起始背景色的透明度
                if (shouldOverrideStartColor && transition.animations.isNotEmpty()) {
                    transition.animations[0] =
                        transition.animations.first().let {
                            Color.valueOf(
                                it.red(), it.green(), it.blue(),
                                photoBackgroundAlpha.value ?: 1f
                            )
                        }
                }
            }

    /**
     * 获取退出时的位置动画参数
     */
    @Suppress("NestedBlockDepth")
    private fun findExitPositionTransition(
        exitActivityTransition: PageTransitionMaker.PageActivityTransition?,
        overrideStartPosition: Rect?,
        overrideEndPosition: Rect?
    ): AnimationKeyParams<Rect>? = pageViewModel.inputArguments.transition.value?.copy()?.let { transition ->
        transition.exitTransition.thumbnailPositions.let { positions ->

            val hasActivityTransition = (exitActivityTransition != null)

            /**
             * 是否需要重建动画。
             * - 有Activity动画
             * - overrideStartPosition 不为空。
             * - overrideEndPosition 不为空。
             */
            val needRebuildAnimations =
                (hasActivityTransition || (overrideStartPosition != null) || (overrideEndPosition != null))
                        && positions.animations.isEmpty()
            /*
             * 0. 准备好需要复写的关键帧用到的数据
             * 0-1 动画开始时延
             */
            val transitionStartDelay = positions.startDelay
            // 0-2 动画时长
            var transitionDuration = positions.duration
            // 0-3 动画关键帧
            val transitionAnimations = if (needRebuildAnimations) {
                arrayOf(Rect(), Rect())
            } else {
                positions.animations.clone()
            }
            // 0-4 动画缩略图半径
            val transitionThumbnailRadiusX = positions.thumbnailCornerRadiusX
            val transitionThumbnailRadiusY = positions.thumbnailCornerRadiusY

            // 0-5 动画中断距离
            val interruptDistance = positions.interruptDistance

            // 1. 复写转场动画的起始位置
            overrideStartPosition?.let {
                // 如果存在要复写的起始动画位置，则复写首个位置的Rect
                transitionAnimations.first().set(it)
            }

            // 2. 复写转场动画的结束位置
            if (hasActivityTransition) {
                // 如果存在Activity动画，终点位置应该是Activity动画指定的终点。
                exitActivityTransition?.sourceBounds?.let { destBound ->
                    if (destBound === emptySourceBounds()) {
                        // 当目标Bound为空，将动画时长改为0，不执行动画。
                        transitionDuration = 0
                    } else if (exitActivityTransition.transition == Resources.ID_NULL) {
                        // 相机无缝返回相机动画，当AnimationKeyParams的缩略图目标rect不为空，优先使用。再来才使用目标destBound
                        if (positions.animations.isEmpty()) {
                            transitionAnimations.last().set(destBound.toRect())
                        }
                    } else {
                        val pageSize = _pageSize.value
                        val pageWidth = pageSize?.width ?: 0
                        val pageHeight = pageSize?.height ?: 0

                        // Marked by 重构后赋值: 详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                        val destWidth = PhotoPageTransitionManager.DEFAULT_BACK_ANIMATION_DEST_SIZE
                        // Marked by 重构后赋值: 详见ReadMe/大图页面转场动画.md/与相机间的转场动画/遗留问题/1. 与相机间转场动画接口定义不完善
                        val destHeight = PhotoPageTransitionManager.DEFAULT_BACK_ANIMATION_DEST_SIZE

                        val destLeft = (destBound.left * pageWidth).roundToInt() - destWidth / 2
                        val destTop = (destBound.top * pageHeight).roundToInt() - destHeight / 2
                        val destRight = destLeft + destWidth
                        val destBottom = destTop + destHeight
                        transitionAnimations.last().set(destLeft, destTop, destRight, destBottom)
                    }
                }
            } else {
                // 如果不存在Activity动画，终点位置使用外部指定。
                overrideEndPosition?.let {
                    // 如果存在要复写的起始动画位置，则复写首个位置的Rect
                    transitionAnimations.last().set(it)
                }
            }

            AnimationKeyParams(
                duration = transitionDuration,
                startDelay = transitionStartDelay,
                animations = transitionAnimations,
                endPosition = positions.endPosition,
                interpolator = positions.interpolator,
                thumbnailCornerRadiusX = transitionThumbnailRadiusX,
                thumbnailCornerRadiusY = transitionThumbnailRadiusY,
                interruptDistance = interruptDistance
            )
        }
    }

    /**
     * 调整屏幕亮度，根据mode提升或降低屏幕亮度
     * 1、mode为Camera，设置成与Camera一致
     * 2、mode为Normal，设置成正常亮度
     */
    private fun adjustScreenBrightness(window: Window, mode: IScreen.BrightnessMode) {
        hardwareAbility?.let { hardwareAbility ->
            hardwareAbility.screen?.enableBrightnessMode(window, mode, isUsedCameraBrightness)
        } ?: GLog.w(TAG, LogFlag.DL) {
            "[enableBrightnessMode] hardwareAbility not found!"
        }
    }

    /**
     * 大图是否已稳定，可接受点击事件进入编辑
     * 正在执行菜单项操作、退出、翻页、抠图等情况下，不能响应进编辑请求
     */
    internal fun isPhotoPageStable(): Boolean {
        // 1.大图页正在退出、翻页等等
        if (photoPagerScrollState.value != PhotoPager.SCROLL_STATE_IDLE ||
            photoPageTransitionState.value != PRESENTED
        ) {
            GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] focusPager viewData not ready, not response.")
            return false
        }

        // 2.大图页大缩图没有准备就绪，不能进编辑
        pageViewModel.contentLoading.focusPagerThumbnail.value?.run {
            if (viewData.mediaId != pageViewModel.dataLoading.focusItemViewData?.mediaId) {
                GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] focusPager thumbnail not ready, not response.")
                return false
            }
            thumbnail?.let {
                if (it !is ThumbnailDrawable || it.bitmap?.isRecycled == true) {
                    GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] focusPager thumbnail not ready, not response.")
                    return false
                }
            } ?: let {
                GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] focusPager thumbnail still null, not response.")
                return false
            }
        } ?: let {
            GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] value of focusPagerThumbnail is null, not response.")
            return false
        }

        // 3.大图页有菜单项正在执行
        if (pageViewModel.menuControl.menuActionExecuting.value == true) {
            GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] some menuItem is executing! not response.")
            return false
        }

        // 4.正在抠图、或者正在显示抠图保存、分享相关的loading
        if (pageViewModel.lnS.isShowingLnSView.value == true ||
            pageViewModel.lnS.contentLoadingShowState.value == true
        ) {
            GLog.w(TAG, LogFlag.DL, "[isPhotoPageStable] photoPager is doing LnS func! not response.")
            return false
        }

        return true
    }

    /**
     * 当底部虚拟导航栏高度要变化时，call me
     * @param isStable 高度是否稳定
     */
    internal fun notifyNaviBarHeightChange(newHeight: Int) {
        _naviBarHeight.postValueIfChanged(newHeight)
    }

    override fun onCleared() {
        super.onCleared()
        selfSplittingManager.release()
    }

    /**
     * 变更大图入场动画、退场动画状态
     */
    private fun changePhotoPageTransitionState(state: PageTransitionState) {
        GLog.d(TAG, LogFlag.DL) { "[changePhotoPageTransitionState] photo page transition state=$state" }
        _photoPageTransitionState.setOrPostValue(state)
        _transitionState.value = state
    }

    /**
     * 跨模块过程调用通知PHOTO_PAGE_TRANSITION_STATE状态
     * @param transitionState PHOTO_PAGE_TRANSITION_STATE状态
     */
    private fun processPhotoPageTransitionState(transitionState: String) {
        val params = Bundle().apply {
            putString(OpenAbilityConstant.TRANSITION_STATE, transitionState)
        }
        moduleAccessAbility?.processCallCrossModule(PhotoPageModuleTopics.PHOTO_PAGE_TRANSITION_STATE.name, params = params)
    }

    /**
     * 可跳转的目标页面
     *
     * 目前仅针对业务添加了 时间轴内部页/编辑页/移动到
     * 后续可依据业务自行增加
     */
    internal enum class JumpablePage {
        /**
         * 大图页面
         */
        PHOTO_PAGE,

        /**
         * 内部页(时间轴)
         */
        INTERNAL_PAGE,

        /**
         * 编辑页
         */
        EDITOR_PAGE,

        /**
         * “移动到”页面
         */
        MOVE_TO_PAGE,

        /**
         * 连拍页面
         */
        CSHOT_PAGE,

        /**
         * 内部分享页
         * */
        INTERNAL_SHARE_PAGE,

        /**
         * 合成GIF页面
         */
        SYNTHESIS_GIF_PAGE;

        /**
         * 是否为大图页面
         */
        fun isPhotoPage() = this == PHOTO_PAGE

        /**
         * 是否为相册内部（时间轴）
         */
        fun isInternalPage() = this == INTERNAL_PAGE

        /**
         * 是否为编辑页面
         */
        fun isEditorPage() = this == EDITOR_PAGE

        /**
         * 是否为“移动到”页面
         */
        fun isMoveToPage() = this == MOVE_TO_PAGE

        /**
         * 是否为连拍页面
         */
        fun isCShotPage() = this == CSHOT_PAGE

        /**
         * 是否为内部分享页
         */
        fun isInternalSharePage() = this == INTERNAL_SHARE_PAGE

        /**
         * 是否为合成GIF页面
         */
        fun isSynthesisGifPage() = this == SYNTHESIS_GIF_PAGE
    }

    /**
     * 第一帧渲染的状态
     */
    internal enum class FirstFrameRenderingStatus {
        NONE_READY,
        THUMBNAIL_READY,
        CONTENT_READY,
        ALL_READY,
        ERROR;

        val isThumbnailReady: Boolean
            get() = (this == THUMBNAIL_READY) || (this == ALL_READY)

        val isContentReady: Boolean
            get() = (this == CONTENT_READY) || (this == ALL_READY)

        /**
         * 至少有一个Ready了
         */
        val isAtLeastOneReady: Boolean
            get() = isThumbnailReady || isContentReady

        val isError: Boolean
            get() = (this == ERROR)
    }

    /**
     * 大图页的状态
     *
     * 如果需要修改，一定要严格注意顺序，顺序会影响对比结果
     *
     * 当前顺序为：[NEW] < [ENTERING] < [PRESENTED] < [EXITING] < [DESTROYED]
     */
    enum class PageTransitionState {
        /**
         * 页面处于新创建状态
         */
        NEW,


        /**
         * 页面开始执行进入动画
         */
        ENTERING,


        /**
         * 进入动画完，页面处于常规展示
         */
        PRESENTED,


        /**
         * 页面开始执行退出动画
         */
        EXITING,


        /**
         * 页面处于销毁状态
         */
        DESTROYED;

        fun isDoingTransition() = (this == ENTERING) || (this == EXITING)

        /**
         * 页面是否已开始执行退出[EXITING]/销毁[DESTROYED]
         */
        fun isExiting() = (this >= EXITING)

        /**
         * 大图展开动画是否已经处于常规展示状态
         */
        fun isPresented() = (this == PRESENTED)
    }

    /**
     * Pre Transition当前是否正在做入场动画
     */
    internal fun isPreTransitionEntering(): Boolean {
        return preTransitionState == PhotoPageTransitionState.TRANSITION_ENTERING.name
    }

    /**
     * 入场动画过程中被中断，通知Pre Transition做退场动画
     */
    internal fun notifyPreTransitionDoExitWhenEnterTransition() {
        processPhotoPageTransitionState(PhotoPageTransitionState.TRANSITION_DO_EXIT_WHEN_ENTER.name)
    }

    /**
     * 检查当前屏幕能否支持到某个刷新率（单位：Hz）。
     *
     * @see RefreshRateRequest
     */
    internal fun isCurrentDisplaySupportForRefreshRate(request: RefreshRateRequest): Boolean {
        val screenAbility = hardwareAbility?.screen ?: let {
            GLog.w(TAG, LogFlag.DL) { "[isCurrentDisplaySupportForRefreshRateRequest] hardwareAbility#Screen is null? false will be returned." }
            return false
        }

        val supportedList = screenAbility.querySupportedMainDisplayWindowRefreshRates()
        return supportedList.firstOrNull { it == request.freshRate } != null
    }

    /**
     * 请求设置、更新页面刷新率
     *
     * @see RefreshRateRequest
     */
    internal fun notifyChangeWindowRefreshRate(window: Window?, request: RefreshRateRequest): Boolean {
        GLog.d(TAG, LogFlag.DL) { "[notifyChangeWindowRefreshRate] request=$request" }

        window ?: let {
            GLog.w(TAG, LogFlag.DL) { "[notifyRefreshRateRequest] window is null! Refresh set failed." }
            return false
        }

        return hardwareAbility?.screen?.setWindowRefreshRate(window, request.freshRate) ?: let {
            GLog.w(TAG, LogFlag.DL) { "[notifyChangeWindowRefreshRate] hardwareAbility#Screen is null! Refresh set failed." }
            false
        }
    }

    /**
     * 更新缩图轴是否可见
     */
    internal fun updateThumblineVisibility(isVisible: Boolean) {
        _isThumbLineVisible.tryEmit(isVisible)
    }

    /**
     * 刷新率请求，配合 [notifyChangeWindowRefreshRate] 使用
     */
    internal enum class RefreshRateRequest(val freshRate: Int) {
        /**
         * 请求默认刷新率。跟随系统
         */
        REQUEST_DEFAULT(IScreen.DEFAULT_WINDOW_REFRESH_RATE),

        /**
         * 请求60Hz低刷新率。
         * 特殊场景使用，比如普通视频播放时
         */
        REQUEST_LOW(REFRESH_RATE_LOW),

        /**
         * 请求高刷新率，能突破系统设置的相册帧率限制。
         * 特殊场景使用，比如高帧视频播放时。
         */
        REQUEST_HIGH(REFRESH_RATE_HIGH)
    }

    /**
     * slot滑动的事件
     */
    sealed class SlideEvent {
        /**
         * 下滑开始
         */
        object SlideDownBegin : SlideEvent()

        /**
         * 下滑结束
         */
        object SlideDownEnd : SlideEvent()

        /**
         * 下滑取消
         */
        object SlideDownCancel : SlideEvent()
    }

///////////////////// 以下为内部定义 ////////////////////////

    companion object {
        private const val TAG = "PhotoPageManagementViewModel"
        private const val SHOW: Boolean = true
        private const val HIDE: Boolean = false

        private const val REFRESH_RATE_LOW = 60
        private const val REFRESH_RATE_HIGH = 120

        /**
         * 最长无缝动画结束时间，保底超时.
         */
        private const val TIME_OUT_MS_SEAMLESS_TRANSITION = 500L
    }
}

/**
 * 从 [PhotoItemViewData] 中解析属性并创建 [TransitionPreviewData] 对象
 *
 * @param viewData 用于解析数据的 [PhotoItemViewData]
 * @param drawable 和 [PhotoItemViewData] 匹配的缩图信息，可以为 null，代表无缩图
 */
internal fun TransitionPreviewData.Companion.from(viewData: PhotoItemViewData, drawable: Drawable? = null): TransitionPreviewData {
    var currentSize = viewData.findCorrectedItemSize()
    if ((currentSize.width == 0) || (currentSize.height == 0)) {
        /*若尺寸异常，从缩图获取尺寸*/
        drawable?.apply {
            currentSize = Size(intrinsicWidth, intrinsicHeight)
            GLog.w(TAG, "[from] invalid size in viewData($viewData), use drawable size($currentSize)")
        }
    }
    return TransitionPreviewData(
        size = currentSize,
        mimeType = viewData.mimeType ?: EMPTY_STRING,
        drawable = drawable,
        extra = viewData.extraAttrs
    )
}

/**
 * 获取根据旋转角度矫正过的图片宽高。
 *
 * ItemViewData直接获取的宽高是没有考虑旋转角度的，
 * 在方法内部会根据旋转角度对宽高进行校正。
 */
internal fun PhotoItemViewData.findCorrectedItemSize(): Size {
    val rotation = supportedAbilities.getInt(SlotOverlayHelper.SUPPORT_MEDIA_ROTATION)
    val shouldReversalWH = (rotation % Math2DUtil.DEG_180I) != 0
    val originalItemWidth = supportedAbilities.getInt(SlotOverlayHelper.SUPPORT_WIDTH)
    val originalItemHeight = supportedAbilities.getInt(SlotOverlayHelper.SUPPORT_HEIGHT)

    val itemWidth = if (shouldReversalWH) originalItemHeight else originalItemWidth
    val itemHeight = if (shouldReversalWH) originalItemWidth else originalItemHeight
    return Size(itemWidth, itemHeight)
}

/**
 * 大图装饰器的状态，内部指定了当前是否应该显示以及状态变更时的动画参数
 * @param shouldShow 是否应该显示装饰器
 */
internal sealed class PhotoDecorationState(val shouldShow: Boolean, val animator: DecorationAnimator) {

    /**
     * 显示装饰器
     * @param animator 隐藏时使用的动画参数
     * @param operationType 操作类型
     */
    internal class Show(
        animator: DecorationAnimator,
        val operationType: String? = null
    ) : PhotoDecorationState(shouldShow = true, animator = animator) {
        companion object {
            /**
             * 以进入沉浸式的形式显示装饰器
             * 会显示额外的 UI 元素（如底栏、顶栏、播控...），会根据图片的大小进入普通模式或半沉浸模式。详见：[PhotoMenuSection.shouldUseHalfImmersiveMode]
             */
            val IMMERSIVE = Show(DecorationAnimator())
        }
    }

    /**
     * 隐藏装饰器
     * @param animator 隐藏时使用的动画参数
     * @param shouldHideNaviBar 隐藏底部导航栏（当前页面沉侵状态时隐藏，跳转页面不隐藏）
     * @param shouldCancelAnimator 是否需要取消动画，默认false
     * @param operationType 操作类型
     */
    internal class Hide(
        animator: DecorationAnimator,
        val shouldHideNaviBar: Boolean,
        val shouldCancelAnimator: Boolean = false,
        val operationType: String? = null
    ) : PhotoDecorationState(shouldShow = false, animator = animator) {
        companion object {
            /**
             * 以退出沉浸式的形式隐藏装饰器
             * 不显示额外的 UI 元素。
             */
            val IMMERSIVE = Hide(DecorationAnimator(), shouldHideNaviBar = true)

            /**
             * 以退出沉浸式的形式隐藏装饰器
             * 不隐藏的ui元素包含：顶部系统控制栏
             */
            val IMMERSE_SYSTEM_NOT_HIDE = Hide(DecorationAnimator(), shouldHideNaviBar = false)

            /**
             * 进入的沉浸式链路：首次进入大图,
             */
            const val OPERATION_TYPE_ENTER = "OPERATION_TYPE_ENTER"

            /**
             * 进入的沉浸式链路：下拉退出大图
             */
            const val OPERATION_TYPE_SLIDE_DOWN = "OPERATION_TYPE_SLIDE_DOWN"

            /**
             * 进入的沉浸式链路：通过动画退出大图
             */
            const val OPERATION_TYPE_EXIT = "OPERATION_TYPE_EXIT"
        }
    }

    /**
     * 装饰器的动画参数，目前为弹性动画
     *
     * @param defaultParam 默认的动画参数（非单独指定动画参数时使用）
     * @param halfImmersiveHideParam 半沉浸模式下隐藏动画的参数
     * @param onStart 动画开始的回调
     * @param onEnd 动画结束的回调
     */
    internal data class DecorationAnimator(
        val defaultParam: SpringAnimationParam = if (ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT)) {
            DEFAULT_LIGHT_OS
        } else {
            DEFAULT_NORMAL
        },
        val halfImmersiveHideParam: SpringAnimationParam = SpringAnimationParam.HALF_IMMERSIVE_HIDE,
        val onStart: () -> Unit = {},
        val onEnd: () -> Unit = {}
    )

    /**
     * 弹性动画的参数
     *
     * @param bounce 弹力，此值越大效果越明显
     * @param response 此值越小动画越快
     */
    internal data class SpringAnimationParam(
        val bounce: Float,
        val response: Float,
    ) {
        companion object {
            /**
             * 动画的默认弹性
             */
            private const val DEFAULT_BOUNCE = 0f

            /**
             * 动画的默认 response(常规)
             */
            private const val DEFAULT_RESPONSE_NORMAL = 0.3f

            /**
             * 动画的默认 response(轻量OS)
             */
            private const val DEFAULT_RESPONSE_LIGHT_OS = 0.1f

            /**
             * 大图背景response
             */
            private const val PHOTO_PAGE_RESPONSE = 0.3f

            /**
             * 半沉浸模式隐藏动画的 response
             */
            private const val HALF_IMMERSIVE_HIDE_RESPONSE = 0.25f

            /**
             * 默认动画参数(常规)
             */
            val DEFAULT_NORMAL = SpringAnimationParam(DEFAULT_BOUNCE, DEFAULT_RESPONSE_NORMAL)

            /**
             * 默认动画参数(轻量OS)
             */
            val DEFAULT_LIGHT_OS = SpringAnimationParam(DEFAULT_BOUNCE, DEFAULT_RESPONSE_LIGHT_OS)

            /**
             * 半沉浸式隐藏动画的参数
             */
            val HALF_IMMERSIVE_HIDE = SpringAnimationParam(DEFAULT_BOUNCE, HALF_IMMERSIVE_HIDE_RESPONSE)

            /**
             * 大图背景 Animation Param
             */
            val PHOTO_PAGE_ANIMATION_PARAM = SpringAnimationParam(DEFAULT_BOUNCE, PHOTO_PAGE_RESPONSE)

            /**
             * spring动画中alpha动画参数 固定值
             */
            const val MIN_ALPHA_VISIBLE_CHANGE = 0.00390625f
        }
    }
}

/**
 * 页面操作状态
 *
 * - 业务承接方：根据关心的点做对应操作
 * - 业务使用方：根据关心的点组合操作并通过 [PhotoPageManagementViewModel.requestPageOperation] 请求发送
 *
 * 注意：可以通过 `+`、`-`、`in` 操作符进行组合和判断
 */
internal sealed class PageOperationState private constructor(private val flags: Int) {

    /**
     * 无任何特殊操作
     */
    object NoneOperation : PageOperationState(FLAG_NONE_OPERATION)

    /**
     * 抑制大图 layout 操作
     */
    object SuppressPhotoLayout : PageOperationState(FLAG_SUPPRESS_PHOTO_LAYOUT)

    /**
     * 抑制大图手势
     */
    object SuppressPhotoGesture : PageOperationState(FLAG_SUPPRESS_PHOTO_GESTURE)

    /**
     * 抑制 playback 控制
     */
    object SuppressPlayback : PageOperationState(FLAG_SUPPRESS_PLAYBACK)

    /**
     * 抑制通知外部焦点变更
     */
    object SuppressOutputFocus : PageOperationState(FLAG_SUPPRESS_OUTPUT_FOCUS)

    /**
     * 抑制更新焦点位置缩图
     */
    object SuppressFocusThumbnail : PageOperationState(FLAG_SUPPRESS_FOCUS_THUMBNAIL)

    /**
     * 抑制菜单截图
     */
    object SuppressMenuCaptureVideoFrame : PageOperationState(FLAG_SUPPRESS_MENU_CAPTURE_VIDEO_FRAME)

    /**
     * 抑制禁用大图菜单
     */
    object DisableMenuAll : PageOperationState(FLAG_SUPPRESS_MENU_ALL)

    /**
     * 抑制功能区
     */
    object DisableFunctionalAll : PageOperationState(FLAG_SUPPRESS_FUNCTIONAL_ALL)

    /**
     * 抑制前台数据加载
     */
    object DisableDataInForeground : PageOperationState(FLAG_SUPPRESS_DATA_IN_FOREGROUND)

    /**
     * 抑制后台数据加载
     */
    object DisableDataInBackground : PageOperationState(FLAG_SUPPRESS_DATA_IN_BACKGROUND)

    /**
     * 抑制智能推荐功能区
     */
    object DisableIntelliFunc : PageOperationState(FLAG_SUPPRESS_INTELLI_FUNC)

    /**
     * 抑制任何情况的数据加载
     */
    object DisableDataAll : PageOperationState(FLAG_SUPPRESS_DATA_ALL)

    /**
     * 抑制所有效果，目前包含：
     *
     * - [SuppressPhotoLayout]
     * - [SuppressPhotoGesture]
     * - [SuppressPlayback]
     * - [SuppressOutputFocus]
     * - [SuppressFocusThumbnail]
     * - [SuppressMenuCaptureVideoFrame]
     * - [DisableFunctionalAll]
     * - [DisableDataInForeground]
     * - [DisableDataInBackground]
     * - [DisableDataAll]
     * - [DisableIntelliFunc]
     */
    object SuppressAll : PageOperationState(FLAG_SUPPRESS_ALL)

    /**
     * 是否有任意操作，即不是 [NoneOperation]
     */
    val hasOperations: Boolean
        get() = this != NoneOperation


    /**
     * 支持操作符 "+"
     */
    operator fun plus(other: PageOperationState): PageOperationState {
        if (other.flags == 0) return this
        return CombinationPageOperationState(this.flags or other.flags)
    }

    /**
     * 支持操作符 "-"
     */
    operator fun minus(other: PageOperationState): PageOperationState {
        if (other.flags == 0) return this
        return CombinationPageOperationState(this.flags and (other.flags.inv()))
    }

    /**
     * 支持操作符 "in"
     */
    operator fun contains(other: PageOperationState): Boolean {
        return (this.flags and other.flags) == other.flags
    }

    override fun equals(other: Any?): Boolean {
        return flags == (other as? PageOperationState)?.flags
    }

    override fun hashCode(): Int {
        return flags.hashCode()
    }

    override fun toString(): String {
        return "$TAG(flags=${flags.toString(BINARY_RADIX)})"
    }

    private class CombinationPageOperationState(flags: Int) : PageOperationState(flags)

    private companion object {
        private const val TAG: String = "PageOperationState"

        private const val BINARY_RADIX: Int = 2

        private const val FLAG_NONE_OPERATION: Int = 0
        private const val FLAG_SUPPRESS_PHOTO_LAYOUT: Int = 1 shl 0
        private const val FLAG_SUPPRESS_FUNCTIONAL_ALL: Int = 1 shl 1
        private const val FLAG_SUPPRESS_PHOTO_GESTURE: Int = 1 shl 2
        private const val FLAG_SUPPRESS_PLAYBACK: Int = 1 shl 3
        private const val FLAG_SUPPRESS_OUTPUT_FOCUS: Int = 1 shl 4
        private const val FLAG_SUPPRESS_FOCUS_THUMBNAIL: Int = 1 shl 5
        private const val FLAG_SUPPRESS_MENU_CAPTURE_VIDEO_FRAME: Int = 1 shl 6
        private const val FLAG_SUPPRESS_MENU_ALL: Int = 1 shl 7
        private const val FLAG_SUPPRESS_DATA_IN_FOREGROUND: Int = 1 shl 8
        private const val FLAG_SUPPRESS_DATA_IN_BACKGROUND: Int = 1 shl 9
        private const val FLAG_SUPPRESS_DATA_ALL: Int = FLAG_SUPPRESS_DATA_IN_FOREGROUND.plus(FLAG_SUPPRESS_DATA_IN_BACKGROUND)
        private const val FLAG_SUPPRESS_INTELLI_FUNC: Int = 1 shl 10
        private const val FLAG_SUPPRESS_ALL: Int = Int.MAX_VALUE
    }
}

internal val PhotoPageManagementViewModel.thumbLineHeightValue: Int
    get() = thumbLineHeight.value ?: 0

/**
 * 底部操作按钮中点距离底部的距离，
 * 用于与 view 本身的 height 一起计算 marginBottom
 */
internal val PhotoPageManagementViewModel.defaultFabCenterToTargetDistanceValue: Int
    get() = defaultFabCenterToTargetDistance.value ?: 0

/**
 * 大图背景动画执行时机事件
 */
enum class PhotoPageAnimationEvent {

    /**
     * 动画开始
     *
     */
    ANIMATION_START,

    /**
     * 动画结束
     *
     */
    ANIMATION_END
}