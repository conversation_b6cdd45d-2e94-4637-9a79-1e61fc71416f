/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : AnimatableStateImageView.kt
 ** Description : AnimatableStateImageView
 ** Version     : 1.0
 ** Date        : 2025/8/1 16:07
 ** Author      : 15712990633
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  18311499802     2025/8/1  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.graphics.drawable.Animatable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 一个自定义的ImageView，可以在两种状态之间切换，每种状态都有一个可动画的drawable。该视图在状态切换时可以播放动画。
 *
 * ## 功能特性
 *
 * - 两种不同的状态，每种状态都有自己的drawable
 * - 支持可动画的drawable（如AnimationDrawable、AnimatedVectorDrawable等）
 * - 可选择在切换状态时是否播放动画
 * - 提供控制动画播放的方法
 * - 支持XML属性配置
 *
 * ## 使用方法
 *
 * ### XML布局
 *
 * ```xml
 * <com.coloros.common.ui.AnimatableStateImageView
 *     android:id="@+id/animatable_state_image_view"
 *     android:layout_width="48dp"
 *     android:layout_height="48dp"
 *     app:firstStateDrawable="@drawable/first_state_drawable"
 *     app:secondStateDrawable="@drawable/second_state_drawable"
 *     app:initialState="first" />
 * ```
 *
 * ### 可用的XML属性
 *
 * - `app:firstStateDrawable` - 第一个状态的drawable
 * - `app:secondStateDrawable` - 第二个状态的drawable
 * - `app:initialState` - 视图的初始状态（"first"或"second"）
 *
 * ### Kotlin代码
 *
 * ```kotlin
 * // 获取视图引用
 * val imageView: AnimatableStateImageView = findViewById(R.id.animatable_state_image_view)
 *
 * // 以编程方式设置drawable
 * imageView.setFirstStateDrawableResource(R.drawable.first_state_drawable)
 * imageView.setSecondStateDrawableResource(R.drawable.second_state_drawable)
 *
 * // 带动画切换状态
 * imageView.toggleState(true)
 *
 * // 设置特定状态（带动画或不带动画）
 * imageView.setState(AnimatableStateImageView.STATE_FIRST, true)
 * imageView.setState(AnimatableStateImageView.STATE_SECOND, false)
 *
 * // 检查当前状态
 * val currentState = imageView.getCurrentState()
 * if (currentState == AnimatableStateImageView.STATE_FIRST) {
 *     // 第一个状态时的操作
 * } else {
 *     // 第二个状态时的操作
 * }
 *
 * // 手动控制动画
 * imageView.startAnimation()
 * imageView.stopAnimation()
 * val isAnimating = imageView.isAnimating()
 * ```
 *
 * ## 注意事项
 *
 * - 当视图从窗口分离或其可见性变为GONE或INVISIBLE时，会自动停止动画。
 * - 如果设置了非Animatable的drawable，动画相关的方法将不会有任何效果。
 * - 你可以使用任何实现了Animatable接口的drawable类型，如AnimationDrawable、AnimatedVectorDrawable或GifDrawable。
 *
 */
class AnimatableStateImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private var currentState: Int = STATE_FIRST

    // 每个状态的Drawable
    private var firstStateDrawable: Drawable? = null
    private var secondStateDrawable: Drawable? = null

    // 用于跟踪动画是否正在运行的标志
    private var isAnimationRunning: Boolean = false

    init {
        attrs?.let {
            val attributes = context.obtainStyledAttributes(it, R.styleable.AnimatableStateImageView)
            // 从属性中获取drawable
            firstStateDrawable = attributes.getDrawable(R.styleable.AnimatableStateImageView_firstStateDrawable)
            secondStateDrawable = attributes.getDrawable(R.styleable.AnimatableStateImageView_secondStateDrawable)

            // 获取初始状态
            currentState = attributes.getInt(R.styleable.AnimatableStateImageView_initialState, STATE_FIRST)

            attributes.recycle()
        }

        // 根据状态设置初始drawable
        updateDrawableForCurrentState(false)
    }

    /**
     * 设置第一个状态的drawable
     *
     * @param drawable 用于第一个状态的drawable
     */
    fun setFirstStateDrawable(drawable: Drawable?) {
        firstStateDrawable = drawable
        if (currentState == STATE_FIRST) {
            updateDrawableForCurrentState(false)
        }
    }

    /**
     * 使用资源ID设置第一个状态的drawable
     *
     * @param resId 用于第一个状态的drawable的资源ID
     */
    fun setFirstStateDrawableResource(@DrawableRes resId: Int) {
        setFirstStateDrawable(context.getDrawable(resId))
    }

    /**
     * 设置第二个状态的drawable
     *
     * @param drawable 用于第二个状态的drawable
     */
    fun setSecondStateDrawable(drawable: Drawable?) {
        secondStateDrawable = drawable
        if (currentState == STATE_SECOND) {
            updateDrawableForCurrentState(false)
        }
    }

    /**
     * 使用资源ID设置第二个状态的drawable
     *
     * @param resId 用于第二个状态的drawable的资源ID
     */
    fun setSecondStateDrawableResource(@DrawableRes resId: Int) {
        setSecondStateDrawable(context.getDrawable(resId))
    }

    /**
     * 获取视图的当前状态
     *
     * @return 当前状态（STATE_FIRST或STATE_SECOND）
     */
    fun getCurrentState(): Int = currentState

    /**
     * 设置视图的当前状态
     *
     * @param state 要设置的状态（STATE_FIRST或STATE_SECOND）
     * @param animate 是否动画显示状态变化
     */
    fun setState(state: Int, animate: Boolean) {
        if (currentState == state) return

        currentState = state
        updateDrawableForCurrentState(animate)
    }

    /**
     * 在两个状态之间切换
     *
     * @param animate 是否动画显示状态变化
     */
    fun toggleState(animate: Boolean) {
        setState(if (currentState == STATE_FIRST) STATE_SECOND else STATE_FIRST, animate)
    }

    /**
     * 检查视图当前是否正在动画
     *
     * @return 如果动画正在运行则为true，否则为false
     */
    fun isAnimating(): Boolean = isAnimationRunning

    /**
     * 停止任何正在运行的动画
     */
    fun stopAnimation() {
        if (isAnimationRunning && (drawable is Animatable)) {
            (drawable as Animatable).stop()
            isAnimationRunning = false
        }
    }

    /**
     * 为当前drawable启动动画
     */
    fun startAnimation() {
        if (!isAnimationRunning && (drawable is Animatable)) {
            (drawable as Animatable).start()
            isAnimationRunning = true
        }
    }

    /**
     * 根据当前状态更新drawable
     *
     * @param animate 是否为drawable添加动画
     */
    private fun updateDrawableForCurrentState(animate: Boolean) {
        // 停止任何正在运行的动画
        stopAnimation()

        // 获取当前状态的drawable
        val drawable = if (currentState == STATE_FIRST) firstStateDrawable else secondStateDrawable

        // 设置drawable
        drawable?.let {
            setImageDrawable(it)

            // 如果请求动画且drawable可动画，则启动动画
            if (animate && it is Animatable) {
                it.start()
                isAnimationRunning = true
            }
        } ?: run {
            GLog.w(TAG, LogFlag.DF, "No drawable available for state: $currentState")
        }
    }

    override fun onDetachedFromWindow() {
        stopAnimation()
        super.onDetachedFromWindow()
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        if (visibility != VISIBLE) {
            stopAnimation()
        }
    }

    companion object {
        private const val TAG = "AnimatableStateImageView"

        // 状态定义
        const val STATE_FIRST = 0
        const val STATE_SECOND = 1
    }
}
