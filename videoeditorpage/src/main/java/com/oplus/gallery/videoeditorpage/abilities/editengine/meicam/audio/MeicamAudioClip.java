/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MeicamAudioClip.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.audio;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAudioClip;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.data.BaseKeyFrameInfo;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.AudioClipParams;
import com.oplus.gallery.videoeditorpage.utlis.CollectionUtils;
import com.oplus.gallery.videoeditorpage.video.business.music.volume.entity.Volume;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IAudioClip;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

public class MeicamAudioClip implements IAudioClip, IBaseClip {
    private static final String TAG = "MeicamAudioClip";
    private transient NvsAudioClip mNvsAudioClip;
    private String mMediaPath = "";
    private long mTrimInTime = -1;
    private long mTrimOutTime = -1;
    private long mInPoint = 0;
    private long mFadeInDuration = 0;
    private long mFadeOutDuration = 0;
    private double mSpeed = 1;
    private Volume mVolume = new Volume();
    private boolean mKeepAudioPitch = DEFAULT_SPEED_KEEP_AUDIO_PITCH;
    private String mDisplayName;
    private boolean mEdited = false;
    private int mTrackIndex = 0;
    private int mMusicId = -1;
    private String mResourceType = "";
    private String mFileSystemPath = "";
    //音色id
    private String mToneId;

    private long mTextReadId;
    private boolean mAutoDuration = true;

    private int mBeatType = 0;
    private HashMap<Integer, ArrayList<Long>> mManualBeatMap = new HashMap<>();
    private HashMap<Integer, ArrayList<Long>> mBeatMap = new HashMap<>();
    private HashMap<String, Object> mAttachment = new HashMap<>();

    private boolean mMuted = false; //标志当前是否为静音，默认为false
    private List<BaseKeyFrameInfo> mKeyFrameList = new ArrayList<>();
    private boolean mFileChanged = true;
    private NvsAVFileInfo mNvsAVFileInfo;

    /**
     * there must have a default constructor
     * in case of change of class members, Gson can't read a valid value
     */
    protected MeicamAudioClip() {
        super();
    }

    public MeicamAudioClip(AudioClipParams clipParams) {
        mMediaPath = clipParams.getPath();
        mFileChanged = true;
        mResourceType = clipParams.getResourceType();
        mMusicId = clipParams.getMusicId();
        mTrimInTime = clipParams.getTrimInTime();
        mTrimOutTime = clipParams.getTrimOutTime();
        mFileSystemPath = clipParams.getFileSystemPath();
        mInPoint = clipParams.getInPoint();
    }

    @Override
    public void bindNvsObject(NvsAudioClip nvsAudioClip) {
        mNvsAudioClip = nvsAudioClip;
        setMuted(mMuted);
    }

    @Override
    public void addKeyFrame(BaseKeyFrameInfo keyFrame) {
        mKeyFrameList.add(keyFrame);
    }

    @Override
    public List<BaseKeyFrameInfo> getKeyFrameList() {
        if (mKeyFrameList == null) {
            mKeyFrameList = new ArrayList<>();
        }
        return mKeyFrameList;
    }

    public NvsAudioClip getNvsAudioClip() {
        return mNvsAudioClip;
    }

    public void clearNvsObjects() {
        mNvsAudioClip = null;
    }

    @Override
    public long getDuration() {
        return (long) ((mTrimOutTime - mTrimInTime) / mSpeed);
    }

    public void setInPoint(long inPoint) {
        mInPoint = inPoint;
    }

    @Override
    public long getInPoint() {
        return mInPoint;
    }

    @Override
    public long getOutPoint() {
        // InPoint in timeline, unnecessary to maintain in member variable
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "getOutPoint but mNvsAudioClip is null");
            return 0;
        }
        return mNvsAudioClip.getOutPoint();
    }

    @Override
    public void setInTime(long inPoint) {
        setTrimInPoint(inPoint, true);
    }

    @Override
    public void setOutTime(long outPoint) {
        setTrimOutPoint(outPoint, true);
    }

    @Override
    public long getTrimIn() {
        return this.mTrimInTime;
    }

    @Override
    public long getTrimOut() {
        return this.mTrimOutTime;
    }

    /**
     * 根据新时长同步更新淡入淡出时长
     *
     * @param newDuration 新片段时长
     * @param oldDuration 旧片段时长
     */
    private void updateFadeTime(long newDuration, long oldDuration) {
        // 计算新旧片段时长的比例
        double ratio = (1d * newDuration / oldDuration);
        // 等比例缩放淡入淡出时长
        setFadeInDuration(Math.round(mFadeInDuration * ratio));
        setFadeOutDuration(Math.round(mFadeOutDuration * ratio));
    }

    @Override
    public long setTrimInPoint(long trimIn, boolean affectSibling) {
        long oldDuration = getDuration();
        mTrimInTime = trimIn;
        if (mNvsAudioClip != null) {
            mTrimInTime = mNvsAudioClip.changeTrimInPoint(trimIn, affectSibling);
            mInPoint = mNvsAudioClip.getInPoint();
        } else {
            GLog.e(TAG, "Call changeTrimInPoint error: mNvsAudioClip == null:" + "trimIn " + trimIn + ", affectSibling " + affectSibling);
        }
        long newDuration = getDuration();
        // 片段入口点变化，同步更新淡化效果时长
        updateFadeTime(newDuration, oldDuration);
        return mTrimInTime;
    }

    @Override
    public long setTrimOutPoint(long trimOut, boolean affectSibling) {
        long oldDuration = getDuration();
        mTrimOutTime = trimOut;
        if (mNvsAudioClip != null) {
            mTrimOutTime = mNvsAudioClip.changeTrimOutPoint(trimOut, affectSibling);
        } else {
            GLog.e(TAG, "Call changeTrimOutPoint error: mNvsAudioClip == null:" + "trimOut " + trimOut + ", affectSibling " + affectSibling);
        }
        long newDuration = getDuration();
        // 片段出口点变化，同步更新淡化效果时长
        updateFadeTime(newDuration, oldDuration);
        return mTrimOutTime;
    }

    @Override
    public int getType() {
        return StreamingConstant.ClipType.CLIP_TYPE_AUDIO;
    }

    @Override
    public String getFilePath() {
        return this.mMediaPath;
    }

    @Override
    public void setFileSystemPath(String fileSystemPath) {
        mFileSystemPath = fileSystemPath;
    }

    @Override
    public int getTrackIndex() {
        return mTrackIndex;
    }

    @Override
    public void setTrackIndex(int trackIndex) {
        this.mTrackIndex = trackIndex;
    }

    @Override
    public long getFileDuration() {
        if ((mNvsAVFileInfo == null) || mFileChanged) {
            mFileChanged = false;
            //耗时操作，需要尽量避免
            mNvsAVFileInfo = EditorEngineGlobalContext.getInstance().getNvsAVFileInfo(getFilePath());
        }
        if (mNvsAVFileInfo == null) {
            GLog.e(TAG, "getFileDuration avFileInfo is null");
            return 0;
        }
        return mNvsAVFileInfo.getDuration();
    }

    @Override
    public void setFilePath(String filePath) {
        mFileChanged = !TextUtils.equals(mMediaPath, filePath);
        mMediaPath = filePath;
        GLog.d(TAG, "setFilePath, filePath:" + mMediaPath);
    }

    @Override
    public double getSpeed() {
        return this.mSpeed;
    }

    @Override
    public void setSpeed(double newSpeed) {
        if (newSpeed <= 0) {
            GLog.e(TAG, "speed is invalid");
            return;
        }
        this.mSpeed = newSpeed;
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "Call changeSpeed error: mNvsAudioClip == null:" + "newSpeed " + newSpeed);
            return;
        }
        mNvsAudioClip.changeSpeed(newSpeed);
    }

    @Override
    public void setSpeed(double newSpeed, boolean keepAudioPitch) {
        if (newSpeed <= 0) {
            GLog.e(TAG, "speed is invalid");
            return;
        }
        this.mSpeed = newSpeed;
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "Call changeSpeed error: mNvsAudioClip == null:" + "newSpeed " + newSpeed + ", keepAudioPitch " + keepAudioPitch);
            return;
        }
        mNvsAudioClip.changeSpeed(newSpeed, keepAudioPitch);
        mKeepAudioPitch = keepAudioPitch;
    }

    @Override
    public boolean getKeepAudioPitch() {
        return mKeepAudioPitch;
    }

    @Override
    public void setVolumeGain(float leftVolumeGain, float rightVolumeGain) {
        this.mVolume = new Volume(leftVolumeGain, rightVolumeGain);
        this.mMuted = (leftVolumeGain == Volume.VOLUME_MUTE_VALUE);
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "Call setVolumeGain error: mNvsAudioClip is null.");
            return;
        }
        mNvsAudioClip.setVolumeGain(mVolume.getLeftVolume(), mVolume.getRightVolume());
    }

    @Override
    public Volume getVolumeGain() {
        return this.mVolume;
    }

    @Override
    public boolean isMuted() {
        return mMuted;
    }

    @Override
    public void setMuted(boolean muted) {
        mMuted = muted;
        mVolume.setMute(muted);
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "Call setMuted error: mNvsAudioClip is null.");
            return;
        }
        mNvsAudioClip.setVolumeGain(mVolume.getLeftVolume(), mVolume.getRightVolume());
    }

    @Override
    public void setFadeInDuration(long duration) {
        this.mFadeInDuration = duration;
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "Call setFadeInDuration error: mNvsAudioClip == null:" + "duration " + duration);
            return;
        }
        mNvsAudioClip.setFadeInDuration(duration);
    }

    @Override
    public long getFadeInDuration() {
        return this.mFadeInDuration;
    }

    @Override
    public void setFadeOutDuration(long duration) {
        this.mFadeOutDuration = duration;
        if (mNvsAudioClip == null) {
            GLog.e(TAG, "Call setFadeOutDuration error: mNvsAudioClip == null:" + "duration " + duration);
            return;
        }
        mNvsAudioClip.setFadeOutDuration(duration);
    }

    @Override
    public long getFadeOutDuration() {
        return this.mFadeOutDuration;
    }

    @Override
    public void setToneId(String id) {
        mToneId = id;
    }

    @Override
    public String getToneId() {
        return mToneId;
    }

    @Override
    public void setTextReadId(long id) {
        mTextReadId = id;
    }

    @Override
    public long getTextReadId() {
        return mTextReadId;
    }

    @Override
    public void setDisplayName(String musicName) {
        mDisplayName = musicName;
    }

    @Override
    public String getDisplayName() {
        return mDisplayName;
    }

    @Override
    public int getClipIndex() {
        if (mNvsAudioClip != null) {
            return mNvsAudioClip.getIndex();
        }
        return 0;
    }

    @Override
    public void setBeatArray(int beatType, ArrayList<Long> beatArray) {
        mBeatMap.put(beatType, filterValidBeats(beatArray));
        mBeatType = beatType;
    }

    /**
     * 过滤出有效的节拍点
     *
     * @param beatArray 待过滤的节拍点
     * @return 过滤后有效的节拍点
     */
    private @NonNull ArrayList<Long> filterValidBeats(ArrayList<Long> beatArray) {
        ArrayList<Long> validBeatList = new ArrayList<>();
        if (beatArray != null && !beatArray.isEmpty()) {
            beatArray.forEach(beat -> {
                if (beat >= mTrimInTime && beat <= mTrimOutTime) {
                    validBeatList.add(beat);
                }
            });
        }
        return validBeatList;
    }

    @Override
    public ArrayList<Long> getBeatArray(int beatType) {
        return mBeatMap.get(beatType);
    }

    @Override
    public ArrayList<Long> getCurUsedBeatArray() {
        return mBeatMap.get(mBeatType);
    }

    @Override
    public int getCurUsedBeatType() {
        return mBeatType;
    }

    @Override
    public void addBeatPoint(long timestamp) {
        ArrayList<Long> beatArray = getCurUsedBeatArray();
        if (beatArray != null) {
            beatArray.add(timestamp);
            Collections.sort(beatArray);
        }

        ArrayList<Long> manual = getManualBeatArray(mBeatType);
        if (manual == null) {
            mManualBeatMap.put(mBeatType, new ArrayList<Long>());
            manual = getManualBeatArray(mBeatType);
        }
        if (manual != null) {
            manual.add(timestamp);
            Collections.sort(manual);
        }
    }

    @Override
    public void deleteBeatPoint(long timestamp) {
        ArrayList<Long> beatArray = getCurUsedBeatArray();
        if (beatArray != null) {
            beatArray.remove(timestamp);
        }

        ArrayList<Long> manual = getManualBeatArray(mBeatType);
        if (manual != null) {
            manual.remove(timestamp);
        }
    }

    @Override
    public ArrayList<Long> getManualBeatArray(int beatType) {
        return mManualBeatMap.get(beatType);
    }

    @Override
    public void copyManualBeatArray(IAudioClip audioClip) {
        mManualBeatMap = CollectionUtils.deepCopyMapOfList(((MeicamAudioClip) audioClip).mManualBeatMap);
        mManualBeatMap.entrySet().forEach(entry -> {
            entry.setValue(filterValidBeats(entry.getValue()));
        });
    }

    @Override
    public void copyBeatArray(IAudioClip audioClip) {
        mBeatMap = CollectionUtils.deepCopyMapOfList(((MeicamAudioClip) audioClip).mBeatMap);
        mBeatMap.entrySet().forEach(entry -> {
            entry.setValue(filterValidBeats(entry.getValue()));
        });
    }

    @Override
    public void copyAttachment(IAudioClip audioClip) {
        if (audioClip instanceof MeicamAudioClip) {
            this.mAttachment = (HashMap<String, Object>) ((MeicamAudioClip) audioClip).mAttachment.clone();
        }
    }

    @Override
    public void setMusicEdited() {
        mEdited = true;
    }

    @Override
    public boolean getAutoDuration() {
        return mAutoDuration;
    }

    @Override
    public void setAutoDuration(boolean autoDuration) {
        mAutoDuration = autoDuration;
    }

    @Override
    public boolean isMusicEdited() {
        return mEdited;
    }

    @Override
    public int getMusicId() {
        return mMusicId;
    }

    public void setAttachment(String key, Object object) {
        mAttachment.put(key, object);
    }

    @Override
    public Object getAttachment(String key) {
        return mAttachment.get(key);
    }

    public String getResourceType() {
        return mResourceType;
    }

    @Override
    public String getFileSystemPath() {
        return mFileSystemPath;
    }

    @Override
    public String toString() {
        return String.format("MeicamAudioClip{mTrimInTime=%d, mTrimOutTime=%d, mInPoint=%d}", mTrimInTime, mTrimOutTime, mInPoint);
    }
}
