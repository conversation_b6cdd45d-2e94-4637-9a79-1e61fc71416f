/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CustomRenderer.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.custom;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.SLRectF;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.filter.CustomEffectRenderer;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.meicam.sdk.NvsCustomVideoFx;

import java.util.HashMap;

public class CustomRenderer implements NvsCustomVideoFx.Renderer {
    private static final String TAG = "CustomRenderer";
    private static final int MILLISECOND_TO_SECOND = 1000;

    private String mPath;
    private CustomEffectRenderer mEffectRenderer;
    private IVideoClip mTargetClip;
    private int mEffectType = -1;
    private SLRectF mSLRectF;
    private HashMap<String, Float> mFloatParams;
    private boolean mFill2Edge = false;
    private boolean mIsInverseRegion = false;

    public CustomRenderer(String path) {
        mPath = path;
    }

    public CustomRenderer setTargetClip(IVideoClip mTargetClip, int effectType) {
        this.mTargetClip = mTargetClip;
        mEffectType = effectType;
        return this;
    }

    public void setCutRectF(SLRectF rectF, boolean fill) {
        mSLRectF = rectF;
        this.mFill2Edge = fill;
    }

    public void setFloatParams(HashMap<String, Float> params, boolean inverseRegion) {
        mFloatParams = params;
        this.mIsInverseRegion = inverseRegion;
    }

    @Override
    public void onInit() {
        if ((mEffectRenderer == null) || !mEffectRenderer.isProgramReady()) {
            mEffectRenderer = new CustomEffectRenderer();
            mEffectRenderer.setEffectType(mEffectType);
            mEffectRenderer.createProgram();
        }
    }

    @Override
    public void onCleanup() {
    }

    @Override
    public void onPreloadResources() {
    }

    @Override
    public void onRender(NvsCustomVideoFx.RenderContext renderContext) {
        GLog.d(TAG, "onRender hasBuddyVideoFrame= " + renderContext.hasBuddyVideoFrame
                + "; effectTime=" + renderContext.effectTime
                + "; effectStartTime=" + renderContext.effectStartTime
                + "; effectEndTime=" + renderContext.effectEndTime
                + "; inputTexId=" + renderContext.inputVideoFrame.texId
                + "; isUpsideDownTexture=" + renderContext.inputVideoFrame.isUpsideDownTexture);

        if ((mEffectRenderer == null) || !mEffectRenderer.isProgramReady()) {
            mEffectRenderer = new CustomEffectRenderer();
            mEffectRenderer.setEffectType(mEffectType);
            mEffectRenderer.createProgram();
        }

        GLog.d(TAG, "onRender, getMattingMaskCache texturesize :" + renderContext.inputVideoFrame.width + "*" + renderContext.inputVideoFrame.height);
        mEffectRenderer.setEffectType(mEffectType);
        mEffectRenderer.setFloatParams(mFloatParams, mIsInverseRegion);
        mEffectRenderer.setCutRectF(mSLRectF, mFill2Edge);
        mEffectRenderer.setRenderTarget(mTargetClip, renderContext.inputVideoFrame.isUpsideDownTexture);
        mEffectRenderer.setTextureSize(renderContext.inputVideoFrame.width, renderContext.inputVideoFrame.height);
        mEffectRenderer.setInTextureId(renderContext.inputVideoFrame.texId);
        mEffectRenderer.setOutTextureId(renderContext.outputVideoFrame.texId);
        mEffectRenderer.draw();
    }
}
