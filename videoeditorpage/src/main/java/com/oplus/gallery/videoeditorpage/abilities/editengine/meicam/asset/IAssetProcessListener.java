/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IAssetProcessListener.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.asset;

public interface IAssetProcessListener {

    /**
     * Installation finished
     *
     * @param assetPackageId       The asset package Id
     * @param assetPackageFilePath The asset package file path
     * @param assetPackageType     The asset package type
     */
    void onFinishAssetPackageInstallation(String assetPackageId, String assetPackageFilePath, int assetPackageType);

    /**
     * Upgrade finished
     *
     * @param assetPackageId       The asset package Id
     * @param assetPackageFilePath The asset package file path
     * @param assetPackageType     The asset package type
     */
    void onFinishAssetPackageUpgrading(String assetPackageId, String assetPackageFilePath, int assetPackageType);

    /**
     * 资源处理失败
     * @param code 错误码
     */
    void onAssetProcessError(int code);
}
