/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MediaInfo.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/12/05
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/05    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.data;

import android.database.Cursor;
import android.text.TextUtils;

import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.standard_lib.graphics.GridDrawableType;
import com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.memories.app.MemoriesManager;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

public class MemoriesInfo {
    public static final String THEME_MUSIC = "theme";
    private static final String TAG = "MemoriesInfo";

    public int mSetId;
    public int mType;
    public int mCount;
    public long mTaken;
    public long mStartTime;
    public long mEndTime;
    public long mDuration;
    public String mName;
    public String mSubName;
    public String mTheme;
    public String mMusic;
    public MediaInfo mCover;
    public List<MediaInfo> mMediaInfos = new ArrayList<>();

    private MediaInfo getCover() {
        for (MediaInfo info : mMediaInfos) {
            if ((info != null) && (info.mIsCover)) {
                GLog.d(TAG, "getCover size = " + mMediaInfos.size() + ", info = " + info);
                return info;
            }
        }

        return null;
    }

    /**
     * obtain一个精彩回忆
     * @return MemoriesInfo
     */
    public MemoriesInfo obtain() {
        MemoriesInfo memoriesInfo = new MemoriesInfo();
        memoriesInfo.mSetId = mSetId;
        memoriesInfo.mType = mType;
        memoriesInfo.mCount = mCount;
        memoriesInfo.mTaken = mTaken;
        memoriesInfo.mStartTime = mStartTime;
        memoriesInfo.mEndTime = mEndTime;
        memoriesInfo.mDuration = mDuration;
        memoriesInfo.mName = mName;
        memoriesInfo.mSubName = mSubName;
        memoriesInfo.mTheme = mTheme;
        memoriesInfo.mMusic = mMusic;
        for (MediaInfo info : mMediaInfos) {
            memoriesInfo.mMediaInfos.add(info.obtain());
        }
        memoriesInfo.mCover = memoriesInfo.getCover();
        return memoriesInfo;
    }

    public String getTimeText() {
        if (!TextUtils.isEmpty(mSubName)) {
            return mSubName;
        }
        return VideoEditorHelper.getDateString(mStartTime) + " - " + VideoEditorHelper.getDateString(mEndTime);
    }

    private boolean isSameCover(MediaInfo curCover, MediaInfo prevCover) {
        GLog.d(TAG, "isSameCover prevCover = " + prevCover + ", curCover = " + curCover);
        if (((prevCover == null) && (curCover == null))
                || ((prevCover != null) && prevCover.equals(curCover))) {
            return true;
        }

        return false;
    }

    private boolean isThemeMusic(String music) {
        return (!TextUtils.isEmpty(music) && music.startsWith(THEME_MUSIC));
    }

    private boolean isSameMusic(String curMusic, String prevMusic) {
        boolean result = false;
        if (TextUtils.equals(prevMusic, curMusic)) {
            result = true;
        }
        GLog.d(TAG, "isSameMusic result = " + result + ", prevMusic = " + prevMusic + ", curMusic = " + curMusic);
        return result;
    }

    @Override
    public int hashCode() {
        int result = (TextUtils.isEmpty(mName) ? 0 : mName.hashCode());
        return result + mSetId;
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof MemoriesInfo) {
            MemoriesInfo memoriesInfo = (MemoriesInfo) o;
            if ((memoriesInfo.mSetId == mSetId)
                    && (memoriesInfo.mType == mType)
                    && (memoriesInfo.mCount == mCount)
                    && (memoriesInfo.mTaken == mTaken)
                    && (memoriesInfo.mStartTime == mStartTime)
                    && (memoriesInfo.mEndTime == mEndTime)
                    && (memoriesInfo.mDuration == mDuration)
                    && TextUtils.equals(memoriesInfo.mName, mName)
                    && TextUtils.equals(memoriesInfo.mSubName, mSubName)
                    && TextUtils.equals(memoriesInfo.mTheme, mTheme)
                    && isSameCover(memoriesInfo.mCover, mCover)
                    && isSameMusic(memoriesInfo.mMusic, mMusic)
                    && memoriesInfo.mMediaInfos.equals(mMediaInfos)) {
                return true;
            }
        }
        return false;
    }

    private int getInVideoSize() {
        int count = 0;
        for (MediaInfo info : mMediaInfos) {
            if (info.mInVideo) {
                count++;
            }
        }
        return count;
    }

    @Override
    public String toString() {
        return "Id:" + mSetId
                + ", title:" + mName
                + ", subTitle:" + mSubName
                + ", type:" + mType
                + ", count:" + mCount
                + ", createTime:" + VideoEditorHelper.getDateString(mTaken)
                + ", startTime:" + VideoEditorHelper.getDateString(mStartTime)
                + ", endTime:" + VideoEditorHelper.getDateString(mEndTime)
                + ", theme:" + mTheme
                + ", music:" + mMusic
                + ", cover:" + mCover
                + ", imgSize:" + mMediaInfos.size()
                + ", inVideoSize:" + getInVideoSize()
                + ", mediaInfos:" + mMediaInfos;
    }

    public static MediaInfo getCover(List<MediaInfo> mediaInfos) {
        for (MediaInfo info : mediaInfos) {
            if ((info != null) && info.mIsCover) {
                GLog.d(TAG, "getCover size = " + mediaInfos.size() + ", info = " + info);
                return info;
            }
        }

        return null;
    }

    public List<MediaInfo> getCovers() {
        int defaultCoverCount = GridDrawableType.FIVE_GRID.getThumbnailNum();
        List<MediaInfo> covers = new ArrayList<>();
        for (MediaInfo info : mMediaInfos) {
            if ((info != null) && info.mIsCover) {
                covers.add(info);
            }
            //获取回忆列表封面上的五张图片
            if (defaultCoverCount == covers.size()) {
                break;
            }
        }
        GLog.d(TAG, LogFlag.DL, () -> "[getCovers] mediaInfos size = " + mMediaInfos.size() + ", covers size = " + covers.size());
        return covers;
    }

    public static MediaInfo getNextCover(List<MediaInfo> mediaInfos) {
        // get next cover
        if (!mediaInfos.isEmpty()) {
            for (int i = 0; i < mediaInfos.size(); i++) {
                MediaInfo info = mediaInfos.get(i);
                if ((info != null) && info.mInVideo) {
                    info.mIsCover = true;
                    GLog.d(TAG, "getNextCover info = " + info);
                    return info;
                }
            }
        }
        return null;
    }

    public static List<MemoriesInfo> buildMemoriesInfoList(Cursor cursor) {
        List<MemoriesInfo> list = new ArrayList<>();
        if (cursor == null) {
            return list;
        }

        final int setIdIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns._ID);
        final int nameIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.NAME);
        final int typeIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.TYPE);
        final int takenIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.TAKEN);
        final int startIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.START_TIME);
        final int endIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.END_TIME);
        final int themeIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.THEME);
        final int musicIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.MUSIC);
        final int countIndex = cursor.getColumnIndex(GalleryStore.MemoriesSetColumns.META_NUM);
        while (cursor.moveToNext()) {
            MemoriesInfo info = new MemoriesInfo();
            info.mSetId = cursor.getInt(setIdIndex);
            info.mName = cursor.getString(nameIndex);
            info.mType = cursor.getInt(typeIndex);
            info.mTaken = cursor.getLong(takenIndex);
            info.mStartTime = cursor.getLong(startIndex);
            info.mEndTime = cursor.getLong(endIndex);
            info.mTheme = cursor.getString(themeIndex);
            info.mMusic = cursor.getString(musicIndex);
            info.mCount = cursor.getInt(countIndex);
            list.add(info);
            GLog.d(TAG, "buildMemoriesInfoList size = " + list.size() + ", info = " + info);
        }
        return list;
    }

    public void dump(PrintWriter writer) {
        if (MemoriesManager.DUMP_DEBUG) {
            writer.println("----------------------- Dump MemoriesInfo start -----------------------");
            writer.println("setId = " + mSetId);
            writer.println("name = " + mName);
            writer.println("subName = " + mSubName);
            writer.println("type = " + mType);
            writer.println("count = " + mCount);
            writer.println("createTime = " + VideoEditorHelper.getDateString(mTaken));
            writer.println("startTime = " + VideoEditorHelper.getDateString(mStartTime));
            writer.println("endTime = " + VideoEditorHelper.getDateString(mEndTime));
            writer.println("imgSize = " + mMediaInfos.size());
            writer.println("inVideoSize = " + getInVideoSize());
            writer.println("----------------------------------------");
            writer.println("theme = " + mTheme);
            writer.println("music = " + mMusic);
            writer.println("cover = " + mCover);

            if (mMediaInfos.size() > 0) {
                writer.println("-------- dump mMediaInfos --------");
                writer.println("dump mMediaInfos.size = " + mMediaInfos.size());
                for (int i = 0; i < mMediaInfos.size(); i++) {
                    if (mMediaInfos.get(i) != null) {
                        writer.println("dump mMediaInfos i " + i + ", info = " + mMediaInfos.get(i));
                    }
                }
            }
            writer.println("----------------------- Dump MemoriesInfo end -----------------------");
            writer.println("");
        }
    }
}
