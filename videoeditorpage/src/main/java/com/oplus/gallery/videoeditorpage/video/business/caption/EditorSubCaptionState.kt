/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : EditorSubCaptionState.kt
 ** Description : 字幕状态二级页面管理接口
 ** Version     : 1.0
 ** Date        : 2025/5/19 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/5/19  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption

import android.content.Context
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.BaseCaption
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.caption.MeicamCaption
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel
import com.oplus.gallery.videoeditorpage.abilities.editengine.params.CaptionParams
import com.oplus.gallery.videoeditorpage.video.business.base.EditorEffectTrackState.Companion.CLICK_TYPE_ADD
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseState
import com.oplus.gallery.videoeditorpage.video.business.base.PageLevelEnum
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.StyleViewData
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorEffectTrackConfig
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView
import java.lang.ref.WeakReference

/**
 * 编辑字幕状态
 */
class EditorSubCaptionState(
    context: Context,
    editorControlView: EditorControlView,
    weakTimelineViewModel: WeakReference<TimelineViewModel>?,
    var caption: BaseCaption?,
    private val clickType: Int,
    private val shouldShowKeyboard: Boolean,
    private val captionChangedCallback: (newCaption: BaseCaption?) -> Unit
) : EditorTrackBaseState<EditorSubCaptionUIController>(
    TAG,
    context,
    editorControlView,
    weakTimelineViewModel
), CaptionOperationListener {

    /**
     * 字幕编辑弹窗
     */
    private var inputCaptionDialog: CaptionInputDialogFragment? = null

    override val trackConfig: EditorEffectTrackConfig
        get() = EditorEffectTrackConfig.getConfig(EditorTrackScene.CAPTION)

    override fun getClipModelType(): Int {
        return ClipModel.ClipType.CLIP_TEXT
    }

    /**
     * 退出二级字幕编辑页，当点击了预览编辑框上的删除按钮时需要调用该方法退出字幕编辑
     */
    fun exitEditPage() {
        onCaptionDialogStateChanged(CaptionDialogState.FINISHED, false)
    }
    /**
     * 调起编辑输入框
     */
    private fun showInputCaptionDialog() {
        dismissInputCaptionDialog()
        inputCaptionDialog = null
        inputCaptionDialog = CaptionInputDialogFragment(mContext, this, shouldShowKeyboard).apply {
            selectedCaption = caption as? MeicamCaption
        }
        inputCaptionDialog?.show()
    }

    /**
     * 隐藏编辑弹窗
     */
    private fun dismissInputCaptionDialog() {
        inputCaptionDialog?.let {
            if (it.isShowing) it.dismiss()
        }
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        if (shouldUpdateDialogLayout(uiConfig)) {
            inputCaptionDialog?.updateLayout()
        }
    }

    /**
     * 当以下任意一个 UI 配置发生变化时，返回 `true`：
     * - 屏幕模式（如横屏/竖屏切换）
     * - 是否进入多窗口模式
     * - 是否进入悬浮窗模式
     * @param uiConfig 当前的 UI 配置对象，包含屏幕模式和窗口模式的状态
     * @return 如果任意配置发生变化，返回 `true`；否则返回 `false`
     */
    private fun shouldUpdateDialogLayout(uiConfig: AppUiResponder.AppUiConfig): Boolean {
        return uiConfig.screenMode.isChanged()
                || uiConfig.isInMultiWindow.isChanged()
                || uiConfig.isInFloatingWindow.isChanged()
    }

    /**
     * 替换当前字幕的字幕类型，如普通字幕使用拼接字幕样式时需要先把普通字幕替换成拼接字幕
     */

    private fun replaceCurrentCaptionWithType(captionType: Int, assetPackageId: String): MeicamCaption? {
        editorEngine.stopPlayer()
        var newCaption: MeicamCaption? = null
        val timeline = timeline ?: return newCaption
        if (caption?.captionType != captionType) {
            newCaption = (caption as? MeicamCaption)?.copy(timeline, captionType)?.apply {
                captionStyleId = assetPackageId
            }?.also {
                editorEngine.removeCaption(caption)
                captionChangedCallback(it)
                inputCaptionDialog?.selectedCaption = it as MeicamCaption
                caption = it
            } as? MeicamCaption
        }
        return newCaption
    }

    /**
     * 字幕样式发生变更，需要重新创建新类型字幕做替换
     *
     * @param captionType 字幕类型
     * @param assetPackageId 样式资源包id
     */
    override fun onCaptionTypeChanged(captionType: Int, assetPackageId: String): MeicamCaption? {
        return replaceCurrentCaptionWithType(captionType, assetPackageId)
    }

    /**
     * 字幕需要渲染效果预览
     *
     */
    override fun onCaptionPropertyUpdated() {
        // 更新预览区域
        updateCaptionRenderingForPreview()
    }

    /**
     * 字幕编辑完成时的回调
     *
     * @param isAddCaption 标记点击完成按钮是做了新增字幕还是编辑字幕操作
     */
    override fun onCaptionDialogStateChanged(state: CaptionDialogState, isAddCaption: Boolean) {
        editorEngine.stopPlayer()
        when (state) {
            CaptionDialogState.FINISHED -> {
                // 编辑完成，保存数据
                caption?.let {
                    if (it.isDefaultCaption) {
                        editorEngine.removeCaption(it)
                        caption = null
                        captionChangedCallback(null)
                    } else {
                        operationSaveHelper?.saveCurrentTimeline(
                            if (isAddCaption) OperationType.CAPTION_ADD else OperationType.CAPTION_UPDATE
                        )
                    }
                }
            }

            CaptionDialogState.DISMISSED -> {
                // 中断退出，删除默认字幕或回退状态
                caption?.let {
                    if (it.isDefaultCaption) {
                        editorEngine.removeCaption(it)
                        caption = null
                        captionChangedCallback(null)
                    }
                }
            }
        }
        iFxChangeListener?.onFxChange(if (caption?.isDefaultCaption == true) null else caption)
        // 退出页面
        super.clickDone()
    }

    /**
     * 默认样式准备好后回调通知去创建添加默认字幕
     * @param captionStyle 字幕样式数据
     * @param assetPackageId 样式资源安装后的id
     */
    override fun onDefaultCaptionCreate(captionStyle: StyleViewData, assetPackageId: String): MeicamCaption? {
        if (clickType != CLICK_TYPE_ADD) return (caption as? MeicamCaption)
        getEffectStartEndTime(
            mEditorEngine.currentTimeline,
            mEditorEngine.currentTimeline.captionList
        )?.let { range ->
            val fontPath = captionStyle.fontViewData?.localPath ?: TextUtil.EMPTY_STRING
            val fontId = captionStyle.fontViewData?.resourceId ?: TextUtil.EMPTY_STRING
            val params = CaptionParams(range.inPoint, range.outPoint)
                .withCaptionType(captionStyle.captionType)
                .withText(mContext.getString(R.string.videoeditor_caption_edit_input_text_hide))
                .withDefaultTrackIndex()
                .withAssetPackageId(assetPackageId)
                .withFontPath(fontPath)
            editorEngine.stopPlayer()
            caption = editorEngine.appendCaption(params)
            caption?.let {
                it.styleId = captionStyle.resourceId
                it.fontId = fontId
                captionChangedCallback.invoke(it)
                inputCaptionDialog?.selectedCaption = it as? MeicamCaption
                updateCaptionRenderingForPreview()
            }
            return caption as? MeicamCaption
        }
        return null
    }

    /**
     * 切换样式进行播放预览
     */
    override fun onCaptionStartPlay() {
        caption?.let {
            editorEngine.startPlayer(it.inTime, it.outTime)
        }
    }

    /**
     * 默认字幕的内容有变化时进行回调，字幕内容不是“请输入文字”时进行回调，用于更新当前字幕实例
     * @param caption 字幕
     */
    override fun onDefaultCaptionTextChanged(caption: MeicamCaption) {
        editorEngine.stopPlayer()
        captionChangedCallback.invoke(caption)
    }

    /**
     * 更新字幕效果显示,当有字幕的样式、字体、文本、颜色等发生修改后如果需要实时预览，则要调用该函数
     */
    private fun updateCaptionRenderingForPreview() {
        // 渲染的时候先停止播放，当前时码线在当前选中的字幕时间内则使用当前时码线的时间位置进行seek避免发生跳变
        editorEngine.stopPlayer()
        val timelinePosition = editorEngine.getTimelineCurrentPosition()
        // 初始化默认字幕seek渲染位置
        var position = timelinePosition
        caption?.let {
            position = if (timelinePosition <= it.inTime) {
                // 时码线在字幕开始时间之前，则使用字幕开始时间
                it.inTime
            } else if (timelinePosition >= it.outTime) {
                // 时码线在字幕结束时间之后，则使用矫正后字幕结束时间（特效轨道有效时间是半开半闭形式：[inTime, outTime)）
                it.outTime - TimelineViewModel.ADJUST
            } else {
                // 使用时码线时间
                timelinePosition
            }
        }
        editorEngine.seekTo(position, STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER)
        menuState?.onPlayPositionChange(position)
        onPlayPositionChange(position)
    }

    override fun create() {
        super.create()
        showInputCaptionDialog()
    }
    override fun resume(isActivityResume: Boolean) {
        super.resume(isActivityResume)
        updateCaptionRenderingForPreview()
    }

    override fun destroy() {
        dismissInputCaptionDialog()
        super.destroy()
    }

    override fun isPreview(): Boolean {
        return false
    }

    override fun createUIController(): EditorSubCaptionUIController {
        return EditorSubCaptionUIController(
            mContext,
            mEditorControlView,
            this,
        )
    }

    /**
     * 配置一级页是否显示撤销/重做按钮
     */
    override fun showOperaIcon(): Boolean {
        return false
    }

    /**
     * 配置页面层级，用于baseUIController控制页面通用布局
     */
    override fun getPageLevel(): PageLevelEnum {
        return PageLevelEnum.PAGE_LEVEL_SECOND
    }

    companion object {
        private const val TAG = "EditorSubCaptionState"
    }
}