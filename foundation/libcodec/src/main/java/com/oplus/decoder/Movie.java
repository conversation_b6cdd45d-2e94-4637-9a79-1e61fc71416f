/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Movie.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/01/31
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2021/01/31		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.decoder;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;

import com.oplus.gallery.standard_lib.codec.DecodeUtils;
import com.oplus.gallery.standard_lib.util.loadLibrary.LoadLibraryTool;

import java.io.InputStream;

/**
 * 注意：该类不能随意修改包名。因为存在于native层的交互，一旦修改包名会导致native逻辑异常，从而导致解码异常。
 */
public class Movie {
    private final long mNativeMovie;
    private static final String TAG = "Movie";
    private Bitmap mBitmap;
    private int mWidthLimit;
    private int mHeightLimit;

    private Movie(long nativeMovie) {
        if (nativeMovie == 0) {
            throw new RuntimeException("native movie creation failed");
        }
        mNativeMovie = nativeMovie;
        mWidthLimit = -1;
        mHeightLimit = -1;
    }

    static {
        LoadLibraryTool.loadLibrary("oplus_gifdecoder");
    }

    public native int width();

    public native int height();

    /**
     * This method get frame duration of specified GIF frame
     */
    public native int getFrameDuration(int frameIndex);

    /**
     * This method get total frame count of GIF file
     */
    public native int getTotalFrameCount();

    /**
     * This method get Bitmap of specified GIF frame
     */
    public native Bitmap getFrameBitmap(int frameIndex);

    public native void setLimitSize(int width, int height);

    /**
     * This method release all the Info stored for GIF. After this method is
     * call, Movie Object should no longer be used. eg. mMovie.closeGif();
     * mMovie = null;
     */
    public native void closeGif();

    public native boolean isGifStream();

    public static native Movie openInputStream(InputStream is);

    // modify original public method to make decode work properly end.

    private static native void nativeDestructor(long nativeMovie);

    /**
     * Encode UV shader source
     * @param source UV shader source code string
     * @return encoded byte array of UV shader source
     */
    public static native byte[] encode(String source);

    /**
     * Decode UV shader cipher
     * @param assetManager
     * @param path UV Shader cipher path
     * @return UV Shader source code string
     */
    public static native String decode(AssetManager assetManager, String path);

    public void close() {
        closeGif();
        if (mBitmap != null) {
            mBitmap.recycle();
            mBitmap = null;
        }
    }

    private static final Bitmap createBitmap(Bitmap srcBitmap, int widthLimit, int heightLimit,
                                             boolean isRecycle) {
        int width = srcBitmap.getWidth();
        int height = srcBitmap.getHeight();
        if (widthLimit < 1) {
            widthLimit = 1;
        }
        if (heightLimit < 1) {
            heightLimit = 1;
        }
        float scaleX = (width <= widthLimit) ? 1.0f : ((float) widthLimit / width);
        float scaleY = (height <= heightLimit) ? 1.0f : ((float) heightLimit / height);
        int targetWidth = (int) (width * scaleX);
        int targetHeight = (int) (height * scaleY);
        Bitmap target = Bitmap.createBitmap(targetWidth, targetHeight, DecodeUtils.DECODE_CONFIG);
        Canvas canvas = new Canvas(target);
        canvas.scale(scaleX, scaleY);
        Paint paint = new Paint(Paint.FILTER_BITMAP_FLAG | Paint.DITHER_FLAG);
        canvas.drawBitmap(srcBitmap, 0, 0, paint);
        if (isRecycle) {
            srcBitmap.recycle();
        }
        return target;
    }

    public void setLimit(int widthLimit, int heightLimit) {
        mWidthLimit = (widthLimit < 1) ? 1 : widthLimit;
        mHeightLimit = (heightLimit < 1) ? 1 : heightLimit;
    }

    public void setLimit(int limit) {
        if (limit < 1) {
            return;
        }
        int w = width();
        int h = height();
        if (limit < w || limit < h) {
            float scale = Math.min((float) limit / w, (float) limit / h);
            mWidthLimit = (int) (scale * w);
            mHeightLimit = (int) (scale * h);
        } else {
            mWidthLimit = w;
            mHeightLimit = h;
        }
    }

    public Bitmap getBitmap(int frameIndex) {
        mHeightLimit = (mHeightLimit == -1) ? height() : mHeightLimit;
        mWidthLimit = (mWidthLimit == -1) ? width() : mWidthLimit;
        setLimitSize(mWidthLimit, mHeightLimit);
        return getFrameBitmap(frameIndex);
    }

    @Override
    protected void finalize() throws Throwable {
        try {
            if (mBitmap != null) {
                mBitmap.recycle();
            }
            nativeDestructor(mNativeMovie);
        } finally {
            super.finalize();
        }
    }
}
