/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigApiCallDetector.kt
 ** Description: 配置接口调用代码检查器
 ** Version: 1.0
 ** Date: 2023/4/26
 ** Author: <EMAIL>
 **
 ** - Revision History:
 ** <author>                        <date>       <version>    <desc>
 **
 ** <EMAIL>              2023/4/26      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.foundation.codecheck.detector

import com.android.tools.lint.detector.api.Category
import com.android.tools.lint.detector.api.ConstantEvaluator
import com.android.tools.lint.detector.api.Detector
import com.android.tools.lint.detector.api.Implementation
import com.android.tools.lint.detector.api.Issue
import com.android.tools.lint.detector.api.JavaContext
import com.android.tools.lint.detector.api.Scope
import com.android.tools.lint.detector.api.Severity
import com.intellij.psi.PsiMethod
import org.jetbrains.uast.UCallExpression
import org.jetbrains.uast.UExpression

class ConfigApiCallDetector : Detector(), Detector.UastScanner {

    override fun getApplicableMethodNames(): List<String> {
        return boolCfgMethods + floatCfgMethods + intCfgMethods +
                longCfgMethods + stringCfgMethods + objCfgMethods
    }

    override fun visitMethodCall(context: JavaContext, node: UCallExpression, method: PsiMethod) {
        val evaluator = context.evaluator
        if (evaluator.isMemberInClass(method, "com.oplus.abilityconfig.api.IConfigAbility")
            || (evaluator.isMemberInClass(method, "com.oplus.abilityconfig.api.IConfigSetterAbility"))
            || (evaluator.isMemberInClass(method, "com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper"))
        ) {
            checkConfigApiCall(node, context)
        }
    }

    /**
     * 检查业务端对config api的调用
     * 检查api接口使用与实际配置ID对应的数据类型是否匹配
     * 如果configId对应的存储类型是int，那么就应该使用getIntConfig读取，而不是getBooleanConfig接口。
     *
     * @param node config api调用的表达式
     * @param context 上下文对象
     */
    private fun checkConfigApiCall(node: UCallExpression, context: JavaContext) {
        val methodName = node.methodName
        val configId: String = getConstParamValue(
            context,
            node.valueArguments[0]
        ).toString()
        val map = ConfigDataTypeMap.buildMap()
        map.forEach {
            if (it.key == configId) {
                // 方法与配置项存储的数据类型匹配
                if (intCfgMethods.contains(methodName) && it.value == DataType.INT) {
                    println("${TAG}configID data type match config api. It's right call")
                } else if (boolCfgMethods.contains(methodName) && it.value == DataType.BOOL) {
                    println("${TAG}configID data type match config api. It's right call")
                } else if (longCfgMethods.contains(methodName) && it.value == DataType.LONG) {
                    println("${TAG}configID data type match config api. It's right call")
                } else if (floatCfgMethods.contains(methodName) && it.value == DataType.FLOAT) {
                    println("${TAG}configID data type match config api. It's right call")
                } else if (objCfgMethods.contains(methodName) && it.value == DataType.OBJECT) {
                    println("${TAG}configID data type match config api. It's right call")
                } else if (stringCfgMethods.contains(methodName) && it.value == DataType.STRING) {
                    println("${TAG}configID data type match config api. It's right call")
                } else {
                    reportError(context, node)
                }
            }
        }
    }

    /**
     * 读取常量参数的值
     *
     * @param context
     * @param argument
     * @return
     */
    private fun getConstParamValue(
        context: JavaContext,
        argument: UExpression,
    ): Any? {
        // 只能读取常量值，不能读取数组等对象的值
        val value = ConstantEvaluator.evaluate(context, argument) ?: return null
        return when (value) {
            is Int -> value.toInt()
            is Long -> value.toLong()
            is Float -> value.toFloat()
            is Short -> value.toShort()
            is String -> value.toString()
            else -> value
        }
    }

    private fun reportError(context: JavaContext, node: UCallExpression) {
        context.report(
            ISSUE, node, context.getCallLocation(
                call = node,
                includeReceiver = false,
                includeArguments = false
            ),
            message = "配置ID注解标注的数据类型与您所使用的config api不匹配"
        )
    }

    object DataType {
        const val BOOL = 0
        const val INT = 1
        const val LONG = 2
        const val FLOAT = 3
        const val STRING = 4
        const val OBJECT = 5
    }

    companion object {

        private val IMPLEMENTATION: Implementation =
            Implementation(ConfigApiCallDetector::class.java, Scope.JAVA_FILE_SCOPE)

        /**
         * 定义、创建一个问题
         */
        internal val ISSUE: Issue = Issue.create(
            id = "ConfigApiCallError", // 唯一的 id，简要表达当前问题。
            briefDescription = "Config API 使用错误", // 唯一的 id，简要表达当前问题。
            explanation = "要写入与配置ID注解标注的数据类型匹配的数据类型", // 唯一的 id，简要表达当前问题。
            category = Category.CORRECTNESS, // 唯一的 id，简要表达当前问题。
            8, // 优先级，从 1 到 10，10 最重要。
            Severity.ERROR, // 严重程度，包括 FATAL、ERROR、WARNING、INFORMATIONAL和 IGNORE。
            IMPLEMENTATION // Issue 和哪个 Detector 绑定，以及声明检查的范围。
        )

        private val boolCfgMethods = listOf("getBooleanConfig", "getBoolean")
        private val floatCfgMethods = listOf("getFloatConfig", "getFloat")
        private val intCfgMethods = listOf("getIntConfig", "getInt")
        private val longCfgMethods = listOf("getLongConfig", "getLong")
        private val stringCfgMethods = listOf("getStringConfig", "getString")
        private val objCfgMethods = listOf("getObjectConfig", "getObject")

        private const val TAG = "ConfigApiCallDetector------"
    }
}