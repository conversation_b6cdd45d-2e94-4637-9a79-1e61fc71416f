/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MediaExtractorHelper.kt
 * Description:
 * Version: 1.0
 * Date: 2020/09/04
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D  2020/09/04           1.0           OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.standard_lib.codec.player

import android.content.Context
import android.media.MediaExtractor
import android.media.MediaFormat
import android.net.Uri
import android.os.ParcelFileDescriptor
import com.oplus.gallery.standard_lib.codec.SlowMotionVideoUtils
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.MIME_AUDIO
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.MIME_VIDEO
import com.oplus.gallery.foundation.util.debug.GLog

data class MediaDetails(
    val context: Context,
    val uri: Uri? = null,
    val fileDescriptor: ParcelFileDescriptor? = null,
    var formats: List<MediaFormat> = emptyList(),
    var slowMotionInfo: SlowMotionVideoUtils.SlowMotion? = null,

    var videoWidth: Int = 0,
    var videoHeight: Int = 0,
    var videoOrientation: Int = 0,

    var frameRate: MediaRational = MediaRational(0, 1),

    var audioChannel: Int = 0,
    var sampleRate: Int = 0,

    var duration: Long = 0,
    var videoType: Int = 0,
    var videoBitRate: Int = 0,
    var audioBitRate: Int = 0,
    var channel: Int = 0,

    var videoCodecType: String = "${PlayerAdapter.MIME_VIDEO}*",
    var audioCodecType: String = "${PlayerAdapter.MIME_AUDIO}*"
)

data class MediaRational(var numerator: Int, var denominator: Int) {

    constructor(decimal: Float, accuracy: Int) : this((decimal * accuracy).toInt(), accuracy)

    val decimal = numerator.toFloat() / denominator.toFloat()
}

class MediaDetailsExtractor(
    private val context: Context,
    private val uri: Uri
) {

    companion object {
        private const val TAG = "MediaExtractorHelper"
    }

    fun getVideoInfo(): MediaDetails {
        val source = MediaDetails(context, uri)

        val startTime = System.currentTimeMillis()
        var isVideoDone = false
        var isAudioDone = false

        val extractor = MediaExtractor()
        try {
            extractor.setDataSource(context, uri, null)
            val trackCount = extractor.trackCount
            for (i in 0 until trackCount) {
                val format = extractor.getTrackFormat(i)
                GLog.d(TAG) { "<getVideoInfo> format = $format" }
                val mimeType = format.getString(MediaFormat.KEY_MIME, "")
                if (mimeType.isEmpty()) {
                    continue
                }
                if (mimeType.startsWith(MIME_VIDEO)) {
                    if (isVideoDone) {
                        continue
                    }
                    getVideoInfo(format, source, mimeType)
                    isVideoDone = true
                } else if (mimeType.startsWith(MIME_AUDIO)) {
                    if (isAudioDone) {
                        continue
                    }
                    getAudioInfo(format, source, mimeType)
                    isAudioDone = true
                }
            }
        } catch (e: Exception) {
            GLog.e(TAG, "<determineVideoInfo> cannot determine completed info. ")
            isVideoDone = false
            isAudioDone = false
        } finally {
            extractor.release()
            GLog.i(
                TAG, """<determineVideoInfo> uri is ${source.uri}($source), determine ${isAudioDone && isVideoDone}
                     video size:         (${source.videoWidth},${source.videoHeight}),
                     frame rotation:      ${source.videoOrientation} °,
                     frames per second:   ${source.frameRate} fps,
                     video codec:         ${source.videoCodecType} 
                     video bit rate:      ${source.videoBitRate} bps
                     duration:            ${source.duration} ms
                     ----------------------------
                     audio channels:      $source,
                     audio sample rate:   ${source.sampleRate} Hz,
                     audio codec:         ${source.audioCodecType}
                     audio bit rate:      ${source.audioBitRate} bps
                     ----------------------------
                     ----------------------------
                     spend time is ${System.currentTimeMillis() - startTime} ms"""
            )
        }
        return source
    }

    private fun getVideoInfo(
        format: MediaFormat,
        source: MediaDetails,
        mimeType: String
    ) {
        val width = format.getInteger(MediaFormat.KEY_WIDTH, 0)
        val height = format.getInteger(MediaFormat.KEY_HEIGHT, 0)
        val orientation = format.getInteger(MediaFormat.KEY_ROTATION, 0)
        val fps = format.getInteger(MediaFormat.KEY_FRAME_RATE, 0)
        val bitRate = format.getInteger(MediaFormat.KEY_BIT_RATE, 0)
        val duration = format.getLong(MediaFormat.KEY_DURATION, 0) / 1000L

        GLog.i(
            TAG, "<determineVideoInfo><Video> resolution: ($width, $height), orientation: $orientation°, " +
                    "fps: $fps fps, bitrate: $bitRate bps, codec: $mimeType"
        )

        source.run {
            videoCodecType = mimeType
            videoHeight = height
            videoWidth = width
            videoBitRate = bitRate
            videoOrientation = orientation
            this.duration = duration
        }
    }

    private fun getAudioInfo(
        format: MediaFormat,
        source: MediaDetails,
        mimeType: String
    ) {
        val bitRate = format.getInteger(MediaFormat.KEY_BIT_RATE, 0)
        val channel = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT, 0)
        GLog.i(TAG, "<determineVideoInfo><Audio> channel: $channel, bitrate: $bitRate bps, codec: $mimeType")

        source.run {
            audioCodecType = mimeType
            this.channel = channel
            audioBitRate = bitRate
        }
    }
}