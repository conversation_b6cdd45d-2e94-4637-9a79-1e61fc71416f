/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoCoordinateLayoutInfoManager.kt
 ** Description : 大图与缩略图轴联动时的策略管理类
 ** Version     : 1.0
 ** Date        : 2023/03/02
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/03/02  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.thumblinesection

import android.content.Context
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils.isLand
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType.Preview
import com.oplus.gallery.photo_page.ui.section.thumblinesection.PhotoCoordinateLayoutInfoManager.PresentationType.Thumbnail
import com.oplus.gallery.photo_page.ui.section.thumblinesection.ThumbLineLayoutManager.ItemLayoutInfo
import com.oplus.gallery.photo_page.ui.section.thumblinesection.playback.PlaybackThumbLineSizeSpecCalculator
import com.oplus.gallery.photo_page.viewmodel.dataloading.PhotoItemViewData

/**
 * 大图与缩略图轴联动时的布局信息管理类
 *
 * 用于获取当前缩略图的布局属性,具体逻辑由 [PhotoCoordinateStrategy] 实现
 */
internal class PhotoCoordinateLayoutInfoManager(
    context: Context,
    private val playbackSizeSpecCalculator: PlaybackThumbLineSizeSpecCalculator
) {

    /**
     * 大图与缩略图轴联动时的策略,用于获取中高端机型图片 Item 当前缩略图的布局属性
     */
    private val photoImageCoordinateStrategy: PhotoImageCoordinateStrategy by lazy { PhotoImageCoordinateStrategy(context) }

    /**
     * 大图与缩略图轴联动时的策略,用于获取中高端机型视频 Item 当前缩略图的布局属性
     */
    private val photoVideoCoordinateStrategy: PhotoVideoCoordinateStrategy by lazy {
        PhotoVideoCoordinateStrategy(context, playbackSizeSpecCalculator)
    }

    /**
     * 大图与缩略图轴联动时的策略,用于获取低端机型图片 Item 当前缩略图的布局属性
     */
    private val photoImageLiteCoordinateStrategy: PhotoImageLiteCoordinateStrategy by lazy { PhotoImageLiteCoordinateStrategy(context) }

    /**
     * 大图与缩略图轴联动时的策略,用于获取低端机型视频 Item 当前缩略图的布局属性
     */
    private val photoVideoLiteCoordinateStrategy: PhotoVideoLiteCoordinateStrategy by lazy { PhotoVideoLiteCoordinateStrategy(context) }

    /**
     * 大图与缩略图轴联动时的策略,用于默认 Item 当前缩略图的布局属性
     */
    private val dummyCoordinateStrategy: DummyCoordinateStrategy by lazy { DummyCoordinateStrategy(context) }

    /**
     * 获取默认状态下该 [type] 类型的 [ThumbLineLayoutManager.ItemLayoutInfo]
     *
     * @param shouldMaxSize 是否默认获取最大值，影响 [ThumbLineLayoutManager.ItemLayoutInfo.width]，默认值为 false，即默认获取最小值
     */
    fun getLayoutInfo(
        type: ContentType,
        viewData: PhotoItemViewData? = null,
        presentationType: PresentationType = Thumbnail,
        shouldMaxSize: Boolean = false
    ): ItemLayoutInfo {
        return getStrategy(type).getLayoutInfo(viewData, presentationType, shouldMaxSize)
    }

    /**
     * 根据 [state] 获取当前的 [ThumbLineLayoutManager.ItemLayoutInfo]
     */
    fun getLayoutInfo(state: PhotoItemCoordinateState): ThumbLineLayoutManager.ItemLayoutInfo {
        val strategy = getStrategy(state.type)
        return ItemLayoutInfo.obtain().apply {
            width = strategy.getWidth(state)
            height = strategy.getHeight(state)
            topMargin = strategy.getTopMargin(state)
            bottomMargin = strategy.getBottomMargin(state)
            startMargin = strategy.getStartMargin(state)
            endMargin = strategy.getEndMargin(state)
        }
    }

    /**
     * 获取当前中心 Item 的左右间距
     */
    fun getCenterHorizontalGap(type: ContentType): Float {
        return getStrategy(type).getCenterHorizontalGap()
    }

    /**
     * 根据 [type] 获取对应的策略
     *
     * 中低端机型适配时,需要在此处增加判断条件
     */
    private fun getStrategy(type: ContentType): PhotoCoordinateStrategy {
        return when (type) {
            ContentType.Image -> photoImageCoordinateStrategy
            ContentType.Video -> photoVideoCoordinateStrategy
            else -> dummyCoordinateStrategy
        }
    }

    /**
     * 设置是否使用大尺寸
     */
    fun setLargeSize(isLargeSize: Boolean) {
        photoImageCoordinateStrategy.isLargeSize = isLargeSize
        photoVideoCoordinateStrategy.isLargeSize = isLargeSize
        playbackSizeSpecCalculator.isLargeSize = isLargeSize
    }

    /**
     * 获取信息的 bean
     */
    data class PhotoItemCoordinateState(

        /**
         * 该状态下对应的 [PhotoItemViewData] 数据
         *
         * @see PhotoItemViewData
         */
        val viewData: PhotoItemViewData,

        /**
         * 当前状态的展示类型
         *
         * @see PresentationType
         */
        val presentationType: PresentationType,

        /**
         * [com.oplus.gallery.photopager.PhotoPager] 中,此 Item 显示的比例
         */
        val percent: Float,

        /**
         * 内容类型
         */
        val type: ContentType,

        /**
         * 内容源的宽度
         */
        val widthSource: Int,

        /**
         * 内容源的高度
         */
        val heightSource: Int,

        /**
         * 缩略图轴的中线,在 View 的哪边
         */
        val placement: Placement
    )

    /**
     * 缩图轴 item 的展示形态
     */
    enum class PresentationType {
        /**
         * 缩图态：只有一个小缩图
         */
        Thumbnail,

        /**
         * 预览态：
         * - 图片：相对于 [Thumbnail] 而言，稍微放大一点，样式不同
         * - 视频：一个 Seekbar，并显示两帧预览
         */
        Preview,

        /**
         * 详情态：
         * - 图片：无该状态
         * - 视频：一个 Seekbar，由 [PlaybackThumbLineSizeSpecCalculator] 计算出预览帧数，可以进行滑动 seek 操作
         */
        Detail;


        /**
         * 是否为 [Thumbnail] 状态
         */
        val isThumbnail: Boolean get() = this == Thumbnail

        /**
         * 是否为 [Preview] 状态
         */
        val isPreview: Boolean get() = this == Preview

        /**
         * 是否为 [Detail] 状态
         */
        val isDetail: Boolean get() = this == Detail
    }

    /**
     * 内容的类型
     */
    enum class ContentType {
        /**
         * 图片
         */
        Image,

        /**
         * 视频
         */
        Video,

        /**
         * 未知
         */
        Unknown
    }

    /**
     * 缩略图轴的中线,在 View 的哪边
     */
    enum class Placement {

        /**
         * 缩略图轴的中线,在 View 的中间
         */
        Center,

        /**
         * 缩略图轴的中线,在 View 的左边
         */
        Start,

        /**
         * 缩略图轴的中线,在 View 的右边
         */
        End;

        fun isCenter(): Boolean {
            return this == Center
        }

        fun isStart(): Boolean {
            return this == Start
        }

        fun isEnd(): Boolean {
            return this == End
        }
    }
}

/**
 * 大图与缩略图轴联动时的策略,用于获取当前缩略图的布局属性
 * 共有5个实现类:
 * - [PhotoImageCoordinateStrategy] 中高端机型图片 Item
 * - [PhotoVideoCoordinateStrategy] 中高端机型视频 Item
 * - [PhotoImageLiteCoordinateStrategy] 低端机型图片 Item
 * - [PhotoVideoLiteCoordinateStrategy] 低端机型视频 Item
 * - [DummyCoordinateStrategy] 默认实现 Item
 */
internal interface PhotoCoordinateStrategy {

    /**
     * 宽度
     */
    fun getWidth(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float

    /**
     * 高度
     */
    fun getHeight(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float

    /**
     * 与顶端的距离
     */
    fun getTopMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float

    /**
     * 与底部的距离
     */
    fun getBottomMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float

    /**
     * 与 Start 方向的距离
     */
    fun getStartMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float

    /**
     * 与 End 方向的距离
     */
    fun getEndMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float

    /**
     * 默认的 LayoutInfo
     *
     * @param shouldMaxSize 是否默认获取最大值，影响 [ThumbLineLayoutManager.ItemLayoutInfo.width]，默认值为 false，即默认获取最小值
     */
    fun getLayoutInfo(
        viewData: PhotoItemViewData? = null,
        presentationType: PresentationType = Thumbnail,
        shouldMaxSize: Boolean = false
    ): ItemLayoutInfo

    /**
     * 中心(放大) Item 的横向间距
     */
    fun getCenterHorizontalGap(): Float
}

/**
 * 大图与缩略图轴联动时的策略,用于获取中高端机型图片 Item 当前缩略图的布局属性
 */
internal class PhotoImageCoordinateStrategy(cxt: Context) : PhotoCoordinateStrategy {

    private val context = cxt.applicationContext

    private val largeWidthNormal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_item_width_normal)
    }

    private val smallWidthNormal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_width_normal)
    }

    private val largeHeightNormal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_item_height_normal)
    }

    private val smallHeightNormal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_height_normal)
    }

    private val largeWidthCenter by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_item_width_center)
    }

    private val smallWidthCenter by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_width_center)
    }

    private val largeHeightCenter by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_item_height_center)
    }

    private val smallHeightCenter by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_height_center)
    }

    private val largeGapCenterHorizontal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_gap_center_horizontal_image)
    }

    private val smallGapCenterHorizontal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_horizontal_image)
    }

    private val largeGapCenterVertical by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_gap_center_vertical)
    }

    private val smallGapCenterTop by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_top)
    }

    private val smallGapCenterBottom by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_bottom)
    }

    private val smallGapCenterBottomLandscape by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_bottom_landscape)
    }

    private val gapNormalHorizontal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_horizontal)
    }

    private val largeGapNormalVertical by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_gap_normal_vertical)
    }

    private val smallGapNormalTop by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_top)
    }

    private val smallGapNormalBottom by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom)
    }

    private val smallGapNormalBottomLandscape by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom_landscape)
    }

    private val isLandscape get() = context.isLand()

    var isLargeSize = ScreenUtils.isMiddleAndLargeScreen(context)

    /**
     * 普通 Item 的宽
     */
    private fun widthNormal() = if (isLargeSize) {
        largeWidthNormal
    } else {
        smallWidthNormal
    }

    /**
     * 普通 Item 的高
     */
    private fun heightNormal() = if (isLargeSize) {
        largeHeightNormal
    } else {
        smallHeightNormal
    }

    /**
     * 中心 Item 的宽(完全展开时)
     */
    private fun widthCenter() = if (isLargeSize) {
        largeWidthCenter
    } else {
        smallWidthCenter
    }

    /**
     * 中心 Item 的高(完全展开时)
     */
    private fun heightCenter() = if (isLargeSize) {
        largeHeightCenter
    } else {
        smallHeightCenter
    }

    /**
     * 中心 Item 的间距(水平方向)(完全展开时)
     */
    private fun gapCenterHorizontal() = if (isLargeSize) {
        largeGapCenterHorizontal
    } else {
        smallGapCenterHorizontal
    }

    /**
     * 中心 Item 的间距(顶部)(完全展开时)
     */
    private fun gapCenterTop() = if (isLargeSize) {
        largeGapCenterVertical
    } else {
        smallGapCenterTop
    }

    /**
     * 中心 Item 的间距(底部)
     */
    private fun gapCenterBottom() = if (isLargeSize) {
        largeGapCenterVertical
    } else if (isLandscape) {
        smallGapCenterBottomLandscape
    } else {
        smallGapCenterBottom
    }

    /**
     * 普通 Item 的间距(水平方向)
     */
    private fun gapNormalHorizontal() = gapNormalHorizontal

    /**
     * 普通 Item 的间距(顶部)
     */
    private fun gapNormalTop() = if (isLargeSize) {
        largeGapNormalVertical
    } else {
        smallGapNormalTop
    }

    /**
     * 普通 Item 的间距(底部)
     */
    private fun gapNormalBottom() = if (isLargeSize) {
        largeGapNormalVertical
    } else if (isLandscape) {
        smallGapNormalBottomLandscape
    } else {
        smallGapNormalBottom
    }

    override fun getWidth(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> widthNormal()
            else -> (widthNormal() + (widthCenter() - widthNormal()) * state.percent)
        }
    }

    override fun getHeight(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> heightNormal()
            else -> (heightNormal() + (heightCenter() - heightNormal()) * state.percent)
        }
    }

    override fun getTopMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> gapNormalTop()
            else -> (gapNormalTop() + (gapCenterTop() - gapNormalTop()) * state.percent)
        }
    }

    override fun getBottomMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> gapNormalBottom()
            else -> (gapNormalBottom() + (gapCenterBottom() - gapNormalBottom()) * state.percent)
        }
    }

    override fun getStartMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> (gapNormalHorizontal() / 2)
            state.placement.isStart() -> (gapCenterHorizontal() * state.percent)
            state.placement.isEnd() -> {
                val end = gapCenterHorizontal() - gapNormalHorizontal() / 2
                val start = gapNormalHorizontal() / 2
                start + (end - start) * state.percent
            }
            else -> (gapCenterHorizontal() - gapNormalHorizontal() / 2)
        }
    }

    override fun getEndMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> (gapNormalHorizontal() / 2)
            state.placement.isEnd() -> (gapCenterHorizontal() * state.percent)
            state.placement.isStart() -> {
                val end = gapCenterHorizontal() - gapNormalHorizontal() / 2
                val start = gapNormalHorizontal() / 2
                start + (end - start) * state.percent
            }
            else -> (gapCenterHorizontal() - gapNormalHorizontal() / 2)
        }
    }

    override fun getLayoutInfo(viewData: PhotoItemViewData?, presentationType: PresentationType, shouldMaxSize: Boolean): ItemLayoutInfo {
        if (presentationType.isPreview) {
            return ItemLayoutInfo.obtain().apply {
                width = widthCenter()
                height = heightCenter()
                topMargin = gapNormalTop()
                bottomMargin = gapNormalBottom()
                startMargin = (gapCenterHorizontal() - gapNormalHorizontal() / 2)
                endMargin = (gapCenterHorizontal() - gapNormalHorizontal() / 2)
            }
        } else {
            return ItemLayoutInfo.obtain().apply {
                width = widthNormal()
                height = heightNormal()
                topMargin = gapNormalTop()
                bottomMargin = gapNormalBottom()
                startMargin = gapNormalHorizontal() / 2
                endMargin = gapNormalHorizontal() / 2
            }
        }
    }

    override fun getCenterHorizontalGap(): Float {
        return gapCenterHorizontal()
    }
}

/**
 * 大图与缩略图轴联动时的策略,用于获取中高端机型视频 Item 当前缩略图的布局属性
 */
internal class PhotoVideoCoordinateStrategy(
    cxt: Context,
    private val playbackSizeSpecCalculator: PlaybackThumbLineSizeSpecCalculator
) : PhotoCoordinateStrategy {

    private val context = cxt.applicationContext

    private val largeGapCenterHorizontal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_gap_center_horizontal_video)
    }

    private val smallGapCenterHorizontal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_horizontal_video)
    }

    private val largeGapCenterVertical by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_gap_center_vertical)
    }

    private val smallGapCenterTop by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_top)
    }

    private val smallGapCenterBottom by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_bottom)
    }

    private val smallGapCenterBottomLandscape by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_center_bottom_landscape)
    }

    private val largeDetailGapCenterVertical by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_item_playback_detail_gap_center_vertical)
    }

    private val smallDetailGapCenterTop by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_playback_detail_gap_center_top)
    }

    private val smallDetailGapCenterBottom by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_playback_detail_gap_center_bottom)
    }

    private val smallDetailGapCenterBottomLandscape by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_item_playback_detail_gap_center_bottom_landscape)
    }

    private val gapNormalHorizontal by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_horizontal)
    }

    private val largeGapNormalVertical by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_ml_screen_gap_normal_vertical)
    }

    private val smallGapNormalTop by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_top)
    }

    private val smallGapNormalBottom by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom)
    }

    private val smallGapNormalBottomLandscape by lazy {
        context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom_landscape)
    }

    private val isLandscape get() = context.isLand()

    var isLargeSize = ScreenUtils.isMiddleAndLargeScreen(context)

    /**
     * 中心 Item 的间距(水平方向)(完全展开时)
     */
    private fun gapCenterHorizontal() = if (isLargeSize) {
        largeGapCenterHorizontal
    } else {
        smallGapCenterHorizontal
    }

    /**
     * 中心 Item 的间距(顶部)([PresentationType.Thumbnail] 和 [PresentationType.Preview] 状态时)
     */
    private fun gapCenterTop() = if (isLargeSize) {
        largeGapCenterVertical
    } else {
        smallGapCenterTop
    }

    /**
     * 中心 Item 的间距(底部)([PresentationType.Thumbnail] 和 [PresentationType.Preview] 状态时)
     */
    private fun gapCenterBottom() = if (isLargeSize) {
        largeGapCenterVertical
    } else if (isLandscape) {
        smallGapCenterBottomLandscape
    } else {
        smallGapCenterBottom
    }

    /**
     * 中心 Item 的间距(顶部)([PresentationType.Detail] 状态时)
     */
    private fun detailGapCenterTop() = if (isLargeSize) {
        largeDetailGapCenterVertical
    } else {
        smallDetailGapCenterTop
    }

    /**
     * 中心 Item 的间距(底部)([PresentationType.Detail] 状态时)
     */
    private fun detailGapCenterBottom() = if (isLargeSize) {
        largeDetailGapCenterVertical
    } else if (isLandscape) {
        smallDetailGapCenterBottomLandscape
    } else {
        smallDetailGapCenterBottom
    }

    /**
     * 普通 Item 的间距(水平方向)
     */
    private fun gapNormalHorizontal() = gapNormalHorizontal

    /**
     * 普通 Item 的间距(顶部)
     */
    private fun gapNormalTop() = if (isLargeSize) {
        largeGapNormalVertical
    } else {
        smallGapNormalTop
    }

    /**
     * 普通 Item 的间距(底部)
     */
    private fun gapNormalBottom() = if (isLargeSize) {
        largeGapNormalVertical
    } else if (isLandscape) {
        smallGapNormalBottomLandscape
    } else {
        smallGapNormalBottom
    }

    override fun getWidth(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isDetail -> playbackSizeSpecCalculator.getWidth(state.viewData, state.presentationType)
            state.presentationType.isThumbnail -> playbackSizeSpecCalculator.getWidth(state.viewData, state.presentationType)
            else -> {
                val thumbWidth = playbackSizeSpecCalculator.getWidth(state.viewData, Thumbnail)
                val previewWidth = playbackSizeSpecCalculator.getWidth(state.viewData, Preview)
                (thumbWidth + (previewWidth - thumbWidth) * state.percent)
            }
        }
    }

    override fun getHeight(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail || state.presentationType.isDetail -> {
                playbackSizeSpecCalculator.getHeight(state.viewData, state.presentationType)
            }
            else -> {
                val thumbHeight = playbackSizeSpecCalculator.getHeight(state.viewData, Thumbnail)
                val previewHeight = playbackSizeSpecCalculator.getHeight(state.viewData, Preview)
                (thumbHeight + (previewHeight - thumbHeight) * state.percent)
            }
        }
    }

    override fun getTopMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> gapNormalTop()
            state.presentationType.isDetail -> detailGapCenterTop()
            else -> (gapNormalTop() + (gapCenterTop() - gapNormalTop()) * state.percent)
        }
    }

    override fun getBottomMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> gapNormalBottom()
            state.presentationType.isDetail -> detailGapCenterBottom()
            else -> (gapNormalBottom() + (gapCenterBottom() - gapNormalBottom()) * state.percent)
        }
    }

    override fun getStartMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> (gapNormalHorizontal() / 2)
            state.placement.isStart() -> (gapCenterHorizontal() * state.percent)
            state.placement.isEnd() -> {
                val end = gapCenterHorizontal() - gapNormalHorizontal() / 2
                val start = gapNormalHorizontal() / 2
                start + (end - start) * state.percent
            }
            else -> (gapCenterHorizontal() - gapNormalHorizontal() / 2)
        }
    }

    override fun getEndMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return when {
            state.presentationType.isThumbnail -> (gapNormalHorizontal() / 2)
            state.placement.isEnd() -> (gapCenterHorizontal() * state.percent)
            state.placement.isStart() -> {
                val end = gapCenterHorizontal() - gapNormalHorizontal() / 2
                val start = gapNormalHorizontal() / 2
                start + (end - start) * state.percent
            }
            else -> (gapCenterHorizontal() - gapNormalHorizontal() / 2)
        }
    }

    override fun getLayoutInfo(viewData: PhotoItemViewData?, presentationType: PresentationType, shouldMaxSize: Boolean): ItemLayoutInfo {
        if (presentationType.isPreview) {
            return ItemLayoutInfo.obtain().apply {
                width = playbackSizeSpecCalculator.getWidth(viewData, presentationType, shouldMaxSize)
                height = playbackSizeSpecCalculator.getHeight(viewData, presentationType, shouldMaxSize)
                topMargin = gapNormalTop()
                bottomMargin = gapNormalBottom()
                startMargin = (gapCenterHorizontal() - gapNormalHorizontal() / 2)
                endMargin = (gapCenterHorizontal() - gapNormalHorizontal() / 2)
            }
        } else if (presentationType.isDetail) {
            return ItemLayoutInfo.obtain().apply {
                width = playbackSizeSpecCalculator.getWidth(viewData, presentationType, shouldMaxSize)
                height = playbackSizeSpecCalculator.getHeight(viewData, presentationType, shouldMaxSize)
                topMargin = detailGapCenterTop()
                bottomMargin = detailGapCenterBottom()
                startMargin = (gapCenterHorizontal() - gapNormalHorizontal() / 2)
                endMargin = (gapCenterHorizontal() - gapNormalHorizontal() / 2)
            }
        } else {
            // 默认presentationType.isThumbnail
            return ItemLayoutInfo.obtain().apply {
                width = playbackSizeSpecCalculator.getWidth(viewData, presentationType, shouldMaxSize)
                height = playbackSizeSpecCalculator.getHeight(viewData, presentationType, shouldMaxSize)
                topMargin = gapNormalTop()
                bottomMargin = gapNormalBottom()
                startMargin = gapNormalHorizontal() / 2
                endMargin = gapNormalHorizontal() / 2
            }
        }
    }

    override fun getCenterHorizontalGap(): Float {
        return gapCenterHorizontal()
    }
}

/**
 * 大图与缩略图轴联动时的策略,用于获取低端机型图片 Item 当前缩略图的布局属性
 */
internal class PhotoImageLiteCoordinateStrategy(context: Context) : PhotoCoordinateStrategy {

    /**
     * 普通 Item 的宽
     */
    private val widthNormal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_item_width_normal) }

    /**
     * 普通 Item 的高
     */
    private val heightNormal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_item_height_normal) }

    /**
     * 普通 Item 的间距(水平方向)
     */
    private val gapNormalHorizontal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_horizontal) }

    /**
     * 普通 Item 的间距(顶部)
     */
    private val gapNormalTop: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_top) }

    /**
     * 普通 Item 的间距(底部)
     */
    private val gapNormalBottom: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom) }

    override fun getWidth(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return widthNormal
    }

    override fun getHeight(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return heightNormal
    }

    override fun getTopMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalTop
    }

    override fun getBottomMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalBottom
    }

    override fun getStartMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalHorizontal / 2
    }

    override fun getEndMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalHorizontal / 2
    }

    override fun getLayoutInfo(viewData: PhotoItemViewData?, presentationType: PresentationType, shouldMaxSize: Boolean): ItemLayoutInfo {
        return ItemLayoutInfo.obtain().apply {
            width = widthNormal
            height = heightNormal
            topMargin = gapNormalTop
            bottomMargin = gapNormalBottom
            startMargin = gapNormalHorizontal / 2
            endMargin = gapNormalHorizontal / 2
        }
    }

    override fun getCenterHorizontalGap(): Float {
        return gapNormalHorizontal
    }
}

/**
 * 大图与缩略图轴联动时的策略,用于获取低端机型视频 Item 当前缩略图的布局属性
 */
internal class PhotoVideoLiteCoordinateStrategy(context: Context) : PhotoCoordinateStrategy {

    /**
     * 普通 Item 的宽
     */
    private val widthNormal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_item_width_normal) }

    /**
     * 普通 Item 的高
     */
    private val heightNormal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_item_height_normal) }

    /**
     * 普通 Item 的间距(水平方向)
     */
    private val gapNormalHorizontal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_horizontal) }

    /**
     * 普通 Item 的间距(顶部)
     */
    private val gapNormalTop: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_top) }

    /**
     * 普通 Item 的间距(底部)
     */
    private val gapNormalBottom: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom) }

    override fun getWidth(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return widthNormal
    }

    override fun getHeight(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return heightNormal
    }

    override fun getTopMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalTop
    }

    override fun getBottomMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalBottom
    }

    override fun getStartMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalHorizontal / 2
    }

    override fun getEndMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalHorizontal / 2
    }

    override fun getLayoutInfo(viewData: PhotoItemViewData?, presentationType: PresentationType, shouldMaxSize: Boolean): ItemLayoutInfo {
        return ItemLayoutInfo.obtain().apply {
            width = widthNormal
            height = heightNormal
            topMargin = gapNormalTop
            bottomMargin = gapNormalBottom
            startMargin = gapNormalHorizontal / 2
            endMargin = gapNormalHorizontal / 2
        }
    }

    override fun getCenterHorizontalGap(): Float {
        return gapNormalHorizontal
    }
}

/**
 * 大图与缩略图轴联动时的策略,用于默认 Item 当前缩略图的布局属性
 */
internal class DummyCoordinateStrategy(context: Context) : PhotoCoordinateStrategy {

    /**
     * 普通 Item 的宽
     */
    private val widthNormal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_item_width_normal) }

    /**
     * 普通 Item 的高
     */
    private val heightNormal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_item_height_normal) }

    /**
     * 普通 Item 的间距(水平方向)
     */
    private val gapNormalHorizontal: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_horizontal) }

    /**
     * 普通 Item 的间距(顶部)
     */
    private val gapNormalTop: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_top) }

    /**
     * 普通 Item 的间距(底部)
     */
    private val gapNormalBottom: Float by lazy { context.resources.getDimension(R.dimen.photopage_thumbline_gap_normal_bottom) }

    override fun getWidth(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return widthNormal
    }

    override fun getHeight(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return heightNormal
    }

    override fun getTopMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalTop
    }

    override fun getBottomMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalBottom
    }

    override fun getStartMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalHorizontal / 2
    }

    override fun getEndMargin(
        state: PhotoCoordinateLayoutInfoManager.PhotoItemCoordinateState
    ): Float {
        return gapNormalHorizontal / 2
    }

    override fun getLayoutInfo(viewData: PhotoItemViewData?, presentationType: PresentationType, shouldMaxSize: Boolean): ItemLayoutInfo {
        return ItemLayoutInfo.obtain().apply {
            width = widthNormal
            height = heightNormal
            topMargin = gapNormalTop
            bottomMargin = gapNormalBottom
            startMargin = gapNormalHorizontal / 2
            endMargin = gapNormalHorizontal / 2
        }
    }

    override fun getCenterHorizontalGap(): Float {
        return gapNormalHorizontal
    }
}

