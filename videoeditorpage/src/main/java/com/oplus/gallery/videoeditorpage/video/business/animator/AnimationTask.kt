/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  AnimationTask.kt
 ** Description: 封装动画的任务
 ** Version: 1.0
 ** Date : 2025/6/30
 ** Author: 80413407
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>      <version>       <desc>
 **  80413407      2025/6/30     1.0     NEW
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.animator

import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation

/**
 * 封装动画的任务
 */
class AnimationTask(
    private val animation: COUISpringAnimation,
    private val runnable: Runnable,
    private val cancelFunction: () -> Unit
) {
    /**
     * 启动动画
     */
    fun start() {
        if (animation.isRunning.not()) {
            animation.start()
        }
    }

    /**
     * 取消动画
     */
    fun cancel() {
        if (animation.isRunning) {
            animation.cancel()
        } else {
            cancelFunction.invoke()
        }
    }

    /**
     * 获取Runnable实例
     */
    fun getRunnable(): Runnable {
        return runnable
    }
}