/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryThemeHelper.java
 * * Description: IGalleryThemeHelper interface.
 * * Version: 1.0
 * * Date : 2017/12/29
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/29    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

import com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;

import java.util.ArrayList;

public interface IGalleryThemeHelper {
    /*---------------- theme helper start ----------------*/
    boolean initThemeVideoClips(ArrayList<MediaInfo> mediaInfos);

    boolean insertThemeVideoClip(ArrayList<MediaInfo> infoList);

    boolean insertThemeVideoClip(MediaInfo info);

    boolean deleteThemeVideoClip(MediaInfo info);

    ArrayList<String> getThemeVideoClipList();

    int getThemeVideoClipCount();

    boolean addThemeVideoCover(MediaInfo info);

    boolean removeThemeVideoCover();

    MediaInfo getThemeVideoCover();

    void setThemeMusicMute(boolean isMute);

    void addThemeMusic(String musicId);

    void addThemeTrimMusic(String musicId, long startTime, long endTime);

    void removeThemeMusic(boolean force);

    String getCurrentThemeMusic();

    int getThemeCurrentMusicPos();

    boolean addTheme(ThemeInfo theme);

    void removeCurrentTheme();

    String getCurrentTheme();

    int getThemeCurrentThemePos();

    boolean addThemeCaption(String title, String hint);

    boolean updateThemeCaptionTitle(String title);

    void removeThemeCaption();

    void reAddThemeCaption();

    String getThemeTitleText();

    String getThemeHintText();

    boolean changeThemeDuration(long time);

    long getThemeMinTotalTime();

    long getThemeMaxTotalTime();

    void seekToThemePosition(int index);

    void cleanBuiltinTransition();
    /*---------------- theme helper end ----------------*/
}
