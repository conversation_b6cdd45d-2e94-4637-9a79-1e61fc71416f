/***********************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - GalleryVideoTrimView.java
 * Description: for video thumbnail view.
 * Version: 1.0
 * Date : 2017/11/14
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
 * <EMAIL>    2017/11/14    1.0    build this module
</desc></version></data></author> */
package com.oplus.gallery.videoeditorpage.video.business.wallpaper

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import com.oplus.gallery.videoeditorpage.video.business.wallpaper.IGalleryThumbController.ThumbScrollerListener
import com.oplus.gallery.videoeditorpage.widget.GalleryHorizontalScrollView.SCROLL_STATE_IDLE

/**
 * 剪辑控件容器类 使用静态代理模式代理缩图轴对外提供能力 目前只有壁纸业务使用
 */
class GalleryVideoThumbnailView : LinearLayout, IGalleryThumbController {

    /**
     * 真正提供能力的controller
     */
    private var thumbController: IGalleryThumbController? = null

    /**
     * @return 每微秒对应的像素数
     */
    override val pixelPerMicrosecond: Double
        get() = thumbController?.pixelPerMicrosecond ?: INVALID_NUM.toDouble()

    /**
     * 缩图轴的滚动状态
     */
    override val scrollState: Int
        get() = thumbController?.scrollState ?: SCROLL_STATE_IDLE

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attributeSet: AttributeSet?) : super(context, attributeSet)

    /**
     * 设置缩图轴的controller
     */
    fun setThumbController(controller: IGalleryThumbController) {
        thumbController = controller
    }

    override fun mapTimelinePosFromX(x: Int): Long {
        return thumbController?.mapTimelinePosFromX(x) ?: INVALID_NUM.toLong()
    }

    override fun mapXFromTimelinePos(time: Long): Int {
        return thumbController?.mapXFromTimelinePos(time) ?: INVALID_NUM
    }

    override fun setScrollListener(scrollerListener: ThumbScrollerListener) {
        thumbController?.setScrollListener(scrollerListener)
    }

    override fun setTouchListener(touchListener: OnTouchListener) {
        thumbController?.setTouchListener(touchListener)
    }

    override fun smoothScrollTo(x: Int, y: Int) {
        thumbController?.smoothScrollTo(x, y)
    }

    override fun scrollTo(x: Int, y: Int) {
        thumbController?.scrollTo(x, y)
    }

    override fun fullScroll(pos: Int) {
        thumbController?.fullScroll(pos)
    }

    override fun stopScroll() {
        thumbController?.stopScroll()
    }

    companion object {
        const val INVALID_NUM = -1
    }
}
