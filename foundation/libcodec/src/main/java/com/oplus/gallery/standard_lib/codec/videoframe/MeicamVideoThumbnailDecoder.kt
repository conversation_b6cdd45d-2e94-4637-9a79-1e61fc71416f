/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoThumbnailDecoder.kt
 ** Description:美摄的缩图解码器
 ** Version: 1.0
 ** Date : 2022/11/9
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG: MeicamVideoThumbnailDecoder
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewujie@Apps.Gallery3D      2022/11/09    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.standard_lib.codec.videoframe

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.meicam.sdk.NvsStreamingContext.VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED
import com.meicam.sdk.NvsStreamingContext.VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED_TRANSFER
import com.meicam.sdk.NvsStreamingContext.VIDEO_FRAME_RETRIEVER_FLAG_TONE_MAPPING_LINEAR_USED
import com.meicam.sdk.NvsStreamingContext.VIDEO_FRAME_RETRIEVER_FLAG_MEDIA_CODEC_FORCE_YUV10BIT_FORMAT
import com.meicam.sdk.NvsVideoFrameRetriever
import com.meicam.sdk.NvsVideoStreamInfo
import com.oplus.breakpad.BreakpadUtil.generateKey
import com.oplus.breakpad.runNativeGuarding
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_DOLBY_DECODER_LIMIT
import com.oplus.gallery.foundation.util.thread.ThreadUtils
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.io.FileDescriptor
import java.util.concurrent.LinkedBlockingQueue
import kotlin.math.min
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.PerformanceLevel
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.cpuLevel
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.MeicamEdit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 美摄的缩图解码器
 */
class MeicamVideoThumbnailDecoder(
    private val decoderType: DecoderType = DecoderType.SOFT
) : VideoThumbnailDecoder() {

    private var nvsRetriever: NvsVideoFrameRetriever? = null
    private var thumbnailHeight = INVALID_SIZE
    private var filePath: String? = null

    override fun setDataSource(path: String) {
        filePath = path
        createNvsRetriever(path)
    }

    override fun setDataSource(fd: FileDescriptor) {
        GLog.e(TAG, "setDataSource(fd: FileDescriptor) error, meicam engine done not support FileDescriptor, replace with uri")
    }

    override fun setDataSource(context: Context, uri: Uri) {
        filePath = uri.toString()
        createNvsRetriever(uri.toString())
    }

    override fun setDataSource(fd: FileDescriptor, offset: Long, length: Long) = Unit

    override fun setThumbnailSize(size: Int) {
        thumbnailHeight = calculateThumbnailHeight(size)
    }

    override fun decodeFrameBitmapAtTime(timeUs: Long, size: Int): Bitmap? {
        val inputHeight = if ((size == INVALID_SIZE) && (thumbnailHeight == INVALID_SIZE)) {
            nvsRetriever?.nvVideoStreamInfo?.let {
                clipVideoHeight(getVideoHeight(it))
            } ?: INVALID_SIZE
        } else if (size > 0) {
            calculateThumbnailHeight(size)
        } else {
            thumbnailHeight
        }
        if (TextUtils.isEmpty(filePath)) {
            GLog.w(TAG, "[decodeFrameBitmapAtTime] error, because path is null or empty")
            return null
        }
        if (inputHeight <= 0) {
            GLog.e(TAG, "[decodeFrameBitmapAtTime] error, because inputHeight is less than 0, inputHeight:$inputHeight")
            return null
        }
        val result = runNativeGuarding("decodeFrameBitmapAtTime", generateKey(filePath, 0L)) {
            nvsRetriever?.getFrameAtTimeWithCustomVideoFrameHeight(timeUs, inputHeight)
        }.onNativeFailure() {
            GLog.w(TAG, "decodeFrameBitmapAtTime: decodeFrameBitmapAtTime failed, crash file = ${PathMask.mask(filePath)}")
        }.getOrNull()
        return result ?: null
    }

    override fun decodeFrameBitmapAtTime(timeUs: Long, size: Int, optionFlag: OptionFlag): Bitmap? {
        return decodeFrameBitmapAtTime(timeUs, size)
    }

    private fun getVideoHeight(nvVideoStreamInfo: NvsVideoStreamInfo): Int {
        return when (nvVideoStreamInfo.displayRotation) {
            NvsVideoStreamInfo.VIDEO_ROTATION_90,
            NvsVideoStreamInfo.VIDEO_ROTATION_270 -> nvVideoStreamInfo.imageWidth

            else -> nvVideoStreamInfo.imageHeight
        }
    }

    private fun createNvsRetriever(path: String) {
        if (DEBUG_DOLBY_DECODER_LIMIT) {
            GLog.d(TAG, "[createNvsRetriever], prepare curBlockSize: ${decoderQueue?.size}, thread:${Thread.currentThread().id}")
        }
        if (TextUtils.isEmpty(path)) {
            GLog.w(TAG, "createNvsRetriever path is null or empty,and return")
            return
        }
        // 子线程才做队列阻塞，防止阻塞到主线程
        if (ThreadUtils.isMainThread().not()) {
            // 将当次请求加入队列尾部，若队列已满，则阻塞
            decoderQueue?.put(this)
        }
        if (DEBUG_DOLBY_DECODER_LIMIT) {
            GLog.d(TAG, "[createNvsRetriever], curBlockSize: ${decoderQueue?.size}, thread:${Thread.currentThread().id} start")
        }
        nvsRetriever?.release()
        MeicamContextAliveCounter.requestContext(ContextGetter.context, tag = TAG)?.run {
            // 优先使用硬解解缩图，如果硬解失败会自动替换成软解
            val result = runNativeGuarding("createNvsRetriever", generateKey(path, 0L)) {
                nvsRetriever = createVideoFrameRetriever(path, parseDecoderType(decoderType))
            }.onNativeFailure() {
                GLog.w(TAG, "createNvsRetriever: requestContext failed, crash file = ${PathMask.mask(path)}")
            }
        }
    }

    @VisibleForTesting
    fun calculateThumbnailHeight(minSize: Int): Int {
        if (minSize <= 0) {
            GLog.e(TAG, "[calculateThumbnailHeight], can't calculate thumbnail height because input size is less than 0")
            return INVALID_SIZE
        }
        val nvVideoStreamInfo = nvsRetriever?.nvVideoStreamInfo ?: let {
            GLog.e(TAG, "[calculateThumbnailHeight], can't calculate thumbnail height because nvVideoStreamInfo is null")
            return INVALID_SIZE
        }
        if ((nvVideoStreamInfo.imageWidth <= 0) || (nvVideoStreamInfo.imageHeight <= 0)) {
            GLog.e(TAG, "[calculateThumbnailHeight], can't calculate thumbnail height because image width or height less than 0")
            return INVALID_SIZE
        }
        val minFactor = min(
            nvVideoStreamInfo.imageWidth / minSize.toFloat(),
            nvVideoStreamInfo.imageHeight / minSize.toFloat()
        )
        val videoHeight = getVideoHeight(nvVideoStreamInfo)
        val thumbnailHeight = Math.round(videoHeight / minFactor)
        if (thumbnailHeight < MEICAM_THUMBNAIL_INPUT_HEIGHT_MIN_SEGMENT) {
            GLog.w(TAG, "[calculateThumbnailHeight], thumbnail height=$thumbnailHeight, will be cropped to 0")
        }
        return clipVideoHeight(thumbnailHeight)
    }

    /**
     *  美摄的取帧接口输入的高度必须为2的倍数，此方法会向下裁剪到2的倍数
     *  @param input 需要裁剪的值
     *  @return 裁剪后的值，为2的整数倍
     */
    private fun clipVideoHeight(input: Int): Int {
        return input and MEICAM_THUMBNAIL_INPUT_HEIGHT_MASK
    }

    override fun close() {
        /**
         *  美摄不支持在解码过程中调用release,需要外部调用者在子线程的缩图解码完成后调用close
         */
        if (ThreadUtils.isMainThread()) {
            GLog.w(TAG, "[close], can't close nvsRetriever on main thread")
            return
        }
        //取出队列头部，同时通知最早被阻塞的请求继续执行
        decoderQueue?.poll()
        if (DEBUG_DOLBY_DECODER_LIMIT) {
            GLog.d(TAG, "[close], block poll, curBlockSize: ${decoderQueue?.size}, thread:${Thread.currentThread().id} end")
        }

        nvsRetriever?.apply {
            nvsRetriever = null
            AppScope.launch(Dispatchers.MeicamEdit) {
                /*
                这里释放耗时会阻塞解码线程返回bitmap: SDK负责人表示,需确保nvsRetriever实例同一时间内只有一个线程中使用,不能并发使用
                release已经是最后的阶段,不会再有其它线程调用了，可以放在子线程中
                 */
                release()
            }
            MeicamContextAliveCounter.tryCloseContext(TAG)
        }
    }

    override fun isValid(): Boolean {
        return nvsRetriever != null
    }

    private fun parseDecoderType(type: DecoderType): Int {
        return when (type) {
            DecoderType.SOFT -> VIDEO_FRAME_RETRIEVER_FLAG_SOFT_DECODER
            DecoderType.HARD -> VIDEO_FRAME_RETRIEVER_FLAG_HARDWARE_DECODER
            DecoderType.HARD_WITH_DOLBY_GPU -> VIDEO_FRAME_RETRIEVER_FLAG_DOLBY_GPU_DECODER
            DecoderType.HARD_WITH_DOLBY_GPU_P010 -> VIDEO_FRAME_RETRIEVER_FLAG_DOLBY_GPU_WITH_OUTPUT_P010_FORMAT_DECODER
        }
    }

    companion object {
        private const val TAG = "MeicamVideoThumbnailDecoder"

        /**
         * 先后使用硬软解码器
         * 这里 是用 1 或上 4
         * 其中
         * 4： 表示仅使用 软解
         * 1：使用硬解，且色彩转换由美摄处理
         * 5： 先后使用硬软解码器，其中硬件解码器完全由解码器去做色彩转换（跟1的差异就是美摄下的参数不同）
         * 133：先后使用硬软解码器，使用线性ToneMapping算法
         * 如果后续此变量增加下发的flag，请在下面的VIDEO_FRAME_RETRIEVER_FLAG_DOLBY_GPU_DECODER同步添加
         */
        private const val VIDEO_FRAME_RETRIEVER_FLAG_HARDWARE_DECODER =
            VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED or
                    VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED_TRANSFER or
                    VIDEO_FRAME_RETRIEVER_FLAG_TONE_MAPPING_LINEAR_USED

        /**
         * 用于杜比视频且支持杜比视频GPU方案的下发参数
         */
        private const val VIDEO_FRAME_RETRIEVER_FLAG_DOLBY_GPU_DECODER =
            VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED or
                    VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED_TRANSFER or
                    VIDEO_FRAME_RETRIEVER_FLAG_TONE_MAPPING_LINEAR_USED

        /**
         * 用于杜比视频且支持杜比视频GPU方案 且 让杜比解码器按P010格式输出
         * 背景：VIDEO_FRAME_RETRIEVER_FLAG_MEDIA_CODEC_FORCE_YUV10BIT_FORMAT该Flag用于美摄在部分机型上无法解析YUV420Flexible，规范方案是按YUVP010格式解析输出
         */
        private const val VIDEO_FRAME_RETRIEVER_FLAG_DOLBY_GPU_WITH_OUTPUT_P010_FORMAT_DECODER = VIDEO_FRAME_RETRIEVER_FLAG_DOLBY_GPU_DECODER or
                VIDEO_FRAME_RETRIEVER_FLAG_MEDIA_CODEC_FORCE_YUV10BIT_FORMAT

        /**
         * 4： 表示仅使用 软解
         * 132：使用软解，且使用线性ToneMapping算法
         * */
        private const val VIDEO_FRAME_RETRIEVER_FLAG_SOFT_DECODER = VIDEO_FRAME_RETRIEVER_FLAG_DEFAULT_MEDIA_CODEC_USED_TRANSFER or
                VIDEO_FRAME_RETRIEVER_FLAG_TONE_MAPPING_LINEAR_USED

        // 美摄的取帧接口输入的高必须是2的倍数
        private const val MEICAM_THUMBNAIL_INPUT_HEIGHT_MIN_SEGMENT = 2

        // 和缩图的高做与运算用,可以让高保持为2的倍数
        private const val MEICAM_THUMBNAIL_INPUT_HEIGHT_MASK = Int.MAX_VALUE - 0b1

        private const val HIGH_PERFORMANCE_PLATFORM_JOB_LIMIT = 2

        private const val LOW_PERFORMANCE_PLATFORM_JOB_LIMIT = 1

        /**
         * 1.获取cpuLevel接口getPlatformLevel在14.0.1后失效，os15上没人维护，zhufeng/konka项目获取到的值为未定义
         * 2.导致在高端机上解码杜比视频限制的只能同时一个任务在进行，会有解码慢问题
         * 3.os16上才会将getPlatformLevel这个接口功能重新加回来，暂时使用动画等级判断条件顶一下
         */
        private var jobLimit =
            if ((cpuLevel == PerformanceLevel.HIGH) || (PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.HIGH)) {
                HIGH_PERFORMANCE_PLATFORM_JOB_LIMIT
            } else LOW_PERFORMANCE_PLATFORM_JOB_LIMIT

        /**
         * 线程安全的自带阻塞的队列
         * 1，在某次请求时，block等于JOB_LIMIT，阻塞当次请求
         * 2，直到poll后，取出最早阻塞的请求继续执行
         */
        private var decoderQueue: LinkedBlockingQueue<MeicamVideoThumbnailDecoder>? = null

        fun setLimit(isSingleThumbnailCode: Boolean) {
            if (isSingleThumbnailCode) {
                jobLimit = LOW_PERFORMANCE_PLATFORM_JOB_LIMIT
            }

            if (decoderQueue == null) {
                decoderQueue = LinkedBlockingQueue(jobLimit)
            }
        }
    }
}