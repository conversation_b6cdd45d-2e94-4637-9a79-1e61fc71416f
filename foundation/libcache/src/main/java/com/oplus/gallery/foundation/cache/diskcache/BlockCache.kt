/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BlockCache.kt
 ** Description : Cache which contains bitmap content and block info.
 ** Version     : 1.0
 ** Date        : 2020/09/20
 ** Author      : JinPeng@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/09/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cache.diskcache

import android.content.Context
import android.graphics.Bitmap
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.foundation.util.debug.GLog
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.PrintWriter
import java.io.Serializable
import java.lang.ref.WeakReference
import kotlin.experimental.and
import kotlin.experimental.inv
import kotlin.math.abs

class BlockCache(val block: Block, bitmap: Bitmap) {

    private val bitmapRef: WeakReference<Bitmap> = WeakReference(bitmap)

    val bitmap: Bitmap?
        get() = bitmapRef.get()

    override fun toString(): String {
        return "BlockCache($block, bmp:${bitmap.hashCode()})"
    }

    class BlockCacheInfo(val version: Int) : Serializable {
        // map of year/month and it's blockMap . in addition , blockMap is the map of blockKey and blockInfo.
        // <dateKey, <blockKey, block>>
        private val blockMap = mutableMapOf<Int, MutableMap<Int, Block>>()

        fun addBlock(dateKey: Int, blockKey: Int, block: Block) {
            val blocks = blockMap[dateKey] ?: mutableMapOf()
            blocks[blockKey] = block
            blockMap[dateKey] = blocks
        }

        fun removeBlock(dateKey: Int, blockKey: Int) {
            blockMap[dateKey]?.let {
                if (it.containsKey(blockKey)) it.remove(blockKey)
            }
        }

        fun getBlock(blockKey: Int): Block? {
            blockMap.values.forEach { value ->
                if (value.containsKey(blockKey)) return value[blockKey]
            }
            return null
        }

        fun getBlock(dateKey: Int, blockKey: Int): Block? {
            blockMap[dateKey]?.let {
                if (it.containsKey(blockKey)) return it[blockKey]
            }
            return null
        }

        fun removeItem(itemKey: Long): Unit {
            blockMap.values.forEach { value ->
                value.values.forEach { block ->
                    if (block.itemList.containsKey(itemKey)) {
                        block.itemList.remove(itemKey)
                        return@removeItem
                    }
                }
            }
        }

        fun getBlock(dateKey: Int, itemKey: Long): Pair<Int, Block>? {
            blockMap[dateKey]?.forEach { (blockKey, block) ->
                if (block.itemList.containsKey(itemKey)) return Pair(blockKey, block)
            }
            return null
        }

        fun getAllBlockInfo(): Collection<Map<Int, Block>> = blockMap.values

        fun dump(writer: PrintWriter) {
            writer.println("dump cache info version=$version")
            writer.println("dump nodeMap size=${blockMap.size}")
            blockMap.forEach { entry ->
                writer.println("node dateKey=${entry.key}, blocksMapSize=${entry.value.size}")
                entry.value.forEach { (blockKey, block) ->
                    writer.println("blockMap blockKey=$blockKey, $block")
                    block.itemList.keys.forEach { itemKey ->
                        writer.println("block itemKey=$itemKey, ${block.itemList[itemKey]}")
                    }
                }
            }
        }

        companion object {
            private const val TAG = "BlockCache.BlockCacheInfo"
        }
    }

    companion object {
        private const val TAG = "BlockCache"
        private const val CACHE_VERSION = 7
        private const val CACHE_INFO_FILE_NAME = "cacheinfo"
        private const val CACHE_DIR_NAME = "timeline"

        fun dataObfuscation(data: ByteArray, length: Int): ByteArray {
            if (data.isNotEmpty() && (data.size >= length)) {
                for (i in 0 until length) {
                    data[i] = (data[i].inv() and 0xFF.toByte())
                }
            }
            return data
        }

        @JvmStatic
        fun getCacheDirPath(context: Context): String? {
            context.externalCacheDir?.absolutePath ?: let {
                GLog.w(TAG, "getCacheDirPath fail externalCacheDir is null")
                return null
            }
            return context.externalCacheDir?.absolutePath + File.separator + CACHE_DIR_NAME + File.separator
        }

        fun getCacheFilePath(context: Context, typeIndex: Int, keyCrc: Long, suffix: String): String? {
            return getCacheDirPath(context)?.let {
                it + getCacheFilePrefix(typeIndex) + abs(keyCrc) + suffix
            }
        }

        private fun getCacheInfoPath(context: Context, typeIndex: Int): String? {
            return getCacheDirPath(context)?.plus(CACHE_INFO_FILE_NAME + typeIndex)
        }

        private fun getCacheFilePrefix(typeIndex: Int): String {
            return "data_" + typeIndex + "_"
        }

        @JvmStatic
        fun saveBlockCacheInfo(context: Context, cacheInfo: BlockCacheInfo, typeIndex: Int): Boolean {
            return getCacheInfoPath(context, typeIndex)?.let { cacheInfoPath ->
                var result = false
                // save to disk , with serializing
                runCatching {
                    val bos = ByteArrayOutputStream()
                    ObjectOutputStream(bos).use {
                        it.writeObject(cacheInfo)
                    }
                    val cacheInfoFile = File(cacheInfoPath)
                    if (!cacheInfoFile.exists()) {
                        val success = cacheInfoFile.createNewFile()
                        GLog.d(TAG, "saveBlockCacheInfo  file.createNewFile success:$success")
                    }
                    var data: ByteArray? = null
                    bos.use {
                        data = dataObfuscation(it.toByteArray(), it.toByteArray().size)
                    }
                    FileOutputStream(cacheInfoFile.file).use {
                        it.write(data)
                    }
                    result = true
                }.onFailure {
                    GLog.e(TAG, "saveBlockCacheInfo  error: ", it)
                }
                result
            } ?: false
        }

        @JvmStatic
        fun getBlockCacheInfo(context: Context, typeIndex: Int): BlockCacheInfo {
            val time = System.currentTimeMillis()
            var cacheInfo: BlockCacheInfo? = null
            getCacheInfoPath(context, typeIndex)?.let { cacheInfoPath ->
                runCatching {
                    val cacheInfoFile = File(cacheInfoPath)
                    if (cacheInfoFile.exists() && cacheInfoFile.isFile) {
                        FileInputStream(cacheInfoFile.file).use { fis ->
                            (fis.channel).use { fileChannel ->
                                val length = fileChannel.size().toInt()
                                var data = ByteArray(length)
                                val result = fis.read(data, 0, length)
                                if (result == length) {
                                    data = dataObfuscation(data, length)
                                    ObjectInputStream(ByteArrayInputStream(data)).use { ois ->
                                        val obj = ois.readObject()
                                        if (obj is BlockCacheInfo) {
                                            cacheInfo = obj
                                        }
                                    }
                                }
                            }
                        }
                    }
                }.onFailure {
                    GLog.e(TAG, "getBlockCacheInfo exception: ", it)
                }
            }

            // cache info has been broken or upgraded , clear all and rebuild it
            if ((cacheInfo == null) || (cacheInfo!!.version != CACHE_VERSION)) {
                GLog.d(TAG, "getBlockCacheInfo version changed. cacheInfo=$cacheInfo, version=$CACHE_VERSION")
                clearBlockCacheInfo(context, typeIndex)
                cacheInfo = BlockCacheInfo(CACHE_VERSION)
            }
            GLog.d(TAG, "getBlockCacheInfo type: $typeIndex, time: ${GLog.getTime(time)}")
            return cacheInfo as BlockCacheInfo
        }

        private fun clearBlockCacheInfo(context: Context, typeIndex: Int) {
            getCacheInfoPath(context, typeIndex)?.let { cacheInfoPath ->
                runCatching {
                    File(cacheInfoPath).listFiles()?.let { invalidFiles ->
                        GLog.d(TAG, "clearBlockCacheInfo delete files. invalidFiles.length=${invalidFiles.size}")
                        for (invalidFile in invalidFiles) {
                            getCacheFilePrefix(typeIndex).let {
                                // remove this type's jpg picture
                                val name = invalidFile.name
                                if (name.contains(it) || name.contains(CACHE_INFO_FILE_NAME)) {
                                    val success = invalidFile.delete()
                                    GLog.d(TAG, "clearBlockCacheInfo delete file=$name, success=$success.")
                                }
                            }
                        }
                    }
                }.onFailure {
                    GLog.e(TAG, "clearBlockCacheInfo rebuild fail=${it.message}")
                }
            }
        }
    }
}
