/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExtendableSeekbar
 ** Description: 拓展coui seekbar
 ** Version: 1.0
 ** Date : 2025/4/24
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2025/04/24    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.oplus.gallery.videoeditorpage.R
import com.coui.appcompat.seekbar.COUISeekBar

/**
 * 拓展coui seekbar，提供能力：
 * 1.允许设置min值为负数，并且在滑动的时候值变化也能以实际对应的负数值对外回调
 * 2.设置开启/关闭状态(颜色不一样)
 */
class GallerySeekbar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUISeekBar(context, attrs, defStyleAttr) {

    /**
     * 圆圈颜色
     */
    private val thumbColor = ContextCompat.getColorStateList(context, R.color.seekbar_thumb_color)

    /**
     * 进度条颜色
     */
    private val progressColor = ContextCompat.getColorStateList(context, R.color.seekbar_progress_color)

    var minValue = 0
    var maxValue = 0

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        updateColorStateList()
        invalidate()
        requestLayout()
    }

    /**
     * 设置最小值
     * @param min 最小值
     */
    fun setMinData(min: Int) {
        this.minValue = min
        resetMax()
    }

    /**
     * 设置最大值
     * @param max 最大值
     */
    fun setMaxData(max: Int) {
        this.maxValue = max
        resetMax()
    }

    /**
     * 设置当前值
     * @param value 当前值
     */
    fun setCurrentValue(value: Int) {
        progress = if (minValue == -maxValue) {
            value + maxValue
        } else {
            value
        }
    }

    /**
     * seekbar滚动时的值回调
     */
    fun setOnExtendSeekBarChangeListener(listener: OnSeekBarChangeListener) {
        var localProgress = 0
        setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            override fun onProgressChanged(seekbar: COUISeekBar, progress: Int, fromUser: Boolean) {
                localProgress = progress
                if (minValue == -maxValue) {
                    localProgress -= (seekbar.max / 2)
                }
                listener.onProgressChanged(seekbar, localProgress, fromUser)
            }

            override fun onStartTrackingTouch(p0: COUISeekBar) {
                listener.onStartTrackingTouch(p0)
            }

            override fun onStopTrackingTouch(p0: COUISeekBar) {
                listener.onStopTrackingTouch(p0)
            }
        })
    }

    /**
     * 更新不同状态下的颜色值
     */
    private fun updateColorStateList() {
        thumbColor?.let {
            this.setThumbColor(it)
        }
        progressColor?.let {
            this.setProgressColor(it)
        }
    }

    /**
     * 重置seekbar的最大值
     */
    private fun resetMax() {
        if (minValue == -maxValue) {
            min = 0
            max = 2 * maxValue
            setStartFromMiddle(true)
        } else {
            min = minValue
            max = maxValue
            setStartFromMiddle(false)
        }
    }
}