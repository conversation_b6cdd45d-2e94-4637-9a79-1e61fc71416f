/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorTransitionUIController
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yeguang<PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.transition;


import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.ui.TimelineViewModel;
import com.oplus.gallery.videoeditorpage.resource.room.entity.TransitionEntity;
import com.oplus.gallery.videoeditorpage.video.business.base.EditorTrackBaseUIController;
import com.oplus.gallery.videoeditorpage.widget.adapter.BaseRecycleAdapter;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.videoeditorpage.widget.HorizontalListView;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 转场业务UI
 */
public class EditorTransitionUIController extends EditorTrackBaseUIController<Object> {
    private static final String TAG = "EditorTransitionUIController";

    /** 轨道延迟可见的时长（毫秒） */
    private static final int TRACK_VISIBLE_DELAY = 50;
    /** 场景过渡实体列表 */
    private final List<TransitionEntity> mTransitionEntityList = new ArrayList<>();
    /** BaseRecycleAdapter.OnItemClickListener监听器 */
    private final BaseRecycleAdapter.OnItemClickListener<TransitionEntity> mItemClickListener
            = new BaseRecycleAdapter.OnItemClickListener<TransitionEntity>() {
        @Override
        public void onItemClick(View view, int position, TransitionEntity item) { }

        @Override
        public void onItemSelected(View view, int position, TransitionEntity item, boolean isInitSelect) {
            mCurrentTransitionIndex = position;
            GLog.d(TAG, LogFlag.DL, "[onItemSelected] onItemSelected, position: " + position);

            if ((position >= mTransitionEntityList.size()) || (position < 0)) {
                GLog.e(TAG, LogFlag.DL, "[onItemSelected] position is invalid");
                return;
            }

            if (mOnIconClickListener != null) {
                mOnIconClickListener.onSelectTransitionEntity(item, position, false);
            }
        }

        @Override
        public void onItemUnSelected(int viewId) { }
    };

    /** 场景item容器 */
    private HorizontalListView mHorizontalListView;
    /** 当前转场索引 */
    private int mCurrentTransitionIndex;
    private OnIconClickListener mOnIconClickListener;
    private TransitionMenuDataAdapter mAdapter;
    /** 片段数量 */
    private int mClipCount;

    public EditorTransitionUIController(
            Context context,
            ViewGroup rootView,
            EditorTransitionState state,
            String currentName,
            int clipCount,
            TimelineViewModel timelineViewModel
    ) {
        super(context, rootView, state, timelineViewModel);
        mClipCount = clipCount;
        setCurrentTransitionIndex(currentName);
        setNeedTranslateAnimate(true);
    }

    public void setOnIconClickListener(OnIconClickListener iconClickListener) {
        this.mOnIconClickListener = iconClickListener;
    }

    @Override
    public void createView() {
        super.createView();

        mHorizontalListView = mContentContainer.findViewById(R.id.video_transition_list);
        mAdapter = new TransitionMenuDataAdapter(mContext, mTransitionEntityList);
        mAdapter.setItemClickListener(mItemClickListener);
        mAdapter.setHasStableIds(true);
        mHorizontalListView.setAdapter(mAdapter);
        setCurrentTransitionIndex(mCurrentTransitionIndex);
    }

    @Override
    public void resume(boolean isActivityResume) {
        super.resume(isActivityResume);
        // Marked by DuanYibin: 进入转场页面时轨道布局会变动，造成轨道闪烁。后面做了动画后将不会有这个问题
        if (mTrackView != null) {
            mTrackView.setVisibility(View.GONE);
            mTrackView.postDelayed(() -> mTrackView.setVisibility(View.VISIBLE), TRACK_VISIBLE_DELAY);
        }
    }

    @Override
    public void destroyView() {
        super.destroyView();
        if (mAdapter != null) {
            mAdapter.release();
        }
        mOnIconClickListener = null;
    }

    @Override
    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig appUiConfig) {
        super.onAppUiStateChanged(appUiConfig);
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_transition;
    }

    @Override
    public boolean needTitleCheckBox() {
        return mClipCount > AppConstants.Number.NUMBER_2;
    }

    /**
     * “应用至全部”按钮点击事件回调
     */
    @Override
    protected void onApplyAll() {
        super.onApplyAll();
        if (mOnIconClickListener != null) {
            if (mCurrentTransitionIndex >= mTransitionEntityList.size()) {
                GLog.e(TAG, LogFlag.DL, "onApplyAll, index error:" + mCurrentTransitionIndex + ", size:" + mTransitionEntityList.size());
                return;
            }
            mOnIconClickListener.onSelectTransitionEntity(mTransitionEntityList.get(mCurrentTransitionIndex), mCurrentTransitionIndex, true);
        }
    }

    /**
     * 设置转场当前的索引
     * @param transName 转场名称
     */
    public void setCurrentTransitionIndex(String transName) {
        int index = 0;
        if (mTransitionEntityList != null) {
            for (int i = 0; i < mTransitionEntityList.size(); i++) {
                String name = mTransitionEntityList.get(i).getResourceId();
                if (TextUtils.equals(name, transName)) {
                    index = i;
                    break;
                }
            }
        }

        if (mCurrentTransitionIndex != index) {
            setCurrentTransitionIndex(index);
        }
    }

    /**
     * 设置当前选中的索引
     * @param index 索引值
     */
    private void setCurrentTransitionIndex(int index) {
        if (mTransitionEntityList == null) {
            GLog.e(TAG, LogFlag.DL, "[setCurrentTransitionIndex] mTransitionEntityList is null");
            return;
        }
        if ((mHorizontalListView != null)
                && (mAdapter != null)
                && (index >= AppConstants.Number.NUMBER_0)
                && (index < mTransitionEntityList.size())
        ) {
            mAdapter.setSelection(index);
            mHorizontalListView.post(() -> {
                TransitionMenuDataAdapter adapter = (TransitionMenuDataAdapter) mHorizontalListView.getAdapter();
                if (adapter == null) {
                    GLog.e(TAG, LogFlag.DL, "[createView] post: adapter is null");
                    return;
                }

                int visibleItemNum = mHorizontalListView.getVisibleItemNum();
                int maxPosition = adapter.getItemCount() - AppConstants.Number.NUMBER_1;
                int animPosition = index;
                if (index < mCurrentTransitionIndex) {
                    animPosition = Math.max(0, index - visibleItemNum / AppConstants.Number.NUMBER_2);
                } else if (index > mCurrentTransitionIndex) {
                    animPosition = Math.min(index + visibleItemNum / AppConstants.Number.NUMBER_2, maxPosition);
                }
                mHorizontalListView.smoothScrollToPosition(animPosition);

                GLog.i(TAG, LogFlag.DL, "[setCurrentTransitionIndex] mHorizontalListView.post:"
                        + " mCurrentIndex = " + mCurrentTransitionIndex
                        + " selectIndex = " + index
                        + ", visibleItemNum = " + visibleItemNum
                        + ", animPosition = " + animPosition
                        + ", maxPosition = " + maxPosition);

                mCurrentTransitionIndex = index;
            });
        }
    }

    /**
     * 刷新转场列表全部
     * @param entities 转场数据
     */
    public void notifyDataSetChanged(List<TransitionEntity> entities) {
        mActivity.runOnUiThread(() -> {
            mTransitionEntityList.clear();
            mTransitionEntityList.addAll(entities);
            mAdapter.notifyDataSetChanged();
        });
    }

    /**
     * 刷新转场列表单项
     * @param position 刷新位置
     */
    public void notifyItemChanged(int position) {
        if (mAdapter != null) {
            mAdapter.notifyItemChanged(position);
        }
    }

    /**
     * 刷新转场列表单项
     * @param entity 刷新项
     */
    public void notifyItemChanged(TransitionEntity entity) {
        if (entity == null) {
            GLog.e(TAG, "notifyItemChanged. entity is null.");
            return;
        }
        if (mAdapter != null) {
            int size = mTransitionEntityList.size();
            TransitionEntity tmp = null;
            for (int i = 0; i < size; i++) {
                tmp = mTransitionEntityList.get(i);
                if (tmp.getId().equals(entity.getId())) {
                    tmp.mergeFrom(entity);
                    mAdapter.notifyItemChanged(i);
                    break;
                }
            }
        }
    }

    public interface OnIconClickListener {
        default void onSelectTransitionEntity(TransitionEntity entity, int position, boolean isApplyToAll) { }
    }


    @Override
    protected int getContainerId() {
        return R.id.menu_container;
    }

    @Override
    public int getContentLayoutId(@NonNull AppUiResponder.AppUiConfig appUiConfig) {
        if (EditorUIConfig.isEditorLandscape(appUiConfig)) {
            return R.layout.videoeditor_transition_layout_landscape;
        } else {
            return R.layout.videoeditor_transition_layout;
        }
    }

    @NonNull
    @Override
    public List<Integer> getReconfigureViewIds() {
        List<Integer> ids = new ArrayList<>();
        ids.add(R.id.bottom_action_bar);
        ids.add(R.id.video_transition_list);
        return ids;
    }
}
