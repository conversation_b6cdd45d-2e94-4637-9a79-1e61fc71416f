/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseVideoTimelineEffect.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

open class BaseVideoTimelineEffect(effectName: String?) : BaseVideoEffect(effectName) {
    var isUseAllClipExceptTail = false
    var zhName: String? = null
    var chName: String? = null
    var enName: String? = null

    constructor(effectName: String?, effectType: Int) : this(effectName) {
        this.type = effectType
    }
}