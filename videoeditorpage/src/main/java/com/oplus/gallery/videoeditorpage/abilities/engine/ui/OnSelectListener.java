/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - OnSelectListener.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.engine.ui;

import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;
import com.oplus.gallery.videoeditorpage.video.business.track.view.ClipViewWrapper;

public interface OnSelectListener {
    /**
     * 选中片段后的回调
     * @param view     片段视图
     * @param selected 是否选中
     * @param clip     片段模型
     * @param withAnim 是否需要动画: true表示需要动画, false表示不需要动画，ClipViewWrapper视频片段视图控件在处理点击事件时，该回调则需要动画，其他场景不需要动画
     */
    void onSelected(ClipViewWrapper view, boolean selected, ClipModel clip, boolean withAnim);

    void onLongClick(ClipViewWrapper view, ClipModel clip);
}
