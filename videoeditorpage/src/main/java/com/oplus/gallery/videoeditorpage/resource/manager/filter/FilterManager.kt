/*********************************************************************************
 ** Copyright (C), 2019-2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FilterManager.kt
 ** Description: 滤镜缓存管理
 **
 ** Version: 1.0.0
 ** Date: 2025/6/24
 ** Author: 80411952@OppoGallery3D
 ** TAG:[Filter]
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** 80411952@OppoGallery3D         2025/6/24      1.0.0        初始创建
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.manager.filter

import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.resource.room.entity.FilterEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.system.measureTimeMillis

/**
 * 滤镜资源管理类
 */
object FilterManager {
    private const val TAG = "FilterManager"

    /**
     * 滤镜图标缓存
     */
    private val filterCaches = mutableListOf<FilterEntity>()

    /**
     * 滤镜处理器，处理ipu滤镜和非ipu滤镜
     */
    private val filterProcessor by lazy {
        FilterProcessor()
    }

    /**
     * 仅用作提前加载，缓存结果
     */
    @WorkerThread
    fun getResourceListSync() {
        val duration = measureTimeMillis {
            // 要统计耗时的代码
            filterProcessor.loadFilterEntityList { list ->
                synchronized(this) {
                    if (filterCaches.isEmpty()) {
                        filterCaches.addAll(list)
                        GLog.d(TAG, LogFlag.DL, "[getResourceListSync] addAll size: ${filterCaches.size}")
                    }
                }
            }
        }
        GLog.d(TAG, LogFlag.DL, "[getResourceListSync] cost: $duration")
    }

    /**
     * 获取滤镜列表，一般情况下都是直接返回缓存，以防万一，如果缓存为空则重新加载
     */
    fun getResourceLists(callback: (List<FilterEntity>) -> Unit) {
        if (filterCaches.isEmpty()) {
            AppScope.launch(Dispatchers.IO) {
                filterProcessor.loadFilterEntityList { list ->
                    synchronized(this) {
                        if (filterCaches.isEmpty()) {
                            filterCaches.addAll(list)
                            GLog.d(TAG, LogFlag.DL, "[getResourceLists] addAll size: ${filterCaches.size}")
                        }
                        callback(filterCaches)
                    }
                }
            }
        } else {
            GLog.d(TAG, LogFlag.DL, "[getResourceLists] return filterCaches size: ${filterCaches.size}")
            callback(filterCaches)
        }
    }

    fun clear() {
        synchronized(this) {
            filterCaches.clear()
            GLog.d(TAG, LogFlag.DL, "[clear] filterCaches is clear")
        }
    }

    /**
     * 创建“无”滤镜
     * @return "无"滤镜对象
     */
    fun createNoneFilter(): FilterEntity {
        return FilterEntity().apply {
            filterName = ContextGetter.context.resources.getString(R.string.videoeditor_editor_text_none)
        }
    }
}
