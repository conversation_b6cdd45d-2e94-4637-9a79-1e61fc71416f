/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PipelineUtil.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/14
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_BUS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** huca<PERSON><PERSON>@Apps.Gallery		2020/08/14		1.0		OPLUS_ARCH_BUS
 *********************************************************************************/

package com.oplus.gallery.bus_lib.utils

import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import androidx.annotation.RequiresApi
import com.oplus.gallery.bus_lib.annotations.Subscribe
import java.lang.reflect.Method
import java.lang.reflect.Modifier

internal object BusUtil {
    private const val HANDLER_THREAD_NAME = "pipeline-HT"
    val mainHandler: Handler = Handler(Looper.getMainLooper())
    val threadHandler: Handler = HandlerThread(HANDLER_THREAD_NAME).run {
        start()
        Handler(looper)
    }

    @Suppress("UtilMustStaticRule")
    @RequiresApi(Build.VERSION_CODES.O)
    fun Method.isPipelineAnnotion(): Boolean {
        return if (isAnnotationPresent(Subscribe::class.java)) {
            if ((parameterCount != 1)) {
                throw IllegalArgumentException("Method $this has @Subscribe annotation param size must 1")
            }
            parameterTypes.forEach { paramType ->
                if (paramType.isInterface) {
                    throw IllegalArgumentException("Method $this has @Subscribe annotation on param $paramType must not be a interface")
                }
            }
            if ((modifiers and Modifier.PUBLIC) == 0) {
                throw IllegalArgumentException("Method $this has @Subscribe annotation on must be public")
            }
            true
        } else {
            false
        }
    }
}