/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DecodeUtilsTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/24
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/24    1.0                create
 ******************************************************************************/

package com.oplus.gallery.standard_lib.codec

import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized

@RunWith(Parameterized::class)
class TestComputeSampleSizeLarger(
    private val inScale: Float,
    private val outResult: Int
) {

    @Test
    fun test_computeSampleSizeLarger() {
        val result = DecodeUtils.computeSampleSizeLarger(inScale)
        Assert.assertEquals(outResult, result)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun getParameters(): Collection<*> {
            return listOf(
                arrayOf(0f, 1),
                arrayOf(2f, 1),
                arrayOf(0.3f, 2),
                arrayOf(0.2f, 4),
                arrayOf(0.1f, 8),
                arrayOf(-2, 1)
            )
        }
    }
}

@RunWith(Parameterized::class)
class TestComputeSampleSize(
    private val inScale: Float,
    private val expectedResult: Int
) {

    @Test
    fun test_ComputeSampleSize() {
        val result = DecodeUtils.computeSampleSize(inScale)
        Assert.assertEquals(expectedResult, result)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun getParameters(): Collection<*> {

            return listOf(
                arrayOf(0f, 1),
                arrayOf(4, 1),
                arrayOf(0.6f, 2),
                arrayOf(0.4f, 4),
                arrayOf(0.1f, 16)
            )
        }
    }
}

@RunWith(Parameterized::class)
class TestComputeSampleSizeIILL(
    private val valueComputeInitialSampleSize: Int,
    private val expectedValue: Int
) {
    @After
    fun after() {
        unmockkAll()
    }

    @Test
    fun test_computeSampleSize() {
        mockkStatic(DecodeUtils::class)
        every { DecodeUtils.computeInitialSampleSize(any(), any(), any(), any()) } returns valueComputeInitialSampleSize

        val result = DecodeUtils.computeSampleSize(0, 0, 1, 1)

        Assert.assertEquals(expectedValue, result)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun getParameters(): Collection<*> {
            return listOf(
                arrayOf(0, 1),
                arrayOf(1, 1),
                arrayOf(2, 2),
                arrayOf(5, 8),
                arrayOf(9, 16),
                arrayOf(13, 16),
                arrayOf(20, 24)
            )
        }
    }
}

@RunWith(Parameterized::class)
class TestComputeInitialSampleSize(
    private val inWidth: Int,
    private val inHeight: Int,
    private val inMinSideLength: Long,
    private val inMaxNumOfPixels: Long,
    private val expectedResult: Int
) {
    @Test
    fun test_computeInitialSampleSize() {
        val result = DecodeUtils.computeInitialSampleSize(inWidth, inHeight, inMinSideLength, inMaxNumOfPixels)
        Assert.assertEquals(expectedResult, result)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun getParameters(): Collection<*> {
            return listOf(
                arrayOf(22, 22, -1, -1, 1),
                arrayOf(22, 22, 0, 22, 1),
                arrayOf(22, 22, 22, 0, 1),
                arrayOf(4, 6, 2, -1, 2),
                arrayOf(6, 4, 2, -1, 2),
                arrayOf(6, 4, 2, 3, 3)
            )
        }
    }
}