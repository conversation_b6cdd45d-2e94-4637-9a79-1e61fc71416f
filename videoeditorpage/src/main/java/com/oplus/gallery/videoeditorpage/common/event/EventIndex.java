/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EventIndex.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.common.event;

/**
 * EventBus目前最被诟病的一点就是无法追溯事件，所以为了能更好的控制EvenBus，我们创建了该类作为事件索引标识。
 *
 * <AUTHOR>
 */
public class EventIndex {
    public static final String MAIN_CREATE_FRAGMENT_BACK_TO_TOP = "main_create_fragment_back_to_top";
    public static final String SAVE_DRAFT_ACTION_FINISHED = "save_draft_finished";
    public static final String DELETED_DRAFT = "deleted_draft";
    public static final String DELETED_WORKS = "deleted_works";
    public static final String SELECT_MAIN_PAGE_TAB = "select_main_page_tab";
    public static final String SELECT_TEMPLATE_PAGE_TAB = "select_template_page_tab";
    public static final String NOTICE_TEMPLATE_LIKE_NUM_CHANGE = "notice_template_like_num_change";
    public static final String MAIN_CREATE_FRAGMENT_SCROLL_TO_POSITION = "main_create_fragment_scroll_to_position";
    public static final String MAIN_CREATE_FRAGMENT_APPBAR_TO_TOP = "main_create_fragment_appbar_to_top";
    public static final String PERSONAL_PAGE_APPBAR_TO_TOP = "personal_page_appbar_to_top";
    public static final String TEMPLATE_BANNER = "template_banner";
    public static final String STATEMENT_MESSAGE = "statement_message";
    public static final String SAVE_DRAFT_OR_WORKS_TIP = "save_draft_or_works_tip";
    public static final String NOTIFY_EDITOR_SDK_DOWNLOAD_TEMPLATE = "notify_editor_sdk_download_template";
    public static final String CRASH_ABNORMAL = "crash_abnormal";
    public static final String COLD_START_30 = "cold_start_30";
    public static final String SET_LIKE_TAB_VISIBLE = "set_like_tab_visible";
    public static final String UPDATE_LIKE_TAB_LIST = "add_to_like_tab";
    public static final String TEMPLATE_PLAY_ACTIVITY_FINISH = "template_play_activity_finish";
    public static final String NOTICE_ACTIVITY_WITH_TEMPLATE_PLAY_FINISH = "notice_activity_with_template_play_finish";
    public static final String SEND_TIK_TOK_BACK_MESSAGE = "send_tik_tok_back_message";
    public static final String NOTICE_MATERIAL_ROOT_VIEW_CLICK = "notice_material_root_view_click";
    public static final String NOTICE_INNER_MESSAGE_UNREAD_COUNT = "notice_inner_message_unread_count";
    public static final String NOTICE_TEMPLATE_LIST_FRAGMENT_FROM_SHOW_LABEL = "notice_template_list_fragment_from_show_label";
    public static final String NOTICE_NET_CHANGE = "notice_net_change";

    public static final String LIMITTASKMSG = "limit_task_msg";
    public static final String LIMITTASKMSG_LIST = "limit_task_list";
    public static final String SWITCH_ACTIVITY = "switch_activity";
    public static final String NOTICE_COMPILE_DONE_FROM_TEMPLATE_EXPORT = "notice_compile_done_from_template_export";
    public static final String TEMPLATE_MUSIC_DELETE = "com.heytap.delete_templatemusic";
    public static final String NAVIGATIONBAR_VISIBILITY_CHANGED = "com.heytap.NAVIGATIONBAR_VISIBILITY_CHANGED";
    public static final String NOTIFY_DRAFT_SAVE = "notify_draft_save";

    public static final String THIRD_COMPILE_RESULT = "third_compile_result";
    public static final String NOTIFY_TAB_SHOW_MARK = "notify_tab_show_mark";
    public static final String LIST_SCROLL_STATUS = "list_scroll_status";
    public static final String MAIN_DATA_REFRESH = "main_data_refresh";
    public static final String MUSIC_ALL_PEOPLE_USE_PAGE_USE_MUSIC = "music_all_people_use_page_use_music";
    public static final String NOTICE_LOCAL_FX_INSTALL_MESSAGE = "notice_local_fx_install_message";
    public static final String NOTICE_LOCAL_FX_ADD_TO_TRACK_MESSAGE = "notice_local_fx_add_to_track_message";
    public static final String NOTICE_LOCAL_FX_DESTROY_UI_CONTROLLER_MESSAGE = "notice_local_fx_destroy_ui_controller_message";
    public static final String NOTICE_WEIBO_SHARE_RESULT = "notice_weibo_share_result";
    public static final String NOTICE_WECHAT_SHARE_RESULT = "notice_wechat_share_result";
    public static final String NOTICE_SHARE_STATISTICS = "notice_share_statistics";
    public static final String NOTICE_SHARE_POPUP_STATISTICS = "notice_share_popup_statistics";

    public class CameraTemplate {
        public static final String CLIP_RETAKE_ALL = "clip_retake_all";
        public static final String DESTROY_CAMERA = "destroy_camera";
        public static final String INTO_CAMERASHOOT = "into_camerashoot";
        public static final String RETRY_CAMERANET = "retry_cameranet";
        public static final String CLICK_SCROLL = "click_scroll";
        public static final String BACK_CAMERA = "back_camera";
    }

    public static final String CLICK_TEMPLATEPLAY = "click_templateplay";
    public static final String ACCOUNT_CHECK = "account_check";
    public static final String OVERSEA_RED_TEMPLATE = "oversea_red_template";
    public static final String OVERSEA_RED_STICKER = "oversea_red_sticker";
    public static final String OVERSEA_ENTER_TEMPLATE = "oversea_enter_template";
    public static final String OVERSEA_TEMPLATE_ANIMATION_END = "oversea_template_animation_end";
    public static final String AI_TEMPLATE_DUARTION_CHANGED = "ai_template_duartion_changed";
    public static final String SPECIAL_TEMPLATE_TOP = "special_template_top";
    public static final String CLICK_TEMPLATE_PLAY = "click_template_play";
    public static final String TEMPLATE_VIDEO_ITEM = "template_video_item";
    public static final String APK_VERSION_INFO = "apk_version_info";
    public static final String TEMPLATE_PLAY_EXP_ID = "template_play_exp_id";

    public static final String UGC_RECYCLERVIEW_EVENT = "ugc_recyclerview_event";
    public static final String NOTIFY_SELECT_LABEL_ITEM = "notify_select_label_item";
    public static final String NOTIFY_TEMPLATE_LAYOUT_TOP = "notify_template_layout_top";

    public interface UGCRecyclerviewEvent {
        String UGC_RECYCLERVIEW_EVENT_MOVE = "ugc_recyclerview_event_move";
        String UGC_RECYCLERVIEW_EVENT_UP = "ugc_recyclerview_event_up";
        String UGC_RECYCLERVIEW_EVENT_SLIDE = "ugc_recyclerview_event_slide";
    }
}