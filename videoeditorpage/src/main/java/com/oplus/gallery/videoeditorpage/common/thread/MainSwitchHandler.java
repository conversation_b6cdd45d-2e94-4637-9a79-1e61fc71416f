/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MainSwitchHandler.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.common.thread;

import android.os.Handler;
import android.os.Looper;

public class MainSwitchHandler extends Handler {
    private volatile static MainSwitchHandler sMainHandler;

    private MainSwitchHandler() {
        super(Looper.getMainLooper());
    }

    public static MainSwitchHandler getInstance() {
        if (sMainHandler == null) {
            synchronized (MainSwitchHandler.class) {
                if (sMainHandler == null) {
                    sMainHandler = new MainSwitchHandler();
                }
            }
        }
        return sMainHandler;
    }
}
