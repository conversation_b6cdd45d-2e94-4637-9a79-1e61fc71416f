/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ReverseVideoInfo.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.data;

public class ReverseVideoInfo {
    private String mSrcPath;
    private String mPath;
    private long mMediaDuration;
    private long mOriginTrimInPoint;

    public ReverseVideoInfo(String path, String sourcePath) {
        mPath = path;
        mSrcPath = sourcePath;
    }

    public void setMeidaDuration(long trimDuration) {
        mMediaDuration = trimDuration;
    }

    public long getMediaDuration() {
        return mMediaDuration;
    }

    public void setOriginTrimInPoint(long trimInPoint) {
        mOriginTrimInPoint = trimInPoint;
    }

    public long getOriginTrimInPoint() {
        return mOriginTrimInPoint;
    }

    public String getReverseFilePath() {
        return mPath;
    }

    public String getSourceFilePath() {
        return mSrcPath;
    }

}
