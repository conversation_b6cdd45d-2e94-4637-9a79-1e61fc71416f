/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - HdrTransformData.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/31
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/31		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.codec.extend

import com.oplus.gallery.foundation.codec.extend.HdrTransformData.Companion.checkNotZero
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.BOOL_VALUE_FALSE
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.BOOL_VALUE_TRUE
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.CCM_VERSION
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.DEFAULT_GAMMA_TRANSFORM_DATA_CAPACITY
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.LIGHT_UP_VERSION
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.defaultHdrTransformData
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.dolbyHlgGamma
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct.Companion.dolbySrgbGamma
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.put
import com.oplus.gallery.foundation.util.ext.readIntArray
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 图片编辑输入下变换的参数，下变换process处理需要的参数
 *
 * 对应的key为：[EXTEND_KEY_HDR_TRANSFORM_DATA]
 */
class HdrTransformDataStruct(byteArray: ByteArray) : ExtendStruct<HdrTransformData>(byteArray) {

    var version: Float32

    var tmcMode: Signed32
    var cameraMode: Signed32
    var luxIndex: Signed32
    var faceLumaRatio: Float32
    var faceNum: Signed32
    var zoomFactor: Float32

    var gammaEnable: Signed32
    var hlgDstGammaTable: Array<Signed32>
    var srgbDstGammaTable: Array<Signed32>

    var sharpenEnable: Signed32
    var sharpenRadius: Signed32
    var sharpenSigma: Float32

    var lutEnable: Signed32

    /**
     * 颜色补偿矩阵，目的：
     *
     * 为了在照片模式，夜景模式，高像素模式不开滤镜下拍照时，在相册编辑下变换时补偿饱和度
     *
     * 无需颜色补偿矩阵的场景：
     *
     * 开了滤镜或者是人像模式，大师模式，XPAN 模式，以及前置的场景，打包的是单位矩阵
     * */
    var ccmData: Array<Float32>? = null

    /**
     * 是否对三通道图进行提亮增强处理
     */
    var lightUpEnable: Signed32? = null

    /**
     * 提亮增强的参数，受[lightUpEnable]开关影响
     */
    var imageCoeff: Array<Float32>? = null

    init {
        version = Float32()

        tmcMode = Signed32()
        cameraMode = Signed32()
        luxIndex = Signed32()
        faceLumaRatio = Float32()
        faceNum = Signed32()
        zoomFactor = Float32()

        gammaEnable = Signed32()
        hlgDstGammaTable = array(arrayOfNulls<Signed32>(GAMMA_TABLE_SIZE))
        srgbDstGammaTable = array(arrayOfNulls<Signed32>(GAMMA_TABLE_SIZE))

        sharpenEnable = Signed32()
        sharpenRadius = Signed32()
        sharpenSigma = Float32()

        lutEnable = Signed32()

        // 依据下变换结构体版本判断是否需要读取ccm数据
        if (version.get() >= CCM_VERSION) {
            ccmData = array(arrayOfNulls<Float32>(CCM_DATA_SIZE))
        }
        if (version.get() >= LIGHT_UP_VERSION) {
            lightUpEnable = Signed32()
            imageCoeff = array(arrayOfNulls<Float32>(LIGHT_UP_COEFF_NUM))
        }
    }

    override fun toData(): HdrTransformData {
        return runCatching {
            HdrTransformData(
                version.get(),
                tmcMode.get(),
                cameraMode.get(),
                luxIndex.get(),
                faceLumaRatio.get(),
                faceNum.get(),
                zoomFactor.get(),
                gammaEnable.get() != BOOL_VALUE_FALSE,
                hlgDstGammaTable.get().takeIf { it.checkNotZero() } ?: defaultHlgGamma,
                srgbDstGammaTable.get().takeIf { it.checkNotZero() } ?: defaultSrgbGamma,
                sharpenEnable.get() != BOOL_VALUE_FALSE,
                sharpenRadius.get(),
                sharpenSigma.get(),
                lutEnable.get() != BOOL_VALUE_FALSE,
                ccmData?.get(),
                lightUpEnable?.get() == BOOL_VALUE_TRUE,
                imageCoeff?.get()
            )
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "toData: is error." }
        }.getOrDefault(defaultHdrTransformData())
    }

    companion object {
        private const val TAG = "HdrTransformDataStruct"
        private const val GAMMA_TABLE_SIZE = 257
        internal const val BOOL_VALUE_FALSE = 0
        internal const val BOOL_VALUE_TRUE = 1

        // 下变换新增的ccm颜色补偿矩阵的size
        private const val CCM_DATA_SIZE = 9

        // 新增的ccm数据对应的下变换结构体版本
        internal const val CCM_VERSION = 1.1f

        /**
         * 新增提亮增强版本，目前只给IPU人像使用
         */
        internal const val LIGHT_UP_VERSION = 1.2f

        /**
         * 默认的携带 gamma 曲线的下变换数据的初始大小
         */
        internal const val DEFAULT_GAMMA_TRANSFORM_DATA_CAPACITY = 3000
        private const val LIGHT_UP_COEFF_NUM = 5

        /**
         * 默认的hlg gamma值，为当gamma为0时提供此值
         */
        val defaultHlgGamma: IntArray by lazy { ContextGetter.context.assets.open("hdrtransform/hlg.gamma").use { it.readIntArray() } }

        /**
         * 默认的srgb gamma值，为当gamma为0时提供此值
         */
        val defaultSrgbGamma: IntArray by lazy { ContextGetter.context.assets.open("hdrtransform/srgb.gamma").use { it.readIntArray() } }

        /**
         * 创建模式的下变换参数
         *
         * @return 返回下变换参数
         */
        fun defaultHdrTransformData(): HdrTransformData {
            return HdrTransformData(
                1.0f,
                0,
                1,
                0,
                0.0f,
                0,
                0f,
                false,
                null,
                null,
                false,
                0,
                0f,
                true,
                null,
                false,
                null
            )
        }

        /**
         * 影像提供的 dolby hlg gamma 曲线
         */
        val dolbyHlgGamma: IntArray = intArrayOf(
            0, 92, 150, 190, 223, 252, 276, 297, 317, 336, 353, 370, 385, 400, 414, 428, 441, 454, 466, 478, 489, 500,
            511, 522, 532, 541, 550, 559, 567, 575, 582, 589, 596, 603, 610, 616, 622, 628, 633, 639, 644, 649, 654,
            659, 664, 668, 673, 677, 682, 686, 690, 694, 698, 702, 706, 710, 713, 717, 720, 724, 727, 731, 734, 737,
            740, 743, 746, 749, 752, 755, 758, 761, 764, 767, 770, 772, 775, 777, 780, 782, 785, 787, 790, 792, 795,
            797, 800, 802, 804, 806, 809, 811, 813, 815, 817, 819, 822, 824, 826, 828, 830, 832, 834, 836, 838, 840,
            841, 843, 845, 847, 849, 851, 853, 854, 856, 858, 860, 862, 863, 865, 867, 868, 870, 872, 873, 875, 876,
            878, 879, 881, 883, 884, 886, 887, 889, 890, 892, 894, 895, 897, 898, 899, 901, 902, 903, 905, 906, 908,
            909, 910, 912, 913, 914, 916, 917, 918, 920, 921, 922, 924, 925, 926, 927, 929, 930, 931, 933, 934, 935,
            936, 937, 939, 940, 941, 942, 944, 945, 946, 947, 948, 949, 951, 952, 953, 954, 955, 956, 957, 958, 959,
            961, 962, 963, 964, 965, 966, 967, 968, 969, 970, 971, 972, 973, 974, 976, 977, 978, 979, 980, 981, 982,
            983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002,
            1003, 1004, 1005, 1006, 1007, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1014, 1015, 1016, 1017,
            1018, 1019, 1019, 1020, 1021, 1022, 1023, 1023, 1023
        )

        /**
         * 影像提供的 dolby srgb gamma 曲线
         */
        val dolbySrgbGamma: IntArray = intArrayOf(
            0, 51, 87, 113, 135, 153, 170, 185, 198, 211, 223, 235, 245, 256, 265, 275, 284, 292, 301, 309, 317, 324,
            332, 339, 346, 353, 360, 366, 373, 379, 385, 392, 398, 403, 409, 415, 420, 426, 431, 436, 442, 447, 452,
            457, 462, 467, 472, 476, 481, 486, 490, 495, 499, 504, 508, 512, 517, 521, 525, 529, 533, 537, 541, 545,
            549, 553, 557, 561, 565, 569, 572, 576, 580, 584, 587, 591, 594, 598, 601, 605, 608, 612, 615, 619, 622,
            625, 629, 632, 635, 639, 642, 645, 648, 652, 655, 658, 661, 664, 667, 670, 673, 676, 679, 682, 685, 688,
            691, 694, 697, 700, 703, 706, 709, 711, 714, 717, 720, 723, 725, 728, 731, 734, 736, 739, 742, 744, 747,
            750, 752, 755, 758, 760, 763, 765, 768, 770, 773, 775, 778, 781, 783, 786, 788, 790, 793, 795, 798, 800,
            803, 805, 808, 810, 812, 815, 817, 819, 822, 824, 826, 829, 831, 833, 836, 838, 840, 843, 845, 847, 849,
            852, 854, 856, 858, 860, 863, 865, 867, 869, 871, 874, 876, 878, 880, 882, 884, 886, 889, 891, 893, 895,
            897, 899, 901, 903, 905, 907, 909, 911, 913, 915, 918, 920, 922, 924, 926, 928, 930, 932, 934, 936, 938,
            939, 941, 943, 945, 947, 949, 951, 953, 955, 957, 959, 961, 963, 965, 966, 968, 970, 972, 974, 976, 978,
            980, 981, 983, 985, 987, 989, 991, 993, 994, 996, 998, 1000, 1002, 1003, 1005, 1007, 1009, 1011, 1012,
            1014, 1016, 1018, 1019, 1021, 1023
        )

        /**
         * 只使用了杜比 gamma 曲线的下变换参数, 用于相机没有写入下变换信息的文件做下变换的时候
         *
         * 和 default 的差异：设置 gammaEnable = true ，使用 dolby 的 hlg 和 srgb 曲线，禁用了 lutEnable
         * 影像下变换算法内部 gammaEnable == true -> gamma，  (gammaEnable == false) && (lutEnable == true) -> lut
         *
         * @return 返回下变换参数
         *
         * ps：现在只用于写入文件的场景（写入FileExtend或保存文件时应用到图片视频效果里），
         * 读文件读不到下变换信息时默认值还使用 defaultHdrTransformData，这块对效果的
         * 影响范围太大，并且后续有可能切换成美摄的下变换算法，先不改。
         */
        fun dolbyHdrTransformData(): HdrTransformData {
            return HdrTransformData(
                1.0f,
                0,
                1,
                0,
                0.0f,
                0,
                0f,
                gammaEnable = true,
                hlgDstGammaTable = dolbyHlgGamma,
                srgbDstGammaTable = dolbySrgbGamma,
                false,
                0,
                0f,
                false,
                null,
                false,
                null
            )
        }
    }
}

/**
 * 图片编辑输入下变换的参数，下变换process处理需要的参数。
 *
 * 从文件结构[HdrTransformDataStruct]转换得出。
 *
 * @param version 版本号
 * @param tmcMode Tmc Mode，取值：TMC_CV、TMC_AI
 * @param cameraMode Camera Mode，取值：CAMERA_MODE_MAIN、CAMERA_MODE_ULTRA_WIDE、CAMERA_MODE_TELE1、CAMERA_MODE_TELE2、CAMERA_MODE_FRONT_MAIN
 * @param luxIndex xxx
 * @param faceLumaRatio xxx
 * @param faceNum 人脸个数
 * @param zoomFactor 当前zoom值
 * @param gammaEnable 是否启用gamma
 * @param hlgDstGammaTable HLG Gamma
 * @param srgbDstGammaTable sRGB Gamma
 * @param sharpenEnable 是否启用xxx
 * @param sharpenRadius xxx
 * @param sharpenSigma xxx
 * @param lutEnable 是否启用LUT
 * @param ccmData 颜色补偿矩阵
 * @param lightUpEnable 是否对三通道图提亮增强处理
 * @param imageCoeff 提亮的参数，受[lightUpEnable]开关影响
 */
@Suppress("LongParameterList")
data class HdrTransformData(
    val version: Float = 0f,
    val tmcMode: Int = 0,
    val cameraMode: Int = 0,
    val luxIndex: Int = 0,
    val faceLumaRatio: Float = 0f,
    val faceNum: Int = 0,
    val zoomFactor: Float = 0f,
    val gammaEnable: Boolean = false,
    val hlgDstGammaTable: IntArray? = null,
    val srgbDstGammaTable: IntArray? = null,
    val sharpenEnable: Boolean = false,
    val sharpenRadius: Int = 0,
    val sharpenSigma: Float = 0f,
    val lutEnable: Boolean = false,
    val ccmData: FloatArray? = null,
    val lightUpEnable: Boolean = false,
    val imageCoeff: FloatArray? = null
) : IInverseStructConvert {
    override fun toBytes(): ByteArray {
        val buffer: ByteBuffer = ByteBuffer.allocate(DEFAULT_GAMMA_TRANSFORM_DATA_CAPACITY)
        buffer.order(ByteOrder.LITTLE_ENDIAN)
        buffer.putFloat(version)
        buffer.putInt(tmcMode)
        buffer.putInt(cameraMode)
        buffer.putInt(luxIndex)
        buffer.putFloat(faceLumaRatio)
        buffer.putInt(faceNum)
        buffer.putFloat(zoomFactor)

        buffer.putInt(if (gammaEnable) BOOL_VALUE_TRUE else BOOL_VALUE_FALSE)
        buffer.put(hlgDstGammaTable ?: dolbyHlgGamma)
        buffer.put(srgbDstGammaTable ?: dolbySrgbGamma)

        buffer.putInt(if (sharpenEnable) BOOL_VALUE_TRUE else BOOL_VALUE_FALSE)
        // 如果 sharpenEnable 为 true，radius 和 sigma 不能为 null，直接抛异常
        buffer.putInt(sharpenRadius)
        buffer.putFloat(sharpenSigma)
        buffer.putInt(if (lutEnable) BOOL_VALUE_TRUE else BOOL_VALUE_FALSE)
        // version >= 1.1 才有 ccmData
        if (version >= CCM_VERSION) {
            buffer.put(ccmData!!)
        }
        // vserion >= 1.2 才有 lightUpEnable
        if (version >= LIGHT_UP_VERSION) {
            buffer.putInt(if (lightUpEnable) BOOL_VALUE_TRUE else BOOL_VALUE_FALSE)
            buffer.put(imageCoeff!!)
        }
        buffer.limit(buffer.position())
        buffer.rewind()
        val actualArray = ByteArray(buffer.limit())
        buffer.get(actualArray)
        return actualArray
    }

    /**
     * 因为 java 文件调用没有 copy() 方法，单独定义一个重赋值gamma曲线的方法
     */
    fun copyWithGamma(hlgGamma: IntArray, srgbGamma: IntArray): HdrTransformData {
        return copy(
            gammaEnable = true,
            hlgDstGammaTable = hlgGamma,
            srgbDstGammaTable = srgbGamma
        )
    }

    companion object {
        /**
         * Boolean值转Int值，启用为1
         */
        const val ENABLE_VALUE = 1

        /**
         * Boolean值转Int值，禁用为0
         */
        const val DISABLE_VALUE = 0

        /**
         * 判断gamma是否为0，由于gamma是一条线性曲线，因此只判断最后最后一个值是否大于0即可
         *
         * @return 返回是否为全零
         */
        internal fun IntArray.checkNotZero(): Boolean {
            return this[size - 1] > 0
        }
    }
}

/**
 * 获取下变换参数，在没有下变换参数时提供默认的下变换参数
 */
fun HdrTransformDataStruct?.toNotNullData(): HdrTransformData {
    return this?.toData() ?: defaultHdrTransformData()
}