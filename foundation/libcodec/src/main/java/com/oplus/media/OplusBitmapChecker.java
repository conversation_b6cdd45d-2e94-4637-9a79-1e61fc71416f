/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OplusBitmapChecker
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/3/6
 ** Author      : 80412226
 ** TAG         : NA
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80412226                         2025/6/27      1.0         NA
 *********************************************************************************/
// 包名必须是 com.oplus.media，不能改（对应系统的 这个类）
package com.oplus.media;

import android.graphics.Bitmap;

/**
 * 判断是否是残缺的图片，这个类只是一个空壳，实际调用时，调用的是系统的 OplusBitmapChecker（所以 这个类的包名必须与系统的包名相同）
 *
 * 特定版本的系统这个功能才会生效，例如：高通 + OS 16以上的版本（具体可以问：80268992）
 *
 * 这个类对应的系统真正实现：http://gerrit.scm.adc.com:8080/#/c/38007823/11/core/java/com/oplus/media/OplusBitmapChecker.java
 */
public class OplusBitmapChecker {
    private final static Boolean DEFAULT_VALUE = false;

    private OplusBitmapChecker() {}

    /**
     * 判断 Bitmap 对应的图片是否是残缺的；
     * 判断功能是否生效：运行时如果逻辑走到这里了，说明没有调用系统的 [getIncompleteFlag] 方法，功能没有生效；
     *                功能生效时，应该调用系统的 [getIncompleteFlag] 方法
     *
     * @return true 是残缺的图片，false 不是残缺的图片
     *
     * 备注：如果Bitmap是从多媒体库获取的 Bitmap，这个值无效，因为这里的赋值是通过 解码器 + 相册 才会生效
     * （比如：getMediaThumbnail 方法就是从多媒体库获取的 Bitmap）
     */
    public static boolean getIncompleteFlag(Bitmap bm) {
        return DEFAULT_VALUE;
    }
}
