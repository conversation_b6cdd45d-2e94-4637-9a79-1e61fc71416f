/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/6/13
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/6/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.helper;


import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;
import com.oplus.gallery.videoeditorpage.resource.room.dao.ThemeDao;

import java.util.List;

public class ThemeTableHelper extends AbsTableHelper<ThemeItem> {
    private static final String TAG = "ThemeTableHelper";

    public ThemeTableHelper() {
        mBaseDao = ResourceDatabaseHelper.getInstance().getDb().getThemeDao();
    }

    @Override
    public void clearBuiltin() {
        try {
            ((ThemeDao) mBaseDao).clearBuiltin();
        } catch (Exception e) {
            GLog.e(TAG, "clearBuiltin e:" + e);
        }
    }

    @Override
    public List<ThemeItem> getAllBuiltin() {
        try {
            return ((ThemeDao) mBaseDao).getAllBuiltin();
        } catch (Exception e) {
            GLog.e(TAG, "getAllBuiltin e:" + e);
            return null;
        }
    }

    @Override
    public int getAllBuiltinSize() {
        List<ThemeItem> entityList = getAllBuiltin();
        if (entityList == null) {
            GLog.e(TAG, "getAllBuiltinSize entityList is null!");
            return 0;
        }
        return entityList.size();
    }

    @Override
    public List<ThemeItem> getAll() {
        try {
            return ((ThemeDao) mBaseDao).queryAll();
        } catch (Exception e) {
            GLog.e(TAG, "getAll e:" + e);
            return null;
        }
    }

    public List<ThemeItem> queryIconExistedMemoriesTheme() {
        try {
            return ((ThemeDao) mBaseDao).queryIconExistedMemoriesTheme();
        } catch (Exception e) {
            GLog.e(TAG, "queryIconExistedMemoriesTheme e:" + e);
            return null;
        }
    }

    @Override
    public int getAllResourceSize() {
        List<ThemeItem> entityList = getAll();
        if (entityList == null) {
            GLog.e(TAG, "getAllResourceSize entityList is null!");
            return 0;
        }
        return entityList.size();
    }

    @Override
    public ThemeItem getEntityByPosition(int position) {
        try {
            return ((ThemeDao) mBaseDao).getEntityByPosition(position);
        } catch (Exception e) {
            GLog.e(TAG, "getEntityByPosition e:" + e);
            return null;
        }
    }

    @Override
    public ThemeItem getEntityByResourceId(int resourceId) {
        try {
            return ((ThemeDao) mBaseDao).getEntityByThemeId(resourceId);
        } catch (Exception e) {
            GLog.e(TAG, "getEntityByResourceId e:" + e);
            return null;
        }
    }

    public ThemeItem getItemBySongId(int songId) {
        try {
            return ((ThemeDao) mBaseDao).getItemByMusicId(songId);
        } catch (Exception e) {
            GLog.e(TAG, "getItemBySongId e:" + e);
            return null;
        }
    }

    @Override
    public int getDownloadState(int resourceId) {
        ThemeItem entity = getEntityByResourceId(resourceId);
        if (entity == null) {
            return -1;
        }
        return entity.getDownloadState();
    }

    @Override
    public int deleteInvalidEntity(int maxPosition) {
        try {
            return ((ThemeDao) mBaseDao).deleteInvalidEntity(maxPosition);
        } catch (Exception e) {
            GLog.e(TAG, "deleteInvalidEntity e:" + e);
            return -1;
        }
    }

    @Override
    public List<ThemeItem> getInvalidEntityList(int maxPosition) {
        try {
            return ((ThemeDao) mBaseDao).getInvalidEntityList(maxPosition);
        } catch (Exception e) {
            GLog.e(TAG, "getInvalidEntityList e:" + e);
            return null;
        }
    }

    @Override
    public List<ThemeItem> getNoIconEntityList() {
        try {
            return ((ThemeDao) mBaseDao).getNoIconEntityList();
        } catch (Exception e) {
            GLog.e(TAG, "getNoIconEntityList e:" + e);
            return null;
        }
    }

    public List<ThemeItem> queryAllMemoriesTheme() {
        try {
            return ((ThemeDao) mBaseDao).queryAllMemoriesTheme();
        } catch (Exception e) {
            GLog.e(TAG, "queryAllMemoriesTheme e:" + e);
            return null;
        }
    }

    public ThemeItem getThemeByPath(String path) {
        try {
            return ((ThemeDao) mBaseDao).getThemeByPath(path);
        } catch (Exception e) {
            GLog.e(TAG, "getThemeByPath e:" + e);
            return null;
        }
    }

    public List<ThemeItem> queryAllVideoTheme() {
        try {
            return ((ThemeDao) mBaseDao).queryAllVideoTheme();
        } catch (Exception e) {
            GLog.e(TAG, "queryAllMemoriesTheme e:" + e);
            return null;
        }
    }

    public List<ThemeItem> queryEnableVideoTheme() {
        try {
            return ((ThemeDao) mBaseDao).queryEnableVideoTheme();
        } catch (Exception e) {
            GLog.e(TAG, "queryEnableVideoTheme e:" + e);
            return null;
        }
    }

    public List<ThemeItem> queryEnableMemoriesTheme() {
        try {
            return ((ThemeDao) mBaseDao).queryEnableMemoriesTheme();
        } catch (Exception e) {
            GLog.e(TAG, "queryEnableMemoriesTheme e:" + e);
            return null;
        }
    }

    public List<ThemeItem> queryNeedDownloadMemoriesTheme() {
        try {
            return ((ThemeDao) mBaseDao).queryDisableMemoriesTheme();
        } catch (Exception e) {
            GLog.e(TAG, "queryNeedDownloadMemoriesTheme e:" + e);
            return null;
        }
    }
}
