<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="316dp"
    android:height="148dp"
    android:viewportWidth="316"
    android:viewportHeight="148">
  <group>
    <clip-path
        android:pathData="M0,0h316v148h-316z"/>
    <path
        android:pathData="M0,0h316v148h-316z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="57.65"
            android:startY="-17"
            android:endX="112.41"
            android:endY="197.37"
            android:type="linear">
          <item android:offset="0" android:color="#FF282929"/>
          <item android:offset="1" android:color="#FF252627"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M358,19.31L0,19.31L0,18.51L358,18.51L358,19.31Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="20.35"
            android:startY="19.81"
            android:endX="358"
            android:endY="19.81"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.46" android:color="#FF36393B"/>
          <item android:offset="0.72" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M358,100.31L42,100.31L42,99.51L358,99.51L358,100.31Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="42"
            android:startY="100.81"
            android:endX="358"
            android:endY="100.81"
            android:type="linear">
          <item android:offset="0.3" android:color="#0036393B"/>
          <item android:offset="0.45" android:color="#FF36393B"/>
          <item android:offset="0.78" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M205,59.71L23.46,59.71L23.46,58.91L205,58.91L205,59.71Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="23.46"
            android:startY="59.81"
            android:endX="205"
            android:endY="59.81"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.53" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M359.2,58.91L286.6,58.91L286.6,58.11L359.2,58.11L359.2,58.91Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="241.5"
            android:startY="58.51"
            android:endX="359.2"
            android:endY="58.51"
            android:type="linear">
          <item android:offset="0" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M305.8,140.71H116.2V139.91H305.8V140.71Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="116.2"
            android:startY="141.21"
            android:endX="305.8"
            android:endY="141.21"
            android:type="linear">
          <item android:offset="0.29" android:color="#0036393B"/>
          <item android:offset="0.69" android:color="#FF36393B"/>
          <item android:offset="0.93" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M164.2,182.11L164.2,-19.49L165,-19.49L165,182.11L164.2,182.11Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="163.7"
            android:startY="-19.49"
            android:endX="163.7"
            android:endY="182.11"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.39" android:color="#CC36393B"/>
          <item android:offset="0.7" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M64,62.59L64,-19.49L64.8,-19.49L64.8,62.59L64,62.59Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="63.5"
            android:startY="-19.49"
            android:endX="63.5"
            android:endY="62.59"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.39" android:color="#CC36393B"/>
          <item android:offset="0.89" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M205,182.11L205,-19.49L205.8,-19.49L205.8,182.11L205,182.11Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="204.5"
            android:startY="-19.49"
            android:endX="204.5"
            android:endY="182.11"
            android:type="linear">
          <item android:offset="0.05" android:color="#0036393B"/>
          <item android:offset="0.23" android:color="#FF36393B"/>
          <item android:offset="0.6" android:color="#FF36393B"/>
          <item android:offset="0.78" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M245.6,182.11L245.6,99.91L246.4,99.91L246.4,182.11L245.6,182.11ZM245.6,18.61L245.6,-19.49L246.4,-19.49L246.4,18.61L245.6,18.61Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="245.5"
            android:startY="-19.49"
            android:endX="245.5"
            android:endY="182.11"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.73" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M286.6,-18.89L286.6,-19.49L287.4,-19.49L287.4,-18.89L286.6,-18.89ZM286.6,-16.49L286.6,-17.69L287.4,-17.69L287.4,-16.49L286.6,-16.49ZM286.6,-14.09L286.6,-15.29L287.4,-15.29L287.4,-14.09L286.6,-14.09ZM286.6,-11.69L286.6,-12.89L287.4,-12.89L287.4,-11.69L286.6,-11.69ZM286.6,-9.29L286.6,-10.49L287.4,-10.49L287.4,-9.29L286.6,-9.29ZM286.6,-6.89L286.6,-8.09L287.4,-8.09L287.4,-6.89L286.6,-6.89ZM286.6,-4.49L286.6,-5.69L287.4,-5.69L287.4,-4.49L286.6,-4.49ZM286.6,-2.09L286.6,-3.29L287.4,-3.29L287.4,-2.09L286.6,-2.09ZM286.6,0.31L286.6,-0.89L287.4,-0.89L287.4,0.31L286.6,0.31ZM286.6,2.71L286.6,1.51L287.4,1.51L287.4,2.71L286.6,2.71ZM286.6,5.11L286.6,3.91L287.4,3.91L287.4,5.11L286.6,5.11ZM286.6,7.51L286.6,6.31L287.4,6.31L287.4,7.51L286.6,7.51ZM286.6,9.91L286.6,8.71L287.4,8.71L287.4,9.91L286.6,9.91ZM286.6,12.31L286.6,11.11L287.4,11.11L287.4,12.31L286.6,12.31ZM286.6,14.71L286.6,13.51L287.4,13.51L287.4,14.71L286.6,14.71ZM286.6,17.11L286.6,15.91L287.4,15.91L287.4,17.11L286.6,17.11ZM286.6,19.51L286.6,18.31L287.4,18.31L287.4,19.51L286.6,19.51ZM286.6,21.91L286.6,20.71L287.4,20.71L287.4,21.91L286.6,21.91ZM286.6,24.31L286.6,23.11L287.4,23.11L287.4,24.31L286.6,24.31ZM286.6,26.71L286.6,25.51L287.4,25.51L287.4,26.71L286.6,26.71ZM286.6,29.11L286.6,27.91L287.4,27.91L287.4,29.11L286.6,29.11ZM286.6,31.51L286.6,30.31L287.4,30.31L287.4,31.51L286.6,31.51ZM286.6,33.91L286.6,32.71L287.4,32.71L287.4,33.91L286.6,33.91ZM286.6,36.31L286.6,35.11L287.4,35.11L287.4,36.31L286.6,36.31ZM286.6,38.71L286.6,37.51L287.4,37.51L287.4,38.71L286.6,38.71ZM286.6,41.11L286.6,39.91L287.4,39.91L287.4,41.11L286.6,41.11ZM286.6,43.51L286.6,42.31L287.4,42.31L287.4,43.51L286.6,43.51ZM286.6,45.91L286.6,44.71L287.4,44.71L287.4,45.91L286.6,45.91ZM286.6,48.31L286.6,47.11L287.4,47.11L287.4,48.31L286.6,48.31ZM286.6,50.71L286.6,49.51L287.4,49.51L287.4,50.71L286.6,50.71ZM286.6,53.11L286.6,51.91L287.4,51.91L287.4,53.11L286.6,53.11ZM286.6,55.51L286.6,54.31L287.4,54.31L287.4,55.51L286.6,55.51ZM286.6,57.91L286.6,56.71L287.4,56.71L287.4,57.91L286.6,57.91ZM286.6,60.31L286.6,59.11L287.4,59.11L287.4,60.31L286.6,60.31ZM286.6,62.71L286.6,61.51L287.4,61.51L287.4,62.71L286.6,62.71ZM286.6,65.11L286.6,63.91L287.4,63.91L287.4,65.11L286.6,65.11ZM286.6,67.51L286.6,66.31L287.4,66.31L287.4,67.51L286.6,67.51ZM286.6,69.91L286.6,68.71L287.4,68.71L287.4,69.91L286.6,69.91ZM286.6,72.31L286.6,71.11L287.4,71.11L287.4,72.31L286.6,72.31ZM286.6,74.71L286.6,73.51L287.4,73.51L287.4,74.71L286.6,74.71ZM286.6,77.11L286.6,75.91L287.4,75.91L287.4,77.11L286.6,77.11ZM286.6,79.51L286.6,78.31L287.4,78.31L287.4,79.51L286.6,79.51ZM286.6,81.91L286.6,80.71L287.4,80.71L287.4,81.91L286.6,81.91ZM286.6,84.31L286.6,83.11L287.4,83.11L287.4,84.31L286.6,84.31ZM286.6,86.71L286.6,85.51L287.4,85.51L287.4,86.71L286.6,86.71ZM286.6,89.11L286.6,87.91L287.4,87.91L287.4,89.11L286.6,89.11ZM286.6,91.51L286.6,90.31L287.4,90.31L287.4,91.51L286.6,91.51ZM286.6,93.91L286.6,92.71L287.4,92.71L287.4,93.91L286.6,93.91ZM286.6,96.31L286.6,95.11L287.4,95.11L287.4,96.31L286.6,96.31ZM286.6,98.71L286.6,97.51L287.4,97.51L287.4,98.71L286.6,98.71ZM286.6,101.11L286.6,99.91L287.4,99.91L287.4,101.11L286.6,101.11ZM286.6,103.51L286.6,102.31L287.4,102.31L287.4,103.51L286.6,103.51ZM286.6,105.91L286.6,104.71L287.4,104.71L287.4,105.91L286.6,105.91ZM286.6,108.31L286.6,107.11L287.4,107.11L287.4,108.31L286.6,108.31ZM286.6,110.71L286.6,109.51L287.4,109.51L287.4,110.71L286.6,110.71ZM286.6,113.11L286.6,111.91L287.4,111.91L287.4,113.11L286.6,113.11ZM286.6,115.51L286.6,114.31L287.4,114.31L287.4,115.51L286.6,115.51ZM286.6,117.91L286.6,116.71L287.4,116.71L287.4,117.91L286.6,117.91ZM286.6,120.31L286.6,119.11L287.4,119.11L287.4,120.31L286.6,120.31ZM286.6,122.71L286.6,121.51L287.4,121.51L287.4,122.71L286.6,122.71ZM286.6,125.11L286.6,123.91L287.4,123.91L287.4,125.11L286.6,125.11ZM286.6,127.51L286.6,126.31L287.4,126.31L287.4,127.51L286.6,127.51ZM286.6,129.91L286.6,128.71L287.4,128.71L287.4,129.91L286.6,129.91ZM286.6,132.31L286.6,131.11L287.4,131.11L287.4,132.31L286.6,132.31ZM286.6,134.71L286.6,133.51L287.4,133.51L287.4,134.71L286.6,134.71ZM286.6,137.11L286.6,135.91L287.4,135.91L287.4,137.11L286.6,137.11ZM286.6,139.51L286.6,138.31L287.4,138.31L287.4,139.51L286.6,139.51ZM286.6,141.91L286.6,140.71L287.4,140.71L287.4,141.91L286.6,141.91ZM286.6,144.31L286.6,143.11L287.4,143.11L287.4,144.31L286.6,144.31ZM286.6,146.71L286.6,145.51L287.4,145.51L287.4,146.71L286.6,146.71ZM286.6,149.11L286.6,147.91L287.4,147.91L287.4,149.11L286.6,149.11ZM286.6,151.51L286.6,150.31L287.4,150.31L287.4,151.51L286.6,151.51ZM286.6,153.91L286.6,152.71L287.4,152.71L287.4,153.91L286.6,153.91ZM286.6,156.31L286.6,155.11L287.4,155.11L287.4,156.31L286.6,156.31ZM286.6,158.71L286.6,157.51L287.4,157.51L287.4,158.71L286.6,158.71ZM286.6,161.11L286.6,159.91L287.4,159.91L287.4,161.11L286.6,161.11ZM286.6,163.51L286.6,162.31L287.4,162.31L287.4,163.51L286.6,163.51ZM286.6,165.91L286.6,164.71L287.4,164.71L287.4,165.91L286.6,165.91ZM286.6,168.31L286.6,167.11L287.4,167.11L287.4,168.31L286.6,168.31ZM286.6,170.71L286.6,169.51L287.4,169.51L287.4,170.71L286.6,170.71ZM286.6,173.11L286.6,171.91L287.4,171.91L287.4,173.11L286.6,173.11ZM286.6,175.51L286.6,174.31L287.4,174.31L287.4,175.51L286.6,175.51ZM286.6,177.91L286.6,176.71L287.4,176.71L287.4,177.91L286.6,177.91ZM286.6,180.31L286.6,179.11L287.4,179.11L287.4,180.31L286.6,180.31ZM286.6,182.11L286.6,181.51L287.4,181.51L287.4,182.11L286.6,182.11Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="286.1"
            android:startY="-19.49"
            android:endX="286.1"
            android:endY="182.11"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.19" android:color="#FF36393B"/>
          <item android:offset="0.63" android:color="#FF36393B"/>
          <item android:offset="0.8" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M124,-16.89L124,-17.49L124.8,-17.49L124.8,-16.89L124,-16.89ZM124,-14.49L124,-15.69L124.8,-15.69L124.8,-14.49L124,-14.49ZM124,-12.08L124,-13.28L124.8,-13.28L124.8,-12.08L124,-12.08ZM124,-9.68L124,-10.88L124.8,-10.88L124.8,-9.68L124,-9.68ZM124,-7.27L124,-8.48L124.8,-8.48L124.8,-7.27L124,-7.27ZM124,-4.87L124,-6.07L124.8,-6.07L124.8,-4.87L124,-4.87ZM124,-2.47L124,-3.67L124.8,-3.67L124.8,-2.47L124,-2.47ZM124,-0.06L124,-1.26L124.8,-1.26L124.8,-0.06L124,-0.06ZM124,2.34L124,1.14L124.8,1.14L124.8,2.34L124,2.34ZM124,4.75L124,3.54L124.8,3.54L124.8,4.75L124,4.75ZM124,7.15L124,5.95L124.8,5.95L124.8,7.15L124,7.15ZM124,9.55L124,8.35L124.8,8.35L124.8,9.55L124,9.55ZM124,11.96L124,10.76L124.8,10.76L124.8,11.96L124,11.96ZM124,14.36L124,13.16L124.8,13.16L124.8,14.36L124,14.36ZM124,16.77L124,15.56L124.8,15.56L124.8,16.77L124,16.77ZM124,19.17L124,17.97L124.8,17.97L124.8,19.17L124,19.17ZM124,21.57L124,20.37L124.8,20.37L124.8,21.57L124,21.57ZM124,23.98L124,22.78L124.8,22.78L124.8,23.98L124,23.98ZM124,26.38L124,25.18L124.8,25.18L124.8,26.38L124,26.38ZM124,28.79L124,27.58L124.8,27.58L124.8,28.79L124,28.79ZM124,31.19L124,29.99L124.8,29.99L124.8,31.19L124,31.19ZM124,33.6L124,32.39L124.8,32.39L124.8,33.6L124,33.6ZM124,36L124,34.8L124.8,34.8L124.8,36L124,36ZM124,38.4L124,37.2L124.8,37.2L124.8,38.4L124,38.4ZM124,40.81L124,39.61L124.8,39.61L124.8,40.81L124,40.81ZM124,43.21L124,42.01L124.8,42.01L124.8,43.21L124,43.21ZM124,45.62L124,44.41L124.8,44.41L124.8,45.62L124,45.62ZM124,48.02L124,46.82L124.8,46.82L124.8,48.02L124,48.02ZM124,50.42L124,49.22L124.8,49.22L124.8,50.42L124,50.42ZM124,52.83L124,51.63L124.8,51.63L124.8,52.83L124,52.83ZM124,55.23L124,54.03L124.8,54.03L124.8,55.23L124,55.23ZM124,57.64L124,56.43L124.8,56.43L124.8,57.64L124,57.64ZM124,60.04L124,58.84L124.8,58.84L124.8,60.04L124,60.04ZM124,62.44L124,61.24L124.8,61.24L124.8,62.44L124,62.44ZM124,64.85L124,63.65L124.8,63.65L124.8,64.85L124,64.85ZM124,67.25L124,66.05L124.8,66.05L124.8,67.25L124,67.25ZM124,69.66L124,68.45L124.8,68.45L124.8,69.66L124,69.66ZM124,72.06L124,70.86L124.8,70.86L124.8,72.06L124,72.06ZM124,74.46L124,73.26L124.8,73.26L124.8,74.46L124,74.46ZM124,76.87L124,75.67L124.8,75.67L124.8,76.87L124,76.87ZM124,79.27L124,78.07L124.8,78.07L124.8,79.27L124,79.27ZM124,81.68L124,80.47L124.8,80.47L124.8,81.68L124,81.68ZM124,84.08L124,82.88L124.8,82.88L124.8,84.08L124,84.08ZM124,86.48L124,85.28L124.8,85.28L124.8,86.48L124,86.48ZM124,88.89L124,87.69L124.8,87.69L124.8,88.89L124,88.89ZM124,91.29L124,90.09L124.8,90.09L124.8,91.29L124,91.29ZM124,93.7L124,92.49L124.8,92.49L124.8,93.7L124,93.7ZM124,96.1L124,94.9L124.8,94.9L124.8,96.1L124,96.1ZM124,98.5L124,97.3L124.8,97.3L124.8,98.5L124,98.5ZM124,100.31L124,99.71L124.8,99.71L124.8,100.31L124,100.31Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="123.5"
            android:startY="-17.49"
            android:endX="123.5"
            android:endY="100.31"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.19" android:color="#FF36393B"/>
          <item android:offset="0.63" android:color="#FF36393B"/>
          <item android:offset="0.9" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M286.6,99.91L286.6,18.91L287.4,18.91L287.4,99.91L286.6,99.91Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M258.66,50.11L278.8,88.51H238.6L258.66,50.11Z"
        android:fillColor="#2A2C2D"/>
    <path
        android:pathData="M260.49,47.89L252.76,62.78L252.04,62.41L259.78,47.52L260.49,47.89Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="260.58"
            android:startY="47.94"
            android:endX="252.84"
            android:endY="62.82"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.16" android:color="#FF36393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M257.31,46.62L280.82,91.39L280.11,91.77L256.6,47L257.31,46.62Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="257.39"
            android:startY="46.58"
            android:endX="280.91"
            android:endY="91.35"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.07" android:color="#FF36393B"/>
          <item android:offset="0.92" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M238.6,36.01L265.9,88.21H211.41L238.6,36.01Z"
        android:fillColor="#2E3031"/>
    <path
        android:pathData="M283,88.51L206.8,88.51L206.8,87.71L283,87.71L283,88.51Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="206.8"
            android:startY="89.01"
            android:endX="283"
            android:endY="89.01"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.06" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M237.19,31.92L268,91.32L267.29,91.69L236.48,32.29L237.19,31.92Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="237.28"
            android:startY="31.88"
            android:endX="268.09"
            android:endY="91.28"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M239.69,32.52L208.88,91.92L209.59,92.29L240.4,32.89L239.69,32.52Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="239.6"
            android:startY="32.48"
            android:endX="208.79"
            android:endY="91.88"
            android:type="linear">
          <item android:offset="0" android:color="#0036393B"/>
          <item android:offset="0.1" android:color="#FF36393B"/>
          <item android:offset="0.94" android:color="#FF36393B"/>
          <item android:offset="1" android:color="#0036393B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M266.8,45.11C270.67,45.11 273.8,41.98 273.8,38.11C273.8,34.24 270.67,31.11 266.8,31.11C262.93,31.11 259.8,34.24 259.8,38.11C259.8,41.98 262.93,45.11 266.8,45.11ZM266.8,45.91C271.11,45.91 274.6,42.42 274.6,38.11C274.6,33.8 271.11,30.31 266.8,30.31C262.49,30.31 259,33.8 259,38.11C259,42.42 262.49,45.91 266.8,45.91Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M206.8,19.21C206.8,20.04 206.13,20.71 205.3,20.71C204.47,20.71 203.8,20.04 203.8,19.21C203.8,18.38 204.47,17.71 205.3,17.71C206.13,17.71 206.8,18.38 206.8,19.21Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M205.3,20.11C205.8,20.11 206.2,19.71 206.2,19.21C206.2,18.71 205.8,18.31 205.3,18.31C204.8,18.31 204.4,18.71 204.4,19.21C204.4,19.71 204.8,20.11 205.3,20.11ZM205.3,20.71C206.13,20.71 206.8,20.04 206.8,19.21C206.8,18.38 206.13,17.71 205.3,17.71C204.47,17.71 203.8,18.38 203.8,19.21C203.8,20.04 204.47,20.71 205.3,20.71Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M206.8,58.81C206.8,59.64 206.13,60.31 205.3,60.31C204.47,60.31 203.8,59.64 203.8,58.81C203.8,57.98 204.47,57.31 205.3,57.31C206.13,57.31 206.8,57.98 206.8,58.81Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M205.3,59.71C205.8,59.71 206.2,59.3 206.2,58.81C206.2,58.31 205.8,57.91 205.3,57.91C204.8,57.91 204.4,58.31 204.4,58.81C204.4,59.3 204.8,59.71 205.3,59.71ZM205.3,60.31C206.13,60.31 206.8,59.64 206.8,58.81C206.8,57.98 206.13,57.31 205.3,57.31C204.47,57.31 203.8,57.98 203.8,58.81C203.8,59.64 204.47,60.31 205.3,60.31Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M206.8,100.21C206.8,101.04 206.13,101.71 205.3,101.71C204.47,101.71 203.8,101.04 203.8,100.21C203.8,99.38 204.47,98.71 205.3,98.71C206.13,98.71 206.8,99.38 206.8,100.21Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M205.3,101.11C205.8,101.11 206.2,100.71 206.2,100.21C206.2,99.71 205.8,99.31 205.3,99.31C204.8,99.31 204.4,99.71 204.4,100.21C204.4,100.71 204.8,101.11 205.3,101.11ZM205.3,101.71C206.13,101.71 206.8,101.04 206.8,100.21C206.8,99.38 206.13,98.71 205.3,98.71C204.47,98.71 203.8,99.38 203.8,100.21C203.8,101.04 204.47,101.71 205.3,101.71Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M247.6,100.21C247.6,101.04 246.93,101.71 246.1,101.71C245.27,101.71 244.6,101.04 244.6,100.21C244.6,99.38 245.27,98.71 246.1,98.71C246.93,98.71 247.6,99.38 247.6,100.21Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M246.1,101.11C246.6,101.11 247,100.71 247,100.21C247,99.71 246.6,99.31 246.1,99.31C245.6,99.31 245.2,99.71 245.2,100.21C245.2,100.71 245.6,101.11 246.1,101.11ZM246.1,101.71C246.93,101.71 247.6,101.04 247.6,100.21C247.6,99.38 246.93,98.71 246.1,98.71C245.27,98.71 244.6,99.38 244.6,100.21C244.6,101.04 245.27,101.71 246.1,101.71Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M247.6,19.21C247.6,20.04 246.93,20.71 246.1,20.71C245.27,20.71 244.6,20.04 244.6,19.21C244.6,18.38 245.27,17.71 246.1,17.71C246.93,17.71 247.6,18.38 247.6,19.21Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M246.1,20.11C246.6,20.11 247,19.71 247,19.21C247,18.71 246.6,18.31 246.1,18.31C245.6,18.31 245.2,18.71 245.2,19.21C245.2,19.71 245.6,20.11 246.1,20.11ZM246.1,20.71C246.93,20.71 247.6,20.04 247.6,19.21C247.6,18.38 246.93,17.71 246.1,17.71C245.27,17.71 244.6,18.38 244.6,19.21C244.6,20.04 245.27,20.71 246.1,20.71Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M288.4,19.21C288.4,20.04 287.73,20.71 286.9,20.71C286.07,20.71 285.4,20.04 285.4,19.21C285.4,18.38 286.07,17.71 286.9,17.71C287.73,17.71 288.4,18.38 288.4,19.21Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M286.9,20.11C287.4,20.11 287.8,19.71 287.8,19.21C287.8,18.71 287.4,18.31 286.9,18.31C286.4,18.31 286,18.71 286,19.21C286,19.71 286.4,20.11 286.9,20.11ZM286.9,20.71C287.73,20.71 288.4,20.04 288.4,19.21C288.4,18.38 287.73,17.71 286.9,17.71C286.07,17.71 285.4,18.38 285.4,19.21C285.4,20.04 286.07,20.71 286.9,20.71Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M288.4,58.81C288.4,59.64 287.73,60.31 286.9,60.31C286.07,60.31 285.4,59.64 285.4,58.81C285.4,57.98 286.07,57.31 286.9,57.31C287.73,57.31 288.4,57.98 288.4,58.81Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M286.9,59.71C287.4,59.71 287.8,59.3 287.8,58.81C287.8,58.31 287.4,57.91 286.9,57.91C286.4,57.91 286,58.31 286,58.81C286,59.3 286.4,59.71 286.9,59.71ZM286.9,60.31C287.73,60.31 288.4,59.64 288.4,58.81C288.4,57.98 287.73,57.31 286.9,57.31C286.07,57.31 285.4,57.98 285.4,58.81C285.4,59.64 286.07,60.31 286.9,60.31Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M288.4,100.21C288.4,101.04 287.73,101.71 286.9,101.71C286.07,101.71 285.4,101.04 285.4,100.21C285.4,99.38 286.07,98.71 286.9,98.71C287.73,98.71 288.4,99.38 288.4,100.21Z"
        android:fillColor="#272728"/>
    <path
        android:pathData="M286.9,101.11C287.4,101.11 287.8,100.71 287.8,100.21C287.8,99.71 287.4,99.31 286.9,99.31C286.4,99.31 286,99.71 286,100.21C286,100.71 286.4,101.11 286.9,101.11ZM286.9,101.71C287.73,101.71 288.4,101.04 288.4,100.21C288.4,99.38 287.73,98.71 286.9,98.71C286.07,98.71 285.4,99.38 285.4,100.21C285.4,101.04 286.07,101.71 286.9,101.71Z"
        android:fillColor="#36393B"
        android:fillType="evenOdd"/>
  </group>
</vector>
