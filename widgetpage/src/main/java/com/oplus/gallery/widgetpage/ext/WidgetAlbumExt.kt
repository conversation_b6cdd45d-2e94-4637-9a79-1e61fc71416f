/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetAlbumExt.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/1/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/1/17        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ext

import android.content.res.Resources
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.business_lib.model.data.local.set.WidgetAlbum
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns
import com.oplus.gallery.foundation.util.collections.ExtraMap
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.widgetpage.R
import com.oplus.gallery.widgetpage.track.WidgetTrackConstant

fun WidgetAlbum.getWidgetInfo(): ExtraMap {
    return ExtraMap().apply {
        put(IntentConstant.WidgetConstant.KEY_WIDGET_CODE, widgetCode)
        put(IntentConstant.WidgetConstant.KEY_DISPLAY_LIST_ID, getDisplayListId())
        put(IntentConstant.WidgetConstant.KEY_WIDGET_MODE, mode)
        put(IntentConstant.WidgetConstant.KEY_TEXT_ID_OF_REMOVE_FROM_LIST_MENU, getTextOfRemoveFromListMenu(mode))
        put(IntentConstant.WidgetConstant.KEY_TOAST_TEXT_ID_AFTER_ALL_REMOVED, getToastAfterAllRemoved(mode))
        put(IntentConstant.WidgetConstant.KEY_WIDGET_TRACK_MODE_NAME, getTrackModeName(mode))
    }
}

private fun getTextOfRemoveFromListMenu(mode: Int): Int {
    return configMap[mode]?.textResIdOfRemoveFromListMenu ?: Resources.ID_NULL
}

private fun getToastAfterAllRemoved(mode: Int): Int {
    return configMap[mode]?.toastResIdAfterAllRemoved ?: Resources.ID_NULL
}

private fun getTrackModeName(mode: Int): String {
    return configMap[mode]?.trackModeName ?: TextUtil.EMPTY_STRING
}

private val configMap by lazy {
    mapOf(
        WidgetSetColumns.MODE_RECOMMENDED to Config.RECOMMENDED,
        WidgetSetColumns.MODE_CUSTOM to Config.CUSTOM
    )
}

private enum class Config(
    val textResIdOfRemoveFromListMenu: Int,
    val toastResIdAfterAllRemoved: Int,
    val trackModeName: String
) {
    RECOMMENDED(
        R.string.widget_recommended_moved_out,
        R.string.widget_all_recommended_removed,
        WidgetTrackConstant.Value.MODE_RECOMMENDED
    ),
    CUSTOM(
        R.string.widget_custom_moved_out,
        R.string.widget_all_custom_removed,
        WidgetTrackConstant.Value.MODE_CUSTOM
    )
}