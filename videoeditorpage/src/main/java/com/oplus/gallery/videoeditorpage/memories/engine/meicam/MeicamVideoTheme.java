/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MeicamVideoTheme.java
 * * Description: theme for meicam video
 * * Version: 1.0
 * * Date : 2018/06/02
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2018/06/02    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam;


import android.text.TextUtils;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsAudioTrack;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoTheme;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.LocalSourceManager;
import com.oplus.gallery.videoeditorpage.memories.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;

import java.util.ArrayList;

public class MeicamVideoTheme implements IGalleryVideoTheme<ThemeItem> {
    private static final String TAG = "MeicamVideoTheme";

    public static final String FILTER_FOLDER = "assets:/filter/";
    public static final String FILTER_FILE = ".videofx";
    public static final String LIC_FILE = ".lic";
    public static final String VIDEO_THEME_NONE = "none";

    private ThemeItem mCurTheme = null;
    private boolean mIsThemeVolumeMuted = false;
    private String mPrevVideoFilter = null;

    private NvsTimeline mTimeline;
    private NvsStreamingContext mStreamingContext;

    private ArrayList<String> mThemeList = new ArrayList<>();
    private ArrayList<String> mFilterList = new ArrayList<>();

    public MeicamVideoTheme(NvsStreamingContext streamingContext) {
        mStreamingContext = streamingContext;
        mCurTheme = ThemeSourceManager.getInstance().getNoneThemeItem();
//        mThemeList.add(VIDEO_THEME_NONE);
    }

    public void setTimeline(NvsTimeline timeline) {
        mTimeline = timeline;
        GLog.d(TAG, "setTimeline videoTrackCount:" + mTimeline.videoTrackCount());
    }

    private void installVideoThemeRes(String name, String lic, StringBuilder themeBuilder) {
        NvsAssetPackageManager packageManager = mStreamingContext.getAssetPackageManager();
        if (packageManager != null) {
            int result = packageManager.installAssetPackage(name, lic,
                    NvsAssetPackageManager.ASSET_PACKAGE_TYPE_THEME, false, themeBuilder);
            if (result == NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED) {
                GLog.d(TAG, "installVideoThemeRes(), theme already installed, need update");
                int newThemeVersion = packageManager.getAssetPackageVersionFromAssetPackageFilePath(name);
                String themeID = packageManager.getAssetPackageIdFromAssetPackageFilePath(name);
                int oldThemeVersion = packageManager.getAssetPackageVersion(themeID, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX);
                if (newThemeVersion > oldThemeVersion) {
                    GLog.d(TAG, "installVideoThemeRes(), oldThemeVersion = " + oldThemeVersion + ", newThemeVersion = " + newThemeVersion);
                    result = packageManager.upgradeAssetPackage(name, lic,
                            NvsAssetPackageManager.ASSET_PACKAGE_TYPE_THEME, false, themeBuilder);
                }
            } else {
                GLog.e(TAG, "installVideoThemeRes() Failed! packageManager is null");
            }
            GLog.d(TAG, "installVideoThemeRes(), result:" + result);
        }
    }

    @Override
    public void installVideoThemeRes(final String[] themeList, final String[] licList) {
        StringBuilder themeBuilder = new StringBuilder();
        mThemeList.clear();
        long time = System.currentTimeMillis();
        if ((themeList != null) && (themeList.length > 0)
                && (licList != null) && (themeList.length == licList.length)) {
            int index = 0;
            for (String name : themeList) {
                String lic = licList[index];
                GLog.d(TAG, "installVideoThemeRes(), theme file:" + name + ", licFile: " + lic);
                installVideoThemeRes(name, lic, themeBuilder);
                mThemeList.add(themeBuilder.toString());
                GLog.d(TAG, "installVideoThemeRes, get themeId: " + mThemeList.get(index));
                index++;
            }
        }
        GLog.d(TAG, "installVideoThemeRes() costs:" + (System.currentTimeMillis() - time));
    }

    @Override
    public void installVideoThemeFilterRes(final String[] filterList, final String[] licList) {
        StringBuilder filterBuilder = new StringBuilder();
        long time = System.currentTimeMillis();
        if ((filterList != null) && (filterList.length > 0)
                && (licList != null) && (filterList.length == licList.length)) {
            int index = 0;
            for (String name : filterList) {
                GLog.d(TAG, "installVideoThemeFilterRes(), videoFx file:" + name);
                if (TextUtils.isEmpty(name) || VIDEO_THEME_NONE.equalsIgnoreCase(name)) {
                    mFilterList.add(name);
                    index++;
                    continue;
                }
                StringBuilder fxBuilder = new StringBuilder(FILTER_FOLDER);
                StringBuilder licBuilder = new StringBuilder(FILTER_FOLDER);
                fxBuilder.append(name);
                fxBuilder.append(FILTER_FILE);
                licBuilder.append(licList[index]);
                licBuilder.append(LIC_FILE);
                NvsAssetPackageManager packageManager = mStreamingContext.getAssetPackageManager();
                if (packageManager != null) {
                    int result = packageManager.installAssetPackage(fxBuilder.toString(),
                            licBuilder.toString(), NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX, false, filterBuilder);
                    if (result == NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED) {
                        GLog.d(TAG, "installVideoThemeFilterRes(), videoFx already installed, need update");
                        int newFilterVersion = packageManager.getAssetPackageVersionFromAssetPackageFilePath(fxBuilder.toString());
                        String filterID = packageManager.getAssetPackageIdFromAssetPackageFilePath(fxBuilder.toString());
                        int oldFilterVersion = packageManager.getAssetPackageVersion(filterID, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX);
                        if (newFilterVersion > oldFilterVersion) {
                            GLog.d(TAG, "installVideoThemeFilterRes(), oldFilterVersion = " + oldFilterVersion + ", newFilterVersion = " + newFilterVersion);
                            result = packageManager.upgradeAssetPackage(fxBuilder.toString(),
                                    licBuilder.toString(), NvsAssetPackageManager.ASSET_PACKAGE_TYPE_VIDEOFX, false, filterBuilder);
                        }
                    }
                    GLog.d(TAG, "installVideoThemeFilterRes(), result:" + result);
                } else {
                    GLog.e(TAG, "installVideoThemeFilterRes() Failed! packageManager is null");
                }
                GLog.d(TAG, "installVideoThemeFilterRes(), filterBuilder.toString():" + filterBuilder.toString());
                mFilterList.add(filterBuilder.toString());
                index++;
            }
        }
        GLog.d(TAG, "installVideoThemeFilterRes() costs:" + (System.currentTimeMillis() - time));
    }


    @Override
    public boolean addVideoTheme(ThemeItem theme, int pos) {
        if (mTimeline == null) {
            GLog.w(TAG, "addVideoTheme mTimeline is null.");
            return false;
        }
        if ((theme != null) && theme.equals(mCurTheme)) {
            GLog.d(TAG, "addVideoTheme position do not change");
            return false;
        }
        mCurTheme = theme;
        if (theme != null) {
            if ((pos >= 0) && (pos < mThemeList.size())) {
                return addVideoTheme(mThemeList.get(pos));
            }
        }
        return false;
    }

    @Override
    public void installDownloadTheme(int pos, ThemeItem themeItem) {
        StringBuilder themeBuilder = new StringBuilder();
        installVideoThemeRes(ThemeSourceManager.getVideoThemeFile(themeItem), ThemeSourceManager.getVideoThemeLic(themeItem), themeBuilder);
        mThemeList.set(pos, themeBuilder.toString());
    }

    @Override
    public boolean addVideoTheme(String themeId) {
        long time = System.currentTimeMillis();
        GLog.d(TAG, "addVideoTheme, themeId = " + themeId);
        if (mTimeline == null) {
            GLog.w(TAG, "addVideoTheme mTimeline is null.");
            return false;
        }
        boolean result = false;
        try {
            int mCurThemePos = (mThemeList.indexOf(themeId) > 0) ? mThemeList.indexOf(themeId) : 0;
            if (TextUtils.isEmpty(themeId) || VIDEO_THEME_NONE.equalsIgnoreCase(themeId)) {
                removeVideoTheme();
                if (!mFilterList.contains(mPrevVideoFilter)) {
                    GLog.d(TAG, "addVideoTheme, theme none, add prev filter = " + mPrevVideoFilter);
                    applyVideoThemeFilter(mPrevVideoFilter);
                }
                GLog.d(TAG, "addVideoTheme.removeVideoTheme, themeId = " + themeId);
                return true;
            }
            GLog.d(TAG, "addVideoTheme.applyTheme, mCurTheme = " + mCurTheme.getEnName() + ", themeId = " + themeId);
            result = mTimeline.applyTheme(themeId);
            if (result) {
                openThemeVolume(true);
                mTimeline.setAudioFadeOutDuration(MeicamAudioClip.DEFAULT_MUSIC_FADE_TIME);
                // TODO: 2019/7/30 get filter
                if ((mCurThemePos > 0) && ((mCurThemePos - 1) < mFilterList.size())) {
                    String filter = mFilterList.get(mCurThemePos - 1);
                    GLog.d(TAG, "addVideoTheme, filter = " + filter);
                    if (!TextUtils.isEmpty(filter) && !VIDEO_THEME_NONE.equalsIgnoreCase(filter)) {
                        applyVideoThemeFilter(filter);
                    } else {
                        removeVideoThemeFilter();
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "addVideoTheme themeId:" + themeId + ", e:", e);
        }

        GLog.d(TAG, "addVideoTheme themeId:" + themeId + ", result:" + result + ", cost time:" + (System.currentTimeMillis() - time));
        return result;
    }

    @Override
    public void removeVideoTheme() {
        GLog.d(TAG, "removeCurrentVideoTheme");
        if (mTimeline == null) {
            GLog.w(TAG, "removeVideoTheme mTimeline is null.");
            return;
        }
        try {
            mTimeline.removeCurrentTheme();
            mTimeline.setAudioFadeOutDuration(0);
            openThemeVolume(false);
            removeVideoThemeFilter();
            mCurTheme = ThemeSourceManager.getInstance().getNoneThemeItem();
        } catch (Exception e) {
            GLog.e(TAG, "removeCurrentVideoTheme e:", e);
        }
    }

    @Override
    public ThemeItem getCurrentVideoThemePos() {
        GLog.d(TAG, "getCurrentVideoThemePos mCurTheme = " + mCurTheme);
        return mCurTheme;
    }

    @Override
    public String getCurrentVideoTheme() {
        if (mTimeline == null) {
            GLog.w(TAG, "getCurrentVideoTheme mTimeline is null.");
            return null;
        }
        String themeId = null;
        try {
            themeId = mTimeline.getCurrentThemeId();
        } catch (Exception e) {
            GLog.e(TAG, "getCurrentVideoTheme e:", e);
        }

        GLog.d(TAG, "getCurrentVideoTheme themeId = " + themeId);
        return themeId;
    }

    @Override
    public void saveVideoThemeState() {
        if (mTimeline != null) {
            float leftVolume = mTimeline.getThemeMusicVolumeGain().leftVolume;
            float rightVolume = mTimeline.getThemeMusicVolumeGain().rightVolume;
            mIsThemeVolumeMuted = (Float.compare(leftVolume, 0f) == 0)
                    && (Float.compare(rightVolume, 0f) == 0);
            GLog.d(TAG, "saveVideoThemeState, leftVolume:" + leftVolume
                    + ", rightVolume:" + rightVolume
                    + ", mIsThemeVolumeMuted:" + mIsThemeVolumeMuted);
            NvsVideoTrack track = mTimeline.getVideoTrackByIndex(0);
            if ((track != null) && (track.getClipCount() > 0)) {
                NvsVideoFx videoFx = track.getClipByIndex(0).getFxByIndex(0);
                if (videoFx != null) {
                    mPrevVideoFilter = videoFx.getVideoFxPackageId();
                    GLog.d(TAG, "saveVideoThemeState, mPrevVideoFilter:" + mPrevVideoFilter);
                }
            }
        }
    }

    @Override
    public void resetVideoThemeState() {
        GLog.d(TAG, "resetVideoThemeState, mCurTheme:" + mCurTheme
                + ", volumeMuted:" + mIsThemeVolumeMuted
                + ", prevFilter:" + mPrevVideoFilter);
        openThemeVolume(!mIsThemeVolumeMuted);
        if (!TextUtils.isEmpty(mPrevVideoFilter)) {
            applyVideoThemeFilter(mPrevVideoFilter);
        }
    }

    @Override
    public void setThemeMusicMute(boolean isMute) {
        openThemeVolume(!isMute);
    }

    public boolean isThemeChanged() {
        return (mCurTheme != null) && !LocalSourceManager.NONE_ITEM_THUMBNAIL.equals(mCurTheme.getThumbnailPath());
    }

    private boolean applyVideoThemeFilter(String filter) {
        if (mTimeline == null) {
            GLog.w(TAG, "applyVideoThemeFilter mTimeline is null.");
            return false;
        }
        if (TextUtils.isEmpty(filter)) {
            GLog.w(TAG, "applyVideoFilter filter is null.");
            return false;
        }

        NvsVideoTrack track = mTimeline.getVideoTrackByIndex(0);
        if (track == null) {
            GLog.w(TAG, "applyVideoThemeFilter track is null!");
            return false;
        }

        int clipCount = track.getClipCount();
        if (clipCount <= 0) {
            GLog.d(TAG, "applyVideoThemeFilter clip count is 0");
            return false;
        }

        GLog.d(TAG, "applyVideoThemeFilter filter = " + filter);
        for (int i = 0; i < clipCount; i++) {
            NvsVideoClip clip = track.getClipByIndex(i);
            if (clip == null) {
                continue;
            }
            clip.removeAllFx();
            clip.appendPackagedFx(filter);
        }
        return true;
    }

    public void removeVideoThemeFilter() {
        if (mTimeline == null) {
            GLog.w(TAG, "removeVideoThemeFilter mTimeline is null.");
            return;
        }

        NvsVideoTrack track = mTimeline.getVideoTrackByIndex(0);
        if (track == null) {
            GLog.w(TAG, "removeVideoThemeFilter track is null!");
            return;
        }

        int clipCount = track.getClipCount();
        if (clipCount <= 0) {
            GLog.d(TAG, "removeVideoThemeFilter clip count is 0");
            return;
        }
        for (int i = 0; i < track.getClipCount(); i++) {
            NvsVideoClip clip = track.getClipByIndex(i);
            if (clip == null) {
                continue;
            }
            clip.removeAllFx();
        }
    }

    private void openThemeVolume(boolean open) {
        if (mTimeline != null) {
            GLog.d(TAG, "openThemeVolume open:" + open);
            float volume = open ? 1f : 0f;
            mTimeline.setThemeMusicVolumeGain(volume, volume);
            openAudioVolume(!open);
        }
    }

    private void openAudioVolume(boolean open) {
        if (mTimeline != null) {
            NvsAudioTrack audioTrack = mTimeline.getAudioTrackByIndex(0);
            if (audioTrack != null) {
                GLog.d(TAG, "openAudioVolume open:" + open);
                float volume = open ? 1f : 0f;
                audioTrack.setVolumeGain(volume, volume);
            }
        }
    }
}
