/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - IBaseClip.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base

/**
 * 视频、音频、特效都有开始和结束时间
 * 抽象出一个接口方便统一管理特效
 */
interface IBaseClip {
    /**
     * 开始时间
     */
    fun getInPoint(): Long

    /**
     * 结束时间
     */
    fun getOutPoint(): Long

    /**
     * 设置开始时间
     */
    fun setInTime(inPoint: Long)

    /**
     * 设置结束时间
     */
    fun setOutTime(outPoint: Long)

    /**
     * 轨道索引
     */
    fun getTrackIndex(): Int

    /**
     * 设置轨道索引
     */
    fun setTrackIndex(trackIndex: Int)
}