/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PhotoOliveSection.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/02/01
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/02/01      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.olive

import android.app.Activity
import android.graphics.drawable.Drawable
import android.view.MotionEvent
import android.view.View
import androidx.annotation.MainThread
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.coui.appcompat.snackbar.COUISnackBar
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business.renderer.brighten.IPageOliveRenderUpdater
import com.oplus.gallery.business.renderer.brighten.IPageRenderPresentationVisibilityUpdater
import com.oplus.gallery.business.renderer.IPhotoSlotStateOperator
import com.oplus.gallery.business.renderer.olive.OLiveRenderer
import com.oplus.gallery.business.renderer.olive.OLiveState
import com.oplus.gallery.foundation.arch.sectionpage.ISectionPage
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.collect
import com.oplus.gallery.foundation.util.ext.collectNotNull
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photo_page.R
import com.oplus.gallery.photo_page.ui.AbstractPhotoSection
import com.oplus.gallery.photo_page.ui.PhotoFragment
import com.oplus.gallery.photo_page.ui.section.photopagersection.PhotoPagerSection
import com.oplus.gallery.photo_page.viewmodel.PhotoViewModel
import com.oplus.gallery.photo_page.viewmodel.dataloading.DiffedPhotoItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusItemViewData
import com.oplus.gallery.photo_page.viewmodel.dataloading.focusSlot
import com.oplus.gallery.photo_page.viewmodel.funcRecommend.VisibleFrom.VISIBLE_FROM_OLIVE
import com.oplus.gallery.photo_page.viewmodel.olive.OliveScrollPlayableState
import com.oplus.gallery.photo_page.viewmodel.olive.PhotoOliveViewModel
import com.oplus.gallery.photo_page.viewmodel.olive.PhotoOliveViewModel.Companion.isOLive
import com.oplus.gallery.photo_page.viewmodel.olive.PhotoOliveViewModel.OLiveLongPressConditions.READY
import com.oplus.gallery.photo_page.viewmodel.olive.PhotoOliveViewModel.OLiveLongPressConditions.SLIMMING_FILE
import com.oplus.gallery.photo_page.viewmodel.track.producttracker.tracker.OLivePlayType
import com.oplus.gallery.photopager.PhotoPager
import com.oplus.gallery.photopager.PhotoSlot
import com.oplus.gallery.photopager.renderer.DrawableRenderer
import com.oplus.gallery.photopager.viewpager.widget.ViewPager
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

/**
 * Olive切片
 * 需要接收和处理用户长按相册播放的行为，包括播放视频以及下载原图
 */
internal class PhotoOliveSection(
    sectionPage: ISectionPage<PhotoFragment, PhotoViewModel>,
) : AbstractPhotoSection(sectionPage) {

    private val pagerSection: PhotoPagerSection? get() = sectionPage.pageInstance.requireSection()

    private val activity: Activity?
        get() = sectionPage.pageContext as? BaseActivity
    private var contentView: View? = null

    /**
     * [ISettingsAbility]配置能力的对象，避免频繁创建
     */
    private val settingsAbility: ISettingsAbility? by lazy {
        context?.getAppAbility<ISettingsAbility>().apply {
            if (this == null) {
                GLog.i(TAG, LogFlag.DL) { "[settingsAbility]init settingsAbility failed , settingsAbility is null" }
            }
        }
    }

    private var isLongPressed: Boolean = false
    /**
     * tips提示弹窗
     */
    private var oliveTipsSnackBar: COUISnackBar? = null

    /**
     * 点击开启按钮监听
     */
    private val openSnackBarClickListener = View.OnClickListener {
        viewModel.olive.setOliveStatusChange(true, sectionPage.pageInstance) {
            viewModel.pageManagement.changeImmersionInteractive(false)
        }
        oliveTipsSnackBar?.dismiss()
    }

    /**
     * tips提示文案
     */
    private val oliveCloseTextTip: String by lazy {
        context?.resources?.getString(R.string.photopage_olive_photo_closed_Tips) ?: TextUtil.EMPTY_STRING
    }

    /**
     * 手指是否已经离开屏幕
     *
     * 之所以添加这个变量，是因为存在这种场景：
     * 前提-点开一张带有抠图主体的Olive资源
     * 短长按抠图出现后，由于抠图结果出现了，所以Olive的系统长按不响应；
     * 当抠图继续长按约400ms后（系统长按时长）松手，此时抠图post一个LiveData出去通知抠图显示之后继续长按了。
     * 由于post的并没有立即通知而是下一帧，这就导致系统松手的UP先执行，然后才收到通知lnSLongPressedAfterShown。
     * UP时由于还未长按，所以isLongPressed为false，导致uninterruptedSlotTouchListener中不会停止播放；
     * 收到通知时，去执行长按播放逻辑————这就的导致了抠图一会儿播放Olive后松手，资源没有停止播放。
     *
     * 添加该变量后，可以判断如果手指已经离开屏幕，则即使收到通知也不执行长按播放逻辑
     */
    private var isFingerLeft = true

    /**
     * 长按监听器
     *
     * - 1.处于 分屏、浮窗、互联 场景时，PhotoPagerSection会拦截掉长按事件，所以事件不会分发给当前Section
     * - 2.处于 超级文本编辑界面 如果长按了，那么PhotoPagerSection也收不到长按事件，所以也分发不到当前Section
     * - 3.处于 抠图 如果长按了，那么PhotoPagerSection也收不到长按事件，所以也分发不到当前Section————Marked by dandy 这里需要单独实现与抠图的交互
     * - 4.如果是不支持Olive的资源，则不会注册该监听
     */
    private val slotLongPressListener = { slot: Int ->
        GLog.d(TAG) { "<slotLongPressListener> slot=$slot" }
        handleLongPress(false)
    }

    /**
     * 不拦截大图触摸事件的监听
     * 只接受事件，不拦截事件
     */
    private val uninterruptedSlotTouchListener = { slot: Int, event: MotionEvent ->
        if (event.action == MotionEvent.ACTION_DOWN) {
            isLongPressed = false
            isFingerLeft = false
            viewModel.olive.notifyLongPressHandling(isLongPressed)
        }
        if ((event.action == MotionEvent.ACTION_UP) || (event.action == MotionEvent.ACTION_CANCEL)) {
            isFingerLeft = true
            // 松手后，停止播放
            if (isLongPressed) {
                viewModel.olive.stopPlayOLive()
                GLog.d(TAG) { "uninterruptedSlotTouchListener slot=$slot action_up, stop play olive." }
            }
            isLongPressed = false
            viewModel.olive.notifyLongPressHandling(isLongPressed)
        }
    }

    private val onPageChangeCallback = object : ViewPager.OnPageChangeCallback() {
        private var pageScrollState: Int = PhotoPager.SCROLL_STATE_IDLE
        override fun onPageSelected(position: Int) {
            if (GProperty.DEBUG_OLIVE_PHOTO == GProperty.FEATURE_FORCE_ENABLE) {
                GLog.d(TAG) { "<onPageChangeCallback> position=$position pageScrollState=$pageScrollState" }
            }
            val isSelectFromUserScroll = pageScrollState != PhotoPager.SCROLL_STATE_IDLE
            /**
             * 当非用户滑动场景(缩图轴切换等)，render焦点确定后，第一时间（一帧内）识别是否需要显示封面
             * 规避bug 8530257，在不支持滑动播放olive，非滑动大图浏览图片场景（点击缩图轴切换等），因大图状态机攀升慢导致的闪（1-3帧）新焦点图视频界面问题
             */
            if (isSelectFromUserScroll.not()) {
                invokeSlotRenderer(position) {
                    (it as? IPageOliveRenderUpdater)?.tryUpdateCoverVisibility(View.VISIBLE, false)
                }
            }
            viewModel.olive.updateFocusSlotIsScrollFromViewPager(position, isSelectFromUserScroll)
        }

        override fun onPageScrollStateChanged(state: Int) {
            pageScrollState = state
            when (state) {
                PhotoPager.SCROLL_STATE_IDLE -> {
                    viewModel.olive.focusSlotOLiveState.value.let {
                        tryUpdateFunctionAreaVisibleState(it, reason = "onPageScrollStateChanged")
                    }
                }

                PhotoPager.SCROLL_STATE_DRAGGING -> {
                    /**
                     * Marked by zhangwenming，该地方了逻辑为了规避Olive从编辑回到大图后，此时播放会将旁边的Olive资源透过来的问题。
                     * 在播放当前Olive资源的时候，会将左右两边的Olive内容给设置为不可见，并在滑动的时候再恢复可见。
                     */
                    if (viewModel.olive.shouldSupressOLiveVisibility) {
                        viewModel.olive.updateSupressOLiveVisibilityFlag(false)
                        viewModel.olive.prepareToPlayOLiveResource.removeObserver(prepareToPlayOLiveResourceObserver)
                        val focusSlot = viewModel.dataLoading.focusSlot
                        updateOLivePresentationVisibility(focusSlot + 1, true)
                        updateOLivePresentationVisibility(focusSlot - 1, true)
                    }
                    showOrHideOliveCloseTips(false)
                }

                else -> Unit
            }
        }
    }

    private val prepareToPlayOLiveResourceObserver: Observer<Boolean> by lazy {
        Observer<Boolean> {
            if (viewModel.olive.shouldSupressOLiveVisibility) {
                val focusSlot = viewModel.dataLoading.focusSlot
                updateOLivePresentationVisibility(focusSlot + 1, false)
                updateOLivePresentationVisibility(focusSlot - 1, false)
            }
        }
    }

    /**
     * diffedFocus流：
     * 时机：当入场动画结束 且 滑动idle时
     * 数据：只有当图片是jpeg且是olive图时才下发
     */
    private val diffedFocusJpegOliveFilterFlow by lazy {
        viewModel.dataLoading.diffedFocusViewData.filter { diffed ->
            // 只关注(mimeType=jpeg) and (isOlive)的情况
            MimeTypeUtils.isJpeg(diffed?.newItem?.mimeType)
                    && (diffed?.newItem?.isOLive == true)
        }
    }

    /**
     * diffedFocus流：
     * 数据：只有当图片olive时才下发
     */
    private val diffedFocusIdOliveFilterFlow by lazy {
        viewModel.dataLoading.diffedFocusViewData.filter {
            // 只关注 id不同 且 Olive资源的情况
            (it?.oldItem?.id != it?.newItem?.id) && (it?.newItem.isOLive())
        }
    }

    /**
     * [ensureFocusOlivePresentationVisibilityCollector]对应的协程任务引用
     */
    private var diffedFocusViewDataCollectJob: Job? = null

    /**
     * [diffedFocusIdOliveFilterFlow]对应的收集器
     */
    private val ensureFocusOlivePresentationVisibilityCollector: FlowCollector<DiffedPhotoItemViewData?> by lazy {
        FlowCollector {
            if (viewModel.olive.shouldSupressOLiveVisibility) {
                updateOLivePresentationVisibility(viewModel.dataLoading.focusSlot, true)
            }
        }
    }

    /**
     * diffedFocus流：
     * 数据：当焦点slot变更时下发
     */
    private val diffedFocusSlotFilterFlow by lazy {
        viewModel.dataLoading.diffedFocusViewData.filter {
            (it?.oldItem?.position != it?.newItem?.position)
        }
    }

    /**
     * [diffedFocusSlotFilterFlow]对应的收集器
     */
    private val ensureFocusSlotCollector: FlowCollector<DiffedPhotoItemViewData?> by lazy {
        FlowCollector {
            val newItemViewData = viewModel.dataLoading.diffedFocusViewData.value?.newItem ?: return@FlowCollector
            val forcePageBrightenEnable = newItemViewData.isHdrVideoOlive
            /**
             * 注：此处onPageBrightenEnableEvenInvisibleChanged不一定能下发成功。
             * 可能场景：多个4KHDR的olive滑动，只有焦点能达到C状态。那滑动时新焦点的render还未创建，此处下发不成功。
             * 补救方法：在[contentRendererStatusListener]中监听焦点render创建后，再次尝试下发一次。
             */
            pagerSection?.foreachSlotRenderer {
                (it as? OLiveRenderer)?.onPageBrightenEnableEvenInvisibleChanged(forcePageBrightenEnable)
            }
        }
    }

    /**
     * 监听Slot ContentRender状态。目的：
     *
     * 1. 焦点render在创建后要尝试重新下发强制提亮参数。
     * 原因：[ensureFocusSlotCollector]中下发强制提亮参数时机较早，在只有焦点能达到C状态的情况下，新焦点的render可能还未创建出来。
     *
     * 2. 用于监听，当前 slot 是否是快速跃升到 renderer 状态，
     * 依据：进入 Renderer 状态时是 Focus 且是用户滑动导致的变更。
     */
    private val contentRendererStatusListener: PhotoSlot.ContentRendererStatusListener by lazy {
        PhotoSlot.ContentRendererStatusListener { slot, viewdata, isValidBefore, isValidNow ->
            // 需要是当前 focus 的 slot
            if (slot != currentFocus) return@ContentRendererStatusListener

            pagerSection?.invokeSlotRenderer(slot) {
                // 1. 焦点render创建后尝试重新下发强制提亮参数。前一次下发时可能render还未创建。
                val focusItemViewData = viewModel.dataLoading.focusItemViewData
                focusItemViewData?.let { itemViewData ->
                    (it as? OLiveRenderer)?.onPageBrightenEnableEvenInvisibleChanged(itemViewData.isHdrVideoOlive)
                }

                // 2. 监听slot是否快速升到Render状态。对应的 renderer 需实现 IRapidStateRaiseOperator 接口
                (it as? IPhotoSlotStateOperator)?.let { operator ->
                    // 需刚初始化 ContentRenderer
                    val isSlotValid = !isValidBefore && isValidNow
                    // 需由用户滑动触发
                    val isUserScroll = viewModel.olive.isPhotoSlotChangedFormUserSlide()
                    operator.isRapidStateRaiseByUserScroll = isSlotValid && isUserScroll
                }
            }
        }
    }

    override fun onViewCreated(view: View) {
        super.onViewCreated(view)
        contentView = view
        subscribeFromViewModel()
        pagerSection?.also {
            it.registerOnPageChangeCallback(onPageChangeCallback)
            it.addContentRendererStatusListener(contentRendererStatusListener)
        } ?: let {
            GLog.e(TAG) { "<onViewCreated> why is pagerSection null, no config? CHECK CODE!" }
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        viewModel.olive.notifyWindowFocusChanged(hasFocus)
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
    }

    override fun onResume() {
        super.onResume()
        if (viewModel.olive.shouldSupressOLiveVisibility) {
            viewModel.olive.prepareToPlayOLiveResource.observeForever(prepareToPlayOLiveResourceObserver)
            diffedFocusViewDataCollectJob = viewModel.viewModelScope.launch(Dispatchers.Main.immediate) {
                diffedFocusIdOliveFilterFlow.collectNotNull(ensureFocusOlivePresentationVisibilityCollector)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        viewModel.olive.prepareToPlayOLiveResource.removeObserver(prepareToPlayOLiveResourceObserver)
        diffedFocusViewDataCollectJob?.cancel()     // 及时cancel掉防止泄漏
        diffedFocusViewDataCollectJob = null
    }

    /**
     * 显示olive关闭提示
     * @param show 是否显示
     */
    @MainThread
    private fun showOrHideOliveCloseTips(show: Boolean) {
        if (show.not()) {
            oliveTipsSnackBar?.dismiss()
            oliveTipsSnackBar = null
            return
        }
        //不为空，代表tips为显示状态
        if (oliveTipsSnackBar != null) {
            return
        }
        contentView?.let {
            var bottomMargin = context?.resources?.getDimensionPixelSize(com.support.snackbar.R.dimen.coui_snack_bar_margin_bottom)
                ?: ScreenUtils.dpToPixel(R.dimen.photopage_olive_tips_snackbar_margin_bottom_default)
            val couldShowBottomMenu: Boolean = viewModel.menuControl.bottomMenuVisibility.value ?: true
            if (couldShowBottomMenu) {
                GLog.d(TAG, "show bottom menu")
                bottomMargin = context?.resources?.getDimensionPixelSize(R.dimen.photopage_olive_tips_snackbar_margin_bottom)
                    ?: ScreenUtils.dpToPixel(R.dimen.photopage_olive_tips_snackbar_margin_bottom_default)
            }
            oliveTipsSnackBar =
                COUISnackBar.make(it, oliveCloseTextTip, DEFAULT_TIPS_SNACK_BAR_DISPLAY_DURATION, bottomMargin).apply {
                    setOnAction(R.string.photopage_button_olive_tag_open, openSnackBarClickListener)
                    show()
                    setOnStatusChangeListener(object : COUISnackBar.OnStatusChangeListener {
                        override fun onShown(p0: COUISnackBar?) {}
                        override fun onDismissed(p0: COUISnackBar?) {
                            oliveTipsSnackBar = null
                        }
                    })
                }
        } ?: GLog.e(TAG) { "[showOliveCloseTips] view is null" }
    }

    private fun onOliveSupportChanged(support: Boolean) {
        GLog.d(TAG) { "<onOliveSupportChanged> supportOlive= $support" }
        if (support) {
            // 向PhotoPager注册长按事件监听
            pagerSection?.registerSlotLongPressListener(slotLongPressListener)
            pagerSection?.registerUninterruptedTouchEventListener(uninterruptedSlotTouchListener)
        } else {
            pagerSection?.unregisterSlotLongPressListener(slotLongPressListener)
            pagerSection?.unregisterUninterruptedTouchEventListener(uninterruptedSlotTouchListener)
        }
    }

    /**
     * 处理长按
     * @param canHasOtherOverlay 是否能有其他Overlay。如果不能但实际有则不处理长按，
     *                           eg:长按时抠图结果已经显示了，则不处理该长按；当抠图后继续长按时允许有Overlay，此时会处理长按
     *
     *  @return 是否处理长按
     */
    private fun handleLongPress(canHasOtherOverlay: Boolean): Boolean {
        val hasOtherOverlay = fun(): Boolean {
            // 抠图结果显示时，阻断Olive长按；后续还有其他的阻断长按的，可以在这里添加
            return viewModel.lnS.lnSLaunchResultState.value == true
        }
        if (canHasOtherOverlay.not() && hasOtherOverlay()) {
            GLog.w(TAG) { "<handleLongPress> canHasOtherOverlay is false but has other overlay, skip handle long press." }
            return false
        }
        //oliveEnable不可用则弹出提示
        if (viewModel.olive.oliveEnable.not()) {
            showOrHideOliveCloseTips(true)
            return false
        }
        var canHandleLongPress = false
        viewModel.olive.checkCanHandleLongPressed(activity) { conditions ->
            canHandleLongPress = (conditions == READY)
            if (canHandleLongPress) {
                GLog.d(TAG) { "<handleLongPress> play olive from long pressed" }
                // 通知vm标记此次播放来源于用户手动触发
                viewModel.olive.notifyWhenManualPlayFocusOLiveFromUser()
                // 交互要求，长按播放时需要震一下子
                sectionPage.pageContext?.let { VibratorUtils.vibrate(it, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK) }
                val playRange = viewModel.olive.currentOLiveVideoPlayRange
                // 交互要求：长按播放没有up时，不能回到封面. 故：当触发播放来源是长按手势时，不需要olive播放自动控制封面状态 故： autoChangeToCover = false
                viewModel.olive.startPlayOLive(playRange,
                    autoChangeToCover = false,
                    playEffectList = PhotoOliveViewModel.oliveClickPlayEffects,
                    playType = AVPlayer.VideoPlayType.MAIN)

                activity?.let { viewModel.pageManagement.updateColorDirectAbility(it, false, from = "$TAG handleLongPress") }
                isLongPressed = true
                viewModel.olive.notifyLongPressHandling(isLongPressed)
            }

            // 添加埋点信息
            if ((conditions == READY) || (conditions == SLIMMING_FILE)) {
                viewModel.olive.trackOlivePlayEvent(OLivePlayType.LONG_PRESS_PHOTO, canHasOtherOverlay)
            }
        }
        return canHandleLongPress
    }

    private fun subscribeFromViewModel() {
        // 监听焦点变更
        diffedFocusSlotFilterFlow.collect(this@PhotoOliveSection, ensureFocusSlotCollector)

        // 1：支持Olive，开始向PhotoPager注册长按事件监听
        viewModel.olive.supportOlive.observe(this, ::onOliveSupportChanged)
        viewModel.lnS.lnSLongPressedAfterShown.observe(this) { pressed ->
            GLog.d(TAG) { "<subscribeFromViewModel> lnSLongPressedAfterShown pressed=$pressed isFingerLeft=$isFingerLeft" }
            if (pressed.not()) {
                return@observe
            }
            // 如果手指已经离开，不允许操作长按播放逻辑
            if (isFingerLeft) {
                return@observe
            }
            handleLongPress(true)
        }

        viewModel.olive.focusSlotOLiveState.observe(this) { oliveState ->
            GTrace.trace({ "$TAG.focusSlotOLiveState" }) {
                tryUpdateFunctionAreaVisibleState(oliveState, reason = "focusSlotOLiveState")
            }
        }

        /**
         * 当olive损坏，且是用户主动触发的播放时，提示用户文件损坏
         *
         * 注意：当用户点击了播放且停留在焦点界面时 才会弹出提示
         */
        viewModel.olive.focusOLivePlayError.observe(this) {
            if (viewModel.olive.manualPlayFocusOLiveFormUser.value == true) {
                ToastUtil.showShortToast(R.string.photopage_playback_failed)
            }
        }

        viewModel.olive.oliveTipsVisible.observe(this) { visible ->
            showOrHideOliveCloseTips(visible)
        }
        viewModel.brighten.hdrSdrRatio.observe(this) { hdrSdrRatio ->
            /**
             * 矫正相邻的图片的“屏幕提亮后的亮度相比于基准亮度的scale值”
             * 否则会出现以下异常：左右相邻的HDR视频的Olive由于没有时机去更新这个scale值，那么就会使用OliveDrawableLoader初始化时的默认的1.0f，左右滑动的时候首帧会太亮
             */
            pagerSection?.foreachSlotRenderer { renderer ->
                (renderer as? IPageOliveRenderUpdater)?.updateHdrSdrRatio(hdrSdrRatio)
            }
        }
    }

    /**
     * 尝试设置当前功能区域是否对用户可见
     * - 1.Olive播放视频期间，不可见
     * - 2.大图滑动期间，不可见
     * - 3.大图处于封面且静止时，可见
     */
    private fun tryUpdateFunctionAreaVisibleState(oliveState: OLiveState?, reason: String) {
        // 播放视频时，功能区不可见
        val shouldShowByOliveState = (oliveState != OLiveState.VIDEO)
        val isScrolling = viewModel.pageManagement.photoPagerScrollState.value != PhotoPager.SCROLL_STATE_IDLE
        val scrollPlayableState = viewModel.olive.scrollPlayableState.value
        val isOlive = viewModel.olive.supportOlive.value == true
        val visible = when (scrollPlayableState) {
            /**
             * 资源滑动变更后，如果当前资源是Olive则返回false，功能区不可见；否则是Olive，依据Olive的OliveState来决定是否可见
             */
            OliveScrollPlayableState.NONE -> if (isOlive) false else shouldShowByOliveState
            /**
             * 进入当前资源后状态为CANNOT_PLAY，说明当前资源是Olive资源，并且状态机已经走到了Present，
             * 但是由于不是手动滑动过来的（首次进入/缩图轴滑到的）所以不能播放，所以此时功能区可见
             */
            OliveScrollPlayableState.CANNOT_PLAY -> true
            /**
             * 资源滑动后状态为CAN_PLAY，说明当前资源是Olive资源，并且状态机已经走到了Present，
             * 而且是手动滑动过来的，要播放，但是还没播放，此时功能区不可见，待播放之后再可见
             */
            OliveScrollPlayableState.CAN_PLAY -> false
            /**
             * 资源滑动后状态为PLAYED，说明Olive已经去播放了，
             * 如果资源是Olive且正在滑动，则功能区不可见；
             * 如果资源不是Olive或者没有滑动，则由OliveState来决定是否显示功能区（非Olive资源的OliveState为null，功能区可见）
             */
            OliveScrollPlayableState.PLAYED -> (if (isScrolling && isOlive) false else shouldShowByOliveState)
            else -> true
        }
        GLog.d(TAG, LogFlag.DL) {
            "<tryUpdateFunctionAreaVisibleState>, index:${viewModel.dataLoading.focusSlot}, oliveState=$oliveState," +
                    "shouldShowByOliveState=$shouldShowByOliveState isOlive=$isOlive, " +
                    "isScrolling=$isScrolling, scrollPlayableState=$scrollPlayableState, visible=$visible"
        }
        viewModel.pageManagement.updateFunctionAreaVisibleState(visible, "$TAG.$reason")
        viewModel.photoFunc.notifyVisible(VISIBLE_FROM_OLIVE, visible)
    }

    private fun invokeSlotRenderer(index: Int, action: (DrawableRenderer<View, Drawable>) -> Unit) {
        pagerSection?.findPhotoSlotBySlot(index)?.contentRenderer?.let { action(it) }
    }

    override fun onViewDestroy() {
        super.onViewDestroy()
        pagerSection?.apply {
            unregisterSlotLongPressListener(slotLongPressListener)
            unregisterUninterruptedTouchEventListener(uninterruptedSlotTouchListener)
            unregisterOnPageChangeCallback(onPageChangeCallback)
            removeContentRendererStatusListener(contentRendererStatusListener)
        }
        oliveTipsSnackBar?.dismiss()
        oliveTipsSnackBar = null
    }

    /**
     * 该地方了逻辑为了规避Olive从编辑回到大图后，此时播放会将旁边的Olive资源透过来的问题。
     * 在播放当前Olive资源的时候，会将左右两边的Olive内容给设置为不可见，并在滑动的时候再恢复可见。
     */
    private fun updateOLivePresentationVisibility(slot: Int, visible: Boolean = false) {
        if (viewModel.dataLoading.diffedPhotoViewDataSet.value?.get(slot)?.isOLive() == true) {
            pagerSection?.invokeSlotRenderer(slot) {
                val visibility = if (visible) View.VISIBLE else View.INVISIBLE
                (it as? IPageRenderPresentationVisibilityUpdater)?.updatePresentationVisibility(visibility)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        //关闭 ability
        closeAbility()
    }

    /**
     * 关闭Ability
     */
    private fun closeAbility() {
        settingsAbility?.close()
    }

    private companion object {
        private const val TAG = "PhotoOliveSection"
        private const val KEY_OLIVE_GUIDE = "mainOliveGuide.requestCode"

        /**
         * 默认的提示snack bar显示时长，交互确认用4s
         */
        private const val DEFAULT_TIPS_SNACK_BAR_DISPLAY_DURATION = 4000
    }
}