/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransitionEntity
 ** Description:用于编辑视频转场动效的UI数据载体，相当于ViewData
 ** Version: 1.0
 ** Date: 2025-07-09
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2025-07-09     1.0
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.entity

import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_DOWNLOAD_FILE
import com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_NOT_DOWNLOADED
import com.oplus.gallery.videoeditorpage.resource.room.bean.TransitionItem
import com.oplus.gallery.videoeditorpage.resource.util.ResourceUtils

/**
 * 转场实体
 */
data class TransitionEntity(
    var id: String = TextUtil.EMPTY_STRING,
    var name: String = TextUtil.EMPTY_STRING,
    var nameId: String = TextUtil.EMPTY_STRING,
    var iconPath: String = TextUtil.EMPTY_STRING,
    var resourceId: String = TextUtil.EMPTY_STRING,
    var resourcePath: String = TextUtil.EMPTY_STRING,
    var resourceUrl: String = TextUtil.EMPTY_STRING,
    var md5: String = TextUtil.EMPTY_STRING,
    var downloadState: Int = TYPE_NOT_DOWNLOADED,
    var progress: Int = 0,
    var builtin: Int = ResourceUtils.NETWORK_RESOURCE
) {

    fun isNeedDownloadFile(): Boolean {
        return (this.downloadState and TYPE_DOWNLOAD_FILE) != TYPE_DOWNLOAD_FILE
    }

    fun mergeFrom(item: TransitionItem) {
        name = item.nameText
        nameId = item.nameId
        iconPath = item.iconPath ?: TextUtil.EMPTY_STRING
        resourceId = item.resourceId ?: TextUtil.EMPTY_STRING
        resourcePath = item.resourcePath ?: TextUtil.EMPTY_STRING
        resourceUrl = item.resourceUrl ?: TextUtil.EMPTY_STRING
        md5 = item.resourceMd5 ?: TextUtil.EMPTY_STRING
        downloadState = item.downloadState
        progress = item.progress
        builtin = item.builtin
    }

    fun mergeFrom(entity: TransitionEntity) {
        name = entity.name
        nameId = entity.nameId
        iconPath = entity.iconPath
        resourceId = entity.resourceId
        resourcePath = entity.resourcePath
        resourceUrl = entity.resourceUrl
        md5 = entity.md5
        downloadState = entity.downloadState
        progress = entity.progress
        builtin = entity.builtin
    }
}
