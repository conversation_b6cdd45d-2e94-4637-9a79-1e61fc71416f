/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: EditorSwitchMenuAdapter.kt
 ** Description:编辑开启关闭菜单列表适配器
 ** Version: 1.0
 ** Date : 2025/2/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  zhen<PERSON><EMAIL>    2025/2/19        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.videoeditorpage.widget.adapter

import android.content.Context
import android.graphics.Paint
import android.view.View
import com.oplus.gallery.basebiz.widget.EditorMenuItemView
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.EditorViewAlphaAnimViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable
import com.oplus.gallery.business_lib.template.editor.adapter.setViewSelectedState
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation
import com.oplus.gallery.business_lib.template.editor.anim.EditorViewAlphaAnimation
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.utlis.ThemeHelper
import com.oplus.gallery.basebiz.R as BaseR

/**
 * 编辑开启关闭菜单列表适配器
 */
open class EditorSwitchMenuAdapter(
    context: Context,
    menuItemListData: List<EditorMenuItemViewData>
) : VideoEditorMenuAdapter(context, menuItemListData) {

    private val resources = context.resources
    private val disableGoneAlpha = resources.getFloat(BaseR.dimen.base_editor_menu_state_disable_alpha)
    private val disableDefaultAlpha = resources.getFloat(BaseR.dimen.base_editor_menu_state_disable_alpha)
    private val highlightAlpha = resources.getFloat(BaseR.dimen.base_editor_menu_state_default_alpha)
    private val itemBackgroundSelectedColor = resources.getColor(BaseR.color.adjust_editor_menu_item_mask, null)
    private val itemBackgroundUnselectedColor = resources.getColor(BaseR.color.adjust_editor_menu_item_normal_mask, null)

    private val selectedProgressColor = resources.getColor(BaseR.color.adjust_editor_menu_item_progress_undone_color, null)
    private val unselectedProgressColor = resources.getColor(BaseR.color.adjust_editor_menu_item_normal_progress_undone_color, null)
    private val selectedColor = ThemeHelper.getCouiColorContainerTheme(context)
    private val textSelectedColor = resources.getColor(R.color.videoeditor_video_editor_bg, null)
    private val unDoneColor = ThemeHelper.getCouiColorContainerThemeHalftone(context)
    private val defaultTextColor = resources.getColor(R.color.editor_caption_color_hint, null)

    override fun getItemLayoutId(viewType: Int): Int {
        return BaseR.layout.base_editor_menu_adjust_item_layout
    }

    override fun bindData(viewHolder: BaseRecycleViewHolder?, position: Int, item: EditorMenuItemViewData?) {
        super.bindData(viewHolder, position, item)
        val menuItemView = getMenuItemView(viewHolder)?.apply {
            normalDrawFlag = (EditorMenuItemView.DRAW_BACKGROUND_COLOR
                or EditorMenuItemView.DRAW_PROGRESS
                or EditorMenuItemView.DRAW_CENTER_ICON)
            selectedDrawFlag = (EditorMenuItemView.DRAW_BACKGROUND_COLOR
                or EditorMenuItemView.DRAW_CENTER_ICON
                or EditorMenuItemView.DRAW_CENTER_TEXT
                or EditorMenuItemView.DRAW_PROGRESS)
            isDrawBackgroundColor = true
        } ?: run {
            GLog.e(TAG, LogFlag.DL) { "bindData. menuItemView is null" }
            return
        }
        updateView(viewHolder, position, item, menuItemView)
        getNameTextView(viewHolder)?.setTextColor(
            if ((item?.isSelected == true) && (item.isDisableStyle.not())) {
                textSelectedColor
            } else {
                defaultTextColor
            }
        )
    }

    /**
     * 更新列表项的View，包括更新menuItemView以及设置选中状态
     */
    protected open fun updateView(
        viewHolder: BaseRecycleViewHolder?,
        position: Int,
        item: EditorMenuItemViewData?,
        menuItemView: EditorMenuItemView
    ) {
        updateEditorMenuItem(menuItemView)
        viewHolder?.setViewSelectedState(getSelectedState(position, item))
    }

    /**
     * 获取项的选中状态
     */
    protected open fun getSelectedState(position: Int, item: EditorMenuItemViewData?): Int {
        return if (item?.isDisableStyle?.not() == true) {
            Selectable.SELECTED
        } else {
            Selectable.UNSELECTED
        }
    }

    private fun updateEditorMenuItem(menuItemView: EditorMenuItemView) {
        menuItemView.apply {
            itemBackgroundColor = if (isSelected) itemBackgroundSelectedColor else itemBackgroundUnselectedColor
            progressUndoneColor = unDoneColor
            progressDoneColor = selectedColor
            isDrawProgress = true
            isDrawZeroProgress = false
            progressPaintCap = Paint.Cap.ROUND
            setTextColor(selectedColor)
        }
    }

    override fun createViewHolder(itemView: View, viewType: Int): BaseRecycleViewHolder {
        val animHolder = EditorViewAlphaAnimViewHolder(itemView, EditorViewAlphaAnimation(), EditorPressAnimation())
        animHolder.apply {
            setSupportPressAnim(animHolder)
            setSelectedAnimEnable(true)
            setSelectedAnimView(animHolder)
            setDisableAlpha(disableGoneAlpha)
            setUnselectedAlpha(disableDefaultAlpha)
            setSelectedAlpha(highlightAlpha)
        }
        return animHolder
    }

    companion object {
        private const val TAG = "EditorSwitchMenuAdapter"
    }
}