/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RuntimePermissionRationaleAlert.java
 ** Description : explain why need apply runtime permission
 ** Version: 1.0
 ** Date: 2019/12/03
 ** Author: chenzengxin
 ** TAG: OPLUS_FEATURE_RUN_TIME_PERMISSION_RATIONALE_ALERT
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin		   2019/12/03   1.0		  build this module
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.memories.ui.dialog;

import android.app.Activity;
import android.view.KeyEvent;

import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog;

public class RuntimePermissionRationaleAlert {

    public RuntimePermissionRationaleAlert() {

    }

    public interface RationaleCallback {
        void onSettingClick();
        void onDismissClick();
    }

    public void showDialog(Activity activity,RationaleCallback callback) {
        if (activity == null) {
            return;
        }
        if ((!activity.isFinishing()) && (!activity.isDestroyed())) {
            new ConfirmDialog.Builder(activity)
                .setPositiveButton(com.oplus.gallery.basebiz.R.string.base_permission_setting,
                    (dialog, which) -> callback.onSettingClick())
                .setNegativeButton(com.oplus.gallery.basebiz.R.string.base_permission_statement_cancel,
                    (dialog, which) -> callback.onDismissClick())
                .setMessage(com.oplus.gallery.basebiz.R.string.base_permission_request_read_photo_state_statement)
                .setTitle(com.oplus.gallery.basebiz.R.string.base_permission_request_read_photo_state)
                .setCancelable(false)
                .setOnKeyListener((dialog, keyCode, event) -> {
                    if ((keyCode == KeyEvent.KEYCODE_BACK)
                        && (event.getAction() == KeyEvent.ACTION_DOWN)) {
                        dialog.dismiss();
                        callback.onDismissClick();
                    }
                    return false;
                })
                .build()
                .show();
        }
    }
}