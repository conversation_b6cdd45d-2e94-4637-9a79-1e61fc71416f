/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SongStorage.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/28
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/08/28		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.storage

import android.database.SQLException
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.resource.room.dao.VideoAudioDao
import com.oplus.gallery.videoeditorpage.resource.room.entity.VideoAudioItem
import com.oplus.gallery.videoeditorpage.resource.room.helper.ResourceDatabaseHelper

class VideoAudioStorage : BaseResourceStorage<VideoAudioItem>() {

    private var videoAudioDao: VideoAudioDao = ResourceDatabaseHelper.getInstance().db.videoAudioDao
    override fun save(item: VideoAudioItem): Long {
        try {
            return videoAudioDao.insert(item)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "save e:$e")
        }
        return -1
    }

    override fun save(items: List<VideoAudioItem>): Boolean {
        runCatching {
            val result = videoAudioDao.insert(items)
            return result.all { it != -1L }
        }.onFailure { e ->
            GLog.e(TAG, LogFlag.DL, "save e:$e")
        }
        return false
    }

    override fun clear() {
        try {
            videoAudioDao.deleteAll()
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "clear e:$e")
        }
    }

    override fun get(originalVideoPath: String): VideoAudioItem? {
        try {
            return getEntityByOriginalVideoPath(originalVideoPath)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "get e:$e")
        }
        return null
    }

    override fun getByPath(path: String): VideoAudioItem? {
        try {
            return getEntityByFilePath(path)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getByPath e:$e")
        }
        return null
    }

    override fun getBuiltin(): MutableList<VideoAudioItem> {
        try {
            return videoAudioDao.all ?: mutableListOf()
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getBuiltin e:$e")
        }
        return mutableListOf()
    }

    override fun getNetwork(): MutableList<VideoAudioItem> = mutableListOf()

    override fun getAllOrderByPosition(): List<VideoAudioItem> {
        try {
            return videoAudioDao.all ?: emptyList()
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getAll e:$e")
        }
        return emptyList()
    }

    override fun getLoaded(): MutableList<VideoAudioItem> {
        try {
            return videoAudioDao.all ?: mutableListOf()
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getLoaded e:$e")
        }
        return mutableListOf()
    }

    override fun clearBuiltin() {
        try {
            videoAudioDao.deleteAll()
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "clearBuiltin e:$e")
        }
    }

    /**
     * 获取内置歌曲的数量
     *
     * 此方法用于从数据源获取内置歌曲的总数它使用了[videoAudioDao]来查询内置歌曲的数量，
     * 并且在查询失败时记录错误信息并返回默认值0
     *
     * @return 内置歌曲的数量如果查询失败，则返回0
     */
    override fun getBuiltinCount(): Int {
        return runCatching {
            videoAudioDao.queryBuiltinCount()
        }.onFailure { e ->
            GLog.e(TAG, LogFlag.DL, "[getBuiltinCount],e:$e")
        }.getOrDefault(0)
    }

    fun getEntityByFilePath(filePath: String?): VideoAudioItem? {
        try {
            return videoAudioDao.getEntityByFilePath(filePath)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getEntityByFilePath e: $e")
            return null
        }
    }

    fun getEntityByDateTaken(dateTaken: String?): MutableList<VideoAudioItem>? {
        try {
            return videoAudioDao.getEntityByDateTaken(dateTaken)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getEntityByDateTaken e: $e")
            return null
        }
    }

    fun delete(filePath: String?): Int? {
        try {
            return videoAudioDao.deleteEntityByFilePath(filePath)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getEntityByDateTaken e: $e")
            return null
        }
    }

    fun getEntityByOriginalVideoPath(originalVideoPath: String?): VideoAudioItem? {
        try {
            return videoAudioDao.getEntityByOriginalVideoPath(originalVideoPath)
        } catch (e: SQLException) {
            GLog.e(TAG, LogFlag.DL, "getEntityByOriginalVideoPath e: $e")
            return null
        }
    }

    companion object {
        private const val TAG = "VideoAudioStorage"
    }
}