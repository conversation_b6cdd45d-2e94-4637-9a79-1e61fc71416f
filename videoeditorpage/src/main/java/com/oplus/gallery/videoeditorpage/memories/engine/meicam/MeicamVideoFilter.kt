/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoFilter
 ** Description: 美摄滤镜操作类
 **
 ** Version: 1.0
 ** Date: 2022/07/29
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/07/29  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import android.text.TextUtils
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoFx
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.videoedit.data.FilterInfo
import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoFilter

class MeicamVideoFilter : IGalleryVideoFilter {

    companion object {
        private const val TAG = "MeicamVideoFilter"
        private const val FILTER_DEFAULT_INDEX = 0
        const val FILTER_COLOR_PROPERTY = "Color Property"
        const val FILTER_VIGNETTE = "Vignette"
        const val FILTER_SHARPEN = "Sharpen"
        const val FILTER_LUT = "Lut"
    }

    private val mMeicamVideoFilterManager = MeicamVideoFilterManager()
    private var mTimeline: NvsTimeline? = null
    private var mFilterPath: String? = null
    private var mFilterInfo: FilterInfo? = null
    private var mFilterItemIndex = 0

    fun setTimeline(meicamTimeline: MeicamTimeline) {
        mTimeline = meicamTimeline.nvsTimeline
    }

    override fun applyPackagedFx(filter: String?): Boolean {
        if (mTimeline == null) {
            GLog.w(TAG, "applyVideoFilter mTimeline is null.")
            return false
        }
        if (TextUtils.isEmpty(filter)) {
            GLog.w(TAG, "applyVideoFilter filter is null.")
            return false
        }
        val track: NvsVideoTrack? = mTimeline?.getVideoTrackByIndex(0)
        if (track == null) {
            GLog.w(TAG, "applyVideoFilter track is null!")
            return false
        }
        val clipCount = track.clipCount
        if (clipCount <= 0) {
            GLog.d(TAG, "applyVideoFilter clip count is 0")
            return false
        }
        GLog.d(TAG, "applyVideoFilter filter = $filter")
        for (i in 0 until clipCount) {
            val clip = track.getClipByIndex(i) ?: continue
            clip.removeAllFx()
            clip.appendPackagedFx(filter)
        }
        return true
    }

    override fun removeVideoFilter() {
        val track: NvsVideoTrack = mTimeline?.getVideoTrackByIndex(0) ?: return
        for (i in 0 until track.clipCount) {
            val clip = track.getClipByIndex(i) ?: continue
            clip.removeAllFx()
        }
        mFilterPath = ""
    }

    override fun applyLutFx(path: String?): Boolean {
        return applyLutFx(path, MeicamVideoClipEffect.DEFAULT_STRENGTH, FILTER_DEFAULT_INDEX)
    }

    override fun applyLutFx(path: String?, strength: Float, index: Int): Boolean {
        val videoTrack: NvsVideoTrack? = mTimeline?.getVideoTrackByIndex(0)
        if (videoTrack == null) {
            GLog.e(TAG, "applyLutFx, video track is null")
            return false
        }
        mFilterItemIndex = index
        mFilterPath = path
        val clipCount = videoTrack.clipCount
        var clip: NvsVideoClip? = null
        for (i in 0 until clipCount) {
            clip = videoTrack.getClipByIndex(i)
            // for now, we only have lut built-in effect, change this after support more category
            val videoClipEffect: BaseVideoClipEffect? = mMeicamVideoFilterManager.getVideoClipFilter(FILTER_LUT)
            if (videoClipEffect == null) {
                GLog.e(TAG, "applyLutFx failed, videoClipEffect = null")
                return false
            }
            insertEffect(index, videoClipEffect, clip)
            videoClipEffect.strength = strength
            videoClipEffect.setStringValue(MeicamVideoSubTitle.PARAM_DATA_FILE_PATH, path)
        }
        clearFilterItemWhenTemplateChange()
        return true
    }

    private fun clearFilterItemWhenTemplateChange() {
        mFilterInfo = null
    }

    override fun setFilter(filterInfo: FilterInfo?, index: Int): Boolean {
        if (filterInfo == null) {
            GLog.e(TAG, "setFilter filterItem is null index = $index")
            return false
        }
        var ret = false
        if (filterInfo.filePath?.isNotEmpty() == true) {
            if (TextUtils.isEmpty(filterInfo.filePath)) {
                return false
            }
            ret = applyLutFx(filterInfo.filePath, filterInfo.defaultStrength, index)
        } else {
            ret = removeFilter(index)
            mFilterPath = ""
        }
        GLog.d(TAG, "setFilter, ret = $ret")
        if (ret) {
            mFilterInfo = filterInfo
        }
        return ret
    }

    override fun resetFilter(): Boolean {
        return setFilter(mFilterInfo, FILTER_DEFAULT_INDEX)
    }

    override fun getFilterPath(): String? {
        return mFilterPath
    }

    override fun getCurrentInfo(): FilterInfo? {
        return mFilterInfo
    }

    override fun getCurrentFilterIndex(): Int {
        return mFilterItemIndex
    }

    override fun setCurrentInfo(filterInfo: FilterInfo?) {
        GLog.d(TAG, "setCurrentItem, filterInfo = $filterInfo")
        mFilterInfo = filterInfo
    }

    override fun removeFilter(index: Int): Boolean {
        val videoTrack: NvsVideoTrack = mTimeline?.getVideoTrackByIndex(0) ?: return false
        var clip: NvsVideoClip? = null
        var ret = true
        for (i in 0 until videoTrack.clipCount) {
            clip = videoTrack.getClipByIndex(i)
            if (!removeFilter(index, clip)) {
                ret = false
            }
        }
        GLog.d(TAG, "removeFilter")
        return ret
    }

    fun isFilterChanged(): Boolean {
        return mFilterInfo?.filePath?.isNotEmpty() == true
    }

    private fun insertEffect(index: Int, videoClipEffect: BaseVideoClipEffect, clip: NvsVideoClip?) {
        if (index < 0) {
            GLog.e(TAG, "insertEffect to $index")
            return
        }
        if (clip == null) {
            GLog.e(TAG, "insertEffect clip is null. ")
            return
        }
        if (videoClipEffect.type == BaseVideoEffect.TYPE_CUSTOMER_FX) {
            GLog.d(TAG, "insertEffect do nothing, no custom effect for now")
            return
        }
        if (videoClipEffect !is MeicamVideoClipEffect) {
            GLog.e(TAG, "insertEffect not instance of MeicamVideoClipEffect")
            return
        }
        removeFilter(index, clip)
        val addResult = insertEffectToNvsObj(index, videoClipEffect, clip)
        // ensure is not different from NvsVideoClip
        if (addResult != null) {
            videoClipEffect.setNvsVideoFx(addResult)
        } else {
            GLog.d(TAG, "insertEffect nvs is not null, but insert failed")
        }
    }

    private fun insertEffectToNvsObj(index: Int, videoClipEffect: BaseVideoClipEffect, clip: NvsVideoClip?): NvsVideoFx? {
        if (clip == null) {
            GLog.e(TAG, "insertEffectToNvsObj clip is null!")
            return null
        }
        var result: NvsVideoFx? = null
        if (videoClipEffect is MeicamVideoClipEffect) {
            val effect = videoClipEffect
            GLog.d(TAG, "insertEffectToNvsObj: getType: " + effect.type + " getName: " + effect.name + " index: " + index)
            when (effect.type) {
                BaseVideoEffect.TYPE_PACKAGED_FX -> result = clip.insertPackagedFx(effect.name, index)
                BaseVideoEffect.TYPE_BUILT_IN_FX -> result = clip.insertBuiltinFx(effect.name, index)
                BaseVideoEffect.TYPE_CUSTOMER_FX -> {
                }
                else -> {
                }
            }
        }
        return result
    }

    private fun removeFilter(index: Int, clip: NvsVideoClip): Boolean {
        val count = clip.fxCount
        return if (index < count) {
            clip.removeFx(index)
        } else false
    }
}