/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: File
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.data;

import androidx.annotation.Nullable;

import java.io.FileFilter;
import java.io.FilenameFilter;
import java.io.IOException;

public class File implements Comparable<File> {

    public static final String SEPARATOR = java.io.File.separator;

    private final java.io.File mFile;

    public File(String pathName) {
        mFile = new java.io.File(pathName);
    }

    public File(String parent, String child) {
        mFile = new java.io.File(parent, child);
    }

    public File(File file, String child) {
        this(file.getFile(), child);
    }

    public File(java.io.File file, String child) {
        mFile = new java.io.File(file, child);
    }

    private File(java.io.File file) {
        mFile = file;
    }

    public static File getFile(java.io.File file) {
        if (file == null) {
            return null;
        } else {
            return new File(file);
        }
    }

    public java.io.File getFile() {
        return mFile;
    }

    public String getName() {
        return mFile.getName();
    }

    public String getParent() {
        return mFile.getParent();
    }

    public String getPath() {
        return mFile.getPath();
    }

    public File getParentFile() {
        return getFile(mFile.getParentFile());
    }

    public boolean isAbsolute() {
        return mFile.isAbsolute();
    }

    public boolean exists() {
        return mFile.exists();
    }

    public File getAbsoluteFile() {
        return getFile(mFile.getAbsoluteFile());
    }

    public String getAbsolutePath() {
        return mFile.getAbsolutePath();
    }

    public String getCanonicalPath() throws IOException {
        return mFile.getCanonicalPath();
    }

    public File getCanonicalFile() throws IOException {
        return getFile(mFile.getCanonicalFile());
    }

    public boolean canRead() {
        return mFile.canRead();
    }

    public boolean canWrite() {
        return mFile.canWrite();
    }

    public boolean isDirectory() {
        return mFile.isDirectory();
    }

    public boolean isFile() {
        return mFile.isFile();
    }

    public boolean isHidden() {
        return mFile.isHidden();
    }

    public long lastModified() {
        return mFile.lastModified();
    }

    public long length() {
        return mFile.length();
    }

    public boolean createNewFile() throws IOException {
        return mFile.createNewFile();
    }

    public boolean delete() {
        return mFile.delete();
    }

    public void deleteOnExit() {
        mFile.deleteOnExit();
    }

    public String[] list() {
        return mFile.list();
    }

    public String[] list(FilenameFilter filter) {
        return mFile.list(filter);
    }

    public File[] listFiles() {
        java.io.File[] files = mFile.listFiles();
        if (files == null) {
            return null;
        }
        File[] results = new File[files.length];
        for (int i = 0; i < files.length; i++) {
            results[i] = getFile(files[i]);
        }
        return results;
    }

    public File[] listFiles(FilenameFilter filter) {
        java.io.File[] files = mFile.listFiles(filter);
        if (files == null) {
            return null;
        }
        File[] results = new File[files.length];
        for (int i = 0; i < files.length; i++) {
            results[i] = getFile(files[i]);
        }
        return results;
    }

    public File[] listFiles(FileFilter filter) {
        java.io.File[] files = mFile.listFiles(filter);
        if (files == null) {
            return null;
        }
        File[] results = new File[files.length];
        for (int i = 0; i < files.length; i++) {
            results[i] = getFile(files[i]);
        }
        return results;
    }

    public boolean mkdir() {
        return mFile.mkdir();
    }

    public boolean mkdirs() {
        return mFile.mkdirs();
    }

    public boolean renameTo(File file) {
        return mFile.renameTo(file.getFile());
    }

    public boolean setLastModified(long time) {
        return mFile.setLastModified(time);
    }

    public boolean setReadOnly() {
        return mFile.setReadOnly();
    }

    public boolean setWritable(boolean writable, boolean ownerOnly) {
        return mFile.setWritable(writable, ownerOnly);
    }

    public boolean setWritable(boolean writable) {
        return setWritable(writable, true);
    }

    public boolean setReadable(boolean readable, boolean ownerOnly) {
        return mFile.setReadable(readable, ownerOnly);
    }

    public boolean setReadable(boolean readable) {
        return setReadable(readable, true);
    }

    public boolean setExecutable(boolean executable, boolean ownerOnly) {
        return mFile.setExecutable(executable, ownerOnly);
    }

    public boolean setExecutable(boolean executable) {
        return setExecutable(executable, true);
    }

    public boolean canExecute() {
        return mFile.canExecute();
    }

    public long getTotalSpace() {
        return mFile.getTotalSpace();
    }

    public long getFreeSpace() {
        return mFile.getFreeSpace();
    }

    public long getUsableSpace() {
        return mFile.getUsableSpace();
    }

    @Override
    public boolean equals(Object obj) {
        if ((obj != null) && (obj instanceof File)) {
            return compareTo((File) obj) == 0;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return mFile.hashCode();
    }

    @Override
    public String toString() {
        return mFile.getPath();
    }

    @Override
    public int compareTo(File o) {
        return mFile.compareTo(o.getFile());
    }
}
