/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseSticker.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tang<PERSON>bin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.sticker;

import android.graphics.PointF;

import androidx.annotation.NonNull;

import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.BaseVideoTimelineEffect;

import java.util.List;

public abstract class BaseSticker extends BaseVideoTimelineEffect {

    public static final float DEFAULT_SCALE = 1;
    private long mStickerId = 0;
    private int mLimitLength;
    private String mPreviewUrl;
    private PointF mAnchor;

    public BaseSticker(String name) {
        super(name);
        mStickerId = System.currentTimeMillis();
    }

    public void setStickerId(long stickerId) {
        mStickerId = stickerId;
    }

    public long getStickerId() {
        return mStickerId;
    }

    public int getLimitLength() {
        return mLimitLength;
    }

    public void setLimitLength(int limitLength) {
        mLimitLength = limitLength;
    }

    /**
     * 平移贴纸
     * @param translation 平移向量，包含x和y方向的偏移量
     */
    public abstract void translateStcker(PointF translation);

    /**
     * 设置贴纸的平移位置
     * @param translation 平移向量，包含x和y方向的坐标值
     */
    public abstract void setTranslation(PointF translation);

    /**
     * 获取贴纸的平移位置
     * @return 返回贴纸当前的平移向量
     */
    public abstract PointF getTranslation();

    /**
     * 缩放贴纸
     * @param scaleFactor 缩放因子，大于1表示放大，小于1表示缩小
     * @param anchor 缩放锚点，缩放操作围绕该点进行
     */
    public abstract void scaleSticker(float scaleFactor, PointF anchor);

    /**
     * 旋转贴纸
     * @param angle 旋转角度，单位为度
     * @param anchor 旋转锚点，旋转操作围绕该点进行
     */
    public abstract void rotateSticker(float angle, PointF anchor);

    /**
     * 设置贴纸的缩放比例
     * @param scale 缩放比例值
     */
    public abstract void setScale(float scale);

    /**
     * 获取贴纸的缩放比例
     * @return 返回当前的缩放比例
     */
    public abstract float getScale();

    /**
     * 获取贴纸的旋转角度
     * @return 返回当前的旋转角度，单位为度
     */
    public abstract float getRotation();

    /**
     * 设置贴纸的旋转角度
     * @param rotation 旋转角度，单位为度
     */
    public abstract void setRotation(float rotation);

    /**
     * 获取贴纸边界矩形的顶点坐标
     * @return 返回包含四个顶点坐标的列表
     */
    public abstract List<PointF> getBoundingRectangleVertices();

    /**
     * 设置贴纸的Z轴值（层级深度）
     * @param zValue Z轴值，用于控制贴纸的显示层级
     */
    public abstract void setZValue(float zValue);

    /**
     * 获取贴纸的Z轴值（层级深度）
     * @return 返回当前的Z轴值
     */
    public abstract float getZValue();

    /**
     * 设置裁剪亲和性是否启用
     * @param enabled true表示启用裁剪亲和性，false表示禁用
     */
    public abstract void setClipAffinityEnabled(boolean enabled);

    /**
     * 获取裁剪亲和性是否启用的状态
     * @return 返回裁剪亲和性的启用状态
     */
    public abstract boolean getClipAffinityEnabled();

    /**
     * 根据键名获取附件对象
     * @param key 附件的键名
     * @return 返回与键名对应的附件对象，如果不存在则返回null
     */
    public abstract Object getAttachment(String key);

    /**
     * 设置附件对象
     * @param key 附件的键名
     * @param value 要设置的附件对象
     */
    public abstract void setAttachment(String key, Object value);

    public String getPreviewUrl() {
        return mPreviewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.mPreviewUrl = previewUrl;
    }

    public PointF getAnchor() {
        return mAnchor;
    }

    public void setAnchor(PointF anchor) {
        this.mAnchor = anchor;
    }

    @NonNull
    @Override
    public String toString() {
        return "BaseSticker {"
                + "mPreviewUrl:" + mPreviewUrl
                + ", mTrackIndex:" + mTrackIndex
                + ", mInTime:" + mInTime
                + ", mOutTime:" + mOutTime
                + "}";
    }
}
