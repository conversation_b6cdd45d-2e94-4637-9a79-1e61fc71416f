/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/6/26
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/6/26  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.memories.resource.data;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class TemplateResponseBean {

    @SerializedName("albumTemplateList")
    private List<TemplateListBean> mTemplateList;

    public List<TemplateListBean> getTemplateList() {
        return mTemplateList;
    }

    public void setTemplateList(List<TemplateListBean> templateList) {
        this.mTemplateList = templateList;
    }

    public static class TemplateListBean extends BaseResourceBean {
        @SerializedName("templateName")
        private String mTemplateName;
        @SerializedName("chName")
        private String mChName;
        @SerializedName("enName")
        private String mEnName;
        @SerializedName("category")
        private String mCategory;
        @SerializedName("songId")
        private int mSongId;
        @SerializedName("iconPath")
        private String mIconPath;
        @SerializedName("zipPath")
        private String mZipPath;
        @SerializedName("templateId")
        private int mTemplateId;
        @SerializedName("updateTime")
        private String mUpdateTime;

        public String getTemplateName() {
            return mTemplateName;
        }

        public void setTemplateName(String templateName) {
            this.mTemplateName = templateName;
        }

        public String getChName() {
            return mChName;
        }

        public void setChName(String chName) {
            this.mChName = chName;
        }

        public String getEnName() {
            return mEnName;
        }

        public void setEnName(String enName) {
            this.mEnName = enName;
        }

        public String getCategory() {
            return mCategory;
        }

        public void setCategory(String category) {
            this.mCategory = category;
        }

        public int getSongId() {
            return mSongId;
        }

        public void setSongId(int songId) {
            this.mSongId = songId;
        }

        public String getIconPath() {
            return mIconPath;
        }

        public void setIconPath(String iconPath) {
            this.mIconPath = iconPath;
        }

        public String getZipPath() {
            return mZipPath;
        }

        public void setZipPath(String zipPath) {
            this.mZipPath = zipPath;
        }

        public int getTemplateId() {
            return mTemplateId;
        }

        public void setTemplateId(int templateId) {
            this.mTemplateId = templateId;
        }

        public String getUpdateTime() {
            return mUpdateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.mUpdateTime = updateTime;
        }
    }
}
