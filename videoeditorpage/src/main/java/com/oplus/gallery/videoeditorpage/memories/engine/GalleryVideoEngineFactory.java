/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - GalleryVideoEngineFactory.java
 * * Description: GalleryVideoEngine factory.
 * * Version: 1.0
 * * Date : 2017/12/23
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/12/23    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine;

import android.content.Context;

import com.oplus.gallery.videoeditorpage.memories.engine.interfaces.IGalleryVideoEngine;
import com.oplus.gallery.videoeditorpage.memories.engine.meicam.MeicamVideoEngine;

public class GalleryVideoEngineFactory {
    public enum Engine {
        MEICAM,
    }
    public GalleryVideoEngineFactory() {
    }

    public IGalleryVideoEngine createVideoEngine(Engine type, Context context, GalleryVideoEngineListener listener) {
        IGalleryVideoEngine GalleryVideoEngine = null;
        switch (type) {
            case MEICAM:
            default: {
                GalleryVideoEngine = new MeicamVideoEngine(context, listener);
                break;
            }
        }
        return GalleryVideoEngine;
    }
}
