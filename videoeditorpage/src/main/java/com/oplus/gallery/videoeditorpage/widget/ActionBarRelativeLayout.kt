/********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ActionBarRelativeLayout
 ** Description: 标题栏容器，用于动态调整将两侧按钮所占宽度保持一致
 ** Version: 1.0
 ** Date : 2025/07/02
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2025/07/02    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.RelativeLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import com.oplus.gallery.videoeditorpage.R

/**
 * 标题栏容器，用于动态调整将两侧按钮所占宽度保持一致
 */
class ActionBarRelativeLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr, defStyleRes) {

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        val cancelButton = findViewById<View>(R.id.action_bar_back) ?: return
        val doneButton = findViewById<View>(R.id.action_bar_done) ?: return

        // 两按钮宽度需保持一致
        if (cancelButton.width < doneButton.width) {
            cancelButton.layoutParams.width = doneButton.width
        } else if (cancelButton.width > doneButton.width) {
            doneButton.layoutParams.width = cancelButton.width
        }
    }
}