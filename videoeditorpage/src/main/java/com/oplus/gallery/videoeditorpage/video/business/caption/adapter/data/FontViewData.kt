/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FontViewData.kt
 ** Description : 文字字体实体信息
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data

import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.videoeditorpage.resource.room.bean.CaptionFontItem

/**
 * 字幕字体的item
 */
class FontViewData(
    /**
     * 字体素材资源url
     */
    fontUrl: String,

    /**
     * 字体的id，用于区分不同字体
     */
    fontId: String,

    /**
     * 中文icon素材url
     */
    zhIconUrl: String,

    /**
     * 英文icon素材url
     */
    enIconUrl: String,

    /**
     * 字体名称翻译词条id
     */
    var fontNameId: String = TextUtil.EMPTY_STRING,

    /**
     * 字体名称翻译资源文件下载地址
     */
    var fontNameResourceUrl: String = TextUtil.EMPTY_STRING

) : DownloadItem(fontUrl, fontId, zhIconUrl, enIconUrl) {

    /**
     * 当前字体显示的名称，从数据库查找对应词条id当前语种下的翻译名称
     */
    var fontDisplayName: String = TextUtil.EMPTY_STRING

    /**
     * 字体名称翻译资源文件本地路径
     */
    var fontNameResourceLocalPath: String = TextUtil.EMPTY_STRING

    /**
     * 转字体数据库实体
     */
    fun toFontItem(): CaptionFontItem {
        return CaptionFontItem().also {
            it.resourceId = resourceId
            it.fontNameId = fontNameId
            it.fontNameResourceUrl = fontNameResourceUrl
            it.fontNameResourceLocalPath = fontNameResourceLocalPath
            it.resourceUrl = resourceUrl
            it.zhIconUrl = zhIconUrl
            it.enIconUrl = enIconUrl
            it.builtin = builtin
            it.downloadState = downloadState
            it.localPath = localPath
            it.orderNum = orderNum
            it.resourceMd5 = resourceMd5
            it.version = version
        }
    }
    companion object {
        private const val TAG = "FontViewData"
    }
}

/**
 * 字体数据库实体实例转换成字体item
 */
fun CaptionFontItem.toFont(): FontViewData {
    return FontViewData(
        this.resourceUrl,
        this.resourceId,
        this.zhIconUrl,
        this.enIconUrl,
        this.fontNameId,
        this.fontNameResourceUrl
    ).also {
        it.downloadState = downloadState
        it.localPath = localPath
        it.fontNameResourceLocalPath = fontNameResourceLocalPath
        it.orderNum = orderNum
        it.resourceMd5 = resourceMd5
        it.builtin = builtin
        it.version = version
    }
}