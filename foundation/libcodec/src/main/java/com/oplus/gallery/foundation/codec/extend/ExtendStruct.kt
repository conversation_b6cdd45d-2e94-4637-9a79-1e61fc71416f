/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ExtendStruct.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/01
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery		2024/11/01		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.foundation.codec.extend

import com.oplus.gallery.foundation.util.ext.toBuffer
import javolution.io.Struct
import java.nio.ByteOrder

/**
 * FileExtend文件结构解析基础类
 */
abstract class ExtendStruct<T>(byteArray: ByteArray) : Struct(), IStructConvert<T> {
    init {
        setByteBuffer(byteArray.toBuffer(false, byteOrder()), 0)
    }

    final override fun byteOrder(): ByteOrder {
        return ByteOrder.LITTLE_ENDIAN
    }

    protected fun Array<Float32>.get(): FloatArray {
        return FloatArray(size) {
            this[it].get()
        }
    }

    protected fun Array<Array<Signed32>>.get(): Array<IntArray> {
        return Array(size) { i ->
            IntArray(this[i].size) { j ->
                this[i][j].get()
            }
        }
    }

    protected fun Array<Array<Signed8>>.get(): Array<IntArray> {
        return Array(size) { i ->
            IntArray(this[i].size) { j ->
                this[i][j].get().toInt()
            }
        }
    }

    protected fun Array<Float32>.getPercentage(percentage: Int): FloatArray {
        return FloatArray(size) { index ->
            this[index].get() * percentage
        }
    }

    protected fun Array<Signed32>.get(): IntArray {
        return IntArray(size) {
            this[it].get()
        }
    }
}