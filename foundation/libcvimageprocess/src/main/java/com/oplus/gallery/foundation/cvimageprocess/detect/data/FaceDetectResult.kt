/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceDetectResult
 ** Description: 人脸检测结果
 **
 ** Version: 1.0
 ** Date: 2024/10/22
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2024/10/22  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.foundation.cvimageprocess.detect.data

/**
 * 人脸检测结果
 */
data class FaceDetectResult(
    /**
     * 是否有人脸
     */
    val hasFace: Boolean = false
) {

    override fun toString(): String {
        return "$TAG{hasFace:$hasFace}"
    }

    companion object {
        private const val TAG = "FaceDetectResult"
    }
}