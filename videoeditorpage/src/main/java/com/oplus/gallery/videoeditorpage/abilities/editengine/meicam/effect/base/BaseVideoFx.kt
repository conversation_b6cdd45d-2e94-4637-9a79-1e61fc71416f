/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseVideoFx.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base

import com.oplus.gallery.foundation.util.text.TextUtil

open class BaseVideoFx(
    name: String?,
    var type: Int
) : BaseVideoTimelineEffect(name) {

    /**
     * 特效强度
     */
    open var strength = 0f

    /**
     * 特效id，一般是本地数据库中的id
     */
    var fxId: Long = 0

    /**
     * 特效简中名，用于显示
     */
    var zhName: String = TextUtil.EMPTY_STRING

    /**
     * 特效繁中名，用于显示
     */
    var chName: String = TextUtil.EMPTY_STRING

    /**
     * 特效英文名，用于显示
     */
    var enName: String = TextUtil.EMPTY_STRING

    /**
     * 特效名称
     */
    var effectName: String? = null

    /**
     * 用于保存额外的特效参数
     */
    @JvmField
    var stringParams: HashMap<String, String>? = null

    /**
     * 设置特效参数
     */
    open fun setStringValue(paramName: String, value: String) {
        stringParams?.set(paramName, value)
    }

    /**
     * 获取特效参数
     */
    fun getStringValue(paramName: String): String? {
        return if (stringParams?.containsKey(paramName) == true) {
            stringParams?.get(paramName)
        } else null
    }

    override fun toString(): String {
        return "BaseVideoFx(type=$type,inTime=$inTime, zhName=$zhName, outTime=$outTime, trackIndex=${getTrackIndex()}"
    }

    companion object {
        const val TAG = "BaseVideoFx"

        /**
         * 包裹视频特效类型
         */
        const val TYPE_PACKAGED_FX = 0

        /**
         * 内建视频特效类型
         */
        const val TYPE_BUILT_IN_FX = 1

        /**
         * 自定义视频特效类型
         */
        const val TYPE_CUSTOMER_FX = 2

        /**
         * AI自定义视频特效类型
         */
        const val TYPE_AI_CUSTOMER_FX = 4

        /**
         * config目录
         */
        const val CONFIG = "config"

        /**
         * pass目录
         */
        const val SHADER_PASS_DIR = "pass"
    }
}