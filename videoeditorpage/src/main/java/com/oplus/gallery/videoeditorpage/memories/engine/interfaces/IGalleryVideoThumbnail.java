/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - IGalleryVideoThumbnail.java
 ** Description: interface for show thumbnail view
 ** Version: 1.0
 ** Date : 2018/01/05
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2018/01/05    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.engine.interfaces;

import android.view.ViewGroup;

public interface IGalleryVideoThumbnail {

    void configOnlyDecodeKeyFrame(boolean onlyDecodeKeyFrame);

    void showTrimThumbnail(ViewGroup parent);

    void showVideoThumbnail(ViewGroup parent);

    void showTrimDetailThumbnail(ViewGroup parent, long time);

    void showFxThumbnail(ViewGroup parent);

    void showSubtitleThumbnail(ViewGroup parent);

    /**
     * 加载视频导出olive缩图轴组件
     * @param parent 父容器
     */
    void showVideoOliveThumbnail(ViewGroup parent);

    /**
     * 加载自定义剪辑缩图轴组件
     */
    void showCustomTrimThumbnail(ViewGroup parent, long time, int padding);
}
