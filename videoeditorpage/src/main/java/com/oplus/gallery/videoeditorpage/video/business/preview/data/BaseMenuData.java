/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: BaseMenuData
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.preview.data;

public class BaseMenuData {
    public static final int VIEW_STATUS_ENABLE = 0;
    public static final int VIEW_STATUS_DISABLE = 1;

    protected int mViewId;
    protected int mViewStatus = VIEW_STATUS_ENABLE;

    public BaseMenuData(int viewId) {
        mViewId = viewId;
    }

    public int getViewId() {
        return mViewId;
    }

    public void setViewStatus(int viewStatus) {
        mViewStatus = viewStatus;
    }

    public int getViewStatus() {
        return mViewStatus;
    }
}
