/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetViewModel.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/25        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.ui.cardwidget.item

import android.app.Application
import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.cardwidget.util.getCardType
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.local.item.DisplayListItem
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.location.LocationManager
import com.oplus.gallery.business_lib.model.data.location.api.MatchLevel
import com.oplus.gallery.business_lib.model.data.location.utils.LocationHelper
import com.oplus.gallery.business_lib.viewmodel.base.BaseViewModel
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.DensityUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.model.WidgetModel
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.widgetpage.R
import com.oplus.gallery.widgetpage.WidgetConstants
import com.oplus.gallery.widgetpage.ext.getCardMode
import com.oplus.gallery.widgetpage.image.WidgetImageFileLoader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat

class WidgetViewModel(application: Application) : BaseViewModel(application) {
    companion object {
        private const val TAG = "WidgetViewModel"
        private const val INDEX_NOT_FOUND = -1
        private const val READ_CACHE = 0b01
        private const val READ_GEOCODER = 0b10
        private const val SEPARATOR_POINT = "·"
        private const val DATE_STYLE = "yyyy.MM.dd"
        private const val DEFAULT_INT = 0
        private const val DEFAULT_SPLIT_FIRST = 0
        private const val DEFAULT_SPLIT_SECOND = 1
        private const val DEFAULT_LENGTH = 2
        private const val TIME_SIZE_SMALL = "14dp"
        private const val TIME_SIZE_MID = "16dp"
        private const val TIME_SIZE_BIG = "32dp"
        private const val LOCATION_SIZE = "12dp"

        private val widgetConfigMap = mapOf(
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2 to WidgetConfig(
                R.dimen.widget_size_2,
                R.dimen.widget_size_2,
                R.drawable.widget_image_choice_2x2
            ),
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2 to WidgetConfig(
                R.dimen.widget_width_size_4,
                R.dimen.widget_size_2,
                R.drawable.widget_image_choice_4x2
            ),
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4 to WidgetConfig(
                R.dimen.widget_width_size_4,
                R.dimen.widget_height_size_4,
                R.drawable.widget_image_choice_4x4
            ),
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_2X2 to WidgetConfig(
                R.dimen.widget_size_2,
                R.dimen.widget_size_2,
                R.drawable.widget_image_custom_2x2
            ),
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X2 to WidgetConfig(
                R.dimen.widget_width_size_4,
                R.dimen.widget_size_2,
                R.drawable.widget_image_custom_4x2
            ),
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X4 to WidgetConfig(
                R.dimen.widget_width_size_4,
                R.dimen.widget_height_size_4,
                R.drawable.widget_image_custom_4x4
            )
        )

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        fun findNextValidIndexInDisplayList(displayList: List<DisplayListItem>, startIndex: Int, localMediaFilePaths: List<String>): Int {
            val displayListSize = displayList.size
            for (i in 0 until displayListSize) {
                val index = (startIndex + i) % displayListSize
                if ((displayList[index].invalid == GalleryStore.WidgetDisplayListColumns.INVALID_DEFAULT) &&
                    (localMediaFilePaths.contains(displayList[index].filePath))
                ) {
                    return index
                }
            }
            return INDEX_NOT_FOUND
        }

        private data class WidgetConfig(
            val widthDimenId: Int,
            val heightDimenId: Int,
            val defaultImageResId: Int
        )
    }

    private var widgetCode: String = TextUtil.EMPTY_STRING
    private var model: WidgetModel? = null
    private val internalLiveData = MutableLiveData<WidgetUpdateViewData?>()
    private var lastPostUpdateTaskTime = 0L
    val liveData: LiveData<WidgetUpdateViewData?> = internalLiveData
    private val defaultDensityDpi = DensityUtils.defaultDensityDpi

    init {
        decodeSession.changeLimitSize(1)
    }

    fun setWidgetCode(widgetCode: String) {
        this.widgetCode = widgetCode
        model = DataRepository.getAlbumModel(DataRepository.LocalAlbumModelGetter.TYPE_WIDGET_ALBUM, Bundle().apply {
            putString(DataRepository.KEY_PATH_STR, SourceConstants.Local.PATH_ALBUM_WIDGET_ANY.getChild(widgetCode).toString())
        }) as WidgetModel
    }

    /**
     * 卡片变为可见，刷新内容
     * 如果已显示照片且有下一张照片，则切换下一张照片
     */
    override fun onResume() {
        GLog.d(TAG, "onResume: $widgetCode")

        checkAndLaunchUpdate(false)
    }

    /**
     * 卡片变为不可见
     * 取消正在进行的非强制刷新
     */
    override fun onPause() {
        GLog.d(TAG, "onPause: $widgetCode")
    }

    /**
     * 强制刷新
     * 只有在当前显示的照片已移除的情况下才切换下一张照片
     */
    fun forceUpdate() {
        GLog.d(TAG, "forceUpdate: $widgetCode")
        checkAndLaunchUpdate(true)
    }

    /**
     * 重置LiveData
     */
    fun resetLiveData() {
        internalLiveData.value = null
    }

    private fun checkAndLaunchUpdate(forceUpdate: Boolean) {
        val time = System.currentTimeMillis()
        lastPostUpdateTaskTime = time
        launch(Dispatchers.ReloadTask) {
            /*
             * time记录每个抛进ReloadTask任务的时间，和lastPostUpdateTaskTime相同就是最新要执行的任务，
             * 该场景只需处理最新的任务即可，减少无用的任务执行
             */
            if (time != lastPostUpdateTaskTime) {
                GLog.w(TAG, "checkAndLaunchUpdate task is not last, lastPostUpdateTaskTime:$lastPostUpdateTaskTime, time:$time")
                return@launch
            }
            update(forceUpdate)
        }
    }

    private fun update(forceUpdate: Boolean) {
        GLog.d(TAG, "update: $widgetCode $forceUpdate")
        runCatching {
            val widgetConfig = getWidgetConfig(widgetCode)
            model?.let {
                it.reload()
                if (it.isContentLoaded().not()) { // 清除数据等情况导致卡片数据丢失，需要重新插入数据
                    GLog.w(TAG, "update: widget data not found, do as default")
                    it.addWidget(widgetCode)
                }
            }
            if (PermissionHelper.isDisagreeUserPolicy()) { // 相册未授权
                GLog.d(TAG, "update: no permission")
                postUpdateData(
                    DefaultWidgetUpdateViewData(
                        getNoPermissionCardLayoutName(widgetCode),
                        widgetCode,
                        WidgetConstants.Action.ACTION_GALLERY_AUTHORIZE_PERMISSIONS
                    )
                )
                return
            }
            if (!LocalMediaDataHelper.isExistLocalMedia()) {
                setNoDataCardWidget()
                return
            }
            val displayList = model?.getDisplayList() ?: emptyList()
            if (displayList.isEmpty()) {
                setEmptyCardWedget(widgetCode, widgetConfig)
            } else {
                updateWithDisplayList(forceUpdate, displayList)
            }
        }.onFailure {
            GLog.e(TAG, "update: exception ", it)
        }
    }

    private fun setEmptyCardWedget(widgetCode: String, widgetConfig: WidgetConfig) {
        if (widgetCode.getCardMode() == GalleryStore.WidgetSetColumns.MODE_CUSTOM) {
            GLog.d(TAG, "setEmptyCardWedget: no custom list data")
            postUpdateData(
                DefaultWidgetUpdateViewData(
                    getEmptyCardLayoutName(widgetCode),
                    widgetCode,
                    WidgetConstants.Action.ACTION_WIDGET_CUSTOM_PICTURE_MANAGER,
                    widgetConfig.defaultImageResId
                )
            )
        } else if (widgetCode.getCardMode() == GalleryStore.WidgetSetColumns.MODE_RECOMMENDED) {
            GLog.d(TAG, "setEmptyCardWedget: no recommended list data")
            postUpdateData(
                DefaultWidgetUpdateViewData(
                    getEmptyCardLayoutName(widgetCode),
                    widgetCode,
                    WidgetConstants.Action.ACTION_WIDGET_CHOICE_PICTURE_MANAGER,
                    widgetConfig.defaultImageResId
                )
            )
        }
    }

    /**
     * 相册无数据状态
     */
    private fun setNoDataCardWidget() {
        postUpdateData(
            DefaultWidgetUpdateViewData(
                getNoDataCardLayoutName(widgetCode),
                widgetCode,
                TextUtil.EMPTY_STRING,
            )
        )
        return
    }

    /**
     * 获取空状态卡片布局
     */
    private fun getEmptyCardLayoutName(widgetCode: String): String {
        return when (widgetCode.getCardType()) {
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2 -> WidgetConstants.Layout.RECOMMENDED_EMPTY
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2 -> WidgetConstants.Layout.RECOMMENDED_EMPTY_4_2
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4 -> WidgetConstants.Layout.RECOMMENDED_EMPTY_4_4

            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_2X2,
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X2,
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X4 -> WidgetConstants.Layout.CUSTOM_EMPTY

            else -> WidgetConstants.Layout.CUSTOM_EMPTY
        }
    }

    /**
     * 获取相册无数据状态卡片布局
     */
    private fun getNoDataCardLayoutName(widgetCode: String): String {
        return when (widgetCode.getCardType()) {
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2,
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_2X2 -> WidgetConstants.Layout.NO_DATA_EMPTY
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X2,
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2 -> WidgetConstants.Layout.NO_DATA_EMPTY_4_2
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X4,
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4 -> WidgetConstants.Layout.NO_DATA_EMPTY_4_4
            else -> WidgetConstants.Layout.NO_DATA_EMPTY
        }
    }

    /**
     * 获取无权限卡片布局
     */
    private fun getNoPermissionCardLayoutName(widgetCode: String): String {
        return when (widgetCode.getCardType()) {
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2,
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_2X2 -> WidgetConstants.Layout.NO_PERMISSION_2_2
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2,
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X2 -> WidgetConstants.Layout.NO_PERMISSION_4_2
            WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4,
            WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X4 -> WidgetConstants.Layout.NO_PERMISSION_4_4

            else -> WidgetConstants.Layout.NO_PERMISSION_2_2
        }
    }

    private fun updateWithDisplayList(forceUpdate: Boolean, displayList: List<DisplayListItem>) {
        val widgetConfig = getWidgetConfig(widgetCode)
        var previousIndex = INDEX_NOT_FOUND
        val lastDisplayFilePath = model?.getLastDisplayFilePath()
        val localMediaFilePaths = LocalMediaDataHelper.getFilePathsInLocalMedia(displayList.map { it.filePath })
        val currentIndex = lastDisplayFilePath?.let { lastDisplayFilePath ->
            displayList.forEachIndexed { index, item ->
                if (item.filePath == lastDisplayFilePath) {
                    previousIndex = index
                    return@forEachIndexed
                }
            }
            val startIndex = if (previousIndex >= 0) {
                if (forceUpdate) previousIndex else previousIndex + 1
            } else 0
            findNextValidIndexInDisplayList(displayList, startIndex, localMediaFilePaths)
        } ?: findNextValidIndexInDisplayList(displayList, 0, localMediaFilePaths)
        if (currentIndex < 0) {
            setEmptyCardWedget(widgetCode, widgetConfig)
            return
        }
        val nextIndex = findNextValidIndexInDisplayList(displayList, currentIndex + 1, localMediaFilePaths)
        val currentFilePath = displayList[currentIndex].filePath
        val nextFilePath = displayList[nextIndex].filePath
        GLog.d(
            TAG, "updateWithDisplayList: previousIndex=$previousIndex currentIndex=$currentIndex nextIndex=$nextIndex\n" +
                "previous=${PathMask.mask(lastDisplayFilePath)} \n" +
                "current=${PathMask.mask(currentFilePath)} \n" +
                "next=${PathMask.mask(nextFilePath)}"
        )
        val previousValid = if (previousIndex >= 0) {
            displayList[previousIndex].invalid == GalleryStore.WidgetDisplayListColumns.INVALID_DEFAULT
        } else false
        loadImageUriAndUpdate(
            lastDisplayFilePath,
            currentFilePath,
            nextFilePath,
            previousValid,
            widgetConfig,
            forceUpdate
        )
    }

    private fun loadImageUriAndUpdate(
        lastFilePath: String?,
        currentFilePath: String,
        nextFilePath: String,
        previousValid: Boolean,
        widgetConfig: WidgetConfig,
        forceUpdate: Boolean
    ) {
        if (!LocalMediaDataHelper.isExistLocalMedia()) {
            setNoDataCardWidget()
            return
        }
        val context = ContextGetter.context
        val itemPath = LocalMediaDataHelper.getPathByFilePath(currentFilePath)
        itemPath ?: let {
            GLog.d(TAG, "loadImageUriAndUpdate: path not match, do as default")
            setEmptyCardWedget(widgetCode, widgetConfig)
            return
        }
        val setPath = SourceConstants.Local.PATH_ALBUM_WIDGET_ANY.getChild(widgetCode)
        val displayFileChanged = lastFilePath != currentFilePath
        val currentDensity = context.resources.displayMetrics.densityDpi
        val targetWidth = (context.resources.getDimension(widgetConfig.widthDimenId) * defaultDensityDpi / currentDensity).toInt()
        val targetHeight = (context.resources.getDimension(widgetConfig.heightDimenId) * defaultDensityDpi / currentDensity).toInt()
        if ((lastFilePath != null) && displayFileChanged && previousValid) {
            /*
             * 有图片切换
             * 因为第一次跳过智能裁剪，所以加载上一张图片也相应跳过智能裁剪，否则第一张切到第二张会出现未裁剪图片突变成裁剪图片再切换的情况
             * 大多数情况下，显示过的上一张已经有智能裁剪的缓存文件了，所以这里也能拿到智能裁剪的结果
             */
            loadImage(context, lastFilePath, targetWidth, targetHeight, forceAutoCrop = false) { previousUri ->
                loadImage(context, currentFilePath, targetWidth, targetHeight) { currentUri ->
                    updateWithImage(
                        targetWidth,
                        currentUri,
                        previousUri,
                        currentFilePath,
                        itemPath,
                        setPath
                    )
                    loadImage(context, nextFilePath, targetWidth, targetHeight)
                }
            }
        } else { // 没有图片切换（第一次/刷新当前图片/当前展示图片被删除触发刷新）
            // 第一次跳过智能裁剪，减少操作完成到照片显示的耗时
            loadImage(context, currentFilePath, targetWidth, targetHeight, forceAutoCrop = previousValid) {
                updateWithImage(
                    targetWidth,
                    it,
                    null,
                    currentFilePath,
                    itemPath,
                    setPath
                )
                lastFilePath ?: loadImage(context, nextFilePath, targetWidth, targetHeight)
            }
        }
    }

    private fun loadImage(
        context: Context,
        filePath: String,
        targetWidth: Int,
        targetHeight: Int,
        forceAutoCrop: Boolean = true,
        callback: ((uri: Uri) -> Unit)? = null
    ) {
        WidgetImageFileLoader.loadImageFile(
            context,
            decodeSession,
            filePath,
            targetWidth,
            targetHeight,
            forceAutoCrop
        ) { uri -> callback?.invoke(uri) }
    }

    private fun updateWithImage(
        targetWidth: Int,
        currentUri: Uri,
        previousUri: Uri?,
        currentFilePath: String,
        itemPath: Path,
        setPath: Path
    ) {
        val extra = Bundle()
        extra.putInt(WidgetConstants.BundleKey.KEY_WIDGET_WIDTH, targetWidth)
        extra.putString(IntentConstant.WidgetConstant.KEY_WIDGET_MODE, widgetCode.getCardMode().toString())
        if (widgetCode.getCardMode() == GalleryStore.WidgetSetColumns.MODE_RECOMMENDED) {
            val localMediaItem = DataManager.getMediaObject(itemPath) as? LocalMediaItem
            val date = localMediaItem?.let {
                SimpleDateFormat(DATE_STYLE).format(it.dateTakenInMs)
            }
            val latLng = DoubleArray(DEFAULT_LENGTH)
            localMediaItem?.getLatLong(latLng)
            extra.putString(WidgetConstants.BundleKey.KEY_TIME, date ?: TextUtil.EMPTY_STRING)
            extra.putString(WidgetConstants.BundleKey.KEY_LOCATION, getLocationText(latLng) ?: TextUtil.EMPTY_STRING)
            var timeSize = TextUtil.EMPTY_STRING
            var locationSize = TextUtil.EMPTY_STRING
            var maskingLayer = DEFAULT_INT
            when (widgetCode.getCardType()) {
                WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2 -> {
                    timeSize = TIME_SIZE_SMALL
                    locationSize = LOCATION_SIZE
                    maskingLayer = R.drawable.widget_masking_layer_2x2
                }
                WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2 -> {
                    timeSize = TIME_SIZE_MID
                    locationSize = LOCATION_SIZE
                    maskingLayer = R.drawable.widget_masking_layer_4x2
                }
                WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4 -> {
                    timeSize = TIME_SIZE_BIG
                    locationSize = LOCATION_SIZE
                    maskingLayer = R.drawable.widget_masking_layer_4x4
                }
            }
            extra.putString(WidgetConstants.BundleKey.KEY_TIME_SIZE, timeSize)
            extra.putString(WidgetConstants.BundleKey.KEY_LOCATION_SIZE, locationSize)
            extra.putInt(WidgetConstants.BundleKey.KEY_MASKING_LAYER, maskingLayer)
        }
        postUpdateData(
            ImageWidgetUpdateViewData(
                currentUri,
                previousUri,
                itemPath,
                setPath,
                extra
            ),
            currentFilePath
        )
    }

    private fun getWidgetConfig(widgetCode: String): WidgetConfig {
        val widgetConfig = widgetConfigMap[widgetCode.getCardType()]
        widgetConfig ?: let {
            throw IllegalStateException("widget type not supported!")
        }
        return widgetConfig
    }

    private fun postUpdateData(viewData: WidgetUpdateViewData, currentFilePath: String? = null) {
        GLog.d(TAG, "postUpdateData: $viewData")
        internalLiveData.postValue(viewData)
        model?.notifyDisplayed(currentFilePath)
    }

    private fun getLocationText(latLng: DoubleArray?): String? {
        latLng?.let {
            it[DEFAULT_SPLIT_FIRST] = (it[DEFAULT_SPLIT_FIRST] * LocationHelper.PRECISION_CUT_OFF).toInt() / LocationHelper.PRECISION_CUT_OFF
            it[DEFAULT_SPLIT_SECOND] = (it[DEFAULT_SPLIT_SECOND] * LocationHelper.PRECISION_CUT_OFF).toInt() / LocationHelper.PRECISION_CUT_OFF
            return LocationManager.lookupAddress(
                it[DEFAULT_SPLIT_FIRST],
                it[DEFAULT_SPLIT_SECOND],
                READ_CACHE or READ_GEOCODER
            )?.getFullAddress(
                MatchLevel.MATCH_CITY,
                MatchLevel.MATCH_DISTRICT, SEPARATOR_POINT
            )
        }
        return null
    }

    override fun getTag(): String {
        return TAG
    }
}