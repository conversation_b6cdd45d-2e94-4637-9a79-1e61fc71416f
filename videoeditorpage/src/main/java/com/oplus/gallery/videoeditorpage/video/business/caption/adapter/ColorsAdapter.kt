/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : ColorsAdapter.kt
 ** Description : 文字颜色适配器
 ** Version     : 1.0
 ** Date        : 2025/4/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.caption.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.ColorViewHolder
import com.oplus.gallery.videoeditorpage.video.business.caption.adapter.data.ColorItem

/**
 * 颜色选择器适配器
 */
class ColorsAdapter(recyclerView: RecyclerView) : BaseAdapter<ColorItem, ColorViewHolder>(
    recyclerView,
    /**
     * 创建VH的回调接口
     */
    { parent: ViewGroup, viewType: Int ->  ColorViewHolder(parent) }
) {

    /**
     * 配置单选控件
     */
    override var multipleSelectable = false

    init {
        // 设置item数据差异计算回调
        diffCallback = ColorDiffCallback()
    }

    /**
     * 显示默认的颜色到UI控件
     */
    fun showColorValue(colorHexString: String) {
        data.forEachIndexed { index, colorItem ->
            val hexString = when (colorHexString.length) {
                // 替换 #AARRGGBB 的 AA 为 FF
                ARGB_LENGTH -> "${DEFAULT_ALPHA_HEX_STRING}${colorHexString.substring(REPLACE_ALPHA_POSITION)}"
                // 替换 #RRGGBB 为 #FFRRGGBB
                RGB_LENGTH -> "${DEFAULT_ALPHA_HEX_STRING}${colorHexString.substring(REPLACE_DEFAULT_POSITION)}"
                else -> colorHexString
            }
            if (hexString.uppercase() == colorItem.color) {
                selectedPosition = index
                return@forEachIndexed
            }
        }
    }

    /**
     * 文字样式数据变化差异回调器
     */
    private class ColorDiffCallback : DiffCallback<ColorItem>() {

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return (oldItem.color == newItem.color)
        }
    }

    companion object {
        /**
         * 替换有alpha通道字符串结束位置
         */
        private const val REPLACE_ALPHA_POSITION = 3

        /**
         * 替换无alpha通道字符串结束位置
         */
        private const val REPLACE_DEFAULT_POSITION = 1

        /**
         * RGB颜色值长度
         */
        private const val RGB_LENGTH = 7

        /**
         * ARGB颜色长度
         */
        private const val ARGB_LENGTH = 9

        /**
         * 默认的alpha值
         */
        private const val DEFAULT_ALPHA_HEX_STRING = "#FF"
    }
}