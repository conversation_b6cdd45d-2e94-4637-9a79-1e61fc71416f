/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - GallerySlowMotionControlView.java
 ** Description: for edit slow motion.
 ** Version: 1.0
 ** Date : 2017/12/05
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/12/05    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories.business.music;

import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.animation.Interpolator;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;
import androidx.core.view.animation.PathInterpolatorCompat;
import androidx.customview.widget.ExploreByTouchHelper;

import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.MINUTES_60;
import static com.oplus.gallery.videoeditorpage.memories.util.VideoEditorHelper.SECONDS_1000;

public class GalleryMusicTrimView extends View {
    private static final String TAG = "GalleryMusicTrimView";

    private static final int TOUCHED_LEFT_TRIM = 1000;
    private static final int TOUCHED_RIGHT_TRIM = 1001;

    private static final int ANIMATION_DURATION = 183; //ms

    private int mTrimLineHeight;
    private int mTrimLineWidth;
    private int mCurrentTrimLineWidth;
    private int mMaxTrimLineWidth;

    private int mNormalLineWidth;
    private int mDrawPadding;
    private float mLineStartY;
    private float mLineEndY;

    private int mTrimLineGap;
    private int mTouchGap;
    private long mDuration;

    private float mLeftTrim = 0;
    private float mRightTrim = 0;
    private int mCurrentPlayPos = 0;
    private float mRestoredLeftTrim = -1f;
    private float mRestoredRightTrim = -1f;

    private int mWidth = 0;
    private int mHeight = 0;

    private Paint mBackgroundLinePaint;
    private Paint mTrimLinePaint;

    private Rect mThumbSrcRect;
    private Bitmap mThumbBitmap;

    private Rect mTrimSrcRect;
    private Bitmap mTrimBitmapNormal;
    private Bitmap mTrimBitmapPressed;

    private float mActionDownX = 0;
    private boolean mTouchLeft = false;
    private boolean mTouchRight = false;
    private boolean mShowPosition = true;

    private TrimMusicChangeListener mSlowMotionChangeListener;
    private BarGraphAccessHelper mBarGraphAccessHelper;

    private AnimatorSet mProgressAnimator = new AnimatorSet();
    private Interpolator mProgressScaleInterpolator =
            PathInterpolatorCompat.create(0.33f, 0f, 0.67f, 1f);
    /**
     * 是否启动按压移动时放大动画
     */
    private boolean mIsStartEnlargeAnimWhenMove = false;
    /**
     * 是否启动松手时恢复动画
     */
    private boolean mIsStartReleaseAnimWhenEndMotion = false;

    public GalleryMusicTrimView(Context context) {
        super(context);
        init();
    }

    public GalleryMusicTrimView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public GalleryMusicTrimView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public GalleryMusicTrimView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        mBarGraphAccessHelper = new BarGraphAccessHelper(this);

        if (getImportantForAccessibility() == IMPORTANT_FOR_ACCESSIBILITY_AUTO) {
            setImportantForAccessibility(IMPORTANT_FOR_ACCESSIBILITY_YES);
        }

        mTrimLineWidth = getResources().getDimensionPixelSize(R.dimen.video_editor_trim_seek_line_width);
        mCurrentTrimLineWidth = mTrimLineWidth;
        mNormalLineWidth = getResources().getDimensionPixelSize(R.dimen.video_editor_trim_normal_line_width);
        mTrimLineHeight = getResources().getDimensionPixelSize(R.dimen.video_editor_trim_pos_line_height);
        mTouchGap = getResources().getDimensionPixelSize(R.dimen.video_editor_trim_touch_gap);
        mDrawPadding = getResources().getDimensionPixelSize(R.dimen.video_editor_music_trim_padding);

        ColorStateList stateList = getResources().getColorStateList(
                com.support.seekbar.R.color.coui_seekbar_background_selector, getContext().getTheme());
        int backgroundColor = getBackgroundColor(this, stateList, getContext().getColor(
                com.support.seekbar.R.color.coui_seekbar_background_color_normal));
        mBackgroundLinePaint = new Paint();
        mBackgroundLinePaint.setStrokeWidth(mNormalLineWidth);
        mBackgroundLinePaint.setColor(backgroundColor);
        mBackgroundLinePaint.setAntiAlias(true);
        mBackgroundLinePaint.setStrokeCap(Paint.Cap.ROUND);

        mTrimLinePaint = new Paint();
        mTrimLinePaint.setColor(COUIContextUtil.getAttrColor(getContext(), com.oplus.gallery.foundation.ui.R.attr.gColorPrimary,
                R.color.videoeditor_video_editor_theme_red));
        mTrimLinePaint.setAntiAlias(true);

        mThumbBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_video_editor_music_trim_seek_pos);
        mThumbSrcRect = new Rect(0, 0, mThumbBitmap.getWidth(), mThumbBitmap.getHeight());

        mTrimBitmapNormal = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_video_editor_music_trim_pos_normal);
        mTrimBitmapPressed = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_video_editor_music_trim_pos_pressed);
        mMaxTrimLineWidth = Math.max(mTrimBitmapPressed.getWidth() / 2, mTrimLineWidth);
        mTrimSrcRect = new Rect(0, 0, mTrimBitmapPressed.getWidth(), mTrimBitmapPressed.getHeight());
        initEnlargeAnimation();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (changed) {
            updateContentArea();
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if ((mLeftTrim < 0) || (mRightTrim < 0)) {
            return;
        }

        // draw back grey line
        canvas.drawLine(0f + mDrawPadding, mHeight / 2f, mWidth + mDrawPadding, mHeight / 2f, mBackgroundLinePaint);

        // draw trim line
        mTrimLinePaint.setStrokeWidth(mCurrentTrimLineWidth);
        canvas.drawLine(mLeftTrim + mDrawPadding, mHeight / 2f, mRightTrim + mDrawPadding, mHeight / 2f, mTrimLinePaint);

        // draw position bitmap
        if (mShowPosition) {
            Rect destRect = new Rect(mCurrentPlayPos - mThumbBitmap.getWidth() / 2 + mDrawPadding,
                    0, mCurrentPlayPos + mThumbBitmap.getWidth() / 2 + mDrawPadding, mHeight);
            canvas.drawBitmap(mThumbBitmap, mThumbSrcRect, destRect, null);
        }

        // draw trim pos bitmap
        Rect leftRect = new Rect((int) mLeftTrim - mTrimBitmapPressed.getWidth() / 2 + mDrawPadding,
                0, (int) mLeftTrim + mTrimBitmapPressed.getWidth() / 2 + mDrawPadding, mHeight);
        if (mTouchLeft) {
            canvas.drawBitmap(mTrimBitmapPressed, mTrimSrcRect, leftRect, null);
        } else {
            canvas.drawBitmap(mTrimBitmapNormal, mTrimSrcRect, leftRect, null);
        }

        Rect rightRect = new Rect((int) mRightTrim - mTrimBitmapPressed.getWidth() / 2 + mDrawPadding,
                0, (int) mRightTrim + mTrimBitmapPressed.getWidth() / 2 + mDrawPadding, mHeight);
        if (mTouchRight) {
            canvas.drawBitmap(mTrimBitmapPressed, mTrimSrcRect, rightRect, null);
        } else {
            canvas.drawBitmap(mTrimBitmapNormal, mTrimSrcRect, rightRect, null);
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float eventX = event.getX() - mDrawPadding;
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mActionDownX = eventX;
                GLog.d(TAG, "ACTION_DOWN mActionDownX:" + mActionDownX + " mTouchGap:" + mTouchGap);
                if ((mLeftTrim >= 0) && (mActionDownX > mLeftTrim - mTouchGap * 2)
                        && (mActionDownX < mLeftTrim + mTouchGap * 2)) {
                    mTouchLeft = true;
                    mShowPosition = false;
                    setPressed(true);
                    invalidate();
                }
                if ((mRightTrim >= 0) && (mActionDownX > mRightTrim - mTouchGap * 2)
                        && (mActionDownX < mRightTrim + mTouchGap * 2)) {
                    mTouchRight = true;
                    mShowPosition = false;
                    setPressed(true);
                }

                //mLeftTrim is very closer to mRight.choose the cloest one
                if (mTouchLeft && mTouchRight) {
                    float leftDistance = Math.abs(mActionDownX - mLeftTrim);
                    float rightDistance = Math.abs(mActionDownX - mRightTrim);
                    if (leftDistance <= rightDistance) {
                        mTouchRight = false;
                    } else {
                        mTouchLeft = false;
                    }
                    invalidate();
                } else if (mTouchLeft || mTouchRight) {
                    invalidate();
                }
                mIsStartEnlargeAnimWhenMove = mTouchLeft || mTouchRight;
                final ViewParent parent = getParent();
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(true);
                }
                break;
            case MotionEvent.ACTION_MOVE:
                GLog.d(TAG, "ACTION_MOVE  eventX:" + eventX + " mTouchLeft:" + mTouchLeft + " mTouchRight:" + mTouchRight);
                if (Math.abs(eventX - mActionDownX) > 1) {
                    if (mTouchLeft) {
                        GLog.d(TAG, "ACTION_MOVE  mTouchLeft");
                        if (eventX <= 0) {
                            mLeftTrim = 0;
                        } else if (eventX >= mRightTrim - mTrimLineGap) {
                            mLeftTrim = mRightTrim - mTrimLineGap;
                        } else {
                            mLeftTrim = eventX;
                        }
                        invalidate();
                        if (mSlowMotionChangeListener != null) {
                            mSlowMotionChangeListener.onTrimChange(mLeftTrim / (float) mWidth, -1);
                        }
                        startTouchAnimWhenMove();
                    } else if (mTouchRight) {
                        GLog.d(TAG, "ACTION_MOVE  mTouchRight");
                        if (eventX <= mLeftTrim + mTrimLineGap) {
                            mRightTrim = mLeftTrim + mTrimLineGap;
                        } else if (eventX >= mWidth) {
                            mRightTrim = mWidth;
                        } else {
                            mRightTrim = eventX;
                        }
                        invalidate();
                        if (mSlowMotionChangeListener != null) {
                            mSlowMotionChangeListener.onTrimChange(-1, mRightTrim / (float) mWidth);
                        }
                        startTouchAnimWhenMove();
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                final ViewParent viewparent = getParent();
                if (viewparent != null) {
                    viewparent.requestDisallowInterceptTouchEvent(false);
                }
                if (mTouchLeft || mTouchRight) {
                    mCurrentPlayPos = (int) mLeftTrim;
                    if (mSlowMotionChangeListener != null) {
                        mSlowMotionChangeListener.onTrimFinish(mTouchRight);
                    }
                }
                mTouchLeft = false;
                mTouchRight = false;
                mShowPosition = true;
                setPressed(false);
                invalidate();
                startReleaseAnimWhenEndMotion();
                break;
            case MotionEvent.ACTION_CANCEL:
                startReleaseAnimWhenEndMotion();
                break;
            default:
                break;
        }
        return true;
    }

    public void setSlowMotionChangeListener(TrimMusicChangeListener listener) {
        mSlowMotionChangeListener = listener;
    }

    public interface TrimMusicChangeListener {

        void onTrimChange(float leftPosition, float rightPosition);

        void onTrimFinish(boolean isTouchRight);
    }

    public void updateCurrentPos(float pos) {
        mCurrentPlayPos = (int) (pos * mWidth);
        invalidate();
    }

    private void updateContentArea() {
        mWidth = getWidth() - mDrawPadding * 2;
        mHeight = getHeight();
        mLineStartY = (mHeight - mTrimLineHeight) / 2f;
        mLineEndY = mHeight - mLineStartY;
        /*
        第一次指定trim的位置为左右端点
        如果不是第一次，非touch情况才根据进度指定trim位置
        */
        if ((mRestoredLeftTrim < 0) || (mRestoredRightTrim < 0)) {
            mLeftTrim = 0;
            mRightTrim = mWidth;
        } else if (!mTouchLeft && !mTouchRight) {
            mLeftTrim = mRestoredLeftTrim * mWidth;
            mRightTrim = mRestoredRightTrim * mWidth;
        }

        if (mDuration > 0) {
            float minPercent = ((float) MusicTrimFragment.TRIM_MUSIC_MIN_DURATION) / (float) mDuration;
            mTrimLineGap = (int) Math.ceil(minPercent * mWidth);
        }
        if (mTrimLineGap < mTrimLineWidth) {
            mTrimLineGap = mTrimLineWidth;
        }

        GLog.d(TAG, "initViewWidth() mWidth:" + mWidth + " mHeight:" + mHeight
                + " mLineStartY:" + mLineStartY + " mLineEndY:" + mLineEndY);
    }

    public void setTrimStatus(long duration, float leftTrim, float rightTrim, int pos) {
        GLog.d(TAG, "Playing->setTrimStatus() duration:" + duration + ", leftTrim = " + leftTrim + ", rightTrim = " + rightTrim + ",mWidth->" + mWidth);
        mDuration = duration;
        mRestoredLeftTrim = leftTrim;
        mRestoredRightTrim = rightTrim;
        mLeftTrim = leftTrim * mWidth;
        mRightTrim = rightTrim * mWidth;
        mCurrentPlayPos = (int) (((float) pos / duration) * mWidth);
        postInvalidate();
    }

    public void resetTrimStatus() {
        mLeftTrim = 0;
        mRightTrim = mWidth;
        mCurrentPlayPos = 0;
    }

    private String getTrimPosDescription(int id) {
        StringBuilder description = new StringBuilder();
        int totalTime = (int) mDuration / SECONDS_1000;
        int curTime = 0;
        Context context = getContext();
        switch (id) {
            case TOUCHED_LEFT_TRIM:
                curTime = (int) ((mLeftTrim / (float) mWidth) * mDuration) / SECONDS_1000;
                description.append(context.getString(R.string.videoeditor_music_trim_start_description));
                break;
            case TOUCHED_RIGHT_TRIM:
                curTime = (int) ((mRightTrim / (float) mWidth) * mDuration) / SECONDS_1000;
                description.append(context.getString(R.string.videoeditor_music_trim_end_description));
                break;
            default:
                break;
        }
        description.append(context.getResources().getString(R.string.videoeditor_talkback_seek_bar_duration,
                curTime / MINUTES_60, curTime % MINUTES_60,
                totalTime / MINUTES_60, totalTime % MINUTES_60));
        return description.toString();
    }

    @Override
    public boolean dispatchHoverEvent(MotionEvent event) {
        if ((mBarGraphAccessHelper != null)
                && mBarGraphAccessHelper.dispatchHoverEvent(event)) {
            GLog.d(TAG, "dispatchHoverEvent()");
            return true;
        }
        return super.dispatchHoverEvent(event);
    }


    private class BarGraphAccessHelper extends ExploreByTouchHelper {

        private final View mMyView;

        BarGraphAccessHelper(View view) {
            super(view);
            mMyView = view;
        }

        @Override
        protected int getVirtualViewAt(float x, float y) {
            float eventX = x - mDrawPadding;
            if ((eventX < 0) || (y < 0)
                    || (eventX > mMyView.getMeasuredWidth())
                    || (y > mMyView.getMeasuredHeight())) {
                return INVALID_ID;
            }
            int touchedId = INVALID_ID;
            if ((mLeftTrim >= 0) && (eventX > mLeftTrim - mTouchGap * 2)
                    && (eventX < mLeftTrim + mTouchGap * 2)) {
                touchedId = TOUCHED_LEFT_TRIM;
            }
            if ((mRightTrim >= 0) && (eventX > mRightTrim - mTouchGap * 2)
                    && (eventX < mRightTrim + mTouchGap * 2)) {
                if (touchedId == TOUCHED_LEFT_TRIM) {
                    float leftDistance = Math.abs(eventX - mLeftTrim);
                    float rightDistance = Math.abs(eventX - mRightTrim);
                    if (leftDistance <= rightDistance) {
                        touchedId = TOUCHED_LEFT_TRIM;
                    } else {
                        touchedId = TOUCHED_RIGHT_TRIM;
                    }
                } else {
                    touchedId = TOUCHED_RIGHT_TRIM;
                }
            }
            GLog.d(TAG, "getVirtualViewAt() ACTION_DOWN x:" + x + " touchedId:" + touchedId);
            return touchedId;
        }

        @Override
        protected void getVisibleVirtualViews(List<Integer> virtualViewIds) {
            virtualViewIds.add(TOUCHED_LEFT_TRIM);
            virtualViewIds.add(TOUCHED_RIGHT_TRIM);
        }

        @Override
        public void onInitializeAccessibilityNodeInfo(View view, AccessibilityNodeInfoCompat accessibilityNodeInfo) {
            super.onInitializeAccessibilityNodeInfo(view, accessibilityNodeInfo);
            accessibilityNodeInfo.addAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD);
            accessibilityNodeInfo.addAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD);
        }

        @Override
        protected void onPopulateEventForVirtualView(int id, AccessibilityEvent accessibilityEvent) {
            if (id == INVALID_ID) {
                throw new IllegalArgumentException("Invalid virtual view id");
            }
            accessibilityEvent.setClassName(getClass().getName());
            if (id == TOUCHED_LEFT_TRIM) {
                accessibilityEvent.setContentDescription(getTrimPosDescription(TOUCHED_LEFT_TRIM));
            } else if (id == TOUCHED_RIGHT_TRIM) {
                accessibilityEvent.setContentDescription(getTrimPosDescription(TOUCHED_RIGHT_TRIM));
            }
        }

        @Override
        @SuppressWarnings("deprecation")
        protected void onPopulateNodeForVirtualView(int id, @NonNull AccessibilityNodeInfoCompat accessibilityNodeInfo) {
            if (id == INVALID_ID) {
                throw new IllegalArgumentException("Invalid virtual view id");
            }
            accessibilityNodeInfo.setClassName(getClass().getName());
            if (id == TOUCHED_LEFT_TRIM) {
                accessibilityNodeInfo.setContentDescription(getTrimPosDescription(TOUCHED_LEFT_TRIM));
                accessibilityNodeInfo.setBoundsInParent(getItemBounds(id));
                accessibilityNodeInfo.addAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD);
                accessibilityNodeInfo.addAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD);
            } else if (id == TOUCHED_RIGHT_TRIM) {
                accessibilityNodeInfo.setContentDescription(getTrimPosDescription(TOUCHED_RIGHT_TRIM));
                accessibilityNodeInfo.setBoundsInParent(getItemBounds(id));
                accessibilityNodeInfo.addAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD);
                accessibilityNodeInfo.addAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD);
            }
        }

        @Override
        public boolean performAccessibilityAction(View view, int action, Bundle bundle) {
            if (super.performAccessibilityAction(view, action, bundle)) {
                return true;
            }
            switch (action) {
                case AccessibilityNodeInfo.ACTION_SCROLL_FORWARD:
                    GLog.d(TAG, "performAccessibilityAction() ACTION_SCROLL_FORWARD");
                    return true;
                case AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD:
                    GLog.d(TAG, "performAccessibilityAction() ACTION_SCROLL_BACKWARD");
                    return true;
                    default:
                        break;
            }
            return false;
        }

        @Override
        protected boolean onPerformActionForVirtualView(int virtualViewId, int action, Bundle bundle) {
            return false;
        }

        private Rect getItemBounds(int id) {
            Rect rect = new Rect();
            switch (id) {
                case TOUCHED_LEFT_TRIM:
                    rect.set((int) mLeftTrim + mDrawPadding - mTrimLineWidth, (int) mLineStartY,
                            (int) mLeftTrim + mDrawPadding + mTrimLineWidth, (int) mLineEndY);
                    break;
                case TOUCHED_RIGHT_TRIM:
                    rect.set((int) mRightTrim + mDrawPadding - mTrimLineWidth, (int) mLineStartY,
                            (int) mRightTrim + mDrawPadding + mTrimLineWidth, (int) mLineEndY);
                    break;
                default:
                    break;
            }
            GLog.d(TAG, "getItemBounds() id:" + id + " rect:" + rect);
            return rect;
        }
    }

    /**
     * 进度条放大动画
     */
    private void initEnlargeAnimation() {
        mProgressAnimator.setInterpolator(mProgressScaleInterpolator);
        //enlarge
        ValueAnimator backgroundEnlargeAnimator = ValueAnimator.ofInt(mCurrentTrimLineWidth, mMaxTrimLineWidth);
        backgroundEnlargeAnimator.setDuration(ANIMATION_DURATION);
        backgroundEnlargeAnimator.addUpdateListener(valueAnimator -> {
            mCurrentTrimLineWidth = (int) valueAnimator.getAnimatedValue();
            invalidate();
        });
        mProgressAnimator.play(backgroundEnlargeAnimator);
    }

    private void startProgressEnlargeAnimation() {
        if (mProgressAnimator.isRunning()) {
            mProgressAnimator.cancel();
        }
        mProgressAnimator.start();
    }

    /**
     * 抬手后，恢复进度条大小
     */
    private void startProgressReleaseAnimation() {
        if (mProgressAnimator.isRunning()) {
            mProgressAnimator.cancel();
        }
        AnimatorSet releaseAnimatorSet = new AnimatorSet();
        releaseAnimatorSet.setInterpolator(mProgressScaleInterpolator);
        //enlarge
        ValueAnimator animator = ValueAnimator.ofInt(mCurrentTrimLineWidth, mTrimLineWidth);
        animator.setDuration(ANIMATION_DURATION);
        animator.addUpdateListener(valueAnimator -> {
            mCurrentTrimLineWidth = (int) valueAnimator.getAnimatedValue();
            invalidate();
        });
        releaseAnimatorSet.play(animator);
        releaseAnimatorSet.start();
    }

    private int getBackgroundColor(View view, ColorStateList colorStateList, int defaultValue) {
        if (colorStateList == null) {
            return defaultValue;
        }
        int[] state = view.getDrawableState();
        return colorStateList.getColorForState(state, defaultValue);
    }

    private void startReleaseAnimWhenEndMotion() {
        if (mIsStartReleaseAnimWhenEndMotion) {
            mIsStartReleaseAnimWhenEndMotion = false;
            startProgressReleaseAnimation();
        }
    }

    private void startTouchAnimWhenMove() {
        if (mIsStartEnlargeAnimWhenMove) {
            mIsStartEnlargeAnimWhenMove = false;
            mIsStartReleaseAnimWhenEndMotion = true;
            startProgressEnlargeAnimation();
        }
    }
}
