/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IPipeline.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/13
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_PIPELINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		OPLUS_ARCH_PIPELINE
 *********************************************************************************/

package com.oplus.gallery.bus_lib

import com.oplus.gallery.bus_lib.annotations.FinderType

interface IBus {
    fun register(subscriber: Any, @FinderType finderType: Int = FinderType.CLASS_METHOD_FINDER)
    fun unregister(subscriber: Any)
    fun post(event: Any)
    fun postSticky(event: Any)
}