/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - VideoEditorCornerPoint.java
 * * Description: VideoEditorCornerPoint.
 * * Version: 1.0
 * * Date : 2017/11/04
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2020/10/21    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.ui

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import com.oplus.gallery.videoeditorpage.R

class CornerPoint @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    private val mPaint = Paint()
    private val mCornerPointColor: Int
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        mPaint.color = mCornerPointColor
        mPaint.style = Paint.Style.FILL
        canvas.drawCircle(width / 2.0f, height / 2.0f, width / 2.0f, mPaint)
    }

    fun setShowState(isVisible: Boolean) {
        if (isVisible) {
            this.visibility = View.VISIBLE
        } else {
            this.visibility = View.GONE
        }
    }

    init {
        val a = context.obtainStyledAttributes(attrs, R.styleable.CornerPoint)
        mCornerPointColor = a.getColor(R.styleable.CornerPoint_cornerPointColor, Color.TRANSPARENT)
        a.recycle()
        mPaint.isFilterBitmap = true
        mPaint.isAntiAlias = true
    }
}