/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseVideoEffect.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.google.gson.annotations.SerializedName

open class BaseVideoEffect(name: String?) {
    @SerializedName("name")
    var name: String? = name

    @SerializedName("type")
    var type: Int = -1
    var playType: Int = -1
    var playDuration: Long = -1
    var duration: Long = -1
    var inTime: Long = -1
        set(value) {
            field = value
            changeInPoint(value)
        }
    var outTime: Long = -1
        set(value) {
            field = value
            changeOutPoint(value)
        }
    var trackIndex: Int = -1

    open fun changeInPoint(inTime: Long) {
        // do in sub class
    }

    open fun changeOutPoint(inTime: Long) {
        // do in sub class
    }

    open fun reAlignVideoSticker(inTimeNs: Long) {
        // do in sub class
    }

    companion object {
        const val TYPE_PACKAGED_FX = 0
        const val TYPE_BUILT_IN_FX = 1
        const val TYPE_CUSTOMER_FX = 2

        const val PLAY_TYPE_IN = 0
        const val PLAY_TYPE_OUT = 1
        const val PLAY_TYPE_COMPOSE = 2
    }
}