/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CartoonAttachDraw.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.engine.ui.fx;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.Size;

import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.storyboard.BaseStoryBoardVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.video.business.track.model.ClipModel;
import com.oplus.gallery.videoeditorpage.video.business.track.util.TrackHelper;

import java.util.List;

public class CartoonAttachDraw implements IAttachDraw {
    private static final String TAG = "CartoonAttachDraw";
    private static final long TIME_SHOW_PERFECT = 10_000;

    private Rect mRect;
    private Rect mRectRight;
    private Paint mPaint;
    private Paint mPaintRight;

    @Override
    public int myBusiness() {
        return ClipModel.BusinessAttached.BUSINESS_CARTOON;
    }

    @Override
    public void onDraw(ClipModel clipModel, Canvas canvas) {

        if ((clipModel.getType() == ClipModel.ClipType.CLIP_VIDEO)
            || (clipModel.getType() == ClipModel.ClipType.CLIP_PICTURE)) {
            IClip clip = clipModel.getClip();
            if ((clip instanceof IVideoClip)) {
                IVideoClip videoClip = (IVideoClip) clip;
                List<BaseVideoClipEffect> baseVideoClipEffects
                    = videoClip.getEffectsByType(BaseStoryBoardVideoClipEffect.StoryboardType.TYPE_CARTOON);
                if ((baseVideoClipEffects == null) || (baseVideoClipEffects.isEmpty())) {
                    return;
                }
                if (mPaint == null) {
                    mPaint = new Paint();
                    mPaint.setAntiAlias(true);
                    mPaint.setDither(true);
                }
                int w = canvas.getWidth();
                int h = canvas.getHeight();
                if (mRect == null) {
                    mRect = new Rect(0, 0, w, h);
                }

                for (BaseVideoClipEffect baseVideoClipEffect : baseVideoClipEffects) {
                    long effectDuration = baseVideoClipEffect.getEffectPlayDuration();
                    long clipDuration = videoClip.getDuration();
                    int playType = baseVideoClipEffect.getEffectPlayType();

                    if (effectDuration >= clipDuration - TIME_SHOW_PERFECT) {
                        baseVideoClipEffect.setEffectPlayDuration(clipDuration);
                        effectDuration = clipDuration;
                    }
                    int effectWidth = (int) (TrackHelper.INSTANCE.getLengthByCurrentDuration(effectDuration, ContextGetter.context));
                    if ((clipDuration <= 0) || (effectDuration <= 0)) {
                        GLog.e(TAG, "refreshCartoonCover continue clipDuration:" + clipDuration);
                        continue;
                    }
                    drawRect(playType, canvas, effectWidth, new Size(w, h));
                }
            }
        }
    }

    private void drawRect(int playType, Canvas canvas, int effectWidth, Size size) {
        switch (playType) {
            case StreamingConstant.Cartoon.TYPE_IN:
                mPaint.setColor(ContextGetter.context.getColor(R.color.color_90e16060));
                mRect.set(0, 0, effectWidth, size.getHeight());
                canvas.drawRect(mRect, mPaint);
                break;
            case StreamingConstant.Cartoon.TYPE_COMPOUND:
                mPaint.setColor(ContextGetter.context.getColor(R.color.color_904eab9b));
                mRect.set(0, 0, effectWidth, size.getHeight());
                canvas.drawRect(mRect, mPaint);
                break;
            case StreamingConstant.Cartoon.TYPE_OUT:
                if (mPaintRight == null) {
                    mPaintRight = new Paint();
                    mPaintRight.setAntiAlias(true);
                    mPaintRight.setDither(true);
                    mPaintRight.setColor(ContextGetter.context.getColor(R.color.color_906c77dc));
                }
                if (mRectRight == null) {
                    mRectRight = new Rect(size.getWidth() - effectWidth, 0, size.getWidth(), size.getHeight());
                } else {
                    mRectRight.set(size.getWidth() - effectWidth, 0, size.getWidth(), size.getHeight());
                }
                canvas.drawRect(mRectRight, mPaintRight);
                break;
            default:
                break;
        }
    }
}