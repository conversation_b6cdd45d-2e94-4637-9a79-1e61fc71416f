/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetTrackHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/12/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/12/15        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.track

import android.database.Cursor
import androidx.annotation.WorkerThread
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper.getLocalMediaItem
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.WidgetSetColumns
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.widgetpage.track.WidgetTrackConstant.EventId
import com.oplus.gallery.widgetpage.track.WidgetTrackConstant.Key
import com.oplus.gallery.widgetpage.track.WidgetTrackConstant.Value

object WidgetTrackHelper {
    private fun track(eventId: String, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
                event = eventId,
                type = WidgetTrackConstant.TYPE_WIDGET,
                func = func
        )
    }

    fun getTrackModeName(mode: Int): String {
        return when (mode) {
            WidgetSetColumns.MODE_RECOMMENDED -> Value.MODE_RECOMMENDED
            WidgetSetColumns.MODE_CUSTOM -> Value.MODE_CUSTOM
            else -> TextUtil.EMPTY_STRING
        }
    }

    fun trackAddWidget() {
        track(EventId.WIDGET_ADD_OR_REMOVE) {
            it.putProperty(Key.ADD_OR_REMOVE, Value.ADD)
            it.save()
        }
    }

    fun trackRemoveWidget() {
        track(EventId.WIDGET_ADD_OR_REMOVE) {
            it.putProperty(Key.ADD_OR_REMOVE, Value.REMOVE)
            it.save()
        }
    }

    fun trackSelectWidgetMode(previousMode: Int, selectedMode: Int) {
        track(EventId.WIDGET_MODE_SELECT) {
            val type = if (previousMode == WidgetSetColumns.MODE_DEFAULT) Value.TYPE_MODE_NEW else Value.TYPE_MODE_CHANGE
            it.putProperty(Key.TYPE, type)
            it.putProperty(Key.MODE, getTrackModeName(selectedMode))
            it.save()
        }
    }

    fun trackWidgetClick(mode: Int, itemPath: Path) {
        track(EventId.WIDGET_CLICK) {
            it.putProperty(Key.MODE, getTrackModeName(mode))
            val labels = getLabelIdsOfMediaItem(itemPath)?.joinToString("|")
            it.putProperty(Key.LABEL, labels)
            it.save()
        }
    }

    private fun getLabelIdsOfMediaItem(path: Path): List<Int>? {
        return QueryReq.Builder<List<Int>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
            .setProjection(arrayOf(ScanLabelColumns.SCENE_ID))
            .setWhere(GalleryStore.ScanLabel.DATA + ConstantUtils.EQUAL_TO)
            .setWhereArgs(arrayOf(getLocalMediaItem(path)!!.filePath))
            .setConvert { cursor: Cursor ->
                mutableListOf<Int>().apply {
                    while (cursor.moveToNext()) {
                        cursor.getInt(0).takeIf {
                            LabelDictionary.getLabelName(it).isNullOrEmpty().not()
                        }?.run {
                            add(this)
                        }
                    }
                }
            }
            .build().exec()
    }
}