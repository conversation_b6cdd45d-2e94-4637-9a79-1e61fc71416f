/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ExampleInstrumentedTest.kt
 ** Description : ExampleInstrumentedTest
 ** Version     : 1.0
 ** Date        : 2024/03/18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/03/18      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.foundation.libcolordirect

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4

import org.junit.Test
import org.junit.runner.RunWith

import org.junit.Assert.*

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class ExampleInstrumentedTest {
    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.oplus.gallery.foundation.libcolordirect.test", appContext.packageName)
    }
}