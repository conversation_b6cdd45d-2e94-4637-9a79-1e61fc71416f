/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamTimeline.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/27
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/27		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.memories.engine.meicam

import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsTimelineVideoFx
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.DOLBY_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.SDR_VIDEO_TYPE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio
import kotlin.math.roundToInt

class MeicamTimeline(
    val nvsTimeline: NvsTimeline,
    videoWidth: Int,
    videoHeight: Int
) {

    companion object {
        const val TAG = "MeicamTimeline"
    }

    var width: Int = videoWidth
        private set
    var height: Int = videoHeight
        private set

    var videoCodecType = SDR_VIDEO_TYPE

    fun addEffectToTimeline(meicamTimelineEffect: MeicamTimelineEffect?) {
        if (meicamTimelineEffect == null) {
            GLog.e(TAG, "addEffectToTimeline, meicamTimelineEffect is null")
            return
        }
        if (nvsTimeline != null) {
            var result: NvsTimelineVideoFx? = null
            when (meicamTimelineEffect.type) {
                BaseVideoEffect.TYPE_PACKAGED_FX -> {
                    result = nvsTimeline.addPackagedTimelineVideoFx(
                        meicamTimelineEffect.inTime,
                        meicamTimelineEffect.outTime - meicamTimelineEffect.inTime,
                        meicamTimelineEffect.name
                    )
                }

                BaseVideoEffect.TYPE_BUILT_IN_FX -> {
                    result = nvsTimeline.addBuiltinTimelineVideoFx(
                        meicamTimelineEffect.inTime,
                        meicamTimelineEffect.outTime - meicamTimelineEffect.inTime,
                        meicamTimelineEffect.name
                    )
                }

                BaseVideoEffect.TYPE_CUSTOMER_FX -> GLog.e(TAG, "addEffectToTimeline, BaseVideoClipEffect.TYPE_CUSTOMER_FX")

                else -> GLog.e(TAG, "addEffectToTimeline , type not found")
            }
            meicamTimelineEffect.nvsTimelineVideoFx = result
            if (meicamTimelineEffect is MeicamStickerEffect) {
                meicamTimelineEffect.syncPropertyToNvs(nvsTimeline.videoRes.imageWidth, nvsTimeline.videoRes.imageHeight)
            }
        }
    }

    fun removeEffectFromTimeline(meicamTimelineEffect: MeicamTimelineEffect?) {
        meicamTimelineEffect?.let {
            nvsTimeline.removeTimelineVideoFx(it.nvsTimelineVideoFx)
        }
    }

    /**
     * 设置timeline的视频宽高
     * 非杜比视频的宽必须是16的倍数
     * 其他宽高都是8的倍数
     * 此方法会默认处理为倍数值, 实际宽高需要从getWidth,getHeight接口重新读取
     * @param width
     * @param height
     */
    fun changeVideoSize(width: Int, height: Int) {
        val useLimit = videoCodecType.and(DOLBY_VIDEO_TYPE) != DOLBY_VIDEO_TYPE
        val size = VideoRatio.correctSize(width, height, useLimit)
        this.width = size.width
        this.height = size.height
        nvsTimeline.changeVideoSize(this.width, this.height)
        GLog.d(TAG, "changeVideoSize before w: h = $width: $height, after width = ${this.width} height = ${this.height}")
    }

    /**
     * 设置timeline的视频宽必须是16的倍数,高必须是8的倍数
     * 此方法会默认处理为倍数值, 实际宽高需要从getWidth,getHeight接口重新读取
     * @param width
     * @param height
     */
    fun changeVideoSize(width: Float, height: Float) {
        changeVideoSize(width.roundToInt(), height.roundToInt())
    }
}