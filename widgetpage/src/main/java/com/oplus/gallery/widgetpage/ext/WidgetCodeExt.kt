/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WidgetCodeExt.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/6/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2022/6/29		1.0		OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.widgetpage.ext

import com.oplus.cardwidget.util.getCardType
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.widgetpage.WidgetConstants

/**
 * 通过widgetCode获取cardMode
 */
fun String.getCardMode(): Int {
    return when (this.getCardType()) {
        WidgetConstants.Type.WIDGET_TYPE_CHOICE_2x2,
        WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x2,
        WidgetConstants.Type.WIDGET_TYPE_CHOICE_4x4 -> GalleryStore.WidgetSetColumns.MODE_RECOMMENDED
        WidgetConstants.Type.WIDGET_TYPE_CUSTOM_2X2,
        WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X2,
        WidgetConstants.Type.WIDGET_TYPE_CUSTOM_4X4 -> GalleryStore.WidgetSetColumns.MODE_CUSTOM
        else -> GalleryStore.WidgetSetColumns.MODE_RECOMMENDED
    }
}
