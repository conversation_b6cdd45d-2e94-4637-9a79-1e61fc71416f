/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoAlphaSpringAnimation.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-05-23
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/05/23  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photo_page.ui.section.pagecontainer

import android.view.View
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.oplus.gallery.tools.createAlphaSpringAnimation

/**
 * 大图封装的 Alpha 弹性动画，
 * 支持原动画参数反转动画，并移除老的结束回调及设置新的结束回调
 */
internal class PhotoAlphaSpringAnimation(
    private val view: View
) {
    /**
     * 当前是否在做显示动画
     */
    val isShowing get() = springAnimation?.let { it.isRunning && (it.spring.finalPosition == TARGET_ALPHA_SHOW) } ?: false

    /**
     * 当前是否在做隐藏动画
     */
    val isHiding get() = springAnimation?.let { it.isRunning && (it.spring.finalPosition == TARGET_ALPHA_HIDE) } ?: false


    /**
     * 目前正在执行的动画
     * @see animateToTarget
     */
    private var springAnimation: COUISpringAnimation? = null

    /**
     * 当前动画的结束回调，用于反转动画时移除
     * @see
     */
    private var currentEndListener: COUIDynamicAnimation.OnAnimationEndListener? = null

    /**
     * 做显示动画，如果当前正在自行隐藏动画，会停止隐藏动画（移除回调）并从当前 alpha 开始做显示动画。
     */
    fun show(
        bounce: Float,
        response: Float,
        onStart: (() -> Unit)? = null,
        onEnd: ((canceled: Boolean) -> Unit)? = null,
        onUpdate: ((animation: COUISpringAnimation?, value: Float) -> Unit)? = null
    ) {
        animateToTarget(TARGET_ALPHA_SHOW, bounce, response, onStart, onEnd, onUpdate)
    }

    /**
     * 做隐藏动画，如果当前正在自行显示动画，会停止显示动画（移除回调）并从当前 alpha 开始做隐藏动画。
     */
    fun hide(
        bounce: Float,
        response: Float,
        onStart: (() -> Unit)? = null,
        onEnd: ((canceled: Boolean) -> Unit)? = null,
        onUpdate: ((animation: COUISpringAnimation?, value: Float) -> Unit)? = null,
    ) {
        animateToTarget(TARGET_ALPHA_HIDE, bounce, response, onStart, onEnd, onUpdate)
    }

    /**
     * 取消当前动画，当前动画的 onEnd 回调会被执行。
     */
    fun cancel() {
        springAnimation?.cancel()
    }

    private fun animateToTarget(
        targetValue: Float,
        bounce: Float,
        response: Float,
        onStart: (() -> Unit)? = null,
        onEnd: ((canceled: Boolean) -> Unit)? = null,
        onUpdate: ((animation: COUISpringAnimation?, value: Float) -> Unit)? = null
    ) {
        // 如果正在执行的动画目标值与指定的目标值相同则跳过（正在做相同的动画）
        if (springAnimation?.spring?.finalPosition == targetValue) return

        val endListener = COUIDynamicAnimation.OnAnimationEndListener { _, canceled, _, _ ->
            // 动画结束，移除目前的动画
            springAnimation = null
            currentEndListener = null
            onEnd?.invoke(canceled)
        }

        val updateListener = COUIDynamicAnimation.OnAnimationUpdateListener { animation, value, _ ->
            onUpdate?.invoke(animation as? COUISpringAnimation, value)
        }

        if (springAnimation == null) {
            // 先前未进行动画
            springAnimation = view.createAlphaSpringAnimation(targetValue, bounce, response)
            springAnimation?.addUpdateListener(updateListener)
            springAnimation?.addEndListener(endListener)
            currentEndListener = endListener
            springAnimation?.start()
        } else {
            // 正进行反向的动画，清空回调，设置新的回调
            currentEndListener?.let { listener -> springAnimation?.removeEndListener(listener) }
            springAnimation?.cancel()
            springAnimation?.addUpdateListener(updateListener)
            springAnimation?.addEndListener(endListener)
            currentEndListener = endListener
            springAnimation?.animateToFinalPosition(targetValue)
        }
        onStart?.invoke()
    }

    companion object {
        private const val TAG = "PhotoAlphaSpringAnimation"

        private const val TARGET_ALPHA_SHOW = 1f
        private const val TARGET_ALPHA_HIDE = 0f
    }
}