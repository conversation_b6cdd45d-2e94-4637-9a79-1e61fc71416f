/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:  ScrollDetectorHScrollView
 ** Description:
 ** Version: 1.0
 ** Date : 2025/7/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                      <date>      <version>       <desc>
 **  <EMAIL>      2025/7/12      1.0     NEW
 ****************************************************************/


package com.oplus.gallery.videoeditorpage.widget

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.HorizontalScrollView

/**
 * 可以监听滚动的水平滚动控件
 */
class ScrollDetectorHScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : HorizontalScrollView(context, attrs, defStyleAttr) {

    // 滚动监听器，用于监听滚动事件
    var onScrollListener: OnScrollListener? = null
    // 记录最近一次滚动的水平位置
    private var lastScrollX = 0

    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            if (msg.what == MSG_SCROLL_STOP) {
                if (scrollX == lastScrollX) {
                    onScrollListener?.onStop(scrollX)
                } else {
                    lastScrollX = scrollX
                    sendEmptyMessageDelayed(MSG_SCROLL_STOP, SCROLL_STOP_DETECT_DELAY)
                }
            }
        }
    }

    init {
        isHorizontalScrollBarEnabled = true
        overScrollMode = View.OVER_SCROLL_NEVER // 禁用弹性滚动
        setOnTouchListener { _, motionEvent ->
            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 按下时的操作
                    onScrollListener?.onDown(scrollX)
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    handler.removeMessages(MSG_SCROLL_STOP)
                    lastScrollX = scrollX
                    handler.sendEmptyMessageDelayed(MSG_SCROLL_STOP, SCROLL_STOP_DETECT_DELAY)
                }
            }
            false
        }
        setOnScrollChangeListener { _, scrollX, _, oldScrollX, _ ->
            if (scrollX != oldScrollX) {
                onScrollListener?.onMove(scrollX)
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        handler.removeCallbacksAndMessages(null) // 移除所有待处理消息
        setOnTouchListener(null)
        setOnScrollChangeListener(null)
        onScrollListener = null
    }

    /**
     * 监听滚动事件的接口
     */
    interface OnScrollListener {
        /**
         * 当滚动停止时回调
         * @param scrollX 当前滚动的水平位置
         */
        fun onStop(scrollX: Int)

        /**
         * 当正在滚动时调用
         *
         * @param scrollX 当前滚动的水平位置
         */
        fun onMove(scrollX: Int)

        /**
         * 当按下滚动控件时调用
         * @param scrollX 当前滚动的水平位置
         */
        fun onDown(scrollX: Int)
    }

    companion object {
        // 滚动停止监听延迟时间
        private const val SCROLL_STOP_DETECT_DELAY = 100L

        // 滚动停止的消息代码，用于内部消息处理
        private const val MSG_SCROLL_STOP = 1
    }
}