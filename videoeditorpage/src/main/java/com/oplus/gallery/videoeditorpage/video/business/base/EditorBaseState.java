/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: EditorBaseState
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.business.base;


import static com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine.FRAME_DURATION;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;

import androidx.annotation.CallSuper;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.uikit.responsiveui.IAppUiObserver;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoTrack;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.base.IBaseClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.timeline.ITimeline;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.video.VideoClipPlaybackMode;
import com.oplus.gallery.videoeditorpage.base.EditorStateContext;
import com.oplus.gallery.videoeditorpage.common.event.EventIndex;
import com.oplus.gallery.videoeditorpage.common.event.LiveDataBus;
import com.oplus.gallery.videoeditorpage.utlis.ClickUtil;
import com.oplus.gallery.videoeditorpage.utlis.ScreenUtils;
import com.oplus.gallery.videoeditorpage.video.app.EditorActivity;
import com.oplus.gallery.videoeditorpage.video.business.adjust.EditorAdjustState;
import com.oplus.gallery.videoeditorpage.video.business.caption.EditorCaptionState;
import com.oplus.gallery.videoeditorpage.video.business.filter.EditorFilterState;
import com.oplus.gallery.videoeditorpage.video.business.joint.JointHelper;
import com.oplus.gallery.videoeditorpage.video.business.manager.EnginePlayingTimeManager;
import com.oplus.gallery.videoeditorpage.video.business.menu.EditorMenuState;
import com.oplus.gallery.videoeditorpage.video.business.output.OperationSaveHelper;
import com.oplus.gallery.videoeditorpage.video.business.output.OperationType;
import com.oplus.gallery.videoeditorpage.video.business.output.SaveInfo;
import com.oplus.gallery.videoeditorpage.video.business.picker.data.PickerItemInfo;
import com.oplus.gallery.videoeditorpage.video.business.preview.EditorPreviewView;
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackScene;
import com.oplus.gallery.videoeditorpage.video.business.track.config.EditorTrackUIConfig;
import com.oplus.gallery.videoeditorpage.video.business.track.interfaces.IClip;
import com.oplus.gallery.videoeditorpage.video.business.trim.IEffectClipResponder;
import com.oplus.gallery.videoeditorpage.video.controler.EditorControlView;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

import kotlin.Pair;

/**
 * 编辑业务基类 对应ViewModel
 *
 * @param <T> UI控制器基类 对应 View
 */
public abstract class EditorBaseState<T extends EditorBaseUIController>
        implements EditorEngine.ITimelineChangedListener, IAppUiObserver {
    public static final String TAG = "EditorBaseState";

    @IntDef({EditorBaseState.ActionType.TYPE_CHANGE_ASPECT_RATIO, EditorBaseState.ActionType.TYPE_BACK_PRESSED})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ActionType {
        int TYPE_CHANGE_ASPECT_RATIO = 1;
        int TYPE_BACK_PRESSED = 1 << 1;
        int TYPE_COMPILE = 1 << 2;
        int TYPE_PLAY_VIDEO = 1 << 3;
    }

    /** 点击片段进入剪辑、音乐、文字页时需要时码线所在片段强选中的标记 */
    public static final String CLIP_STRONG_STATE_FLAG = "CLIP_STRONG_STATE_FLAG";

    protected T mUIController;
    protected Context mContext;
    protected EditorControlView mEditorControlView;
    protected EditorPreviewView mEditorPreviewView;
    protected FrameLayout mFbBottom;
    protected EditorEngine mEditorEngine;

    protected OperationSaveHelper mOperationSaveHelper;
    /**
     * 引擎播放时间管理器，用于获取视频播放引擎的播放位置和时长等
     */
    protected EnginePlayingTimeManager mEnginePlayingTimeManager;
    //顶部操作栏，有返回按钮，生成按钮等,用于控制显示隐藏状态
    protected RelativeLayout mActionBar;
    private String mEditorTypeName;
    private boolean mIsDestroy = false;
    private StateLifeChangeListener mStateLifeChangeListener;

    private String mTabId = null; //对贴纸操作时对应的tab，区分是在文字还是贴纸tab子界面进行的操作
    /** 业务切换时携带的路由参数 */
    private Object mRouteParams;

    /**
     * 状态基类构造函数
     *
     * @param editorType        编辑类型（实际为子类的TAG）
     * @param context           上下文
     * @param editorControlView 编辑的控制视图
     */
    public EditorBaseState(String editorType, Context context, EditorControlView editorControlView) {
        mEditorTypeName = editorType;
        mContext = context;
        mEditorControlView = editorControlView;
        mEditorEngine = mEditorControlView.getEditorEngine();
        mOperationSaveHelper = mEditorControlView.getOperationSaveHelper();
        mEnginePlayingTimeManager = mEditorControlView.getEnginePlayingTimeManager();
    }

    public void setRouteParams(Object routeParams) {
        this.mRouteParams = routeParams;
    }

    public void setFbBottom(FrameLayout layout, EditorPreviewView previewView) {
        mFbBottom = layout;
        this.mEditorPreviewView = previewView;
    }

    /**
     * 设置顶部actionBar变量
     *
     * @param actionBar
     */
    public void setActionBar(RelativeLayout actionBar) {
        this.mActionBar = actionBar;
    }

    @Nullable
    public Object getRouteParams() {
        return mRouteParams;
    }

    public EditorEngine getEditorEngine() {
        return mEditorEngine;
    }

    public EditorControlView getEditorControlView() {
        return mEditorControlView;
    }

    /**
     * 获取引擎播放时间管理器实例的方法。
     */
    public EnginePlayingTimeManager getEnginePlayingTimeManager() {
        return mEnginePlayingTimeManager;
    }

    public void stopEngine() {
        EditorEngine editorEngine = getEditorEngine();
        if (editorEngine == null) {
            return;
        }
        if (!editorEngine.isPlaying()) {
            return;
        }
        editorEngine.stopPlayer();
    }

    /**
     * 创建视图
     * @return 返回视图
     */
    protected abstract T createUIController();

    protected EditorTrackScene getEditorTrackScene() {
        return EditorTrackScene.NONE;
    }

    public EditorTrackUIConfig getEditorTrackUIConfig() {
        return EditorTrackUIConfig.getUIConfig(getEditorTrackScene());
    }

    /**
     * 是否需要显示撤销、恢复按钮
     *
     * @return 返回操作是否成功
     */
    public abstract boolean showOperaIcon();

    public boolean keepPreviewTrackView() {
        return false;
    }

    @CallSuper
    public void create() {
        GLog.d(TAG, "create, name = " + mEditorTypeName + ", this = " + this);

        // createUIController从构造函数调整到这里，是因为子类在重写该方法时，子类的属性参数不能在super构造函数中使用
        mUIController = createUIController();
        if (mUIController != null) {
            mUIController.createView();
        }
    }

    @CallSuper
    public void resume(boolean isActivityResume) {
        GLog.d(TAG, "resume, name = " + mEditorTypeName + ", isActivityResume = " + isActivityResume + ", this = " + this);
        if (mUIController != null) {
            mUIController.resume(isActivityResume);
            EditorEngine engine = getEditorEngine();
            if (engine == null) {
                GLog.e(TAG, "engine is null");
                return;
            }
            ITimeline timeline = engine.getCurrentTimeline();
            if (timeline == null) {
                GLog.e(TAG, "timeline is null");
                return;
            }
            mUIController.resetMuteByTimeline(timeline);
        }

        if (mEditorControlView != null) {
            if ((mFbBottom != null) && (mFbBottom.getVisibility() == View.VISIBLE)) {
                mEditorControlView.postDelayed(() -> LiveDataBus.get().with(EventIndex.NAVIGATIONBAR_VISIBILITY_CHANGED, Boolean.class).postValue(false), EditorBaseUIController.ANIMATION_FADE_IN_DURATION);
            } else {
                mEditorControlView.onLayoutChange(this, ScreenUtils.isNavigationBarShowing(), !isActivityResume && !isPreview());
            }
        }

        if ((mEditorEngine != null) && !mEditorEngine.containTimelineChangeListener(this)) {
            mEditorEngine.addTimelineChangeListener(this);
        }
    }


    @CallSuper
    public void pause(boolean isActivityPause) {
        GLog.d(TAG, "pause, name = " + mEditorTypeName + ", isActivityPause = " + isActivityPause + ", this = " + this);
        stopEngine();
        if (mUIController != null) {
            mUIController.pause(isActivityPause);
        }
        if (mEditorEngine != null) {
            mEditorEngine.removeTimelineListener(this);
        }
    }

    @CallSuper
    public void destroy() {
        GLog.d(TAG, "destroy, name = " + mEditorTypeName + ", mIsDestroy = " + mIsDestroy + ", this = " + this);

        if (mIsDestroy) {
            return;
        }
        stopEngine();
        if (mUIController != null) {
            mUIController.destroyView();
        }
        if (mEditorEngine != null) {
            mEditorEngine.removeTimelineListener(this);
        }
        mIsDestroy = true;
    }

    public void playbackTimeline() {
        if (mEditorEngine == null) {
            GLog.e(TAG, LogFlag.DL, "editorEngine is null");
            return;
        }
        long position = mEditorEngine.getTimelineCurrentPosition();
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            GLog.e(TAG, LogFlag.DL, "timeline is null");
            return;
        }
        if (position >= (timeline.getDuration() - FRAME_DURATION)) {
            position = 0;
        }
        Pair<Long, Long> playStartEndTimePair = getPlayStartEndTimePair(position);
        if ((playStartEndTimePair == null) && (mEnginePlayingTimeManager != null)) {
            playStartEndTimePair = mEnginePlayingTimeManager.calculateStartEndTimeByPlaybackMode(position, -1);
        }
        if (playStartEndTimePair != null) {
            mEditorEngine.startPlayer(playStartEndTimePair.getFirst(), playStartEndTimePair.getSecond());
        }
    }

    public boolean addMore() {
        if (ClickUtil.isDoubleClick()) {
            GLog.w(TAG, "onAddButtonClick() isDoubleClick");
            return false;
        }
        stopEngine();
        JointHelper.gotoGallerySelection((Context) getEditorStateContext(), this);

        return true;
    }

    public EditorMenuState getMenuState() {
        if (mEditorControlView != null) {
            return (EditorMenuState) mEditorControlView.getEditorStateManager().getMenuState();
        }
        return null;
    }

    public void seekTimeline(long position, int flag, boolean scrollTimelineView) {
        if (mEditorControlView != null) {
            mEditorControlView.seekTimeline(position, flag, scrollTimelineView);
        }
    }

    public void setMute(boolean isMuteHandle) {
        GLog.d(TAG, "setMute, isMuteHandle : " + isMuteHandle);
        ITimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline == null) {
            return;
        }
        //        int videoTrackCount = timeline.getVideoTrackCount();
        //        for (int index = 0; index < videoTrackCount; index++) {
        IVideoTrack videoTrack = timeline.getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "main track is null!");
            return;
        }
        videoTrack.setMuted(isMuteHandle);
        List<IVideoClip> clipList = videoTrack.getClipList();
        if ((clipList != null) && (clipList.size() > 0)) {
            for (IVideoClip videoClip : clipList) {
                if (videoClip != null) {
                    videoClip.setMuted(isMuteHandle);
                }
            }
        }
        if (isMuteHandle) {
            saveCurrentTimeline(OperationType.SILENCE);
        } else {
            saveCurrentTimeline(OperationType.ORIGINAL_AUDIO);
        }
    }

    public boolean isDestroy() {
        return mIsDestroy;
    }

    public void backAndForwardHandle(boolean isBack) {
    }

    public T getUIController() {
        return mUIController;
    }

    public void clickCancel() {
        GLog.d(TAG, "clickCancel, name = " + mEditorTypeName + ", this = " + this);
        stopEngine();
        if ((mUIController != null) && mUIController.isBusying()) {
            GLog.w(TAG, "mUIController is busying now, do not response click cancel!");
            return;
        }
        mEditorControlView.getEditorStateManager().finish(this);
    }

    public void clickDone() {
        GLog.d(TAG, "clickDone, name = " + mEditorTypeName + ", this = " + this);
        stopEngine();
        if ((mUIController != null) && mUIController.isBusying()) {
            GLog.w(TAG, "mUIController is busying now, do not response click done!");
            return;
        }

        mEditorControlView.getEditorStateManager().finish(this);
    }

    /**
     * 当前的state播放单个视频片段还是所有片段
     */
    public VideoClipPlaybackMode getVideoPlaybackMode() {
        return VideoClipPlaybackMode.MULTI_VIDEO_CLIPS;
    }

    public void onSeekTimelinePosition(long position, long duration) {
        // Specific subclasses override if necessary
    }

    public void setStateLifeChangeListener(StateLifeChangeListener onStateLifeChangeListener) {
        this.mStateLifeChangeListener = onStateLifeChangeListener;
    }

    public StateLifeChangeListener getStateLifeChangeListener() {
        return mStateLifeChangeListener;
    }

    protected EditorStateContext getEditorStateContext() {
        return (EditorStateContext) mContext;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof EditorBaseState) {
            if (!TextUtils.isEmpty(mEditorTypeName) && mEditorTypeName.equals(((EditorBaseState) obj).mEditorTypeName)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public int hashCode() {
        return ((!TextUtils.isEmpty(mEditorTypeName)) ? mEditorTypeName.hashCode() : 0);
    }

    public boolean onBackPressed() {
        GLog.d(TAG, "onBackPressed");
        clickCancel();
        return true;
    }

    public void onNavigationBarVisibilityChanged(boolean isShowing) {
        mUIController.onNavigationBarVisibilityChanged(isShowing);
    }

    public void setTabId(String tabId) {
        mTabId = tabId;
    }

    public String getTabId() {
        return mTabId;
    }

    public boolean isPreview() {
        return false;
    }


    public List<IVideoClip> getCurrentTimelineClips() {
        EditorEngine engine = getEditorEngine();
        if (engine == null) {
            GLog.e(TAG, "editor engine is null");
            return null;
        }
        ITimeline timeline = engine.getCurrentTimeline();
        if (timeline == null) {
            GLog.e(TAG, "timeline is null");
            return null;
        }
        IVideoTrack videoTrack = timeline.getVideoTrack(0);
        if (videoTrack == null) {
            GLog.e(TAG, "video track is null");
            return null;
        }

        List<IVideoClip> clips = videoTrack.getClipList();

        return clips;
    }

    @CallSuper
    @Override
    public void onCurrentTimelineChanged(ITimeline timeline, boolean updateTimelineView) {
        // when timeline changed(by draft init or undo/redo),
        // state usually need reset view for current timeline.
        if (timeline == null) {
            return;
        }
        if (mUIController != null) {
            mUIController.resetStateByCurrentTimeline(timeline);
        }
    }

    public OperationSaveHelper getOperationSaveHelper() {
        return mOperationSaveHelper;
    }

    public void saveCurrentTimeline() {
        if (mOperationSaveHelper != null) {
            mOperationSaveHelper.saveCurrentTimeline();
        }
    }

    public void saveCurrentTimeline(String extra) {
        if (mOperationSaveHelper != null) {
            mOperationSaveHelper.saveCurrentTimeline(null, extra);
        }
    }

    /**
     * 保存当前时间线
     *
     * @param operationType 操作类型
     */
    public void saveCurrentTimeline(OperationType operationType) {
        if (mOperationSaveHelper != null) {
            mOperationSaveHelper.saveCurrentTimeline(operationType, null);
        }
    }

    public void restoreCurrentTimeline(boolean updateTimelineView) {
        if (mOperationSaveHelper != null) {
            mOperationSaveHelper.restoreUndoByTop(updateTimelineView);
        }
    }

    /**
     * 获取当前view对应的页面等级，第二级页面等级需要隐藏状态栏
     *
     * @return
     */
    public PageLevelEnum getPageLevel() {
        return PageLevelEnum.PAGE_LEVEL_FIRST;
    }

    public void onPlayPositionChange(long currentPosition) {
    }

    public void stopTimelineViewFling() {

    }

    public void updateFilter() {

    }

    public void updateUndo(SaveInfo saveInfo) {
    }

    public void onPipClipSelected(boolean isSelected, IVideoClip videoClip) {

    }

    public void pickMaterialComplete(int requestCode, int resultCode, Intent data, List<PickerItemInfo> pickerList) {

    }

    public boolean isEditing() {
        return false;
    }

    public boolean canDoAction(int actionType) {
        return true;
    }

    public int getHeight() {
        if (mUIController != null) {
            return mUIController.getHeight();
        }

        return 0;
    }

    public interface StateLifeChangeListener {
        void onReady();
    }

    public void playAndPause(int playAndPauseType) {

    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {

    }

    /**
     * 判断是否需要跳过动画
     * 目前三种情况为true
     * 1. 无调节/滤镜/文字/音乐 效果时，从其他页面点击调节/滤镜/文字/音乐时，自动跳转到二级页的场景
     * 2. 从大图点击设为壁纸 这种直接跳过视频编辑一级页的场景
     * 3. 从设置-声音与振动-电话铃声-视频铃声-本地视频，选择视频后直接进入编辑的场景
     *
     * @return 如果需要跳过动画则返回true，否则返回false
     */
    public boolean isSkipAnim() {
        return false;
    }

    /**
     * 当前页面是否显示二级菜单栏。用于页面切换时判断是否需要做二级菜单栏的动画
     * 在一级页并且当前时码线对应的视频有特效时，才显示二级菜单栏
     *
     * @return true 有特效，false 没有特效
     */
    public boolean isShowSubMenu() {
        return (getPageLevel() == PageLevelEnum.PAGE_LEVEL_FIRST)
                && ((this instanceof EditorCaptionState) || (this instanceof EditorAdjustState) || (this instanceof EditorFilterState))
                && getVideoFxs().stream().anyMatch(it ->
                it.getInPoint() <= mEditorEngine.getTimelineCurrentPosition()
                        && it.getOutPoint() >= mEditorEngine.getTimelineCurrentPosition());
    }

    protected List<IBaseClip> getVideoFxs() {
        return null;
    }

    public EditorActivity requireEditorActivity() {
        if (mContext instanceof EditorActivity) {
            return (EditorActivity) mContext;
        }
        throw new IllegalStateException("EditorBaseState require EditorActivity");
    }

    @Override
    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig appUiConfig) {
        if (mUIController != null) {
            mUIController.onAppUiStateChanged(appUiConfig);
        }
    }

    /**
     * 如果当前状态会临时更改时间线，则返回更改前的备份时间线
     *
     * @return null 表示不会临时修改时间线，否则返回修改前的备份时间线
     */
    public ITimeline getBackupTimeline() {
        return null;
    }

    /**
     * 获取片段响应器列表，用于响应视频片段发生变更时，对滤镜、调节特效做处理
     * 如：导入新视频、删除视频、分割、变速、调整视频长度、排序
     *
     * @return 片段响应器列表
     */
    public List<IEffectClipResponder> getClipResponders() {
        return null;
    }

    /**
     * 获取正在处理的片段，为方便获取二级页面处理的片段，其他页面（不论层级）亦可实现，但需注意返回正确的片段
     * 比如：调节视频音量返回的是VideoClip，调节音频音量返回的是AudioClip
     *
     * @return 正在处理的片段
     */
    public IClip getProcessingClip() {
        return null;
    }

    /**
     * 获取播放开始结束时间对
     */
    protected Pair<Long, Long> getPlayStartEndTimePair(long startPosition) {
        return null;
    }
}
