/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FilterSelector.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/11/8 11:46
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2024/11/8  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aicomposition.filterselector

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.Outline
import android.graphics.Rect
import android.graphics.RectF
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.INVISIBLE
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewGroup.VISIBLE
import android.view.ViewOutlineProvider
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.doOnEnd
import androidx.core.view.doOnNextLayout
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.processing.AiCompositionMaskView
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.track.AiCompositionTrackData
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.ui.AiCompositionInterpolateStrategy
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.vm.FilterRecommendHelper
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.foundation.ui.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import kotlin.math.ceil

/**
 * 滤镜选择器
 */
internal open class FilterSelector(
    private val sectionBus: ISectionBus<EditingFragment, EditingVM>
) : AiCompositionMaskView.OnSlideListener, FilterAdapter.OnFilterSelectChangeListener {

    /**
     * Ai构图是否支持自动调节
     *
     *   注: 轻量os不支持自动调节
     */
    private val isSupportAutoAdjust: Boolean by lazy {
        sectionBus.viewBus.get<Boolean>(TopicID.AiComposition.TOPIC_IS_SUPPORT_AUTO_ADJUST) ?: false
    }

    private var filterContainer: ViewGroup? = null

    /**
     * 滤镜标题容器
     */
    private var filterTitleContainer: ViewGroup? = null

    /**
     * 滤镜指示器容器
     */
    private var filterListContainer: ViewGroup? = null

    /**
     * 大师滤镜图标
     */
    private var filterTitleIcon: ImageView? = null

    /**
     * 滤镜名称
     */
    private var filterTitleText: TextView? = null

    /**
     * 滤镜名称前缀文字，用于显示CC、NC、NH
     */
    private var filterTitlePrefixText: TextView? = null

    /**
     *  滤镜图标/前缀与滤镜名称之间的分割点
     */
    private var filterTitlePoint: TextView? = null

    /**
     * 滤镜指示器控件
     */
    private var filterListView: EditorLinearListView? = null

    /**
     * 滤镜居中吸附指示器
     */
    private var filterCenterIndicator: ImageView? = null

    /**
     * 保存当前滤镜选择器的位置，默认初始值为0
     */
    private var filterSelectedPosition: Int = 0

    /**
     * 将要选中的滤镜位置，当前正在滚动动画中，正滚向想要选中的滤镜位置
     */
    private var selectingPosition: Int = filterSelectedPosition
        get() {
            if (filterListView?.scrollState != RecyclerView.SCROLL_STATE_SETTLING) {
                return filterSelectedPosition
            }
            return field
        }

    /**
     * 标记当前是否需要展示“横滑切换滤镜”指引
     */
    private var needShowFilterGuide = true

    private var filterAdapter: FilterAdapter? = null

    private var filterData: MutableList<FilterViewData> = mutableListOf()

    private val titleOffsetAnimator = ValueAnimator.ofInt(0, 0).apply {
        duration = AiCompositionInterpolateStrategy.ANIMATION_DURATION
        interpolator = AiCompositionInterpolateStrategy.INTERPOLATOR
        doOnEnd { updateFilterListPosition(animationProperties.clipRect.current) }
    }

    /**
     * 滤镜标题定时消失任务
     */
    private var fadeOutRunnable: Runnable = Runnable {
        //执行了3s消失逻辑，则标记“横滑切换滤镜”不需要再显示
        needShowFilterGuide = false
        filterTitleContainer?.animate()?.alpha(0f)?.apply {
            duration = FILTER_TITLE_FADE_DURATION
            interpolator = FILTER_TITLE_FADE_INTERPOLATOR
            start()
        }
    }

    /**
     * 滤镜选择变化监听
     */
    private var selectChangeListener: FilterAdapter.OnFilterSelectChangeListener? = null

    /**
     * 裁切区域变化监听
     */
    private val clipRectChangeObserver: (RectF) -> Unit = { clipRect ->
        updateFilterListPosition(clipRect)
    }

    /**
     * 滤镜列表滚动监听，滚动时触发子view重绘
     */
    private val filterListViewScrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            recyclerView.forEach {
                it.invalidate()
            }
        }

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            updateFilterViewBackground(newState)
        }
    }

    /**
     * 模块初始化，需要在数据加载好后再做初始化
     *
     * @param maskView 滤镜选择控件容器
     */
    fun init(container: View, maskView: AiCompositionMaskView?, viewDataList: List<FilterRecommendHelper.FilterArgument>) {
        val config = sectionBus.hostInstance.getCurrentAppUiConfig()
        updateViews(container, config)
        updateFilterTitleIfVisible()
        maskView?.setOnSlideListener(this)
        setFilterList(viewDataList, config)
        AiCompositionTrackData.filterKey = EMPTY_STRING
        AiCompositionTrackData.filterType = sectionBus.rootView.resources.getString(R.string.track_ai_composition_filter_name_no_filter)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updateViews(container: View, config: AppUiResponder.AppUiConfig) {
        val isSmallLandscape = isSmallScreenLandscape(config)
        //更新容器及标题
        updateTitleViews(container, isSmallLandscape)
        //更新指示器
        updateFilterListViews(container, isSmallLandscape)
        //设置滤镜列表
        resetFilterListView(isSmallLandscape)
    }

    /**
     * 如果过滤器容器可见，则更新过滤器标题。
     */
    private fun updateFilterTitleIfVisible() {
        filterAdapter?.selectedPosition?.also { selectedPosition ->
            if ((filterContainer?.visibility ?: View.INVISIBLE) == View.VISIBLE) {
                updateFilterTitle(selectedPosition)
            }
        }
    }

    /**
     * 更新滤镜标题
     *
     */
    private fun updateTitleViews(container: View, isSmallLandscape: Boolean) {
        val filterContainerVisibility = filterContainer?.visibility ?: View.INVISIBLE
        filterContainer = container.findViewById<ViewGroup?>(R.id.filter_selector_container).apply {
            layoutParams.height = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_container_small_landscape_height,
                R.dimen.photo_editor_ai_composition_filter_container_height
            )
            visibility = filterContainerVisibility
            if (filterContainerVisibility == View.VISIBLE) {
                updateFilterListPosition(animationProperties.clipRect.current)
            }
        }
        filterTitleContainer = container.findViewById<ViewGroup>(R.id.filter_title_container).apply {
            setBackgroundColor(context.getColor(R.color.photo_editor_ai_composition_filter_title_bg_color1))
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    val cornerRadius = context.resources.getDimension(R.dimen.photo_editor_ai_composition_filter_title_corner_radius)
                    outline.setRoundRect(Rect(0, 0, width, height), cornerRadius)
                }
            }
            layoutParams.height = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_title_small_landscape_height,
                R.dimen.photo_editor_ai_composition_filter_title_height
            )
            updateLayoutParams<MarginLayoutParams> {
                topMargin = getDimensionBasedOnOrientation(
                    isSmallLandscape,
                    R.dimen.photo_editor_ai_composition_filter_title_small_landscape_top_margin,
                    R.dimen.photo_editor_ai_composition_filter_title_top_margin
                )
            }
            clipToOutline = true
            visibility = INVISIBLE
        }
        filterTitleIcon = container.findViewById(R.id.filter_title_icon)
        filterTitleText = container.findViewById<TextView>(R.id.filter_title_text).apply {
            val textSize = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_title_text_small_landscape_size,
                R.dimen.photo_editor_ai_composition_filter_title_text_size
            ).toFloat()
            setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        }
        filterTitlePoint = container.findViewById(R.id.filter_title_point)
        filterTitlePrefixText = container.findViewById<TextView>(R.id.filter_title_prefix_text).apply {
            val textSize = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_title_text_small_landscape_size,
                R.dimen.photo_editor_ai_composition_filter_title_text_size
            ).toFloat()
            setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        }
    }

    /**
     * 更新滤镜列表控件
     *
     */
    private fun updateFilterListViews(container: View, isSmallLandscape: Boolean) {
        filterListContainer = container.findViewById<ViewGroup?>(R.id.filter_indicator_container).apply {
            layoutParams.height = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_list_small_landscape_height,
                R.dimen.photo_editor_ai_composition_filter_list_height
            )
            updateLayoutParams<MarginLayoutParams> {
                topMargin = getDimensionBasedOnOrientation(
                    isSmallLandscape,
                    R.dimen.photo_editor_ai_composition_filter_list_small_landscape_top_margin,
                    R.dimen.photo_editor_ai_composition_filter_list_top_margin
                )
            }
        }
        filterListView?.removeOnScrollListener(filterListViewScrollListener)
        filterListView?.setOnCenterViewChangedListener(null)
        filterListView?.setOnAdsorptionChangeListener(null)
        filterListView = container.findViewById<EditorLinearListView>(R.id.composition_filter_list).apply {
            val itemSpacing = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_title_small_landscape_spacing,
                R.dimen.photo_editor_ai_composition_filter_title_spacing
            )
            setItemSpacing(itemSpacing)
        }
        filterCenterIndicator = container.findViewById<ImageView>(R.id.filter_center_indicator).apply {
            layoutParams.width = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_list_center_small_landscape_indicator_height,
                R.dimen.photo_editor_ai_composition_filter_list_center_indicator_height
            )
            layoutParams.height = getDimensionBasedOnOrientation(
                isSmallLandscape,
                R.dimen.photo_editor_ai_composition_filter_list_center_small_landscape_indicator_height,
                R.dimen.photo_editor_ai_composition_filter_list_center_indicator_height
            )
        }
    }

    /**
     * 重新设置filterListview的属性配置
     */
    private fun resetFilterListView(isSmallLandscape: Boolean) {
        filterListView?.let { listView ->
            listView.setHorizontalFlingFriction(EditorLinearListView.ADJUST_FRICTION_COEFFICIENT)
            listView.isDisplayInCenter = true
            listView.adapter = filterAdapter
            filterAdapter?.isSmallScreenLandscape = isSmallLandscape
            listView.setKeepFocusItemPosition(filterSelectedPosition)
            listView.scrollToPosition(filterSelectedPosition)
            filterAdapter?.notifyDataSetChanged()

            fun performClick(view: View, position: Int) {
                if (position != filterAdapter?.selectedPosition) {
                    view.performClick()
                }
            }

            listView.setOnAdsorptionChangeListener { view, position ->
                performClick(view, position)
            }
            listView.setOnCenterViewChangedListener { view, oldPosition, newPosition ->
                GLog.d(TAG, LogFlag.DL) { "onCenterViewChanged. $oldPosition -> $newPosition" }
                //当前位置发生变化，已展示过“横滑切换滤镜”引导语，标记隐藏引导语
                needShowFilterGuide = false
                if (filterListView?.isScrollingFromUser == false) {
                    GLog.d(TAG, LogFlag.DL) { "onCenterViewChanged. isScrollingFromUser is false, skip." }
                    return@setOnCenterViewChangedListener
                }
                view.post {
                    performClick(view, newPosition)
                }
            }
            // 滚动时触发
            listView.addOnScrollListener(filterListViewScrollListener)
            listView.post {
                listView.scrollToPosition(filterSelectedPosition)
                filterListViewScrollListener.onScrolled(listView, 0, 0)
            }
            updateFilterItemBackgroundUntilSuccess()
        }
    }

    /**
     * 设置滤镜数据列表
     *
     * @param viewDataList 滤镜列表数据
     */
    private fun setFilterList(viewDataList: List<FilterRecommendHelper.FilterArgument>, config: AppUiResponder.AppUiConfig) {
        val listView = filterListView ?: return
        val filterTitle = listView.context.getString(R.string.picture3d_editor_text_ai_composition_filter_title)
        filterData.clear()
        // 1, 第一个添加原始无滤镜效果
        filterData.add(
            FilterViewData(
                filterTitle,
                DEFAULT_FILTER_TYPE,
                R.drawable.none_filter_icon
            )
        )
        // 2, 第二个"自动调节"选项; (轻量os没有调节选项)
        if (isSupportAutoAdjust) {
            filterData.add(
                FilterViewData(
                    listView.context.getString(R.string.picture3d_editor_text_ai_composition_auto_adjust_title),
                    DEFAULT_FILTER_TYPE,
                    R.drawable.picture3d_ai_composition_auto_adjust
                )
            )
        }
        // 3, 后续就是正常的滤镜推荐列表
        viewDataList.forEach { filterArgument ->
            filterData.add(
                FilterViewData(
                    filterArgument.filter.name,
                    filterArgument.filter.masterType,
                    filterArgument.filter.iconId
                )
            )
        }
        filterAdapter = FilterAdapter(listView.context, filterData).apply {
            isSmallScreenLandscape = isSmallScreenLandscape(config)
            listView.setKeepFocusItemPosition(0)
            select(0)
            selectChangeListener = this@FilterSelector
        }
        listView.adapter = filterAdapter

        updateFilterTitle(0)
    }

    /**
     * 显示滤镜选择器控件
     */
    fun showSelector() {
        updateFilterListPosition(animationProperties.clipRect.current)
        sectionBus.viewBus.subscribeT(TopicID.Preview.TOPIC_PREVIEW_CLIP_RECT, clipRectChangeObserver)
        // 晚一帧显示，避免点对比时出现闪的问题
        sectionBus.rootView.post {
            filterContainer?.let {
                it.alpha = MathUtils.ZERO_F
                it.animate()
                    .alpha(MathUtils.ONE_F)
                    .setDuration(FILTER_CONTAINER_FADE_IN_DURATION)
                    .setInterpolator(COUIEaseInterpolator())
                    .withStartAction { it.visibility = View.VISIBLE }
                    .start()
            } ?: run { GLog.e(TAG, LogFlag.DL) { "filterContainer is null" } }
        }
    }

    /**
     * 隐藏滤镜选择器控件
     */
    fun hideSelector() {
        filterContainer?.let {
            it.animate().cancel()
            it.visibility = View.INVISIBLE
        }
        sectionBus.viewBus.unsubscribe(TopicID.Preview.TOPIC_PREVIEW_CLIP_RECT, clipRectChangeObserver)
    }

    /**
     * 显示滤镜名称图标
     */
    private fun showFilterTitle() {
        filterTitleContainer?.let {
            if (it.alpha != 1f) {
                it.animate().alpha(1f).apply {
                    duration = FILTER_TITLE_FADE_DURATION
                    interpolator = FILTER_TITLE_FADE_INTERPOLATOR
                    withEndAction {
                        //显示动画结束后再开启定时器执行隐藏动画，避免显示动画和隐藏动画冲突
                        it.handler?.let { handler ->
                            handler.removeCallbacks(fadeOutRunnable)
                            handler.postDelayed(fadeOutRunnable, FILTER_TITLE_HIDE_DELAY)
                        }
                    }
                    start()
                }
            } else {
                it.handler?.let { handler ->
                    handler.removeCallbacks(fadeOutRunnable)
                    handler.postDelayed(fadeOutRunnable, FILTER_TITLE_HIDE_DELAY)
                }
            }
        }
    }

    override fun onFilterSelectChanged(position: Int) {
        GLog.d(TAG, LogFlag.DL) { "onFilterSelectChanged. position=$position" }
        filterSelectedPosition = position
        updateFilterTitle(position)
        selectChangeListener?.onFilterSelectChanged(position)
    }

    /**
     * 更新滤镜标题内容
     */
    private fun updateFilterTitle(position: Int) {
        filterTitleContainer?.let {
            if (needShowFilterGuide.not() && position == 0) {
                // 因为其他item的延时消息，会把alpha设置回来，所以直接使用invisible
                it.visibility = INVISIBLE
                return@updateFilterTitle
            }
        }
        filterTitleContainer?.visibility = VISIBLE
        filterTitlePoint?.visibility = View.GONE
        filterTitleIcon?.visibility = View.GONE
        filterTitlePrefixText?.visibility = View.GONE
        val centerData = filterData.getOrNull(position) ?: return
        when (centerData.masterType) {
            DEFAULT_MASTER_TYPE -> {
                filterTitleIcon?.visibility = View.VISIBLE
                filterTitlePoint?.visibility = View.VISIBLE
            }

            DEFAULT_FILM_TYPE -> {
                filterTitlePrefixText?.let {
                    it.visibility = View.VISIBLE
                    it.setText(R.string.picture3d_new_editor_text_filter_c_g_ssx04_2020)
                }
                filterTitlePoint?.visibility = View.VISIBLE
            }

            CC_FILM_TYPE -> {
                filterTitlePrefixText?.let {
                    it.visibility = View.VISIBLE
                    it.setText(R.string.base_photo_edit_cc_film)
                }
                filterTitlePoint?.visibility = View.VISIBLE
            }

            NC_FILM_TYPE -> {
                filterTitlePrefixText?.let {
                    it.visibility = View.VISIBLE
                    it.setText(R.string.base_photo_edit_nc_film)
                }
                filterTitlePoint?.visibility = View.VISIBLE
            }

            NH_FILM_TYPE -> {
                filterTitlePrefixText?.let {
                    it.visibility = View.VISIBLE
                    it.setText(R.string.base_photo_edit_nh_film)
                }
                filterTitlePoint?.visibility = View.VISIBLE
            }
        }
        filterTitleText?.text = centerData.filterName

        showFilterTitle()
    }

    /**
     * 更新滤镜列表的位置
     */
    private fun updateFilterListPosition(clipRect: RectF) {
        if (clipRect.width() <= 0 || clipRect.height() <= 0) {
            return
        }
        //更新滤镜选择控件位置
        if (clipRect.isEmpty.not()) {
            val isSmallLandscape = isSmallScreenLandscape(sectionBus.hostInstance.getCurrentAppUiConfig())
            filterContainer?.let { view ->
                val viewHeight = view.getDimensionBasedOnOrientation(
                    isSmallLandscape,
                    R.dimen.photo_editor_ai_composition_filter_container_small_landscape_height,
                    R.dimen.photo_editor_ai_composition_filter_container_height
                )
                view.updateLayoutParams<MarginLayoutParams> {
                    val ratio = clipRect.width() / clipRect.height()
                    val bgColorRes: Int
                    if (ratio > EXTREME_RATIO_MAX) {
                        leftMargin = clipRect.left.toInt()
                        rightMargin = (view.parent as ViewGroup).width - clipRect.right.toInt()
                        bgColorRes = R.color.photo_editor_ai_composition_filter_title_bg_color2
                    } else if (ratio < EXTREME_RATIO_MIN) {
                        if (isSmallLandscape) {
                            leftMargin = sectionBus.rootView.findViewById<View>(R.id.left_safe_area)?.right ?: 0
                            rightMargin =
                                view.resources.getDimensionPixelSize(R.dimen.photo_editor_ai_composition_filter_container_small_landscape_margin)
                        } else {
                            leftMargin = 0
                            rightMargin = 0
                        }
                        bgColorRes = R.color.photo_editor_ai_composition_filter_title_bg_color3
                    } else {
                        leftMargin = clipRect.left.toInt()
                        rightMargin = (view.parent as ViewGroup).width - clipRect.right.toInt()
                        bgColorRes = R.color.photo_editor_ai_composition_filter_title_bg_color1
                        setFilterListFadingEdge(view)
                    }

                    val offset = if (titleOffsetAnimator.isRunning) {
                        titleOffsetAnimator.animatedValue as Int
                    } else if (!isFilterShowInPreviewRegion(ratio)) {
                        titleOffsetAnimator.setIntValues(0, 0)
                        titleOffsetAnimator.setCurrentFraction(0f)
                        0
                    } else {
                        titleOffsetAnimator.setIntValues(-viewHeight, -viewHeight)
                        titleOffsetAnimator.setCurrentFraction(0f)
                        -viewHeight
                    }
                    //topMargin值进1位解决滤镜控件增加蒙层背景后没有完全覆盖到预览区域导致出现底部像素出现断层白边问题
                    view.translationY = ceil(clipRect.bottom) + offset
                    filterTitleContainer?.setBackgroundColor(view.context.getColor(bgColorRes))
                }
            }
            //更新滤镜指示器位置
            filterListView?.setKeepFocusItemPosition(filterSelectedPosition)
        } else {
            GLog.w(TAG, LogFlag.DL) { "updateFilterListPosition clipRect = null" }
            //裁切区域为空，滤镜选择器隐藏
            hideSelector()
        }
    }

    /**
     * 设置滤镜列表的渐隐边距。
     * fix在小横屏壁纸比例下，滤镜列表由于宽度限制，导致旁边的滤镜item透明度太低，因此需要把fadingEdge减小
     * 同时由于fadingEdge不能突变，会导致item的透明度会经历类似0.3->1->0.5的闪变，因此fadingEdge也是随着宽度的降低而降低。
     */
    private fun setFilterListFadingEdge(view: ViewGroup) {
        val thresholdMaxWidth = view.resources.getDimension(R.dimen.photo_editor_ai_composition_filter_list_fading_edge_max_width)
        val thresholdMinWidth = view.resources.getDimension(R.dimen.photo_editor_ai_composition_filter_list_fading_edge_min_width)
        val smallFadingEdge = view.resources.getDimension(R.dimen.photo_editor_ai_composition_filter_list_center_small_landscape_fading_edge_width)
        val normalFadingEdge = view.resources.getDimension(R.dimen.base_editor_fading_edge_width)

        val viewWidth = view.width.toFloat()
        val fadingWidth = when {
            viewWidth < thresholdMinWidth -> smallFadingEdge
            viewWidth >= thresholdMaxWidth -> normalFadingEdge
            else -> {
                val ratio = (viewWidth - thresholdMinWidth) / (thresholdMaxWidth - thresholdMinWidth)
                smallFadingEdge + (normalFadingEdge - smallFadingEdge) * ratio
            }
        }
        if (filterListView?.currentFadingWidth == fadingWidth) {
            return
        }
        filterListView?.setFadingWidth(fadingWidth)
    }

    /**
     * 设置滤镜选择变化监听
     */
    fun setOnFilterSelectChangeListener(listener: FilterAdapter.OnFilterSelectChangeListener?) {
        selectChangeListener = listener
    }

    /**
     * 手势左滑
     */
    override fun onSlideLeft() {
        // 为避免快速来回滑动图片切换滤镜时，滤镜跳变跨度过大，基于将要选中的位置递增，而非已选中位置
        val nextPosition = selectingPosition + 1
        GLog.d(TAG, LogFlag.DL) { "onSlideLeft. position:$selectingPosition -> $nextPosition" }
        if (nextPosition in 0 until (filterAdapter?.itemCount ?: 0)) {
            filterListView?.stopScroll()
            filterListView?.smoothScrollToPosition(nextPosition)
            filterListView?.setKeepFocusItemPosition(nextPosition)
            selectingPosition = nextPosition
        }
    }

    /**
     * 手势右滑
     */
    override fun onSlideRight() {
        // 为避免快速来回滑动图片切换滤镜时，滤镜跳变跨度过大，基于将要选中的位置递减，而非已选中位置
        val nextPosition = selectingPosition - 1
        GLog.d(TAG, LogFlag.DL) { "onSlideRight. position:$selectingPosition -> $nextPosition" }
        if (nextPosition in 0 until (filterAdapter?.itemCount ?: 0)) {
            filterListView?.stopScroll()
            filterListView?.smoothScrollToPosition(nextPosition)
            filterListView?.setKeepFocusItemPosition(nextPosition)
            selectingPosition = nextPosition
        }
    }

    fun updateFinalClipRect(finalClipRect: RectF) {
        if (finalClipRect.isEmpty || (filterContainer?.isVisible != true)) return

        val height = filterContainer?.height ?: 0
        val finalRatio = finalClipRect.width() / finalClipRect.height()
        val currentOffset = titleOffsetAnimator.animatedValue as Int
        val finalOffset = if (isFilterShowInPreviewRegion(finalRatio)) -height else 0
        if (currentOffset != finalOffset) {
            titleOffsetAnimator.setIntValues(currentOffset, finalOffset)
            titleOffsetAnimator.start()
        }
    }

    /**
     * 更新滤镜列表焦点item的背景色，选中item为深色，非选中item为浅色
     */
    private fun updateFilterViewBackground(scrollState: Int): Boolean {
        filterListView?.forEach { itemView ->
            val filterView = (itemView as? FilterItemView) ?: return false
            filterView.itemBackgroundId = R.color.photo_editor_ai_composition_filter_item_unselected_color
        }
        // IDLE状态将焦点item背景色更新为深色
        if (scrollState == RecyclerView.SCROLL_STATE_IDLE) {
            filterListView?.let {
                val linearLayoutManager = it.layoutManager as LinearLayoutManager
                val itemView = linearLayoutManager.findViewByPosition(filterSelectedPosition)
                val filterView = (itemView as? FilterItemView) ?: return false
                filterView.itemBackgroundId = R.color.photo_editor_ai_composition_filter_item_selected_color
            }
        }
        return true
    }

    /**
     * 更新滤镜列表焦点item的背景色，直到成功
     */
    private fun updateFilterItemBackgroundUntilSuccess() {
        filterListView?.apply {
            doOnNextLayout {
                if (!updateFilterViewBackground(scrollState)) {
                    GLog.w(TAG, LogFlag.DL) { "updateFilterItemBackgroundUntilSuccess. Failed. Try again..." }
                    updateFilterItemBackgroundUntilSuccess()
                }
            }
        }
    }

    /**
     * 滤镜列表是否显示在预览图区域内
     */
    private fun isFilterShowInPreviewRegion(ratio: Float): Boolean {
        return (ratio < EXTREME_RATIO_MAX) || isSmallScreenLandscape(sectionBus.hostInstance.getCurrentAppUiConfig())
    }

    fun onUiConfigChanged(container: ViewGroup, appUiConfig: AppUiResponder.AppUiConfig, isWindowSizeChanged: Boolean) {
        if (isWindowSizeChanged) {
            container.post {
                updateViews(container, appUiConfig)
                // 切横竖屏中断滤镜位移动画
                if (appUiConfig.orientation.isChanged()) {
                    titleOffsetAnimator.cancel()
                }
            }
        }
    }

    /**
     * 模块销毁
     */
    fun destroy() {
        // 释放资源
        filterListView?.apply {
            setOnAdsorptionChangeListener(null)
            setOnCenterViewChangedListener(null)
            removeOnScrollListener(filterListViewScrollListener)
        }
        // 中断动画
        titleOffsetAnimator.cancel()
        filterContainer?.animate()?.cancel()
        filterTitleContainer?.handler?.removeCallbacksAndMessages(fadeOutRunnable)
        selectChangeListener = null
        filterListView = null
        sectionBus.viewBus.unsubscribe(TopicID.Preview.TOPIC_PREVIEW_CLIP_RECT, clipRectChangeObserver)
    }

    private val animationProperties: PreviewAnimationProperties
        get() {
            return sectionBus.viewBus.get(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES) ?: PreviewAnimationProperties.empty()
        }

    /**
     * 是否小横屏
     */
    private fun isSmallScreenLandscape(config: AppUiResponder.AppUiConfig): Boolean {
        val isLargeScreen = ScreenUtils.isMiddleAndLargeScreen(sectionBus.hostInstance.requireContext())
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        return !isLargeScreen && isLandscape
    }

    /**
     * 根据屏幕方向返回对应的尺寸值。
     *
     * @param isSmallLandscape 是否为小横屏模式。
     * @param smallLandscapeResId 小横屏模式下的资源 ID。
     * @param normalResId 正常模式下的资源 ID。
     * @return 对应的尺寸值。
     */
    private fun View.getDimensionBasedOnOrientation(
        isSmallLandscape: Boolean,
        smallLandscapeResId: Int,
        normalResId: Int
    ): Int {
        return if (isSmallLandscape) {
            resources.getDimensionPixelSize(smallLandscapeResId)
        } else {
            resources.getDimensionPixelSize(normalResId)
        }
    }

    companion object {
        private const val TAG = "FilterSelector"

        /**
         * 滤镜默认类型
         */
        private const val DEFAULT_FILTER_TYPE = 0

        /**
         * 滤镜大师类型
         */
        private const val DEFAULT_MASTER_TYPE = 1

        /**
         * 滤镜胶片类型
         */
        private const val DEFAULT_FILM_TYPE = 2

        /**
         * 滤镜胶片类型 CC
         */
        private const val CC_FILM_TYPE = 3

        /**
         * 滤镜胶片类型 NC
         */
        private const val NC_FILM_TYPE = 4

        /**
         * 滤镜胶片类型 NH
         */
        private const val NH_FILM_TYPE = 5

        /**
         * 极限宽高比例最小值
         */
        private const val EXTREME_RATIO_MIN = 1 / 3f

        /**
         * 极限宽高比例最大值
         */
        private const val EXTREME_RATIO_MAX = 65 / 20.5f

        /**
         * 滤镜标签延迟3s后消失
         */
        private const val FILTER_TITLE_HIDE_DELAY = 3000L

        private const val FILTER_TITLE_FADE_DURATION = 450L
        private val FILTER_TITLE_FADE_INTERPOLATOR = COUIMoveEaseInterpolator()

        /**
         * 滤镜容器淡入时长
         */
        private const val FILTER_CONTAINER_FADE_IN_DURATION = 200L
    }
}