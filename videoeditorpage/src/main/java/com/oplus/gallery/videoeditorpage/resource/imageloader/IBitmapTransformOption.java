/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: IBitmapTransformOption
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/


package com.oplus.gallery.videoeditorpage.resource.imageloader;

import android.graphics.Bitmap;
import android.util.Size;

public interface IBitmapTransformOption {
    static final DefaultCornerBitmapOption DEFAULT_CORNER_BITMAP_HANDLER =
            new DefaultCornerBitmapOption();

    Size getSize();

    Bitmap transform(Bitmap inTransformBitmap, Bitmap target);
}
