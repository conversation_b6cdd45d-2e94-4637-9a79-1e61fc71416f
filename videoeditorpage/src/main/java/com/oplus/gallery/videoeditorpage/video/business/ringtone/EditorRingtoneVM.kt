/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorRingtoneVM
 ** Description:视频铃声编辑 EditorRingtoneVM
 ** Version: 1.0
 ** Date : 2025/06/10
 ** Author: ZhongQi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ZhongQi                     2025/06/10       1.0      first created
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.business.ringtone

import android.app.Application
import android.util.Size
import android.view.ViewGroup
import androidx.annotation.IntDef
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditorpage.abilities.editengine.base.EditorEngine
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.player.IVideoPlayerListenerImpl
import com.oplus.gallery.videoeditorpage.video.business.base.EditorBaseViewModel
import com.oplus.gallery.videoeditorpage.video.business.input.VideoType.SDR_VIDEO_TYPE
import com.oplus.gallery.videoeditorpage.video.business.output.ExportListener
import com.oplus.gallery.videoeditorpage.video.business.output.task.ExportVideoTask
import com.oplus.gallery.videoeditorpage.video.business.output.task.OutputData
import com.oplus.gallery.videoeditorpage.video.business.output.task.ResultSaveFileData
import com.oplus.gallery.videoeditorpage.video.business.output.task.SaveVideoData
import java.util.concurrent.Executors

class EditorRingtoneVM(application: Application, val editorEngine: EditorEngine) : EditorBaseViewModel(application), LifecycleObserver {
    /**
     * 当前播放状态
     */
    private val _playState = MutableLiveData<Int>(PlayState.INVALID)
    val playState: LiveData<Int> = _playState

    /**
     * 播放位置变化
     */
    private val _playPosition = MutableLiveData<Long>()
    val playPosition: LiveData<Long> = _playPosition

    /**
     * 导出进度变更
     */
    private val _compileProgress = MutableLiveData<Float>()
    val compileProgress: LiveData<Float> = _compileProgress

    /**
     * 导出结果变更
     */
    private val _compileResult = MutableLiveData<OutputData<ResultSaveFileData?>>()
    val compileResult: LiveData<OutputData<ResultSaveFileData?>> = _compileResult

    private var hasReset = false

    /**
     * 播放状态监听
     */
    private val playStateListener = object : IVideoPlayerListenerImpl {
        override fun onPreloadingCompletion() {
            GLog.d(TAG, LogFlag.DL) {
                "IVideoPlayerListenerImpl [onPreloadingCompletion]"
            }
            onPlayStatusChange()
        }

        override fun onStopped() {
            GLog.d(TAG, LogFlag.DL) {
                "IVideoPlayerListenerImpl [onStopped]"
            }
            onPlayStatusChange()
        }

        override fun onEOF(absolutePosition: Long) {
            GLog.d(TAG, LogFlag.DL) {
                "IVideoPlayerListenerImpl [onEOF]"
            }
            onPlayStatusChange()
            hasReset = true
            onPlayFinish()
        }

        override fun onPositionChange(absolutePosition: Long) {
            _playPosition.postValue(absolutePosition)
        }
    }

    /**
     * 导出监听
     */
    private val exportListener: ExportListener<ResultSaveFileData?> = object : ExportListener<ResultSaveFileData?> {
        override fun onProcess(process: Float) {
            _compileProgress.postValue(process)
        }

        override fun onExportComplete(outputData: OutputData<ResultSaveFileData?>) {
            _compileResult.postValue(outputData)
        }
    }

    fun create() {
        editorEngine.addVideoPlayerListener(playStateListener)
    }

    fun destroy() {
        editorEngine.removeVideoPlayerListener(playStateListener)
    }

    fun showRingtoneTrimThumbLoader(parent: ViewGroup, time: Long, padding: Int) {
        editorEngine.showCustomTrimThumbLoader(parent, time, padding)
    }

    fun adjustClip() {
        val clip = editorEngine.currentTimeline.videoTrackList[0].clipList[0]
        clip.changeTrimPointOnTrimAdjust(0, true)
        clip.changeTrimPointOnTrimAdjust(getVideoDuration(), false)
    }

    fun isPlaying(): Boolean {
        return editorEngine.isPlaying
    }

    fun startPlayer(startTime: Long, endTime: Long) {
        editorEngine.startPlayer(startTime, endTime)
    }

    fun stopPlayer() {
        editorEngine.forceStopPlayer()
    }

    fun seekTo(progress: Long, flag: Int) {
        editorEngine.seekTo(progress, flag)
    }

    fun seekTo(time: Long) {
        editorEngine.seekTo(time)
    }

    fun getVideoDuration(): Long {
        return editorEngine.timelineDuration
    }

    fun setResetStatus(status: Boolean) {
        hasReset = status
    }

    fun getResetStatus(): Boolean {
        return hasReset
    }

    fun getTotalTime(): Long {
        return editorEngine.getTotalTime()
    }

    fun getCurrentTime(): Long {
        return editorEngine.getCurrentTime()
    }

    fun startExportTask(startTime: Long, endTime: Long, timeStamp: Long) {
        val clip = editorEngine.currentTimeline.videoTrackList[0].clipList[0]
        val fileInfo = editorEngine.getAVFileInfo(clip.filePath)
        GLog.d(TAG, LogFlag.DL) { "clickDone, height =${fileInfo.videoHeidht}, timeStamp:$timeStamp" }

        val newTask = ExportVideoTask(
            editorEngine,
            SaveVideoData(
                startTime,
                endTime,
                fileInfo.fps,
                SDR_VIDEO_TYPE,
                Size(fileInfo.videoWidth, fileInfo.videoHeidht),
                timeStamp
            ),
            null,
            exportListener
        )
        Executors.newSingleThreadExecutor().submit(Runnable {
            newTask.run()
        })
    }

    private fun onPlayStatusChange() {
        _playState.postValue(if (editorEngine.isPlaying) PlayState.PLAYING else PlayState.PAUSE)
    }

    private fun onPlayFinish() {
        _playState.postValue(PlayState.FINISH)
    }

    class RingtoneVMFactory(
        val application: Application,
        val engine: EditorEngine
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(EditorRingtoneVM::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return EditorRingtoneVM(application, engine) as T
            } else {
                throw IllegalArgumentException("Unknown ViewModel class")
            }
        }
    }

    companion object {
        private const val TAG = "EditorRingtoneVM"
    }
}

@IntDef(
    PlayState.INVALID,
    PlayState.PLAYING,
    PlayState.PAUSE,
    PlayState.FINISH
)
annotation class PlayState {
    companion object {
        const val INVALID = -1
        const val PLAYING = 1
        const val PAUSE = 2
        const val FINISH = 3
    }
}