/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: ThumbnailRequest
 ** Description:
 ** Version: 1.0
 ** Date : 2025/8/1
 ** Author: yeguangjin@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yegua<PERSON><PERSON>@Apps.Gallery3D      2025/8/1    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.videoeditorpage.resource.imageloader;

import com.oplus.gallery.videoeditorpage.utlis.TextUtil;

import java.util.UUID;

public class ThumbnailRequest<Data> {
    public static final String TAG = "ThumbnailRequest";
    private Data mItem;
    private ThumbnailListener mThumbnailListener;
    private IBitmapTransformOption mBitmapHandle;
    private String mTargetTag;

    public ThumbnailRequest setSource(Data item) {
        mItem = item;
        return this;
    }

    public Data getSource() {
        return mItem;
    }

    public ThumbnailRequest setThumbnailListener(ThumbnailListener thumbnailListener) {
        mThumbnailListener = thumbnailListener;
        return this;
    }

    public ThumbnailListener getThumbnailListener() {
        return mThumbnailListener;
    }

    public IBitmapTransformOption getBitmapHandle() {
        return mBitmapHandle;
    }

    public ThumbnailRequest setBitmapHandle(IBitmapTransformOption bitmapHandle) {
        mBitmapHandle = bitmapHandle;
        return this;
    }

    public String getRequestKey() {
        return formRequestKey(mTargetTag, mBitmapHandle);
    }

    public static String formRequestKey(String targetTag, IBitmapTransformOption bitmapHandle) {
        StringBuilder builder = new StringBuilder();
        builder.append(targetTag);

        if (bitmapHandle != null) {
            builder.append("@");
            builder.append(bitmapHandle.hashCode());
        }

        String requestKey = builder.toString();
        return requestKey;
    }

    public ThumbnailRequest setTargetTag(String targetTag) {
        if (TextUtil.isEmpty(targetTag)) {
            mTargetTag = UUID.randomUUID().toString();
        } else {
            mTargetTag = targetTag;
        }

        return this;
    }
}
