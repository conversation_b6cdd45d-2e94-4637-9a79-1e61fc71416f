/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditVideoDataConverter.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/6/13
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/6/13        1.0         create this module
 ******************************************************************************/
package com.oplus.gallery.videoeditorpage.video.app

import androidx.core.net.toUri
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.util.formatNvmFilePath
import com.oplus.gallery.business_lib.videoedit.EditVideoData
import com.oplus.gallery.business_lib.videoedit.VideoSpecHelper
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.context.EditorEngineGlobalContext
import com.oplus.gallery.videoeditorpage.video.business.picker.data.PickerItemInfo

/**
 * 视频编辑数据转换器
 *
 * 负责在视频编辑相关的数据模型之间进行转换，主要用于将[EditVideoData]转换为[PickerItemInfo]。
 * 这个转换过程会处理普通视频和Olive照片的特殊情况。
 */
object EditVideoDataConverter {
    /**
     * 将[EditVideoData]转换为[PickerItemInfo]
     *
     * 注意：会有耗时操作，最好放在工作线程
     *
     * @param editVideoData 需要转换的视频编辑数据
     * @return 转换后的选择器项目信息
     *
     * 转换过程会处理以下情况：
     * 1. 如果无法获取MediaItem，则直接使用EditVideoData中的信息创建PickerItemInfo
     * 2. 对于普通视频，会根据旋转角度调整宽高
     * 3. 对于Olive照片，会提取其中的微视频信息，包括：
     *    - 视频时长计算
     *    - 媒体URI的特殊构建
     */
    fun convertToPickerItemInfo(editVideoData: EditVideoData): PickerItemInfo {
        val mediaItem = DataManager.getMediaObject(editVideoData.uriPath.toUri()) as? MediaItem ?: run {
            return PickerItemInfo().apply {
                srcFilePath = editVideoData.uriPath
                path = srcFilePath // 直接使用已赋值的 srcFilePath
                mediaType = editVideoData.mediaType
                duration = EditorEngineGlobalContext.getInstance().getDuration(path)
                width = editVideoData.width
                height = editVideoData.height
                mediaHdrType = editVideoData.mediaHdrType
            }
        }

        val mediaSize = VideoSpecHelper.getRotated(mediaItem.rotation, mediaItem.width, mediaItem.height)

        var mediaUri = editVideoData.uriPath
        var mediaDuration = EditorEngineGlobalContext.getInstance().getDuration(mediaUri)
        val isOlive = mediaItem.isOlivePhoto()
        if (isOlive) {
            OLiveDecode.create(mediaItem.filePath).decode()?.microVideo?.let {
                mediaUri = formatNvmFilePath(mediaUri.toUri(), it.offset, it.length)
                mediaDuration = EditorEngineGlobalContext.getInstance().getDuration(mediaUri)
            }
            if (mediaDuration == AppConstants.Number.NUMBER_0.toLong()) {
                mediaDuration = mediaItem.duration.toLong() * TimeUtils.TIME_1_MS_IN_US
            }
        }

        return PickerItemInfo().apply {
            isOlivePhoto = isOlive
            dateTakenInMs = mediaItem.dateTakenInMs
            srcFilePath = mediaUri
            path = srcFilePath // 直接使用已赋值的 srcFilePath
            mediaType = editVideoData.mediaType
            duration = mediaDuration
            width = mediaSize.width
            height = mediaSize.height
            mediaHdrType = editVideoData.mediaHdrType
        }
    }
}
