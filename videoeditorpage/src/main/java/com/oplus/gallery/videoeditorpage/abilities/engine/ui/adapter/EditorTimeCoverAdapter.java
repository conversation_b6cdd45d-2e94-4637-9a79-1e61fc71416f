/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorTimeCoverAdapter.java
 * Description:
 *
 * Version: 1.0
 * Date: 2025/8/2
 * Author: tangzhibin
 * ------------------------------- Revision History: ---------------------------
 * <author>         <date>        <version>     <desc>
 * -----------------------------------------------------------------------------
 * tangzhibin      2025/8/2        1.0         create this module
 ******************************************************************************/

package com.oplus.gallery.videoeditorpage.abilities.engine.ui.adapter;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.abilities.editengine.interfaces.IVideoClip;
import com.oplus.gallery.videoeditorpage.abilities.editengine.constant.StreamingConstant;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.BaseVideoClipEffect;
import com.oplus.gallery.videoeditorpage.abilities.editengine.meicam.effect.clip.MeicamVideoClipEffect;

import java.util.ArrayList;
import java.util.List;

public class EditorTimeCoverAdapter extends RecyclerView.Adapter {
    private static final String TAG = "EditorTimeCoverAdapter";
    protected final static int TYPE_HEAD = 0;
    protected final static int TYPE_MID = 1;
    protected final static int TYPE_FOOT = 2;
    protected int mLeftPadding;
    protected int mRightPadding;
    protected int mSelectPosition;
    protected boolean mHaveTailClip;
    protected boolean mNeedShowTailText = true;
    protected boolean mNeedShowThumbnailSpread = false;
    protected Context mContext;
    protected List<Integer> mItemLengthList = new ArrayList<>();
    protected List<IVideoClip> mClipList = new ArrayList<>();
    private final static int LAST_POSITION_SPACE = 2;
    private int mMinThumbnailWidth;
    private double mPixelPerMicrosecond;
    private int mSelectTextPaddingStart;
    private int mUnSelectTextPaddingStart;
    private int mTextPaddingEnd;
    private int mThumbnailSpreadOffset;

    private OnMuteClickListener mOnMuteClickListener;
    private boolean mIsVideoMute = false;
    private String mMuteText;
    private ColorStateList mUnmuteColor;
    private ColorStateList mMuteColor;
    private Drawable mUnmuteDrawable;
    private Drawable mMuteDrawable;
    private boolean mShowMuteBtn = true;

    public EditorTimeCoverAdapter(Context context, int leftPadding, int rightPadding, List<IVideoClip> clipList, double pixelPerMicrosecond) {
        this.mContext = context;
        this.mLeftPadding = leftPadding;
        this.mRightPadding = rightPadding;
        this.mPixelPerMicrosecond = pixelPerMicrosecond;
        mSelectTextPaddingStart = mContext.getResources().getDimensionPixelOffset(R.dimen.edit_time_edit_timeline_add_tail_span_text_padding_start);
        mUnSelectTextPaddingStart = mContext.getResources().getDimensionPixelOffset(R.dimen.edit_time_edit_timeline_add_tail_icon_margin_start);
        mTextPaddingEnd = mContext.getResources().getDimensionPixelOffset(R.dimen.edit_time_edit_timeline_add_tail_span_text_padding_end);
        mThumbnailSpreadOffset = context.getResources().getDimensionPixelSize(R.dimen.edit_time_edit_timeline_span_line_height);
        init(clipList);
    }

    public void setOnMuteClickListener(OnMuteClickListener listener) {
        mOnMuteClickListener = listener;
    }

    public void setMuteButtonState(boolean isMute) {
        mIsVideoMute = isMute;
        notifyItemChanged(0);
    }

    public void setMuteButtonVisible(boolean show) {
        mShowMuteBtn = show;
        notifyItemChanged(0);
    }

    public void setMuteIconRes(String muteText, ColorStateList unmuteColor, ColorStateList muteColor, Drawable unmuteDrawable, Drawable muteDrawable) {
        mMuteText = muteText;
        mUnmuteColor = unmuteColor;
        mMuteColor = muteColor;
        mUnmuteDrawable = unmuteDrawable;
        mMuteDrawable = muteDrawable;
    }

    public MuteButtonInfo getMuteButtonInfo() {
        if ((mMuteColor == null) || (mMuteDrawable == null) || (mUnmuteColor == null)
                || (mUnmuteDrawable == null)) {
            return null;
        }
        MuteButtonInfo info = new MuteButtonInfo();
        info.mMuteColor = mMuteColor;
        info.mMuteDrawable = mMuteDrawable;
        info.mUnmuteColor = mUnmuteColor;
        info.mUnmuteDrawable = mUnmuteDrawable;
        info.mMuteText = mMuteText;
        info.mIsVideoMute = mIsVideoMute;
        return info;
    }

    public void setHaveTailClip(boolean haveTailClip) {
        this.mHaveTailClip = haveTailClip;
    }

    public void showThumbnailSpread(boolean isShowThumbnail) {
        this.mNeedShowThumbnailSpread = isShowThumbnail;
    }

    public void hideTailText() {
        mNeedShowTailText = false;
        notifyLastClipPosition();
    }

    public void showTailText() {
        mNeedShowTailText = true;
        notifyLastClipPosition();
    }

    private void notifyLastClipPosition() {
        int lastClipPosition = getLastClipPosition();
        notifyItemChanged(lastClipPosition);
    }

    private void init(List<IVideoClip> clipList) {
        mItemLengthList.clear();
        mItemLengthList.add(mLeftPadding);
        mClipList.clear();
        mClipList = clipList;
        int start = 0;
        int end = 0;
        for (IVideoClip iClip : clipList) {
            start = (int) Math.floor(iClip.getInPoint() * mPixelPerMicrosecond + 0.5D);
            end = (int) Math.floor(iClip.getOutPoint() * mPixelPerMicrosecond + 0.5D);
            mItemLengthList.add(end - start);
        }
        mItemLengthList.add(mRightPadding);
    }

    public void refresh(int before, int after) {
        before = before + 1;
        after = after + 1;
        mSelectPosition = after;
        if ((before > mItemLengthList.size() - 1) || (after > mItemLengthList.size() - 1)) {
            GLog.e(TAG, "refresh: index is too big");
            return;
        }
        notifyItemChanged(before);
        notifyItemChanged(after);
    }

    public void refresh(int index) {
        mSelectPosition = index + 1;
        notifyItemChanged(index + 1);
    }

    public void refreshItemLength(int length, int index) {
        index = index + 1;
        mItemLengthList.set(index, length);
        notifyItemChanged(index);
    }

    protected int checkMinItemLength(int original) {
        if (original < mMinThumbnailWidth) {
            return mMinThumbnailWidth;
        }
        return original;
    }

    protected int checkMinItemBgLength(int original) {
        if (original < mMinThumbnailWidth - mThumbnailSpreadOffset) {
            return mMinThumbnailWidth - mThumbnailSpreadOffset;
        }
        return original;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return TYPE_HEAD;
        } else if (position == (getItemCount() - 1)) {
            return TYPE_FOOT;
        } else {
            return TYPE_MID;
        }
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        if (viewType == TYPE_HEAD) {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.engine_timeline_editor_mute_sound_view, viewGroup, false);
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mLeftPadding, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            view.setBackgroundColor(viewGroup.getContext().getResources().getColor(R.color.editor_engine_background_color));
            return new HeadHolder(view);
        } else if (viewType == TYPE_FOOT) {
            View view = new View(mContext);
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mRightPadding, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            return new FootHolder(view);
        } else {
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.engine_timeline_editor_cover_view, null);
            return new MyViewHolder(view);
        }
    }

    public void setMinThumbnailWidth(int minThumbnailWidth) {
        mMinThumbnailWidth = minThumbnailWidth;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof HeadHolder) {
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mItemLengthList.get(position), ViewGroup.LayoutParams.MATCH_PARENT);
            holder.itemView.setLayoutParams(layoutParams);
            ((HeadHolder) holder).setMuteButtonState();
        } else if (holder instanceof MyViewHolder) {
            onBindMyViewHolder((MyViewHolder) holder, position);
        }
    }

    private void onBindMyViewHolder(@NonNull MyViewHolder holder, int position) {
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mItemLengthList.get(position), ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.width = checkMinItemLength(layoutParams.width);
        holder.itemView.setLayoutParams(layoutParams);

        if (mNeedShowThumbnailSpread && isHasCartoonEffect(position)) {
            final BaseVideoClipEffect currentCartoonEffect = getCurrentCartoonEffect(position);
            if (currentCartoonEffect != null) {
                holder.mThumbnailSpreadView.setVisibility(View.VISIBLE);
                final int playType = currentCartoonEffect.getEffectPlayType();
                final long playDuration = currentCartoonEffect.getEffectPlayDuration();
                int coverWidth = (int) Math.floor(playDuration * mPixelPerMicrosecond + 0.5D);
                int coverLeftMargin = 0;
                int coverBackground = 0;
                if (playType == StreamingConstant.Cartoon.TYPE_IN) {
                    coverWidth = coverWidth - mThumbnailSpreadOffset;
                    coverLeftMargin = mThumbnailSpreadOffset;
                    if (coverWidth >= (mItemLengthList.get(position) - mThumbnailSpreadOffset)) {
                        coverBackground = R.drawable.edit_drawable_corner_left_right_green;
                    } else {
                        coverBackground = R.drawable.edit_drawable_corner_left_green;
                    }
                } else if (playType ==  StreamingConstant.Cartoon.TYPE_OUT) {
                    coverWidth = coverWidth - mThumbnailSpreadOffset;
                    coverLeftMargin = mItemLengthList.get(position) - coverWidth;
                    if (coverWidth >= (mItemLengthList.get(position) - mThumbnailSpreadOffset)) {
                        coverBackground = R.drawable.edit_drawable_corner_left_right_green;
                    } else {
                        coverBackground = R.drawable.edit_drawable_corner_right_green;
                    }
                } else {
                    coverWidth = coverWidth - mThumbnailSpreadOffset;
                    coverLeftMargin = mThumbnailSpreadOffset;
                    coverBackground = R.drawable.edit_drawable_corner_left_right_green;
                }
                RelativeLayout.LayoutParams coverLayoutParams = new RelativeLayout.LayoutParams(coverWidth, RelativeLayout.LayoutParams.MATCH_PARENT);
                coverLayoutParams.width = checkMinItemBgLength(coverLayoutParams.width);
                coverLayoutParams.setMargins(coverLeftMargin, mThumbnailSpreadOffset, mThumbnailSpreadOffset, mThumbnailSpreadOffset);
                holder.mThumbnailSpreadView.setLayoutParams(coverLayoutParams);
                holder.mThumbnailSpreadView.setBackgroundResource(coverBackground);
            } else {
                holder.mThumbnailSpreadView.setVisibility(View.INVISIBLE);
                GLog.d(TAG, "get current clip cartoon effect is null");
            }
        } else {
            holder.mThumbnailSpreadView.setVisibility(View.INVISIBLE);
        }

        bindViewSelection(holder, position);
    }

    private void bindViewSelection(MyViewHolder holder, int position) {
        boolean selected = (mSelectPosition == position);
        if ((mHaveTailClip) && (mNeedShowTailText)) {
            int lastClipPosition = getLastClipPosition();
            if (position == lastClipPosition) {
                holder.bindWithText(selected);
            } else {
                holder.bindWithoutText(selected);
            }
        } else {
            if (isPlaceHolderClip(position)) {
                holder.bindDashView(selected);
            } else {
                holder.bindWithoutText(selected);
            }
        }
    }

    private boolean isPlaceHolderClip(int position) {
        if ((position <= 0) || (position > mClipList.size())) {
            return false;
        }
        IVideoClip videoClip = mClipList.get(position - 1);
        return ((videoClip != null)
                && (videoClip.getClipType() == IVideoClip.ClipType.TYPE_PLACE_HOLDER_CLIP));
    }

    private boolean isHasCartoonEffect(int position) {
        IVideoClip iVideoClip = mClipList.get(position - 1);
        if (iVideoClip == null) {
            GLog.d(TAG, "thumbnail cover video clip is null");
            return false;
        }
        List<BaseVideoClipEffect> clipEffectList = iVideoClip.getEffectList();
        if (clipEffectList == null) {
            GLog.d(TAG, "thumbnail cover clipEffectList is null");
            return false;
        }
        for (BaseVideoClipEffect clipEffect : clipEffectList) {
            if (clipEffect == null) {
                continue;
            }
            if ((clipEffect instanceof MeicamVideoClipEffect)
                    && ((clipEffect.getEffectType()) == StreamingConstant.Cartoon.TYPE_CARTOON_EFFECT)) {
                return true;
            }
        }
        return false;
    }

    private BaseVideoClipEffect getCurrentCartoonEffect(int position) {
        IVideoClip iVideoClip = mClipList.get(position - 1);
        if (iVideoClip == null) {
            GLog.d(TAG, "thumbnail cover video clip is null");
            return null;
        }
        List<BaseVideoClipEffect> clipEffectList = iVideoClip.getEffectList();
        if (clipEffectList == null) {
            GLog.d(TAG, "thumbnail cover clipEffectList is null");
            return null;
        }
        for (BaseVideoClipEffect clipEffect : clipEffectList) {
            if (clipEffect == null) {
                continue;
            }
            if ((clipEffect.getEffectType()) == StreamingConstant.Cartoon.TYPE_CARTOON_EFFECT) {
                return clipEffect;
            }
        }
        return null;
    }

    protected int getLastClipPosition() {
        return getItemCount() - LAST_POSITION_SPACE;
    }

    @Override
    public int getItemCount() {
        return (mItemLengthList.size());
    }

    class MyViewHolder extends RecyclerView.ViewHolder {

        public TextView mTextView;
        private View mThumbnailSpreadView;

        MyViewHolder(@NonNull View itemView) {
            super(itemView);
            mTextView = itemView.findViewById(R.id.edit_timeline_cover_text);
            mThumbnailSpreadView = itemView.findViewById(R.id.thumbnail_spread_view);
        }

        public void bindWithText(boolean selected) {
            mTextView.setVisibility(View.VISIBLE);
            if (selected) {
                itemView.setBackgroundResource(R.drawable.edit_drawable_corner_white_grey);
                mTextView.setPadding(mSelectTextPaddingStart, 0, mTextPaddingEnd, 0);
            } else {
                itemView.setBackgroundResource(R.drawable.edit_drawable_corner_tranc_grey);
                mTextView.setPadding(mUnSelectTextPaddingStart, 0, mTextPaddingEnd, 0);
            }
        }

        public void bindWithoutText(boolean selected) {
            mTextView.setVisibility(View.GONE);
            if (selected) {
                mTextView.setPadding(mSelectTextPaddingStart, 0, mTextPaddingEnd, 0);
                itemView.setBackgroundResource(R.drawable.edit_drawable_corner_white_black);
            } else {
                mTextView.setPadding(mUnSelectTextPaddingStart, 0, mTextPaddingEnd, 0);
                itemView.setBackgroundResource(R.drawable.edit_drawable_corner_black_black);
            }
        }

        public void bindDashView(boolean selected) {
            mTextView.setVisibility(View.GONE);
            if (selected) {
                mTextView.setPadding(mSelectTextPaddingStart, 0, mTextPaddingEnd, 0);
                itemView.setBackgroundResource(R.drawable.edit_clip_palceholder_corner_dash_bg);
            } else {
                mTextView.setPadding(mUnSelectTextPaddingStart, 0, mTextPaddingEnd, 0);
                itemView.setBackground(null);
            }
        }
    }

    public class HeadHolder extends RecyclerView.ViewHolder {
        public TextView mMuteIcon;

        HeadHolder(@NonNull View itemView) {
            super(itemView);
            mMuteIcon = itemView.findViewById(R.id.mute_icon);
            mMuteIcon.setVisibility(mShowMuteBtn ? View.VISIBLE : View.GONE);
            mMuteIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (mOnMuteClickListener != null) {
                        mIsVideoMute = !mIsVideoMute;
                        mOnMuteClickListener.onMuteIconClick(mIsVideoMute);
                        setMuteButtonState();
                    }
                }
            });
        }

        public void setMuteButtonState() {
            if ((mMuteIcon == null) || (mMuteColor == null) || (mUnmuteColor == null)) {
                return;
            }
            mMuteIcon.setSelected(mIsVideoMute);
            mMuteIcon.setText(mMuteText);
            mMuteIcon.setCompoundDrawablePadding(mContext.getResources().getDimensionPixelSize(R.dimen.music_mute_compound_padding));
            mMuteIcon.setPadding(0, mContext.getResources().getDimensionPixelSize(R.dimen.editor_sound_text_margin_bottom), 0, 0);
            if (mIsVideoMute) {
                mMuteIcon.setTextColor(mMuteColor);
                mMuteIcon.setCompoundDrawables(null, mUnmuteDrawable, null, null);
            } else {
                mMuteIcon.setTextColor(mUnmuteColor);
                mMuteIcon.setCompoundDrawables(null, mMuteDrawable, null, null);
            }
            mMuteIcon.setVisibility(mShowMuteBtn ? View.VISIBLE : View.GONE);
        }
    }

    class FootHolder extends RecyclerView.ViewHolder {

        FootHolder(@NonNull View itemView) {
            super(itemView);
        }
    }

    public interface OnMuteClickListener<T> {
        void onMuteIconClick(boolean isMuteHandle);
    }

    public class MuteButtonInfo {
        public String mMuteText;
        public ColorStateList mUnmuteColor;
        public ColorStateList mMuteColor;
        public Drawable mUnmuteDrawable;
        public Drawable mMuteDrawable;
        public boolean mIsVideoMute;
    }
}
