/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorThemeUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.business.time;

import static com.oplus.gallery.foundation.util.display.ScreenUtils.dpToPixel;
import android.content.Context;
import android.content.res.Resources;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.memories.base.EditorBaseState;
import com.oplus.gallery.videoeditorpage.memories.base.EditorMemoriesBaseUiController;
import com.oplus.gallery.videoeditorpage.memories.ui.BottomActionBar;
import com.oplus.gallery.videoeditorpage.memories.app.ControlBarView;
import com.oplus.gallery.videoeditorpage.memories.config.VideoEditorUIConfig;

public class EditorTimeSetUIController extends EditorMemoriesBaseUiController {
    private static final String TAG = "EditorTimeSetUIController";
    private static final int LANDSCAPE_LAYOUT_ID = R.layout.videoeditor_memories_frame_timeset_layout_landscape;
    private static final int PORTRAIT_LAYOUT_ID = R.layout.videoeditor_memories_frame_timeset_layout;
    private TimeSetPickerView mTimeSetPickerView;
    private View mPickerSelectedView;
    private boolean mIsInTimeSetState;

    public EditorTimeSetUIController(Context context, ViewGroup rootView, EditorBaseState state) {
        super(context, rootView, state);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_memories_editor_sub_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_memories_editor_timeset_menu_layout;
    }

    @Override
    public int getContentLayoutId(@NonNull AppUiResponder.AppUiConfig config) {
        return isLandscapeLayout(config) ? LANDSCAPE_LAYOUT_ID : PORTRAIT_LAYOUT_ID;
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_text_preview_editor_timeset;
    }

    @Override
    public void onShow() {
        hidePlayButtonTimeContainer();
        mIsInTimeSetState = true;
        setImageDoneButtonEnable(false);
        mTimeSetPickerView = mContainer.findViewById(R.id.timeset_picker);
        mTimeSetPickerView.setOnDataChangeListener(mListener);
        mTimeSetPickerView.setTotalTime((int) mEditorState.getEngineManager().getThemeMaxTotalTime(),
                (int) mEditorState.getEngineManager().getThemeMinTotalTime(),
                (int) mEditorState.getEngineManager().getTotalTime());
        mPickerSelectedView =  mContainer.findViewById(R.id.videoeditor_hightlight_background);
        super.onShow();
    }

    @Override
    public void hide(boolean animate) {
        super.hide(animate);
        mIsInTimeSetState = false;
    }

    @Override
    public void hide(boolean animate, boolean removeContainer) {
        super.hide(animate, removeContainer);
        mIsInTimeSetState = false;
    }

    private TimeSetPickerView.OnDataChangeListener mListener = new TimeSetPickerView.OnDataChangeListener() {
        @Override
        public void onTimeDataChange(long milliTime) {
            long oldTime = mEditorState.getEngineManager().getTotalTime();
            GLog.d(TAG, "TimeSetPickerView.onTimeDataChange milliTime = " + milliTime + ", oldTime = " + oldTime);
            if ((milliTime != oldTime) && mIsInTimeSetState) {
                mTimeSetPickerView.setTag(milliTime);
                mEditorState.click(mTimeSetPickerView);
                setImageDoneButtonEnable(true);
            }
        }
    };

    private void setImageDoneButtonEnable(boolean enable) {
        BottomActionBar actionbar = getBottomActionBar();
        if (actionbar != null) {
            actionbar.setActionImageDoneEnable(enable);
        }
    }

    @Override
    public void adaptTimeSeekBarContainer(int layoutId, View view, AppUiResponder.AppUiConfig config, BaseActivity context) {
        View timeSeekBarView = ((ControlBarView) mRootView).getTimeSeekBar();
        if (timeSeekBarView == null) {
            return;
        }
        timeSeekBarView.post(() -> {
            if (context.isFinishing() || context.isDestroyed()) {
                GLog.d(TAG, "adaptTimeSeekBarContainer, UI controller has been destroyed, cancel perform post runnable");
                return;
            }
            checkSafeArea(context, config);
            int windowWidthDp = ScreenUtils.pixelToDp(config.getWindowWidth().getCurrent());
            int timeSeekBarWidth = dpToPixel(VideoEditorUIConfig.getTimeSeekBarWidth(windowWidthDp))
                    - context.horizontalNaviBarHeight(false);
            LinearLayout timeSeekBarViewContentView = timeSeekBarView.findViewById(R.id.time_seek_bar_content);
            if ((timeSeekBarViewContentView != null) && (timeSeekBarViewContentView.getLayoutParams() != null)) {
                timeSeekBarViewContentView.getLayoutParams().width = timeSeekBarWidth;
            }
            int viewPaddingLeft = VideoEditorUIConfig.getLeftSafeWidth();
            int viewPaddingRight = VideoEditorUIConfig.getRightSafeWidth();
            timeSeekBarView.setPadding(viewPaddingLeft, 0, viewPaddingRight, 0);
            int timeSeekBarHeight = getTimeSeekBarHeight();
            int titleBarHeight = getTitleBarHeight();
            Resources res = context.getResources();
            int toolBarHeightLandscape = res.getDimensionPixelOffset(R.dimen.memories_editor_timeset_menu_layout_height_landscape);
            int toolBarHeight = isLandscapeLayout(config) ? toolBarHeightLandscape : getMenuBarHeight();
            int bottomMargin = titleBarHeight + toolBarHeight;
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) timeSeekBarView.getLayoutParams();
            boolean isLayoutChange = (bottomMargin != layoutParams.bottomMargin) || (timeSeekBarHeight != layoutParams.height);
            if (isLayoutChange) {
                layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT;
                layoutParams.height = timeSeekBarHeight;
                layoutParams.bottomMargin = bottomMargin;
                layoutParams.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
            }
            if (isLayoutChange) {
                timeSeekBarView.requestLayout();
            }
        });
    }

    @Override
    public void adaptToolBarContainer(int layoutId, @NonNull View view, @NonNull AppUiResponder.AppUiConfig config, @NonNull BaseActivity context) {
        /* pickView start 选中view适配 */
        mPickerSelectedView.post(() -> {
            if (context.isFinishing() || context.isDestroyed()) {
                GLog.d(TAG, "adaptToolBarContainer, UI controller has been destroyed, cancel perform post runnable");
                return;
            }
            int windowWidth = config.getWindowWidth().getCurrent();
            int windowWidthDp = ScreenUtils.pixelToDp(windowWidth);
            int selectedViewWidth =  dpToPixel(VideoEditorUIConfig.getPickerSelectedViewWidthForTimeSet(windowWidthDp));
            int horizontalNaviBarHeight = context.horizontalNaviBarHeight(false);
            int selectedViewMinEdgeResId = R.dimen.memories_editor_timeset_menu_selected_bg_margin_border;
            int selectedViewMinEdge = context.getResources().getDimensionPixelSize(selectedViewMinEdgeResId) * 2;
            if ((windowWidth - selectedViewWidth) < selectedViewMinEdge) {
                selectedViewWidth = selectedViewWidth - selectedViewMinEdge;
            }
            selectedViewWidth = selectedViewWidth - horizontalNaviBarHeight;
            mTimeSetPickerView.setPadding(context.leftNaviBarHeight(false),
                    0,
                    context.rightNaviBarHeight(false),
                    0);
            int selectedViewHeightDimenId = isLandscapeLayout(config) ? R.dimen.memories_editor_timeset_menu_selected_bg_height_landscape
                    : R.dimen.memories_editor_timeset_menu_selected_bg_height;
            int selectedViewHeight = context.getResources().getDimensionPixelOffset(selectedViewHeightDimenId);
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) mPickerSelectedView.getLayoutParams();
            boolean isLayoutChange = (selectedViewWidth != layoutParams.width)
                    || (selectedViewHeight != layoutParams.height);
            if (isLayoutChange) {
                layoutParams.width = selectedViewWidth;
                layoutParams.height = selectedViewHeight;
                mPickerSelectedView.setLayoutParams(layoutParams);
            }
        });
        /*pickView end 选中view适配*/
    }

    @Override
    public boolean isLandscapeLayout(AppUiResponder.AppUiConfig config) {
        return ScreenUtils.pixelToDp(config.getWindowHeight().getCurrent()) <= VideoEditorUIConfig.WINDOW_HEIGHT_540;
    }

    @Override
    public void adaptPreviewArea(int layoutId,
                                 @NonNull View view,
                                 @NonNull AppUiResponder.AppUiConfig config,
                                 @NonNull BaseActivity context) {
        adaptPreviewArea(layoutId, view, config, context, false);
    }
}