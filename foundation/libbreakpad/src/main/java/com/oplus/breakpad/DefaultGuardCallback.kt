/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DefaultGuardCallback.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/27
 ** Author: WUDANYANG
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG  2021/05/27		1.0		BREAKPAD
 *********************************************************************************/
package com.oplus.breakpad

import com.oplus.gallery.foundation.util.debug.GLog

class DefaultGuardCallback : IGuardCallback {

    companion object {
        const val TAG = "DefaultGuardCallback"
    }

    override fun onResult(result: Int) {
        GLog.d(TAG, "onResult: result = $result")
    }
}